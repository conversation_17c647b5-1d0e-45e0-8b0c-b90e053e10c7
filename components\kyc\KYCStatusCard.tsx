import React, { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';

interface KYCStatusCardProps {
  status: 'pending' | 'completed' | 'rejected' | 'expired' | 'pending_approval';
  kycData?: any;
  onStartKYC?: () => void;
  onEditKYC?: () => void;
}

interface KYCDocument {
  id: string;
  type: 'id_document' | 'selfie' | 'proof_of_address';
  fileName: string;
  url: string;
  uploadedAt: string;
}

interface ImageModalProps {
  isOpen: boolean;
  imageUrl: string;
  title: string;
  onClose: () => void;
}

const ImageModal: React.FC<ImageModalProps> = ({ isOpen, imageUrl, title, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50" onClick={onClose}>
      <div className="relative max-w-4xl max-h-[90vh] p-4" onClick={(e) => e.stopPropagation()}>
        <button
          onClick={onClose}
          className="absolute top-2 right-2 text-white bg-black bg-opacity-50 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-75 z-10"
        >
          ✕
        </button>
        <img
          src={imageUrl}
          alt={title}
          className="max-w-full max-h-full object-contain rounded-lg"
          style={{ maxHeight: 'calc(90vh - 2rem)' }}
        />
        <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white px-3 py-1 rounded">
          {title}
        </div>
      </div>
    </div>
  );
};

export const KYCStatusCard: React.FC<KYCStatusCardProps> = ({
  status,
  kycData,
  onStartKYC,
  onEditKYC
}) => {
  // Debug logging
  console.log('🎯 KYCStatusCard received:', {
    status,
    kycData,
    hasData: !!kycData,
    userId: kycData?.user_id,
    fullName: kycData?.full_legal_name
  });

  const [showDetailedView, setShowDetailedView] = useState(false);
  const [documents, setDocuments] = useState<KYCDocument[]>([]);
  const [loadingDocuments, setLoadingDocuments] = useState(false);
  const [selectedImage, setSelectedImage] = useState<{ url: string; title: string } | null>(null);

  // Clear documents when KYC data changes (user switch)
  useEffect(() => {
    console.log('🔄 KYC data changed, clearing documents');
    setDocuments([]);
    setSelectedImage(null);
  }, [kycData?.user_id]);

  // Fetch KYC documents when detailed view is opened
  useEffect(() => {
    if (showDetailedView && kycData?.user_id && (status === 'completed' || status === 'pending_approval')) {
      console.log('🔍 Detailed view opened, fetching documents for user:', kycData.user_id);
      fetchKYCDocuments();
    } else if (!showDetailedView) {
      // Clear documents when detailed view is closed to save memory
      setDocuments([]);
    }
  }, [showDetailedView, kycData?.user_id, status]);

  const fetchKYCDocuments = async () => {
    if (!kycData?.user_id) {
      console.warn('⚠️ No user_id found in kycData, cannot fetch documents');
      return;
    }

    setLoadingDocuments(true);
    try {
      const serviceClient = getServiceRoleClient();
      const currentUserId = kycData.user_id;

      console.log('🔍 Fetching documents for user ID:', currentUserId);
      console.log('🔍 User full name:', kycData.full_legal_name);

      // Get all files from proof bucket
      const { data: files, error } = await serviceClient.storage
        .from('proof')
        .list('', {
          limit: 100,
          sortBy: { column: 'created_at', order: 'desc' }
        });

      if (error) {
        console.error('❌ Error fetching documents:', error);
        return;
      }

      console.log('📁 Total files in proof bucket:', files?.length || 0);

      const documentList: KYCDocument[] = [];

      if (files) {
        // STRICT filtering - only files that belong to this specific user
        const userFiles = files.filter(file => {
          const fileName = file.name.toLowerCase();
          const userIdStr = currentUserId.toString();

          // Only include files that explicitly contain this user's ID
          const belongsToUser = (
            fileName.includes(`${userIdStr}_`) ||
            fileName.includes(`user_${userIdStr}`) ||
            fileName.startsWith(`${userIdStr}_`) ||
            fileName.includes(`_${userIdStr}_`) ||
            fileName.includes(`_${userIdStr}.`)
          );

          console.log(`📄 File: ${file.name} - Belongs to user ${userIdStr}:`, belongsToUser);
          return belongsToUser;
        });

        console.log('👤 User-specific files found:', userFiles.length);
        console.log('👤 User files:', userFiles.map(f => f.name));

        // If no user-specific files found, don't show random documents
        if (userFiles.length === 0) {
          console.log('ℹ️ No documents found for current user');
          setDocuments([]);
          return;
        }

        for (const file of userFiles) {
          const { data: { publicUrl } } = serviceClient.storage
            .from('proof')
            .getPublicUrl(file.name);

          // Determine document type from filename
          let type: 'id_document' | 'selfie' | 'proof_of_address' = 'id_document';
          const fileName = file.name.toLowerCase();

          if (fileName.includes('selfie') || fileName.includes('facial') || fileName.includes('verification')) {
            type = 'selfie';
          } else if (fileName.includes('address') || fileName.includes('proof') || fileName.includes('residence')) {
            type = 'proof_of_address';
          }

          documentList.push({
            id: file.id || file.name,
            type,
            fileName: file.name,
            url: publicUrl,
            uploadedAt: file.created_at || file.updated_at || new Date().toISOString()
          });
        }
      }

      console.log('📋 Final document list for user:', documentList);
      setDocuments(documentList);
    } catch (error) {
      console.error('❌ Error fetching KYC documents:', error);
      setDocuments([]); // Clear documents on error
    } finally {
      setLoadingDocuments(false);
    }
  };
  const getStatusConfig = () => {
    switch (status) {
      case 'completed':
        return {
          icon: '✅',
          title: 'KYC Verification Complete',
          description: 'Your identity has been successfully verified',
          bgColor: 'bg-green-900/30',
          borderColor: 'border-green-700',
          textColor: 'text-green-400',
          descColor: 'text-green-200'
        };
      case 'rejected':
        return {
          icon: '❌',
          title: 'KYC Verification Rejected',
          description: 'Please review and resubmit your information',
          bgColor: 'bg-red-900/30',
          borderColor: 'border-red-700',
          textColor: 'text-red-400',
          descColor: 'text-red-200'
        };
      case 'expired':
        return {
          icon: '⏰',
          title: 'KYC Verification Expired',
          description: 'Your verification has expired and needs to be renewed',
          bgColor: 'bg-orange-900/30',
          borderColor: 'border-orange-700',
          textColor: 'text-orange-400',
          descColor: 'text-orange-200'
        };
      case 'pending_approval':
        return {
          icon: '📋',
          title: 'KYC Submitted - Pending Admin Approval',
          description: 'Your KYC information has been submitted and is being reviewed by our team. You will be notified once approved.',
          bgColor: 'bg-blue-900/30',
          borderColor: 'border-blue-700',
          textColor: 'text-blue-400',
          descColor: 'text-blue-200'
        };
      default: // pending
        return {
          icon: '⏳',
          title: 'KYC Verification Required',
          description: 'Complete your identity verification to access all features',
          bgColor: 'bg-yellow-900/30',
          borderColor: 'border-yellow-700',
          textColor: 'text-yellow-400',
          descColor: 'text-yellow-200'
        };
    }
  };

  const config = getStatusConfig();

  const renderDetailedKYCView = () => {
    if (!kycData) {
      console.warn('⚠️ No KYC data available for detailed view');
      return null;
    }

    console.log('📋 Rendering detailed view for user:', kycData.user_id, kycData.full_legal_name);

    return (
      <div className="mt-6 space-y-6">
        {/* Personal Information Section */}
        <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700">
          <h4 className="text-white font-semibold text-lg mb-4 flex items-center">
            <span className="mr-2">👤</span>
            Personal Information
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-400">First Name:</span>
              <span className="text-white ml-2">{kycData.first_name}</span>
            </div>
            <div>
              <span className="text-gray-400">Last Name:</span>
              <span className="text-white ml-2">{kycData.last_name}</span>
            </div>
            <div>
              <span className="text-gray-400">Full Legal Name:</span>
              <span className="text-white ml-2">{kycData.full_legal_name}</span>
            </div>
            <div>
              <span className="text-gray-400">Nationality:</span>
              <span className="text-white ml-2">{kycData.nationality || 'Not specified'}</span>
            </div>
            <div>
              <span className="text-gray-400">Phone Number:</span>
              <span className="text-white ml-2">{kycData.phone_number}</span>
            </div>
            <div>
              <span className="text-gray-400">Email Address:</span>
              <span className="text-white ml-2">{kycData.email_address}</span>
            </div>
          </div>
        </div>

        {/* Address Information Section */}
        <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700">
          <h4 className="text-white font-semibold text-lg mb-4 flex items-center">
            <span className="mr-2">🏠</span>
            Address Information
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="col-span-2">
              <span className="text-gray-400">Street Address:</span>
              <span className="text-white ml-2">{kycData.street_address}</span>
            </div>
            <div>
              <span className="text-gray-400">City:</span>
              <span className="text-white ml-2">{kycData.city}</span>
            </div>
            <div>
              <span className="text-gray-400">Province/State:</span>
              <span className="text-white ml-2">{kycData.province || 'Not specified'}</span>
            </div>
            <div>
              <span className="text-gray-400">Postal Code:</span>
              <span className="text-white ml-2">{kycData.postal_code}</span>
            </div>
            <div>
              <span className="text-gray-400">Country:</span>
              <span className="text-white ml-2">{kycData.country_name}</span>
            </div>
          </div>
        </div>

        {/* Document Information Section */}
        <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700">
          <h4 className="text-white font-semibold text-lg mb-4 flex items-center">
            <span className="mr-2">📄</span>
            Document Information
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-400">ID Document Type:</span>
              <span className="text-white ml-2">
                {kycData.id_type === 'national_id' ? 'National ID' :
                 kycData.id_type === 'passport' ? 'Passport' :
                 kycData.id_type === 'drivers_license' ? 'Driver\'s License' :
                 kycData.id_type || 'Not specified'}
              </span>
            </div>
            <div>
              <span className="text-gray-400">ID Number:</span>
              <span className="text-white ml-2">
                {(() => {
                  try {
                    if (kycData.id_number_encrypted && typeof kycData.id_number_encrypted === 'string') {
                      // Try to decode the encrypted ID number
                      const decoded = atob(kycData.id_number_encrypted);
                      return `****${decoded.slice(-4)}`;
                    }
                    return '****';
                  } catch (error) {
                    // If decoding fails, show partial from hash or just stars
                    return '****';
                  }
                })()}
              </span>
            </div>
            <div>
              <span className="text-gray-400">Data Consent:</span>
              <span className={`ml-2 ${kycData.data_consent_given ? 'text-green-400' : 'text-red-400'}`}>
                {kycData.data_consent_given ? '✓ Given' : '✗ Not Given'}
              </span>
            </div>
            <div>
              <span className="text-gray-400">Privacy Policy:</span>
              <span className={`ml-2 ${kycData.privacy_policy_accepted ? 'text-green-400' : 'text-red-400'}`}>
                {kycData.privacy_policy_accepted ? '✓ Accepted' : '✗ Not Accepted'}
              </span>
            </div>
          </div>
        </div>

        {/* Status Information Section */}
        <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700">
          <h4 className="text-white font-semibold text-lg mb-4 flex items-center">
            <span className="mr-2">📊</span>
            Status Information
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-400">KYC Status:</span>
              <span className={`ml-2 font-medium ${
                status === 'completed' ? 'text-green-400' :
                status === 'pending_approval' ? 'text-blue-400' :
                status === 'rejected' ? 'text-red-400' :
                status === 'expired' ? 'text-orange-400' :
                'text-yellow-400'
              }`}>
                {status === 'completed' ? 'Completed' :
                 status === 'pending_approval' ? 'Pending Approval' :
                 status === 'rejected' ? 'Rejected' :
                 status === 'expired' ? 'Expired' :
                 'Pending'}
              </span>
            </div>
            <div>
              <span className="text-gray-400">Submission Date:</span>
              <span className="text-white ml-2">
                {kycData.created_at ? new Date(kycData.created_at).toLocaleDateString() : 'Not available'}
              </span>
            </div>
            <div>
              <span className="text-gray-400">
                {status === 'completed' ? 'Completion Date:' : 'Last Updated:'}
              </span>
              <span className="text-white ml-2">
                {kycData.kyc_completed_at ? new Date(kycData.kyc_completed_at).toLocaleDateString() :
                 kycData.updated_at ? new Date(kycData.updated_at).toLocaleDateString() : 'Not available'}
              </span>
            </div>
            <div>
              <span className="text-gray-400">Certificate Status:</span>
              <span className="text-white ml-2">
                {kycData.certificate_requested ? 'Requested' : 'Not Requested'}
              </span>
            </div>
          </div>
        </div>

        {/* Document Images Section */}
        {(status === 'completed' || status === 'pending_approval') && (
          <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700">
            <h4 className="text-white font-semibold text-lg mb-4 flex items-center">
              <span className="mr-2">🖼️</span>
              Uploaded Documents
            </h4>

            {loadingDocuments ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <span className="text-gray-400 ml-3">Loading documents...</span>
              </div>
            ) : documents.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {documents.map((doc) => (
                  <div key={doc.id} className="bg-gray-700/50 rounded-lg p-3 border border-gray-600">
                    <div className="aspect-video bg-gray-600 rounded-lg mb-3 overflow-hidden cursor-pointer hover:opacity-80 transition-opacity"
                         onClick={() => setSelectedImage({ url: doc.url, title: getDocumentTitle(doc.type) })}>
                      <img
                        src={doc.url}
                        alt={getDocumentTitle(doc.type)}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjMzc0MTUxIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iNjAiIGZpbGw9IiM5Q0EzQUYiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIE5vdCBBdmFpbGFibGU8L3RleHQ+Cjwvc3ZnPg==';
                        }}
                      />
                    </div>
                    <div className="text-sm">
                      <div className="text-white font-medium mb-1">
                        {getDocumentTitle(doc.type)}
                      </div>
                      <div className="text-gray-400 text-xs">
                        Uploaded: {new Date(doc.uploadedAt).toLocaleDateString()}
                      </div>
                      <div className="text-gray-500 text-xs mt-1 truncate">
                        {doc.fileName}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <span className="text-gray-400 text-sm">No documents found</span>
                <p className="text-gray-500 text-xs mt-1">
                  Documents may not be available for viewing or may be stored in a different location.
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  const getDocumentTitle = (type: string): string => {
    switch (type) {
      case 'id_document':
        return 'Government ID Document';
      case 'selfie':
        return 'Identity Verification Photo';
      case 'proof_of_address':
        return 'Proof of Address';
      default:
        return 'Document';
    }
  };

  return (
    <div className={`${config.bgColor} rounded-lg p-6 border ${config.borderColor}`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-4">
          <span className="text-3xl">{config.icon}</span>
          <div className="flex-1">
            <h3 className={`${config.textColor} font-semibold text-lg mb-1`}>
              {config.title}
            </h3>
            <p className={`${config.descColor} text-sm mb-4`}>
              {config.description}
            </p>

            {/* Simple KYC Summary for completed and pending_approval status */}
            {(status === 'completed' || status === 'pending_approval') && kycData && (
              <div className="space-y-4">
                {/* User ID Debug Info (remove in production) */}
                <div className="text-xs text-gray-500 bg-gray-800/30 p-2 rounded border">
                  <div>🔍 Debug: User ID: {kycData.user_id}</div>
                  <div>👤 Name: {kycData.full_legal_name}</div>
                  <div>📧 Email: {kycData.email_address}</div>
                  <div>📅 Submitted: {new Date(kycData.kyc_completed_at).toLocaleString()}</div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                  <div>
                    <span className="text-gray-400">Full Name:</span>
                    <span className="text-white ml-2">{kycData.full_legal_name}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Country:</span>
                    <span className="text-white ml-2">{kycData.country_name}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">
                      {status === 'completed' ? 'Verified:' : 'Submitted:'}
                    </span>
                    <span className="text-white ml-2">
                      {new Date(kycData.kyc_completed_at).toLocaleDateString()}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-400">Status:</span>
                    <span className={`ml-2 font-medium ${
                      status === 'completed' ? 'text-green-400' : 'text-blue-400'
                    }`}>
                      {status === 'completed' ? '✓ Admin Approved' : '⏳ Pending Review'}
                    </span>
                  </div>
                </div>

                {/* Toggle for detailed view */}
                <div className="flex justify-center pt-2">
                  <button
                    onClick={() => setShowDetailedView(!showDetailedView)}
                    className="px-4 py-2 bg-blue-600/20 text-blue-400 rounded-lg hover:bg-blue-600/30 text-sm font-medium border border-blue-600/30 transition-colors"
                  >
                    {showDetailedView ? '📋 Hide Detailed Information' : '📋 View Detailed Information & Documents'}
                  </button>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex space-x-3">
              {status === 'pending' && (
                <div className="px-4 py-2 bg-blue-900/20 text-blue-400 rounded-lg text-sm font-medium border border-blue-700/50">
                  💡 Click "KYC Verification" in the left menu to complete your verification
                </div>
              )}

              {status === 'pending_approval' && (
                <div className="px-4 py-2 bg-blue-900/30 text-blue-400 rounded-lg text-sm font-medium border border-blue-700">
                  ⏳ Awaiting Admin Review
                </div>
              )}

              {(status === 'completed' || status === 'rejected' || status === 'expired') && (
                <button
                  onClick={() => {
                    if (onEditKYC) {
                      onEditKYC();
                    } else {
                      // Fallback: redirect to dashboard KYC section
                      window.location.href = '/dashboard';
                    }
                  }}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 text-sm font-medium"
                >
                  {status === 'completed' ? 'Edit Information' : 'Resubmit KYC'}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Additional Information */}
      {status === 'pending' && (
        <div className="mt-4 p-3 bg-blue-900/30 rounded-lg border border-blue-700">
          <h4 className="text-blue-400 font-medium text-sm mb-2">What you'll need:</h4>
          <ul className="text-blue-200 text-xs space-y-1">
            <li>• Valid government-issued ID (National ID or Passport)</li>
            <li>• Current residential address</li>
            <li>• Phone number and email address</li>
            <li>• 5-10 minutes to complete the form</li>
          </ul>
        </div>
      )}

      {status === 'rejected' && (
        <div className="mt-4 p-3 bg-red-900/30 rounded-lg border border-red-700">
          <h4 className="text-red-400 font-medium text-sm mb-2">Common rejection reasons:</h4>
          <ul className="text-red-200 text-xs space-y-1">
            <li>• Unclear or blurry document images</li>
            <li>• Information doesn't match official documents</li>
            <li>• Incomplete or missing required fields</li>
            <li>• Document has expired or is invalid</li>
          </ul>
        </div>
      )}

      {status === 'completed' && (
        <div className="mt-4 p-3 bg-green-900/30 rounded-lg border border-green-700">
          <div className="flex items-center space-x-2">
            <span className="text-green-400">🎉</span>
            <p className="text-green-200 text-sm">
              <strong>Congratulations!</strong> You can now access and download your share certificates.
            </p>
          </div>
        </div>
      )}

      {/* Detailed KYC Information View */}
      {showDetailedView && renderDetailedKYCView()}

      {/* Image Modal */}
      <ImageModal
        isOpen={!!selectedImage}
        imageUrl={selectedImage?.url || ''}
        title={selectedImage?.title || ''}
        onClose={() => setSelectedImage(null)}
      />
    </div>
  );
};

export default KYCStatusCard;
