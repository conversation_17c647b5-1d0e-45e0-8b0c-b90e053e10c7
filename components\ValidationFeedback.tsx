import React from 'react';
import { ValidationResult } from '../lib/realtimeValidation';

interface ValidationFeedbackProps {
  validation: ValidationResult | null;
  className?: string;
}

export const ValidationFeedback: React.FC<ValidationFeedbackProps> = ({ 
  validation, 
  className = '' 
}) => {
  if (!validation) {
    return null;
  }

  const getIconAndStyles = () => {
    switch (validation.type) {
      case 'success':
        return {
          icon: (
            <svg className="validation-feedback-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          ),
          className: 'validation-feedback-success'
        };
      case 'error':
        return {
          icon: (
            <svg className="validation-feedback-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          ),
          className: 'validation-feedback-error'
        };
      case 'checking':
        return {
          icon: (
            <div className="loading-spinner"></div>
          ),
          className: 'validation-feedback-checking'
        };
      default:
        return {
          icon: (
            <svg className="validation-feedback-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          ),
          className: 'validation-feedback-default'
        };
    }
  };

  const { icon, className: feedbackClass } = getIconAndStyles();

  return (
    <div className={`validation-feedback ${feedbackClass} ${className}`}>
      <span className="validation-feedback-content">
        {icon}
        <span className="validation-feedback-message">{validation.message}</span>
      </span>
    </div>
  );
};

interface ValidationInputProps {
  id: string;
  name: string;
  type: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  validation: ValidationResult | null;
  placeholder?: string;
  autoComplete?: string;
  className?: string;
  label: string;
  required?: boolean;
}

export const ValidationInput: React.FC<ValidationInputProps> = ({
  id,
  name,
  type,
  value,
  onChange,
  validation,
  placeholder,
  autoComplete,
  className = '',
  label,
  required = false
}) => {
  const getValidationClass = () => {
    if (!validation) return '';

    switch (validation.type) {
      case 'success':
        return 'validation-success';
      case 'error':
        return 'validation-error';
      case 'checking':
        return 'validation-checking';
      default:
        return '';
    }
  };

  const getIcon = () => {
    if (!validation || validation.isChecking) return null;

    if (validation.isValid) {
      return (
        <div className="validation-icon validation-icon-success">
          <svg className="validation-svg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
      );
    } else {
      return (
        <div className="validation-icon validation-icon-error">
          <svg className="validation-svg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </div>
      );
    }
  };

  return (
    <div className="validation-input-container">
      {label && (
        <label htmlFor={id} className="form-label">
          {label} {required && <span style={{color: 'var(--error)'}}>*</span>}
        </label>
      )}
      <div className="validation-input-wrapper">
        <input
          id={id}
          name={name}
          type={type}
          value={value}
          onChange={onChange}
          autoComplete={autoComplete}
          className={`form-input validation-input ${getValidationClass()} ${className}`}
          placeholder={placeholder}
        />
        {getIcon()}
        {validation?.isChecking && (
          <div className="validation-icon validation-icon-checking">
            <div className="loading-spinner"></div>
          </div>
        )}
      </div>
      <ValidationFeedback validation={validation} />
    </div>
  );
};
