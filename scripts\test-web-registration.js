import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

// Test the web registration flow using the same functions as the web app
async function testWebRegistration() {
  try {
    console.log('🌐 Testing web registration flow...');
    
    const testUser = {
      email: `webtest${Date.now()}@example.com`,
      password: 'WebTest123',
      confirmPassword: 'WebTest123',
      fullName: 'Web Test User',
      sponsorUsername: 'TTTFOUNDER'
    };
    
    console.log('📧 Test user data:', {
      email: testUser.email,
      fullName: testUser.fullName,
      sponsorUsername: testUser.sponsorUsername
    });
    
    // Clean up any existing test user (both database and auth)
    const { data: existingUser } = await supabase
      .from('users')
      .select('id')
      .eq('email', testUser.email)
      .single();

    if (existingUser) {
      console.log('🗑️ Cleaning up existing database user...');
      await supabase.from('referrals').delete().eq('referred_id', existingUser.id);
      await supabase.from('users').delete().eq('id', existingUser.id);
    }

    // Also clean up any existing auth user
    try {
      const { data: authUsers } = await supabase.auth.admin.listUsers();
      const existingAuthUser = authUsers.users.find(u => u.email === testUser.email);
      if (existingAuthUser) {
        console.log('🗑️ Cleaning up existing auth user...');
        await supabase.auth.admin.deleteUser(existingAuthUser.id);
      }
    } catch (error) {
      console.log('⚠️ Could not clean up auth user (this is okay):', error.message);
    }
    
    // Test email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(testUser.email)) {
      console.error('❌ Email validation failed');
      return;
    }
    console.log('✅ Email validation passed');
    
    // Test password validation
    const passwordValid = testUser.password.length >= 8 && 
                         /[a-z]/.test(testUser.password) && 
                         /[A-Z]/.test(testUser.password) && 
                         /\d/.test(testUser.password);
    
    if (!passwordValid) {
      console.error('❌ Password validation failed');
      return;
    }
    console.log('✅ Password validation passed');
    
    // Test password confirmation
    if (testUser.password !== testUser.confirmPassword) {
      console.error('❌ Password confirmation failed');
      return;
    }
    console.log('✅ Password confirmation passed');
    
    // Test sponsor validation
    const { data: sponsor, error: sponsorError } = await supabase
      .from('users')
      .select('*')
      .eq('username', testUser.sponsorUsername)
      .single();
    
    if (sponsorError || !sponsor) {
      console.error('❌ Sponsor validation failed:', sponsorError);
      return;
    }
    console.log('✅ Sponsor validation passed:', sponsor.username);
    
    // Test Supabase auth registration (this is what the web app does)
    console.log('🔐 Testing Supabase auth registration...');
    
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: testUser.email,
      password: testUser.password,
      options: {
        data: {
          full_name: testUser.fullName,
          username: testUser.email.split('@')[0],
          is_email_user: true
        }
      }
    });
    
    if (authError) {
      console.error('❌ Supabase auth registration failed:', authError.message);
      return;
    }
    
    console.log('✅ Supabase auth registration successful');
    console.log('   User ID:', authData.user.id);
    console.log('   Email:', authData.user.email);
    
    // The web app would then create the database user and referral
    // Let's simulate that process
    console.log('📝 Creating database user record...');
    
    // Hash password using the same method as the web app
    const encoder = new TextEncoder();
    const data = encoder.encode(testUser.password + 'aureus_salt_2024');
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const passwordHash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    
    const { data: dbUser, error: dbError } = await supabase
      .from('users')
      .insert({
        username: testUser.email.split('@')[0],
        email: testUser.email,
        password_hash: passwordHash,
        full_name: testUser.fullName,
        is_active: true,
        is_verified: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (dbError) {
      console.error('❌ Database user creation failed:', dbError);
      return;
    }
    
    console.log('✅ Database user created successfully:', dbUser.username);
    
    // Create referral relationship
    console.log('🔗 Creating referral relationship...');
    
    const referralCode = `${sponsor.username}_${dbUser.id}_${Date.now()}`;
    
    const { data: referral, error: referralError } = await supabase
      .from('referrals')
      .insert({
        referrer_id: sponsor.id,
        referred_id: dbUser.id,
        referral_code: referralCode,
        commission_rate: 15.00,
        status: 'active',
        created_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (referralError) {
      console.error('❌ Referral creation failed:', referralError);
      return;
    }
    
    console.log('✅ Referral relationship created:', referralCode);
    
    // Test login with the newly created user
    console.log('🔐 Testing login with new user...');
    
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: testUser.email,
      password: testUser.password
    });
    
    if (loginError) {
      console.error('❌ Login test failed:', loginError.message);
      return;
    }
    
    console.log('✅ Login test successful');
    console.log('   Logged in as:', loginData.user.email);
    
    console.log('\n🎉 Web registration flow test completed successfully!');
    console.log('📊 Summary:');
    console.log('  - Form validation working ✅');
    console.log('  - Sponsor lookup working ✅');
    console.log('  - Supabase auth registration working ✅');
    console.log('  - Database user creation working ✅');
    console.log('  - Referral relationship creation working ✅');
    console.log('  - Login after registration working ✅');
    
    // Clean up
    console.log('🧹 Cleaning up test data...');
    await supabase.from('referrals').delete().eq('referred_id', dbUser.id);
    await supabase.from('users').delete().eq('id', dbUser.id);
    console.log('✅ Test data cleaned up');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

testWebRegistration();
