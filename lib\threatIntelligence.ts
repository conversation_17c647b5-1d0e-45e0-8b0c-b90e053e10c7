/**
 * ADVANCED THREAT INTELLIGENCE SYSTEM
 * 
 * This module provides IP reputation checking, geolocation analysis,
 * known threat database integration, and real-time threat detection.
 */

import { supabase } from './supabase';

interface ThreatIntelligenceResult {
  isThreat: boolean;
  threatLevel: 'NONE' | 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  threatTypes: string[];
  confidence: number; // 0-100
  sources: string[];
  metadata: any;
}

interface IPReputationData {
  ip: string;
  reputation: 'CLEAN' | 'SUSPICIOUS' | 'MALICIOUS';
  threatTypes: string[];
  lastSeen: Date;
  sources: string[];
  geolocation: {
    country: string;
    city: string;
    region: string;
    isp: string;
    isVpn: boolean;
    isTor: boolean;
    isProxy: boolean;
  };
}

interface GeolocationRisk {
  country: string;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  riskFactors: string[];
  isHighRisk: boolean;
  isSanctioned: boolean;
  fraudScore: number;
}

class ThreatIntelligenceEngine {
  private ipReputationCache: Map<string, IPReputationData> = new Map();
  private geolocationCache: Map<string, GeolocationRisk> = new Map();
  
  private readonly HIGH_RISK_COUNTRIES = [
    'CN', 'RU', 'KP', 'IR', 'SY', 'AF', 'IQ', 'LY', 'SO', 'SD'
  ];

  private readonly SANCTIONED_COUNTRIES = [
    'KP', 'IR', 'SY', 'CU', 'MM'
  ];

  private readonly KNOWN_MALICIOUS_IPS = new Set([
    // This would be populated from threat intelligence feeds
    '*************', // Example malicious IP
  ]);

  private readonly VPN_PROVIDERS = [
    'nordvpn', 'expressvpn', 'surfshark', 'cyberghost', 'protonvpn',
    'mullvad', 'windscribe', 'tunnelbear', 'hotspotshield'
  ];

  /**
   * Comprehensive threat analysis for IP address
   */
  async analyzeThreat(
    ipAddress: string,
    userAgent?: string,
    additionalContext?: any
  ): Promise<ThreatIntelligenceResult> {
    try {
      console.log(`🔍 Analyzing threat intelligence for IP: ${ipAddress}`);

      const threatTypes: string[] = [];
      const sources: string[] = [];
      let threatLevel: 'NONE' | 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'NONE';
      let confidence = 0;
      let isThreat = false;

      // 1. IP Reputation Check
      const ipReputation = await this.checkIPReputation(ipAddress);
      if (ipReputation.reputation !== 'CLEAN') {
        isThreat = true;
        threatTypes.push(...ipReputation.threatTypes);
        sources.push('IP_REPUTATION');
        confidence += 30;
        
        if (ipReputation.reputation === 'MALICIOUS') {
          threatLevel = 'HIGH';
        } else {
          threatLevel = 'MEDIUM';
        }
      }

      // 2. Geolocation Risk Assessment
      const geoRisk = await this.assessGeolocationRisk(ipAddress);
      if (geoRisk.isHighRisk) {
        isThreat = true;
        threatTypes.push(...geoRisk.riskFactors);
        sources.push('GEOLOCATION');
        confidence += 20;
        
        if (geoRisk.riskLevel === 'CRITICAL') {
          threatLevel = 'CRITICAL';
        } else if (threatLevel === 'NONE') {
          threatLevel = geoRisk.riskLevel;
        }
      }

      // 3. VPN/Proxy Detection
      const vpnDetection = await this.detectVPNProxy(ipAddress, userAgent);
      if (vpnDetection.isVPN || vpnDetection.isProxy) {
        threatTypes.push(vpnDetection.isVPN ? 'VPN_USAGE' : 'PROXY_USAGE');
        sources.push('VPN_DETECTION');
        confidence += 15;
        
        if (threatLevel === 'NONE') {
          threatLevel = 'LOW';
        }
      }

      // 4. Tor Network Detection
      const torDetection = await this.detectTorNetwork(ipAddress);
      if (torDetection.isTor) {
        isThreat = true;
        threatTypes.push('TOR_NETWORK');
        sources.push('TOR_DETECTION');
        confidence += 25;
        threatLevel = 'HIGH';
      }

      // 5. Known Threat Database Check
      const knownThreat = await this.checkKnownThreats(ipAddress);
      if (knownThreat.isThreat) {
        isThreat = true;
        threatTypes.push(...knownThreat.threatTypes);
        sources.push('THREAT_DATABASE');
        confidence += 40;
        threatLevel = 'CRITICAL';
      }

      // 6. Behavioral Analysis
      const behaviorAnalysis = await this.analyzeBehavioralThreats(ipAddress, additionalContext);
      if (behaviorAnalysis.isThreat) {
        isThreat = true;
        threatTypes.push(...behaviorAnalysis.threatTypes);
        sources.push('BEHAVIORAL_ANALYSIS');
        confidence += 20;
      }

      // Log threat intelligence result
      if (isThreat) {
        await this.logThreatIntelligence(ipAddress, threatLevel, threatTypes, {
          confidence,
          sources,
          userAgent,
          ...additionalContext
        });
      }

      console.log(`🔍 Threat analysis complete: ${threatLevel} (${confidence}% confidence)`);
      
      return {
        isThreat,
        threatLevel,
        threatTypes,
        confidence,
        sources,
        metadata: {
          ipReputation,
          geoRisk,
          vpnDetection,
          torDetection,
          knownThreat,
          behaviorAnalysis
        }
      };

    } catch (error) {
      console.error('❌ Threat intelligence analysis error:', error);
      return {
        isThreat: false,
        threatLevel: 'NONE',
        threatTypes: [],
        confidence: 0,
        sources: [],
        metadata: { error: error.message }
      };
    }
  }

  /**
   * Check IP reputation against multiple sources
   */
  private async checkIPReputation(ipAddress: string): Promise<IPReputationData> {
    try {
      // Check cache first
      if (this.ipReputationCache.has(ipAddress)) {
        const cached = this.ipReputationCache.get(ipAddress)!;
        // Use cache if less than 1 hour old
        if (Date.now() - cached.lastSeen.getTime() < 60 * 60 * 1000) {
          return cached;
        }
      }

      const threatTypes: string[] = [];
      const sources: string[] = [];
      let reputation: 'CLEAN' | 'SUSPICIOUS' | 'MALICIOUS' = 'CLEAN';

      // 1. Check against known malicious IPs
      if (this.KNOWN_MALICIOUS_IPS.has(ipAddress)) {
        reputation = 'MALICIOUS';
        threatTypes.push('KNOWN_MALICIOUS');
        sources.push('INTERNAL_BLACKLIST');
      }

      // 2. Check for common attack patterns in IP
      if (this.isCommonAttackIP(ipAddress)) {
        reputation = 'SUSPICIOUS';
        threatTypes.push('ATTACK_PATTERN');
        sources.push('PATTERN_ANALYSIS');
      }

      // 3. Get geolocation data
      const geolocation = await this.getIPGeolocation(ipAddress);

      // 4. Check for hosting providers (often used for attacks)
      if (this.isHostingProvider(geolocation.isp)) {
        threatTypes.push('HOSTING_PROVIDER');
        sources.push('ISP_ANALYSIS');
        if (reputation === 'CLEAN') {
          reputation = 'SUSPICIOUS';
        }
      }

      const reputationData: IPReputationData = {
        ip: ipAddress,
        reputation,
        threatTypes,
        lastSeen: new Date(),
        sources,
        geolocation
      };

      // Cache the result
      this.ipReputationCache.set(ipAddress, reputationData);

      return reputationData;

    } catch (error) {
      console.error('❌ IP reputation check error:', error);
      return {
        ip: ipAddress,
        reputation: 'CLEAN',
        threatTypes: [],
        lastSeen: new Date(),
        sources: [],
        geolocation: {
          country: 'Unknown',
          city: 'Unknown',
          region: 'Unknown',
          isp: 'Unknown',
          isVpn: false,
          isTor: false,
          isProxy: false
        }
      };
    }
  }

  /**
   * Assess geolocation-based risk
   */
  private async assessGeolocationRisk(ipAddress: string): Promise<GeolocationRisk> {
    try {
      const geolocation = await this.getIPGeolocation(ipAddress);
      const riskFactors: string[] = [];
      let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'LOW';
      let fraudScore = 0;

      // Check if country is high-risk
      if (this.HIGH_RISK_COUNTRIES.includes(geolocation.country)) {
        riskFactors.push('HIGH_RISK_COUNTRY');
        riskLevel = 'HIGH';
        fraudScore += 40;
      }

      // Check if country is sanctioned
      const isSanctioned = this.SANCTIONED_COUNTRIES.includes(geolocation.country);
      if (isSanctioned) {
        riskFactors.push('SANCTIONED_COUNTRY');
        riskLevel = 'CRITICAL';
        fraudScore += 60;
      }

      // Check for VPN/Proxy indicators
      if (geolocation.isVpn) {
        riskFactors.push('VPN_DETECTED');
        fraudScore += 20;
      }

      if (geolocation.isProxy) {
        riskFactors.push('PROXY_DETECTED');
        fraudScore += 25;
      }

      if (geolocation.isTor) {
        riskFactors.push('TOR_DETECTED');
        riskLevel = 'HIGH';
        fraudScore += 50;
      }

      const isHighRisk = riskLevel !== 'LOW' || fraudScore > 30;

      return {
        country: geolocation.country,
        riskLevel,
        riskFactors,
        isHighRisk,
        isSanctioned,
        fraudScore
      };

    } catch (error) {
      console.error('❌ Geolocation risk assessment error:', error);
      return {
        country: 'Unknown',
        riskLevel: 'LOW',
        riskFactors: [],
        isHighRisk: false,
        isSanctioned: false,
        fraudScore: 0
      };
    }
  }

  /**
   * Detect VPN/Proxy usage
   */
  private async detectVPNProxy(ipAddress: string, userAgent?: string): Promise<{
    isVPN: boolean;
    isProxy: boolean;
    provider?: string;
    confidence: number;
  }> {
    try {
      const geolocation = await this.getIPGeolocation(ipAddress);
      let isVPN = geolocation.isVpn;
      let isProxy = geolocation.isProxy;
      let provider: string | undefined;
      let confidence = 0;

      // Check ISP name for VPN providers
      if (geolocation.isp) {
        const ispLower = geolocation.isp.toLowerCase();
        const vpnProvider = this.VPN_PROVIDERS.find(vpn => ispLower.includes(vpn));
        
        if (vpnProvider) {
          isVPN = true;
          provider = vpnProvider;
          confidence = 90;
        }
      }

      // Additional heuristics
      if (userAgent) {
        // Check for VPN-related user agent patterns
        if (userAgent.includes('VPN') || userAgent.includes('Proxy')) {
          isProxy = true;
          confidence = Math.max(confidence, 70);
        }
      }

      return { isVPN, isProxy, provider, confidence };

    } catch (error) {
      console.error('❌ VPN/Proxy detection error:', error);
      return { isVPN: false, isProxy: false, confidence: 0 };
    }
  }

  /**
   * Detect Tor network usage
   */
  private async detectTorNetwork(ipAddress: string): Promise<{
    isTor: boolean;
    confidence: number;
  }> {
    try {
      // In a real implementation, this would check against Tor exit node lists
      // For now, we'll use basic heuristics
      
      const geolocation = await this.getIPGeolocation(ipAddress);
      let isTor = geolocation.isTor;
      let confidence = 0;

      // Check if ISP indicates Tor usage
      if (geolocation.isp && geolocation.isp.toLowerCase().includes('tor')) {
        isTor = true;
        confidence = 95;
      }

      return { isTor, confidence };

    } catch (error) {
      console.error('❌ Tor detection error:', error);
      return { isTor: false, confidence: 0 };
    }
  }

  /**
   * Check against known threat databases
   */
  private async checkKnownThreats(ipAddress: string): Promise<{
    isThreat: boolean;
    threatTypes: string[];
    confidence: number;
  }> {
    try {
      const threatTypes: string[] = [];
      let confidence = 0;

      // Check internal threat database
      const { data: internalThreats } = await supabase
        .from('threat_intelligence')
        .select('*')
        .eq('ip_address', ipAddress)
        .eq('is_active', true);

      if (internalThreats && internalThreats.length > 0) {
        threatTypes.push(...internalThreats.map(threat => threat.threat_type));
        confidence = 90;
      }

      // Check for recent attack attempts from this IP
      const { data: recentAttacks } = await supabase
        .from('admin_audit_logs')
        .select('*')
        .like('action', '%ATTACK%')
        .eq('metadata->>ipAddress', ipAddress)
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

      if (recentAttacks && recentAttacks.length > 0) {
        threatTypes.push('RECENT_ATTACK_ATTEMPTS');
        confidence = Math.max(confidence, 70);
      }

      return {
        isThreat: threatTypes.length > 0,
        threatTypes,
        confidence
      };

    } catch (error) {
      console.error('❌ Known threats check error:', error);
      return { isThreat: false, threatTypes: [], confidence: 0 };
    }
  }

  /**
   * Analyze behavioral threats
   */
  private async analyzeBehavioralThreats(ipAddress: string, context?: any): Promise<{
    isThreat: boolean;
    threatTypes: string[];
    confidence: number;
  }> {
    try {
      const threatTypes: string[] = [];
      let confidence = 0;

      // Check for rapid requests from same IP
      const { data: recentRequests } = await supabase
        .from('admin_audit_logs')
        .select('created_at')
        .eq('metadata->>ipAddress', ipAddress)
        .gte('created_at', new Date(Date.now() - 5 * 60 * 1000).toISOString());

      if (recentRequests && recentRequests.length > 50) {
        threatTypes.push('HIGH_REQUEST_VOLUME');
        confidence = 80;
      }

      // Check for failed authentication attempts
      const { data: failedAuth } = await supabase
        .from('admin_audit_logs')
        .select('*')
        .like('action', '%FAILED%')
        .eq('metadata->>ipAddress', ipAddress)
        .gte('created_at', new Date(Date.now() - 60 * 60 * 1000).toISOString());

      if (failedAuth && failedAuth.length > 10) {
        threatTypes.push('BRUTE_FORCE_ATTEMPT');
        confidence = Math.max(confidence, 85);
      }

      return {
        isThreat: threatTypes.length > 0,
        threatTypes,
        confidence
      };

    } catch (error) {
      console.error('❌ Behavioral threat analysis error:', error);
      return { isThreat: false, threatTypes: [], confidence: 0 };
    }
  }

  /**
   * Get IP geolocation data
   */
  private async getIPGeolocation(ipAddress: string): Promise<{
    country: string;
    city: string;
    region: string;
    isp: string;
    isVpn: boolean;
    isTor: boolean;
    isProxy: boolean;
  }> {
    try {
      // In a real implementation, this would call a geolocation API
      // For now, we'll return mock data based on IP patterns
      
      if (ipAddress.startsWith('192.168.') || ipAddress.startsWith('10.') || ipAddress.startsWith('172.')) {
        return {
          country: 'ZA',
          city: 'Cape Town',
          region: 'Western Cape',
          isp: 'Local Network',
          isVpn: false,
          isTor: false,
          isProxy: false
        };
      }

      // Mock geolocation data
      return {
        country: 'US',
        city: 'New York',
        region: 'NY',
        isp: 'Example ISP',
        isVpn: false,
        isTor: false,
        isProxy: false
      };

    } catch (error) {
      console.error('❌ Geolocation lookup error:', error);
      return {
        country: 'Unknown',
        city: 'Unknown',
        region: 'Unknown',
        isp: 'Unknown',
        isVpn: false,
        isTor: false,
        isProxy: false
      };
    }
  }

  /**
   * Helper methods
   */
  private isCommonAttackIP(ipAddress: string): boolean {
    // Check for common attack IP patterns
    const attackPatterns = [
      /^185\./, // Common botnet range
      /^194\./, // Common attack range
      /^46\./, // Common VPS range used for attacks
    ];

    return attackPatterns.some(pattern => pattern.test(ipAddress));
  }

  private isHostingProvider(isp: string): boolean {
    const hostingProviders = [
      'amazon', 'aws', 'google', 'microsoft', 'azure', 'digitalocean',
      'linode', 'vultr', 'ovh', 'hetzner', 'cloudflare'
    ];

    return hostingProviders.some(provider => 
      isp.toLowerCase().includes(provider)
    );
  }

  /**
   * Log threat intelligence results
   */
  private async logThreatIntelligence(
    ipAddress: string,
    threatLevel: string,
    threatTypes: string[],
    metadata: any
  ): Promise<void> {
    try {
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_email: 'threat_intelligence_system',
          action: `THREAT_DETECTED_${threatLevel}`,
          target_type: 'threat_intelligence',
          target_id: ipAddress,
          metadata: {
            ipAddress,
            threatLevel,
            threatTypes,
            ...metadata,
            timestamp: new Date().toISOString()
          },
          created_at: new Date().toISOString()
        });

    } catch (error) {
      console.error('❌ Failed to log threat intelligence:', error);
    }
  }

  /**
   * Get threat intelligence statistics
   */
  async getThreatStats(): Promise<any> {
    try {
      const { data: threats, error } = await supabase
        .from('admin_audit_logs')
        .select('*')
        .like('action', 'THREAT_DETECTED_%')
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

      if (error) {
        throw error;
      }

      const stats = {
        totalThreats: threats.length,
        criticalThreats: threats.filter(t => t.action.includes('CRITICAL')).length,
        highThreats: threats.filter(t => t.action.includes('HIGH')).length,
        mediumThreats: threats.filter(t => t.action.includes('MEDIUM')).length,
        lowThreats: threats.filter(t => t.action.includes('LOW')).length,
        topThreatTypes: this.getTopThreatTypes(threats),
        topCountries: this.getTopThreatCountries(threats)
      };

      return stats;

    } catch (error) {
      console.error('❌ Failed to get threat stats:', error);
      return null;
    }
  }

  private getTopThreatTypes(threats: any[]): any[] {
    const typeCounts: Record<string, number> = {};
    
    threats.forEach(threat => {
      const types = threat.metadata?.threatTypes || [];
      types.forEach((type: string) => {
        typeCounts[type] = (typeCounts[type] || 0) + 1;
      });
    });

    return Object.entries(typeCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([type, count]) => ({ type, count }));
  }

  private getTopThreatCountries(threats: any[]): any[] {
    const countryCounts: Record<string, number> = {};
    
    threats.forEach(threat => {
      const country = threat.metadata?.geolocation?.country || 'Unknown';
      countryCounts[country] = (countryCounts[country] || 0) + 1;
    });

    return Object.entries(countryCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([country, count]) => ({ country, count }));
  }
}

// Create singleton instance
export const threatIntelligence = new ThreatIntelligenceEngine();

export default threatIntelligence;
