/**
 * API endpoint for handling contact form submissions
 * Sends <NAME_EMAIL> using Resend service
 */

import { Resend } from 'resend';
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Simple validation function for now
function validateContactInput(data) {
  const { name, surname, email, message } = data;

  if (!name || !surname || !email || !message) {
    return { success: false, errors: ['All fields are required'] };
  }

  // Basic malicious pattern detection
  const maliciousPatterns = [
    /('|\\')|(;|\\;)|(--|\\--)|(\/\*|\\\*\/)|(\bDROP\b)|(\bDELETE\b)|(\bINSERT\b)|(\bUPDATE\b)|(\bSELECT\b)|(\bUNION\b)/i,
    /<script[^>]*>.*?<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi
  ];

  const allText = `${name} ${surname} ${email} ${message}`;
  const detectedPatterns = [];

  for (const pattern of maliciousPatterns) {
    if (pattern.test(allText)) {
      detectedPatterns.push('MALICIOUS_INPUT');
      break;
    }
  }

  if (detectedPatterns.length > 0) {
    return {
      success: false,
      errors: [`Malicious patterns detected: ${detectedPatterns.join(', ')}`]
    };
  }

  return {
    success: true,
    data: { name, surname, email, message }
  };
}

// Load environment variables
dotenv.config();

const RESEND_API_KEY = process.env.RESEND_API_KEY || process.env.VITE_RESEND_API_KEY;
const RESEND_FROM_EMAIL = process.env.RESEND_FROM_EMAIL || process.env.VITE_RESEND_FROM_EMAIL || '<EMAIL>';
const RESEND_FROM_NAME = process.env.RESEND_FROM_NAME || process.env.VITE_RESEND_FROM_NAME || 'Aureus Alliance Holdings';
const SUPPORT_EMAIL = '<EMAIL>';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Support email addresses - ONLY for support team notifications
const SUPPORT_EMAILS = [
  '<EMAIL>'     // Primary support email
];

/**
 * Retry function for API calls with exponential backoff
 */
async function retryWithBackoff(fn, maxRetries = 3, baseDelay = 1000) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await fn();
      if (!result.error) {
        return result;
      }

      if (attempt === maxRetries) {
        return result; // Return the last error
      }

      // Exponential backoff: 1s, 2s, 4s
      const delay = baseDelay * Math.pow(2, attempt - 1);
      console.log(`⏳ Retry attempt ${attempt}/${maxRetries} in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));

    } catch (error) {
      if (attempt === maxRetries) {
        throw error;
      }

      const delay = baseDelay * Math.pow(2, attempt - 1);
      console.log(`⏳ Retry attempt ${attempt}/${maxRetries} after error in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}

// Initialize Resend client
let resend = null;

try {
  if (RESEND_API_KEY) {
    resend = new Resend(RESEND_API_KEY);
    console.log('✅ Contact form Resend email service initialized');
  } else {
    console.warn('⚠️ RESEND_API_KEY not found - contact form email service disabled');
  }
} catch (error) {
  console.error('❌ Failed to initialize contact form Resend service:', error);
}

/**
 * Generate contact form email content for support team
 */
function generateContactEmailContent(contactData) {
  const { name, surname, email, message, timestamp, userAgent, ipAddress } = contactData;
  const fullName = `${name} ${surname}`.trim();
  
  const subject = `New Contact Form Submission from ${fullName}`;

  const html = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <title>Contact Form Submission</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%); color: #FFD700; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .field { margin-bottom: 20px; }
          .field-label { font-weight: bold; color: #555; margin-bottom: 5px; display: block; }
          .field-value { background: white; padding: 12px; border-radius: 4px; border: 1px solid #ddd; }
          .message-field { min-height: 100px; white-space: pre-wrap; }
          .metadata { background: #e9e9e9; padding: 15px; border-radius: 4px; font-size: 12px; color: #666; margin-top: 20px; }
          .logo { max-width: 150px; height: auto; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1 style="margin: 0; font-size: 24px;">New Contact Form Submission</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">Aureus Alliance Holdings</p>
          </div>
          
          <div class="content">
            <div class="field">
              <span class="field-label">👤 Full Name:</span>
              <div class="field-value">${fullName}</div>
            </div>
            
            <div class="field">
              <span class="field-label">📧 Email Address:</span>
              <div class="field-value">
                <a href="mailto:${email}" style="color: #0066cc; text-decoration: none;">${email}</a>
              </div>
            </div>
            
            <div class="field">
              <span class="field-label">💬 Message:</span>
              <div class="field-value message-field">${message}</div>
            </div>
            
            <div class="metadata">
              <strong>📊 Submission Details:</strong><br>
              <strong>Timestamp:</strong> ${timestamp}<br>
              ${ipAddress ? `<strong>IP Address:</strong> ${ipAddress}<br>` : ''}
              ${userAgent ? `<strong>User Agent:</strong> ${userAgent}<br>` : ''}
              <strong>Source:</strong> Contact Form - Aureus Alliance Holdings Website
            </div>
            
            <div style="margin-top: 30px; padding: 20px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px;">
              <strong>📋 Next Steps:</strong>
              <ul style="margin: 10px 0; padding-left: 20px;">
                <li>Review the inquiry and determine appropriate response</li>
                <li>Reply directly to <strong>${email}</strong></li>
                <li>Log the interaction in the support system</li>
                <li>Follow up within 24-48 hours for optimal customer service</li>
              </ul>
            </div>
          </div>
        </div>
      </body>
    </html>
  `;

  const text = `
New Contact Form Submission - Aureus Alliance Holdings

Full Name: ${fullName}
Email: ${email}

Message:
${message}

Submission Details:
Timestamp: ${timestamp}
${ipAddress ? `IP Address: ${ipAddress}` : ''}
${userAgent ? `User Agent: ${userAgent}` : ''}
Source: Contact Form - Aureus Alliance Holdings Website

Next Steps:
- Review the inquiry and determine appropriate response
- Reply directly to ${email}
- Log the interaction in the support system
- Follow up within 24-48 hours for optimal customer service
  `;

  return { subject, html, text };
}

/**
 * Generate confirmation email content for the user
 */
function generateConfirmationEmailContent(contactData) {
  const { name, surname } = contactData;
  const fullName = `${name} ${surname}`.trim();
  const greeting = fullName || 'Thank you';
  
  const subject = 'Thank you for contacting Aureus Alliance Holdings';

  const html = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <title>Contact Confirmation</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%); color: #FFD700; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .highlight { background: #fff3cd; padding: 15px; border-radius: 4px; border-left: 4px solid #FFD700; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1 style="margin: 0; font-size: 24px;">Message Received</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">Aureus Alliance Holdings</p>
          </div>
          
          <div class="content">
            <p><strong>Hello ${greeting},</strong></p>
            
            <p>Thank you for contacting Aureus Alliance Holdings. We have successfully received your message and our support team will review it shortly.</p>
            
            <div class="highlight">
              <strong>⏱️ What happens next?</strong>
              <ul style="margin: 10px 0; padding-left: 20px;">
                <li>Our support team will review your inquiry</li>
                <li>You can expect a response within 24-48 hours</li>
                <li>We'll reply directly to this email address</li>
                <li>For urgent matters, you can also reach us via WhatsApp or Telegram</li>
              </ul>
            </div>
            
            <p>In the meantime, feel free to explore our website to learn more about our gold mining operations and shareholder opportunities.</p>
            
            <p style="margin-top: 30px;">
              Best regards,<br>
              <strong>The Aureus Alliance Holdings Support Team</strong>
            </p>
            
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
            
            <p style="font-size: 12px; color: #666; text-align: center;">
              This is an automated confirmation email. Please do not reply directly to this message.
              For support inquiries, please use our contact form <NAME_EMAIL>
            </p>
          </div>
        </div>
      </body>
    </html>
  `;

  const text = `
Hello ${greeting},

Thank you for contacting Aureus Alliance Holdings. We have successfully received your message and our support team will review it shortly.

What happens next?
- Our support team will review your inquiry
- You can expect a response within 24-48 hours
- We'll reply directly to this email address
- For urgent matters, you can also reach us via WhatsApp or Telegram

In the meantime, feel free to explore our website to learn more about our gold mining operations and shareholder opportunities.

Best regards,
The Aureus Alliance Holdings Support Team

---
This is an automated confirmation email. Please do not reply directly to this message.
For support inquiries, please use our contact form <NAME_EMAIL>
  `;

  return { subject, html, text };
}

// Old validation function removed - now using secure validation from inputValidation.ts

/**
 * API handler for contact form submissions
 */
export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });
  }

  // Check if email service is configured
  if (!resend || !RESEND_API_KEY) {
    console.error('❌ Contact form email service not configured');
    return res.status(500).json({ 
      success: false, 
      error: 'Email service not configured' 
    });
  }

  try {
    console.log('🔍 Contact form request body:', req.body);
    console.log('🔍 Environment check - SUPABASE_URL:', supabaseUrl ? 'SET' : 'NOT SET');
    console.log('🔍 Environment check - SUPABASE_SERVICE_KEY:', supabaseServiceKey ? 'SET' : 'NOT SET');

    // SECURITY: Validate and sanitize all input data
    const validation = validateContactInput(req.body);
    console.log('🔍 Validation result:', validation);

    if (!validation.success) {
      console.error('❌ Contact form validation failed:', validation.errors);
      return res.status(400).json({
        success: false,
        error: 'Invalid contact form data',
        details: validation.errors
      });
    }

    const { name, surname, email, message } = validation.data;
    console.log('✅ Validation passed, sanitized data:', { name, surname, email, message });

    // Get client information for security logging
    const headers = req.headers || {};
    const ipAddress = headers['x-forwarded-for']?.split(',')[0] ||
                     headers['x-real-ip'] ||
                     req.connection?.remoteAddress ||
                     req.socket?.remoteAddress ||
                     'unknown';
    const userAgent = headers['user-agent'] || 'unknown';

    // SECURITY: Store contact submission in database
    console.log('🔍 Attempting to store in database...');
    const { data: contactRecord, error: dbError } = await supabase
      .from('contact_submissions')
      .insert([{
        name: name,
        surname: surname,
        email: email,
        message: message,
        ip_address: ipAddress,
        user_agent: userAgent,
        security_validated: true,
        status: 'pending'
      }])
      .select()
      .single();

    if (dbError) {
      console.error('❌ Failed to store contact submission:', dbError);
      return res.status(500).json({
        success: false,
        error: 'Failed to process contact form submission'
      });
    }

    console.log('✅ Contact submission stored successfully:', contactRecord?.id);

    // Prepare contact data for email
    const contactData = {
      name: name,
      surname: surname,
      email: email,
      message: message,
      timestamp: new Date().toISOString(),
      userAgent: userAgent,
      ipAddress: ipAddress
    };

    console.log(`📧 Processing contact form submission from ${contactData.name} ${contactData.surname} (${contactData.email})`);

    // Generate email content for support team
    const supportEmailContent = generateContactEmailContent(contactData);
    
    // Send email to support team with backup strategy
    let supportEmailResult = null;
    let supportEmailSuccess = false;

    // Try sending to all support emails to ensure delivery with retry logic
    for (const emailAddress of SUPPORT_EMAILS) {
      try {
        console.log(`📧 Attempting to send support email to: ${emailAddress}`);

        // Use retry logic for email sending
        supportEmailResult = await retryWithBackoff(async () => {
          return await resend.emails.send({
            from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
            to: [emailAddress],
            subject: supportEmailContent.subject,
            html: supportEmailContent.html,
            text: supportEmailContent.text,
            tags: [
              { name: 'category', value: 'contact_form' },
              { name: 'source', value: 'website' },
              { name: 'recipient_type', value: emailAddress === SUPPORT_EMAIL ? 'primary' : 'backup' }
            ]
          });
        }, 2, 1000); // 2 retries, 1 second base delay

        if (!supportEmailResult.error) {
          console.log(`✅ Support email sent successfully to ${emailAddress}: ${supportEmailResult.data?.id}`);
          supportEmailSuccess = true;
          // Continue sending to all addresses to ensure delivery
        } else {
          console.warn(`⚠️ Failed to send support email to ${emailAddress} after retries:`, supportEmailResult.error);
        }
      } catch (emailError) {
        console.warn(`⚠️ Error sending support email to ${emailAddress} after retries:`, emailError);
      }
    }

    if (!supportEmailSuccess) {
      console.error('❌ Failed to send support email to any recipient');
      return res.status(500).json({
        success: false,
        error: 'Failed to send message to support team'
      });
    }

    // Generate and send confirmation email to user
    const confirmationEmailContent = generateConfirmationEmailContent(contactData);

    let confirmationEmailResult = null;
    try {
      console.log(`📧 Sending confirmation email to: ${contactData.email}`);

      // Use retry logic for confirmation email too
      confirmationEmailResult = await retryWithBackoff(async () => {
        return await resend.emails.send({
          from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
          to: [contactData.email],
          subject: confirmationEmailContent.subject,
          html: confirmationEmailContent.html,
          text: confirmationEmailContent.text,
          tags: [
            { name: 'category', value: 'contact_confirmation' },
            { name: 'source', value: 'website' }
          ]
        });
      }, 2, 1000); // 2 retries, 1 second base delay

      if (confirmationEmailResult.error) {
        console.warn('⚠️ Failed to send confirmation email to user after retries:', confirmationEmailResult.error);
        // Don't fail the request if confirmation email fails
      } else {
        console.log(`✅ Confirmation email sent successfully to ${contactData.email}: ${confirmationEmailResult.data?.id}`);
      }
    } catch (confirmationError) {
      console.warn('⚠️ Error sending confirmation email after retries:', confirmationError);
    }

    // Log successful submission
    console.log(`✅ Contact form submission processed successfully for ${contactData.email}`);

    return res.status(200).json({
      success: true,
      message: 'Your message has been sent successfully. We will get back to you within 24-48 hours.',
      supportEmailId: supportEmailResult.data?.id,
      confirmationEmailId: confirmationEmailResult.data?.id || null
    });

  } catch (error) {
    console.error('❌ Contact form API error:', error);
    return res.status(500).json({ 
      success: false, 
      error: 'Internal server error. Please try again later.' 
    });
  }
}
