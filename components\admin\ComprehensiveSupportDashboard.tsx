import React, { useState, useEffect } from 'react'
import { getServiceRoleClient } from '../../lib/supabase'
import { 
  getAgentTickets, 
  type SupportTicket, 
  type ChatSession, 
  type SupportAgent,
  type AgentAvailability 
} from '../../lib/supportSystem'
import SupportAgentManager from './SupportAgentManager'
import TicketingSystem from '../support/TicketingSystem'

interface ComprehensiveSupportDashboardProps {
  currentUser?: any
}

const ComprehensiveSupportDashboard: React.FC<ComprehensiveSupportDashboardProps> = ({ currentUser }) => {
  const [activeTab, setActiveTab] = useState('overview')
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    totalTickets: 0,
    openTickets: 0,
    inProgressTickets: 0,
    resolvedTickets: 0,
    activeAgents: 0,
    availableAgents: 0,
    activeChatSessions: 0,
    avgResponseTime: 0
  })
  const [recentTickets, setRecentTickets] = useState<SupportTicket[]>([])
  const [currentAgent, setCurrentAgent] = useState<SupportAgent | null>(null)

  useEffect(() => {
    loadDashboardData()
  }, [currentUser])

  const loadDashboardData = async () => {
    setLoading(true)
    try {
      await Promise.all([
        loadStats(),
        loadRecentTickets(),
        loadCurrentAgent()
      ])
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const serviceClient = getServiceRoleClient()
      
      // Get ticket stats
      const { data: tickets } = await serviceClient
        .from('support_tickets')
        .select('status')
      
      const ticketStats = {
        totalTickets: tickets?.length || 0,
        openTickets: tickets?.filter(t => t.status === 'open').length || 0,
        inProgressTickets: tickets?.filter(t => t.status === 'in_progress').length || 0,
        resolvedTickets: tickets?.filter(t => t.status === 'resolved').length || 0
      }

      // Get agent stats
      const { data: agents } = await serviceClient
        .from('support_agents')
        .select(`
          *,
          availability:agent_availability(*)
        `)
        .eq('is_active', true)

      const agentStats = {
        activeAgents: agents?.length || 0,
        availableAgents: agents?.filter(a => a.availability?.[0]?.status === 'available').length || 0
      }

      // Get active chat sessions
      const { data: sessions } = await serviceClient
        .from('chat_sessions')
        .select('id')
        .eq('status', 'active')

      const sessionStats = {
        activeChatSessions: sessions?.length || 0
      }

      // Calculate average response time (simplified)
      const { data: responseData } = await serviceClient
        .from('support_tickets')
        .select('created_at, first_response_at')
        .not('first_response_at', 'is', null)
        .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())

      let avgResponseTime = 0
      if (responseData && responseData.length > 0) {
        const totalResponseTime = responseData.reduce((sum, ticket) => {
          const created = new Date(ticket.created_at).getTime()
          const responded = new Date(ticket.first_response_at).getTime()
          return sum + (responded - created)
        }, 0)
        avgResponseTime = Math.round(totalResponseTime / responseData.length / (1000 * 60)) // Convert to minutes
      }

      setStats({
        ...ticketStats,
        ...agentStats,
        ...sessionStats,
        avgResponseTime
      })
    } catch (error) {
      console.error('Error loading stats:', error)
    }
  }

  const loadRecentTickets = async () => {
    try {
      const serviceClient = getServiceRoleClient()
      const { data, error } = await serviceClient
        .from('support_tickets')
        .select(`
          *,
          user:users(id, username, full_name, email),
          agent:support_agents(agent_name, agent_email)
        `)
        .order('created_at', { ascending: false })
        .limit(10)

      if (error) throw error
      setRecentTickets(data || [])
    } catch (error) {
      console.error('Error loading recent tickets:', error)
    }
  }

  const loadCurrentAgent = async () => {
    try {
      if (!currentUser?.email) return

      const serviceClient = getServiceRoleClient()
      const { data: agent } = await serviceClient
        .from('support_agents')
        .select(`
          *,
          availability:agent_availability(*)
        `)
        .eq('agent_email', currentUser.email)
        .single()

      if (agent) {
        setCurrentAgent(agent)
      }
    } catch (error) {
      console.error('Error loading current agent:', error)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-blue-100 text-blue-800'
      case 'in_progress': return 'bg-yellow-100 text-yellow-800'
      case 'waiting_user': return 'bg-orange-100 text-orange-800'
      case 'resolved': return 'bg-green-100 text-green-800'
      case 'closed': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const tabs = [
    { id: 'overview', name: 'Overview', icon: '📊' },
    { id: 'tickets', name: 'All Tickets', icon: '🎫' },
    { id: 'my-tickets', name: 'My Tickets', icon: '📋' },
    { id: 'live-chat', name: 'Live Chat', icon: '💬' },
    { id: 'agents', name: 'Agent Management', icon: '👥' },
    { id: 'analytics', name: 'Analytics', icon: '📈' }
  ]

  if (loading && activeTab === 'overview') {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Support Dashboard</h2>
          <p className="text-gray-600">
            Comprehensive support management and analytics
          </p>
        </div>
        {currentAgent && (
          <div className="flex items-center space-x-3">
            <div className="text-right">
              <div className="text-sm font-medium text-gray-900">{currentAgent.agent_name}</div>
              <div className="text-xs text-gray-500">Support Agent</div>
            </div>
            <div className={`w-3 h-3 rounded-full ${
              currentAgent.availability?.[0]?.status === 'available' ? 'bg-green-500' :
              currentAgent.availability?.[0]?.status === 'busy' ? 'bg-yellow-500' :
              currentAgent.availability?.[0]?.status === 'away' ? 'bg-orange-500' :
              'bg-gray-500'
            }`}></div>
          </div>
        )}
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === tab.id
                  ? 'border-yellow-500 text-yellow-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div>
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                      <span className="text-blue-600 text-sm">🎫</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Total Tickets</p>
                    <p className="text-2xl font-semibold text-gray-900">{stats.totalTickets}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
                      <span className="text-yellow-600 text-sm">⏳</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Open Tickets</p>
                    <p className="text-2xl font-semibold text-gray-900">{stats.openTickets}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                      <span className="text-green-600 text-sm">👥</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Available Agents</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {stats.availableAgents}/{stats.activeAgents}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center">
                      <span className="text-purple-600 text-sm">⚡</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Avg Response</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {stats.avgResponseTime}m
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Recent Tickets */}
            <div className="bg-white rounded-lg border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Recent Tickets</h3>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Ticket
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        User
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Created
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Agent
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {recentTickets.slice(0, 5).map((ticket) => (
                      <tr key={ticket.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {ticket.ticket_number}
                          </div>
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {ticket.title}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {ticket.user?.full_name || ticket.user?.username}
                          </div>
                          <div className="text-sm text-gray-500">
                            {ticket.user_type}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {ticket.category || 'General'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(ticket.status)}`}>
                            {ticket.status.replace('_', ' ')}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(ticket.created_at)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {ticket.agent?.agent_name || 'Unassigned'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'tickets' && (
          <div>
            <TicketingSystem />
          </div>
        )}

        {activeTab === 'my-tickets' && currentAgent && (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">My Assigned Tickets</h3>
            {/* This would show tickets assigned to the current agent */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <p className="text-gray-600">Loading tickets assigned to {currentAgent.agent_name}...</p>
            </div>
          </div>
        )}

        {activeTab === 'live-chat' && (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Live Chat Management</h3>
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <p className="text-gray-600">Live chat interface will be implemented here.</p>
            </div>
          </div>
        )}

        {activeTab === 'agents' && (
          <div>
            <SupportAgentManager currentUser={currentUser} />
          </div>
        )}

        {activeTab === 'analytics' && (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Support Analytics</h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h4 className="text-md font-medium text-gray-900 mb-4">Ticket Volume by Status</h4>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Open</span>
                    <span className="text-sm font-medium text-gray-900">{stats.openTickets}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">In Progress</span>
                    <span className="text-sm font-medium text-gray-900">{stats.inProgressTickets}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Resolved</span>
                    <span className="text-sm font-medium text-gray-900">{stats.resolvedTickets}</span>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h4 className="text-md font-medium text-gray-900 mb-4">Agent Performance</h4>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Active Agents</span>
                    <span className="text-sm font-medium text-gray-900">{stats.activeAgents}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Available Now</span>
                    <span className="text-sm font-medium text-gray-900">{stats.availableAgents}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Active Chats</span>
                    <span className="text-sm font-medium text-gray-900">{stats.activeChatSessions}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ComprehensiveSupportDashboard
