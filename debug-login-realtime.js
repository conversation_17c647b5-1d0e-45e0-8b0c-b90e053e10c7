// REAL-TIME LOGIN DEBUG SCRIPT
// Paste this into browser console while on the login page

console.log('🔍 REAL-TIME LOGIN DEBUGGER ACTIVE');
console.log('=====================================');

// Override the signInWithEmailEnhanced function to add debugging
if (window.supabase) {
  console.log('✅ Supabase client found');
  
  // Store original function
  const originalSignIn = window.signInWithEmailEnhanced;
  
  // Create debug version
  window.signInWithEmailEnhanced = async function(email, password) {
    console.log('🔐 LOGIN ATTEMPT STARTED');
    console.log('========================');
    console.log(`📧 Email: ${email}`);
    console.log(`🔑 Password: ${password ? '[PROVIDED]' : '[MISSING]'}`);
    console.log(`🔗 Supabase URL: ${window.supabase.supabaseUrl}`);
    
    try {
      // Step 1: Check users table
      console.log('\n📋 Step 1: Checking users table...');
      const { data: webUser, error: webError } = await window.supabase
        .from('users')
        .select('*')
        .eq('email', email.toLowerCase().trim())
        .single();
      
      if (webError) {
        console.log('❌ Users table error:', webError);
      } else if (webUser) {
        console.log('✅ Found user in users table:');
        console.log(`   ID: ${webUser.id}`);
        console.log(`   Username: ${webUser.username}`);
        console.log(`   Active: ${webUser.is_active}`);
        console.log(`   Has password hash: ${!!webUser.password_hash}`);
      } else {
        console.log('❌ No user found in users table');
      }
      
      // Step 2: Test Supabase auth
      console.log('\n📋 Step 2: Testing Supabase auth...');
      const { data: authData, error: authError } = await window.supabase.auth.signInWithPassword({
        email,
        password
      });
      
      if (authError) {
        console.log('❌ Supabase auth error:', authError.message);
        console.log('   Full error:', authError);
      } else {
        console.log('✅ Supabase auth successful:');
        console.log(`   User ID: ${authData.user?.id}`);
        console.log(`   Email: ${authData.user?.email}`);
      }
      
      // Call original function if it exists
      if (originalSignIn) {
        console.log('\n📋 Step 3: Calling original function...');
        const result = await originalSignIn(email, password);
        console.log('📊 Original function result:', result);
        return result;
      } else {
        // Return our own result
        if (authError) {
          return { user: null, error: { message: authError.message } };
        } else {
          return { user: authData.user, error: null };
        }
      }
      
    } catch (error) {
      console.log('❌ Debug function error:', error);
      return { user: null, error: { message: error.message } };
    }
  };
  
  console.log('✅ Login debug override installed');
} else {
  console.log('❌ Supabase client not found');
}

// Monitor form submissions
document.addEventListener('submit', function(event) {
  const form = event.target;
  if (form.querySelector('input[type="email"]') && form.querySelector('input[type="password"]')) {
    console.log('📝 LOGIN FORM SUBMITTED');
    console.log('=======================');
    
    const emailInput = form.querySelector('input[type="email"]');
    const passwordInput = form.querySelector('input[type="password"]');
    
    console.log(`📧 Form email: ${emailInput?.value}`);
    console.log(`🔑 Form password: ${passwordInput?.value ? '[PROVIDED]' : '[MISSING]'}`);
  }
});

// Monitor network requests
const originalFetch = window.fetch;
window.fetch = function(...args) {
  const url = args[0];
  if (typeof url === 'string' && (url.includes('auth') || url.includes('sign'))) {
    console.log('🌐 AUTH NETWORK REQUEST:', url);
  }
  return originalFetch.apply(this, args);
};

console.log('✅ Real-time debugger ready!');
console.log('💡 Now try to login and watch the console...');
