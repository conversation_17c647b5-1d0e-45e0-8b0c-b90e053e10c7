/**
 * SETUP PROFILE PICTURES STORAGE BUCKET
 * 
 * This script creates a Supabase Storage bucket for profile pictures
 * and sets up the necessary policies for secure access.
 */

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client with service role key for admin operations
const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtlandlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNzI2NzQ5NCwiZXhwIjoyMDQyODQzNDk0fQ.6sP8nJlnlNqzqQs7JNcVl8Ej8NuU8KhZ8Ej8NuU8KhZ';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupProfilePicturesStorage() {
  console.log('📷 Setting up profile pictures storage bucket...\n');

  try {
    // Step 1: Create the profile-pictures bucket
    console.log('1️⃣ Creating profile-pictures bucket...');
    
    const { data: bucketData, error: bucketError } = await supabase.storage
      .createBucket('profile-pictures', {
        public: true,
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        fileSizeLimit: 5242880 // 5MB in bytes
      });

    if (bucketError) {
      if (bucketError.message.includes('already exists')) {
        console.log('✅ Bucket already exists, continuing...');
      } else {
        console.error('❌ Error creating bucket:', bucketError);
        return;
      }
    } else {
      console.log('✅ Profile pictures bucket created successfully');
    }

    // Step 2: Set up storage policies
    console.log('\n2️⃣ Setting up storage policies...');

    // Policy 1: Allow authenticated users to upload their own profile pictures
    const uploadPolicy = {
      name: 'Users can upload their own profile pictures',
      definition: `
        (bucket_id = 'profile-pictures'::text) AND 
        (auth.uid() IS NOT NULL) AND 
        (storage.filename(name) LIKE 'profile-' || auth.uid()::text || '-%')
      `,
      check: `
        (bucket_id = 'profile-pictures'::text) AND 
        (auth.uid() IS NOT NULL) AND 
        (storage.filename(name) LIKE 'profile-' || auth.uid()::text || '-%')
      `
    };

    // Policy 2: Allow public read access to all profile pictures
    const readPolicy = {
      name: 'Public read access to profile pictures',
      definition: `bucket_id = 'profile-pictures'::text`,
      check: `bucket_id = 'profile-pictures'::text`
    };

    // Policy 3: Allow users to delete their own profile pictures
    const deletePolicy = {
      name: 'Users can delete their own profile pictures',
      definition: `
        (bucket_id = 'profile-pictures'::text) AND 
        (auth.uid() IS NOT NULL) AND 
        (storage.filename(name) LIKE 'profile-' || auth.uid()::text || '-%')
      `,
      check: `
        (bucket_id = 'profile-pictures'::text) AND 
        (auth.uid() IS NOT NULL) AND 
        (storage.filename(name) LIKE 'profile-' || auth.uid()::text || '-%')
      `
    };

    console.log('📝 Note: Storage policies need to be set up manually in Supabase Dashboard');
    console.log('   Go to: Storage > Policies > profile-pictures bucket');
    console.log('\n   Required Policies:');
    console.log('   1. INSERT Policy:', uploadPolicy.name);
    console.log('   2. SELECT Policy:', readPolicy.name);
    console.log('   3. DELETE Policy:', deletePolicy.name);

    // Step 3: Test bucket access
    console.log('\n3️⃣ Testing bucket access...');
    
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();
    
    if (listError) {
      console.error('❌ Error listing buckets:', listError);
      return;
    }

    const profileBucket = buckets.find(bucket => bucket.name === 'profile-pictures');
    if (profileBucket) {
      console.log('✅ Profile pictures bucket found and accessible');
      console.log('   Bucket ID:', profileBucket.id);
      console.log('   Public:', profileBucket.public);
      console.log('   Created:', profileBucket.created_at);
    } else {
      console.log('❌ Profile pictures bucket not found');
      return;
    }

    console.log('\n🎉 Profile pictures storage setup complete!');
    console.log('\n📝 Manual steps required:');
    console.log('   1. Go to Supabase Dashboard > Storage > Policies');
    console.log('   2. Create the three policies mentioned above');
    console.log('   3. Test uploading a profile picture from the dashboard');

    console.log('\n🔗 Storage URL format:');
    console.log(`   ${supabaseUrl}/storage/v1/object/public/profile-pictures/[filename]`);

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the setup
setupProfilePicturesStorage();
