/**
 * CREATE NOTIFICATION LOGS TABLE
 * 
 * Creates a notification_logs table to track email notifications
 * sent by the system, including KYC status notifications.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing required environment variables');
  console.error('Required: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Initialize Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function executeSQL(sql, description) {
  try {
    console.log(`🔄 ${description}...`);
    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });
    
    if (error) {
      console.error(`❌ ${description} failed:`, error);
      return false;
    }
    
    console.log(`✅ ${description} completed successfully`);
    return true;
  } catch (err) {
    console.error(`❌ ${description} failed:`, err);
    return false;
  }
}

async function createNotificationLogsTable() {
  console.log('🚀 Starting notification logs table creation...');

  // Step 1: Create notification_logs table
  const createTableSQL = `
    CREATE TABLE IF NOT EXISTS notification_logs (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      notification_type VARCHAR(50) NOT NULL,
      status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'bounced')),
      email_message_id VARCHAR(255),
      subject VARCHAR(255),
      recipient_email VARCHAR(255),
      metadata JSONB,
      error_message TEXT,
      sent_at TIMESTAMP WITH TIME ZONE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `;

  if (!await executeSQL(createTableSQL, 'Creating notification_logs table')) {
    return false;
  }

  // Step 2: Create indexes
  const createIndexesSQL = `
    CREATE INDEX IF NOT EXISTS idx_notification_logs_user_id ON notification_logs(user_id);
    CREATE INDEX IF NOT EXISTS idx_notification_logs_type ON notification_logs(notification_type);
    CREATE INDEX IF NOT EXISTS idx_notification_logs_status ON notification_logs(status);
    CREATE INDEX IF NOT EXISTS idx_notification_logs_created_at ON notification_logs(created_at);
    CREATE INDEX IF NOT EXISTS idx_notification_logs_email_message_id ON notification_logs(email_message_id) WHERE email_message_id IS NOT NULL;
  `;

  if (!await executeSQL(createIndexesSQL, 'Creating notification_logs indexes')) {
    return false;
  }

  // Step 3: Add comments
  const addCommentsSQL = `
    COMMENT ON TABLE notification_logs IS 'Log of all email notifications sent by the system';
    COMMENT ON COLUMN notification_logs.user_id IS 'ID of the user who received the notification';
    COMMENT ON COLUMN notification_logs.notification_type IS 'Type of notification (e.g., kyc_status_change, payment_confirmation)';
    COMMENT ON COLUMN notification_logs.status IS 'Status of the notification delivery';
    COMMENT ON COLUMN notification_logs.email_message_id IS 'Message ID from the email service provider';
    COMMENT ON COLUMN notification_logs.metadata IS 'Additional data related to the notification';
    COMMENT ON COLUMN notification_logs.error_message IS 'Error message if notification failed';
  `;

  if (!await executeSQL(addCommentsSQL, 'Adding table comments')) {
    return false;
  }

  // Step 4: Create RLS policies
  const createPoliciesSQL = `
    ALTER TABLE notification_logs ENABLE ROW LEVEL SECURITY;
    
    CREATE POLICY "Users can view their own notification logs" ON notification_logs
      FOR SELECT USING (auth.uid()::text = user_id::text);
    
    CREATE POLICY "Service role can manage all notification logs" ON notification_logs
      FOR ALL USING (auth.role() = 'service_role');
  `;

  if (!await executeSQL(createPoliciesSQL, 'Creating RLS policies')) {
    return false;
  }

  // Step 5: Create updated_at trigger
  const createTriggerSQL = `
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
    END;
    $$ language 'plpgsql';

    DROP TRIGGER IF EXISTS update_notification_logs_updated_at ON notification_logs;
    CREATE TRIGGER update_notification_logs_updated_at
        BEFORE UPDATE ON notification_logs
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
  `;

  if (!await executeSQL(createTriggerSQL, 'Creating updated_at trigger')) {
    return false;
  }

  console.log('✅ Notification logs table creation completed successfully!');
  return true;
}

// Execute the migration
createNotificationLogsTable()
  .then((success) => {
    if (success) {
      console.log('🎉 Migration completed successfully!');
      process.exit(0);
    } else {
      console.error('💥 Migration failed!');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('💥 Migration failed with error:', error);
    process.exit(1);
  });
