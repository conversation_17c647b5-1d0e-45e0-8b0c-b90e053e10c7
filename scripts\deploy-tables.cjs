#!/usr/bin/env node

/**
 * EMAIL MARKETING TABLES DEPLOYMENT SCRIPT (CommonJS)
 * 
 * This script creates the missing email marketing tables in Supabase.
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  console.error('Required: VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function executeSQL(sql, description) {
  console.log(`🔄 ${description}...`);
  try {
    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });
    if (error) {
      console.error(`❌ ${description} failed:`, error.message);
      return false;
    } else {
      console.log(`✅ ${description} completed`);
      return true;
    }
  } catch (err) {
    console.error(`❌ ${description} error:`, err.message);
    return false;
  }
}

async function deployTables() {
  console.log('🚀 Deploying Email Marketing Tables...\n');

  let successCount = 0;
  let errorCount = 0;

  // 1. Create lead_lists table
  const success1 = await executeSQL(`
    CREATE TABLE IF NOT EXISTS lead_lists (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      list_name VARCHAR(255) NOT NULL,
      description TEXT,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    )
  `, 'Creating lead_lists table');
  if (success1) successCount++; else errorCount++;

  // 2. Create lead_list_members table
  const success2 = await executeSQL(`
    CREATE TABLE IF NOT EXISTS lead_list_members (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      list_id UUID NOT NULL REFERENCES lead_lists(id) ON DELETE CASCADE,
      lead_id UUID NOT NULL REFERENCES affiliate_leads(id) ON DELETE CASCADE,
      added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      UNIQUE(list_id, lead_id)
    )
  `, 'Creating lead_list_members table');
  if (success2) successCount++; else errorCount++;

  // 3. Create email_sends table
  const success3 = await executeSQL(`
    CREATE TABLE IF NOT EXISTS email_sends (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      campaign_id UUID NOT NULL REFERENCES email_campaigns(id) ON DELETE CASCADE,
      lead_id UUID REFERENCES affiliate_leads(id) ON DELETE SET NULL,
      email_address VARCHAR(255) NOT NULL,
      status VARCHAR(20) DEFAULT 'sent' CHECK (status IN ('sent', 'delivered', 'failed')),
      sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      error_message TEXT
    )
  `, 'Creating email_sends table');
  if (success3) successCount++; else errorCount++;

  // 4. Add missing columns to email_campaigns
  const success4 = await executeSQL(`
    ALTER TABLE email_campaigns 
    ADD COLUMN IF NOT EXISTS template_id UUID REFERENCES email_templates(id) ON DELETE SET NULL,
    ADD COLUMN IF NOT EXISTS list_id UUID REFERENCES lead_lists(id) ON DELETE SET NULL,
    ADD COLUMN IF NOT EXISTS subject VARCHAR(500),
    ADD COLUMN IF NOT EXISTS sent_count INTEGER DEFAULT 0,
    ADD COLUMN IF NOT EXISTS delivered_count INTEGER DEFAULT 0,
    ADD COLUMN IF NOT EXISTS failed_count INTEGER DEFAULT 0,
    ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'draft',
    ADD COLUMN IF NOT EXISTS sent_at TIMESTAMP WITH TIME ZONE
  `, 'Adding columns to email_campaigns');
  if (success4) successCount++; else errorCount++;

  // 5. Create indexes
  const success5 = await executeSQL(`
    CREATE INDEX IF NOT EXISTS idx_lead_lists_user_id ON lead_lists(user_id)
  `, 'Creating lead_lists index');
  if (success5) successCount++; else errorCount++;

  const success6 = await executeSQL(`
    CREATE INDEX IF NOT EXISTS idx_lead_list_members_list_id ON lead_list_members(list_id)
  `, 'Creating lead_list_members list_id index');
  if (success6) successCount++; else errorCount++;

  const success7 = await executeSQL(`
    CREATE INDEX IF NOT EXISTS idx_lead_list_members_lead_id ON lead_list_members(lead_id)
  `, 'Creating lead_list_members lead_id index');
  if (success7) successCount++; else errorCount++;

  const success8 = await executeSQL(`
    CREATE INDEX IF NOT EXISTS idx_email_sends_campaign_id ON email_sends(campaign_id)
  `, 'Creating email_sends index');
  if (success8) successCount++; else errorCount++;

  // 6. Enable RLS
  const success9 = await executeSQL(`
    ALTER TABLE lead_lists ENABLE ROW LEVEL SECURITY
  `, 'Enabling RLS on lead_lists');
  if (success9) successCount++; else errorCount++;

  const success10 = await executeSQL(`
    ALTER TABLE lead_list_members ENABLE ROW LEVEL SECURITY
  `, 'Enabling RLS on lead_list_members');
  if (success10) successCount++; else errorCount++;

  const success11 = await executeSQL(`
    ALTER TABLE email_sends ENABLE ROW LEVEL SECURITY
  `, 'Enabling RLS on email_sends');
  if (success11) successCount++; else errorCount++;

  // 7. Create RLS policies
  const success12 = await executeSQL(`
    CREATE POLICY IF NOT EXISTS "Users can view their own lead lists" ON lead_lists
      FOR SELECT USING (user_id = auth.uid()::integer)
  `, 'Creating SELECT policy for lead_lists');
  if (success12) successCount++; else errorCount++;

  const success13 = await executeSQL(`
    CREATE POLICY IF NOT EXISTS "Users can insert their own lead lists" ON lead_lists
      FOR INSERT WITH CHECK (user_id = auth.uid()::integer)
  `, 'Creating INSERT policy for lead_lists');
  if (success13) successCount++; else errorCount++;

  const success14 = await executeSQL(`
    CREATE POLICY IF NOT EXISTS "Users can update their own lead lists" ON lead_lists
      FOR UPDATE USING (user_id = auth.uid()::integer)
  `, 'Creating UPDATE policy for lead_lists');
  if (success14) successCount++; else errorCount++;

  const success15 = await executeSQL(`
    CREATE POLICY IF NOT EXISTS "Users can delete their own lead lists" ON lead_lists
      FOR DELETE USING (user_id = auth.uid()::integer)
  `, 'Creating DELETE policy for lead_lists');
  if (success15) successCount++; else errorCount++;

  // 8. Grant permissions
  const success16 = await executeSQL(`
    GRANT ALL ON lead_lists TO service_role
  `, 'Granting permissions on lead_lists');
  if (success16) successCount++; else errorCount++;

  const success17 = await executeSQL(`
    GRANT ALL ON lead_list_members TO service_role
  `, 'Granting permissions on lead_list_members');
  if (success17) successCount++; else errorCount++;

  const success18 = await executeSQL(`
    GRANT ALL ON email_sends TO service_role
  `, 'Granting permissions on email_sends');
  if (success18) successCount++; else errorCount++;

  console.log('\n🔍 Verifying deployment...\n');

  // Verify tables
  const tables = ['lead_lists', 'lead_list_members', 'email_sends'];
  for (const table of tables) {
    try {
      const { data, error } = await supabase.from(table).select('*').limit(1);
      if (error) {
        console.log(`❌ ${table}: ${error.message}`);
      } else {
        console.log(`✅ ${table}: accessible`);
      }
    } catch (err) {
      console.log(`❌ ${table}: ${err.message}`);
    }
  }

  console.log('\n📊 DEPLOYMENT SUMMARY:');
  console.log(`✅ Successful operations: ${successCount}`);
  console.log(`❌ Failed operations: ${errorCount}`);

  if (errorCount === 0) {
    console.log('\n🎉 Email Marketing Tables deployment completed successfully!');
  } else {
    console.log('\n⚠️ Deployment completed with some errors.');
  }
}

deployTables().catch(console.error);
