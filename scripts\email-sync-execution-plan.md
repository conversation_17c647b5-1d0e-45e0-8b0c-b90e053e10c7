# Email Synchronization Execution Plan

## Overview
Synchronize email addresses between `users` table and `kyc_information` table, treating KYC emails as authoritative source.

## Current Status
- **Total KYC Records**: 41 completed
- **Email Mismatches**: 20 users need synchronization
- **Primary Issue**: Telegram users with `@telegram.local` emails need real email addresses from KYC

## Affected Users Preview
| User ID | Username | Current Email | KYC Email | Issue Type |
|---------|----------|---------------|-----------|------------|
| 197 | KUDJOS1 | <EMAIL> | <EMAIL> | Telegram user |
| 191 | Nina_Mannel | <EMAIL> | <EMAIL> | Telegram user |
| 176 | fentsi | <EMAIL> | <EMAIL> | Telegram user |
| ... | ... | ... | ... | ... |

## Execution Steps

### Phase 1: Safety Preparations
1. **Create Backup Table** ✅
   ```sql
   -- Creates email_sync_backup with all affected records
   ```

2. **Validation Checks** ✅
   - Check for potential duplicate emails
   - Validate email formats in KYC records
   - Identify any edge cases

3. **Create Audit Log** ✅
   ```sql
   -- Creates email_sync_audit_log for tracking changes
   ```

### Phase 2: Dry Run & Validation
4. **Preview Changes**
   - Review all 20 users that will be affected
   - Confirm KYC emails are valid
   - Check for any potential conflicts

5. **Manual Review**
   - Verify critical user accounts (admins, test users)
   - Confirm no business-critical emails will be affected

### Phase 3: Execution
6. **Execute Synchronization**
   - Insert audit records
   - Update user emails to match KYC emails
   - Update timestamps

7. **Immediate Verification**
   - Confirm no mismatches remain
   - Check for duplicate emails
   - Verify audit log completeness

### Phase 4: Post-Sync Testing
8. **Functional Testing**
   - Test login with updated emails
   - Verify email notifications work
   - Check related systems (certificates, notifications)

9. **User Communication**
   - Notify affected users of email changes
   - Update any cached email references

### Phase 5: Cleanup
10. **Final Verification**
    - 24-hour monitoring period
    - Confirm no issues reported

11. **Cleanup**
    - Archive backup table
    - Document changes in system log

## Rollback Plan
If issues arise:
1. **Immediate Rollback**: Restore from `email_sync_backup` table
2. **Verification**: Confirm all emails restored to original state
3. **Investigation**: Analyze what went wrong
4. **Fix & Retry**: Address issues and re-execute with fixes

## Risk Assessment
- **Low Risk**: Most changes are Telegram users getting real emails
- **Medium Risk**: 1 user with non-telegram email mismatch
- **Mitigation**: Complete backup and audit trail

## Success Criteria
- ✅ All 20 email mismatches resolved
- ✅ No duplicate emails created
- ✅ All users can still login
- ✅ Email notifications work correctly
- ✅ Complete audit trail maintained

## Next Steps
1. Review this plan
2. Execute Phase 1 (Safety Preparations)
3. Execute Phase 2 (Dry Run & Validation)
4. Get approval for Phase 3 (Execution)
5. Monitor and verify results
