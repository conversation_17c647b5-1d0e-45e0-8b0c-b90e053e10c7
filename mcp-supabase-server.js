#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Create MCP server
const server = new Server(
  {
    name: 'supabase-mcp-server',
    version: '1.0.0',
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

// List available tools
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [
      {
        name: 'supabase_query',
        description: 'Execute a SELECT query on Supabase database',
        inputSchema: {
          type: 'object',
          properties: {
            table: {
              type: 'string',
              description: 'Table name to query',
            },
            select: {
              type: 'string',
              description: 'Columns to select (default: *)',
              default: '*',
            },
            filter: {
              type: 'object',
              description: 'Filter conditions (e.g., {"column": "value"})',
            },
            limit: {
              type: 'number',
              description: 'Maximum number of rows to return',
            },
          },
          required: ['table'],
        },
      },
      {
        name: 'supabase_insert',
        description: 'Insert data into a Supabase table',
        inputSchema: {
          type: 'object',
          properties: {
            table: {
              type: 'string',
              description: 'Table name to insert into',
            },
            data: {
              type: 'object',
              description: 'Data to insert',
            },
          },
          required: ['table', 'data'],
        },
      },
      {
        name: 'supabase_update',
        description: 'Update data in a Supabase table',
        inputSchema: {
          type: 'object',
          properties: {
            table: {
              type: 'string',
              description: 'Table name to update',
            },
            data: {
              type: 'object',
              description: 'Data to update',
            },
            filter: {
              type: 'object',
              description: 'Filter conditions for update',
            },
          },
          required: ['table', 'data', 'filter'],
        },
      },
      {
        name: 'supabase_delete',
        description: 'Delete data from a Supabase table',
        inputSchema: {
          type: 'object',
          properties: {
            table: {
              type: 'string',
              description: 'Table name to delete from',
            },
            filter: {
              type: 'object',
              description: 'Filter conditions for deletion',
            },
          },
          required: ['table', 'filter'],
        },
      },
      {
        name: 'supabase_list_tables',
        description: 'List all tables in the Supabase database',
        inputSchema: {
          type: 'object',
          properties: {},
        },
      },
      {
        name: 'supabase_describe_table',
        description: 'Get schema information for a specific table',
        inputSchema: {
          type: 'object',
          properties: {
            table: {
              type: 'string',
              description: 'Table name to describe',
            },
          },
          required: ['table'],
        },
      },
      {
        name: 'supabase_storage_list',
        description: 'List files in a Supabase storage bucket',
        inputSchema: {
          type: 'object',
          properties: {
            bucket: {
              type: 'string',
              description: 'Storage bucket name',
            },
            path: {
              type: 'string',
              description: 'Path within bucket (optional)',
              default: '',
            },
          },
          required: ['bucket'],
        },
      },
    ],
  };
});

// Handle tool calls
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;

  try {
    switch (name) {
      case 'supabase_query': {
        let query = supabase.from(args.table).select(args.select || '*');
        
        if (args.filter) {
          Object.entries(args.filter).forEach(([key, value]) => {
            query = query.eq(key, value);
          });
        }
        
        if (args.limit) {
          query = query.limit(args.limit);
        }
        
        const { data, error } = await query;
        
        if (error) {
          return {
            content: [
              {
                type: 'text',
                text: `Error querying ${args.table}: ${error.message}`,
              },
            ],
          };
        }
        
        return {
          content: [
            {
              type: 'text',
              text: `Query results from ${args.table}:\n${JSON.stringify(data, null, 2)}`,
            },
          ],
        };
      }

      case 'supabase_insert': {
        const { data, error } = await supabase
          .from(args.table)
          .insert(args.data)
          .select();
        
        if (error) {
          return {
            content: [
              {
                type: 'text',
                text: `Error inserting into ${args.table}: ${error.message}`,
              },
            ],
          };
        }
        
        return {
          content: [
            {
              type: 'text',
              text: `Successfully inserted into ${args.table}:\n${JSON.stringify(data, null, 2)}`,
            },
          ],
        };
      }

      case 'supabase_update': {
        let query = supabase.from(args.table).update(args.data);
        
        Object.entries(args.filter).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
        
        const { data, error } = await query.select();
        
        if (error) {
          return {
            content: [
              {
                type: 'text',
                text: `Error updating ${args.table}: ${error.message}`,
              },
            ],
          };
        }
        
        return {
          content: [
            {
              type: 'text',
              text: `Successfully updated ${args.table}:\n${JSON.stringify(data, null, 2)}`,
            },
          ],
        };
      }

      case 'supabase_delete': {
        let query = supabase.from(args.table);
        
        Object.entries(args.filter).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
        
        const { data, error } = await query.delete().select();
        
        if (error) {
          return {
            content: [
              {
                type: 'text',
                text: `Error deleting from ${args.table}: ${error.message}`,
              },
            ],
          };
        }
        
        return {
          content: [
            {
              type: 'text',
              text: `Successfully deleted from ${args.table}:\n${JSON.stringify(data, null, 2)}`,
            },
          ],
        };
      }

      case 'supabase_list_tables': {
        // Query information_schema to get table names
        const { data, error } = await supabase.rpc('get_table_names');
        
        if (error) {
          // Fallback: try to query some known tables
          const knownTables = [
            'users', 'telegram_users', 'aureus_share_purchases', 
            'crypto_payment_transactions', 'referrals', 'commission_balances',
            'investment_phases', 'admin_users', 'site_content'
          ];
          
          return {
            content: [
              {
                type: 'text',
                text: `Known tables in Aureus database:\n${knownTables.join('\n')}`,
              },
            ],
          };
        }
        
        return {
          content: [
            {
              type: 'text',
              text: `Tables in database:\n${JSON.stringify(data, null, 2)}`,
            },
          ],
        };
      }

      case 'supabase_describe_table': {
        // Get table schema information
        const { data, error } = await supabase
          .from(args.table)
          .select('*')
          .limit(1);
        
        if (error) {
          return {
            content: [
              {
                type: 'text',
                text: `Error describing table ${args.table}: ${error.message}`,
              },
            ],
          };
        }
        
        const columns = data && data.length > 0 ? Object.keys(data[0]) : [];
        
        return {
          content: [
            {
              type: 'text',
              text: `Table ${args.table} columns:\n${columns.join('\n')}`,
            },
          ],
        };
      }

      case 'supabase_storage_list': {
        const { data, error } = await supabase.storage
          .from(args.bucket)
          .list(args.path || '');
        
        if (error) {
          return {
            content: [
              {
                type: 'text',
                text: `Error listing storage bucket ${args.bucket}: ${error.message}`,
              },
            ],
          };
        }
        
        return {
          content: [
            {
              type: 'text',
              text: `Files in bucket ${args.bucket}:\n${JSON.stringify(data, null, 2)}`,
            },
          ],
        };
      }

      default:
        return {
          content: [
            {
              type: 'text',
              text: `Unknown tool: ${name}`,
            },
          ],
        };
    }
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error executing ${name}: ${error.message}`,
        },
      ],
    };
  }
});

// Start the server
async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.error('Supabase MCP Server running on stdio');
}

main().catch((error) => {
  console.error('Server error:', error);
  process.exit(1);
});