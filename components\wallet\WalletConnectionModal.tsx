import React, { useState, useEffect } from 'react';
import { walletConnectionService, WalletConnectionResult } from '../../lib/services/walletConnectionService';

interface WalletConnectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onWalletConnected: (address: string) => void;
  requiredNetwork?: string;
}

export const WalletConnectionModal: React.FC<WalletConnectionModalProps> = ({
  isOpen,
  onClose,
  onWalletConnected,
  requiredNetwork = '0x38' // Default to BSC
}) => {
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [availableWallets, setAvailableWallets] = useState<string[]>([]);
  const [connectedAddress, setConnectedAddress] = useState<string | null>(null);
  const [networkInfo, setNetworkInfo] = useState<{ chainId: string; networkName: string } | null>(null);

  useEffect(() => {
    if (isOpen) {
      // Check available wallets
      const wallets = walletConnectionService.getAvailableWallets();
      setAvailableWallets(wallets);
      
      // Check if already connected
      const address = walletConnectionService.getConnectedAddress();
      if (address) {
        setConnectedAddress(address);
        loadNetworkInfo();
      }
    }
  }, [isOpen]);

  const loadNetworkInfo = async () => {
    try {
      const info = await walletConnectionService.getNetworkInfo();
      setNetworkInfo(info);
    } catch (error) {
      console.error('Failed to load network info:', error);
    }
  };

  const handleWalletConnect = async (walletType: string) => {
    setIsConnecting(true);
    setError(null);

    try {
      let result: WalletConnectionResult;

      if (walletType === 'SafePal') {
        result = await walletConnectionService.connectSafePal();
      } else if (walletType === 'MetaMask') {
        result = await walletConnectionService.connectMetaMask();
      } else {
        throw new Error('Unsupported wallet type');
      }

      if (result.success && result.address) {
        setConnectedAddress(result.address);
        await loadNetworkInfo();
        
        // Check if on correct network
        const currentNetwork = await walletConnectionService.getNetworkInfo();
        if (currentNetwork.chainId !== requiredNetwork) {
          const switched = await walletConnectionService.switchNetwork(requiredNetwork);
          if (!switched) {
            setError('Please switch to the correct network in your wallet');
            return;
          }
        }

        onWalletConnected(result.address);
      } else {
        setError(result.error || 'Failed to connect wallet');
      }
    } catch (error: any) {
      setError(error.message || 'Failed to connect wallet');
    } finally {
      setIsConnecting(false);
    }
  };

  const handleDisconnect = () => {
    walletConnectionService.disconnect();
    setConnectedAddress(null);
    setNetworkInfo(null);
    onClose();
  };

  const getNetworkName = (chainId: string): string => {
    const networks: { [key: string]: string } = {
      '0x1': 'Ethereum',
      '0x38': 'Binance Smart Chain',
      '0x89': 'Polygon',
      '0xa86a': 'Avalanche'
    };
    return networks[chainId] || 'Unknown Network';
  };

  const getRequiredNetworkName = (): string => {
    return getNetworkName(requiredNetwork);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 border border-gray-700">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-white">Connect Wallet</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white text-2xl"
          >
            ×
          </button>
        </div>

        {error && (
          <div className="bg-red-900/30 border border-red-700 rounded-lg p-3 mb-4">
            <p className="text-red-400 text-sm">{error}</p>
          </div>
        )}

        {connectedAddress ? (
          <div className="space-y-4">
            <div className="bg-green-900/30 border border-green-700 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <span className="text-green-400 text-lg">✅</span>
                <span className="text-green-400 font-medium">Wallet Connected</span>
              </div>
              <p className="text-gray-300 text-sm font-mono break-all">
                {connectedAddress}
              </p>
            </div>

            {networkInfo && (
              <div className="bg-blue-900/30 border border-blue-700 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-blue-400 text-lg">🌐</span>
                  <span className="text-blue-400 font-medium">Network</span>
                </div>
                <p className="text-gray-300 text-sm">
                  {networkInfo.networkName}
                </p>
                {networkInfo.chainId !== requiredNetwork && (
                  <p className="text-yellow-400 text-xs mt-1">
                    ⚠️ Please switch to {getRequiredNetworkName()}
                  </p>
                )}
              </div>
            )}

            <div className="flex space-x-3">
              <button
                onClick={handleDisconnect}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Disconnect
              </button>
              <button
                onClick={onClose}
                className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                Continue
              </button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="bg-blue-900/30 border border-blue-700 rounded-lg p-4 mb-4">
              <p className="text-blue-400 text-sm">
                <span className="font-medium">Required Network:</span> {getRequiredNetworkName()}
              </p>
              <p className="text-gray-400 text-xs mt-1">
                Make sure your wallet is connected to the correct network for USDT payments.
              </p>
            </div>

            {availableWallets.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-gray-400 mb-4">
                  <span className="text-4xl">👛</span>
                </div>
                <h3 className="text-white font-medium mb-2">No Wallet Detected</h3>
                <p className="text-gray-400 text-sm mb-4">
                  Please install a supported wallet extension:
                </p>
                <div className="space-y-2">
                  <a
                    href="https://chrome.google.com/webstore/detail/safepal-extension-wallet/lgmpcpglpngdoalbgeoldeajfclnhafa"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Install SafePal Wallet
                  </a>
                  <a
                    href="https://metamask.io/download/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                  >
                    Install MetaMask
                  </a>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <p className="text-gray-300 text-sm mb-4">
                  Choose a wallet to connect for secure USDT payments:
                </p>
                
                {availableWallets.map((wallet) => (
                  <button
                    key={wallet}
                    onClick={() => handleWalletConnect(wallet)}
                    disabled={isConnecting}
                    className="w-full flex items-center justify-between p-4 bg-gray-700 hover:bg-gray-600 rounded-lg border border-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-bold">
                          {wallet === 'SafePal' ? 'SP' : 'MM'}
                        </span>
                      </div>
                      <div className="text-left">
                        <p className="text-white font-medium">{wallet}</p>
                        <p className="text-gray-400 text-xs">
                          {wallet === 'SafePal' ? 'Recommended' : 'Alternative'}
                        </p>
                      </div>
                    </div>
                    {isConnecting ? (
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-yellow-500"></div>
                    ) : (
                      <span className="text-gray-400">→</span>
                    )}
                  </button>
                ))}
              </div>
            )}
          </div>
        )}

        <div className="mt-6 pt-4 border-t border-gray-700">
          <p className="text-gray-400 text-xs text-center">
            🔒 Your wallet connection is secure and encrypted. We never store your private keys.
          </p>
        </div>
      </div>
    </div>
  );
};
