import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function setupWithdrawalProofsBucket() {
  console.log('🗄️ Setting up withdrawal proofs storage bucket...')

  try {
    // Check if bucket already exists
    const { data: buckets, error: listError } = await supabase.storage.listBuckets()
    
    if (listError) {
      console.error('❌ Error listing buckets:', listError)
      return
    }

    const bucketExists = buckets.some(bucket => bucket.name === 'withdrawal_proofs')
    
    if (bucketExists) {
      console.log('✅ withdrawal_proofs bucket already exists')
    } else {
      // Create the bucket
      const { data, error } = await supabase.storage.createBucket('withdrawal_proofs', {
        public: true,
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/pdf'],
        fileSizeLimit: 10485760 // 10MB limit
      })

      if (error) {
        console.error('❌ Error creating bucket:', error)
        return
      }

      console.log('✅ withdrawal_proofs bucket created successfully')
    }

    // Set up RLS policies for the bucket
    console.log('🔒 Setting up RLS policies...')
    
    // Allow service role to manage all files
    const serviceRolePolicy = `
      CREATE POLICY "Service role can manage withdrawal proofs" ON storage.objects
      FOR ALL USING (bucket_id = 'withdrawal_proofs' AND auth.role() = 'service_role')
      WITH CHECK (bucket_id = 'withdrawal_proofs' AND auth.role() = 'service_role');
    `

    // Allow admins to view and upload files
    const adminPolicy = `
      CREATE POLICY "Admins can manage withdrawal proofs" ON storage.objects
      FOR ALL USING (
        bucket_id = 'withdrawal_proofs' AND 
        auth.uid() IN (
          SELECT auth_user_id FROM users 
          WHERE id IN (
            SELECT user_id FROM admin_users WHERE email = auth.email()
          )
        )
      )
      WITH CHECK (
        bucket_id = 'withdrawal_proofs' AND 
        auth.uid() IN (
          SELECT auth_user_id FROM users 
          WHERE id IN (
            SELECT user_id FROM admin_users WHERE email = auth.email()
          )
        )
      );
    `

    // Allow public read access for viewing proofs
    const publicReadPolicy = `
      CREATE POLICY "Public can view withdrawal proofs" ON storage.objects
      FOR SELECT USING (bucket_id = 'withdrawal_proofs');
    `

    try {
      // Note: These policies might already exist, so we'll catch errors
      await supabase.rpc('exec_sql', { sql_query: serviceRolePolicy })
      console.log('✅ Service role policy created')
    } catch (error) {
      console.log('ℹ️ Service role policy might already exist')
    }

    try {
      await supabase.rpc('exec_sql', { sql_query: adminPolicy })
      console.log('✅ Admin policy created')
    } catch (error) {
      console.log('ℹ️ Admin policy might already exist')
    }

    try {
      await supabase.rpc('exec_sql', { sql_query: publicReadPolicy })
      console.log('✅ Public read policy created')
    } catch (error) {
      console.log('ℹ️ Public read policy might already exist')
    }

    console.log('\n🎉 Withdrawal proofs storage setup completed!')
    console.log('📁 Bucket: withdrawal_proofs')
    console.log('🔒 RLS policies configured')
    console.log('📏 File size limit: 10MB')
    console.log('📄 Allowed types: JPEG, PNG, GIF, WebP, PDF')

  } catch (error) {
    console.error('❌ Setup failed:', error)
  }
}

// Run the setup
setupWithdrawalProofsBucket()
