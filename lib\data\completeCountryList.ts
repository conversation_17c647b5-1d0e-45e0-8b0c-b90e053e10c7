/**
 * COMPLETE WORLD COUNTRY LIST
 * 
 * This file contains all 195 UN-recognized sovereign states plus commonly included territories
 * for comprehensive country selection across the Aureus Africa website.
 * 
 * Based on ISO 3166-1 alpha-3 country codes
 * Updated: 2024
 */

export interface CompleteCountry {
  code: string; // ISO 3166-1 alpha-3
  name: string;
  flag: string;
  region: 'Africa' | 'Asia' | 'Europe' | 'North America' | 'South America' | 'Oceania';
  subregion: string;
  currency: string;
  currencySymbol: string;
}

export const COMPLETE_COUNTRY_LIST: CompleteCountry[] = [
  // Africa (54 countries)
  { code: 'DZA', name: 'Algeria', flag: '🇩🇿', region: 'Africa', subregion: 'Northern Africa', currency: 'DZD', currencySymbol: 'د.ج' },
  { code: 'AGO', name: 'Angola', flag: '🇦🇴', region: 'Africa', subregion: 'Middle Africa', currency: 'AOA', currencySymbol: 'Kz' },
  { code: 'BEN', name: 'Benin', flag: '🇧🇯', region: 'Africa', subregion: 'Western Africa', currency: 'XOF', currencySymbol: 'CFA' },
  { code: 'BWA', name: 'Botswana', flag: '🇧🇼', region: 'Africa', subregion: 'Southern Africa', currency: 'BWP', currencySymbol: 'P' },
  { code: 'BFA', name: 'Burkina Faso', flag: '🇧🇫', region: 'Africa', subregion: 'Western Africa', currency: 'XOF', currencySymbol: 'CFA' },
  { code: 'BDI', name: 'Burundi', flag: '🇧🇮', region: 'Africa', subregion: 'Eastern Africa', currency: 'BIF', currencySymbol: 'FBu' },
  { code: 'CPV', name: 'Cape Verde', flag: '🇨🇻', region: 'Africa', subregion: 'Western Africa', currency: 'CVE', currencySymbol: '$' },
  { code: 'CMR', name: 'Cameroon', flag: '🇨🇲', region: 'Africa', subregion: 'Middle Africa', currency: 'XAF', currencySymbol: 'FCFA' },
  { code: 'CAF', name: 'Central African Republic', flag: '🇨🇫', region: 'Africa', subregion: 'Middle Africa', currency: 'XAF', currencySymbol: 'FCFA' },
  { code: 'TCD', name: 'Chad', flag: '🇹🇩', region: 'Africa', subregion: 'Middle Africa', currency: 'XAF', currencySymbol: 'FCFA' },
  { code: 'COM', name: 'Comoros', flag: '🇰🇲', region: 'Africa', subregion: 'Eastern Africa', currency: 'KMF', currencySymbol: 'CF' },
  { code: 'COG', name: 'Republic of the Congo', flag: '🇨🇬', region: 'Africa', subregion: 'Middle Africa', currency: 'XAF', currencySymbol: 'FCFA' },
  { code: 'COD', name: 'Democratic Republic of the Congo', flag: '🇨🇩', region: 'Africa', subregion: 'Middle Africa', currency: 'CDF', currencySymbol: 'FC' },
  { code: 'CIV', name: 'Côte d\'Ivoire', flag: '🇨🇮', region: 'Africa', subregion: 'Western Africa', currency: 'XOF', currencySymbol: 'CFA' },
  { code: 'DJI', name: 'Djibouti', flag: '🇩🇯', region: 'Africa', subregion: 'Eastern Africa', currency: 'DJF', currencySymbol: 'Fdj' },
  { code: 'EGY', name: 'Egypt', flag: '🇪🇬', region: 'Africa', subregion: 'Northern Africa', currency: 'EGP', currencySymbol: '£' },
  { code: 'GNQ', name: 'Equatorial Guinea', flag: '🇬🇶', region: 'Africa', subregion: 'Middle Africa', currency: 'XAF', currencySymbol: 'FCFA' },
  { code: 'ERI', name: 'Eritrea', flag: '🇪🇷', region: 'Africa', subregion: 'Eastern Africa', currency: 'ERN', currencySymbol: 'Nfk' },
  { code: 'SWZ', name: 'Eswatini', flag: '🇸🇿', region: 'Africa', subregion: 'Southern Africa', currency: 'SZL', currencySymbol: 'L' },
  { code: 'ETH', name: 'Ethiopia', flag: '🇪🇹', region: 'Africa', subregion: 'Eastern Africa', currency: 'ETB', currencySymbol: 'Br' },
  { code: 'GAB', name: 'Gabon', flag: '🇬🇦', region: 'Africa', subregion: 'Middle Africa', currency: 'XAF', currencySymbol: 'FCFA' },
  { code: 'GMB', name: 'Gambia', flag: '🇬🇲', region: 'Africa', subregion: 'Western Africa', currency: 'GMD', currencySymbol: 'D' },
  { code: 'GHA', name: 'Ghana', flag: '🇬🇭', region: 'Africa', subregion: 'Western Africa', currency: 'GHS', currencySymbol: '₵' },
  { code: 'GIN', name: 'Guinea', flag: '🇬🇳', region: 'Africa', subregion: 'Western Africa', currency: 'GNF', currencySymbol: 'FG' },
  { code: 'GNB', name: 'Guinea-Bissau', flag: '🇬🇼', region: 'Africa', subregion: 'Western Africa', currency: 'XOF', currencySymbol: 'CFA' },
  { code: 'KEN', name: 'Kenya', flag: '🇰🇪', region: 'Africa', subregion: 'Eastern Africa', currency: 'KES', currencySymbol: 'KSh' },
  { code: 'LSO', name: 'Lesotho', flag: '🇱🇸', region: 'Africa', subregion: 'Southern Africa', currency: 'LSL', currencySymbol: 'L' },
  { code: 'LBR', name: 'Liberia', flag: '🇱🇷', region: 'Africa', subregion: 'Western Africa', currency: 'LRD', currencySymbol: '$' },
  { code: 'LBY', name: 'Libya', flag: '🇱🇾', region: 'Africa', subregion: 'Northern Africa', currency: 'LYD', currencySymbol: 'ل.د' },
  { code: 'MDG', name: 'Madagascar', flag: '🇲🇬', region: 'Africa', subregion: 'Eastern Africa', currency: 'MGA', currencySymbol: 'Ar' },
  { code: 'MWI', name: 'Malawi', flag: '🇲🇼', region: 'Africa', subregion: 'Eastern Africa', currency: 'MWK', currencySymbol: 'MK' },
  { code: 'MLI', name: 'Mali', flag: '🇲🇱', region: 'Africa', subregion: 'Western Africa', currency: 'XOF', currencySymbol: 'CFA' },
  { code: 'MRT', name: 'Mauritania', flag: '🇲🇷', region: 'Africa', subregion: 'Western Africa', currency: 'MRU', currencySymbol: 'UM' },
  { code: 'MUS', name: 'Mauritius', flag: '🇲🇺', region: 'Africa', subregion: 'Eastern Africa', currency: 'MUR', currencySymbol: '₨' },
  { code: 'MAR', name: 'Morocco', flag: '🇲🇦', region: 'Africa', subregion: 'Northern Africa', currency: 'MAD', currencySymbol: 'د.م.' },
  { code: 'MOZ', name: 'Mozambique', flag: '🇲🇿', region: 'Africa', subregion: 'Eastern Africa', currency: 'MZN', currencySymbol: 'MT' },
  { code: 'NAM', name: 'Namibia', flag: '🇳🇦', region: 'Africa', subregion: 'Southern Africa', currency: 'NAD', currencySymbol: 'N$' },
  { code: 'NER', name: 'Niger', flag: '🇳🇪', region: 'Africa', subregion: 'Western Africa', currency: 'XOF', currencySymbol: 'CFA' },
  { code: 'NGA', name: 'Nigeria', flag: '🇳🇬', region: 'Africa', subregion: 'Western Africa', currency: 'NGN', currencySymbol: '₦' },
  { code: 'RWA', name: 'Rwanda', flag: '🇷🇼', region: 'Africa', subregion: 'Eastern Africa', currency: 'RWF', currencySymbol: 'RF' },
  { code: 'STP', name: 'São Tomé and Príncipe', flag: '🇸🇹', region: 'Africa', subregion: 'Middle Africa', currency: 'STN', currencySymbol: 'Db' },
  { code: 'SEN', name: 'Senegal', flag: '🇸🇳', region: 'Africa', subregion: 'Western Africa', currency: 'XOF', currencySymbol: 'CFA' },
  { code: 'SYC', name: 'Seychelles', flag: '🇸🇨', region: 'Africa', subregion: 'Eastern Africa', currency: 'SCR', currencySymbol: '₨' },
  { code: 'SLE', name: 'Sierra Leone', flag: '🇸🇱', region: 'Africa', subregion: 'Western Africa', currency: 'SLL', currencySymbol: 'Le' },
  { code: 'SOM', name: 'Somalia', flag: '🇸🇴', region: 'Africa', subregion: 'Eastern Africa', currency: 'SOS', currencySymbol: 'S' },
  { code: 'ZAF', name: 'South Africa', flag: '🇿🇦', region: 'Africa', subregion: 'Southern Africa', currency: 'ZAR', currencySymbol: 'R' },
  { code: 'SSD', name: 'South Sudan', flag: '🇸🇸', region: 'Africa', subregion: 'Eastern Africa', currency: 'SSP', currencySymbol: '£' },
  { code: 'SDN', name: 'Sudan', flag: '🇸🇩', region: 'Africa', subregion: 'Northern Africa', currency: 'SDG', currencySymbol: 'ج.س.' },
  { code: 'TZA', name: 'Tanzania', flag: '🇹🇿', region: 'Africa', subregion: 'Eastern Africa', currency: 'TZS', currencySymbol: 'TSh' },
  { code: 'TGO', name: 'Togo', flag: '🇹🇬', region: 'Africa', subregion: 'Western Africa', currency: 'XOF', currencySymbol: 'CFA' },
  { code: 'TUN', name: 'Tunisia', flag: '🇹🇳', region: 'Africa', subregion: 'Northern Africa', currency: 'TND', currencySymbol: 'د.ت' },
  { code: 'UGA', name: 'Uganda', flag: '🇺🇬', region: 'Africa', subregion: 'Eastern Africa', currency: 'UGX', currencySymbol: 'USh' },
  { code: 'ZMB', name: 'Zambia', flag: '🇿🇲', region: 'Africa', subregion: 'Eastern Africa', currency: 'ZMW', currencySymbol: 'ZK' },
  { code: 'ZWE', name: 'Zimbabwe', flag: '🇿🇼', region: 'Africa', subregion: 'Eastern Africa', currency: 'ZWL', currencySymbol: 'Z$' },

  // Asia (49 countries)
  { code: 'AFG', name: 'Afghanistan', flag: '🇦🇫', region: 'Asia', subregion: 'Southern Asia', currency: 'AFN', currencySymbol: '؋' },
  { code: 'ARM', name: 'Armenia', flag: '🇦🇲', region: 'Asia', subregion: 'Western Asia', currency: 'AMD', currencySymbol: '֏' },
  { code: 'AZE', name: 'Azerbaijan', flag: '🇦🇿', region: 'Asia', subregion: 'Western Asia', currency: 'AZN', currencySymbol: '₼' },
  { code: 'BHR', name: 'Bahrain', flag: '🇧🇭', region: 'Asia', subregion: 'Western Asia', currency: 'BHD', currencySymbol: '.د.ب' },
  { code: 'BGD', name: 'Bangladesh', flag: '🇧🇩', region: 'Asia', subregion: 'Southern Asia', currency: 'BDT', currencySymbol: '৳' },
  { code: 'BTN', name: 'Bhutan', flag: '🇧🇹', region: 'Asia', subregion: 'Southern Asia', currency: 'BTN', currencySymbol: 'Nu.' },
  { code: 'BRN', name: 'Brunei', flag: '🇧🇳', region: 'Asia', subregion: 'South-Eastern Asia', currency: 'BND', currencySymbol: '$' },
  { code: 'KHM', name: 'Cambodia', flag: '🇰🇭', region: 'Asia', subregion: 'South-Eastern Asia', currency: 'KHR', currencySymbol: '៛' },
  { code: 'CHN', name: 'China', flag: '🇨🇳', region: 'Asia', subregion: 'Eastern Asia', currency: 'CNY', currencySymbol: '¥' },
  { code: 'CYP', name: 'Cyprus', flag: '🇨🇾', region: 'Asia', subregion: 'Western Asia', currency: 'EUR', currencySymbol: '€' },
  { code: 'GEO', name: 'Georgia', flag: '🇬🇪', region: 'Asia', subregion: 'Western Asia', currency: 'GEL', currencySymbol: '₾' },
  { code: 'IND', name: 'India', flag: '🇮🇳', region: 'Asia', subregion: 'Southern Asia', currency: 'INR', currencySymbol: '₹' },
  { code: 'IDN', name: 'Indonesia', flag: '🇮🇩', region: 'Asia', subregion: 'South-Eastern Asia', currency: 'IDR', currencySymbol: 'Rp' },
  { code: 'IRN', name: 'Iran', flag: '🇮🇷', region: 'Asia', subregion: 'Southern Asia', currency: 'IRR', currencySymbol: '﷼' },
  { code: 'IRQ', name: 'Iraq', flag: '🇮🇶', region: 'Asia', subregion: 'Western Asia', currency: 'IQD', currencySymbol: 'ع.د' },
  { code: 'ISR', name: 'Israel', flag: '🇮🇱', region: 'Asia', subregion: 'Western Asia', currency: 'ILS', currencySymbol: '₪' },
  { code: 'JPN', name: 'Japan', flag: '🇯🇵', region: 'Asia', subregion: 'Eastern Asia', currency: 'JPY', currencySymbol: '¥' },
  { code: 'JOR', name: 'Jordan', flag: '🇯🇴', region: 'Asia', subregion: 'Western Asia', currency: 'JOD', currencySymbol: 'د.ا' },
  { code: 'KAZ', name: 'Kazakhstan', flag: '🇰🇿', region: 'Asia', subregion: 'Central Asia', currency: 'KZT', currencySymbol: '₸' },
  { code: 'KWT', name: 'Kuwait', flag: '🇰🇼', region: 'Asia', subregion: 'Western Asia', currency: 'KWD', currencySymbol: 'د.ك' },
  { code: 'KGZ', name: 'Kyrgyzstan', flag: '🇰🇬', region: 'Asia', subregion: 'Central Asia', currency: 'KGS', currencySymbol: 'лв' },
  { code: 'LAO', name: 'Laos', flag: '🇱🇦', region: 'Asia', subregion: 'South-Eastern Asia', currency: 'LAK', currencySymbol: '₭' },
  { code: 'LBN', name: 'Lebanon', flag: '🇱🇧', region: 'Asia', subregion: 'Western Asia', currency: 'LBP', currencySymbol: 'ل.ل' },
  { code: 'MYS', name: 'Malaysia', flag: '🇲🇾', region: 'Asia', subregion: 'South-Eastern Asia', currency: 'MYR', currencySymbol: 'RM' },
  { code: 'MDV', name: 'Maldives', flag: '🇲🇻', region: 'Asia', subregion: 'Southern Asia', currency: 'MVR', currencySymbol: '.ރ' },
  { code: 'MNG', name: 'Mongolia', flag: '🇲🇳', region: 'Asia', subregion: 'Eastern Asia', currency: 'MNT', currencySymbol: '₮' },
  { code: 'MMR', name: 'Myanmar', flag: '🇲🇲', region: 'Asia', subregion: 'South-Eastern Asia', currency: 'MMK', currencySymbol: 'Ks' },
  { code: 'NPL', name: 'Nepal', flag: '🇳🇵', region: 'Asia', subregion: 'Southern Asia', currency: 'NPR', currencySymbol: '₨' },
  { code: 'PRK', name: 'North Korea', flag: '🇰🇵', region: 'Asia', subregion: 'Eastern Asia', currency: 'KPW', currencySymbol: '₩' },
  { code: 'OMN', name: 'Oman', flag: '🇴🇲', region: 'Asia', subregion: 'Western Asia', currency: 'OMR', currencySymbol: 'ر.ع.' },
  { code: 'PAK', name: 'Pakistan', flag: '🇵🇰', region: 'Asia', subregion: 'Southern Asia', currency: 'PKR', currencySymbol: '₨' },
  { code: 'PSE', name: 'Palestine', flag: '🇵🇸', region: 'Asia', subregion: 'Western Asia', currency: 'ILS', currencySymbol: '₪' },
  { code: 'PHL', name: 'Philippines', flag: '🇵🇭', region: 'Asia', subregion: 'South-Eastern Asia', currency: 'PHP', currencySymbol: '₱' },
  { code: 'QAT', name: 'Qatar', flag: '🇶🇦', region: 'Asia', subregion: 'Western Asia', currency: 'QAR', currencySymbol: 'ر.ق' },
  { code: 'SAU', name: 'Saudi Arabia', flag: '🇸🇦', region: 'Asia', subregion: 'Western Asia', currency: 'SAR', currencySymbol: 'ر.س' },
  { code: 'SGP', name: 'Singapore', flag: '🇸🇬', region: 'Asia', subregion: 'South-Eastern Asia', currency: 'SGD', currencySymbol: '$' },
  { code: 'KOR', name: 'South Korea', flag: '🇰🇷', region: 'Asia', subregion: 'Eastern Asia', currency: 'KRW', currencySymbol: '₩' },
  { code: 'LKA', name: 'Sri Lanka', flag: '🇱🇰', region: 'Asia', subregion: 'Southern Asia', currency: 'LKR', currencySymbol: '₨' },
  { code: 'SYR', name: 'Syria', flag: '🇸🇾', region: 'Asia', subregion: 'Western Asia', currency: 'SYP', currencySymbol: '£' },
  { code: 'TWN', name: 'Taiwan', flag: '🇹🇼', region: 'Asia', subregion: 'Eastern Asia', currency: 'TWD', currencySymbol: 'NT$' },
  { code: 'TJK', name: 'Tajikistan', flag: '🇹🇯', region: 'Asia', subregion: 'Central Asia', currency: 'TJS', currencySymbol: 'SM' },
  { code: 'THA', name: 'Thailand', flag: '🇹🇭', region: 'Asia', subregion: 'South-Eastern Asia', currency: 'THB', currencySymbol: '฿' },
  { code: 'TLS', name: 'Timor-Leste', flag: '🇹🇱', region: 'Asia', subregion: 'South-Eastern Asia', currency: 'USD', currencySymbol: '$' },
  { code: 'TUR', name: 'Turkey', flag: '🇹🇷', region: 'Asia', subregion: 'Western Asia', currency: 'TRY', currencySymbol: '₺' },
  { code: 'TKM', name: 'Turkmenistan', flag: '🇹🇲', region: 'Asia', subregion: 'Central Asia', currency: 'TMT', currencySymbol: 'T' },
  { code: 'ARE', name: 'United Arab Emirates', flag: '🇦🇪', region: 'Asia', subregion: 'Western Asia', currency: 'AED', currencySymbol: 'د.إ' },
  { code: 'UZB', name: 'Uzbekistan', flag: '🇺🇿', region: 'Asia', subregion: 'Central Asia', currency: 'UZS', currencySymbol: 'лв' },
  { code: 'VNM', name: 'Vietnam', flag: '🇻🇳', region: 'Asia', subregion: 'South-Eastern Asia', currency: 'VND', currencySymbol: '₫' },
  { code: 'YEM', name: 'Yemen', flag: '🇾🇪', region: 'Asia', subregion: 'Western Asia', currency: 'YER', currencySymbol: '﷼' },

  // Europe (44 countries)
  { code: 'ALB', name: 'Albania', flag: '🇦🇱', region: 'Europe', subregion: 'Southern Europe', currency: 'ALL', currencySymbol: 'L' },
  { code: 'AND', name: 'Andorra', flag: '🇦🇩', region: 'Europe', subregion: 'Southern Europe', currency: 'EUR', currencySymbol: '€' },
  { code: 'AUT', name: 'Austria', flag: '🇦🇹', region: 'Europe', subregion: 'Western Europe', currency: 'EUR', currencySymbol: '€' },
  { code: 'BLR', name: 'Belarus', flag: '🇧🇾', region: 'Europe', subregion: 'Eastern Europe', currency: 'BYN', currencySymbol: 'Br' },
  { code: 'BEL', name: 'Belgium', flag: '🇧🇪', region: 'Europe', subregion: 'Western Europe', currency: 'EUR', currencySymbol: '€' },
  { code: 'BIH', name: 'Bosnia and Herzegovina', flag: '🇧🇦', region: 'Europe', subregion: 'Southern Europe', currency: 'BAM', currencySymbol: 'KM' },
  { code: 'BGR', name: 'Bulgaria', flag: '🇧🇬', region: 'Europe', subregion: 'Eastern Europe', currency: 'BGN', currencySymbol: 'лв' },
  { code: 'HRV', name: 'Croatia', flag: '🇭🇷', region: 'Europe', subregion: 'Southern Europe', currency: 'EUR', currencySymbol: '€' },
  { code: 'CZE', name: 'Czech Republic', flag: '🇨🇿', region: 'Europe', subregion: 'Eastern Europe', currency: 'CZK', currencySymbol: 'Kč' },
  { code: 'DNK', name: 'Denmark', flag: '🇩🇰', region: 'Europe', subregion: 'Northern Europe', currency: 'DKK', currencySymbol: 'kr' },
  { code: 'EST', name: 'Estonia', flag: '🇪🇪', region: 'Europe', subregion: 'Northern Europe', currency: 'EUR', currencySymbol: '€' },
  { code: 'FIN', name: 'Finland', flag: '🇫🇮', region: 'Europe', subregion: 'Northern Europe', currency: 'EUR', currencySymbol: '€' },
  { code: 'FRA', name: 'France', flag: '🇫🇷', region: 'Europe', subregion: 'Western Europe', currency: 'EUR', currencySymbol: '€' },
  { code: 'DEU', name: 'Germany', flag: '🇩🇪', region: 'Europe', subregion: 'Western Europe', currency: 'EUR', currencySymbol: '€' },
  { code: 'GRC', name: 'Greece', flag: '🇬🇷', region: 'Europe', subregion: 'Southern Europe', currency: 'EUR', currencySymbol: '€' },
  { code: 'HUN', name: 'Hungary', flag: '🇭🇺', region: 'Europe', subregion: 'Eastern Europe', currency: 'HUF', currencySymbol: 'Ft' },
  { code: 'ISL', name: 'Iceland', flag: '🇮🇸', region: 'Europe', subregion: 'Northern Europe', currency: 'ISK', currencySymbol: 'kr' },
  { code: 'IRL', name: 'Ireland', flag: '🇮🇪', region: 'Europe', subregion: 'Northern Europe', currency: 'EUR', currencySymbol: '€' },
  { code: 'ITA', name: 'Italy', flag: '🇮🇹', region: 'Europe', subregion: 'Southern Europe', currency: 'EUR', currencySymbol: '€' },
  { code: 'XKX', name: 'Kosovo', flag: '🇽🇰', region: 'Europe', subregion: 'Southern Europe', currency: 'EUR', currencySymbol: '€' },
  { code: 'LVA', name: 'Latvia', flag: '🇱🇻', region: 'Europe', subregion: 'Northern Europe', currency: 'EUR', currencySymbol: '€' },
  { code: 'LIE', name: 'Liechtenstein', flag: '🇱🇮', region: 'Europe', subregion: 'Western Europe', currency: 'CHF', currencySymbol: 'Fr' },
  { code: 'LTU', name: 'Lithuania', flag: '🇱🇹', region: 'Europe', subregion: 'Northern Europe', currency: 'EUR', currencySymbol: '€' },
  { code: 'LUX', name: 'Luxembourg', flag: '🇱🇺', region: 'Europe', subregion: 'Western Europe', currency: 'EUR', currencySymbol: '€' },
  { code: 'MLT', name: 'Malta', flag: '🇲🇹', region: 'Europe', subregion: 'Southern Europe', currency: 'EUR', currencySymbol: '€' },
  { code: 'MDA', name: 'Moldova', flag: '🇲🇩', region: 'Europe', subregion: 'Eastern Europe', currency: 'MDL', currencySymbol: 'L' },
  { code: 'MCO', name: 'Monaco', flag: '🇲🇨', region: 'Europe', subregion: 'Western Europe', currency: 'EUR', currencySymbol: '€' },
  { code: 'MNE', name: 'Montenegro', flag: '🇲🇪', region: 'Europe', subregion: 'Southern Europe', currency: 'EUR', currencySymbol: '€' },
  { code: 'NLD', name: 'Netherlands', flag: '🇳🇱', region: 'Europe', subregion: 'Western Europe', currency: 'EUR', currencySymbol: '€' },
  { code: 'MKD', name: 'North Macedonia', flag: '🇲🇰', region: 'Europe', subregion: 'Southern Europe', currency: 'MKD', currencySymbol: 'ден' },
  { code: 'NOR', name: 'Norway', flag: '🇳🇴', region: 'Europe', subregion: 'Northern Europe', currency: 'NOK', currencySymbol: 'kr' },
  { code: 'POL', name: 'Poland', flag: '🇵🇱', region: 'Europe', subregion: 'Eastern Europe', currency: 'PLN', currencySymbol: 'zł' },
  { code: 'PRT', name: 'Portugal', flag: '🇵🇹', region: 'Europe', subregion: 'Southern Europe', currency: 'EUR', currencySymbol: '€' },
  { code: 'ROU', name: 'Romania', flag: '🇷🇴', region: 'Europe', subregion: 'Eastern Europe', currency: 'RON', currencySymbol: 'lei' },
  { code: 'RUS', name: 'Russia', flag: '🇷🇺', region: 'Europe', subregion: 'Eastern Europe', currency: 'RUB', currencySymbol: '₽' },
  { code: 'SMR', name: 'San Marino', flag: '🇸🇲', region: 'Europe', subregion: 'Southern Europe', currency: 'EUR', currencySymbol: '€' },
  { code: 'SRB', name: 'Serbia', flag: '🇷🇸', region: 'Europe', subregion: 'Southern Europe', currency: 'RSD', currencySymbol: 'Дин.' },
  { code: 'SVK', name: 'Slovakia', flag: '🇸🇰', region: 'Europe', subregion: 'Eastern Europe', currency: 'EUR', currencySymbol: '€' },
  { code: 'SVN', name: 'Slovenia', flag: '🇸🇮', region: 'Europe', subregion: 'Southern Europe', currency: 'EUR', currencySymbol: '€' },
  { code: 'ESP', name: 'Spain', flag: '🇪🇸', region: 'Europe', subregion: 'Southern Europe', currency: 'EUR', currencySymbol: '€' },
  { code: 'SWE', name: 'Sweden', flag: '🇸🇪', region: 'Europe', subregion: 'Northern Europe', currency: 'SEK', currencySymbol: 'kr' },
  { code: 'CHE', name: 'Switzerland', flag: '🇨🇭', region: 'Europe', subregion: 'Western Europe', currency: 'CHF', currencySymbol: 'Fr' },
  { code: 'UKR', name: 'Ukraine', flag: '🇺🇦', region: 'Europe', subregion: 'Eastern Europe', currency: 'UAH', currencySymbol: '₴' },
  { code: 'GBR', name: 'United Kingdom', flag: '🇬🇧', region: 'Europe', subregion: 'Northern Europe', currency: 'GBP', currencySymbol: '£' },
  { code: 'VAT', name: 'Vatican City', flag: '🇻🇦', region: 'Europe', subregion: 'Southern Europe', currency: 'EUR', currencySymbol: '€' },

  // North America (23 countries)
  { code: 'ATG', name: 'Antigua and Barbuda', flag: '🇦🇬', region: 'North America', subregion: 'Caribbean', currency: 'XCD', currencySymbol: '$' },
  { code: 'BHS', name: 'Bahamas', flag: '🇧🇸', region: 'North America', subregion: 'Caribbean', currency: 'BSD', currencySymbol: '$' },
  { code: 'BRB', name: 'Barbados', flag: '🇧🇧', region: 'North America', subregion: 'Caribbean', currency: 'BBD', currencySymbol: '$' },
  { code: 'BLZ', name: 'Belize', flag: '🇧🇿', region: 'North America', subregion: 'Central America', currency: 'BZD', currencySymbol: 'BZ$' },
  { code: 'CAN', name: 'Canada', flag: '🇨🇦', region: 'North America', subregion: 'Northern America', currency: 'CAD', currencySymbol: 'C$' },
  { code: 'CRI', name: 'Costa Rica', flag: '🇨🇷', region: 'North America', subregion: 'Central America', currency: 'CRC', currencySymbol: '₡' },
  { code: 'CUB', name: 'Cuba', flag: '🇨🇺', region: 'North America', subregion: 'Caribbean', currency: 'CUP', currencySymbol: '₱' },
  { code: 'DMA', name: 'Dominica', flag: '🇩🇲', region: 'North America', subregion: 'Caribbean', currency: 'XCD', currencySymbol: '$' },
  { code: 'DOM', name: 'Dominican Republic', flag: '🇩🇴', region: 'North America', subregion: 'Caribbean', currency: 'DOP', currencySymbol: 'RD$' },
  { code: 'SLV', name: 'El Salvador', flag: '🇸🇻', region: 'North America', subregion: 'Central America', currency: 'USD', currencySymbol: '$' },
  { code: 'GRD', name: 'Grenada', flag: '🇬🇩', region: 'North America', subregion: 'Caribbean', currency: 'XCD', currencySymbol: '$' },
  { code: 'GTM', name: 'Guatemala', flag: '🇬🇹', region: 'North America', subregion: 'Central America', currency: 'GTQ', currencySymbol: 'Q' },
  { code: 'HTI', name: 'Haiti', flag: '🇭🇹', region: 'North America', subregion: 'Caribbean', currency: 'HTG', currencySymbol: 'G' },
  { code: 'HND', name: 'Honduras', flag: '🇭🇳', region: 'North America', subregion: 'Central America', currency: 'HNL', currencySymbol: 'L' },
  { code: 'JAM', name: 'Jamaica', flag: '🇯🇲', region: 'North America', subregion: 'Caribbean', currency: 'JMD', currencySymbol: 'J$' },
  { code: 'MEX', name: 'Mexico', flag: '🇲🇽', region: 'North America', subregion: 'Central America', currency: 'MXN', currencySymbol: '$' },
  { code: 'NIC', name: 'Nicaragua', flag: '🇳🇮', region: 'North America', subregion: 'Central America', currency: 'NIO', currencySymbol: 'C$' },
  { code: 'PAN', name: 'Panama', flag: '🇵🇦', region: 'North America', subregion: 'Central America', currency: 'PAB', currencySymbol: 'B/.' },
  { code: 'KNA', name: 'Saint Kitts and Nevis', flag: '🇰🇳', region: 'North America', subregion: 'Caribbean', currency: 'XCD', currencySymbol: '$' },
  { code: 'LCA', name: 'Saint Lucia', flag: '🇱🇨', region: 'North America', subregion: 'Caribbean', currency: 'XCD', currencySymbol: '$' },
  { code: 'VCT', name: 'Saint Vincent and the Grenadines', flag: '🇻🇨', region: 'North America', subregion: 'Caribbean', currency: 'XCD', currencySymbol: '$' },
  { code: 'TTO', name: 'Trinidad and Tobago', flag: '🇹🇹', region: 'North America', subregion: 'Caribbean', currency: 'TTD', currencySymbol: 'TT$' },
  { code: 'USA', name: 'United States', flag: '🇺🇸', region: 'North America', subregion: 'Northern America', currency: 'USD', currencySymbol: '$' },

  // South America (12 countries)
  { code: 'ARG', name: 'Argentina', flag: '🇦🇷', region: 'South America', subregion: 'South America', currency: 'ARS', currencySymbol: '$' },
  { code: 'BOL', name: 'Bolivia', flag: '🇧🇴', region: 'South America', subregion: 'South America', currency: 'BOB', currencySymbol: '$b' },
  { code: 'BRA', name: 'Brazil', flag: '🇧🇷', region: 'South America', subregion: 'South America', currency: 'BRL', currencySymbol: 'R$' },
  { code: 'CHL', name: 'Chile', flag: '🇨🇱', region: 'South America', subregion: 'South America', currency: 'CLP', currencySymbol: '$' },
  { code: 'COL', name: 'Colombia', flag: '🇨🇴', region: 'South America', subregion: 'South America', currency: 'COP', currencySymbol: '$' },
  { code: 'ECU', name: 'Ecuador', flag: '🇪🇨', region: 'South America', subregion: 'South America', currency: 'USD', currencySymbol: '$' },
  { code: 'GUY', name: 'Guyana', flag: '🇬🇾', region: 'South America', subregion: 'South America', currency: 'GYD', currencySymbol: '$' },
  { code: 'PRY', name: 'Paraguay', flag: '🇵🇾', region: 'South America', subregion: 'South America', currency: 'PYG', currencySymbol: 'Gs' },
  { code: 'PER', name: 'Peru', flag: '🇵🇪', region: 'South America', subregion: 'South America', currency: 'PEN', currencySymbol: 'S/.' },
  { code: 'SUR', name: 'Suriname', flag: '🇸🇷', region: 'South America', subregion: 'South America', currency: 'SRD', currencySymbol: '$' },
  { code: 'URY', name: 'Uruguay', flag: '🇺🇾', region: 'South America', subregion: 'South America', currency: 'UYU', currencySymbol: '$U' },
  { code: 'VEN', name: 'Venezuela', flag: '🇻🇪', region: 'South America', subregion: 'South America', currency: 'VES', currencySymbol: 'Bs' },

  // Oceania (14 countries)
  { code: 'AUS', name: 'Australia', flag: '🇦🇺', region: 'Oceania', subregion: 'Australia and New Zealand', currency: 'AUD', currencySymbol: 'A$' },
  { code: 'FJI', name: 'Fiji', flag: '🇫🇯', region: 'Oceania', subregion: 'Melanesia', currency: 'FJD', currencySymbol: '$' },
  { code: 'KIR', name: 'Kiribati', flag: '🇰🇮', region: 'Oceania', subregion: 'Micronesia', currency: 'AUD', currencySymbol: 'A$' },
  { code: 'MHL', name: 'Marshall Islands', flag: '🇲🇭', region: 'Oceania', subregion: 'Micronesia', currency: 'USD', currencySymbol: '$' },
  { code: 'FSM', name: 'Micronesia', flag: '🇫🇲', region: 'Oceania', subregion: 'Micronesia', currency: 'USD', currencySymbol: '$' },
  { code: 'NRU', name: 'Nauru', flag: '🇳🇷', region: 'Oceania', subregion: 'Micronesia', currency: 'AUD', currencySymbol: 'A$' },
  { code: 'NZL', name: 'New Zealand', flag: '🇳🇿', region: 'Oceania', subregion: 'Australia and New Zealand', currency: 'NZD', currencySymbol: 'NZ$' },
  { code: 'PLW', name: 'Palau', flag: '🇵🇼', region: 'Oceania', subregion: 'Micronesia', currency: 'USD', currencySymbol: '$' },
  { code: 'PNG', name: 'Papua New Guinea', flag: '🇵🇬', region: 'Oceania', subregion: 'Melanesia', currency: 'PGK', currencySymbol: 'K' },
  { code: 'WSM', name: 'Samoa', flag: '🇼🇸', region: 'Oceania', subregion: 'Polynesia', currency: 'WST', currencySymbol: 'WS$' },
  { code: 'SLB', name: 'Solomon Islands', flag: '🇸🇧', region: 'Oceania', subregion: 'Melanesia', currency: 'SBD', currencySymbol: '$' },
  { code: 'TON', name: 'Tonga', flag: '🇹🇴', region: 'Oceania', subregion: 'Polynesia', currency: 'TOP', currencySymbol: 'T$' },
  { code: 'TUV', name: 'Tuvalu', flag: '🇹🇻', region: 'Oceania', subregion: 'Polynesia', currency: 'AUD', currencySymbol: 'A$' },
  { code: 'VUT', name: 'Vanuatu', flag: '🇻🇺', region: 'Oceania', subregion: 'Melanesia', currency: 'VUV', currencySymbol: 'Vt' }
];

/**
 * Get all countries sorted alphabetically
 */
export const getAllCountries = (): CompleteCountry[] => {
  return COMPLETE_COUNTRY_LIST.sort((a, b) => a.name.localeCompare(b.name));
};

/**
 * Get countries by region
 */
export const getCountriesByRegion = (region: CompleteCountry['region']): CompleteCountry[] => {
  return COMPLETE_COUNTRY_LIST
    .filter(country => country.region === region)
    .sort((a, b) => a.name.localeCompare(b.name));
};

/**
 * Get country by code
 */
export const getCountryByCode = (code: string): CompleteCountry | null => {
  return COMPLETE_COUNTRY_LIST.find(country => country.code === code) || null;
};

/**
 * Search countries by name
 */
export const searchCountries = (query: string): CompleteCountry[] => {
  const searchTerm = query.toLowerCase();
  return COMPLETE_COUNTRY_LIST
    .filter(country => 
      country.name.toLowerCase().includes(searchTerm) ||
      country.code.toLowerCase().includes(searchTerm)
    )
    .sort((a, b) => a.name.localeCompare(b.name));
};
