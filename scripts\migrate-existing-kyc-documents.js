/**
 * <PERSON><PERSON><PERSON><PERSON> EXISTING KYC DOCUMENTS
 * 
 * Scans the 'proof' storage bucket for existing KYC documents and creates
 * corresponding records in the kyc_documents table.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import crypto from 'crypto';

// Load environment variables
dotenv.config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing required environment variables');
  console.error('Required: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Initialize Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

// Document type mapping based on filename patterns
const getDocumentTypeFromFilename = (filename) => {
  const lower = filename.toLowerCase();
  
  if (lower.includes('facial') || lower.includes('selfie') || lower.includes('verification')) {
    return {
      type: 'facial_recognition',
      category: 'biometric_verification'
    };
  }
  
  if (lower.includes('id_front') || lower.includes('id_document_front')) {
    return {
      type: 'id_document_front',
      category: 'identity_verification'
    };
  }
  
  if (lower.includes('id_back') || lower.includes('id_document_back')) {
    return {
      type: 'id_document_back',
      category: 'identity_verification'
    };
  }
  
  if (lower.includes('address') || lower.includes('proof') || lower.includes('residence')) {
    return {
      type: 'proof_of_address',
      category: 'address_verification'
    };
  }
  
  // Default for ID documents
  return {
    type: 'id_document_front',
    category: 'identity_verification'
  };
};

// Extract user ID from filename
const getUserIdFromFilename = (filename) => {
  const matches = filename.match(/(\d+)/);
  return matches ? parseInt(matches[1]) : null;
};

// Calculate file hash
const calculateFileHash = async (fileData) => {
  const hash = crypto.createHash('sha256');
  hash.update(fileData);
  return hash.digest('hex');
};

async function migrateExistingDocuments() {
  console.log('🚀 Starting KYC documents migration...');

  try {
    // Step 1: List all files in the proof bucket
    console.log('📋 Step 1: Scanning proof storage bucket...');
    
    const { data: files, error: listError } = await supabase.storage
      .from('proof')
      .list('', {
        limit: 1000,
        sortBy: { column: 'created_at', order: 'desc' }
      });

    if (listError) {
      console.error('❌ Error listing files:', listError);
      return false;
    }

    console.log(`✅ Found ${files?.length || 0} files in proof bucket`);

    if (!files || files.length === 0) {
      console.log('ℹ️ No files found to migrate');
      return true;
    }

    // Step 2: Get existing KYC information records
    console.log('📋 Step 2: Fetching KYC information records...');
    
    const { data: kycRecords, error: kycError } = await supabase
      .from('kyc_information')
      .select('id, user_id, created_at');

    if (kycError) {
      console.error('❌ Error fetching KYC records:', kycError);
      return false;
    }

    console.log(`✅ Found ${kycRecords?.length || 0} KYC records`);

    // Create user_id to kyc_id mapping
    const userToKycMap = new Map();
    kycRecords?.forEach(record => {
      userToKycMap.set(record.user_id, record.id);
    });

    // Step 3: Process each file
    console.log('📋 Step 3: Processing files and creating document records...');
    
    let processed = 0;
    let created = 0;
    let skipped = 0;
    let errors = 0;

    for (const file of files) {
      try {
        processed++;
        console.log(`\n🔄 Processing file ${processed}/${files.length}: ${file.name}`);

        // Extract user ID from filename
        const userId = getUserIdFromFilename(file.name);
        if (!userId) {
          console.log(`⚠️ Could not extract user ID from filename: ${file.name}`);
          skipped++;
          continue;
        }

        // Find corresponding KYC record
        const kycId = userToKycMap.get(userId);
        if (!kycId) {
          console.log(`⚠️ No KYC record found for user ID: ${userId}`);
          skipped++;
          continue;
        }

        // Determine document type and category
        const { type, category } = getDocumentTypeFromFilename(file.name);

        // Check if document record already exists
        const { data: existingDoc, error: checkError } = await supabase
          .from('kyc_documents')
          .select('id')
          .eq('kyc_id', kycId)
          .eq('document_type', type)
          .single();

        if (checkError && checkError.code !== 'PGRST116') {
          console.error(`❌ Error checking existing document:`, checkError);
          errors++;
          continue;
        }

        if (existingDoc) {
          console.log(`ℹ️ Document record already exists for ${file.name}`);
          skipped++;
          continue;
        }

        // Download file to calculate hash and get metadata
        const { data: fileData, error: downloadError } = await supabase.storage
          .from('proof')
          .download(file.name);

        let fileHash = null;
        let fileSizeBytes = null;
        let mimeType = null;

        if (!downloadError && fileData) {
          const arrayBuffer = await fileData.arrayBuffer();
          const uint8Array = new Uint8Array(arrayBuffer);
          fileHash = await calculateFileHash(uint8Array);
          fileSizeBytes = uint8Array.length;
          mimeType = fileData.type;
        }

        // Create document record
        const documentRecord = {
          kyc_id: kycId,
          user_id: userId,
          document_type: type,
          document_category: category,
          file_name: file.name,
          file_path: file.name, // In proof bucket, path is just the filename
          storage_bucket: 'proof',
          file_size_bytes: fileSizeBytes,
          mime_type: mimeType,
          file_hash: fileHash,
          verification_status: 'pending', // Default status for migrated documents
          uploaded_at: file.created_at || new Date().toISOString(),
          metadata: {
            migrated: true,
            migration_date: new Date().toISOString(),
            original_file_metadata: {
              id: file.id,
              last_accessed_at: file.last_accessed_at,
              updated_at: file.updated_at
            }
          }
        };

        const { error: insertError } = await supabase
          .from('kyc_documents')
          .insert(documentRecord);

        if (insertError) {
          console.error(`❌ Error creating document record for ${file.name}:`, insertError);
          errors++;
          continue;
        }

        console.log(`✅ Created document record for ${file.name} (${type})`);
        created++;

      } catch (error) {
        console.error(`❌ Error processing file ${file.name}:`, error);
        errors++;
      }
    }

    // Step 4: Summary
    console.log('\n=====================================');
    console.log('📊 Migration Summary:');
    console.log(`   Total files processed: ${processed}`);
    console.log(`   Document records created: ${created}`);
    console.log(`   Files skipped: ${skipped}`);
    console.log(`   Errors encountered: ${errors}`);
    console.log('=====================================');

    if (errors > 0) {
      console.log('⚠️ Migration completed with errors. Please review the logs above.');
      return false;
    }

    console.log('✅ Migration completed successfully!');
    return true;

  } catch (error) {
    console.error('💥 Migration failed with error:', error);
    return false;
  }
}

// Execute the migration
migrateExistingDocuments()
  .then((success) => {
    if (success) {
      console.log('\n🎉 Document migration completed!');
      console.log('The KYC admin dashboard can now display document information.');
      process.exit(0);
    } else {
      console.error('\n💥 Document migration failed!');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('\n💥 Migration failed with error:', error);
    process.exit(1);
  });
