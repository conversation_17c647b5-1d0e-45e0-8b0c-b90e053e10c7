import { supabase, getServiceRoleClient } from '../supabase';

export interface TermsAcceptanceRecord {
  id: string;
  user_id: number;
  telegram_id?: number;
  terms_type: 'registration' | 'share_purchase' | 'privacy_policy' | 'general';
  version: string;
  accepted_at: string;
  created_at: string;
  updated_at: string;
}

export interface TermsAcceptanceRequest {
  userId: number;
  telegramId?: number;
  termsType: 'registration' | 'share_purchase' | 'privacy_policy' | 'general';
  version?: string;
  ipAddress?: string;
  userAgent?: string;
}

class TermsAcceptanceService {
  private readonly CURRENT_TERMS_VERSION = '2.0';
  private readonly CURRENT_PRIVACY_VERSION = '1.5';

  /**
   * Record terms acceptance for a user
   */
  async recordTermsAcceptance(request: TermsAcceptanceRequest): Promise<TermsAcceptanceRecord> {
    console.log('📝 Recording terms acceptance:', request);

    const serviceClient = getServiceRoleClient();
    
    const version = request.version || (
      request.termsType === 'privacy_policy' 
        ? this.CURRENT_PRIVACY_VERSION 
        : this.CURRENT_TERMS_VERSION
    );

    const acceptanceData = {
      user_id: request.userId,
      telegram_id: request.telegramId || null,
      terms_type: request.termsType,
      version: version,
      accepted_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data, error } = await serviceClient
      .from('terms_acceptance')
      .insert([acceptanceData])
      .select()
      .single();

    if (error) {
      console.error('❌ Failed to record terms acceptance:', error);
      throw new Error(`Failed to record terms acceptance: ${error.message}`);
    }

    console.log('✅ Terms acceptance recorded successfully:', data);
    return data;
  }

  /**
   * Check if user has accepted current terms
   */
  async hasAcceptedCurrentTerms(
    userId: number, 
    termsType: 'registration' | 'share_purchase' | 'privacy_policy' | 'general' = 'general'
  ): Promise<boolean> {
    console.log(`🔍 Checking terms acceptance for user ${userId}, type: ${termsType}`);

    const serviceClient = getServiceRoleClient();
    
    const currentVersion = termsType === 'privacy_policy' 
      ? this.CURRENT_PRIVACY_VERSION 
      : this.CURRENT_TERMS_VERSION;

    const { data, error } = await serviceClient
      .from('terms_acceptance')
      .select('*')
      .eq('user_id', userId)
      .eq('terms_type', termsType)
      .eq('version', currentVersion)
      .order('accepted_at', { ascending: false })
      .limit(1);

    if (error) {
      console.error('❌ Failed to check terms acceptance:', error);
      return false;
    }

    const hasAccepted = data && data.length > 0;
    console.log(`✅ Terms acceptance check result: ${hasAccepted}`);
    
    return hasAccepted;
  }

  /**
   * Get user's terms acceptance history
   */
  async getUserTermsHistory(userId: number): Promise<TermsAcceptanceRecord[]> {
    console.log(`📋 Getting terms history for user ${userId}`);

    const serviceClient = getServiceRoleClient();

    const { data, error } = await serviceClient
      .from('terms_acceptance')
      .select('*')
      .eq('user_id', userId)
      .order('accepted_at', { ascending: false });

    if (error) {
      console.error('❌ Failed to get terms history:', error);
      throw new Error(`Failed to get terms history: ${error.message}`);
    }

    console.log(`✅ Retrieved ${data?.length || 0} terms acceptance records`);
    return data || [];
  }

  /**
   * Check if user needs to accept updated terms
   */
  async needsTermsUpdate(
    userId: number, 
    termsType: 'registration' | 'share_purchase' | 'privacy_policy' | 'general' = 'general'
  ): Promise<{ needsUpdate: boolean; currentVersion: string; userVersion?: string }> {
    console.log(`🔄 Checking if user ${userId} needs terms update for type: ${termsType}`);

    const serviceClient = getServiceRoleClient();
    
    const currentVersion = termsType === 'privacy_policy' 
      ? this.CURRENT_PRIVACY_VERSION 
      : this.CURRENT_TERMS_VERSION;

    const { data, error } = await serviceClient
      .from('terms_acceptance')
      .select('version, accepted_at')
      .eq('user_id', userId)
      .eq('terms_type', termsType)
      .order('accepted_at', { ascending: false })
      .limit(1);

    if (error) {
      console.error('❌ Failed to check terms version:', error);
      return { needsUpdate: true, currentVersion };
    }

    if (!data || data.length === 0) {
      console.log('⚠️ User has never accepted terms');
      return { needsUpdate: true, currentVersion };
    }

    const userVersion = data[0].version;
    const needsUpdate = userVersion !== currentVersion;

    console.log(`✅ Terms version check: user=${userVersion}, current=${currentVersion}, needsUpdate=${needsUpdate}`);
    
    return { needsUpdate, currentVersion, userVersion };
  }

  /**
   * Get current terms version
   */
  getCurrentTermsVersion(termsType: 'registration' | 'share_purchase' | 'privacy_policy' | 'general' = 'general'): string {
    return termsType === 'privacy_policy' 
      ? this.CURRENT_PRIVACY_VERSION 
      : this.CURRENT_TERMS_VERSION;
  }

  /**
   * Bulk check terms acceptance for multiple users
   */
  async bulkCheckTermsAcceptance(
    userIds: number[], 
    termsType: 'registration' | 'share_purchase' | 'privacy_policy' | 'general' = 'general'
  ): Promise<{ [userId: number]: boolean }> {
    console.log(`📊 Bulk checking terms acceptance for ${userIds.length} users`);

    const serviceClient = getServiceRoleClient();
    
    const currentVersion = termsType === 'privacy_policy' 
      ? this.CURRENT_PRIVACY_VERSION 
      : this.CURRENT_TERMS_VERSION;

    const { data, error } = await serviceClient
      .from('terms_acceptance')
      .select('user_id')
      .in('user_id', userIds)
      .eq('terms_type', termsType)
      .eq('version', currentVersion);

    if (error) {
      console.error('❌ Failed to bulk check terms acceptance:', error);
      // Return all false if error
      return userIds.reduce((acc, userId) => ({ ...acc, [userId]: false }), {});
    }

    const acceptedUserIds = new Set(data?.map(record => record.user_id) || []);
    
    const result = userIds.reduce((acc, userId) => ({
      ...acc,
      [userId]: acceptedUserIds.has(userId)
    }), {});

    console.log(`✅ Bulk terms check complete: ${acceptedUserIds.size}/${userIds.length} users have accepted`);
    
    return result;
  }

  /**
   * Record terms acceptance with additional metadata
   */
  async recordTermsAcceptanceWithMetadata(
    request: TermsAcceptanceRequest & {
      ipAddress?: string;
      userAgent?: string;
      acceptanceMethod?: 'website' | 'mobile' | 'api';
    }
  ): Promise<TermsAcceptanceRecord> {
    console.log('📝 Recording terms acceptance with metadata:', {
      userId: request.userId,
      termsType: request.termsType,
      version: request.version,
      acceptanceMethod: request.acceptanceMethod || 'website'
    });

    // For now, we'll use the basic record method
    // In the future, we could extend the database schema to include metadata
    return this.recordTermsAcceptance(request);
  }
}

// Export singleton instance
export const termsAcceptanceService = new TermsAcceptanceService();
