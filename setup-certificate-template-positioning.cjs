/**
 * Setup Certificate Template Positioning System
 * Creates database table for storing certificate element positioning data
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client with service role key
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function executeSQL(sql, description) {
  try {
    console.log(`🔄 ${description}...`);
    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });
    
    if (error) {
      console.error(`❌ Error ${description.toLowerCase()}:`, error);
      return false;
    }
    
    console.log(`✅ ${description} completed successfully`);
    return true;
  } catch (error) {
    console.error(`❌ Exception during ${description.toLowerCase()}:`, error);
    return false;
  }
}

async function setupCertificateTemplatePositioning() {
  console.log('🚀 Setting up Certificate Template Positioning System...\n');
  
  // Step 1: Create certificate_template_elements table
  const createTemplateElementsSQL = `
    CREATE TABLE IF NOT EXISTS certificate_template_elements (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      element_id VARCHAR(100) NOT NULL,
      element_type VARCHAR(50) NOT NULL CHECK (element_type IN ('text', 'image', 'line', 'rectangle', 'watermark')),
      template_version VARCHAR(20) DEFAULT 'v1.0',
      
      -- Position and dimensions
      x_position DECIMAL(10,2) NOT NULL DEFAULT 0,
      y_position DECIMAL(10,2) NOT NULL DEFAULT 0,
      width DECIMAL(10,2),
      height DECIMAL(10,2),
      
      -- Text properties
      font_family VARCHAR(100) DEFAULT 'Arial, sans-serif',
      font_size INTEGER DEFAULT 12,
      font_weight VARCHAR(20) DEFAULT 'normal',
      font_color VARCHAR(20) DEFAULT '#000000',
      text_anchor VARCHAR(20) DEFAULT 'start',
      
      -- Content and data binding
      static_content TEXT,
      data_field VARCHAR(100), -- Maps to certificate data fields
      is_dynamic BOOLEAN DEFAULT false,
      
      -- Visual properties
      opacity DECIMAL(3,2) DEFAULT 1.0,
      rotation DECIMAL(5,2) DEFAULT 0,
      z_index INTEGER DEFAULT 1,
      
      -- Metadata
      description TEXT,
      is_active BOOLEAN DEFAULT true,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      
      -- Ensure unique element_id per template version
      UNIQUE(element_id, template_version)
    );
  `;
  
  if (!await executeSQL(createTemplateElementsSQL, 'Creating certificate_template_elements table')) {
    return false;
  }
  
  // Step 2: Create indexes for performance
  const createIndexesSQL = `
    CREATE INDEX IF NOT EXISTS idx_template_elements_version ON certificate_template_elements(template_version);
    CREATE INDEX IF NOT EXISTS idx_template_elements_type ON certificate_template_elements(element_type);
    CREATE INDEX IF NOT EXISTS idx_template_elements_active ON certificate_template_elements(is_active);
    CREATE INDEX IF NOT EXISTS idx_template_elements_z_index ON certificate_template_elements(z_index);
  `;
  
  if (!await executeSQL(createIndexesSQL, 'Creating indexes')) {
    return false;
  }
  
  // Step 3: Create template versions table for versioning support
  const createTemplateVersionsSQL = `
    CREATE TABLE IF NOT EXISTS certificate_template_versions (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      version VARCHAR(20) UNIQUE NOT NULL,
      name VARCHAR(200) NOT NULL,
      description TEXT,
      is_active BOOLEAN DEFAULT false,
      is_default BOOLEAN DEFAULT false,
      
      -- Template metadata
      canvas_width INTEGER DEFAULT 1123,
      canvas_height INTEGER DEFAULT 794,
      canvas_dpi INTEGER DEFAULT 300,
      background_color VARCHAR(20) DEFAULT '#ffffff',
      
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `;
  
  if (!await executeSQL(createTemplateVersionsSQL, 'Creating certificate_template_versions table')) {
    return false;
  }
  
  // Step 4: Insert default template version
  const insertDefaultVersionSQL = `
    INSERT INTO certificate_template_versions (version, name, description, is_active, is_default)
    VALUES ('v1.0', 'Default Certificate Template', 'Standard Aureus Africa share certificate template', true, true)
    ON CONFLICT (version) DO NOTHING;
  `;
  
  if (!await executeSQL(insertDefaultVersionSQL, 'Inserting default template version')) {
    return false;
  }
  
  // Step 5: Insert default certificate elements based on current hardcoded positions
  const insertDefaultElementsSQL = `
    INSERT INTO certificate_template_elements (
      element_id, element_type, template_version, x_position, y_position, 
      font_family, font_size, font_weight, font_color, text_anchor,
      data_field, is_dynamic, description
    ) VALUES 
    -- Certificate Number (top left)
    ('cert_number_top', 'text', 'v1.0', 97, 43, 'Arial, sans-serif', 11, 'bold', '#000000', 'start', 'certificateNumber', true, 'Certificate number displayed at top left'),
    
    -- Number of Shares (top right)
    ('shares_count_top', 'text', 'v1.0', 900, 43, 'Arial, sans-serif', 11, 'bold', '#000000', 'start', 'sharesCount', true, 'Share count displayed at top right'),
    
    -- NAME AND ADDRESS section
    ('user_name', 'text', 'v1.0', 25, 335, 'Arial, sans-serif', 12, 'normal', '#000000', 'start', 'userName', true, 'User full name'),
    ('user_id_number', 'text', 'v1.0', 25, 355, 'Arial, sans-serif', 12, 'normal', '#000000', 'start', 'userIdNumber', true, 'User ID number with ID: prefix'),
    ('user_address', 'text', 'v1.0', 25, 375, 'Arial, sans-serif', 12, 'normal', '#000000', 'start', 'userAddress', true, 'User address with Address: prefix'),
    ('aureus_id', 'text', 'v1.0', 25, 412, 'Arial, sans-serif', 12, 'normal', '#000000', 'start', 'sunId', true, 'Aureus ID with Aureus ID: prefix'),
    
    -- CLASS OF SHARE section
    ('share_class_1', 'text', 'v1.0', 337, 390, 'Arial, sans-serif', 9, 'normal', '#000000', 'middle', null, false, 'NO PAR VALUE text'),
    ('share_class_2', 'text', 'v1.0', 337, 405, 'Arial, sans-serif', 9, 'normal', '#000000', 'middle', null, false, 'SHARES text'),
    
    -- REF NO. section
    ('ref_number', 'text', 'v1.0', 462, 400, 'Arial, sans-serif', 9, 'normal', '#000000', 'middle', 'refNumber', true, 'Reference number'),
    
    -- DATE section
    ('issue_date', 'text', 'v1.0', 600, 400, 'Arial, sans-serif', 9, 'normal', '#000000', 'middle', 'issueDate', true, 'Issue date'),
    
    -- CERT NO section
    ('cert_number_table', 'text', 'v1.0', 760, 400, 'Arial, sans-serif', 9, 'normal', '#000000', 'middle', 'certificateNumber', true, 'Certificate number in table'),
    
    -- NO OF SHARES section
    ('shares_count_table', 'text', 'v1.0', 920, 400, 'Arial, sans-serif', 9, 'normal', '#000000', 'middle', 'sharesCount', true, 'Share count in table')
    
    ON CONFLICT (element_id, template_version) DO NOTHING;
  `;
  
  if (!await executeSQL(insertDefaultElementsSQL, 'Inserting default certificate elements')) {
    return false;
  }
  
  // Step 6: Add static content for non-dynamic elements
  const updateStaticContentSQL = `
    UPDATE certificate_template_elements 
    SET static_content = 'NO PAR VALUE'
    WHERE element_id = 'share_class_1' AND template_version = 'v1.0';
    
    UPDATE certificate_template_elements 
    SET static_content = 'SHARES'
    WHERE element_id = 'share_class_2' AND template_version = 'v1.0';
  `;
  
  if (!await executeSQL(updateStaticContentSQL, 'Updating static content')) {
    return false;
  }
  
  console.log('\n✅ Certificate Template Positioning System setup completed successfully!');
  console.log('\n📊 Summary:');
  console.log('   • Created certificate_template_elements table');
  console.log('   • Created certificate_template_versions table');
  console.log('   • Added default template version (v1.0)');
  console.log('   • Inserted 12 default certificate elements with current positioning');
  console.log('   • Created performance indexes');
  
  return true;
}

// Run the setup
setupCertificateTemplatePositioning()
  .then(success => {
    if (success) {
      console.log('\n🎉 Setup completed successfully!');
      process.exit(0);
    } else {
      console.log('\n❌ Setup failed. Please check the errors above.');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\n💥 Unexpected error during setup:', error);
    process.exit(1);
  });
