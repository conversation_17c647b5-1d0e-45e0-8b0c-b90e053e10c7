import React, { useState, useEffect } from 'react'
import { supabase, getServiceRoleClient } from '../../lib/supabase'
import bcrypt from 'bcryptjs'

interface NewUserData {
  // Basic Info
  email: string
  password: string
  firstName: string
  lastName: string
  phone: string

  // Telegram Integration
  telegramUserId?: string
  telegramUsername?: string

  // Referral System
  sponsorId: number

  // KYC Status
  kycStatus: 'pending' | 'completed' | 'rejected'

  // Account Settings
  isActive: boolean
  isVerified: boolean
}

interface AdminUserCreationProps {
  onClose: () => void
  onUserCreated: () => void
}

export const AdminUserCreation: React.FC<AdminUserCreationProps> = ({
  onClose,
  onUserCreated
}) => {
  const [formData, setFormData] = useState<NewUserData>({
    email: '',
    password: '',
    firstName: '',
    lastName: '',
    phone: '',
    telegramUserId: '',
    telegramUsername: '',
    sponsorId: 4, // Default sponsor
    kycStatus: 'pending',
    isActive: true,
    isVerified: true // Auto-verify admin created accounts
  })

  const [sponsors, setSponsors] = useState<Array<{id: number, name: string, email: string}>>([])
  const [loading, setLoading] = useState(false)
  const [step, setStep] = useState<'form' | 'confirmation' | 'processing'>('form')
  const [errors, setErrors] = useState<{[key: string]: string}>({})

  useEffect(() => {
    loadSponsors()
  }, [])

  const loadSponsors = async () => {
    try {
      // 🔧 FIX: Use service role client for admin operations
      const serviceClient = getServiceRoleClient()
      const { data: users, error } = await serviceClient
        .from('users')
        .select('id, first_name, last_name, email')
        .eq('is_active', true)
        .order('first_name')

      if (error) {
        console.error('Error loading sponsors:', error)
        return
      }

      const sponsorList = users?.map(user => ({
        id: user.id,
        name: `${user.first_name} ${user.last_name}`.trim(),
        email: user.email
      })) || []

      setSponsors(sponsorList)
    } catch (error) {
      console.error('Error loading sponsors:', error)
    }
  }



  const validateForm = (): boolean => {
    const newErrors: {[key: string]: string} = {}

    // Basic validation
    if (!formData.email) newErrors.email = 'Email is required'
    if (!formData.email.includes('@')) newErrors.email = 'Valid email is required'
    if (!formData.password) newErrors.password = 'Password is required'
    if (formData.password.length < 6) newErrors.password = 'Password must be at least 6 characters'
    if (!formData.firstName) newErrors.firstName = 'First name is required'
    if (!formData.lastName) newErrors.lastName = 'Last name is required'
    if (!formData.phone) newErrors.phone = 'Phone number is required'
    if (!formData.sponsorId) newErrors.sponsorId = 'Sponsor is required'

    // Telegram username validation (if provided)
    if (formData.telegramUsername && formData.telegramUsername.trim()) {
      const username = formData.telegramUsername.trim()
      if (username.length < 3) {
        newErrors.telegramUsername = 'Telegram username must be at least 3 characters'
      } else if (!/^[a-zA-Z0-9_]+$/.test(username)) {
        newErrors.telegramUsername = 'Telegram username can only contain letters, numbers, and underscores'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const createUser = async (): Promise<number | null> => {
    try {
      console.log('🔄 Creating new user account...')

      // Hash password
      const passwordHash = await bcrypt.hash(formData.password, 12)

      // 🔧 FIX: Use service role client for admin operations
      const serviceClient = getServiceRoleClient()

      // Create user in users table
      const { data: newUser, error: userError } = await serviceClient
        .from('users')
        .insert({
          email: formData.email,
          password_hash: passwordHash,
          first_name: formData.firstName,
          last_name: formData.lastName,
          phone: formData.phone,
          is_active: formData.isActive,
          is_verified: formData.isVerified,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select('id')
        .single()

      if (userError) {
        console.error('❌ Error creating user:', userError)
        throw new Error(`Failed to create user: ${userError.message}`)
      }

      console.log('✅ User created with ID:', newUser.id)
      return newUser.id

    } catch (error) {
      console.error('❌ Error in createUser:', error)
      throw error
    }
  }

  const linkTelegramUser = async (userId: number) => {
    if (!formData.telegramUserId) return

    try {
      console.log('🔗 Linking Telegram user...')
      
      // Check if telegram user exists
      const { data: existingTgUser, error: checkError } = await supabase
        .from('telegram_users')
        .select('*')
        .eq('telegram_id', formData.telegramUserId)
        .single()

      if (checkError && checkError.code !== 'PGRST116') {
        console.error('Error checking telegram user:', checkError)
        return
      }

      if (existingTgUser) {
        // Update existing telegram user with web user ID
        const { error: updateError } = await serviceClient
          .from('telegram_users')
          .update({
            user_id: userId,
            email: formData.email,
            full_name: `${formData.firstName} ${formData.lastName}`,
            phone: formData.phone,
            is_web_enabled: true,
            web_linked_at: new Date().toISOString()
          })
          .eq('telegram_id', formData.telegramUserId)

        if (updateError) {
          console.error('Error updating telegram user:', updateError)
        } else {
          console.log('✅ Telegram user linked successfully')
        }
      } else {
        // Create new telegram user record
        const { error: createError } = await supabase
          .from('telegram_users')
          .insert({
            telegram_id: parseInt(formData.telegramUserId),
            user_id: userId,
            username: formData.telegramUsername || '',
            email: formData.email,
            full_name: `${formData.firstName} ${formData.lastName}`,
            phone: formData.phone,
            is_web_enabled: true,
            web_linked_at: new Date().toISOString(),
            created_at: new Date().toISOString()
          })

        if (createError) {
          console.error('Error creating telegram user:', createError)
        } else {
          console.log('✅ New telegram user record created')
        }
      }

      // Update users table with telegram_id
      const { error: linkError } = await supabase
        .from('users')
        .update({ telegram_id: parseInt(formData.telegramUserId) })
        .eq('id', userId)

      if (linkError) {
        console.error('Error linking telegram ID to user:', linkError)
      }

    } catch (error) {
      console.error('❌ Error linking telegram user:', error)
    }
  }

  const createReferralRelationship = async (userId: number) => {
    try {
      console.log('🤝 Creating referral relationship...')
      
      // Generate referral code
      const referralCode = `REF_${userId}_${Date.now()}`
      
      // Create referral record
      const { data: referralData, error: referralError } = await serviceClient
        .from('referrals')
        .insert({
          referrer_id: formData.sponsorId,
          referred_id: userId,
          referral_code: referralCode,
          commission_rate: 15.00,
          status: 'active',
          created_at: new Date().toISOString()
        })
        .select()
        .single()

      if (referralError) {
        console.error('Error creating referral:', referralError)
        throw new Error(`Failed to create referral: ${referralError.message}`)
      }

      // Trigger email notification for new referral
      if (referralData?.id) {
        try {
          const { emailNotificationTriggers } = await import('../../lib/services/emailNotificationTriggers')
          await emailNotificationTriggers.triggerReferralNotification(referralData.id)
        } catch (emailError) {
          console.warn('Failed to trigger referral email notification:', emailError)
          // Don't fail user creation if email fails
        }
      }

      console.log('✅ Referral relationship created')

    } catch (error) {
      console.error('❌ Error creating referral relationship:', error)
      throw error
    }
  }

  const createCommissionBalance = async (userId: number) => {
    try {
      console.log('💰 Creating commission balance...')

      // Initialize commission balance for new user
      const { error: commissionError } = await supabase
        .from('commission_balances')
        .insert({
          user_id: userId,
          usdt_balance: 0,
          share_balance: 0,
          total_earned_usdt: 0,
          total_earned_shares: 0,
          escrowed_amount: 0,
          total_withdrawn: 0,
          created_at: new Date().toISOString()
        })

      if (commissionError) {
        console.error('Error creating commission balance:', commissionError)
        throw new Error(`Failed to create commission balance: ${commissionError.message}`)
      }

      console.log('✅ Commission balance created')

    } catch (error) {
      console.error('❌ Error creating commission balance:', error)
      throw error
    }
  }

  const setupKYC = async (userId: number) => {
    if (formData.kycStatus === 'pending') return

    try {
      console.log('📋 Setting up KYC status...')
      
      const { error: kycError } = await supabase
        .from('kyc_information')
        .insert({
          user_id: userId,
          first_name: formData.firstName,
          last_name: formData.lastName,
          phone_number: formData.phone,
          email_address: formData.email,
          kyc_status: formData.kycStatus,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })

      if (kycError) {
        console.warn('Warning: Could not create KYC record:', kycError)
      } else {
        console.log('✅ KYC status set')
      }

    } catch (error) {
      console.error('❌ Error setting up KYC:', error)
    }
  }

  const handleSubmit = async () => {
    if (!validateForm()) return

    setStep('confirmation')
  }

  const handleConfirm = async () => {
    setLoading(true)
    setStep('processing')

    try {
      // Step 1: Create user account
      const userId = await createUser()
      if (!userId) throw new Error('Failed to create user')

      // Step 2: Link Telegram user (if provided)
      await linkTelegramUser(userId)

      // Step 3: Create referral relationship
      await createReferralRelationship(userId)

      // Step 4: Create commission balance
      await createCommissionBalance(userId)

      // Step 5: Setup KYC status
      await setupKYC(userId)

      console.log('🎉 User account creation completed successfully!')
      alert(`✅ User account created successfully!\n\nUser ID: ${userId}\nEmail: ${formData.email}\nName: ${formData.firstName} ${formData.lastName}\n\nThe user can now log in and make share purchases through the normal payment process.`)

      onUserCreated()
      onClose()

    } catch (error) {
      console.error('❌ User creation failed:', error)
      alert(`❌ User creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
      setStep('form')
    } finally {
      setLoading(false)
    }
  }



  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        
        {step === 'form' && (
          <>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-white">👤 Create New User Account</h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-white text-xl"
              >
                ×
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              
              {/* Basic Information */}
              <div className="space-y-4">
                <h4 className="font-semibold text-blue-300">📋 Basic Information</h4>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Email *</label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({...formData, email: e.target.value})}
                    className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 text-white"
                    placeholder="<EMAIL>"
                  />
                  {errors.email && <p className="text-red-400 text-sm mt-1">{errors.email}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Password *</label>
                  <input
                    type="password"
                    value={formData.password}
                    onChange={(e) => setFormData({...formData, password: e.target.value})}
                    className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 text-white"
                    placeholder="Minimum 6 characters"
                  />
                  {errors.password && <p className="text-red-400 text-sm mt-1">{errors.password}</p>}
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">First Name *</label>
                    <input
                      type="text"
                      value={formData.firstName}
                      onChange={(e) => setFormData({...formData, firstName: e.target.value})}
                      className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 text-white"
                    />
                    {errors.firstName && <p className="text-red-400 text-sm mt-1">{errors.firstName}</p>}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Last Name *</label>
                    <input
                      type="text"
                      value={formData.lastName}
                      onChange={(e) => setFormData({...formData, lastName: e.target.value})}
                      className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 text-white"
                    />
                    {errors.lastName && <p className="text-red-400 text-sm mt-1">{errors.lastName}</p>}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Phone Number *</label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => setFormData({...formData, phone: e.target.value})}
                    className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 text-white"
                    placeholder="+27 78 369 9799"
                  />
                  {errors.phone && <p className="text-red-400 text-sm mt-1">{errors.phone}</p>}
                </div>
              </div>

              {/* Advanced Settings */}
              <div className="space-y-4">
                <h4 className="font-semibold text-green-300">⚙️ Advanced Settings</h4>
                
                {/* Telegram Integration */}
                <div className="bg-gray-700 rounded p-3">
                  <h5 className="font-medium text-blue-300 mb-2">🤖 Telegram Integration</h5>
                  <div className="space-y-2">
                    <input
                      type="text"
                      value={formData.telegramUserId}
                      onChange={(e) => setFormData({...formData, telegramUserId: e.target.value})}
                      className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 text-white text-sm"
                      placeholder="Telegram User ID (optional)"
                    />
                    <input
                      type="text"
                      value={formData.telegramUsername}
                      onChange={(e) => setFormData({...formData, telegramUsername: e.target.value})}
                      className={`w-full bg-gray-600 border rounded px-3 py-2 text-white text-sm ${
                        errors.telegramUsername ? 'border-red-500' : 'border-gray-500'
                      }`}
                      placeholder="Telegram Username (optional)"
                    />
                    {errors.telegramUsername && (
                      <p className="text-red-400 text-xs mt-1">{errors.telegramUsername}</p>
                    )}
                  </div>
                </div>

                {/* Referral System */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Sponsor *</label>
                  <select
                    value={formData.sponsorId}
                    onChange={(e) => setFormData({...formData, sponsorId: parseInt(e.target.value)})}
                    className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 text-white"
                  >
                    {sponsors.map(sponsor => (
                      <option key={sponsor.id} value={sponsor.id}>
                        {sponsor.name} ({sponsor.email})
                      </option>
                    ))}
                  </select>
                  {errors.sponsorId && <p className="text-red-400 text-sm mt-1">{errors.sponsorId}</p>}
                </div>



                {/* Account Status */}
                <div className="bg-gray-700 rounded p-3">
                  <h5 className="font-medium text-purple-300 mb-2">🔒 Account Status</h5>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.isActive}
                        onChange={(e) => setFormData({...formData, isActive: e.target.checked})}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-300">Account Active</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.isVerified}
                        onChange={(e) => setFormData({...formData, isVerified: e.target.checked})}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-300">Email Verified</span>
                    </label>
                    <select
                      value={formData.kycStatus}
                      onChange={(e) => setFormData({...formData, kycStatus: e.target.value as any})}
                      className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 text-white text-sm"
                    >
                      <option value="pending">KYC Pending</option>
                      <option value="completed">KYC Completed</option>
                      <option value="rejected">KYC Rejected</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={handleSubmit}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded font-semibold"
              >
                Review & Create User
              </button>
              <button
                onClick={onClose}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded"
              >
                Cancel
              </button>
            </div>
          </>
        )}

        {step === 'confirmation' && (
          <>
            <div className="text-center mb-6">
              <h3 className="text-xl font-bold text-yellow-400 mb-2">⚠️ Confirm User Creation</h3>
              <p className="text-gray-300">Please review the details before creating the user account.</p>
            </div>

            <div className="bg-gray-700 rounded-lg p-4 mb-6">
              <h4 className="font-semibold text-white mb-3">👤 User Summary:</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <strong className="text-blue-300">Name:</strong> {formData.firstName} {formData.lastName}
                </div>
                <div>
                  <strong className="text-blue-300">Email:</strong> {formData.email}
                </div>
                <div>
                  <strong className="text-blue-300">Phone:</strong> {formData.phone}
                </div>
                <div>
                  <strong className="text-blue-300">Sponsor:</strong> {sponsors.find(s => s.id === formData.sponsorId)?.name}
                </div>
                {formData.telegramUserId && (
                  <div>
                    <strong className="text-blue-300">Telegram ID:</strong> {formData.telegramUserId}
                  </div>
                )}

                <div>
                  <strong className="text-blue-300">KYC Status:</strong> {formData.kycStatus}
                </div>
                <div>
                  <strong className="text-blue-300">Account Status:</strong> {formData.isActive ? 'Active' : 'Inactive'}
                </div>
              </div>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={handleConfirm}
                disabled={loading}
                className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white px-4 py-2 rounded font-semibold"
              >
                {loading ? 'Creating...' : 'CREATE USER ACCOUNT'}
              </button>
              <button
                onClick={() => setStep('form')}
                disabled={loading}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded"
              >
                Back to Edit
              </button>
            </div>
          </>
        )}

        {step === 'processing' && (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
            <h3 className="text-lg font-bold text-blue-400 mb-2">Creating User Account...</h3>
            <p className="text-gray-300">Setting up user profile, referrals, and share purchases.</p>
            <p className="text-sm text-gray-400 mt-2">This may take a few moments.</p>
          </div>
        )}

      </div>
    </div>
  )
}
