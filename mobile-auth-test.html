<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Authentication Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔧 Mobile Authentication Race Condition Test</h1>
    
    <div class="test-section info">
        <h3>📱 Device Detection</h3>
        <p><strong>User Agent:</strong> <span id="userAgent"></span></p>
        <p><strong>Is Mobile:</strong> <span id="isMobile"></span></p>
        <p><strong>Screen Size:</strong> <span id="screenSize"></span></p>
    </div>

    <div class="test-section">
        <h3>🧪 Authentication State Tests</h3>
        <button onclick="testLocalStorageRaceCondition()">Test localStorage Race Condition</button>
        <button onclick="testMultipleRedirects()">Test Multiple Redirects</button>
        <button onclick="testMobileAuthManager()">Test Mobile Auth Manager</button>
        <button onclick="clearTestData()">Clear Test Data</button>
    </div>

    <div class="test-section">
        <h3>📊 Current localStorage State</h3>
        <button onclick="showLocalStorageState()">Show localStorage</button>
        <div id="localStorageState"></div>
    </div>

    <div class="test-section">
        <h3>📝 Test Log</h3>
        <div id="log"></div>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        // Mobile Authentication State Manager (Simulated)
        const mobileAuthManager = {
            isRedirecting: false,
            redirectAttempts: 0,
            maxAttempts: 3,
            
            startRedirect: function(userData) {
                if (this.isRedirecting) {
                    log('📱 Mobile redirect already in progress, ignoring duplicate request');
                    return false;
                }
                
                this.isRedirecting = true;
                this.redirectAttempts = 0;
                
                log('📱 Starting mobile authentication redirect sequence');
                
                // Set mobile-specific authentication flag
                localStorage.setItem('aureus_mobile_auth_redirect', 'true');
                localStorage.setItem('aureus_auth_timestamp', Date.now().toString());
                
                // Store user data
                localStorage.setItem('aureus_user', JSON.stringify(userData));
                localStorage.setItem('aureus_user_type', userData.user_type || 'shareholder');
                
                return true;
            },
            
            cleanup: function() {
                this.isRedirecting = false;
                this.redirectAttempts = 0;
                localStorage.removeItem('aureus_mobile_auth_redirect');
                localStorage.removeItem('aureus_auth_timestamp');
                log('📱 Mobile auth manager cleanup complete');
            }
        };

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            
            document.getElementById('userAgent').textContent = navigator.userAgent;
            document.getElementById('isMobile').textContent = isMobile ? 'Yes' : 'No';
            document.getElementById('screenSize').textContent = `${window.screen.width}x${window.screen.height}`;
            
            log('🔧 Mobile Authentication Test Page Loaded');
            log(`📱 Mobile Device: ${isMobile}`);
        });

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function testLocalStorageRaceCondition() {
            log('🧪 Testing localStorage race condition...');
            
            // Simulate multiple rapid localStorage operations
            const operations = [
                () => localStorage.setItem('aureus_auth_success_redirect', 'true'),
                () => localStorage.setItem('aureus_mobile_auth_redirect', 'true'),
                () => localStorage.removeItem('aureus_auth_success_redirect'),
                () => localStorage.setItem('aureus_user', JSON.stringify({id: 123, email: '<EMAIL>'})),
                () => localStorage.removeItem('aureus_mobile_auth_redirect')
            ];

            operations.forEach((op, index) => {
                setTimeout(op, index * 10); // Rapid succession
            });

            setTimeout(() => {
                log('✅ Race condition test completed');
                showLocalStorageState();
            }, 100);
        }

        function testMultipleRedirects() {
            log('🧪 Testing multiple redirect attempts...');
            
            const userData = { id: 123, email: '<EMAIL>', user_type: 'shareholder' };
            
            // Simulate multiple rapid redirect calls
            for (let i = 0; i < 5; i++) {
                setTimeout(() => {
                    const result = mobileAuthManager.startRedirect(userData);
                    log(`📱 Redirect attempt ${i + 1}: ${result ? 'Started' : 'Blocked'}`);
                }, i * 50);
            }

            setTimeout(() => {
                log('✅ Multiple redirect test completed');
                mobileAuthManager.cleanup();
            }, 500);
        }

        function testMobileAuthManager() {
            log('🧪 Testing mobile auth manager...');
            
            const userData = { id: 123, email: '<EMAIL>', user_type: 'shareholder' };
            
            // Test normal flow
            const started = mobileAuthManager.startRedirect(userData);
            log(`📱 Auth manager started: ${started}`);
            
            // Test duplicate prevention
            const duplicate = mobileAuthManager.startRedirect(userData);
            log(`📱 Duplicate blocked: ${!duplicate}`);
            
            setTimeout(() => {
                mobileAuthManager.cleanup();
                log('✅ Mobile auth manager test completed');
            }, 1000);
        }

        function showLocalStorageState() {
            const stateDiv = document.getElementById('localStorageState');
            let html = '<h4>Current localStorage:</h4>';
            
            const relevantKeys = [
                'aureus_user',
                'aureus_telegram_user',
                'aureus_user_type',
                'aureus_mobile_auth_redirect',
                'aureus_auth_timestamp',
                'aureus_auth_success_redirect',
                'aureus_post_login_redirect'
            ];

            relevantKeys.forEach(key => {
                const value = localStorage.getItem(key);
                if (value) {
                    html += `<p><strong>${key}:</strong> ${value}</p>`;
                }
            });

            if (html === '<h4>Current localStorage:</h4>') {
                html += '<p><em>No relevant keys found</em></p>';
            }

            stateDiv.innerHTML = html;
        }

        function clearTestData() {
            const keysToRemove = [
                'aureus_user',
                'aureus_telegram_user',
                'aureus_user_type',
                'aureus_mobile_auth_redirect',
                'aureus_auth_timestamp',
                'aureus_auth_success_redirect',
                'aureus_post_login_redirect'
            ];

            keysToRemove.forEach(key => localStorage.removeItem(key));
            mobileAuthManager.cleanup();
            
            log('🧹 Test data cleared');
            showLocalStorageState();
        }
    </script>
</body>
</html>
