/**
 * COURSE CREATION WIZARD
 * 
 * Multi-step wizard for creating comprehensive training courses:
 * - Basic course information
 * - Content upload and management
 * - Lesson structure and ordering
 * - Assessment creation
 * - Review and publish
 */

import React, { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';
import { RichTextEditor } from './RichTextEditor';
import { VideoUploadManager } from './VideoUploadManager';
import { FileAttachmentManager } from './FileAttachmentManager';

interface CourseCreationWizardProps {
  onClose: () => void;
  onCourseCreated: (courseId: number) => void;
  editingCourse?: any;
}

interface TrainingCategory {
  id: number;
  name: string;
  description: string;
  icon: string;
  color: string;
}

interface CourseFormData {
  title: string;
  description: string;
  category_id: number;
  difficulty_level: 'Beginner' | 'Intermediate' | 'Advanced';
  estimated_duration: number;
  thumbnail_url?: string;
  status: 'draft' | 'under_review' | 'published';
}

type WizardStep = 'basic' | 'content' | 'lessons' | 'assessments' | 'review';

export const CourseCreationWizard: React.FC<CourseCreationWizardProps> = ({
  onClose,
  onCourseCreated,
  editingCourse
}) => {
  const [currentStep, setCurrentStep] = useState<WizardStep>('basic');
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<TrainingCategory[]>([]);
  const [courseData, setCourseData] = useState<CourseFormData>({
    title: '',
    description: '',
    category_id: 0,
    difficulty_level: 'Beginner',
    estimated_duration: 30,
    status: 'draft'
  });
  const [thumbnailFile, setThumbnailFile] = useState<File | null>(null);
  const [thumbnailPreview, setThumbnailPreview] = useState<string>('');

  useEffect(() => {
    loadCategories();
    if (editingCourse) {
      setCourseData({
        title: editingCourse.title || '',
        description: editingCourse.description || '',
        category_id: editingCourse.category_id || 0,
        difficulty_level: editingCourse.difficulty_level || 'Beginner',
        estimated_duration: editingCourse.estimated_duration || 30,
        thumbnail_url: editingCourse.thumbnail_url,
        status: editingCourse.status || 'draft'
      });
      if (editingCourse.thumbnail_url) {
        setThumbnailPreview(editingCourse.thumbnail_url);
      }
    }
  }, [editingCourse]);

  const loadCategories = async () => {
    try {
      const serviceClient = getServiceRoleClient();
      const { data, error } = await serviceClient
        .from('training_categories')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true });

      if (error) throw error;
      setCategories(data || []);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const handleThumbnailUpload = async (file: File) => {
    try {
      setLoading(true);
      
      // Create unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `course-thumbnail-${Date.now()}.${fileExt}`;
      
      // Upload to Supabase storage
      const { data, error } = await supabase.storage
        .from('training-assets')
        .upload(`thumbnails/${fileName}`, file);

      if (error) throw error;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('training-assets')
        .getPublicUrl(`thumbnails/${fileName}`);

      setCourseData(prev => ({ ...prev, thumbnail_url: publicUrl }));
      setThumbnailPreview(publicUrl);
      
    } catch (error) {
      console.error('Error uploading thumbnail:', error);
      alert('Failed to upload thumbnail. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const generateSlug = (title: string): string => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  };

  const handleSaveCourse = async () => {
    try {
      setLoading(true);
      
      if (!courseData.title.trim() || !courseData.description.trim() || !courseData.category_id) {
        alert('Please fill in all required fields.');
        return;
      }

      const serviceClient = getServiceRoleClient();
      const slug = generateSlug(courseData.title);

      const coursePayload = {
        ...courseData,
        slug,
        created_by: 1 // TODO: Get actual admin user ID
      };

      let result;
      if (editingCourse) {
        // Update existing course
        const { data, error } = await serviceClient
          .from('training_courses')
          .update(coursePayload)
          .eq('id', editingCourse.id)
          .select()
          .single();
        
        if (error) throw error;
        result = data;
      } else {
        // Create new course
        const { data, error } = await serviceClient
          .from('training_courses')
          .insert(coursePayload)
          .select()
          .single();
        
        if (error) throw error;
        result = data;
      }

      onCourseCreated(result.id);
      onClose();
      
    } catch (error) {
      console.error('Error saving course:', error);
      alert('Failed to save course. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderBasicInfo = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">📚 Basic Course Information</h3>
        <p className="text-gray-400 mb-6">
          Set up the fundamental details for your training course.
        </p>
      </div>

      {/* Course Title */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Course Title *
        </label>
        <input
          type="text"
          value={courseData.title}
          onChange={(e) => setCourseData(prev => ({ ...prev, title: e.target.value }))}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Enter course title (max 100 characters)"
          maxLength={100}
          required
        />
        <p className="text-xs text-gray-500 mt-1">{courseData.title.length}/100 characters</p>
      </div>

      {/* Course Description */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Course Description *
        </label>
        <textarea
          value={courseData.description}
          onChange={(e) => setCourseData(prev => ({ ...prev, description: e.target.value }))}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Describe what students will learn in this course..."
          rows={4}
          maxLength={2000}
          required
        />
        <p className="text-xs text-gray-500 mt-1">{courseData.description.length}/2000 characters</p>
      </div>

      {/* Category Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Category *
        </label>
        <select
          value={courseData.category_id}
          onChange={(e) => setCourseData(prev => ({ ...prev, category_id: parseInt(e.target.value) }))}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          required
        >
          <option value={0}>Select a category</option>
          {categories.map((category) => (
            <option key={category.id} value={category.id}>
              {category.icon} {category.name}
            </option>
          ))}
        </select>
      </div>

      {/* Difficulty Level */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Difficulty Level
        </label>
        <div className="grid grid-cols-3 gap-3">
          {['Beginner', 'Intermediate', 'Advanced'].map((level) => (
            <button
              key={level}
              type="button"
              onClick={() => setCourseData(prev => ({ ...prev, difficulty_level: level as any }))}
              className={`px-4 py-2 rounded-lg border transition-colors ${
                courseData.difficulty_level === level
                  ? 'bg-blue-600 border-blue-500 text-white'
                  : 'bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600'
              }`}
            >
              {level}
            </button>
          ))}
        </div>
      </div>

      {/* Estimated Duration */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Estimated Duration (minutes)
        </label>
        <input
          type="number"
          value={courseData.estimated_duration}
          onChange={(e) => setCourseData(prev => ({ ...prev, estimated_duration: parseInt(e.target.value) || 0 }))}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          min={1}
          max={600}
          required
        />
      </div>

      {/* Thumbnail Upload */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Course Thumbnail
        </label>
        <div className="space-y-3">
          {thumbnailPreview && (
            <div className="relative inline-block">
              <img
                src={thumbnailPreview}
                alt="Course thumbnail"
                className="w-32 h-20 object-cover rounded-lg border border-gray-600"
              />
              <button
                type="button"
                onClick={() => {
                  setThumbnailPreview('');
                  setCourseData(prev => ({ ...prev, thumbnail_url: undefined }));
                }}
                className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
              >
                ×
              </button>
            </div>
          )}
          <input
            type="file"
            accept="image/jpeg,image/png,image/webp"
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file) {
                if (file.size > 10 * 1024 * 1024) {
                  alert('File size must be less than 10MB');
                  return;
                }
                setThumbnailFile(file);
                handleThumbnailUpload(file);
              }
            }}
            className="block w-full text-sm text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-600 file:text-white hover:file:bg-blue-700"
          />
          <p className="text-xs text-gray-500">
            Upload JPG, PNG, or WebP. Max size: 10MB. Recommended: 800x450px
          </p>
        </div>
      </div>
    </div>
  );

  const renderContentManagement = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">📝 Course Content & Resources</h3>
        <p className="text-gray-400 mb-6">
          Upload videos, add rich text content, and attach resource files for your course.
        </p>
      </div>

      {/* Video Upload Section */}
      <div className="bg-gray-700 rounded-lg p-6">
        <h4 className="text-white font-medium mb-4">🎥 Course Videos</h4>
        <VideoUploadManager
          onVideoUploaded={(videoData) => {
            console.log('Video uploaded:', videoData);
            // Handle video upload - could save to course lessons
          }}
          maxFileSize={500}
          className="mb-4"
        />
      </div>

      {/* Rich Text Content */}
      <div className="bg-gray-700 rounded-lg p-6">
        <h4 className="text-white font-medium mb-4">📄 Course Description & Content</h4>
        <RichTextEditor
          value={courseData.description}
          onChange={(value) => setCourseData(prev => ({ ...prev, description: value }))}
          placeholder="Write detailed course content, learning objectives, and instructions..."
          maxLength={5000}
        />
      </div>

      {/* File Attachments */}
      <div className="bg-gray-700 rounded-lg p-6">
        <h4 className="text-white font-medium mb-4">📎 Course Resources</h4>
        <FileAttachmentManager
          courseId={editingCourse?.id}
          onFilesUploaded={(files) => {
            console.log('Files uploaded:', files);
            // Handle file uploads - save to course resources
          }}
          maxFileSize={50}
          maxFiles={10}
        />
      </div>
    </div>
  );

  const renderStepContent = () => {
    switch (currentStep) {
      case 'basic':
        return renderBasicInfo();
      case 'content':
        return renderContentManagement();
      case 'lessons':
        return <div className="text-white">Lesson structure coming soon...</div>;
      case 'assessments':
        return <div className="text-white">Assessment builder coming soon...</div>;
      case 'review':
        return <div className="text-white">Review and publish coming soon...</div>;
      default:
        return renderBasicInfo();
    }
  };

  const steps = [
    { id: 'basic', name: 'Basic Info', icon: '📚' },
    { id: 'content', name: 'Content', icon: '📝' },
    { id: 'lessons', name: 'Lessons', icon: '📖' },
    { id: 'assessments', name: 'Assessments', icon: '📊' },
    { id: 'review', name: 'Review', icon: '✅' }
  ];

  const currentStepIndex = steps.findIndex(step => step.id === currentStep);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gray-700 px-6 py-4 border-b border-gray-600">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-white">
              {editingCourse ? '✏️ Edit Course' : '➕ Create New Course'}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white text-2xl"
            >
              ×
            </button>
          </div>
        </div>

        {/* Progress Steps */}
        <div className="bg-gray-700 px-6 py-3 border-b border-gray-600">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center space-x-2 px-3 py-1 rounded-lg ${
                  index === currentStepIndex
                    ? 'bg-blue-600 text-white'
                    : index < currentStepIndex
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-600 text-gray-300'
                }`}>
                  <span>{step.icon}</span>
                  <span className="text-sm font-medium">{step.name}</span>
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-8 h-0.5 mx-2 ${
                    index < currentStepIndex ? 'bg-green-500' : 'bg-gray-600'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {renderStepContent()}
        </div>

        {/* Footer */}
        <div className="bg-gray-700 px-6 py-4 border-t border-gray-600 flex justify-between">
          <button
            onClick={() => {
              const prevIndex = currentStepIndex - 1;
              if (prevIndex >= 0) {
                setCurrentStep(steps[prevIndex].id as WizardStep);
              }
            }}
            disabled={currentStepIndex === 0}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            ← Previous
          </button>

          <div className="flex space-x-3">
            <button
              onClick={handleSaveCourse}
              disabled={loading}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
            >
              {loading ? 'Saving...' : editingCourse ? 'Update Course' : 'Save Draft'}
            </button>

            {currentStepIndex < steps.length - 1 ? (
              <button
                onClick={() => {
                  const nextIndex = currentStepIndex + 1;
                  setCurrentStep(steps[nextIndex].id as WizardStep);
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Next →
              </button>
            ) : (
              <button
                onClick={() => {
                  setCourseData(prev => ({ ...prev, status: 'under_review' }));
                  handleSaveCourse();
                }}
                disabled={loading}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
              >
                Submit for Review
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
