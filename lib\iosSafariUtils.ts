/**
 * iOS Safari Utilities
 * 
 * Provides utilities for detecting and handling iOS Safari-specific behaviors
 * to ensure proper navigation and functionality across all mobile browsers.
 */

export interface BrowserInfo {
  isMobile: boolean;
  isIOSSafari: boolean;
  isIOSChrome: boolean;
  isIOSFirefox: boolean;
  isAndroid: boolean;
  isAndroidChrome: boolean;
  userAgent: string;
  version?: string;
}

/**
 * Enhanced browser detection with specific iOS Safari identification
 */
export function detectBrowser(): BrowserInfo {
  const userAgent = navigator.userAgent;
  
  // iOS device detection
  const isIOS = /iPad|iPhone|iPod/.test(userAgent);
  
  // iOS Safari detection (excludes Chrome, Firefox, etc. on iOS)
  const isIOSSafari = isIOS && /Safari/.test(userAgent) && !/CriOS|FxiOS|OPiOS|mercury/.test(userAgent);
  
  // iOS Chrome detection
  const isIOSChrome = isIOS && /CriOS/.test(userAgent);
  
  // iOS Firefox detection
  const isIOSFirefox = isIOS && /FxiOS/.test(userAgent);
  
  // Android detection
  const isAndroid = /Android/.test(userAgent);
  
  // Android Chrome detection
  const isAndroidChrome = isAndroid && /Chrome/.test(userAgent) && !/Edge|OPR/.test(userAgent);
  
  // General mobile detection
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
  
  // Extract version for iOS Safari
  let version: string | undefined;
  if (isIOSSafari) {
    const versionMatch = userAgent.match(/Version\/(\d+\.\d+)/);
    version = versionMatch ? versionMatch[1] : undefined;
  }
  
  return {
    isMobile,
    isIOSSafari,
    isIOSChrome,
    isIOSFirefox,
    isAndroid,
    isAndroidChrome,
    userAgent,
    version
  };
}

/**
 * iOS Safari-safe navigation function
 * Uses location.href for iOS Safari to avoid pushState issues
 */
export function navigateToPage(url: string, browserInfo?: BrowserInfo): void {
  const browser = browserInfo || detectBrowser();
  
  console.log('🧭 Navigating to:', url, { 
    isIOSSafari: browser.isIOSSafari,
    method: browser.isIOSSafari ? 'location.href' : 'pushState'
  });
  
  if (browser.isIOSSafari) {
    // Use location.href for iOS Safari for more reliable navigation
    window.location.href = url;
  } else {
    // Use pushState for other browsers for smoother navigation
    window.history.pushState({}, '', url);
    
    // Trigger a popstate event to ensure React Router updates
    window.dispatchEvent(new PopStateEvent('popstate'));
  }
}

/**
 * iOS Safari-specific fixes for common issues
 */
export function applyIOSSafariFixes(): void {
  const browser = detectBrowser();
  
  if (!browser.isIOSSafari) {
    return;
  }
  
  console.log('🍎 Applying iOS Safari-specific fixes...');
  
  // Fix viewport height issues
  const setViewportHeight = () => {
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
  };
  
  setViewportHeight();
  window.addEventListener('resize', setViewportHeight);
  window.addEventListener('orientationchange', () => {
    setTimeout(setViewportHeight, 100);
  });
  
  // Fix touch event handling
  document.addEventListener('touchstart', () => {}, { passive: true });
  
  // Fix input focus issues
  const inputs = document.querySelectorAll('input, textarea, select');
  inputs.forEach(input => {
    input.addEventListener('focus', () => {
      // Scroll to input to prevent keyboard covering it
      setTimeout(() => {
        input.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }, 300);
    });
  });
  
  console.log('✅ iOS Safari fixes applied');
}

/**
 * Check if current browser has known navigation issues
 */
export function hasNavigationIssues(): boolean {
  const browser = detectBrowser();
  return browser.isIOSSafari;
}

/**
 * Log browser information for debugging
 */
export function logBrowserInfo(): void {
  const browser = detectBrowser();
  
  console.log('🔍 Browser Detection Results:', {
    isMobile: browser.isMobile,
    isIOSSafari: browser.isIOSSafari,
    isIOSChrome: browser.isIOSChrome,
    isIOSFirefox: browser.isIOSFirefox,
    isAndroid: browser.isAndroid,
    isAndroidChrome: browser.isAndroidChrome,
    version: browser.version,
    userAgent: browser.userAgent.substring(0, 100) + '...'
  });
}
