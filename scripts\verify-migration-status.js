/**
 * TELEGRAM-TO-WEBSITE MIGRATION STATUS VERIFICATION SCRIPT
 * 
 * This script verifies the migration status and synchronization
 * between telegram_users and users tables after migration.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function verifyMigrationStatus() {
  console.log('🔍 TELEGRAM-TO-WEBSITE MIGRATION STATUS VERIFICATION');
  console.log('===================================================\n');

  try {
    // Get migration statistics
    console.log('📊 Fetching migration statistics...\n');

    const { data: stats, error: statsError } = await supabase.rpc('get_migration_stats');
    
    if (statsError) {
      // If function doesn't exist, calculate manually
      const results = await calculateMigrationStats();
      displayMigrationResults(results);
    } else {
      displayMigrationResults(stats);
    }

    // Check for incomplete migrations
    await checkIncompleteMigrations();

    // Verify data integrity
    await verifyDataIntegrity();

    // Check for migration candidates
    await checkMigrationCandidates();

    console.log('\n✅ Migration status verification completed!');

  } catch (error) {
    console.error('❌ Error during migration verification:', error);
    throw error;
  }
}

async function calculateMigrationStats() {
  console.log('📊 Calculating migration statistics...');

  // Get telegram_users counts
  const { data: telegramStats, error: telegramError } = await supabase
    .from('telegram_users')
    .select('user_id, is_registered')
    .neq('user_id', null);

  if (telegramError) throw telegramError;

  // Get users with telegram_id
  const { data: webUsers, error: webError } = await supabase
    .from('users')
    .select('id, telegram_id, username, email, created_at')
    .neq('telegram_id', null);

  if (webError) throw webError;

  // Get total counts
  const { count: totalTelegramUsers } = await supabase
    .from('telegram_users')
    .select('*', { count: 'exact', head: true });

  const { count: totalWebUsers } = await supabase
    .from('users')
    .select('*', { count: 'exact', head: true });

  const { count: unmigratedTelegramUsers } = await supabase
    .from('telegram_users')
    .select('*', { count: 'exact', head: true })
    .is('user_id', null)
    .eq('is_registered', true);

  return {
    total_telegram_users: totalTelegramUsers,
    total_web_users: totalWebUsers,
    migrated_users: telegramStats.length,
    web_users_with_telegram: webUsers.length,
    unmigrated_registered_telegram_users: unmigratedTelegramUsers,
    migration_completion_rate: totalTelegramUsers > 0 ? 
      ((telegramStats.length / totalTelegramUsers) * 100).toFixed(2) : 0
  };
}

function displayMigrationResults(results) {
  console.log('📊 MIGRATION STATISTICS');
  console.log('========================');
  console.log(`Total Telegram Users: ${results.total_telegram_users}`);
  console.log(`Total Website Users: ${results.total_web_users}`);
  console.log(`Successfully Migrated: ${results.migrated_users}`);
  console.log(`Web Users with Telegram ID: ${results.web_users_with_telegram}`);
  console.log(`Unmigrated Registered Users: ${results.unmigrated_registered_telegram_users}`);
  console.log(`Migration Completion Rate: ${results.migration_completion_rate}%\n`);

  // Status assessment
  if (results.migration_completion_rate >= 95) {
    console.log('✅ EXCELLENT: Migration is nearly complete!');
  } else if (results.migration_completion_rate >= 80) {
    console.log('🟡 GOOD: Most users have been migrated.');
  } else if (results.migration_completion_rate >= 50) {
    console.log('🟠 MODERATE: Significant migration progress made.');
  } else {
    console.log('🔴 LOW: Many users still need to be migrated.');
  }
  console.log('');
}

async function checkIncompleteMigrations() {
  console.log('🔍 Checking for incomplete migrations...');

  // Find telegram_users with user_id but no corresponding user
  const { data: orphanedTelegramUsers, error: orphanError } = await supabase
    .from('telegram_users')
    .select(`
      id, user_id, username, telegram_id, 
      first_name, last_name, temp_email
    `)
    .not('user_id', 'is', null)
    .not('user_id', 'in', `(SELECT id FROM users)`);

  if (orphanError) {
    console.warn('⚠️ Could not check for orphaned telegram users:', orphanError.message);
  } else if (orphanedTelegramUsers && orphanedTelegramUsers.length > 0) {
    console.log(`❌ Found ${orphanedTelegramUsers.length} orphaned telegram_users records:`);
    orphanedTelegramUsers.forEach((user, index) => {
      console.log(`${index + 1}. ID: ${user.id}, user_id: ${user.user_id}, username: ${user.username}`);
    });
  } else {
    console.log('✅ No orphaned telegram_users records found.');
  }

  // Find users with telegram_id but no corresponding telegram_users
  const { data: orphanedWebUsers, error: webOrphanError } = await supabase
    .from('users')
    .select('id, username, telegram_id, email')
    .not('telegram_id', 'is', null)
    .not('telegram_id', 'in', `(SELECT telegram_id FROM telegram_users)`);

  if (webOrphanError) {
    console.warn('⚠️ Could not check for orphaned web users:', webOrphanError.message);
  } else if (orphanedWebUsers && orphanedWebUsers.length > 0) {
    console.log(`❌ Found ${orphanedWebUsers.length} orphaned users records:`);
    orphanedWebUsers.forEach((user, index) => {
      console.log(`${index + 1}. ID: ${user.id}, telegram_id: ${user.telegram_id}, username: ${user.username}`);
    });
  } else {
    console.log('✅ No orphaned users records found.');
  }

  console.log('');
}

async function verifyDataIntegrity() {
  console.log('🔍 Verifying data integrity...');

  // Check username consistency
  const { data: usernameMismatches, error: usernameError } = await supabase
    .from('telegram_users')
    .select(`
      user_id, username,
      users!inner(id, username)
    `)
    .neq('user_id', null)
    .neq('username', 'users.username');

  if (usernameError) {
    console.warn('⚠️ Could not check username consistency:', usernameError.message);
  } else if (usernameMismatches && usernameMismatches.length > 0) {
    console.log(`⚠️ Found ${usernameMismatches.length} username mismatches:`);
    usernameMismatches.forEach((mismatch, index) => {
      console.log(`${index + 1}. User ID: ${mismatch.user_id}`);
      console.log(`   Telegram: ${mismatch.username}`);
      console.log(`   Website: ${mismatch.users.username}`);
    });
  } else {
    console.log('✅ All usernames are consistent between tables.');
  }

  // Check telegram_id consistency
  const { data: telegramIdMismatches, error: telegramIdError } = await supabase
    .from('telegram_users')
    .select(`
      user_id, telegram_id,
      users!inner(id, telegram_id)
    `)
    .neq('user_id', null)
    .neq('telegram_id', 'users.telegram_id');

  if (telegramIdError) {
    console.warn('⚠️ Could not check telegram_id consistency:', telegramIdError.message);
  } else if (telegramIdMismatches && telegramIdMismatches.length > 0) {
    console.log(`❌ Found ${telegramIdMismatches.length} telegram_id mismatches - THIS IS CRITICAL!`);
    telegramIdMismatches.forEach((mismatch, index) => {
      console.log(`${index + 1}. User ID: ${mismatch.user_id}`);
      console.log(`   Telegram table: ${mismatch.telegram_id}`);
      console.log(`   Users table: ${mismatch.users.telegram_id}`);
    });
  } else {
    console.log('✅ All telegram_ids are consistent between tables.');
  }

  console.log('');
}

async function checkMigrationCandidates() {
  console.log('🔍 Checking for migration candidates...');

  // Find registered telegram users without user_id
  const { data: migrationCandidates, error: candidatesError } = await supabase
    .from('telegram_users')
    .select('id, username, telegram_id, first_name, last_name, temp_email, created_at')
    .is('user_id', null)
    .eq('is_registered', true)
    .order('created_at', { ascending: false });

  if (candidatesError) {
    console.warn('⚠️ Could not check for migration candidates:', candidatesError.message);
  } else if (migrationCandidates && migrationCandidates.length > 0) {
    console.log(`📋 Found ${migrationCandidates.length} users ready for migration:`);
    migrationCandidates.slice(0, 10).forEach((user, index) => {
      console.log(`${index + 1}. ${user.username || 'No username'}`);
      console.log(`   Name: ${user.first_name} ${user.last_name || ''}`);
      console.log(`   Email: ${user.temp_email || 'None'}`);
      console.log(`   Telegram ID: ${user.telegram_id}`);
      console.log(`   Created: ${user.created_at}`);
      console.log('');
    });

    if (migrationCandidates.length > 10) {
      console.log(`... and ${migrationCandidates.length - 10} more users ready for migration.\n`);
    }

    console.log('💡 RECOMMENDATION: Set up migration notifications to inform these users.');
  } else {
    console.log('✅ No registered users waiting for migration.');
  }

  console.log('');
}

// Run the verification
if (import.meta.url === `file://${process.argv[1]}`) {
  verifyMigrationStatus()
    .then(() => {
      console.log('\n🎉 Migration verification completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Migration verification failed:', error);
      process.exit(1);
    });
}

export { verifyMigrationStatus };
