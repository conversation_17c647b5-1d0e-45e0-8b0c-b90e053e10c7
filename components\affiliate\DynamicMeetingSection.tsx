import React, { useState, useEffect } from 'react';

interface Meeting {
  id: number;
  title: string;
  topic: string;
  description?: string;
  date: string;
  time: string;
  timezone: string;
  duration: number;
  maxAttendees: number;
  currentAttendees: number;
  availableSpots: number;
  isFullyBooked: boolean;
  status: string;
}

interface DynamicMeetingSectionProps {
  onBookingClick: (meeting: Meeting) => void;
}

const DynamicMeetingSection: React.FC<DynamicMeetingSectionProps> = ({ onBookingClick }) => {
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadUpcomingMeetings();
  }, []);

  const loadUpcomingMeetings = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log('🔍 Loading upcoming meetings...');
      
      const response = await fetch('/api/meetings/upcoming');
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to load meetings');
      }

      const data = await response.json();
      console.log('✅ Loaded meetings:', data.meetings);
      
      setMeetings(data.meetings || []);
    } catch (err) {
      console.error('❌ Error loading meetings:', err);
      setError(err instanceof Error ? err.message : 'Failed to load meetings');
    } finally {
      setLoading(false);
    }
  };

  const formatDateTime = (date: string, time: string, timezone: string) => {
    try {
      const dateTime = new Date(`${date}T${time}`);
      return {
        date: dateTime.toLocaleDateString('en-US', { 
          weekday: 'long',
          year: 'numeric', 
          month: 'long', 
          day: 'numeric' 
        }),
        time: dateTime.toLocaleTimeString('en-US', { 
          hour: '2-digit', 
          minute: '2-digit',
          hour12: true
        }),
        timezone: timezone || 'SAST'
      };
    } catch (error) {
      return {
        date: date,
        time: time,
        timezone: timezone || 'SAST'
      };
    }
  };

  const getTopicsFromDescription = (description?: string) => {
    if (!description) return [];
    
    // Extract bullet points or numbered items from description
    const lines = description.split('\n').filter(line => line.trim());
    const topics = lines
      .filter(line => line.includes('•') || line.includes('-') || /^\d+\./.test(line.trim()))
      .map(line => line.replace(/^[•\-\d\.]\s*/, '').trim())
      .slice(0, 4); // Limit to 4 topics
    
    return topics.length > 0 ? topics : [
      'Company Updates & Announcements',
      'Mining Operations Progress',
      'Shareholder Q&A Session',
      'Future Expansion Plans'
    ];
  };

  if (loading) {
    return (
      <section id="meetings" className="meetings-section">
        <div className="section-container">
          <div className="section-content">
            <div className="section-text">
              <h2 className="section-title">Connect with Our Community</h2>
              <p className="section-description">
                Join our exclusive Zoom meetings to learn directly from the founders, ask questions,
                and connect with other shareholders. Get insider updates and build relationships with our community.
              </p>
              <div style={{ textAlign: 'center', padding: '40px', color: '#ccc' }}>
                <div style={{ fontSize: '24px', marginBottom: '10px' }}>⏳</div>
                Loading upcoming meetings...
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section id="meetings" className="meetings-section">
        <div className="section-container">
          <div className="section-content">
            <div className="section-text">
              <h2 className="section-title">Connect with Our Community</h2>
              <p className="section-description">
                Join our exclusive Zoom meetings to learn directly from the founders, ask questions,
                and connect with other shareholders. Get insider updates and build relationships with our community.
              </p>
              <div style={{ textAlign: 'center', padding: '40px', color: '#f44336' }}>
                <div style={{ fontSize: '24px', marginBottom: '10px' }}>⚠️</div>
                Unable to load meetings at this time. Please try again later.
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  if (meetings.length === 0) {
    return (
      <section id="meetings" className="meetings-section">
        <div className="section-container">
          <div className="section-content">
            <div className="section-text">
              <h2 className="section-title">Connect with Our Community</h2>
              <p className="section-description">
                Join our exclusive Zoom meetings to learn directly from the founders, ask questions,
                and connect with other shareholders. Get insider updates and build relationships with our community.
              </p>
              <div style={{ textAlign: 'center', padding: '40px', color: '#ccc' }}>
                <div style={{ fontSize: '24px', marginBottom: '10px' }}>📅</div>
                No upcoming meetings scheduled at this time. Check back soon for new announcements!
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="meetings" className="meetings-section">
      <div className="section-container">
        <div className="section-content">
          <div className="section-text">
            <h2 className="section-title">Connect with Our Community</h2>
            <p className="section-description">
              Join our exclusive Zoom meetings to learn directly from the founders, ask questions,
              and connect with other shareholders. Get insider updates and build relationships with our community.
            </p>

            <div className="meetings-grid">
              {meetings.slice(0, 3).map((meeting) => {
                const dateTime = formatDateTime(meeting.date, meeting.time, meeting.timezone);
                const topics = getTopicsFromDescription(meeting.description);
                
                return (
                  <div key={meeting.id} className="meeting-card">
                    <div className="meeting-header">
                      <div className="meeting-icon">📅</div>
                      <h3>{meeting.title}</h3>
                    </div>
                    <div className="meeting-content">
                      <div className="next-meeting">
                        <h4>{meeting.topic}</h4>
                        <div className="meeting-schedule">
                          <div className="schedule-item">
                            <span className="schedule-label">Date:</span>
                            <span className="schedule-value">{dateTime.date}</span>
                          </div>
                          <div className="schedule-item">
                            <span className="schedule-label">Time:</span>
                            <span className="schedule-value">{dateTime.time} {dateTime.timezone}</span>
                          </div>
                          <div className="schedule-item">
                            <span className="schedule-label">Duration:</span>
                            <span className="schedule-value">{meeting.duration} minutes</span>
                          </div>
                          <div className="schedule-item">
                            <span className="schedule-label">Available Spots:</span>
                            <span className="schedule-value" style={{ 
                              color: meeting.isFullyBooked ? '#f44336' : '#4CAF50' 
                            }}>
                              {meeting.isFullyBooked ? 'Fully Booked' : `${meeting.availableSpots} of ${meeting.maxAttendees}`}
                            </span>
                          </div>
                        </div>
                        {topics.length > 0 && (
                          <div className="meeting-topics">
                            <h5>Topics to Cover:</h5>
                            <ul>
                              {topics.map((topic, index) => (
                                <li key={index}>{topic}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                      <button 
                        className="meeting-register-btn"
                        onClick={() => onBookingClick(meeting)}
                        disabled={meeting.isFullyBooked}
                        style={{
                          backgroundColor: meeting.isFullyBooked ? '#666' : '#FFD700',
                          color: meeting.isFullyBooked ? '#ccc' : '#000',
                          cursor: meeting.isFullyBooked ? 'not-allowed' : 'pointer',
                          opacity: meeting.isFullyBooked ? 0.6 : 1
                        }}
                      >
                        {meeting.isFullyBooked ? 'Fully Booked' : 'Book Your Spot'}
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>

            <div className="meeting-cta">
              <p>Want to stay updated on all our meetings and events?</p>
              <button
                onClick={() => {
                  // Scroll to registration or trigger registration modal
                  const registrationButton = document.querySelector('.primary-cta');
                  if (registrationButton) {
                    (registrationButton as HTMLElement).click();
                  }
                }}
                className="section-cta"
              >
                Join Our Community
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DynamicMeetingSection;
