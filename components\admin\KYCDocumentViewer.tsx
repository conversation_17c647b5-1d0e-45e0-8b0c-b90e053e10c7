/**
 * KYC DOCUMENT VIEWER COMPONENT
 *
 * Advanced document viewer for KYC submissions with:
 * - Image preview with zoom and pan
 * - Document-specific approval/rejection
 * - Side-by-side comparison for ID front/back
 * - Download functionality
 * - Audit trail logging
 */

import React, { useState, useEffect, useRef } from 'react';
import { getServiceRoleClient } from '../../lib/supabase';

interface KYCDocument {
  id: string;
  type: 'id_document' | 'selfie' | 'proof_of_address';
  fileName: string;
  url: string;
  uploadedAt: string;
  verificationStatus?: 'pending' | 'approved' | 'rejected' | 'requires_resubmission' | 'expired';
  reviewedBy?: string;
  reviewedAt?: string;
  rejectionReason?: string;
  adminNotes?: string;
}

interface KYCDocumentViewerProps {
  kycData: any;
  onDocumentStatusChange?: () => void;
  currentUser: any;
  documentType?: 'id_document' | 'selfie' | 'proof_of_address';
}

interface ImageViewerState {
  scale: number;
  translateX: number;
  translateY: number;
  isDragging: boolean;
  dragStart: { x: number; y: number };
}

const KYCDocumentViewer: React.FC<KYCDocumentViewerProps> = ({
  kycData,
  onDocumentStatusChange,
  currentUser,
  documentType
}) => {
  const [documents, setDocuments] = useState<KYCDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedDocument, setSelectedDocument] = useState<KYCDocument | null>(null);
  const [showImageModal, setShowImageModal] = useState(false);
  const [processing, setProcessing] = useState(false);

  // Load documents from storage
  useEffect(() => {
    loadDocuments();
  }, [kycData]);

  const loadDocuments = async () => {
    if (!kycData?.id) return;

    setLoading(true);
    try {
      const serviceClient = getServiceRoleClient();
      const kycId = kycData.id;

      console.log('🔍 Fetching documents from database for KYC ID:', kycId);

      // Get documents from kyc_documents table instead of storage bucket
      const { data: dbDocuments, error } = await serviceClient
        .from('kyc_documents')
        .select('*')
        .eq('kyc_id', kycId)
        .order('uploaded_at', { ascending: false });

      if (error) {
        console.error('❌ Error fetching documents from database:', error);
        return;
      }

      console.log('📄 Total documents in database:', dbDocuments?.length || 0);

      const documentList: KYCDocument[] = [];

      if (dbDocuments && dbDocuments.length > 0) {
        // Group documents by type and keep only the most recent one
        const documentsByType: Record<string, any> = {};

        for (const dbDoc of dbDocuments) {
          // Get public URL for the document
          const { data: { publicUrl } } = serviceClient.storage
            .from(dbDoc.storage_bucket || 'proof')
            .getPublicUrl(dbDoc.file_path || dbDoc.file_name);

          // Map database document types to component types
          let type: 'id_document' | 'selfie' | 'proof_of_address' = 'id_document';

          if (dbDoc.document_type === 'selfie_verification' || dbDoc.document_type === 'facial_recognition') {
            type = 'selfie';
          } else if (dbDoc.document_type === 'proof_of_address') {
            type = 'proof_of_address';
          } else if (dbDoc.document_type === 'id_document' || dbDoc.document_type === 'id_document_front' || dbDoc.document_type === 'id_document_back') {
            type = 'id_document';
          }

          const uploadedAt = dbDoc.uploaded_at;

          // Only keep the most recent document for each type
          if (!documentsByType[type] || new Date(uploadedAt) > new Date(documentsByType[type].uploadedAt)) {
            documentsByType[type] = {
              id: dbDoc.id,
              type,
              fileName: dbDoc.file_name,
              url: publicUrl,
              uploadedAt: dbDoc.uploaded_at,
              verificationStatus: dbDoc.verification_status,
              reviewedBy: dbDoc.reviewed_by,
              reviewedAt: dbDoc.reviewed_at,
              rejectionReason: dbDoc.rejection_reason,
              adminNotes: dbDoc.admin_notes
            };
          }
        }

        // Convert to array
        for (const doc of Object.values(documentsByType)) {
          documentList.push(doc);
        }

        console.log('📄 Total database documents found:', dbDocuments.length);
        console.log('📄 Document types found:', Object.keys(documentsByType));
        console.log('📄 Final documents (latest only):', documentList.length);
        console.log('📄 Documents loaded with status:', documentList.map(doc => ({
          type: doc.type,
          fileName: doc.fileName,
          verificationStatus: doc.verificationStatus,
          uploadedAt: doc.uploadedAt
        })));
      }

      setDocuments(documentList);

    } catch (error) {
      console.error('❌ Error loading documents:', error);
    } finally {
      setLoading(false);
    }
  };

  // Document type display names
  const getDocumentTypeName = (type: string) => {
    const typeNames: Record<string, string> = {
      'id_document': 'ID Document',
      'proof_of_address': 'Proof of Address',
      'selfie': 'Selfie Verification'
    };
    return typeNames[type] || type;
  };

  // Get document type icon
  const getDocumentIcon = (type: string) => {
    switch (type) {
      case 'id_document': return '🆔';
      case 'proof_of_address': return '🏠';
      case 'selfie': return '🤳';
      default: return '📄';
    }
  };

  // Handle document selection for viewing
  const handleDocumentSelect = (document: KYCDocument) => {
    setSelectedDocument(document);
    setShowImageModal(true);
  };

  // Handle document download
  const handleDownloadDocument = (document: KYCDocument) => {
    try {
      const link = window.document.createElement('a');
      link.href = document.url;
      link.download = document.fileName;
      link.target = '_blank';
      window.document.body.appendChild(link);
      link.click();
      window.document.body.removeChild(link);
      console.log(`✅ Downloaded document: ${document.fileName}`);
    } catch (error) {
      console.error('❌ Error downloading document:', error);
      // Fallback: open in new tab
      window.open(document.url, '_blank');
    }
  };

  // Handle opening document in new window
  const handleOpenDocument = (document: KYCDocument) => {
    try {
      window.open(document.url, '_blank', 'noopener,noreferrer');
      console.log(`✅ Opened document in new window: ${document.fileName}`);
    } catch (error) {
      console.error('❌ Error opening document:', error);
      alert('Unable to open document in new window. Please check your popup blocker settings.');
    }
  };

  if (loading) {
    return (
      <div className="glass-card p-8 text-center">
        <div className="text-4xl mb-4">⏳</div>
        <p className="text-gray-400">Loading documents...</p>
      </div>
    );
  }

  // Filter documents by type if specified
  const filteredDocuments = documentType
    ? documents.filter(doc => doc.type === documentType)
    : documents;

  console.log(`🔍 KYCDocumentViewer for type "${documentType}":`, {
    totalDocuments: documents.length,
    filteredDocuments: filteredDocuments.length,
    documentType,
    filteredDocs: filteredDocuments.map(doc => ({
      fileName: doc.fileName,
      type: doc.type,
      uploadedAt: doc.uploadedAt
    }))
  });

  if (filteredDocuments.length === 0) {
    const documentTypeName = documentType ? getDocumentTypeName(documentType) : 'documents';
    return (
      <div className="glass-card p-8 text-center">
        <div className="text-6xl mb-4">📄</div>
        <p className="text-gray-400 text-lg">No {documentTypeName.toLowerCase()} uploaded</p>
        <p className="text-gray-500 text-sm mt-2">
          This KYC submission doesn't have any {documentTypeName.toLowerCase()} yet.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Documents Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredDocuments.map((document) => (
          <div
            key={document.id}
            className="bg-gray-800/50 rounded-lg p-4 border border-gray-700 hover:border-yellow-500/50 transition-colors"
          >
            {/* Document Header */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <span className="text-2xl">{getDocumentIcon(document.type)}</span>
                <div>
                  <h4 className="font-medium text-white text-sm">
                    {getDocumentTypeName(document.type)}
                  </h4>
                  <p className="text-xs text-gray-400 mt-1">
                    {document.fileName}
                  </p>
                </div>
              </div>
              {/* Verification Status Badge */}
              {document.verificationStatus && (
                <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                  document.verificationStatus === 'pending' ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30' :
                  document.verificationStatus === 'approved' ? 'bg-green-500/20 text-green-400 border border-green-500/30' :
                  document.verificationStatus === 'rejected' ? 'bg-red-500/20 text-red-400 border border-red-500/30' :
                  'bg-gray-500/20 text-gray-400 border border-gray-500/30'
                }`}>
                  {document.verificationStatus === 'pending' ? '⏳ Pending' :
                   document.verificationStatus === 'approved' ? '✅ Approved' :
                   document.verificationStatus === 'rejected' ? '❌ Rejected' :
                   document.verificationStatus}
                </div>
              )}
            </div>

            {/* Document Preview */}
            <div
              className="aspect-video bg-gray-900 rounded-lg mb-3 flex items-center justify-center overflow-hidden cursor-pointer hover:bg-gray-800 transition-colors"
              onClick={() => handleDocumentSelect(document)}
            >
              <img
                src={document.url}
                alt={document.fileName}
                className="max-w-full max-h-full object-contain"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.nextElementSibling?.classList.remove('hidden');
                }}
              />
              <div className="text-gray-400 text-center hidden">
                <div className="text-2xl mb-2">📄</div>
                <div className="text-xs">Click to view</div>
              </div>
            </div>

            {/* Document Info */}
            <div className="text-xs text-gray-400 space-y-1 mb-3">
              <div>Uploaded: {new Date(document.uploadedAt).toLocaleDateString()}</div>
            </div>

            {/* Actions */}
            <div className="flex space-x-2">
              <button
                onClick={() => handleDocumentSelect(document)}
                className="flex-1 px-3 py-2 bg-blue-600 hover:bg-blue-500 text-white text-xs rounded transition-colors"
              >
                👁️ View
              </button>
              <button
                onClick={() => handleOpenDocument(document)}
                className="px-3 py-2 bg-green-600 hover:bg-green-500 text-white text-xs rounded transition-colors"
                title="Open in new window"
              >
                🔗
              </button>
              <button
                onClick={() => handleDownloadDocument(document)}
                className="px-3 py-2 bg-gray-600 hover:bg-gray-500 text-white text-xs rounded transition-colors"
                title="Download document"
              >
                ⬇️
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Image Modal */}
      {showImageModal && selectedDocument && (
        <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50">
          <div className="max-w-4xl max-h-[90vh] w-full mx-4">
            {/* Modal Header */}
            <div className="bg-gray-800 rounded-t-lg p-4 flex justify-between items-center">
              <div className="flex items-center space-x-3">
                <span className="text-2xl">{getDocumentIcon(selectedDocument.type)}</span>
                <div>
                  <h3 className="text-lg font-semibold text-white">
                    {getDocumentTypeName(selectedDocument.type)}
                  </h3>
                  <p className="text-sm text-gray-400">{selectedDocument.fileName}</p>
                </div>
              </div>
              <button
                onClick={() => setShowImageModal(false)}
                className="text-gray-400 hover:text-white text-2xl"
              >
                ✕
              </button>
            </div>

            {/* Image Container */}
            <div className="bg-gray-900 rounded-b-lg p-4 flex items-center justify-center min-h-[60vh]">
              <img
                src={selectedDocument.url}
                alt={selectedDocument.fileName}
                className="max-w-full max-h-full object-contain"
                style={{ maxHeight: '70vh' }}
              />
            </div>

            {/* Modal Footer */}
            <div className="bg-gray-800 rounded-b-lg p-4 flex justify-center space-x-4">
              <button
                onClick={() => handleOpenDocument(selectedDocument)}
                className="px-4 py-2 bg-green-600 hover:bg-green-500 text-white rounded transition-colors"
              >
                🔗 Open
              </button>
              <button
                onClick={() => handleDownloadDocument(selectedDocument)}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-500 text-white rounded transition-colors"
              >
                ⬇️ Download
              </button>
              <button
                onClick={() => setShowImageModal(false)}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default KYCDocumentViewer;
