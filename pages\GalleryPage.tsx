import React from 'react';
import { EnhancedGallery } from '../components/gallery/EnhancedGallery';

interface GalleryPageProps {
  onNavigate: (page: string) => void;
}

const GalleryPage: React.FC<GalleryPageProps> = ({ onNavigate }) => {
  return (
    <div className="page">
      {/* Hero Section */}
      <section className="gallery-hero">
        <div className="container">
          <div className="gallery-hero-content">
            {/* Breadcrumb Navigation */}
            <nav className="gallery-breadcrumb">
              <button onClick={() => onNavigate('home')} className="breadcrumb-link">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" stroke="currentColor" strokeWidth="2"/>
                  <polyline points="9,22 9,12 15,12 15,22" stroke="currentColor" strokeWidth="2"/>
                </svg>
                Home
              </button>
              <span className="breadcrumb-separator">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <polyline points="9,18 15,12 9,6" stroke="currentColor" strokeWidth="2"/>
                </svg>
              </span>
              <span className="breadcrumb-current">Gallery</span>
            </nav>

            {/* Main Hero Content */}
            <div className="gallery-hero-main">
              <div className="gallery-hero-text">
                <h1 className="gallery-hero-title">
                  Proof of Concept
                  <span className="gallery-hero-title-accent">& Gallery</span>
                </h1>
                <p className="gallery-hero-description">
                  A visual journey through our on-site activities, team collaborations, and project milestones.
                  Regular photo updates from our operational countries serve as proof of our legitimate, active business operations.
                </p>
              </div>

              {/* Professional Stats Grid */}
              <div className="gallery-hero-stats">
                <div className="gallery-stat-card">
                  <div className="gallery-stat-icon">📸</div>
                  <div className="gallery-stat-content">
                    <div className="gallery-stat-value">500+</div>
                    <div className="gallery-stat-label">Professional Photos</div>
                  </div>
                </div>
                <div className="gallery-stat-card">
                  <div className="gallery-stat-icon">🏷️</div>
                  <div className="gallery-stat-content">
                    <div className="gallery-stat-value">12</div>
                    <div className="gallery-stat-label">Organized Categories</div>
                  </div>
                </div>
                <div className="gallery-stat-card">
                  <div className="gallery-stat-icon">🔄</div>
                  <div className="gallery-stat-content">
                    <div className="gallery-stat-value">Weekly</div>
                    <div className="gallery-stat-label">Fresh Updates</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Introduction */}
      <section className="gallery-intro">
        <div className="container">
          <div className="section-header">
            <h2 className="section-title">Visual Documentation</h2>
            <p className="section-subtitle">
              Comprehensive photographic evidence of our mining operations and development progress
            </p>
          </div>

          <div className="intro-grid">
            <div className="intro-card enhanced-card">
              <div className="intro-icon">📸</div>
              <h3>Project Documentation</h3>
              <p>
                Real photos from our mining operations, showcasing the actual work being done
                on the ground and the progress of our expansion plans across multiple sites.
              </p>
            </div>

            <div className="intro-card enhanced-card">
              <div className="intro-icon">🏗️</div>
              <h3>Site Development</h3>
              <p>
                Visual evidence of our mining site development, equipment deployment,
                and infrastructure improvements across our operational areas in Zimbabwe.
              </p>
            </div>

            <div className="intro-card enhanced-card">
              <div className="intro-icon">👥</div>
              <h3>Team & Operations</h3>
              <p>
                Meet our team in action and see the day-to-day operations that drive
                our gold production and shareholder value creation initiatives.
              </p>
            </div>

            <div className="intro-card enhanced-card">
              <div className="intro-icon">🌍</div>
              <h3>Environmental Responsibility</h3>
              <p>
                Documentation of our commitment to responsible mining practices and
                environmental stewardship in all our operational territories.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Content */}
      <section className="gallery-content">
        <div className="container">
          <div className="gallery-wrapper">
            <EnhancedGallery
              showCategories={true}
              showSearch={false}
              itemsPerPage={12}
              showFeaturedFirst={true}
              fallbackMode="static"
              layout="mixed"
              height="600px"
              compact={true}
            />
          </div>
        </div>
      </section>

      {/* Gallery Features */}
      <section className="gallery-features">
        <div className="container">
          <div className="section-header">
            <h2 className="section-title">Gallery Features</h2>
            <p className="section-subtitle">
              Advanced features designed to showcase our operations with maximum transparency
            </p>
          </div>

          <div className="features-grid">
            <div className="feature-card enhanced-card">
              <div className="feature-icon">🔍</div>
              <h4>High-Resolution Images</h4>
              <p>All images are captured in high resolution to provide clear, detailed views of our operations and progress milestones.</p>
            </div>

            <div className="feature-card enhanced-card">
              <div className="feature-icon">📅</div>
              <h4>Chronological Organization</h4>
              <p>Images are organized by date and project phase, making it easy to track our development over time and verify progress.</p>
            </div>

            <div className="feature-card enhanced-card">
              <div className="feature-icon">🏷️</div>
              <h4>Categorized Content</h4>
              <p>Browse by category including operations, equipment, team, environmental, and milestone achievements for easy navigation.</p>
            </div>

            <div className="feature-card enhanced-card">
              <div className="feature-icon">📱</div>
              <h4>Mobile Optimized</h4>
              <p>Gallery is fully responsive and optimized for viewing on all devices, from desktop to mobile with touch-friendly controls.</p>
            </div>

            <div className="feature-card enhanced-card">
              <div className="feature-icon">🔒</div>
              <h4>Verified Authenticity</h4>
              <p>All photos include metadata verification and are taken directly from our operational sites with GPS coordinates.</p>
            </div>

            <div className="feature-card enhanced-card">
              <div className="feature-icon">⚡</div>
              <h4>Fast Loading</h4>
              <p>Optimized image delivery with progressive loading ensures quick access to our extensive photo documentation.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Legitimacy Proof Section */}
      <section className="legitimacy-proof">
        <div className="container">
          <div className="proof-content">
            <div className="proof-header">
              <div className="proof-icon">✓</div>
              <h2>Proof of Legitimacy</h2>
            </div>
            <p className="proof-description">
              Our gallery serves as transparent evidence of our active mining operations. Every photo is taken
              directly from our operational sites in Zimbabwe and other countries, providing shareholders with
              verifiable proof of our legitimate business activities and ongoing development progress.
            </p>
            <div className="proof-metrics">
              <div className="proof-metric">
                <div className="metric-value">Weekly</div>
                <div className="metric-label">Photo Updates</div>
              </div>
              <div className="proof-metric">
                <div className="metric-value">GPS</div>
                <div className="metric-label">Verified Locations</div>
              </div>
              <div className="proof-metric">
                <div className="metric-value">Real-Time</div>
                <div className="metric-label">Documentation</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="gallery-cta">
        <div className="container">
          <div className="cta-content">
            <h2>Ready to Be Part of Our Story?</h2>
            <p>
              Join thousands of shareholders who are already part of Aureus Alliance Holdings'
              journey toward sustainable gold production and shared prosperity. See the proof of our operations firsthand.
            </p>
            <div className="cta-buttons">
              <button
                className="btn-primary"
                onClick={() => onNavigate('investment-phases')}
              >
                View Share Phases
              </button>
              <button
                className="btn-secondary"
                onClick={() => onNavigate('calculator')}
              >
                Calculate Dividends
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Update Notification */}
      <section className="gallery-notification">
        <div className="container">
          <div className="notification-card enhanced-card">
            <div className="notification-icon">📸</div>
            <div className="notification-content">
              <h3>Regular Gallery Updates</h3>
              <p>
                We regularly add new photos to this gallery during our trips to Zimbabwe and other operational countries.
                These images serve as proof of our ongoing work and demonstrate that this is a legitimate, active business
                with real operations and tangible progress.
              </p>
              <div className="notification-meta">
                <div className="meta-item">
                  <span className="meta-label">Update Frequency:</span>
                  <span className="meta-value">Weekly during operational visits</span>
                </div>
                <div className="meta-item">
                  <span className="meta-label">Verification Status:</span>
                  <span className="verification-badge">✓ Verified Operations</span>
                </div>
                <div className="meta-item">
                  <span className="meta-label">Last Updated:</span>
                  <span className="meta-value">December 2024</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default GalleryPage;
