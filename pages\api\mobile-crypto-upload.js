// Mobile-specific crypto payment upload API endpoint
// This bypasses direct Supabase client calls that fail on mobile browsers

import { createClient } from '@supabase/supabase-js';
import { <PERSON>uff<PERSON> } from 'buffer';

// Force the correct Supabase configuration
const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const serviceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

// Create service role client for server-side operations
const serviceClient = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

export default async function handler(req, res) {
  // Set CORS headers for mobile browsers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('📱 Mobile crypto upload API called');
    console.log('📱 Request method:', req.method);
    console.log('📱 Request headers:', req.headers);

    const {
      fileData,
      fileName,
      filePath,
      paymentData,
      userAgent
    } = req.body;

    console.log('📱 Mobile upload request:', {
      fileName,
      filePath,
      hasFileData: !!fileData,
      fileDataLength: fileData?.length || 0,
      hasPaymentData: !!paymentData,
      paymentDataKeys: paymentData ? Object.keys(paymentData) : [],
      userAgent: userAgent?.substring(0, 50) + '...'
    });

    // Validate required fields
    if (!fileData || !fileName || !filePath || !paymentData) {
      console.error('❌ Missing required fields:', {
        hasFileData: !!fileData,
        hasFileName: !!fileName,
        hasFilePath: !!filePath,
        hasPaymentData: !!paymentData
      });
      return res.status(400).json({
        error: 'Missing required fields',
        details: 'fileData, fileName, filePath, and paymentData are required'
      });
    }

    // Convert base64 file data back to buffer
    let fileBuffer;
    try {
      fileBuffer = Buffer.from(fileData, 'base64');
      console.log('📱 File buffer size:', fileBuffer.length);

      if (fileBuffer.length === 0) {
        throw new Error('File buffer is empty');
      }
    } catch (bufferError) {
      console.error('❌ File buffer conversion error:', bufferError);
      return res.status(400).json({
        error: 'Invalid file data',
        details: 'Could not convert base64 file data to buffer'
      });
    }

    // Upload file to Supabase Storage using service role client
    console.log('📤 Uploading to Supabase Storage:', filePath);
    const { data: uploadData, error: uploadError } = await serviceClient.storage
      .from('proof')
      .upload(filePath, fileBuffer, {
        contentType: 'image/jpeg',
        upsert: false
      });

    if (uploadError) {
      console.error('❌ Upload error:', uploadError);
      return res.status(500).json({ 
        error: 'Upload failed', 
        details: uploadError.message 
      });
    }

    console.log('✅ File uploaded successfully:', uploadData);

    // Get public URL
    const { data: { publicUrl } } = serviceClient.storage
      .from('proof')
      .getPublicUrl(filePath);

    console.log('📎 Public URL:', publicUrl);

    // Create payment record in database
    const finalPaymentData = {
      ...paymentData,
      screenshot_url: publicUrl
    };

    console.log('💾 Creating payment record:', finalPaymentData);

    const { data: paymentResult, error: paymentError } = await serviceClient
      .from('crypto_payment_transactions')
      .insert([finalPaymentData])
      .select()
      .single();

    if (paymentError) {
      console.error('❌ Payment creation error:', paymentError);
      
      // Handle duplicate transaction hash
      if (paymentError.code === '23505' && paymentError.message.includes('transaction_hash')) {
        return res.status(400).json({ 
          error: 'This transaction hash has already been submitted. Each transaction can only be submitted once.' 
        });
      }

      return res.status(500).json({ 
        error: 'Payment creation failed', 
        details: paymentError.message 
      });
    }

    console.log('✅ Payment created successfully:', paymentResult);

    res.status(200).json({ 
      success: true, 
      payment: paymentResult,
      fileUrl: publicUrl
    });

  } catch (error) {
    console.error('❌ Mobile crypto upload API error:', error);
    res.status(500).json({ 
      error: 'Internal server error', 
      details: error.message 
    });
  }
}
