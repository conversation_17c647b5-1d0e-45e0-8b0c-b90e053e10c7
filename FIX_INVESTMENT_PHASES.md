# 🔧 Fix: "No phases found in database" Error

## Problem
The affiliate landing page shows the error:
```
❌ No phases found in database
❌ Unable to load phase data from database
```

## Root Cause
The `investment_phases` table exists in your database but is **empty**. The GoldDiggersClub component requires phase data to display the affiliate program information.

## Solution

### Step 1: Check Current Status
First, check if you have any existing phases:

```bash
node scripts/check-investment-phases.js
```

### Step 2: Create Investment Phases
If no phases exist, create all 20 phases (Pre Sale through Phase 19):

```bash
node scripts/setup-investment-phases.js
```

This will create:
- **Pre Sale (Phase 0)**: $5.00 per share, 200,000 shares, **ACTIVE**
- **Phase 1-19**: $10.00-$100.00 per share, 100,000 shares each, **INACTIVE**

### Step 3: Verify the Fix
1. Run the check script again:
   ```bash
   node scripts/check-investment-phases.js
   ```

2. Refresh your affiliate landing page - the error should be gone and you should see:
   - Phase selector dropdown with all 20 phases
   - Current commission structure (15% USDT + 15% Shares for Pre Sale)
   - Gold Diggers Club competition information

## Expected Result

After running the setup script, your affiliate page will show:

```
🏆 Gold Diggers Club
Compete for your share of $150,000 in prizes!

Phase: Pre Sale - $5 (Active)

Commission Structure:
✅ PRESALE EXCLUSIVE!
USDT Commission: 15%
Bonus NFT Shares: 15%
```

## Commission Structure Details

- **Pre Sale (Phase 0)**: 15% USDT + 15% Bonus NFT Shares
- **Phases 1-19**: 15% USDT Commission Only

This matches your Aureus Alliance expansion plan with the automated phase system maintaining the 15% USDT + 15% Shares commission structure.

## Troubleshooting

If you still see errors after running the setup:

1. **Check Supabase Connection**:
   - Verify your `.env` file has correct `REACT_APP_SUPABASE_URL` and `REACT_APP_SUPABASE_ANON_KEY`

2. **Check Database Permissions**:
   - Ensure your Supabase RLS policies allow reading from `investment_phases` table
   - The table should have a policy: "Authenticated users can read investment phases"

3. **Clear Browser Cache**:
   - Hard refresh your browser (Ctrl+F5 or Cmd+Shift+R)

4. **Check Console Logs**:
   - Open browser developer tools and check for any additional error messages

## Alternative: Manual Database Insert

If the scripts don't work, you can manually insert the data in your Supabase dashboard:

```sql
INSERT INTO investment_phases (phase_number, phase_name, price_per_share, total_shares_available, shares_sold, is_active, start_date) VALUES
(0, 'Pre Sale', 5.00, 200000, 0, true, NOW()),
(1, 'Phase 1', 10.00, 100000, 0, false, NULL),
(2, 'Phase 2', 15.00, 100000, 0, false, NULL),
-- ... continue for all 20 phases
```

The complete SQL is available in the setup script if needed.
