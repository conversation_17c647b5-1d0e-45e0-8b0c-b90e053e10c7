import React, { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';
import AdminCertificateCreator from './AdminCertificateCreator';
import { certificateService } from '../../lib/certificateService';
import { svgCertificateGenerator } from '../../lib/svgCertificateGenerator';

interface Certificate {
  id: string;
  user_id: number;
  purchase_id: string;
  certificate_number: string; // Keep for backward compatibility
  cert_no: string; // New sequential certificate number
  ref_no: string; // New cumulative share range
  shares_count: number;
  issue_date: string;
  status: string;
  certificate_data: any;
}

interface Purchase {
  id: string;
  user_id: number;
  package_name: string;
  shares_purchased: number;
  total_amount: number;
  status: string;
  created_at: string;
  user_email?: string;
  user_name?: string;
  existing_certificate?: {
    id: string;
    cert_no: string;
    ref_no: string;
    certificate_number: string;
    status: string;
  };
}

export const CertificateManagement: React.FC = () => {
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [pendingPurchases, setPendingPurchases] = useState<Purchase[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [selectedPurchases, setSelectedPurchases] = useState<Set<string>>(new Set());
  // Individual button states for each purchase
  const [processingPurchases, setProcessingPurchases] = useState<Set<string>>(new Set());
  const [activeTab, setActiveTab] = useState<'list' | 'create'>('list');
  // Bulk certificate selection for deletion
  const [selectedCertificates, setSelectedCertificates] = useState<Set<string>>(new Set());
  const [stats, setStats] = useState({
    total: 0,
    issued: 0,
    revoked: 0,
    totalShares: 0,
    totalValue: 0
  });

  useEffect(() => {
    loadData();
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const certificateStats = await certificateService.getCertificateStats();
      setStats(certificateStats);
    } catch (error) {
      console.error('Error loading certificate stats:', error);
    }
  };

  const loadData = async () => {
    setLoading(true);
    try {
      // Use service role client for admin operations
      const adminClient = getServiceRoleClient();

      // Load existing certificates with dual numbering fields
      const { data: certsData, error: certsError } = await adminClient
        .from('certificates')
        .select('*, cert_no, ref_no')
        .order('created_at', { ascending: true });

      if (certsError) {
        console.error('Error loading certificates:', certsError);
        setCertificates([]);
      } else {
        // Load user details for each certificate
        const certsWithUsers = await Promise.all(
          (certsData || []).map(async (cert) => {
            const { data: userData } = await adminClient
              .from('users')
              .select('email, full_name')
              .eq('id', cert.user_id)
              .single();

            return {
              ...cert,
              user: userData || { email: 'Unknown', full_name: 'Unknown User' }
            };
          })
        );

        setCertificates(certsWithUsers);
      }

      // Load all approved purchases (with and without certificates)
      console.log('🔄 Loading active share purchases...');
      const { data: purchasesData, error: purchasesError } = await adminClient
        .from('aureus_share_purchases')
        .select('*')
        .eq('status', 'active')
        .order('created_at', { ascending: true });

      if (purchasesError) {
        console.error('❌ Error loading purchases:', purchasesError);
        setPendingPurchases([]);
      } else {
        console.log('✅ Found', purchasesData?.length || 0, 'active purchases');

        // Create a map of certificates by purchase_id for quick lookup
        const certificatesByPurchase = new Map();
        (certsData || []).forEach(cert => {
          if (cert.purchase_id) {
            certificatesByPurchase.set(cert.purchase_id, {
              id: cert.id,
              cert_no: cert.cert_no,
              ref_no: cert.ref_no,
              certificate_number: cert.certificate_number,
              status: cert.status
            });
          }
        });

        // Load user details for each purchase and include certificate info
        const purchasesWithUsers = await Promise.all(
          (purchasesData || []).map(async (purchase) => {
            const { data: userData } = await adminClient
              .from('users')
              .select('email, full_name')
              .eq('id', purchase.user_id)
              .single();

            return {
              ...purchase,
              user: userData || { email: 'Unknown', full_name: 'Unknown User' },
              existing_certificate: certificatesByPurchase.get(purchase.id) || null
            };
          })
        );

        // Filter to show only purchases without certificates in the "pending" section
        const purchasesWithoutCerts = purchasesWithUsers.filter(p => !p.existing_certificate);
        setPendingPurchases(purchasesWithoutCerts);
      }
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const createCertificate = async (purchase: Purchase) => {
    // Add this purchase to processing set
    setProcessingPurchases(prev => new Set(prev).add(purchase.id));

    try {
      // Call the admin function to create certificate
      const { data, error } = await supabase.rpc('admin_create_certificate_dual', {
        p_user_id: purchase.user_id,
        p_purchase_id: purchase.id,
        p_shares_count: purchase.shares_purchased,
        p_certificate_data: {
          package_name: purchase.package_name,
          total_amount: purchase.total_amount,
          created_by: 'admin_manual'
        }
      });

      if (error) {
        console.error('Error creating certificate:', error);
        alert(`Error creating certificate: ${error.message}`);
      } else {
        alert(`Certificate created successfully: ${data}`);
        await loadData(); // Refresh data
      }
    } catch (error) {
      console.error('Error creating certificate:', error);
      alert('Error creating certificate');
    } finally {
      // Remove this purchase from processing set
      setProcessingPurchases(prev => {
        const newSet = new Set(prev);
        newSet.delete(purchase.id);
        return newSet;
      });
    }
  };

  const createPNGCertificate = async (purchase: Purchase, saveToDb: boolean = true) => {
    // Add this purchase to processing set
    setProcessingPurchases(prev => new Set(prev).add(purchase.id));

    try {
      // Generate PNG certificate
      const result = await svgCertificateGenerator.generateCertificatePNG(purchase.id, saveToDb);

      if (!result.pngBlob) {
        throw new Error('Failed to generate PNG certificate');
      }

      // Create download link for PNG
      const url = URL.createObjectURL(result.pngBlob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `certificate-${result.certificateData?.certificateNumber || purchase.id}.png`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      const fileSize = (result.pngBlob.size / 1024 / 1024).toFixed(2);
      const message = saveToDb
        ? `✅ Certificate created, saved to database, and downloaded!\n📁 File size: ${fileSize}MB\n📜 Certificate #: ${result.certificateData?.certificateNumber}`
        : `✅ Test certificate downloaded!\n📁 File size: ${fileSize}MB`;

      alert(message);

      if (saveToDb) {
        await loadData(); // Refresh data
      }
    } catch (error) {
      console.error('Error creating PNG certificate:', error);
      alert(`❌ Error creating PNG certificate: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      // Remove this purchase from processing set
      setProcessingPurchases(prev => {
        const newSet = new Set(prev);
        newSet.delete(purchase.id);
        return newSet;
      });
    }
  };

  const previewCertificate = async (purchase: Purchase) => {
    // Add this purchase to processing set
    setProcessingPurchases(prev => new Set(prev).add(purchase.id));

    try {

      // Generate SVG certificate preview without saving to database
      const svgCertificate = await svgCertificateGenerator.generateCertificatePreview(purchase.id);

      if (!svgCertificate) {
        throw new Error('Failed to generate certificate preview');
      }

      // Open preview in new window
      const previewWindow = window.open('', '_blank', 'width=1200,height=800');
      if (previewWindow) {
        previewWindow.document.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>Certificate Preview - Purchase ${purchase.id}</title>
              <style>
                body {
                  margin: 0;
                  padding: 20px;
                  background: #f0f0f0;
                  font-family: Arial, sans-serif;
                }
                .container {
                  max-width: 1000px;
                  margin: 0 auto;
                  background: white;
                  padding: 20px;
                  border-radius: 8px;
                  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                }
                .header {
                  text-align: center;
                  margin-bottom: 20px;
                  padding-bottom: 10px;
                  border-bottom: 2px solid #D4AF37;
                }
                .certificate-preview {
                  text-align: center;
                }
                .actions {
                  margin-top: 20px;
                  text-align: center;
                }
                button {
                  margin: 0 10px;
                  padding: 10px 20px;
                  background: #D4AF37;
                  color: white;
                  border: none;
                  border-radius: 4px;
                  cursor: pointer;
                }
                button:hover {
                  background: #B8860B;
                }
              </style>
            </head>
            <body>
              <div class="container">
                <div class="header">
                  <h1>📜 Aureus Alliance Holdings Certificate Preview</h1>
                  <p>Purchase ID: ${purchase.id} | User: ${purchase.user_name || 'N/A'}</p>
                </div>
                <div class="certificate-preview">
                  ${svgCertificate}
                </div>
                <div class="actions">
                  <button onclick="window.print()">🖨️ Print Certificate</button>
                  <button onclick="window.close()">✕ Close Preview</button>
                </div>
              </div>
            </body>
          </html>
        `);
        previewWindow.document.close();
      }

    } catch (error) {
      console.error('Error previewing certificate:', error);
      alert(`❌ Error previewing certificate: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      // Remove this purchase from processing set
      setProcessingPurchases(prev => {
        const newSet = new Set(prev);
        newSet.delete(purchase.id);
        return newSet;
      });
    }
  };

  const deleteCertificate = async (certificateId: string, purchaseId: string, certificateNumber?: string) => {
    const certDisplay = certificateNumber || certificateId;
    if (!confirm(`⚠️ DELETE CERTIFICATE CONFIRMATION\n\nCertificate: ${certDisplay}\nPurchase ID: ${purchaseId}\n\nThis action will:\n• Permanently delete the certificate\n• Allow the purchase to have a new certificate created\n• Cannot be undone\n\nType "DELETE" to confirm:`)) {
      return;
    }

    // Enhanced confirmation
    const userInput = prompt(`To confirm deletion of certificate ${certDisplay}, type "DELETE" (case sensitive):`);
    if (userInput !== 'DELETE') {
      alert('❌ Deletion cancelled - confirmation text did not match');
      return;
    }

    // Add this purchase to processing set
    setProcessingPurchases(prev => new Set(prev).add(purchaseId));

    try {
      // Delete the certificate from database
      const { error } = await supabase
        .from('certificates')
        .delete()
        .eq('id', certificateId);

      if (error) {
        console.error('Error deleting certificate:', error);
        alert(`❌ Error deleting certificate: ${error.message}`);
      } else {
        alert(`✅ Certificate ${certDisplay} deleted successfully!\n\nThe purchase can now have a new certificate created.`);
        await loadData(); // Reload data to update UI
      }
    } catch (error) {
      console.error('Error deleting certificate:', error);
      alert('❌ Error deleting certificate');
    } finally {
      // Remove this purchase from processing set
      setProcessingPurchases(prev => {
        const newSet = new Set(prev);
        newSet.delete(purchaseId);
        return newSet;
      });
    }
  };

  const confirmCertificate = async (certificateId: string) => {
    try {
      const adminClient = getServiceRoleClient();
      const { error } = await adminClient
        .from('certificates')
        .update({
          status: 'issued',
          confirmed_at: new Date().toISOString(),
          confirmed_by: 'admin'
        })
        .eq('id', certificateId);

      if (error) {
        console.error('Error confirming certificate:', error);
        alert('Error confirming certificate');
      } else {
        alert('✅ Certificate confirmed successfully');
        await loadData(); // Refresh data
      }
    } catch (error) {
      console.error('Error confirming certificate:', error);
      alert('Error confirming certificate');
    }
  };

  const rejectCertificate = async (certificateId: string) => {
    const reason = prompt('Please enter rejection reason (optional):');

    if (!confirm('⚠️ Are you sure you want to reject this certificate?\n\nThis will:\n• Delete the certificate\n• Allow the purchase to be recreated\n• Cannot be undone')) {
      return;
    }

    try {
      const adminClient = getServiceRoleClient();

      // Get certificate details before deletion
      const { data: certificate } = await adminClient
        .from('certificates')
        .select('purchase_id, user_id, shares_count, cert_no')
        .eq('id', certificateId)
        .single();

      if (!certificate) {
        alert('Certificate not found');
        return;
      }

      // Delete the certificate (this makes the purchase available for re-creation)
      const { error } = await adminClient
        .from('certificates')
        .delete()
        .eq('id', certificateId);

      if (error) {
        console.error('Error rejecting certificate:', error);
        alert('Error rejecting certificate');
      } else {
        // Log the rejection
        console.log(`🚫 Certificate ${certificate.cert_no} rejected for purchase ${certificate.purchase_id}. Reason: ${reason || 'No reason provided'}`);
        alert('🚫 Certificate rejected successfully.\n\nThe purchase is now available for re-creation.');
        await loadData(); // Refresh data
      }
    } catch (error) {
      console.error('Error rejecting certificate:', error);
      alert('Error rejecting certificate');
    }
  };

  const bulkDeleteCertificates = async () => {
    if (selectedCertificates.size === 0) {
      alert('Please select certificates to delete');
      return;
    }

    const certificateCount = selectedCertificates.size;
    if (!confirm(`⚠️ BULK DELETE CONFIRMATION\n\nYou are about to delete ${certificateCount} certificate(s).\n\nThis action will:\n• Permanently delete all selected certificates\n• Allow those purchases to have new certificates created\n• Cannot be undone\n\nAre you sure you want to continue?`)) {
      return;
    }

    // Enhanced confirmation for bulk delete
    const userInput = prompt(`To confirm bulk deletion of ${certificateCount} certificates, type "DELETE ALL" (case sensitive):`);
    if (userInput !== 'DELETE ALL') {
      alert('❌ Bulk deletion cancelled - confirmation text did not match');
      return;
    }

    try {
      setCreating(true);
      let successCount = 0;
      let errorCount = 0;

      for (const certificateId of selectedCertificates) {
        const { error } = await supabase
          .from('certificates')
          .delete()
          .eq('id', certificateId);

        if (error) {
          console.error(`Error deleting certificate ${certificateId}:`, error);
          errorCount++;
        } else {
          successCount++;
        }
      }

      alert(`Bulk deletion completed!\n✅ Deleted: ${successCount}\n❌ Errors: ${errorCount}`);
      setSelectedCertificates(new Set());
      await loadData();
    } catch (error) {
      console.error('Error in bulk deletion:', error);
      alert('❌ Error in bulk certificate deletion');
    } finally {
      setCreating(false);
    }
  };

  const createBatchCertificates = async () => {
    if (selectedPurchases.size === 0) {
      alert('Please select purchases to create certificates for');
      return;
    }

    try {
      setCreating(true);
      let successCount = 0;
      let errorCount = 0;

      for (const purchaseId of selectedPurchases) {
        const purchase = pendingPurchases.find(p => p.id === purchaseId);
        if (!purchase) continue;

        const { data, error } = await supabase.rpc('admin_create_certificate_dual', {
          p_user_id: purchase.user_id,
          p_purchase_id: purchase.id,
          p_shares_count: purchase.shares_purchased,
          p_certificate_data: {
            package_name: purchase.package_name,
            total_amount: purchase.total_amount,
            created_by: 'admin_batch'
          }
        });

        if (error) {
          console.error(`Error creating certificate for purchase ${purchaseId}:`, error);
          errorCount++;
        } else {
          successCount++;
        }
      }

      alert(`Batch creation complete: ${successCount} certificates created, ${errorCount} errors`);
      setSelectedPurchases(new Set());
      await loadData();
    } catch (error) {
      console.error('Error in batch creation:', error);
      alert('Error in batch certificate creation');
    } finally {
      setCreating(false);
    }
  };

  const togglePurchaseSelection = (purchaseId: string) => {
    const newSelection = new Set(selectedPurchases);
    if (newSelection.has(purchaseId)) {
      newSelection.delete(purchaseId);
    } else {
      newSelection.add(purchaseId);
    }
    setSelectedPurchases(newSelection);
  };

  const selectAllPurchases = () => {
    if (selectedPurchases.size === pendingPurchases.length) {
      setSelectedPurchases(new Set());
    } else {
      setSelectedPurchases(new Set(pendingPurchases.map(p => p.id)));
    }
  };

  const toggleCertificateSelection = (certificateId: string) => {
    const newSelection = new Set(selectedCertificates);
    if (newSelection.has(certificateId)) {
      newSelection.delete(certificateId);
    } else {
      newSelection.add(certificateId);
    }
    setSelectedCertificates(newSelection);
  };

  const selectAllCertificates = () => {
    if (selectedCertificates.size === certificates.length) {
      setSelectedCertificates(new Set());
    } else {
      setSelectedCertificates(new Set(certificates.map(c => c.id)));
    }
  };

  const downloadCertificate = async (certificate: Certificate) => {
    try {
      const fileUrl = certificate.certificate_data?.file_url;
      if (!fileUrl) {
        alert('Certificate file not found');
        return;
      }

      // Fetch the certificate file
      const response = await fetch(fileUrl);
      if (!response.ok) {
        throw new Error('Failed to fetch certificate');
      }

      const blob = await response.blob();
      const url = URL.createObjectURL(blob);

      // Create download link
      const link = document.createElement('a');
      link.href = url;
      link.download = `${certificate.cert_no || certificate.certificate_number}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading certificate:', error);
      alert('Failed to download certificate');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-white">Loading certificate data...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Stats */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-white">📜 Certificate Management</h2>
          <p className="text-gray-400">Generate and manage professional share certificates</p>
        </div>

        {/* Certificate Stats */}
        <div className="grid grid-cols-4 gap-4 text-center">
          <div className="bg-gray-800 rounded-lg p-3 border border-gray-700">
            <p className="text-2xl font-bold text-blue-400">{stats.total}</p>
            <p className="text-xs text-gray-400">Total Certificates</p>
          </div>
          <div className="bg-gray-800 rounded-lg p-3 border border-gray-700">
            <p className="text-2xl font-bold text-green-400">{stats.issued}</p>
            <p className="text-xs text-gray-400">Issued</p>
          </div>
          <div className="bg-gray-800 rounded-lg p-3 border border-gray-700">
            <p className="text-2xl font-bold text-yellow-400">{stats.totalShares.toLocaleString()}</p>
            <p className="text-xs text-gray-400">Total Shares</p>
          </div>
          <div className="bg-gray-800 rounded-lg p-3 border border-gray-700">
            <p className="text-2xl font-bold text-purple-400">${stats.totalValue.toLocaleString()}</p>
            <p className="text-xs text-gray-400">Total Value</p>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-800 rounded-lg p-1">
        <button
          onClick={() => setActiveTab('list')}
          className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'list'
              ? 'bg-blue-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-gray-700'
          }`}
        >
          📋 Certificate List
        </button>
        <button
          onClick={() => setActiveTab('create')}
          className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'create'
              ? 'bg-blue-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-gray-700'
          }`}
        >
          ⚡ Instant Generator
        </button>
      </div>

      {/* Tab Content */}
      {activeTab === 'create' ? (
        <AdminCertificateCreator />
      ) : (
        <div className="space-y-6">
        {/* Enhanced Statistics Dashboard */}
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-900/30 rounded-lg p-4 border border-blue-700">
            <div className="text-2xl font-bold text-blue-400">{certificates.length}</div>
            <div className="text-blue-200">📜 Total Certificates</div>
            <div className="text-xs text-blue-300 mt-1">Issued & Active</div>
          </div>
          <div className="bg-orange-900/30 rounded-lg p-4 border border-orange-700">
            <div className="text-2xl font-bold text-orange-400">{pendingPurchases.length}</div>
            <div className="text-orange-200">⏳ Pending Certificates</div>
            <div className="text-xs text-orange-300 mt-1">Ready to Create</div>
          </div>
          <div className="bg-green-900/30 rounded-lg p-4 border border-green-700">
            <div className="text-2xl font-bold text-green-400">
              {certificates.reduce((sum, cert) => sum + cert.shares_count, 0).toLocaleString()}
            </div>
            <div className="text-green-200">📊 Total Certified Shares</div>
            <div className="text-xs text-green-300 mt-1">Across All Certificates</div>
          </div>
          <div className="bg-purple-900/30 rounded-lg p-4 border border-purple-700">
            <div className="text-2xl font-bold text-purple-400">{processingPurchases.size}</div>
            <div className="text-purple-200">⚡ Active Operations</div>
            <div className="text-xs text-purple-300 mt-1">Currently Processing</div>
          </div>
        </div>

        {/* System Status Indicator */}
        {(creating || processingPurchases.size > 0) && (
          <div className="bg-blue-900/20 border border-blue-700 rounded-lg p-4 mb-4">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-400"></div>
              <div>
                <div className="text-blue-400 font-semibold">🔄 System Processing</div>
                <div className="text-sm text-blue-300">
                  {creating && "Batch operations in progress..."}
                  {processingPurchases.size > 0 && `${processingPurchases.size} individual operation(s) running...`}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Pending Purchases - Need Certificates */}
      {pendingPurchases.length > 0 && (
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold text-white">⏳ Approved Purchases Needing Certificates</h3>
            <div className="space-x-2">
              <button
                onClick={selectAllPurchases}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                disabled={creating}
              >
                {selectedPurchases.size === pendingPurchases.length ? 'Deselect All' : 'Select All'}
              </button>
              <button
                onClick={createBatchCertificates}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
                disabled={creating || selectedPurchases.size === 0}
              >
                {creating ? 'Creating...' : `Create ${selectedPurchases.size} Certificates`}
              </button>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-700">
                  <th className="text-left p-2 text-gray-400">Select</th>
                  <th className="text-left p-2 text-gray-400">User</th>
                  <th className="text-left p-2 text-gray-400">Package</th>
                  <th className="text-left p-2 text-gray-400">Shares</th>
                  <th className="text-left p-2 text-gray-400">Amount</th>
                  <th className="text-left p-2 text-gray-400">Date</th>
                  <th className="text-left p-2 text-gray-400">Action</th>
                </tr>
              </thead>
              <tbody>
                {pendingPurchases.map((purchase) => (
                  <tr key={purchase.id} className="border-b border-gray-700/50">
                    <td className="p-2">
                      <input
                        type="checkbox"
                        checked={selectedPurchases.has(purchase.id)}
                        onChange={() => togglePurchaseSelection(purchase.id)}
                        className="rounded"
                      />
                    </td>
                    <td className="p-2 text-white">
                      <div className="flex items-center space-x-2">
                        <div>
                          <div>{purchase.user?.full_name || 'N/A'}</div>
                          <div className="text-xs text-gray-400">{purchase.user?.email}</div>
                        </div>
                        <span className="px-2 py-1 bg-orange-900/30 text-orange-400 rounded text-xs border border-orange-700">
                          📋 Needs Certificate
                        </span>
                      </div>
                    </td>
                    <td className="p-2 text-white">{purchase.package_name}</td>
                    <td className="p-2 text-white">{purchase.shares_purchased.toLocaleString()}</td>
                    <td className="p-2 text-white">${purchase.total_amount.toLocaleString()}</td>
                    <td className="p-2 text-gray-400">
                      {new Date(purchase.created_at).toLocaleDateString()}
                    </td>
                    <td className="p-2">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => createCertificate(purchase)}
                          className="px-3 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700 disabled:opacity-50"
                          disabled={processingPurchases.has(purchase.id)}
                        >
                          {processingPurchases.has(purchase.id) ? 'Creating...' : 'Create Certificate'}
                        </button>
                        <button
                          onClick={() => createPNGCertificate(purchase)}
                          className="px-3 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700 disabled:opacity-50"
                          disabled={processingPurchases.has(purchase.id)}
                          title="Generate and save PNG Certificate with KYC data"
                        >
                          {processingPurchases.has(purchase.id) ? '⏳ Creating...' : '📜 Create PNG'}
                        </button>
                        <button
                          onClick={() => previewCertificate(purchase)}
                          className="px-3 py-1 bg-purple-600 text-white rounded text-xs hover:bg-purple-700 disabled:opacity-50"
                          disabled={processingPurchases.has(purchase.id)}
                          title="Preview Certificate"
                        >
                          {processingPurchases.has(purchase.id) ? '⏳ Previewing...' : '👁️ Preview'}
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

        {/* Existing Certificates */}
        <div className="mt-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold text-white">📋 Issued Certificates</h3>
            {certificates.length > 0 && (
              <div className="space-x-2">
                <button
                  onClick={selectAllCertificates}
                  className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 disabled:opacity-50"
                  disabled={creating}
                >
                  {selectedCertificates.size === certificates.length ? 'Deselect All' : 'Select All'}
                </button>
                <button
                  onClick={bulkDeleteCertificates}
                  className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
                  disabled={creating || selectedCertificates.size === 0}
                >
                  {creating ? 'Deleting...' : `Delete ${selectedCertificates.size} Selected`}
                </button>
              </div>
            )}
          </div>

        {certificates.length === 0 ? (
          <p className="text-gray-400">No certificates issued yet.</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-700">
                  <th className="text-left p-2 text-gray-400">Select</th>
                  <th className="text-left p-2 text-gray-400">Certificate #</th>
                  <th className="text-left p-2 text-gray-400">User</th>
                  <th className="text-left p-2 text-gray-400">Shares</th>
                  <th className="text-left p-2 text-gray-400">Issue Date</th>
                  <th className="text-left p-2 text-gray-400">Status</th>
                  <th className="text-left p-2 text-gray-400">Actions</th>
                </tr>
              </thead>
              <tbody>
                {certificates.map((certificate) => (
                  <tr key={certificate.id} className="border-b border-gray-700/50">
                    <td className="p-2">
                      <input
                        type="checkbox"
                        checked={selectedCertificates.has(certificate.id)}
                        onChange={() => toggleCertificateSelection(certificate.id)}
                        className="rounded"
                      />
                    </td>
                    <td className="p-2 text-white font-mono">
                      <div className="text-sm">CERT: {certificate.cert_no || certificate.certificate_number}</div>
                      <div className="text-xs text-gray-400">REF: {certificate.ref_no || 'N/A'}</div>
                    </td>
                    <td className="p-2 text-white">
                      <div>{certificate.user?.full_name || 'N/A'}</div>
                      <div className="text-xs text-gray-400">{certificate.user?.email}</div>
                    </td>
                    <td className="p-2 text-white">{certificate.shares_count.toLocaleString()}</td>
                    <td className="p-2 text-gray-400">
                      {new Date(certificate.issue_date).toLocaleDateString()}
                    </td>
                    <td className="p-2">
                      <span className={`px-2 py-1 rounded text-xs ${
                        certificate.status === 'issued'
                          ? 'bg-green-900/30 text-green-400 border border-green-700'
                          : certificate.status === 'pending_review'
                          ? 'bg-yellow-900/30 text-yellow-400 border border-yellow-700'
                          : 'bg-gray-900/30 text-gray-400 border border-gray-700'
                      }`}>
                        {certificate.status === 'pending_review' ? 'Pending Review' : certificate.status}
                      </span>
                    </td>
                    <td className="p-2">
                      <div className="flex space-x-2">
                        {certificate.status === 'pending_review' ? (
                          <>
                            <button
                              onClick={() => confirmCertificate(certificate.id)}
                              className="px-3 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700"
                              title="Confirm Certificate"
                            >
                              ✅ Confirm
                            </button>
                            <button
                              onClick={() => rejectCertificate(certificate.id)}
                              className="px-3 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700"
                              title="Reject Certificate"
                            >
                              ❌ Reject
                            </button>
                          </>
                        ) : (
                          <>
                            {certificate.certificate_data?.file_url && (
                              <button
                                onClick={() => downloadCertificate(certificate)}
                                className="px-3 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700"
                              >
                                📥 Download
                              </button>
                            )}
                            <button
                              onClick={() => deleteCertificate(certificate.id, certificate.purchase_id, certificate.cert_no || certificate.certificate_number)}
                              disabled={processingPurchases.has(certificate.purchase_id)}
                              className="px-3 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700 disabled:opacity-50"
                              title="Delete Certificate"
                            >
                              {processingPurchases.has(certificate.purchase_id) ? '⏳ Deleting...' : '🗑️ Delete'}
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
        </div>
        </div>
      )}
    </div>
  );
};

export default CertificateManagement;
