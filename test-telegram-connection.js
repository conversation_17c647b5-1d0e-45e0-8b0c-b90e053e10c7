/**
 * TEST TELEGRAM CONNECTION FEATURE
 * 
 * This script tests the new Telegram account connection functionality:
 * 1. Send verification code API
 * 2. Verify code API  
 * 3. Telegram sync request API
 */

import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables
dotenv.config();

const BASE_URL = 'http://localhost:8002';
const TEST_USER_ID = 4; // <PERSON> Rademeyer's user ID
const TEST_EMAIL = '<EMAIL>';

async function testTelegramConnectionFlow() {
  console.log('🧪 Testing Telegram Connection Feature...\n');

  try {
    // Step 1: Test sending verification code
    console.log('📧 Step 1: Testing send verification code...');
    const sendCodeResponse = await fetch(`${BASE_URL}/api/send-verification-code`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: TEST_USER_ID,
        email: TEST_EMAIL,
        purpose: 'password_reset' // Temporarily using existing purpose for testing
      }),
    });

    const sendCodeResult = await sendCodeResponse.json();
    console.log('Send Code Response:', sendCodeResult);

    if (!sendCodeResult.success) {
      console.error('❌ Failed to send verification code');
      return;
    }

    console.log('✅ Verification code sent successfully!\n');

    // Step 2: Test verification code validation (with dummy code)
    console.log('🔍 Step 2: Testing code verification...');
    const verifyCodeResponse = await fetch(`${BASE_URL}/api/verify-code`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: TEST_USER_ID,
        email: TEST_EMAIL,
        code: '123456', // Dummy code - will fail but tests the endpoint
        purpose: 'password_reset' // Temporarily using existing purpose for testing
      }),
    });

    const verifyCodeResult = await verifyCodeResponse.json();
    console.log('Verify Code Response:', verifyCodeResult);

    // This should fail with invalid code, which is expected
    if (!verifyCodeResult.success) {
      console.log('✅ Code verification endpoint working (expected failure with dummy code)\n');
    }

    // Step 3: Test telegram sync request (this should work)
    console.log('🔗 Step 3: Testing telegram sync request...');
    const syncRequestResponse = await fetch(`${BASE_URL}/api/telegram-sync-request`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: TEST_USER_ID,
        email: TEST_EMAIL
      }),
    });

    const syncRequestResult = await syncRequestResponse.json();
    console.log('Sync Request Response:', syncRequestResult);

    if (syncRequestResult.success) {
      console.log('✅ Telegram sync request created successfully!');
      console.log('🔑 Sync Token:', syncRequestResult.syncToken?.substring(0, 8) + '...');
      console.log('⏰ Expires At:', syncRequestResult.expiresAt);
    } else {
      console.error('❌ Failed to create sync request');
    }

    console.log('\n🎉 Telegram Connection Feature Test Complete!');
    console.log('\n📋 Test Summary:');
    console.log('✅ Send verification code API - Working');
    console.log('✅ Verify code API - Working (endpoint functional)');
    console.log(syncRequestResult.success ? '✅ Telegram sync request API - Working' : '❌ Telegram sync request API - Failed');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test
testTelegramConnectionFlow();
