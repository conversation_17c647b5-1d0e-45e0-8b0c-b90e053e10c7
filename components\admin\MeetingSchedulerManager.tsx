import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';

interface Meeting {
  id: number;
  title: string;
  topic: string;
  description?: string;
  meeting_date: string;
  meeting_time: string;
  timezone: string;
  duration_minutes: number;
  max_attendees: number;
  current_attendees: number;
  meeting_url?: string;
  meeting_password?: string;
  status: 'scheduled' | 'cancelled' | 'completed' | 'in_progress';
  created_at: string;
  updated_at: string;
}

interface MeetingBooking {
  id: number;
  meeting_id: number;
  attendee_name: string;
  attendee_email: string;
  phone_number?: string;
  booking_date: string;
  status: 'confirmed' | 'cancelled' | 'attended' | 'no_show';
  confirmation_code: string;
  notes?: string;
  meetings?: {
    id: number;
    title: string;
    topic: string;
    meeting_date: string;
    meeting_time: string;
  };
}

const MeetingSchedulerManager: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'meetings' | 'bookings'>('meetings');
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [bookings, setBookings] = useState<MeetingBooking[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingMeeting, setEditingMeeting] = useState<Meeting | null>(null);
  const [selectedMeetingId, setSelectedMeetingId] = useState<number | null>(null);

  // Form state for creating/editing meetings
  const [formData, setFormData] = useState({
    title: '',
    topic: '',
    description: '',
    meeting_date: '',
    meeting_time: '',
    timezone: 'SAST',
    duration_minutes: 60,
    max_attendees: 100,
    meeting_url: '',
    meeting_password: ''
  });

  useEffect(() => {
    loadMeetings();
  }, []);

  useEffect(() => {
    if (activeTab === 'bookings') {
      loadBookings();
    }
  }, [activeTab, selectedMeetingId]);

  const loadMeetings = async () => {
    setLoading(true);
    setError(null);

    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('Not authenticated');
      }

      const response = await fetch('/api/admin/meetings', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to load meetings');
      }

      const data = await response.json();
      setMeetings(data.meetings || []);
    } catch (err) {
      console.error('Error loading meetings:', err);
      setError(err instanceof Error ? err.message : 'Failed to load meetings');
    } finally {
      setLoading(false);
    }
  };

  const loadBookings = async () => {
    setLoading(true);
    setError(null);

    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('Not authenticated');
      }

      const url = selectedMeetingId 
        ? `/api/admin/meetings/bookings?meetingId=${selectedMeetingId}`
        : '/api/admin/meetings/bookings';

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to load bookings');
      }

      const data = await response.json();
      setBookings(data.bookings || []);
    } catch (err) {
      console.error('Error loading bookings:', err);
      setError(err instanceof Error ? err.message : 'Failed to load bookings');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateMeeting = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('Not authenticated');
      }

      const response = await fetch('/api/admin/meetings', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: formData.title,
          topic: formData.topic,
          description: formData.description,
          meetingDate: formData.meeting_date,
          meetingTime: formData.meeting_time,
          timezone: formData.timezone,
          durationMinutes: formData.duration_minutes,
          maxAttendees: formData.max_attendees,
          meetingUrl: formData.meeting_url,
          meetingPassword: formData.meeting_password
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create meeting');
      }

      setShowCreateModal(false);
      resetForm();
      await loadMeetings();
    } catch (err) {
      console.error('Error creating meeting:', err);
      setError(err instanceof Error ? err.message : 'Failed to create meeting');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateMeeting = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingMeeting) return;

    setLoading(true);
    setError(null);

    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('Not authenticated');
      }

      const response = await fetch('/api/admin/meetings', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          id: editingMeeting.id,
          title: formData.title,
          topic: formData.topic,
          description: formData.description,
          meeting_date: formData.meeting_date,
          meeting_time: formData.meeting_time,
          timezone: formData.timezone,
          duration_minutes: formData.duration_minutes,
          max_attendees: formData.max_attendees,
          meeting_url: formData.meeting_url,
          meeting_password: formData.meeting_password
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update meeting');
      }

      setEditingMeeting(null);
      resetForm();
      await loadMeetings();
    } catch (err) {
      console.error('Error updating meeting:', err);
      setError(err instanceof Error ? err.message : 'Failed to update meeting');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteMeeting = async (meetingId: number) => {
    if (!confirm('Are you sure you want to delete this meeting? This will also delete all bookings.')) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`/api/admin/meetings?id=${meetingId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete meeting');
      }

      await loadMeetings();
    } catch (err) {
      console.error('Error deleting meeting:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete meeting');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      topic: '',
      description: '',
      meeting_date: '',
      meeting_time: '',
      timezone: 'SAST',
      duration_minutes: 60,
      max_attendees: 100,
      meeting_url: '',
      meeting_password: ''
    });
  };

  const startEdit = (meeting: Meeting) => {
    setEditingMeeting(meeting);
    setFormData({
      title: meeting.title,
      topic: meeting.topic,
      description: meeting.description || '',
      meeting_date: meeting.meeting_date,
      meeting_time: meeting.meeting_time,
      timezone: meeting.timezone,
      duration_minutes: meeting.duration_minutes,
      max_attendees: meeting.max_attendees,
      meeting_url: meeting.meeting_url || '',
      meeting_password: meeting.meeting_password || ''
    });
  };

  const formatDateTime = (date: string, time: string) => {
    return new Date(`${date}T${time}`).toLocaleString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return '#4CAF50';
      case 'cancelled': return '#f44336';
      case 'completed': return '#2196F3';
      case 'in_progress': return '#FF9800';
      case 'confirmed': return '#4CAF50';
      case 'attended': return '#2196F3';
      case 'no_show': return '#f44336';
      default: return '#666';
    }
  };

  return (
    <div style={{ padding: '20px', backgroundColor: '#121212', minHeight: '100vh', color: '#fff' }}>
      <div style={{ marginBottom: '30px' }}>
        <h1 style={{ color: '#FFD700', marginBottom: '10px' }}>Meeting Scheduler Management</h1>
        <p style={{ color: '#ccc' }}>Manage meetings and view bookings for the community meetings system</p>
      </div>

      {error && (
        <div style={{
          backgroundColor: '#f44336',
          color: '#fff',
          padding: '12px',
          borderRadius: '8px',
          marginBottom: '20px'
        }}>
          {error}
        </div>
      )}

      {/* Tab Navigation */}
      <div style={{ marginBottom: '30px' }}>
        <div style={{ display: 'flex', gap: '10px', borderBottom: '1px solid #333' }}>
          <button
            onClick={() => setActiveTab('meetings')}
            style={{
              padding: '12px 24px',
              backgroundColor: activeTab === 'meetings' ? '#FFD700' : 'transparent',
              color: activeTab === 'meetings' ? '#000' : '#fff',
              border: 'none',
              borderRadius: '8px 8px 0 0',
              cursor: 'pointer',
              fontWeight: 'bold'
            }}
          >
            📅 Meetings ({meetings.length})
          </button>
          <button
            onClick={() => setActiveTab('bookings')}
            style={{
              padding: '12px 24px',
              backgroundColor: activeTab === 'bookings' ? '#FFD700' : 'transparent',
              color: activeTab === 'bookings' ? '#000' : '#fff',
              border: 'none',
              borderRadius: '8px 8px 0 0',
              cursor: 'pointer',
              fontWeight: 'bold'
            }}
          >
            👥 Bookings ({bookings.length})
          </button>
        </div>
      </div>

      {/* Meetings Tab */}
      {activeTab === 'meetings' && (
        <div>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
            <h2 style={{ color: '#FFD700' }}>Scheduled Meetings</h2>
            <button
              onClick={() => setShowCreateModal(true)}
              style={{
                padding: '12px 24px',
                backgroundColor: '#4CAF50',
                color: '#fff',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontWeight: 'bold'
              }}
            >
              ➕ Create New Meeting
            </button>
          </div>

          {loading ? (
            <div style={{ textAlign: 'center', padding: '40px', color: '#ccc' }}>
              Loading meetings...
            </div>
          ) : meetings.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '40px', color: '#ccc' }}>
              No meetings found. Create your first meeting to get started.
            </div>
          ) : (
            <div style={{ display: 'grid', gap: '20px' }}>
              {meetings.map((meeting) => (
                <div
                  key={meeting.id}
                  style={{
                    backgroundColor: '#1E1E1E',
                    border: '1px solid #333',
                    borderRadius: '12px',
                    padding: '20px'
                  }}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '15px' }}>
                    <div>
                      <h3 style={{ color: '#FFD700', marginBottom: '5px' }}>{meeting.title}</h3>
                      <p style={{ color: '#ccc', marginBottom: '10px' }}>{meeting.topic}</p>
                      <div style={{ display: 'flex', gap: '20px', fontSize: '14px', color: '#aaa' }}>
                        <span>📅 {formatDateTime(meeting.meeting_date, meeting.meeting_time)}</span>
                        <span>⏱️ {meeting.duration_minutes} min</span>
                        <span>👥 {meeting.current_attendees}/{meeting.max_attendees}</span>
                        <span style={{ color: getStatusColor(meeting.status) }}>
                          ● {meeting.status.toUpperCase()}
                        </span>
                      </div>
                    </div>
                    <div style={{ display: 'flex', gap: '10px' }}>
                      <button
                        onClick={() => startEdit(meeting)}
                        style={{
                          padding: '8px 16px',
                          backgroundColor: '#2196F3',
                          color: '#fff',
                          border: 'none',
                          borderRadius: '6px',
                          cursor: 'pointer',
                          fontSize: '12px'
                        }}
                      >
                        ✏️ Edit
                      </button>
                      <button
                        onClick={() => {
                          setSelectedMeetingId(meeting.id);
                          setActiveTab('bookings');
                        }}
                        style={{
                          padding: '8px 16px',
                          backgroundColor: '#FF9800',
                          color: '#fff',
                          border: 'none',
                          borderRadius: '6px',
                          cursor: 'pointer',
                          fontSize: '12px'
                        }}
                      >
                        👥 View Bookings
                      </button>
                      <button
                        onClick={() => handleDeleteMeeting(meeting.id)}
                        style={{
                          padding: '8px 16px',
                          backgroundColor: '#f44336',
                          color: '#fff',
                          border: 'none',
                          borderRadius: '6px',
                          cursor: 'pointer',
                          fontSize: '12px'
                        }}
                      >
                        🗑️ Delete
                      </button>
                    </div>
                  </div>
                  {meeting.description && (
                    <p style={{ color: '#ccc', fontSize: '14px', marginBottom: '10px' }}>
                      {meeting.description}
                    </p>
                  )}
                  {meeting.meeting_url && (
                    <div style={{ fontSize: '14px', color: '#aaa' }}>
                      🔗 <a href={meeting.meeting_url} target="_blank" rel="noopener noreferrer" style={{ color: '#4CAF50' }}>
                        Meeting Link
                      </a>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Bookings Tab */}
      {activeTab === 'bookings' && (
        <div>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
            <h2 style={{ color: '#FFD700' }}>Meeting Bookings</h2>
            <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
              <select
                value={selectedMeetingId || ''}
                onChange={(e) => setSelectedMeetingId(e.target.value ? parseInt(e.target.value) : null)}
                style={{
                  padding: '8px 12px',
                  backgroundColor: '#1E1E1E',
                  color: '#fff',
                  border: '1px solid #333',
                  borderRadius: '6px'
                }}
              >
                <option value="">All Meetings</option>
                {meetings.map((meeting) => (
                  <option key={meeting.id} value={meeting.id}>
                    {meeting.title} - {meeting.meeting_date}
                  </option>
                ))}
              </select>
              <button
                onClick={loadBookings}
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#2196F3',
                  color: '#fff',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer'
                }}
              >
                🔄 Refresh
              </button>
            </div>
          </div>

          {loading ? (
            <div style={{ textAlign: 'center', padding: '40px', color: '#ccc' }}>
              Loading bookings...
            </div>
          ) : bookings.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '40px', color: '#ccc' }}>
              No bookings found for the selected criteria.
            </div>
          ) : (
            <div style={{ display: 'grid', gap: '15px' }}>
              {bookings.map((booking) => (
                <div
                  key={booking.id}
                  style={{
                    backgroundColor: '#1E1E1E',
                    border: '1px solid #333',
                    borderRadius: '12px',
                    padding: '15px'
                  }}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <div>
                      <h4 style={{ color: '#FFD700', marginBottom: '5px' }}>
                        {booking.attendee_name}
                      </h4>
                      <p style={{ color: '#ccc', fontSize: '14px', marginBottom: '5px' }}>
                        📧 {booking.attendee_email}
                      </p>
                      {booking.phone_number && (
                        <p style={{ color: '#ccc', fontSize: '14px', marginBottom: '5px' }}>
                          📱 {booking.phone_number}
                        </p>
                      )}
                      <div style={{ display: 'flex', gap: '15px', fontSize: '12px', color: '#aaa', marginBottom: '10px' }}>
                        <span>🎫 {booking.confirmation_code}</span>
                        <span>📅 {new Date(booking.booking_date).toLocaleDateString()}</span>
                        <span style={{ color: getStatusColor(booking.status) }}>
                          ● {booking.status.toUpperCase()}
                        </span>
                      </div>
                      {booking.meetings && (
                        <p style={{ color: '#aaa', fontSize: '12px' }}>
                          Meeting: {booking.meetings.title} - {formatDateTime(booking.meetings.meeting_date, booking.meetings.meeting_time)}
                        </p>
                      )}
                      {booking.notes && (
                        <p style={{ color: '#ccc', fontSize: '14px', marginTop: '10px', fontStyle: 'italic' }}>
                          📝 {booking.notes}
                        </p>
                      )}
                    </div>
                    <div style={{ display: 'flex', gap: '5px', flexDirection: 'column' }}>
                      <select
                        value={booking.status}
                        onChange={async (e) => {
                          try {
                            const { data: { session } } = await supabase.auth.getSession();
                            if (!session) return;

                            const response = await fetch('/api/admin/meetings/bookings', {
                              method: 'PUT',
                              headers: {
                                'Authorization': `Bearer ${session.access_token}`,
                                'Content-Type': 'application/json'
                              },
                              body: JSON.stringify({
                                id: booking.id,
                                status: e.target.value
                              })
                            });

                            if (response.ok) {
                              await loadBookings();
                            }
                          } catch (err) {
                            console.error('Error updating booking status:', err);
                          }
                        }}
                        style={{
                          padding: '4px 8px',
                          backgroundColor: '#1E1E1E',
                          color: '#fff',
                          border: '1px solid #333',
                          borderRadius: '4px',
                          fontSize: '12px'
                        }}
                      >
                        <option value="confirmed">Confirmed</option>
                        <option value="cancelled">Cancelled</option>
                        <option value="attended">Attended</option>
                        <option value="no_show">No Show</option>
                      </select>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Create/Edit Meeting Modal */}
      {(showCreateModal || editingMeeting) && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: '#1E1E1E',
            border: '1px solid #333',
            borderRadius: '12px',
            padding: '30px',
            width: '90%',
            maxWidth: '600px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <h2 style={{ color: '#FFD700', marginBottom: '20px' }}>
              {editingMeeting ? 'Edit Meeting' : 'Create New Meeting'}
            </h2>

            <form onSubmit={editingMeeting ? handleUpdateMeeting : handleCreateMeeting}>
              <div style={{ display: 'grid', gap: '15px' }}>
                <div>
                  <label style={{ display: 'block', color: '#ccc', marginBottom: '5px' }}>
                    Title *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    required
                    style={{
                      width: '100%',
                      padding: '10px',
                      backgroundColor: '#121212',
                      color: '#fff',
                      border: '1px solid #333',
                      borderRadius: '6px'
                    }}
                  />
                </div>

                <div>
                  <label style={{ display: 'block', color: '#ccc', marginBottom: '5px' }}>
                    Topic *
                  </label>
                  <input
                    type="text"
                    value={formData.topic}
                    onChange={(e) => setFormData({ ...formData, topic: e.target.value })}
                    required
                    style={{
                      width: '100%',
                      padding: '10px',
                      backgroundColor: '#121212',
                      color: '#fff',
                      border: '1px solid #333',
                      borderRadius: '6px'
                    }}
                  />
                </div>

                <div>
                  <label style={{ display: 'block', color: '#ccc', marginBottom: '5px' }}>
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    rows={3}
                    style={{
                      width: '100%',
                      padding: '10px',
                      backgroundColor: '#121212',
                      color: '#fff',
                      border: '1px solid #333',
                      borderRadius: '6px',
                      resize: 'vertical'
                    }}
                  />
                </div>

                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', color: '#ccc', marginBottom: '5px' }}>
                      Date *
                    </label>
                    <input
                      type="date"
                      value={formData.meeting_date}
                      onChange={(e) => setFormData({ ...formData, meeting_date: e.target.value })}
                      required
                      min={new Date().toISOString().split('T')[0]}
                      style={{
                        width: '100%',
                        padding: '10px',
                        backgroundColor: '#121212',
                        color: '#fff',
                        border: '1px solid #333',
                        borderRadius: '6px'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', color: '#ccc', marginBottom: '5px' }}>
                      Time *
                    </label>
                    <input
                      type="time"
                      value={formData.meeting_time}
                      onChange={(e) => setFormData({ ...formData, meeting_time: e.target.value })}
                      required
                      style={{
                        width: '100%',
                        padding: '10px',
                        backgroundColor: '#121212',
                        color: '#fff',
                        border: '1px solid #333',
                        borderRadius: '6px'
                      }}
                    />
                  </div>
                </div>

                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', color: '#ccc', marginBottom: '5px' }}>
                      Duration (minutes)
                    </label>
                    <input
                      type="number"
                      value={formData.duration_minutes}
                      onChange={(e) => setFormData({ ...formData, duration_minutes: parseInt(e.target.value) || 60 })}
                      min="15"
                      max="480"
                      style={{
                        width: '100%',
                        padding: '10px',
                        backgroundColor: '#121212',
                        color: '#fff',
                        border: '1px solid #333',
                        borderRadius: '6px'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', color: '#ccc', marginBottom: '5px' }}>
                      Max Attendees *
                    </label>
                    <input
                      type="number"
                      value={formData.max_attendees}
                      onChange={(e) => setFormData({ ...formData, max_attendees: parseInt(e.target.value) || 100 })}
                      min="1"
                      max="1000"
                      required
                      style={{
                        width: '100%',
                        padding: '10px',
                        backgroundColor: '#121212',
                        color: '#fff',
                        border: '1px solid #333',
                        borderRadius: '6px'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', color: '#ccc', marginBottom: '5px' }}>
                      Timezone
                    </label>
                    <select
                      value={formData.timezone}
                      onChange={(e) => setFormData({ ...formData, timezone: e.target.value })}
                      style={{
                        width: '100%',
                        padding: '10px',
                        backgroundColor: '#121212',
                        color: '#fff',
                        border: '1px solid #333',
                        borderRadius: '6px'
                      }}
                    >
                      <option value="SAST">SAST (South Africa)</option>
                      <option value="UTC">UTC</option>
                      <option value="EST">EST</option>
                      <option value="PST">PST</option>
                      <option value="GMT">GMT</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label style={{ display: 'block', color: '#ccc', marginBottom: '5px' }}>
                    Meeting URL (Zoom, Teams, etc.)
                  </label>
                  <input
                    type="url"
                    value={formData.meeting_url}
                    onChange={(e) => setFormData({ ...formData, meeting_url: e.target.value })}
                    placeholder="https://zoom.us/j/..."
                    style={{
                      width: '100%',
                      padding: '10px',
                      backgroundColor: '#121212',
                      color: '#fff',
                      border: '1px solid #333',
                      borderRadius: '6px'
                    }}
                  />
                </div>

                <div>
                  <label style={{ display: 'block', color: '#ccc', marginBottom: '5px' }}>
                    Meeting Password
                  </label>
                  <input
                    type="text"
                    value={formData.meeting_password}
                    onChange={(e) => setFormData({ ...formData, meeting_password: e.target.value })}
                    style={{
                      width: '100%',
                      padding: '10px',
                      backgroundColor: '#121212',
                      color: '#fff',
                      border: '1px solid #333',
                      borderRadius: '6px'
                    }}
                  />
                </div>
              </div>

              <div style={{ display: 'flex', gap: '10px', marginTop: '30px', justifyContent: 'flex-end' }}>
                <button
                  type="button"
                  onClick={() => {
                    setShowCreateModal(false);
                    setEditingMeeting(null);
                    resetForm();
                  }}
                  style={{
                    padding: '12px 24px',
                    backgroundColor: '#666',
                    color: '#fff',
                    border: 'none',
                    borderRadius: '6px',
                    cursor: 'pointer'
                  }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  style={{
                    padding: '12px 24px',
                    backgroundColor: '#4CAF50',
                    color: '#fff',
                    border: 'none',
                    borderRadius: '6px',
                    cursor: loading ? 'not-allowed' : 'pointer',
                    opacity: loading ? 0.7 : 1
                  }}
                >
                  {loading ? 'Saving...' : (editingMeeting ? 'Update Meeting' : 'Create Meeting')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default MeetingSchedulerManager;
