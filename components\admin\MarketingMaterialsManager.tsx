import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';

interface MarketingMaterial {
  id: string;
  title: string;
  description: string;
  file_url: string;
  file_type: string;
  file_size: number;
  language: string;
  category: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface MarketingMaterialsManagerProps {
  currentUser: any;
}

const LANGUAGES = [
  { code: 'en', name: 'English' },
  { code: 'af', name: 'Afrikaans' },
  { code: 'zu', name: 'Zulu' },
  { code: 'xh', name: 'Xhosa' },
  { code: 'fr', name: 'French' },
  { code: 'pt', name: 'Portuguese' },
  { code: 'es', name: 'Spanish' }
];

const CATEGORIES = [
  'Presentations',
  'Brochures',
  'Fact Sheets',
  'Videos',
  'Images',
  'Legal Documents',
  'Training Materials',
  'Templates'
];

export const MarketingMaterialsManager: React.FC<MarketingMaterialsManagerProps> = ({ currentUser }) => {
  const [materials, setMaterials] = useState<MarketingMaterial[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [activeTab, setActiveTab] = useState<'list' | 'upload'>('list');
  const [selectedLanguage, setSelectedLanguage] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  
  // Upload form state
  const [uploadForm, setUploadForm] = useState({
    title: '',
    description: '',
    language: 'en',
    category: 'Presentations',
    file: null as File | null
  });

  useEffect(() => {
    loadMaterials();
  }, []);

  const loadMaterials = async () => {
    try {
      setLoading(true);
      
      // First check if table exists, if not create it
      const { data: materials, error } = await supabase
        .from('marketing_materials')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        if (error.code === '42P01') {
          // Table doesn't exist, create it
          console.log('Creating marketing_materials table...');
          await createMarketingMaterialsTable();
          setMaterials([]);
        } else {
          console.error('Error loading materials:', error);
        }
      } else {
        setMaterials(materials || []);
      }
    } catch (error) {
      console.error('Error loading materials:', error);
    } finally {
      setLoading(false);
    }
  };

  const createMarketingMaterialsTable = async () => {
    try {
      // This would typically be done via SQL migration, but for demo purposes:
      console.log('Marketing materials table needs to be created via SQL migration');
      // The actual table creation should be done in Supabase SQL editor
    } catch (error) {
      console.error('Error creating table:', error);
    }
  };

  const handleFileUpload = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!uploadForm.file || !uploadForm.title.trim()) {
      alert('Please provide a title and select a file');
      return;
    }

    setUploading(true);
    
    try {
      // Upload file to Supabase Storage
      const fileExt = uploadForm.file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `marketing-materials/${fileName}`;

      // Check if bucket exists and create if needed
      const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
      
      if (!bucketsError) {
        const marketingBucket = buckets.find(bucket => bucket.name === 'marketing-materials');
        if (!marketingBucket) {
          console.log('📦 Creating marketing-materials bucket...');
          await supabase.storage.createBucket('marketing-materials', {
            public: true,
            allowedMimeTypes: [
              'application/pdf',
              'application/vnd.ms-powerpoint',
              'application/vnd.openxmlformats-officedocument.presentationml.presentation',
              'application/msword',
              'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
              'image/jpeg',
              'image/png',
              'image/webp',
              'video/mp4',
              'video/webm'
            ],
            fileSizeLimit: 50 * 1024 * 1024 // 50MB
          });
        }
      }

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('marketing-materials')
        .upload(filePath, uploadForm.file);

      if (uploadError) {
        throw new Error('Failed to upload file: ' + uploadError.message);
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('marketing-materials')
        .getPublicUrl(filePath);

      // Save material record to database
      const { data: material, error: dbError } = await supabase
        .from('marketing_materials')
        .insert({
          title: uploadForm.title.trim(),
          description: uploadForm.description.trim(),
          file_url: publicUrl,
          file_type: uploadForm.file.type,
          file_size: uploadForm.file.size,
          language: uploadForm.language,
          category: uploadForm.category,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (dbError) {
        throw new Error('Failed to save material record: ' + dbError.message);
      }

      // Reset form and refresh list
      setUploadForm({
        title: '',
        description: '',
        language: 'en',
        category: 'Presentations',
        file: null
      });
      
      setActiveTab('list');
      await loadMaterials();
      
      alert('Material uploaded successfully!');
    } catch (error) {
      console.error('Upload error:', error);
      alert('Failed to upload material: ' + (error as Error).message);
    } finally {
      setUploading(false);
    }
  };

  const toggleMaterialStatus = async (id: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('marketing_materials')
        .update({ 
          is_active: !currentStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) {
        throw new Error(error.message);
      }

      await loadMaterials();
    } catch (error) {
      console.error('Error updating material status:', error);
      alert('Failed to update material status');
    }
  };

  const deleteMaterial = async (id: string, fileUrl: string) => {
    if (!confirm('Are you sure you want to delete this material?')) {
      return;
    }

    try {
      // Delete from database
      const { error: dbError } = await supabase
        .from('marketing_materials')
        .delete()
        .eq('id', id);

      if (dbError) {
        throw new Error(dbError.message);
      }

      // Delete file from storage
      const filePath = fileUrl.split('/').pop();
      if (filePath) {
        await supabase.storage
          .from('marketing-materials')
          .remove([`marketing-materials/${filePath}`]);
      }

      await loadMaterials();
      alert('Material deleted successfully');
    } catch (error) {
      console.error('Error deleting material:', error);
      alert('Failed to delete material');
    }
  };

  const filteredMaterials = materials.filter(material => {
    if (selectedLanguage && material.language !== selectedLanguage) return false;
    if (selectedCategory && material.category !== selectedCategory) return false;
    return true;
  });

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">📁 Marketing Materials Manager</h2>
        <div className="flex space-x-2">
          <button
            onClick={() => setActiveTab('list')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === 'list'
                ? 'bg-amber-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            📋 Materials List
          </button>
          <button
            onClick={() => setActiveTab('upload')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === 'upload'
                ? 'bg-amber-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            📤 Upload Material
          </button>
        </div>
      </div>

      {activeTab === 'list' && (
        <div className="space-y-4">
          {/* Filters */}
          <div className="flex space-x-4 mb-6">
            <select
              value={selectedLanguage}
              onChange={(e) => setSelectedLanguage(e.target.value)}
              className="px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
            >
              <option value="">All Languages</option>
              {LANGUAGES.map(lang => (
                <option key={lang.code} value={lang.code}>{lang.name}</option>
              ))}
            </select>
            
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
            >
              <option value="">All Categories</option>
              {CATEGORIES.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          {/* Materials List */}
          {loading ? (
            <div className="text-center py-8">
              <div className="text-4xl mb-4">⏳</div>
              <p className="text-gray-400">Loading materials...</p>
            </div>
          ) : filteredMaterials.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-4xl mb-4">📁</div>
              <p className="text-gray-400">No materials found</p>
            </div>
          ) : (
            <div className="grid gap-4">
              {filteredMaterials.map(material => (
                <div key={material.id} className="bg-gray-800 rounded-lg border border-gray-700 p-4">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-white mb-2">{material.title}</h3>
                      <p className="text-gray-400 text-sm mb-2">{material.description}</p>
                      <div className="flex space-x-4 text-xs text-gray-500">
                        <span>📂 {material.category}</span>
                        <span>🌍 {LANGUAGES.find(l => l.code === material.language)?.name}</span>
                        <span>📊 {formatFileSize(material.file_size)}</span>
                        <span>📅 {new Date(material.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                    <div className="flex space-x-2 ml-4">
                      <a
                        href={material.file_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors"
                      >
                        📥 Download
                      </a>
                      <button
                        onClick={() => toggleMaterialStatus(material.id, material.is_active)}
                        className={`px-3 py-1 text-sm rounded transition-colors ${
                          material.is_active
                            ? 'bg-green-600 hover:bg-green-700 text-white'
                            : 'bg-gray-600 hover:bg-gray-700 text-white'
                        }`}
                      >
                        {material.is_active ? '✅ Active' : '❌ Inactive'}
                      </button>
                      <button
                        onClick={() => deleteMaterial(material.id, material.file_url)}
                        className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors"
                      >
                        🗑️ Delete
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {activeTab === 'upload' && (
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <h3 className="text-xl font-semibold text-white mb-6">📤 Upload New Material</h3>
          
          <form onSubmit={handleFileUpload} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Title *</label>
              <input
                type="text"
                value={uploadForm.title}
                onChange={(e) => setUploadForm(prev => ({ ...prev, title: e.target.value }))}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                placeholder="Enter material title"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Description</label>
              <textarea
                value={uploadForm.description}
                onChange={(e) => setUploadForm(prev => ({ ...prev, description: e.target.value }))}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                placeholder="Enter material description"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Language</label>
                <select
                  value={uploadForm.language}
                  onChange={(e) => setUploadForm(prev => ({ ...prev, language: e.target.value }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                >
                  {LANGUAGES.map(lang => (
                    <option key={lang.code} value={lang.code}>{lang.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Category</label>
                <select
                  value={uploadForm.category}
                  onChange={(e) => setUploadForm(prev => ({ ...prev, category: e.target.value }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                >
                  {CATEGORIES.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">File *</label>
              <input
                type="file"
                onChange={(e) => setUploadForm(prev => ({ ...prev, file: e.target.files?.[0] || null }))}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                accept=".pdf,.ppt,.pptx,.doc,.docx,.jpg,.jpeg,.png,.webp,.mp4,.webm"
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                Supported formats: PDF, PowerPoint, Word, Images (JPG, PNG, WebP), Videos (MP4, WebM). Max size: 50MB
              </p>
            </div>

            <button
              type="submit"
              disabled={uploading}
              className={`w-full py-3 rounded-lg font-medium transition-colors ${
                uploading
                  ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                  : 'bg-amber-600 hover:bg-amber-700 text-white'
              }`}
            >
              {uploading ? '⏳ Uploading...' : '📤 Upload Material'}
            </button>
          </form>
        </div>
      )}
    </div>
  );
};
