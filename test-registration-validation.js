/**
 * Test script to verify registration validation and email sending
 */

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'https://fgubaqoftdeefcakejwu.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA'
);

async function testDuplicateValidation() {
  console.log('\n🔍 Testing duplicate validation...');
  
  // Test with existing email
  const existingEmail = '<EMAIL>';
  const testUsername = 'testuser123';
  
  const { data: existingUsers, error: checkError } = await supabase
    .from('users')
    .select('email, username')
    .or(`email.eq.${existingEmail},username.eq.${testUsername}`);

  if (checkError) {
    console.error('❌ Error checking for duplicates:', checkError);
    return;
  }

  console.log('📊 Duplicate check results:', existingUsers);

  if (existingUsers && existingUsers.length > 0) {
    const duplicateEmail = existingUsers.find(user => user.email === existingEmail);
    const duplicateUsername = existingUsers.find(user => user.username === testUsername);
    
    if (duplicateEmail) {
      console.log('✅ Duplicate email validation working - found existing email:', duplicateEmail);
    }
    
    if (duplicateUsername) {
      console.log('✅ Duplicate username validation working - found existing username:', duplicateUsername);
    }
  } else {
    console.log('ℹ️ No duplicates found for test data');
  }
}

async function testEmailAPI() {
  console.log('\n📧 Testing email API...');
  
  try {
    const response = await fetch('http://localhost:8003/api/send-verification-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        code: '123456',
        purpose: 'registration',
        userName: 'Test User'
      }),
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error('❌ API Error:', responseData);
    } else {
      console.log('✅ Email API working:', responseData);
    }
  } catch (error) {
    console.error('❌ Email API test failed:', error.message);
  }
}

async function testProxyAPI() {
  console.log('\n🔗 Testing proxy API (via port 8001)...');
  
  try {
    const response = await fetch('http://localhost:8001/api/send-verification-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        code: '654321',
        purpose: 'registration',
        userName: 'Proxy Test User'
      }),
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error('❌ Proxy API Error:', responseData);
    } else {
      console.log('✅ Proxy API working:', responseData);
    }
  } catch (error) {
    console.error('❌ Proxy API test failed:', error.message);
  }
}

async function runTests() {
  console.log('🧪 Starting registration validation tests...');
  
  await testDuplicateValidation();
  await testEmailAPI();
  await testProxyAPI();
  
  console.log('\n✅ All tests completed!');
}

runTests().catch(console.error);
