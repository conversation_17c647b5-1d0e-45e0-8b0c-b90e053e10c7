import React, { useState, useEffect, useRef } from 'react'
import { 
  getAvailableAgents, 
  createSupportTicket, 
  createChatSession, 
  sendChatMessage, 
  getChatMessages,
  getCurrentDbUserId,
  getUserType,
  type SupportAgent,
  type ChatMessage,
  type ChatSession,
  type SupportTicket
} from '../../lib/supportSystem'

interface LiveChatWidgetProps {
  isOpen: boolean
  onToggle: () => void
  position?: 'bottom-right' | 'bottom-left'
}

const LiveChatWidget: React.FC<LiveChatWidgetProps> = ({ 
  isOpen, 
  onToggle, 
  position = 'bottom-right' 
}) => {
  const [loading, setLoading] = useState(false)
  const [availableAgents, setAvailableAgents] = useState<SupportAgent[]>([])
  const [currentTicket, setCurrentTicket] = useState<SupportTicket | null>(null)
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null)
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [userType, setUserType] = useState<'shareholder' | 'affiliate'>('shareholder')
  const [userId, setUserId] = useState<number | null>(null)
  const [chatStep, setChatStep] = useState<'initial' | 'ticket_form' | 'chatting'>('initial')
  const [ticketTitle, setTicketTitle] = useState('')
  const [ticketDescription, setTicketDescription] = useState('')
  const [ticketCategory, setTicketCategory] = useState('')
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (isOpen) {
      initializeChat()
    }
  }, [isOpen])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const initializeChat = async () => {
    setLoading(true)
    try {
      const currentUserId = await getCurrentDbUserId()
      if (!currentUserId) {
        console.error('User not logged in')
        return
      }

      setUserId(currentUserId)
      const type = await getUserType(currentUserId)
      setUserType(type)

      // Check for available agents
      const agents = await getAvailableAgents(type)
      setAvailableAgents(agents)
    } catch (error) {
      console.error('Error initializing chat:', error)
    } finally {
      setLoading(false)
    }
  }

  const startNewTicket = async () => {
    if (!userId || !ticketTitle.trim()) return

    setLoading(true)
    try {
      const ticket = await createSupportTicket(
        userId,
        ticketTitle,
        ticketDescription,
        ticketCategory || 'general',
        'medium'
      )

      if (ticket) {
        setCurrentTicket(ticket)
        
        // Create chat session
        const session = await createChatSession(ticket.id, userId, ticket.assigned_agent_id)
        if (session) {
          setCurrentSession(session)
          setChatStep('chatting')
          
          // Send initial system message
          await sendChatMessage(
            session.id,
            ticket.id,
            'system',
            0,
            `Support ticket ${ticket.ticket_number} created. ${availableAgents.length > 0 ? 'An agent will be with you shortly.' : 'No agents are currently available. Your message will be queued and an agent will respond as soon as possible.'}`,
            'system_notification'
          )
          
          loadMessages(session.id)
        }
      }
    } catch (error) {
      console.error('Error starting new ticket:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadMessages = async (sessionId: string) => {
    try {
      const chatMessages = await getChatMessages(sessionId)
      setMessages(chatMessages)
    } catch (error) {
      console.error('Error loading messages:', error)
    }
  }

  const sendMessage = async () => {
    if (!newMessage.trim() || !currentSession || !userId) return

    const messageContent = newMessage.trim()
    setNewMessage('')

    try {
      const message = await sendChatMessage(
        currentSession.id,
        currentSession.ticket_id,
        'user',
        userId,
        messageContent
      )

      if (message) {
        setMessages(prev => [...prev, message])
      }
    } catch (error) {
      console.error('Error sending message:', error)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      if (chatStep === 'chatting') {
        sendMessage()
      }
    }
  }

  const getStatusColor = () => {
    if (availableAgents.length > 0) return 'bg-green-500'
    return 'bg-red-500'
  }

  const getStatusText = () => {
    if (availableAgents.length > 0) return `${availableAgents.length} agent${availableAgents.length > 1 ? 's' : ''} available`
    return 'No agents available'
  }

  const positionClasses = position === 'bottom-right' 
    ? 'bottom-4 right-4' 
    : 'bottom-4 left-4'

  if (!isOpen) {
    return (
      <div className={`fixed ${positionClasses} z-50`}>
        <button
          onClick={onToggle}
          className="bg-yellow-600 hover:bg-yellow-700 text-white rounded-full p-4 shadow-lg transition-all duration-200 hover:scale-105"
        >
          <div className="flex items-center space-x-2">
            <div className="relative">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456L3 21l2.456-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
              </svg>
              <div className={`absolute -top-1 -right-1 w-3 h-3 ${getStatusColor()} rounded-full border-2 border-white`}></div>
            </div>
            <span className="text-sm font-medium">Live Chat</span>
          </div>
        </button>
      </div>
    )
  }

  return (
    <div className={`fixed ${positionClasses} z-50 w-96 h-[500px] bg-white rounded-lg shadow-2xl border border-gray-200 flex flex-col`}>
      {/* Header */}
      <div className="bg-yellow-600 text-white p-4 rounded-t-lg flex justify-between items-center">
        <div>
          <h3 className="font-semibold">Live Support Chat</h3>
          <div className="flex items-center space-x-2 text-sm">
            <div className={`w-2 h-2 ${getStatusColor()} rounded-full`}></div>
            <span>{getStatusText()}</span>
          </div>
          <div className="text-xs opacity-90">
            User Type: {userType === 'shareholder' ? 'Shareholder' : 'Affiliate'}
          </div>
        </div>
        <button
          onClick={onToggle}
          className="text-white hover:text-gray-200 text-xl"
        >
          ×
        </button>
      </div>

      {/* Content */}
      <div className="flex-1 flex flex-col">
        {loading ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600"></div>
          </div>
        ) : chatStep === 'initial' ? (
          <div className="flex-1 p-4 flex flex-col justify-center">
            <div className="text-center mb-6">
              <div className="text-4xl mb-2">👋</div>
              <h4 className="text-lg font-semibold text-gray-800 mb-2">Welcome to Support</h4>
              <p className="text-gray-600 text-sm">
                {availableAgents.length > 0 
                  ? 'Our support agents are ready to help you!'
                  : 'No agents are currently online, but we\'ll respond to your message as soon as possible.'
                }
              </p>
            </div>
            <button
              onClick={() => setChatStep('ticket_form')}
              className="bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
            >
              Start New Conversation
            </button>
          </div>
        ) : chatStep === 'ticket_form' ? (
          <div className="flex-1 p-4 overflow-y-auto">
            <h4 className="font-semibold text-gray-800 mb-4">Tell us how we can help</h4>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Subject *
                </label>
                <input
                  type="text"
                  value={ticketTitle}
                  onChange={(e) => setTicketTitle(e.target.value)}
                  placeholder="Brief description of your issue"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 text-gray-900 placeholder-gray-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select
                  value={ticketCategory}
                  onChange={(e) => setTicketCategory(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500"
                >
                  <option value="">Select category</option>
                  <option value="general">General Support</option>
                  <option value="technical">Technical Issue</option>
                  <option value="billing">Billing Question</option>
                  {userType === 'shareholder' && (
                    <>
                      <option value="investment">Investment Question</option>
                      <option value="portfolio">Portfolio Management</option>
                    </>
                  )}
                  {userType === 'affiliate' && (
                    <>
                      <option value="commission">Commission Question</option>
                      <option value="marketing">Marketing Support</option>
                    </>
                  )}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  value={ticketDescription}
                  onChange={(e) => setTicketDescription(e.target.value)}
                  placeholder="Please provide more details about your question or issue"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 text-gray-900 placeholder-gray-500"
                />
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setChatStep('initial')}
                  className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors"
                >
                  Back
                </button>
                <button
                  onClick={startNewTicket}
                  disabled={!ticketTitle.trim()}
                  className="flex-1 bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-lg transition-colors disabled:opacity-50"
                >
                  Start Chat
                </button>
              </div>
            </div>
          </div>
        ) : (
          <>
            {/* Messages */}
            <div className="flex-1 p-4 overflow-y-auto bg-gray-50">
              {currentTicket && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                  <div className="text-sm font-medium text-blue-800">
                    Ticket #{currentTicket.ticket_number}
                  </div>
                  <div className="text-sm text-blue-600">{currentTicket.title}</div>
                </div>
              )}
              
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`mb-3 ${
                    message.sender_type === 'user' 
                      ? 'flex justify-end' 
                      : 'flex justify-start'
                  }`}
                >
                  <div
                    className={`max-w-[80%] px-3 py-2 rounded-lg text-sm ${
                      message.sender_type === 'user'
                        ? 'bg-yellow-600 text-white'
                        : message.sender_type === 'system'
                        ? 'bg-gray-200 text-gray-700 text-center'
                        : 'bg-white text-gray-800 border border-gray-200'
                    }`}
                  >
                    <div>{message.content}</div>
                    <div className={`text-xs mt-1 ${
                      message.sender_type === 'user' ? 'text-yellow-100' : 'text-gray-500'
                    }`}>
                      {new Date(message.created_at).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>

            {/* Message Input */}
            <div className="p-4 border-t border-gray-200">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type your message..."
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500"
                />
                <button
                  onClick={sendMessage}
                  disabled={!newMessage.trim()}
                  className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                </button>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default LiveChatWidget
