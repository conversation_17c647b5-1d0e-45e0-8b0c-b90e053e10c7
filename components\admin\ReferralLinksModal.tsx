import React, { useState, useEffect } from 'react'
import { supabase, getServiceRoleClient } from '../../lib/supabase'

interface User {
  id: number
  username: string
  email: string
  full_name: string | null
  telegram_id: number | null
  created_at: string
}

interface ReferralData {
  id: number
  referral_code: string
  created_at: string
  status: string
  campaign_source?: string
}

interface ReferralLinksModalProps {
  user: User
  isOpen: boolean
  onClose: () => void
}

export const ReferralLinksModal: React.FC<ReferralLinksModalProps> = ({
  user,
  isOpen,
  onClose
}) => {
  const [referralData, setReferralData] = useState<ReferralData[]>([])
  const [loading, setLoading] = useState(true)
  const [copiedLink, setCopiedLink] = useState<string>('')

  useEffect(() => {
    if (isOpen && user) {
      loadReferralData()
    }
  }, [isOpen, user])

  const loadReferralData = async () => {
    try {
      setLoading(true)
      const serviceClient = getServiceRoleClient()

      // Get referral codes where this user is the referrer
      const { data: referrals, error } = await serviceClient
        .from('referrals')
        .select('id, referral_code, created_at, status, campaign_source')
        .eq('referrer_id', user.id)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error loading referral data:', error)
      } else {
        setReferralData(referrals || [])
      }
    } catch (err) {
      console.error('Error loading referral data:', err)
    } finally {
      setLoading(false)
    }
  }

  const generateReferralLinks = () => {
    const username = user.username || `user${user.id}`
    
    return {
      usernameBased: `https://aureus.africa/${username}`
    }
  }

  const copyToClipboard = async (text: string, linkType: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedLink(linkType)
      setTimeout(() => setCopiedLink(''), 2000)
    } catch (error) {
      console.error('Failed to copy:', error)
      // Fallback for older browsers
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      setCopiedLink(linkType)
      setTimeout(() => setCopiedLink(''), 2000)
    }
  }

  if (!isOpen) return null

  const links = generateReferralLinks()

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-gray-900 border-b border-gray-700 px-6 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-bold text-white">
                🔗 Referral Links: {user.full_name || user.username}
              </h2>
              <p className="text-gray-400 text-sm">
                User ID: {user.id} | Username: @{user.username}
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white text-2xl"
            >
              ×
            </button>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Primary Referral Links */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">📋 Primary Referral Links</h3>
            
            {/* Username-based Landing Page */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                🌐 Custom Landing Page
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="text"
                  value={links.usernameBased}
                  readOnly
                  className="flex-1 bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 text-sm font-mono"
                />
                <button
                  onClick={() => copyToClipboard(links.usernameBased, 'username')}
                  className={`px-3 py-2 rounded text-sm font-medium transition-colors ${
                    copiedLink === 'username'
                      ? 'bg-green-600 text-white'
                      : 'bg-yellow-500 text-black hover:bg-yellow-400'
                  }`}
                >
                  {copiedLink === 'username' ? '✓ Copied' : 'Copy'}
                </button>
              </div>
              <p className="text-xs text-gray-400 mt-1">
                Personalized landing page with user's profile and company information
              </p>
            </div>




          </div>

          {/* Generated Referral Codes */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">🎫 Generated Referral Codes</h3>
            
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400"></div>
                <span className="ml-3 text-gray-300">Loading referral codes...</span>
              </div>
            ) : referralData.length > 0 ? (
              <div className="space-y-3">
                {referralData.map((referral) => (
                  <div key={referral.id} className="bg-gray-700 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="font-mono text-sm text-white mb-1">
                          {referral.referral_code}
                        </div>
                        <div className="text-xs text-gray-400">
                          Created: {new Date(referral.created_at).toLocaleDateString()} | 
                          Status: <span className={`${referral.status === 'active' ? 'text-green-400' : 'text-red-400'}`}>
                            {referral.status}
                          </span>
                          {referral.campaign_source && ` | Source: ${referral.campaign_source}`}
                        </div>
                      </div>
                      <button
                        onClick={() => copyToClipboard(referral.referral_code, `code-${referral.id}`)}
                        className={`px-3 py-1 rounded text-xs font-medium transition-colors ${
                          copiedLink === `code-${referral.id}`
                            ? 'bg-green-600 text-white'
                            : 'bg-gray-600 text-white hover:bg-gray-500'
                        }`}
                      >
                        {copiedLink === `code-${referral.id}` ? '✓' : 'Copy'}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="text-gray-400 text-sm">
                  No referral codes generated yet for this user.
                </div>
              </div>
            )}
          </div>

          {/* Usage Instructions */}
          <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
            <h4 className="text-blue-400 font-medium mb-2">📖 Usage Instructions</h4>
            <ul className="text-sm text-blue-300 space-y-1">
              <li>• <strong>Custom Landing Page:</strong> Best for social media and direct sharing</li>
              <li>• <strong>Web Registration:</strong> Direct signup with automatic referral tracking</li>
              <li>• <strong>Telegram Bot:</strong> For users who prefer Telegram registration</li>
              <li>• <strong>Referral Codes:</strong> System-generated codes for tracking specific campaigns</li>
            </ul>
          </div>
        </div>

        {/* Footer */}
        <div className="sticky bottom-0 bg-gray-900 border-t border-gray-700 px-6 py-4">
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
