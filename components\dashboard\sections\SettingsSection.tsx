import React from 'react';
import { UserSettingsDashboard } from '../../UserSettingsDashboard';
import { ProfilePictureUploadEnhanced } from '../../ProfilePictureUploadEnhanced';
import { UsernameEditor } from '../../user/UsernameEditor';
import { SecurePasswordChangeForm } from '../../user/SecurePasswordChangeForm';

interface SettingsSectionProps {
  user: any;
  onRefreshData: () => Promise<void>;
}

export const SettingsSection: React.FC<SettingsSectionProps> = ({
  user,
  onRefreshData
}) => {
  const userId = user?.database_user?.id || user?.id;
  const userEmail = user?.database_user?.email || user?.email;
  const currentUsername = user?.database_user?.username || user?.username;

  if (!userId || !userEmail || !currentUsername) {
    return (
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <div className="text-center text-gray-400">
          <p>Unable to load user settings. Please refresh the page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Enhanced User Settings Dashboard */}
      <UserSettingsDashboard
        userId={userId}
        userEmail={userEmail}
        currentUsername={currentUsername}
        onRefreshData={onRefreshData}
      />

      {/* Profile Picture Section */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Profile Picture</h3>
        <div className="max-w-sm">
          <ProfilePictureUploadEnhanced
            userId={userId}
            currentImageUrl={user?.database_user?.profile_image_url || user?.profile_image_url}
            size="large"
            showSaveButton={true}
            autoSave={false}
            userInitials={user?.database_user?.full_name?.charAt(0) || user?.full_name?.charAt(0) || 'U'}
            userName={user?.database_user?.full_name || user?.full_name || 'User'}
            onImageUpdate={(newImageUrl) => {
              // Update user state to reflect new image
              if (user?.database_user) {
                user.database_user.profile_image_url = newImageUrl;
              }
              onRefreshData?.();
            }}
          />
        </div>
      </div>

      {/* KYC Verification */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4">KYC Verification</h3>
        <p className="text-gray-400 mb-4">
          Complete your KYC verification to unlock commission withdrawals and dividend payments.
        </p>

        <div className="bg-blue-900/30 rounded-lg p-4 border border-blue-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-blue-400 text-2xl">🆔</span>
              <div>
                <h4 className="text-blue-400 font-semibold">Comprehensive KYC System</h4>
                <p className="text-blue-200 text-sm">
                  Complete your identity verification with document upload and facial recognition
                </p>
              </div>
            </div>
            <button
              onClick={() => {
                window.location.href = '/kyc-certification';
              }}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm font-medium"
            >
              Start KYC Process →
            </button>
          </div>
        </div>
      </div>

      {/* Account Information */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Account Information</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Full Name
            </label>
            <div className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white">
              {user?.database_user?.full_name || user?.full_name || 'Not set'}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Email Address
            </label>
            <div className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white">
              {userEmail}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Phone Number
            </label>
            <div className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white">
              {user?.database_user?.phone || user?.phone || 'Not set'}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Country
            </label>
            <div className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white">
              {user?.database_user?.country_name || user?.country_name || 'Not set'}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Account ID
            </label>
            <div className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white font-mono text-sm">
              {userId}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Member Since
            </label>
            <div className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white">
              {new Date(user?.database_user?.created_at || user?.created_at).toLocaleDateString()}
            </div>
          </div>

          {user?.database_user?.telegram_id && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Telegram Connected
              </label>
              <div className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-green-400">
                ✅ Connected (ID: {user.database_user.telegram_id})
              </div>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Account Status
            </label>
            <div className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                user?.database_user?.is_active || user?.is_active
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}>
                {user?.database_user?.is_active || user?.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Verification Status
            </label>
            <div className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                user?.database_user?.is_verified || user?.is_verified
                  ? 'bg-green-100 text-green-800'
                  : 'bg-yellow-100 text-yellow-800'
              }`}>
                {user?.database_user?.is_verified || user?.is_verified ? 'Verified' : 'Pending'}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
