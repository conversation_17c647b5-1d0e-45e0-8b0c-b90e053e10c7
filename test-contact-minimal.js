/**
 * Minimal test for contact API - database only
 */

import axios from 'axios';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const BASE_URL = 'http://localhost:8002';

async function testMinimalContact() {
  console.log('🧪 Testing Minimal Contact API...');
  
  try {
    console.log('Making request to:', `${BASE_URL}/api/contact`);
    
    const response = await axios.post(`${BASE_URL}/api/contact`, {
      name: 'Test',
      surname: 'User', 
      email: '<EMAIL>',
      message: 'Simple test message'
    }, {
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      },
      validateStatus: () => true // Accept all status codes
    });
    
    console.log('Status:', response.status);
    console.log('Headers:', response.headers);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
    if (response.status === 500) {
      console.log('\n❌ Server Error - Check server logs for details');
    } else if (response.status === 200) {
      console.log('\n✅ Success!');
    } else {
      console.log(`\n⚠️ Unexpected status: ${response.status}`);
    }
    
  } catch (error) {
    console.error('❌ Request failed:', error.message);
    if (error.response) {
      console.log('Response status:', error.response.status);
      console.log('Response data:', error.response.data);
    }
  }
  
  console.log('\n🎯 Minimal contact test completed!');
}

testMinimalContact().catch(console.error);
