/**
 * SECURE SESSION MANAGEMENT SYSTEM
 * 
 * This module provides comprehensive session security including
 * proper expiration, invalidation, and concurrent session limits.
 */

import { supabase } from './supabase';
import { createClient } from '@supabase/supabase-js';

interface SessionData {
  userId: number;
  userEmail: string;
  sessionId: string;
  createdAt: Date;
  lastActivity: Date;
  ipAddress: string;
  userAgent: string;
  isActive: boolean;
}

interface SessionConfig {
  maxAge: number; // 24 hours in milliseconds
  maxConcurrentSessions: number; // 3 sessions per user
  inactivityTimeout: number; // 2 hours in milliseconds
  renewalThreshold: number; // Renew if less than 4 hours remaining
}

class SessionSecurityManager {
  private config: SessionConfig;
  private activeSessions: Map<string, SessionData> = new Map();

  constructor(config: Partial<SessionConfig> = {}) {
    this.config = {
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      maxConcurrentSessions: 3, // 3 sessions per user
      inactivityTimeout: 2 * 60 * 60 * 1000, // 2 hours
      renewalThreshold: 4 * 60 * 60 * 1000, // 4 hours
      ...config
    };

    // Clean up expired sessions every 30 minutes
    setInterval(() => this.cleanupExpiredSessions(), 30 * 60 * 1000);
  }

  /**
   * Create a new secure session
   */
  async createSession(
    userId: number,
    userEmail: string,
    ipAddress: string,
    userAgent: string
  ): Promise<{ success: boolean; sessionId?: string; error?: string }> {
    try {
      console.log(`🔐 Creating session for user ${userId}`);

      // Check concurrent session limit
      const userSessions = await this.getUserSessions(userId);
      if (userSessions.length >= this.config.maxConcurrentSessions) {
        // Remove oldest session
        const oldestSession = userSessions.sort((a, b) => 
          new Date(a.lastActivity).getTime() - new Date(b.lastActivity).getTime()
        )[0];
        
        await this.invalidateSession(oldestSession.sessionId);
        console.log(`🗑️ Removed oldest session for user ${userId}`);
      }

      // Generate secure session ID
      const sessionId = await this.generateSecureSessionId();
      const now = new Date();

      const sessionData: SessionData = {
        userId,
        userEmail,
        sessionId,
        createdAt: now,
        lastActivity: now,
        ipAddress,
        userAgent,
        isActive: true
      };

      // Store session in database
      const { error: dbError } = await supabase
        .from('user_sessions')
        .insert({
          session_id: sessionId,
          user_id: userId,
          user_email: userEmail,
          ip_address: ipAddress,
          user_agent: userAgent,
          created_at: now.toISOString(),
          last_activity: now.toISOString(),
          expires_at: new Date(now.getTime() + this.config.maxAge).toISOString(),
          is_active: true
        });

      if (dbError) {
        console.error('❌ Failed to store session in database:', dbError);
        return { success: false, error: 'Failed to create session' };
      }

      // Store in memory cache
      this.activeSessions.set(sessionId, sessionData);

      // Log session creation
      await this.logSessionEvent('SESSION_CREATED', userId, sessionId, {
        ipAddress,
        userAgent: userAgent.substring(0, 100)
      });

      console.log(`✅ Session created for user ${userId}: ${sessionId}`);
      return { success: true, sessionId };

    } catch (error) {
      console.error('❌ Session creation error:', error);
      return { success: false, error: 'Session creation failed' };
    }
  }

  /**
   * Validate and refresh session
   */
  async validateSession(sessionId: string): Promise<{
    valid: boolean;
    sessionData?: SessionData;
    needsRenewal?: boolean;
    error?: string;
  }> {
    try {
      if (!sessionId) {
        return { valid: false, error: 'No session ID provided' };
      }

      // Check memory cache first
      let sessionData = this.activeSessions.get(sessionId);

      // If not in cache, check database
      if (!sessionData) {
        const { data: dbSession, error: dbError } = await supabase
          .from('user_sessions')
          .select('*')
          .eq('session_id', sessionId)
          .eq('is_active', true)
          .single();

        if (dbError || !dbSession) {
          return { valid: false, error: 'Session not found' };
        }

        sessionData = {
          userId: dbSession.user_id,
          userEmail: dbSession.user_email,
          sessionId: dbSession.session_id,
          createdAt: new Date(dbSession.created_at),
          lastActivity: new Date(dbSession.last_activity),
          ipAddress: dbSession.ip_address,
          userAgent: dbSession.user_agent,
          isActive: dbSession.is_active
        };

        // Add to cache
        this.activeSessions.set(sessionId, sessionData);
      }

      const now = new Date();

      // Check if session expired
      const sessionAge = now.getTime() - sessionData.createdAt.getTime();
      if (sessionAge > this.config.maxAge) {
        await this.invalidateSession(sessionId);
        return { valid: false, error: 'Session expired' };
      }

      // Check inactivity timeout
      const inactivityTime = now.getTime() - sessionData.lastActivity.getTime();
      if (inactivityTime > this.config.inactivityTimeout) {
        await this.invalidateSession(sessionId);
        return { valid: false, error: 'Session inactive too long' };
      }

      // Update last activity
      sessionData.lastActivity = now;
      this.activeSessions.set(sessionId, sessionData);

      // Update database
      await supabase
        .from('user_sessions')
        .update({ last_activity: now.toISOString() })
        .eq('session_id', sessionId);

      // Check if session needs renewal
      const timeUntilExpiry = this.config.maxAge - sessionAge;
      const needsRenewal = timeUntilExpiry < this.config.renewalThreshold;

      return {
        valid: true,
        sessionData,
        needsRenewal
      };

    } catch (error) {
      console.error('❌ Session validation error:', error);
      return { valid: false, error: 'Session validation failed' };
    }
  }

  /**
   * Invalidate session (logout)
   */
  async invalidateSession(sessionId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`🔒 Invalidating session: ${sessionId}`);

      // Get session data for logging
      const sessionData = this.activeSessions.get(sessionId);

      // Remove from memory cache
      this.activeSessions.delete(sessionId);

      // Mark as inactive in database
      const { error: dbError } = await supabase
        .from('user_sessions')
        .update({
          is_active: false,
          invalidated_at: new Date().toISOString()
        })
        .eq('session_id', sessionId);

      if (dbError) {
        console.error('❌ Failed to invalidate session in database:', dbError);
        return { success: false, error: 'Failed to invalidate session' };
      }

      // Log session invalidation
      if (sessionData) {
        await this.logSessionEvent('SESSION_INVALIDATED', sessionData.userId, sessionId, {
          reason: 'manual_logout'
        });
      }

      console.log(`✅ Session invalidated: ${sessionId}`);
      return { success: true };

    } catch (error) {
      console.error('❌ Session invalidation error:', error);
      return { success: false, error: 'Session invalidation failed' };
    }
  }

  /**
   * Invalidate all sessions for a user
   */
  async invalidateAllUserSessions(userId: number): Promise<{ success: boolean; count: number }> {
    try {
      console.log(`🔒 Invalidating all sessions for user ${userId}`);

      // Get all user sessions
      const userSessions = await this.getUserSessions(userId);

      // Remove from memory cache
      for (const session of userSessions) {
        this.activeSessions.delete(session.sessionId);
      }

      // Mark all as inactive in database
      const { error: dbError } = await supabase
        .from('user_sessions')
        .update({
          is_active: false,
          invalidated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .eq('is_active', true);

      if (dbError) {
        console.error('❌ Failed to invalidate user sessions:', dbError);
        return { success: false, count: 0 };
      }

      // Log bulk invalidation
      await this.logSessionEvent('ALL_SESSIONS_INVALIDATED', userId, 'bulk', {
        sessionCount: userSessions.length,
        reason: 'security_action'
      });

      console.log(`✅ Invalidated ${userSessions.length} sessions for user ${userId}`);
      return { success: true, count: userSessions.length };

    } catch (error) {
      console.error('❌ Bulk session invalidation error:', error);
      return { success: false, count: 0 };
    }
  }

  /**
   * Get all active sessions for a user
   */
  private async getUserSessions(userId: number): Promise<SessionData[]> {
    try {
      const { data: sessions, error } = await supabase
        .from('user_sessions')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('last_activity', { ascending: false });

      if (error) {
        console.error('❌ Failed to get user sessions:', error);
        return [];
      }

      return sessions.map(session => ({
        userId: session.user_id,
        userEmail: session.user_email,
        sessionId: session.session_id,
        createdAt: new Date(session.created_at),
        lastActivity: new Date(session.last_activity),
        ipAddress: session.ip_address,
        userAgent: session.user_agent,
        isActive: session.is_active
      }));

    } catch (error) {
      console.error('❌ Get user sessions error:', error);
      return [];
    }
  }

  /**
   * Generate cryptographically secure session ID
   */
  private async generateSecureSessionId(): Promise<string> {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Clean up expired sessions
   */
  private async cleanupExpiredSessions(): Promise<void> {
    try {
      const now = new Date();
      const expiredTime = new Date(now.getTime() - this.config.maxAge);

      // Clean up memory cache
      let cleanedFromCache = 0;
      for (const [sessionId, sessionData] of this.activeSessions.entries()) {
        if (sessionData.createdAt < expiredTime) {
          this.activeSessions.delete(sessionId);
          cleanedFromCache++;
        }
      }

      // Clean up database
      const { error } = await supabase
        .from('user_sessions')
        .update({
          is_active: false,
          invalidated_at: now.toISOString()
        })
        .lt('created_at', expiredTime.toISOString())
        .eq('is_active', true);

      if (error) {
        console.error('❌ Failed to cleanup expired sessions:', error);
      } else {
        console.log(`🧹 Cleaned up expired sessions: ${cleanedFromCache} from cache`);
      }

    } catch (error) {
      console.error('❌ Session cleanup error:', error);
    }
  }

  /**
   * Log session events
   */
  private async logSessionEvent(
    eventType: string,
    userId: number,
    sessionId: string,
    metadata: any
  ): Promise<void> {
    try {
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_email: 'session_system',
          action: eventType,
          target_type: 'user_session',
          target_id: userId.toString(),
          metadata: {
            ...metadata,
            sessionId,
            timestamp: new Date().toISOString()
          },
          created_at: new Date().toISOString()
        });

    } catch (error) {
      console.error('❌ Failed to log session event:', error);
    }
  }

  /**
   * Get session statistics
   */
  async getSessionStats(): Promise<any> {
    try {
      const { data: stats, error } = await supabase
        .from('user_sessions')
        .select('user_id, is_active, created_at')
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

      if (error) {
        throw error;
      }

      const activeSessions = stats.filter(s => s.is_active).length;
      const totalSessions = stats.length;
      const uniqueUsers = new Set(stats.map(s => s.user_id)).size;

      return {
        activeSessions,
        totalSessions,
        uniqueUsers,
        cacheSize: this.activeSessions.size,
        averageSessionsPerUser: uniqueUsers > 0 ? (activeSessions / uniqueUsers).toFixed(2) : 0
      };

    } catch (error) {
      console.error('❌ Failed to get session stats:', error);
      return null;
    }
  }
}

// Create singleton instance
export const sessionSecurity = new SessionSecurityManager();

/**
 * Session middleware for Express/Next.js
 */
export const sessionMiddleware = async (req: any, res: any, next: any) => {
  try {
    const sessionId = req.cookies?.sessionId || req.headers['x-session-id'];

    if (!sessionId) {
      req.session = null;
      return next();
    }

    const validation = await sessionSecurity.validateSession(sessionId);

    if (validation.valid && validation.sessionData) {
      req.session = validation.sessionData;
      req.sessionNeedsRenewal = validation.needsRenewal;

      // Add session renewal header if needed
      if (validation.needsRenewal) {
        res.setHeader('X-Session-Renewal-Needed', 'true');
      }
    } else {
      req.session = null;
      // Clear invalid session cookie
      res.clearCookie('sessionId');
    }

    next();

  } catch (error) {
    console.error('❌ Session middleware error:', error);
    req.session = null;
    next();
  }
};

export default sessionSecurity;
