#!/usr/bin/env node

/**
 * EMAIL DELIVERY DIAGNOSTIC TOOL
 * 
 * This script helps diagnose email delivery issues with Resend service
 * and provides actionable steps to fix delivery problems.
 */

import { Resend } from 'resend';
import dns from 'dns';
import { promisify } from 'util';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const resolveMx = promisify(dns.resolveMx);
const resolveTxt = promisify(dns.resolveTxt);

const RESEND_API_KEY = process.env.RESEND_API_KEY;
const DOMAIN = 'aureus.africa';

if (!RESEND_API_KEY) {
  console.error('❌ RESEND_API_KEY not found in environment variables');
  process.exit(1);
}

const resend = new Resend(RESEND_API_KEY);

async function diagnoseDomainConfiguration() {
  console.log('🔍 EMAIL DELIVERY DIAGNOSTIC REPORT');
  console.log('=====================================\n');
  
  console.log(`📧 Analyzing domain: ${DOMAIN}\n`);
  
  // Step 1: Check MX Records
  console.log('1️⃣ CHECKING MX RECORDS');
  console.log('----------------------');
  try {
    const mxRecords = await resolveMx(DOMAIN);
    if (mxRecords && mxRecords.length > 0) {
      console.log('✅ MX records found:');
      mxRecords.forEach((record, index) => {
        console.log(`   ${index + 1}. ${record.exchange} (priority: ${record.priority})`);
      });
    } else {
      console.log('❌ No MX records found - this will cause email delivery failures');
    }
  } catch (error) {
    console.log('❌ Error checking MX records:', error.message);
  }
  console.log('');
  
  // Step 2: Check SPF Records
  console.log('2️⃣ CHECKING SPF RECORDS');
  console.log('-----------------------');
  try {
    const txtRecords = await resolveTxt(DOMAIN);
    const spfRecords = txtRecords.filter(record => 
      record.some(txt => txt.startsWith('v=spf1'))
    );
    
    if (spfRecords.length > 0) {
      console.log('✅ SPF records found:');
      spfRecords.forEach((record, index) => {
        console.log(`   ${index + 1}. ${record.join('')}`);
      });
      
      // Check if Resend is included
      const hasResendSpf = spfRecords.some(record => 
        record.some(txt => txt.includes('resend.com') || txt.includes('include:_spf.resend.com'))
      );
      
      if (hasResendSpf) {
        console.log('✅ Resend SPF record found');
      } else {
        console.log('⚠️ Resend SPF record NOT found - this may cause delivery issues');
        console.log('   Add this to your DNS: include:_spf.resend.com');
      }
    } else {
      console.log('❌ No SPF records found - this will cause email delivery failures');
      console.log('   Add this TXT record: "v=spf1 include:_spf.resend.com ~all"');
    }
  } catch (error) {
    console.log('❌ Error checking SPF records:', error.message);
  }
  console.log('');
  
  // Step 3: Check DKIM Records
  console.log('3️⃣ CHECKING DKIM RECORDS');
  console.log('------------------------');
  try {
    // Common DKIM selectors for Resend
    const dkimSelectors = ['resend', 'default', 'mail'];
    let dkimFound = false;
    
    for (const selector of dkimSelectors) {
      try {
        const dkimDomain = `${selector}._domainkey.${DOMAIN}`;
        const dkimRecords = await resolveTxt(dkimDomain);
        if (dkimRecords && dkimRecords.length > 0) {
          console.log(`✅ DKIM record found for selector "${selector}"`);
          dkimFound = true;
        }
      } catch (e) {
        // DKIM record not found for this selector
      }
    }
    
    if (!dkimFound) {
      console.log('❌ No DKIM records found - this will cause email delivery failures');
      console.log('   You need to add DKIM records provided by Resend');
    }
  } catch (error) {
    console.log('❌ Error checking DKIM records:', error.message);
  }
  console.log('');
  
  // Step 4: Check Resend Domain Status
  console.log('4️⃣ CHECKING RESEND DOMAIN STATUS');
  console.log('--------------------------------');
  try {
    const domains = await resend.domains.list();
    console.log('📋 Raw domains response:', JSON.stringify(domains, null, 2));

    if (domains && domains.data && domains.data.data && Array.isArray(domains.data.data)) {
      const auresDomain = domains.data.data.find(d => d.name === DOMAIN);

      if (auresDomain) {
        console.log('✅ Domain found in Resend:');
        console.log(`   Name: ${auresDomain.name}`);
        console.log(`   Status: ${auresDomain.status}`);
        console.log(`   Region: ${auresDomain.region || 'N/A'}`);

        if (auresDomain.status === 'verified') {
          console.log('✅ Domain is verified');
        } else {
          console.log('❌ Domain is NOT verified - this will cause delivery failures');
        }
      } else {
        console.log('❌ Domain not found in Resend - you need to add and verify it');
        console.log('📋 Available domains:');
        domains.data.data.forEach(domain => {
          console.log(`   - ${domain.name} (${domain.status})`);
        });
      }
    } else {
      console.log('❌ Unexpected domains response format');
    }
  } catch (error) {
    console.log('❌ Error checking Resend domain status:', error.message);
    console.log('   This might indicate API access issues or domain not configured');
  }
  console.log('');
  
  // Step 5: Recommendations
  console.log('5️⃣ RECOMMENDATIONS');
  console.log('------------------');
  console.log('Based on the analysis above, here are the steps to fix email delivery:\n');
  
  console.log('🔧 IMMEDIATE ACTIONS NEEDED:');
  console.log('1. Add/verify the aureus.africa domain in your Resend dashboard');
  console.log('2. Configure DNS records as provided by Resend:');
  console.log('   - Add MX records for email receiving');
  console.log('   - Add SPF record: "v=spf1 include:_spf.resend.com ~all"');
  console.log('   - Add DKIM records provided by Resend');
  console.log('3. Wait 24-48 hours for DNS propagation');
  console.log('4. Test email delivery again\n');
  
  console.log('📋 NEXT STEPS:');
  console.log('1. Login to your Resend dashboard');
  console.log('2. Go to Domains section');
  console.log('3. Add aureus.africa domain if not already added');
  console.log('4. Copy the DNS records provided by Resend');
  console.log('5. Add these records to your domain DNS settings');
  console.log('6. Verify the domain in Resend dashboard');
}

// Run the diagnostic
diagnoseDomainConfiguration().catch(console.error);
