import React from 'react';
import { DashboardSection, useDashboardNavigation } from '../../hooks/useDashboardNavigation.tsx';
import { UserPermissions } from '../../hooks/useUserPermissions';
import { DashboardData } from '../../hooks/useDashboardData';

interface MobileBottomNavigationProps {
  activeSection: DashboardSection;
  onSectionChange: (section: DashboardSection) => void;
  permissions: UserPermissions;
  dashboardData: DashboardData;
}

export const MobileBottomNavigation: React.FC<MobileBottomNavigationProps> = ({
  activeSection,
  onSectionChange,
  permissions,
  dashboardData
}) => {
  const { shareholderNavigationItems } = useDashboardNavigation();

  // Get the most important navigation items for bottom nav (max 5)
  const primaryNavItems = shareholderNavigationItems.filter(item => {
    // Only show the most essential items in bottom nav
    const essentialItems = ['overview', 'purchase-shares', 'portfolio', 'notifications', 'settings'];
    return essentialItems.includes(item.id);
  }).filter(item => {
    // Apply permission filtering
    switch (item.id) {
      case 'purchase-shares':
        return permissions.canPurchaseShares;
      case 'portfolio':
        return permissions.canViewPortfolio;
      case 'settings':
        return permissions.canAccessSettings;
      default:
        return true;
    }
  }).slice(0, 5); // Limit to 5 items max

  const getBadgeCount = (sectionId: DashboardSection): number | undefined => {
    switch (sectionId) {
      case 'notifications':
        return dashboardData.notifications?.unread || 0;
      default:
        return undefined;
    }
  };

  return (
    <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-gray-800 border-t border-gray-700 z-30 safe-area-inset-bottom">
      <div className="flex items-center justify-around px-2 py-3">
        {primaryNavItems.map((item) => {
          const isActive = activeSection === item.id;
          const badgeCount = getBadgeCount(item.id);
          const IconComponent = item.icon;

          return (
            <button
              key={item.id}
              onClick={() => onSectionChange(item.id)}
              className={`flex flex-col items-center justify-center px-3 py-3 rounded-lg transition-all duration-200 min-w-0 flex-1 max-w-[80px] touch-manipulation ${
                isActive
                  ? 'bg-yellow-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700 active:bg-gray-600'
              }`}
              title={item.description}
              style={{ minHeight: '44px' }}
            >
              <div className="relative">
                <IconComponent className="w-6 h-6 mb-1" />
                
                {/* Badge for notifications */}
                {badgeCount !== undefined && badgeCount > 0 && (
                  <span className="absolute -top-2 -right-2 inline-flex items-center justify-center px-1.5 py-0.5 text-xs font-bold bg-red-500 text-white rounded-full min-w-[18px] h-[18px]">
                    {badgeCount > 9 ? '9+' : badgeCount}
                  </span>
                )}

                {/* New indicator */}
                {item.isNew && (
                  <span className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full"></span>
                )}
              </div>
              
              <span className={`text-xs font-medium truncate w-full text-center ${
                isActive ? 'text-white' : 'text-gray-400'
              }`}>
                {item.label}
              </span>
            </button>
          );
        })}
      </div>
    </div>
  );
};
