import React, { useState, useEffect } from 'react'
import { 
  getTrainingContent, 
  getUserTrainingProgress, 
  updateTrainingProgress,
  getCurrentDbUserId,
  getUserType,
  type TrainingContent,
  type UserTrainingProgress 
} from '../../lib/supportSystem'

interface TrainingContentManagerProps {
  userType?: 'shareholder' | 'affiliate'
  userId?: number
}

const TrainingContentManager: React.FC<TrainingContentManagerProps> = ({ 
  userType: propUserType, 
  userId: propUserId 
}) => {
  const [content, setContent] = useState<TrainingContent[]>([])
  const [progress, setProgress] = useState<UserTrainingProgress[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedContent, setSelectedContent] = useState<TrainingContent | null>(null)
  const [userType, setUserType] = useState<'shareholder' | 'affiliate'>(propUserType || 'shareholder')
  const [userId, setUserId] = useState<number | null>(propUserId || null)
  const [activeCategory, setActiveCategory] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    initializeTraining()
  }, [propUserType, propUserId])

  const initializeTraining = async () => {
    setLoading(true)
    try {
      let currentUserId = propUserId
      let currentUserType = propUserType

      if (!currentUserId) {
        currentUserId = await getCurrentDbUserId()
      }
      
      if (!currentUserType && currentUserId) {
        currentUserType = await getUserType(currentUserId)
      }

      if (!currentUserId || !currentUserType) {
        console.error('User not logged in or type not determined')
        return
      }

      setUserId(currentUserId)
      setUserType(currentUserType)

      await Promise.all([
        loadTrainingContent(currentUserType),
        loadUserProgress(currentUserId)
      ])
    } catch (error) {
      console.error('Error initializing training:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadTrainingContent = async (type: 'shareholder' | 'affiliate') => {
    try {
      const trainingContent = await getTrainingContent(type)
      setContent(trainingContent)
    } catch (error) {
      console.error('Error loading training content:', error)
    }
  }

  const loadUserProgress = async (currentUserId: number) => {
    try {
      const userProgress = await getUserTrainingProgress(currentUserId)
      setProgress(userProgress)
    } catch (error) {
      console.error('Error loading user progress:', error)
    }
  }

  const handleStartContent = async (contentItem: TrainingContent) => {
    if (!userId) return

    try {
      await updateTrainingProgress(userId, contentItem.id, 0, 0, 'in_progress')
      await loadUserProgress(userId)
      setSelectedContent(contentItem)
    } catch (error) {
      console.error('Error starting content:', error)
    }
  }

  const handleCompleteContent = async (contentItem: TrainingContent) => {
    if (!userId) return

    try {
      const existingProgress = progress.find(p => p.content_id === contentItem.id)
      const timeSpent = existingProgress?.time_spent_minutes || contentItem.duration_minutes || 30

      await updateTrainingProgress(userId, contentItem.id, 100, timeSpent, 'completed')
      await loadUserProgress(userId)
    } catch (error) {
      console.error('Error completing content:', error)
    }
  }

  const getProgressForContent = (contentId: string): UserTrainingProgress | undefined => {
    return progress.find(p => p.content_id === contentId)
  }

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800'
      case 'in_progress': return 'bg-yellow-100 text-yellow-800'
      case 'failed': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return '🎥'
      case 'document': return '📄'
      case 'presentation': return '📊'
      case 'quiz': return '❓'
      case 'webinar': return '🎓'
      default: return '📚'
    }
  }

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case 'beginner': return 'bg-green-100 text-green-800'
      case 'intermediate': return 'bg-yellow-100 text-yellow-800'
      case 'advanced': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredContent = content.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.description?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = activeCategory === 'all' || item.category_id === activeCategory
    return matchesSearch && matchesCategory
  })

  const categories = Array.from(new Set(content.map(item => item.category_id))).filter(Boolean)

  const formatDuration = (minutes?: number) => {
    if (!minutes) return 'N/A'
    if (minutes < 60) return `${minutes}m`
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return `${hours}h ${mins}m`
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            {userType === 'shareholder' ? 'Shareholder' : 'Affiliate'} Training Center
          </h2>
          <p className="text-gray-600">
            Access training materials designed specifically for {userType}s
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">
            {progress.filter(p => p.status === 'completed').length} of {content.length} completed
          </span>
          <div className="w-32 bg-gray-200 rounded-full h-2">
            <div 
              className="bg-yellow-600 h-2 rounded-full transition-all duration-300"
              style={{ 
                width: `${content.length > 0 ? (progress.filter(p => p.status === 'completed').length / content.length) * 100 : 0}%` 
              }}
            ></div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <input
            type="text"
            placeholder="Search training content..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500"
          />
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => setActiveCategory('all')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              activeCategory === 'all'
                ? 'bg-yellow-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            All Categories
          </button>
          {categories.map((categoryId) => (
            <button
              key={categoryId}
              onClick={() => setActiveCategory(categoryId)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                activeCategory === categoryId
                  ? 'bg-yellow-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Category {categoryId.slice(-4)}
            </button>
          ))}
        </div>
      </div>

      {/* Content Grid */}
      {filteredContent.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">📚</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Training Content</h3>
          <p className="text-gray-600">
            {searchTerm ? 'No content matches your search criteria.' : 'No training content available for your user type.'}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredContent.map((item) => {
            const itemProgress = getProgressForContent(item.id)
            return (
              <div
                key={item.id}
                className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow"
              >
                {/* Content Header */}
                <div className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-2xl">{getContentTypeIcon(item.content_type)}</span>
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getDifficultyColor(item.difficulty_level)}`}>
                        {item.difficulty_level}
                      </span>
                    </div>
                    {item.is_featured && (
                      <span className="bg-yellow-100 text-yellow-800 text-xs font-semibold px-2 py-1 rounded-full">
                        Featured
                      </span>
                    )}
                  </div>
                  
                  <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                    {item.title}
                  </h3>
                  
                  {item.description && (
                    <p className="text-gray-600 text-sm mb-3 line-clamp-3">
                      {item.description}
                    </p>
                  )}

                  <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
                    <span>Duration: {formatDuration(item.duration_minutes)}</span>
                    <span className="capitalize">{item.content_type}</span>
                  </div>

                  {/* Progress Bar */}
                  {itemProgress && (
                    <div className="mb-3">
                      <div className="flex justify-between text-sm text-gray-600 mb-1">
                        <span>Progress</span>
                        <span>{itemProgress.progress_percentage}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-yellow-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${itemProgress.progress_percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {/* Status Badge */}
                  {itemProgress && (
                    <div className="mb-3">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(itemProgress.status)}`}>
                        {itemProgress.status.replace('_', ' ')}
                      </span>
                    </div>
                  )}

                  {/* Tags */}
                  {item.tags && item.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mb-3">
                      {item.tags.slice(0, 3).map((tag) => (
                        <span
                          key={tag}
                          className="inline-flex px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full"
                        >
                          {tag}
                        </span>
                      ))}
                      {item.tags.length > 3 && (
                        <span className="inline-flex px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                          +{item.tags.length - 3} more
                        </span>
                      )}
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="px-4 pb-4">
                  {!itemProgress ? (
                    <button
                      onClick={() => handleStartContent(item)}
                      className="w-full bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                    >
                      Start Learning
                    </button>
                  ) : itemProgress.status === 'completed' ? (
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setSelectedContent(item)}
                        className="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                      >
                        Review
                      </button>
                      {itemProgress.certificate_issued && (
                        <button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                          Certificate
                        </button>
                      )}
                    </div>
                  ) : (
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setSelectedContent(item)}
                        className="flex-1 bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                      >
                        Continue
                      </button>
                      <button
                        onClick={() => handleCompleteContent(item)}
                        className="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                      >
                        Mark Complete
                      </button>
                    </div>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      )}

      {/* Content Detail Modal */}
      {selectedContent && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">
                    {selectedContent.title}
                  </h3>
                  <div className="flex items-center space-x-4 mt-2">
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getDifficultyColor(selectedContent.difficulty_level)}`}>
                      {selectedContent.difficulty_level}
                    </span>
                    <span className="text-sm text-gray-500">
                      {formatDuration(selectedContent.duration_minutes)}
                    </span>
                    <span className="text-sm text-gray-500 capitalize">
                      {selectedContent.content_type}
                    </span>
                  </div>
                </div>
                <button
                  onClick={() => setSelectedContent(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
            
            <div className="p-6 overflow-y-auto max-h-[70vh]">
              {selectedContent.description && (
                <div className="mb-6">
                  <h4 className="text-lg font-medium text-gray-900 mb-2">Description</h4>
                  <p className="text-gray-700">{selectedContent.description}</p>
                </div>
              )}

              {selectedContent.learning_objectives && selectedContent.learning_objectives.length > 0 && (
                <div className="mb-6">
                  <h4 className="text-lg font-medium text-gray-900 mb-2">Learning Objectives</h4>
                  <ul className="list-disc list-inside space-y-1">
                    {selectedContent.learning_objectives.map((objective, index) => (
                      <li key={index} className="text-gray-700">{objective}</li>
                    ))}
                  </ul>
                </div>
              )}

              {selectedContent.prerequisites && selectedContent.prerequisites.length > 0 && (
                <div className="mb-6">
                  <h4 className="text-lg font-medium text-gray-900 mb-2">Prerequisites</h4>
                  <ul className="list-disc list-inside space-y-1">
                    {selectedContent.prerequisites.map((prereq, index) => (
                      <li key={index} className="text-gray-700">{prereq}</li>
                    ))}
                  </ul>
                </div>
              )}

              {selectedContent.content_url && (
                <div className="mb-6">
                  <h4 className="text-lg font-medium text-gray-900 mb-2">Content</h4>
                  <div className="bg-gray-100 rounded-lg p-4">
                    <p className="text-gray-600 mb-4">Click the button below to access the training content:</p>
                    <a
                      href={selectedContent.content_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                    >
                      <span className="mr-2">{getContentTypeIcon(selectedContent.content_type)}</span>
                      Open {selectedContent.content_type}
                    </a>
                  </div>
                </div>
              )}

              {selectedContent.download_url && (
                <div className="mb-6">
                  <h4 className="text-lg font-medium text-gray-900 mb-2">Downloads</h4>
                  <a
                    href={selectedContent.download_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Download Materials
                  </a>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default TrainingContentManager
