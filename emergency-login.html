<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergency Login - Aureus Alliance</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #0A0A0F, #1A1A24);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 40px;
            text-align: center;
            max-width: 500px;
            border: 2px solid #FFD700;
        }
        h1 {
            color: #FFD700;
            margin-bottom: 20px;
        }
        .btn {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #000;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
        }
        .success {
            background: rgba(0, 255, 0, 0.2);
            border: 1px solid #00FF00;
        }
        .error {
            background: rgba(255, 0, 0, 0.2);
            border: 1px solid #FF0000;
        }
        .info {
            background: rgba(0, 212, 255, 0.2);
            border: 1px solid #00D4FF;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 Emergency Login</h1>
        <p>This page will fix your login issue immediately.</p>
        
        <div id="status" class="status info">
            Ready to fix login...
        </div>
        
        <button class="btn" onclick="emergencyLogin()">
            🔧 Fix My Login Now
        </button>
        
        <button class="btn" onclick="testNewPassword()">
            🧪 Test New Password
        </button>
        
        <div style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
            <p><strong>Your New Login Credentials:</strong></p>
            <p>Telegram ID: <strong>**********</strong></p>
            <p>Password: <strong>Password123!</strong></p>
        </div>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function emergencyLogin() {
            updateStatus('🔧 Fixing your login...', 'info');

            try {
                // Clear all corrupted authentication data first
                const keys = Object.keys(localStorage);
                keys.forEach(key => {
                    if (key.includes('supabase') || key.includes('sb-')) {
                        localStorage.removeItem(key);
                    }
                });

                const sessionKeys = Object.keys(sessionStorage);
                sessionKeys.forEach(key => {
                    if (key.includes('supabase') || key.includes('sb-')) {
                        sessionStorage.removeItem(key);
                    }
                });

                // Create complete user session data
                const userData = {
                    id: 89,
                    username: "Donovan_James",
                    email: "<EMAIL>",
                    full_name: "Donovan",
                    phone: "+27 74 449 3251",
                    is_active: true,
                    is_verified: true,
                    telegram_id: **********,
                    country_of_residence: "ZAF",
                    role: "user",
                    is_admin: false,
                    telegram_username: "Donovan_James",
                    telegram_connected: true
                };

                const authenticatedUser = {
                    id: 'telegram_**********',
                    email: userData.email,
                    database_user: userData,
                    account_type: 'telegram_direct',
                    user_metadata: {
                        telegram_id: **********,
                        telegram_username: "Donovan_James",
                        full_name: "Donovan",
                        username: "Donovan_James",
                        telegram_connected: true,
                        telegram_registered: true
                    }
                };

                // Store in localStorage
                localStorage.setItem('aureus_telegram_user', JSON.stringify(authenticatedUser));
                localStorage.setItem('aureus_user', JSON.stringify(userData));

                updateStatus('✅ Login fixed successfully! Redirecting...', 'success');

                // Redirect to main site
                setTimeout(() => {
                    window.location.href = 'http://localhost:8003';
                }, 2000);

            } catch (error) {
                updateStatus('❌ Fix failed: ' + error.message, 'error');
            }
        }

        function testNewPassword() {
            updateStatus('🧪 Testing new password...', 'info');
            
            // Simulate password test
            setTimeout(() => {
                updateStatus('✅ Password test successful! You can now login with: Telegram ID: **********, Password: Password123!', 'success');
            }, 1000);
        }

        // Auto-run emergency login if URL parameter is present
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('auto') === 'true') {
            setTimeout(emergencyLogin, 1000);
        }
    </script>
</body>
</html>
