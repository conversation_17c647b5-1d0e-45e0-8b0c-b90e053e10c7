import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function setupReportingEnhancements() {
  console.log('📊 Setting up reporting system enhancements...')

  try {
    // 1. Create report history table for tracking generated reports
    console.log('📋 Creating report_history table...')
    
    const reportHistorySQL = `
      CREATE TABLE IF NOT EXISTS report_history (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        admin_user_id VARCHAR(255) NOT NULL,
        report_type VARCHAR(50) NOT NULL DEFAULT 'share_ledger',
        filename VARCHAR(255) NOT NULL,
        filters JSONB DEFAULT '{}'::jsonb,
        transaction_count INTEGER DEFAULT 0,
        file_size_bytes BIGINT DEFAULT 0,
        generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        download_count INTEGER DEFAULT 0,
        last_downloaded_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Create indexes for better performance
      CREATE INDEX IF NOT EXISTS idx_report_history_admin_user_id ON report_history(admin_user_id);
      CREATE INDEX IF NOT EXISTS idx_report_history_generated_at ON report_history(generated_at);
      CREATE INDEX IF NOT EXISTS idx_report_history_report_type ON report_history(report_type);

      -- Add comments
      COMMENT ON TABLE report_history IS 'Tracks all generated reports for audit and re-download purposes';
      COMMENT ON COLUMN report_history.admin_user_id IS 'ID of admin who generated the report';
      COMMENT ON COLUMN report_history.filters IS 'JSON object containing the filters used for the report';
      COMMENT ON COLUMN report_history.transaction_count IS 'Number of transactions included in the report';
    `

    const { error: reportHistoryError } = await supabase.rpc('exec_sql', { sql_query: reportHistorySQL })
    
    if (reportHistoryError) {
      console.error('❌ Error creating report_history table:', reportHistoryError)
    } else {
      console.log('✅ report_history table created successfully')
    }

    // 2. Create indexes on existing tables for better reporting performance
    console.log('📊 Creating performance indexes...')
    
    const performanceIndexes = [
      // Aureus share purchases indexes
      'CREATE INDEX IF NOT EXISTS idx_aureus_share_purchases_user_created ON aureus_share_purchases(user_id, created_at);',
      'CREATE INDEX IF NOT EXISTS idx_aureus_share_purchases_status_created ON aureus_share_purchases(status, created_at);',
      'CREATE INDEX IF NOT EXISTS idx_aureus_share_purchases_created_at ON aureus_share_purchases(created_at DESC);',
      
      // Commission transactions indexes
      'CREATE INDEX IF NOT EXISTS idx_commission_transactions_referrer_created ON commission_transactions(referrer_id, created_at);',
      'CREATE INDEX IF NOT EXISTS idx_commission_transactions_status_created ON commission_transactions(status, created_at);',
      'CREATE INDEX IF NOT EXISTS idx_commission_transactions_created_at ON commission_transactions(created_at DESC);',
      
      // Commission conversions indexes
      'CREATE INDEX IF NOT EXISTS idx_commission_conversions_user_created ON commission_conversions(user_id, created_at);',
      'CREATE INDEX IF NOT EXISTS idx_commission_conversions_status_created ON commission_conversions(status, created_at);',
      'CREATE INDEX IF NOT EXISTS idx_commission_conversions_created_at ON commission_conversions(created_at DESC);',
      
      // Commission withdrawal requests indexes (additional ones)
      'CREATE INDEX IF NOT EXISTS idx_commission_withdrawal_requests_user_created ON commission_withdrawal_requests(user_id, created_at);',
      'CREATE INDEX IF NOT EXISTS idx_commission_withdrawal_requests_status_created ON commission_withdrawal_requests(status, created_at);'
    ]

    for (const indexSQL of performanceIndexes) {
      try {
        const { error } = await supabase.rpc('exec_sql', { sql_query: indexSQL })
        if (error) {
          console.error(`❌ Error creating index: ${error.message}`)
        } else {
          console.log(`✅ Index created: ${indexSQL.split(' ')[5]}`)
        }
      } catch (err) {
        console.log(`ℹ️ Index might already exist: ${indexSQL.split(' ')[5]}`)
      }
    }

    // 3. Create a view for unified transaction reporting
    console.log('🔍 Creating unified transaction view...')
    
    const unifiedViewSQL = `
      CREATE OR REPLACE VIEW unified_transaction_view AS
      -- Share Purchases
      SELECT 
        'share_purchase' as transaction_type,
        asp.id as reference_id,
        asp.user_id,
        u.username,
        u.full_name,
        u.email,
        asp.total_amount as usdt_amount,
        asp.shares_purchased as shares_amount,
        0 as commission_amount,
        asp.status,
        asp.payment_method,
        asp.package_name as description,
        asp.created_at,
        asp.updated_at
      FROM aureus_share_purchases asp
      JOIN users u ON asp.user_id = u.id
      
      UNION ALL
      
      -- Commission Withdrawals
      SELECT 
        'commission_withdrawal' as transaction_type,
        cwr.id as reference_id,
        cwr.user_id,
        u.username,
        u.full_name,
        u.email,
        -cwr.withdrawal_amount as usdt_amount, -- Negative for outflow
        0 as shares_amount,
        0 as commission_amount,
        cwr.status,
        cwr.network as payment_method,
        CONCAT('Withdrawal to ', cwr.wallet_address) as description,
        cwr.created_at,
        cwr.processed_at as updated_at
      FROM commission_withdrawal_requests cwr
      JOIN users u ON cwr.user_id = u.id
      
      UNION ALL
      
      -- Commission Conversions
      SELECT 
        'usdt_conversion' as transaction_type,
        cc.id as reference_id,
        cc.user_id,
        u.username,
        u.full_name,
        u.email,
        -cc.usdt_amount as usdt_amount, -- Negative USDT (spent)
        cc.shares_requested as shares_amount, -- Positive shares (gained)
        0 as commission_amount,
        cc.status,
        'conversion' as payment_method,
        CONCAT('Converted ', cc.usdt_amount, ' USDT to ', cc.shares_requested, ' shares') as description,
        cc.created_at,
        cc.updated_at
      FROM commission_conversions cc
      JOIN users u ON cc.user_id = u.id
      
      UNION ALL
      
      -- Commission Earnings
      SELECT 
        'commission_earned' as transaction_type,
        ct.id as reference_id,
        ct.referrer_id as user_id,
        u.username,
        u.full_name,
        u.email,
        ct.usdt_commission as usdt_amount,
        ct.share_commission as shares_amount,
        ct.usdt_commission as commission_amount,
        ct.status,
        'commission' as payment_method,
        CONCAT('Commission from referral') as description,
        ct.created_at,
        ct.payment_date as updated_at
      FROM commission_transactions ct
      JOIN users u ON ct.referrer_id = u.id;

      -- Add comment
      COMMENT ON VIEW unified_transaction_view IS 'Unified view of all transaction types for reporting purposes';
    `

    const { error: viewError } = await supabase.rpc('exec_sql', { sql_query: unifiedViewSQL })
    
    if (viewError) {
      console.error('❌ Error creating unified view:', viewError)
    } else {
      console.log('✅ Unified transaction view created successfully')
    }

    // 4. Set up RLS policies for report_history table
    console.log('🔒 Setting up RLS policies...')
    
    const rlsPolicies = [
      `
        CREATE POLICY IF NOT EXISTS "Service role can manage report history" 
        ON report_history FOR ALL 
        USING (auth.role() = 'service_role')
        WITH CHECK (auth.role() = 'service_role');
      `,
      `
        CREATE POLICY IF NOT EXISTS "Admins can manage report history" 
        ON report_history FOR ALL 
        USING (
          EXISTS (
            SELECT 1 FROM admin_users 
            WHERE email = auth.email()
          )
        )
        WITH CHECK (
          EXISTS (
            SELECT 1 FROM admin_users 
            WHERE email = auth.email()
          )
        );
      `
    ]

    for (const policy of rlsPolicies) {
      try {
        const { error } = await supabase.rpc('exec_sql', { sql_query: policy })
        if (error) {
          console.error('❌ Error creating RLS policy:', error)
        } else {
          console.log('✅ RLS policy created successfully')
        }
      } catch (err) {
        console.log('ℹ️ RLS policy might already exist')
      }
    }

    // 5. Enable RLS on report_history table
    const enableRLSSQL = 'ALTER TABLE report_history ENABLE ROW LEVEL SECURITY;'
    
    try {
      const { error } = await supabase.rpc('exec_sql', { sql_query: enableRLSSQL })
      if (error) {
        console.error('❌ Error enabling RLS:', error)
      } else {
        console.log('✅ RLS enabled on report_history table')
      }
    } catch (err) {
      console.log('ℹ️ RLS might already be enabled')
    }

    console.log('\n🎉 Reporting system enhancements completed!')
    console.log('📋 Report history tracking enabled')
    console.log('📊 Performance indexes created')
    console.log('🔍 Unified transaction view available')
    console.log('🔒 RLS policies configured')

  } catch (error) {
    console.error('❌ Setup failed:', error)
  }
}

// Run the setup
setupReportingEnhancements()
