/**
 * SECURE API MIDDLEWARE FOR SQL INJECTION PROTECTION
 * 
 * This middleware provides comprehensive protection against SQL injection
 * and other security threats for all API endpoints.
 */

import { NextRequest, NextResponse } from 'next/server';
import { detectMaliciousInput, sanitizeObject, securityCheckInput } from './inputValidation';
import { supabase, getServiceRoleClient } from './supabase';

/**
 * Security configuration for different endpoint types
 */
interface SecurityConfig {
  requireAuth: boolean;
  requireAdmin: boolean;
  validateInput: boolean;
  logRequests: boolean;
  rateLimit: boolean;
  csrfProtection: boolean;
  maxRequestSize: number; // in bytes
}

/**
 * Request security context
 */
interface SecurityContext {
  isAuthenticated: boolean;
  isAdmin: boolean;
  userId?: number;
  userEmail?: string;
  ipAddress: string;
  userAgent: string;
  endpoint: string;
  method: string;
}

/**
 * Security audit log
 */
interface SecurityEvent {
  timestamp: string;
  event_type: 'SQL_INJECTION_ATTEMPT' | 'XSS_ATTEMPT' | 'CSRF_ATTEMPT' | 'RATE_LIMIT_EXCEEDED' | 'UNAUTHORIZED_ACCESS';
  user_id?: number;
  ip_address: string;
  user_agent: string;
  endpoint: string;
  method: string;
  input_data?: string;
  patterns_detected?: string[];
  blocked: boolean;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

/**
 * Rate limiting store (in production, use Redis)
 */
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

/**
 * Log security events to database
 */
async function logSecurityEvent(event: SecurityEvent): Promise<void> {
  try {
    const serviceClient = getServiceRoleClient();
    await serviceClient
      .from('security_audit_log')
      .insert([event]);
  } catch (error) {
    console.error('Failed to log security event:', error);
  }
}

/**
 * Get client IP address
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const remoteAddr = request.headers.get('x-vercel-forwarded-for');
  
  return forwarded?.split(',')[0] || realIP || remoteAddr || 'unknown';
}

/**
 * Check rate limiting
 */
function checkRateLimit(identifier: string, maxRequests: number = 100, windowMs: number = 60000): {
  allowed: boolean;
  remaining: number;
  resetTime: number;
} {
  const now = Date.now();
  const windowStart = now - windowMs;
  
  const current = rateLimitStore.get(identifier);
  
  if (!current || current.resetTime < windowStart) {
    // Reset or initialize
    rateLimitStore.set(identifier, { count: 1, resetTime: now + windowMs });
    return { allowed: true, remaining: maxRequests - 1, resetTime: now + windowMs };
  }
  
  if (current.count >= maxRequests) {
    return { allowed: false, remaining: 0, resetTime: current.resetTime };
  }
  
  current.count++;
  return { allowed: true, remaining: maxRequests - current.count, resetTime: current.resetTime };
}

/**
 * Validate CSRF token
 */
function validateCSRFToken(request: NextRequest): boolean {
  const token = request.headers.get('x-csrf-token');
  const cookie = request.cookies.get('csrf-token')?.value;
  
  if (!token || !cookie || token !== cookie) {
    return false;
  }
  
  return true;
}

/**
 * Authenticate request
 */
async function authenticateRequest(request: NextRequest): Promise<{
  isAuthenticated: boolean;
  isAdmin: boolean;
  user?: any;
}> {
  try {
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return { isAuthenticated: false, isAdmin: false };
    }
    
    const token = authHeader.substring(7);
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      return { isAuthenticated: false, isAdmin: false };
    }
    
    // Check if user is admin
    const serviceClient = getServiceRoleClient();
    const { data: adminUser } = await serviceClient
      .from('admin_users')
      .select('*')
      .eq('email', user.email)
      .eq('is_active', true)
      .single();
    
    return {
      isAuthenticated: true,
      isAdmin: !!adminUser,
      user: { ...user, adminUser }
    };
  } catch (error) {
    console.error('Authentication error:', error);
    return { isAuthenticated: false, isAdmin: false };
  }
}

/**
 * Validate and sanitize request body
 */
function validateRequestBody(body: any, context: string): {
  isValid: boolean;
  sanitized: any;
  issues: string[];
} {
  if (!body) {
    return { isValid: true, sanitized: body, issues: [] };
  }
  
  const securityCheck = securityCheckInput(body);
  
  if (!securityCheck.safe) {
    return {
      isValid: false,
      sanitized: null,
      issues: securityCheck.issues
    };
  }
  
  return {
    isValid: true,
    sanitized: securityCheck.sanitized,
    issues: []
  };
}

/**
 * Main security middleware factory
 */
export function createSecureMiddleware(config: Partial<SecurityConfig> = {}) {
  const defaultConfig: SecurityConfig = {
    requireAuth: false,
    requireAdmin: false,
    validateInput: true,
    logRequests: true,
    rateLimit: true,
    csrfProtection: false,
    maxRequestSize: 1024 * 1024 // 1MB
  };
  
  const finalConfig = { ...defaultConfig, ...config };
  
  return async function secureMiddleware(
    request: NextRequest,
    handler: (req: NextRequest, context: SecurityContext) => Promise<NextResponse>
  ): Promise<NextResponse> {
    const startTime = Date.now();
    const ipAddress = getClientIP(request);
    const userAgent = request.headers.get('user-agent') || 'unknown';
    const endpoint = request.nextUrl.pathname;
    const method = request.method;
    
    // Create security context
    const securityContext: SecurityContext = {
      isAuthenticated: false,
      isAdmin: false,
      ipAddress,
      userAgent,
      endpoint,
      method
    };
    
    try {
      // 1. Rate limiting
      if (finalConfig.rateLimit) {
        const rateLimitKey = `${ipAddress}:${endpoint}`;
        const rateLimit = checkRateLimit(rateLimitKey);
        
        if (!rateLimit.allowed) {
          await logSecurityEvent({
            timestamp: new Date().toISOString(),
            event_type: 'RATE_LIMIT_EXCEEDED',
            ip_address: ipAddress,
            user_agent: userAgent,
            endpoint,
            method,
            blocked: true,
            severity: 'MEDIUM'
          });
          
          return NextResponse.json(
            { error: 'Rate limit exceeded' },
            { 
              status: 429,
              headers: {
                'X-RateLimit-Remaining': rateLimit.remaining.toString(),
                'X-RateLimit-Reset': rateLimit.resetTime.toString()
              }
            }
          );
        }
      }
      
      // 2. Request size validation
      const contentLength = parseInt(request.headers.get('content-length') || '0');
      if (contentLength > finalConfig.maxRequestSize) {
        return NextResponse.json(
          { error: 'Request too large' },
          { status: 413 }
        );
      }
      
      // 3. Authentication
      if (finalConfig.requireAuth || finalConfig.requireAdmin) {
        const auth = await authenticateRequest(request);
        securityContext.isAuthenticated = auth.isAuthenticated;
        securityContext.isAdmin = auth.isAdmin;
        
        if (auth.user) {
          securityContext.userId = auth.user.id;
          securityContext.userEmail = auth.user.email;
        }
        
        if (finalConfig.requireAuth && !auth.isAuthenticated) {
          await logSecurityEvent({
            timestamp: new Date().toISOString(),
            event_type: 'UNAUTHORIZED_ACCESS',
            ip_address: ipAddress,
            user_agent: userAgent,
            endpoint,
            method,
            blocked: true,
            severity: 'HIGH'
          });
          
          return NextResponse.json(
            { error: 'Authentication required' },
            { status: 401 }
          );
        }
        
        if (finalConfig.requireAdmin && !auth.isAdmin) {
          await logSecurityEvent({
            timestamp: new Date().toISOString(),
            event_type: 'UNAUTHORIZED_ACCESS',
            ip_address: ipAddress,
            user_agent: userAgent,
            endpoint,
            method,
            blocked: true,
            severity: 'HIGH'
          });
          
          return NextResponse.json(
            { error: 'Admin access required' },
            { status: 403 }
          );
        }
      }
      
      // 4. CSRF protection
      if (finalConfig.csrfProtection && ['POST', 'PUT', 'DELETE', 'PATCH'].includes(method)) {
        if (!validateCSRFToken(request)) {
          await logSecurityEvent({
            timestamp: new Date().toISOString(),
            event_type: 'CSRF_ATTEMPT',
            user_id: securityContext.userId,
            ip_address: ipAddress,
            user_agent: userAgent,
            endpoint,
            method,
            blocked: true,
            severity: 'HIGH'
          });
          
          return NextResponse.json(
            { error: 'CSRF token validation failed' },
            { status: 403 }
          );
        }
      }
      
      // 5. Input validation
      if (finalConfig.validateInput && ['POST', 'PUT', 'PATCH'].includes(method)) {
        try {
          const body = await request.json();
          const validation = validateRequestBody(body, endpoint);
          
          if (!validation.isValid) {
            await logSecurityEvent({
              timestamp: new Date().toISOString(),
              event_type: 'SQL_INJECTION_ATTEMPT',
              user_id: securityContext.userId,
              ip_address: ipAddress,
              user_agent: userAgent,
              endpoint,
              method,
              input_data: JSON.stringify(body).substring(0, 500),
              patterns_detected: validation.issues,
              blocked: true,
              severity: 'CRITICAL'
            });
            
            return NextResponse.json(
              { 
                error: 'Invalid input detected',
                details: validation.issues
              },
              { status: 400 }
            );
          }
          
          // Replace request body with sanitized version
          (request as any).sanitizedBody = validation.sanitized;
        } catch (error) {
          // If JSON parsing fails, continue without body validation
        }
      }
      
      // 6. Call the actual handler
      const response = await handler(request, securityContext);
      
      // 7. Log successful requests if configured
      if (finalConfig.logRequests) {
        const duration = Date.now() - startTime;
        console.log(`${method} ${endpoint} - ${response.status} - ${duration}ms - ${ipAddress}`);
      }
      
      return response;
      
    } catch (error) {
      console.error('Security middleware error:', error);
      
      await logSecurityEvent({
        timestamp: new Date().toISOString(),
        event_type: 'SQL_INJECTION_ATTEMPT',
        user_id: securityContext.userId,
        ip_address: ipAddress,
        user_agent: userAgent,
        endpoint,
        method,
        blocked: true,
        severity: 'CRITICAL'
      });
      
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}

/**
 * Predefined middleware configurations
 */
export const securityPresets = {
  // Public endpoints (no auth required)
  public: createSecureMiddleware({
    requireAuth: false,
    requireAdmin: false,
    validateInput: true,
    rateLimit: true,
    csrfProtection: false,
    logRequests: false
  }),
  
  // Authenticated user endpoints
  authenticated: createSecureMiddleware({
    requireAuth: true,
    requireAdmin: false,
    validateInput: true,
    rateLimit: true,
    csrfProtection: true,
    logRequests: true
  }),
  
  // Admin-only endpoints
  admin: createSecureMiddleware({
    requireAuth: true,
    requireAdmin: true,
    validateInput: true,
    rateLimit: true,
    csrfProtection: true,
    logRequests: true
  }),
  
  // Payment processing endpoints (high security)
  payment: createSecureMiddleware({
    requireAuth: true,
    requireAdmin: false,
    validateInput: true,
    rateLimit: true,
    csrfProtection: true,
    logRequests: true,
    maxRequestSize: 5 * 1024 * 1024 // 5MB for file uploads
  })
};

export default createSecureMiddleware;
