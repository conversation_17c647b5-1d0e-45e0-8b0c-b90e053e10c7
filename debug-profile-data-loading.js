#!/usr/bin/env node

/**
 * DEBUG PROFILE DATA LOADING
 * 
 * Investigates why user profile details are not showing on dashboard
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const debugProfileDataLoading = async () => {
  try {
    console.log('🔍 DEBUGGING PROFILE DATA LOADING...\n');

    const telegramId = '1393852532';

    // Step 1: Get the logged-in user's complete data
    console.log('📋 Step 1: Getting User Complete Data');
    
    const { data: telegramUser } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramId)
      .single();

    if (!telegramUser) {
      console.log('❌ Telegram user not found');
      return;
    }

    const { data: user } = await supabase
      .from('users')
      .select('*')
      .eq('id', telegramUser.user_id)
      .single();

    if (!user) {
      console.log('❌ User not found');
      return;
    }

    console.log('✅ User Data Retrieved:');
    console.log(`   ID: ${user.id}`);
    console.log(`   Username: ${user.username}`);
    console.log(`   Email: ${user.email}`);
    console.log(`   Full Name: ${user.full_name || 'NOT SET'}`);
    console.log(`   Phone: ${user.phone || 'NOT SET'}`);
    console.log(`   Address: ${user.address || 'NOT SET'}`);
    console.log(`   Country: ${user.country_of_residence || 'NOT SET'}`);
    console.log(`   Is Active: ${user.is_active}`);
    console.log(`   Is Verified: ${user.is_verified}`);
    console.log(`   Is Admin: ${user.is_admin}`);

    // Step 2: Check user's financial data
    console.log('\n📋 Step 2: Checking User Financial Data');
    
    // Check user shares
    const { data: shares, error: sharesError } = await supabase
      .from('user_shares')
      .select('*')
      .eq('user_id', user.id);

    if (sharesError) {
      console.log('❌ Error fetching shares:', sharesError.message);
    } else {
      console.log(`✅ User Shares: ${shares?.length || 0} records found`);
      if (shares && shares.length > 0) {
        const totalShares = shares.reduce((sum, share) => sum + (share.shares_owned || 0), 0);
        console.log(`   Total Shares Owned: ${totalShares}`);
      }
    }

    // Check user transactions
    const { data: transactions, error: transError } = await supabase
      .from('transactions')
      .select('*')
      .eq('user_id', user.id);

    if (transError) {
      console.log('❌ Error fetching transactions:', transError.message);
    } else {
      console.log(`✅ User Transactions: ${transactions?.length || 0} records found`);
      if (transactions && transactions.length > 0) {
        const totalAmount = transactions.reduce((sum, trans) => sum + (trans.amount || 0), 0);
        console.log(`   Total Transaction Amount: $${totalAmount}`);
      }
    }

    // Check user commissions
    const { data: commissions, error: commError } = await supabase
      .from('commissions')
      .select('*')
      .eq('user_id', user.id);

    if (commError) {
      console.log('❌ Error fetching commissions:', commError.message);
    } else {
      console.log(`✅ User Commissions: ${commissions?.length || 0} records found`);
      if (commissions && commissions.length > 0) {
        const totalCommission = commissions.reduce((sum, comm) => sum + (comm.amount || 0), 0);
        console.log(`   Total Commission: $${totalCommission}`);
      }
    }

    // Step 3: Check what data the dashboard should be showing
    console.log('\n📋 Step 3: Dashboard Data Analysis');
    
    // Check if user has any portfolio data
    const { data: portfolio, error: portfolioError } = await supabase
      .from('user_portfolio')
      .select('*')
      .eq('user_id', user.id);

    if (portfolioError) {
      console.log('⚠️ Portfolio table might not exist:', portfolioError.message);
    } else {
      console.log(`✅ User Portfolio: ${portfolio?.length || 0} records found`);
    }

    // Check user balance/wallet
    const { data: wallet, error: walletError } = await supabase
      .from('user_wallets')
      .select('*')
      .eq('user_id', user.id);

    if (walletError) {
      console.log('⚠️ Wallet table might not exist:', walletError.message);
    } else {
      console.log(`✅ User Wallet: ${wallet?.length || 0} records found`);
      if (wallet && wallet.length > 0) {
        console.log(`   Wallet Balance: $${wallet[0].balance || 0}`);
      }
    }

    // Step 4: Check what tables exist for user data
    console.log('\n📋 Step 4: Available Data Tables Check');
    
    const tablesToCheck = [
      'user_investments',
      'user_dividends', 
      'user_referrals',
      'user_kyc',
      'kyc_information',
      'user_settings',
      'user_notifications'
    ];

    for (const tableName of tablesToCheck) {
      try {
        const { data, error } = await supabase
          .from(tableName)
          .select('*')
          .eq('user_id', user.id)
          .limit(1);

        if (error) {
          console.log(`⚠️ ${tableName}: ${error.message}`);
        } else {
          console.log(`✅ ${tableName}: Available (${data?.length || 0} records)`);
        }
      } catch (err) {
        console.log(`❌ ${tableName}: Error - ${err.message}`);
      }
    }

    // Step 5: Generate sample data if needed
    console.log('\n📋 Step 5: Sample Data Generation');
    
    const sampleUserData = {
      id: user.id,
      username: user.username,
      email: user.email,
      full_name: user.full_name || 'JP Rademeyer',
      phone: user.phone || '+27 123 456 789',
      address: user.address || '123 Main Street, Cape Town',
      country_of_residence: user.country_of_residence || 'South Africa',
      total_shares: 0,
      total_investment: 0,
      total_commissions: 0,
      wallet_balance: 0,
      is_verified: user.is_verified,
      is_admin: user.is_admin,
      member_since: user.created_at
    };

    console.log('📋 Sample User Data for Dashboard:');
    console.log(JSON.stringify(sampleUserData, null, 2));

    // Step 6: Check if user session data is properly stored
    console.log('\n📋 Step 6: Session Data Check');
    
    const sessionData = {
      userId: user.id,
      telegramId: telegramUser.telegram_id,
      username: user.username,
      email: user.email,
      isAdmin: user.is_admin,
      isVerified: user.is_verified,
      fullName: user.full_name,
      phone: user.phone,
      address: user.address,
      country: user.country_of_residence
    };

    console.log('📋 Session Data that should be available:');
    console.log(JSON.stringify(sessionData, null, 2));

    console.log('\n' + '='.repeat(60));
    console.log('🎯 PROFILE DATA LOADING ANALYSIS:');
    console.log('✅ User data exists in database');
    console.log('✅ Login is working correctly');
    console.log('⚠️ Dashboard may not be fetching user-specific data');
    console.log('⚠️ May be showing default/placeholder data instead');
    console.log('\n📋 LIKELY ISSUES:');
    console.log('1. Dashboard not reading user session correctly');
    console.log('2. API calls not using correct user ID');
    console.log('3. Frontend not fetching personalized data');
    console.log('4. User data tables may be empty (new user)');
    console.log('='.repeat(60));

  } catch (error) {
    console.error('❌ Profile data debugging failed:', error);
  }
};

debugProfileDataLoading();
