import React, { useState, useEffect } from 'react'
import { supabase } from '../../lib/supabase'
import { logAdminAction } from '../../lib/adminAuth'

interface FundMetrics {
  totalRevenue: number
  totalShares: number
  totalUsers: number
  averageSharePrice: number
  totalCommissionsPaid: number
  pendingPayments: number
  cashFlow: number
  profitMargin: number
}

interface PhaseData {
  id: number
  phase_number: number
  phase_name: string
  price_per_share: number
  total_shares_available: number
  shares_sold: number
  revenue: number
  is_active: boolean
}

interface Transaction {
  id: string
  type: 'purchase' | 'commission' | 'withdrawal'
  amount: number
  user_id: number
  created_at: string
  status: string
  description: string
}

export const FundManagementDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<FundMetrics>({
    totalRevenue: 0,
    totalShares: 0,
    totalUsers: 0,
    averageSharePrice: 0,
    totalCommissionsPaid: 0,
    pendingPayments: 0,
    cashFlow: 0,
    profitMargin: 0
  })
  const [phases, setPhases] = useState<PhaseData[]>([])
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'phases' | 'transactions'>('overview')

  useEffect(() => {
    loadFundData()
  }, [])

  const loadFundData = async () => {
    setLoading(true)
    try {
      await Promise.all([
        loadMetrics(),
        loadPhases(),
        loadTransactions()
      ])
    } catch (error) {
      console.error('Error loading fund data:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadMetrics = async () => {
    try {
      // Get total revenue from share purchases
      const { data: purchases, error: purchaseError } = await supabase
        .from('aureus_share_purchases')
        .select('total_amount, shares_purchased')
        .eq('status', 'active')

      if (purchaseError) throw purchaseError

      // Get total users
      const { count: userCount, error: userError } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true })

      if (userError) throw userError

      // Get commission data
      const { data: commissions, error: commissionError } = await supabase
        .from('commission_transactions')
        .select('usdt_commission')
        .eq('status', 'approved')

      if (commissionError) throw commissionError

      // Get pending payments
      const { count: pendingCount, error: pendingError } = await supabase
        .from('crypto_payment_transactions')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'pending')

      if (pendingError) throw pendingError

      // Calculate metrics
      const totalRevenue = purchases?.reduce((sum, p) => sum + p.total_amount, 0) || 0
      const totalShares = purchases?.reduce((sum, p) => sum + p.shares_purchased, 0) || 0
      const totalCommissionsPaid = commissions?.reduce((sum, c) => sum + (c.usdt_commission || 0), 0) || 0
      const averageSharePrice = totalShares > 0 ? totalRevenue / totalShares : 0
      const cashFlow = totalRevenue - totalCommissionsPaid
      const profitMargin = totalRevenue > 0 ? ((totalRevenue - totalCommissionsPaid) / totalRevenue) * 100 : 0

      setMetrics({
        totalRevenue,
        totalShares,
        totalUsers: userCount || 0,
        averageSharePrice,
        totalCommissionsPaid,
        pendingPayments: pendingCount || 0,
        cashFlow,
        profitMargin
      })

    } catch (error) {
      console.error('Error loading metrics:', error)
    }
  }

  const loadPhases = async () => {
    try {
      const { data, error } = await supabase
        .from('investment_phases')
        .select('*')
        .order('phase_number')

      if (error) throw error

      // Calculate revenue for each phase
      const phasesWithRevenue = await Promise.all(
        (data || []).map(async (phase) => {
          const { data: purchases, error: purchaseError } = await supabase
            .from('aureus_share_purchases')
            .select('total_amount')
            .eq('phase_id', phase.id)
            .eq('status', 'active')

          if (purchaseError) {
            console.error('Error loading phase purchases:', purchaseError)
            return { ...phase, revenue: 0 }
          }

          const revenue = purchases?.reduce((sum, p) => sum + p.total_amount, 0) || 0
          return { ...phase, revenue }
        })
      )

      setPhases(phasesWithRevenue)
    } catch (error) {
      console.error('Error loading phases:', error)
    }
  }

  const loadTransactions = async () => {
    try {
      // Get recent share purchases
      const { data: purchases, error: purchaseError } = await supabase
        .from('aureus_share_purchases')
        .select('id, user_id, total_amount, created_at, status')
        .order('created_at', { ascending: false })
        .limit(50)

      if (purchaseError) throw purchaseError

      // Get recent commission transactions
      const { data: commissions, error: commissionError } = await supabase
        .from('commission_transactions')
        .select('id, referrer_id, usdt_commission, created_at, status')
        .order('created_at', { ascending: false })
        .limit(50)

      if (commissionError) throw commissionError

      // Combine and format transactions
      const purchaseTransactions: Transaction[] = (purchases || []).map(p => ({
        id: p.id,
        type: 'purchase' as const,
        amount: p.total_amount,
        user_id: p.user_id,
        created_at: p.created_at,
        status: p.status,
        description: `Share purchase - $${p.total_amount.toFixed(2)}`
      }))

      const commissionTransactions: Transaction[] = (commissions || []).map(c => ({
        id: c.id,
        type: 'commission' as const,
        amount: c.usdt_commission || 0,
        user_id: c.referrer_id,
        created_at: c.created_at,
        status: c.status,
        description: `Commission payment - $${(c.usdt_commission || 0).toFixed(2)}`
      }))

      const allTransactions = [...purchaseTransactions, ...commissionTransactions]
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, 100)

      setTransactions(allTransactions)
    } catch (error) {
      console.error('Error loading transactions:', error)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(2)}%`
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-white">Loading fund data...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">Fund Management Dashboard</h2>
        <div className="flex space-x-4">
          <button
            onClick={() => setActiveTab('overview')}
            className={`px-4 py-2 rounded-lg ${
              activeTab === 'overview'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            Overview
          </button>
          <button
            onClick={() => setActiveTab('phases')}
            className={`px-4 py-2 rounded-lg ${
              activeTab === 'phases'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            Phases
          </button>
          <button
            onClick={() => setActiveTab('transactions')}
            className={`px-4 py-2 rounded-lg ${
              activeTab === 'transactions'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            Transactions
          </button>
        </div>
      </div>

      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
              <h3 className="text-lg font-semibold text-white mb-2">Total Revenue</h3>
              <p className="text-3xl font-bold text-green-400">{formatCurrency(metrics.totalRevenue)}</p>
            </div>
            <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
              <h3 className="text-lg font-semibold text-white mb-2">Total Shares Sold</h3>
              <p className="text-3xl font-bold text-blue-400">{metrics.totalShares.toLocaleString()}</p>
            </div>
            <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
              <h3 className="text-lg font-semibold text-white mb-2">Total Users</h3>
              <p className="text-3xl font-bold text-purple-400">{metrics.totalUsers.toLocaleString()}</p>
            </div>
            <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
              <h3 className="text-lg font-semibold text-white mb-2">Avg Share Price</h3>
              <p className="text-3xl font-bold text-yellow-400">{formatCurrency(metrics.averageSharePrice)}</p>
            </div>
          </div>

          {/* Financial Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
              <h3 className="text-lg font-semibold text-white mb-2">Commissions Paid</h3>
              <p className="text-3xl font-bold text-red-400">{formatCurrency(metrics.totalCommissionsPaid)}</p>
            </div>
            <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
              <h3 className="text-lg font-semibold text-white mb-2">Net Cash Flow</h3>
              <p className="text-3xl font-bold text-green-400">{formatCurrency(metrics.cashFlow)}</p>
            </div>
            <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
              <h3 className="text-lg font-semibold text-white mb-2">Profit Margin</h3>
              <p className="text-3xl font-bold text-blue-400">{formatPercentage(metrics.profitMargin)}</p>
            </div>
            <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
              <h3 className="text-lg font-semibold text-white mb-2">Pending Payments</h3>
              <p className="text-3xl font-bold text-orange-400">{metrics.pendingPayments}</p>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'phases' && (
        <div className="space-y-6">
          <h3 className="text-xl font-semibold text-white">Investment Phases</h3>

          <div className="bg-gray-800 rounded-lg border border-gray-600 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Phase
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Price per Share
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Shares Available
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Shares Sold
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Progress
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Revenue
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-600">
                  {phases.map((phase) => {
                    const progress = phase.total_shares_available > 0
                      ? (phase.shares_sold / phase.total_shares_available) * 100
                      : 0

                    return (
                      <tr key={phase.id} className="hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-white">Phase {phase.phase_number}</div>
                            <div className="text-sm text-gray-400">{phase.phase_name}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          {formatCurrency(phase.price_per_share)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          {phase.total_shares_available.toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          {phase.shares_sold.toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="w-full bg-gray-600 rounded-full h-2 mr-2">
                              <div
                                className="bg-blue-600 h-2 rounded-full"
                                style={{ width: `${Math.min(progress, 100)}%` }}
                              ></div>
                            </div>
                            <span className="text-sm text-gray-300">{progress.toFixed(1)}%</span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          {formatCurrency(phase.revenue)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            phase.is_active
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {phase.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'transactions' && (
        <div className="space-y-6">
          <h3 className="text-xl font-semibold text-white">Recent Transactions</h3>

          <div className="bg-gray-800 rounded-lg border border-gray-600 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      User ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Description
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Date
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-600">
                  {transactions.map((transaction) => (
                    <tr key={`${transaction.type}-${transaction.id}`} className="hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          transaction.type === 'purchase'
                            ? 'bg-green-100 text-green-800'
                            : transaction.type === 'commission'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {transaction.type}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {formatCurrency(transaction.amount)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {transaction.user_id}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {transaction.description}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          transaction.status === 'approved'
                            ? 'bg-green-100 text-green-800'
                            : transaction.status === 'rejected'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {transaction.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {new Date(transaction.created_at).toLocaleDateString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
