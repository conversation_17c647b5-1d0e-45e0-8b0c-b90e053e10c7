#!/usr/bin/env node

/**
 * Check Database Schema
 * 
 * This script checks the actual column names in the database tables
 * to fix the 400 errors caused by non-existent columns.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ Missing SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function checkDatabaseSchema() {
  console.log('🔍 Checking Database Schema\n');

  try {
    // Check users table structure
    console.log('1. Checking users table structure...');
    const { data: usersData, error: usersError } = await supabase
      .from('users')
      .select('*')
      .eq('id', 4)
      .single();

    if (usersError) {
      console.error('❌ Error fetching users data:', usersError);
    } else {
      console.log('✅ Users table columns:');
      Object.keys(usersData).forEach(column => {
        console.log(`   • ${column}: ${typeof usersData[column]} = ${usersData[column]}`);
      });
    }

    // Check commission_balances table structure
    console.log('\n2. Checking commission_balances table structure...');
    const { data: commissionData, error: commissionError } = await supabase
      .from('commission_balances')
      .select('*')
      .eq('user_id', 4)
      .single();

    if (commissionError) {
      console.error('❌ Error fetching commission_balances data:', commissionError);
    } else {
      console.log('✅ Commission_balances table columns:');
      Object.keys(commissionData).forEach(column => {
        console.log(`   • ${column}: ${typeof commissionData[column]} = ${commissionData[column]}`);
      });
    }

    // Check aureus_share_purchases table structure
    console.log('\n3. Checking aureus_share_purchases table structure...');
    const { data: sharesData, error: sharesError } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .eq('user_id', 4)
      .limit(1);

    if (sharesError) {
      console.error('❌ Error fetching aureus_share_purchases data:', sharesError);
    } else if (sharesData && sharesData.length > 0) {
      console.log('✅ Aureus_share_purchases table columns:');
      Object.keys(sharesData[0]).forEach(column => {
        console.log(`   • ${column}: ${typeof sharesData[0][column]} = ${sharesData[0][column]}`);
      });
    } else {
      console.log('ℹ️ No share purchases found for user 4');
    }

    // Check referrals table structure
    console.log('\n4. Checking referrals table structure...');
    const { data: referralsData, error: referralsError } = await supabase
      .from('referrals')
      .select('*')
      .eq('referrer_id', 4)
      .limit(1);

    if (referralsError) {
      console.error('❌ Error fetching referrals data:', referralsError);
    } else if (referralsData && referralsData.length > 0) {
      console.log('✅ Referrals table columns:');
      Object.keys(referralsData[0]).forEach(column => {
        console.log(`   • ${column}: ${typeof referralsData[0][column]} = ${referralsData[0][column]}`);
      });
    } else {
      console.log('ℹ️ No referrals found for user 4');
    }

    console.log('\n🔧 COLUMN MAPPING FIXES NEEDED:');
    console.log('');

    if (usersData) {
      console.log('**Users table:**');
      if (!usersData.hasOwnProperty('first_name')) {
        const nameColumns = Object.keys(usersData).filter(col => 
          col.includes('name') || col.includes('full')
        );
        console.log(`❌ 'first_name' doesn't exist. Available name columns: ${nameColumns.join(', ')}`);
      } else {
        console.log('✅ first_name column exists');
      }
    }

    if (commissionData) {
      console.log('\n**Commission_balances table:**');
      if (!commissionData.hasOwnProperty('total_withdrawn_usdt')) {
        const withdrawnColumns = Object.keys(commissionData).filter(col => 
          col.includes('withdrawn') || col.includes('withdraw')
        );
        console.log(`❌ 'total_withdrawn_usdt' doesn't exist. Available withdrawn columns: ${withdrawnColumns.join(', ')}`);
      } else {
        console.log('✅ total_withdrawn_usdt column exists');
      }
    }

    console.log('\n📋 RECOMMENDED QUERY FIXES:');
    console.log('');
    console.log('Update UserDashboard.tsx queries to use correct column names:');
    
    if (usersData) {
      const correctUserColumns = Object.keys(usersData).filter(col => 
        ['telegram_id', 'username'].includes(col) || col.includes('name')
      );
      console.log(`• Users query: select('${correctUserColumns.join(', ')}')`);
    }

    if (commissionData) {
      const correctCommissionColumns = Object.keys(commissionData).filter(col => 
        ['usdt_balance', 'share_balance', 'total_earned_usdt', 'total_earned_shares', 'escrowed_amount'].includes(col) ||
        col.includes('withdrawn')
      );
      console.log(`• Commission query: select('${correctCommissionColumns.join(', ')}')`);
    }

  } catch (error) {
    console.error('❌ Schema check failed:', error);
  }
}

checkDatabaseSchema();
