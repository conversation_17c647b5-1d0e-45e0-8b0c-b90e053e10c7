/**
 * CHECK USER 367 SHARE SOURCES
 * 
 * This script checks all possible sources of shares for user 367
 * to understand why the admin dashboard shows 0 shares
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client with service role
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkUser367ShareSources() {
  console.log('🔍 CHECKING USER 367 SHARE SOURCES');
  console.log('==================================');
  
  try {
    // ===== CHECK 1: aureus_share_purchases =====
    console.log('\n📊 CHECK 1: aureus_share_purchases');
    
    const { data: purchases, error: purchaseError } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .eq('user_id', 367);
      
    if (purchaseError) {
      console.log('❌ Error:', purchaseError.message);
    } else {
      console.log(`   → Records found: ${purchases.length}`);
      if (purchases.length > 0) {
        purchases.forEach((p, index) => {
          console.log(`   ${index + 1}. ${p.shares_purchased} shares for $${p.total_amount} (status: ${p.status})`);
        });
        const totalPurchased = purchases.reduce((sum, p) => sum + (p.shares_purchased || 0), 0);
        console.log(`   → Total purchased shares: ${totalPurchased}`);
      } else {
        console.log('   → No share purchases found');
      }
    }
    
    // ===== CHECK 2: commission_balances =====
    console.log('\n💰 CHECK 2: commission_balances');
    
    const { data: commission, error: commissionError } = await supabase
      .from('commission_balances')
      .select('*')
      .eq('user_id', 367)
      .single();
      
    if (commissionError) {
      console.log('❌ Error:', commissionError.message);
    } else {
      console.log('   → USDT Balance:', commission.usdt_balance);
      console.log('   → Share Balance:', commission.share_balance);
      console.log('   → Total Earned USDT:', commission.total_earned_usdt);
      console.log('   → Total Earned Shares:', commission.total_earned_shares);
      console.log('   → Updated At:', commission.updated_at);
    }
    
    // ===== CHECK 3: share_transfers (received) =====
    console.log('\n🔄 CHECK 3: share_transfers (received)');
    
    const { data: transfers, error: transferError } = await supabase
      .from('share_transfers')
      .select('*')
      .eq('recipient_user_id', 367);
      
    if (transferError) {
      console.log('❌ Error:', transferError.message);
    } else {
      console.log(`   → Transfers received: ${transfers.length}`);
      if (transfers.length > 0) {
        transfers.forEach((t, index) => {
          console.log(`   ${index + 1}. ${t.shares_transferred} shares from user ${t.sender_user_id}`);
          console.log(`      → Status: ${t.status}`);
          console.log(`      → Created: ${t.created_at}`);
          console.log(`      → Completed: ${t.completed_at}`);
        });
      } else {
        console.log('   → No transfers received');
      }
    }
    
    // ===== CHECK 4: commission_transactions =====
    console.log('\n🤝 CHECK 4: commission_transactions');
    
    const { data: commissionTxns, error: commissionTxnError } = await supabase
      .from('commission_transactions')
      .select('*')
      .eq('referrer_id', 367);
      
    if (commissionTxnError) {
      console.log('❌ Error:', commissionTxnError.message);
    } else {
      console.log(`   → Commission transactions: ${commissionTxns.length}`);
      if (commissionTxns.length > 0) {
        commissionTxns.forEach((ct, index) => {
          console.log(`   ${index + 1}. USDT: $${ct.usdt_commission}, Shares: ${ct.share_commission}`);
          console.log(`      → Status: ${ct.status}`);
        });
      } else {
        console.log('   → No commission transactions found');
      }
    }
    
    // ===== ANALYSIS =====
    console.log('\n📋 ANALYSIS');
    console.log('===========');
    
    console.log('🔍 Current UserManager calculation logic:');
    console.log('   → ONLY counts aureus_share_purchases.shares_purchased');
    console.log('   → IGNORES commission_balances.share_balance');
    console.log('   → IGNORES transferred shares');
    console.log('');
    console.log('💡 The issue:');
    console.log('   → User 367 has 1 share in commission_balances.share_balance');
    console.log('   → User 367 has 0 shares in aureus_share_purchases');
    console.log('   → Admin dashboard only shows aureus_share_purchases');
    console.log('   → Therefore shows 0 shares instead of 1 share');
    console.log('');
    console.log('✅ Solution needed:');
    console.log('   → Update UserManager to include commission_balances.share_balance');
    console.log('   → Total shares = purchased shares + commission share balance');
    
    return true;
    
  } catch (error) {
    console.error('💥 Fatal error:', error);
    return false;
  }
}

// Run the check
checkUser367ShareSources()
  .then((success) => {
    if (success) {
      console.log('\n✅ Share source analysis completed!');
    } else {
      console.log('\n❌ Share source analysis failed');
    }
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error('\n💥 Analysis failed:', error);
    process.exit(1);
  });
