<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aureus Alliance - Error Fixes Verification</title>
    
    <!-- Test CSP Configuration -->
    <meta http-equiv="Content-Security-Policy" content="
      default-src 'self';
      script-src 'self' 'unsafe-inline' 'unsafe-eval' https://vercel.live https://*.supabase.co https://js.stripe.com https://checkout.stripe.com blob:;
      style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
      img-src 'self' data: blob: https: https://*.supabase.co https://images.unsplash.com https://via.placeholder.com;
      font-src 'self' https://fonts.gstatic.com data:;
      connect-src 'self' https://*.supabase.co https://api.stripe.com https://checkout.stripe.com wss://*.supabase.co;
      frame-src 'self' https://js.stripe.com https://checkout.stripe.com;
      worker-src 'self' blob:;
      object-src 'none';
      base-uri 'self';
      form-action 'self';
      frame-ancestors 'none';
    ">
    
    <!-- Google Fonts Test -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #111827, #1f2937);
            color: #ffffff;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .test-section h2 {
            color: #f59e0b;
            margin-top: 0;
        }
        
        .status {
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 500;
        }
        
        .success {
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid #10b981;
            color: #10b981;
        }
        
        .warning {
            background: rgba(245, 158, 11, 0.2);
            border: 1px solid #f59e0b;
            color: #f59e0b;
        }
        
        .error {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid #ef4444;
            color: #ef4444;
        }
        
        .test-button {
            background: #f59e0b;
            color: #111827;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            background: #d97706;
            transform: translateY(-2px);
        }
        
        .log {
            background: #111827;
            border: 1px solid #374151;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .svg-test {
            display: inline-block;
            margin: 10px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        
        .resource-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .resource-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        
        .resource-item img {
            max-width: 64px;
            max-height: 64px;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Aureus Alliance - Error Fixes Verification</h1>
        <p>This page tests all the critical error fixes implemented for the Aureus Alliance website.</p>
        
        <!-- Test Section 1: SVG Path Fixes -->
        <div class="test-section">
            <h2>🎨 SVG Path Error Fixes</h2>
            <p>Testing SVG paths with malformed data and automatic fixing:</p>
            
            <div class="svg-test">
                <h4>Malformed SVG (should be auto-fixed):</h4>
                <svg width="50" height="50" viewBox="0 0 24 24" id="test-svg-1">
                    <path d="M10,10 L20,20 tc0.2,0,0.4-0.2,0" stroke="currentColor" fill="none" stroke-width="2"/>
                </svg>
            </div>
            
            <div class="svg-test">
                <h4>Valid SVG (should remain unchanged):</h4>
                <svg width="50" height="50" viewBox="0 0 24 24" id="test-svg-2">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="green" fill="none" stroke-width="2"/>
                </svg>
            </div>
            
            <button class="test-button" onclick="testSVGFixes()">Test SVG Path Validation</button>
            <button class="test-button" onclick="testJQuerySVG()">Test jQuery SVG Fixes</button>
            <div id="svg-log" class="log"></div>
        </div>

        <!-- Test Section 2: CSP Compliance -->
        <div class="test-section">
            <h2>🛡️ Content Security Policy Tests</h2>
            <p>Testing CSP compliance and resource loading:</p>
            
            <button class="test-button" onclick="testInlineStyles()">Test Inline Styles</button>
            <button class="test-button" onclick="testExternalFonts()">Test Google Fonts</button>
            <button class="test-button" onclick="testBlobResources()">Test Blob Resources</button>
            <div id="csp-log" class="log"></div>
        </div>

        <!-- Test Section 3: Resource Loading -->
        <div class="test-section">
            <h2>📁 Resource Loading Tests</h2>
            <p>Testing PWA manifest icons and resources:</p>
            
            <div class="resource-test">
                <div class="resource-item">
                    <h4>App Icon 192x192</h4>
                    <img src="/icons/icon-192x192.png" alt="App Icon" onload="resourceLoaded(this)" onerror="resourceFailed(this)">
                    <div class="status" id="icon-192-status">Loading...</div>
                </div>
                
                <div class="resource-item">
                    <h4>Shortcut Buy Icon</h4>
                    <img src="/icons/shortcut-buy.png" alt="Buy Icon" onload="resourceLoaded(this)" onerror="resourceFailed(this)">
                    <div class="status" id="shortcut-buy-status">Loading...</div>
                </div>
                
                <div class="resource-item">
                    <h4>Manifest File</h4>
                    <div id="manifest-status" class="status">Testing...</div>
                </div>
                
                <div class="resource-item">
                    <h4>Service Worker</h4>
                    <div id="sw-status" class="status">Testing...</div>
                </div>
            </div>
            
            <button class="test-button" onclick="testAllResources()">Test All Resources</button>
            <div id="resource-log" class="log"></div>
        </div>

        <!-- Test Section 4: Error Monitoring -->
        <div class="test-section">
            <h2>📊 Error Monitoring System</h2>
            <p>Testing the comprehensive error monitoring and fixing system:</p>
            
            <button class="test-button" onclick="testErrorMonitoring()">Test Error Monitor</button>
            <button class="test-button" onclick="triggerTestErrors()">Trigger Test Errors</button>
            <button class="test-button" onclick="getErrorStats()">Get Error Statistics</button>
            <div id="monitor-log" class="log"></div>
        </div>

        <!-- Test Section 5: Overall Health Check -->
        <div class="test-section">
            <h2>✅ Overall System Health</h2>
            <p>Comprehensive health check of all fixes:</p>
            
            <button class="test-button" onclick="runFullHealthCheck()">Run Full Health Check</button>
            <div id="health-log" class="log"></div>
        </div>
    </div>

    <!-- Load Error Monitoring System -->
    <script type="module">
        import('./lib/errorMonitoring.js').then(() => {
            console.log('✅ Error monitoring system loaded for testing');
            updateMonitorStatus('✅ Error monitoring system active');
        }).catch(err => {
            console.warn('⚠️ Error monitoring failed to load:', err);
            updateMonitorStatus('⚠️ Error monitoring failed to load');
        });
        
        function updateMonitorStatus(message) {
            const log = document.getElementById('monitor-log');
            if (log) {
                log.innerHTML += `<div>${new Date().toLocaleTimeString()}: ${message}</div>`;
            }
        }
    </script>

    <script>
        // Test Functions
        
        function testSVGFixes() {
            const log = document.getElementById('svg-log');
            log.innerHTML = '<div class="success">🧪 Testing SVG path fixes...</div>';
            
            try {
                // Test malformed path fixing
                const testSVG = document.getElementById('test-svg-1');
                const path = testSVG.querySelector('path');
                const originalPath = path.getAttribute('d');
                
                log.innerHTML += `<div>Original path: ${originalPath}</div>`;
                
                // Trigger a re-set to test the interceptor
                path.setAttribute('d', 'M10,10 L20,20 tc0.2,0,0.4-0.2,0 invalid');
                const newPath = path.getAttribute('d');
                
                log.innerHTML += `<div>Fixed path: ${newPath}</div>`;
                
                if (!newPath.includes('tc') && !newPath.includes('invalid')) {
                    log.innerHTML += '<div class="success">✅ SVG path interceptor working correctly</div>';
                } else {
                    log.innerHTML += '<div class="warning">⚠️ SVG path interceptor may not be active</div>';
                }
                
            } catch (error) {
                log.innerHTML += `<div class="error">❌ SVG test failed: ${error.message}</div>`;
            }
        }
        
        function testJQuerySVG() {
            const log = document.getElementById('svg-log');
            
            if (typeof $ === 'undefined') {
                log.innerHTML += '<div class="warning">⚠️ jQuery not loaded - skipping jQuery tests</div>';
                return;
            }
            
            try {
                const $testPath = $('#test-svg-1 path');
                $testPath.attr('d', 'M5,5 L15,15 tc0.2,0,0.4-0.2,0');
                const resultPath = $testPath.attr('d');
                
                if (!resultPath.includes('tc')) {
                    log.innerHTML += '<div class="success">✅ jQuery SVG interceptor working</div>';
                } else {
                    log.innerHTML += '<div class="warning">⚠️ jQuery SVG interceptor not active</div>';
                }
            } catch (error) {
                log.innerHTML += `<div class="error">❌ jQuery test failed: ${error.message}</div>`;
            }
        }
        
        function testInlineStyles() {
            const log = document.getElementById('csp-log');
            log.innerHTML = '<div class="success">🧪 Testing inline styles...</div>';
            
            try {
                // Test inline style
                const testDiv = document.createElement('div');
                testDiv.style.color = 'red';
                testDiv.style.background = 'blue';
                document.body.appendChild(testDiv);
                
                const computedStyle = window.getComputedStyle(testDiv);
                if (computedStyle.color && computedStyle.backgroundColor) {
                    log.innerHTML += '<div class="success">✅ Inline styles working (CSP allows style-src unsafe-inline)</div>';
                } else {
                    log.innerHTML += '<div class="error">❌ Inline styles blocked by CSP</div>';
                }
                
                document.body.removeChild(testDiv);
                
            } catch (error) {
                log.innerHTML += `<div class="error">❌ Inline style test failed: ${error.message}</div>`;
            }
        }
        
        function testExternalFonts() {
            const log = document.getElementById('csp-log');
            
            // Check if Google Fonts loaded
            const fontTest = document.createElement('div');
            fontTest.style.fontFamily = 'Inter, sans-serif';
            fontTest.style.position = 'absolute';
            fontTest.style.visibility = 'hidden';
            fontTest.textContent = 'Test';
            document.body.appendChild(fontTest);
            
            setTimeout(() => {
                const computedFont = window.getComputedStyle(fontTest).fontFamily;
                if (computedFont.includes('Inter')) {
                    log.innerHTML += '<div class="success">✅ Google Fonts loaded successfully</div>';
                } else {
                    log.innerHTML += '<div class="warning">⚠️ Google Fonts may not have loaded</div>';
                }
                document.body.removeChild(fontTest);
            }, 1000);
        }
        
        function testBlobResources() {
            const log = document.getElementById('csp-log');
            
            try {
                // Test blob URL creation
                const blob = new Blob(['console.log("Blob test");'], { type: 'application/javascript' });
                const blobUrl = URL.createObjectURL(blob);
                
                log.innerHTML += '<div class="success">✅ Blob URLs can be created</div>';
                log.innerHTML += `<div>Blob URL: ${blobUrl.substring(0, 50)}...</div>`;
                
                URL.revokeObjectURL(blobUrl);
                
            } catch (error) {
                log.innerHTML += `<div class="error">❌ Blob test failed: ${error.message}</div>`;
            }
        }
        
        function resourceLoaded(img) {
            const statusId = img.alt.toLowerCase().replace(/\s+/g, '-') + '-status';
            const statusEl = document.getElementById(statusId) || img.nextElementSibling;
            if (statusEl) {
                statusEl.className = 'status success';
                statusEl.textContent = '✅ Loaded';
            }
        }
        
        function resourceFailed(img) {
            const statusId = img.alt.toLowerCase().replace(/\s+/g, '-') + '-status';
            const statusEl = document.getElementById(statusId) || img.nextElementSibling;
            if (statusEl) {
                statusEl.className = 'status error';
                statusEl.textContent = '❌ Failed to load';
            }
        }
        
        function testAllResources() {
            const log = document.getElementById('resource-log');
            log.innerHTML = '<div class="success">🧪 Testing all resources...</div>';
            
            // Test manifest
            fetch('/manifest.json')
                .then(response => {
                    if (response.ok) {
                        document.getElementById('manifest-status').className = 'status success';
                        document.getElementById('manifest-status').textContent = '✅ Manifest OK';
                        log.innerHTML += '<div class="success">✅ Manifest.json loaded successfully</div>';
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .catch(error => {
                    document.getElementById('manifest-status').className = 'status error';
                    document.getElementById('manifest-status').textContent = '❌ Failed';
                    log.innerHTML += `<div class="error">❌ Manifest failed: ${error.message}</div>`;
                });
            
            // Test service worker
            fetch('/sw.js')
                .then(response => {
                    if (response.ok) {
                        document.getElementById('sw-status').className = 'status success';
                        document.getElementById('sw-status').textContent = '✅ SW Available';
                        log.innerHTML += '<div class="success">✅ Service worker file available</div>';
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .catch(error => {
                    document.getElementById('sw-status').className = 'status warning';
                    document.getElementById('sw-status').textContent = '⚠️ SW Missing';
                    log.innerHTML += `<div class="warning">⚠️ Service worker: ${error.message}</div>`;
                });
        }
        
        function testErrorMonitoring() {
            const log = document.getElementById('monitor-log');
            
            if (window.errorMonitor) {
                const stats = window.errorMonitor.getErrorStats();
                log.innerHTML += `<div class="success">✅ Error monitor active</div>`;
                log.innerHTML += `<div>Current stats: ${JSON.stringify(stats, null, 2)}</div>`;
            } else {
                log.innerHTML += '<div class="warning">⚠️ Error monitor not available</div>';
            }
        }
        
        function triggerTestErrors() {
            const log = document.getElementById('monitor-log');
            log.innerHTML += '<div class="success">🧪 Triggering test errors...</div>';
            
            // Trigger SVG path error (should be suppressed)
            try {
                const testPath = document.createElement('path');
                testPath.setAttribute('d', 'M10,10 tc0.2,0,0.4-0.2,0 invalid');
                log.innerHTML += '<div>Triggered SVG path error (should be suppressed)</div>';
            } catch (error) {
                log.innerHTML += `<div>SVG error: ${error.message}</div>`;
            }
            
            // Check if errors were handled
            setTimeout(() => {
                if (window.errorMonitor) {
                    const stats = window.errorMonitor.getErrorStats();
                    log.innerHTML += `<div>Updated stats: ${JSON.stringify(stats, null, 2)}</div>`;
                }
            }, 1000);
        }
        
        function getErrorStats() {
            const log = document.getElementById('monitor-log');
            
            if (window.errorMonitor) {
                const report = window.errorMonitor.getErrorReport();
                log.innerHTML = `<div class="success">📊 Error Report:</div>`;
                log.innerHTML += `<pre>${JSON.stringify(report, null, 2)}</pre>`;
            } else {
                log.innerHTML += '<div class="warning">⚠️ Error monitor not available for stats</div>';
            }
        }
        
        function runFullHealthCheck() {
            const log = document.getElementById('health-log');
            log.innerHTML = '<div class="success">🏥 Running full health check...</div>';
            
            let checks = 0;
            let passed = 0;
            
            // Check 1: Error monitoring
            checks++;
            if (window.errorMonitor) {
                passed++;
                log.innerHTML += '<div class="success">✅ Error monitoring system active</div>';
            } else {
                log.innerHTML += '<div class="warning">⚠️ Error monitoring not active</div>';
            }
            
            // Check 2: SVG path handling
            checks++;
            try {
                const testPath = document.createElement('path');
                testPath.setAttribute('d', 'M10,10 tc0.2,0,0.4-0.2,0');
                const result = testPath.getAttribute('d');
                if (!result.includes('tc')) {
                    passed++;
                    log.innerHTML += '<div class="success">✅ SVG path interceptor working</div>';
                } else {
                    log.innerHTML += '<div class="warning">⚠️ SVG path interceptor not working</div>';
                }
            } catch (error) {
                log.innerHTML += `<div class="error">❌ SVG test failed: ${error.message}</div>`;
            }
            
            // Check 3: CSP compliance
            checks++;
            try {
                const testDiv = document.createElement('div');
                testDiv.style.color = 'red';
                if (testDiv.style.color) {
                    passed++;
                    log.innerHTML += '<div class="success">✅ CSP allows necessary inline styles</div>';
                } else {
                    log.innerHTML += '<div class="error">❌ CSP too restrictive</div>';
                }
            } catch (error) {
                log.innerHTML += `<div class="error">❌ CSP test failed: ${error.message}</div>`;
            }
            
            // Check 4: Resource availability
            checks++;
            fetch('/manifest.json')
                .then(response => {
                    if (response.ok) {
                        passed++;
                        log.innerHTML += '<div class="success">✅ PWA resources available</div>';
                    } else {
                        log.innerHTML += '<div class="warning">⚠️ Some PWA resources missing</div>';
                    }
                    
                    // Final summary
                    const score = Math.round((passed / checks) * 100);
                    log.innerHTML += `<div class="success">🎯 Health Score: ${score}% (${passed}/${checks} checks passed)</div>`;
                    
                    if (score >= 80) {
                        log.innerHTML += '<div class="success">🎉 System health is EXCELLENT!</div>';
                    } else if (score >= 60) {
                        log.innerHTML += '<div class="warning">⚠️ System health is GOOD but could be improved</div>';
                    } else {
                        log.innerHTML += '<div class="error">❌ System health needs attention</div>';
                    }
                })
                .catch(error => {
                    log.innerHTML += `<div class="error">❌ Resource check failed: ${error.message}</div>`;
                    const score = Math.round((passed / checks) * 100);
                    log.innerHTML += `<div class="warning">🎯 Partial Health Score: ${score}% (${passed}/${checks} checks passed)</div>`;
                });
        }
        
        // Auto-run basic tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testAllResources();
                if (window.errorMonitor) {
                    testErrorMonitoring();
                }
            }, 2000);
        });
    </script>
</body>
</html>
