/**
 * AUTHENTICATION STATE HOOK
 * 
 * Manages authentication state, loading states, and error handling
 * for all authentication methods (Telegram, web login, registration).
 */

import { useState, useCallback } from 'react';

export type AuthTab = 'telegram' | 'web-login' | 'web-register';
export type ResetStep = 'email' | 'pin' | 'password';

export interface AuthState {
  activeTab: AuthTab;
  isLoading: boolean;
  error: string | null;
  success: string | null;
  showTermsModal: boolean;
  showPrivacyModal: boolean;
  showForgotPassword: boolean;
  resetStep: ResetStep;
}

export interface UseAuthStateReturn {
  authState: AuthState;
  setActiveTab: (tab: AuthTab) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setSuccess: (success: string | null) => void;
  setShowTermsModal: (show: boolean) => void;
  setShowPrivacyModal: (show: boolean) => void;
  setShowForgotPassword: (show: boolean) => void;
  setResetStep: (step: ResetStep) => void;
  clearMessages: () => void;
  resetAuthState: () => void;
}

export const useAuthState = (initialTab: AuthTab = 'web-register'): UseAuthStateReturn => {
  const [authState, setAuthState] = useState<AuthState>({
    activeTab: initialTab,
    isLoading: false,
    error: null,
    success: null,
    showTermsModal: false,
    showPrivacyModal: false,
    showForgotPassword: false,
    resetStep: 'email'
  });

  const setActiveTab = useCallback((tab: AuthTab) => {
    setAuthState(prev => ({
      ...prev,
      activeTab: tab,
      error: null,
      success: null
    }));
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    setAuthState(prev => ({
      ...prev,
      isLoading: loading
    }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setAuthState(prev => ({
      ...prev,
      error,
      success: null,
      isLoading: false
    }));
  }, []);

  const setSuccess = useCallback((success: string | null) => {
    setAuthState(prev => ({
      ...prev,
      success,
      error: null
    }));
  }, []);

  const setShowTermsModal = useCallback((show: boolean) => {
    setAuthState(prev => ({
      ...prev,
      showTermsModal: show
    }));
  }, []);

  const setShowPrivacyModal = useCallback((show: boolean) => {
    setAuthState(prev => ({
      ...prev,
      showPrivacyModal: show
    }));
  }, []);

  const setShowForgotPassword = useCallback((show: boolean) => {
    setAuthState(prev => ({
      ...prev,
      showForgotPassword: show,
      resetStep: 'email'
    }));
  }, []);

  const setResetStep = useCallback((step: ResetStep) => {
    setAuthState(prev => ({
      ...prev,
      resetStep: step
    }));
  }, []);

  const clearMessages = useCallback(() => {
    setAuthState(prev => ({
      ...prev,
      error: null,
      success: null
    }));
  }, []);

  const resetAuthState = useCallback(() => {
    setAuthState({
      activeTab: initialTab,
      isLoading: false,
      error: null,
      success: null,
      showTermsModal: false,
      showPrivacyModal: false,
      showForgotPassword: false,
      resetStep: 'email'
    });
  }, [initialTab]);

  return {
    authState,
    setActiveTab,
    setLoading,
    setError,
    setSuccess,
    setShowTermsModal,
    setShowPrivacyModal,
    setShowForgotPassword,
    setResetStep,
    clearMessages,
    resetAuthState
  };
};
