<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aureus Design System Test</title>
    <link rel="stylesheet" href="./index.css">
</head>
<body>
    <!-- Test our unified design system -->
    <div class="page">
        <!-- Header -->
        <header class="header">
            <div class="container">
                <div class="header-content">
                    <a href="/" class="logo">Aureus Alliance</a>
                    <nav class="nav-menu">
                        <a href="/" class="nav-item active">Home</a>
                        <a href="/about" class="nav-item">About</a>
                        <a href="/contact" class="nav-item">Contact</a>
                    </nav>
                    <button class="theme-toggle" aria-label="Toggle theme">🌙</button>
                </div>
            </div>
        </header>

        <!-- Hero Section -->
        <section class="hero">
            <div class="container">
                <div class="hero-content">
                    <h1 class="hero-title">Aureus Alliance Holdings</h1>
                    <p class="hero-subtitle">Professional Corporate Design System Test</p>
                    <div class="cta-buttons">
                        <button class="btn btn-primary">Primary Button</button>
                        <button class="btn btn-secondary">Secondary Button</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Content Section -->
        <section class="section">
            <div class="container">
                <div class="section-header">
                    <h2>Design System Components</h2>
                    <p>Testing our unified design system with premium color palette</p>
                </div>

                <!-- Metrics Grid -->
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">$1,234,567</div>
                        <div class="metric-label">Total Value</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">25</div>
                        <div class="metric-label">Active Projects</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">98%</div>
                        <div class="metric-label">Success Rate</div>
                    </div>
                </div>

                <!-- Cards Grid -->
                <div class="grid grid-responsive">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Professional Design</h3>
                            <p class="card-subtitle">Premium color palette</p>
                        </div>
                        <div class="card-content">
                            <p>This card uses our unified design system with the exact premium color palette from aureus_ui_color_palette_premium.md</p>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-primary btn-sm">Learn More</button>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Theme Switching</h3>
                            <p class="card-subtitle">Dark/Light modes</p>
                        </div>
                        <div class="card-content">
                            <p>Click the theme toggle button in the header to switch between light and dark themes seamlessly.</p>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-success btn-sm">Try It</button>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Responsive Design</h3>
                            <p class="card-subtitle">Mobile-first approach</p>
                        </div>
                        <div class="card-content">
                            <p>The design system is fully responsive and works perfectly on mobile, tablet, and desktop devices.</p>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-warning btn-sm">Resize Window</button>
                        </div>
                    </div>
                </div>

                <!-- Form Example -->
                <div class="card" style="margin-top: 2rem;">
                    <div class="card-header">
                        <h3 class="card-title">Form Components</h3>
                        <p class="card-subtitle">Professional form styling</p>
                    </div>
                    <div class="card-content">
                        <div class="form-group">
                            <label class="form-label" for="test-email">Email Address</label>
                            <input type="email" id="test-email" class="form-input" placeholder="Enter your email">
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="test-message">Message</label>
                            <textarea id="test-message" class="form-textarea" placeholder="Enter your message"></textarea>
                        </div>
                        <div class="cta-buttons">
                            <button class="btn btn-primary">Submit</button>
                            <button class="btn btn-secondary">Cancel</button>
                        </div>
                    </div>
                </div>

                <!-- Color Palette Display -->
                <div class="section-header" style="margin-top: 3rem;">
                    <h2>Premium Color Palette</h2>
                    <p>Exact colors from aureus_ui_color_palette_premium.md</p>
                </div>

                <div class="grid grid-cols-4">
                    <div class="card text-center">
                        <div style="background: var(--gold); height: 60px; border-radius: var(--radius-md); margin-bottom: 1rem;"></div>
                        <h4>Gold</h4>
                        <p class="text-muted">#FFD700</p>
                    </div>
                    <div class="card text-center">
                        <div style="background: var(--blue); height: 60px; border-radius: var(--radius-md); margin-bottom: 1rem;"></div>
                        <h4>Blue</h4>
                        <p class="text-muted">#007BFF</p>
                    </div>
                    <div class="card text-center">
                        <div style="background: var(--emerald); height: 60px; border-radius: var(--radius-md); margin-bottom: 1rem;"></div>
                        <h4>Emerald</h4>
                        <p class="text-muted">#27AE60</p>
                    </div>
                    <div class="card text-center">
                        <div style="background: var(--copper); height: 60px; border-radius: var(--radius-md); margin-bottom: 1rem;"></div>
                        <h4>Copper</h4>
                        <p class="text-muted">#B75E2A</p>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Load our JavaScript -->
    <script src="./aureus.js"></script>
</body>
</html>
