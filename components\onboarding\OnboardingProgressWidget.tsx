import React, { useState } from 'react';
import { useOnboarding } from '../../lib/hooks/useOnboarding';

interface OnboardingProgressWidgetProps {
  userId: number;
  onViewFullDashboard?: () => void;
  showWelcomeMessage?: boolean;
}

export const OnboardingProgressWidget: React.FC<OnboardingProgressWidgetProps> = ({
  userId,
  onViewFullDashboard,
  showWelcomeMessage = true
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const {
    status,
    loading,
    error,
    startStep,
    completeStep,
    isComplete,
    progressPercentage,
    estimatedTimeRemaining,
    achievements
  } = useOnboarding({ userId, autoLoad: true });

  const handleStepAction = async (stepId: string) => {
    const success = await startStep(stepId);
    if (success) {
      // Optionally redirect to the specific step or show a modal
      console.log(`Started step: ${stepId}`);
    }
  };

  if (loading) {
    return (
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="h-2 bg-gray-700 rounded w-full mb-2"></div>
          <div className="h-4 bg-gray-700 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
        <p className="text-red-300 text-sm">Failed to load onboarding progress</p>
      </div>
    );
  }

  // Don't show widget if onboarding is complete and user has seen welcome
  if (isComplete && !showWelcomeMessage) {
    return null;
  }

  return (
    <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg border border-gray-700 p-6 shadow-lg">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <span className="text-2xl">🎯</span>
          <div>
            <h3 className="text-lg font-semibold text-white">
              {isComplete ? 'Welcome to Aureus Africa!' : 'Getting Started'}
            </h3>
            <p className="text-sm text-gray-400">
              {isComplete 
                ? 'You\'ve completed your onboarding journey!'
                : `${status?.completed_steps || 0} of ${status?.total_steps || 0} steps completed`
              }
            </p>
          </div>
        </div>
        
        {!isComplete && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-gray-400 hover:text-white transition-colors"
          >
            {isExpanded ? '▼' : '▶'}
          </button>
        )}
      </div>

      {/* Progress Bar */}
      {!isComplete && (
        <div className="mb-4">
          <div className="flex justify-between text-sm text-gray-400 mb-2">
            <span>Progress</span>
            <span>{progressPercentage}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-3">
            <div 
              className="bg-gradient-to-r from-gold to-yellow-500 h-3 rounded-full transition-all duration-500 relative overflow-hidden"
              style={{ width: `${progressPercentage}%` }}
            >
              <div className="absolute inset-0 bg-white/20 animate-pulse"></div>
            </div>
          </div>
          {estimatedTimeRemaining > 0 && (
            <p className="text-xs text-gray-500 mt-1">
              ⏱️ About {estimatedTimeRemaining} minutes remaining
            </p>
          )}
        </div>
      )}

      {/* Completion Celebration */}
      {isComplete && (
        <div className="text-center py-4">
          <div className="text-4xl mb-3">🎉</div>
          <p className="text-gray-300 mb-4">
            Congratulations! You've unlocked all features and earned {achievements.length} achievements.
          </p>
          <div className="flex justify-center gap-3">
            <button
              onClick={onViewFullDashboard}
              className="px-4 py-2 bg-gold text-black font-medium rounded-lg hover:bg-yellow-500 transition-colors"
            >
              View Achievements
            </button>
          </div>
        </div>
      )}

      {/* Current Step */}
      {!isComplete && status?.current_step && (
        <div className="bg-blue-900/30 border border-blue-500/30 rounded-lg p-4 mb-4">

          <div className="flex items-center gap-3">
            <span className="text-xl">{status.current_step.icon}</span>
            <div className="flex-1">
              <h4 className="font-medium text-white">{status.current_step.name}</h4>
              <p className="text-sm text-gray-400">{status.current_step.description}</p>
            </div>
            <button
              onClick={() => handleStepAction(status.current_step!.id)}
              className="px-3 py-1 bg-blue-600 text-white text-sm font-medium rounded hover:bg-blue-500 transition-colors"
            >
              Continue
            </button>
          </div>
        </div>
      )}

      {/* Next Steps (Expanded View) */}
      {!isComplete && isExpanded && (
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-300 mb-3">Next Steps:</h4>
          {status?.next_steps.slice(0, 3).map(step => (
            <div key={step.id} className="flex items-center justify-between py-2 px-3 bg-gray-700/50 rounded-lg">
              <div className="flex items-center gap-3">
                <span className="text-lg">{step.icon}</span>
                <div>
                  <p className="text-white text-sm font-medium">{step.name}</p>
                  <div className="flex items-center gap-2 text-xs text-gray-400">
                    <span>⏱️ {step.estimatedTime} min</span>
                    {step.required && <span className="text-red-400">Required</span>}
                  </div>
                </div>
              </div>
              <button
                onClick={() => handleStepAction(step.id)}
                className="px-3 py-1 bg-gold text-black text-xs font-medium rounded hover:bg-yellow-500 transition-colors"
              >
                Start
              </button>
            </div>
          ))}
          
          {status && status.next_steps.length > 3 && (
            <p className="text-xs text-gray-500 text-center">
              +{status.next_steps.length - 3} more steps available
            </p>
          )}
        </div>
      )}

      {/* Quick Actions */}
      {!isComplete && (
        <div className="flex gap-2 mt-4">
          <button
            onClick={onViewFullDashboard}
            className="flex-1 px-4 py-2 bg-gray-700 text-gray-300 font-medium rounded-lg hover:bg-gray-600 transition-colors"
          >
            View All Steps
          </button>
          {!isExpanded && status?.next_steps && status.next_steps.length > 0 && (
            <button
              onClick={() => handleStepAction(status.next_steps[0].id)}
              className="flex-1 px-4 py-2 bg-gold text-black font-medium rounded-lg hover:bg-yellow-500 transition-colors"
            >
              Start Next Step
            </button>
          )}
        </div>
      )}

      {/* Recent Achievements */}
      {achievements.length > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-300">Recent Achievements</h4>
            <span className="text-xs text-gray-500">{achievements.length} total</span>
          </div>
          <div className="flex gap-2">
            {achievements.slice(-3).map(achievement => (
              <div
                key={achievement.id}
                className="flex items-center gap-2 px-3 py-1 bg-gray-700/50 rounded-full"
                title={achievement.description}
              >
                <span className="text-sm">{achievement.icon}</span>
                <span className="text-xs text-gray-300 truncate max-w-20">
                  {achievement.name}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Motivational Messages */}
      {!isComplete && progressPercentage > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-700">
          <div className="flex items-center gap-2">
            <span className="text-sm">
              {progressPercentage < 25 ? '🌱' : 
               progressPercentage < 50 ? '🌿' : 
               progressPercentage < 75 ? '🌳' : '🚀'}
            </span>
            <p className="text-xs text-gray-400">
              {progressPercentage < 25 ? 'Great start! Keep going to unlock more features.' :
               progressPercentage < 50 ? 'You\'re making excellent progress!' :
               progressPercentage < 75 ? 'Almost there! You\'re doing amazing.' :
               'Final stretch! You\'re so close to completing everything.'}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};
