/**
 * ADMIN PIN AUTHENTICATION SERVICE
 * 
 * Specialized service for admin two-factor authentication using PIN codes
 * <NAME_EMAIL> with comprehensive security features.
 * 
 * Features:
 * - PIN generation and secure storage
 * - Rate limiting to prevent PIN spam
 * - Audit logging for all admin authentication events
 * - Professional email formatting
 * - 15-minute PIN expiry with 3 attempt limit
 */

import { getServiceRoleClient } from './supabase'
import { logAdminAction } from './adminAuth'

export interface AdminPinResult {
  success: boolean
  error?: string
  attemptsRemaining?: number
  nextPinAllowedAt?: Date
}

export interface AdminPinSession {
  sessionId: string
  email: string
  pinCode: string
  expiresAt: Date
  attemptsRemaining: number
  createdAt: Date
}

export class AdminPinService {
  private static readonly ADMIN_EMAIL = '<EMAIL>'
  private static readonly PIN_LENGTH = 6
  private static readonly PIN_EXPIRY_MINUTES = 15
  private static readonly MAX_ATTEMPTS = 3
  private static readonly RATE_LIMIT_MINUTES = 5 // Minimum time between PIN requests

  /**
   * Generate a secure 6-digit PIN
   */
  private static generatePin(): string {
    return Math.floor(100000 + Math.random() * 900000).toString()
  }

  /**
   * Generate a unique session ID
   */
  private static generateSessionId(): string {
    return `admin_pin_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Check if admin can request a new PIN (rate limiting)
   */
  private static async checkRateLimit(adminEmail: string): Promise<{ allowed: boolean; nextAllowedAt?: Date }> {
    try {
      const serviceClient = getServiceRoleClient()
      const rateLimitTime = new Date(Date.now() - this.RATE_LIMIT_MINUTES * 60 * 1000)

      const { data: recentPins, error } = await serviceClient
        .from('admin_pin_sessions')
        .select('created_at')
        .eq('admin_email', adminEmail)
        .gte('created_at', rateLimitTime.toISOString())
        .order('created_at', { ascending: false })
        .limit(1)

      if (error) {
        console.error('❌ Error checking admin PIN rate limit:', error)
        return { allowed: false }
      }

      if (recentPins && recentPins.length > 0) {
        const lastPinTime = new Date(recentPins[0].created_at)
        const nextAllowedAt = new Date(lastPinTime.getTime() + this.RATE_LIMIT_MINUTES * 60 * 1000)
        
        if (nextAllowedAt > new Date()) {
          return { allowed: false, nextAllowedAt }
        }
      }

      return { allowed: true }
    } catch (error) {
      console.error('❌ Error in admin PIN rate limit check:', error)
      return { allowed: false }
    }
  }

  /**
   * Send PIN email using the existing RESEND API service
   */
  private static async sendPinEmail(pin: string, adminEmail: string, sessionId: string): Promise<boolean> {
    try {
      console.log(`📧 Sending admin PIN email to ${this.ADMIN_EMAIL} for admin: ${adminEmail}`)

      const response = await fetch('/api/send-verification-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: this.ADMIN_EMAIL,
          code: pin,
          purpose: 'admin_authentication',
          userName: 'Administrator',
          expiryMinutes: this.PIN_EXPIRY_MINUTES,
          sessionId: sessionId,
          adminEmail: adminEmail
        }),
      })

      const result = await response.json()

      if (result.success) {
        console.log('✅ Admin PIN email sent successfully:', result.messageId)
        return true
      } else {
        console.error('❌ Failed to send admin PIN email:', result.error)
        return false
      }
    } catch (error) {
      console.error('❌ Error sending admin PIN email:', error)
      return false
    }
  }

  /**
   * Generate and send admin PIN
   */
  static async generateAndSendPin(adminEmail: string): Promise<AdminPinResult> {
    try {
      console.log('🔐 Generating admin PIN for:', adminEmail)

      // Check rate limiting
      const rateLimitCheck = await this.checkRateLimit(adminEmail)
      if (!rateLimitCheck.allowed) {
        const waitMinutes = rateLimitCheck.nextAllowedAt 
          ? Math.ceil((rateLimitCheck.nextAllowedAt.getTime() - Date.now()) / (60 * 1000))
          : this.RATE_LIMIT_MINUTES

        return {
          success: false,
          error: `Please wait ${waitMinutes} minute(s) before requesting another PIN`
        }
      }

      const serviceClient = getServiceRoleClient()
      const pin = this.generatePin()
      const sessionId = this.generateSessionId()
      const expiresAt = new Date(Date.now() + this.PIN_EXPIRY_MINUTES * 60 * 1000)

      // Store PIN session in database
      const { data, error } = await serviceClient
        .from('admin_pin_sessions')
        .insert({
          session_id: sessionId,
          admin_email: adminEmail,
          pin_code: pin,
          expires_at: expiresAt.toISOString(),
          attempts_remaining: this.MAX_ATTEMPTS,
          verified: false,
          created_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) {
        console.error('❌ Error storing admin PIN session:', error)
        return {
          success: false,
          error: 'Failed to generate PIN session'
        }
      }

      // Send PIN email
      const emailSent = await this.sendPinEmail(pin, adminEmail, sessionId)
      
      if (!emailSent) {
        // Clean up database record if email failed
        await serviceClient
          .from('admin_pin_sessions')
          .delete()
          .eq('session_id', sessionId)

        return {
          success: false,
          error: 'Failed to send PIN email'
        }
      }

      // Log admin action
      await logAdminAction(adminEmail, 'admin_pin_requested', {
        session_id: sessionId,
        target_email: this.ADMIN_EMAIL,
        expires_at: expiresAt.toISOString()
      })

      console.log('✅ Admin PIN generated and sent successfully')
      return {
        success: true
      }

    } catch (error) {
      console.error('❌ Error generating admin PIN:', error)
      return {
        success: false,
        error: 'Failed to generate admin PIN'
      }
    }
  }

  /**
   * Verify admin PIN
   */
  static async verifyPin(adminEmail: string, pin: string): Promise<AdminPinResult> {
    try {
      console.log('🔍 Verifying admin PIN for:', adminEmail)

      const serviceClient = getServiceRoleClient()

      // Find valid PIN session
      const { data: pinSession, error: findError } = await serviceClient
        .from('admin_pin_sessions')
        .select('*')
        .eq('admin_email', adminEmail)
        .eq('pin_code', pin)
        .eq('verified', false)
        .gt('expires_at', new Date().toISOString())
        .gt('attempts_remaining', 0)
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      if (findError || !pinSession) {
        console.log('❌ Invalid or expired admin PIN')
        
        // Log failed attempt
        await logAdminAction(adminEmail, 'admin_pin_verification_failed', {
          reason: 'invalid_or_expired_pin',
          timestamp: new Date().toISOString()
        })

        return {
          success: false,
          error: 'Invalid or expired PIN'
        }
      }

      // Mark PIN as verified and decrement attempts
      const { error: updateError } = await serviceClient
        .from('admin_pin_sessions')
        .update({
          verified: true,
          attempts_remaining: pinSession.attempts_remaining - 1,
          verified_at: new Date().toISOString()
        })
        .eq('session_id', pinSession.session_id)

      if (updateError) {
        console.error('❌ Error updating admin PIN session:', updateError)
        return {
          success: false,
          error: 'Failed to verify PIN'
        }
      }

      // Log successful verification
      await logAdminAction(adminEmail, 'admin_pin_verified', {
        session_id: pinSession.session_id,
        verified_at: new Date().toISOString()
      })

      console.log('✅ Admin PIN verified successfully')
      return {
        success: true
      }

    } catch (error) {
      console.error('❌ Error verifying admin PIN:', error)
      return {
        success: false,
        error: 'Failed to verify PIN'
      }
    }
  }

  /**
   * Clean up expired PIN sessions (maintenance function)
   */
  static async cleanupExpiredSessions(): Promise<void> {
    try {
      const serviceClient = getServiceRoleClient()
      
      const { error } = await serviceClient
        .from('admin_pin_sessions')
        .delete()
        .lt('expires_at', new Date().toISOString())

      if (error) {
        console.error('❌ Error cleaning up expired admin PIN sessions:', error)
      } else {
        console.log('✅ Cleaned up expired admin PIN sessions')
      }
    } catch (error) {
      console.error('❌ Error in admin PIN cleanup:', error)
    }
  }
}
