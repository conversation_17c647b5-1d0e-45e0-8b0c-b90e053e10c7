// Test script for mobile crypto upload API
import fetch from 'node-fetch';

async function testMobileAPI() {
  console.log('🧪 Testing mobile crypto upload API...');
  
  const testData = {
    fileData: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==', // 1x1 pixel PNG
    fileName: 'test.jpg',
    filePath: 'test/test.jpg',
    paymentData: {
      user_id: 89,
      amount: 100,
      shares_to_purchase: 20,
      network: 1,
      currency: 'USDT',
      sender_wallet: 'test_wallet',
      receiver_wallet: 'test_receiver',
      transaction_hash: 'test_hash_' + Date.now(),
      status: 'pending',
      created_at: new Date().toISOString()
    },
    userAgent: 'Test Agent'
  };

  try {
    console.log('📤 Sending request to mobile API...');
    
    const response = await fetch('http://localhost:8000/api/mobile-crypto-upload', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });

    console.log('📊 Response status:', response.status);
    console.log('📊 Response headers:', Object.fromEntries(response.headers));
    
    const responseText = await response.text();
    console.log('📊 Response body:', responseText);
    
    if (response.ok) {
      console.log('✅ Mobile API test successful!');
      try {
        const responseData = JSON.parse(responseText);
        console.log('📊 Parsed response:', responseData);
      } catch (parseError) {
        console.log('⚠️  Response is not valid JSON');
      }
    } else {
      console.log('❌ Mobile API test failed');
    }
    
  } catch (error) {
    console.error('❌ Test error:', error.message);
    console.error('❌ Error details:', error);
  }
}

testMobileAPI();
