import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';

interface CertificateTemplateElement {
  id: string;
  element_id: string;
  element_type: 'text' | 'image' | 'line' | 'rectangle' | 'watermark';
  template_version: string;
  x_position: number;
  y_position: number;
  width?: number;
  height?: number;
  font_family?: string;
  font_size?: number;
  font_weight?: string;
  font_color?: string;
  text_anchor?: string;
  static_content?: string;
  data_field?: string;
  is_dynamic: boolean;
  opacity?: number;
  rotation?: number;
  z_index?: number;
  description?: string;
  is_active: boolean;
}

interface CertificateElementEditorProps {
  templateVersion?: string;
  onElementUpdate?: (element: CertificateTemplateElement) => void;
}

export const CertificateElementEditor: React.FC<CertificateElementEditorProps> = ({
  templateVersion = 'v1.0',
  onElementUpdate
}) => {
  const [elements, setElements] = useState<CertificateTemplateElement[]>([]);
  const [selectedElement, setSelectedElement] = useState<CertificateTemplateElement | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState<string>('');

  useEffect(() => {
    loadElements();
  }, [templateVersion]);

  const loadElements = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('certificate_template_elements')
        .select('*')
        .eq('template_version', templateVersion)
        .eq('is_active', true)
        .order('z_index', { ascending: true });

      if (error) throw error;

      setElements(data || []);
      if (data && data.length > 0 && !selectedElement) {
        setSelectedElement(data[0]);
      }
    } catch (error) {
      console.error('Error loading elements:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateElement = async (elementId: string, updates: Partial<CertificateTemplateElement>) => {
    try {
      setSaving(true);
      
      const { error } = await supabase
        .from('certificate_template_elements')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', elementId);

      if (error) throw error;

      // Update local state
      setElements(prev => prev.map(el => 
        el.id === elementId ? { ...el, ...updates } : el
      ));

      if (selectedElement && selectedElement.id === elementId) {
        setSelectedElement(prev => prev ? { ...prev, ...updates } : null);
      }

      // Trigger callback for real-time preview updates
      if (onElementUpdate && selectedElement) {
        onElementUpdate({ ...selectedElement, ...updates });
      }

      setSaveMessage('✅ Saved successfully');
      setTimeout(() => setSaveMessage(''), 2000);

    } catch (error) {
      console.error('Error updating element:', error);
      setSaveMessage('❌ Save failed');
      setTimeout(() => setSaveMessage(''), 3000);
    } finally {
      setSaving(false);
    }
  };

  const adjustPosition = (axis: 'x' | 'y', increment: number) => {
    if (!selectedElement) return;

    const currentValue = axis === 'x' ? selectedElement.x_position : selectedElement.y_position;
    const newValue = Math.max(0, currentValue + increment);
    
    updateElement(selectedElement.id, {
      [axis === 'x' ? 'x_position' : 'y_position']: newValue
    });
  };

  const adjustDimension = (dimension: 'width' | 'height', increment: number) => {
    if (!selectedElement) return;

    const currentValue = selectedElement[dimension] || 0;
    const newValue = Math.max(1, currentValue + increment);

    updateElement(selectedElement.id, { [dimension]: newValue });
  };

  const autoFitWidth = () => {
    if (!selectedElement) return;

    // Estimate required width based on content and font size
    let estimatedWidth = 100; // Default minimum width

    if (selectedElement.static_content) {
      const fontSize = selectedElement.font_size || 12;
      estimatedWidth = Math.max(estimatedWidth, selectedElement.static_content.length * fontSize * 0.6);
    } else if (selectedElement.data_field) {
      // Estimate based on typical content for each field
      const fontSize = selectedElement.font_size || 12;
      switch (selectedElement.data_field) {
        case 'userName':
          estimatedWidth = 200; // Typical name length
          break;
        case 'userIdNumber':
          estimatedWidth = 150; // ID: + 13 digits
          break;
        case 'userAddress':
          estimatedWidth = 300; // Address can be long
          break;
        case 'certificateNumber':
          estimatedWidth = 100; // 0-000-001 format
          break;
        case 'sharesCount':
          estimatedWidth = 80; // Numbers
          break;
        default:
          estimatedWidth = 120;
      }
    }

    // Round up to nearest 10px for cleaner values
    const newWidth = Math.ceil(estimatedWidth / 10) * 10;
    updateElement(selectedElement.id, { width: newWidth });
  };

  const adjustFontSize = (increment: number) => {
    if (!selectedElement) return;

    const currentSize = selectedElement.font_size || 12;
    const newSize = Math.max(6, Math.min(72, currentSize + increment));
    
    updateElement(selectedElement.id, { font_size: newSize });
  };

  if (loading) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400"></div>
          <span className="ml-3 text-gray-300">Loading template elements...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-white">🎯 Element Positioning Controls</h2>
        {saveMessage && (
          <div className="text-sm font-medium">
            {saveMessage.includes('✅') ? (
              <span className="text-green-400">{saveMessage}</span>
            ) : (
              <span className="text-red-400">{saveMessage}</span>
            )}
          </div>
        )}
      </div>

      <div className="grid lg:grid-cols-2 gap-6">
        {/* Element Selection */}
        <div>
          <h3 className="text-lg font-medium text-white mb-3">Select Element</h3>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {elements.map((element) => (
              <button
                key={element.id}
                onClick={() => setSelectedElement(element)}
                className={`w-full text-left p-3 rounded border transition-colors ${
                  selectedElement?.id === element.id
                    ? 'bg-yellow-600 border-yellow-500 text-white'
                    : 'bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600'
                }`}
              >
                <div className="font-medium">{element.element_id}</div>
                <div className="text-sm opacity-75">{element.description}</div>
                <div className="text-xs mt-1">
                  X: {element.x_position}px, Y: {element.y_position}px
                </div>
                <div className="text-xs">
                  W: {element.width || 'auto'}px, H: {element.height || 'auto'}px
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Position Controls */}
        {selectedElement && (
          <div>
            <h3 className="text-lg font-medium text-white mb-3">
              Position Controls: {selectedElement.element_id}
            </h3>
            
            <div className="space-y-4">
              {/* Current Position & Dimensions Display */}
              <div className="bg-gray-700 p-4 rounded border border-gray-600">
                <h4 className="font-medium text-yellow-400 mb-2">Current Position & Dimensions</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-300">X Position:</span>
                    <span className="ml-2 font-mono text-white">{selectedElement.x_position}px</span>
                  </div>
                  <div>
                    <span className="text-gray-300">Y Position:</span>
                    <span className="ml-2 font-mono text-white">{selectedElement.y_position}px</span>
                  </div>
                  <div>
                    <span className="text-gray-300">Width:</span>
                    <span className="ml-2 font-mono text-white">{selectedElement.width || 'auto'}px</span>
                  </div>
                  <div>
                    <span className="text-gray-300">Height:</span>
                    <span className="ml-2 font-mono text-white">{selectedElement.height || 'auto'}px</span>
                  </div>
                </div>
              </div>

              {/* X Position Controls */}
              <div className="bg-gray-700 p-4 rounded border border-gray-600">
                <h4 className="font-medium text-white mb-3">X Position (Horizontal)</h4>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => adjustPosition('x', -20)}
                    className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm"
                    disabled={saving}
                  >
                    -20px
                  </button>
                  <button
                    onClick={() => adjustPosition('x', -5)}
                    className="px-3 py-1 bg-red-500 hover:bg-red-600 text-white rounded text-sm"
                    disabled={saving}
                  >
                    -5px
                  </button>
                  <button
                    onClick={() => adjustPosition('x', -1)}
                    className="px-3 py-1 bg-red-400 hover:bg-red-500 text-white rounded text-sm"
                    disabled={saving}
                  >
                    -1px
                  </button>
                  <span className="mx-2 text-gray-300 font-mono min-w-[60px] text-center">
                    {selectedElement.x_position}px
                  </span>
                  <button
                    onClick={() => adjustPosition('x', 1)}
                    className="px-3 py-1 bg-green-400 hover:bg-green-500 text-white rounded text-sm"
                    disabled={saving}
                  >
                    +1px
                  </button>
                  <button
                    onClick={() => adjustPosition('x', 5)}
                    className="px-3 py-1 bg-green-500 hover:bg-green-600 text-white rounded text-sm"
                    disabled={saving}
                  >
                    +5px
                  </button>
                  <button
                    onClick={() => adjustPosition('x', 20)}
                    className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-sm"
                    disabled={saving}
                  >
                    +20px
                  </button>
                </div>
              </div>

              {/* Y Position Controls */}
              <div className="bg-gray-700 p-4 rounded border border-gray-600">
                <h4 className="font-medium text-white mb-3">Y Position (Vertical)</h4>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => adjustPosition('y', -20)}
                    className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm"
                    disabled={saving}
                  >
                    -20px
                  </button>
                  <button
                    onClick={() => adjustPosition('y', -5)}
                    className="px-3 py-1 bg-red-500 hover:bg-red-600 text-white rounded text-sm"
                    disabled={saving}
                  >
                    -5px
                  </button>
                  <button
                    onClick={() => adjustPosition('y', -1)}
                    className="px-3 py-1 bg-red-400 hover:bg-red-500 text-white rounded text-sm"
                    disabled={saving}
                  >
                    -1px
                  </button>
                  <span className="mx-2 text-gray-300 font-mono min-w-[60px] text-center">
                    {selectedElement.y_position}px
                  </span>
                  <button
                    onClick={() => adjustPosition('y', 1)}
                    className="px-3 py-1 bg-green-400 hover:bg-green-500 text-white rounded text-sm"
                    disabled={saving}
                  >
                    +1px
                  </button>
                  <button
                    onClick={() => adjustPosition('y', 5)}
                    className="px-3 py-1 bg-green-500 hover:bg-green-600 text-white rounded text-sm"
                    disabled={saving}
                  >
                    +5px
                  </button>
                  <button
                    onClick={() => adjustPosition('y', 20)}
                    className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-sm"
                    disabled={saving}
                  >
                    +20px
                  </button>
                </div>
              </div>

              {/* Width Controls */}
              <div className="bg-gray-700 p-4 rounded border border-gray-600">
                <h4 className="font-medium text-white mb-3">Width</h4>
                <div className="flex items-center gap-2 mb-2">
                  <button
                    onClick={() => adjustDimension('width', -20)}
                    className="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white rounded text-sm"
                    disabled={saving}
                  >
                    -20px
                  </button>
                  <button
                    onClick={() => adjustDimension('width', -5)}
                    className="px-3 py-1 bg-purple-500 hover:bg-purple-600 text-white rounded text-sm"
                    disabled={saving}
                  >
                    -5px
                  </button>
                  <button
                    onClick={() => adjustDimension('width', -1)}
                    className="px-3 py-1 bg-purple-400 hover:bg-purple-500 text-white rounded text-sm"
                    disabled={saving}
                  >
                    -1px
                  </button>
                  <span className="mx-2 text-gray-300 font-mono min-w-[60px] text-center">
                    {selectedElement.width || 'auto'}px
                  </span>
                  <button
                    onClick={() => adjustDimension('width', 1)}
                    className="px-3 py-1 bg-purple-400 hover:bg-purple-500 text-white rounded text-sm"
                    disabled={saving}
                  >
                    +1px
                  </button>
                  <button
                    onClick={() => adjustDimension('width', 5)}
                    className="px-3 py-1 bg-purple-500 hover:bg-purple-600 text-white rounded text-sm"
                    disabled={saving}
                  >
                    +5px
                  </button>
                  <button
                    onClick={() => adjustDimension('width', 20)}
                    className="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white rounded text-sm"
                    disabled={saving}
                  >
                    +20px
                  </button>
                </div>
                <div className="flex justify-center">
                  <button
                    onClick={autoFitWidth}
                    className="px-4 py-1 bg-yellow-600 hover:bg-yellow-700 text-white rounded text-sm font-medium"
                    disabled={saving}
                    title="Automatically adjust width to fit content"
                  >
                    🎯 Auto-Fit Width
                  </button>
                </div>
              </div>

              {/* Height Controls */}
              <div className="bg-gray-700 p-4 rounded border border-gray-600">
                <h4 className="font-medium text-white mb-3">Height</h4>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => adjustDimension('height', -10)}
                    className="px-3 py-1 bg-indigo-600 hover:bg-indigo-700 text-white rounded text-sm"
                    disabled={saving}
                  >
                    -10px
                  </button>
                  <button
                    onClick={() => adjustDimension('height', -2)}
                    className="px-3 py-1 bg-indigo-500 hover:bg-indigo-600 text-white rounded text-sm"
                    disabled={saving}
                  >
                    -2px
                  </button>
                  <button
                    onClick={() => adjustDimension('height', -1)}
                    className="px-3 py-1 bg-indigo-400 hover:bg-indigo-500 text-white rounded text-sm"
                    disabled={saving}
                  >
                    -1px
                  </button>
                  <span className="mx-2 text-gray-300 font-mono min-w-[60px] text-center">
                    {selectedElement.height || 'auto'}px
                  </span>
                  <button
                    onClick={() => adjustDimension('height', 1)}
                    className="px-3 py-1 bg-indigo-400 hover:bg-indigo-500 text-white rounded text-sm"
                    disabled={saving}
                  >
                    +1px
                  </button>
                  <button
                    onClick={() => adjustDimension('height', 2)}
                    className="px-3 py-1 bg-indigo-500 hover:bg-indigo-600 text-white rounded text-sm"
                    disabled={saving}
                  >
                    +2px
                  </button>
                  <button
                    onClick={() => adjustDimension('height', 10)}
                    className="px-3 py-1 bg-indigo-600 hover:bg-indigo-700 text-white rounded text-sm"
                    disabled={saving}
                  >
                    +10px
                  </button>
                </div>
              </div>

              {/* Font Size Controls */}
              {selectedElement.element_type === 'text' && (
                <div className="bg-gray-700 p-4 rounded border border-gray-600">
                  <h4 className="font-medium text-white mb-3">Font Size</h4>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => adjustFontSize(-2)}
                      className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm"
                      disabled={saving}
                    >
                      -2
                    </button>
                    <button
                      onClick={() => adjustFontSize(-1)}
                      className="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm"
                      disabled={saving}
                    >
                      -1
                    </button>
                    <span className="mx-2 text-gray-300 font-mono min-w-[40px] text-center">
                      {selectedElement.font_size}px
                    </span>
                    <button
                      onClick={() => adjustFontSize(1)}
                      className="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm"
                      disabled={saving}
                    >
                      +1
                    </button>
                    <button
                      onClick={() => adjustFontSize(2)}
                      className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm"
                      disabled={saving}
                    >
                      +2
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
