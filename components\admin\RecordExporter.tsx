import React, { useState } from 'react'
import { supabase } from '../../lib/supabase'

interface RecordExporterProps {
  type: 'commission' | 'withdrawal'
  onClose: () => void
}

export const RecordExporter: React.FC<RecordExporterProps> = ({ type, onClose }) => {
  const [loading, setLoading] = useState(false)
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
    endDate: new Date().toISOString().split('T')[0]
  })
  const [format, setFormat] = useState<'csv' | 'json'>('csv')

  const exportRecords = async () => {
    setLoading(true)
    try {
      let data: any[] = []
      
      if (type === 'commission') {
        const { data: records, error } = await supabase
          .from('commission_transactions')
          .select(`
            *,
            referrer:users!referrer_id(username, full_name, email),
            referred:users!referred_id(username, full_name, email)
          `)
          .gte('created_at', new Date(dateRange.startDate).toISOString())
          .lte('created_at', new Date(dateRange.endDate + 'T23:59:59').toISOString())
          .order('created_at', { ascending: false })

        if (error) throw error
        data = records || []
      } else {
        const { data: records, error } = await supabase
          .from('commission_withdrawal_requests')
          .select(`
            *,
            user:users!user_id(username, full_name, email)
          `)
          .gte('created_at', new Date(dateRange.startDate).toISOString())
          .lte('created_at', new Date(dateRange.endDate + 'T23:59:59').toISOString())
          .order('created_at', { ascending: false })

        if (error) throw error
        data = records || []
      }

      if (format === 'csv') {
        exportToCSV(data)
      } else {
        exportToJSON(data)
      }
    } catch (error) {
      console.error('Export error:', error)
      alert('Failed to export records. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const exportToCSV = (data: any[]) => {
    if (data.length === 0) {
      alert('No records found for the selected date range.')
      return
    }

    let csvContent = ''
    
    if (type === 'commission') {
      // CSV headers for commission records
      csvContent = 'Date,Referrer Username,Referrer Email,Referred Username,USDT Commission,Share Commission,Status,Purchase Amount\n'
      
      data.forEach(record => {
        csvContent += [
          new Date(record.created_at).toLocaleDateString(),
          record.referrer?.username || 'N/A',
          record.referrer?.email || 'N/A',
          record.referred?.username || 'N/A',
          record.usdt_commission || 0,
          record.share_commission || 0,
          record.status || 'N/A',
          record.share_purchase_amount || 0
        ].join(',') + '\n'
      })
    } else {
      // CSV headers for withdrawal records
      csvContent = 'Date,Username,Email,Amount,Network,User Wallet,Company Wallet,Transaction Hash,Proof of Payment,Status,Admin Notes\n'

      data.forEach(record => {
        csvContent += [
          new Date(record.created_at).toLocaleDateString(),
          record.user?.username || 'N/A',
          record.user?.email || 'N/A',
          record.withdrawal_amount || 0,
          record.network || 'N/A',
          record.wallet_address || 'N/A',
          record.company_wallet_address || 'N/A',
          record.transaction_hash || 'N/A',
          record.proof_of_payment ? 'Yes' : 'No',
          record.status || 'N/A',
          (record.admin_notes || 'N/A').replace(/,/g, ';') // Replace commas to avoid CSV issues
        ].join(',') + '\n'
      })
    }

    // Download CSV file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `${type}_records_${dateRange.startDate}_to_${dateRange.endDate}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const exportToJSON = (data: any[]) => {
    if (data.length === 0) {
      alert('No records found for the selected date range.')
      return
    }

    // Download JSON file
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `${type}_records_${dateRange.startDate}_to_${dateRange.endDate}.json`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: '#1F2937',
        borderRadius: '12px',
        padding: '32px',
        border: '1px solid #374151',
        maxWidth: '500px',
        width: '90%'
      }}>
        <h3 style={{
          fontSize: '20px',
          fontWeight: '600',
          color: '#F59E0B',
          marginBottom: '24px'
        }}>
          📊 Export {type === 'commission' ? 'Commission' : 'Withdrawal'} Records
        </h3>

        {/* Date Range */}
        <div style={{ marginBottom: '20px' }}>
          <label style={{
            display: 'block',
            fontSize: '14px',
            fontWeight: '600',
            color: '#E5E7EB',
            marginBottom: '8px'
          }}>
            Date Range
          </label>
          <div style={{ display: 'flex', gap: '12px' }}>
            <input
              type="date"
              value={dateRange.startDate}
              onChange={(e) => setDateRange({ ...dateRange, startDate: e.target.value })}
              style={{
                flex: 1,
                padding: '12px',
                backgroundColor: '#374151',
                border: '1px solid #4B5563',
                borderRadius: '8px',
                color: '#FFFFFF',
                fontSize: '14px'
              }}
            />
            <span style={{ color: '#9CA3AF', alignSelf: 'center' }}>to</span>
            <input
              type="date"
              value={dateRange.endDate}
              onChange={(e) => setDateRange({ ...dateRange, endDate: e.target.value })}
              style={{
                flex: 1,
                padding: '12px',
                backgroundColor: '#374151',
                border: '1px solid #4B5563',
                borderRadius: '8px',
                color: '#FFFFFF',
                fontSize: '14px'
              }}
            />
          </div>
        </div>

        {/* Format Selection */}
        <div style={{ marginBottom: '24px' }}>
          <label style={{
            display: 'block',
            fontSize: '14px',
            fontWeight: '600',
            color: '#E5E7EB',
            marginBottom: '8px'
          }}>
            Export Format
          </label>
          <select
            value={format}
            onChange={(e) => setFormat(e.target.value as 'csv' | 'json')}
            style={{
              width: '100%',
              padding: '12px',
              backgroundColor: '#374151',
              border: '1px solid #4B5563',
              borderRadius: '8px',
              color: '#FFFFFF',
              fontSize: '14px'
            }}
          >
            <option value="csv">CSV (Excel Compatible)</option>
            <option value="json">JSON (Raw Data)</option>
          </select>
        </div>

        {/* Action Buttons */}
        <div style={{
          display: 'flex',
          gap: '12px',
          justifyContent: 'flex-end'
        }}>
          <button
            onClick={onClose}
            disabled={loading}
            style={{
              padding: '12px 24px',
              backgroundColor: '#6B7280',
              color: '#FFFFFF',
              border: 'none',
              borderRadius: '8px',
              fontSize: '14px',
              fontWeight: '600',
              cursor: loading ? 'not-allowed' : 'pointer',
              opacity: loading ? 0.7 : 1
            }}
          >
            Cancel
          </button>
          <button
            onClick={exportRecords}
            disabled={loading}
            style={{
              padding: '12px 24px',
              backgroundColor: '#F59E0B',
              color: '#000000',
              border: 'none',
              borderRadius: '8px',
              fontSize: '14px',
              fontWeight: '600',
              cursor: loading ? 'not-allowed' : 'pointer',
              opacity: loading ? 0.7 : 1
            }}
          >
            {loading ? 'Exporting...' : '📊 Export Records'}
          </button>
        </div>
      </div>
    </div>
  )
}

export default RecordExporter
