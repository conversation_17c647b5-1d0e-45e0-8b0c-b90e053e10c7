import React, { useState, useEffect } from 'react';
import { validatePasswordStrength, getPasswordStrengthScore } from '../../lib/passwordSecurity';

interface SecurePasswordChangeFormProps {
  userId: number;
  userEmail: string;
  onPasswordChanged?: () => void;
  onClose?: () => void;
}

export const SecurePasswordChangeForm: React.FC<SecurePasswordChangeFormProps> = ({
  userId,
  userEmail,
  onPasswordChanged,
  onClose
}) => {
  const [step, setStep] = useState<'password' | 'pin'>('password');
  const [newPassword, setNewPassword] = useState('');
  const [pin, setPin] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);
  const [showPassword, setShowPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState({
    score: 0,
    label: '',
    color: ''
  });

  // Cooldown timer effect
  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => {
        setResendCooldown(resendCooldown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  const handlePasswordChange = (value: string) => {
    setNewPassword(value);
    
    // Update password strength
    const score = getPasswordStrengthScore(value);
    setPasswordStrength({
      score,
      label: score >= 80 ? 'Strong' : score >= 60 ? 'Good' : score >= 40 ? 'Fair' : 'Weak',
      color: score >= 80 ? 'green' : score >= 60 ? 'yellow' : score >= 40 ? 'orange' : 'red'
    });

    // Clear error when user starts typing
    if (error) setError('');
  };

  const handleRequestPIN = async () => {
    if (!newPassword.trim()) {
      setError('Please enter a new password');
      return;
    }

    // Validate password strength
    const validation = validatePasswordStrength(newPassword);
    if (!validation.valid) {
      setError(validation.errors[0]);
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Send PIN via API
      const response = await fetch('/api/password-change-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          email: userEmail,
          newPassword
        }),
      });

      const result = await response.json();

      if (result.success) {
        setStep('pin');
        setResendCooldown(60); // Start 60-second cooldown
      } else {
        setError(result.message || 'Failed to send verification PIN');

        // Handle rate limiting
        if (response.status === 429) {
          const waitTime = result.message.match(/(\d+) seconds/);
          if (waitTime) {
            setResendCooldown(parseInt(waitTime[1]));
          }
        }
      }
    } catch (error: any) {
      console.error('Error sending PIN:', error);
      setError('Failed to send verification PIN. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyPIN = async () => {
    if (!pin.trim()) {
      setError('Please enter the PIN code');
      return;
    }

    if (pin.length !== 6) {
      setError('PIN must be 6 digits');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Verify PIN and change password via API
      const response = await fetch('/api/password-change-verification', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          email: userEmail,
          pin
        }),
      });

      const result = await response.json();

      if (result.success) {
        setSuccess(true);

        if (onPasswordChanged) {
          onPasswordChanged();
        }

        // Auto-close after 3 seconds
        setTimeout(() => {
          if (onClose) {
            onClose();
          }
        }, 3000);
      } else {
        setError(result.message || 'Invalid or expired PIN');
      }

    } catch (error: any) {
      console.error('Password change error:', error);
      setError('Failed to change password. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleResendPIN = async () => {
    setLoading(true);
    setError('');

    try {
      // Resend PIN via API (same as initial request)
      const response = await fetch('/api/password-change-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          email: userEmail,
          newPassword
        }),
      });

      const result = await response.json();

      if (result.success) {
        setError(''); // Clear any previous errors
        setResendCooldown(60); // Start 60-second cooldown
        // You could show a success message here if needed
      } else {
        setError(result.message || 'Failed to resend PIN');

        // Handle rate limiting
        if (response.status === 429) {
          const waitTime = result.message.match(/(\d+) seconds/);
          if (waitTime) {
            setResendCooldown(parseInt(waitTime[1]));
          }
        }
      }
    } catch (error: any) {
      console.error('Error resending PIN:', error);
      setError('Failed to resend PIN. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getPasswordStrengthColor = () => {
    switch (passwordStrength.color) {
      case 'green': return 'bg-green-500';
      case 'yellow': return 'bg-yellow-500';
      case 'orange': return 'bg-orange-500';
      case 'red': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  if (success) {
    return (
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <div className="text-center">
          <div className="bg-green-900/50 border border-green-500 text-green-400 px-4 py-3 rounded-lg mb-4">
            ✅ Password changed successfully!
          </div>
          <p className="text-gray-400">Your password has been updated securely.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-white">Change Password</h3>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            ✕
          </button>
        )}
      </div>

      {error && (
        <div className="bg-red-900/50 border border-red-500 text-red-400 px-4 py-3 rounded-lg mb-4">
          ❌ {error}
        </div>
      )}

      {step === 'password' && (
        <div className="space-y-4">
          <p className="text-gray-400 text-sm">
            Enter your new password. We'll send a verification PIN to your email to confirm the change.
          </p>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              New Password
            </label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                value={newPassword}
                onChange={(e) => handlePasswordChange(e.target.value)}
                className="w-full px-4 py-3 pr-12 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-amber-500 transition-colors"
                placeholder="Enter your new password"
                disabled={loading}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                disabled={loading}
              >
                {showPassword ? '👁️' : '👁️‍🗨️'}
              </button>
            </div>

            {/* Password Strength Indicator */}
            {newPassword && (
              <div className="mt-2">
                <div className="flex items-center justify-between text-sm mb-1">
                  <span className="text-gray-400">Password Strength</span>
                  <span className={`font-medium ${
                    passwordStrength.color === 'green' ? 'text-green-400' :
                    passwordStrength.color === 'yellow' ? 'text-yellow-400' :
                    passwordStrength.color === 'orange' ? 'text-orange-400' :
                    'text-red-400'
                  }`}>
                    {passwordStrength.label}
                  </span>
                </div>
                <div className="w-full bg-gray-600 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${getPasswordStrengthColor()}`}
                    style={{ width: `${passwordStrength.score}%` }}
                  ></div>
                </div>
              </div>
            )}
          </div>

          <button
            onClick={handleRequestPIN}
            disabled={loading || !newPassword || passwordStrength.score < 60}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-colors"
          >
            {loading ? 'Sending PIN...' : 'Send Verification PIN'}
          </button>
        </div>
      )}

      {step === 'pin' && (
        <div className="space-y-4">
          <div className="bg-blue-900/50 border border-blue-500 text-blue-400 px-4 py-3 rounded-lg">
            📧 We've sent a verification PIN to your email address. Please enter it below to confirm your password change.
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Verification PIN
            </label>
            <input
              type="text"
              value={pin}
              onChange={(e) => {
                const value = e.target.value.replace(/\D/g, '').slice(0, 6);
                setPin(value);
                if (error) setError('');
              }}
              className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white text-center text-lg tracking-widest placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-amber-500 transition-colors"
              placeholder="000000"
              maxLength={6}
              disabled={loading}
            />
          </div>

          <div className="flex space-x-3">
            <button
              onClick={handleVerifyPIN}
              disabled={loading || pin.length !== 6}
              className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-colors"
            >
              {loading ? 'Verifying...' : 'Verify PIN & Change Password'}
            </button>
            
            <button
              onClick={handleResendPIN}
              disabled={loading || resendCooldown > 0}
              className="bg-gray-600 hover:bg-gray-700 disabled:bg-gray-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
            >
              {resendCooldown > 0 ? `Resend PIN (${resendCooldown}s)` : 'Resend PIN'}
            </button>
          </div>

          <button
            onClick={() => {
              setStep('password');
              setPin('');
              setError('');
            }}
            className="w-full text-gray-400 hover:text-white text-sm transition-colors"
          >
            ← Back to password entry
          </button>
        </div>
      )}
    </div>
  );
};
