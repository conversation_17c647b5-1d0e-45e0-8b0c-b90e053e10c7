import React, { useState, useEffect } from 'react'
import { registerUserProgressive, supabase } from '../lib/supabase'
import { validatePasswordStrength } from '../lib/passwordSecurity'
import { realtimeValidation, ValidationResult } from '../lib/realtimeValidation'
import { ValidationInput } from './ValidationFeedback'
import { API_BASE_URL } from '../constants'
import { applyReferralToRegistration, clearReferralData } from '../lib/referralPersistence'

interface EmailRegistrationFormProps {
  onRegistrationSuccess: (user: any) => void
  onSwitchToLogin: () => void
  onSwitchToTelegram?: () => void
  userType?: 'shareholder' | 'affiliate' // New prop to determine registration type
  defaultSponsor?: string // Add defaultSponsor prop
}

// Progressive form steps
type RegistrationStep = 'email' | 'username' | 'complete'

export const EmailRegistrationFormProgressive: React.FC<EmailRegistrationFormProps> = ({
  onRegistrationSuccess,
  onSwitchToLogin,
  onSwitchToTelegram,
  userType = 'affiliate', // Default to affiliate for backward compatibility
  defaultSponsor
}) => {
  // Progressive form state
  const [currentStep, setCurrentStep] = useState<RegistrationStep>('email')
  const [registrationMode, setRegistrationMode] = useState<'new' | 'telegram'>('new')
  const [generalError, setGeneralError] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Simplified form data (removed phone, country, telegram, fullName fields)
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    username: '',
    sponsorUsername: defaultSponsor || '',
    campaignSource: ''
  })

  // New state for sponsor assignment flow
  const [hasSponsor, setHasSponsor] = useState<boolean | null>(null)
  const [showSponsorQuestion, setShowSponsorQuestion] = useState(true)

  // Email verification state
  const [emailVerification, setEmailVerification] = useState({
    isVerified: false,
    isModalOpen: false,
    tempUserId: null as number | null,
    isEmailValid: false,
    verificationCode: undefined as string | undefined,
    codeExpiry: undefined as number | undefined
  })

  // Real-time validation states
  const [emailValidation, setEmailValidation] = useState<ValidationResult | null>(null)
  const [usernameValidation, setUsernameValidation] = useState<ValidationResult | null>(null)

  // Password visibility states (default to visible)
  const [showPassword, setShowPassword] = useState(true)
  const [showConfirmPassword, setShowConfirmPassword] = useState(true)
  const [sponsorValidation, setSponsorValidation] = useState<ValidationResult | null>(null)
  const [errors, setErrors] = useState<{ [key: string]: string }>({})

  // Validate default sponsor on component mount and handle sponsor question logic
  useEffect(() => {
    if (defaultSponsor && userType === 'affiliate') {
      // If there's a default sponsor (from referral link), skip the sponsor question
      setHasSponsor(true)
      setShowSponsorQuestion(false)
      realtimeValidation.validateSponsorUsername(defaultSponsor, setSponsorValidation, formData.username)
    } else if (userType === 'shareholder') {
      // For shareholders, skip the sponsor question and auto-assign AUREUS
      setHasSponsor(false)
      setShowSponsorQuestion(false)
    }
  }, [defaultSponsor, userType])

  // Apply stored referral data on component mount
  useEffect(() => {
    const referralData = applyReferralToRegistration()

    if (referralData.sponsorUsername) {
      console.log('✅ Applying stored referral data:', referralData)

      setFormData(prev => ({
        ...prev,
        sponsorUsername: referralData.sponsorUsername || '',
        campaignSource: referralData.campaignSource || ''
      }))

      // If there's referral data, skip the sponsor question
      setHasSponsor(true)
      setShowSponsorQuestion(false)

      // Validate the sponsor username if it's for affiliate registration
      if (userType === 'affiliate' && referralData.sponsorUsername) {
        realtimeValidation.validateSponsorUsername(referralData.sponsorUsername, setSponsorValidation, formData.username)
      }
    }
  }, [userType])

  // Handle sponsor question response
  const handleSponsorQuestion = (answer: boolean) => {
    setHasSponsor(answer)
    setShowSponsorQuestion(false)

    if (!answer) {
      // If user doesn't have a sponsor, auto-assign AUREUS
      setFormData(prev => ({
        ...prev,
        sponsorUsername: 'AUREUS'
      }))
      // Validate AUREUS automatically
      realtimeValidation.validateSponsorUsername('AUREUS', setSponsorValidation, formData.username)
    } else {
      // Clear any auto-assigned sponsor if user says they have one
      setFormData(prev => ({
        ...prev,
        sponsorUsername: ''
      }))
      setSponsorValidation(null)
    }
  }

  // Handle input changes with real-time validation
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))

    // Clear errors when user starts typing
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[name]
        return newErrors
      })
    }

    // Real-time validation for email
    if (name === 'email') {
      realtimeValidation.validateEmail(value, setEmailValidation)
      setEmailVerification(prev => ({
        ...prev,
        isEmailValid: /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value.trim())
      }))
    }

    // Real-time validation for username
    if (name === 'username') {
      realtimeValidation.validateUsername(value, setUsernameValidation)
    }

    // Real-time validation for sponsor username - only when user has indicated they have a sponsor
    if (hasSponsor && name === 'sponsorUsername' && value.trim().length > 0) {
      realtimeValidation.validateSponsorUsername(value.trim(), setSponsorValidation, formData.username)
    } else if (name === 'sponsorUsername') {
      setSponsorValidation(null)
    }
  }

  // Handle email verification
  const handleEmailVerification = async (email: string) => {
    try {
      setGeneralError('')
      console.log('🔍 Starting email verification process for:', email)

      // Validate email format
      const emailValueRaw = email.trim()
      if (!emailValueRaw || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValueRaw)) {
        setErrors(prev => ({ ...prev, email: 'Please enter a valid email address' }))
        return
      }

      // Check for duplicates
      const resp = await fetch(`${API_BASE_URL}/api/check-duplicates`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: emailValueRaw.toLowerCase() })
      })
      const dup = await resp.json()
      if (resp.ok && dup.emailDuplicate) {
        setErrors(prev => ({ ...prev, email: 'This email address is already registered. Please use a different email or try logging in.' }))
        return
      }

      // Generate verification code
      const verificationCode = Math.floor(100000 + Math.random() * 900000).toString()
      setEmailVerification(prev => ({
        ...prev,
        tempUserId: null,
        isModalOpen: true,
        verificationCode,
        codeExpiry: Date.now() + (15 * 60 * 1000) // 15 minutes
      }))

      // Send verification email
      const response = await fetch(`${API_BASE_URL}/api/send-verification-email`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: email,
          code: verificationCode,
          purpose: 'registration',
          userName: 'User',
          expiryMinutes: 15
        }),
      })

      const responseData = await response.json()
      if (!response.ok) {
        console.error('❌ API Error:', responseData)
        setGeneralError(`Failed to send verification email: ${responseData.error || 'Server error'}`)
        return
      }

      console.log('✅ Verification email sent successfully')
    } catch (error) {
      console.error('Email verification error:', error)
      setGeneralError(`Failed to send verification email: ${error instanceof Error ? error.message : 'Network error'}`)
    }
  }

  // Handle email verification success
  const handleEmailVerificationSuccess = () => {
    setEmailVerification(prev => ({
      ...prev,
      isVerified: true,
      isModalOpen: false,
      tempUserId: null,
      verificationCode: undefined,
      codeExpiry: undefined
    }))
    // Advance to username step after email verification
    setCurrentStep('username')
  }

  // Handle username validation completion
  const handleUsernameValidated = () => {
    // Advance to complete step after username is validated
    setCurrentStep('complete')
  }

  // Handle verification code submission
  const handleVerificationCodeSubmit = async (code: string) => {
    if (!emailVerification.verificationCode || !emailVerification.codeExpiry) {
      setGeneralError('No verification code found. Please request a new one.')
      return
    }

    if (Date.now() > emailVerification.codeExpiry) {
      setGeneralError('Verification code has expired. Please request a new one.')
      return
    }

    if (code === emailVerification.verificationCode) {
      handleEmailVerificationSuccess()
    } else {
      setGeneralError('Invalid verification code. Please try again.')
    }
  }

  const handleEmailVerificationClose = () => {
    setEmailVerification(prev => ({
      ...prev,
      isModalOpen: false,
      tempUserId: null,
      verificationCode: undefined,
      codeExpiry: undefined
    }))
  }

  // Form validation
  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {}

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format'
    } else if (!emailVerification.isVerified) {
      newErrors.email = 'Please verify your email address first'
    }

    // Username validation
    if (!formData.username.trim()) {
      newErrors.username = 'Username is required'
    } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username) || formData.username.length < 2) {
      newErrors.username = 'Username must be at least 2 characters and contain only letters, numbers, and underscores'
    }



    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else {
      const passwordValidation = validatePasswordStrength(formData.password)
      if (!passwordValidation.valid) {
        newErrors.password = passwordValidation.errors[0]
      }
    }

    // Confirm password validation
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match'
    }

    // Sponsor username validation - only required when user has indicated they have a sponsor
    if (hasSponsor) {
      if (!formData.sponsorUsername.trim()) {
        newErrors.sponsorUsername = 'Please enter your sponsor\'s username'
      } else if (sponsorValidation && !sponsorValidation.isValid) {
        newErrors.sponsorUsername = 'Please enter a valid sponsor username'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setGeneralError('')

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      // Register user with simplified progressive registration
      const userData = {
        email: formData.email.toLowerCase().trim(),
        password: formData.password,
        confirmPassword: formData.confirmPassword,
        username: formData.username.toLowerCase().trim(),
        sponsorUsername: hasSponsor
          ? formData.sponsorUsername.toLowerCase().trim()
          : 'AUREUS', // Auto-assign AUREUS for users without sponsors
        campaignSource: formData.campaignSource || (hasSponsor ? 'affiliate-registration' : 'direct-registration'),
        userType: userType // Add user type to registration data
      }

      const result = await registerUserProgressive(userData)

      if (result.success && result.user) {
        console.log('✅ Registration successful:', result.user)

        // Clear referral data after successful registration
        clearReferralData()

        onRegistrationSuccess(result.user)
      } else {
        // Ensure error is a string, not an object
        const errorMessage = typeof result.error === 'string'
          ? result.error
          : result.error?.message || 'Registration failed. Please try again.'
        setGeneralError(errorMessage)
      }
    } catch (error) {
      console.error('❌ Registration error:', error)
      setGeneralError('Registration failed. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <>
      <div className="registration-form">
      {/* Progress Indicator */}
      <div className="progress-indicator">
        <div className={`progress-step ${currentStep === 'email' ? 'active' : currentStep === 'username' || currentStep === 'complete' ? 'completed' : ''}`}>
          <div className="progress-step-circle">
            {currentStep === 'username' || currentStep === 'complete' ? '✓' : '1'}
          </div>
          <span className="progress-step-label">Email</span>
        </div>
        <div className={`progress-connector ${currentStep === 'username' || currentStep === 'complete' ? 'completed' : ''}`}></div>
        <div className={`progress-step ${currentStep === 'username' ? 'active' : currentStep === 'complete' ? 'completed' : ''}`}>
          <div className="progress-step-circle">
            {currentStep === 'complete' ? '✓' : '2'}
          </div>
          <span className="progress-step-label">Username</span>
        </div>
        <div className={`progress-connector ${currentStep === 'complete' ? 'completed' : ''}`}></div>
        <div className={`progress-step ${currentStep === 'complete' ? 'active' : ''}`}>
          <div className="progress-step-circle">3</div>
          <span className="progress-step-label">Complete</span>
        </div>
      </div>




        {/* Progressive Form Steps */}
        <form onSubmit={handleSubmit} className="form">
          {/* Step 1: Email Validation */}
          {currentStep === 'email' && (
            <div className="form-section">
              <div className="form-header">
                <h4 className="form-step-title">Step 1: Email Verification</h4>
                <p className="form-step-subtitle">Enter your email address to get started</p>
              </div>

              <div className="form-group">
                <label htmlFor="email" className="form-label">
                  Email Address <span style={{color: 'var(--error)'}}>*</span>
                </label>
                <ValidationInput
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  validation={emailValidation}
                  placeholder="Enter your email address"
                  autoComplete="email"
                  className="form-input"
                />

                {/* Send Code Button */}
                {registrationMode === 'new' && emailVerification.isEmailValid && !emailVerification.isVerified && (
                  <div className="form-button-container">
                    <button
                      type="button"
                      onClick={() => handleEmailVerification(formData.email)}
                      className="btn btn-primary"
                      disabled={
                        !emailVerification.isEmailValid ||
                        !!errors.email ||
                        (emailValidation && (!emailValidation.isValid || emailValidation.isChecking))
                      }
                    >
                      Send Verification Code
                    </button>
                  </div>
                )}

                {registrationMode === 'new' && emailVerification.isEmailValid && !emailVerification.isVerified && (
                  <p className="form-help-text">
                    📧 Click "Send Verification Code" to receive a 6-digit code via email
                  </p>
                )}

                {registrationMode === 'new' && emailVerification.isVerified && (
                  <p className="form-success-text">
                    ✅ Email verified successfully! You can now proceed to the next step.
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Step 2: Username Selection */}
          {currentStep === 'username' && (
            <div className="form-section">
              <div className="form-header">
                <h4 className="form-step-title">Step 2: Choose Username</h4>
                <p className="form-step-subtitle">Select a unique username for your account</p>
              </div>

              <div className="form-group">
                <label htmlFor="username" className="form-label">
                  Username <span style={{color: 'var(--error)'}}>*</span>
                </label>
                <ValidationInput
                  id="username"
                  name="username"
                  type="text"
                  value={formData.username}
                  onChange={handleInputChange}
                  validation={usernameValidation}
                  placeholder="Enter your username"
                  autoComplete="username"
                  className="form-input"
                />

                {/* Continue Button */}
                {usernameValidation && usernameValidation.isValid && (
                  <div className="form-button-container">
                    <button
                      type="button"
                      onClick={handleUsernameValidated}
                      className="btn btn-primary"
                    >
                      Continue to Final Step
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Step 3: Complete Registration */}
          {currentStep === 'complete' && (
            <div className="form-section">
              <div className="form-header">
                <h4 className="form-step-title">Step 3: Complete Registration</h4>
                <p className="form-step-subtitle">Create your password and link to your sponsor</p>
              </div>

              {/* Password */}
              <div className="form-group">
                <label htmlFor="password" className="form-label">
                  Password <span style={{color: 'var(--error)'}}>*</span>
                </label>
                <div className="password-input-container">
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    value={formData.password}
                    onChange={handleInputChange}
                    className="form-input password-input"
                    placeholder="Create a strong password"
                    autoComplete="new-password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="password-toggle-btn"
                  >
                    {showPassword ? (
                      <svg className="password-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 11-4.243-4.243m4.242 4.242L9.88 9.88" />
                      </svg>
                    ) : (
                      <svg className="password-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="form-error-text">{errors.password}</p>
                )}
              </div>

              {/* Confirm Password */}
              <div className="form-group">
                <label htmlFor="confirmPassword" className="form-label">
                  Confirm Password <span style={{color: 'var(--error)'}}>*</span>
                </label>
                <div className="password-input-container">
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className="form-input password-input"
                    placeholder="Confirm your password"
                    autoComplete="new-password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="password-toggle-btn"
                  >
                    {showConfirmPassword ? (
                      <svg className="password-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 11-4.243-4.243m4.242 4.242L9.88 9.88" />
                      </svg>
                    ) : (
                      <svg className="password-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    )}
                  </button>
                </div>
                {errors.confirmPassword && (
                  <p className="form-error-text">{errors.confirmPassword}</p>
                )}
              </div>

              {/* Sponsor Question Step */}
              {showSponsorQuestion && (
                <div className="form-group">
                  <label className="form-label">
                    Were you introduced to Aureus Alliance Holdings by someone?
                  </label>
                  <div className="sponsor-question-buttons">
                    <button
                      type="button"
                      onClick={() => handleSponsorQuestion(true)}
                      className="sponsor-question-btn sponsor-question-yes"
                    >
                      <span className="btn-icon">👥</span>
                      <span className="btn-text">
                        <strong>Yes</strong>
                        <small>I have a sponsor/referrer</small>
                      </span>
                    </button>
                    <button
                      type="button"
                      onClick={() => handleSponsorQuestion(false)}
                      className="sponsor-question-btn sponsor-question-no"
                    >
                      <span className="btn-icon">🔍</span>
                      <span className="btn-text">
                        <strong>No</strong>
                        <small>I found Aureus on my own</small>
                      </span>
                    </button>
                  </div>
                  <p className="form-help-text">
                    This helps us connect you with the right support and track referrals properly.
                  </p>
                </div>
              )}

              {/* Sponsor Username Input - Only show when user has a sponsor */}
              {!showSponsorQuestion && hasSponsor && (
                <div className="form-group">
                  <label className="form-label" style={{color: 'var(--gold)', fontWeight: 'bold'}}>
                    Sponsor Username <span style={{color: 'var(--error)'}}>*</span>
                  </label>
                  {defaultSponsor ? (
                    <div>
                      <div className="sponsor-display">
                        {formData.sponsorUsername}
                        {sponsorValidation && sponsorValidation.isValid && (
                          <span className="sponsor-verified">✓</span>
                        )}
                      </div>
                      {sponsorValidation && sponsorValidation.isValid && (
                        <div className="sponsor-status-success">
                          ✓ Sponsor username found
                        </div>
                      )}
                      <p className="form-help-text">
                        You are registering through this sponsor's referral link
                      </p>
                    </div>
                  ) : (
                    <div>
                      <ValidationInput
                        id="sponsorUsername"
                        name="sponsorUsername"
                        type="text"
                        value={formData.sponsorUsername}
                        onChange={handleInputChange}
                        validation={sponsorValidation}
                        placeholder="Enter your sponsor's username"
                        className="form-input"
                      />
                      <p className="form-help-text">
                        Enter the username of the person who introduced you to Aureus Alliance Holdings
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* No Sponsor Confirmation - Show when user selected "No" */}
              {!showSponsorQuestion && !hasSponsor && (
                <div className="form-group">
                  <div className="no-sponsor-confirmation">
                    <div className="confirmation-icon">✅</div>
                    <div className="confirmation-text">
                      <strong>No problem!</strong>
                      <p>You'll be automatically connected to our founder's support network for guidance and assistance.</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Submit Button */}
              <div className="form-button-container">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="btn btn-primary btn-lg"
                  style={{width: '100%'}}
                >
                  {isSubmitting ? (
                    <div className="btn-loading">
                      <div className="loading-spinner"></div>
                      <span>Creating Account...</span>
                    </div>
                  ) : (
                    <span>Create Account</span>
                  )}
                </button>
              </div>
            </div>
          )}
        </form>

        {/* General Error Display */}
        {generalError && (
          <div className="alert alert-error">
            <div className="alert-content">
              <span className="alert-icon">⚠️</span>
              <p>{generalError}</p>
            </div>
          </div>
        )}

        {/* Switch to Login */}
        <div className="form-footer">
          <p style={{color: 'var(--text-muted)', marginBottom: 'var(--space-lg)'}}>Already have an account?</p>
          <button
            onClick={onSwitchToLogin}
            className="link-button"
          >
            Sign in instead
          </button>
        </div>

      {/* Email Verification Modal */}
      {emailVerification.isModalOpen && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3 className="modal-title">
                📧 Verify Your Email Address
              </h3>
            </div>
            <div className="modal-body">
              <p className="modal-text">
                We've sent a 6-digit verification code to <strong>{formData.email}</strong>.
                Please enter it below to continue with your registration.
              </p>

              {/* Show general error if any */}
              {generalError && (
                <div className="alert alert-error">
                  <div className="alert-content">
                    <span className="alert-icon">⚠️</span>
                    <p>{generalError}</p>
                  </div>
                </div>
              )}

              <form onSubmit={(e) => {
              e.preventDefault()
              const code = (e.target as HTMLFormElement).code.value
              if (code.length === 6) {
                handleVerificationCodeSubmit(code)
              }
            }} className="form">
              <div className="form-group">
                <label className="form-label">
                  Verification Code
                </label>
                <input
                  name="code"
                  type="text"
                  placeholder="Enter 6-digit code"
                  className="form-input verification-code-input"
                  maxLength={6}
                  autoComplete="off"
                  onChange={(e) => {
                    const value = e.target.value.replace(/\D/g, '').slice(0, 6)
                    e.target.value = value
                  }}
                />
              </div>

              <div className="modal-buttons">
                <button
                  type="button"
                  onClick={handleEmailVerificationClose}
                  className="btn btn-secondary"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={() => handleEmailVerification(formData.email)}
                  className="btn btn-outline"
                >
                  Resend
                </button>
                <button
                  type="submit"
                  className="btn btn-primary"
                >
                  Verify
                </button>
              </div>
            </form>
            </div>
          </div>
        </div>
      )}
    </div>

    <style jsx>{`
      .sponsor-display {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
        background: var(--surface-secondary, #2a2a2a);
        border: 2px solid var(--border-color, #444);
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary, #fff);
        margin-bottom: 8px;
      }

      .sponsor-verified {
        color: var(--success, #10b981);
        font-size: 18px;
        font-weight: bold;
      }

      .sponsor-status-success {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 8px 12px;
        background: var(--success-bg, rgba(16, 185, 129, 0.1));
        border: 1px solid var(--success, #10b981);
        border-radius: 6px;
        color: var(--success, #10b981);
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 8px;
      }

      .sponsor-question-buttons {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-bottom: 16px;
      }

      .sponsor-question-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
        padding: 20px 16px;
        background: var(--surface-secondary, #2a2a2a);
        border: 2px solid var(--border-color, #444);
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        min-height: 120px;
      }

      .sponsor-question-btn:hover {
        border-color: var(--primary, #ffc107);
        background: var(--surface-hover, #333);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(255, 193, 7, 0.2);
      }

      .sponsor-question-yes:hover {
        border-color: var(--success, #10b981);
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
      }

      .sponsor-question-no:hover {
        border-color: var(--info, #3b82f6);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
      }

      .btn-icon {
        font-size: 32px;
        margin-bottom: 8px;
      }

      .btn-text {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }

      .btn-text strong {
        color: var(--text-primary, #fff);
        font-size: 16px;
        font-weight: 600;
      }

      .btn-text small {
        color: var(--text-muted, #888);
        font-size: 13px;
        font-weight: 400;
        line-height: 1.3;
      }

      .no-sponsor-confirmation {
        display: flex;
        align-items: center;
        gap: 16px;
        padding: 20px;
        background: var(--success-bg, rgba(16, 185, 129, 0.1));
        border: 2px solid var(--success, #10b981);
        border-radius: 12px;
        margin-bottom: 16px;
      }

      .confirmation-icon {
        font-size: 32px;
        flex-shrink: 0;
      }

      .confirmation-text strong {
        color: var(--success, #10b981);
        font-size: 16px;
        font-weight: 600;
        display: block;
        margin-bottom: 4px;
      }

      .confirmation-text p {
        color: var(--text-primary, #fff);
        font-size: 14px;
        line-height: 1.4;
        margin: 0;
      }

      @media (max-width: 768px) {
        .sponsor-question-buttons {
          grid-template-columns: 1fr;
          gap: 12px;
        }

        .sponsor-question-btn {
          min-height: 100px;
          padding: 16px 12px;
        }

        .btn-icon {
          font-size: 28px;
        }

        .no-sponsor-confirmation {
          flex-direction: column;
          text-align: center;
          gap: 12px;
        }
      }
    `}</style>
    </>
  )
}


