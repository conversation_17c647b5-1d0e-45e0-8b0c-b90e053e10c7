import React, { useState, useEffect } from 'react'
import { supabase } from '../../lib/supabase'
import { logAdminAction } from '../../lib/adminAuth'
import * as XLSX from 'xlsx'
import {
  saveReportHistory,
  getRecentReports,
  generateReportFilename,
  validateReportFilters,
  formatFileSize,
  ReportHistoryEntry
} from '../../lib/reportUtils'

interface User {
  id: number
  username: string
  full_name: string | null
  email: string
}

interface ReportFilters {
  startDate: string
  endDate: string
  userId: string // 'all' or specific user ID
  transactionTypes: {
    purchases: boolean
    withdrawals: boolean
    conversions: boolean
    commissions: boolean
  }
  statusFilter: string // 'all', 'completed', 'pending', 'failed'
}

interface ShareLedgerReportSystemProps {
  currentUser?: any
}

export const ShareLedgerReportSystem: React.FC<ShareLedgerReportSystemProps> = ({
  currentUser
}) => {
  const [loading, setLoading] = useState(false)
  const [users, setUsers] = useState<User[]>([])
  const [recentReports, setRecentReports] = useState<ReportHistoryEntry[]>([])
  const [generatingReport, setGeneratingReport] = useState(false)

  const [filters, setFilters] = useState<ReportFilters>({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
    endDate: new Date().toISOString().split('T')[0],
    userId: 'all',
    transactionTypes: {
      purchases: true,
      withdrawals: true,
      conversions: true,
      commissions: true
    },
    statusFilter: 'all'
  })

  useEffect(() => {
    loadUsers()
    // loadRecentReports() - temporarily disabled
  }, [])

  const loadUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, username, full_name, email')
        .order('username')

      if (error) throw error
      setUsers(data || [])
    } catch (error) {
      console.error('Error loading users:', error)
    }
  }

  const loadRecentReports = async () => {
    try {
      // Temporarily disabled until report_history table is created
      console.log('Report history temporarily disabled - table does not exist')
      setRecentReports([])
    } catch (error) {
      console.error('Error loading recent reports:', error)
      setRecentReports([])
    }
  }

  const setPresetDateRange = (preset: string) => {
    const now = new Date()
    let startDate: Date

    switch (preset) {
      case 'last7days':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case 'last30days':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case 'last3months':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      case 'last6months':
        startDate = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000)
        break
      case 'lastyear':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
        break
      default:
        return
    }

    setFilters({
      ...filters,
      startDate: startDate.toISOString().split('T')[0],
      endDate: now.toISOString().split('T')[0]
    })
  }

  const fetchAllTransactionData = async () => {
    const startDateTime = new Date(filters.startDate + 'T00:00:00Z').toISOString()
    const endDateTime = new Date(filters.endDate + 'T23:59:59Z').toISOString()
    
    const allTransactions: any[] = []

    try {
      // 1. Fetch Share Purchases
      if (filters.transactionTypes.purchases) {
        let purchaseQuery = supabase
          .from('aureus_share_purchases')
          .select(`
            id,
            user_id,
            package_name,
            shares_purchased,
            total_amount,
            commission_used,
            remaining_payment,
            payment_method,
            status,
            created_at,
            updated_at,
            users!inner(id, username, full_name, email)
          `)
          .gte('created_at', startDateTime)
          .lte('created_at', endDateTime)

        if (filters.userId !== 'all') {
          purchaseQuery = purchaseQuery.eq('user_id', parseInt(filters.userId))
        }

        if (filters.statusFilter !== 'all') {
          purchaseQuery = purchaseQuery.eq('status', filters.statusFilter)
        }

        const { data: purchases, error: purchaseError } = await purchaseQuery

        if (purchaseError) throw purchaseError

        purchases?.forEach(purchase => {
          allTransactions.push({
            date: purchase.created_at,
            type: 'Share Purchase',
            userId: purchase.user_id,
            username: purchase.users.username,
            userFullName: purchase.users.full_name,
            userEmail: purchase.users.email,
            amount: purchase.total_amount,
            shares: purchase.shares_purchased,
            status: purchase.status,
            referenceId: purchase.id,
            description: `${purchase.package_name} - ${purchase.shares_purchased} shares`,
            paymentMethod: purchase.payment_method,
            commissionUsed: purchase.commission_used,
            remainingPayment: purchase.remaining_payment
          })
        })
      }

      // 2. Fetch Commission Withdrawals
      if (filters.transactionTypes.withdrawals) {
        let withdrawalQuery = supabase
          .from('commission_withdrawal_requests')
          .select(`
            id,
            user_id,
            withdrawal_amount,
            wallet_address,
            network,
            currency,
            status,
            admin_notes,
            transaction_hash,
            processed_at,
            created_at,
            users!inner(id, username, full_name, email)
          `)
          .gte('created_at', startDateTime)
          .lte('created_at', endDateTime)

        if (filters.userId !== 'all') {
          withdrawalQuery = withdrawalQuery.eq('user_id', parseInt(filters.userId))
        }

        if (filters.statusFilter !== 'all') {
          withdrawalQuery = withdrawalQuery.eq('status', filters.statusFilter)
        }

        const { data: withdrawals, error: withdrawalError } = await withdrawalQuery

        if (withdrawalError) throw withdrawalError

        withdrawals?.forEach(withdrawal => {
          allTransactions.push({
            date: withdrawal.created_at,
            type: 'Commission Withdrawal',
            userId: withdrawal.user_id,
            username: withdrawal.users.username,
            userFullName: withdrawal.users.full_name,
            userEmail: withdrawal.users.email,
            amount: -withdrawal.withdrawal_amount, // Negative for withdrawals
            shares: 0,
            status: withdrawal.status,
            referenceId: withdrawal.id,
            description: `Withdrawal to ${withdrawal.wallet_address}`,
            paymentMethod: withdrawal.network,
            transactionHash: withdrawal.transaction_hash,
            adminNotes: withdrawal.admin_notes
          })
        })
      }

      // 3. Fetch Commission Conversions
      if (filters.transactionTypes.conversions) {
        let conversionQuery = supabase
          .from('commission_conversions')
          .select(`
            id,
            user_id,
            shares_requested,
            usdt_amount,
            share_price,
            status,
            created_at,
            users!inner(id, username, full_name, email)
          `)
          .gte('created_at', startDateTime)
          .lte('created_at', endDateTime)

        if (filters.userId !== 'all') {
          conversionQuery = conversionQuery.eq('user_id', parseInt(filters.userId))
        }

        if (filters.statusFilter !== 'all') {
          conversionQuery = conversionQuery.eq('status', filters.statusFilter)
        }

        const { data: conversions, error: conversionError } = await conversionQuery

        if (conversionError) throw conversionError

        conversions?.forEach(conversion => {
          allTransactions.push({
            date: conversion.created_at,
            type: 'USDT to Shares Conversion',
            userId: conversion.user_id,
            username: conversion.users.username,
            userFullName: conversion.users.full_name,
            userEmail: conversion.users.email,
            amount: -conversion.usdt_amount, // Negative USDT (spent)
            shares: conversion.shares_requested, // Positive shares (gained)
            status: conversion.status,
            referenceId: conversion.id,
            description: `Converted ${conversion.usdt_amount} USDT to ${conversion.shares_requested} shares`,
            conversionRate: conversion.share_price
          })
        })
      }

      // 4. Fetch Commission Transactions (Earnings)
      if (filters.transactionTypes.commissions) {
        let commissionQuery = supabase
          .from('commission_transactions')
          .select(`
            id,
            referrer_id,
            referred_id,
            share_purchase_id,
            commission_rate,
            share_purchase_amount,
            usdt_commission,
            share_commission,
            status,
            payment_date,
            created_at,
            referrer:users!referrer_id(id, username, full_name, email),
            referred:users!referred_id(id, username, full_name, email)
          `)
          .gte('created_at', startDateTime)
          .lte('created_at', endDateTime)

        if (filters.userId !== 'all') {
          commissionQuery = commissionQuery.eq('referrer_id', parseInt(filters.userId))
        }

        if (filters.statusFilter !== 'all') {
          commissionQuery = commissionQuery.eq('status', filters.statusFilter)
        }

        const { data: commissions, error: commissionError } = await commissionQuery

        if (commissionError) throw commissionError

        commissions?.forEach(commission => {
          allTransactions.push({
            date: commission.created_at,
            type: 'Commission Earned',
            userId: commission.referrer_id,
            username: commission.referrer?.username,
            userFullName: commission.referrer?.full_name,
            userEmail: commission.referrer?.email,
            amount: commission.usdt_commission, // Positive for earnings
            shares: commission.share_commission,
            status: commission.status,
            referenceId: commission.id,
            description: `Commission from ${commission.referred?.username || 'Unknown'} purchase`,
            commissionRate: commission.commission_rate,
            purchaseAmount: commission.share_purchase_amount,
            referredUser: commission.referred?.username
          })
        })
      }

      // Sort all transactions by date (newest first)
      allTransactions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

      return allTransactions
    } catch (error) {
      console.error('Error fetching transaction data:', error)
      throw error
    }
  }

  const generateExcelReport = async () => {
    setGeneratingReport(true)
    try {
      console.log('🔄 Generating Excel report...')

      // Validate filters first
      const validationErrors = validateReportFilters(filters)
      if (validationErrors.length > 0) {
        alert('Validation errors:\n' + validationErrors.join('\n'))
        return
      }

      // Fetch all transaction data
      const allTransactions = await fetchAllTransactionData()

      if (allTransactions.length === 0) {
        alert('No transactions found for the selected criteria.')
        return
      }

      // Create new workbook
      const workbook = XLSX.utils.book_new()

      // Sheet 1: Complete Transaction Ledger
      const ledgerData = allTransactions.map(tx => ({
        'Date': new Date(tx.date).toLocaleDateString(),
        'Time': new Date(tx.date).toLocaleTimeString(),
        'Transaction Type': tx.type,
        'User ID': tx.userId,
        'Username': tx.username,
        'Full Name': tx.userFullName || '',
        'Email': tx.userEmail,
        'USDT Amount': tx.amount || 0,
        'Shares': tx.shares || 0,
        'Status': tx.status,
        'Reference ID': tx.referenceId,
        'Description': tx.description,
        'Payment Method': tx.paymentMethod || '',
        'Transaction Hash': tx.transactionHash || '',
        'Commission Rate': tx.commissionRate || '',
        'Admin Notes': tx.adminNotes || ''
      }))

      const ledgerSheet = XLSX.utils.json_to_sheet(ledgerData)
      XLSX.utils.book_append_sheet(workbook, ledgerSheet, 'Complete Ledger')

      // Sheet 2: Share Purchases Only
      const purchases = allTransactions.filter(tx => tx.type === 'Share Purchase')
      if (purchases.length > 0) {
        const purchaseData = purchases.map(tx => ({
          'Date': new Date(tx.date).toLocaleDateString(),
          'Username': tx.username,
          'Package': tx.description.split(' - ')[0],
          'Shares Purchased': tx.shares,
          'Total Amount': tx.amount,
          'Commission Used': tx.commissionUsed || 0,
          'Remaining Payment': tx.remainingPayment || 0,
          'Payment Method': tx.paymentMethod,
          'Status': tx.status,
          'Reference ID': tx.referenceId
        }))

        const purchaseSheet = XLSX.utils.json_to_sheet(purchaseData)
        XLSX.utils.book_append_sheet(workbook, purchaseSheet, 'Share Purchases')
      }

      // Sheet 3: Commission Activities
      const commissionActivities = allTransactions.filter(tx =>
        tx.type === 'Commission Withdrawal' || tx.type === 'Commission Earned'
      )
      if (commissionActivities.length > 0) {
        const commissionData = commissionActivities.map(tx => ({
          'Date': new Date(tx.date).toLocaleDateString(),
          'Username': tx.username,
          'Activity Type': tx.type,
          'USDT Amount': tx.amount,
          'Shares': tx.shares || 0,
          'Status': tx.status,
          'Description': tx.description,
          'Referred User': tx.referredUser || '',
          'Commission Rate': tx.commissionRate || '',
          'Reference ID': tx.referenceId
        }))

        const commissionSheet = XLSX.utils.json_to_sheet(commissionData)
        XLSX.utils.book_append_sheet(workbook, commissionSheet, 'Commission Activities')
      }

      // Sheet 4: Conversions
      const conversions = allTransactions.filter(tx => tx.type === 'USDT to Shares Conversion')
      if (conversions.length > 0) {
        const conversionData = conversions.map(tx => ({
          'Date': new Date(tx.date).toLocaleDateString(),
          'Username': tx.username,
          'USDT Spent': Math.abs(tx.amount),
          'Shares Received': tx.shares,
          'Conversion Rate': tx.conversionRate,
          'Status': tx.status,
          'Reference ID': tx.referenceId
        }))

        const conversionSheet = XLSX.utils.json_to_sheet(conversionData)
        XLSX.utils.book_append_sheet(workbook, conversionSheet, 'Conversions')
      }

      // Sheet 5: Summary Report
      const summaryData = generateSummaryData(allTransactions)
      const summarySheet = XLSX.utils.json_to_sheet(summaryData)
      XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary Report')

      // Generate filename using utility
      const filename = generateReportFilename('Share-Ledger-Report', filters)

      // Download the file
      XLSX.writeFile(workbook, filename)

      // Calculate approximate file size (rough estimate)
      const fileSizeEstimate = allTransactions.length * 200 // Rough estimate: 200 bytes per transaction

      // Save to report history - temporarily disabled
      const reportId = null // await saveReportHistory(...) - disabled until table exists

      // Log admin action
      await logAdminAction(
        currentUser?.adminUser?.id || 'unknown',
        'excel_report_generated',
        `Generated share ledger report: ${filename}`,
        {
          filters,
          transactionCount: allTransactions.length,
          filename,
          reportId
        }
      )

      // Reload recent reports - temporarily disabled
      // await loadRecentReports()

      console.log('✅ Excel report generated successfully:', filename)
      alert(`Excel report generated successfully!\nFile: ${filename}\nTransactions: ${allTransactions.length}`)

    } catch (error) {
      console.error('❌ Error generating Excel report:', error)
      alert('Failed to generate Excel report. Please try again.')
    } finally {
      setGeneratingReport(false)
    }
  }

  const generateSummaryData = (transactions: any[]) => {
    const summary: any[] = []

    // Overall totals
    const totalPurchases = transactions.filter(tx => tx.type === 'Share Purchase').length
    const totalWithdrawals = transactions.filter(tx => tx.type === 'Commission Withdrawal').length
    const totalConversions = transactions.filter(tx => tx.type === 'USDT to Shares Conversion').length
    const totalCommissions = transactions.filter(tx => tx.type === 'Commission Earned').length

    const totalUSDTIn = transactions
      .filter(tx => tx.amount > 0)
      .reduce((sum, tx) => sum + tx.amount, 0)

    const totalUSDTOut = Math.abs(transactions
      .filter(tx => tx.amount < 0)
      .reduce((sum, tx) => sum + tx.amount, 0))

    const totalSharesTransacted = transactions
      .reduce((sum, tx) => sum + (tx.shares || 0), 0)

    summary.push(
      { 'Metric': 'Total Transactions', 'Value': transactions.length },
      { 'Metric': 'Share Purchases', 'Value': totalPurchases },
      { 'Metric': 'Commission Withdrawals', 'Value': totalWithdrawals },
      { 'Metric': 'USDT Conversions', 'Value': totalConversions },
      { 'Metric': 'Commission Earnings', 'Value': totalCommissions },
      { 'Metric': '', 'Value': '' }, // Empty row
      { 'Metric': 'Total USDT Inflow', 'Value': totalUSDTIn.toFixed(2) },
      { 'Metric': 'Total USDT Outflow', 'Value': totalUSDTOut.toFixed(2) },
      { 'Metric': 'Net USDT Flow', 'Value': (totalUSDTIn - totalUSDTOut).toFixed(2) },
      { 'Metric': 'Total Shares Transacted', 'Value': totalSharesTransacted },
      { 'Metric': '', 'Value': '' }, // Empty row
    )

    // User-specific summary if single user selected
    if (filters.userId !== 'all') {
      const userTransactions = transactions.filter(tx => tx.userId.toString() === filters.userId)
      const userName = userTransactions[0]?.username || 'Unknown'

      summary.push(
        { 'Metric': `User: ${userName}`, 'Value': '' },
        { 'Metric': 'User Transactions', 'Value': userTransactions.length },
        { 'Metric': 'User USDT Activity', 'Value': userTransactions.reduce((sum, tx) => sum + tx.amount, 0).toFixed(2) },
        { 'Metric': 'User Shares Activity', 'Value': userTransactions.reduce((sum, tx) => sum + (tx.shares || 0), 0) }
      )
    }

    return summary
  }

  return (
    <div style={{
      backgroundColor: '#0F0F0F',
      minHeight: '100vh',
      padding: '24px',
      color: '#FFFFFF'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        {/* Header */}
        <div style={{ marginBottom: '32px' }}>
          <h1 style={{
            fontSize: '28px',
            fontWeight: '700',
            color: '#F59E0B',
            marginBottom: '8px'
          }}>
            📊 Share Ledger Report System
          </h1>
          <p style={{ color: '#9CA3AF', fontSize: '16px' }}>
            Generate comprehensive Excel reports of all financial transactions
          </p>
        </div>

        <div style={{
          display: 'grid',
          gridTemplateColumns: '2fr 1fr',
          gap: '32px'
        }}>
          {/* Main Report Configuration */}
          <div style={{
            backgroundColor: '#1F2937',
            borderRadius: '12px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            <h2 style={{
              fontSize: '20px',
              fontWeight: '600',
              color: '#F59E0B',
              marginBottom: '24px'
            }}>
              📋 Report Configuration
            </h2>

            {/* Date Range Selection */}
            <div style={{ marginBottom: '24px' }}>
              <label style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: '600',
                color: '#E5E7EB',
                marginBottom: '12px'
              }}>
                Date Range
              </label>

              {/* Preset Buttons */}
              <div style={{
                display: 'flex',
                gap: '8px',
                marginBottom: '12px',
                flexWrap: 'wrap'
              }}>
                {[
                  { key: 'last7days', label: 'Last 7 Days' },
                  { key: 'last30days', label: 'Last 30 Days' },
                  { key: 'last3months', label: 'Last 3 Months' },
                  { key: 'last6months', label: 'Last 6 Months' },
                  { key: 'lastyear', label: 'Last Year' }
                ].map((preset) => (
                  <button
                    key={preset.key}
                    onClick={() => setPresetDateRange(preset.key)}
                    style={{
                      padding: '6px 12px',
                      backgroundColor: '#374151',
                      color: '#E5E7EB',
                      border: '1px solid #4B5563',
                      borderRadius: '6px',
                      fontSize: '12px',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease'
                    }}
                    onMouseOver={(e) => {
                      e.currentTarget.style.backgroundColor = '#4B5563'
                    }}
                    onMouseOut={(e) => {
                      e.currentTarget.style.backgroundColor = '#374151'
                    }}
                  >
                    {preset.label}
                  </button>
                ))}
              </div>

              {/* Custom Date Inputs */}
              <div style={{ display: 'flex', gap: '12px' }}>
                <div style={{ flex: 1 }}>
                  <label style={{
                    display: 'block',
                    fontSize: '12px',
                    color: '#9CA3AF',
                    marginBottom: '4px'
                  }}>
                    From Date
                  </label>
                  <input
                    type="date"
                    value={filters.startDate}
                    onChange={(e) => setFilters({ ...filters, startDate: e.target.value })}
                    style={{
                      width: '100%',
                      padding: '10px',
                      backgroundColor: '#374151',
                      border: '1px solid #4B5563',
                      borderRadius: '6px',
                      color: '#FFFFFF',
                      fontSize: '14px'
                    }}
                  />
                </div>
                <div style={{ flex: 1 }}>
                  <label style={{
                    display: 'block',
                    fontSize: '12px',
                    color: '#9CA3AF',
                    marginBottom: '4px'
                  }}>
                    To Date
                  </label>
                  <input
                    type="date"
                    value={filters.endDate}
                    onChange={(e) => setFilters({ ...filters, endDate: e.target.value })}
                    style={{
                      width: '100%',
                      padding: '10px',
                      backgroundColor: '#374151',
                      border: '1px solid #4B5563',
                      borderRadius: '6px',
                      color: '#FFFFFF',
                      fontSize: '14px'
                    }}
                  />
                </div>
              </div>
            </div>

            {/* User Selection */}
            <div style={{ marginBottom: '24px' }}>
              <label style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: '600',
                color: '#E5E7EB',
                marginBottom: '8px'
              }}>
                User Filter
              </label>
              <select
                value={filters.userId}
                onChange={(e) => setFilters({ ...filters, userId: e.target.value })}
                style={{
                  width: '100%',
                  padding: '12px',
                  backgroundColor: '#374151',
                  border: '1px solid #4B5563',
                  borderRadius: '8px',
                  color: '#FFFFFF',
                  fontSize: '14px'
                }}
              >
                <option value="all">All Users</option>
                {users.map((user) => (
                  <option key={user.id} value={user.id}>
                    {user.username} - {user.full_name} ({user.email})
                  </option>
                ))}
              </select>
            </div>

            {/* Transaction Type Filters */}
            <div style={{ marginBottom: '24px' }}>
              <label style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: '600',
                color: '#E5E7EB',
                marginBottom: '12px'
              }}>
                Transaction Types
              </label>
              <div style={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr',
                gap: '12px'
              }}>
                {[
                  { key: 'purchases', label: 'Share Purchases', icon: '🛒' },
                  { key: 'withdrawals', label: 'Commission Withdrawals', icon: '💸' },
                  { key: 'conversions', label: 'USDT to Shares Conversions', icon: '🔄' },
                  { key: 'commissions', label: 'Commission Earnings', icon: '💰' }
                ].map((type) => (
                  <label
                    key={type.key}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      padding: '12px',
                      backgroundColor: '#374151',
                      borderRadius: '8px',
                      cursor: 'pointer',
                      border: '1px solid #4B5563',
                      transition: 'all 0.2s ease'
                    }}
                    onMouseOver={(e) => {
                      e.currentTarget.style.backgroundColor = '#4B5563'
                    }}
                    onMouseOut={(e) => {
                      e.currentTarget.style.backgroundColor = '#374151'
                    }}
                  >
                    <input
                      type="checkbox"
                      checked={filters.transactionTypes[type.key as keyof typeof filters.transactionTypes]}
                      onChange={(e) => setFilters({
                        ...filters,
                        transactionTypes: {
                          ...filters.transactionTypes,
                          [type.key]: e.target.checked
                        }
                      })}
                      style={{
                        width: '16px',
                        height: '16px',
                        accentColor: '#F59E0B'
                      }}
                    />
                    <span style={{ fontSize: '16px' }}>{type.icon}</span>
                    <span style={{ color: '#E5E7EB', fontSize: '14px' }}>{type.label}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Status Filter */}
            <div style={{ marginBottom: '24px' }}>
              <label style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: '600',
                color: '#E5E7EB',
                marginBottom: '8px'
              }}>
                Status Filter
              </label>
              <select
                value={filters.statusFilter}
                onChange={(e) => setFilters({ ...filters, statusFilter: e.target.value })}
                style={{
                  width: '100%',
                  padding: '12px',
                  backgroundColor: '#374151',
                  border: '1px solid #4B5563',
                  borderRadius: '8px',
                  color: '#FFFFFF',
                  fontSize: '14px'
                }}
              >
                <option value="all">All Statuses</option>
                <option value="completed">Completed</option>
                <option value="approved">Approved</option>
                <option value="active">Active</option>
                <option value="pending">Pending</option>
                <option value="processing">Processing</option>
                <option value="failed">Failed</option>
                <option value="rejected">Rejected</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>

            {/* Generate Report Button */}
            <button
              onClick={generateExcelReport}
              disabled={generatingReport || !Object.values(filters.transactionTypes).some(Boolean)}
              style={{
                width: '100%',
                padding: '16px',
                backgroundColor: generatingReport ? '#6B7280' : '#F59E0B',
                color: generatingReport ? '#9CA3AF' : '#000000',
                border: 'none',
                borderRadius: '8px',
                fontSize: '16px',
                fontWeight: '600',
                cursor: generatingReport ? 'not-allowed' : 'pointer',
                transition: 'all 0.2s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px'
              }}
            >
              {generatingReport ? (
                <>
                  <div style={{
                    width: '16px',
                    height: '16px',
                    border: '2px solid #9CA3AF',
                    borderTop: '2px solid #F59E0B',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite'
                  }} />
                  Generating Report...
                </>
              ) : (
                <>
                  📊 Generate Excel Report
                </>
              )}
            </button>

            {!Object.values(filters.transactionTypes).some(Boolean) && (
              <p style={{
                color: '#EF4444',
                fontSize: '12px',
                marginTop: '8px',
                textAlign: 'center'
              }}>
                Please select at least one transaction type
              </p>
            )}
          </div>

          {/* Sidebar - Report Information */}
          <div style={{
            backgroundColor: '#1F2937',
            borderRadius: '12px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            <h3 style={{
              fontSize: '18px',
              fontWeight: '600',
              color: '#F59E0B',
              marginBottom: '20px'
            }}>
              📋 Report Information
            </h3>

            {/* Report Structure Info */}
            <div style={{
              backgroundColor: '#374151',
              borderRadius: '8px',
              padding: '16px',
              marginBottom: '20px'
            }}>
              <h4 style={{
                fontSize: '14px',
                fontWeight: '600',
                color: '#E5E7EB',
                marginBottom: '12px'
              }}>
                Excel Document Structure
              </h4>
              <div style={{ fontSize: '12px', color: '#9CA3AF', lineHeight: '1.5' }}>
                <div style={{ marginBottom: '8px' }}>
                  <strong style={{ color: '#F59E0B' }}>Sheet 1:</strong> Complete Transaction Ledger
                </div>
                <div style={{ marginBottom: '8px' }}>
                  <strong style={{ color: '#F59E0B' }}>Sheet 2:</strong> Share Purchases Only
                </div>
                <div style={{ marginBottom: '8px' }}>
                  <strong style={{ color: '#F59E0B' }}>Sheet 3:</strong> Commission Activities
                </div>
                <div style={{ marginBottom: '8px' }}>
                  <strong style={{ color: '#F59E0B' }}>Sheet 4:</strong> USDT Conversions
                </div>
                <div>
                  <strong style={{ color: '#F59E0B' }}>Sheet 5:</strong> Summary Report
                </div>
              </div>
            </div>

            {/* Data Sources */}
            <div style={{
              backgroundColor: '#374151',
              borderRadius: '8px',
              padding: '16px',
              marginBottom: '20px'
            }}>
              <h4 style={{
                fontSize: '14px',
                fontWeight: '600',
                color: '#E5E7EB',
                marginBottom: '12px'
              }}>
                Data Sources
              </h4>
              <div style={{ fontSize: '12px', color: '#9CA3AF', lineHeight: '1.5' }}>
                <div style={{ marginBottom: '4px' }}>• aureus_share_purchases</div>
                <div style={{ marginBottom: '4px' }}>• commission_withdrawal_requests</div>
                <div style={{ marginBottom: '4px' }}>• commission_conversions</div>
                <div style={{ marginBottom: '4px' }}>• commission_transactions</div>
              </div>
            </div>

            {/* Current Filter Summary */}
            <div style={{
              backgroundColor: '#374151',
              borderRadius: '8px',
              padding: '16px',
              marginBottom: '20px'
            }}>
              <h4 style={{
                fontSize: '14px',
                fontWeight: '600',
                color: '#E5E7EB',
                marginBottom: '12px'
              }}>
                Current Filters
              </h4>
              <div style={{ fontSize: '12px', color: '#9CA3AF', lineHeight: '1.5' }}>
                <div style={{ marginBottom: '4px' }}>
                  <strong>Date Range:</strong> {filters.startDate} to {filters.endDate}
                </div>
                <div style={{ marginBottom: '4px' }}>
                  <strong>User:</strong> {filters.userId === 'all' ? 'All Users' : users.find(u => u.id.toString() === filters.userId)?.username || 'Unknown'}
                </div>
                <div style={{ marginBottom: '4px' }}>
                  <strong>Types:</strong> {Object.entries(filters.transactionTypes)
                    .filter(([_, enabled]) => enabled)
                    .map(([type, _]) => type.charAt(0).toUpperCase() + type.slice(1))
                    .join(', ') || 'None selected'}
                </div>
                <div>
                  <strong>Status:</strong> {filters.statusFilter === 'all' ? 'All Statuses' : filters.statusFilter}
                </div>
              </div>
            </div>

            {/* Recent Reports */}
            <div>
              <h4 style={{
                fontSize: '14px',
                fontWeight: '600',
                color: '#E5E7EB',
                marginBottom: '12px'
              }}>
                Recent Reports
              </h4>
              {recentReports.length === 0 ? (
                <p style={{
                  color: '#9CA3AF',
                  fontSize: '12px',
                  textAlign: 'center',
                  padding: '20px',
                  backgroundColor: '#374151',
                  borderRadius: '8px'
                }}>
                  No recent reports found
                </p>
              ) : (
                <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                  {recentReports.map((report) => (
                    <div
                      key={report.id}
                      style={{
                        backgroundColor: '#374151',
                        borderRadius: '6px',
                        padding: '12px',
                        marginBottom: '8px',
                        fontSize: '12px',
                        border: '1px solid #4B5563'
                      }}
                    >
                      <div style={{
                        color: '#E5E7EB',
                        fontWeight: '600',
                        marginBottom: '6px',
                        wordBreak: 'break-all'
                      }}>
                        {report.filename.length > 40
                          ? report.filename.substring(0, 40) + '...'
                          : report.filename}
                      </div>
                      <div style={{ color: '#9CA3AF', marginBottom: '4px' }}>
                        📅 {new Date(report.generated_at).toLocaleDateString()} {new Date(report.generated_at).toLocaleTimeString()}
                      </div>
                      <div style={{ color: '#9CA3AF', marginBottom: '4px' }}>
                        📊 {report.transaction_count} transactions
                      </div>
                      <div style={{ color: '#9CA3AF', marginBottom: '4px' }}>
                        💾 {formatFileSize(report.file_size_bytes)}
                      </div>
                      <div style={{ color: '#9CA3AF' }}>
                        📥 Downloaded {report.download_count} times
                      </div>
                      {report.last_downloaded_at && (
                        <div style={{ color: '#9CA3AF', fontSize: '10px', marginTop: '4px' }}>
                          Last: {new Date(report.last_downloaded_at).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* CSS for spinner animation */}
        <style jsx="true">{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    </div>
  )
}

export default ShareLedgerReportSystem
