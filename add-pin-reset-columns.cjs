#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function addPinResetColumns() {
  console.log('🔧 Adding PIN reset columns to users table...');
  
  try {
    // First, check if the columns already exist by trying to select them
    const { data, error } = await supabase
      .from('users')
      .select('reset_pin_hash, reset_pin_expires, reset_pin_attempts, reset_pin_verified_at')
      .limit(1);
    
    if (error && error.code === '42703') {
      console.log('❌ Columns do not exist, adding them...');
      
      // Add the columns one by one using individual UPDATE statements
      const columns = [
        'reset_pin_hash TEXT',
        'reset_pin_expires TIMESTAMP WITH TIME ZONE',
        'reset_pin_attempts INTEGER DEFAULT 0',
        'reset_pin_verified_at TIMESTAMP WITH TIME ZONE'
      ];
      
      for (const column of columns) {
        const columnName = column.split(' ')[0];
        console.log(`📝 Adding column: ${columnName}`);
        
        try {
          // Try to add the column
          const { error: addError } = await supabase
            .from('users')
            .update({ [columnName]: null })
            .eq('id', -1); // This will fail but might give us info about the column
          
          if (addError && addError.code === '42703') {
            console.log(`  ➕ Column ${columnName} needs to be added`);
            // Column doesn't exist, we need to add it via SQL
            // Since we can't execute DDL directly, let's try a different approach
          } else {
            console.log(`  ✅ Column ${columnName} already exists`);
          }
        } catch (e) {
          console.log(`  ❓ Column ${columnName} status unknown:`, e.message);
        }
      }
      
      console.log('📋 Manual SQL needed:');
      console.log('Please run this SQL in your Supabase SQL editor:');
      console.log('');
      console.log('ALTER TABLE users ADD COLUMN IF NOT EXISTS reset_pin_hash TEXT;');
      console.log('ALTER TABLE users ADD COLUMN IF NOT EXISTS reset_pin_expires TIMESTAMP WITH TIME ZONE;');
      console.log('ALTER TABLE users ADD COLUMN IF NOT EXISTS reset_pin_attempts INTEGER DEFAULT 0;');
      console.log('ALTER TABLE users ADD COLUMN IF NOT EXISTS reset_pin_verified_at TIMESTAMP WITH TIME ZONE;');
      console.log('');
      
    } else if (error) {
      console.error('❌ Error checking columns:', error);
    } else {
      console.log('✅ All PIN reset columns already exist');
      console.log('📊 Sample data:', data);
    }
    
    // Test the functionality by updating a test record
    console.log('🧪 Testing column functionality...');
    
    const testUserId = 102; // Use a known user ID
    const testPinHash = 'test_hash_' + Date.now();
    const testExpires = new Date(Date.now() + 15 * 60 * 1000).toISOString();
    
    const { error: updateError } = await supabase
      .from('users')
      .update({
        reset_pin_hash: testPinHash,
        reset_pin_expires: testExpires,
        reset_pin_attempts: 0
      })
      .eq('id', testUserId);
    
    if (updateError) {
      console.error('❌ Test update failed:', updateError);
      console.log('🔧 This means the columns need to be added manually');
    } else {
      console.log('✅ Test update successful - columns are working');
      
      // Clean up test data
      await supabase
        .from('users')
        .update({
          reset_pin_hash: null,
          reset_pin_expires: null,
          reset_pin_attempts: null,
          reset_pin_verified_at: null
        })
        .eq('id', testUserId);
      
      console.log('🧹 Test data cleaned up');
    }
    
  } catch (e) {
    console.error('❌ Exception:', e.message);
  }
}

addPinResetColumns()
  .then(() => {
    console.log('✅ PIN reset columns setup complete');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  });
