import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testPhaseCommissions() {
  console.log('🔍 TESTING PHASE-SPECIFIC COMMISSION QUERIES\n');

  try {
    // Test 1: Get commissions earned in Phase 0 (Pre Sale)
    console.log('1️⃣ Top 5 commission earners in Pre Sale (Phase ID: 1)...');
    
    const { data: presaleCommissions, error: error1 } = await supabase
      .from('commission_transactions')
      .select('referrer_id, usdt_commission, share_commission')
      .eq('phase_id', 1)
      .order('usdt_commission', { ascending: false })
      .limit(5);

    if (error1) {
      console.error('❌ Error:', error1);
    } else {
      console.log('📊 Top 5 commission earners in Pre Sale:');
      presaleCommissions?.forEach((c, i) => {
        console.log(`   ${i+1}. User ${c.referrer_id}: $${c.usdt_commission} USDT + ${c.share_commission || 0} shares`);
      });
    }

    // Test 2: Get total commissions by phase
    console.log('\n2️⃣ Getting commission totals by phase...');
    
    const { data: phaseStats, error: error2 } = await supabase
      .from('commission_transactions')
      .select(`
        phase_id,
        investment_phases(phase_name),
        usdt_commission,
        share_commission
      `)
      .not('phase_id', 'is', null);

    if (error2) {
      console.error('❌ Error:', error2);
    } else {
      const phaseMap = new Map();
      
      phaseStats?.forEach(stat => {
        const phaseId = stat.phase_id;
        const phaseName = stat.investment_phases?.phase_name || `Phase ${phaseId}`;
        
        if (!phaseMap.has(phaseId)) {
          phaseMap.set(phaseId, { name: phaseName, totalUSDT: 0, totalShares: 0, count: 0 });
        }
        
        const phase = phaseMap.get(phaseId);
        phase.totalUSDT += stat.usdt_commission || 0;
        phase.totalShares += stat.share_commission || 0;
        phase.count += 1;
      });

      console.log('📈 Commission totals by phase:');
      Array.from(phaseMap.entries()).forEach(([phaseId, stats]) => {
        console.log(`   • ${stats.name}: $${stats.totalUSDT.toFixed(2)} USDT + ${stats.totalShares} shares (${stats.count} transactions)`);
      });
    }

    // Test 3: Get user's commission breakdown by phase (example user)
    console.log('\n3️⃣ Commission breakdown for User 4...');
    
    const { data: userCommissions, error: error3 } = await supabase
      .from('commission_transactions')
      .select(`
        phase_id,
        investment_phases(phase_name, phase_number),
        usdt_commission,
        share_commission
      `)
      .eq('referrer_id', 4)
      .not('phase_id', 'is', null)
      .order('phase_id', { ascending: true });

    if (error3) {
      console.error('❌ Error:', error3);
    } else {
      console.log('📊 User 4 commission breakdown:');
      userCommissions?.forEach(comm => {
        const phaseName = comm.investment_phases?.phase_name || `Phase ${comm.phase_id}`;
        console.log(`   • ${phaseName}: $${comm.usdt_commission} USDT + ${comm.share_commission || 0} shares`);
      });
    }

    console.log('\n✅ Phase-specific commission tracking is working perfectly!');
    console.log('🏆 Competition leaderboards will now show real phase-specific data!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testPhaseCommissions();
