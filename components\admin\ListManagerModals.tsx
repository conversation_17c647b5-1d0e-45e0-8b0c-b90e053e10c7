/**
 * LIST MANAGER MODALS
 * 
 * Modal components for List Manager functionality:
 * - CreateContactModal
 * - CreateListModal  
 * - CreateTemplateModal
 * - CreateCampaignModal
 */

import React, { useState } from 'react';
import { ListManagerService, Contact, ContactList, EmailTemplate, BulkEmailCampaign } from '../../lib/services/listManagerService';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  currentUser: any;
}

// CREATE CONTACT MODAL
export function CreateContactModal({ isOpen, onClose, onSuccess, currentUser }: ModalProps) {
  const [formData, setFormData] = useState({
    email: '',
    first_name: '',
    last_name: '',
    phone: '',
    company: '',
    tags: '',
    source: 'manual' as const,
    status: 'active' as const
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const listManagerService = ListManagerService.getInstance();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const contactData = {
        ...formData,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
        custom_fields: {}
      };

      const result = await listManagerService.createContact(contactData);
      
      if (result) {
        onSuccess();
        onClose();
        setFormData({
          email: '',
          first_name: '',
          last_name: '',
          phone: '',
          company: '',
          tags: '',
          source: 'manual',
          status: 'active'
        });
      } else {
        setError('Failed to create contact');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create contact');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md">
        <h2 className="text-xl font-semibold text-white mb-4">Create New Contact</h2>
        
        {error && (
          <div className="bg-red-900 border border-red-700 text-red-100 px-3 py-2 rounded mb-4 text-sm">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Email Address *
            </label>
            <input
              type="email"
              required
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              placeholder="<EMAIL>"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                First Name
              </label>
              <input
                type="text"
                value={formData.first_name}
                onChange={(e) => setFormData({ ...formData, first_name: e.target.value })}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Last Name
              </label>
              <input
                type="text"
                value={formData.last_name}
                onChange={(e) => setFormData({ ...formData, last_name: e.target.value })}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Phone
            </label>
            <input
              type="tel"
              value={formData.phone}
              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Company
            </label>
            <input
              type="text"
              value={formData.company}
              onChange={(e) => setFormData({ ...formData, company: e.target.value })}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Tags (comma-separated)
            </label>
            <input
              type="text"
              value={formData.tags}
              onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              placeholder="investor, newsletter, vip"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Source
              </label>
              <select
                value={formData.source}
                onChange={(e) => setFormData({ ...formData, source: e.target.value as any })}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              >
                <option value="manual">Manual</option>
                <option value="import">Import</option>
                <option value="website">Website</option>
                <option value="api">API</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Status
              </label>
              <select
                value={formData.status}
                onChange={(e) => setFormData({ ...formData, status: e.target.value as any })}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              >
                <option value="active">Active</option>
                <option value="unsubscribed">Unsubscribed</option>
                <option value="bounced">Bounced</option>
                <option value="invalid">Invalid</option>
              </select>
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-300 hover:text-white"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="bg-yellow-600 hover:bg-yellow-700 px-4 py-2 rounded text-white disabled:opacity-50"
            >
              {loading ? 'Creating...' : 'Create Contact'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// CREATE LIST MODAL
export function CreateListModal({ isOpen, onClose, onSuccess, currentUser }: ModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    tags: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const listManagerService = ListManagerService.getInstance();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const listData = {
        ...formData,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
        created_by: currentUser?.id,
        is_active: true
      };

      const result = await listManagerService.createList(listData);
      
      if (result) {
        onSuccess();
        onClose();
        setFormData({ name: '', description: '', tags: '' });
      } else {
        setError('Failed to create list');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create list');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md">
        <h2 className="text-xl font-semibold text-white mb-4">Create New List</h2>
        
        {error && (
          <div className="bg-red-900 border border-red-700 text-red-100 px-3 py-2 rounded mb-4 text-sm">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              List Name *
            </label>
            <input
              type="text"
              required
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              placeholder="Newsletter Subscribers"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white h-20"
              placeholder="Description of this contact list..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Tags (comma-separated)
            </label>
            <input
              type="text"
              value={formData.tags}
              onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              placeholder="newsletter, marketing, vip"
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-300 hover:text-white"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="bg-yellow-600 hover:bg-yellow-700 px-4 py-2 rounded text-white disabled:opacity-50"
            >
              {loading ? 'Creating...' : 'Create List'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// CREATE TEMPLATE MODAL
export function CreateTemplateModal({ isOpen, onClose, onSuccess, currentUser }: ModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    html_content: '',
    text_content: '',
    template_type: 'newsletter' as const,
    variables: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const listManagerService = ListManagerService.getInstance();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const templateData = {
        ...formData,
        variables: formData.variables.split(',').map(v => v.trim()).filter(v => v),
        created_by: currentUser?.id,
        is_active: true
      };

      const result = await listManagerService.createTemplate(templateData);
      
      if (result) {
        onSuccess();
        onClose();
        setFormData({
          name: '',
          subject: '',
          html_content: '',
          text_content: '',
          template_type: 'newsletter',
          variables: ''
        });
      } else {
        setError('Failed to create template');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create template');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h2 className="text-xl font-semibold text-white mb-4">Create Email Template</h2>
        
        {error && (
          <div className="bg-red-900 border border-red-700 text-red-100 px-3 py-2 rounded mb-4 text-sm">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Template Name *
              </label>
              <input
                type="text"
                required
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                placeholder="Monthly Newsletter"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Template Type
              </label>
              <select
                value={formData.template_type}
                onChange={(e) => setFormData({ ...formData, template_type: e.target.value as any })}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              >
                <option value="newsletter">Newsletter</option>
                <option value="announcement">Announcement</option>
                <option value="promotional">Promotional</option>
                <option value="transactional">Transactional</option>
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Subject Line *
            </label>
            <input
              type="text"
              required
              value={formData.subject}
              onChange={(e) => setFormData({ ...formData, subject: e.target.value })}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              placeholder="Welcome to Aureus Alliance Holdings - {{name}}"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              HTML Content *
            </label>
            <textarea
              required
              value={formData.html_content}
              onChange={(e) => setFormData({ ...formData, html_content: e.target.value })}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white h-32"
              placeholder="<h1>Hello {{name}}!</h1><p>Welcome to our newsletter...</p>"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Text Content (optional)
            </label>
            <textarea
              value={formData.text_content}
              onChange={(e) => setFormData({ ...formData, text_content: e.target.value })}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white h-20"
              placeholder="Hello {{name}}! Welcome to our newsletter..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Variables (comma-separated)
            </label>
            <input
              type="text"
              value={formData.variables}
              onChange={(e) => setFormData({ ...formData, variables: e.target.value })}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              placeholder="name, email, company"
            />
            <p className="text-xs text-gray-400 mt-1">
              Use variables in your content like: {`{{name}}, {{email}}, {{company}}`}
            </p>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-300 hover:text-white"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="bg-yellow-600 hover:bg-yellow-700 px-4 py-2 rounded text-white disabled:opacity-50"
            >
              {loading ? 'Creating...' : 'Create Template'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// CREATE CAMPAIGN MODAL
export function CreateCampaignModal({ isOpen, onClose, onSuccess, currentUser }: ModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    template_id: '',
    list_ids: [] as string[],
    subject: '',
    scheduled_at: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [lists, setLists] = useState<ContactList[]>([]);

  const listManagerService = ListManagerService.getInstance();

  // Load templates and lists when modal opens
  React.useEffect(() => {
    if (isOpen) {
      loadTemplatesAndLists();
    }
  }, [isOpen]);

  const loadTemplatesAndLists = async () => {
    try {
      const [templateList, contactLists] = await Promise.all([
        listManagerService.getTemplates(currentUser?.id),
        listManagerService.getLists(currentUser?.id)
      ]);
      setTemplates(templateList);
      setLists(contactLists);
    } catch (err) {
      console.error('Error loading templates and lists:', err);
    }
  };

  const handleListToggle = (listId: string) => {
    setFormData(prev => ({
      ...prev,
      list_ids: prev.list_ids.includes(listId)
        ? prev.list_ids.filter(id => id !== listId)
        : [...prev.list_ids, listId]
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      if (formData.list_ids.length === 0) {
        setError('Please select at least one contact list');
        return;
      }

      // Calculate total recipients
      const selectedLists = lists.filter(list => formData.list_ids.includes(list.id));
      const totalRecipients = selectedLists.reduce((sum, list) => sum + list.contact_count, 0);

      const campaignData = {
        ...formData,
        status: 'draft' as const,
        total_recipients: totalRecipients,
        created_by: currentUser?.id
      };

      const result = await listManagerService.createCampaign(campaignData);

      if (result) {
        onSuccess();
        onClose();
        setFormData({
          name: '',
          template_id: '',
          list_ids: [],
          subject: '',
          scheduled_at: ''
        });
      } else {
        setError('Failed to create campaign');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create campaign');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h2 className="text-xl font-semibold text-white mb-4">Create Email Campaign</h2>

        {error && (
          <div className="bg-red-900 border border-red-700 text-red-100 px-3 py-2 rounded mb-4 text-sm">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Campaign Name *
            </label>
            <input
              type="text"
              required
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              placeholder="Monthly Newsletter - January 2025"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Email Template *
            </label>
            <select
              required
              value={formData.template_id}
              onChange={(e) => {
                const selectedTemplate = templates.find(t => t.id === e.target.value);
                setFormData({
                  ...formData,
                  template_id: e.target.value,
                  subject: selectedTemplate?.subject || formData.subject
                });
              }}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
            >
              <option value="">Select a template</option>
              {templates.map((template) => (
                <option key={template.id} value={template.id}>
                  {template.name} ({template.template_type})
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Subject Line *
            </label>
            <input
              type="text"
              required
              value={formData.subject}
              onChange={(e) => setFormData({ ...formData, subject: e.target.value })}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              placeholder="Your monthly update from Aureus Alliance"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Contact Lists * (Select one or more)
            </label>
            <div className="space-y-2 max-h-40 overflow-y-auto bg-gray-700 rounded p-3">
              {lists.map((list) => (
                <label key={list.id} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.list_ids.includes(list.id)}
                    onChange={() => handleListToggle(list.id)}
                    className="rounded"
                  />
                  <span className="text-white text-sm">
                    {list.name} ({list.contact_count} contacts)
                  </span>
                </label>
              ))}
            </div>
            {lists.length === 0 && (
              <p className="text-gray-400 text-sm">No contact lists available. Create a list first.</p>
            )}
          </div>

          {formData.list_ids.length > 0 && (
            <div className="bg-blue-900 bg-opacity-50 rounded p-3">
              <p className="text-blue-200 text-sm">
                Total Recipients: {lists.filter(list => formData.list_ids.includes(list.id)).reduce((sum, list) => sum + list.contact_count, 0)}
              </p>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Schedule Send (optional)
            </label>
            <input
              type="datetime-local"
              value={formData.scheduled_at}
              onChange={(e) => setFormData({ ...formData, scheduled_at: e.target.value })}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
            />
            <p className="text-xs text-gray-400 mt-1">
              Leave empty to save as draft. You can send it manually later.
            </p>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-300 hover:text-white"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || formData.list_ids.length === 0}
              className="bg-yellow-600 hover:bg-yellow-700 px-4 py-2 rounded text-white disabled:opacity-50"
            >
              {loading ? 'Creating...' : 'Create Campaign'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
