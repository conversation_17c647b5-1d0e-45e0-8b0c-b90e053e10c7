import React, { useState, useEffect } from 'react'
import { getSiteContent, updateSiteContent, getCurrentUser } from '../lib/supabase'

interface ContentEditorProps {
  section: string
}

export const ContentEditor: React.FC<ContentEditorProps> = ({ section }) => {
  const [content, setContent] = useState<any>({})
  const [originalContent, setOriginalContent] = useState<any>({})
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState('')
  const [user, setUser] = useState<any>(null)

  useEffect(() => {
    loadContent()
    loadUser()
  }, [section])

  const loadUser = async () => {
    const currentUser = await getCurrentUser()
    setUser(currentUser)
  }

  const loadContent = async () => {
    setLoading(true)
    try {
      const data = await getSiteContent(section)
      const contentObj: any = {}
      
      if (data) {
        data.forEach((item: any) => {
          contentObj[item.key] = typeof item.value === 'string' ? 
            item.value.replace(/^"|"$/g, '') : item.value
        })
      }
      
      setContent(contentObj)
      setOriginalContent(contentObj)
    } catch (error) {
      console.error('Error loading content:', error)
      setMessage('Error loading content')
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async (key: string, value: any) => {
    if (!user) return
    
    setSaving(true)
    try {
      await updateSiteContent(section, key, JSON.stringify(value), user.email)
      setMessage('Content saved successfully!')
      setTimeout(() => setMessage(''), 3000)
    } catch (error) {
      console.error('Error saving content:', error)
      setMessage('Error saving content')
    } finally {
      setSaving(false)
    }
  }

  const handleInputChange = (key: string, value: string) => {
    setContent(prev => ({ ...prev, [key]: value }))
  }

  const renderField = (key: string, label: string, type: 'text' | 'textarea' | 'email' | 'tel' | 'number' | 'url' = 'text', placeholder?: string) => {
    const value = content[key] || ''

    return (
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-300 mb-2">
          {label}
        </label>
        {type === 'textarea' ? (
          <textarea
            value={value}
            onChange={(e) => handleInputChange(key, e.target.value)}
            onBlur={() => handleSave(key, value)}
            rows={4}
            placeholder={placeholder}
            className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent text-white placeholder-gray-400 resize-vertical"
          />
        ) : (
          <input
            type={type}
            value={value}
            onChange={(e) => handleInputChange(key, e.target.value)}
            onBlur={() => handleSave(key, value)}
            placeholder={placeholder}
            className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent text-white placeholder-gray-400"
          />
        )}
      </div>
    )
  }

  const renderSection = (title: string, fields: Array<{key: string, label: string, type?: any, placeholder?: string}>) => (
    <div className="mb-8 p-6 bg-gray-800/30 rounded-lg border border-gray-700/50">
      <h3 className="text-lg font-semibold text-yellow-400 mb-4">{title}</h3>
      {fields.map(field => renderField(field.key, field.label, field.type || 'text', field.placeholder))}
    </div>
  )

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500 mx-auto mb-4"></div>
        <p className="text-gray-400">Loading content...</p>
      </div>
    )
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-white capitalize">
          Edit {section} Section
        </h2>
        {saving && (
          <div className="flex items-center text-yellow-400">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-400 mr-2"></div>
            Saving...
          </div>
        )}
      </div>

      {message && (
        <div className={`p-3 rounded-lg mb-6 ${
          message.includes('Error') 
            ? 'bg-red-900/50 border border-red-700 text-red-300' 
            : 'bg-green-900/50 border border-green-700 text-green-300'
        }`}>
          {message}
        </div>
      )}

      <div className="space-y-6">
        {section === 'hero' && (
          <>
            {renderSection('Main Content', [
              {key: 'main_title', label: 'Main Title', placeholder: 'Secure Your Wealth with Real Gold'},
              {key: 'subtitle', label: 'Subtitle', placeholder: 'AUREUS ALLIANCE HOLDINGS'},
              {key: 'description', label: 'Description', type: 'textarea', placeholder: 'A rare opportunity to purchase shares...'}
            ])}
            {renderSection('Pricing & Stats', [
              {key: 'share_price', label: 'Share Price ($)', type: 'number', placeholder: '5'},
              {key: 'projected_dividend', label: 'Projected Annual Dividend ($)', type: 'number', placeholder: '134.51'},
              {key: 'total_shares', label: 'Total Shares Display', placeholder: '1.4M'}
            ])}
            {renderSection('Call-to-Action Buttons', [
              {key: 'cta_primary', label: 'Primary CTA Text', placeholder: 'Explore The Project'},
              {key: 'cta_secondary', label: 'Secondary CTA Text', placeholder: 'Try Calculator'}
            ])}
          </>
        )}

        {section === 'about' && (
          <>
            {renderSection('Section Header', [
              {key: 'title', label: 'Section Title', placeholder: 'About Aureus Alliance Holdings'},
              {key: 'subtitle', label: 'Section Subtitle', placeholder: 'Pioneering the convergence...'}
            ])}
            {renderSection('Content Blocks', [
              {key: 'main_description', label: 'Main Description', type: 'textarea', placeholder: 'Aureus Alliance Holdings is pioneering...'},
              {key: 'foundation_text', label: 'Foundation Text', type: 'textarea', placeholder: 'Our foundation is built on...'},
              {key: 'ecosystem_text', label: 'Ecosystem Text', type: 'textarea', placeholder: 'The Aureus ecosystem includes...'}
            ])}
          </>
        )}

        {section === 'highlights' && (
          <>
            {renderSection('Section Header', [
              {key: 'title', label: 'Section Title', placeholder: 'Key Highlights & Value Proposition'},
              {key: 'subtitle', label: 'Section Subtitle', type: 'textarea', placeholder: 'Core strengths and financial incentives...'}
            ])}
            {renderSection('Financial Data', [
              {key: 'presale_price', label: 'Presale Price ($)', type: 'number', placeholder: '5'},
              {key: 'presale_shares', label: 'Presale Shares Available', type: 'number', placeholder: '200000'},
              {key: 'annual_revenue', label: 'Annual Mine Revenue ($M)', type: 'number', placeholder: '342'},
              {key: 'net_profit', label: 'Net Profit After Costs ($M)', type: 'number', placeholder: '188'},
              {key: 'projected_dividend_per_share', label: 'Projected Dividend Per Share ($)', type: 'number', placeholder: '134.51'}
            ])}
          </>
        )}

        {section === 'calculator' && (
          <>
            {renderSection('Calculator Settings', [
              {key: 'title', label: 'Calculator Title', placeholder: 'Financial Calculator'},
              {key: 'subtitle', label: 'Calculator Subtitle', type: 'textarea', placeholder: 'Experience the power of data-driven...'},
              {key: 'default_land_size', label: 'Default Land Size (ha)', type: 'number', placeholder: '25'},
              {key: 'default_user_shares', label: 'Default User Shares', type: 'number', placeholder: '1000'},
              {key: 'gold_price_default', label: 'Default Gold Price ($/kg)', type: 'number', placeholder: '100000'}
            ])}

            <div className="mt-8 flex justify-end">
              <button
                onClick={() => {
                  // Save all calculator settings at once
                  Object.keys(content).forEach(key => {
                    if (content[key] !== (originalContent[key] || '')) {
                      handleSave(key, content[key])
                    }
                  })
                }}
                disabled={saving}
                className="px-6 py-3 bg-gradient-to-r from-amber-500 to-yellow-600 text-black font-semibold rounded-xl hover:from-amber-400 hover:to-yellow-500 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {saving ? 'Saving...' : 'Save Calculator Settings'}
              </button>
            </div>
          </>
        )}

        {section === 'contact' && (
          <>
            {renderSection('Contact Information', [
              {key: 'email', label: 'Email Address', type: 'email', placeholder: '<EMAIL>'},
              {key: 'phone', label: 'Phone Number', type: 'tel', placeholder: '+27 11 123 4567'},
              {key: 'telegram_bot', label: 'Telegram Bot URL', type: 'url', placeholder: 'https://t.me/AureusAllianceBot'},
              {key: 'address', label: 'Address', type: 'textarea', placeholder: 'Johannesburg, South Africa'}
            ])}
          </>
        )}

        {section === 'settings' && (
          <>
            {renderSection('Site Configuration', [
              {key: 'site_name', label: 'Site Name', placeholder: 'Aureus Alliance Holdings'},
              {key: 'meta_description', label: 'Meta Description', type: 'textarea', placeholder: 'Invest in sustainable gold mining operations...'},
              {key: 'analytics_id', label: 'Google Analytics ID', placeholder: 'GA-XXXXXXXXX'}
            ])}
            {renderSection('Branding Assets', [
              {key: 'logo_url', label: 'Logo URL', type: 'url', placeholder: 'https://i.ibb.co/L542wzV/ubuntu-afrique-logo.png'},
              {key: 'favicon_url', label: 'Favicon URL', type: 'url', placeholder: 'https://example.com/favicon.ico'}
            ])}
          </>
        )}
      </div>
    </div>
  )
}
