import { supabase } from './supabase'
import crypto from 'crypto'

/**
 * BUSINESS SECURITY PROTECTION SYSTEM
 * 
 * This module implements CRITICAL business security measures to prevent
 * financial data manipulation and protect against business-destroying attacks.
 */

export interface SecurityValidationResult {
  valid: boolean
  errors: string[]
  securityLevel: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW'
  businessRisk: string
}

export interface FinancialTransaction {
  userId: number
  amount: number
  type: 'share_purchase' | 'commission_adjustment' | 'payment' | 'withdrawal'
  metadata?: any
}

export class BusinessSecurityManager {
  /**
   * Validate financial transaction security
   */
  static async validateFinancialTransaction(
    transaction: FinancialTransaction,
    adminId?: number
  ): Promise<SecurityValidationResult> {
    const errors: string[] = []
    let securityLevel: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW' = 'LOW'
    let businessRisk = 'Minimal risk'

    try {
      // 1. Validate user exists
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('id, email, created_at')
        .eq('id', transaction.userId)
        .single()

      if (userError || !user) {
        errors.push('Invalid user ID - user does not exist')
        securityLevel = 'CRITICAL'
        businessRisk = 'BUSINESS DESTROYING - Fraudulent user creation possible'
      }

      // 2. Validate amount limits
      if (transaction.amount <= 0) {
        errors.push('Invalid amount - must be positive')
        securityLevel = 'HIGH'
        businessRisk = 'Financial fraud attempt'
      }

      if (transaction.amount > 1000000) {
        errors.push('Amount exceeds maximum limit ($1,000,000)')
        securityLevel = 'CRITICAL'
        businessRisk = 'BUSINESS DESTROYING - Massive financial manipulation'
      }

      // 3. Validate admin authorization for sensitive operations
      if (['commission_adjustment'].includes(transaction.type)) {
        if (!adminId) {
          errors.push('Admin authorization required for commission adjustments')
          securityLevel = 'CRITICAL'
          businessRisk = 'BUSINESS DESTROYING - Unauthorized commission creation'
        } else {
          const adminValid = await this.validateAdminAuthorization(adminId, 'canManagePayments')
          if (!adminValid) {
            errors.push('Invalid admin authorization')
            securityLevel = 'CRITICAL'
            businessRisk = 'BUSINESS DESTROYING - Unauthorized admin access'
          }
        }
      }

      // 4. Check for suspicious patterns
      const suspiciousActivity = await this.detectSuspiciousActivity(transaction)
      if (suspiciousActivity.detected) {
        errors.push(`Suspicious activity detected: ${suspiciousActivity.reason}`)
        securityLevel = 'HIGH'
        businessRisk = 'Potential fraud or system abuse'
      }

      // 5. Validate business logic constraints
      const businessLogicValid = await this.validateBusinessLogic(transaction)
      if (!businessLogicValid.valid) {
        errors.push(...businessLogicValid.errors)
        securityLevel = 'CRITICAL'
        businessRisk = 'BUSINESS DESTROYING - Business logic bypass attempt'
      }

      return {
        valid: errors.length === 0,
        errors,
        securityLevel,
        businessRisk
      }

    } catch (error: any) {
      return {
        valid: false,
        errors: [`Security validation failed: ${error.message}`],
        securityLevel: 'CRITICAL',
        businessRisk: 'BUSINESS DESTROYING - Security system compromise'
      }
    }
  }

  /**
   * Validate admin authorization
   */
  static async validateAdminAuthorization(
    adminId: number,
    requiredPermission: string
  ): Promise<boolean> {
    try {
      const { data: admin, error } = await supabase
        .from('admin_users')
        .select('id, role, is_active')
        .eq('user_id', adminId)
        .eq('is_active', true)
        .single()

      if (error || !admin) {
        return false
      }

      // Check role permissions
      const permissions = this.getAdminPermissions(admin.role)
      return permissions[requiredPermission as keyof typeof permissions] || false

    } catch (error) {
      console.error('Admin authorization validation failed:', error)
      return false
    }
  }

  /**
   * Get admin permissions based on role
   */
  static getAdminPermissions(role: string) {
    switch (role) {
      case 'super_admin':
        return {
          canManageUsers: true,
          canManagePayments: true,
          canManageSponsors: true,
          canManageGallery: true,
          canViewAuditLogs: true,
          canManageAdmins: true,
          canAccessDebug: true
        }
      case 'admin':
        return {
          canManageUsers: true,
          canManagePayments: true,
          canManageSponsors: true,
          canManageGallery: true,
          canViewAuditLogs: true,
          canManageAdmins: false,
          canAccessDebug: false
        }
      default:
        return {
          canManageUsers: false,
          canManagePayments: false,
          canManageSponsors: false,
          canManageGallery: false,
          canViewAuditLogs: false,
          canManageAdmins: false,
          canAccessDebug: false
        }
    }
  }

  /**
   * Detect suspicious activity patterns
   */
  static async detectSuspiciousActivity(
    transaction: FinancialTransaction
  ): Promise<{ detected: boolean; reason?: string }> {
    try {
      // Check for rapid successive transactions
      const { data: recentTransactions, error } = await supabase
        .from('admin_audit_logs')
        .select('created_at, metadata')
        .eq('target_id', transaction.userId.toString())
        .gte('created_at', new Date(Date.now() - 5 * 60 * 1000).toISOString()) // Last 5 minutes
        .order('created_at', { ascending: false })

      if (!error && recentTransactions && recentTransactions.length > 10) {
        return {
          detected: true,
          reason: 'Excessive transaction frequency (>10 in 5 minutes)'
        }
      }

      // Check for unusual amounts
      if (transaction.amount > 100000) {
        return {
          detected: true,
          reason: 'Unusually large transaction amount (>$100,000)'
        }
      }

      // Check for round numbers (often indicates manual manipulation)
      if (transaction.amount % 1000 === 0 && transaction.amount > 10000) {
        return {
          detected: true,
          reason: 'Suspicious round number amount'
        }
      }

      return { detected: false }

    } catch (error) {
      console.error('Suspicious activity detection failed:', error)
      return { detected: false }
    }
  }

  /**
   * Validate business logic constraints
   */
  static async validateBusinessLogic(
    transaction: FinancialTransaction
  ): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = []

    try {
      switch (transaction.type) {
        case 'share_purchase':
          // Validate share purchase logic
          const shareValidation = await this.validateSharePurchase(transaction)
          if (!shareValidation.valid) {
            errors.push(...shareValidation.errors)
          }
          break

        case 'commission_adjustment':
          // Validate commission adjustment logic
          const commissionValidation = await this.validateCommissionAdjustment(transaction)
          if (!commissionValidation.valid) {
            errors.push(...commissionValidation.errors)
          }
          break

        case 'payment':
          // Validate payment logic
          const paymentValidation = await this.validatePayment(transaction)
          if (!paymentValidation.valid) {
            errors.push(...paymentValidation.errors)
          }
          break
      }

      return {
        valid: errors.length === 0,
        errors
      }

    } catch (error: any) {
      return {
        valid: false,
        errors: [`Business logic validation failed: ${error.message}`]
      }
    }
  }

  /**
   * Validate share purchase business logic
   */
  static async validateSharePurchase(
    transaction: FinancialTransaction
  ): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = []

    try {
      // Get current phase information
      const { data: currentPhase, error: phaseError } = await supabase
        .from('investment_phases')
        .select('*')
        .eq('is_active', true)
        .single()

      if (phaseError || !currentPhase) {
        errors.push('No active investment phase found')
        return { valid: false, errors }
      }

      // Calculate shares based on current price
      const pricePerShare = parseFloat(currentPhase.price_per_share)
      const expectedShares = transaction.amount / pricePerShare

      // Validate share calculation
      if (expectedShares <= 0) {
        errors.push('Invalid share calculation - amount too small')
      }

      // Check phase capacity
      const remainingShares = currentPhase.total_shares - (currentPhase.shares_sold || 0)
      if (expectedShares > remainingShares) {
        errors.push('Insufficient shares available in current phase')
      }

      return {
        valid: errors.length === 0,
        errors
      }

    } catch (error: any) {
      return {
        valid: false,
        errors: [`Share purchase validation failed: ${error.message}`]
      }
    }
  }

  /**
   * Validate commission adjustment business logic
   */
  static async validateCommissionAdjustment(
    transaction: FinancialTransaction
  ): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = []

    try {
      // Get current commission balance
      const { data: currentBalance, error } = await supabase
        .from('commission_balances')
        .select('*')
        .eq('user_id', transaction.userId)
        .single()

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        errors.push('Failed to retrieve current commission balance')
        return { valid: false, errors }
      }

      // Validate adjustment doesn't create negative balance
      const currentUSDT = currentBalance?.usdt_balance || 0
      const currentShares = currentBalance?.share_balance || 0

      if (transaction.metadata?.usdtAdjustment) {
        const newUSDTBalance = currentUSDT + parseFloat(transaction.metadata.usdtAdjustment)
        if (newUSDTBalance < 0) {
          errors.push('Commission adjustment would create negative USDT balance')
        }
      }

      if (transaction.metadata?.shareAdjustment) {
        const newShareBalance = currentShares + parseFloat(transaction.metadata.shareAdjustment)
        if (newShareBalance < 0) {
          errors.push('Commission adjustment would create negative share balance')
        }
      }

      return {
        valid: errors.length === 0,
        errors
      }

    } catch (error: any) {
      return {
        valid: false,
        errors: [`Commission adjustment validation failed: ${error.message}`]
      }
    }
  }

  /**
   * Validate payment business logic
   */
  static async validatePayment(
    transaction: FinancialTransaction
  ): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = []

    try {
      // Validate payment amount against current phase pricing
      const { data: currentPhase, error } = await supabase
        .from('investment_phases')
        .select('price_per_share, minimum_purchase')
        .eq('is_active', true)
        .single()

      if (!error && currentPhase) {
        const minPurchase = parseFloat(currentPhase.minimum_purchase || '0')
        if (transaction.amount < minPurchase) {
          errors.push(`Payment amount below minimum purchase requirement ($${minPurchase})`)
        }
      }

      return {
        valid: errors.length === 0,
        errors
      }

    } catch (error: any) {
      return {
        valid: false,
        errors: [`Payment validation failed: ${error.message}`]
      }
    }
  }

  /**
   * Log security event for audit trail
   */
  static async logSecurityEvent(
    eventType: string,
    userId: number,
    details: any,
    securityLevel: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW' = 'MEDIUM'
  ): Promise<void> {
    try {
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_email: 'security_system',
          action: eventType,
          target_type: 'security_event',
          target_id: userId.toString(),
          metadata: {
            security_level: securityLevel,
            details,
            timestamp: new Date().toISOString(),
            event_id: crypto.randomBytes(16).toString('hex')
          },
          created_at: new Date().toISOString()
        })

    } catch (error) {
      console.error('Failed to log security event:', error)
    }
  }

  /**
   * Secure API endpoint wrapper
   */
  static secureEndpoint(
    handler: (req: any, res: any) => Promise<any>,
    requiredPermission?: string
  ) {
    return async (req: any, res: any) => {
      try {
        // 1. Rate limiting check
        const rateLimitResult = await this.checkRateLimit(req)
        if (!rateLimitResult.allowed) {
          return res.status(429).json({
            error: 'Rate limit exceeded',
            retryAfter: rateLimitResult.retryAfter
          })
        }

        // 2. Authentication check
        const user = await this.authenticateRequest(req)
        if (!user) {
          return res.status(401).json({ error: 'Authentication required' })
        }

        // 3. Authorization check
        if (requiredPermission) {
          const authorized = await this.validateAdminAuthorization(user.id, requiredPermission)
          if (!authorized) {
            await this.logSecurityEvent('UNAUTHORIZED_ACCESS_ATTEMPT', user.id, {
              endpoint: req.url,
              permission: requiredPermission
            }, 'HIGH')

            return res.status(403).json({ error: 'Insufficient permissions' })
          }
        }

        // 4. Input validation
        const inputValidation = this.validateInput(req)
        if (!inputValidation.valid) {
          return res.status(400).json({
            error: 'Invalid input',
            details: inputValidation.errors
          })
        }

        // 5. Execute handler with security context
        req.securityContext = {
          user,
          validatedInput: inputValidation.sanitizedInput,
          securityLevel: 'AUTHENTICATED'
        }

        return await handler(req, res)

      } catch (error: any) {
        console.error('Secure endpoint error:', error)
        return res.status(500).json({ error: 'Internal server error' })
      }
    }
  }

  /**
   * Check rate limiting
   */
  static async checkRateLimit(req: any): Promise<{ allowed: boolean; retryAfter?: number }> {
    // Implementation would use Redis or in-memory store
    // For now, return allowed
    return { allowed: true }
  }

  /**
   * Authenticate request
   */
  static async authenticateRequest(req: any): Promise<any> {
    try {
      const token = req.headers.authorization?.replace('Bearer ', '')
      if (!token) {
        return null
      }

      // Validate JWT token (implementation depends on your auth system)
      // For now, return mock user
      return { id: 1, email: '<EMAIL>' }

    } catch (error) {
      return null
    }
  }

  /**
   * Validate and sanitize input
   */
  static validateInput(req: any): { valid: boolean; errors: string[]; sanitizedInput: any } {
    const errors: string[] = []
    const sanitizedInput = { ...req.body }

    // Basic input validation
    if (req.body) {
      // Check for SQL injection patterns
      const sqlPatterns = [
        /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER)\b)/i,
        /(UNION|OR|AND)\s+\d+\s*=\s*\d+/i,
        /['"]\s*(OR|AND)\s*['"]\d+['"]\s*=\s*['"]\d+['"]*/i
      ]

      for (const [key, value] of Object.entries(req.body)) {
        if (typeof value === 'string') {
          for (const pattern of sqlPatterns) {
            if (pattern.test(value)) {
              errors.push(`Potential SQL injection detected in field: ${key}`)
            }
          }

          // Sanitize HTML
          sanitizedInput[key] = value.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      sanitizedInput
    }
  }

  /**
   * Test the business security system
   */
  static async testBusinessSecurity(): Promise<boolean> {
    try {
      console.log('🧪 Testing business security system...')

      // Test 1: Valid transaction
      const validTransaction: FinancialTransaction = {
        userId: 1,
        amount: 1000,
        type: 'payment'
      }

      const validResult = await this.validateFinancialTransaction(validTransaction)
      console.log(`   Valid transaction test: ${validResult.valid ? 'PASS' : 'FAIL'}`)

      // Test 2: Invalid amount
      const invalidTransaction: FinancialTransaction = {
        userId: 1,
        amount: -100,
        type: 'payment'
      }

      const invalidResult = await this.validateFinancialTransaction(invalidTransaction)
      console.log(`   Invalid amount test: ${!invalidResult.valid ? 'PASS' : 'FAIL'}`)

      // Test 3: Excessive amount
      const excessiveTransaction: FinancialTransaction = {
        userId: 1,
        amount: 2000000,
        type: 'payment'
      }

      const excessiveResult = await this.validateFinancialTransaction(excessiveTransaction)
      console.log(`   Excessive amount test: ${!excessiveResult.valid ? 'PASS' : 'FAIL'}`)

      console.log('✅ Business security system test completed')
      return true

    } catch (error: any) {
      console.error('❌ Business security system test failed:', error)
      return false
    }
  }
}
