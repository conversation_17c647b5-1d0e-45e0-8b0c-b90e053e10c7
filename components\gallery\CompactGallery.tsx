import React, { useState } from 'react';
import { GallerySlideshow } from './GallerySlideshow';
import { GalleryGrid } from './GalleryGrid';
import { GalleryLightbox, useLightboxNavigation } from './GalleryLightbox';
import type { GalleryImage } from '../../types/gallery';

interface CompactGalleryProps {
  images: GalleryImage[];
  layout?: 'slideshow' | 'grid' | 'mixed';
  showCategories?: boolean;
  maxImages?: number;
  height?: string;
  onImageClick?: (image: GalleryImage) => void;
}

export const CompactGallery: React.FC<CompactGalleryProps> = ({
  images,
  layout = 'slideshow',
  showCategories = true,
  maxImages = 9,
  height = '400px',
  onImageClick
}) => {
  const [lightboxIndex, setLightboxIndex] = useState<number>(-1);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [viewMode, setViewMode] = useState<'slideshow' | 'grid'>(
    layout === 'mixed' ? 'slideshow' : layout as 'slideshow' | 'grid'
  );

  // Filter images by category
  const filteredImages = selectedCategory 
    ? images.filter(img => img.category_id === selectedCategory)
    : images;

  // Limit images for compact display
  const displayImages = filteredImages.slice(0, maxImages);

  // Get unique categories
  const categories = Array.from(
    new Map(
      images.map(img => [img.category_id, img.category])
    ).values()
  );

  const handleImageClick = (image: GalleryImage) => {
    if (onImageClick) {
      onImageClick(image);
    } else {
      const index = displayImages.findIndex(img => img.id === image.id);
      setLightboxIndex(index);
    }
  };

  const handleLightboxClose = () => {
    setLightboxIndex(-1);
  };

  const handleLightboxNext = () => {
    setLightboxIndex(prev => (prev + 1) % displayImages.length);
  };

  const handleLightboxPrevious = () => {
    setLightboxIndex(prev => (prev - 1 + displayImages.length) % displayImages.length);
  };

  const handleLightboxNavigate = (index: number) => {
    setLightboxIndex(index);
  };

  // Use the lightbox navigation hook
  useLightboxNavigation(displayImages, handleLightboxNavigate);

  if (displayImages.length === 0) {
    return (
      <div className="compact-gallery-empty">
        <div className="compact-gallery-empty-content">
          <div className="compact-gallery-empty-icon">📸</div>
          <h3>No images available</h3>
          <p>Check back later for updates from our operations.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="compact-gallery">
      {/* Header with Controls */}
      <div className="compact-gallery-header">
        <div className="compact-gallery-info">
          <h3 className="compact-gallery-title">
            {selectedCategory 
              ? categories.find(cat => cat.id === selectedCategory)?.name || 'Gallery'
              : 'Operations Gallery'
            }
          </h3>
          <p className="compact-gallery-subtitle">
            Showing {displayImages.length} of {filteredImages.length} images
          </p>
        </div>

        <div className="compact-gallery-controls">
          {/* View Mode Toggle */}
          {layout === 'mixed' && (
            <div className="view-mode-toggle">
              <button
                className={`view-mode-btn ${viewMode === 'slideshow' ? 'active' : ''}`}
                onClick={() => setViewMode('slideshow')}
                aria-label="Slideshow view"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" strokeWidth="2"/>
                  <circle cx="8.5" cy="8.5" r="1.5" fill="currentColor"/>
                  <path d="M21 15l-5-5L5 21" stroke="currentColor" strokeWidth="2"/>
                </svg>
              </button>
              <button
                className={`view-mode-btn ${viewMode === 'grid' ? 'active' : ''}`}
                onClick={() => setViewMode('grid')}
                aria-label="Grid view"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <rect x="3" y="3" width="7" height="7" stroke="currentColor" strokeWidth="2"/>
                  <rect x="14" y="3" width="7" height="7" stroke="currentColor" strokeWidth="2"/>
                  <rect x="3" y="14" width="7" height="7" stroke="currentColor" strokeWidth="2"/>
                  <rect x="14" y="14" width="7" height="7" stroke="currentColor" strokeWidth="2"/>
                </svg>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Category Filter */}
      {showCategories && categories.length > 1 && (
        <div className="compact-gallery-categories">
          <button
            onClick={() => setSelectedCategory('')}
            className={`category-btn ${!selectedCategory ? 'active' : ''}`}
          >
            All ({images.length})
          </button>
          {categories.map((category) => {
            const count = images.filter(img => img.category_id === category.id).length;
            return (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`category-btn ${selectedCategory === category.id ? 'active' : ''}`}
              >
                {category.name} ({count})
              </button>
            );
          })}
        </div>
      )}

      {/* Gallery Content */}
      <div className="compact-gallery-content">
        {viewMode === 'slideshow' ? (
          <GallerySlideshow
            images={displayImages}
            height={height}
            autoPlay={true}
            autoPlayInterval={6000}
            showThumbnails={displayImages.length > 1}
            showIndicators={displayImages.length > 1}
            showArrows={displayImages.length > 1}
            onImageClick={handleImageClick}
          />
        ) : (
          <div style={{ minHeight: height }}>
            <GalleryGrid
              images={displayImages}
              onImageClick={handleImageClick}
              columns={3}
              showOverlay={true}
              showCategories={false}
            />
          </div>
        )}
      </div>

      {/* View More Link */}
      {filteredImages.length > maxImages && (
        <div className="compact-gallery-footer">
          <p className="view-more-text">
            Showing {displayImages.length} of {filteredImages.length} images
          </p>
          <button className="view-more-btn">
            View All Images
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M7 17L17 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              <path d="M7 7h10v10" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            </svg>
          </button>
        </div>
      )}

      {/* Lightbox */}
      <GalleryLightbox
        images={displayImages}
        currentIndex={Math.max(0, lightboxIndex)}
        isOpen={lightboxIndex >= 0}
        onClose={handleLightboxClose}
        onNext={handleLightboxNext}
        onPrevious={handleLightboxPrevious}
      />
    </div>
  );
};
