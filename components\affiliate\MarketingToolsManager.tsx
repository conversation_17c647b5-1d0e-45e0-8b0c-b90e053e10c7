/**
 * MARKETING TOOLS MANAGER
 *
 * Comprehensive marketing tools system for affiliates including:
 * - Lead Management System
 * - Email Marketing Integration
 * - Link Generation & Sharing Tools
 * - Marketing Materials Library
 * - Communication Templates
 * - Analytics & Reporting
 *
 * Version: 5.2.3 - Fixed firstName undefined error
 */

import React, { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';
import { MarketingToolsService } from '../../lib/services/marketingToolsService';
import resendEmailService from '../../lib/resendEmailService';

interface Lead {
  id: string;
  user_id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  notes?: string;
  status: 'new' | 'contacted' | 'interested' | 'converted' | 'lost';
  source: string;
  tags?: string[];
  last_contacted_at?: string;
  converted_at?: string;
  created_at: string;
  updated_at: string;
}

interface EmailTemplate {
  id: string;
  template_name: string;
  template_type: string;
  subject_line: string;
  html_content: string;
  text_content?: string;
  description?: string;
  is_active: boolean;
  created_by?: number;
  created_at: string;
  updated_at: string;
}

interface ReferralLink {
  id: string;
  user_id: number;
  link_name: string;
  base_url: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_term?: string;
  utm_content?: string;
  custom_parameters: any;
  clicks_count: number;
  conversions_count: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface LeadList {
  id: string;
  user_id: number;
  list_name: string;
  description?: string;
  lead_count: number;
  created_at: string;
  updated_at: string;
}

interface EmailCampaign {
  id: string;
  user_id: number;
  template_id: string;
  list_id: string;
  subject: string;
  sent_count: number;
  delivered_count: number;
  failed_count: number;
  status: 'draft' | 'sending' | 'sent' | 'failed';
  created_at: string;
  sent_at?: string;
}

interface EmailSend {
  id: string;
  campaign_id: string;
  lead_id: string;
  email_address: string;
  status: 'sent' | 'delivered' | 'failed';
  sent_at?: string;
  error_message?: string;
}

interface MarketingToolsManagerProps {
  userId: number;
  affiliateData: any;
}

export const MarketingToolsManager: React.FC<MarketingToolsManagerProps> = ({
  userId,
  affiliateData
}) => {
  // Debug log to check affiliateData
  console.log('MarketingToolsManager - affiliateData:', affiliateData);

  const [activeMarketingTab, setActiveMarketingTab] = useState<'leads' | 'lists' | 'email' | 'links' | 'materials' | 'templates' | 'analytics'>('leads');
  const [leads, setLeads] = useState<Lead[]>([]);
  const [leadLists, setLeadLists] = useState<LeadList[]>([]);
  const [emailTemplates, setEmailTemplates] = useState<EmailTemplate[]>([]);
  const [emailCampaigns, setEmailCampaigns] = useState<EmailCampaign[]>([]);
  const [referralLinks, setReferralLinks] = useState<ReferralLink[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Service instance
  const marketingService = MarketingToolsService.getInstance();

  // CSV import/export state
  const [showImportModal, setShowImportModal] = useState(false);
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [importResults, setImportResults] = useState<{ success: number; errors: string[] } | null>(null);

  // Lead selection for bulk operations
  const [selectedLeads, setSelectedLeads] = useState<string[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);

  // Lead form state
  const [showAddLeadForm, setShowAddLeadForm] = useState(false);
  const [showEditLeadForm, setShowEditLeadForm] = useState(false);
  const [editingLead, setEditingLead] = useState<Lead | null>(null);
  const [leadForm, setLeadForm] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    notes: '',
    status: 'new' as const,
    source: 'manual',
    tags: '',
    selected_lists: [] as string[] // Add list selection to form
  });

  // Lead lists mapping for display
  const [leadListMemberships, setLeadListMemberships] = useState<Record<string, string[]>>({});

  // Link form state
  const [showAddLinkForm, setShowAddLinkForm] = useState(false);
  const [linkForm, setLinkForm] = useState({
    link_name: '',
    utm_source: '',
    utm_medium: '',
    utm_campaign: '',
    utm_term: '',
    utm_content: ''
  });

  // Lead list state
  const [showAddListForm, setShowAddListForm] = useState(false);
  const [listForm, setListForm] = useState({
    list_name: '',
    description: ''
  });
  const [selectedListId, setSelectedListId] = useState<string>('');
  const [showAssignLeadsModal, setShowAssignLeadsModal] = useState(false);

  // Email template state
  const [showEmailPreview, setShowEmailPreview] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null);
  const [showEmailComposer, setShowEmailComposer] = useState(false);
  const [emailComposer, setEmailComposer] = useState({
    to: '',
    subject: '',
    template_id: '',
    list_id: ''
  });
  const [emailSending, setEmailSending] = useState(false);
  const [sendProgress, setSendProgress] = useState({ sent: 0, total: 0 });

  useEffect(() => {
    loadMarketingData();
  }, [userId]);

  const loadMarketingData = async () => {
    try {
      setLoading(true);
      const serviceClient = getServiceRoleClient();

      // Load leads
      const { data: leadsData, error: leadsError } = await serviceClient
        .from('affiliate_leads')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (leadsError) throw leadsError;

      // Load email templates using the service (which filters out system templates)
      const marketingService = new MarketingToolsService();
      const templatesData = await marketingService.getEmailTemplates();
      const templatesError = null; // Service handles errors internally

      if (templatesError) throw templatesError;

      // Load lead lists
      const { data: listsData, error: listsError } = await serviceClient
        .from('lead_lists')
        .select(`
          *,
          lead_count:lead_list_members(count)
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (listsError) throw listsError;

      // Load email campaigns
      const { data: campaignsData, error: campaignsError } = await serviceClient
        .from('email_campaigns')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (campaignsError) throw campaignsError;

      // Load referral links
      const { data: linksData, error: linksError } = await serviceClient
        .from('referral_links')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (linksError) throw linksError;

      // Load lead list memberships for display
      const { data: membershipsData, error: membershipsError } = await serviceClient
        .from('lead_list_members')
        .select(`
          lead_id,
          list_id,
          lead_lists!inner(list_name, user_id)
        `)
        .eq('lead_lists.user_id', userId);

      if (membershipsError) throw membershipsError;

      setLeads(leadsData || []);

      // Process lead lists with proper lead counts
      const processedLists = (listsData || []).map(list => ({
        ...list,
        lead_count: Array.isArray(list.lead_count) ? list.lead_count.length :
                   (list.lead_count && typeof list.lead_count === 'object' && list.lead_count.count !== undefined) ? list.lead_count.count :
                   (typeof list.lead_count === 'number') ? list.lead_count : 0
      }));
      setLeadLists(processedLists);

      setEmailTemplates(templatesData || []);
      setEmailCampaigns(campaignsData || []);
      setReferralLinks(linksData || []);

      // Process lead list memberships for display
      const memberships: Record<string, string[]> = {};
      (membershipsData || []).forEach((membership: any) => {
        if (!memberships[membership.lead_id]) {
          memberships[membership.lead_id] = [];
        }
        memberships[membership.lead_id].push(membership.lead_lists.list_name);
      });
      setLeadListMemberships(memberships);

    } catch (err) {
      console.error('Error loading marketing data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load marketing data');
    } finally {
      setLoading(false);
    }
  };

  const handleAddLead = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const serviceClient = getServiceRoleClient();

      const leadData = {
        first_name: leadForm.first_name,
        last_name: leadForm.last_name,
        email: leadForm.email,
        phone: leadForm.phone,
        notes: leadForm.notes,
        status: leadForm.status,
        source: leadForm.source,
        user_id: userId,
        tags: leadForm.tags ? leadForm.tags.split(',').map(tag => tag.trim()) : []
      };

      const { data: newLead, error } = await serviceClient
        .from('affiliate_leads')
        .insert(leadData)
        .select()
        .single();

      if (error) throw error;

      // If lists are selected, add the lead to those lists
      if (leadForm.selected_lists.length > 0) {
        const listMemberData = leadForm.selected_lists.map(listId => ({
          list_id: listId,
          lead_id: newLead.id,
          added_at: new Date().toISOString()
        }));

        const { error: listError } = await serviceClient
          .from('lead_list_members')
          .insert(listMemberData);

        if (listError) {
          console.error('Error adding lead to lists:', listError);
          // Don't fail the entire operation, just log the error
        } else {
          // Update lead counts for affected lists
          setLeadLists(prev => prev.map(list =>
            leadForm.selected_lists.includes(list.id)
              ? { ...list, lead_count: list.lead_count + 1 }
              : list
          ));
        }
      }

      setLeads(prev => [newLead, ...prev]);
      setShowAddLeadForm(false);
      setLeadForm({
        first_name: '',
        last_name: '',
        email: '',
        phone: '',
        notes: '',
        status: 'new',
        source: 'manual',
        tags: '',
        selected_lists: []
      });

      setSuccess('Lead added successfully!');
      setTimeout(() => setSuccess(null), 3000);

    } catch (err) {
      console.error('Error adding lead:', err);
      setError(err instanceof Error ? err.message : 'Failed to add lead');
    }
  };

  const handleAddReferralLink = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const serviceClient = getServiceRoleClient();
      
      // Generate the referral URL with UTM parameters
      const username = affiliateData?.user?.username || 'affiliate';
      const baseUrl = `${window.location.origin}/${username}`;
      const urlParams = new URLSearchParams();
      
      if (linkForm.utm_source) urlParams.append('utm_source', linkForm.utm_source);
      if (linkForm.utm_medium) urlParams.append('utm_medium', linkForm.utm_medium);
      if (linkForm.utm_campaign) urlParams.append('utm_campaign', linkForm.utm_campaign);
      if (linkForm.utm_term) urlParams.append('utm_term', linkForm.utm_term);
      if (linkForm.utm_content) urlParams.append('utm_content', linkForm.utm_content);

      const fullUrl = urlParams.toString() ? `${baseUrl}?${urlParams.toString()}` : baseUrl;

      const linkData = {
        ...linkForm,
        user_id: userId,
        base_url: fullUrl
      };

      const { data, error } = await serviceClient
        .from('referral_links')
        .insert(linkData)
        .select()
        .single();

      if (error) throw error;

      setReferralLinks(prev => [data, ...prev]);
      setShowAddLinkForm(false);
      setLinkForm({
        link_name: '',
        utm_source: '',
        utm_medium: '',
        utm_campaign: '',
        utm_term: '',
        utm_content: ''
      });

    } catch (err) {
      console.error('Error adding referral link:', err);
      setError(err instanceof Error ? err.message : 'Failed to add referral link');
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setSuccess('Link copied to clipboard!');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
      setError('Failed to copy to clipboard');
    }
  };

  // Lead List Management Functions
  const handleAddList = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const serviceClient = getServiceRoleClient();

      const listData = {
        ...listForm,
        user_id: userId
      };

      const { data, error } = await serviceClient
        .from('lead_lists')
        .insert(listData)
        .select()
        .single();

      if (error) throw error;

      setLeadLists(prev => [{ ...data, lead_count: 0 }, ...prev]);
      setShowAddListForm(false);
      setListForm({
        list_name: '',
        description: ''
      });
      setSuccess('Lead list created successfully!');
      setTimeout(() => setSuccess(null), 3000);

    } catch (err) {
      console.error('Error adding lead list:', err);
      setError(err instanceof Error ? err.message : 'Failed to add lead list');
    }
  };

  const handleDeleteList = async (listId: string) => {
    if (!confirm('Are you sure you want to delete this list? This will also remove all lead assignments.')) {
      return;
    }

    try {
      const serviceClient = getServiceRoleClient();

      // Delete list members first
      await serviceClient
        .from('lead_list_members')
        .delete()
        .eq('list_id', listId);

      // Delete the list
      const { error } = await serviceClient
        .from('lead_lists')
        .delete()
        .eq('id', listId);

      if (error) throw error;

      setLeadLists(prev => prev.filter(list => list.id !== listId));
      setSuccess('Lead list deleted successfully!');
      setTimeout(() => setSuccess(null), 3000);

    } catch (err) {
      console.error('Error deleting lead list:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete lead list');
    }
  };

  const handleAssignLeadsToList = async () => {
    if (!selectedListId || selectedLeads.length === 0) {
      setError('Please select a list and at least one lead');
      return;
    }

    try {
      const serviceClient = getServiceRoleClient();

      // Create lead list member records
      const memberData = selectedLeads.map(leadId => ({
        list_id: selectedListId,
        lead_id: leadId,
        added_at: new Date().toISOString()
      }));

      const { error } = await serviceClient
        .from('lead_list_members')
        .insert(memberData);

      if (error) throw error;

      // Update lead count for the list
      const { data: countData } = await serviceClient
        .from('lead_list_members')
        .select('id', { count: 'exact' })
        .eq('list_id', selectedListId);

      setLeadLists(prev => prev.map(list =>
        list.id === selectedListId
          ? { ...list, lead_count: countData?.length || 0 }
          : list
      ));

      setSelectedLeads([]);
      setShowAssignLeadsModal(false);
      setSuccess(`${selectedLeads.length} leads assigned to list successfully!`);
      setTimeout(() => setSuccess(null), 3000);

    } catch (err) {
      console.error('Error assigning leads to list:', err);
      setError(err instanceof Error ? err.message : 'Failed to assign leads to list');
    }
  };

  const copyTemplate = async (template: string) => {
    try {
      // Safely access affiliate data with fallbacks
      const username = affiliateData?.user?.username || 'affiliate';
      const firstName = affiliateData?.user?.first_name || '';
      const lastName = affiliateData?.user?.last_name || '';
      const affiliateName = firstName && lastName ? `${firstName} ${lastName}` : username;
      const affiliateLink = `${window.location.origin}/${username}`;

      // Process merge fields with affiliate data
      const processedTemplate = template
        .replace(/{firstName}/g, '[FIRST_NAME]')
        .replace(/{lastName}/g, '[LAST_NAME]')
        .replace(/{affiliateLink}/g, affiliateLink)
        .replace(/{affiliateName}/g, affiliateName);

      await navigator.clipboard.writeText(processedTemplate);
      setSuccess('Template copied to clipboard!');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error('Failed to copy template:', err);
      setError('Failed to copy template');
    }
  };

  // Email Campaign Functions
  const handleSendEmail = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedTemplate || (!emailComposer.to && !emailComposer.list_id)) {
      setError('Please select a template and specify recipients');
      return;
    }

    try {
      setEmailSending(true);
      setError(null);
      const serviceClient = getServiceRoleClient();

      // Get affiliate data for merge fields
      const username = affiliateData?.user?.username || 'affiliate';
      const firstName = affiliateData?.user?.first_name || '';
      const lastName = affiliateData?.user?.last_name || '';
      const affiliateName = firstName && lastName ? `${firstName} ${lastName}` : username;
      const affiliateLink = `${window.location.origin}/${username}`;

      let recipients: { email: string; firstName?: string; lastName?: string; id?: string }[] = [];

      if (emailComposer.list_id) {
        // Send to list
        const { data: listMembers, error: listError } = await serviceClient
          .from('lead_list_members')
          .select(`
            lead_id,
            affiliate_leads!inner(id, email, first_name, last_name)
          `)
          .eq('list_id', emailComposer.list_id);

        if (listError) throw listError;

        recipients = listMembers?.map(member => ({
          email: member.affiliate_leads.email,
          firstName: member.affiliate_leads.first_name,
          lastName: member.affiliate_leads.last_name,
          id: member.affiliate_leads.id
        })) || [];
      } else {
        // Send to single recipient
        recipients = [{ email: emailComposer.to }];
      }

      if (recipients.length === 0) {
        setError('No recipients found');
        return;
      }

      // Create email campaign
      const campaignName = emailComposer.list_id
        ? `${selectedTemplate.template_name} to List`
        : `${selectedTemplate.template_name} to ${emailComposer.to}`;

      const campaignData = {
        campaign_name: campaignName,
        user_id: userId,
        template_id: selectedTemplate.id,
        list_id: emailComposer.list_id || null,
        subject: emailComposer.subject,
        content: selectedTemplate.html_content, // Add the template content
        sent_count: 0,
        delivered_count: 0,
        failed_count: 0,
        status: 'sending' as const
      };

      const { data: campaign, error: campaignError } = await serviceClient
        .from('email_campaigns')
        .insert(campaignData)
        .select()
        .single();

      if (campaignError) throw campaignError;

      setSendProgress({ sent: 0, total: recipients.length });

      let successCount = 0;
      let failedCount = 0;
      const emailSends: any[] = [];

      // Send emails to each recipient
      for (let i = 0; i < recipients.length; i++) {
        const recipient = recipients[i];

        try {
          // Process template with merge fields
          let htmlContent = selectedTemplate.html_content
            .replace(/{firstName}/g, recipient.firstName || 'Friend')
            .replace(/{lastName}/g, recipient.lastName || '')
            .replace(/{affiliateLink}/g, affiliateLink)
            .replace(/{affiliateName}/g, affiliateName);

          // Add header and footer
          const fullEmailContent = generateEmailHeader() +
                                 '<div style="padding: 20px; background: white;">' +
                                 htmlContent +
                                 '</div>' +
                                 generateEmailFooter();

          // Send email using same pattern as 2FA PIN emails (no backend URL prefix)
          const response = await fetch('/api/send-marketing-email', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              to: recipient.email,
              subject: emailComposer.subject,
              htmlContent: fullEmailContent,
              textContent: selectedTemplate.text_content || undefined,
              userId: userId,
              templateId: selectedTemplate.id,
              campaignId: campaign.id
            })
          });

          const emailResult = await response.json();

          const sendRecord = {
            campaign_id: campaign.id,
            lead_id: recipient.id || null,
            email_address: recipient.email,
            status: emailResult.success ? 'sent' : 'failed',
            sent_at: emailResult.success ? new Date().toISOString() : null,
            error_message: emailResult.error || null
          };

          emailSends.push(sendRecord);

          if (emailResult.success) {
            successCount++;
            console.log(`✅ Email sent successfully to ${recipient.email}:`, emailResult.messageId);
          } else {
            failedCount++;
            console.error(`❌ Failed to send email to ${recipient.email}:`, emailResult.error);
          }

          setSendProgress({ sent: i + 1, total: recipients.length });

          // Rate limiting delay
          await new Promise(resolve => setTimeout(resolve, 1000));

        } catch (emailError) {
          failedCount++;
          console.error(`❌ Error sending email to ${recipient.email}:`, emailError);

          emailSends.push({
            campaign_id: campaign.id,
            lead_id: recipient.id || null,
            email_address: recipient.email,
            status: 'failed',
            sent_at: null,
            error_message: emailError instanceof Error ? emailError.message : 'Unknown error'
          });
        }
      }

      // Save email send records
      if (emailSends.length > 0) {
        await serviceClient
          .from('email_sends')
          .insert(emailSends);
      }

      // Update campaign with final counts
      await serviceClient
        .from('email_campaigns')
        .update({
          sent_count: successCount,
          failed_count: failedCount,
          status: failedCount === 0 ? 'sent' : (successCount > 0 ? 'sent' : 'failed'),
          sent_at: new Date().toISOString()
        })
        .eq('id', campaign.id);

      // Refresh campaigns list
      loadMarketingData();

      setSuccess(`Email campaign completed! ${successCount} sent, ${failedCount} failed`);
      setShowEmailComposer(false);
      setEmailComposer({
        to: '',
        subject: '',
        template_id: '',
        list_id: ''
      });

    } catch (err) {
      console.error('Error sending email campaign:', err);
      setError(err instanceof Error ? err.message : 'Failed to send email campaign');
    } finally {
      setEmailSending(false);
      setSendProgress({ sent: 0, total: 0 });
    }
  };

  const handleCSVImport = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!csvFile) return;

    try {
      const csvText = await csvFile.text();
      const results = await marketingService.importLeadsFromCSV(userId, csvText);
      setImportResults(results);

      if (results.success > 0) {
        setSuccess(`Successfully imported ${results.success} leads!`);
        loadMarketingData(); // Refresh the leads list
      }

      if (results.errors.length > 0) {
        setError(`Import completed with ${results.errors.length} errors. Check the results below.`);
      }
    } catch (err) {
      console.error('Error importing CSV:', err);
      setError('Failed to import CSV file');
    }
  };

  const handleCSVExport = async () => {
    try {
      const csvContent = await marketingService.exportLeadsToCSV(userId);
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `leads_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      setSuccess('Leads exported successfully!');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error('Error exporting CSV:', err);
      setError('Failed to export leads');
    }
  };

  const handleBulkDelete = async () => {
    if (selectedLeads.length === 0) return;

    if (!confirm(`Are you sure you want to delete ${selectedLeads.length} leads? This action cannot be undone.`)) {
      return;
    }

    try {
      const results = await marketingService.bulkDeleteLeads(userId, selectedLeads);
      setSuccess(`Successfully deleted ${results.success} leads!`);
      setSelectedLeads([]);
      setShowBulkActions(false);
      loadMarketingData(); // Refresh the leads list
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error('Error deleting leads:', err);
      setError('Failed to delete leads');
    }
  };

  // Edit Lead Functions
  const handleEditLead = (lead: Lead) => {
    setEditingLead(lead);
    setLeadForm({
      first_name: lead.first_name,
      last_name: lead.last_name,
      email: lead.email,
      phone: lead.phone || '',
      notes: lead.notes || '',
      status: lead.status,
      source: lead.source,
      tags: lead.tags ? lead.tags.join(', ') : '',
      selected_lists: leadListMemberships[lead.id] ?
        leadLists.filter(list => leadListMemberships[lead.id].includes(list.list_name)).map(list => list.id) : []
    });
    setShowEditLeadForm(true);
  };

  const handleUpdateLead = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingLead) return;

    try {
      const serviceClient = getServiceRoleClient();

      const leadData = {
        first_name: leadForm.first_name,
        last_name: leadForm.last_name,
        email: leadForm.email,
        phone: leadForm.phone,
        notes: leadForm.notes,
        status: leadForm.status,
        source: leadForm.source,
        tags: leadForm.tags ? leadForm.tags.split(',').map(tag => tag.trim()) : [],
        updated_at: new Date().toISOString()
      };

      const { error } = await serviceClient
        .from('affiliate_leads')
        .update(leadData)
        .eq('id', editingLead.id);

      if (error) throw error;

      // Handle list memberships update
      const currentListIds = leadListMemberships[editingLead.id] ?
        leadLists.filter(list => leadListMemberships[editingLead.id].includes(list.list_name)).map(list => list.id) : [];

      const listsToAdd = leadForm.selected_lists.filter(listId => !currentListIds.includes(listId));
      const listsToRemove = currentListIds.filter(listId => !leadForm.selected_lists.includes(listId));

      // Remove from lists
      if (listsToRemove.length > 0) {
        await serviceClient
          .from('lead_list_members')
          .delete()
          .eq('lead_id', editingLead.id)
          .in('list_id', listsToRemove);
      }

      // Add to new lists
      if (listsToAdd.length > 0) {
        const listMemberData = listsToAdd.map(listId => ({
          list_id: listId,
          lead_id: editingLead.id,
          added_at: new Date().toISOString()
        }));

        await serviceClient
          .from('lead_list_members')
          .insert(listMemberData);
      }

      // Refresh data
      loadMarketingData();
      setShowEditLeadForm(false);
      setEditingLead(null);
      setLeadForm({
        first_name: '',
        last_name: '',
        email: '',
        phone: '',
        notes: '',
        status: 'new',
        source: 'manual',
        tags: '',
        selected_lists: []
      });

      setSuccess('Lead updated successfully!');
      setTimeout(() => setSuccess(null), 3000);

    } catch (err) {
      console.error('Error updating lead:', err);
      setError(err instanceof Error ? err.message : 'Failed to update lead');
    }
  };

  // Delete Lead Function
  const handleDeleteLead = async (leadId: string) => {
    if (!confirm('Are you sure you want to delete this lead? This action cannot be undone.')) {
      return;
    }

    try {
      const serviceClient = getServiceRoleClient();

      // Delete lead list memberships first
      await serviceClient
        .from('lead_list_members')
        .delete()
        .eq('lead_id', leadId);

      // Delete the lead
      const { error } = await serviceClient
        .from('affiliate_leads')
        .delete()
        .eq('id', leadId);

      if (error) throw error;

      // Refresh data
      loadMarketingData();
      setSuccess('Lead deleted successfully!');
      setTimeout(() => setSuccess(null), 3000);

    } catch (err) {
      console.error('Error deleting lead:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete lead');
    }
  };

  const toggleLeadSelection = (leadId: string) => {
    setSelectedLeads(prev =>
      prev.includes(leadId)
        ? prev.filter(id => id !== leadId)
        : [...prev, leadId]
    );
  };

  const toggleSelectAll = () => {
    if (selectedLeads.length === leads.length) {
      setSelectedLeads([]);
    } else {
      setSelectedLeads(leads.map(lead => lead.id));
    }
  };

  // Email template handlers
  const generateEmailHeader = () => {
    return `
      <table width="100%" cellpadding="0" cellspacing="0" style="background: #ffffff; border-collapse: collapse;">
        <tr>
          <td style="padding: 15px; text-align: center;">
            <table width="100%" cellpadding="0" cellspacing="0" style="max-width: 600px; margin: 0 auto;">
              <tr>
                <td style="text-align: center;">
                  <img src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-s.png"
                       alt="Aureus Alliance Holdings"
                       style="height: 70px; display: block; margin: 0 auto 8px auto;" />
                </td>
              </tr>
              <tr>
                <td style="text-align: center;">
                  <h1 style="color: #2c3e50; margin: 0 !important; padding: 0; font-size: 28px; font-weight: 700; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; letter-spacing: -0.5px; line-height: 1.2;">
                    Aureus Alliance Holdings (Pty) Ltd
                  </h1>
                </td>
              </tr>
              <tr>
                <td style="text-align: center;">
                  <p style="color: #d4af37; margin: 0 !important; padding: 3px 0 0 0; font-size: 16px; font-weight: 600; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.2;">
                    Professional Gold Mining Company
                  </p>
                </td>
              </tr>
              <tr>
                <td style="text-align: center;">
                  <p style="color: #7f8c8d; margin: 0 !important; padding: 2px 0 0 0; font-size: 13px; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.2;">
                    CIPC Registration: 2025/368711/07
                  </p>
                </td>
              </tr>
            </table>
          </td>
        </tr>
        <tr>
          <td style="height: 3px; background: linear-gradient(90deg, #d4af37 0%, #f1c40f 50%, #d4af37 100%);"></td>
        </tr>
      </table>
    `;
  };

  const generateEmailFooter = () => {
    return `
      <table width="100%" cellpadding="0" cellspacing="0" style="background: #f8f9fa; border-collapse: collapse; margin-top: 20px;">
        <tr>
          <td style="height: 3px; background: linear-gradient(90deg, #d4af37 0%, #f1c40f 50%, #d4af37 100%);"></td>
        </tr>
        <tr>
          <td style="padding: 20px 15px;">
            <table width="100%" cellpadding="0" cellspacing="0" style="max-width: 600px; margin: 0 auto;">
              <tr>
                <td style="text-align: center;">
                  <h3 style="color: #2c3e50; margin: 0 !important; padding: 0 0 8px 0; font-size: 20px; font-weight: 600; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.2;">
                    Contact Information
                  </h3>
                </td>
              </tr>
              <tr>
                <td>
                  <table width="100%" cellpadding="0" cellspacing="0">
                    <tr>
                      <td style="width: 50%; padding: 2px 8px; vertical-align: top;">
                        <p style="margin: 0 !important; padding: 0; color: #34495e; font-size: 14px; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.3;">
                          <strong style="color: #2c3e50;">Email:</strong> <EMAIL>
                        </p>
                      </td>
                      <td style="width: 50%; padding: 2px 8px; vertical-align: top;">
                        <p style="margin: 0 !important; padding: 0; color: #34495e; font-size: 14px; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.3;">
                          <strong style="color: #2c3e50;">Info:</strong> <EMAIL>
                        </p>
                      </td>
                    </tr>
                    <tr>
                      <td style="width: 50%; padding: 2px 8px; vertical-align: top;">
                        <p style="margin: 0 !important; padding: 0; color: #34495e; font-size: 14px; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.3;">
                          <strong style="color: #2c3e50;">Website:</strong>
                          <a href="https://www.aureus.africa" style="color: #d4af37; text-decoration: none; font-weight: 500;">www.aureus.africa</a>
                        </p>
                      </td>
                      <td style="width: 50%; padding: 2px 8px; vertical-align: top;">
                        <p style="margin: 0 !important; padding: 0; color: #34495e; font-size: 14px; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.3;">
                          <strong style="color: #2c3e50;">Phone:</strong> +27 74 449 3251
                        </p>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
              <tr>
                <td style="text-align: center; padding: 5px 8px 10px 8px;">
                  <p style="margin: 0 !important; padding: 0; color: #34495e; font-size: 14px; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.3;">
                    <strong style="color: #2c3e50;">Address:</strong> 1848 Mees Avenue, Randpark Ridge, Randburg, Gauteng, 2169
                  </p>
                </td>
              </tr>
              <tr>
                <td style="border-top: 1px solid #e9ecef; padding-top: 10px; text-align: center;">
                  <p style="color: #6c757d; font-size: 13px; margin: 0 !important; padding: 0 0 3px 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.3;">
                    © ${new Date().getFullYear()} Aureus Alliance Holdings (Pty) Ltd. All rights reserved.
                  </p>
                  <p style="color: #868e96; font-size: 12px; margin: 0 !important; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.3;">
                    This email was sent from our professional marketing system.
                    <a href="#" style="color: #d4af37; text-decoration: none; font-weight: 500;">Unsubscribe</a>
                  </p>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    `;
  };

  const handlePreviewTemplate = (template: EmailTemplate) => {
    setSelectedTemplate(template);
    setShowEmailPreview(true);
  };

  const handleUseTemplate = (template: EmailTemplate) => {
    setEmailComposer({
      to: '',
      subject: template.subject_line,
      template_id: template.id,
      list_id: ''
    });
    setSelectedTemplate(template);
    setShowEmailComposer(true);
  };

  // Create Aureus Opportunity Introduction Template
  const createAureusOpportunityTemplate = async () => {
    try {
      const serviceClient = getServiceRoleClient();

      const templateData = {
        template_name: 'Aureus Opportunity Introduction',
        template_type: 'marketing',
        subject_line: 'Discover Gold-Backed Share Opportunities with Aureus Alliance Holdings',
        description: 'Professional introduction to Aureus Alliance Holdings gold-backed share opportunities',
        html_content: `
          <div style="max-width: 600px; margin: 0 auto; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">

            <!-- Header CTA -->
            <div style="text-align: center; margin: 20px 0;">
              <a href="{affiliateLink}" style="background-color: #d4af37; color: #000000; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                🌟 Discover Gold-Backed Opportunities
              </a>
            </div>

            <h2 style="color: #2c3e50; margin-bottom: 20px;">Hello {firstName},</h2>

            <p style="line-height: 1.6; margin-bottom: 15px;">
              I wanted to share an exciting opportunity that combines the stability of gold with the growth potential of an expanding business across Africa.
            </p>

            <h3 style="color: #d4af37; margin: 25px 0 15px 0;">About Aureus Alliance Holdings</h3>
            <p style="line-height: 1.6; margin-bottom: 15px;">
              A registered South African holdings company with a plan to build a diverse portfolio of ventures across Africa, including mining, property development, and AI technology.
            </p>
            <ul style="line-height: 1.8; margin-bottom: 20px; padding-left: 20px;">
              <li>Shareholders receive a CIPC-registered share certificate.</li>
            </ul>

            <h3 style="color: #d4af37; margin: 25px 0 15px 0;">The Aureus Advantage</h3>
            <p style="line-height: 1.6; margin-bottom: 15px;">
              Unlike insurance or pensions that gives instant large coverage for when you need it most but can expire if you cant afford it anymore or life throws you a curveball, Aureus offers a buy once, own forever model with no monthly fees or expiry dates. The goal is to provide generational dividends year after year, building a financial legacy.
            </p>

            <h3 style="color: #d4af37; margin: 25px 0 15px 0;">NFT Share Certificates</h3>
            <p style="line-height: 1.6; margin-bottom: 15px;">
              All 1.4 million shares will be converted into unique, blockchain-secured NFTs once all phases are sold.
            </p>
            <ul style="line-height: 1.8; margin-bottom: 20px; padding-left: 20px;">
              <li>Your NFT is a digital share certificate backed by real-world assets and profit-generating ventures, unlike many other NFTs.</li>
              <li>Dividends will be paid directly to the NFT via an automated smart contract. The ownership of the NFT dictates who receives the dividends.</li>
              <li>An optional Aureus Crypto, Visa, or Mastercard will allow for instant spending of crypto dividends.</li>
            </ul>

            <h3 style="color: #d4af37; margin: 25px 0 15px 0;">Share Pricing & Availability</h3>
            <p style="line-height: 1.6; margin-bottom: 15px;">
              1.4 million shares are available across 20 phases.
            </p>
            <ul style="line-height: 1.8; margin-bottom: 20px; padding-left: 20px;">
              <li><strong>Presale:</strong> First 200,000 shares at $5 each.</li>
              <li><strong>Main Phases:</strong> Prices increase incrementally from $10 up to $1,000 for the final 50,000 shares.</li>
            </ul>

            <h3 style="color: #d4af37; margin: 25px 0 15px 0;">Projected Growth & Dividends</h3>
            <p style="line-height: 1.6; margin-bottom: 15px;">
              Our growth is based on a plan to build over 200 eco-friendly wash plants.
            </p>
            <ul style="line-height: 1.8; margin-bottom: 20px; padding-left: 20px;">
              <li><strong>2026:</strong> Projected $200 per share (from 10 wash plants).</li>
              <li><strong>2028:</strong> Projected $1,000 per share (from 50 wash plants).</li>
              <li><strong>2030:</strong> Projected $4,000 per share (from 200+ wash plants).</li>
            </ul>

            <h3 style="color: #d4af37; margin: 25px 0 15px 0;">Key Partnerships</h3>
            <p style="line-height: 1.6; margin-bottom: 15px;">
              <strong>SUN (Smart United Network):</strong> Our dedicated software and sales partner. SUN receives 25% of every share sold, and Aureus holds a 15% ownership stake in SUN.
            </p>

            <h3 style="color: #d4af37; margin: 25px 0 15px 0;">Community Impact</h3>
            <p style="line-height: 1.6; margin-bottom: 15px;">
              We are committed to reinvesting a significant portion of earnings into local communities to fund housing, clean water, and schools. We plan to build a state-of-the-art hospital in Kadoma, Zimbabwe, by 2030.
            </p>

            <!-- Main CTA Button -->
            <div style="text-align: center; margin: 30px 0;">
              <a href="{affiliateLink}" style="background-color: #d4af37; color: #000000; padding: 15px 40px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block; font-size: 18px;">
                🚀 Explore Share Opportunities
              </a>
            </div>

            <h3 style="color: #d4af37; margin: 25px 0 15px 0;">How to Join</h3>
            <ul style="line-height: 1.8; margin-bottom: 20px; padding-left: 20px;">
              <li><strong>Payments:</strong> We accept South African Rands (EFT) and USDT cryptocurrency.</li>
              <li><strong>Affiliate Program:</strong> Earn a 15% direct commission on every new shareholder you refer, plus presale bonuses and prizes.</li>
            </ul>

            <p style="line-height: 1.6; margin-bottom: 15px;">
              I believe this opportunity aligns perfectly with building long-term wealth through real assets. If you're interested in learning more, I'd be happy to answer any questions you might have.
            </p>

            <p style="line-height: 1.6; margin-bottom: 15px;">
              Best regards,<br>
              {affiliateName}
            </p>

            <!-- Footer CTA -->
            <div style="text-align: center; margin: 30px 0; padding: 20px; background-color: #f8f9fa; border-radius: 8px;">
              <p style="margin-bottom: 15px; color: #2c3e50; font-weight: bold;">Ready to secure your financial future with gold-backed shares?</p>
              <a href="{affiliateLink}" style="background-color: #d4af37; color: #000000; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                Get Started Today
              </a>
            </div>

          </div>
        `,
        text_content: `
Hello {firstName},

I wanted to share an exciting opportunity that combines the stability of gold with the growth potential of an expanding business across Africa.

About Aureus Alliance Holdings:
A registered South African holdings company with a plan to build a diverse portfolio of ventures across Africa, including mining, property development, and AI technology.
* Shareholders receive a CIPC-registered share certificate.

The Aureus Advantage:
Unlike insurance or pensions that gives instant large coverage for when you need it most but can expire if you cant afford it anymore or life throws you a curveball, Aureus offers a buy once, own forever model with no monthly fees or expiry dates. The goal is to provide generational dividends year after year, building a financial legacy.

NFT Share Certificates:
All 1.4 million shares will be converted into unique, blockchain-secured NFTs once all phases are sold.
* Your NFT is a digital share certificate backed by real-world assets and profit-generating ventures, unlike many other NFTs.
* Dividends will be paid directly to the NFT via an automated smart contract. The ownership of the NFT dictates who receives the dividends.
* An optional Aureus Crypto, Visa, or Mastercard will allow for instant spending of crypto dividends.

Share Pricing & Availability:
1.4 million shares are available across 20 phases.
* Presale: First 200,000 shares at $5 each.
* Main Phases: Prices increase incrementally from $10 up to $1,000 for the final 50,000 shares.

Projected Growth & Dividends:
Our growth is based on a plan to build over 200 eco-friendly wash plants.
* 2026: Projected $200 per share (from 10 wash plants).
* 2028: Projected $1,000 per share (from 50 wash plants).
* 2030: Projected $4,000 per share (from 200+ wash plants).

Key Partnerships:
SUN (Smart United Network): Our dedicated software and sales partner. SUN receives 25% of every share sold, and Aureus holds a 15% ownership stake in SUN.

Community Impact:
We are committed to reinvesting a significant portion of earnings into local communities to fund housing, clean water, and schools. We plan to build a state-of-the-art hospital in Kadoma, Zimbabwe, by 2030.

How to Join:
* Payments: We accept South African Rands (EFT) and USDT cryptocurrency.
* Affiliate Program: Earn a 15% direct commission on every new shareholder you refer, plus presale bonuses and prizes.

I believe this opportunity aligns perfectly with building long-term wealth through real assets. If you're interested in learning more, I'd be happy to answer any questions you might have.

Learn more: {affiliateLink}

Best regards,
{affiliateName}
        `,
        is_active: true,
        created_by: userId
      };

      const { data, error } = await serviceClient
        .from('email_templates')
        .insert(templateData)
        .select()
        .single();

      if (error) throw error;

      setEmailTemplates(prev => [data, ...prev]);
      setSuccess('Aureus Opportunity Introduction template created successfully!');
      setTimeout(() => setSuccess(null), 3000);

    } catch (err) {
      console.error('Error creating Aureus template:', err);
      setError(err instanceof Error ? err.message : 'Failed to create template');
    }
  };



  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600"></div>
        <span className="ml-3 text-gray-300">Loading marketing tools...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Marketing Tools Navigation */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-1">
        <div className="flex space-x-1 overflow-x-auto">
          {[
            { id: 'leads', label: '👥 Lead Management', icon: '👥' },
            { id: 'lists', label: '📋 Lead Lists', icon: '📋' },
            { id: 'email', label: '📧 Email Marketing', icon: '📧' },
            { id: 'links', label: '🔗 Referral Links', icon: '🔗' },
            { id: 'materials', label: '📁 Materials', icon: '📁' },
            { id: 'templates', label: '📝 Templates', icon: '📝' },
            { id: 'analytics', label: '📊 Analytics', icon: '📊' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveMarketingTab(tab.id as any)}
              className={`flex-shrink-0 px-4 py-3 rounded-lg text-sm font-medium transition-colors ${
                activeMarketingTab === tab.id
                  ? 'bg-yellow-600 text-black'
                  : 'text-gray-300 hover:text-white hover:bg-gray-700'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label.replace(/^.* /, '')}
            </button>
          ))}
        </div>
      </div>

      {error && (
        <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <span className="text-red-400">⚠️</span>
            <p className="text-red-300">{error}</p>
            <button
              onClick={() => setError(null)}
              className="ml-auto text-red-400 hover:text-red-300"
            >
              ✕
            </button>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <span className="text-green-400">✅</span>
            <p className="text-green-300">{success}</p>
            <button
              onClick={() => setSuccess(null)}
              className="ml-auto text-green-400 hover:text-green-300"
            >
              ✕
            </button>
          </div>
        </div>
      )}

      {/* Marketing Tools Content */}
      <div className="min-h-[500px]">
        {/* Lead Management Tab */}
        {activeMarketingTab === 'leads' && (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <span className="text-3xl">👥</span>
                <div>
                  <h2 className="text-xl font-bold text-white">Lead Management</h2>
                  <p className="text-gray-400 text-sm">Manage your prospects and track conversions</p>
                </div>
              </div>
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowImportModal(true)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                >
                  Import CSV
                </button>
                <button
                  onClick={handleCSVExport}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                >
                  Export CSV
                </button>
                <button
                  onClick={() => setShowAddLeadForm(true)}
                  className="bg-yellow-600 hover:bg-yellow-700 text-black px-4 py-2 rounded-lg font-medium transition-colors"
                >
                  + Add Lead
                </button>
              </div>
            </div>

            {/* Lead Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-blue-400 mb-1">
                  {leads.length}
                </div>
                <div className="text-gray-300 text-sm">Total Leads</div>
              </div>
              <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-green-400 mb-1">
                  {leads.filter(l => l.status === 'converted').length}
                </div>
                <div className="text-gray-300 text-sm">Converted</div>
              </div>
              <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-yellow-400 mb-1">
                  {leads.filter(l => l.status === 'interested').length}
                </div>
                <div className="text-gray-300 text-sm">Interested</div>
              </div>
              <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-purple-400 mb-1">
                  {leads.length > 0 ? ((leads.filter(l => l.status === 'converted').length / leads.length) * 100).toFixed(1) : 0}%
                </div>
                <div className="text-gray-300 text-sm">Conversion Rate</div>
              </div>
            </div>

            {/* Leads Table */}
            <div className="border-t border-gray-700 pt-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-white font-medium">Your Leads</h4>
                {selectedLeads.length > 0 && (
                  <div className="flex items-center space-x-3">
                    <span className="text-sm text-gray-400">{selectedLeads.length} selected</span>
                    <button
                      onClick={handleBulkDelete}
                      className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors"
                    >
                      Delete Selected
                    </button>
                  </div>
                )}
              </div>
              {leads.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-gray-700">
                        <th className="text-left py-3 px-4 text-gray-300 font-medium">
                          <input
                            type="checkbox"
                            checked={selectedLeads.length === leads.length && leads.length > 0}
                            onChange={toggleSelectAll}
                            className="rounded border-gray-600 bg-gray-700 text-yellow-600 focus:ring-yellow-500"
                          />
                        </th>
                        <th className="text-left py-3 px-4 text-gray-300 font-medium">Name</th>
                        <th className="text-left py-3 px-4 text-gray-300 font-medium">Email</th>
                        <th className="text-left py-3 px-4 text-gray-300 font-medium">Phone</th>
                        <th className="text-left py-3 px-4 text-gray-300 font-medium">Status</th>
                        <th className="text-left py-3 px-4 text-gray-300 font-medium">Source</th>
                        <th className="text-left py-3 px-4 text-gray-300 font-medium">Lists</th>
                        <th className="text-left py-3 px-4 text-gray-300 font-medium">Created</th>
                        <th className="text-left py-3 px-4 text-gray-300 font-medium">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {leads.map((lead) => (
                        <tr key={lead.id} className="border-b border-gray-700/50 hover:bg-gray-700/30">
                          <td className="py-3 px-4">
                            <input
                              type="checkbox"
                              checked={selectedLeads.includes(lead.id)}
                              onChange={() => toggleLeadSelection(lead.id)}
                              className="rounded border-gray-600 bg-gray-700 text-yellow-600 focus:ring-yellow-500"
                            />
                          </td>
                          <td className="py-3 px-4 text-white">
                            {lead.first_name} {lead.last_name}
                          </td>
                          <td className="py-3 px-4 text-gray-300">{lead.email}</td>
                          <td className="py-3 px-4 text-gray-300">{lead.phone || '-'}</td>
                          <td className="py-3 px-4">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              lead.status === 'converted' ? 'bg-green-900/30 text-green-300' :
                              lead.status === 'interested' ? 'bg-yellow-900/30 text-yellow-300' :
                              lead.status === 'contacted' ? 'bg-blue-900/30 text-blue-300' :
                              lead.status === 'lost' ? 'bg-red-900/30 text-red-300' :
                              'bg-gray-900/30 text-gray-300'
                            }`}>
                              {lead.status.toUpperCase()}
                            </span>
                          </td>
                          <td className="py-3 px-4 text-gray-300">{lead.source}</td>
                          <td className="py-3 px-4">
                            {leadListMemberships[lead.id] && leadListMemberships[lead.id].length > 0 ? (
                              <div className="flex flex-wrap gap-1">
                                {leadListMemberships[lead.id].map((listName, index) => (
                                  <span
                                    key={index}
                                    className="px-2 py-1 bg-yellow-900/30 text-yellow-300 text-xs rounded-full"
                                  >
                                    {listName}
                                  </span>
                                ))}
                              </div>
                            ) : (
                              <span className="text-gray-500 text-xs">No lists</span>
                            )}
                          </td>
                          <td className="py-3 px-4 text-gray-300">
                            {new Date(lead.created_at).toLocaleDateString()}
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex space-x-2">
                              <button
                                onClick={() => handleEditLead(lead)}
                                className="text-blue-400 hover:text-blue-300 text-xs"
                              >
                                Edit
                              </button>
                              <button
                                onClick={() => handleDeleteLead(lead.id)}
                                className="text-red-400 hover:text-red-300 text-xs"
                              >
                                Delete
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="text-4xl mb-4">👥</div>
                  <p className="text-gray-400 mb-4">No leads yet</p>
                  <button
                    onClick={() => setShowAddLeadForm(true)}
                    className="bg-yellow-600 hover:bg-yellow-700 text-black px-6 py-2 rounded-lg font-medium transition-colors"
                  >
                    Add Your First Lead
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Referral Links Tab */}
        {activeMarketingTab === 'links' && (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <span className="text-3xl">🔗</span>
                <div>
                  <h2 className="text-xl font-bold text-white">Referral Links</h2>
                  <p className="text-gray-400 text-sm">Create trackable referral links with UTM parameters</p>
                </div>
              </div>
              <button
                onClick={() => setShowAddLinkForm(true)}
                className="bg-yellow-600 hover:bg-yellow-700 text-black px-4 py-2 rounded-lg font-medium transition-colors"
              >
                + Create Link
              </button>
            </div>

            {/* Default Referral Link */}
            <div className="bg-gray-700/50 rounded-lg p-4 mb-6">
              <h4 className="text-white font-medium mb-2">Your Default Referral Link</h4>
              <div className="flex items-center space-x-3">
                <input
                  type="text"
                  value={`${window.location.origin}/${affiliateData?.user?.username || 'affiliate'}`}
                  readOnly
                  className="flex-1 bg-gray-600 text-white px-3 py-2 rounded border border-gray-500 text-sm"
                />
                <button
                  onClick={() => copyToClipboard(`${window.location.origin}/${affiliateData?.user?.username || 'affiliate'}`)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm transition-colors"
                >
                  Copy
                </button>
              </div>
            </div>

            {/* Custom Links */}
            <div className="border-t border-gray-700 pt-4">
              <h4 className="text-white font-medium mb-3">Custom Tracking Links</h4>
              {referralLinks.length > 0 ? (
                <div className="space-y-3">
                  {referralLinks.map((link) => (
                    <div key={link.id} className="bg-gray-700/50 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h5 className="text-white font-medium">{link.link_name}</h5>
                        <div className="flex items-center space-x-4 text-sm text-gray-300">
                          <span>Clicks: {link.clicks_count}</span>
                          <span>Conversions: {link.conversions_count}</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <input
                          type="text"
                          value={link.base_url}
                          readOnly
                          className="flex-1 bg-gray-600 text-white px-3 py-2 rounded border border-gray-500 text-sm"
                        />
                        <button
                          onClick={() => copyToClipboard(link.base_url)}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm transition-colors"
                        >
                          Copy
                        </button>
                      </div>
                      {(link.utm_source || link.utm_medium || link.utm_campaign) && (
                        <div className="mt-2 text-xs text-gray-400">
                          UTM: {[link.utm_source, link.utm_medium, link.utm_campaign].filter(Boolean).join(' • ')}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="text-4xl mb-4">🔗</div>
                  <p className="text-gray-400 mb-4">No custom links yet</p>
                  <button
                    onClick={() => setShowAddLinkForm(true)}
                    className="bg-yellow-600 hover:bg-yellow-700 text-black px-6 py-2 rounded-lg font-medium transition-colors"
                  >
                    Create Your First Link
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Lead Lists Tab */}
        {activeMarketingTab === 'lists' && (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <span className="text-3xl">📋</span>
                <div>
                  <h3 className="text-xl font-bold text-white">Lead Lists</h3>
                  <p className="text-gray-400">Organize your leads into targeted lists for email campaigns</p>
                </div>
              </div>
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowAddListForm(true)}
                  className="bg-yellow-600 hover:bg-yellow-700 text-black px-4 py-2 rounded-lg font-medium transition-colors"
                >
                  + Create List
                </button>
                {selectedLeads.length > 0 && (
                  <button
                    onClick={() => setShowAssignLeadsModal(true)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                  >
                    Assign to List ({selectedLeads.length})
                  </button>
                )}
              </div>
            </div>

            {/* Lead Lists Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
              {leadLists.map((list) => (
                <div key={list.id} className="bg-gray-700/50 rounded-lg p-4 border border-gray-600">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h4 className="text-white font-medium mb-1">{list.list_name}</h4>
                      {list.description && (
                        <p className="text-gray-400 text-sm mb-2">{list.description}</p>
                      )}
                      <div className="flex items-center space-x-4 text-sm text-gray-300">
                        <span>{typeof list.lead_count === 'number' ? list.lead_count : 0} leads</span>
                        <span>{new Date(list.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                    <button
                      onClick={() => handleDeleteList(list.id)}
                      className="text-red-400 hover:text-red-300 p-1"
                      title="Delete list"
                    >
                      🗑️
                    </button>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => {
                        setSelectedListId(list.id);
                        setActiveMarketingTab('email');
                      }}
                      className="flex-1 bg-yellow-600 hover:bg-yellow-700 text-black px-3 py-2 rounded text-sm font-medium transition-colors"
                    >
                      Send Email
                    </button>
                    <button
                      onClick={() => {
                        // TODO: View list members
                        console.log('View list members:', list.id);
                      }}
                      className="bg-gray-600 hover:bg-gray-500 text-white px-3 py-2 rounded text-sm font-medium transition-colors"
                    >
                      View
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {leadLists.length === 0 && (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">📋</div>
                <h3 className="text-xl font-medium text-white mb-2">No Lead Lists Yet</h3>
                <p className="text-gray-400 mb-6">Create your first lead list to organize your contacts for targeted email campaigns.</p>
                <button
                  onClick={() => setShowAddListForm(true)}
                  className="bg-yellow-600 hover:bg-yellow-700 text-black px-6 py-3 rounded-lg font-medium transition-colors"
                >
                  Create Your First List
                </button>
              </div>
            )}

            {/* Quick Actions */}
            {leadLists.length > 0 && (
              <div className="border-t border-gray-700 pt-6">
                <h4 className="text-white font-medium mb-4">Quick Actions</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-gray-700/30 rounded-lg p-4 text-center">
                    <div className="text-2xl mb-2">📊</div>
                    <h5 className="text-white font-medium mb-1">Total Lists</h5>
                    <p className="text-2xl font-bold text-yellow-400">{leadLists.length}</p>
                  </div>
                  <div className="bg-gray-700/30 rounded-lg p-4 text-center">
                    <div className="text-2xl mb-2">👥</div>
                    <h5 className="text-white font-medium mb-1">Total Contacts</h5>
                    <p className="text-2xl font-bold text-blue-400">
                      {leadLists.reduce((sum, list) => sum + (list.lead_count || 0), 0)}
                    </p>
                  </div>
                  <div className="bg-gray-700/30 rounded-lg p-4 text-center">
                    <div className="text-2xl mb-2">📧</div>
                    <h5 className="text-white font-medium mb-1">Campaigns Sent</h5>
                    <p className="text-2xl font-bold text-green-400">{emailCampaigns.length}</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Email Marketing Tab */}
        {activeMarketingTab === 'email' && (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="flex items-center gap-3 mb-6">
              <span className="text-3xl">📧</span>
              <div>
                <h2 className="text-xl font-bold text-white">Email Marketing</h2>
                <p className="text-gray-400 text-sm">Send personalized emails to your leads using professional templates</p>
              </div>
            </div>

            {/* Email Templates */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {emailTemplates.map((template) => (
                <div key={template.id} className="bg-gray-700/50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-white font-medium">{template.template_name}</h4>
                    <span className="px-2 py-1 bg-blue-900/30 text-blue-300 text-xs rounded">
                      {template.template_type}
                    </span>
                  </div>
                  <p className="text-gray-400 text-sm mb-3">{template.description}</p>
                  <p className="text-gray-300 text-sm mb-4">
                    <strong>Subject:</strong> {template.subject_line}
                  </p>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleUseTemplate(template)}
                      className="flex-1 bg-yellow-600 hover:bg-yellow-700 text-black px-3 py-2 rounded text-sm font-medium transition-colors"
                    >
                      Use Template
                    </button>
                    <button
                      onClick={() => handlePreviewTemplate(template)}
                      className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded text-sm transition-colors"
                    >
                      Preview
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {emailTemplates.length === 0 && (
              <div className="text-center py-8">
                <div className="text-4xl mb-4">📧</div>
                <p className="text-gray-400 mb-2">No email templates available</p>
                <p className="text-xs text-gray-500">Email templates are managed by administrators</p>
              </div>
            )}
          </div>
        )}

        {/* Marketing Materials Tab */}
        {activeMarketingTab === 'materials' && (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="flex items-center gap-3 mb-6">
              <span className="text-3xl">📁</span>
              <div>
                <h2 className="text-xl font-bold text-white">Marketing Materials</h2>
                <p className="text-gray-400 text-sm">Download company-approved marketing materials and assets</p>
              </div>
            </div>

            {/* Company Presentations Section */}
            <div className="mb-8">
              <div className="flex items-center gap-2 mb-4">
                <span className="text-2xl">📋</span>
                <h3 className="text-lg font-bold text-white">Company Presentations</h3>
              </div>
              <p className="text-gray-400 text-sm mb-6">
                Professional company presentations for your marketing and client meetings
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* English Presentation */}
                <div className="bg-gray-700 rounded-lg p-6 hover:bg-gray-600 transition-colors">
                  <div className="text-center">
                    <div className="text-4xl mb-4">🇬🇧</div>
                    <h4 className="text-lg font-semibold text-white mb-3">English Presentation</h4>
                    <p className="text-gray-300 text-sm mb-4">Complete company overview and mining operations details</p>
                    <a
                      href="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/marketing-materials/Aureus%20Presentation%20Plan%20-%20Eng.pdf"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-2 bg-yellow-600 hover:bg-yellow-700 text-black px-4 py-2 rounded-lg font-medium transition-colors"
                    >
                      📄 Download PDF
                    </a>
                  </div>
                </div>

                {/* Hindi Presentation */}
                <div className="bg-gray-700 rounded-lg p-6 hover:bg-gray-600 transition-colors">
                  <div className="text-center">
                    <div className="text-4xl mb-4">🇮🇳</div>
                    <h4 className="text-lg font-semibold text-white mb-3">Hindi Presentation</h4>
                    <p className="text-gray-300 text-sm mb-4">हिंदी में कंपनी की पूरी जानकारी और खनन संचालन विवरण</p>
                    <a
                      href="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/marketing-materials/Aureus%20Presentation%20Plan%20-%20Hindi.pdf"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-2 bg-yellow-600 hover:bg-yellow-700 text-black px-4 py-2 rounded-lg font-medium transition-colors"
                    >
                      📄 Download PDF
                    </a>
                  </div>
                </div>

                {/* Indonesian Presentation */}
                <div className="bg-gray-700 rounded-lg p-6 hover:bg-gray-600 transition-colors">
                  <div className="text-center">
                    <div className="text-4xl mb-4">🇮🇩</div>
                    <h4 className="text-lg font-semibold text-white mb-3">Indonesian Presentation</h4>
                    <p className="text-gray-300 text-sm mb-4">Gambaran lengkap perusahaan dan detail operasi pertambangan</p>
                    <a
                      href="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/marketing-materials/Aureus%20Presentation%20Plan%20-%20Indonesian.pdf"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-2 bg-yellow-600 hover:bg-yellow-700 text-black px-4 py-2 rounded-lg font-medium transition-colors"
                    >
                      📄 Download PDF
                    </a>
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Materials Section */}
            <div className="border-t border-gray-700 pt-6">
              <div className="flex items-center gap-2 mb-4">
                <span className="text-2xl">🎨</span>
                <h3 className="text-lg font-bold text-white">Additional Marketing Assets</h3>
              </div>
              <div className="text-center py-8">
                <div className="text-4xl mb-4">🎨</div>
                <p className="text-gray-400 mb-2">More marketing materials coming soon</p>
                <p className="text-xs text-gray-500">Banners, videos, social media templates, and other promotional content will be available here</p>
              </div>
            </div>
          </div>
        )}

        {/* Communication Templates Tab */}
        {activeMarketingTab === 'templates' && (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <span className="text-3xl">📝</span>
                <div>
                  <h2 className="text-xl font-bold text-white">Communication Templates</h2>
                  <p className="text-gray-400 text-sm">Pre-written templates for SMS, social media, and phone scripts</p>
                </div>
              </div>
              <button
                onClick={createAureusOpportunityTemplate}
                className="bg-yellow-600 hover:bg-yellow-700 text-black px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
              >
                <span>✨</span>
                <span>Create Aureus Email Template</span>
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* SMS Templates */}
              <div className="bg-gray-700/50 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3 flex items-center">
                  <span className="mr-2">💬</span>
                  SMS Templates
                </h4>
                <div className="space-y-3">
                  <div className="bg-gray-600/50 rounded p-3">
                    <p className="text-sm text-gray-300 mb-2"><strong>Introduction Message:</strong></p>
                    <p className="text-xs text-gray-400">
                      "Hi &#123;firstName&#125;! I wanted to share an exciting gold mining investment opportunity with you.
                      Would you be interested in learning more? Check out: &#123;affiliateLink&#125;"
                    </p>
                    <button
                      onClick={() => copyTemplate("Hi {firstName}! I wanted to share an exciting gold mining investment opportunity with you. Would you be interested in learning more? Check out: {affiliateLink}")}
                      className="mt-2 text-blue-400 hover:text-blue-300 text-xs"
                    >
                      Copy Template
                    </button>
                  </div>
                  <div className="bg-gray-600/50 rounded p-3">
                    <p className="text-sm text-gray-300 mb-2"><strong>Follow-up Message:</strong></p>
                    <p className="text-xs text-gray-400">
                      "Hi &#123;firstName&#125;, just following up on the gold mining opportunity I shared.
                      Any questions? I'm here to help! &#123;affiliateLink&#125;"
                    </p>
                    <button
                      onClick={() => copyTemplate("Hi {firstName}, just following up on the gold mining opportunity I shared. Any questions? I'm here to help! {affiliateLink}")}
                      className="mt-2 text-blue-400 hover:text-blue-300 text-xs"
                    >
                      Copy Template
                    </button>
                  </div>
                </div>
              </div>

              {/* Social Media Templates */}
              <div className="bg-gray-700/50 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3 flex items-center">
                  <span className="mr-2">📱</span>
                  Social Media Posts
                </h4>
                <div className="space-y-3">
                  <div className="bg-gray-600/50 rounded p-3">
                    <p className="text-sm text-gray-300 mb-2"><strong>Facebook Post:</strong></p>
                    <p className="text-xs text-gray-400">
                      "🏆 Exciting opportunity in gold mining! Join me in this incredible investment journey.
                      Secure your financial future with real gold-backed shares. Learn more: &#123;affiliateLink&#125; #GoldInvestment #FinancialFreedom"
                    </p>
                    <button
                      onClick={() => copyTemplate("🏆 Exciting opportunity in gold mining! Join me in this incredible investment journey. Secure your financial future with real gold-backed shares. Learn more: {affiliateLink} #GoldInvestment #FinancialFreedom")}
                      className="mt-2 text-blue-400 hover:text-blue-300 text-xs"
                    >
                      Copy Template
                    </button>
                  </div>
                  <div className="bg-gray-600/50 rounded p-3">
                    <p className="text-sm text-gray-300 mb-2"><strong>LinkedIn Post:</strong></p>
                    <p className="text-xs text-gray-400">
                      "Professional investors are turning to gold-backed securities. I'm excited to share this opportunity
                      with my network. Diversify your portfolio today: &#123;affiliateLink&#125;"
                    </p>
                    <button
                      onClick={() => copyTemplate("Professional investors are turning to gold-backed securities. I'm excited to share this opportunity with my network. Diversify your portfolio today: {affiliateLink}")}
                      className="mt-2 text-blue-400 hover:text-blue-300 text-xs"
                    >
                      Copy Template
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Analytics Tab */}
        {activeMarketingTab === 'analytics' && (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="flex items-center gap-3 mb-6">
              <span className="text-3xl">📊</span>
              <div>
                <h2 className="text-xl font-bold text-white">Marketing Analytics</h2>
                <p className="text-gray-400 text-sm">Track your marketing performance and ROI</p>
              </div>
            </div>

            {/* Analytics Overview */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-blue-400 mb-1">
                  {referralLinks.reduce((sum, link) => sum + link.clicks_count, 0)}
                </div>
                <div className="text-gray-300 text-sm">Total Clicks</div>
              </div>
              <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-green-400 mb-1">
                  {referralLinks.reduce((sum, link) => sum + link.conversions_count, 0)}
                </div>
                <div className="text-gray-300 text-sm">Total Conversions</div>
              </div>
              <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-yellow-400 mb-1">
                  {leads.filter(l => l.status === 'converted').length}
                </div>
                <div className="text-gray-300 text-sm">Lead Conversions</div>
              </div>
              <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-purple-400 mb-1">
                  {affiliateData?.referralCount || 0}
                </div>
                <div className="text-gray-300 text-sm">Total Referrals</div>
              </div>
            </div>

            <div className="text-center py-8">
              <div className="text-4xl mb-4">📊</div>
              <p className="text-gray-400 mb-2">Detailed analytics coming soon</p>
              <p className="text-xs text-gray-500">Advanced reporting and conversion tracking will be available here</p>
            </div>
          </div>
        )}
      </div>

      {/* Add Lead Modal */}
      {showAddLeadForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg border border-gray-700 w-full max-w-md max-h-[90vh] flex flex-col">
            <div className="flex items-center justify-between p-6 pb-4 border-b border-gray-700">
              <h3 className="text-lg font-bold text-white">Add New Lead</h3>
              <button
                onClick={() => setShowAddLeadForm(false)}
                className="text-gray-400 hover:text-white"
              >
                ✕
              </button>
            </div>

            <div className="flex-1 overflow-y-auto p-6">
              <form onSubmit={handleAddLead} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    First Name *
                  </label>
                  <input
                    type="text"
                    required
                    value={leadForm.first_name}
                    onChange={(e) => setLeadForm(prev => ({ ...prev, first_name: e.target.value }))}
                    className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Last Name *
                  </label>
                  <input
                    type="text"
                    required
                    value={leadForm.last_name}
                    onChange={(e) => setLeadForm(prev => ({ ...prev, last_name: e.target.value }))}
                    className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Email Address *
                </label>
                <input
                  type="email"
                  required
                  value={leadForm.email}
                  onChange={(e) => setLeadForm(prev => ({ ...prev, email: e.target.value }))}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Phone Number
                </label>
                <input
                  type="tel"
                  value={leadForm.phone}
                  onChange={(e) => setLeadForm(prev => ({ ...prev, phone: e.target.value }))}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Status
                </label>
                <select
                  value={leadForm.status}
                  onChange={(e) => setLeadForm(prev => ({ ...prev, status: e.target.value as any }))}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none"
                >
                  <option value="new">New</option>
                  <option value="contacted">Contacted</option>
                  <option value="interested">Interested</option>
                  <option value="converted">Converted</option>
                  <option value="lost">Lost</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Source
                </label>
                <input
                  type="text"
                  value={leadForm.source}
                  onChange={(e) => setLeadForm(prev => ({ ...prev, source: e.target.value }))}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none"
                  placeholder="e.g., Facebook, LinkedIn, Referral"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Tags (comma-separated)
                </label>
                <input
                  type="text"
                  value={leadForm.tags}
                  onChange={(e) => setLeadForm(prev => ({ ...prev, tags: e.target.value }))}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none"
                  placeholder="e.g., high-value, warm-lead, investor"
                />
              </div>

              {/* List Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Add to Lists (optional)
                </label>
                {leadLists.length > 0 ? (
                  <select
                    multiple
                    value={leadForm.selected_lists}
                    onChange={(e) => {
                      const selectedOptions = Array.from(e.target.selectedOptions, option => option.value);
                      setLeadForm(prev => ({
                        ...prev,
                        selected_lists: selectedOptions
                      }));
                    }}
                    className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none h-24"
                    size={4}
                  >
                    {leadLists.map(list => (
                      <option key={list.id} value={list.id} className="py-1">
                        {list.list_name} ({typeof list.lead_count === 'number' ? list.lead_count : 0} leads)
                      </option>
                    ))}
                  </select>
                ) : (
                  <div className="w-full bg-gray-700 text-gray-400 px-3 py-2 rounded border border-gray-600 text-sm text-center">
                    No lists available. Create a list first in the Lists tab.
                  </div>
                )}
                <div className="mt-1 text-xs text-gray-400">
                  Hold Ctrl/Cmd to select multiple lists. Selected: {leadForm.selected_lists.length} list(s)
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Notes
                </label>
                <textarea
                  value={leadForm.notes}
                  onChange={(e) => setLeadForm(prev => ({ ...prev, notes: e.target.value }))}
                  rows={3}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none"
                  placeholder="Additional notes about this lead..."
                />
              </div>

                <div className="flex space-x-3 pt-4">
                  <button
                    type="submit"
                    className="flex-1 bg-yellow-600 hover:bg-yellow-700 text-black px-4 py-2 rounded font-medium transition-colors"
                  >
                    Add Lead
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowAddLeadForm(false)}
                    className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded font-medium transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Edit Lead Modal */}
      {showEditLeadForm && editingLead && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg border border-gray-700 w-full max-w-md max-h-[90vh] flex flex-col">
            <div className="flex items-center justify-between p-6 pb-4 border-b border-gray-700">
              <h3 className="text-lg font-bold text-white">Edit Lead</h3>
              <button
                onClick={() => {
                  setShowEditLeadForm(false);
                  setEditingLead(null);
                }}
                className="text-gray-400 hover:text-white"
              >
                ✕
              </button>
            </div>

            <div className="flex-1 overflow-y-auto p-6">
              <form onSubmit={handleUpdateLead} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    First Name *
                  </label>
                  <input
                    type="text"
                    required
                    value={leadForm.first_name}
                    onChange={(e) => setLeadForm(prev => ({ ...prev, first_name: e.target.value }))}
                    className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Last Name *
                  </label>
                  <input
                    type="text"
                    required
                    value={leadForm.last_name}
                    onChange={(e) => setLeadForm(prev => ({ ...prev, last_name: e.target.value }))}
                    className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Email Address *
                </label>
                <input
                  type="email"
                  required
                  value={leadForm.email}
                  onChange={(e) => setLeadForm(prev => ({ ...prev, email: e.target.value }))}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Phone Number
                </label>
                <input
                  type="tel"
                  value={leadForm.phone}
                  onChange={(e) => setLeadForm(prev => ({ ...prev, phone: e.target.value }))}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Status
                </label>
                <select
                  value={leadForm.status}
                  onChange={(e) => setLeadForm(prev => ({ ...prev, status: e.target.value as any }))}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none"
                >
                  <option value="new">New</option>
                  <option value="contacted">Contacted</option>
                  <option value="interested">Interested</option>
                  <option value="converted">Converted</option>
                  <option value="lost">Lost</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Source
                </label>
                <input
                  type="text"
                  value={leadForm.source}
                  onChange={(e) => setLeadForm(prev => ({ ...prev, source: e.target.value }))}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none"
                  placeholder="e.g., Facebook, LinkedIn, Referral"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Tags (comma-separated)
                </label>
                <input
                  type="text"
                  value={leadForm.tags}
                  onChange={(e) => setLeadForm(prev => ({ ...prev, tags: e.target.value }))}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none"
                  placeholder="e.g., high-value, warm-lead, investor"
                />
              </div>

              {/* List Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Update Lists
                </label>
                {leadLists.length > 0 ? (
                  <select
                    multiple
                    value={leadForm.selected_lists}
                    onChange={(e) => {
                      const selectedOptions = Array.from(e.target.selectedOptions, option => option.value);
                      setLeadForm(prev => ({
                        ...prev,
                        selected_lists: selectedOptions
                      }));
                    }}
                    className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none h-24"
                    size={4}
                  >
                    {leadLists.map(list => (
                      <option key={list.id} value={list.id} className="py-1">
                        {list.list_name} ({typeof list.lead_count === 'number' ? list.lead_count : 0} leads)
                      </option>
                    ))}
                  </select>
                ) : (
                  <div className="w-full bg-gray-700 text-gray-400 px-3 py-2 rounded border border-gray-600 text-sm text-center">
                    No lists available. Create a list first in the Lists tab.
                  </div>
                )}
                <div className="mt-1 text-xs text-gray-400">
                  Hold Ctrl/Cmd to select multiple lists. Selected: {leadForm.selected_lists.length} list(s)
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Notes
                </label>
                <textarea
                  value={leadForm.notes}
                  onChange={(e) => setLeadForm(prev => ({ ...prev, notes: e.target.value }))}
                  rows={3}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none"
                  placeholder="Additional notes about this lead..."
                />
              </div>

                <div className="flex space-x-3 pt-4">
                  <button
                    type="submit"
                    className="flex-1 bg-yellow-600 hover:bg-yellow-700 text-black px-4 py-2 rounded font-medium transition-colors"
                  >
                    Update Lead
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setShowEditLeadForm(false);
                      setEditingLead(null);
                    }}
                    className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded font-medium transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Add Referral Link Modal */}
      {showAddLinkForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-white">Create Referral Link</h3>
              <button
                onClick={() => setShowAddLinkForm(false)}
                className="text-gray-400 hover:text-white"
              >
                ✕
              </button>
            </div>

            <form onSubmit={handleAddReferralLink} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Link Name *
                </label>
                <input
                  type="text"
                  required
                  value={linkForm.link_name}
                  onChange={(e) => setLinkForm(prev => ({ ...prev, link_name: e.target.value }))}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none"
                  placeholder="e.g., Facebook Campaign, Email Newsletter"
                />
              </div>

              <div className="text-sm text-gray-400 mb-4">
                <p className="font-medium mb-2">UTM Parameters (for tracking):</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  UTM Source
                </label>
                <input
                  type="text"
                  value={linkForm.utm_source}
                  onChange={(e) => setLinkForm(prev => ({ ...prev, utm_source: e.target.value }))}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none"
                  placeholder="e.g., facebook, google, email"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  UTM Medium
                </label>
                <input
                  type="text"
                  value={linkForm.utm_medium}
                  onChange={(e) => setLinkForm(prev => ({ ...prev, utm_medium: e.target.value }))}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none"
                  placeholder="e.g., social, cpc, email"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  UTM Campaign
                </label>
                <input
                  type="text"
                  value={linkForm.utm_campaign}
                  onChange={(e) => setLinkForm(prev => ({ ...prev, utm_campaign: e.target.value }))}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none"
                  placeholder="e.g., summer_promo, newsletter_jan"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  UTM Term
                </label>
                <input
                  type="text"
                  value={linkForm.utm_term}
                  onChange={(e) => setLinkForm(prev => ({ ...prev, utm_term: e.target.value }))}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none"
                  placeholder="e.g., gold+investment"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  UTM Content
                </label>
                <input
                  type="text"
                  value={linkForm.utm_content}
                  onChange={(e) => setLinkForm(prev => ({ ...prev, utm_content: e.target.value }))}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none"
                  placeholder="e.g., banner_ad, text_link"
                />
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  type="submit"
                  className="flex-1 bg-yellow-600 hover:bg-yellow-700 text-black px-4 py-2 rounded font-medium transition-colors"
                >
                  Create Link
                </button>
                <button
                  type="button"
                  onClick={() => setShowAddLinkForm(false)}
                  className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded font-medium transition-colors"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* CSV Import Modal */}
      {showImportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-white">Import Leads from CSV</h3>
              <button
                onClick={() => {
                  setShowImportModal(false);
                  setImportResults(null);
                  setCsvFile(null);
                }}
                className="text-gray-400 hover:text-white"
              >
                ✕
              </button>
            </div>

            <form onSubmit={handleCSVImport} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  CSV File
                </label>
                <input
                  type="file"
                  accept=".csv"
                  onChange={(e) => setCsvFile(e.target.files?.[0] || null)}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-yellow-500 focus:outline-none"
                  required
                />
                <p className="text-xs text-gray-400 mt-1">
                  Required columns: first_name, last_name, email. Optional: phone, status, source, notes
                </p>
              </div>

              {importResults && (
                <div className="bg-gray-700/50 rounded p-3">
                  <p className="text-sm text-white mb-2">Import Results:</p>
                  <p className="text-xs text-green-400">✅ Successfully imported: {importResults.success}</p>
                  {importResults.errors.length > 0 && (
                    <div className="mt-2">
                      <p className="text-xs text-red-400">❌ Errors: {importResults.errors.length}</p>
                      <div className="max-h-20 overflow-y-auto mt-1">
                        {importResults.errors.slice(0, 5).map((error, index) => (
                          <p key={index} className="text-xs text-red-300">• {error}</p>
                        ))}
                        {importResults.errors.length > 5 && (
                          <p className="text-xs text-gray-400">... and {importResults.errors.length - 5} more</p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}

              <div className="flex space-x-3 pt-4">
                <button
                  type="submit"
                  disabled={!csvFile}
                  className="flex-1 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-black disabled:text-gray-400 px-4 py-2 rounded font-medium transition-colors"
                >
                  Import Leads
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowImportModal(false);
                    setImportResults(null);
                    setCsvFile(null);
                  }}
                  className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded font-medium transition-colors"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Email Template Preview Modal */}
      {showEmailPreview && selectedTemplate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg border border-gray-700 w-full max-w-4xl max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <h3 className="text-lg font-bold text-white">Email Template Preview</h3>
              <button
                onClick={() => {
                  setShowEmailPreview(false);
                  setSelectedTemplate(null);
                }}
                className="text-gray-400 hover:text-white"
              >
                ✕
              </button>
            </div>
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <div className="mb-4">
                <h4 className="text-white font-medium mb-2">{selectedTemplate.template_name}</h4>
                <p className="text-gray-400 text-sm mb-4">{selectedTemplate.description}</p>
                <p className="text-gray-300 text-sm mb-4">
                  <strong>Subject:</strong> {selectedTemplate.subject_line}
                </p>
              </div>
              <div className="bg-white rounded-lg p-4">
                <style>
                  {`
                    .email-preview * {
                      color: #111827 !important;
                    }
                    .email-preview h1, .email-preview h2, .email-preview h3, .email-preview h4, .email-preview h5, .email-preview h6 {
                      color: #111827 !important;
                      font-weight: bold !important;
                    }
                    .email-preview p {
                      color: #374151 !important;
                      margin: 1rem 0 !important;
                    }
                    .email-preview a {
                      color: #1f2937 !important;
                      text-decoration: underline !important;
                    }
                    .email-preview strong, .email-preview b {
                      color: #111827 !important;
                      font-weight: bold !important;
                    }
                    .email-preview em, .email-preview i {
                      color: #111827 !important;
                      font-style: italic !important;
                    }
                  `}
                </style>
                <div
                  dangerouslySetInnerHTML={{
                    __html: generateEmailHeader() +
                           '<div style="padding: 20px; background: white;">' +
                           selectedTemplate.html_content +
                           '</div>' +
                           generateEmailFooter()
                  }}
                  className="email-preview"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-3 p-6 border-t border-gray-700">
              <button
                onClick={() => {
                  setShowEmailPreview(false);
                  handleUseTemplate(selectedTemplate);
                }}
                className="bg-yellow-600 hover:bg-yellow-700 text-black px-4 py-2 rounded font-medium transition-colors"
              >
                Use This Template
              </button>
              <button
                onClick={() => {
                  setShowEmailPreview(false);
                  setSelectedTemplate(null);
                }}
                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded font-medium transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Email Composer Modal */}
      {showEmailComposer && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg border border-gray-700 w-full max-w-4xl max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <h3 className="text-lg font-bold text-white">Compose Email</h3>
              <button
                onClick={() => {
                  setShowEmailComposer(false);
                  setEmailComposer({
                    to: '',
                    subject: '',
                    template_id: '',
                    list_id: ''
                  });
                }}
                className="text-gray-400 hover:text-white"
              >
                ✕
              </button>
            </div>
            <form onSubmit={handleSendEmail} className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Send to Lead List:</label>
                    <select
                      value={emailComposer.list_id}
                      onChange={(e) => setEmailComposer(prev => ({ ...prev, list_id: e.target.value, to: e.target.value ? '' : prev.to }))}
                      className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-yellow-600"
                    >
                      <option value="">Select a lead list...</option>
                      {leadLists.map(list => (
                        <option key={list.id} value={list.id}>
                          {list.list_name} ({typeof list.lead_count === 'number' ? list.lead_count : 0} leads)
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Or Single Email:</label>
                    <input
                      type="email"
                      value={emailComposer.to}
                      onChange={(e) => setEmailComposer(prev => ({ ...prev, to: e.target.value, list_id: e.target.value ? '' : prev.list_id }))}
                      className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-yellow-600"
                      placeholder="<EMAIL>"
                      disabled={!!emailComposer.list_id}
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Subject:</label>
                  <input
                    type="text"
                    value={emailComposer.subject}
                    onChange={(e) => setEmailComposer(prev => ({ ...prev, subject: e.target.value }))}
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-yellow-600"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Email Preview:</label>
                  <div className="bg-white rounded-lg p-4 border border-gray-600 max-h-96 overflow-y-auto">
                    <style>
                      {`
                        .email-preview h1, .email-preview h2, .email-preview h3 {
                          color: #1f2937 !important;
                          margin: 1rem 0 !important;
                        }
                        .email-preview p {
                          color: #374151 !important;
                          margin: 1rem 0 !important;
                        }
                        .email-preview a {
                          color: #1f2937 !important;
                          text-decoration: underline !important;
                        }
                        .email-preview strong, .email-preview b {
                          color: #111827 !important;
                          font-weight: bold !important;
                        }
                        .email-preview em, .email-preview i {
                          color: #111827 !important;
                          font-style: italic !important;
                        }
                      `}
                    </style>
                    {selectedTemplate && (
                      <div
                        dangerouslySetInnerHTML={{
                          __html: generateEmailHeader() +
                                 '<div style="padding: 20px; background: white;">' +
                                 selectedTemplate.html_content +
                                 '</div>' +
                                 generateEmailFooter()
                        }}
                        className="email-preview"
                      />
                    )}
                  </div>
                  <p className="text-xs text-gray-400 mt-2">
                    This email will be sent using the selected template. Content cannot be modified for security reasons.
                  </p>
                </div>
              </div>
              {/* Progress Indicator */}
              {emailSending && (
                <div className="mb-6 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-blue-300 font-medium">Sending emails...</span>
                    <span className="text-blue-300 text-sm">{sendProgress.sent} / {sendProgress.total}</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${sendProgress.total > 0 ? (sendProgress.sent / sendProgress.total) * 100 : 0}%` }}
                    ></div>
                  </div>
                </div>
              )}

              <div className="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-700">
                <button
                  type="button"
                  onClick={() => {
                    setShowEmailComposer(false);
                    setEmailComposer({
                      to: '',
                      subject: '',
                      template_id: '',
                      list_id: ''
                    });
                  }}
                  disabled={emailSending}
                  className="bg-gray-600 hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded font-medium transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={emailSending || (!emailComposer.to && !emailComposer.list_id)}
                  className="bg-yellow-600 hover:bg-yellow-700 disabled:opacity-50 disabled:cursor-not-allowed text-black px-4 py-2 rounded font-medium transition-colors flex items-center space-x-2"
                >
                  {emailSending ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black"></div>
                      <span>Sending...</span>
                    </>
                  ) : (
                    <span>Send Email</span>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Add Lead List Modal */}
      {showAddListForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg border border-gray-700 w-full max-w-md">
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <h3 className="text-lg font-bold text-white">Create Lead List</h3>
              <button
                onClick={() => setShowAddListForm(false)}
                className="text-gray-400 hover:text-white"
              >
                ✕
              </button>
            </div>
            <form onSubmit={handleAddList} className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">List Name *</label>
                  <input
                    type="text"
                    value={listForm.list_name}
                    onChange={(e) => setListForm(prev => ({ ...prev, list_name: e.target.value }))}
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-yellow-600"
                    placeholder="e.g., High Value Prospects"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Description</label>
                  <textarea
                    value={listForm.description}
                    onChange={(e) => setListForm(prev => ({ ...prev, description: e.target.value }))}
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-yellow-600 h-20 resize-none"
                    placeholder="Optional description for this list..."
                  />
                </div>
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => setShowAddListForm(false)}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded font-medium transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-yellow-600 hover:bg-yellow-700 text-black px-4 py-2 rounded font-medium transition-colors"
                >
                  Create List
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Assign Leads to List Modal */}
      {showAssignLeadsModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg border border-gray-700 w-full max-w-md">
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <h3 className="text-lg font-bold text-white">Assign Leads to List</h3>
              <button
                onClick={() => setShowAssignLeadsModal(false)}
                className="text-gray-400 hover:text-white"
              >
                ✕
              </button>
            </div>
            <div className="p-6">
              <p className="text-gray-300 mb-4">
                Assign {selectedLeads.length} selected lead{selectedLeads.length !== 1 ? 's' : ''} to a list:
              </p>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Select List</label>
                  <select
                    value={selectedListId}
                    onChange={(e) => setSelectedListId(e.target.value)}
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-yellow-600"
                    required
                  >
                    <option value="">Choose a list...</option>
                    {leadLists.map(list => (
                      <option key={list.id} value={list.id}>
                        {list.list_name} ({typeof list.lead_count === 'number' ? list.lead_count : 0} leads)
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => setShowAssignLeadsModal(false)}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded font-medium transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleAssignLeadsToList}
                  disabled={!selectedListId}
                  className="bg-yellow-600 hover:bg-yellow-700 disabled:opacity-50 disabled:cursor-not-allowed text-black px-4 py-2 rounded font-medium transition-colors"
                >
                  Assign Leads
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
