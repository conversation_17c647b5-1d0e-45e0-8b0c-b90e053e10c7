import React, { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';
import { realtimeValidation, ValidationResult } from '../../lib/realtimeValidation';
import { ValidationInput } from '../ValidationFeedback';

interface UsernameEditorProps {
  currentUsername: string;
  userId: number;
  onUsernameUpdate: (newUsername: string) => void;
  onClose: () => void;
}

export const UsernameEditor: React.FC<UsernameEditorProps> = ({
  currentUsername,
  userId,
  onUsernameUpdate,
  onClose
}) => {
  const [newUsername, setNewUsername] = useState(currentUsername);
  const [usernameValidation, setUsernameValidation] = useState<ValidationResult | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Reset states when component mounts
  useEffect(() => {
    setNewUsername(currentUsername);
    setUsernameValidation(null);
    setError(null);
    setSuccess(false);
  }, [currentUsername]);

  // Handle username input change with real-time validation
  const handleUsernameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setNewUsername(value);
    setError(null);
    setSuccess(false);

    // Only validate if username is different from current and meets basic requirements
    if (value.trim() !== currentUsername && value.trim().length >= 2) {
      realtimeValidation.validateUsername(value.trim(), setUsernameValidation);
    } else if (value.trim() === currentUsername) {
      // Clear validation if username is same as current
      setUsernameValidation(null);
    } else {
      // Show basic validation error for short usernames
      setUsernameValidation({
        isValid: false,
        isChecking: false,
        message: 'Username must be at least 2 characters long',
        type: 'error'
      });
    }
  };

  // Handle username update
  const handleUpdateUsername = async () => {
    if (!newUsername.trim() || newUsername.trim() === currentUsername) {
      setError('Please enter a different username');
      return;
    }

    if (!usernameValidation || !usernameValidation.isValid) {
      setError('Please enter a valid username');
      return;
    }

    setIsUpdating(true);
    setError(null);

    try {
      console.log('🔄 Starting username update process...');
      console.log('📝 User ID:', userId);
      console.log('📝 Current username:', currentUsername);
      console.log('📝 New username:', newUsername.trim());

      // Use service role client to bypass RLS policies for reliable updates
      const serviceClient = getServiceRoleClient();

      // First, check if username is already taken by another user
      const { data: existingUser, error: checkError } = await serviceClient
        .from('users')
        .select('id, username')
        .eq('username', newUsername.trim())
        .neq('id', userId)
        .single();

      if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows found (which is good)
        console.error('❌ Error checking username uniqueness:', checkError);
        throw new Error('Failed to verify username availability');
      }

      if (existingUser) {
        setError('Username is already taken by another user');
        return;
      }

      console.log('✅ Username is available');

      // Try to use atomic function first, fallback to manual transaction
      let updateSuccess = false;

      try {
        // Try comprehensive function first (updates ALL tables)
        const { data: result, error: comprehensiveError } = await serviceClient.rpc('update_username_comprehensive', {
          p_user_id: userId,
          p_new_username: newUsername.trim()
        });

        if (!comprehensiveError && result) {
          updateSuccess = true;
          console.log('✅ Used comprehensive function for username update');
          console.log('📊 Update result:', result);
          console.log('🔄 Tables updated:', result.tables_updated?.join(', ') || 'unknown');
        } else {
          console.log('⚠️ Comprehensive function not available, using fallback method');
          console.log('Error:', comprehensiveError?.message);
          throw comprehensiveError;
        }
      } catch (comprehensiveError) {
        // Fallback to manual transaction
        console.log('🔄 Using manual transaction for username update');

        // Update users table
        const { error: usersError } = await serviceClient
          .from('users')
          .update({
            username: newUsername.trim(),
            updated_at: new Date().toISOString()
          })
          .eq('id', userId);

        if (usersError) {
          console.error('❌ Error updating users table:', usersError);

          // Handle specific error cases
          if (usersError.message?.includes('unique constraint') || usersError.code === '23505') {
            setError('Username is already taken');
          } else if (usersError.message?.includes('permission denied')) {
            setError('You do not have permission to update your username');
          } else {
            setError(usersError.message || 'Failed to update username');
          }
          return;
        }

        // Update telegram_users table if user has telegram account
        const { error: telegramError } = await serviceClient
          .from('telegram_users')
          .update({
            username: newUsername.trim(),
            updated_at: new Date().toISOString()
          })
          .eq('user_id', userId);

        // Don't fail if telegram update fails (user might not have telegram account)
        if (telegramError && telegramError.code !== 'PGRST116') {
          console.warn('⚠️ Could not update telegram_users username:', telegramError.message);
        } else if (!telegramError) {
          console.log('✅ Updated telegram_users table');
        }

        // Update referral codes in referrals table
        console.log('🔄 Updating referral codes...');

        // First, get all referrals where this user is the referrer
        const { data: referrals, error: referralsSelectError } = await serviceClient
          .from('referrals')
          .select('id, referral_code')
          .eq('referrer_id', userId);

        if (referralsSelectError) {
          console.warn('⚠️ Could not fetch referrals for username update:', referralsSelectError.message);
        } else if (referrals && referrals.length > 0) {
          console.log(`📋 Found ${referrals.length} referrals to update`);

          // Update each referral code that contains the old username
          let updatedReferrals = 0;
          for (const referral of referrals) {
            if (referral.referral_code && referral.referral_code.includes(currentUsername)) {
              const newReferralCode = referral.referral_code.replace(currentUsername, newUsername.trim());

              const { error: referralUpdateError } = await serviceClient
                .from('referrals')
                .update({
                  referral_code: newReferralCode,
                  updated_at: new Date().toISOString()
                })
                .eq('id', referral.id);

              if (referralUpdateError) {
                console.warn(`⚠️ Could not update referral ${referral.id}:`, referralUpdateError.message);
              } else {
                updatedReferrals++;
                console.log(`✅ Updated referral code: ${referral.referral_code} → ${newReferralCode}`);
              }
            }
          }

          console.log(`✅ Updated ${updatedReferrals} referral codes`);
        } else {
          console.log('📋 No referrals found for this user');
        }

        // Update user_share_holdings table (CRITICAL - this was missing!)
        console.log('🔄 Updating user_share_holdings table...');
        const { error: shareHoldingsError } = await serviceClient
          .from('user_share_holdings')
          .update({
            username: newUsername.trim()
          })
          .eq('user_id', userId);

        if (shareHoldingsError) {
          console.warn('⚠️ Could not update user_share_holdings table:', shareHoldingsError.message);
          // Don't fail the entire operation for this table update
        } else {
          console.log('✅ Updated user_share_holdings table');
        }

        updateSuccess = true;
        console.log('✅ Manual transaction completed successfully');
      }

      if (!updateSuccess) {
        setError('Failed to update username');
        return;
      }

      console.log('✅ Username updated successfully in database');

      // Success
      setSuccess(true);
      onUsernameUpdate(newUsername.trim());

      // Close modal after brief delay
      setTimeout(() => {
        onClose();
      }, 1500);

    } catch (error: any) {
      console.error('❌ Error updating username:', error);
      setError(error.message || 'Failed to update username');
    } finally {
      setIsUpdating(false);
    }
  };

  // Check if username can be updated
  const canUpdate = newUsername.trim() !== currentUsername && 
                   usernameValidation?.isValid && 
                   !isUpdating;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 w-full max-w-md mx-4">
        <h3 className="text-xl font-bold text-white mb-4">✏️ Change Username</h3>

        {success ? (
          <div className="text-center py-8">
            <div className="text-green-400 text-4xl mb-4">✓</div>
            <h4 className="text-lg font-semibold text-white mb-2">Username Updated!</h4>
            <p className="text-gray-300 text-sm">Your username has been successfully changed.</p>
          </div>
        ) : (
          <>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Current Username
                </label>
                <div className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-gray-400">
                  {currentUsername}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  New Username <span className="text-red-400">*</span>
                </label>
                <ValidationInput
                  type="text"
                  value={newUsername}
                  onChange={handleUsernameChange}
                  validation={usernameValidation}
                  placeholder="Enter new username"
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-yellow-500"
                />
                <p className="text-gray-400 text-xs mt-1">
                  Username can only contain letters, numbers, underscores, and hyphens
                </p>
              </div>

              {error && (
                <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-3">
                  <p className="text-red-400 text-sm">{error}</p>
                </div>
              )}
            </div>

            <div className="flex gap-3 mt-6">
              <button
                onClick={onClose}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                disabled={isUpdating}
              >
                Cancel
              </button>
              <button
                onClick={handleUpdateUsername}
                className="flex-1 bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-lg transition-colors disabled:opacity-50"
                disabled={!canUpdate}
              >
                {isUpdating ? 'Updating...' : 'Update Username'}
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};
