/**
 * LIST MANAGER DASHBOARD
 * 
 * Comprehensive contact management and bulk email system with:
 * - Contact management (create, edit, delete, import)
 * - List management (create lists, add/remove contacts)
 * - Email template management
 * - Bulk email campaigns
 * - Analytics and reporting
 */

import React, { useState, useEffect } from 'react';
import { ListManagerService, Contact, ContactList, EmailTemplate, BulkEmailCampaign } from '../../lib/services/listManagerService';
import { CreateContactModal, CreateListModal, CreateTemplateModal, CreateCampaignModal } from './ListManagerModals';

interface ListManagerDashboardProps {
  currentUser: any;
}

export default function ListManagerDashboard({ currentUser }: ListManagerDashboardProps) {
  const [activeTab, setActiveTab] = useState<'contacts' | 'lists' | 'templates' | 'campaigns'>('contacts');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Data states
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [contactLists, setContactLists] = useState<ContactList[]>([]);
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [campaigns, setCampaigns] = useState<BulkEmailCampaign[]>([]);

  // Pagination and filtering
  const [currentPage, setCurrentPage] = useState(1);
  const [totalContacts, setTotalContacts] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Modal states
  const [showCreateContact, setShowCreateContact] = useState(false);
  const [showCreateList, setShowCreateList] = useState(false);
  const [showCreateTemplate, setShowCreateTemplate] = useState(false);
  const [showCreateCampaign, setShowCreateCampaign] = useState(false);

  const listManagerService = ListManagerService.getInstance();
  const ITEMS_PER_PAGE = 20;

  useEffect(() => {
    loadData();
  }, [activeTab, currentPage, searchQuery, statusFilter]);

  const loadData = async () => {
    setLoading(true);
    setError(null);

    try {
      switch (activeTab) {
        case 'contacts':
          await loadContacts();
          break;
        case 'lists':
          await loadLists();
          break;
        case 'templates':
          await loadTemplates();
          break;
        case 'campaigns':
          await loadCampaigns();
          break;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const loadContacts = async () => {
    const filters = {
      status: statusFilter === 'all' ? undefined : statusFilter,
      search: searchQuery || undefined,
      limit: ITEMS_PER_PAGE,
      offset: (currentPage - 1) * ITEMS_PER_PAGE
    };

    const result = await listManagerService.getContacts(filters);
    setContacts(result.contacts);
    setTotalContacts(result.total);
  };

  const loadLists = async () => {
    const lists = await listManagerService.getLists(currentUser?.id);
    setContactLists(lists);
  };

  const loadTemplates = async () => {
    const templateList = await listManagerService.getTemplates(currentUser?.id);
    setTemplates(templateList);
  };

  const loadCampaigns = async () => {
    const campaignList = await listManagerService.getCampaigns(currentUser?.id);
    setCampaigns(campaignList);
  };

  const totalPages = Math.ceil(totalContacts / ITEMS_PER_PAGE);

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-yellow-400 mb-2">List Manager</h1>
          <p className="text-gray-300">Comprehensive contact management and bulk email system</p>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 mb-6 bg-gray-800 p-1 rounded-lg">
          {[
            { key: 'contacts', label: 'Contacts' },
            { key: 'lists', label: 'Lists' },
            { key: 'templates', label: 'Templates' },
            { key: 'campaigns', label: 'Campaigns' }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => {
                setActiveTab(tab.key as any);
                setCurrentPage(1);
              }}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                activeTab === tab.key
                  ? 'bg-yellow-600 text-white'
                  : 'text-gray-300 hover:bg-gray-700'
              }`}
            >
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded mb-6">
            <strong>Error:</strong> {error}
          </div>
        )}

        {/* Main Content Area */}
        <div className="bg-gray-800 rounded-lg p-6">
          {/* Contacts Tab */}
          {activeTab === 'contacts' && (
            <div>
              {/* Contacts Header */}
              <div className="flex justify-between items-center mb-6">
                <div className="flex items-center space-x-4">
                  <h2 className="text-xl font-semibold">Contacts ({totalContacts})</h2>
                  <div className="flex space-x-2">
                    <select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      className="bg-gray-700 border border-gray-600 rounded px-3 py-1 text-sm"
                    >
                      <option value="all">All Status</option>
                      <option value="active">Active</option>
                      <option value="unsubscribed">Unsubscribed</option>
                      <option value="bounced">Bounced</option>
                      <option value="invalid">Invalid</option>
                    </select>
                    <input
                      type="text"
                      placeholder="Search contacts..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="bg-gray-700 border border-gray-600 rounded px-3 py-1 text-sm w-64"
                    />
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setShowCreateContact(true)}
                    className="bg-yellow-600 hover:bg-yellow-700 px-4 py-2 rounded text-sm font-medium"
                  >
                    Add Contact
                  </button>
                  <button
                    onClick={() => {/* TODO: Import contacts */}}
                    className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded text-sm font-medium"
                  >
                    Import
                  </button>
                  <button
                    onClick={() => {/* TODO: Export contacts */}}
                    className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded text-sm font-medium"
                  >
                    Export
                  </button>
                </div>
              </div>

              {/* Contacts Table */}
              {loading ? (
                <div className="text-center py-8">
                  <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400"></div>
                  <p className="mt-2 text-gray-400">Loading contacts...</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-gray-700">
                        <th className="text-left py-3 px-4">Email</th>
                        <th className="text-left py-3 px-4">Name</th>
                        <th className="text-left py-3 px-4">Company</th>
                        <th className="text-left py-3 px-4">Status</th>
                        <th className="text-left py-3 px-4">Tags</th>
                        <th className="text-left py-3 px-4">Created</th>
                        <th className="text-left py-3 px-4">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {contacts.map((contact) => (
                        <tr key={contact.id} className="border-b border-gray-700 hover:bg-gray-750">
                          <td className="py-3 px-4">
                            <div className="font-medium">{contact.email}</div>
                          </td>
                          <td className="py-3 px-4">
                            {contact.full_name || '-'}
                          </td>
                          <td className="py-3 px-4">
                            {contact.company || '-'}
                          </td>
                          <td className="py-3 px-4">
                            <span className={`px-2 py-1 rounded text-xs ${
                              contact.status === 'active' ? 'bg-green-900 text-green-200' :
                              contact.status === 'unsubscribed' ? 'bg-yellow-900 text-yellow-200' :
                              contact.status === 'bounced' ? 'bg-red-900 text-red-200' :
                              'bg-gray-700 text-gray-300'
                            }`}>
                              {contact.status}
                            </span>
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex flex-wrap gap-1">
                              {contact.tags.slice(0, 3).map((tag, index) => (
                                <span key={index} className="bg-blue-900 text-blue-200 px-2 py-1 rounded text-xs">
                                  {tag}
                                </span>
                              ))}
                              {contact.tags.length > 3 && (
                                <span className="text-gray-400 text-xs">+{contact.tags.length - 3}</span>
                              )}
                            </div>
                          </td>
                          <td className="py-3 px-4 text-gray-400">
                            {new Date(contact.created_at).toLocaleDateString()}
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex space-x-2">
                              <button
                                onClick={() => {/* TODO: Edit contact */}}
                                className="text-blue-400 hover:text-blue-300 text-xs"
                              >
                                Edit
                              </button>
                              <button
                                onClick={() => {/* TODO: Delete contact */}}
                                className="text-red-400 hover:text-red-300 text-xs"
                              >
                                Delete
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>

                  {contacts.length === 0 && (
                    <div className="text-center py-8 text-gray-400">
                      No contacts found. Create your first contact to get started.
                    </div>
                  )}
                </div>
              )}

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center items-center space-x-2 mt-6">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="px-3 py-1 rounded bg-gray-700 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>
                  <span className="text-sm text-gray-400">
                    Page {currentPage} of {totalPages}
                  </span>
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="px-3 py-1 rounded bg-gray-700 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
              )}
            </div>
          )}

          {/* Lists Tab */}
          {activeTab === 'lists' && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold">Contact Lists ({contactLists.length})</h2>
                <button
                  onClick={() => setShowCreateList(true)}
                  className="bg-yellow-600 hover:bg-yellow-700 px-4 py-2 rounded text-sm font-medium"
                >
                  Create List
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {contactLists.map((list) => (
                  <div key={list.id} className="bg-gray-700 rounded-lg p-4">
                    <h3 className="font-semibold text-lg mb-2">{list.name}</h3>
                    <p className="text-gray-300 text-sm mb-3">{list.description || 'No description'}</p>
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-400">{list.contact_count} contacts</span>
                      <div className="flex space-x-2">
                        <button className="text-blue-400 hover:text-blue-300">Edit</button>
                        <button className="text-red-400 hover:text-red-300">Delete</button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {contactLists.length === 0 && (
                <div className="text-center py-8 text-gray-400">
                  No contact lists found. Create your first list to organize your contacts.
                </div>
              )}
            </div>
          )}

          {/* Templates Tab */}
          {activeTab === 'templates' && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold">Email Templates ({templates.length})</h2>
                <button
                  onClick={() => setShowCreateTemplate(true)}
                  className="bg-yellow-600 hover:bg-yellow-700 px-4 py-2 rounded text-sm font-medium"
                >
                  Create Template
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {templates.map((template) => (
                  <div key={template.id} className="bg-gray-700 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-semibold text-lg">{template.name}</h3>
                      <span className={`px-2 py-1 rounded text-xs ${
                        template.template_type === 'newsletter' ? 'bg-blue-900 text-blue-200' :
                        template.template_type === 'promotional' ? 'bg-green-900 text-green-200' :
                        template.template_type === 'announcement' ? 'bg-yellow-900 text-yellow-200' :
                        'bg-gray-600 text-gray-300'
                      }`}>
                        {template.template_type}
                      </span>
                    </div>
                    <p className="text-gray-300 text-sm mb-3">{template.subject}</p>
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-400">
                        {template.variables.length} variables
                      </span>
                      <div className="flex space-x-2">
                        <button className="text-blue-400 hover:text-blue-300">Edit</button>
                        <button className="text-green-400 hover:text-green-300">Preview</button>
                        <button className="text-red-400 hover:text-red-300">Delete</button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {templates.length === 0 && (
                <div className="text-center py-8 text-gray-400">
                  No email templates found. Create your first template to start sending campaigns.
                </div>
              )}
            </div>
          )}

          {/* Campaigns Tab */}
          {activeTab === 'campaigns' && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold">Email Campaigns ({campaigns.length})</h2>
                <button
                  onClick={() => setShowCreateCampaign(true)}
                  className="bg-yellow-600 hover:bg-yellow-700 px-4 py-2 rounded text-sm font-medium"
                >
                  Create Campaign
                </button>
              </div>

              <div className="space-y-4">
                {campaigns.map((campaign) => (
                  <div key={campaign.id} className="bg-gray-700 rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-semibold text-lg mb-1">{campaign.name}</h3>
                        <p className="text-gray-300 text-sm mb-2">{campaign.subject}</p>
                        <div className="flex items-center space-x-4 text-sm text-gray-400">
                          <span>Recipients: {campaign.total_recipients}</span>
                          <span>Sent: {campaign.sent_count}</span>
                          <span>Failed: {campaign.failed_count}</span>
                          {campaign.sent_at && (
                            <span>Sent: {new Date(campaign.sent_at).toLocaleDateString()}</span>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <span className={`px-3 py-1 rounded text-sm ${
                          campaign.status === 'sent' ? 'bg-green-900 text-green-200' :
                          campaign.status === 'sending' ? 'bg-blue-900 text-blue-200' :
                          campaign.status === 'failed' ? 'bg-red-900 text-red-200' :
                          campaign.status === 'scheduled' ? 'bg-yellow-900 text-yellow-200' :
                          'bg-gray-600 text-gray-300'
                        }`}>
                          {campaign.status}
                        </span>
                        <div className="flex space-x-2">
                          {campaign.status === 'draft' && (
                            <button className="text-green-400 hover:text-green-300 text-sm">Send</button>
                          )}
                          <button className="text-blue-400 hover:text-blue-300 text-sm">View</button>
                          <button className="text-red-400 hover:text-red-300 text-sm">Delete</button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {campaigns.length === 0 && (
                <div className="text-center py-8 text-gray-400">
                  No email campaigns found. Create your first campaign to start reaching your contacts.
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Modals */}
      <CreateContactModal
        isOpen={showCreateContact}
        onClose={() => setShowCreateContact(false)}
        onSuccess={loadData}
        currentUser={currentUser}
      />

      <CreateListModal
        isOpen={showCreateList}
        onClose={() => setShowCreateList(false)}
        onSuccess={loadData}
        currentUser={currentUser}
      />

      <CreateTemplateModal
        isOpen={showCreateTemplate}
        onClose={() => setShowCreateTemplate(false)}
        onSuccess={loadData}
        currentUser={currentUser}
      />

      <CreateCampaignModal
        isOpen={showCreateCampaign}
        onClose={() => setShowCreateCampaign(false)}
        onSuccess={loadData}
        currentUser={currentUser}
      />
    </div>
  );
}
