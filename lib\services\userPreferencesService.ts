import { supabase, getServiceRoleClient } from '../supabase';

export interface UserPreference {
  id: string;
  user_id: number;
  preference_type: string;
  preference_value: string;
  created_at: string;
  updated_at: string;
}

export interface NotificationPreferences {
  id: number;
  user_id: number;
  telegram_id: number;
  audio_enabled: boolean;
  notification_volume: 'low' | 'medium' | 'high';
  payment_approval_audio: boolean;
  payment_rejection_audio: boolean;
  withdrawal_approval_audio: boolean;
  withdrawal_rejection_audio: boolean;
  commission_update_audio: boolean;
  referral_bonus_audio: boolean;
  system_announcement_audio: boolean;
  quiet_hours_enabled: boolean;
  quiet_hours_start: string;
  quiet_hours_end: string;
  timezone: string;
  custom_sound_enabled: boolean;
  notification_frequency: 'all' | 'important' | 'minimal';
  created_at: string;
  updated_at: string;
}

export interface UserSettings {
  // General Preferences
  theme: 'dark' | 'light' | 'auto';
  language: 'en' | 'fr' | 'es' | 'pt';
  currency_display: 'USD' | 'ZAR' | 'EUR' | 'GBP';
  timezone: string;
  
  // Privacy Settings
  profile_visibility: 'public' | 'private' | 'contacts_only';
  show_earnings: boolean;
  show_referrals: boolean;
  allow_contact: boolean;
  
  // Security Settings
  two_factor_enabled: boolean;
  login_notifications: boolean;
  session_timeout: number; // minutes
  require_password_for_sensitive: boolean;
  
  // Communication Preferences
  email_notifications: boolean;
  marketing_emails: boolean;
  newsletter_subscription: boolean;
  sms_notifications: boolean;
  
  // Dashboard Preferences
  default_dashboard: 'shareholder' | 'affiliate';
  show_welcome_message: boolean;
  compact_view: boolean;
  auto_refresh: boolean;
  refresh_interval: number; // seconds
}

class UserPreferencesService {
  private readonly DEFAULT_SETTINGS: UserSettings = {
    // General
    theme: 'dark',
    language: 'en',
    currency_display: 'USD',
    timezone: 'UTC',
    
    // Privacy
    profile_visibility: 'private',
    show_earnings: false,
    show_referrals: true,
    allow_contact: true,
    
    // Security
    two_factor_enabled: false,
    login_notifications: true,
    session_timeout: 60,
    require_password_for_sensitive: true,
    
    // Communication
    email_notifications: true,
    marketing_emails: false,
    newsletter_subscription: false,
    sms_notifications: false,
    
    // Dashboard
    default_dashboard: 'shareholder',
    show_welcome_message: true,
    compact_view: false,
    auto_refresh: true,
    refresh_interval: 30
  };

  /**
   * Get user preferences
   */
  async getUserPreferences(userId: number): Promise<UserSettings> {
    console.log(`🔍 Getting preferences for user ${userId}`);

    const serviceClient = getServiceRoleClient();

    const { data, error } = await serviceClient
      .from('user_preferences')
      .select('*')
      .eq('user_id', userId);

    if (error) {
      console.error('❌ Failed to get user preferences:', error);
      return this.DEFAULT_SETTINGS;
    }

    // Convert database preferences to settings object
    const settings = { ...this.DEFAULT_SETTINGS };
    
    data?.forEach(pref => {
      const key = pref.preference_type as keyof UserSettings;
      if (key in settings) {
        try {
          // Parse JSON values or use string values
          const value = pref.preference_value.startsWith('{') || pref.preference_value.startsWith('[')
            ? JSON.parse(pref.preference_value)
            : pref.preference_value === 'true' ? true
            : pref.preference_value === 'false' ? false
            : isNaN(Number(pref.preference_value)) ? pref.preference_value
            : Number(pref.preference_value);
          
          (settings as any)[key] = value;
        } catch (e) {
          console.warn(`Failed to parse preference ${key}:`, pref.preference_value);
        }
      }
    });

    console.log('✅ User preferences loaded successfully');
    return settings;
  }

  /**
   * Update user preference
   */
  async updatePreference(
    userId: number, 
    preferenceType: keyof UserSettings, 
    value: any
  ): Promise<boolean> {
    console.log(`📝 Updating preference ${preferenceType} for user ${userId}`);

    const serviceClient = getServiceRoleClient();

    const preferenceValue = typeof value === 'object' 
      ? JSON.stringify(value) 
      : String(value);

    // Check if preference exists
    const { data: existing } = await serviceClient
      .from('user_preferences')
      .select('id')
      .eq('user_id', userId)
      .eq('preference_type', preferenceType)
      .single();

    if (existing) {
      // Update existing preference
      const { error } = await serviceClient
        .from('user_preferences')
        .update({
          preference_value: preferenceValue,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .eq('preference_type', preferenceType);

      if (error) {
        console.error('❌ Failed to update preference:', error);
        return false;
      }
    } else {
      // Create new preference
      const { error } = await serviceClient
        .from('user_preferences')
        .insert({
          user_id: userId,
          preference_type: preferenceType,
          preference_value: preferenceValue,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (error) {
        console.error('❌ Failed to create preference:', error);
        return false;
      }
    }

    console.log('✅ Preference updated successfully');
    return true;
  }

  /**
   * Update multiple preferences
   */
  async updatePreferences(userId: number, preferences: Partial<UserSettings>): Promise<boolean> {
    console.log(`📝 Updating multiple preferences for user ${userId}`);

    try {
      const updates = Object.entries(preferences).map(([key, value]) =>
        this.updatePreference(userId, key as keyof UserSettings, value)
      );

      const results = await Promise.all(updates);
      const success = results.every(result => result);

      if (success) {
        console.log('✅ All preferences updated successfully');
      } else {
        console.warn('⚠️ Some preferences failed to update');
      }

      return success;
    } catch (error) {
      console.error('❌ Failed to update preferences:', error);
      return false;
    }
  }

  /**
   * Get notification preferences
   */
  async getNotificationPreferences(userId: number): Promise<NotificationPreferences | null> {
    console.log(`🔔 Getting notification preferences for user ${userId}`);

    const serviceClient = getServiceRoleClient();

    const { data, error } = await serviceClient
      .from('user_notification_preferences')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('❌ Failed to get notification preferences:', error);
      return null;
    }

    console.log('✅ Notification preferences loaded successfully');
    return data;
  }

  /**
   * Update notification preferences
   */
  async updateNotificationPreferences(
    userId: number, 
    preferences: Partial<NotificationPreferences>
  ): Promise<boolean> {
    console.log(`🔔 Updating notification preferences for user ${userId}`);

    const serviceClient = getServiceRoleClient();

    const { error } = await serviceClient
      .from('user_notification_preferences')
      .upsert({
        user_id: userId,
        ...preferences,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id'
      });

    if (error) {
      console.error('❌ Failed to update notification preferences:', error);
      return false;
    }

    console.log('✅ Notification preferences updated successfully');
    return true;
  }

  /**
   * Reset preferences to defaults
   */
  async resetPreferences(userId: number): Promise<boolean> {
    console.log(`🔄 Resetting preferences to defaults for user ${userId}`);

    const serviceClient = getServiceRoleClient();

    // Delete all existing preferences
    const { error: deleteError } = await serviceClient
      .from('user_preferences')
      .delete()
      .eq('user_id', userId);

    if (deleteError) {
      console.error('❌ Failed to delete existing preferences:', deleteError);
      return false;
    }

    // Insert default preferences
    const defaultEntries = Object.entries(this.DEFAULT_SETTINGS).map(([key, value]) => ({
      user_id: userId,
      preference_type: key,
      preference_value: typeof value === 'object' ? JSON.stringify(value) : String(value),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));

    const { error: insertError } = await serviceClient
      .from('user_preferences')
      .insert(defaultEntries);

    if (insertError) {
      console.error('❌ Failed to insert default preferences:', insertError);
      return false;
    }

    console.log('✅ Preferences reset to defaults successfully');
    return true;
  }

  /**
   * Export user preferences
   */
  async exportPreferences(userId: number): Promise<{ settings: UserSettings; notifications: NotificationPreferences | null } | null> {
    try {
      const [settings, notifications] = await Promise.all([
        this.getUserPreferences(userId),
        this.getNotificationPreferences(userId)
      ]);

      return { settings, notifications };
    } catch (error) {
      console.error('❌ Failed to export preferences:', error);
      return null;
    }
  }

  /**
   * Import user preferences
   */
  async importPreferences(
    userId: number, 
    data: { settings: Partial<UserSettings>; notifications?: Partial<NotificationPreferences> }
  ): Promise<boolean> {
    try {
      const results = await Promise.all([
        this.updatePreferences(userId, data.settings),
        data.notifications ? this.updateNotificationPreferences(userId, data.notifications) : Promise.resolve(true)
      ]);

      return results.every(result => result);
    } catch (error) {
      console.error('❌ Failed to import preferences:', error);
      return false;
    }
  }
}

// Export singleton instance
export const userPreferencesService = new UserPreferencesService();
