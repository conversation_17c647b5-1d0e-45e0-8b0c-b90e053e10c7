{"mcpServers": {"supabase": {"command": "node", "args": ["mcp-supabase-server.js"], "env": {"SUPABASE_URL": "https://fgubaqoftdeefcakejwu.supabase.co", "SUPABASE_SERVICE_ROLE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA"}, "disabled": false, "autoApprove": ["supabase_query", "supabase_insert", "supabase_update", "supabase_delete", "supabase_list_tables", "supabase_describe_table", "supabase_storage_list"]}}}