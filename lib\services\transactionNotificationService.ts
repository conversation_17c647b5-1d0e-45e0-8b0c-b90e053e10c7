/**
 * TRANSACTION NOTIFICATION SERVICE
 * 
 * Automated email notification system for share purchase transactions.
 * Sends immediate <NAME_EMAIL> for every transaction.
 */

import { Resend } from 'resend';
import { getServiceRoleClient } from '../supabase';

// Environment configuration
const RESEND_API_KEY = import.meta.env.VITE_RESEND_API_KEY || process.env.RESEND_API_KEY;
const RESEND_FROM_EMAIL = import.meta.env.VITE_RESEND_FROM_EMAIL || process.env.RESEND_FROM_EMAIL || '<EMAIL>';
const RESEND_FROM_NAME = import.meta.env.VITE_RESEND_FROM_NAME || process.env.RESEND_FROM_NAME || 'Aureus Alliance Holdings';
const ACCOUNTS_EMAIL = '<EMAIL>';
const WEBSITE_URL = 'https://aureus.africa';

// Initialize Resend client
let resend: Resend | null = null;

try {
  if (RESEND_API_KEY) {
    resend = new Resend(RESEND_API_KEY);
    console.log('✅ Transaction notification service initialized');
  } else {
    console.warn('⚠️ RESEND_API_KEY not found - transaction notifications disabled');
  }
} catch (error) {
  console.error('❌ Failed to initialize transaction notification service:', error);
}

export interface TransactionNotificationData {
  transactionId: string;
  userId: number;
  userFullName: string;
  userEmail: string;
  aureusId: string;
  paymentMethod: 'USDT' | 'BANK_TRANSFER';
  amount: number;
  currency: string;
  shareQuantity: number;
  transactionHash?: string;
  proofOfPaymentUrl?: string;
  walletAddress?: string;
  bankReference?: string;
  network?: string;
  status: string;
  createdAt: string;
  kycStatus?: string;
}

export interface NotificationResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

/**
 * Send transaction notification email to accounts team
 */
export async function sendTransactionNotification(data: TransactionNotificationData): Promise<NotificationResult> {
  if (!resend) {
    console.warn('⚠️ Transaction notification service not configured');
    return {
      success: false,
      error: 'Email service not configured'
    };
  }

  try {
    console.log(`📧 Sending transaction notification for ${data.paymentMethod} payment: ${data.transactionId}`);

    const subject = `🔔 New Share Purchase Transaction - ${data.paymentMethod} - $${data.amount}`;
    const htmlContent = generateTransactionEmailHTML(data);
    const textContent = generateTransactionEmailText(data);

    const emailResult = await resend.emails.send({
      from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
      to: [ACCOUNTS_EMAIL],
      subject: subject,
      html: htmlContent,
      text: textContent,
      tags: [
        { name: 'category', value: 'transaction_notification' },
        { name: 'payment_method', value: data.paymentMethod },
        { name: 'amount', value: data.amount.toString() },
        { name: 'user_id', value: data.userId.toString() }
      ]
    });

    if (emailResult.error) {
      throw new Error(`Resend API error: ${emailResult.error.message}`);
    }

    // Log notification in database
    await logTransactionNotification(data, emailResult.data?.id || null, 'sent');

    console.log(`✅ Transaction notification sent successfully: ${emailResult.data?.id}`);
    return {
      success: true,
      messageId: emailResult.data?.id || null
    };

  } catch (error) {
    console.error('❌ Failed to send transaction notification:', error);
    
    // Log failed notification
    await logTransactionNotification(data, null, 'failed', error instanceof Error ? error.message : 'Unknown error');
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Generate HTML email content for transaction notification
 */
function generateTransactionEmailHTML(data: TransactionNotificationData): string {
  const paymentDetails = data.paymentMethod === 'USDT' 
    ? generateUSDTPaymentDetails(data)
    : generateBankTransferDetails(data);

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>New Share Purchase Transaction</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 20px; background-color: #f4f4f4; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #DAA520, #B8860B); color: white; padding: 20px; border-radius: 8px; text-align: center; margin-bottom: 30px; }
        .alert-badge { background: #ff4444; color: white; padding: 5px 15px; border-radius: 20px; font-size: 12px; font-weight: bold; display: inline-block; margin-bottom: 10px; }
        .transaction-summary { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #DAA520; }
        .detail-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 20px 0; }
        .detail-item { background: #f8f9fa; padding: 15px; border-radius: 6px; }
        .detail-label { font-weight: bold; color: #666; font-size: 12px; text-transform: uppercase; margin-bottom: 5px; }
        .detail-value { font-size: 16px; color: #333; word-break: break-all; }
        .payment-method { background: ${data.paymentMethod === 'USDT' ? '#28a745' : '#007bff'}; color: white; padding: 5px 12px; border-radius: 15px; font-size: 12px; font-weight: bold; }
        .status-badge { padding: 5px 12px; border-radius: 15px; font-size: 12px; font-weight: bold; text-transform: uppercase; }
        .status-pending { background: #ffc107; color: #000; }
        .status-approved { background: #28a745; color: white; }
        .status-rejected { background: #dc3545; color: white; }
        .action-buttons { text-align: center; margin: 30px 0; }
        .btn { display: inline-block; padding: 12px 25px; margin: 0 10px; text-decoration: none; border-radius: 6px; font-weight: bold; }
        .btn-primary { background: #DAA520; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 12px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="alert-badge">NEW TRANSACTION</div>
          <h1>Share Purchase Transaction Alert</h1>
          <p>A new share purchase transaction requires your attention</p>
        </div>

        <div class="transaction-summary">
          <h2>Transaction Summary</h2>
          <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
            <div>
              <strong>Amount:</strong> $${data.amount.toLocaleString()} ${data.currency}
              <br>
              <strong>Shares:</strong> ${data.shareQuantity.toLocaleString()} shares
            </div>
            <div style="text-align: right;">
              <span class="payment-method">${data.paymentMethod}</span>
              <br>
              <span class="status-badge status-${data.status.toLowerCase()}">${data.status}</span>
            </div>
          </div>
        </div>

        <h3>User Information</h3>
        <div class="detail-grid">
          <div class="detail-item">
            <div class="detail-label">Full Name</div>
            <div class="detail-value">${data.userFullName}</div>
          </div>
          <div class="detail-item">
            <div class="detail-label">Email Address</div>
            <div class="detail-value">${data.userEmail}</div>
          </div>
          <div class="detail-item">
            <div class="detail-label">Aureus ID</div>
            <div class="detail-value">${data.aureusId}</div>
          </div>
          <div class="detail-item">
            <div class="detail-label">KYC Status</div>
            <div class="detail-value">${data.kycStatus || 'Not Available'}</div>
          </div>
        </div>

        ${paymentDetails}

        <h3>Transaction Details</h3>
        <div class="detail-grid">
          <div class="detail-item">
            <div class="detail-label">Transaction ID</div>
            <div class="detail-value">${data.transactionId}</div>
          </div>
          <div class="detail-item">
            <div class="detail-label">Created At</div>
            <div class="detail-value">${new Date(data.createdAt).toLocaleString()}</div>
          </div>
        </div>

        <div class="action-buttons">
          <a href="${WEBSITE_URL}/admin?tab=payment-management" class="btn btn-primary">
            Review in Admin Dashboard
          </a>
          <a href="${WEBSITE_URL}/admin?tab=transaction-management&id=${data.transactionId}" class="btn btn-secondary">
            View Transaction Details
          </a>
        </div>

        <div class="footer">
          <p>This is an automated notification from Aureus Alliance Holdings transaction monitoring system.</p>
          <p>© 2024 Aureus Alliance Holdings (Pty) Ltd. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

/**
 * Generate USDT payment details section
 */
function generateUSDTPaymentDetails(data: TransactionNotificationData): string {
  return `
    <h3>USDT Payment Details</h3>
    <div class="detail-grid">
      <div class="detail-item">
        <div class="detail-label">Network</div>
        <div class="detail-value">${data.network || 'Not specified'}</div>
      </div>
      <div class="detail-item">
        <div class="detail-label">Transaction Hash</div>
        <div class="detail-value">${data.transactionHash || 'Pending'}</div>
      </div>
      <div class="detail-item">
        <div class="detail-label">Wallet Address</div>
        <div class="detail-value">${data.walletAddress || 'Not provided'}</div>
      </div>
      <div class="detail-item">
        <div class="detail-label">Proof of Payment</div>
        <div class="detail-value">
          ${data.proofOfPaymentUrl ? `<a href="${data.proofOfPaymentUrl}" target="_blank">View Screenshot</a>` : 'Not provided'}
        </div>
      </div>
    </div>
  `;
}

/**
 * Generate bank transfer payment details section
 */
function generateBankTransferDetails(data: TransactionNotificationData): string {
  return `
    <h3>Bank Transfer Details</h3>
    <div class="detail-grid">
      <div class="detail-item">
        <div class="detail-label">Bank Reference</div>
        <div class="detail-value">${data.bankReference || 'Not provided'}</div>
      </div>
      <div class="detail-item">
        <div class="detail-label">Currency</div>
        <div class="detail-value">${data.currency}</div>
      </div>
      <div class="detail-item" style="grid-column: 1 / -1;">
        <div class="detail-label">Proof of Payment</div>
        <div class="detail-value">
          ${data.proofOfPaymentUrl ? `<a href="${data.proofOfPaymentUrl}" target="_blank">View Bank Transfer Proof</a>` : 'Not provided'}
        </div>
      </div>
    </div>
  `;
}

/**
 * Generate plain text email content
 */
function generateTransactionEmailText(data: TransactionNotificationData): string {
  const paymentDetails = data.paymentMethod === 'USDT'
    ? `
USDT Payment Details:
- Network: ${data.network || 'Not specified'}
- Transaction Hash: ${data.transactionHash || 'Pending'}
- Wallet Address: ${data.walletAddress || 'Not provided'}
- Proof of Payment: ${data.proofOfPaymentUrl || 'Not provided'}
`
    : `
Bank Transfer Details:
- Bank Reference: ${data.bankReference || 'Not provided'}
- Currency: ${data.currency}
- Proof of Payment: ${data.proofOfPaymentUrl || 'Not provided'}
`;

  return `
NEW SHARE PURCHASE TRANSACTION ALERT

Transaction Summary:
- Amount: $${data.amount.toLocaleString()} ${data.currency}
- Shares: ${data.shareQuantity.toLocaleString()} shares
- Payment Method: ${data.paymentMethod}
- Status: ${data.status}

User Information:
- Full Name: ${data.userFullName}
- Email: ${data.userEmail}
- Aureus ID: ${data.aureusId}
- KYC Status: ${data.kycStatus || 'Not Available'}

${paymentDetails}

Transaction Details:
- Transaction ID: ${data.transactionId}
- Created At: ${new Date(data.createdAt).toLocaleString()}

Action Required:
Please review this transaction in the admin dashboard:
${WEBSITE_URL}/admin?tab=payment-management

View transaction details:
${WEBSITE_URL}/admin?tab=transaction-management&id=${data.transactionId}

---
This is an automated notification from Aureus Alliance Holdings transaction monitoring system.
© 2024 Aureus Alliance Holdings (Pty) Ltd. All rights reserved.
  `;
}

/**
 * Log transaction notification in database
 */
async function logTransactionNotification(
  data: TransactionNotificationData,
  messageId: string | null,
  status: 'sent' | 'failed',
  error?: string
): Promise<void> {
  try {
    const supabase = getServiceRoleClient();

    await supabase
      .from('email_delivery_log')
      .insert({
        recipient_email: ACCOUNTS_EMAIL,
        email_type: 'transaction_notification',
        subject: `New Share Purchase Transaction - ${data.paymentMethod} - $${data.amount}`,
        status: status,
        message_id: messageId,
        error_message: error,
        metadata: {
          transaction_id: data.transactionId,
          user_id: data.userId,
          payment_method: data.paymentMethod,
          amount: data.amount,
          currency: data.currency
        },
        created_at: new Date().toISOString()
      });

    console.log(`📝 Transaction notification logged: ${status}`);
  } catch (logError) {
    console.error('❌ Failed to log transaction notification:', logError);
  }
}

/**
 * Send notification for USDT payment transaction
 */
export async function notifyUSDTTransaction(
  transactionId: string,
  userId: number,
  amount: number,
  shareQuantity: number,
  transactionHash?: string,
  network?: string,
  walletAddress?: string,
  proofUrl?: string
): Promise<NotificationResult> {
  try {
    // Fetch user details
    const supabase = getServiceRoleClient();
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select(`
        id,
        username,
        full_name,
        email,
        kyc_information (
          kyc_status
        )
      `)
      .eq('id', userId)
      .single();

    if (userError || !userData) {
      throw new Error(`Failed to fetch user data: ${userError?.message}`);
    }

    const notificationData: TransactionNotificationData = {
      transactionId,
      userId,
      userFullName: userData.full_name || userData.username,
      userEmail: userData.email,
      aureusId: userData.username,
      paymentMethod: 'USDT',
      amount,
      currency: 'USDT',
      shareQuantity,
      transactionHash,
      network,
      walletAddress,
      proofOfPaymentUrl: proofUrl,
      status: 'pending',
      createdAt: new Date().toISOString(),
      kycStatus: userData.kyc_information?.[0]?.kyc_status || 'Not Available'
    };

    return await sendTransactionNotification(notificationData);
  } catch (error) {
    console.error('❌ Failed to send USDT transaction notification:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Send notification for bank transfer transaction
 */
export async function notifyBankTransferTransaction(
  transactionId: string,
  userId: number,
  amount: number,
  currency: string,
  shareQuantity: number,
  bankReference?: string,
  proofUrl?: string
): Promise<NotificationResult> {
  try {
    // Fetch user details
    const supabase = getServiceRoleClient();
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select(`
        id,
        username,
        full_name,
        email,
        kyc_information (
          kyc_status
        )
      `)
      .eq('id', userId)
      .single();

    if (userError || !userData) {
      throw new Error(`Failed to fetch user data: ${userError?.message}`);
    }

    const notificationData: TransactionNotificationData = {
      transactionId,
      userId,
      userFullName: userData.full_name || userData.username,
      userEmail: userData.email,
      aureusId: userData.username,
      paymentMethod: 'BANK_TRANSFER',
      amount,
      currency,
      shareQuantity,
      bankReference,
      proofOfPaymentUrl: proofUrl,
      status: 'pending',
      createdAt: new Date().toISOString(),
      kycStatus: userData.kyc_information?.[0]?.kyc_status || 'Not Available'
    };

    return await sendTransactionNotification(notificationData);
  } catch (error) {
    console.error('❌ Failed to send bank transfer transaction notification:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
