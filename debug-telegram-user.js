import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function debugTelegramUser() {
  try {
    console.log('🔍 Checking telegram_users table for telegram_id: 1393852632')

    const { data: telegramUser, error } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', 1393852632)
      .single()

    if (error) {
      console.error('❌ Error fetching telegram user:', error)

      // Check if there are any telegram_users records at all
      console.log('🔍 Checking all telegram_users records...')
      const { data: allUsers, error: allError } = await supabase
        .from('telegram_users')
        .select('*')
        .limit(10)

      if (allError) {
        console.error('❌ Error fetching all telegram users:', allError)
      } else {
        console.log('📋 Found telegram_users records:', allUsers)
      }
      return
    }

    console.log('✅ Telegram user found:', telegramUser)

    if (telegramUser.user_id) {
      console.log('🔍 Checking corresponding users table record for user_id:', telegramUser.user_id)

      const { data: user, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', telegramUser.user_id)
        .single()

      if (userError) {
        console.error('❌ Error fetching user record:', userError)
      } else {
        console.log('✅ User record found:', user)
      }
    } else {
      console.log('⚠️ No user_id found in telegram_users record')
    }

  } catch (error) {
    console.error('❌ Debug script error:', error)
  }
}

debugTelegramUser()