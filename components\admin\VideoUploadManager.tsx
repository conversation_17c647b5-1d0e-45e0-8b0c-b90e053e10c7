/**
 * VIDEO UPLOAD MANAGER
 * 
 * Comprehensive video upload and management system:
 * - Drag & drop video upload
 * - Progress tracking and compression
 * - Thumbnail generation
 * - Video preview and metadata
 * - Multiple format support (MP4, WebM, MOV)
 * - File size validation and optimization
 */

import React, { useState, useRef, useCallback } from 'react';
import { supabase } from '../../lib/supabase';

interface VideoUploadManagerProps {
  onVideoUploaded: (videoData: VideoData) => void;
  maxFileSize?: number; // in MB
  acceptedFormats?: string[];
  className?: string;
}

interface VideoData {
  url: string;
  thumbnailUrl?: string;
  duration?: number;
  fileSize: number;
  fileName: string;
  mimeType: string;
}

interface UploadProgress {
  fileName: string;
  progress: number;
  status: 'uploading' | 'processing' | 'completed' | 'error';
  error?: string;
}

export const VideoUploadManager: React.FC<VideoUploadManagerProps> = ({
  onVideoUploaded,
  maxFileSize = 500, // 500MB default
  acceptedFormats = ['video/mp4', 'video/webm', 'video/quicktime'],
  className = ''
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [uploads, setUploads] = useState<UploadProgress[]>([]);
  const [uploadedVideos, setUploadedVideos] = useState<VideoData[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoPreviewRef = useRef<HTMLVideoElement>(null);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const validateFile = (file: File): string | null => {
    if (!acceptedFormats.includes(file.type)) {
      return `Invalid file type. Accepted formats: ${acceptedFormats.join(', ')}`;
    }

    if (file.size > maxFileSize * 1024 * 1024) {
      return `File size exceeds ${maxFileSize}MB limit`;
    }

    return null;
  };

  const generateThumbnail = (videoFile: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      video.onloadedmetadata = () => {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        
        video.currentTime = Math.min(5, video.duration / 2); // Thumbnail at 5s or middle
      };

      video.onseeked = () => {
        if (ctx) {
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
          canvas.toBlob((blob) => {
            if (blob) {
              const thumbnailUrl = URL.createObjectURL(blob);
              resolve(thumbnailUrl);
            } else {
              reject(new Error('Failed to generate thumbnail'));
            }
          }, 'image/jpeg', 0.8);
        }
      };

      video.onerror = () => reject(new Error('Failed to load video for thumbnail'));
      video.src = URL.createObjectURL(videoFile);
    });
  };

  const getVideoDuration = (videoFile: File): Promise<number> => {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      video.onloadedmetadata = () => {
        resolve(video.duration);
      };
      video.onerror = () => reject(new Error('Failed to get video duration'));
      video.src = URL.createObjectURL(videoFile);
    });
  };

  const uploadVideo = async (file: File) => {
    const uploadId = Date.now().toString();
    
    // Add to uploads tracking
    setUploads(prev => [...prev, {
      fileName: file.name,
      progress: 0,
      status: 'uploading'
    }]);

    try {
      // Validate file
      const validationError = validateFile(file);
      if (validationError) {
        throw new Error(validationError);
      }

      // Generate unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `video-${Date.now()}.${fileExt}`;

      // Get video metadata
      const duration = await getVideoDuration(file);
      
      // Generate thumbnail
      let thumbnailUrl: string | undefined;
      try {
        const thumbnailBlob = await generateThumbnail(file);
        
        // Upload thumbnail
        const thumbnailFileName = `thumbnail-${Date.now()}.jpg`;
        const { data: thumbnailData, error: thumbnailError } = await supabase.storage
          .from('training-assets')
          .upload(`video-thumbnails/${thumbnailFileName}`, thumbnailBlob);

        if (!thumbnailError) {
          const { data: { publicUrl } } = supabase.storage
            .from('training-assets')
            .getPublicUrl(`video-thumbnails/${thumbnailFileName}`);
          thumbnailUrl = publicUrl;
        }
      } catch (thumbnailError) {
        console.warn('Failed to generate thumbnail:', thumbnailError);
      }

      // Update progress
      setUploads(prev => prev.map(upload => 
        upload.fileName === file.name 
          ? { ...upload, progress: 10, status: 'uploading' }
          : upload
      ));

      // Upload video with progress tracking
      const { data, error } = await supabase.storage
        .from('training-assets')
        .upload(`videos/${fileName}`, file, {
          onUploadProgress: (progress) => {
            const percentage = Math.round((progress.loaded / progress.total) * 90) + 10; // 10-100%
            setUploads(prev => prev.map(upload => 
              upload.fileName === file.name 
                ? { ...upload, progress: percentage }
                : upload
            ));
          }
        });

      if (error) throw error;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('training-assets')
        .getPublicUrl(`videos/${fileName}`);

      // Create video data object
      const videoData: VideoData = {
        url: publicUrl,
        thumbnailUrl,
        duration,
        fileSize: file.size,
        fileName: file.name,
        mimeType: file.type
      };

      // Update uploads status
      setUploads(prev => prev.map(upload => 
        upload.fileName === file.name 
          ? { ...upload, progress: 100, status: 'completed' }
          : upload
      ));

      // Add to uploaded videos
      setUploadedVideos(prev => [...prev, videoData]);
      
      // Notify parent component
      onVideoUploaded(videoData);

      // Remove from uploads after delay
      setTimeout(() => {
        setUploads(prev => prev.filter(upload => upload.fileName !== file.name));
      }, 3000);

    } catch (error) {
      console.error('Video upload error:', error);
      
      setUploads(prev => prev.map(upload => 
        upload.fileName === file.name 
          ? { 
              ...upload, 
              status: 'error', 
              error: error instanceof Error ? error.message : 'Upload failed'
            }
          : upload
      ));
    }
  };

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    files.forEach(file => {
      if (file.type.startsWith('video/')) {
        uploadVideo(file);
      }
    });
  }, []);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    files.forEach(uploadVideo);
    
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removeVideo = (index: number) => {
    setUploadedVideos(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <div
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          isDragging
            ? 'border-blue-500 bg-blue-500/10'
            : 'border-gray-600 hover:border-gray-500'
        }`}
      >
        <div className="space-y-4">
          <div className="text-4xl">🎥</div>
          <div>
            <h3 className="text-lg font-semibold text-white mb-2">
              Upload Training Videos
            </h3>
            <p className="text-gray-400 mb-4">
              Drag and drop video files here, or click to browse
            </p>
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
            >
              Choose Files
            </button>
          </div>
          <div className="text-sm text-gray-500">
            <p>Supported formats: MP4, WebM, MOV</p>
            <p>Maximum file size: {maxFileSize}MB</p>
          </div>
        </div>
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={acceptedFormats.join(',')}
        multiple
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Upload Progress */}
      {uploads.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-white font-medium">Uploading Videos</h4>
          {uploads.map((upload, index) => (
            <div key={index} className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-white text-sm font-medium truncate">
                  {upload.fileName}
                </span>
                <span className="text-gray-400 text-sm">
                  {upload.status === 'uploading' && `${upload.progress}%`}
                  {upload.status === 'processing' && 'Processing...'}
                  {upload.status === 'completed' && '✅ Complete'}
                  {upload.status === 'error' && '❌ Error'}
                </span>
              </div>
              
              {upload.status === 'uploading' && (
                <div className="w-full bg-gray-600 rounded-full h-2">
                  <div
                    className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${upload.progress}%` }}
                  />
                </div>
              )}
              
              {upload.status === 'error' && upload.error && (
                <p className="text-red-400 text-sm mt-2">{upload.error}</p>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Uploaded Videos */}
      {uploadedVideos.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-white font-medium">Uploaded Videos</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {uploadedVideos.map((video, index) => (
              <div key={index} className="bg-gray-700 rounded-lg overflow-hidden">
                {/* Video Preview */}
                <div className="relative aspect-video bg-gray-800">
                  {video.thumbnailUrl ? (
                    <img
                      src={video.thumbnailUrl}
                      alt="Video thumbnail"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <div className="text-4xl">🎥</div>
                    </div>
                  )}
                  
                  {/* Play button overlay */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <button
                      onClick={() => {
                        if (videoPreviewRef.current) {
                          videoPreviewRef.current.src = video.url;
                          videoPreviewRef.current.style.display = 'block';
                          videoPreviewRef.current.play();
                        }
                      }}
                      className="bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full p-3 transition-all"
                    >
                      ▶️
                    </button>
                  </div>
                  
                  {/* Duration badge */}
                  {video.duration && (
                    <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                      {formatDuration(video.duration)}
                    </div>
                  )}
                </div>

                {/* Video Info */}
                <div className="p-3">
                  <h5 className="text-white font-medium text-sm truncate mb-1">
                    {video.fileName}
                  </h5>
                  <div className="flex justify-between items-center text-xs text-gray-400">
                    <span>{formatFileSize(video.fileSize)}</span>
                    <button
                      onClick={() => removeVideo(index)}
                      className="text-red-400 hover:text-red-300"
                    >
                      🗑️
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Hidden video preview */}
      <video
        ref={videoPreviewRef}
        controls
        className="hidden fixed inset-0 z-50 w-full h-full bg-black"
        onClick={(e) => {
          if (e.target === videoPreviewRef.current) {
            videoPreviewRef.current.style.display = 'none';
            videoPreviewRef.current.pause();
          }
        }}
      />
    </div>
  );
};
