#!/usr/bin/env node

/**
 * Cleanup Orphaned Telegram Users
 * 
 * This script safely removes unregistered orphaned telegram_users
 * that are not linked to any user account.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ Missing SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function cleanupOrphanedTelegram() {
  try {
    console.log('🧹 Cleaning Up Orphaned Telegram Users\n');
    
    // Find unregistered orphaned telegram_users (safe to remove)
    const { data: orphanedTelegram, error: orphanError } = await supabase
      .from('telegram_users')
      .select('*')
      .is('user_id', null)
      .eq('is_registered', false)
      .eq('registration_step', 'start');
    
    if (orphanError) {
      console.error('❌ Error finding orphaned telegram users:', orphanError);
      return;
    }
    
    console.log(`📊 Found ${orphanedTelegram.length} unregistered orphaned telegram_users`);
    
    if (orphanedTelegram.length === 0) {
      console.log('✅ No orphaned telegram_users to clean up');
      return;
    }
    
    console.log('\n👻 Orphaned telegram_users to be removed:');
    orphanedTelegram.forEach((tg, index) => {
      console.log(`   ${index + 1}. ${tg.first_name} ${tg.last_name || ''} (@${tg.username || 'no_username'})`);
      console.log(`      Telegram ID: ${tg.telegram_id}`);
      console.log(`      Registered: ${tg.is_registered}, Step: ${tg.registration_step}`);
      console.log('');
    });
    
    // Ask for confirmation (in a real scenario, you'd want user input)
    console.log('⚠️  These users started registration but never completed it.');
    console.log('⚠️  They have no user_id link and are safe to remove.');
    console.log('⚠️  This will clean up the database inconsistency.\n');
    
    // Get counts before cleanup
    const { count: beforeCount } = await supabase
      .from('telegram_users')
      .select('*', { count: 'exact', head: true });
    
    console.log(`📊 Before cleanup: ${beforeCount} telegram_users`);
    
    // Remove orphaned telegram_users
    const telegramIds = orphanedTelegram.map(tg => tg.telegram_id);
    
    const { error: deleteError } = await supabase
      .from('telegram_users')
      .delete()
      .in('telegram_id', telegramIds);
    
    if (deleteError) {
      console.error('❌ Error deleting orphaned telegram users:', deleteError);
      return;
    }
    
    // Get counts after cleanup
    const { count: afterCount } = await supabase
      .from('telegram_users')
      .select('*', { count: 'exact', head: true });
    
    console.log(`✅ Successfully removed ${orphanedTelegram.length} orphaned telegram_users`);
    console.log(`📊 After cleanup: ${afterCount} telegram_users`);
    console.log(`📊 Removed: ${beforeCount - afterCount} records`);
    
    // Check remaining inconsistencies
    const { count: usersCount } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true });
    
    console.log(`\n📊 Final counts:`);
    console.log(`   Users: ${usersCount}`);
    console.log(`   Telegram Users: ${afterCount}`);
    console.log(`   Difference: ${usersCount - afterCount} (users has more)`);
    
    if (usersCount - afterCount > 0) {
      console.log('\n💡 Remaining difference is likely due to:');
      console.log('   • Web-only registrations (users without Telegram)');
      console.log('   • Test accounts created directly in database');
      console.log('   • Admin accounts');
      console.log('   This is normal and expected.');
    }
    
    // Check for any remaining orphaned telegram_users
    const { data: remainingOrphans, error: remainingError } = await supabase
      .from('telegram_users')
      .select('*')
      .is('user_id', null);
    
    if (remainingError) {
      console.error('❌ Error checking remaining orphans:', remainingError);
    } else if (remainingOrphans.length > 0) {
      console.log(`\n⚠️  ${remainingOrphans.length} telegram_users still have no user_id link:`);
      remainingOrphans.forEach((tg, index) => {
        console.log(`   ${index + 1}. ${tg.first_name} ${tg.last_name || ''} (@${tg.username || 'no_username'})`);
        console.log(`      Registered: ${tg.is_registered}, Step: ${tg.registration_step}`);
      });
      console.log('   These may need manual investigation.');
    } else {
      console.log('\n✅ No remaining orphaned telegram_users');
    }
    
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
  }
}

cleanupOrphanedTelegram();
