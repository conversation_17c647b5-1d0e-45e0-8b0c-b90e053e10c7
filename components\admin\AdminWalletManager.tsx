import React, { useState, useEffect } from 'react'
import { supabase } from '../../lib/supabase'
import { logAdminAction } from '../../lib/adminAuth'

interface PaymentNetwork {
  id: string
  name: string
  technical: string
  icon: string
  wallet_address: string
  is_active: boolean
  created_at: string
  updated_at: string
}

interface WalletTransaction {
  id: string
  user_id: number
  amount: number
  network: string
  currency: string
  sender_wallet: string
  receiver_wallet: string
  transaction_hash: string
  status: string
  created_at: string
  users?: {
    username: string
    email: string
    full_name?: string
  }
}

export const AdminWalletManager: React.FC = () => {
  const [networks, setNetworks] = useState<PaymentNetwork[]>([])
  const [transactions, setTransactions] = useState<WalletTransaction[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'networks' | 'transactions'>('networks')
  const [editingNetwork, setEditingNetwork] = useState<PaymentNetwork | null>(null)
  const [showAddForm, setShowAddForm] = useState(false)

  const [newNetwork, setNewNetwork] = useState({
    id: '',
    name: '',
    technical: '',
    icon: '',
    wallet_address: '',
    is_active: true
  })

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    setLoading(true)
    try {
      await Promise.all([loadNetworks(), loadTransactions()])
    } catch (error) {
      console.error('Error loading wallet data:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadNetworks = async () => {
    try {
      const { data, error } = await supabase
        .from('payment_networks')
        .select('*')
        .order('name')

      if (error) throw error
      setNetworks(data || [])
    } catch (error) {
      console.error('Error loading networks:', error)
    }
  }

  const loadTransactions = async () => {
    try {
      const { data, error } = await supabase
        .from('crypto_payment_transactions')
        .select(`
          *,
          users (
            username,
            email,
            full_name
          )
        `)
        .order('created_at', { ascending: false })
        .limit(100)

      if (error) throw error
      setTransactions(data || [])
    } catch (error) {
      console.error('Error loading transactions:', error)
    }
  }

  const saveNetwork = async () => {
    try {
      if (editingNetwork) {
        // Update existing network
        const { error } = await supabase
          .from('payment_networks')
          .update({
            name: newNetwork.name,
            technical: newNetwork.technical,
            icon: newNetwork.icon,
            wallet_address: newNetwork.wallet_address,
            is_active: newNetwork.is_active,
            updated_at: new Date().toISOString()
          })
          .eq('id', editingNetwork.id)

        if (error) throw error

        await logAdminAction('update_payment_network', {
          network_id: editingNetwork.id,
          changes: newNetwork
        })

        alert('Network updated successfully!')
      } else {
        // Create new network
        const { error } = await supabase
          .from('payment_networks')
          .insert([{
            id: newNetwork.id,
            name: newNetwork.name,
            technical: newNetwork.technical,
            icon: newNetwork.icon,
            wallet_address: newNetwork.wallet_address,
            is_active: newNetwork.is_active
          }])

        if (error) throw error

        await logAdminAction('create_payment_network', {
          network_data: newNetwork
        })

        alert('Network created successfully!')
      }

      resetForm()
      loadNetworks()
    } catch (error) {
      console.error('Error saving network:', error)
      alert('Error saving network: ' + (error as Error).message)
    }
  }

  const deleteNetwork = async (networkId: string) => {
    if (!confirm('Are you sure you want to delete this network? This action cannot be undone.')) {
      return
    }

    try {
      const { error } = await supabase
        .from('payment_networks')
        .delete()
        .eq('id', networkId)

      if (error) throw error

      await logAdminAction('delete_payment_network', {
        network_id: networkId
      })

      alert('Network deleted successfully!')
      loadNetworks()
    } catch (error) {
      console.error('Error deleting network:', error)
      alert('Error deleting network: ' + (error as Error).message)
    }
  }

  const resetForm = () => {
    setNewNetwork({
      id: '',
      name: '',
      technical: '',
      icon: '',
      wallet_address: '',
      is_active: true
    })
    setEditingNetwork(null)
    setShowAddForm(false)
  }

  const startEdit = (network: PaymentNetwork) => {
    setEditingNetwork(network)
    setNewNetwork({
      id: network.id,
      name: network.name,
      technical: network.technical,
      icon: network.icon,
      wallet_address: network.wallet_address,
      is_active: network.is_active
    })
    setShowAddForm(true)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    // Could add toast notification here
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-white">Loading wallet data...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">Wallet Management</h2>
        <div className="flex space-x-4">
          <button
            onClick={() => setActiveTab('networks')}
            className={`px-4 py-2 rounded-lg ${
              activeTab === 'networks'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            Payment Networks
          </button>
          <button
            onClick={() => setActiveTab('transactions')}
            className={`px-4 py-2 rounded-lg ${
              activeTab === 'transactions'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            Wallet Transactions
          </button>
        </div>
      </div>

      {activeTab === 'networks' && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-semibold text-white">Payment Networks</h3>
            <button
              onClick={() => setShowAddForm(true)}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg"
            >
              Add Network
            </button>
          </div>

          {showAddForm && (
            <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
              <h4 className="text-lg font-semibold text-white mb-4">
                {editingNetwork ? 'Edit Network' : 'Add New Network'}
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-gray-300 mb-2">Network ID</label>
                  <input
                    type="text"
                    value={newNetwork.id}
                    onChange={(e) => setNewNetwork({ ...newNetwork, id: e.target.value })}
                    className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white"
                    placeholder="e.g., BSC, POL, TRON"
                    disabled={!!editingNetwork}
                  />
                </div>
                <div>
                  <label className="block text-gray-300 mb-2">Network Name</label>
                  <input
                    type="text"
                    value={newNetwork.name}
                    onChange={(e) => setNewNetwork({ ...newNetwork, name: e.target.value })}
                    className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white"
                    placeholder="e.g., Binance Smart Chain"
                  />
                </div>
                <div>
                  <label className="block text-gray-300 mb-2">Technical Name</label>
                  <input
                    type="text"
                    value={newNetwork.technical}
                    onChange={(e) => setNewNetwork({ ...newNetwork, technical: e.target.value })}
                    className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white"
                    placeholder="e.g., BEP-20"
                  />
                </div>
                <div>
                  <label className="block text-gray-300 mb-2">Icon</label>
                  <input
                    type="text"
                    value={newNetwork.icon}
                    onChange={(e) => setNewNetwork({ ...newNetwork, icon: e.target.value })}
                    className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white"
                    placeholder="Icon URL or emoji"
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-gray-300 mb-2">Wallet Address</label>
                  <input
                    type="text"
                    value={newNetwork.wallet_address}
                    onChange={(e) => setNewNetwork({ ...newNetwork, wallet_address: e.target.value })}
                    className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white font-mono"
                    placeholder="Company wallet address for this network"
                  />
                </div>
                <div>
                  <label className="flex items-center text-gray-300">
                    <input
                      type="checkbox"
                      checked={newNetwork.is_active}
                      onChange={(e) => setNewNetwork({ ...newNetwork, is_active: e.target.checked })}
                      className="mr-2"
                    />
                    Active
                  </label>
                </div>
              </div>
              <div className="flex space-x-4 mt-6">
                <button
                  onClick={saveNetwork}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg"
                >
                  {editingNetwork ? 'Update' : 'Create'} Network
                </button>
                <button
                  onClick={resetForm}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg"
                >
                  Cancel
                </button>
              </div>
            </div>
          )}

          <div className="bg-gray-800 rounded-lg border border-gray-600 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Network
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Technical
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Wallet Address
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-600">
                  {networks.map((network) => (
                    <tr key={network.id} className="hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <span className="mr-2">{network.icon}</span>
                          <div>
                            <div className="text-sm font-medium text-white">{network.name}</div>
                            <div className="text-sm text-gray-400">{network.id}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {network.technical}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <span className="text-sm text-gray-300 font-mono mr-2">
                            {network.wallet_address.substring(0, 10)}...{network.wallet_address.substring(-8)}
                          </span>
                          <button
                            onClick={() => copyToClipboard(network.wallet_address)}
                            className="text-blue-400 hover:text-blue-300 text-xs"
                            title="Copy full address"
                          >
                            📋
                          </button>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          network.is_active
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {network.is_active ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => startEdit(network)}
                            className="text-blue-400 hover:text-blue-300"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => deleteNetwork(network.id)}
                            className="text-red-400 hover:text-red-300"
                          >
                            Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'transactions' && (
        <div className="space-y-6">
          <h3 className="text-xl font-semibold text-white">Recent Wallet Transactions</h3>

          <div className="bg-gray-800 rounded-lg border border-gray-600 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      User
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Network
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Sender Wallet
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Transaction Hash
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Date
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-600">
                  {transactions.map((transaction) => (
                    <tr key={transaction.id} className="hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-white">
                            {transaction.users?.username || 'Unknown'}
                          </div>
                          <div className="text-sm text-gray-400">
                            {transaction.users?.email}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        ${transaction.amount.toFixed(2)} {transaction.currency}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {transaction.network}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <span className="text-sm text-gray-300 font-mono mr-2">
                            {transaction.sender_wallet.substring(0, 8)}...{transaction.sender_wallet.substring(-6)}
                          </span>
                          <button
                            onClick={() => copyToClipboard(transaction.sender_wallet)}
                            className="text-blue-400 hover:text-blue-300 text-xs"
                            title="Copy full address"
                          >
                            📋
                          </button>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <span className="text-sm text-gray-300 font-mono mr-2">
                            {transaction.transaction_hash.substring(0, 8)}...{transaction.transaction_hash.substring(-6)}
                          </span>
                          <button
                            onClick={() => copyToClipboard(transaction.transaction_hash)}
                            className="text-blue-400 hover:text-blue-300 text-xs"
                            title="Copy full hash"
                          >
                            📋
                          </button>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          transaction.status === 'approved'
                            ? 'bg-green-100 text-green-800'
                            : transaction.status === 'rejected'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {transaction.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {new Date(transaction.created_at).toLocaleDateString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
