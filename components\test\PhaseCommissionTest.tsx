import React, { useState, useEffect } from 'react'
import { supabase } from '../../lib/supabase'

interface TestResult {
  id: string
  test_name: string
  status: 'passed' | 'failed' | 'running'
  result: any
  error?: string
  duration: number
  timestamp: string
}

interface PhaseData {
  id: number
  phase_number: number
  phase_name: string
  price_per_share: number
  is_active: boolean
}

interface CommissionData {
  id: string
  referrer_id: number
  referred_id: number
  usdt_commission: number
  share_commission: number
  status: string
}

export const PhaseCommissionTest: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [loading, setLoading] = useState(false)
  const [phases, setPhases] = useState<PhaseData[]>([])
  const [commissions, setCommissions] = useState<CommissionData[]>([])

  useEffect(() => {
    loadInitialData()
  }, [])

  const loadInitialData = async () => {
    try {
      // Load phases
      const { data: phasesData, error: phasesError } = await supabase
        .from('investment_phases')
        .select('*')
        .order('phase_number')

      if (phasesError) throw phasesError
      setPhases(phasesData || [])

      // Load recent commissions
      const { data: commissionsData, error: commissionsError } = await supabase
        .from('commission_transactions')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(10)

      if (commissionsError) throw commissionsError
      setCommissions(commissionsData || [])

    } catch (error) {
      console.error('Error loading initial data:', error)
    }
  }

  const runTest = async (testName: string, testFunction: () => Promise<any>) => {
    const startTime = Date.now()
    const testId = `${testName}-${Date.now()}`

    // Add running test
    const runningTest: TestResult = {
      id: testId,
      test_name: testName,
      status: 'running',
      result: null,
      duration: 0,
      timestamp: new Date().toISOString()
    }

    setTestResults(prev => [runningTest, ...prev])

    try {
      const result = await testFunction()
      const duration = Date.now() - startTime

      const completedTest: TestResult = {
        id: testId,
        test_name: testName,
        status: 'passed',
        result,
        duration,
        timestamp: new Date().toISOString()
      }

      setTestResults(prev => prev.map(test => 
        test.id === testId ? completedTest : test
      ))

    } catch (error) {
      const duration = Date.now() - startTime

      const failedTest: TestResult = {
        id: testId,
        test_name: testName,
        status: 'failed',
        result: null,
        error: (error as Error).message,
        duration,
        timestamp: new Date().toISOString()
      }

      setTestResults(prev => prev.map(test => 
        test.id === testId ? failedTest : test
      ))
    }
  }

  const testPhaseRetrieval = async () => {
    const { data, error } = await supabase
      .from('investment_phases')
      .select('*')
      .order('phase_number')

    if (error) throw error

    return {
      message: 'Successfully retrieved phases',
      count: data?.length || 0,
      phases: data?.map(p => ({ id: p.id, name: p.phase_name, active: p.is_active }))
    }
  }

  const testCommissionCalculation = async () => {
    // Test commission calculation logic
    const testAmount = 1000
    const commissionRate = 15
    const expectedUsdtCommission = testAmount * (commissionRate / 100)
    const expectedShareCommission = 100 * (commissionRate / 100) // Assuming 100 shares

    return {
      message: 'Commission calculation test',
      testAmount,
      commissionRate,
      expectedUsdtCommission,
      expectedShareCommission,
      calculationCorrect: expectedUsdtCommission === 150 && expectedShareCommission === 15
    }
  }

  const testReferralChain = async () => {
    // Test referral chain retrieval
    const { data: referrals, error } = await supabase
      .from('referrals')
      .select(`
        *,
        referrer:referrer_id (username, email),
        referred:referred_id (username, email)
      `)
      .eq('status', 'active')
      .limit(5)

    if (error) throw error

    return {
      message: 'Referral chain test',
      activeReferrals: referrals?.length || 0,
      sampleReferrals: referrals?.map(r => ({
        referrer: r.referrer?.username,
        referred: r.referred?.username
      }))
    }
  }

  const testCommissionPayment = async () => {
    // Test commission payment logic (simulation)
    const testCommission = {
      referrer_id: 1,
      referred_id: 2,
      usdt_commission: 150,
      share_commission: 15,
      status: 'pending'
    }

    // Simulate commission creation (don't actually create)
    return {
      message: 'Commission payment simulation',
      testCommission,
      validationPassed: testCommission.usdt_commission > 0 && testCommission.share_commission > 0
    }
  }

  const testPhaseTransition = async () => {
    // Test phase transition logic
    const currentPhase = phases.find(p => p.is_active)
    
    if (!currentPhase) {
      throw new Error('No active phase found')
    }

    // Get shares sold for current phase
    const { data: purchases, error } = await supabase
      .from('aureus_share_purchases')
      .select('shares_purchased')
      .eq('phase_id', currentPhase.id)
      .eq('status', 'active')

    if (error) throw error

    const totalSharesSold = purchases?.reduce((sum, p) => sum + p.shares_purchased, 0) || 0

    return {
      message: 'Phase transition test',
      currentPhase: {
        id: currentPhase.id,
        name: currentPhase.phase_name,
        price: currentPhase.price_per_share
      },
      totalSharesSold,
      testPassed: true
    }
  }

  const runAllTests = async () => {
    setLoading(true)
    setTestResults([])

    const tests = [
      { name: 'Phase Retrieval', func: testPhaseRetrieval },
      { name: 'Commission Calculation', func: testCommissionCalculation },
      { name: 'Referral Chain', func: testReferralChain },
      { name: 'Commission Payment', func: testCommissionPayment },
      { name: 'Phase Transition', func: testPhaseTransition }
    ]

    for (const test of tests) {
      await runTest(test.name, test.func)
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    setLoading(false)
  }

  const clearResults = () => {
    setTestResults([])
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">Phase & Commission Testing</h2>
        <div className="flex space-x-4">
          <button
            onClick={runAllTests}
            disabled={loading}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg"
          >
            {loading ? 'Running Tests...' : 'Run All Tests'}
          </button>
          <button
            onClick={clearResults}
            className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg"
          >
            Clear Results
          </button>
        </div>
      </div>

      {/* Current System State */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
          <h3 className="text-lg font-semibold text-white mb-4">Current Phases</h3>
          <div className="space-y-2">
            {phases.map(phase => (
              <div key={phase.id} className="flex justify-between items-center">
                <span className="text-gray-300">
                  Phase {phase.phase_number}: {phase.phase_name}
                </span>
                <span className={`px-2 py-1 text-xs rounded ${
                  phase.is_active ? 'bg-green-600 text-white' : 'bg-gray-600 text-gray-300'
                }`}>
                  {phase.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
          <h3 className="text-lg font-semibold text-white mb-4">Recent Commissions</h3>
          <div className="space-y-2">
            {commissions.slice(0, 5).map(commission => (
              <div key={commission.id} className="flex justify-between items-center text-sm">
                <span className="text-gray-300">
                  ${commission.usdt_commission.toFixed(2)} USDT
                </span>
                <span className={`px-2 py-1 text-xs rounded ${
                  commission.status === 'approved' ? 'bg-green-600 text-white' : 
                  commission.status === 'rejected' ? 'bg-red-600 text-white' :
                  'bg-yellow-600 text-white'
                }`}>
                  {commission.status}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Individual Test Buttons */}
      <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
        <h3 className="text-lg font-semibold text-white mb-4">Individual Tests</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          <button
            onClick={() => runTest('Phase Retrieval', testPhaseRetrieval)}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg"
          >
            Test Phases
          </button>
          <button
            onClick={() => runTest('Commission Calculation', testCommissionCalculation)}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg"
          >
            Test Commissions
          </button>
          <button
            onClick={() => runTest('Referral Chain', testReferralChain)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
          >
            Test Referrals
          </button>
          <button
            onClick={() => runTest('Commission Payment', testCommissionPayment)}
            className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg"
          >
            Test Payments
          </button>
          <button
            onClick={() => runTest('Phase Transition', testPhaseTransition)}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg"
          >
            Test Transitions
          </button>
        </div>
      </div>

      {/* Test Results */}
      {testResults.length > 0 && (
        <div className="bg-gray-800 rounded-lg border border-gray-600 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-600">
            <h3 className="text-xl font-semibold text-white">Test Results</h3>
          </div>
          <div className="divide-y divide-gray-600">
            {testResults.map((result) => (
              <div key={result.id} className="p-6">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="text-lg font-medium text-white">{result.test_name}</h4>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                      result.status === 'passed' ? 'bg-green-100 text-green-800' :
                      result.status === 'failed' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {result.status}
                    </span>
                    <span className="text-gray-400 text-sm">{result.duration}ms</span>
                  </div>
                </div>
                
                {result.error && (
                  <div className="bg-red-900 border border-red-600 rounded p-3 mb-3">
                    <p className="text-red-200 text-sm">{result.error}</p>
                  </div>
                )}
                
                {result.result && (
                  <div className="bg-gray-700 rounded p-3">
                    <pre className="text-gray-300 text-sm overflow-x-auto">
                      {JSON.stringify(result.result, null, 2)}
                    </pre>
                  </div>
                )}
                
                <div className="text-gray-400 text-xs mt-2">
                  {new Date(result.timestamp).toLocaleString()}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
