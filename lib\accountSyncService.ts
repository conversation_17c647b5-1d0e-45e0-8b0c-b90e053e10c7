import { supabase } from './supabase';

export interface AccountMatch {
  match_type: string;
  matched_user_id: number;
  confidence_score: number;
  match_details: any;
}

export interface SyncNotification {
  id: string;
  user_id: number;
  notification_type: string;
  title: string;
  message: string;
  action_url?: string;
  action_text?: string;
  is_read: boolean;
  priority: string;
  metadata?: any;
  created_at: string;
}

export interface AccountLink {
  id: string;
  web_user_id: string;
  database_user_id: number;
  telegram_id?: number;
  link_type: string;
  link_status: string;
  data_merged: boolean;
  merge_summary?: any;
  created_at: string;
}

/**
 * Account Synchronization Service
 * Handles detection, linking, and merging of accounts across platforms
 */
export class AccountSyncService {
  
  /**
   * Detect potential account matches for a user
   */
  static async detectAccountMatches(
    userId: number, 
    email?: string, 
    telegramId?: number
  ): Promise<AccountMatch[]> {
    try {
      const { data, error } = await supabase.rpc('detect_account_matches', {
        _user_id: userId,
        _email: email,
        _telegram_id: telegramId
      });

      if (error) {
        console.error('Error detecting account matches:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Account match detection failed:', error);
      return [];
    }
  }

  /**
   * Create a sync notification for a user
   */
  static async createSyncNotification(
    userId: number,
    type: string,
    title: string,
    message: string,
    actionUrl?: string,
    actionText?: string,
    metadata?: any
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('sync_notifications')
        .insert({
          user_id: userId,
          notification_type: type,
          title,
          message,
          action_url: actionUrl,
          action_text: actionText,
          metadata,
          priority: type === 'account_detected' ? 'high' : 'normal'
        });

      if (error) {
        console.error('Error creating sync notification:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Sync notification creation failed:', error);
      return false;
    }
  }

  /**
   * Get unread sync notifications for a user
   */
  static async getSyncNotifications(userId: number): Promise<SyncNotification[]> {
    try {
      const { data, error } = await supabase
        .from('sync_notifications')
        .select('*')
        .eq('user_id', userId)
        .eq('is_read', false)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching sync notifications:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Sync notifications fetch failed:', error);
      return [];
    }
  }

  /**
   * Mark notification as read
   */
  static async markNotificationRead(notificationId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('sync_notifications')
        .update({ 
          is_read: true, 
          read_at: new Date().toISOString() 
        })
        .eq('id', notificationId);

      return !error;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      return false;
    }
  }

  /**
   * Create account link between web and telegram users
   */
  static async createAccountLink(
    webUserId: string,
    databaseUserId: number,
    telegramId?: number,
    linkType: string = 'web_to_telegram'
  ): Promise<string | null> {
    try {
      const { data, error } = await supabase
        .from('account_links')
        .insert({
          web_user_id: webUserId,
          database_user_id: databaseUserId,
          telegram_id: telegramId,
          link_type: linkType,
          link_status: 'pending'
        })
        .select('id')
        .single();

      if (error) {
        console.error('Error creating account link:', error);
        return null;
      }

      return data.id;
    } catch (error) {
      console.error('Account link creation failed:', error);
      return null;
    }
  }

  /**
   * Confirm and merge account link
   */
  static async confirmAccountLink(linkId: string, performedBy: number): Promise<any> {
    try {
      // Get link details
      const { data: link, error: linkError } = await supabase
        .from('account_links')
        .select('*')
        .eq('id', linkId)
        .single();

      if (linkError || !link) {
        throw new Error('Account link not found');
      }

      // Perform the merge using stored procedure
      const { data: mergeResult, error: mergeError } = await supabase.rpc('merge_user_accounts', {
        _source_user_id: link.database_user_id,
        _target_user_id: link.database_user_id, // In this case, we're confirming the link
        _performed_by: performedBy
      });

      if (mergeError) {
        throw mergeError;
      }

      // Update link status
      await supabase
        .from('account_links')
        .update({
          link_status: 'confirmed',
          confirmed_at: new Date().toISOString(),
          data_merged: true
        })
        .eq('id', linkId);

      return mergeResult;
    } catch (error) {
      console.error('Account link confirmation failed:', error);
      throw error;
    }
  }

  /**
   * Check if user has any account links
   */
  static async getUserAccountLinks(userId: number): Promise<AccountLink[]> {
    try {
      const { data, error } = await supabase
        .from('account_links')
        .select('*')
        .eq('database_user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching account links:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Account links fetch failed:', error);
      return [];
    }
  }

  /**
   * Auto-detect and notify about potential account matches
   */
  static async autoDetectAndNotify(userId: number): Promise<void> {
    try {
      // Get user details
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (userError || !user) {
        return;
      }

      // Detect matches
      const matches = await this.detectAccountMatches(
        userId, 
        user.email, 
        user.telegram_id
      );

      // Create notifications for high-confidence matches
      for (const match of matches) {
        if (match.confidence_score >= 80) {
          await this.createSyncNotification(
            userId,
            'account_detected',
            'Potential Account Match Found',
            `We found another account that might be yours. Would you like to sync your data?`,
            `/dashboard/sync?match=${match.matched_user_id}`,
            'Review Match',
            {
              match_type: match.match_type,
              confidence: match.confidence_score,
              details: match.match_details
            }
          );
        }
      }
    } catch (error) {
      console.error('Auto-detection failed:', error);
    }
  }

  /**
   * Get user's purchase summary for preview before merge
   */
  static async getUserDataSummary(userId: number): Promise<any> {
    try {
      // Get purchases
      const { data: purchases } = await supabase
        .from('aureus_investments')
        .select('*')
        .eq('user_id', userId);

      // Get referrals
      const { data: referrals } = await supabase
        .from('referrals')
        .select('*')
        .eq('referrer_id', userId);

      // Get commission balance
      const { data: commissions } = await supabase
        .from('commission_balances')
        .select('*')
        .eq('user_id', userId);

      return {
        purchases: purchases?.length || 0,
        total_invested: purchases?.reduce((sum, p) => sum + (p.amount || 0), 0) || 0,
        referrals: referrals?.length || 0,
        commission_balance: commissions?.[0]?.balance || 0
      };
    } catch (error) {
      console.error('Error getting user data summary:', error);
      return {
        purchases: 0,
        total_invested: 0,
        referrals: 0,
        commission_balance: 0
      };
    }
  }
}
