import { useState, useEffect } from 'react';
import { userPreferencesService, UserSettings, NotificationPreferences } from '../services/userPreferencesService';

interface UseUserSettingsOptions {
  userId?: number;
  autoLoad?: boolean;
}

interface UseUserSettingsReturn {
  // State
  settings: UserSettings | null;
  notifications: NotificationPreferences | null;
  loading: boolean;
  saving: boolean;
  error: string | null;
  
  // Actions
  updateSetting: (key: keyof UserSettings, value: any) => Promise<boolean>;
  updateSettings: (settings: Partial<UserSettings>) => Promise<boolean>;
  updateNotificationSetting: (key: keyof NotificationPreferences, value: any) => Promise<boolean>;
  updateNotificationSettings: (notifications: Partial<NotificationPreferences>) => Promise<boolean>;
  resetToDefaults: () => Promise<boolean>;
  exportSettings: () => Promise<{ settings: UserSettings; notifications: NotificationPreferences | null } | null>;
  importSettings: (data: { settings: Partial<UserSettings>; notifications?: Partial<NotificationPreferences> }) => Promise<boolean>;
  refreshSettings: () => Promise<void>;
  
  // Computed
  isDarkTheme: boolean;
  isCompactView: boolean;
  shouldAutoRefresh: boolean;
  refreshInterval: number;
}

export const useUserSettings = (options: UseUserSettingsOptions = {}): UseUserSettingsReturn => {
  const {
    userId,
    autoLoad = true
  } = options;

  // State
  const [settings, setSettings] = useState<UserSettings | null>(null);
  const [notifications, setNotifications] = useState<NotificationPreferences | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load settings on mount
  useEffect(() => {
    if (autoLoad && userId) {
      refreshSettings();
    }
  }, [userId, autoLoad]);

  /**
   * Load user settings and notifications
   */
  const refreshSettings = async () => {
    if (!userId) return;

    setLoading(true);
    setError(null);

    try {
      console.log(`🔄 Loading settings for user ${userId}`);

      const [userSettings, notificationSettings] = await Promise.all([
        userPreferencesService.getUserPreferences(userId),
        userPreferencesService.getNotificationPreferences(userId)
      ]);

      setSettings(userSettings);
      setNotifications(notificationSettings);

      console.log('✅ Settings loaded successfully');

    } catch (err: any) {
      console.error('❌ Failed to load settings:', err);
      setError(err.message || 'Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Update a single setting
   */
  const updateSetting = async (key: keyof UserSettings, value: any): Promise<boolean> => {
    if (!userId) {
      setError('User ID is required');
      return false;
    }

    setSaving(true);
    setError(null);

    try {
      console.log(`📝 Updating setting ${key} to:`, value);

      const success = await userPreferencesService.updatePreference(userId, key, value);
      
      if (success && settings) {
        // Update local state
        setSettings(prev => prev ? { ...prev, [key]: value } : null);
        console.log('✅ Setting updated successfully');
      } else {
        setError('Failed to update setting');
      }

      return success;

    } catch (err: any) {
      console.error('❌ Failed to update setting:', err);
      setError(err.message || 'Failed to update setting');
      return false;
    } finally {
      setSaving(false);
    }
  };

  /**
   * Update multiple settings
   */
  const updateSettings = async (newSettings: Partial<UserSettings>): Promise<boolean> => {
    if (!userId) {
      setError('User ID is required');
      return false;
    }

    setSaving(true);
    setError(null);

    try {
      console.log('📝 Updating multiple settings:', newSettings);

      const success = await userPreferencesService.updatePreferences(userId, newSettings);
      
      if (success && settings) {
        // Update local state
        setSettings(prev => prev ? { ...prev, ...newSettings } : null);
        console.log('✅ Settings updated successfully');
      } else {
        setError('Failed to update settings');
      }

      return success;

    } catch (err: any) {
      console.error('❌ Failed to update settings:', err);
      setError(err.message || 'Failed to update settings');
      return false;
    } finally {
      setSaving(false);
    }
  };

  /**
   * Update a single notification setting
   */
  const updateNotificationSetting = async (key: keyof NotificationPreferences, value: any): Promise<boolean> => {
    if (!userId) {
      setError('User ID is required');
      return false;
    }

    setSaving(true);
    setError(null);

    try {
      console.log(`🔔 Updating notification setting ${key} to:`, value);

      const success = await userPreferencesService.updateNotificationPreferences(userId, { [key]: value });
      
      if (success && notifications) {
        // Update local state
        setNotifications(prev => prev ? { ...prev, [key]: value } : null);
        console.log('✅ Notification setting updated successfully');
      } else {
        setError('Failed to update notification setting');
      }

      return success;

    } catch (err: any) {
      console.error('❌ Failed to update notification setting:', err);
      setError(err.message || 'Failed to update notification setting');
      return false;
    } finally {
      setSaving(false);
    }
  };

  /**
   * Update multiple notification settings
   */
  const updateNotificationSettings = async (newNotifications: Partial<NotificationPreferences>): Promise<boolean> => {
    if (!userId) {
      setError('User ID is required');
      return false;
    }

    setSaving(true);
    setError(null);

    try {
      console.log('🔔 Updating multiple notification settings:', newNotifications);

      const success = await userPreferencesService.updateNotificationPreferences(userId, newNotifications);
      
      if (success && notifications) {
        // Update local state
        setNotifications(prev => prev ? { ...prev, ...newNotifications } : null);
        console.log('✅ Notification settings updated successfully');
      } else {
        setError('Failed to update notification settings');
      }

      return success;

    } catch (err: any) {
      console.error('❌ Failed to update notification settings:', err);
      setError(err.message || 'Failed to update notification settings');
      return false;
    } finally {
      setSaving(false);
    }
  };

  /**
   * Reset all settings to defaults
   */
  const resetToDefaults = async (): Promise<boolean> => {
    if (!userId) {
      setError('User ID is required');
      return false;
    }

    setSaving(true);
    setError(null);

    try {
      console.log('🔄 Resetting settings to defaults');

      const success = await userPreferencesService.resetPreferences(userId);
      
      if (success) {
        // Reload settings
        await refreshSettings();
        console.log('✅ Settings reset to defaults successfully');
      } else {
        setError('Failed to reset settings');
      }

      return success;

    } catch (err: any) {
      console.error('❌ Failed to reset settings:', err);
      setError(err.message || 'Failed to reset settings');
      return false;
    } finally {
      setSaving(false);
    }
  };

  /**
   * Export settings
   */
  const exportSettings = async () => {
    if (!userId) {
      setError('User ID is required');
      return null;
    }

    try {
      console.log('📤 Exporting settings');
      return await userPreferencesService.exportPreferences(userId);
    } catch (err: any) {
      console.error('❌ Failed to export settings:', err);
      setError(err.message || 'Failed to export settings');
      return null;
    }
  };

  /**
   * Import settings
   */
  const importSettings = async (data: { settings: Partial<UserSettings>; notifications?: Partial<NotificationPreferences> }): Promise<boolean> => {
    if (!userId) {
      setError('User ID is required');
      return false;
    }

    setSaving(true);
    setError(null);

    try {
      console.log('📥 Importing settings');

      const success = await userPreferencesService.importPreferences(userId, data);
      
      if (success) {
        // Reload settings
        await refreshSettings();
        console.log('✅ Settings imported successfully');
      } else {
        setError('Failed to import settings');
      }

      return success;

    } catch (err: any) {
      console.error('❌ Failed to import settings:', err);
      setError(err.message || 'Failed to import settings');
      return false;
    } finally {
      setSaving(false);
    }
  };

  // Computed values
  const isDarkTheme = settings?.theme === 'dark' || (settings?.theme === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);
  const isCompactView = settings?.compact_view || false;
  const shouldAutoRefresh = settings?.auto_refresh || false;
  const refreshInterval = settings?.refresh_interval || 30;

  return {
    // State
    settings,
    notifications,
    loading,
    saving,
    error,
    
    // Actions
    updateSetting,
    updateSettings,
    updateNotificationSetting,
    updateNotificationSettings,
    resetToDefaults,
    exportSettings,
    importSettings,
    refreshSettings,
    
    // Computed
    isDarkTheme,
    isCompactView,
    shouldAutoRefresh,
    refreshInterval
  };
};
