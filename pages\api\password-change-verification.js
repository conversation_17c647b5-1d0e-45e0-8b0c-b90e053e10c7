/**
 * PASSWORD CHANGE VERIFICATION API
 * 
 * <PERSON>les secure password change with email PIN verification:
 * - Send verification PIN to user's email
 * - Verify PIN and update password
 * - Uses Resend API for email delivery
 */

import { createClient } from '@supabase/supabase-js';
import { Resend } from 'resend';
import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client with service role
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Initialize Resend client
const RESEND_API_KEY = process.env.RESEND_API_KEY || process.env.VITE_RESEND_API_KEY;
const RESEND_FROM_EMAIL = process.env.RESEND_FROM_EMAIL || process.env.VITE_RESEND_FROM_EMAIL || '<EMAIL>';
const RESEND_FROM_NAME = process.env.RESEND_FROM_NAME || process.env.VITE_RESEND_FROM_NAME || 'Aureus Alliance';
const resend = RESEND_API_KEY ? new Resend(RESEND_API_KEY) : null;

// Configuration
const VERIFICATION_CODE_LENGTH = 6;
const VERIFICATION_EXPIRY_MINUTES = 15;
const MAX_VERIFICATION_ATTEMPTS = 3;

export default async function handler(req, res) {
  try {
    switch (req.method) {
      case 'POST':
        // Send verification PIN
        return await handleSendPIN(req, res);
      case 'PUT':
        // Verify PIN and change password
        return await handleVerifyPINAndChangePassword(req, res);
      default:
        return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('Password change verification API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Send verification PIN to user's email
 */
async function handleSendPIN(req, res) {
  try {
    const { userId, email, newPassword } = req.body;

    if (!userId || !email || !newPassword) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields'
      });
    }

    // Validate user exists
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, email, full_name, username')
      .eq('id', userId)
      .eq('email', email)
      .single();

    if (userError || !user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check for existing verification request (rate limiting)
    const { data: existingVerification } = await supabase
      .from('password_change_verifications')
      .select('created_at')
      .eq('user_id', userId)
      .single();

    // Rate limiting: Allow resend only after 60 seconds
    if (existingVerification) {
      const timeSinceLastRequest = Date.now() - new Date(existingVerification.created_at).getTime();
      const minResendInterval = 60 * 1000; // 60 seconds

      if (timeSinceLastRequest < minResendInterval) {
        const remainingSeconds = Math.ceil((minResendInterval - timeSinceLastRequest) / 1000);
        return res.status(429).json({
          success: false,
          message: `Please wait ${remainingSeconds} seconds before requesting a new PIN`
        });
      }
    }

    // Generate 6-digit PIN (same as working password reset)
    const pin = Math.floor(100000 + Math.random() * 900000).toString();
    const expiresAt = new Date(Date.now() + VERIFICATION_EXPIRY_MINUTES * 60 * 1000);

    console.log('🔢 Generated password change PIN:', pin, 'expires at:', expiresAt);
    console.log('🔧 About to store PIN in database with data:', { user_id: userId, pin, expires_at: expiresAt.toISOString() });

    // Store both PIN and new password temporarily (same pattern as password reset)
    const tempPasswordHash = await bcrypt.hash(newPassword, 12);

    // Delete any existing verification for this user first
    await supabase
      .from('password_change_verifications')
      .delete()
      .eq('user_id', userId);

    const { error: storeError } = await supabase
      .from('password_change_verifications')
      .insert({
        user_id: userId,
        pin: pin, // Store PIN directly like password reset
        temp_password_hash: tempPasswordHash,
        expires_at: expiresAt.toISOString(),
        attempts: 0,
        created_at: new Date().toISOString()
      });

    if (storeError) {
      console.error('❌ Error storing PIN:', storeError);
      return res.status(500).json({
        success: false,
        message: 'Failed to generate verification PIN'
      });
    }

    console.log('✅ PIN stored successfully in database');

    // Send email with PIN
    if (!resend) {
      console.error('Resend not configured');
      return res.status(500).json({
        success: false,
        message: 'Email service not available'
      });
    }

    const userName = user.full_name || user.username || 'User';
    
    const emailResult = await resend.emails.send({
      from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
      to: [email],
      subject: 'Password Change Verification - Aureus Alliance',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #1f2937 0%, #374151 100%); padding: 30px; border-radius: 10px; text-align: center;">
            <h1 style="color: #f59e0b; margin: 0 0 20px 0;">🔒 Password Change Verification</h1>
            <p style="color: #e5e7eb; font-size: 16px; margin: 0 0 30px 0;">
              Hello ${userName},
            </p>
            <p style="color: #e5e7eb; font-size: 16px; margin: 0 0 30px 0;">
              You requested to change your password. Please use the verification PIN below to confirm this change:
            </p>
            <div style="background: #374151; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <div style="color: #f59e0b; font-size: 32px; font-weight: bold; letter-spacing: 8px; font-family: monospace;">
                ${pin}
              </div>
            </div>
            <p style="color: #9ca3af; font-size: 14px; margin: 20px 0 0 0;">
              This PIN will expire in ${VERIFICATION_EXPIRY_MINUTES} minutes for security reasons.
            </p>
            <p style="color: #9ca3af; font-size: 14px; margin: 10px 0 0 0;">
              If you didn't request this password change, please ignore this email.
            </p>
          </div>
          <div style="text-align: center; margin-top: 20px;">
            <p style="color: #6b7280; font-size: 12px;">
              © 2025 Aureus Alliance Holdings (Pty) Ltd. All rights reserved.
            </p>
          </div>
        </div>
      `
    });

    if (emailResult.error) {
      console.error('Email sending failed:', emailResult.error);
      return res.status(500).json({
        success: false,
        message: 'Failed to send verification email'
      });
    }

    console.log('✅ Password change PIN sent successfully:', emailResult.data?.id);

    return res.status(200).json({
      success: true,
      message: 'Verification PIN sent to your email'
    });

  } catch (error) {
    console.error('Send PIN error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to send verification PIN'
    });
  }
}

/**
 * Verify PIN and change password
 */
async function handleVerifyPINAndChangePassword(req, res) {
  try {
    const { userId, email, pin } = req.body;

    if (!userId || !email || !pin) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields'
      });
    }

    // Get verification record
    const { data: verification, error: verifyError } = await supabase
      .from('password_change_verifications')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (verifyError || !verification) {
      return res.status(400).json({
        success: false,
        message: 'No verification request found'
      });
    }

    // Check if expired
    if (new Date() > new Date(verification.expires_at)) {
      return res.status(400).json({
        success: false,
        message: 'Verification PIN has expired'
      });
    }

    // Check attempt limit
    if (verification.attempts >= MAX_VERIFICATION_ATTEMPTS) {
      return res.status(400).json({
        success: false,
        message: 'Too many verification attempts'
      });
    }

    // Verify PIN (direct comparison like password reset)
    const pinValid = pin.trim() === verification.pin;

    console.log('🔍 PIN verification:', { provided: pin.trim(), stored: verification.pin, valid: pinValid });

    if (!pinValid) {
      // Increment attempts
      await supabase
        .from('password_change_verifications')
        .update({ attempts: verification.attempts + 1 })
        .eq('user_id', userId);

      return res.status(400).json({
        success: false,
        message: 'Invalid verification PIN',
        attemptsRemaining: MAX_VERIFICATION_ATTEMPTS - (verification.attempts + 1)
      });
    }

    // PIN is valid - update password
    const { error: updateError } = await supabase
      .from('users')
      .update({
        password_hash: verification.temp_password_hash,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (updateError) {
      console.error('Password update error:', updateError);
      return res.status(500).json({
        success: false,
        message: 'Failed to update password'
      });
    }

    // Clean up verification record
    await supabase
      .from('password_change_verifications')
      .delete()
      .eq('user_id', userId);

    console.log('✅ Password changed successfully for user:', userId);

    return res.status(200).json({
      success: true,
      message: 'Password changed successfully'
    });

  } catch (error) {
    console.error('Verify PIN error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to verify PIN'
    });
  }
}
