import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Copy the registration functions from lib/supabase.ts
const validatePassword = (password) => {
  const errors = []
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long')
  }
  
  if (password.length > 128) {
    errors.push('Password must be less than 128 characters')
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter')
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter')
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  
  if (!email || email.trim().length === 0) {
    return { valid: false, error: 'Email is required' }
  }
  
  if (email.length > 255) {
    return { valid: false, error: 'Email is too long' }
  }
  
  if (!emailRegex.test(email)) {
    return { valid: false, error: 'Invalid email format' }
  }
  
  if (email.includes('<script>') || email.includes('<') || email.includes('>')) {
    return { valid: false, error: 'Invalid email format' }
  }
  
  return { valid: true }
}

const checkEmailExists = async (email) => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .eq('email', email.toLowerCase().trim())
      .single()
    
    if (error && error.code !== 'PGRST116') {
      console.error('Error checking email:', error)
      return false
    }
    
    return !!data
  } catch (error) {
    console.error('Error checking email:', error)
    return false
  }
}

const checkUsernameExists = async (username) => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .eq('username', username.toLowerCase().trim())
      .single()
    
    if (error && error.code !== 'PGRST116') {
      console.error('Error checking username:', error)
      return false
    }
    
    return !!data
  } catch (error) {
    console.error('Error checking username:', error)
    return false
  }
}

const getUserByUsername = async (username) => {
  try {
    console.log('🔍 Looking for user with username:', username)
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('username', username.trim())
      .single()

    console.log('🔍 Query result - data:', data)
    console.log('🔍 Query result - error:', error)

    if (error && error.code !== 'PGRST116') {
      console.error('Error getting user by username:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Error getting user by username:', error)
    return null
  }
}

const hashPassword = async (password) => {
  const encoder = new TextEncoder()
  const data = encoder.encode(password + 'aureus_salt_2024')
  const hashBuffer = await crypto.subtle.digest('SHA-256', data)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
}

const createReferralRelationship = async (referrerId, referredId, sponsorUsername) => {
  try {
    const referralCode = `${sponsorUsername}_${referredId}_${Date.now()}`
    
    const { data, error } = await supabase
      .from('referrals')
      .insert({
        referrer_id: referrerId,
        referred_id: referredId,
        referral_code: referralCode,
        commission_rate: 15.00,
        status: 'active',
        created_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) {
      console.error('Error creating referral relationship:', error)
      return false
    }
    
    console.log('✅ Referral relationship created:', data)
    return true
  } catch (error) {
    console.error('Error creating referral relationship:', error)
    return false
  }
}

// Updated registration function
const registerUserWithEmail = async (userData) => {
  try {
    console.log('🔐 Starting user registration for:', userData.email)
    
    // Validate input data
    const emailValidation = validateEmail(userData.email)
    if (!emailValidation.valid) {
      return { user: null, error: { message: emailValidation.error } }
    }
    
    const passwordValidation = validatePassword(userData.password)
    if (!passwordValidation.valid) {
      return { user: null, error: { message: passwordValidation.errors.join(', ') } }
    }
    
    if (userData.password !== userData.confirmPassword) {
      return { user: null, error: { message: 'Passwords do not match' } }
    }
    
    if (!userData.fullName || userData.fullName.trim().length < 2) {
      return { user: null, error: { message: 'Full name is required' } }
    }
    
    if (!userData.sponsorUsername || userData.sponsorUsername.trim().length < 2) {
      return { user: null, error: { message: 'Sponsor username is required' } }
    }
    
    // Check if email already exists
    const emailExists = await checkEmailExists(userData.email)
    if (emailExists) {
      return { user: null, error: { message: 'Email address is already registered' } }
    }
    
    // Generate username from email (before @ symbol)
    const username = userData.email.split('@')[0].toLowerCase().replace(/[^a-z0-9]/g, '')
    
    // Check if username already exists, if so append numbers
    let finalUsername = username
    let counter = 1
    while (await checkUsernameExists(finalUsername)) {
      finalUsername = `${username}${counter}`
      counter++
    }
    
    // Validate sponsor exists
    let sponsor = await getUserByUsername(userData.sponsorUsername)
    if (!sponsor) {
      console.log(`❌ Sponsor ${userData.sponsorUsername} not found, using AUREUS`)
      sponsor = await getUserByUsername('AUREUS')
      if (!sponsor) {
        return { user: null, error: { message: 'Default sponsor not found. Please contact support.' } }
      }
    }
    
    // Hash password
    const passwordHash = await hashPassword(userData.password)
    
    // Create Supabase auth user first
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: userData.email,
      password: userData.password,
      options: {
        data: {
          full_name: userData.fullName,
          username: finalUsername,
          is_email_user: true
        }
      }
    })
    
    if (authError) {
      console.error('❌ Error creating auth user:', authError)
      return { user: null, error: { message: authError.message } }
    }
    
    // Check if user was automatically created in users table
    let { data: existingDbUser } = await supabase
      .from('users')
      .select('*')
      .eq('email', userData.email.toLowerCase().trim())
      .single()
    
    let newUser
    if (existingDbUser) {
      // Update existing user with our data
      console.log('✅ User already exists in database, updating...')
      const { data: updatedUser, error: updateError } = await supabase
        .from('users')
        .update({
          username: finalUsername,
          password_hash: passwordHash,
          full_name: userData.fullName.trim(),
          is_active: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingDbUser.id)
        .select()
        .single()
      
      if (updateError) {
        console.error('❌ Error updating user:', updateError)
        return { user: null, error: { message: 'Failed to update user account' } }
      }
      newUser = updatedUser
    } else {
      // Create new user in users table
      const { data: createdUser, error: userError } = await supabase
        .from('users')
        .insert({
          username: finalUsername,
          email: userData.email.toLowerCase().trim(),
          password_hash: passwordHash,
          full_name: userData.fullName.trim(),
          is_active: true,
          is_verified: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single()
      
      if (userError) {
        console.error('❌ Error creating user:', userError)
        return { user: null, error: { message: 'Failed to create user account' } }
      }
      newUser = createdUser
    }
    
    console.log('✅ User created successfully:', newUser)
    
    // Create referral relationship
    const referralSuccess = await createReferralRelationship(
      sponsor.id,
      newUser.id,
      sponsor.username
    )
    
    if (!referralSuccess) {
      console.warn('⚠️ Failed to create referral relationship, but user was created')
    }
    
    console.log('✅ Registration completed successfully for:', userData.email)
    
    return {
      user: {
        ...authData.user,
        database_user: newUser,
        sponsor: sponsor.username
      },
      error: null
    }
    
  } catch (error) {
    console.error('❌ Registration exception:', error)
    return { user: null, error: { message: 'Registration failed. Please try again.' } }
  }
}

// Test the updated registration
async function testUpdatedRegistration() {
  try {
    console.log('🧪 Testing updated registration function...');
    
    const testUser = {
      email: `newtest${Date.now()}@example.com`,
      password: 'NewTest123',
      confirmPassword: 'NewTest123',
      fullName: 'New Test User',
      sponsorUsername: 'TTTFOUNDER'
    };
    
    console.log('📧 Test user data:', {
      email: testUser.email,
      fullName: testUser.fullName,
      sponsorUsername: testUser.sponsorUsername
    });
    
    const result = await registerUserWithEmail(testUser);
    
    if (result.error) {
      console.error('❌ Registration failed:', result.error.message);
      return;
    }
    
    console.log('✅ Registration successful!');
    console.log('   Auth User ID:', result.user.id);
    console.log('   Database User ID:', result.user.database_user.id);
    console.log('   Username:', result.user.database_user.username);
    console.log('   Sponsor:', result.user.sponsor);
    
    // Test login
    console.log('🔐 Testing login with new user...');
    
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: testUser.email,
      password: testUser.password
    });
    
    if (loginError) {
      console.error('❌ Login failed:', loginError.message);
      return;
    }
    
    console.log('✅ Login successful!');
    console.log('   Logged in as:', loginData.user.email);
    
    console.log('\n🎉 Updated registration flow test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

testUpdatedRegistration();
