/**
 * Test script to check current email configuration
 */

import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('🔧 Current Email Configuration:');
console.log('================================');
console.log('RESEND_API_KEY:', process.env.RESEND_API_KEY ? `${process.env.RESEND_API_KEY.substring(0, 10)}...` : 'NOT SET');
console.log('RESEND_FROM_EMAIL:', process.env.RESEND_FROM_EMAIL || 'NOT SET (will use default)');
console.log('RESEND_FROM_NAME:', process.env.RESEND_FROM_NAME || 'NOT SET (will use default)');
console.log('');

// Check what the defaults would be
const RESEND_FROM_EMAIL = process.env.RESEND_FROM_EMAIL || '<EMAIL>';
const RESEND_FROM_NAME = process.env.RESEND_FROM_NAME || 'Aureus Alliance Holdings';

console.log('🎯 Effective Configuration:');
console.log('============================');
console.log('FROM EMAIL:', RESEND_FROM_EMAIL);
console.log('FROM NAME:', RESEND_FROM_NAME);
console.log('');

// Check if using correct domain
if (RESEND_FROM_EMAIL.includes('aureus.africa')) {
  console.log('✅ Using correct domain: aureus.africa');
} else if (RESEND_FROM_EMAIL.includes('aureusalliance.com')) {
  console.log('❌ Using incorrect domain: aureusalliance.com');
  console.log('💡 Please update RESEND_FROM_EMAIL in .env file to use @aureus.africa');
} else {
  console.log('⚠️ Using unknown domain:', RESEND_FROM_EMAIL);
}
