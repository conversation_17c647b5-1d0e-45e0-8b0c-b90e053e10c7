import { supabase } from './supabase';
import type { 
  GalleryImage, 
  GalleryCategory, 
  CreateGalleryImageRequest, 
  UpdateGalleryImageRequest,
  CreateGalleryCategoryRequest,
  UpdateGalleryCategoryRequest,
  GalleryFilters,
  GalleryQueryOptions,
  GalleryQueryResult,
  ImageUploadResult
} from '../types/gallery';

export class GalleryService {
  // Gallery Categories
  static async getCategories(): Promise<GalleryCategory[]> {
    try {
      // Quick table existence check first
      const { error: tableCheckError } = await supabase
        .from('gallery_categories')
        .select('id', { count: 'exact', head: true })
        .limit(1);

      if (tableCheckError) {
        console.log('Gallery categories table check failed:', tableCheckError);
        if (tableCheckError.code === '42P01' || tableCheckError.message.includes('does not exist') || (tableCheckError.message.includes('relation') && tableCheckError.message.includes('does not exist'))) {
          console.log('Gallery categories table not found, returning empty array immediately');
          return [];
        }
      }

      const { data, error } = await supabase
        .from('gallery_categories')
        .select('*')
        .eq('is_active', true)
        .order('display_order', { ascending: true });

      if (error) {
        // If table doesn't exist, return empty array
        if (error.code === '42P01' || error.message.includes('does not exist') || error.message.includes('relation') && error.message.includes('does not exist')) {
          return [];
        }
        throw new Error(`Failed to fetch categories: ${error.message}`);
      }
      return data || [];
    } catch (error) {
      console.warn('Gallery categories table not found, returning empty array');
      return [];
    }
  }

  static async createCategory(categoryData: CreateGalleryCategoryRequest): Promise<GalleryCategory> {
    const { data, error } = await supabase
      .from('gallery_categories')
      .insert({
        ...categoryData,
        updated_by: 'admin' // TODO: Get from auth context
      })
      .select()
      .single();

    if (error) throw new Error(`Failed to create category: ${error.message}`);
    return data;
  }

  static async updateCategory(categoryData: UpdateGalleryCategoryRequest): Promise<GalleryCategory> {
    const { id, ...updateData } = categoryData;
    const { data, error } = await supabase
      .from('gallery_categories')
      .update({
        ...updateData,
        updated_by: 'admin' // TODO: Get from auth context
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw new Error(`Failed to update category: ${error.message}`);
    return data;
  }

  static async deleteCategory(id: string): Promise<void> {
    const { error } = await supabase
      .from('gallery_categories')
      .update({ is_active: false })
      .eq('id', id);

    if (error) throw new Error(`Failed to delete category: ${error.message}`);
  }

  // Gallery Images
  static async getImages(options: GalleryQueryOptions = {}): Promise<GalleryQueryResult> {
    try {
      console.log('GalleryService.getImages called with options:', options);

      // First, quickly check if the table exists by doing a simple count query
      const { error: tableCheckError } = await supabase
        .from('gallery_images')
        .select('id', { count: 'exact', head: true })
        .limit(1);

      if (tableCheckError) {
        console.log('Gallery table check failed:', tableCheckError);
        if (tableCheckError.code === '42P01' || tableCheckError.message.includes('does not exist') || (tableCheckError.message.includes('relation') && tableCheckError.message.includes('does not exist'))) {
          console.log('Gallery tables not found, returning empty result immediately');
          return {
            images: [],
            total: 0,
            page: 1,
            limit: 0,
            hasMore: false
          };
        }
      }

      let query = supabase
        .from('gallery_images')
        .select(`
          *,
          category:gallery_categories(*)
        `)
        .eq('is_active', true);

    // Apply filters
    if (options.filters) {
      const { category_id, is_featured, search } = options.filters;
      
      if (category_id) {
        query = query.eq('category_id', category_id);
      }
      
      if (is_featured !== undefined) {
        query = query.eq('is_featured', is_featured);
      }
      
      if (search) {
        query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%`);
      }
    }

    // Apply sorting
    if (options.sort) {
      query = query.order(options.sort.field, { ascending: options.sort.direction === 'asc' });
    } else {
      // Default sorting: featured first, then by display_order
      query = query.order('is_featured', { ascending: false })
                   .order('display_order', { ascending: true });
    }

    // Apply pagination
    let count = null;
    if (options.pagination) {
      const { page, limit } = options.pagination;
      const from = (page - 1) * limit;
      const to = from + limit - 1;
      query = query.range(from, to);

      // Get total count for pagination
      const { count: totalCount } = await supabase
        .from('gallery_images')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true);
      count = totalCount;
    }

      const { data, error } = await query;

      console.log('Gallery query result:', { data, error, count });

      if (error) {
        console.error('Gallery query error:', error);
        // If table doesn't exist, return empty result
        if (error.code === '42P01' || error.message.includes('does not exist') || (error.message.includes('relation') && error.message.includes('does not exist'))) {
          console.log('Gallery tables not found, returning empty result');
          return {
            images: [],
            total: 0,
            page: 1,
            limit: 0,
            hasMore: false
          };
        }
        throw new Error(`Failed to fetch images: ${error.message}`);
      }

      const images = data || [];
      const total = count || images.length;
      const page = options.pagination?.page || 1;
      const limit = options.pagination?.limit || images.length;

      return {
        images,
        total,
        page,
        limit,
        hasMore: total > page * limit
      };
    } catch (error) {
      console.warn('Gallery images table not found, returning empty result:', error);
      return {
        images: [],
        total: 0,
        page: 1,
        limit: 0,
        hasMore: false
      };
    }
  }

  static async getImageById(id: string): Promise<GalleryImage | null> {
    const { data, error } = await supabase
      .from('gallery_images')
      .select(`
        *,
        category:gallery_categories(*)
      `)
      .eq('id', id)
      .eq('is_active', true)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw new Error(`Failed to fetch image: ${error.message}`);
    }

    return data;
  }

  static async createImage(imageData: CreateGalleryImageRequest): Promise<GalleryImage> {
    const { data, error } = await supabase
      .from('gallery_images')
      .insert({
        ...imageData,
        updated_by: 'admin' // TODO: Get from auth context
      })
      .select(`
        *,
        category:gallery_categories(*)
      `)
      .single();

    if (error) throw new Error(`Failed to create image: ${error.message}`);
    return data;
  }

  static async updateImage(imageData: UpdateGalleryImageRequest): Promise<GalleryImage> {
    const { id, ...updateData } = imageData;
    const { data, error } = await supabase
      .from('gallery_images')
      .update({
        ...updateData,
        updated_by: 'admin' // TODO: Get from auth context
      })
      .eq('id', id)
      .select(`
        *,
        category:gallery_categories(*)
      `)
      .single();

    if (error) throw new Error(`Failed to update image: ${error.message}`);
    return data;
  }

  static async deleteImage(id: string): Promise<void> {
    const { error } = await supabase
      .from('gallery_images')
      .update({ is_active: false })
      .eq('id', id);

    if (error) throw new Error(`Failed to delete image: ${error.message}`);
  }

  // File Upload
  static async uploadImage(file: File, folder: string = 'gallery'): Promise<ImageUploadResult> {
    // Generate unique filename
    const fileExt = file.name.split('.').pop();
    const fileName = `${folder}/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;

    try {
      // Upload to Supabase storage
      const { data, error } = await supabase.storage
        .from('assets')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        // Check if it's a bucket/policy issue
        if (error.message.includes('signature verification failed') ||
            error.message.includes('bucket') ||
            error.message.includes('policy')) {
          throw new Error(`Storage setup required: The 'assets' bucket needs to be created in Supabase Storage with proper upload policies. Error: ${error.message}`);
        }
        throw new Error(`Failed to upload image: ${error.message}`);
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('assets')
        .getPublicUrl(data.path);

      // Get image dimensions (if possible)
      const dimensions = await this.getImageDimensions(file);

      return {
        url: publicUrl,
        width: dimensions.width,
        height: dimensions.height,
        file_size: file.size
      };
    } catch (error) {
      // Re-throw with more helpful message
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(`Upload failed: ${error}`);
    }
  }

  static async deleteImageFile(imageUrl: string): Promise<void> {
    // Extract file path from URL
    const urlParts = imageUrl.split('/storage/v1/object/public/assets/');
    if (urlParts.length !== 2) return;

    const filePath = urlParts[1];

    const { error } = await supabase.storage
      .from('assets')
      .remove([filePath]);

    if (error) {
      console.warn(`Failed to delete image file: ${error.message}`);
      // Don't throw error as the database record is more important
    }
  }

  // Utility methods
  private static getImageDimensions(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        resolve({ width: img.width, height: img.height });
      };
      img.onerror = () => {
        resolve({ width: 0, height: 0 });
      };
      img.src = URL.createObjectURL(file);
    });
  }

  static validateImageFile(file: File): { valid: boolean; error?: string } {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

    if (!allowedTypes.includes(file.type)) {
      return { valid: false, error: 'Invalid file type. Please upload JPEG, PNG, or WebP images.' };
    }

    if (file.size > maxSize) {
      return { valid: false, error: 'File size too large. Maximum size is 10MB.' };
    }

    return { valid: true };
  }

  // Bulk operations
  static async updateImageOrder(imageIds: string[]): Promise<void> {
    const updates = imageIds.map((id, index) => ({
      id,
      display_order: index + 1,
      updated_by: 'admin'
    }));

    const { error } = await supabase
      .from('gallery_images')
      .upsert(updates);

    if (error) throw new Error(`Failed to update image order: ${error.message}`);
  }

  static async toggleImageFeatured(id: string): Promise<GalleryImage> {
    // First get current state
    const { data: currentImage, error: fetchError } = await supabase
      .from('gallery_images')
      .select('is_featured')
      .eq('id', id)
      .single();

    if (fetchError) throw new Error(`Failed to fetch image: ${fetchError.message}`);

    // Update with opposite value
    const { data, error } = await supabase
      .from('gallery_images')
      .update({ 
        is_featured: !currentImage.is_featured,
        updated_by: 'admin'
      })
      .eq('id', id)
      .select(`
        *,
        category:gallery_categories(*)
      `)
      .single();

    if (error) throw new Error(`Failed to toggle featured status: ${error.message}`);
    return data;
  }
}
