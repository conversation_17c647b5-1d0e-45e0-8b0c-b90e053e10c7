/**
 * EMAIL VERIFICATION MODAL
 * 
 * Reusable modal component for 6-digit PIN email verification
 * with accessibility, loading states, and comprehensive error handling.
 * 
 * Features:
 * - 6-digit PIN input with auto-focus and validation
 * - Real-time code formatting and validation
 * - Resend functionality with rate limiting
 * - Accessibility compliance (WCAG 2.1 AA)
 * - Mobile-responsive design
 * - Integration with emailVerificationService
 */

import React, { useState, useEffect, useRef } from 'react';

export interface EmailVerificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onVerificationSuccess: () => void;
  userId: number;
  email: string;
  purpose: 'registration' | 'account_update' | 'withdrawal' | 'password_reset';
  title?: string;
  description?: string;
  autoSendCode?: boolean;
}

interface VerificationState {
  code: string;
  isLoading: boolean;
  isVerifying: boolean;
  error: string | null;
  success: string | null;
  attemptsRemaining?: number;
  nextCodeAllowedAt?: Date;
  codeSent: boolean;
}

export const EmailVerificationModal: React.FC<EmailVerificationModalProps> = ({
  isOpen,
  onClose,
  onVerificationSuccess,
  userId,
  email,
  purpose,
  title,
  description,
  autoSendCode = true
}) => {
  const [state, setState] = useState<VerificationState>({
    code: '',
    isLoading: false,
    isVerifying: false,
    error: null,
    success: null,
    codeSent: false
  });

  const codeInputRefs = useRef<(HTMLInputElement | null)[]>([]);
  const modalRef = useRef<HTMLDivElement>(null);

  // Auto-send code when modal opens - TEMPORARILY DISABLED FOR DEBUGGING
  // useEffect(() => {
  //   if (isOpen && autoSendCode && !state.codeSent) {
  //     sendVerificationCode();
  //   }
  // }, [isOpen, autoSendCode]);

  // Focus management for accessibility
  useEffect(() => {
    if (isOpen && codeInputRefs.current[0]) {
      setTimeout(() => {
        codeInputRefs.current[0]?.focus();
      }, 100);
    }
  }, [isOpen, state.codeSent]);

  // Keyboard event handling for modal
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  const sendVerificationCode = async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Call the API endpoint instead of the service directly
      const response = await fetch('/api/send-verification-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          email,
          purpose,
          ipAddress: await getClientIP(),
          userAgent: navigator.userAgent
        })
      });

      const result = await response.json();

      if (result.success) {
        setState(prev => ({
          ...prev,
          isLoading: false,
          codeSent: true,
          success: result.message,
          error: null
        }));
      } else {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: result.message,
          nextCodeAllowedAt: result.nextCodeAllowedAt
        }));
      }
    } catch (error) {
      console.error('Error sending verification code:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to send verification code. Please try again.'
      }));
    }
  };

  const handleCodeInput = (index: number, value: string) => {
    // Only allow digits
    const digit = value.replace(/\D/g, '').slice(-1);
    
    const newCode = state.code.split('');
    newCode[index] = digit;
    
    const updatedCode = newCode.join('').slice(0, 6);
    setState(prev => ({ ...prev, code: updatedCode, error: null }));

    // Auto-focus next input
    if (digit && index < 5) {
      codeInputRefs.current[index + 1]?.focus();
    }

    // Auto-verify when 6 digits entered
    if (updatedCode.length === 6) {
      verifyCode(updatedCode);
    }
  };

  const handleKeyDown = (index: number, event: React.KeyboardEvent) => {
    if (event.key === 'Backspace' && !state.code[index] && index > 0) {
      // Focus previous input on backspace
      codeInputRefs.current[index - 1]?.focus();
    } else if (event.key === 'ArrowLeft' && index > 0) {
      codeInputRefs.current[index - 1]?.focus();
    } else if (event.key === 'ArrowRight' && index < 5) {
      codeInputRefs.current[index + 1]?.focus();
    }
  };

  const handlePaste = (event: React.ClipboardEvent) => {
    event.preventDefault();
    const pastedData = event.clipboardData.getData('text').replace(/\D/g, '').slice(0, 6);
    
    if (pastedData.length > 0) {
      setState(prev => ({ ...prev, code: pastedData, error: null }));
      
      // Focus the next empty input or last input
      const nextIndex = Math.min(pastedData.length, 5);
      codeInputRefs.current[nextIndex]?.focus();

      // Auto-verify if 6 digits pasted
      if (pastedData.length === 6) {
        verifyCode(pastedData);
      }
    }
  };

  const verifyCode = async (code: string) => {
    if (code.length !== 6) return;

    setState(prev => ({ ...prev, isVerifying: true, error: null }));

    try {
      // Call the API endpoint instead of the service directly
      const response = await fetch('/api/verify-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          email,
          code,
          purpose,
          ipAddress: await getClientIP(),
          userAgent: navigator.userAgent
        })
      });

      const result = await response.json();

      if (result.success) {
        setState(prev => ({
          ...prev,
          isVerifying: false,
          success: result.message
        }));

        // Delay to show success message
        setTimeout(() => {
          onVerificationSuccess();
          onClose();
        }, 1500);
      } else {
        setState(prev => ({
          ...prev,
          isVerifying: false,
          error: result.message,
          attemptsRemaining: result.attemptsRemaining,
          code: '' // Clear code on failure
        }));

        // Focus first input for retry
        setTimeout(() => {
          codeInputRefs.current[0]?.focus();
        }, 100);
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        isVerifying: false,
        error: 'Verification failed. Please try again.',
        code: ''
      }));
    }
  };

  const getClientIP = async (): Promise<string> => {
    // Skip IP lookup to avoid CSP issues - use localhost for development
    return 'localhost';
  };

  const canResendCode = (): boolean => {
    if (!state.nextCodeAllowedAt) return true;
    return new Date() >= state.nextCodeAllowedAt;
  };

  const getResendCountdown = (): string => {
    if (!state.nextCodeAllowedAt) return '';
    const now = new Date();
    const diff = state.nextCodeAllowedAt.getTime() - now.getTime();
    if (diff <= 0) return '';
    
    const minutes = Math.floor(diff / 60000);
    const seconds = Math.floor((diff % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  if (!isOpen) return null;

  const purposeText = {
    registration: 'complete your registration',
    account_update: 'confirm your account changes',
    withdrawal: 'authorize your withdrawal',
    password_reset: 'reset your password'
  }[purpose];

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={(e) => e.target === e.currentTarget && onClose()}
      role="dialog"
      aria-modal="true"
      aria-labelledby="verification-title"
      aria-describedby="verification-description"
    >
      <div 
        ref={modalRef}
        className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 relative"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
          aria-label="Close verification modal"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* Header */}
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          
          <h2 id="verification-title" className="text-2xl font-bold text-gray-900 mb-2">
            {title || 'Email Verification'}
          </h2>
          
          <p id="verification-description" className="text-gray-600">
            {description || `Enter the 6-digit code sent to ${email} to ${purposeText}.`}
          </p>
        </div>

        {/* Success message */}
        {state.success && (
          <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded-md">
            <div className="flex items-center">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              {state.success}
            </div>
          </div>
        )}

        {/* Error message */}
        {state.error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-md">
            <div className="flex items-center">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <div>
                {state.error}
                {state.attemptsRemaining !== undefined && (
                  <div className="text-sm mt-1">
                    {state.attemptsRemaining} attempts remaining
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Code input section */}
        {state.codeSent && (
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-3 text-center">
              Enter 6-digit verification code
            </label>
            
            <div className="flex justify-center space-x-2 mb-4">
              {[0, 1, 2, 3, 4, 5].map((index) => (
                <input
                  key={index}
                  ref={(el) => (codeInputRefs.current[index] = el)}
                  type="text"
                  inputMode="numeric"
                  pattern="[0-9]*"
                  maxLength={1}
                  value={state.code[index] || ''}
                  onChange={(e) => handleCodeInput(index, e.target.value)}
                  onKeyDown={(e) => handleKeyDown(index, e)}
                  onPaste={handlePaste}
                  className="w-12 h-12 text-center text-xl font-bold border-2 border-gray-300 rounded-md focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200 focus:outline-none transition-colors"
                  aria-label={`Digit ${index + 1} of verification code`}
                  disabled={state.isVerifying}
                />
              ))}
            </div>

            {/* Verification status */}
            {state.isVerifying && (
              <div className="text-center text-gray-600 mb-4">
                <div className="inline-flex items-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-yellow-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Verifying code...
                </div>
              </div>
            )}
          </div>
        )}

        {/* Resend code section */}
        {state.codeSent && (
          <div className="text-center">
            <p className="text-sm text-gray-600 mb-3">
              Didn't receive the code?
            </p>
            
            {canResendCode() ? (
              <button
                onClick={sendVerificationCode}
                disabled={state.isLoading}
                className="text-yellow-600 hover:text-yellow-700 font-medium text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {state.isLoading ? 'Sending...' : 'Resend code'}
              </button>
            ) : (
              <p className="text-sm text-gray-500">
                Resend available in {getResendCountdown()}
              </p>
            )}
          </div>
        )}

        {/* Send code button (if not auto-sent) */}
        {!state.codeSent && !autoSendCode && (
          <div className="text-center">
            <button
              onClick={sendVerificationCode}
              disabled={state.isLoading}
              className="bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-6 rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {state.isLoading ? 'Sending...' : 'Send Verification Code'}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default EmailVerificationModal;
