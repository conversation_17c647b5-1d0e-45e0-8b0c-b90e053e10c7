import React, { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';
import { SendMessageModal } from '../messaging/SendMessageModal';

interface DownlineTreeViewProps {
  userId: number;
  className?: string;
}

interface TreeNode {
  id: number;
  username: string;
  first_name: string;
  last_name: string;
  email: string;
  telegram_username?: string;
  total_referrals: number;
  usdt_earnings: number;
  share_earnings: number;
  purchased_shares: number;
  total_invested: number;
  created_at: string;
  is_active: boolean;
  level: number;
  children: TreeNode[];
  parent_id?: number;
}

interface TreeStats {
  totalNodes: number;
  activeNodes: number;
  totalLevels: number;
  totalUsdtEarnings: number;
  totalShareEarnings: number;
  activeEarners: number;
}

export const DownlineTreeView: React.FC<DownlineTreeViewProps> = ({ userId, className = '' }) => {
  const [treeData, setTreeData] = useState<TreeNode | null>(null);
  const [treeStats, setTreeStats] = useState<TreeStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [expandedNodes, setExpandedNodes] = useState<Set<number>>(new Set([userId]));
  const [selectedNode, setSelectedNode] = useState<TreeNode | null>(null);

  const [maxDepth, setMaxDepth] = useState(3);
  const [showShareholders, setShowShareholders] = useState(true);
  const [showActiveEarners, setShowActiveEarners] = useState(true);
  const [showNonParticipants, setShowNonParticipants] = useState(true);
  const [activeEarnersFilterEnabled, setActiveEarnersFilterEnabled] = useState(false);
  const [messageModalOpen, setMessageModalOpen] = useState(false);
  const [messageRecipient, setMessageRecipient] = useState<TreeNode | null>(null);

  useEffect(() => {
    loadDownlineData();
  }, [userId, maxDepth, showShareholders, showActiveEarners, showNonParticipants, activeEarnersFilterEnabled]);



  const loadDownlineData = async () => {
    setLoading(true);
    try {
      // Use service role client for all queries to ensure consistency
      const serviceClient = getServiceRoleClient();

      // Get all users in the downline
      const { data: allUsers, error } = await serviceClient
        .from('users')
        .select(`
          id,
          username,
          first_name,
          last_name,
          email,
          telegram_username,
          created_at,
          is_active
        `);

      if (error) throw error;

      // Get referral relationships
      const { data: referrals, error: referralError } = await serviceClient
        .from('referrals')
        .select('referrer_id, referred_id')
        .eq('status', 'active');

      if (referralError) throw referralError;

      // Get commission transactions to calculate actual earned commissions (not transferred shares)
      const { data: commissionTransactions, error: commissionError } = await serviceClient
        .from('commission_transactions')
        .select('referrer_id, usdt_commission, share_commission')
        .eq('status', 'approved');

      // Get ALL commission transactions for Active Earners filter (no user filtering)
      const { data: allCommissionTransactions, error: allCommissionError } = await serviceClient
        .from('commission_transactions')
        .select('referrer_id')
        .eq('status', 'approved');

      // Get user share holdings to show personal investments
      const { data: shareHoldings, error: holdingsError } = await serviceClient
        .from('user_share_holdings')
        .select('user_id, total_shares, total_invested');

      if (commissionError) {
        console.error('Error fetching commission transactions:', commissionError);
      }

      if (allCommissionError) {
        console.error('Error fetching all commission transactions:', allCommissionError);
      }

      if (holdingsError) {
        console.error('Error fetching share holdings:', holdingsError);
      }

      // Create commission lookup map from actual earned commissions (not transferred shares)
      const commissionMap = new Map();
      if (commissionTransactions) {
        // Group transactions by referrer_id and sum up actual earned commissions
        commissionTransactions.forEach(transaction => {
          const referrerId = transaction.referrer_id;
          const usdtCommission = parseFloat(transaction.usdt_commission || 0);
          const shareCommission = parseFloat(transaction.share_commission || 0);

          if (!commissionMap.has(referrerId)) {
            commissionMap.set(referrerId, { usdt: 0, shares: 0 });
          }

          const current = commissionMap.get(referrerId);
          current.usdt += usdtCommission;
          current.shares += shareCommission;
        });
      }

      // Create share holdings lookup map
      const holdingsMap = new Map();
      if (shareHoldings) {
        shareHoldings.forEach(holding => {
          holdingsMap.set(holding.user_id, {
            purchased_shares: parseInt(holding.total_shares || 0),
            total_invested: parseFloat(holding.total_invested || 0)
          });
        });
      }

      // Create referral count map
      const referralCountMap = new Map();
      if (referrals) {
        referrals.forEach(referral => {
          const count = referralCountMap.get(referral.referrer_id) || 0;
          referralCountMap.set(referral.referrer_id, count + 1);
        });
      }

      // Create user categorization maps
      const shareholdersSet = new Set();
      const activeEarnersSet = new Set();

      // Categorize shareholders (users with purchased shares)
      if (shareHoldings) {
        shareHoldings.forEach(holding => {
          if (parseInt(holding.total_shares || 0) > 0) {
            shareholdersSet.add(holding.user_id);
          }
        });
      }

      // Categorize active earners (ALL users with commission earnings across entire system)
      if (allCommissionTransactions) {
        allCommissionTransactions.forEach(transaction => {
          activeEarnersSet.add(transaction.referrer_id);
        });
      }

      // Build the tree structure with commission and holdings data
      const tree = buildTree(allUsers || [], referrals || [], userId, 0, commissionMap, referralCountMap, holdingsMap, shareholdersSet, activeEarnersSet);
      setTreeData(tree);

      // Calculate statistics
      const stats = calculateTreeStats(tree, activeEarnersSet);
      setTreeStats(stats);

    } catch (error) {
      console.error('Error loading downline data:', error);
    } finally {
      setLoading(false);
    }
  };

  const buildTree = (
    users: any[],
    referrals: any[],
    rootId: number,
    level: number,
    commissionMap: Map<number, any>,
    referralCountMap: Map<number, number>,
    holdingsMap: Map<number, any>,
    shareholdersSet: Set<number>,
    activeEarnersSet: Set<number>
  ): TreeNode | null => {
    const user = users.find(u => u.id === rootId);
    if (!user || level > maxDepth) return null;

    // Find direct referrals
    const directReferrals = referrals
      .filter(r => r.referrer_id === rootId)
      .map(r => r.referred_id);

    // Build children recursively
    const children = directReferrals
      .map(childId => buildTree(users, referrals, childId, level + 1, commissionMap, referralCountMap, holdingsMap, shareholdersSet, activeEarnersSet))
      .filter(child => {
        if (child === null) return false;

        // Determine user category
        const isShareholder = shareholdersSet.has(child.id);
        const isActiveEarner = activeEarnersSet.has(child.id);
        const isNonParticipant = !isShareholder && !isActiveEarner;

        // Apply Active Earners Filter first (exclusive filter)
        if (activeEarnersFilterEnabled) {
          // When Active Earners Filter is enabled, show ONLY users who have earned commissions
          if (!isActiveEarner) return false;
        }

        // Apply category filters (show/hide toggles)
        if (isShareholder && !showShareholders) return false;
        if (isActiveEarner && !showActiveEarners) return false;
        if (isNonParticipant && !showNonParticipants) return false;

        return true;
      }) as TreeNode[];

    const commissionData = commissionMap.get(user.id) || { usdt: 0, shares: 0 };
    const holdingsData = holdingsMap.get(user.id) || { purchased_shares: 0, total_invested: 0 };

    return {
      id: user.id,
      username: user.username,
      first_name: user.first_name || '',
      last_name: user.last_name || '',
      email: user.email,
      telegram_username: user.telegram_username,
      total_referrals: referralCountMap.get(user.id) || 0,
      usdt_earnings: commissionData.usdt,
      share_earnings: commissionData.shares,
      purchased_shares: holdingsData.purchased_shares,
      total_invested: holdingsData.total_invested,
      created_at: user.created_at,
      is_active: user.is_active,
      level,
      children,
      parent_id: level > 0 ? rootId : undefined
    };
  };

  const calculateTreeStats = (node: TreeNode | null, activeEarnersSet: Set<number>): TreeStats => {
    if (!node) return { totalNodes: 0, activeNodes: 0, totalLevels: 0, totalUsdtEarnings: 0, totalShareEarnings: 0, activeEarners: 0 };

    let totalNodes = 1;
    let activeNodes = node.is_active ? 1 : 0;
    let totalLevels = node.level + 1;
    let totalUsdtEarnings = node.usdt_earnings || 0;
    let totalShareEarnings = node.share_earnings || 0;
    let activeEarners = activeEarnersSet.has(node.id) ? 1 : 0;

    const processChildren = (children: TreeNode[]) => {
      children.forEach(child => {
        totalNodes++;
        if (child.is_active) activeNodes++;
        if (activeEarnersSet.has(child.id)) activeEarners++;
        totalLevels = Math.max(totalLevels, child.level + 1);
        totalUsdtEarnings += (child.usdt_earnings || 0);
        totalShareEarnings += (child.share_earnings || 0);
        processChildren(child.children);
      });
    };

    processChildren(node.children);

    return { totalNodes, activeNodes, totalLevels, totalUsdtEarnings, totalShareEarnings, activeEarners };
  };

  const toggleNode = (nodeId: number) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  const contactUser = (node: TreeNode) => {
    if (node.telegram_username) {
      window.open(`https://t.me/${node.telegram_username}`);
    } else {
      alert('No contact information available for this user.');
    }
  };

  const openMessageModal = (node: TreeNode) => {
    setMessageRecipient(node);
    setMessageModalOpen(true);
  };

  const renderTreeNode = (node: TreeNode, isLast: boolean = false, prefix: string = '') => {
    const isExpanded = expandedNodes.has(node.id);
    const hasChildren = node.children.length > 0;
    const isSelected = selectedNode?.id === node.id;

    return (
      <div key={node.id} className="select-none">
        {/* Node Row */}
        <div
          className={`flex flex-col sm:flex-row sm:items-center p-2 sm:p-3 rounded-lg cursor-pointer transition-colors ${
            isSelected ? 'bg-blue-600/20 border border-blue-500/30' : 'hover:bg-gray-700/50'
          }`}
          onClick={() => setSelectedNode(node)}
        >
          {/* Top row on mobile: Tree lines, avatar, name, and expand button */}
          <div className="flex items-center w-full sm:flex-1">
            {/* Tree Lines - Hidden on mobile for cleaner look */}
            <div className="hidden sm:flex items-center mr-3 text-gray-500">
              <span className="font-mono text-sm">{prefix}</span>
              <span className="font-mono text-sm">{isLast ? '└─' : '├─'}</span>
            </div>

            {/* User Avatar */}
            <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center text-sm font-bold mr-3 flex-shrink-0 ${
              node.is_active ? 'bg-green-600 text-white' : 'bg-gray-600 text-gray-300'
            }`}>
              {node.first_name?.[0] || node.username[0].toUpperCase()}
            </div>

            {/* User Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1">
                <span className="text-white font-medium text-sm sm:text-base truncate">
                  {node.first_name} {node.last_name}
                </span>
                {!node.is_active && (
                  <span className="text-xs bg-red-600 text-white px-2 py-1 rounded flex-shrink-0">Inactive</span>
                )}
              </div>
              <div className="text-xs sm:text-sm text-gray-400 mb-1">
                @{node.username} • Level {node.level}
              </div>

              {/* Mobile: Stack info vertically */}
              <div className="block sm:hidden space-y-1">
                <div className="text-xs text-gray-400">
                  {node.total_referrals} referrals
                </div>
                <div className="text-xs text-gray-400">
                  ${(node.usdt_earnings || 0).toFixed(2)} USDT + {(node.share_earnings || 0).toFixed(2)} shares earned
                </div>
                <div className="text-xs text-blue-400">
                  {node.purchased_shares || 0} shares purchased (${(node.total_invested || 0).toFixed(2)})
                </div>
              </div>

              {/* Desktop: Single line info */}
              <div className="hidden sm:block text-sm text-gray-400">
                {node.total_referrals} referrals • ${(node.usdt_earnings || 0).toFixed(2)} USDT + {(node.share_earnings || 0).toFixed(2)} earned shares
              </div>
              <div className="hidden sm:block text-xs text-blue-400">
                {node.purchased_shares || 0} shares purchased (${(node.total_invested || 0).toFixed(2)} invested)
              </div>
            </div>

            {/* Expand/Collapse Button */}
            {hasChildren && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  toggleNode(node.id);
                }}
                className="ml-2 w-8 h-8 flex items-center justify-center text-gray-400 hover:text-white bg-gray-700 hover:bg-gray-600 rounded-full flex-shrink-0"
              >
                {isExpanded ? '▼' : '▶'}
              </button>
            )}
          </div>

          {/* Actions - Bottom row on mobile, right side on desktop */}
          <div className="flex items-center justify-between mt-2 sm:mt-0 sm:ml-4 sm:flex-shrink-0">
            <div className="flex items-center space-x-3">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  openMessageModal(node);
                }}
                className="text-green-400 hover:text-green-300 text-lg sm:text-base p-1 sm:p-0"
                title="Send internal message"
              >
                💬
              </button>
              {node.telegram_username && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    contactUser(node);
                  }}
                  className="text-blue-400 hover:text-blue-300 text-lg sm:text-base p-1 sm:p-0"
                  title="Contact via Telegram"
                >
                  ✈️
                </button>
              )}
            </div>
            <span className="text-xs text-gray-500 ml-2">
              {new Date(node.created_at).toLocaleDateString()}
            </span>
          </div>
        </div>

        {/* Children */}
        {hasChildren && isExpanded && (
          <div className="ml-4 sm:ml-6 border-l border-gray-700 pl-2 sm:pl-4 mt-2">
            {node.children.map((child, index) => {
              const isLastChild = index === node.children.length - 1;
              const childPrefix = prefix + (isLast ? '  ' : '│ ');
              return renderTreeNode(child, isLastChild, childPrefix);
            })}
          </div>
        )}
      </div>
    );
  };



  if (loading) {
    return (
      <div className={`${className} text-center p-8`}>
        <div className="text-2xl mb-4">🌳</div>
        <div className="text-gray-400">Loading your downline...</div>
      </div>
    );
  }

  if (!treeData) {
    return (
      <div className={`${className} text-center p-8`}>
        <div className="text-4xl mb-4">🌱</div>
        <h3 className="text-white text-xl mb-4">No Downline Yet</h3>
        <p className="text-gray-400 mb-6">
          Start building your network by sharing your referral link!
        </p>
      </div>
    );
  }



  return (
    <div className={className}>
      {/* Header Controls */}
      <div className="mb-6">
        <div className="mb-4">
          <h2 className="text-white text-xl sm:text-2xl font-bold">🌳 Your Downline</h2>
          <p className="text-gray-400 text-sm sm:text-base">Manage and view your referral network</p>
        </div>

        {/* Mobile-friendly controls */}
        <div className="space-y-4">
          {/* Level Filter */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-2">
            <label className="text-sm text-gray-300 font-medium">View Levels:</label>
            <select
              value={maxDepth}
              onChange={(e) => setMaxDepth(Number(e.target.value))}
              className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm w-full sm:w-auto"
            >
              <option value={2}>2 Levels</option>
              <option value={3}>3 Levels</option>
              <option value={5}>5 Levels</option>
              <option value={10}>All Levels</option>
            </select>
          </div>

          {/* Active Earners Filter */}
          <div className="flex items-center">
            <label className="flex items-center space-x-2 text-sm text-yellow-400 font-medium" title="Show only users who have earned commissions in the entire downline tree">
              <input
                type="checkbox"
                checked={activeEarnersFilterEnabled}
                onChange={(e) => setActiveEarnersFilterEnabled(e.target.checked)}
                className="rounded"
              />
              <span className="text-xs sm:text-sm">🎯 Active Earners Filter</span>
            </label>
          </div>

          {/* User Category Filters */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
            <label className="flex items-center space-x-2 text-sm text-gray-300">
              <input
                type="checkbox"
                checked={showShareholders}
                onChange={(e) => setShowShareholders(e.target.checked)}
                className="rounded"
              />
              <span className="text-xs sm:text-sm">📈 Shareholders</span>
            </label>
            <label className="flex items-center space-x-2 text-sm text-gray-300">
              <input
                type="checkbox"
                checked={showActiveEarners}
                onChange={(e) => setShowActiveEarners(e.target.checked)}
                className="rounded"
              />
              <span className="text-xs sm:text-sm">💰 Active Earners</span>
            </label>
            <label className="flex items-center space-x-2 text-sm text-gray-300">
              <input
                type="checkbox"
                checked={showNonParticipants}
                onChange={(e) => setShowNonParticipants(e.target.checked)}
                className="rounded"
              />
              <span className="text-xs sm:text-sm">👤 Non-Participants</span>
            </label>
          </div>
        </div>
      </div>

      {/* Statistics */}
      {treeStats && (
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-6">
          <div className="bg-gray-800 rounded-lg p-3 sm:p-4 text-center border border-gray-700">
            <div className="text-xl sm:text-2xl font-bold text-blue-400">{treeStats.totalNodes}</div>
            <div className="text-xs sm:text-sm text-gray-400">Total Members</div>
          </div>
          <div className="bg-gray-800 rounded-lg p-3 sm:p-4 text-center border border-gray-700">
            <div className="text-xl sm:text-2xl font-bold text-green-400">{treeStats.activeNodes}</div>
            <div className="text-xs sm:text-sm text-gray-400">Active Members</div>
          </div>
          <div className="bg-gray-800 rounded-lg p-3 sm:p-4 text-center border border-gray-700">
            {activeEarnersFilterEnabled ? (
              <>
                <div className="text-xl sm:text-2xl font-bold text-yellow-400">{treeStats.activeEarners}</div>
                <div className="text-xs sm:text-sm text-gray-400">Active Earners</div>
              </>
            ) : (
              <>
                <div className="text-xl sm:text-2xl font-bold text-purple-400">{treeStats.totalLevels}</div>
                <div className="text-xs sm:text-sm text-gray-400">Max Depth</div>
              </>
            )}
          </div>
          <div className="bg-gray-800 rounded-lg p-3 sm:p-4 text-center border border-gray-700 col-span-2 lg:col-span-1">
            <div className="text-sm sm:text-lg font-bold text-green-400">${(treeStats.totalUsdtEarnings || 0).toFixed(2)} USDT</div>
            <div className="text-sm sm:text-lg font-bold text-yellow-400">{(treeStats.totalShareEarnings || 0).toFixed(2)} Shares</div>
            <div className="text-xs sm:text-sm text-gray-400">Total Earnings</div>
          </div>
        </div>
      )}

      {/* Tree View */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-3 sm:p-6 overflow-x-auto">
        <div className="space-y-2 min-w-0">
          {renderTreeNode(treeData, true)}
        </div>
      </div>

      {/* Selected Node Details */}
      {selectedNode && (
        <div className="mt-6 bg-gray-800 rounded-lg border border-gray-700 p-4 sm:p-6">
          <h3 className="text-white text-lg font-semibold mb-4">👤 Member Details</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="text-sm text-gray-400">Name</label>
              <div className="text-white font-medium">{selectedNode.first_name} {selectedNode.last_name}</div>
            </div>
            <div>
              <label className="text-sm text-gray-400">Username</label>
              <div className="text-white">@{selectedNode.username}</div>
            </div>
            <div>
              <label className="text-sm text-gray-400">Level</label>
              <div className="text-white">{selectedNode.level}</div>
            </div>
            <div>
              <label className="text-sm text-gray-400">Status</label>
              <div className={selectedNode.is_active ? 'text-green-400' : 'text-red-400'}>
                {selectedNode.is_active ? 'Active' : 'Inactive'}
              </div>
            </div>
            <div>
              <label className="text-sm text-gray-400">Total Referrals</label>
              <div className="text-white">{selectedNode.total_referrals}</div>
            </div>
            <div>
              <label className="text-sm text-gray-400">USDT Earnings (Withdrawable)</label>
              <div className="text-white">${(selectedNode.usdt_earnings || 0).toFixed(2)} USDT</div>
            </div>
            <div>
              <label className="text-sm text-gray-400">Commission Shares (Earned)</label>
              <div className="text-white">{(selectedNode.share_earnings || 0).toFixed(2)} shares</div>
            </div>
            <div className="sm:col-span-2">
              <label className="text-sm text-gray-400">Purchased Shares (Personal Investment)</label>
              <div className="text-white">{selectedNode.purchased_shares || 0} shares (${(selectedNode.total_invested || 0).toFixed(2)} invested)</div>
            </div>
            <div>
              <label className="text-sm text-gray-400">Joined</label>
              <div className="text-white">{new Date(selectedNode.created_at).toLocaleDateString()}</div>
            </div>
            <div>
              <label className="text-sm text-gray-400">Contact</label>
              <div className="flex flex-col sm:flex-row gap-2 mt-1">
                <button
                  onClick={() => openMessageModal(selectedNode)}
                  className="text-green-400 hover:text-green-300 bg-green-400/10 hover:bg-green-400/20 px-3 py-2 rounded-lg text-sm transition-colors"
                >
                  💬 Send Message
                </button>
                {selectedNode.telegram_username && (
                  <button
                    onClick={() => window.open(`https://t.me/${selectedNode.telegram_username}`)}
                    className="text-blue-400 hover:text-blue-300 bg-blue-400/10 hover:bg-blue-400/20 px-3 py-2 rounded-lg text-sm transition-colors"
                  >
                    ✈️ Telegram
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Send Message Modal */}
      {messageRecipient && (
        <SendMessageModal
          isOpen={messageModalOpen}
          onClose={() => {
            setMessageModalOpen(false);
            setMessageRecipient(null);
          }}
          recipientId={messageRecipient.id}
          recipientName={`${messageRecipient.first_name} ${messageRecipient.last_name} (@${messageRecipient.username})`}
          senderId={userId}
        />
      )}
    </div>
  );
};
