#!/usr/bin/env node

/**
 * COMPLETE FORGOT PASSWORD FLOW TEST
 * 
 * This script tests the complete forgot password flow:
 * 1. User clicks "Forgot your password?" on login page
 * 2. System creates reset token and sends email
 * 3. User clicks reset link from email
 * 4. User resets password successfully
 * 5. User can login with new password
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testCompleteForgotPasswordFlow() {
  console.log('🧪 Testing complete forgot password flow...\n');

  try {
    const testEmail = '<EMAIL>';
    const originalPassword = 'TestPassword123!';
    const newPassword = 'NewSecurePassword456!';
    
    console.log(`📧 Testing with email: ${testEmail}`);
    
    // Step 1: Ensure user has a known password for testing
    console.log('\n1️⃣ Setting up test user with known password...');
    
    const bcrypt = require('bcryptjs');
    const hashedOriginalPassword = await bcrypt.hash(originalPassword, 12);
    
    const { error: setupError } = await supabase
      .from('users')
      .update({
        password_hash: hashedOriginalPassword,
        reset_token: null,
        reset_token_expires: null,
        updated_at: new Date().toISOString()
      })
      .eq('email', testEmail);
    
    if (setupError) {
      console.error('❌ Failed to setup test user:', setupError);
      return false;
    }
    
    console.log('✅ Test user setup with original password');
    
    // Step 2: Simulate forgot password request
    console.log('\n2️⃣ Simulating forgot password request...');
    
    const resetToken = require('crypto').randomBytes(32).toString('hex');
    const resetExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
    
    const { error: resetTokenError } = await supabase
      .from('users')
      .update({
        reset_token: resetToken,
        reset_token_expires: resetExpires.toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('email', testEmail);
    
    if (resetTokenError) {
      console.error('❌ Failed to create reset token:', resetTokenError);
      return false;
    }
    
    console.log('✅ Reset token created successfully');
    console.log('🔗 Reset link would be: http://localhost:8006/reset-password?token=' + resetToken.substring(0, 16) + '...');
    
    // Step 3: Validate reset token
    console.log('\n3️⃣ Validating reset token...');
    
    const { data: tokenUser, error: tokenError } = await supabase
      .from('users')
      .select('id, email, reset_token, reset_token_expires')
      .eq('reset_token', resetToken)
      .single();
    
    if (tokenError || !tokenUser) {
      console.error('❌ Token validation failed:', tokenError);
      return false;
    }
    
    const now = new Date();
    const expiresAt = new Date(tokenUser.reset_token_expires);
    const isValid = now <= expiresAt;
    
    if (!isValid) {
      console.error('❌ Token has expired');
      return false;
    }
    
    console.log('✅ Reset token is valid');
    
    // Step 4: Simulate password reset
    console.log('\n4️⃣ Simulating password reset with new password...');
    
    const hashedNewPassword = await bcrypt.hash(newPassword, 12);
    
    const { error: passwordUpdateError } = await supabase
      .from('users')
      .update({
        password_hash: hashedNewPassword,
        reset_token: null,
        reset_token_expires: null,
        updated_at: new Date().toISOString()
      })
      .eq('id', tokenUser.id);
    
    if (passwordUpdateError) {
      console.error('❌ Failed to update password:', passwordUpdateError);
      return false;
    }
    
    console.log('✅ Password updated successfully');
    console.log('🧹 Reset token cleared from database');
    
    // Step 5: Verify old password no longer works
    console.log('\n5️⃣ Verifying old password no longer works...');
    
    const { data: updatedUser } = await supabase
      .from('users')
      .select('password_hash')
      .eq('email', testEmail)
      .single();
    
    const oldPasswordWorks = await bcrypt.compare(originalPassword, updatedUser.password_hash);
    const newPasswordWorks = await bcrypt.compare(newPassword, updatedUser.password_hash);
    
    console.log('🔐 Password verification:', {
      oldPasswordWorks: oldPasswordWorks,
      newPasswordWorks: newPasswordWorks
    });
    
    if (oldPasswordWorks) {
      console.error('❌ Old password still works - password was not updated');
      return false;
    }
    
    if (!newPasswordWorks) {
      console.error('❌ New password does not work - password update failed');
      return false;
    }
    
    console.log('✅ Password reset completed successfully');
    
    // Step 6: Verify reset token was cleared
    console.log('\n6️⃣ Verifying reset token was cleared...');
    
    const { data: finalUser } = await supabase
      .from('users')
      .select('reset_token, reset_token_expires')
      .eq('email', testEmail)
      .single();
    
    if (finalUser.reset_token !== null || finalUser.reset_token_expires !== null) {
      console.error('❌ Reset token was not properly cleared');
      return false;
    }
    
    console.log('✅ Reset token properly cleared');
    
    console.log('\n🎉 Complete forgot password flow test PASSED!');
    console.log('📋 Flow Summary:');
    console.log('   ✅ User setup with original password');
    console.log('   ✅ Reset token created and stored');
    console.log('   ✅ Reset token validation working');
    console.log('   ✅ Password updated with new password');
    console.log('   ✅ Old password no longer works');
    console.log('   ✅ New password works correctly');
    console.log('   ✅ Reset token properly cleared');
    
    console.log('\n🔗 Manual Testing Instructions:');
    console.log('1. Go to: http://localhost:8006/login');
    console.log('2. Click "Login (Web)" tab');
    console.log(`3. Enter email: ${testEmail}`);
    console.log('4. Click "Forgot your password?"');
    console.log('5. Check email for reset link');
    console.log('6. Click reset link and set new password');
    console.log('7. Login with new password');
    
    return true;
    
  } catch (error) {
    console.error('❌ Complete forgot password flow test failed:', error);
    return false;
  }
}

// Run the test
testCompleteForgotPasswordFlow().then(success => {
  console.log(`\n${success ? '🎉' : '💥'} Test ${success ? 'PASSED' : 'FAILED'}`);
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ Test execution failed:', error);
  process.exit(1);
});
