
// TELEGRAM SESSION CONNECTION FIX
// Add this to your dashboard component or run in browser console

const fixTelegramConnection = () => {
  console.log('🔧 Fixing Telegram session connection...');
  
  // Complete session data with Telegram connection
  const sessionData = {
  "userId": 4,
  "username": "TTTFOUNDER",
  "email": "<EMAIL>",
  "fullName": "<PERSON>demeyer",
  "phone": "+27783699799",
  "address": null,
  "country": "ZAF",
  "isActive": true,
  "isVerified": true,
  "isAdmin": true,
  "telegramId": 1393852532,
  "telegramUsername": "TTTFOUNDER",
  "telegramConnected": true,
  "telegramRegistered": true,
  "loginMethod": "telegram",
  "sessionStart": "2025-07-28T10:10:06.411Z",
  "lastActivity": "2025-07-28T10:10:06.412Z"
};
  
  // Store in localStorage
  localStorage.setItem('aureus_session', JSON.stringify(sessionData));
  localStorage.setItem('aureus_user', JSON.stringify({
    id: 4,
    username: 'TTTFOUNDER',
    email: '<EMAIL>',
    full_name: 'JP Rademeyer',
    phone: '+27783699799',
    address: '',
    country_of_residence: 'ZAF',
    is_active: true,
    is_verified: true,
    is_admin: true,
    telegram_id: '1393852532',
    telegram_username: 'TTTFOUNDER',
    telegram_connected: true
  }));
  
  // Also store Telegram-specific data
  localStorage.setItem('telegram_user', JSON.stringify({
    telegram_id: '1393852532',
    username: 'TTTFOUNDER',
    user_id: 4,
    is_registered: true,
    connected: true
  }));
  
  console.log('✅ Telegram connection data stored');
  console.log('🔄 Refreshing page to apply changes...');
  
  // Refresh the page to apply changes
  window.location.reload();
};

// Run the fix
fixTelegramConnection();
