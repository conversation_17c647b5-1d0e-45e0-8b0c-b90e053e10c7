// Quick script to check investment phases in Supabase
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://ixqjqfqjqjqjqjqjqjqj.supabase.co';
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'your-anon-key';

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkPhases() {
  console.log('🔍 Checking investment phases...\n');
  
  try {
    // Get all phases
    const { data: phases, error } = await supabase
      .from('investment_phases')
      .select('*')
      .order('phase_number');

    if (error) {
      console.error('❌ Error fetching phases:', error);
      return;
    }

    if (!phases || phases.length === 0) {
      console.log('⚠️ No phases found in database');
      return;
    }

    console.log('📊 Investment Phases:');
    console.log('='.repeat(80));
    
    phases.forEach(phase => {
      const remaining = phase.total_shares_available - phase.shares_sold;
      const percentSold = ((phase.shares_sold / phase.total_shares_available) * 100).toFixed(1);
      
      console.log(`Phase ${phase.phase_number}: ${phase.phase_name}`);
      console.log(`  💰 Price: $${phase.price_per_share}/share`);
      console.log(`  📈 Total Available: ${phase.total_shares_available.toLocaleString()} shares`);
      console.log(`  ✅ Sold: ${phase.shares_sold.toLocaleString()} shares (${percentSold}%)`);
      console.log(`  🔄 Remaining: ${remaining.toLocaleString()} shares`);
      console.log(`  🎯 Active: ${phase.is_active ? 'YES' : 'NO'}`);
      console.log('-'.repeat(40));
    });

    // Check for active phase
    const activePhase = phases.find(p => p.is_active);
    if (activePhase) {
      console.log(`\n🎯 CURRENT ACTIVE PHASE: ${activePhase.phase_name}`);
      console.log(`   Price: $${activePhase.price_per_share}/share`);
      console.log(`   Remaining: ${(activePhase.total_shares_available - activePhase.shares_sold).toLocaleString()} shares`);
    } else {
      console.log('\n⚠️ No active phase found!');
      console.log('💡 Suggestion: Set phase 0 (Pre Sale) as active');
    }

  } catch (err) {
    console.error('❌ Error:', err);
  }
}

checkPhases();
