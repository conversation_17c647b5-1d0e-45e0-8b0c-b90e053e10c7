/**
 * QUIZ BUILDER
 * 
 * Interactive quiz and assessment creation tool:
 * - Multiple question types (multiple choice, true/false, short answer, drag-drop)
 * - Question management with drag & drop reordering
 * - Answer validation and scoring
 * - Time limits and attempt restrictions
 * - Detailed feedback and explanations
 * - Preview and testing functionality
 */

import React, { useState, useEffect } from 'react';
import { getServiceRoleClient } from '../../lib/supabase';

interface QuizBuilderProps {
  lessonId: number;
  existingAssessment?: Assessment;
  onSave: (assessment: Assessment) => void;
  onCancel: () => void;
}

interface Assessment {
  id?: number;
  lesson_id: number;
  title: string;
  description: string;
  passing_score: number;
  max_attempts: number;
  time_limit?: number;
  is_required: boolean;
  questions: Question[];
}

interface Question {
  id?: number;
  question_text: string;
  question_type: 'multiple_choice' | 'true_false' | 'short_answer' | 'drag_drop';
  correct_answer: string;
  explanation?: string;
  points: number;
  sort_order: number;
  options?: QuestionOption[];
}

interface QuestionOption {
  id?: number;
  option_text: string;
  is_correct: boolean;
  sort_order: number;
}

export const QuizBuilder: React.FC<QuizBuilderProps> = ({
  lessonId,
  existingAssessment,
  onSave,
  onCancel
}) => {
  const [assessment, setAssessment] = useState<Assessment>({
    lesson_id: lessonId,
    title: '',
    description: '',
    passing_score: 70,
    max_attempts: 0,
    time_limit: undefined,
    is_required: true,
    questions: []
  });
  const [activeQuestionIndex, setActiveQuestionIndex] = useState<number | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  useEffect(() => {
    if (existingAssessment) {
      setAssessment(existingAssessment);
    }
  }, [existingAssessment]);

  const addQuestion = (type: Question['question_type']) => {
    const newQuestion: Question = {
      question_text: '',
      question_type: type,
      correct_answer: '',
      explanation: '',
      points: 1,
      sort_order: assessment.questions.length,
      options: type === 'multiple_choice' ? [
        { option_text: '', is_correct: true, sort_order: 0 },
        { option_text: '', is_correct: false, sort_order: 1 },
        { option_text: '', is_correct: false, sort_order: 2 },
        { option_text: '', is_correct: false, sort_order: 3 }
      ] : type === 'true_false' ? [
        { option_text: 'True', is_correct: true, sort_order: 0 },
        { option_text: 'False', is_correct: false, sort_order: 1 }
      ] : undefined
    };

    setAssessment(prev => ({
      ...prev,
      questions: [...prev.questions, newQuestion]
    }));
    setActiveQuestionIndex(assessment.questions.length);
  };

  const updateQuestion = (index: number, updates: Partial<Question>) => {
    setAssessment(prev => ({
      ...prev,
      questions: prev.questions.map((q, i) => 
        i === index ? { ...q, ...updates } : q
      )
    }));
  };

  const deleteQuestion = (index: number) => {
    setAssessment(prev => ({
      ...prev,
      questions: prev.questions.filter((_, i) => i !== index)
        .map((q, i) => ({ ...q, sort_order: i }))
    }));
    setActiveQuestionIndex(null);
  };

  const moveQuestion = (fromIndex: number, toIndex: number) => {
    const questions = [...assessment.questions];
    const [movedQuestion] = questions.splice(fromIndex, 1);
    questions.splice(toIndex, 0, movedQuestion);
    
    // Update sort orders
    const updatedQuestions = questions.map((q, i) => ({ ...q, sort_order: i }));
    
    setAssessment(prev => ({ ...prev, questions: updatedQuestions }));
  };

  const updateQuestionOption = (questionIndex: number, optionIndex: number, updates: Partial<QuestionOption>) => {
    setAssessment(prev => ({
      ...prev,
      questions: prev.questions.map((q, i) => 
        i === questionIndex ? {
          ...q,
          options: q.options?.map((opt, j) => 
            j === optionIndex ? { ...opt, ...updates } : opt
          )
        } : q
      )
    }));
  };

  const addQuestionOption = (questionIndex: number) => {
    const question = assessment.questions[questionIndex];
    if (!question.options) return;

    const newOption: QuestionOption = {
      option_text: '',
      is_correct: false,
      sort_order: question.options.length
    };

    updateQuestion(questionIndex, {
      options: [...question.options, newOption]
    });
  };

  const removeQuestionOption = (questionIndex: number, optionIndex: number) => {
    const question = assessment.questions[questionIndex];
    if (!question.options || question.options.length <= 2) return;

    updateQuestion(questionIndex, {
      options: question.options.filter((_, i) => i !== optionIndex)
        .map((opt, i) => ({ ...opt, sort_order: i }))
    });
  };

  const handleSave = async () => {
    try {
      // Validate assessment
      if (!assessment.title.trim()) {
        alert('Please enter an assessment title.');
        return;
      }

      if (assessment.questions.length === 0) {
        alert('Please add at least one question.');
        return;
      }

      // Validate questions
      for (let i = 0; i < assessment.questions.length; i++) {
        const question = assessment.questions[i];
        if (!question.question_text.trim()) {
          alert(`Please enter text for question ${i + 1}.`);
          return;
        }

        if (question.question_type === 'multiple_choice' && question.options) {
          const hasCorrectAnswer = question.options.some(opt => opt.is_correct);
          if (!hasCorrectAnswer) {
            alert(`Please mark the correct answer for question ${i + 1}.`);
            return;
          }
        }
      }

      onSave(assessment);
    } catch (error) {
      console.error('Error saving assessment:', error);
      alert('Failed to save assessment. Please try again.');
    }
  };

  const renderQuestionEditor = (question: Question, index: number) => (
    <div className="bg-gray-700 rounded-lg p-6 space-y-4">
      {/* Question Header */}
      <div className="flex items-center justify-between">
        <h4 className="text-white font-medium">Question {index + 1}</h4>
        <div className="flex items-center space-x-2">
          <select
            value={question.question_type}
            onChange={(e) => updateQuestion(index, { 
              question_type: e.target.value as Question['question_type'],
              options: e.target.value === 'multiple_choice' ? [
                { option_text: '', is_correct: true, sort_order: 0 },
                { option_text: '', is_correct: false, sort_order: 1 }
              ] : e.target.value === 'true_false' ? [
                { option_text: 'True', is_correct: true, sort_order: 0 },
                { option_text: 'False', is_correct: false, sort_order: 1 }
              ] : undefined
            })}
            className="bg-gray-600 text-white px-3 py-1 rounded text-sm"
          >
            <option value="multiple_choice">Multiple Choice</option>
            <option value="true_false">True/False</option>
            <option value="short_answer">Short Answer</option>
            <option value="drag_drop">Drag & Drop</option>
          </select>
          <button
            onClick={() => deleteQuestion(index)}
            className="text-red-400 hover:text-red-300 px-2 py-1"
          >
            🗑️
          </button>
        </div>
      </div>

      {/* Question Text */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Question Text *
        </label>
        <textarea
          value={question.question_text}
          onChange={(e) => updateQuestion(index, { question_text: e.target.value })}
          className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Enter your question..."
          rows={3}
          required
        />
      </div>

      {/* Question Options */}
      {(question.question_type === 'multiple_choice' || question.question_type === 'true_false') && question.options && (
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Answer Options
          </label>
          <div className="space-y-2">
            {question.options.map((option, optionIndex) => (
              <div key={optionIndex} className="flex items-center space-x-3">
                <input
                  type={question.question_type === 'multiple_choice' ? 'radio' : 'radio'}
                  name={`question-${index}-correct`}
                  checked={option.is_correct}
                  onChange={() => {
                    // For multiple choice, only one can be correct
                    if (question.question_type === 'multiple_choice') {
                      updateQuestion(index, {
                        options: question.options?.map((opt, i) => ({
                          ...opt,
                          is_correct: i === optionIndex
                        }))
                      });
                    } else {
                      updateQuestionOption(index, optionIndex, { is_correct: !option.is_correct });
                    }
                  }}
                  className="text-blue-500"
                />
                <input
                  type="text"
                  value={option.option_text}
                  onChange={(e) => updateQuestionOption(index, optionIndex, { option_text: e.target.value })}
                  className="flex-1 px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder={`Option ${optionIndex + 1}`}
                  disabled={question.question_type === 'true_false'}
                />
                {question.question_type === 'multiple_choice' && question.options && question.options.length > 2 && (
                  <button
                    onClick={() => removeQuestionOption(index, optionIndex)}
                    className="text-red-400 hover:text-red-300 px-2 py-1"
                  >
                    ×
                  </button>
                )}
              </div>
            ))}
            {question.question_type === 'multiple_choice' && question.options && question.options.length < 6 && (
              <button
                onClick={() => addQuestionOption(index)}
                className="text-blue-400 hover:text-blue-300 text-sm"
              >
                + Add Option
              </button>
            )}
          </div>
        </div>
      )}

      {/* Short Answer */}
      {question.question_type === 'short_answer' && (
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Correct Answer *
          </label>
          <input
            type="text"
            value={question.correct_answer}
            onChange={(e) => updateQuestion(index, { correct_answer: e.target.value })}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Enter the correct answer..."
            required
          />
        </div>
      )}

      {/* Points and Explanation */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Points
          </label>
          <input
            type="number"
            value={question.points}
            onChange={(e) => updateQuestion(index, { points: parseInt(e.target.value) || 1 })}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            min={1}
            max={10}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Explanation (Optional)
          </label>
          <input
            type="text"
            value={question.explanation || ''}
            onChange={(e) => updateQuestion(index, { explanation: e.target.value })}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Explain the correct answer..."
          />
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Assessment Settings */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">📊 Assessment Settings</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Assessment Title *
            </label>
            <input
              type="text"
              value={assessment.title}
              onChange={(e) => setAssessment(prev => ({ ...prev, title: e.target.value }))}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter assessment title..."
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Passing Score (%)
            </label>
            <input
              type="number"
              value={assessment.passing_score}
              onChange={(e) => setAssessment(prev => ({ ...prev, passing_score: parseInt(e.target.value) || 70 }))}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              min={0}
              max={100}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Max Attempts (0 = unlimited)
            </label>
            <input
              type="number"
              value={assessment.max_attempts}
              onChange={(e) => setAssessment(prev => ({ ...prev, max_attempts: parseInt(e.target.value) || 0 }))}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              min={0}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Time Limit (minutes, optional)
            </label>
            <input
              type="number"
              value={assessment.time_limit || ''}
              onChange={(e) => setAssessment(prev => ({ ...prev, time_limit: e.target.value ? parseInt(e.target.value) : undefined }))}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="No time limit"
              min={1}
            />
          </div>
        </div>

        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Description
          </label>
          <textarea
            value={assessment.description}
            onChange={(e) => setAssessment(prev => ({ ...prev, description: e.target.value }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Describe this assessment..."
            rows={3}
          />
        </div>

        <div className="mt-4 flex items-center">
          <input
            type="checkbox"
            id="is_required"
            checked={assessment.is_required}
            onChange={(e) => setAssessment(prev => ({ ...prev, is_required: e.target.checked }))}
            className="mr-2"
          />
          <label htmlFor="is_required" className="text-sm text-gray-300">
            This assessment is required to complete the lesson
          </label>
        </div>
      </div>

      {/* Add Question Buttons */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">➕ Add Questions</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <button
            onClick={() => addQuestion('multiple_choice')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg transition-colors text-sm"
          >
            📝 Multiple Choice
          </button>
          <button
            onClick={() => addQuestion('true_false')}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded-lg transition-colors text-sm"
          >
            ✅ True/False
          </button>
          <button
            onClick={() => addQuestion('short_answer')}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-lg transition-colors text-sm"
          >
            ✏️ Short Answer
          </button>
          <button
            onClick={() => addQuestion('drag_drop')}
            className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-3 rounded-lg transition-colors text-sm"
          >
            🔄 Drag & Drop
          </button>
        </div>
      </div>

      {/* Questions List */}
      {assessment.questions.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-white">📋 Questions ({assessment.questions.length})</h3>
          {assessment.questions.map((question, index) => (
            <div key={index}>
              {renderQuestionEditor(question, index)}
            </div>
          ))}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-between items-center pt-6 border-t border-gray-700">
        <button
          onClick={onCancel}
          className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>

        <div className="flex space-x-3">
          {assessment.questions.length > 0 && (
            <button
              onClick={() => setShowPreview(true)}
              className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              👁️ Preview
            </button>
          )}
          <button
            onClick={handleSave}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            💾 Save Assessment
          </button>
        </div>
      </div>
    </div>
  );
};
