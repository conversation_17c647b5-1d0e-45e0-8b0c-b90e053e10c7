/**
 * SUPPORT TICKET NOTIFICATION SERVICE
 * 
 * Handles email notifications for support ticket workflow:
 * - New ticket notifications to support team
 * - Response notifications to users
 */

import { Resend } from 'resend';
import { 
  NewTicketNotificationTemplate, 
  TicketResponseNotificationTemplate,
  NewTicketNotificationData,
  TicketResponseNotificationData 
} from '../email/templates/SupportTicketEmailTemplate';
import { getServiceRoleClient } from '../supabase';

// Environment configuration
const RESEND_API_KEY = import.meta.env.VITE_RESEND_API_KEY || process.env.RESEND_API_KEY;
const RESEND_FROM_EMAIL = import.meta.env.VITE_RESEND_FROM_EMAIL || process.env.RESEND_FROM_EMAIL || '<EMAIL>';
const RESEND_FROM_NAME = import.meta.env.VITE_RESEND_FROM_NAME || process.env.RESEND_FROM_NAME || 'Aureus Alliance Holdings';
const SUPPORT_EMAIL = '<EMAIL>';
const WEBSITE_URL = 'https://aureus.africa';

// Initialize Resend client
let resend: Resend | null = null;

try {
  if (RESEND_API_KEY) {
    resend = new Resend(RESEND_API_KEY);
    console.log('✅ Support ticket notification service initialized');
  } else {
    console.warn('⚠️ RESEND_API_KEY not found - support ticket notifications disabled');
  }
} catch (error) {
  console.error('❌ Failed to initialize support ticket notification service:', error);
}

// Initialize email templates
const newTicketTemplate = new NewTicketNotificationTemplate();
const responseTemplate = new TicketResponseNotificationTemplate();

export interface SupportTicketNotificationResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

/**
 * Send new ticket notification to support team
 */
export async function sendNewTicketNotification(
  ticketData: {
    id: string;
    ticket_number: string;
    user_id: number;
    title: string;
    description: string;
    priority: 'low' | 'medium' | 'high' | 'urgent';
    category: string;
    user_type: 'shareholder' | 'affiliate';
  }
): Promise<SupportTicketNotificationResult> {
  if (!resend) {
    console.warn('⚠️ Email service not available - skipping new ticket notification');
    return { success: false, error: 'Email service not configured' };
  }

  try {
    console.log('📧 Sending new ticket notification for:', ticketData.ticket_number);

    // Get user information
    const serviceClient = getServiceRoleClient();
    const { data: user, error: userError } = await serviceClient
      .from('users')
      .select('id, username, full_name, email')
      .eq('id', ticketData.user_id)
      .single();

    if (userError || !user) {
      console.error('❌ Error fetching user for ticket notification:', userError);
      return { success: false, error: 'User not found' };
    }

    // Prepare email data
    const emailData: NewTicketNotificationData = {
      ticketNumber: ticketData.ticket_number,
      userInfo: {
        name: user.full_name || user.username || 'Unknown User',
        email: user.email,
        userType: ticketData.user_type,
        userId: user.id
      },
      ticket: {
        title: ticketData.title,
        description: ticketData.description,
        priority: ticketData.priority,
        category: ticketData.category
      },
      adminDashboardUrl: `${WEBSITE_URL}/admin/support/tickets/${ticketData.id}`
    };

    // Generate email content
    const template = newTicketTemplate.generateTemplate(emailData);
    const subject = template.subject;
    const htmlContent = template.htmlContent;
    const textContent = template.textContent;

    // Send email to support team
    const emailResult = await resend.emails.send({
      from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
      to: [SUPPORT_EMAIL],
      subject: subject,
      html: htmlContent,
      text: textContent,
      tags: [
        { name: 'category', value: 'support_ticket' },
        { name: 'type', value: 'new_ticket' },
        { name: 'priority', value: ticketData.priority },
        { name: 'user_type', value: ticketData.user_type }
      ]
    });

    console.log('✅ New ticket notification sent successfully:', emailResult.id);

    // Log the notification
    await logNotification({
      ticket_id: ticketData.id,
      notification_type: 'new_ticket_to_support',
      recipient_email: SUPPORT_EMAIL,
      email_id: emailResult.id,
      status: 'sent'
    });

    return { success: true, messageId: emailResult.id };

  } catch (error) {
    console.error('❌ Error sending new ticket notification:', error);
    
    // Log the failed notification
    await logNotification({
      ticket_id: ticketData.id,
      notification_type: 'new_ticket_to_support',
      recipient_email: SUPPORT_EMAIL,
      status: 'failed',
      error_message: error instanceof Error ? error.message : 'Unknown error'
    });

    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Send ticket response notification to user
 */
export async function sendTicketResponseNotification(
  ticketId: string,
  responseData: {
    statusUpdate?: string;
    responseMessage?: string;
    agentId?: string;
  }
): Promise<SupportTicketNotificationResult> {
  if (!resend) {
    console.warn('⚠️ Email service not available - skipping response notification');
    return { success: false, error: 'Email service not configured' };
  }

  try {
    console.log('📧 Sending ticket response notification for ticket:', ticketId);

    const serviceClient = getServiceRoleClient();

    // Get ticket and user information
    const { data: ticket, error: ticketError } = await serviceClient
      .from('support_tickets')
      .select(`
        id,
        ticket_number,
        title,
        user_id,
        users!inner(id, username, full_name, email)
      `)
      .eq('id', ticketId)
      .single();

    if (ticketError || !ticket) {
      console.error('❌ Error fetching ticket for response notification:', ticketError);
      return { success: false, error: 'Ticket not found' };
    }

    // Get agent information if provided
    let agentInfo = undefined;
    if (responseData.agentId) {
      const { data: agent } = await serviceClient
        .from('support_agents')
        .select('name, email')
        .eq('id', responseData.agentId)
        .single();
      
      if (agent) {
        agentInfo = { name: agent.name, email: agent.email };
      }
    }

    // Prepare email data
    const emailData: TicketResponseNotificationData = {
      ticketNumber: ticket.ticket_number,
      userInfo: {
        name: ticket.users.full_name || ticket.users.username || 'User',
        email: ticket.users.email
      },
      ticket: {
        title: ticket.title,
        statusUpdate: responseData.statusUpdate,
        responseMessage: responseData.responseMessage
      },
      agentInfo: agentInfo,
      dashboardUrl: `${WEBSITE_URL}/dashboard?section=messages&ticket=${ticket.id}`
    };

    // Generate email content
    const template = responseTemplate.generateTemplate(emailData);
    const subject = template.subject;
    const htmlContent = template.htmlContent;
    const textContent = template.textContent;

    // Send email to user
    const emailResult = await resend.emails.send({
      from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
      to: [ticket.users.email],
      subject: subject,
      html: htmlContent,
      text: textContent,
      tags: [
        { name: 'category', value: 'support_ticket' },
        { name: 'type', value: 'response_to_user' },
        { name: 'ticket_id', value: ticketId }
      ]
    });

    console.log('✅ Ticket response notification sent successfully:', emailResult.id);

    // Log the notification
    await logNotification({
      ticket_id: ticketId,
      notification_type: 'response_to_user',
      recipient_email: ticket.users.email,
      email_id: emailResult.id,
      status: 'sent'
    });

    return { success: true, messageId: emailResult.id };

  } catch (error) {
    console.error('❌ Error sending ticket response notification:', error);
    
    // Log the failed notification
    await logNotification({
      ticket_id: ticketId,
      notification_type: 'response_to_user',
      status: 'failed',
      error_message: error instanceof Error ? error.message : 'Unknown error'
    });

    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Log notification attempts for debugging and tracking
 */
async function logNotification(logData: {
  ticket_id: string;
  notification_type: string;
  recipient_email?: string;
  email_id?: string;
  status: 'sent' | 'failed';
  error_message?: string;
}): Promise<void> {
  try {
    const serviceClient = getServiceRoleClient();
    
    await serviceClient
      .from('support_ticket_notifications')
      .insert({
        ticket_id: logData.ticket_id,
        notification_type: logData.notification_type,
        recipient_email: logData.recipient_email,
        email_id: logData.email_id,
        status: logData.status,
        error_message: logData.error_message,
        sent_at: new Date().toISOString()
      });

    console.log('📝 Notification logged successfully');
  } catch (error) {
    console.error('❌ Error logging notification:', error);
    // Don't throw here - logging failure shouldn't break the main flow
  }
}

/**
 * Test email service configuration
 */
export async function testEmailService(): Promise<boolean> {
  if (!resend) {
    console.warn('⚠️ Email service not configured');
    return false;
  }

  try {
    // Send a test email to support
    await resend.emails.send({
      from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
      to: [SUPPORT_EMAIL],
      subject: '🧪 Support Ticket Email Service Test',
      html: '<p>This is a test email to verify the support ticket notification service is working correctly.</p>',
      text: 'This is a test email to verify the support ticket notification service is working correctly.',
      tags: [{ name: 'category', value: 'test' }]
    });

    console.log('✅ Email service test successful');
    return true;
  } catch (error) {
    console.error('❌ Email service test failed:', error);
    return false;
  }
}
