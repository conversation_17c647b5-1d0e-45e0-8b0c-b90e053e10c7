import React from 'react';

interface BookingSuccessModalProps {
  isOpen: boolean;
  confirmationCode: string;
  onClose: () => void;
}

const BookingSuccessModal: React.FC<BookingSuccessModalProps> = ({
  isOpen,
  confirmationCode,
  onClose
}) => {
  if (!isOpen) {
    return null;
  }

  const handleCopyConfirmation = () => {
    navigator.clipboard.writeText(confirmationCode).then(() => {
      // Could add a toast notification here
      console.log('Confirmation code copied to clipboard');
    });
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content booking-success-modal" onClick={(e) => e.stopPropagation()}>
        <div className="success-content">
          <div className="success-icon">
            <div className="checkmark">✓</div>
          </div>
          
          <h2 style={{ color: '#4CAF50', marginBottom: '15px', textAlign: 'center' }}>
            Booking Confirmed!
          </h2>
          
          <p style={{ color: '#ccc', textAlign: 'center', marginBottom: '25px', fontSize: '16px' }}>
            Your meeting spot has been successfully reserved.
          </p>

          <div className="confirmation-details">
            <div className="confirmation-code-section">
              <label style={{ color: '#aaa', fontSize: '14px', marginBottom: '8px', display: 'block' }}>
                Confirmation Code
              </label>
              <div className="confirmation-code-display">
                <span className="confirmation-code">{confirmationCode}</span>
                <button
                  onClick={handleCopyConfirmation}
                  className="copy-button"
                  title="Copy confirmation code"
                >
                  📋
                </button>
              </div>
            </div>

            <div className="next-steps">
              <h3 style={{ color: '#FFD700', marginBottom: '15px', fontSize: '18px' }}>
                What's Next?
              </h3>
              <ul className="steps-list">
                <li>
                  <span className="step-icon">📧</span>
                  <span>You'll receive a confirmation email with meeting details</span>
                </li>
                <li>
                  <span className="step-icon">📅</span>
                  <span>Add the meeting to your calendar</span>
                </li>
                <li>
                  <span className="step-icon">🔗</span>
                  <span>Join the meeting using the link provided in your email</span>
                </li>
                <li>
                  <span className="step-icon">💬</span>
                  <span>Prepare any questions you'd like to ask during the session</span>
                </li>
              </ul>
            </div>

            <div className="important-note">
              <div className="note-icon">💡</div>
              <div className="note-content">
                <strong>Important:</strong> Please save your confirmation code. You may need it to 
                access the meeting or make changes to your booking.
              </div>
            </div>
          </div>

          <div className="success-actions">
            <button
              onClick={onClose}
              className="close-success-btn"
            >
              Got it, thanks!
            </button>
          </div>
        </div>
      </div>

      <style jsx>{`
        .booking-success-modal {
          max-width: 500px;
          width: 90%;
          text-align: center;
        }

        .success-content {
          padding: 20px;
        }

        .success-icon {
          margin: 0 auto 25px;
          width: 80px;
          height: 80px;
          background: linear-gradient(135deg, #4CAF50, #45a049);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          animation: successPulse 0.6s ease-out;
        }

        .checkmark {
          color: white;
          font-size: 40px;
          font-weight: bold;
          animation: checkmarkAppear 0.4s ease-out 0.2s both;
        }

        @keyframes successPulse {
          0% {
            transform: scale(0);
            opacity: 0;
          }
          50% {
            transform: scale(1.1);
          }
          100% {
            transform: scale(1);
            opacity: 1;
          }
        }

        @keyframes checkmarkAppear {
          0% {
            transform: scale(0);
            opacity: 0;
          }
          100% {
            transform: scale(1);
            opacity: 1;
          }
        }

        .confirmation-details {
          text-align: left;
          margin: 30px 0;
        }

        .confirmation-code-section {
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          padding: 20px;
          margin-bottom: 25px;
        }

        .confirmation-code-display {
          display: flex;
          align-items: center;
          gap: 10px;
          background: #121212;
          border: 1px solid #333;
          border-radius: 8px;
          padding: 12px;
        }

        .confirmation-code {
          flex: 1;
          font-family: 'Courier New', monospace;
          font-size: 16px;
          font-weight: bold;
          color: #FFD700;
          letter-spacing: 1px;
        }

        .copy-button {
          background: #333;
          border: none;
          border-radius: 6px;
          padding: 8px 12px;
          color: #fff;
          cursor: pointer;
          font-size: 14px;
          transition: background 0.2s;
        }

        .copy-button:hover {
          background: #555;
        }

        .next-steps {
          margin-bottom: 25px;
        }

        .steps-list {
          list-style: none;
          padding: 0;
          margin: 0;
        }

        .steps-list li {
          display: flex;
          align-items: flex-start;
          gap: 12px;
          margin-bottom: 12px;
          padding: 10px;
          background: rgba(255, 255, 255, 0.02);
          border-radius: 8px;
          color: #ccc;
          font-size: 14px;
          line-height: 1.4;
        }

        .step-icon {
          font-size: 16px;
          flex-shrink: 0;
          margin-top: 2px;
        }

        .important-note {
          display: flex;
          align-items: flex-start;
          gap: 12px;
          background: rgba(255, 193, 7, 0.1);
          border: 1px solid rgba(255, 193, 7, 0.3);
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 25px;
        }

        .note-icon {
          font-size: 18px;
          flex-shrink: 0;
          margin-top: 2px;
        }

        .note-content {
          color: #ccc;
          font-size: 14px;
          line-height: 1.4;
        }

        .note-content strong {
          color: #FFD700;
        }

        .success-actions {
          text-align: center;
          margin-top: 30px;
        }

        .close-success-btn {
          padding: 15px 30px;
          background: linear-gradient(135deg, #4CAF50, #45a049);
          color: white;
          border: none;
          border-radius: 8px;
          font-size: 16px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .close-success-btn:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        @media (max-width: 768px) {
          .booking-success-modal {
            width: 95%;
            margin: 20px;
          }

          .success-icon {
            width: 60px;
            height: 60px;
            margin-bottom: 20px;
          }

          .checkmark {
            font-size: 30px;
          }

          .confirmation-code {
            font-size: 14px;
          }

          .steps-list li {
            font-size: 13px;
          }
        }
      `}</style>
    </div>
  );
};

export default BookingSuccessModal;
