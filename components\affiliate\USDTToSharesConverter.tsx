import React, { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';
import { validateKYCForFinancialOperations, KYCValidationResult } from '../../lib/kycValidation';
import { affiliateTransactionService } from '../../lib/services/affiliateTransactionService';

interface USDTToSharesConverterProps {
  userId: number;
  availableUSDT: number;
  onConversionComplete: () => void;
}

interface ConversionPreview {
  usdtAmount: number;
  sharePrice: number;
  sharesReceived: number;
  conversionRate: string;
}

export const USDTToSharesConverter: React.FC<USDTToSharesConverterProps> = ({
  userId,
  availableUSDT,
  onConversionComplete
}) => {
  const [kycValidation, setKycValidation] = useState<KYCValidationResult | null>(null);
  const [currentPhase, setCurrentPhase] = useState<any>(null);
  const [usdtAmount, setUsdtAmount] = useState('');
  const [preview, setPreview] = useState<ConversionPreview | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [isConverting, setIsConverting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [loading, setLoading] = useState(true);

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        // Load KYC status
        const validation = await validateKYCForFinancialOperations(userId);
        setKycValidation(validation);

        // Load current phase for share price
        const { data: phase, error: phaseError } = await supabase
          .from('investment_phases')
          .select('*')
          .eq('is_active', true)
          .single();

        if (phaseError) {
          console.error('Error loading current phase:', phaseError);
          setError('Unable to load current share price. Please try again.');
        } else {
          setCurrentPhase(phase);
        }
      } catch (error) {
        console.error('Error loading conversion data:', error);
        setError('Failed to load conversion data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [userId]);

  // Update preview when amount changes
  useEffect(() => {
    if (usdtAmount && currentPhase) {
      const amount = parseFloat(usdtAmount);
      if (amount > 0 && amount <= availableUSDT) {
        const sharePrice = parseFloat(currentPhase.price_per_share);
        const sharesReceived = Math.floor(amount / sharePrice);
        
        setPreview({
          usdtAmount: amount,
          sharePrice,
          sharesReceived,
          conversionRate: `1 Share = $${sharePrice.toFixed(2)} USDT`
        });
      } else {
        setPreview(null);
      }
    } else {
      setPreview(null);
    }
  }, [usdtAmount, currentPhase, availableUSDT]);

  const handleConversion = async () => {
    if (!preview || !kycValidation?.canWithdraw) {
      setError('Cannot process conversion. Please check requirements.');
      return;
    }

    setIsConverting(true);
    setError(null);

    try {
      // Use the new transaction service for atomic operations
      const result = await affiliateTransactionService.processCommissionToSharesConversion({
        userId,
        usdtAmount: preview.usdtAmount,
        sharesReceived: preview.sharesReceived,
        sharePrice: preview.sharePrice,
        phaseId: currentPhase.id
      });

      if (!result.success) {
        throw new Error(result.error || 'Failed to process conversion');
      }

      // Send conversion email notification
      try {
        const { resendEmailService } = await import('../../lib/resendEmailService');

        // Get user data for email
        const { data: userData } = await supabase
          .from('users')
          .select('email, full_name, username')
          .eq('id', userId)
          .single();

        if (userData?.email) {
          await resendEmailService.sendConversionNotification({
            email: userData.email,
            fullName: userData.full_name || userData.username || 'User',
            usdtAmount: preview.usdtAmount,
            sharesReceived: preview.sharesReceived,
            sharePrice: preview.sharePrice,
            transactionId: result.transactionId || 'N/A'
          });
        }
      } catch (emailError) {
        console.warn('Failed to send conversion email:', emailError);
        // Don't fail the conversion if email fails
      }

      setSuccess(true);
      setUsdtAmount('');
      setPreview(null);
      setShowConfirmation(false);
      onConversionComplete();

      // Reset success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);

    } catch (error: any) {
      console.error('Conversion error:', error);
      setError(error.message || 'Failed to process conversion. Please try again.');
    } finally {
      setIsConverting(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500"></div>
          <span className="ml-3 text-gray-300">Loading conversion data...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
      <h3 className="text-xl font-bold text-white mb-4">🔄 Convert USDT to Shares</h3>

      {/* KYC Status */}
      <div className={`rounded-lg p-4 mb-6 ${
        kycValidation?.canWithdraw 
          ? 'bg-green-900/20 border border-green-500/30' 
          : 'bg-yellow-900/20 border border-yellow-500/30'
      }`}>
        <div className="flex items-start space-x-3">
          <div className="text-2xl">
            {kycValidation?.canWithdraw ? '✅' : '⚠️'}
          </div>
          <div>
            <h4 className="font-semibold text-white mb-1">
              {kycValidation?.canWithdraw ? 'KYC Verified' : 'KYC Required'}
            </h4>
            <p className={`text-sm ${
              kycValidation?.canWithdraw ? 'text-green-300' : 'text-yellow-300'
            }`}>
              {kycValidation?.message}
            </p>
          </div>
        </div>
      </div>

      {/* Current Balances */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="bg-gray-700 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-300 mb-1">Available USDT</h4>
          <p className="text-2xl font-bold text-blue-400">${availableUSDT.toFixed(2)}</p>
        </div>
        <div className="bg-gray-700 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-300 mb-1">Current Share Price</h4>
          <p className="text-2xl font-bold text-yellow-400">
            ${currentPhase?.price_per_share?.toFixed(2) || '0.00'}
          </p>
          <p className="text-sm text-gray-400">{currentPhase?.phase_name || 'Loading...'}</p>
        </div>
      </div>

      {/* Conversion Form */}
      {kycValidation?.canWithdraw && currentPhase ? (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              USDT Amount to Convert
            </label>
            <input
              type="number"
              value={usdtAmount}
              onChange={(e) => setUsdtAmount(e.target.value)}
              placeholder={`Enter amount (max: ${availableUSDT.toFixed(2)})`}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-yellow-500"
              step="0.01"
              min="0"
              max={availableUSDT}
            />
          </div>

          {/* Conversion Preview */}
          {preview && (
            <div className="bg-gray-700 rounded-lg p-4">
              <h4 className="font-semibold text-white mb-3">Conversion Preview</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-300">USDT Amount:</span>
                  <span className="text-white">${preview.usdtAmount.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Share Price:</span>
                  <span className="text-white">${preview.sharePrice.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Shares Received:</span>
                  <span className="text-yellow-400 font-semibold">{preview.sharesReceived}</span>
                </div>
                <div className="pt-2 border-t border-gray-600">
                  <div className="flex justify-between">
                    <span className="text-gray-300">Conversion Rate:</span>
                    <span className="text-blue-400">{preview.conversionRate}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-3">
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          )}

          {/* Success Message */}
          {success && (
            <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-3">
              <p className="text-green-400 text-sm">
                ✅ Conversion completed successfully! Your shares have been added to your balance.
              </p>
            </div>
          )}

          {/* Convert Button */}
          <button
            onClick={() => setShowConfirmation(true)}
            disabled={!preview || isConverting}
            className="w-full bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-colors"
          >
            {isConverting ? 'Converting...' : 'Convert USDT to Shares'}
          </button>
        </div>
      ) : (
        <div className="text-center py-8">
          <p className="text-gray-400 mb-4">Complete KYC verification to convert USDT to shares.</p>
          <button
            onClick={() => window.location.href = '#kyc'}
            className="bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-6 rounded-lg transition-colors"
          >
            Complete KYC
          </button>
        </div>
      )}

      {/* Confirmation Modal */}
      {showConfirmation && preview && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 w-full max-w-md mx-4">
            <h3 className="text-xl font-bold text-white mb-4">Confirm Conversion</h3>
            
            <div className="space-y-3 mb-6">
              <div className="flex justify-between">
                <span className="text-gray-300">Converting:</span>
                <span className="text-white">${preview.usdtAmount.toFixed(2)} USDT</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-300">Receiving:</span>
                <span className="text-yellow-400 font-semibold">{preview.sharesReceived} Shares</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-300">Rate:</span>
                <span className="text-blue-400">${preview.sharePrice.toFixed(2)} per share</span>
              </div>
            </div>

            <div className="flex gap-3">
              <button
                onClick={() => setShowConfirmation(false)}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                disabled={isConverting}
              >
                Cancel
              </button>
              <button
                onClick={handleConversion}
                className="flex-1 bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                disabled={isConverting}
              >
                {isConverting ? 'Converting...' : 'Confirm'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
