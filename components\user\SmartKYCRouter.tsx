/**
 * SMART KYC ROUTER
 * 
 * Intelligently routes users to the appropriate KYC experience:
 * - Full KYC form for new users or complete resubmissions
 * - Field-specific resubmission for users with rejected fields
 * - Status dashboard for approved users
 */

import React, { useState, useEffect } from 'react';
import { KYCCenter } from './KYCCenter';
import { KYCFieldResubmission } from './KYCFieldResubmission';
import { getServiceRoleClient } from '../../lib/supabase';

interface SmartKYCRouterProps {
  userId: number;
  kycStatus: 'pending' | 'approved' | 'rejected' | 'not_started';
  kycId: string | null;
  onKYCStatusChange: () => void;
}

interface FieldRejection {
  id: string;
  field_name: string;
  approval_status: string;
  admin_notes: string;
  created_at: string;
  updated_at: string;
}

export const SmartKYCRouter: React.FC<SmartKYCRouterProps> = ({
  userId,
  kycStatus,
  kycId,
  onKYCStatusChange
}) => {
  const [rejectedFields, setRejectedFields] = useState<FieldRejection[]>([]);
  const [loading, setLoading] = useState(true);
  const [routingDecision, setRoutingDecision] = useState<'full_kyc' | 'field_resubmission' | 'status_only'>('full_kyc');

  useEffect(() => {
    determineKYCRoute();
  }, [userId, kycStatus, kycId]);

  const determineKYCRoute = async () => {
    try {
      setLoading(true);
      console.log('🧭 Smart KYC Router: Determining route for user', userId, 'with status', kycStatus);

      // Check for force_full_kyc parameter
      const urlParams = new URLSearchParams(window.location.search);
      const forceFullKyc = urlParams.get('force_full_kyc') === 'true';

      if (forceFullKyc) {
        console.log('🔄 Route: Full KYC (forced via URL parameter)');
        setRoutingDecision('full_kyc');
        setLoading(false);
        return;
      }

      // If no KYC ID or not started, show full KYC
      if (!kycId || kycStatus === 'not_started') {
        console.log('📋 Route: Full KYC (new user)');
        setRoutingDecision('full_kyc');
        setLoading(false);
        return;
      }

      // If approved, show status only
      if (kycStatus === 'approved') {
        console.log('✅ Route: Status only (approved)');
        setRoutingDecision('status_only');
        setLoading(false);
        return;
      }

      // If rejected or pending, check for field-specific approvals
      if (kycStatus === 'rejected' || kycStatus === 'pending') {
        try {
          console.log('🔍 Checking field approvals via API for KYC ID:', kycId);

          const response = await fetch(`/api/kyc-field-approvals?kycId=${kycId}`);
          const result = await response.json();

          if (!response.ok) {
            console.error('❌ Error loading field approvals:', result.error);
            // Fallback to full KYC on error
            setRoutingDecision('full_kyc');
            setLoading(false);
            return;
          }

          if (result.success && result.fieldApprovals) {
            const { rejected, pending, approved, hasFieldApprovals } = result.fieldApprovals;
            setRejectedFields(rejected || []);

            // If there are ANY field approvals, show field-specific interface
            // This handles cases where fields were previously rejected but are now pending/approved
            if (hasFieldApprovals) {
              console.log('🎯 Route: Field-specific resubmission (', result.fieldApprovals.all.length, 'field approvals found - rejected:', rejected?.length || 0, ', pending:', pending?.length || 0, ', approved:', approved?.length || 0, ')');
              setRoutingDecision('field_resubmission');
            } else {
              console.log('📋 Route: Full KYC (no field-level approvals found)');
              setRoutingDecision('full_kyc');
            }
          } else {
            console.error('❌ Invalid response format:', result);
            setRoutingDecision('full_kyc');
          }
        } catch (error) {
          console.error('❌ Error fetching field approvals:', error);
          setRoutingDecision('full_kyc');
        }
      }

      setLoading(false);
    } catch (error) {
      console.error('❌ Error determining KYC route:', error);
      setRoutingDecision('full_kyc'); // Fallback to full KYC
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400 mx-auto mb-4"></div>
          <p className="text-gray-400">Determining your KYC status...</p>
        </div>
      </div>
    );
  }

  // Route to appropriate component
  switch (routingDecision) {
    case 'field_resubmission':
      return (
        <div className="space-y-6">
          {/* Show field-specific resubmission interface */}
          <div className="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-3 mb-2">
              <span className="text-2xl">🎯</span>
              <h3 className="text-lg font-semibold text-blue-400">Targeted KYC Update Required</h3>
            </div>
            <p className="text-blue-200 text-sm">
              Good news! Only specific fields need attention. You don't need to complete the entire KYC process again.
            </p>
          </div>
          
          <KYCFieldResubmission
            userId={userId}
            kycId={kycId!}
            onResubmissionComplete={onKYCStatusChange}
          />
        </div>
      );

    case 'status_only':
      return (
        <div className="space-y-6">
          {/* Show status dashboard only for approved users */}
          <KYCCenter
            userId={userId}
            className="space-y-6"
          />
        </div>
      );

    case 'full_kyc':
    default:
      return (
        <div className="space-y-6">
          {/* Show full KYC process */}
          {kycStatus === 'rejected' && (
            <div className="bg-red-900/20 border border-red-600/30 rounded-lg p-4 mb-6">
              <div className="flex items-center gap-3 mb-2">
                <span className="text-2xl">🔄</span>
                <h3 className="text-lg font-semibold text-red-400">Complete KYC Resubmission Required</h3>
              </div>
              <p className="text-red-200 text-sm">
                Your KYC submission requires a complete review. Please go through the entire process again with updated information.
              </p>
            </div>
          )}
          
          <KYCCenter
            userId={userId}
            className="space-y-6"
          />
        </div>
      );
  }
};
