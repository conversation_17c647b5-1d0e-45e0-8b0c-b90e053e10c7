/**
 * <PERSON>CKE<PERSON> RESEND EMAIL SERVICE
 * 
 * Server-side only email service using Resend API for:
 * - Email verification codes
 * - Password reset emails
 * - Account security notifications
 * - Newsletter and bulk email functionality
 * 
 * This file should ONLY be used in API routes and server-side code.
 * Frontend components should call API endpoints instead.
 */

import { Resend } from 'resend';
import { createClient } from '@supabase/supabase-js';

// Environment configuration - server-side only
const RESEND_API_KEY = process.env.RESEND_API_KEY;
const RESEND_FROM_EMAIL = process.env.RESEND_FROM_EMAIL || '<EMAIL>';
const RESEND_FROM_NAME = process.env.RESEND_FROM_NAME || 'Aureus Alliance Holdings';

// Initialize Supabase client with service role
const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Initialize Resend client
let resend = null;

try {
  if (RESEND_API_KEY) {
    resend = new Resend(RESEND_API_KEY);
    console.log('✅ Backend Resend email service initialized');
  } else {
    console.warn('⚠️ RESEND_API_KEY not found - backend email service disabled');
  }
} catch (error) {
  console.error('❌ Failed to initialize backend Resend service:', error);
}

export class BackendResendEmailService {
  isConfigured() {
    return resend !== null && !!RESEND_API_KEY;
  }

  /**
   * Send email verification code
   */
  async sendVerificationCode(data) {
    if (!this.isConfigured()) {
      console.error('❌ Backend Resend service not configured');
      return { success: false, error: 'Email service not configured' };
    }

    try {
      const { email, code, purpose, userName, expiryMinutes = 15 } = data;

      // Generate email content based on purpose
      const emailContent = this.generateVerificationEmailContent(code, purpose, userName, expiryMinutes);

      const result = await resend.emails.send({
        from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
        to: [email],
        subject: emailContent.subject,
        html: emailContent.html,
        text: emailContent.text,
        tags: [
          { name: 'category', value: 'verification' },
          { name: 'purpose', value: purpose }
        ]
      });

      if (result.error) {
        console.error('❌ Backend Resend API error:', result.error);
        return {
          success: false,
          error: result.error.message,
          messageId: null
        };
      }

      console.log(`✅ Backend verification email sent successfully: ${result.data?.id}`);
      return {
        success: true,
        messageId: result.data?.id,
        error: null
      };

    } catch (error) {
      console.error('❌ Backend sendVerificationCode error:', error);
      return {
        success: false,
        error: error.message,
        messageId: null
      };
    }
  }

  /**
   * Send password reset email
   */
  async sendPasswordReset(email, resetToken, userName) {
    if (!this.isConfigured()) {
      return { success: false, error: 'Email service not configured' };
    }

    try {
      const resetLink = `${process.env.FRONTEND_URL || 'https://www.aureus.africa'}/reset-password?token=${resetToken}`;
      
      const emailContent = this.generatePasswordResetEmailContent(resetToken, userName, resetLink);

      const result = await resend.emails.send({
        from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
        to: [email],
        subject: emailContent.subject,
        html: emailContent.html,
        text: emailContent.text,
        tags: [
          { name: 'category', value: 'password_reset' },
          { name: 'purpose', value: 'password_reset' }
        ]
      });

      if (result.error) {
        console.error('❌ Backend password reset email error:', result.error);
        return {
          success: false,
          error: result.error.message,
          messageId: null
        };
      }

      return {
        success: true,
        messageId: result.data?.id,
        error: null
      };

    } catch (error) {
      console.error('❌ Backend sendPasswordReset error:', error);
      return {
        success: false,
        error: error.message,
        messageId: null
      };
    }
  }

  /**
   * Generate verification email content
   */
  generateVerificationEmailContent(code, purpose, userName, expiryMinutes) {
    const purposeText = {
      'registration': 'complete your registration',
      'account_update': 'verify your email address',
      'password_reset': 'reset your password',
      'withdrawal': 'confirm your withdrawal',
      'telegram_connection': 'connect your Telegram account'
    };

    const subject = `Your Aureus Africa verification code: ${code}`;
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email Verification - Aureus Africa</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%); padding: 30px; border-radius: 10px; text-align: center;">
          <h1 style="color: #FFD700; margin-bottom: 20px;">Aureus Alliance Holdings</h1>
          <h2 style="color: #FFFFFF; margin-bottom: 30px;">Email Verification Required</h2>
          
          <div style="background: #FFFFFF; padding: 30px; border-radius: 8px; margin: 20px 0;">
            <p style="font-size: 16px; margin-bottom: 20px;">Hello ${userName || 'User'},</p>
            <p style="font-size: 16px; margin-bottom: 20px;">Please use this verification code to ${purposeText[purpose] || 'verify your email'}:</p>
            
            <div style="background: #f8f9fa; border: 2px solid #FFD700; border-radius: 8px; padding: 20px; margin: 20px 0;">
              <h1 style="color: #1a1a1a; font-size: 32px; letter-spacing: 8px; margin: 0; font-family: monospace;">${code}</h1>
            </div>
            
            <p style="font-size: 14px; color: #666; margin-top: 20px;">
              This code will expire in ${expiryMinutes} minutes for your security.
            </p>
            <p style="font-size: 14px; color: #666;">
              If you didn't request this verification, please ignore this email.
            </p>
          </div>
          
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #444;">
            <p style="color: #CCCCCC; font-size: 12px;">
              © 2024 Aureus Alliance Holdings (Pty) Ltd. All rights reserved.
            </p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
      Aureus Alliance Holdings - Email Verification
      
      Hello ${userName || 'User'},
      
      Please use this verification code to ${purposeText[purpose] || 'verify your email'}:
      
      ${code}
      
      This code will expire in ${expiryMinutes} minutes for your security.
      
      If you didn't request this verification, please ignore this email.
      
      © 2024 Aureus Alliance Holdings (Pty) Ltd. All rights reserved.
    `;

    return { subject, html, text };
  }

  /**
   * Generate password reset email content
   */
  generatePasswordResetEmailContent(resetPin, userName, resetLink) {
    const subject = `Your Aureus Africa password reset PIN: ${resetPin}`;
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Password Reset - Aureus Africa</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%); padding: 30px; border-radius: 10px; text-align: center;">
          <h1 style="color: #FFD700; margin-bottom: 20px;">Aureus Alliance Holdings</h1>
          <h2 style="color: #FFFFFF; margin-bottom: 30px;">Password Reset Request</h2>
          
          <div style="background: #FFFFFF; padding: 30px; border-radius: 8px; margin: 20px 0;">
            <p style="font-size: 16px; margin-bottom: 20px;">Hello ${userName || 'User'},</p>
            <p style="font-size: 16px; margin-bottom: 20px;">Use this PIN to reset your password:</p>
            
            <div style="background: #f8f9fa; border: 2px solid #FFD700; border-radius: 8px; padding: 20px; margin: 20px 0;">
              <h1 style="color: #1a1a1a; font-size: 32px; letter-spacing: 4px; margin: 0; font-family: monospace;">${resetPin}</h1>
            </div>
            
            <p style="font-size: 14px; color: #666; margin-top: 20px;">
              This PIN will expire in 15 minutes for your security.
            </p>
            <p style="font-size: 14px; color: #666;">
              If you didn't request this reset, please ignore this email.
            </p>
          </div>
          
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #444;">
            <p style="color: #CCCCCC; font-size: 12px;">
              © 2024 Aureus Alliance Holdings (Pty) Ltd. All rights reserved.
            </p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
      Aureus Alliance Holdings - Password Reset
      
      Hello ${userName || 'User'},
      
      Use this PIN to reset your password: ${resetPin}
      
      This PIN will expire in 15 minutes for your security.
      
      If you didn't request this reset, please ignore this email.
      
      © 2024 Aureus Alliance Holdings (Pty) Ltd. All rights reserved.
    `;

    return { subject, html, text };
  }
}

// Export singleton instance
export const backendResendEmailService = new BackendResendEmailService();
