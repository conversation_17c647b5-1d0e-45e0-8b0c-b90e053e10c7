# Aureus Africa - Development & Build Guide

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm 8+
- Git

### Initial Setup
```bash
# Clone and navigate to project
cd aureus_africa

# Install dependencies
npm install

# Setup environment and run health check
npm run setup

# Start development (both frontend and backend)
npm run dev:full
```

## 📋 Available Scripts

### Development Scripts
```bash
# Start frontend only (Vite dev server on port 8000)
npm run dev

# Start backend only (Express server on port 8002)
npm run server

# Start both frontend and backend concurrently
npm run dev:full

# Run system health check
npm run health-check
```

### Build Scripts
```bash
# Full production build with validation
npm run build

# Clean build (removes dist/ first)
npm run build:clean

# Build with bundle analysis
npm run build:analyze

# Test production build locally
npm run preview

# Build and preview together
npm run preview:production
```

### Deployment Scripts
```bash
# Create deployment packages for different platforms
npm run deploy:build

# Test build integrity
npm run test:build

# Package for specific hosting platforms
npm run deploy:package
```

### Utility Scripts
```bash
# Lint code
npm run lint

# Validate CSS classes
npm run validate:css

# Run all validations
npm run validate:all
```

## 🔧 Development Workflow

### 1. **CRITICAL: Both Services Required**
The application requires BOTH services to function properly:

- **Frontend (port 8000)**: React application with Vite
- **Backend (port 8002)**: Express server with API routes

**⚠️ APIs will NOT work unless both services are running!**

### 2. **Recommended Development Flow**
```bash
# Terminal 1: Start both services
npm run dev:full

# Terminal 2: Run tests/validation as needed
npm run validate:all
```

### 3. **Service URLs**
- Frontend: http://localhost:8000
- Backend: http://localhost:8002
- Health Check: http://localhost:8002/health
- API Routes: http://localhost:8002/api/*

## 🏗️ Build Process

### Production Build Steps
1. **Environment Validation**: Checks all required VITE_* variables
2. **Clean Build**: Removes previous build artifacts
3. **Vite Build**: Compiles React app with optimizations
4. **Build Verification**: Validates build integrity
5. **Asset Optimization**: Chunks and compresses assets

### Build Output (`dist/`)
```
dist/
├── index.html          # Main application entry
├── manifest.json       # PWA manifest
├── assets/            # Optimized assets
│   ├── index-[hash].js    # Main JavaScript bundle
│   ├── index-[hash].css   # Main CSS bundle
│   ├── vendor-[hash].js   # Third-party libraries
│   └── supabase-[hash].js # Supabase client
└── [other assets]     # Images, fonts, etc.
```

## 🌍 Environment Variables

### Required Variables (Client-side)
```env
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Email Service (Resend)
VITE_RESEND_API_KEY=re_xxxxxxxxxx
VITE_RESEND_FROM_EMAIL=<EMAIL>
VITE_RESEND_FROM_NAME=Aureus Alliance Holdings
```

### Optional Variables
```env
# Service Role Key (Development only - DO NOT use in production)
VITE_SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Application Metadata
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=production
```

### Server-side Only (Not included in client bundle)
```env
# Database Direct Access
SUPABASE_DB_HOST=db.your-project.supabase.co
SUPABASE_DB_PASSWORD=your-db-password
JWT_SECRET=your-jwt-secret
```

## 🚀 Deployment

### Automated Deployment Packages
```bash
npm run deploy:build
```

Creates deployment-ready packages for:
- **cPanel**: Upload to public_html directory
- **Netlify**: Drag-and-drop deployment
- **Vercel**: Git-based deployment

### Manual Deployment
1. Build the application: `npm run build`
2. Upload `dist/` contents to your web server
3. Configure server for client-side routing
4. Set environment variables in hosting platform

## 🔍 Troubleshooting

### Common Issues

**"APIs not working"**
- Ensure both `npm run dev` and `npm run server` are running
- Check that backend is accessible at http://localhost:8002/health

**"Build fails with environment errors"**
- Run `npm run build:env-check` to validate variables
- Ensure all VITE_* variables are set in .env file

**"Blank page after deployment"**
- Check browser console for JavaScript errors
- Verify all assets are loading correctly
- Ensure client-side routing is configured on server

**"404 errors on routes"**
- Configure server to serve index.html for all routes
- Check .htaccess file for Apache servers
- Verify _redirects file for Netlify

### Debug Commands
```bash
# Check system health
npm run health-check

# Validate environment variables
node scripts/check-env-vars.js

# Test build integrity
npm run test:build

# Analyze bundle size
npm run build:analyze
```

## 📊 Performance Optimization

### Bundle Analysis
- Main bundle should be < 500KB
- Vendor chunks are automatically split
- Assets are optimized and compressed

### Recommendations
- Use `npm run build:analyze` to identify large dependencies
- Consider code splitting for large components
- Optimize images and assets before including

## 🔒 Security

### Environment Variables
- Only VITE_* prefixed variables are included in client bundle
- Server-side secrets remain secure
- Service role keys should only be used in development

### Build Security
- Source maps disabled in production
- Security headers configured
- Asset integrity maintained

## 📚 Additional Resources

- [Vite Documentation](https://vitejs.dev/)
- [React Documentation](https://react.dev/)
- [Supabase Documentation](https://supabase.com/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)

---

**Need Help?** Check the troubleshooting section or run `npm run health-check` for system diagnostics.
