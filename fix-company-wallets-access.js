import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-role-key';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixCompanyWalletsAccess() {
  console.log('🔧 Fixing company_wallets table access for payment flow...');
  console.log('=======================================================');

  try {
    // Add a policy to allow public read access to company wallets
    // This is necessary for users to see wallet addresses during payment flow
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Add policy to allow public read access to active company wallets
        -- This is safe because wallet addresses need to be visible for payments
        CREATE POLICY IF NOT EXISTS "Public can read active company wallets" ON public.company_wallets
          FOR SELECT USING (is_active = true);
      `
    });

    if (error) {
      console.error('❌ Error creating public read policy:', error);
      return;
    }

    console.log('✅ Successfully added public read access policy for company_wallets');
    console.log('📋 Policy details:');
    console.log('   - Allows: Public SELECT access');
    console.log('   - Condition: Only active wallets (is_active = true)');
    console.log('   - Purpose: Enable payment flow for all users');

    // Test the access
    console.log('\n🧪 Testing access...');
    
    const { data: testData, error: testError } = await supabase
      .from('company_wallets')
      .select('network, wallet_address, is_active')
      .eq('is_active', true);

    if (testError) {
      console.error('❌ Test failed:', testError);
    } else {
      console.log('✅ Test successful! Found', testData?.length || 0, 'active wallet(s)');
      if (testData && testData.length > 0) {
        testData.forEach(wallet => {
          console.log(`   - ${wallet.network}: ${wallet.wallet_address.substring(0, 10)}...`);
        });
      }
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the fix
fixCompanyWalletsAccess().then(() => {
  console.log('\n🎉 Company wallets access fix completed!');
  console.log('💡 Users can now see wallet addresses during payment flow');
  process.exit(0);
}).catch(error => {
  console.error('💥 Fix failed:', error);
  process.exit(1);
});
