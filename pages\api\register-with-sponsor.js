import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';
import { Resend } from 'resend';
import dotenv from 'dotenv';

dotenv.config();

// Read from standard server envs, with fallback to Vite-prefixed names used in Vercel dashboard
const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !serviceKey) {
  throw new Error('Missing Supabase environment variables');
}

const supabase = createClient(supabaseUrl, serviceKey);

// Initialize Resend client
const RESEND_API_KEY = process.env.RESEND_API_KEY;
const RESEND_FROM_EMAIL = process.env.RESEND_FROM_EMAIL || '<EMAIL>';
const RESEND_FROM_NAME = process.env.RESEND_FROM_NAME || 'Aureus Alliance Holdings';
const resend = RESEND_API_KEY ? new Resend(RESEND_API_KEY) : null;

/**
 * Generate welcome email content
 */
function generateWelcomeEmailContent(userData, loginUrl) {
  const { username, email, full_name, password } = userData;
  const greeting = full_name ? `Hello ${full_name}` : `Hello ${username}`;

  const subject = '🎉 Welcome to Aureus Alliance Holdings - Your Account is Ready!';

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Welcome to Aureus Alliance Holdings</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        .header { text-align: center; padding: 20px 0; border-bottom: 2px solid #d4af37; }
        .logo { color: #d4af37; font-size: 24px; font-weight: bold; }
        .content { padding: 20px 0; }
        .credentials-box { background: #f8f9fa; border: 2px solid #d4af37; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .credential-item { margin: 10px 0; padding: 8px; background: white; border-radius: 4px; }
        .credential-label { font-weight: bold; color: #d4af37; }
        .credential-value { font-family: monospace; background: #e9ecef; padding: 4px 8px; border-radius: 4px; }
        .login-button { display: inline-block; background: #d4af37; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 20px 0; }
        .login-button:hover { background: #b8941f; }
        .steps { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .step { margin: 10px 0; padding: 8px 0; border-bottom: 1px solid #dee2e6; }
        .step:last-child { border-bottom: none; }
        .security-notice { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; padding: 20px 0; border-top: 1px solid #dee2e6; color: #666; font-size: 12px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">🏆 AUREUS ALLIANCE HOLDINGS</div>
          <p>Real Gold • Real Shares • Real Ownership</p>
        </div>

        <div class="content">
          <h2>🎉 Welcome to the Team!</h2>
          <p>${greeting},</p>
          <p>Congratulations! Your Aureus Alliance Holdings account has been successfully created. You're now part of our exclusive gold mining community.</p>

          <div class="credentials-box">
            <h3>🔐 Your Login Credentials</h3>
            <div class="credential-item">
              <div class="credential-label">Username:</div>
              <div class="credential-value">${username}</div>
            </div>
            <div class="credential-item">
              <div class="credential-label">Email:</div>
              <div class="credential-value">${email}</div>
            </div>
            <div class="credential-item">
              <div class="credential-label">Temporary Password:</div>
              <div class="credential-value">${password}</div>
            </div>
          </div>

          <div style="text-align: center;">
            <a href="${loginUrl}" class="login-button">🚀 Access Your Dashboard</a>
          </div>

          <div class="steps">
            <h3>🚀 Getting Started</h3>
            <div class="step">1. 📧 Complete your email verification (check your inbox)</div>
            <div class="step">2. 🔐 Log in and change your password</div>
            <div class="step">3. 📋 Complete your KYC verification</div>
            <div class="step">4. 💰 Explore current share offerings</div>
            <div class="step">5. 🔗 Set up your referral links to earn commissions</div>
            <div class="step">6. 💬 Join our Telegram community for updates</div>
          </div>

          <div class="security-notice">
            <h4>🔒 Security Notice</h4>
            <ul>
              <li>Please change your temporary password after your first login</li>
              <li>Never share your login credentials with anyone</li>
              <li>Aureus Alliance will never ask for your password via email or phone</li>
              <li>Always verify you're on the official website: aureus.africa</li>
            </ul>
          </div>

          <p>💡 <strong>Need help?</strong> Check out our FAQ or contact our support team.</p>
          <p>Welcome to the future of gold ownership!</p>

          <p>Best regards,<br>
          <strong>The Aureus Alliance Holdings Team</strong></p>
        </div>

        <div class="footer">
          <p>This email was sent by Aureus Alliance Holdings (Pty) Ltd</p>
          <p>You received this email because you registered for an account with us.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const text = `
    Aureus Alliance Holdings - Welcome!

    ${greeting},

    Congratulations! Your account has been successfully created.

    Your Login Credentials:
    Username: ${username}
    Email: ${email}
    Temporary Password: ${password}

    Login URL: ${loginUrl}

    Getting Started:
    1. Complete your email verification (check your inbox)
    2. Log in and change your password
    3. Complete your KYC verification
    4. Explore current share offerings
    5. Set up your referral links to earn commissions
    6. Join our Telegram community for updates

    Security Notice:
    - Please change your temporary password after your first login
    - Never share your login credentials with anyone
    - Aureus Alliance will never ask for your password via email or phone

    Welcome to the future of gold ownership!

    Best regards,
    The Aureus Alliance Holdings Team
  `;

  return { subject, html, text };
}

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  const {
    fullName,
    email,
    phone,
    countryOfResidence,
    password,
    sponsorId,
    sponsorUsername
  } = req.body;

  // Validate required fields
  if (!fullName || !email || !phone || !countryOfResidence || !password || !sponsorId) {
    return res.status(400).json({
      success: false,
      message: 'All fields are required'
    });
  }

  try {
    // Check if email already exists
    const { data: existingUser } = await supabase
      .from('users')
      .select('id')
      .eq('email', email.toLowerCase().trim())
      .single();

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'Email already registered'
      });
    }

    // Verify sponsor exists
    const { data: sponsor, error: sponsorError } = await supabase
      .from('users')
      .select('id, username')
      .eq('id', sponsorId)
      .single();

    if (sponsorError || !sponsor) {
      return res.status(400).json({
        success: false,
        message: 'Invalid sponsor'
      });
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, 12);

    // Generate username from email
    const emailPrefix = email.split('@')[0];
    const baseUsername = emailPrefix.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
    
    // Ensure username is unique
    let finalUsername = baseUsername;
    let counter = 1;
    
    while (true) {
      const { data: existingUsername } = await supabase
        .from('users')
        .select('id')
        .eq('username', finalUsername)
        .single();
      
      if (!existingUsername) break;
      
      finalUsername = `${baseUsername}${counter}`;
      counter++;
    }

    // Split full name
    const nameParts = fullName.trim().split(' ');
    const firstName = nameParts[0];
    const lastName = nameParts.slice(1).join(' ') || '';

    // Create user account
    const { data: newUser, error: userError } = await supabase
      .from('users')
      .insert({
        username: finalUsername,
        email: email.toLowerCase().trim(),
        password_hash: passwordHash,
        first_name: firstName,
        last_name: lastName,
        full_name: fullName.trim(),
        phone: phone.trim(),
        country_of_residence: countryOfResidence,
        is_active: true,
        is_verified: false,
        email_verified: true, // Set email as verified by default for new users
        role: 'user',
        account_type: 'email',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (userError) {
      console.error('Error creating user:', userError);
      return res.status(500).json({
        success: false,
        message: 'Failed to create user account'
      });
    }

    // Create referral relationship
    const referralCode = `REF_${newUser.id}_${Date.now()}`;

    const { data: referralData, error: referralError } = await supabase
      .from('referrals')
      .insert({
        referrer_id: sponsorId,
        referred_id: newUser.id,
        referral_code: referralCode,
        commission_rate: 15.00,
        status: 'active',
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (referralError) {
      console.error('Error creating referral:', referralError);
      // Don't fail the registration if referral creation fails
      // The user account is already created
    } else if (referralData?.id) {
      // Trigger email notification for new referral
      try {
        const { emailNotificationTriggers } = await import('../../lib/services/emailNotificationTriggers');
        await emailNotificationTriggers.triggerReferralNotification(referralData.id);
      } catch (emailError) {
        console.warn('Failed to trigger referral email notification:', emailError);
        // Don't fail registration if email fails
      }
    }

    // Send new referral notification to sponsor
    try {
      const { affiliateNotificationService } = await import('../../lib/services/affiliateNotificationService');
      await affiliateNotificationService.notifyNewUserRegistration(newUser.id, sponsorUsername);
    } catch (notificationError) {
      console.warn('Failed to send new referral notification:', notificationError);
      // Don't fail registration if notification fails
    }

    // Update sponsor's referral count
    await supabase.rpc('increment_referral_count', { user_id: sponsorId });

    // Send welcome email with login credentials
    if (resend) {
      try {
        const loginUrl = `${req.headers.origin || 'https://aureus.africa'}/login`;
        const welcomeEmailData = {
          username: newUser.username,
          email: newUser.email,
          full_name: newUser.full_name,
          password: password // Send the original password, not the hash
        };

        const emailContent = generateWelcomeEmailContent(welcomeEmailData, loginUrl);

        console.log('📧 Sending welcome email to:', newUser.email);

        const emailResult = await resend.emails.send({
          from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
          to: [newUser.email],
          subject: emailContent.subject,
          html: emailContent.html,
          text: emailContent.text,
          tags: [
            { name: 'category', value: 'welcome' },
            { name: 'purpose', value: 'registration_complete' }
          ]
        });

        if (emailResult.error) {
          console.error('❌ Failed to send welcome email:', emailResult.error);
          // Don't fail registration if email fails
        } else {
          console.log('✅ Welcome email sent successfully:', emailResult.data?.id);
        }
      } catch (emailError) {
        console.error('❌ Welcome email error:', emailError);
        // Don't fail registration if email fails
      }
    } else {
      console.warn('⚠️ Resend not configured - welcome email not sent');
    }

    return res.status(200).json({
      success: true,
      message: 'Registration successful',
      user: {
        id: newUser.id,
        username: newUser.username,
        email: newUser.email,
        sponsor: {
          id: sponsor.id,
          username: sponsor.username
        }
      }
    });

  } catch (error) {
    console.error('Registration error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}
