/**
 * AUTHENTICATION CONTAINER
 * 
 * Main container component that orchestrates all authentication methods
 * and manages the overall authentication flow.
 */

import React from 'react';
import { AuthTabs } from './AuthTabs';
import { TelegramAuthForm } from './TelegramAuthForm';
import { WebLoginForm } from './WebLoginForm';
import { WebRegisterForm } from './WebRegisterForm';
import { ForgotPasswordModal } from './ForgotPasswordModal';
import { TermsAndConditions } from '../TermsAndConditions';
import { PrivacyPolicy } from '../PrivacyPolicy';
import { useAuthState } from '../../hooks/useAuthState';

interface AuthContainerProps {
  onAuthSuccess: (user: any) => void;
  onBack: () => void;
  userType: 'shareholder' | 'affiliate';
}

export const AuthContainer: React.FC<AuthContainerProps> = ({
  onAuthSuccess,
  onBack,
  userType = 'shareholder'
}) => {
  const {
    authState,
    setActiveTab,
    setLoading,
    setError,
    setSuccess,
    setShowTermsModal,
    setShowPrivacyModal,
    setShowForgotPassword,
    setResetStep,
    clearMessages,
    resetAuthState
  } = useAuthState('web-register');

  const handleAuthSuccess = (user: any) => {
    resetAuthState();
    onAuthSuccess(user);
  };

  const handleTabChange = (tab: typeof authState.activeTab) => {
    clearMessages();
    setActiveTab(tab);
  };

  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <img 
            src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-s.png" 
            alt="Aureus Alliance Holdings" 
            className="mx-auto h-16 w-auto mb-4"
          />
          <h2 className="text-3xl font-bold text-white mb-2">
            {userType === 'shareholder' ? 'Shareholder Portal' : 'Affiliate Portal'}
          </h2>
          <p className="text-gray-400">
            {userType === 'shareholder' 
              ? 'Access your gold-backed investment portfolio' 
              : 'Manage your referrals and commissions'
            }
          </p>
        </div>

        {/* Back Button */}
        <div className="flex justify-center">
          <button
            onClick={onBack}
            className="text-gray-400 hover:text-white transition-colors flex items-center space-x-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            <span>Back to Home</span>
          </button>
        </div>

        {/* Error/Success Messages */}
        {authState.error && (
          <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
            <div className="flex items-center">
              <div className="text-red-400 mr-3">⚠️</div>
              <div>
                <h3 className="text-red-400 font-semibold">Authentication Error</h3>
                <p className="text-red-300 text-sm mt-1">{authState.error}</p>
              </div>
            </div>
          </div>
        )}

        {authState.success && (
          <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4">
            <div className="flex items-center">
              <div className="text-green-400 mr-3">✅</div>
              <div>
                <h3 className="text-green-400 font-semibold">Success</h3>
                <p className="text-green-300 text-sm mt-1">{authState.success}</p>
              </div>
            </div>
          </div>
        )}

        {/* Authentication Tabs */}
        <AuthTabs
          activeTab={authState.activeTab}
          onTabChange={handleTabChange}
          userType={userType}
        />

        {/* Authentication Forms */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          {authState.activeTab === 'telegram' && (
            <TelegramAuthForm
              onAuthSuccess={handleAuthSuccess}
              onError={setError}
              onSuccess={setSuccess}
              onLoading={setLoading}
              isLoading={authState.isLoading}
            />
          )}

          {authState.activeTab === 'web-login' && (
            <WebLoginForm
              onAuthSuccess={handleAuthSuccess}
              onError={setError}
              onSuccess={setSuccess}
              onLoading={setLoading}
              onShowForgotPassword={() => setShowForgotPassword(true)}
              isLoading={authState.isLoading}
            />
          )}

          {authState.activeTab === 'web-register' && (
            <WebRegisterForm
              onAuthSuccess={handleAuthSuccess}
              onError={setError}
              onSuccess={setSuccess}
              onLoading={setLoading}
              onShowTerms={() => setShowTermsModal(true)}
              onShowPrivacy={() => setShowPrivacyModal(true)}
              userType={userType}
              isLoading={authState.isLoading}
            />
          )}
        </div>

        {/* Modals */}
        {authState.showForgotPassword && (
          <ForgotPasswordModal
            isOpen={authState.showForgotPassword}
            onClose={() => setShowForgotPassword(false)}
            onError={setError}
            onSuccess={setSuccess}
            onLoading={setLoading}
            resetStep={authState.resetStep}
            onResetStepChange={setResetStep}
          />
        )}

        {authState.showTermsModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-gray-800 rounded-lg max-w-4xl max-h-[80vh] overflow-y-auto">
              <TermsAndConditions onClose={() => setShowTermsModal(false)} />
            </div>
          </div>
        )}

        {authState.showPrivacyModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-gray-800 rounded-lg max-w-4xl max-h-[80vh] overflow-y-auto">
              <PrivacyPolicy onClose={() => setShowPrivacyModal(false)} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
