const http = require('http');

async function testRegistrationFlow() {
  console.log('🧪 Testing complete registration flow...');
  
  // Test 1: Check duplicates API
  console.log('\n1️⃣ Testing duplicate check API...');
  
  const testEmail = `test.registration.${Date.now()}@example.com`;
  const testUsername = `testuser${Date.now()}`;
  
  const duplicateCheckData = JSON.stringify({ 
    email: testEmail,
    username: testUsername
  });

  const duplicateCheckOptions = {
    hostname: 'localhost',
    port: 8002,
    path: '/api/check-duplicates',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(duplicateCheckData)
    }
  };

  try {
    const duplicateResult = await makeRequest(duplicateCheckOptions, duplicateCheckData);
    console.log('📧 Duplicate check result:', duplicateResult);
    
    if (duplicateResult.emailDuplicate || duplicateResult.usernameDuplicate) {
      console.log('❌ Email or username already exists, using different values');
      return;
    }
    
    console.log('✅ Email and username are available');
  } catch (error) {
    console.error('❌ Duplicate check failed:', error.message);
    return;
  }

  // Test 2: Send verification email
  console.log('\n2️⃣ Testing email verification...');
  
  const verificationCode = '123456';
  const emailData = JSON.stringify({
    email: testEmail,
    code: verificationCode,
    purpose: 'registration',
    userName: 'Test User',
    expiryMinutes: 15
  });

  const emailOptions = {
    hostname: 'localhost',
    port: 8002,
    path: '/api/send-verification-email',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(emailData)
    }
  };

  try {
    const emailResult = await makeRequest(emailOptions, emailData);
    console.log('📧 Email verification result:', emailResult);
    
    if (!emailResult.success) {
      console.log('❌ Email verification failed');
      return;
    }
    
    console.log('✅ Verification email sent successfully');
  } catch (error) {
    console.error('❌ Email verification failed:', error.message);
    return;
  }

  console.log('\n🎉 Registration flow test completed successfully!');
  console.log('📋 Test Summary:');
  console.log(`   Email: ${testEmail}`);
  console.log(`   Username: ${testUsername}`);
  console.log(`   Verification Code: ${verificationCode}`);
  console.log('\n✅ All API endpoints are working correctly');
  console.log('✅ No duplicate constraint violations detected');
  console.log('✅ Email verification system is functional');
}

function makeRequest(options, data) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(responseData);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(result);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${result.error || 'Unknown error'}`));
          }
        } catch (error) {
          reject(new Error(`Parse error: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(data);
    }
    req.end();
  });
}

testRegistrationFlow().catch(console.error);
