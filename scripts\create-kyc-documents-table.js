/**
 * CREATE KYC DOCUMENTS TABLE
 * 
 * Creates a comprehensive table to store KYC document metadata including
 * file paths, document types, upload dates, and verification status.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing required environment variables');
  console.error('Required: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Initialize Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function executeSQL(sql, description) {
  try {
    console.log(`🔄 ${description}...`);
    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });
    
    if (error) {
      console.error(`❌ ${description} failed:`, error);
      return false;
    }
    
    console.log(`✅ ${description} completed successfully`);
    return true;
  } catch (err) {
    console.error(`❌ ${description} failed:`, err);
    return false;
  }
}

async function createKYCDocumentsTable() {
  console.log('🚀 Starting KYC documents table creation...');

  // Step 1: Create kyc_documents table
  const createTableSQL = `
    CREATE TABLE IF NOT EXISTS kyc_documents (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      kyc_id UUID NOT NULL REFERENCES kyc_information(id) ON DELETE CASCADE,
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      
      -- Document Information
      document_type VARCHAR(50) NOT NULL CHECK (document_type IN (
        'id_document_front',
        'id_document_back', 
        'proof_of_address',
        'selfie_verification',
        'facial_recognition',
        'additional_document'
      )),
      document_category VARCHAR(30) NOT NULL CHECK (document_category IN (
        'identity_verification',
        'address_verification', 
        'biometric_verification',
        'supplementary'
      )),
      
      -- File Storage Information
      file_name VARCHAR(255) NOT NULL,
      file_path VARCHAR(500) NOT NULL,
      storage_bucket VARCHAR(50) NOT NULL DEFAULT 'proof',
      file_size_bytes INTEGER,
      mime_type VARCHAR(100),
      file_hash VARCHAR(64), -- SHA-256 hash for integrity verification
      
      -- Document Status and Verification
      verification_status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (verification_status IN (
        'pending',
        'approved',
        'rejected',
        'requires_resubmission',
        'expired'
      )),
      quality_score DECIMAL(3,2), -- 0.00 to 1.00 quality assessment
      confidence_score DECIMAL(3,2), -- 0.00 to 1.00 AI confidence
      
      -- Review Information
      reviewed_by VARCHAR(100), -- Admin email who reviewed
      reviewed_at TIMESTAMP WITH TIME ZONE,
      rejection_reason TEXT,
      admin_notes TEXT,
      
      -- Metadata
      metadata JSONB DEFAULT '{}'::jsonb, -- Additional document-specific data
      
      -- Audit Trail
      uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      
      -- Constraints
      UNIQUE(kyc_id, document_type) -- One document per type per KYC submission
    );
  `;

  if (!await executeSQL(createTableSQL, 'Creating kyc_documents table')) {
    return false;
  }

  // Step 2: Create indexes for performance
  const createIndexesSQL = `
    CREATE INDEX IF NOT EXISTS idx_kyc_documents_kyc_id ON kyc_documents(kyc_id);
    CREATE INDEX IF NOT EXISTS idx_kyc_documents_user_id ON kyc_documents(user_id);
    CREATE INDEX IF NOT EXISTS idx_kyc_documents_type ON kyc_documents(document_type);
    CREATE INDEX IF NOT EXISTS idx_kyc_documents_status ON kyc_documents(verification_status);
    CREATE INDEX IF NOT EXISTS idx_kyc_documents_category ON kyc_documents(document_category);
    CREATE INDEX IF NOT EXISTS idx_kyc_documents_uploaded_at ON kyc_documents(uploaded_at);
    CREATE INDEX IF NOT EXISTS idx_kyc_documents_file_hash ON kyc_documents(file_hash) WHERE file_hash IS NOT NULL;
  `;

  if (!await executeSQL(createIndexesSQL, 'Creating kyc_documents indexes')) {
    return false;
  }

  // Step 3: Add table and column comments
  const addCommentsSQL = `
    COMMENT ON TABLE kyc_documents IS 'Stores metadata for all KYC document uploads including verification status and file information';
    COMMENT ON COLUMN kyc_documents.kyc_id IS 'Reference to the main KYC submission';
    COMMENT ON COLUMN kyc_documents.document_type IS 'Specific type of document (front/back ID, proof of address, etc.)';
    COMMENT ON COLUMN kyc_documents.document_category IS 'High-level category for grouping document types';
    COMMENT ON COLUMN kyc_documents.file_path IS 'Full path to file in Supabase storage';
    COMMENT ON COLUMN kyc_documents.file_hash IS 'SHA-256 hash for file integrity verification';
    COMMENT ON COLUMN kyc_documents.quality_score IS 'Automated quality assessment score (0.00-1.00)';
    COMMENT ON COLUMN kyc_documents.confidence_score IS 'AI confidence in document authenticity (0.00-1.00)';
    COMMENT ON COLUMN kyc_documents.metadata IS 'Additional document-specific data (OCR results, face detection, etc.)';
  `;

  if (!await executeSQL(addCommentsSQL, 'Adding table comments')) {
    return false;
  }

  // Step 4: Create RLS policies
  const createPoliciesSQL = `
    ALTER TABLE kyc_documents ENABLE ROW LEVEL SECURITY;
    
    -- Users can view their own documents
    CREATE POLICY "Users can view their own KYC documents" ON kyc_documents
      FOR SELECT USING (auth.uid()::text = user_id::text);
    
    -- Users can insert their own documents
    CREATE POLICY "Users can upload their own KYC documents" ON kyc_documents
      FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);
    
    -- Users can update their own pending documents
    CREATE POLICY "Users can update their own pending KYC documents" ON kyc_documents
      FOR UPDATE USING (
        auth.uid()::text = user_id::text 
        AND verification_status = 'pending'
      );
    
    -- Service role can manage all documents
    CREATE POLICY "Service role can manage all KYC documents" ON kyc_documents
      FOR ALL USING (auth.role() = 'service_role');
    
    -- Admins can view and update all documents (when authenticated as admin users)
    CREATE POLICY "Admins can manage all KYC documents" ON kyc_documents
      FOR ALL USING (
        EXISTS (
          SELECT 1 FROM users 
          WHERE users.id::text = auth.uid()::text 
          AND users.role = 'admin'
        )
      );
  `;

  if (!await executeSQL(createPoliciesSQL, 'Creating RLS policies')) {
    return false;
  }

  // Step 5: Create updated_at trigger
  const createTriggerSQL = `
    DROP TRIGGER IF EXISTS update_kyc_documents_updated_at ON kyc_documents;
    CREATE TRIGGER update_kyc_documents_updated_at
        BEFORE UPDATE ON kyc_documents
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
  `;

  if (!await executeSQL(createTriggerSQL, 'Creating updated_at trigger')) {
    return false;
  }

  // Step 6: Create document access log table for audit trail
  const createAccessLogSQL = `
    CREATE TABLE IF NOT EXISTS kyc_document_access_log (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      document_id UUID NOT NULL REFERENCES kyc_documents(id) ON DELETE CASCADE,
      user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
      access_type VARCHAR(20) NOT NULL CHECK (access_type IN ('view', 'download', 'approve', 'reject')),
      ip_address INET,
      user_agent TEXT,
      accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    CREATE INDEX IF NOT EXISTS idx_kyc_document_access_log_document_id ON kyc_document_access_log(document_id);
    CREATE INDEX IF NOT EXISTS idx_kyc_document_access_log_user_id ON kyc_document_access_log(user_id);
    CREATE INDEX IF NOT EXISTS idx_kyc_document_access_log_accessed_at ON kyc_document_access_log(accessed_at);
    
    COMMENT ON TABLE kyc_document_access_log IS 'Audit log for all KYC document access and actions';
  `;

  if (!await executeSQL(createAccessLogSQL, 'Creating document access log table')) {
    return false;
  }

  console.log('✅ KYC documents table creation completed successfully!');
  return true;
}

// Execute the migration
createKYCDocumentsTable()
  .then((success) => {
    if (success) {
      console.log('🎉 Migration completed successfully!');
      console.log('\nNext steps:');
      console.log('1. Update KYC upload forms to populate kyc_documents table');
      console.log('2. Migrate existing documents from storage to database records');
      console.log('3. Update admin dashboard to display document information');
      process.exit(0);
    } else {
      console.error('💥 Migration failed!');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('💥 Migration failed with error:', error);
    process.exit(1);
  });
