import React, { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';

interface KYCFormData {
  firstName: string;
  lastName: string;
  idType: 'national_id' | 'passport' | 'drivers_license' | 'residence_permit' | 'work_permit' | 'visa' | 'voter_id' | 'social_security' | 'military_id' | 'aadhaar' | 'sin' | 'other';
  idNumber: string;
  phoneNumber: string;
  emailAddress: string;
  streetAddress: string;
  city: string;
  province: string;
  postalCode: string;
  countryCode: string;
  countryName: string;
  nationality: string;
  dataConsent: boolean;
  privacyPolicyAccepted: boolean;
}

interface DocumentFile {
  file: File;
  preview: string;
  type: 'id_document' | 'proof_of_residence';
  uploaded?: boolean;
  url?: string;
}

interface KYCVerificationFormProps {
  userId: number;
  onKYCComplete?: (status: string) => void;
  existingKYC?: any;
}

export const KYCVerificationForm: React.FC<KYCVerificationFormProps> = ({
  userId,
  onKYCComplete,
  existingKYC
}) => {
  // If KY<PERSON> already exists and is completed, show read-only view
  if (existingKYC && existingKYC.kyc_status === 'completed') {
    return (
      <div className="bg-blue-900/30 rounded-lg p-6 border border-blue-700">
        <div className="flex items-center space-x-3 mb-4">
          <span className="text-3xl">📋</span>
          <div>
            <h3 className="text-blue-400 font-medium text-lg">KYC Already Submitted</h3>
            <p className="text-blue-200 text-sm">
              Your KYC information has been submitted and is pending admin approval.
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-400">Name:</span>
            <span className="text-white ml-2">{existingKYC.first_name} {existingKYC.last_name}</span>
          </div>
          <div>
            <span className="text-gray-400">Email:</span>
            <span className="text-white ml-2">{existingKYC.email_address}</span>
          </div>
          <div>
            <span className="text-gray-400">Phone:</span>
            <span className="text-white ml-2">{existingKYC.phone_number}</span>
          </div>
          <div>
            <span className="text-gray-400">Country:</span>
            <span className="text-white ml-2">{existingKYC.country_name}</span>
          </div>
          <div className="col-span-2">
            <span className="text-gray-400">Submitted:</span>
            <span className="text-white ml-2">
              {new Date(existingKYC.kyc_completed_at).toLocaleDateString()}
            </span>
          </div>
        </div>

        <div className="mt-4 p-3 bg-blue-900/20 rounded-lg border border-blue-800">
          <p className="text-blue-300 text-sm">
            <strong>Next Steps:</strong> Your KYC information is being reviewed by our admin team.
            You will be notified once the review is complete. This typically takes 24-48 hours.
          </p>
        </div>
      </div>
    );
  }

  // Check authentication status on component mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        if (error) {
          console.error('❌ Auth session error:', error);
        } else if (!session) {
          console.warn('⚠️ No active Supabase session found');
        } else {
          console.log('✅ Active Supabase session found:', session.user.email);
        }
      } catch (error) {
        console.error('❌ Auth check failed:', error);
      }
    };

    checkAuth();
    loadUserData();
  }, []);

  const loadUserData = async () => {
    try {
      console.log('🔍 Loading user data for auto-population...');
      const serviceClient = getServiceRoleClient();

      const { data: userData, error } = await serviceClient
        .from('users')
        .select('email, phone_number, country_of_residence, full_name, first_name, last_name')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('❌ Error loading user data:', error);
        return;
      }

      if (userData) {
        console.log('✅ User data loaded:', userData);

        // Auto-populate form with existing user data (only if not already filled from existingKYC)
        setFormData(prev => ({
          ...prev,
          // Use existing KYC data first, then fall back to user data
          firstName: prev.firstName || userData.first_name || (userData.full_name?.split(' ')[0]) || '',
          lastName: prev.lastName || userData.last_name || (userData.full_name?.split(' ').slice(1).join(' ')) || '',
          phoneNumber: prev.phoneNumber || userData.phone_number || '',
          emailAddress: prev.emailAddress || userData.email || '',
          // Map country code to country name
          countryCode: prev.countryCode || userData.country_of_residence || 'ZAF',
          countryName: prev.countryName || getCountryName(userData.country_of_residence) || 'South Africa'
        }));
      }
    } catch (error) {
      console.error('❌ Error in loadUserData:', error);
    }
  };

  const getCountryName = (countryCode: string): string => {
    const countryMap: { [key: string]: string } = {
      'ZAF': 'South Africa',
      'NAM': 'Namibia',
      'SWZ': 'Eswatini',
      'BWA': 'Botswana',
      'ZWE': 'Zimbabwe',
      'MOZ': 'Mozambique',
      'LSO': 'Lesotho',
      'USA': 'United States',
      'GBR': 'United Kingdom',
      'CAN': 'Canada',
      'AUS': 'Australia',
      'DEU': 'Germany',
      'FRA': 'France',
      'IND': 'India',
      'IDN': 'Indonesia'
    };
    return countryMap[countryCode] || countryCode || 'Unknown';
  };
  const [formData, setFormData] = useState<KYCFormData>({
    firstName: existingKYC?.first_name || '',
    lastName: existingKYC?.last_name || '',
    idType: existingKYC?.id_type || 'national_id',
    idNumber: '',
    phoneNumber: existingKYC?.phone_number || '',
    emailAddress: existingKYC?.email_address || '',
    streetAddress: existingKYC?.street_address || '',
    city: existingKYC?.city || '',
    province: existingKYC?.province || '',
    postalCode: existingKYC?.postal_code || '',
    countryCode: existingKYC?.country_code || 'ZAF',
    countryName: existingKYC?.country_name || 'South Africa',
    nationality: existingKYC?.nationality || '',
    dataConsent: existingKYC?.data_consent_given || false,
    privacyPolicyAccepted: existingKYC?.privacy_policy_accepted || false
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isEditing, setIsEditing] = useState(!existingKYC);
  const [documents, setDocuments] = useState<{
    id_document?: DocumentFile;
    proof_of_residence?: DocumentFile;
  }>({});
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});

  const countries = [
    { code: 'ZAF', name: 'South Africa' },
    { code: 'USA', name: 'United States' },
    { code: 'GBR', name: 'United Kingdom' },
    { code: 'CAN', name: 'Canada' },
    { code: 'AUS', name: 'Australia' },
    { code: 'DEU', name: 'Germany' },
    { code: 'FRA', name: 'France' },
    { code: 'NLD', name: 'Netherlands' },
    { code: 'CHE', name: 'Switzerland' },
    { code: 'SWE', name: 'Sweden' }
  ];

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';
    if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';

    // Enhanced ID number validation
    if (!formData.idNumber.trim()) {
      newErrors.idNumber = 'ID/Document number is required';
    } else {
      const idValidationError = validateIdNumber(formData.idNumber);
      if (idValidationError) {
        newErrors.idNumber = idValidationError;
      }
    }

    if (!formData.phoneNumber.trim()) newErrors.phoneNumber = 'Phone number is required';
    if (!formData.emailAddress.trim()) newErrors.emailAddress = 'Email address is required';
    if (!formData.streetAddress.trim()) newErrors.streetAddress = 'Street address is required';
    if (!formData.city.trim()) newErrors.city = 'City is required';
    if (!formData.postalCode.trim()) newErrors.postalCode = 'Postal code is required';
    if (!formData.nationality.trim()) newErrors.nationality = 'Nationality is required';
    if (!formData.dataConsent) newErrors.dataConsent = 'Data consent is required';
    if (!formData.privacyPolicyAccepted) newErrors.privacyPolicyAccepted = 'Privacy policy acceptance is required';

    // Document validation
    if (!documents.id_document && !existingKYC) {
      newErrors.id_document = 'ID/Passport document is required';
    }
    if (!documents.proof_of_residence && !existingKYC) {
      newErrors.proof_of_residence = 'Proof of residence is required';
    }



    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.emailAddress && !emailRegex.test(formData.emailAddress)) {
      newErrors.emailAddress = 'Please enter a valid email address';
    }

    // Phone validation (basic)
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    if (formData.phoneNumber && !phoneRegex.test(formData.phoneNumber.replace(/\s/g, ''))) {
      newErrors.phoneNumber = 'Please enter a valid phone number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleFileUpload = (type: 'id_document' | 'proof_of_residence', file: File) => {
    // Validate file
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];

    if (file.size > maxSize) {
      setErrors(prev => ({ ...prev, [type]: 'File size must be less than 10MB' }));
      return;
    }

    if (!allowedTypes.includes(file.type)) {
      setErrors(prev => ({ ...prev, [type]: 'Only JPEG, PNG, and PDF files are allowed' }));
      return;
    }

    // Create preview
    const preview = URL.createObjectURL(file);

    setDocuments(prev => ({
      ...prev,
      [type]: { file, preview, type }
    }));

    // Clear error
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[type];
      return newErrors;
    });
  };

  const uploadDocument = async (type: 'id_document' | 'proof_of_residence', file: File): Promise<string> => {
    const fileExt = file.name.split('.').pop();
    const timestamp = Date.now();
    const fileName = `kyc_${type}_${userId}_${timestamp}.${fileExt}`;

    setUploadProgress(prev => ({ ...prev, [type]: 0 }));

    try {
      const { data, error } = await supabase.storage
        .from('proof')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) throw error;

      setUploadProgress(prev => ({ ...prev, [type]: 100 }));

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('proof')
        .getPublicUrl(fileName);

      return publicUrl;
    } catch (error) {
      console.error(`Error uploading ${type}:`, error);
      throw new Error(`Failed to upload ${type}: ${error.message}`);
    }
  };

  const hashIdNumber = async (idNumber: string): Promise<string> => {
    try {
      // Normalize ID number: remove spaces, hyphens, convert to uppercase
      const normalizedId = idNumber.replace(/[\s-]/g, '').toUpperCase();

      // Security salt to prevent rainbow table attacks
      const salt = 'AUREUS_KYC_ID_SALT_2024';
      const saltedId = salt + normalizedId + salt;

      // Check if crypto.subtle is available
      if (typeof window !== 'undefined' && window.crypto && window.crypto.subtle) {
        const encoder = new TextEncoder();
        const data = encoder.encode(saltedId);
        const hashBuffer = await window.crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
      } else {
        // Fallback: simple hash for development/testing
        let hash = 0;
        for (let i = 0; i < saltedId.length; i++) {
          const char = saltedId.charCodeAt(i);
          hash = ((hash << 5) - hash) + char;
          hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash).toString(16);
      }
    } catch (error) {
      console.error('Error hashing ID number:', error);
      // Fallback: simple hash with salt
      const normalizedId = idNumber.replace(/[\s-]/g, '').toUpperCase();
      const salt = 'AUREUS_KYC_ID_SALT_2024';
      const saltedId = salt + normalizedId + salt;

      let hash = 0;
      for (let i = 0; i < saltedId.length; i++) {
        const char = saltedId.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
      }
      return Math.abs(hash).toString(16);
    }
  };

  const checkIdNumberDuplicate = async (idNumber: string): Promise<boolean> => {
    try {
      const idNumberHash = await hashIdNumber(idNumber);
      const serviceClient = getServiceRoleClient();

      const { data, error } = await serviceClient
        .from('kyc_information')
        .select('user_id')
        .eq('id_number_hash', idNumberHash)
        .neq('user_id', userId) // Exclude current user for updates
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error checking ID number duplicate:', error);
        return false; // Allow submission if check fails
      }

      return !!data; // Returns true if duplicate found
    } catch (error) {
      console.error('Error in duplicate check:', error);
      return false; // Allow submission if check fails
    }
  };

  const validateIdNumber = (idNumber: string): string | null => {
    // Remove spaces and hyphens for validation
    const cleanId = idNumber.replace(/[\s-]/g, '');

    // Check length (6-25 characters for international compatibility)
    if (cleanId.length < 6 || cleanId.length > 25) {
      return 'ID number must be between 6 and 25 characters';
    }

    // Check for valid characters (alphanumeric only)
    if (!/^[A-Za-z0-9]+$/.test(cleanId)) {
      return 'ID number can only contain letters and numbers';
    }

    // Additional validation based on ID type
    if (formData.idType === 'national_id' && cleanId.length < 8) {
      return 'National ID must be at least 8 characters';
    }

    if (formData.idType === 'passport' && cleanId.length < 6) {
      return 'Passport number must be at least 6 characters';
    }

    return null; // Valid
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      // Check for duplicate ID number first (better user experience)
      console.log('🔍 Checking for duplicate ID number...');
      const isDuplicate = await checkIdNumberDuplicate(formData.idNumber);
      if (isDuplicate) {
        setErrors({ idNumber: 'This identification number is already registered with another account. Each ID can only be used once.' });
        return;
      }

      // Upload documents first
      const documentUrls: Record<string, string> = {};

      for (const [type, doc] of Object.entries(documents)) {
        if (doc && doc.file) {
          try {
            const url = await uploadDocument(type as 'id_document' | 'proof_of_residence', doc.file);
            documentUrls[type] = url;
          } catch (uploadError) {
            throw new Error(`Failed to upload ${type}: ${uploadError.message}`);
          }
        }
      }

      // Hash the ID number for duplicate checking
      const idNumberHash = await hashIdNumber(formData.idNumber);

      // Simple encryption (in production, this should be done server-side)
      const encryptedIdNumber = btoa(formData.idNumber);

      const kycData = {
        user_id: userId,
        first_name: formData.firstName.trim(),
        last_name: formData.lastName.trim(),
        id_type: formData.idType,
        id_number_encrypted: encryptedIdNumber,
        id_number_hash: idNumberHash,
        phone_number: formData.phoneNumber.trim(),
        email_address: formData.emailAddress.trim(),
        street_address: formData.streetAddress.trim(),
        city: formData.city.trim(),
        province: formData.province.trim(),
        postal_code: formData.postalCode.trim(),
        country_code: formData.countryCode,
        country_name: formData.countryName,
        nationality: formData.nationality.trim(),
        data_consent_given: formData.dataConsent,
        privacy_policy_accepted: formData.privacyPolicyAccepted,
        kyc_status: 'completed',
        kyc_completed_at: new Date().toISOString()
      };

      let result;

      // Try with regular client first, fallback to service role client if needed
      let client = supabase;
      let useServiceRole = false;

      try {
        if (existingKYC) {
          // Update existing KYC
          const { data, error } = await client
            .from('kyc_information')
            .update(kycData)
            .eq('id', existingKYC.id)
            .eq('user_id', userId)
            .select()
            .single();

          if (error) throw error;
          result = data;
        } else {
          // Check if user already has KYC record before creating new one
          const { data: existingRecord } = await client
            .from('kyc_information')
            .select('id, kyc_status')
            .eq('user_id', userId)
            .single();

          if (existingRecord) {
            throw new Error('KYC record already exists for this user. Please refresh the page.');
          }

          // Create new KYC
          const { data, error } = await client
            .from('kyc_information')
            .insert(kycData)
            .select()
            .single();

          if (error) {
            // Handle specific duplicate key errors
            if (error.code === '23505' && error.message.includes('id_number_hash')) {
              throw new Error('This ID/Passport number is already registered with another account.');
            }
            throw error;
          }
          result = data;
        }
      } catch (authError) {
        console.warn('⚠️ Regular client failed, trying service role client:', authError.message);

        // Fallback to service role client
        client = getServiceRoleClient();
        useServiceRole = true;

        if (existingKYC) {
          // Update existing KYC with service role
          const { data, error } = await client
            .from('kyc_information')
            .update(kycData)
            .eq('id', existingKYC.id)
            .eq('user_id', userId)
            .select()
            .single();

          if (error) throw error;
          result = data;
        } else {
          // Check if user already has KYC record before creating new one
          const { data: existingRecord } = await client
            .from('kyc_information')
            .select('id, kyc_status')
            .eq('user_id', userId)
            .single();

          if (existingRecord) {
            throw new Error('KYC record already exists for this user. Please refresh the page.');
          }

          // Create new KYC with service role
          const { data, error } = await client
            .from('kyc_information')
            .insert(kycData)
            .select()
            .single();

          if (error) {
            // Handle specific duplicate key errors
            if (error.code === '23505' && error.message.includes('id_number_hash')) {
              throw new Error('This ID/Passport number is already registered with another account.');
            }
            throw error;
          }
          result = data;
        }

        console.log('✅ KYC operation successful with service role client');
      }

      // Log the KYC action (optional - only if function exists)
      try {
        await supabase.rpc('log_kyc_action', {
          p_kyc_id: result.id,
          p_user_id: userId,
          p_action: existingKYC ? 'updated' : 'created',
          p_performed_by_username: 'web_user'
        });
      } catch (logError) {
        console.log('Note: KYC audit logging not available:', logError);
      }



      alert(existingKYC ? 'KYC information updated successfully!' : 'KYC verification completed successfully!');
      setIsEditing(false);

      if (onKYCComplete) {
        onKYCComplete('completed');
      }

    } catch (error: any) {
      console.error('Error submitting KYC:', error);

      let errorMessage = 'Failed to submit KYC information. Please try again.';

      // Handle specific database constraint errors
      if (error?.code === '23505') {
        if (error.message?.includes('id_number_hash')) {
          setErrors({ idNumber: 'This ID/Passport number is already registered with another account. Please contact support if you believe this is an error.' });
          return;
        } else if (error.message?.includes('email')) {
          setErrors({ emailAddress: 'This email address is already registered. Please use a different email address.' });
          return;
        } else {
          errorMessage = 'This information is already registered in our system. Please check your details or contact support.';
        }
      } else if (error?.code === '23503') {
        errorMessage = 'Invalid user account. Please log out and log back in.';
      } else if (error.message?.includes('duplicate')) {
        setErrors({ idNumber: 'This ID/Passport number is already registered' });
        return;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      alert(`Error: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof KYCFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }

    // Auto-update country name when country code changes
    if (field === 'countryCode') {
      const country = countries.find(c => c.code === value);
      if (country) {
        setFormData(prev => ({ ...prev, countryName: country.name }));
      }
    }
  };

  if (existingKYC && !isEditing) {
    return (
      <div className="bg-green-900/30 rounded-lg p-6 border border-green-700">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <span className="text-green-400 text-2xl">✅</span>
            <div>
              <h3 className="text-green-400 font-semibold text-lg">KYC Verification Complete</h3>
              <p className="text-green-200 text-sm">Your identity has been verified successfully</p>
            </div>
          </div>
          <button
            onClick={() => setIsEditing(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm"
          >
            Edit Information
          </button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-400">Full Name:</span>
            <span className="text-white ml-2">{existingKYC.full_legal_name}</span>
          </div>
          <div>
            <span className="text-gray-400">ID Type:</span>
            <span className="text-white ml-2">{existingKYC.id_type === 'national_id' ? 'National ID' : 'Passport'}</span>
          </div>
          <div>
            <span className="text-gray-400">Phone:</span>
            <span className="text-white ml-2">{existingKYC.phone_number}</span>
          </div>
          <div>
            <span className="text-gray-400">Email:</span>
            <span className="text-white ml-2">{existingKYC.email_address}</span>
          </div>
          <div>
            <span className="text-gray-400">Country:</span>
            <span className="text-white ml-2">{existingKYC.country_name}</span>
          </div>
          {existingKYC.nationality && (
            <div>
              <span className="text-gray-400">Nationality:</span>
              <span className="text-white ml-2">{existingKYC.nationality}</span>
            </div>
          )}
          <div>
            <span className="text-gray-400">Status:</span>
            <span className="text-green-400 ml-2 font-semibold">
              {existingKYC.kyc_status === 'completed' ? 'Verified' : existingKYC.kyc_status}
            </span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-white mb-2">
          {existingKYC ? 'Update KYC Information' : 'Complete KYC Verification'}
        </h3>
        <p className="text-gray-400 text-sm">
          Please provide accurate information for identity verification. This is required to access your share certificates.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Personal Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              First Name *
            </label>
            <input
              type="text"
              value={formData.firstName}
              onChange={(e) => handleInputChange('firstName', e.target.value)}
              className={`w-full px-3 py-2 bg-gray-700 border rounded-lg text-white ${
                errors.firstName ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder="Enter your first name"
            />
            {errors.firstName && <p className="text-red-400 text-xs mt-1">{errors.firstName}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Last Name *
            </label>
            <input
              type="text"
              value={formData.lastName}
              onChange={(e) => handleInputChange('lastName', e.target.value)}
              className={`w-full px-3 py-2 bg-gray-700 border rounded-lg text-white ${
                errors.lastName ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder="Enter your last name"
            />
            {errors.lastName && <p className="text-red-400 text-xs mt-1">{errors.lastName}</p>}
          </div>
        </div>

        {/* ID Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              ID Type *
            </label>
            <select
              value={formData.idType}
              onChange={(e) => handleInputChange('idType', e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
            >
              <optgroup label="Primary Government IDs">
                <option value="national_id">National Identity Card</option>
                <option value="passport">Passport</option>
                <option value="drivers_license">Driver's License</option>
              </optgroup>
              <optgroup label="Residence Documents">
                <option value="residence_permit">Residence Permit</option>
                <option value="work_permit">Work Permit</option>
                <option value="visa">Visa Document</option>
              </optgroup>
              <optgroup label="Secondary IDs">
                <option value="voter_id">Voter Registration Card</option>
                <option value="social_security">Social Security Card</option>
                <option value="military_id">Military ID Card</option>
              </optgroup>
              <optgroup label="Regional Variations">
                <option value="aadhaar">Aadhaar Card (India)</option>
                <option value="sin">Social Insurance Number (Canada)</option>
              </optgroup>
              <optgroup label="Other">
                <option value="other">Other Government ID</option>
              </optgroup>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {(() => {
                const typeLabels = {
                  'national_id': 'National ID Number',
                  'passport': 'Passport Number',
                  'drivers_license': 'Driver\'s License Number',
                  'residence_permit': 'Residence Permit Number',
                  'work_permit': 'Work Permit Number',
                  'visa': 'Visa Number',
                  'voter_id': 'Voter ID Number',
                  'social_security': 'Social Security Number',
                  'military_id': 'Military ID Number',
                  'aadhaar': 'Aadhaar Number',
                  'sin': 'Social Insurance Number',
                  'other': 'Document Number'
                };
                return typeLabels[formData.idType] || 'Document Number';
              })()} *
            </label>
            <input
              type="text"
              value={formData.idNumber}
              onChange={(e) => handleInputChange('idNumber', e.target.value)}
              className={`w-full px-3 py-2 bg-gray-700 border rounded-lg text-white ${
                errors.idNumber ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={(() => {
                const typePlaceholders = {
                  'national_id': 'Enter your national ID number',
                  'passport': 'Enter your passport number',
                  'drivers_license': 'Enter your driver\'s license number',
                  'residence_permit': 'Enter your residence permit number',
                  'work_permit': 'Enter your work permit number',
                  'visa': 'Enter your visa number',
                  'voter_id': 'Enter your voter ID number',
                  'social_security': 'Enter your social security number',
                  'military_id': 'Enter your military ID number',
                  'aadhaar': 'Enter your 12-digit Aadhaar number',
                  'sin': 'Enter your 9-digit SIN',
                  'other': 'Enter your document number'
                };
                return typePlaceholders[formData.idType] || 'Enter your document number';
              })()}
              maxLength={25}
            />
            {errors.idNumber && <p className="text-red-400 text-xs mt-1">{errors.idNumber}</p>}
            <p className="text-xs text-gray-400 mt-1">
              Accepts letters, numbers, spaces, and hyphens (6-25 characters)
            </p>
          </div>
        </div>

        {/* Nationality */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Nationality *
          </label>
          <input
            type="text"
            value={formData.nationality}
            onChange={(e) => handleInputChange('nationality', e.target.value)}
            className={`w-full px-3 py-2 bg-gray-700 border rounded-lg text-white ${
              errors.nationality ? 'border-red-500' : 'border-gray-600'
            }`}
            placeholder="Enter your nationality (e.g., South African, American, British)"
          />
          {errors.nationality && <p className="text-red-400 text-xs mt-1">{errors.nationality}</p>}
          <p className="text-xs text-gray-400 mt-1">
            Your nationality as it appears on your passport or official documents
          </p>
        </div>

        {/* Document Upload Section */}
        <div className="space-y-6 pt-6 border-t border-gray-700">
          <h4 className="text-lg font-semibold text-white">📄 Document Upload</h4>

          {/* ID Document Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {(() => {
                const documentLabels = {
                  'national_id': 'National ID Document',
                  'passport': 'Passport Document',
                  'drivers_license': 'Driver\'s License',
                  'residence_permit': 'Residence Permit',
                  'work_permit': 'Work Permit',
                  'visa': 'Visa Document',
                  'voter_id': 'Voter ID Card',
                  'social_security': 'Social Security Card',
                  'military_id': 'Military ID Card',
                  'aadhaar': 'Aadhaar Card',
                  'sin': 'Social Insurance Document',
                  'other': 'Government ID Document'
                };
                return documentLabels[formData.idType] || 'ID Document';
              })()} *
            </label>
            <div className="space-y-2">
              {!documents.id_document ? (
                <div className="border-2 border-dashed border-gray-600 rounded-lg p-6 text-center">
                  <div className="text-4xl mb-2">📄</div>
                  <p className="text-gray-400 mb-4">
                    Upload a clear photo or scan of your {(() => {
                      const documentNames = {
                        'national_id': 'national ID',
                        'passport': 'passport',
                        'drivers_license': 'driver\'s license',
                        'residence_permit': 'residence permit',
                        'work_permit': 'work permit',
                        'visa': 'visa document',
                        'voter_id': 'voter ID card',
                        'social_security': 'social security card',
                        'military_id': 'military ID card',
                        'aadhaar': 'Aadhaar card',
                        'sin': 'social insurance document',
                        'other': 'government ID document'
                      };
                      return documentNames[formData.idType] || 'ID document';
                    })()}
                  </p>
                  <input
                    type="file"
                    accept="image/*,application/pdf"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleFileUpload('id_document', file);
                    }}
                    className="hidden"
                    id="id-document-upload"
                  />
                  <label
                    htmlFor="id-document-upload"
                    className="inline-block px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer"
                  >
                    Choose File
                  </label>
                  <p className="text-xs text-gray-500 mt-2">
                    Supported: JPG, PNG, PDF • Max 10MB
                  </p>
                </div>
              ) : (
                <div className="bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">📄</span>
                      <div>
                        <p className="text-white font-medium">{documents.id_document.file.name}</p>
                        <p className="text-gray-400 text-sm">
                          {(documents.id_document.file.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={() => {
                        setDocuments(prev => ({ ...prev, id_document: undefined }));
                        setUploadProgress(prev => ({ ...prev, id_document: 0 }));
                      }}
                      className="text-red-400 hover:text-red-300"
                    >
                      ✕
                    </button>
                  </div>
                  {uploadProgress.id_document !== undefined && uploadProgress.id_document < 100 && (
                    <div className="mt-2">
                      <div className="bg-gray-600 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${uploadProgress.id_document}%` }}
                        />
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
            {errors.id_document && <p className="text-red-400 text-xs mt-1">{errors.id_document}</p>}
          </div>

          {/* Proof of Residence Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Proof of Residence *
            </label>
            <div className="space-y-2">
              {!documents.proof_of_residence ? (
                <div className="border-2 border-dashed border-gray-600 rounded-lg p-6 text-center">
                  <div className="text-4xl mb-2">🏠</div>
                  <p className="text-gray-400 mb-4">
                    Upload a recent utility bill, bank statement, or lease agreement
                  </p>
                  <input
                    type="file"
                    accept="image/*,application/pdf"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleFileUpload('proof_of_residence', file);
                    }}
                    className="hidden"
                    id="proof-residence-upload"
                  />
                  <label
                    htmlFor="proof-residence-upload"
                    className="inline-block px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer"
                  >
                    Choose File
                  </label>
                  <p className="text-xs text-gray-500 mt-2">
                    Supported: JPG, PNG, PDF • Max 10MB
                  </p>
                </div>
              ) : (
                <div className="bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">🏠</span>
                      <div>
                        <p className="text-white font-medium">{documents.proof_of_residence.file.name}</p>
                        <p className="text-gray-400 text-sm">
                          {(documents.proof_of_residence.file.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={() => {
                        setDocuments(prev => ({ ...prev, proof_of_residence: undefined }));
                        setUploadProgress(prev => ({ ...prev, proof_of_residence: 0 }));
                      }}
                      className="text-red-400 hover:text-red-300"
                    >
                      ✕
                    </button>
                  </div>
                  {uploadProgress.proof_of_residence !== undefined && uploadProgress.proof_of_residence < 100 && (
                    <div className="mt-2">
                      <div className="bg-gray-600 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${uploadProgress.proof_of_residence}%` }}
                        />
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
            {errors.proof_of_residence && <p className="text-red-400 text-xs mt-1">{errors.proof_of_residence}</p>}

            <div className="mt-2 p-3 bg-blue-900/30 rounded-lg border border-blue-700">
              <p className="text-blue-200 text-xs">
                <strong>Acceptable documents:</strong> Utility bill (electricity, water, gas), bank statement,
                lease agreement, or municipal rates bill dated within the last 3 months.
              </p>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        {/* Contact Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Phone Number *
            </label>
            <input
              type="tel"
              value={formData.phoneNumber}
              onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
              className={`w-full px-3 py-2 bg-gray-700 border rounded-lg text-white ${
                errors.phoneNumber ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder="+27 ************"
            />
            {errors.phoneNumber && <p className="text-red-400 text-xs mt-1">{errors.phoneNumber}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Email Address *
            </label>
            <input
              type="email"
              value={formData.emailAddress}
              onChange={(e) => handleInputChange('emailAddress', e.target.value)}
              className={`w-full px-3 py-2 bg-gray-700 border rounded-lg text-white ${
                errors.emailAddress ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder="<EMAIL>"
            />
            {errors.emailAddress && <p className="text-red-400 text-xs mt-1">{errors.emailAddress}</p>}
          </div>
        </div>

        {/* Address Information */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Street Address *
          </label>
          <textarea
            value={formData.streetAddress}
            onChange={(e) => handleInputChange('streetAddress', e.target.value)}
            className={`w-full px-3 py-2 bg-gray-700 border rounded-lg text-white ${
              errors.streetAddress ? 'border-red-500' : 'border-gray-600'
            }`}
            rows={2}
            placeholder="Enter your full street address"
          />
          {errors.streetAddress && <p className="text-red-400 text-xs mt-1">{errors.streetAddress}</p>}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              City *
            </label>
            <input
              type="text"
              value={formData.city}
              onChange={(e) => handleInputChange('city', e.target.value)}
              className={`w-full px-3 py-2 bg-gray-700 border rounded-lg text-white ${
                errors.city ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder="City"
            />
            {errors.city && <p className="text-red-400 text-xs mt-1">{errors.city}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Province/State
            </label>
            <input
              type="text"
              value={formData.province}
              onChange={(e) => handleInputChange('province', e.target.value)}
              className={`w-full px-3 py-2 bg-gray-700 border rounded-lg text-white ${
                errors.province ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder="Province or State"
            />
            {errors.province && <p className="text-red-400 text-xs mt-1">{errors.province}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Postal Code *
            </label>
            <input
              type="text"
              value={formData.postalCode}
              onChange={(e) => handleInputChange('postalCode', e.target.value)}
              className={`w-full px-3 py-2 bg-gray-700 border rounded-lg text-white ${
                errors.postalCode ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder="Postal Code"
            />
            {errors.postalCode && <p className="text-red-400 text-xs mt-1">{errors.postalCode}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Country *
            </label>
            <select
              value={formData.countryCode}
              onChange={(e) => handleInputChange('countryCode', e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
            >
              {countries.map(country => (
                <option key={country.code} value={country.code}>
                  {country.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Consent Checkboxes */}
        <div className="space-y-3 pt-4 border-t border-gray-700">
          <div className="flex items-start space-x-3">
            <input
              type="checkbox"
              id="dataConsent"
              checked={formData.dataConsent}
              onChange={(e) => handleInputChange('dataConsent', e.target.checked)}
              className="mt-1"
            />
            <label htmlFor="dataConsent" className="text-sm text-gray-300">
              I consent to the collection and processing of my personal data for KYC verification purposes. *
            </label>
          </div>
          {errors.dataConsent && <p className="text-red-400 text-xs">{errors.dataConsent}</p>}

          <div className="flex items-start space-x-3">
            <input
              type="checkbox"
              id="privacyPolicy"
              checked={formData.privacyPolicyAccepted}
              onChange={(e) => handleInputChange('privacyPolicyAccepted', e.target.checked)}
              className="mt-1"
            />
            <label htmlFor="privacyPolicy" className="text-sm text-gray-300">
              I have read and accept the Privacy Policy and Terms of Service. *
            </label>
          </div>
          {errors.privacyPolicyAccepted && <p className="text-red-400 text-xs">{errors.privacyPolicyAccepted}</p>}
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <button
            type="submit"
            disabled={loading}
            className="w-full px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
          >
            {loading ? 'Processing...' : (existingKYC ? 'Update KYC Information' : 'Complete KYC Verification')}
          </button>
        </div>
      </form>


    </div>
  );
};

export default KYCVerificationForm;
