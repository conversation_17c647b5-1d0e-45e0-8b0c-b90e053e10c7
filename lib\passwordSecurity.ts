import bcrypt from 'bcryptjs'

/**
 * SECURE PASSWORD UTILITIES
 * 
 * This module provides secure password hashing and validation using bcrypt
 * with dynamic salts, replacing the vulnerable static salt SHA-256 implementation.
 */

export const hashPassword = async (password: string): Promise<string> => {
  try {
    // Use 12 salt rounds for good security/performance balance
    // Each round doubles the computation time
    const saltRounds = 12
    const hash = await bcrypt.hash(password, saltRounds)
    
    console.log(`🔐 Password hashed successfully with ${saltRounds} rounds`)
    return hash
  } catch (error) {
    console.error('❌ Password hashing failed:', error)
    throw new Error('Password hashing failed')
  }
}

export const verifyPassword = async (password: string, hash: string): Promise<boolean> => {
  try {
    const isValid = await bcrypt.compare(password, hash)
    console.log(`🔍 Password verification: ${isValid ? 'SUCCESS' : 'FAILED'}`)
    return isValid
  } catch (error) {
    console.error('❌ Password verification failed:', error)
    return false
  }
}

export const validatePasswordStrength = (password: string): { valid: boolean; errors: string[]; requirements: { [key: string]: boolean } } => {
  const errors: string[] = []
  const requirements = {
    minLength: password.length >= 8,
    hasLowercase: /[a-z]/.test(password),
    hasUppercase: /[A-Z]/.test(password),
    hasNumber: /[0-9]/.test(password),
    hasSpecialChar: /[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)
  }

  // Length requirement (exactly 8 minimum)
  if (!requirements.minLength) {
    errors.push('Password must be at least 8 characters long')
  }

  // Character requirements (exactly as specified)
  if (!requirements.hasLowercase) {
    errors.push('Password must contain at least one lowercase letter (a-z)')
  }

  if (!requirements.hasUppercase) {
    errors.push('Password must contain at least one uppercase letter (A-Z)')
  }

  if (!requirements.hasNumber) {
    errors.push('Password must contain at least one number (0-9)')
  }

  if (!requirements.hasSpecialChar) {
    errors.push('Password must contain at least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)')
  }
  
  // Check for common weak passwords (exact matches only)
  const commonPasswords = [
    'password', '123456', 'qwerty', 'admin', 'letmein',
    'welcome', 'monkey', '1234567890', 'abc123', 'password123',
    'admin123', 'password1', '12345678', 'qwerty123'
  ]

  const lowerPassword = password.toLowerCase()
  const isCommonPassword = commonPasswords.includes(lowerPassword)

  if (isCommonPassword) {
    errors.push('Password is too common and easily guessable')
  }
  
  // Check for repeated characters (only excessive repetition)
  if (/(.)\1{4,}/.test(password)) {
    errors.push('Password should not contain excessive repeated characters')
  }
  
  const valid = errors.length === 0

  if (valid) {
    console.log('✅ Password strength validation passed')
  } else {
    console.log(`❌ Password strength validation failed: ${errors.length} issues`)
  }

  return { valid, errors, requirements }
}

/**
 * Check if a hash is using the old vulnerable format
 */
export const isOldHashFormat = (hash: string): boolean => {
  // Old SHA-256 hashes are 64 characters of hex
  return hash && hash.length === 64 && /^[a-f0-9]+$/.test(hash)
}

/**
 * Check if a hash is using the new bcrypt format
 */
export const isBcryptHash = (hash: string): boolean => {
  // Bcrypt hashes start with $2a$, $2b$, or $2y$
  return hash && /^\$2[aby]\$/.test(hash)
}

/**
 * Get password strength score (0-100)
 */
export const getPasswordStrengthScore = (password: string): number => {
  let score = 0
  
  // Length scoring
  if (password.length >= 8) score += 20
  if (password.length >= 12) score += 10
  if (password.length >= 16) score += 10
  
  // Character variety scoring
  if (/[a-z]/.test(password)) score += 10
  if (/[A-Z]/.test(password)) score += 10
  if (/\d/.test(password)) score += 10
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 15
  
  // Complexity bonus
  const charTypes = [
    /[a-z]/.test(password),
    /[A-Z]/.test(password),
    /\d/.test(password),
    /[!@#$%^&*(),.?":{}|<>]/.test(password)
  ].filter(Boolean).length
  
  if (charTypes >= 3) score += 10
  if (charTypes === 4) score += 5
  
  // Penalties for common patterns
  if (/123|abc|qwe/i.test(password)) score -= 10
  if (/(.)\1{2,}/.test(password)) score -= 10
  
  return Math.max(0, Math.min(100, score))
}

/**
 * Generate a secure random password
 */
export const generateSecurePassword = (length: number = 16): string => {
  const lowercase = 'abcdefghijklmnopqrstuvwxyz'
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const numbers = '0123456789'
  const symbols = '!@#$%^&*(),.?":{}|<>'
  
  const allChars = lowercase + uppercase + numbers + symbols
  
  let password = ''
  
  // Ensure at least one character from each category
  password += lowercase[Math.floor(Math.random() * lowercase.length)]
  password += uppercase[Math.floor(Math.random() * uppercase.length)]
  password += numbers[Math.floor(Math.random() * numbers.length)]
  password += symbols[Math.floor(Math.random() * symbols.length)]
  
  // Fill the rest randomly
  for (let i = 4; i < length; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)]
  }
  
  // Shuffle the password
  return password.split('').sort(() => Math.random() - 0.5).join('')
}

/**
 * Test the password hashing system
 */
export const testPasswordSystem = async (): Promise<boolean> => {
  try {
    console.log('🧪 Testing password system...')
    
    const testPassword = 'TestPassword123!'
    
    // Test hashing
    const hash1 = await hashPassword(testPassword)
    const hash2 = await hashPassword(testPassword)
    
    // Hashes should be different (different salts)
    if (hash1 === hash2) {
      console.error('❌ CRITICAL: Hashes are identical (salt issue)')
      return false
    }
    
    // Test verification
    const valid1 = await verifyPassword(testPassword, hash1)
    const valid2 = await verifyPassword(testPassword, hash2)
    const invalid = await verifyPassword('WrongPassword', hash1)
    
    if (!valid1 || !valid2 || invalid) {
      console.error('❌ Password verification failed')
      return false
    }
    
    // Test strength validation
    const strength = validatePasswordStrength(testPassword)
    if (!strength.valid) {
      console.error('❌ Password strength validation failed')
      return false
    }
    
    console.log('✅ Password system test passed')
    return true
    
  } catch (error) {
    console.error('❌ Password system test failed:', error)
    return false
  }
}
