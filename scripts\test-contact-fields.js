const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testContactFields() {
  console.log('🧪 Testing contact fields and adding sample data...');

  try {
    // First, let's see what users exist and their current contact info
    console.log('👥 Checking existing users...');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, username, email, full_name, phone_number, telegram_username')
      .limit(5);

    if (usersError) {
      console.error('❌ Error fetching users:', usersError);
      return;
    }

    console.log(`📊 Found ${users.length} users:`);
    users.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.full_name || user.username || user.email}`);
      console.log(`     📧 Email: ${user.email}`);
      console.log(`     📱 Phone: ${user.phone_number || 'Not set'}`);
      console.log(`     💬 Telegram: ${user.telegram_username || 'Not set'}`);
      console.log('');
    });

    // Let's add some sample contact data to the first user for testing
    if (users.length > 0) {
      const firstUser = users[0];
      console.log(`🔧 Adding sample contact data to user: ${firstUser.full_name || firstUser.username || firstUser.email}`);
      
      const { error: updateError } = await supabase
        .from('users')
        .update({
          phone_number: '+1234567890',
          telegram_username: 'jprademeyer'
        })
        .eq('id', firstUser.id);

      if (updateError) {
        console.error('❌ Error updating user contact info:', updateError);
      } else {
        console.log('✅ Sample contact data added successfully!');
      }
    }

    // Now let's test the payment query to see if contact info shows up
    console.log('🧪 Testing payment query with contact fields...');
    const { data: payments, error: paymentsError } = await supabase
      .from('crypto_payment_transactions')
      .select(`
        id,
        amount,
        status,
        users!inner(
          id,
          username,
          email,
          full_name,
          phone_number,
          telegram_username
        )
      `)
      .limit(3);

    if (paymentsError) {
      console.error('❌ Error fetching payments:', paymentsError);
      return;
    }

    console.log(`💳 Found ${payments.length} payments with user contact info:`);
    payments.forEach((payment, index) => {
      console.log(`\n  Payment ${index + 1}:`);
      console.log(`    💎 Amount: $${payment.amount}`);
      console.log(`    🆔 ID: ${payment.id}`);
      console.log(`    👤 User: ${payment.users?.full_name || payment.users?.username || 'Unknown'}`);
      console.log(`    📧 Email: ${payment.users?.email || 'Not provided'}`);
      console.log(`    📱 Phone: ${payment.users?.phone_number || 'Not provided'}`);
      console.log(`    💬 Telegram: ${payment.users?.telegram_username ? `@${payment.users.telegram_username}` : 'Not provided'}`);
      
      if (payment.users?.telegram_username) {
        console.log(`    🔗 Telegram Link: https://t.me/${payment.users.telegram_username}`);
      }
    });

    console.log('\n🎉 Contact fields test completed!');
    console.log('\n📋 Next Steps:');
    console.log('1. Refresh your payment management page');
    console.log('2. Look for payments from users with contact info');
    console.log('3. You should now see clickable Telegram and phone links');
    console.log('\n💡 To add contact info to more users, update the users table:');
    console.log('   UPDATE users SET telegram_username = \'username\', phone_number = \'+1234567890\' WHERE id = [user_id];');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testContactFields();
