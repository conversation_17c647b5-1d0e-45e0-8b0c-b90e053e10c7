/**
 * TELEGRAM USERS SYNC VERIFICATION SCRIPT
 * 
 * This script checks if all users in telegram_users table
 * are also present in the users table and identifies any
 * missing users or data inconsistencies.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkTelegramUsersSync() {
  console.log('🔍 TELEGRAM USERS SYNC VERIFICATION');
  console.log('=====================================\n');

  try {
    // Get all telegram users
    console.log('📱 Fetching telegram_users data...');
    const { data: telegramUsers, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .order('id', { ascending: true });

    if (telegramError) {
      throw new Error(`Failed to fetch telegram_users: ${telegramError.message}`);
    }

    console.log(`✅ Found ${telegramUsers.length} users in telegram_users table\n`);

    // Get all regular users
    console.log('👥 Fetching users data...');
    const { data: regularUsers, error: usersError } = await supabase
      .from('users')
      .select('*')
      .order('id', { ascending: true });

    if (usersError) {
      throw new Error(`Failed to fetch users: ${usersError.message}`);
    }

    console.log(`✅ Found ${regularUsers.length} users in users table\n`);

    // Create lookup maps
    const regularUsersMap = new Map();
    regularUsers.forEach(user => {
      regularUsersMap.set(user.id, user);
    });

    // Check for missing users and data inconsistencies
    const missingUsers = [];
    const dataInconsistencies = [];
    const validUsers = [];

    console.log('🔍 Checking for missing users and data inconsistencies...\n');

    for (const telegramUser of telegramUsers) {
      const regularUser = regularUsersMap.get(telegramUser.id);

      if (!regularUser) {
        missingUsers.push(telegramUser);
      } else {
        // Check for data inconsistencies
        const inconsistencies = [];

        if (telegramUser.username !== regularUser.username) {
          inconsistencies.push(`Username mismatch: telegram="${telegramUser.username}" vs users="${regularUser.username}"`);
        }

        if (telegramUser.email && regularUser.email && telegramUser.email !== regularUser.email) {
          inconsistencies.push(`Email mismatch: telegram="${telegramUser.email}" vs users="${regularUser.email}"`);
        }

        if (telegramUser.full_name && regularUser.full_name && telegramUser.full_name !== regularUser.full_name) {
          inconsistencies.push(`Full name mismatch: telegram="${telegramUser.full_name}" vs users="${regularUser.full_name}"`);
        }

        if (inconsistencies.length > 0) {
          dataInconsistencies.push({
            id: telegramUser.id,
            username: telegramUser.username,
            inconsistencies
          });
        } else {
          validUsers.push(telegramUser);
        }
      }
    }

    // Report results
    console.log('📊 SYNC VERIFICATION RESULTS');
    console.log('============================\n');

    console.log(`✅ Valid synced users: ${validUsers.length}`);
    console.log(`⚠️  Data inconsistencies: ${dataInconsistencies.length}`);
    console.log(`❌ Missing from users table: ${missingUsers.length}\n`);

    // Show missing users
    if (missingUsers.length > 0) {
      console.log('❌ USERS MISSING FROM users TABLE:');
      console.log('==================================');
      missingUsers.forEach((user, index) => {
        console.log(`${index + 1}. ID: ${user.id}`);
        console.log(`   Username: ${user.username || 'N/A'}`);
        console.log(`   Email: ${user.email || 'N/A'}`);
        console.log(`   Full Name: ${user.full_name || 'N/A'}`);
        console.log(`   Telegram ID: ${user.telegram_id || 'N/A'}`);
        console.log(`   Created: ${user.created_at || 'N/A'}`);
        console.log('');
      });
    }

    // Show data inconsistencies
    if (dataInconsistencies.length > 0) {
      console.log('⚠️  DATA INCONSISTENCIES:');
      console.log('========================');
      dataInconsistencies.forEach((user, index) => {
        console.log(`${index + 1}. ID: ${user.id} (${user.username})`);
        user.inconsistencies.forEach(inconsistency => {
          console.log(`   - ${inconsistency}`);
        });
        console.log('');
      });
    }

    // Check for users in users table but not in telegram_users
    console.log('🔍 Checking for users without telegram records...\n');
    
    const telegramUsersMap = new Map();
    telegramUsers.forEach(user => {
      telegramUsersMap.set(user.id, user);
    });

    const usersWithoutTelegram = regularUsers.filter(user => !telegramUsersMap.has(user.id));

    console.log(`👥 Users in users table without telegram records: ${usersWithoutTelegram.length}`);

    if (usersWithoutTelegram.length > 0) {
      console.log('\n👥 USERS WITHOUT TELEGRAM RECORDS:');
      console.log('==================================');
      usersWithoutTelegram.slice(0, 10).forEach((user, index) => {
        console.log(`${index + 1}. ID: ${user.id}`);
        console.log(`   Username: ${user.username || 'N/A'}`);
        console.log(`   Email: ${user.email || 'N/A'}`);
        console.log(`   Full Name: ${user.full_name || 'N/A'}`);
        console.log(`   Created: ${user.created_at || 'N/A'}`);
        console.log('');
      });

      if (usersWithoutTelegram.length > 10) {
        console.log(`... and ${usersWithoutTelegram.length - 10} more users\n`);
      }
    }

    // Summary and recommendations
    console.log('📋 SUMMARY AND RECOMMENDATIONS');
    console.log('==============================\n');

    if (missingUsers.length === 0 && dataInconsistencies.length === 0) {
      console.log('✅ All telegram users are properly synced with the users table!');
      console.log('✅ No data inconsistencies found.');
    } else {
      if (missingUsers.length > 0) {
        console.log(`❌ ${missingUsers.length} telegram users are missing from the users table.`);
        console.log('   Recommendation: Run user migration script to create missing users.');
      }

      if (dataInconsistencies.length > 0) {
        console.log(`⚠️  ${dataInconsistencies.length} users have data inconsistencies.`);
        console.log('   Recommendation: Review and update inconsistent data.');
      }
    }

    if (usersWithoutTelegram.length > 0) {
      console.log(`ℹ️  ${usersWithoutTelegram.length} users were created directly (not from Telegram).`);
      console.log('   This is normal for web-only registrations.');
    }

    console.log('\n🔧 NEXT STEPS:');
    if (missingUsers.length > 0) {
      console.log('1. Create missing users in the users table');
      console.log('2. Ensure referral relationships are maintained');
      console.log('3. Verify commission balances are created');
    }
    if (dataInconsistencies.length > 0) {
      console.log('1. Review data inconsistencies');
      console.log('2. Decide which source is authoritative');
      console.log('3. Update inconsistent records');
    }

    // Return results for potential automated processing
    return {
      totalTelegramUsers: telegramUsers.length,
      totalRegularUsers: regularUsers.length,
      validUsers: validUsers.length,
      missingUsers: missingUsers.length,
      dataInconsistencies: dataInconsistencies.length,
      usersWithoutTelegram: usersWithoutTelegram.length,
      missingUsersList: missingUsers,
      dataInconsistenciesList: dataInconsistencies,
      usersWithoutTelegramList: usersWithoutTelegram
    };

  } catch (error) {
    console.error('❌ Error during sync verification:', error);
    throw error;
  }
}

// Run the check
if (import.meta.url === `file://${process.argv[1]}`) {
  checkTelegramUsersSync()
    .then((results) => {
      console.log('\n✅ Sync verification completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Sync verification failed:', error);
      process.exit(1);
    });
}

export { checkTelegramUsersSync };
