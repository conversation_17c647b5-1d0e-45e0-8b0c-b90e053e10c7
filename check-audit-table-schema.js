#!/usr/bin/env node

/**
 * CHECK AUDIT TABLE SCHEMA
 * 
 * This script checks the current schema of the admin_audit_logs table
 * to see which columns actually exist.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const checkAuditTableSchema = async () => {
  try {
    console.log('🔍 Checking admin_audit_logs table schema...\n');

    // Try to get one record to see the actual columns
    const { data, error } = await supabase
      .from('admin_audit_logs')
      .select('*')
      .limit(1);

    if (error) {
      console.error('❌ Error querying admin_audit_logs table:', error);
      
      if (error.message.includes('relation "admin_audit_logs" does not exist')) {
        console.log('📋 Table does not exist. Need to create it.');
        return;
      }
      
      return;
    }

    if (data && data.length > 0) {
      console.log('📋 Current table columns:');
      const columns = Object.keys(data[0]);
      columns.forEach(col => {
        console.log(`   ✓ ${col}`);
      });
      
      console.log('\n📋 Sample record:');
      console.log(JSON.stringify(data[0], null, 2));
    } else {
      // Table exists but is empty, try to get schema info differently
      console.log('📋 Table exists but is empty. Trying to insert a test record to see schema...');
      
      // Try inserting with old schema
      const { error: oldSchemaError } = await supabase
        .from('admin_audit_logs')
        .insert({
          admin_telegram_id: 123456789,
          admin_username: 'test',
          action: 'TEST_ACTION',
          target_type: 'test',
          target_id: 'test_id',
          details: {}
        });

      if (!oldSchemaError) {
        console.log('✅ Table uses OLD SCHEMA (admin_telegram_id, admin_username)');
        
        // Clean up test record
        await supabase
          .from('admin_audit_logs')
          .delete()
          .eq('action', 'TEST_ACTION');
          
        return;
      }

      // Try inserting with new schema
      const { error: newSchemaError } = await supabase
        .from('admin_audit_logs')
        .insert({
          admin_email: '<EMAIL>',
          action: 'TEST_ACTION',
          target_type: 'test',
          target_id: 'test_id',
          old_values: {},
          new_values: {},
          details: {}
        });

      if (!newSchemaError) {
        console.log('✅ Table uses NEW SCHEMA (admin_email, admin_user_id)');
        
        // Clean up test record
        await supabase
          .from('admin_audit_logs')
          .delete()
          .eq('action', 'TEST_ACTION');
          
        return;
      }

      console.log('❌ Could not determine schema:');
      console.log('   Old schema error:', oldSchemaError);
      console.log('   New schema error:', newSchemaError);
    }

  } catch (error) {
    console.error('❌ Check failed:', error);
  }
};

checkAuditTableSchema();
