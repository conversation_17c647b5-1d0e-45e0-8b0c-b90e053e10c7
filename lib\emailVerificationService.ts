/**
 * EMAIL VERIFICATION SERVICE
 * 
 * Handles email verification workflows with 6-digit PIN codes:
 * - Code generation and validation
 * - Rate limiting and security
 * - Integration with Resend email service
 * - Audit logging and monitoring
 */

import bcrypt from 'bcryptjs';
import { supabase } from './supabase';
import { resendEmailService } from './resendEmailService';

// Configuration
const VERIFICATION_CODE_LENGTH = 6;
const VERIFICATION_EXPIRY_MINUTES = parseInt(process.env.EMAIL_VERIFICATION_EXPIRY_MINUTES || '15');
const MAX_VERIFICATION_ATTEMPTS = parseInt(process.env.EMAIL_VERIFICATION_MAX_ATTEMPTS || '3');
const RATE_LIMIT_WINDOW_MINUTES = parseInt(process.env.EMAIL_RATE_LIMIT_WINDOW_MINUTES || '10');
const MAX_CODES_PER_WINDOW = 3;

export interface VerificationCodeRequest {
  userId: number;
  email: string;
  purpose: 'registration' | 'account_update' | 'withdrawal' | 'password_reset' | 'telegram_connection';
  ipAddress?: string;
  userAgent?: string;
}

export interface VerificationCodeValidation {
  userId: number;
  email: string;
  code: string;
  purpose: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface VerificationResult {
  success: boolean;
  message: string;
  error?: string;
  attemptsRemaining?: number;
  nextCodeAllowedAt?: Date;
}

export interface VerificationCodeData {
  id: string;
  userId: number;
  email: string;
  codeHash: string;
  purpose: string;
  expiresAt: Date;
  attempts: number;
  verifiedAt?: Date;
  createdAt: Date;
}

class EmailVerificationService {
  /**
   * Generate and send a 6-digit verification code
   */
  async generateAndSendCode(request: VerificationCodeRequest): Promise<VerificationResult> {
    try {
      const { userId, email, purpose, ipAddress, userAgent } = request;

      // Check rate limiting
      const rateLimitCheck = await this.checkRateLimit(email, ipAddress);
      if (!rateLimitCheck.allowed) {
        return {
          success: false,
          message: 'Too many verification requests. Please try again later.',
          error: 'RATE_LIMITED',
          nextCodeAllowedAt: rateLimitCheck.nextAllowedAt
        };
      }

      // Generate 6-digit code
      const code = this.generateSecureCode();
      const codeHash = await this.hashCode(code);

      // Calculate expiry time
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + VERIFICATION_EXPIRY_MINUTES);

      // Store verification code in database
      const { data: verificationData, error: dbError } = await supabase
        .from('email_verification_codes')
        .insert({
          user_id: userId,
          email: email,
          code_hash: codeHash,
          purpose: purpose,
          expires_at: expiresAt.toISOString(),
          attempts: 0,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (dbError) {
        console.error('❌ Error storing verification code:', dbError);
        return {
          success: false,
          message: 'Failed to generate verification code',
          error: dbError.message
        };
      }

      // Get user name for personalized email
      const { data: userData } = await supabase
        .from('users')
        .select('full_name, username')
        .eq('id', userId)
        .single();

      const userName = userData?.full_name || userData?.username;

      // Send email via Resend
      const emailResult = await resendEmailService.sendVerificationCode({
        email,
        code,
        purpose,
        userName,
        expiryMinutes: VERIFICATION_EXPIRY_MINUTES
      });

      if (!emailResult.success) {
        // Clean up database record if email failed
        await supabase
          .from('email_verification_codes')
          .delete()
          .eq('id', verificationData.id);

        return {
          success: false,
          message: 'Failed to send verification email',
          error: emailResult.error
        };
      }

      // Log the verification request
      await this.logVerificationEvent('CODE_GENERATED', {
        userId,
        email,
        purpose,
        ipAddress,
        userAgent,
        verificationId: verificationData.id
      });

      console.log(`✅ Verification code sent to ${email} for ${purpose}`);

      return {
        success: true,
        message: `Verification code sent to ${email}. Please check your email and enter the 6-digit code.`
      };

    } catch (error) {
      console.error('❌ Error generating verification code:', error);
      return {
        success: false,
        message: 'Failed to generate verification code',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Validate a verification code
   */
  async validateCode(validation: VerificationCodeValidation): Promise<VerificationResult> {
    try {
      const { userId, email, code, purpose, ipAddress, userAgent } = validation;

      // Find active verification code
      const { data: verificationData, error: findError } = await supabase
        .from('email_verification_codes')
        .select('*')
        .eq('user_id', userId)
        .eq('email', email)
        .eq('purpose', purpose)
        .is('verified_at', null)
        .gt('expires_at', new Date().toISOString())
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (findError || !verificationData) {
        await this.logVerificationEvent('CODE_NOT_FOUND', {
          userId,
          email,
          purpose,
          ipAddress,
          userAgent,
          code: code.substring(0, 2) + '****'
        });

        return {
          success: false,
          message: 'Invalid or expired verification code',
          error: 'CODE_NOT_FOUND'
        };
      }

      // Check if too many attempts
      if (verificationData.attempts >= MAX_VERIFICATION_ATTEMPTS) {
        await this.logVerificationEvent('MAX_ATTEMPTS_EXCEEDED', {
          userId,
          email,
          purpose,
          ipAddress,
          userAgent,
          verificationId: verificationData.id
        });

        return {
          success: false,
          message: 'Too many failed attempts. Please request a new verification code.',
          error: 'MAX_ATTEMPTS_EXCEEDED'
        };
      }

      // Verify the code
      const isValidCode = await this.verifyCode(code, verificationData.code_hash);

      // Increment attempt counter
      await supabase
        .from('email_verification_codes')
        .update({
          attempts: verificationData.attempts + 1,
          updated_at: new Date().toISOString()
        })
        .eq('id', verificationData.id);

      if (!isValidCode) {
        const attemptsRemaining = MAX_VERIFICATION_ATTEMPTS - (verificationData.attempts + 1);

        await this.logVerificationEvent('CODE_INVALID', {
          userId,
          email,
          purpose,
          ipAddress,
          userAgent,
          verificationId: verificationData.id,
          attemptsRemaining
        });

        return {
          success: false,
          message: `Invalid verification code. ${attemptsRemaining} attempts remaining.`,
          error: 'INVALID_CODE',
          attemptsRemaining
        };
      }

      // Mark as verified
      await supabase
        .from('email_verification_codes')
        .update({
          verified_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', verificationData.id);

      // Log successful verification
      await this.logVerificationEvent('CODE_VERIFIED', {
        userId,
        email,
        purpose,
        ipAddress,
        userAgent,
        verificationId: verificationData.id
      });

      console.log(`✅ Email verification successful for ${email} (${purpose})`);

      return {
        success: true,
        message: 'Email verification successful'
      };

    } catch (error) {
      console.error('❌ Error validating verification code:', error);
      return {
        success: false,
        message: 'Failed to validate verification code',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Check if user has verified email for specific purpose
   */
  async isEmailVerified(userId: number, email: string, purpose: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('email_verification_codes')
        .select('verified_at')
        .eq('user_id', userId)
        .eq('email', email)
        .eq('purpose', purpose)
        .not('verified_at', 'is', null)
        .order('verified_at', { ascending: false })
        .limit(1)
        .single();

      if (error || !data) {
        return false;
      }

      // Check if verification is recent (within 24 hours for sensitive operations)
      const verifiedAt = new Date(data.verified_at);
      const now = new Date();
      const hoursSinceVerification = (now.getTime() - verifiedAt.getTime()) / (1000 * 60 * 60);

      // For sensitive operations like withdrawals, require recent verification
      if (purpose === 'withdrawal' && hoursSinceVerification > 24) {
        return false;
      }

      return true;

    } catch (error) {
      console.error('❌ Error checking email verification status:', error);
      return false;
    }
  }

  /**
   * Clean up expired verification codes
   */
  async cleanupExpiredCodes(): Promise<void> {
    try {
      const { error } = await supabase
        .from('email_verification_codes')
        .delete()
        .lt('expires_at', new Date().toISOString());

      if (error) {
        console.error('❌ Error cleaning up expired codes:', error);
      } else {
        console.log('✅ Expired verification codes cleaned up');
      }
    } catch (error) {
      console.error('❌ Error in cleanup process:', error);
    }
  }

  /**
   * Generate cryptographically secure 6-digit code
   */
  private generateSecureCode(): string {
    // Use crypto.getRandomValues for secure random generation
    const array = new Uint32Array(1);
    crypto.getRandomValues(array);
    
    // Generate 6-digit code (000000-999999)
    const code = (array[0] % 1000000).toString().padStart(6, '0');
    return code;
  }

  /**
   * Hash verification code for secure storage
   */
  private async hashCode(code: string): Promise<string> {
    const saltRounds = 12;
    return await bcrypt.hash(code, saltRounds);
  }

  /**
   * Verify code against hash
   */
  private async verifyCode(code: string, hash: string): Promise<boolean> {
    return await bcrypt.compare(code, hash);
  }

  /**
   * Check rate limiting for verification requests
   */
  private async checkRateLimit(email: string, ipAddress?: string): Promise<{
    allowed: boolean;
    nextAllowedAt?: Date;
  }> {
    try {
      const windowStart = new Date();
      windowStart.setMinutes(windowStart.getMinutes() - RATE_LIMIT_WINDOW_MINUTES);

      // Check email-based rate limit
      const { data: emailCodes, error: emailError } = await supabase
        .from('email_verification_codes')
        .select('created_at')
        .eq('email', email)
        .gte('created_at', windowStart.toISOString());

      if (emailError) {
        console.error('❌ Error checking email rate limit:', emailError);
        return { allowed: true }; // Allow on error to avoid blocking users
      }

      if (emailCodes && emailCodes.length >= MAX_CODES_PER_WINDOW) {
        const oldestCode = new Date(emailCodes[0].created_at);
        const nextAllowedAt = new Date(oldestCode);
        nextAllowedAt.setMinutes(nextAllowedAt.getMinutes() + RATE_LIMIT_WINDOW_MINUTES);

        return { allowed: false, nextAllowedAt };
      }

      // Check IP-based rate limit if provided
      if (ipAddress) {
        // TODO: Implement IP-based rate limiting
        // This would require additional logging of IP addresses
      }

      return { allowed: true };

    } catch (error) {
      console.error('❌ Error in rate limit check:', error);
      return { allowed: true }; // Allow on error
    }
  }

  /**
   * Log verification events for audit trail
   */
  private async logVerificationEvent(eventType: string, data: Record<string, any>): Promise<void> {
    try {
      await supabase
        .from('account_change_logs')
        .insert({
          user_id: data.userId,
          field_changed: 'email_verification',
          new_value_hash: await bcrypt.hash(JSON.stringify({
            eventType,
            email: data.email,
            purpose: data.purpose,
            timestamp: new Date().toISOString()
          }), 10),
          verification_method: 'email_code',
          email_verified: eventType === 'CODE_VERIFIED',
          verified_at: eventType === 'CODE_VERIFIED' ? new Date().toISOString() : null,
          ip_address: data.ipAddress,
          user_agent: data.userAgent,
          created_at: new Date().toISOString()
        });
    } catch (error) {
      console.error('❌ Error logging verification event:', error);
    }
  }
}

// Export singleton instance
export const emailVerificationService = new EmailVerificationService();
export default emailVerificationService;
