/**
 * VERIFY CODE API
 *
 * Handles verification of 6-digit PIN codes for various purposes:
 * - Registration verification
 * - Password reset
 * - Telegram account connection
 * - Account updates
 */

import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client with service role
const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Configuration
const MAX_VERIFICATION_ATTEMPTS = 3;

// Helper function
const verifyCode = async (code, codeHash) => {
  return await bcrypt.compare(code, codeHash);
};

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { userId, email, code, purpose } = req.body;

    // Validate required fields
    if (!userId || !email || !code || !purpose) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: userId, email, code, and purpose are required'
      });
    }

    // Validate code format (6 digits)
    if (!/^\d{6}$/.test(code)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid code format. Code must be 6 digits.'
      });
    }

    // Validate purpose
    const validPurposes = ['registration', 'password_reset', 'telegram_connection', 'account_update', 'withdrawal'];
    if (!validPurposes.includes(purpose)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid purpose. Must be one of: ' + validPurposes.join(', ')
      });
    }

    console.log(`🔍 Verifying code for ${purpose} - User ID: ${userId}, Email: ${email}`);

    // Get the most recent verification code for this user/email/purpose
    const { data: verificationRecord, error: fetchError } = await supabase
      .from('email_verification_codes')
      .select('*')
      .eq('user_id', parseInt(userId))
      .eq('email', email)
      .eq('purpose', purpose)
      .is('verified_at', null)
      .gt('expires_at', new Date().toISOString())
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (fetchError || !verificationRecord) {
      console.error(`❌ No valid verification code found for ${purpose}:`, fetchError);
      return res.status(400).json({
        success: false,
        message: 'No valid verification code found or code has expired'
      });
    }

    // Check if too many attempts
    if (verificationRecord.attempts >= MAX_VERIFICATION_ATTEMPTS) {
      return res.status(400).json({
        success: false,
        message: 'Too many verification attempts. Please request a new code.',
        attemptsRemaining: 0
      });
    }

    // Verify the code
    const isValidCode = await verifyCode(code, verificationRecord.code_hash);

    // Increment attempt counter
    const newAttempts = verificationRecord.attempts + 1;

    if (isValidCode) {
      // Mark as verified
      const { error: updateError } = await supabase
        .from('email_verification_codes')
        .update({
          verified_at: new Date().toISOString(),
          attempts: newAttempts
        })
        .eq('id', verificationRecord.id);

      if (updateError) {
        console.error('❌ Error updating verification record:', updateError);
        return res.status(500).json({
          success: false,
          message: 'Failed to update verification status'
        });
      }

      console.log(`✅ Code verification successful for ${purpose}`);
      return res.status(200).json({
        success: true,
        message: 'Code verified successfully',
        verifiedAt: new Date().toISOString()
      });
    } else {
      // Update attempt counter
      await supabase
        .from('email_verification_codes')
        .update({ attempts: newAttempts })
        .eq('id', verificationRecord.id);

      const attemptsRemaining = MAX_VERIFICATION_ATTEMPTS - newAttempts;
      console.error(`❌ Code verification failed for ${purpose} - ${attemptsRemaining} attempts remaining`);

      return res.status(400).json({
        success: false,
        message: `Invalid verification code. ${attemptsRemaining} attempts remaining.`,
        attemptsRemaining
      });
    }

  } catch (error) {
    console.error('❌ Verify code error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
}
