#!/usr/bin/env node

/**
 * TEST TELEGRAM ID FIX
 * 
 * This script tests that the telegram ID lookup now works correctly
 * by querying the telegram_users table instead of the users table.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testTelegramIdFix() {
  console.log('🧪 TESTING TELEGRAM ID FIX');
  console.log('===========================\n');
  
  const testTelegramId = 1393852532;
  
  try {
    console.log('🔍 Testing telegram_users table lookup (CORRECT)...');
    
    // Test the correct way - query telegram_users table
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', testTelegramId)
      .maybeSingle();

    if (telegramError) {
      console.log('   ❌ Telegram users query failed:', telegramError.message);
    } else if (telegramUser) {
      console.log('   ✅ Found user in telegram_users table:');
      console.log(`      Telegram ID: ${telegramUser.telegram_id}`);
      console.log(`      Username: ${telegramUser.username}`);
      console.log(`      First Name: ${telegramUser.first_name}`);
      console.log(`      User ID: ${telegramUser.user_id || 'Not linked'}`);
      console.log(`      Temp Email: ${telegramUser.temp_email || 'None'}`);
      console.log(`      Registered: ${telegramUser.is_registered}`);
      
      // If user has a linked user_id, get the main user record
      if (telegramUser.user_id) {
        console.log('\n🔗 Checking linked user record...');
        
        const { data: linkedUser, error: linkedError } = await supabase
          .from('users')
          .select('id, username, email, telegram_id, password_hash')
          .eq('id', telegramUser.user_id)
          .maybeSingle();

        if (linkedError) {
          console.log('   ❌ Linked user query failed:', linkedError.message);
        } else if (linkedUser) {
          console.log('   ✅ Found linked user record:');
          console.log(`      User ID: ${linkedUser.id}`);
          console.log(`      Username: ${linkedUser.username}`);
          console.log(`      Email: ${linkedUser.email}`);
          console.log(`      Has Password: ${!!linkedUser.password_hash}`);
        } else {
          console.log('   ⚠️ No linked user record found');
        }
      }
      
    } else {
      console.log('   ⚠️ No user found with that Telegram ID');
    }

    console.log('\n🚫 Testing users table lookup (INCORRECT - should not be used)...');
    
    // Test the incorrect way - query users table directly
    const { data: userDirect, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('telegram_id', testTelegramId)
      .maybeSingle();

    if (userError) {
      console.log('   ❌ Users table query failed:', userError.message);
      console.log('   ✅ This is expected - users table should not be queried directly for telegram_id');
    } else if (userDirect) {
      console.log('   ⚠️ Found user in users table (this might be legacy data):');
      console.log(`      User ID: ${userDirect.id}`);
      console.log(`      Username: ${userDirect.username}`);
      console.log(`      Email: ${userDirect.email}`);
    } else {
      console.log('   ✅ No user found in users table (this is expected)');
    }

    console.log('\n🎯 TESTING COMPLETE');
    console.log('==================');
    console.log('✅ The fix ensures telegram_id is looked up in telegram_users table first');
    console.log('✅ Then linked to users table via user_id if needed');
    console.log('✅ This should resolve the 406 error you were seeing');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testTelegramIdFix().catch(console.error);
