/**
 * COMPREHENSIVE SECURITY HEADERS SYSTEM
 * 
 * This module provides comprehensive security headers including
 * CSP, HSTS, and other security-focused HTTP headers.
 */

interface SecurityHeadersConfig {
  contentSecurityPolicy: boolean;
  strictTransportSecurity: boolean;
  xFrameOptions: boolean;
  xContentTypeOptions: boolean;
  xXSSProtection: boolean;
  referrerPolicy: boolean;
  permissionsPolicy: boolean;
  crossOriginEmbedderPolicy: boolean;
  crossOriginOpenerPolicy: boolean;
  crossOriginResourcePolicy: boolean;
}

interface CSPDirectives {
  defaultSrc: string[];
  scriptSrc: string[];
  styleSrc: string[];
  imgSrc: string[];
  connectSrc: string[];
  fontSrc: string[];
  objectSrc: string[];
  mediaSrc: string[];
  frameSrc: string[];
  childSrc: string[];
  workerSrc: string[];
  manifestSrc: string[];
  formAction: string[];
  frameAncestors: string[];
  baseUri: string[];
  upgradeInsecureRequests: boolean;
  blockAllMixedContent: boolean;
}

class SecurityHeadersManager {
  private readonly defaultConfig: SecurityHeadersConfig = {
    contentSecurityPolicy: true,
    strictTransportSecurity: true,
    xFrameOptions: true,
    xContentTypeOptions: true,
    xXSSProtection: true,
    referrerPolicy: true,
    permissionsPolicy: true,
    crossOriginEmbedderPolicy: false, // Can break some functionality
    crossOriginOpenerPolicy: true,
    crossOriginResourcePolicy: false // Can break some functionality
  };

  private readonly productionCSP: CSPDirectives = {
    defaultSrc: ["'self'"],
    scriptSrc: [
      "'self'",
      "'unsafe-inline'", // Required for Next.js
      "'unsafe-eval'", // Required for development
      "https://vercel.live",
      "https://*.supabase.co",
      "https://js.stripe.com",
      "https://checkout.stripe.com",
      "https://cdn.jsdelivr.net"
    ],
    styleSrc: [
      "'self'",
      "'unsafe-inline'", // Required for styled-components
      "https://fonts.googleapis.com"
    ],
    imgSrc: [
      "'self'",
      "data:",
      "blob:",
      "https:",
      "https://*.supabase.co",
      "https://images.unsplash.com",
      "https://via.placeholder.com"
    ],
    connectSrc: [
      "'self'",
      "https://*.supabase.co",
      "https://api.stripe.com",
      "https://checkout.stripe.com",
      "https://cdn.jsdelivr.net",
      "wss://*.supabase.co",
      "https://api.resend.com"
    ],
    fontSrc: [
      "'self'",
      "https://fonts.gstatic.com",
      "data:"
    ],
    objectSrc: ["'none'"],
    mediaSrc: ["'self'", "https:"],
    frameSrc: [
      "'self'",
      "https://js.stripe.com",
      "https://checkout.stripe.com"
    ],
    childSrc: ["'self'"],
    workerSrc: ["'self'", "blob:"],
    manifestSrc: ["'self'"],
    formAction: ["'self'"],
    frameAncestors: ["'none'"],
    baseUri: ["'self'"],
    upgradeInsecureRequests: true,
    blockAllMixedContent: true
  };

  private readonly developmentCSP: CSPDirectives = {
    ...this.productionCSP,
    scriptSrc: [
      "'self'",
      "'unsafe-inline'",
      "'unsafe-eval'", // Required for development
      "https://vercel.live",
      "https://*.supabase.co",
      "https://js.stripe.com",
      "https://checkout.stripe.com",
      "http://localhost:*",
      "ws://localhost:*",
      "https://cdn.jsdelivr.net"
    ],
    connectSrc: [
      "'self'",
      "https://*.supabase.co",
      "https://api.stripe.com",
      "https://checkout.stripe.com",
      "https://cdn.jsdelivr.net",
      "wss://*.supabase.co",
      "http://localhost:*",
      "ws://localhost:*",
      "wss://localhost:*",
      "https://api.resend.com"
    ],
    upgradeInsecureRequests: false // Disabled for development
  };

  /**
   * Generate Content Security Policy header value
   */
  private generateCSP(isDevelopment: boolean = false): string {
    const csp = isDevelopment ? this.developmentCSP : this.productionCSP;
    
    const directives: string[] = [];

    // Add each directive
    if (csp.defaultSrc.length > 0) {
      directives.push(`default-src ${csp.defaultSrc.join(' ')}`);
    }
    
    if (csp.scriptSrc.length > 0) {
      directives.push(`script-src ${csp.scriptSrc.join(' ')}`);
    }
    
    if (csp.styleSrc.length > 0) {
      directives.push(`style-src ${csp.styleSrc.join(' ')}`);
    }
    
    if (csp.imgSrc.length > 0) {
      directives.push(`img-src ${csp.imgSrc.join(' ')}`);
    }
    
    if (csp.connectSrc.length > 0) {
      directives.push(`connect-src ${csp.connectSrc.join(' ')}`);
    }
    
    if (csp.fontSrc.length > 0) {
      directives.push(`font-src ${csp.fontSrc.join(' ')}`);
    }
    
    if (csp.objectSrc.length > 0) {
      directives.push(`object-src ${csp.objectSrc.join(' ')}`);
    }
    
    if (csp.mediaSrc.length > 0) {
      directives.push(`media-src ${csp.mediaSrc.join(' ')}`);
    }
    
    if (csp.frameSrc.length > 0) {
      directives.push(`frame-src ${csp.frameSrc.join(' ')}`);
    }
    
    if (csp.childSrc.length > 0) {
      directives.push(`child-src ${csp.childSrc.join(' ')}`);
    }
    
    if (csp.workerSrc.length > 0) {
      directives.push(`worker-src ${csp.workerSrc.join(' ')}`);
    }
    
    if (csp.manifestSrc.length > 0) {
      directives.push(`manifest-src ${csp.manifestSrc.join(' ')}`);
    }
    
    if (csp.formAction.length > 0) {
      directives.push(`form-action ${csp.formAction.join(' ')}`);
    }
    
    if (csp.frameAncestors.length > 0) {
      directives.push(`frame-ancestors ${csp.frameAncestors.join(' ')}`);
    }
    
    if (csp.baseUri.length > 0) {
      directives.push(`base-uri ${csp.baseUri.join(' ')}`);
    }

    // Add boolean directives
    if (csp.upgradeInsecureRequests) {
      directives.push('upgrade-insecure-requests');
    }
    
    if (csp.blockAllMixedContent) {
      directives.push('block-all-mixed-content');
    }

    return directives.join('; ');
  }

  /**
   * Apply comprehensive security headers to response
   */
  applySecurityHeaders(
    res: any,
    config: Partial<SecurityHeadersConfig> = {},
    isDevelopment: boolean = false
  ): void {
    const finalConfig = { ...this.defaultConfig, ...config };

    console.log('🛡️ Applying comprehensive security headers...');

    try {
      // Content Security Policy
      if (finalConfig.contentSecurityPolicy) {
        const cspValue = this.generateCSP(isDevelopment);
        res.setHeader('Content-Security-Policy', cspValue);
        console.log('   ✅ Content-Security-Policy applied');
      }

      // Strict Transport Security (HTTPS only)
      if (finalConfig.strictTransportSecurity && !isDevelopment) {
        res.setHeader(
          'Strict-Transport-Security',
          'max-age=31536000; includeSubDomains; preload'
        );
        console.log('   ✅ Strict-Transport-Security applied');
      }

      // X-Frame-Options
      if (finalConfig.xFrameOptions) {
        res.setHeader('X-Frame-Options', 'DENY');
        console.log('   ✅ X-Frame-Options applied');
      }

      // X-Content-Type-Options
      if (finalConfig.xContentTypeOptions) {
        res.setHeader('X-Content-Type-Options', 'nosniff');
        console.log('   ✅ X-Content-Type-Options applied');
      }

      // X-XSS-Protection
      if (finalConfig.xXSSProtection) {
        res.setHeader('X-XSS-Protection', '1; mode=block');
        console.log('   ✅ X-XSS-Protection applied');
      }

      // Referrer Policy
      if (finalConfig.referrerPolicy) {
        res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
        console.log('   ✅ Referrer-Policy applied');
      }

      // Permissions Policy
      if (finalConfig.permissionsPolicy) {
        const permissionsPolicy = [
          'camera=()',
          'microphone=()',
          'geolocation=()',
          'payment=(self)',
          'usb=()',
          'magnetometer=()',
          'accelerometer=()',
          'gyroscope=()',
          'fullscreen=(self)'
        ].join(', ');
        
        res.setHeader('Permissions-Policy', permissionsPolicy);
        console.log('   ✅ Permissions-Policy applied');
      }

      // Cross-Origin-Opener-Policy
      if (finalConfig.crossOriginOpenerPolicy) {
        res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');
        console.log('   ✅ Cross-Origin-Opener-Policy applied');
      }

      // Cross-Origin-Embedder-Policy (optional, can break functionality)
      if (finalConfig.crossOriginEmbedderPolicy) {
        res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
        console.log('   ✅ Cross-Origin-Embedder-Policy applied');
      }

      // Cross-Origin-Resource-Policy (optional, can break functionality)
      if (finalConfig.crossOriginResourcePolicy) {
        res.setHeader('Cross-Origin-Resource-Policy', 'same-origin');
        console.log('   ✅ Cross-Origin-Resource-Policy applied');
      }

      // Additional security headers
      res.setHeader('X-DNS-Prefetch-Control', 'off');
      res.setHeader('X-Download-Options', 'noopen');
      res.setHeader('X-Permitted-Cross-Domain-Policies', 'none');
      res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      res.setHeader('Surrogate-Control', 'no-store');

      console.log('✅ All security headers applied successfully');

    } catch (error) {
      console.error('❌ Error applying security headers:', error);
    }
  }

  /**
   * Create middleware for Express/Next.js
   */
  createSecurityHeadersMiddleware(
    config: Partial<SecurityHeadersConfig> = {},
    isDevelopment: boolean = process.env.NODE_ENV === 'development'
  ) {
    return (req: any, res: any, next: any) => {
      this.applySecurityHeaders(res, config, isDevelopment);
      next();
    };
  }

  /**
   * Get security headers for Next.js configuration
   */
  getNextJSSecurityHeaders(isDevelopment: boolean = false): Array<{ key: string; value: string }> {
    const headers: Array<{ key: string; value: string }> = [];
    const finalConfig = this.defaultConfig;

    if (finalConfig.contentSecurityPolicy) {
      headers.push({
        key: 'Content-Security-Policy',
        value: this.generateCSP(isDevelopment)
      });
    }

    if (finalConfig.strictTransportSecurity && !isDevelopment) {
      headers.push({
        key: 'Strict-Transport-Security',
        value: 'max-age=31536000; includeSubDomains; preload'
      });
    }

    if (finalConfig.xFrameOptions) {
      headers.push({
        key: 'X-Frame-Options',
        value: 'DENY'
      });
    }

    if (finalConfig.xContentTypeOptions) {
      headers.push({
        key: 'X-Content-Type-Options',
        value: 'nosniff'
      });
    }

    if (finalConfig.xXSSProtection) {
      headers.push({
        key: 'X-XSS-Protection',
        value: '1; mode=block'
      });
    }

    if (finalConfig.referrerPolicy) {
      headers.push({
        key: 'Referrer-Policy',
        value: 'strict-origin-when-cross-origin'
      });
    }

    if (finalConfig.permissionsPolicy) {
      headers.push({
        key: 'Permissions-Policy',
        value: 'camera=(), microphone=(), geolocation=(), payment=(self), usb=(), magnetometer=(), accelerometer=(), gyroscope=(), fullscreen=(self)'
      });
    }

    if (finalConfig.crossOriginOpenerPolicy) {
      headers.push({
        key: 'Cross-Origin-Opener-Policy',
        value: 'same-origin'
      });
    }

    // Additional security headers
    headers.push(
      { key: 'X-DNS-Prefetch-Control', value: 'off' },
      { key: 'X-Download-Options', value: 'noopen' },
      { key: 'X-Permitted-Cross-Domain-Policies', value: 'none' }
    );

    return headers;
  }

  /**
   * Test security headers implementation
   */
  testSecurityHeaders(): boolean {
    console.log('🧪 Testing security headers implementation...');

    try {
      // Test CSP generation
      const prodCSP = this.generateCSP(false);
      const devCSP = this.generateCSP(true);

      if (!prodCSP.includes("default-src 'self'") || !devCSP.includes("'unsafe-eval'")) {
        throw new Error('CSP generation failed');
      }
      console.log('   ✅ CSP generation working');

      // Test header application (mock)
      const mockResponse = {
        headers: new Map(),
        setHeader: function(name: string, value: string) {
          this.headers.set(name, value);
        }
      };

      this.applySecurityHeaders(mockResponse, {}, false);

      if (mockResponse.headers.size < 5) {
        throw new Error('Security headers not properly applied');
      }
      console.log('   ✅ Header application working');

      // Test Next.js headers
      const nextHeaders = this.getNextJSSecurityHeaders(false);
      if (nextHeaders.length < 5) {
        throw new Error('Next.js headers generation failed');
      }
      console.log('   ✅ Next.js headers generation working');

      console.log('✅ Security headers test PASSED');
      return true;

    } catch (error) {
      console.error('❌ Security headers test FAILED:', error);
      return false;
    }
  }
}

// Create singleton instance
export const securityHeaders = new SecurityHeadersManager();

// Export middleware for easy use
export const securityHeadersMiddleware = securityHeaders.createSecurityHeadersMiddleware();

// Export for Next.js configuration
export const nextJSSecurityHeaders = securityHeaders.getNextJSSecurityHeaders();

export default securityHeaders;
