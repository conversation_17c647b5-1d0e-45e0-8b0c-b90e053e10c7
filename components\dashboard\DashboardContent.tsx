import React from 'react';
import { DashboardSection } from '../../hooks/useDashboardNavigation.tsx';
import { UserPermissions } from '../../hooks/useUserPermissions';
import { DashboardData } from '../../hooks/useDashboardData';

// Section Components
import { OverviewSection } from './sections/OverviewSection';
import { PurchaseSharesSection } from './sections/PurchaseSharesSection';
import { PortfolioSection } from './sections/PortfolioSection';
import { ReferralsSection } from './sections/ReferralsSection';
import { PaymentsSection } from './sections/PaymentsSection';
import { NotificationsSection } from './sections/NotificationsSection';
import { SettingsSection } from './sections/SettingsSection';
import { DividendsSection } from './sections/DividendsSection';

interface DashboardContentProps {
  activeSection: DashboardSection;
  user: any;
  permissions: UserPermissions;
  dashboardData: DashboardData;
  loading: boolean;
  onRefreshData: () => Promise<void>;
}

export const DashboardContent: React.FC<DashboardContentProps> = ({
  activeSection,
  user,
  permissions,
  dashboardData,
  loading,
  onRefreshData
}) => {
  // Show loading state for initial load
  if (loading && dashboardData.totalShares === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  // Render the appropriate section based on activeSection
  const renderSection = () => {
    switch (activeSection) {
      case 'overview':
        return (
          <OverviewSection
            user={user}
            dashboardData={dashboardData}
            permissions={permissions}
            onRefreshData={onRefreshData}
          />
        );

      case 'purchase-shares':
        if (!permissions.canPurchaseShares) {
          return <UnauthorizedSection />;
        }
        return (
          <PurchaseSharesSection
            user={user}
            onRefreshData={onRefreshData}
          />
        );

      case 'portfolio':
        if (!permissions.canViewPortfolio) {
          return <UnauthorizedSection />;
        }
        return (
          <PortfolioSection
            user={user}
            dashboardData={dashboardData}
            onRefreshData={onRefreshData}
          />
        );

      case 'dividends':
        return (
          <DividendsSection
            user={user}
            dashboardData={dashboardData}
          />
        );

      case 'referrals':
        if (!permissions.canManageReferrals) {
          return <UnauthorizedSection />;
        }
        return (
          <ReferralsSection
            user={user}
            dashboardData={dashboardData}
            onRefreshData={onRefreshData}
          />
        );

      case 'payments':
        if (!permissions.canViewPayments) {
          return <UnauthorizedSection />;
        }
        return (
          <PaymentsSection
            user={user}
            dashboardData={dashboardData}
          />
        );

      case 'notifications':
        return (
          <NotificationsSection
            user={user}
            dashboardData={dashboardData}
          />
        );

      case 'settings':
        if (!permissions.canAccessSettings) {
          return <UnauthorizedSection />;
        }
        return (
          <SettingsSection
            user={user}
            onRefreshData={onRefreshData}
          />
        );

      default:
        return (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.563M15 9.34c-1.17-1.17-2.73-1.82-4.36-1.82s-3.19.65-4.36 1.82m13.24 2.563A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.563" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">Section Not Found</h3>
            <p className="text-gray-400">The requested section could not be found.</p>
          </div>
        );
    }
  };

  return (
    <div className="dashboard-content">
      {renderSection()}
    </div>
  );
};

// Unauthorized access component
const UnauthorizedSection: React.FC = () => (
  <div className="text-center py-12">
    <div className="text-red-400 mb-4">
      <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
      </svg>
    </div>
    <h3 className="text-xl font-semibold text-white mb-2">Access Restricted</h3>
    <p className="text-gray-400">You don't have permission to access this section.</p>
  </div>
);
