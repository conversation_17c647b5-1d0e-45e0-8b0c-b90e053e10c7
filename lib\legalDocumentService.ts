import { supabase, getServiceRoleClient } from './supabase'

export interface LegalDocumentAgreement {
  id: number
  user_id: number
  document_type: 'terms_conditions' | 'privacy_policy' | 'disclaimer'
  document_version: string
  agreed_at: string
  ip_address?: string
  user_agent?: string
  is_current: boolean
  created_at: string
  updated_at: string
}

export interface AgreementStatus {
  terms_conditions: boolean
  privacy_policy: boolean
  disclaimer: boolean
  share_purchase_agreement: boolean
  all_agreed: boolean
}

export class LegalDocumentService {
  private static readonly CURRENT_VERSION = '2.0'

  /**
   * Convert UUID auth_user_id to integer user_id
   * Handles both UUID format and db_ prefixed IDs
   */
  private static async getUserIdFromAuthId(authUserId: string): Promise<number | null> {
    try {
      console.log('🔍 LEGAL SERVICE: getUserIdFromAuthId called with:', authUserId)

      // Handle db_ prefixed IDs (direct database user IDs)
      if (authUserId.startsWith('db_')) {
        const dbUserId = parseInt(authUserId.replace('db_', ''));
        console.log('✅ LEGAL SERVICE: Using db_ prefixed ID for legal documents:', dbUserId);
        return dbUserId;
      }

      // Handle direct numeric IDs
      if (/^\d+$/.test(authUserId)) {
        const numericId = parseInt(authUserId);
        console.log('✅ LEGAL SERVICE: Using direct numeric ID for legal documents:', numericId);
        return numericId;
      }

      // Handle UUID format - lookup in database
      console.log('🔍 LEGAL SERVICE: Looking up UUID in database:', authUserId);
      const serviceClient = getServiceRoleClient()
      const { data, error } = await serviceClient
        .from('users')
        .select('id')
        .eq('auth_user_id', authUserId)
        .single()

      console.log('🔍 LEGAL SERVICE: UUID lookup result:', { data, error });

      if (error || !data) {
        console.error('❌ LEGAL SERVICE: Error finding user by auth_user_id:', error)
        return null
      }

      console.log('✅ LEGAL SERVICE: Found user ID from UUID:', data.id);
      return data.id
    } catch (error) {
      console.error('❌ LEGAL SERVICE: Exception in getUserIdFromAuthId:', error)
      console.error('❌ LEGAL SERVICE: Exception details:', {
        message: error.message,
        stack: error.stack,
        input_auth_user_id: authUserId
      })
      return null
    }
  }

  /**
   * Record a user's agreement to a legal document
   * @param userIdOrAuthId - Can be either integer user_id or UUID auth_user_id
   */
  static async recordAgreement(
    userIdOrAuthId: number | string,
    documentType: 'terms_conditions' | 'privacy_policy' | 'disclaimer' | 'share_purchase_agreement',
    ipAddress?: string,
    userAgent?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Convert UUID to integer user_id if needed
      let userId: number
      if (typeof userIdOrAuthId === 'string') {
        const convertedId = await this.getUserIdFromAuthId(userIdOrAuthId)
        if (!convertedId) {
          return { success: false, error: 'User not found' }
        }
        userId = convertedId
      } else {
        userId = userIdOrAuthId
      }

      const serviceClient = getServiceRoleClient()

      // First, mark any existing agreements as not current
      await serviceClient
        .from('legal_document_agreements')
        .update({ is_current: false, updated_at: new Date().toISOString() })
        .eq('user_id', userId)
        .eq('document_type', documentType)
        .eq('is_current', true)

      // Insert new agreement record
      const { error } = await serviceClient
        .from('legal_document_agreements')
        .insert({
          user_id: userId,
          document_type: documentType,
          document_version: this.CURRENT_VERSION,
          agreed_at: new Date().toISOString(),
          ip_address: ipAddress,
          user_agent: userAgent,
          is_current: true
        })

      if (error) {
        console.error('Error recording legal agreement:', error)
        return { success: false, error: error.message }
      }

      return { success: true }
    } catch (error) {
      console.error('Error in recordAgreement:', error)
      return { success: false, error: 'Failed to record agreement' }
    }
  }

  /**
   * Check if user has agreed to all current legal documents
   * @param userIdOrAuthId - Can be either integer user_id or UUID auth_user_id
   */
  static async checkAgreementStatus(userIdOrAuthId: number | string): Promise<AgreementStatus> {
    try {
      console.log('🔍 LEGAL SERVICE: checkAgreementStatus called with:', userIdOrAuthId, 'Type:', typeof userIdOrAuthId)

      // Convert UUID to integer user_id if needed
      let userId: number
      if (typeof userIdOrAuthId === 'string') {
        console.log('🔍 LEGAL SERVICE: Converting string ID to numeric:', userIdOrAuthId)
        const convertedId = await this.getUserIdFromAuthId(userIdOrAuthId)
        console.log('🔍 LEGAL SERVICE: Conversion result:', convertedId)

        if (!convertedId) {
          console.log('❌ LEGAL SERVICE: Failed to convert user ID, returning false status')
          return {
            terms_conditions: false,
            privacy_policy: false,
            disclaimer: false,
            share_purchase_agreement: false,
            all_agreed: false
          }
        }
        userId = convertedId
      } else {
        userId = userIdOrAuthId
      }

      console.log('🔍 LEGAL SERVICE: Using final user ID for query:', userId, 'Type:', typeof userId)
      console.log('🔍 LEGAL SERVICE: Query parameters:', {
        user_id: userId,
        is_current: true,
        document_version: this.CURRENT_VERSION
      })

      const serviceClient = getServiceRoleClient()

      const { data: agreements, error } = await serviceClient
        .from('legal_document_agreements')
        .select('document_type, document_version, agreed_at')
        .eq('user_id', userId)
        .eq('is_current', true)
        .eq('document_version', this.CURRENT_VERSION)

      console.log('🔍 LEGAL SERVICE: Database query result:', { agreements, error })
      console.log('🔍 LEGAL SERVICE: Number of agreements found:', agreements?.length || 0)

      if (error) {
        console.error('❌ LEGAL SERVICE: Database error checking agreement status:', error)
        return {
          terms_conditions: false,
          privacy_policy: false,
          disclaimer: false,
          share_purchase_agreement: false,
          all_agreed: false
        }
      }

      console.log('🔍 LEGAL SERVICE: Raw agreements data:', agreements)

      const agreementMap = new Map(
        agreements?.map(a => [a.document_type, true]) || []
      )

      console.log('🔍 LEGAL SERVICE: Agreement map:', Object.fromEntries(agreementMap))

      const status: AgreementStatus = {
        terms_conditions: agreementMap.get('terms_conditions') || false,
        privacy_policy: agreementMap.get('privacy_policy') || false,
        disclaimer: agreementMap.get('disclaimer') || false,
        share_purchase_agreement: agreementMap.get('share_purchase_agreement') || false,
        all_agreed: false
      }

      status.all_agreed = status.terms_conditions && status.privacy_policy && status.disclaimer && status.share_purchase_agreement

      console.log('🔍 LEGAL SERVICE: Final status calculation:', status)
      console.log('🔍 LEGAL SERVICE: All agreed calculation:', {
        terms_conditions: status.terms_conditions,
        privacy_policy: status.privacy_policy,
        disclaimer: status.disclaimer,
        share_purchase_agreement: status.share_purchase_agreement,
        all_agreed: status.all_agreed
      })

      return status
    } catch (error) {
      console.error('❌ LEGAL SERVICE: Exception in checkAgreementStatus:', error)
      console.error('❌ LEGAL SERVICE: Exception details:', {
        message: error.message,
        stack: error.stack,
        input_id: userIdOrAuthId,
        input_type: typeof userIdOrAuthId
      })
      return {
        terms_conditions: false,
        privacy_policy: false,
        disclaimer: false,
        share_purchase_agreement: false,
        all_agreed: false
      }
    }
  }

  /**
   * Get user's agreement history for a specific document type
   * @param userIdOrAuthId - Can be either integer user_id or UUID auth_user_id
   */
  static async getAgreementHistory(
    userIdOrAuthId: number | string,
    documentType?: 'terms_conditions' | 'privacy_policy' | 'disclaimer' | 'share_purchase_agreement'
  ): Promise<LegalDocumentAgreement[]> {
    try {
      // Convert UUID to integer user_id if needed
      let userId: number
      if (typeof userIdOrAuthId === 'string') {
        const convertedId = await this.getUserIdFromAuthId(userIdOrAuthId)
        if (!convertedId) {
          return []
        }
        userId = convertedId
      } else {
        userId = userIdOrAuthId
      }

      const serviceClient = getServiceRoleClient()

      let query = serviceClient
        .from('legal_document_agreements')
        .select('*')
        .eq('user_id', userId)
        .order('agreed_at', { ascending: false })

      if (documentType) {
        query = query.eq('document_type', documentType)
      }

      const { data: agreements, error } = await query

      if (error) {
        console.error('Error getting agreement history:', error)
        return []
      }

      return agreements || []
    } catch (error) {
      console.error('Error in getAgreementHistory:', error)
      return []
    }
  }

  /**
   * Check if user needs to agree to updated legal documents
   * @param userIdOrAuthId - Can be either integer user_id or UUID auth_user_id
   */
  static async checkForUpdatedDocuments(userIdOrAuthId: number | string): Promise<{
    needsUpdate: boolean
    documentsToUpdate: string[]
  }> {
    try {
      const status = await this.checkAgreementStatus(userIdOrAuthId)
      const documentsToUpdate: string[] = []

      if (!status.terms_conditions) documentsToUpdate.push('Terms & Conditions')
      if (!status.privacy_policy) documentsToUpdate.push('Privacy Policy')
      if (!status.disclaimer) documentsToUpdate.push('Disclaimer')
      if (!status.share_purchase_agreement) documentsToUpdate.push('Share Purchase Agreement')

      return {
        needsUpdate: documentsToUpdate.length > 0,
        documentsToUpdate
      }
    } catch (error) {
      console.error('Error checking for updated documents:', error)
      return {
        needsUpdate: true,
        documentsToUpdate: ['Terms & Conditions', 'Privacy Policy', 'Disclaimer', 'Share Purchase Agreement']
      }
    }
  }

  /**
   * Get client IP address (for browser environment)
   */
  static async getClientIP(): Promise<string | undefined> {
    try {
      // In a real application, you might want to use a service to get the client IP
      // For now, we'll return undefined and handle it gracefully
      return undefined
    } catch (error) {
      console.error('Error getting client IP:', error)
      return undefined
    }
  }

  /**
   * Get user agent string
   */
  static getUserAgent(): string | undefined {
    try {
      return typeof navigator !== 'undefined' ? navigator.userAgent : undefined
    } catch (error) {
      console.error('Error getting user agent:', error)
      return undefined
    }
  }

  /**
   * Record agreement with automatic IP and user agent detection
   * @param userIdOrAuthId - Can be either integer user_id or UUID auth_user_id
   */
  static async recordAgreementWithContext(
    userIdOrAuthId: number | string,
    documentType: 'terms_conditions' | 'privacy_policy' | 'disclaimer'
  ): Promise<{ success: boolean; error?: string }> {
    const ipAddress = await this.getClientIP()
    const userAgent = this.getUserAgent()

    return this.recordAgreement(userIdOrAuthId, documentType, ipAddress, userAgent)
  }

  /**
   * Bulk record agreements for all legal documents
   * @param userIdOrAuthId - Can be either integer user_id or UUID auth_user_id
   */
  static async recordAllAgreements(userIdOrAuthId: number | string): Promise<{
    success: boolean
    results: { [key: string]: boolean }
    errors: string[]
  }> {
    const documentTypes: Array<'terms_conditions' | 'privacy_policy' | 'disclaimer'> = [
      'terms_conditions',
      'privacy_policy',
      'disclaimer'
    ]

    const results: { [key: string]: boolean } = {}
    const errors: string[] = []

    for (const docType of documentTypes) {
      const result = await this.recordAgreementWithContext(userIdOrAuthId, docType)
      results[docType] = result.success

      if (!result.success && result.error) {
        errors.push(`${docType}: ${result.error}`)
      }
    }

    const allSuccess = Object.values(results).every(success => success)

    return {
      success: allSuccess,
      results,
      errors
    }
  }

  /**
   * Check if user can purchase shares (must agree to all legal documents first)
   * @param userIdOrAuthId - Can be either integer user_id or UUID auth_user_id
   */
  static async canUserPurchaseShares(userIdOrAuthId: number | string): Promise<{
    canPurchase: boolean
    missingAgreements: string[]
    message: string
  }> {
    try {
      const status = await this.checkAgreementStatus(userIdOrAuthId)

      if (status.all_agreed) {
        return {
          canPurchase: true,
          missingAgreements: [],
          message: 'All legal documents agreed to. Share purchase allowed.'
        }
      }

      const missingAgreements: string[] = []
      if (!status.terms_conditions) missingAgreements.push('Terms & Conditions')
      if (!status.privacy_policy) missingAgreements.push('Privacy Policy')
      if (!status.disclaimer) missingAgreements.push('Legal Disclaimer')
      if (!status.share_purchase_agreement) missingAgreements.push('Share Purchase Agreement')

      return {
        canPurchase: false,
        missingAgreements,
        message: `You must agree to all legal documents before purchasing shares. Missing: ${missingAgreements.join(', ')}`
      }
    } catch (error) {
      console.error('Error checking share purchase eligibility:', error)
      return {
        canPurchase: false,
        missingAgreements: ['Terms & Conditions', 'Privacy Policy', 'Legal Disclaimer'],
        message: 'Unable to verify legal document agreements. Please ensure all documents are agreed to before purchasing shares.'
      }
    }
  }

  /**
   * Get user-friendly names for document types
   */
  static getDocumentDisplayName(documentType: 'terms_conditions' | 'privacy_policy' | 'disclaimer'): string {
    const displayNames = {
      'terms_conditions': 'Terms & Conditions',
      'privacy_policy': 'Privacy Policy',
      'disclaimer': 'Legal Disclaimer'
    }
    return displayNames[documentType]
  }

  /**
   * Validate that all required legal documents exist and are accessible
   */
  static validateLegalDocumentsSystem(): {
    isValid: boolean
    issues: string[]
  } {
    const issues: string[] = []

    // Check if all required document types are defined
    const requiredDocuments = ['terms_conditions', 'privacy_policy', 'disclaimer']
    const currentVersion = this.CURRENT_VERSION

    if (!currentVersion || currentVersion === '') {
      issues.push('Document version not defined')
    }

    // Additional validation could be added here
    // e.g., checking if document components exist, database connectivity, etc.

    return {
      isValid: issues.length === 0,
      issues
    }
  }
}

export default LegalDocumentService
