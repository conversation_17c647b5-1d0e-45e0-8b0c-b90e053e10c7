/**
 * COMPLETE COUNTRY LIST HOOK
 * 
 * Centralized hook for accessing the complete world country list
 * across all Aureus Africa components. Provides consistent country
 * data with search, filtering, and regional grouping capabilities.
 */

import { useMemo } from 'react';
import { 
  COMPLETE_COUNTRY_LIST, 
  getAllCountries, 
  getCountriesByRegion, 
  searchCountries,
  getCountryByCode,
  CompleteCountry 
} from '../data/completeCountryList';

export interface CountryListOptions {
  includeRegions?: CompleteCountry['region'][];
  excludeRegions?: CompleteCountry['region'][];
  searchQuery?: string;
  sortBy?: 'name' | 'region' | 'code';
  prioritizeCountries?: string[]; // Country codes to show at top
}

export interface UseCompleteCountryListReturn {
  // Core data
  allCountries: CompleteCountry[];
  totalCount: number;
  
  // Filtering functions
  getByRegion: (region: CompleteCountry['region']) => CompleteCountry[];
  getByCode: (code: string) => CompleteCountry | null;
  search: (query: string) => CompleteCountry[];
  
  // Grouped data
  countriesByRegion: Record<CompleteCountry['region'], CompleteCountry[]>;
  
  // Utility functions
  isValidCountryCode: (code: string) => boolean;
  getCountryName: (code: string) => string;
  getCountryFlag: (code: string) => string;
  
  // Formatted options for dropdowns
  countryOptions: Array<{ value: string; label: string; flag: string; region: string }>;
  regionOptions: Array<{ value: CompleteCountry['region']; label: string; count: number }>;
}

/**
 * Hook for accessing complete country list with filtering and search capabilities
 */
export const useCompleteCountryList = (options: CountryListOptions = {}): UseCompleteCountryListReturn => {
  const {
    includeRegions,
    excludeRegions,
    searchQuery,
    sortBy = 'name',
    prioritizeCountries = []
  } = options;

  // Memoized filtered and sorted countries
  const filteredCountries = useMemo(() => {
    let countries = getAllCountries();

    // Apply region filters
    if (includeRegions && includeRegions.length > 0) {
      countries = countries.filter(country => includeRegions.includes(country.region));
    }

    if (excludeRegions && excludeRegions.length > 0) {
      countries = countries.filter(country => !excludeRegions.includes(country.region));
    }

    // Apply search filter
    if (searchQuery && searchQuery.trim()) {
      countries = searchCountries(searchQuery).filter(country => 
        includeRegions ? includeRegions.includes(country.region) : true
      );
    }

    // Sort countries
    countries.sort((a, b) => {
      switch (sortBy) {
        case 'region':
          return a.region.localeCompare(b.region) || a.name.localeCompare(b.name);
        case 'code':
          return a.code.localeCompare(b.code);
        case 'name':
        default:
          return a.name.localeCompare(b.name);
      }
    });

    // Prioritize specific countries
    if (prioritizeCountries.length > 0) {
      const prioritized = countries.filter(country => prioritizeCountries.includes(country.code));
      const others = countries.filter(country => !prioritizeCountries.includes(country.code));
      
      // Sort prioritized countries by the order specified in prioritizeCountries
      prioritized.sort((a, b) => {
        const aIndex = prioritizeCountries.indexOf(a.code);
        const bIndex = prioritizeCountries.indexOf(b.code);
        return aIndex - bIndex;
      });
      
      countries = [...prioritized, ...others];
    }

    return countries;
  }, [includeRegions, excludeRegions, searchQuery, sortBy, prioritizeCountries]);

  // Memoized countries grouped by region
  const countriesByRegion = useMemo(() => {
    const grouped: Record<CompleteCountry['region'], CompleteCountry[]> = {
      'Africa': [],
      'Asia': [],
      'Europe': [],
      'North America': [],
      'South America': [],
      'Oceania': []
    };

    filteredCountries.forEach(country => {
      grouped[country.region].push(country);
    });

    return grouped;
  }, [filteredCountries]);

  // Memoized dropdown options
  const countryOptions = useMemo(() => {
    return filteredCountries.map(country => ({
      value: country.code,
      label: country.name,
      flag: country.flag,
      region: country.region
    }));
  }, [filteredCountries]);

  // Memoized region options with counts
  const regionOptions = useMemo(() => {
    const regions: CompleteCountry['region'][] = ['Africa', 'Asia', 'Europe', 'North America', 'South America', 'Oceania'];
    
    return regions.map(region => ({
      value: region,
      label: region,
      count: countriesByRegion[region].length
    })).filter(region => region.count > 0);
  }, [countriesByRegion]);

  // Utility functions
  const isValidCountryCode = (code: string): boolean => {
    return COMPLETE_COUNTRY_LIST.some(country => country.code === code);
  };

  const getCountryName = (code: string): string => {
    const country = getCountryByCode(code);
    return country ? country.name : 'Unknown Country';
  };

  const getCountryFlag = (code: string): string => {
    const country = getCountryByCode(code);
    return country ? country.flag : '🏳️';
  };

  return {
    // Core data
    allCountries: filteredCountries,
    totalCount: filteredCountries.length,
    
    // Filtering functions
    getByRegion: getCountriesByRegion,
    getByCode: getCountryByCode,
    search: searchCountries,
    
    // Grouped data
    countriesByRegion,
    
    // Utility functions
    isValidCountryCode,
    getCountryName,
    getCountryFlag,
    
    // Formatted options
    countryOptions,
    regionOptions
  };
};

/**
 * Hook specifically for African countries (prioritized for Aureus Africa)
 */
export const useAfricanCountries = () => {
  return useCompleteCountryList({
    includeRegions: ['Africa'],
    prioritizeCountries: ['ZAF', 'NAM', 'SWZ', 'BWA', 'ZWE', 'MOZ', 'LSO'] // Southern Africa priority
  });
};

/**
 * Hook for countries supporting specific payment methods
 * Note: This would need to be enhanced with payment method data
 */
export const useCountriesWithPaymentSupport = (paymentMethod?: 'crypto' | 'bank_transfer') => {
  // For now, return all countries - this can be enhanced with payment method filtering
  // when payment method data is added to the country list
  return useCompleteCountryList();
};

/**
 * Hook for popular/frequently selected countries
 */
export const usePopularCountries = () => {
  return useCompleteCountryList({
    prioritizeCountries: [
      'ZAF', // South Africa
      'USA', // United States  
      'GBR', // United Kingdom
      'CAN', // Canada
      'AUS', // Australia
      'DEU', // Germany
      'FRA', // France
      'NLD', // Netherlands
      'CHE', // Switzerland
      'SWE', // Sweden
      'NAM', // Namibia
      'SWZ', // Eswatini
      'BWA', // Botswana
      'IND', // India
      'CHN', // China
      'JPN', // Japan
      'BRA', // Brazil
      'MEX', // Mexico
      'NGA', // Nigeria
      'KEN'  // Kenya
    ]
  });
};
