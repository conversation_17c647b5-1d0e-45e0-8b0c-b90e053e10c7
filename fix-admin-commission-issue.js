import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

const supabase = createClient(supabaseUrl, supabaseKey);

// Admin/Test accounts that should NOT receive commissions
const ADMIN_USER_IDS = [4, 80, 89, 102, 103, 104, 105, 106, 107, 111, 113, 115, 123];

async function fixAdminCommissionIssue() {
  console.log('🚨 FIXING ADMIN COMMISSION ISSUE\n');
  console.log('❌ ADMIN USER IDs (should not receive commissions):', ADMIN_USER_IDS.join(', '));

  // Step 1: Get all admin commission transactions before deletion
  console.log('\n📋 STEP 1: Documenting admin commissions before removal');
  
  const adminCommissionData = {};
  
  for (const adminId of ADMIN_USER_IDS) {
    const { data: adminCommissions, error } = await supabase
      .from('commission_transactions')
      .select(`
        *,
        referrer:users!referrer_id(username, full_name)
      `)
      .eq('referrer_id', adminId);

    if (!error && adminCommissions && adminCommissions.length > 0) {
      const totalUSDT = adminCommissions.reduce((sum, c) => sum + (c.usdt_commission || 0), 0);
      const totalShares = adminCommissions.reduce((sum, c) => sum + (c.share_commission || 0), 0);
      
      adminCommissionData[adminId] = {
        username: adminCommissions[0].referrer?.username,
        full_name: adminCommissions[0].referrer?.full_name,
        transactions: adminCommissions.length,
        totalUSDT,
        totalShares,
        transactionIds: adminCommissions.map(c => c.id)
      };
      
      console.log(`   Admin ${adminId} (${adminCommissionData[adminId].full_name || adminCommissionData[adminId].username}):`);
      console.log(`   • ${adminCommissions.length} transactions`);
      console.log(`   • $${totalUSDT.toFixed(2)} USDT`);
      console.log(`   • ${totalShares.toFixed(4)} shares`);
    }
  }

  // Step 2: Delete admin commission transactions
  console.log('\n🗑️ STEP 2: Removing admin commission transactions');
  
  let totalDeletedTransactions = 0;
  let totalRemovedUSDT = 0;
  let totalRemovedShares = 0;
  
  for (const adminId of ADMIN_USER_IDS) {
    if (adminCommissionData[adminId]) {
      const { error: deleteError } = await supabase
        .from('commission_transactions')
        .delete()
        .eq('referrer_id', adminId);

      if (deleteError) {
        console.log(`   ❌ Failed to delete commissions for admin ${adminId}:`, deleteError.message);
      } else {
        console.log(`   ✅ Deleted ${adminCommissionData[adminId].transactions} transactions for admin ${adminId}`);
        totalDeletedTransactions += adminCommissionData[adminId].transactions;
        totalRemovedUSDT += adminCommissionData[adminId].totalUSDT;
        totalRemovedShares += adminCommissionData[adminId].totalShares;
      }
    }
  }

  // Step 3: Reset admin commission balances to 0
  console.log('\n💰 STEP 3: Resetting admin commission balances to 0');
  
  for (const adminId of ADMIN_USER_IDS) {
    const { error: resetError } = await supabase
      .from('commission_balances')
      .update({
        usdt_balance: 0,
        share_balance: 0,
        total_earned_usdt: 0,
        total_earned_shares: 0,
        last_updated: new Date().toISOString()
      })
      .eq('user_id', adminId);

    if (resetError) {
      console.log(`   ⚠️ Could not reset balance for admin ${adminId}:`, resetError.message);
    } else {
      console.log(`   ✅ Reset commission balance for admin ${adminId}`);
    }
  }

  // Step 4: Get corrected legitimate user commissions
  console.log('\n✅ STEP 4: Verifying legitimate user commissions');
  
  const { data: legitimateCommissions, error: legitError } = await supabase
    .from('commission_transactions')
    .select(`
      referrer_id,
      usdt_commission,
      share_commission,
      referrer:users!referrer_id(username, full_name)
    `)
    .not('referrer_id', 'in', `(${ADMIN_USER_IDS.join(',')})`)
    .not('referrer_id', 'is', null);

  if (!legitError && legitimateCommissions) {
    const userSummary = {};
    
    legitimateCommissions.forEach(comm => {
      if (!userSummary[comm.referrer_id]) {
        userSummary[comm.referrer_id] = {
          id: comm.referrer_id,
          username: comm.referrer?.username,
          full_name: comm.referrer?.full_name,
          transactions: 0,
          totalUSDT: 0,
          totalShares: 0
        };
      }
      
      userSummary[comm.referrer_id].transactions++;
      userSummary[comm.referrer_id].totalUSDT += comm.usdt_commission || 0;
      userSummary[comm.referrer_id].totalShares += comm.share_commission || 0;
    });
    
    const sortedUsers = Object.values(userSummary)
      .sort((a, b) => b.totalShares - a.totalShares);
    
    console.log(`\n📊 CORRECTED LEGITIMATE COMMISSION RECIPIENTS (${sortedUsers.length} users):`);
    console.log('='.repeat(80));
    
    sortedUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.full_name || user.username} (ID: ${user.id})`);
      console.log(`   Transactions: ${user.transactions}`);
      console.log(`   USDT: $${user.totalUSDT.toFixed(2)}`);
      console.log(`   Shares: ${user.totalShares.toFixed(4)}`);
      console.log('');
    });
    
    const grandTotalUSDT = sortedUsers.reduce((sum, user) => sum + user.totalUSDT, 0);
    const grandTotalShares = sortedUsers.reduce((sum, user) => sum + user.totalShares, 0);
    const grandTotalTransactions = sortedUsers.reduce((sum, user) => sum + user.transactions, 0);
    
    console.log('📈 CORRECTED TOTALS (EXCLUDING ADMIN ACCOUNTS):');
    console.log(`• Total Legitimate Users: ${sortedUsers.length}`);
    console.log(`• Total Transactions: ${grandTotalTransactions}`);
    console.log(`• Total USDT Commissions: $${grandTotalUSDT.toFixed(2)}`);
    console.log(`• Total Share Commissions: ${grandTotalShares.toFixed(4)}`);
  }

  // Step 5: Create audit log
  const auditDetails = {
    action: 'REMOVE_ADMIN_COMMISSIONS',
    admin_users_affected: Object.keys(adminCommissionData).map(id => parseInt(id)),
    total_transactions_deleted: totalDeletedTransactions,
    total_usdt_removed: totalRemovedUSDT,
    total_shares_removed: totalRemovedShares,
    admin_commission_data: adminCommissionData,
    timestamp: new Date().toISOString()
  };

  const { error: auditError } = await supabase
    .from('admin_audit_logs')
    .insert({
      admin_telegram_id: 0,
      admin_username: 'SYSTEM_ADMIN_COMMISSION_FIX',
      action: 'REMOVE_ADMIN_COMMISSIONS',
      target_type: 'admin_commission_cleanup',
      target_id: 'systemwide',
      details: JSON.stringify(auditDetails),
      timestamp: new Date().toISOString()
    });

  if (auditError) {
    console.log('⚠️ Could not create audit log:', auditError.message);
  } else {
    console.log('✅ Created comprehensive audit log entry');
  }

  console.log(`\n📊 ADMIN COMMISSION CLEANUP SUMMARY:`);
  console.log(`=`.repeat(50));
  console.log(`• Admin accounts cleaned: ${Object.keys(adminCommissionData).length}`);
  console.log(`• Transactions deleted: ${totalDeletedTransactions}`);
  console.log(`• USDT removed: $${totalRemovedUSDT.toFixed(2)}`);
  console.log(`• Shares removed: ${totalRemovedShares.toFixed(4)}`);
  console.log(`• Commission balances reset to 0`);

  console.log('\n✅ Admin commission cleanup complete!');
  console.log('\n🔍 NEXT: All commission calculations are now correct for legitimate users only.');
}

// Run the fix
fixAdminCommissionIssue().catch(console.error);
