#!/usr/bin/env node

/**
 * EMAIL MARKETING TABLES VERIFICATION SCRIPT
 * 
 * This script verifies that all email marketing tables were created successfully
 * and are accessible with proper permissions.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  console.error('Required: VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function verifyEmailMarketingTables() {
  console.log('🔍 Verifying Email Marketing Tables...\n');

  const tables = [
    {
      name: 'lead_lists',
      description: 'User-created lists for organizing leads'
    },
    {
      name: 'lead_list_members', 
      description: 'Many-to-many relationship for list membership'
    },
    {
      name: 'email_campaigns',
      description: 'Email marketing campaigns tracking'
    },
    {
      name: 'email_sends',
      description: 'Individual email delivery tracking'
    },
    {
      name: 'email_templates',
      description: 'Email templates for campaigns'
    },
    {
      name: 'affiliate_leads',
      description: 'Affiliate leads (should already exist)'
    }
  ];

  let successCount = 0;
  let errorCount = 0;

  for (const table of tables) {
    try {
      console.log(`🔍 Checking table: ${table.name}`);
      
      const { data, error } = await supabase
        .from(table.name)
        .select('*')
        .limit(1);

      if (error) {
        console.error(`❌ ${table.name}: ${error.message}`);
        errorCount++;
      } else {
        console.log(`✅ ${table.name}: accessible - ${table.description}`);
        successCount++;
      }
    } catch (err) {
      console.error(`❌ ${table.name}: ${err.message}`);
      errorCount++;
    }
    
    console.log(''); // Add spacing
  }

  // Test specific functionality
  console.log('🧪 Testing specific functionality...\n');

  // Test lead_lists with user_id filter (should work with service role)
  try {
    console.log('🔍 Testing lead_lists query with user filter...');
    const { data, error } = await supabase
      .from('lead_lists')
      .select('*, lead_count:lead_list_members(count)')
      .eq('user_id', 4)
      .order('created_at', { ascending: false });

    if (error) {
      console.error(`❌ Lead lists query failed: ${error.message}`);
      errorCount++;
    } else {
      console.log(`✅ Lead lists query successful (found ${data.length} lists)`);
      successCount++;
    }
  } catch (err) {
    console.error(`❌ Lead lists query exception: ${err.message}`);
    errorCount++;
  }

  console.log('');

  // Test email_campaigns query
  try {
    console.log('🔍 Testing email_campaigns query...');
    const { data, error } = await supabase
      .from('email_campaigns')
      .select('*')
      .eq('user_id', 4)
      .order('created_at', { ascending: false });

    if (error) {
      console.error(`❌ Email campaigns query failed: ${error.message}`);
      errorCount++;
    } else {
      console.log(`✅ Email campaigns query successful (found ${data.length} campaigns)`);
      successCount++;
    }
  } catch (err) {
    console.error(`❌ Email campaigns query exception: ${err.message}`);
    errorCount++;
  }

  console.log('');

  // Summary
  console.log('📊 VERIFICATION SUMMARY:');
  console.log(`✅ Successful checks: ${successCount}`);
  console.log(`❌ Failed checks: ${errorCount}`);
  console.log(`📋 Total checks: ${successCount + errorCount}`);

  if (errorCount === 0) {
    console.log('\n🎉 All email marketing tables are working correctly!');
    console.log('\n📋 Ready for use:');
    console.log('✅ Lead list management');
    console.log('✅ Email campaign tracking');
    console.log('✅ Individual email delivery monitoring');
    console.log('✅ Template-based email sending');
    console.log('\n🚀 The email marketing system is fully operational!');
  } else {
    console.log('\n⚠️ Some tables have issues. Please run the SQL deployment script first:');
    console.log('   1. Open Supabase SQL Editor');
    console.log('   2. Copy and paste the contents of: DEPLOY_EMAIL_MARKETING_TABLES.sql');
    console.log('   3. Run the script');
    console.log('   4. Run this verification script again');
  }

  return errorCount === 0;
}

// Run the verification
verifyEmailMarketingTables()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('❌ Verification failed:', error);
    process.exit(1);
  });
