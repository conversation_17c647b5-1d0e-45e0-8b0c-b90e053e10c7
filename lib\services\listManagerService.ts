/**
 * LIST MANAGER SERVICE
 * 
 * Comprehensive contact management system with:
 * - Contact creation and management
 * - Custom list management
 * - Email template system
 * - Bulk email sending capabilities
 * - Segmentation and filtering
 */

import { getServiceRoleClient } from '../supabase';
import { resendEmailService } from '../resendEmailService';

export interface Contact {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  full_name?: string;
  phone?: string;
  company?: string;
  tags: string[];
  custom_fields: Record<string, any>;
  source: 'manual' | 'import' | 'website' | 'api';
  status: 'active' | 'unsubscribed' | 'bounced' | 'invalid';
  created_at: string;
  updated_at: string;
}

export interface ContactList {
  id: string;
  name: string;
  description?: string;
  contact_count: number;
  tags: string[];
  created_by: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  html_content: string;
  text_content?: string;
  template_type: 'newsletter' | 'announcement' | 'promotional' | 'transactional';
  variables: string[];
  created_by: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface BulkEmailCampaign {
  id: string;
  name: string;
  template_id: string;
  list_ids: string[];
  subject: string;
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'failed';
  scheduled_at?: string;
  sent_at?: string;
  total_recipients: number;
  sent_count: number;
  failed_count: number;
  created_by: number;
  created_at: string;
  updated_at: string;
}

export class ListManagerService {
  private static instance: ListManagerService;
  private serviceClient = getServiceRoleClient();

  private constructor() {}

  public static getInstance(): ListManagerService {
    if (!ListManagerService.instance) {
      ListManagerService.instance = new ListManagerService();
    }
    return ListManagerService.instance;
  }

  /**
   * CONTACT MANAGEMENT
   */

  async createContact(contactData: Omit<Contact, 'id' | 'created_at' | 'updated_at'>): Promise<Contact | null> {
    try {
      const { data, error } = await this.serviceClient
        .from('contacts')
        .insert({
          ...contactData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating contact:', error);
      return null;
    }
  }

  async updateContact(contactId: string, updates: Partial<Contact>): Promise<Contact | null> {
    try {
      const { data, error } = await this.serviceClient
        .from('contacts')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', contactId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating contact:', error);
      return null;
    }
  }

  async deleteContact(contactId: string): Promise<boolean> {
    try {
      const { error } = await this.serviceClient
        .from('contacts')
        .delete()
        .eq('id', contactId);

      return !error;
    } catch (error) {
      console.error('Error deleting contact:', error);
      return false;
    }
  }

  async getContacts(filters?: {
    status?: string;
    tags?: string[];
    search?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ contacts: Contact[]; total: number }> {
    try {
      let query = this.serviceClient
        .from('contacts')
        .select('*', { count: 'exact' });

      if (filters?.status) {
        query = query.eq('status', filters.status);
      }

      if (filters?.tags && filters.tags.length > 0) {
        query = query.overlaps('tags', filters.tags);
      }

      if (filters?.search) {
        query = query.or(`email.ilike.%${filters.search}%,full_name.ilike.%${filters.search}%`);
      }

      if (filters?.limit) {
        query = query.limit(filters.limit);
      }

      if (filters?.offset) {
        query = query.range(filters.offset, (filters.offset || 0) + (filters.limit || 50) - 1);
      }

      const { data, error, count } = await query.order('created_at', { ascending: false });

      if (error) throw error;

      return {
        contacts: data || [],
        total: count || 0
      };
    } catch (error) {
      console.error('Error fetching contacts:', error);
      return { contacts: [], total: 0 };
    }
  }

  /**
   * LIST MANAGEMENT
   */

  async createList(listData: Omit<ContactList, 'id' | 'contact_count' | 'created_at' | 'updated_at'>): Promise<ContactList | null> {
    try {
      const { data, error } = await this.serviceClient
        .from('contact_lists')
        .insert({
          ...listData,
          contact_count: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating list:', error);
      return null;
    }
  }

  async addContactsToList(listId: string, contactIds: string[]): Promise<boolean> {
    try {
      const listContacts = contactIds.map(contactId => ({
        list_id: listId,
        contact_id: contactId,
        added_at: new Date().toISOString()
      }));

      const { error } = await this.serviceClient
        .from('list_contacts')
        .insert(listContacts);

      if (error) throw error;

      // Update contact count
      await this.updateListContactCount(listId);
      return true;
    } catch (error) {
      console.error('Error adding contacts to list:', error);
      return false;
    }
  }

  async removeContactsFromList(listId: string, contactIds: string[]): Promise<boolean> {
    try {
      const { error } = await this.serviceClient
        .from('list_contacts')
        .delete()
        .eq('list_id', listId)
        .in('contact_id', contactIds);

      if (error) throw error;

      // Update contact count
      await this.updateListContactCount(listId);
      return true;
    } catch (error) {
      console.error('Error removing contacts from list:', error);
      return false;
    }
  }

  private async updateListContactCount(listId: string): Promise<void> {
    try {
      const { count } = await this.serviceClient
        .from('list_contacts')
        .select('*', { count: 'exact', head: true })
        .eq('list_id', listId);

      await this.serviceClient
        .from('contact_lists')
        .update({
          contact_count: count || 0,
          updated_at: new Date().toISOString()
        })
        .eq('id', listId);
    } catch (error) {
      console.error('Error updating list contact count:', error);
    }
  }

  async getLists(createdBy?: number): Promise<ContactList[]> {
    try {
      let query = this.serviceClient
        .from('contact_lists')
        .select('*')
        .eq('is_active', true);

      if (createdBy) {
        query = query.eq('created_by', createdBy);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching lists:', error);
      return [];
    }
  }

  /**
   * EMAIL TEMPLATE MANAGEMENT
   */

  async createTemplate(templateData: Omit<EmailTemplate, 'id' | 'created_at' | 'updated_at'>): Promise<EmailTemplate | null> {
    try {
      const { data, error } = await this.serviceClient
        .from('email_templates')
        .insert({
          ...templateData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating template:', error);
      return null;
    }
  }

  async getTemplates(createdBy?: number): Promise<EmailTemplate[]> {
    try {
      let query = this.serviceClient
        .from('email_templates')
        .select('*')
        .eq('is_active', true);

      if (createdBy) {
        query = query.eq('created_by', createdBy);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching templates:', error);
      return [];
    }
  }

  /**
   * BULK EMAIL CAMPAIGNS
   */

  async createCampaign(campaignData: Omit<BulkEmailCampaign, 'id' | 'sent_count' | 'failed_count' | 'created_at' | 'updated_at'>): Promise<BulkEmailCampaign | null> {
    try {
      const { data, error } = await this.serviceClient
        .from('bulk_email_campaigns')
        .insert({
          ...campaignData,
          sent_count: 0,
          failed_count: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating campaign:', error);
      return null;
    }
  }

  async sendCampaign(campaignId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Get campaign details
      const { data: campaign, error: campaignError } = await this.serviceClient
        .from('bulk_email_campaigns')
        .select(`
          *,
          template:email_templates(*)
        `)
        .eq('id', campaignId)
        .single();

      if (campaignError || !campaign) {
        throw new Error('Campaign not found');
      }

      if (campaign.status !== 'draft' && campaign.status !== 'scheduled') {
        throw new Error('Campaign cannot be sent in current status');
      }

      // Update campaign status to sending
      await this.serviceClient
        .from('bulk_email_campaigns')
        .update({
          status: 'sending',
          updated_at: new Date().toISOString()
        })
        .eq('id', campaignId);

      // Get all contacts from the specified lists
      const { data: listContacts, error: contactsError } = await this.serviceClient
        .from('list_contacts')
        .select(`
          contact:contacts(*)
        `)
        .in('list_id', campaign.list_ids)
        .eq('contacts.status', 'active');

      if (contactsError) {
        throw contactsError;
      }

      // Extract unique contacts (avoid duplicates across lists)
      const uniqueContacts = new Map();
      listContacts?.forEach((lc: any) => {
        if (lc.contact && lc.contact.email) {
          uniqueContacts.set(lc.contact.email, lc.contact);
        }
      });

      const contacts = Array.from(uniqueContacts.values());
      const recipients = contacts.map((contact: any) => contact.email);

      if (recipients.length === 0) {
        throw new Error('No active contacts found in selected lists');
      }

      // Send bulk email using ResendEmailService
      const bulkEmailData = {
        recipients,
        subject: campaign.subject,
        htmlContent: campaign.template.html_content,
        textContent: campaign.template.text_content
      };

      const results = await resendEmailService.sendBulkEmail(bulkEmailData);

      // Count successful and failed sends
      const successCount = results.filter(r => r.success).length;
      const failedCount = results.filter(r => !r.success).length;

      // Update campaign with results
      await this.serviceClient
        .from('bulk_email_campaigns')
        .update({
          status: failedCount === 0 ? 'sent' : 'failed',
          sent_at: new Date().toISOString(),
          sent_count: successCount,
          failed_count: failedCount,
          updated_at: new Date().toISOString()
        })
        .eq('id', campaignId);

      // Log campaign results
      await this.serviceClient
        .from('campaign_logs')
        .insert({
          campaign_id: campaignId,
          total_recipients: recipients.length,
          sent_count: successCount,
          failed_count: failedCount,
          results: results,
          sent_at: new Date().toISOString()
        });

      return {
        success: true
      };

    } catch (error) {
      console.error('Error sending campaign:', error);

      // Update campaign status to failed
      await this.serviceClient
        .from('bulk_email_campaigns')
        .update({
          status: 'failed',
          updated_at: new Date().toISOString()
        })
        .eq('id', campaignId);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  async getCampaigns(createdBy?: number): Promise<BulkEmailCampaign[]> {
    try {
      let query = this.serviceClient
        .from('bulk_email_campaigns')
        .select('*');

      if (createdBy) {
        query = query.eq('created_by', createdBy);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching campaigns:', error);
      return [];
    }
  }

  /**
   * IMPORT/EXPORT FUNCTIONALITY
   */

  async importContacts(contacts: Omit<Contact, 'id' | 'created_at' | 'updated_at'>[]): Promise<{ success: number; failed: number }> {
    let successCount = 0;
    let failedCount = 0;

    for (const contactData of contacts) {
      const result = await this.createContact(contactData);
      if (result) {
        successCount++;
      } else {
        failedCount++;
      }
    }

    return { success: successCount, failed: failedCount };
  }

  async exportContacts(listId?: string): Promise<Contact[]> {
    try {
      let query = this.serviceClient
        .from('contacts')
        .select('*')
        .eq('status', 'active');

      if (listId) {
        // Get contacts from specific list
        const { data: listContacts } = await this.serviceClient
          .from('list_contacts')
          .select('contact_id')
          .eq('list_id', listId);

        if (listContacts && listContacts.length > 0) {
          const contactIds = listContacts.map(lc => lc.contact_id);
          query = query.in('id', contactIds);
        } else {
          return [];
        }
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error exporting contacts:', error);
      return [];
    }
  }
}
