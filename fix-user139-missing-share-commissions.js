import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixUser139MissingShareCommissions() {
  console.log('🔧 FIXING USER 139 MISSING SHARE COMMISSIONS\n');

  const userId = 139;

  // Get user 139's commission transactions that are missing share commissions
  const { data: commissions, error: commissionError } = await supabase
    .from('commission_transactions')
    .select('*')
    .eq('referrer_id', userId)
    .eq('share_commission', 0)
    .order('created_at', { ascending: false });

  if (commissionError) {
    console.log('❌ Error getting commissions:', commissionError.message);
    return;
  }

  console.log(`📋 Found ${commissions.length} commission transactions missing share commissions\n`);

  let totalSharesAdded = 0;
  let successCount = 0;
  let errorCount = 0;

  for (let i = 0; i < commissions.length; i++) {
    const commission = commissions[i];
    
    console.log(`${i + 1}. Processing Commission ID: ${commission.id}`);
    console.log(`   Purchase Amount: $${commission.share_purchase_amount}`);
    
    // Get the actual purchase to find shares purchased
    const { data: purchase, error: purchaseError } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .eq('user_id', commission.referred_id)
      .eq('total_amount', commission.share_purchase_amount)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (purchaseError || !purchase) {
      console.log(`   ❌ Could not find matching purchase`);
      errorCount++;
      continue;
    }

    const sharesPurchased = purchase.shares_purchased || 0;
    const expectedShareCommission = sharesPurchased * 0.15;
    
    console.log(`   Shares Purchased: ${sharesPurchased}`);
    console.log(`   Expected Share Commission: ${expectedShareCommission.toFixed(2)}`);

    if (expectedShareCommission === 0) {
      console.log(`   ⚠️ No shares to commission (shares purchased = 0)`);
      continue;
    }

    // Update the commission transaction to include share commission
    const { error: updateError } = await supabase
      .from('commission_transactions')
      .update({
        share_commission: expectedShareCommission
      })
      .eq('id', commission.id);

    if (updateError) {
      console.log(`   ❌ Failed to update commission transaction:`, updateError.message);
      errorCount++;
      continue;
    }

    console.log(`   ✅ Updated commission transaction with ${expectedShareCommission.toFixed(2)} shares`);
    totalSharesAdded += expectedShareCommission;
    successCount++;
  }

  // Update user 139's commission balance
  console.log(`\n💰 Updating User 139's Commission Balance:`);
  
  const { data: currentBalance, error: balanceError } = await supabase
    .from('commission_balances')
    .select('*')
    .eq('user_id', userId)
    .single();

  if (balanceError) {
    console.log('❌ Error getting current balance:', balanceError.message);
    return;
  }

  const newShareBalance = (currentBalance.share_balance || 0) + totalSharesAdded;
  const newTotalEarnedShares = (currentBalance.total_earned_shares || 0) + totalSharesAdded;

  const { error: updateBalanceError } = await supabase
    .from('commission_balances')
    .update({
      share_balance: newShareBalance,
      total_earned_shares: newTotalEarnedShares,
      last_updated: new Date().toISOString()
    })
    .eq('user_id', userId);

  if (updateBalanceError) {
    console.log('❌ Error updating commission balance:', updateBalanceError.message);
  } else {
    console.log(`✅ Updated commission balance:`);
    console.log(`   Previous Share Balance: ${currentBalance.share_balance || 0}`);
    console.log(`   New Share Balance: ${newShareBalance.toFixed(2)}`);
    console.log(`   Previous Total Earned Shares: ${currentBalance.total_earned_shares || 0}`);
    console.log(`   New Total Earned Shares: ${newTotalEarnedShares.toFixed(2)}`);
  }

  // Create audit log entry
  const auditDetails = {
    user_id: userId,
    action: 'BACKFILL_MISSING_SHARE_COMMISSIONS',
    transactions_updated: successCount,
    total_shares_added: totalSharesAdded,
    commission_ids: commissions.slice(0, successCount).map(c => c.id),
    timestamp: new Date().toISOString()
  };

  const { error: auditError } = await supabase
    .from('admin_audit_logs')
    .insert({
      admin_telegram_id: 0,
      admin_username: 'SYSTEM_BACKFILL',
      action: 'BACKFILL_MISSING_SHARE_COMMISSIONS',
      target_type: 'user_commissions',
      target_id: userId.toString(),
      details: JSON.stringify(auditDetails),
      timestamp: new Date().toISOString()
    });

  if (auditError) {
    console.log('⚠️ Could not create audit log:', auditError.message);
  } else {
    console.log('✅ Created audit log entry');
  }

  console.log(`\n📊 SUMMARY:`);
  console.log(`• Commission transactions processed: ${commissions.length}`);
  console.log(`• Successfully updated: ${successCount}`);
  console.log(`• Errors: ${errorCount}`);
  console.log(`• Total shares added: ${totalSharesAdded.toFixed(2)}`);
  console.log(`• User 139's new share balance: ${newShareBalance.toFixed(2)}`);

  console.log('\n✅ User 139 share commission backfill complete!');
}

// Run the fix
fixUser139MissingShareCommissions().catch(console.error);
