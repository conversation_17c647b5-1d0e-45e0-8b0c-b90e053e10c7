// <PERSON>ript to populate the investment_phases table with all 20 phases
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('Please ensure REACT_APP_SUPABASE_URL and REACT_APP_SUPABASE_ANON_KEY are set in your .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function setupInvestmentPhases() {
  console.log('🚀 Setting up Aureus Africa Investment Phases...\n');

  try {
    // Check if phases already exist
    console.log('🔍 Checking existing investment phases...');
    const { data: existingPhases, error: checkError } = await supabase
      .from('investment_phases')
      .select('*')
      .order('phase_number');

    if (checkError) {
      console.error('❌ Error checking existing phases:', checkError.message);
      return;
    }

    if (existingPhases && existingPhases.length > 0) {
      console.log(`✅ Found ${existingPhases.length} existing phases:`);
      existingPhases.forEach(phase => {
        console.log(`   ${phase.phase_name}: $${phase.price_per_share} ${phase.is_active ? '(ACTIVE)' : ''}`);
      });
      
      console.log('\n⚠️ Investment phases already exist. To recreate them, delete existing phases first.');
      console.log('   You can do this in your Supabase dashboard or by running:');
      console.log('   DELETE FROM investment_phases;');
      return;
    }

    // Create all 20 phases according to Aureus Africa expansion plan
    console.log('📊 Creating all 20 investment phases...');
    
    const allPhases = [
      // Pre Sale - Special phase with lower price and bonus shares
      { 
        phase_number: 0, 
        phase_name: 'Pre Sale', 
        price_per_share: 5.00, 
        total_shares_available: 200000, 
        shares_sold: 0,
        is_active: true,
        start_date: new Date().toISOString(),
        description: 'Pre-sale phase with 15% USDT + 15% Bonus NFT Shares commission'
      },
      // Regular phases 1-19 with progressive pricing
      { phase_number: 1, phase_name: 'Phase 1', price_per_share: 10.00, total_shares_available: 100000, shares_sold: 0, is_active: false },
      { phase_number: 2, phase_name: 'Phase 2', price_per_share: 15.00, total_shares_available: 100000, shares_sold: 0, is_active: false },
      { phase_number: 3, phase_name: 'Phase 3', price_per_share: 20.00, total_shares_available: 100000, shares_sold: 0, is_active: false },
      { phase_number: 4, phase_name: 'Phase 4', price_per_share: 25.00, total_shares_available: 100000, shares_sold: 0, is_active: false },
      { phase_number: 5, phase_name: 'Phase 5', price_per_share: 30.00, total_shares_available: 100000, shares_sold: 0, is_active: false },
      { phase_number: 6, phase_name: 'Phase 6', price_per_share: 35.00, total_shares_available: 100000, shares_sold: 0, is_active: false },
      { phase_number: 7, phase_name: 'Phase 7', price_per_share: 40.00, total_shares_available: 100000, shares_sold: 0, is_active: false },
      { phase_number: 8, phase_name: 'Phase 8', price_per_share: 45.00, total_shares_available: 100000, shares_sold: 0, is_active: false },
      { phase_number: 9, phase_name: 'Phase 9', price_per_share: 50.00, total_shares_available: 100000, shares_sold: 0, is_active: false },
      { phase_number: 10, phase_name: 'Phase 10', price_per_share: 55.00, total_shares_available: 100000, shares_sold: 0, is_active: false },
      { phase_number: 11, phase_name: 'Phase 11', price_per_share: 60.00, total_shares_available: 100000, shares_sold: 0, is_active: false },
      { phase_number: 12, phase_name: 'Phase 12', price_per_share: 65.00, total_shares_available: 100000, shares_sold: 0, is_active: false },
      { phase_number: 13, phase_name: 'Phase 13', price_per_share: 70.00, total_shares_available: 100000, shares_sold: 0, is_active: false },
      { phase_number: 14, phase_name: 'Phase 14', price_per_share: 75.00, total_shares_available: 100000, shares_sold: 0, is_active: false },
      { phase_number: 15, phase_name: 'Phase 15', price_per_share: 80.00, total_shares_available: 100000, shares_sold: 0, is_active: false },
      { phase_number: 16, phase_name: 'Phase 16', price_per_share: 85.00, total_shares_available: 100000, shares_sold: 0, is_active: false },
      { phase_number: 17, phase_name: 'Phase 17', price_per_share: 90.00, total_shares_available: 100000, shares_sold: 0, is_active: false },
      { phase_number: 18, phase_name: 'Phase 18', price_per_share: 95.00, total_shares_available: 100000, shares_sold: 0, is_active: false },
      { phase_number: 19, phase_name: 'Phase 19', price_per_share: 100.00, total_shares_available: 100000, shares_sold: 0, is_active: false }
    ];

    // Insert all phases
    const { data: createdPhases, error: createError } = await supabase
      .from('investment_phases')
      .insert(allPhases)
      .select();

    if (createError) {
      console.error('❌ Error creating investment phases:', createError.message);
      console.error('Details:', createError);
      return;
    }

    console.log(`✅ Successfully created ${createdPhases.length} investment phases!`);
    console.log('\n📊 Phase Summary:');
    console.log('   Pre Sale: $5.00 per share (200,000 shares) - ACTIVE');
    console.log('   Phase 1-19: $10.00-$100.00 per share (100,000 shares each) - Coming Soon');
    
    console.log('\n🏆 Commission Structure:');
    console.log('   Pre Sale (Phase 0): 15% USDT + 15% Bonus NFT Shares');
    console.log('   Phases 1-19: 15% USDT Commission Only');

    console.log('\n🔒 Setting up public access policy...');

    // Add public read access policy for investment_phases
    // This allows the affiliate landing page to be viewed by non-authenticated users
    const policySQL = `
      CREATE POLICY IF NOT EXISTS "Public read access for investment phases"
      ON investment_phases FOR SELECT
      USING (true);
    `;

    try {
      const { error: policyError } = await supabase.rpc('exec_sql', {
        sql_query: policySQL
      });

      if (policyError) {
        console.log('⚠️ Policy creation result:', policyError.message);
        console.log('   This is normal if the policy already exists');
      } else {
        console.log('✅ Public read access policy created for investment_phases');
      }
    } catch (policyErr) {
      console.log('⚠️ Policy setup note:', policyErr.message);
      console.log('   The phases data is still accessible to authenticated users');
    }

    console.log('\n🎯 Next Steps:');
    console.log('   1. Your affiliate landing page should now show all phases');
    console.log('   2. The Gold Diggers Club competition will display phase data');
    console.log('   3. Users can now purchase shares from the active Pre Sale phase');
    console.log('   4. Public users can view phase information without logging in');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the setup
setupInvestmentPhases();
