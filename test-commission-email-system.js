/**
 * Comprehensive test script for commission email notification system
 * Tests all email notifications related to commission processing
 */

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const testCommissionEmailSystem = async () => {
  try {
    console.log('🧪 TESTING COMMISSION EMAIL NOTIFICATION SYSTEM');
    console.log('='.repeat(60));

    // Step 1: Test email service configuration
    console.log('\n1️⃣ Testing email service configuration...');
    
    const resendApiKey = process.env.RESEND_API_KEY;
    const fromEmail = process.env.RESEND_FROM_EMAIL;
    const fromName = process.env.RESEND_FROM_NAME;

    if (!resendApiKey || !fromEmail || !fromName) {
      console.log('❌ Email service not properly configured:');
      console.log(`   • RESEND_API_KEY: ${resendApiKey ? '✅ Set' : '❌ Missing'}`);
      console.log(`   • FROM_EMAIL: ${fromEmail || '❌ Missing'}`);
      console.log(`   • FROM_NAME: ${fromName || '❌ Missing'}`);
      return;
    }

    console.log('✅ Email service configuration verified:');
    console.log(`   • FROM_EMAIL: ${fromEmail}`);
    console.log(`   • FROM_NAME: ${fromName}`);
    console.log(`   • API_KEY: ${resendApiKey.substring(0, 10)}...`);

    // Step 2: Check recent commission transactions
    console.log('\n2️⃣ Checking recent commission transactions...');
    
    const { data: recentCommissions, error: commissionError } = await supabase
      .from('commission_transactions')
      .select(`
        *,
        referrer:referrer_id(id, username, email, full_name),
        referred:referred_id(id, username, email, full_name)
      `)
      .eq('status', 'approved')
      .order('created_at', { ascending: false })
      .limit(5);

    if (commissionError) {
      console.error('❌ Error fetching commission transactions:', commissionError);
      return;
    }

    console.log(`✅ Found ${recentCommissions?.length || 0} recent commission transactions`);
    
    if (recentCommissions && recentCommissions.length > 0) {
      console.log('\n📋 Recent Commission Transactions:');
      recentCommissions.forEach((commission, index) => {
        console.log(`   ${index + 1}. ID: ${commission.id}`);
        console.log(`      • Referrer: ${commission.referrer?.username} (${commission.referrer?.email})`);
        console.log(`      • Referred: ${commission.referred?.username}`);
        console.log(`      • USDT: $${commission.usdt_commission}`);
        console.log(`      • Shares: ${commission.share_commission}`);
        console.log(`      • Date: ${new Date(commission.created_at).toLocaleString()}`);
        console.log('');
      });
    }

    // Step 3: Test email notification for most recent commission
    if (recentCommissions && recentCommissions.length > 0) {
      console.log('\n3️⃣ Testing commission email notification...');
      
      const testCommission = recentCommissions[0];
      const referrer = testCommission.referrer;
      
      if (!referrer?.email) {
        console.log('❌ No email address found for referrer');
        return;
      }

      console.log(`📧 Testing email to: ${referrer.email}`);
      console.log(`   • Referrer: ${referrer.full_name || referrer.username}`);
      console.log(`   • USDT Commission: $${testCommission.usdt_commission}`);
      console.log(`   • Share Commission: ${testCommission.share_commission}`);

      // Import and test the email service
      try {
        // Dynamic import to handle ES modules
        const { resendEmailService } = await import('./lib/resendEmailService.js');
        
        const emailResult = await resendEmailService.sendCommissionEarnedNotification({
          email: '<EMAIL>', // Always send test emails to you
          fullName: referrer.full_name || referrer.username,
          usdtCommission: parseFloat(testCommission.usdt_commission),
          shareCommission: parseFloat(testCommission.share_commission),
          referredUserName: testCommission.referred?.username || 'Test User',
          purchaseAmount: parseFloat(testCommission.share_purchase_amount),
          transactionId: testCommission.id
        });

        if (emailResult.success) {
          console.log('✅ Commission email sent successfully!');
          console.log(`   • Message ID: ${emailResult.messageId}`);
          console.log(`   • Sent to: <EMAIL> (test email)`);
        } else {
          console.log('❌ Commission email failed:');
          console.log(`   • Error: ${emailResult.error}`);
        }
      } catch (importError) {
        console.error('❌ Error importing email service:', importError);
      }
    }

    // Step 4: Check notification tables
    console.log('\n4️⃣ Checking notification system...');
    
    const { data: notifications, error: notificationError } = await supabase
      .from('user_notifications')
      .select('*')
      .eq('category', 'commission')
      .order('created_at', { ascending: false })
      .limit(5);

    if (notificationError) {
      console.log('⚠️ Could not fetch notifications:', notificationError.message);
    } else {
      console.log(`✅ Found ${notifications?.length || 0} commission notifications`);
      
      if (notifications && notifications.length > 0) {
        console.log('\n📋 Recent Commission Notifications:');
        notifications.forEach((notification, index) => {
          console.log(`   ${index + 1}. ${notification.title}`);
          console.log(`      • User ID: ${notification.user_id}`);
          console.log(`      • Read: ${notification.is_read ? 'Yes' : 'No'}`);
          console.log(`      • Date: ${new Date(notification.created_at).toLocaleString()}`);
        });
      }
    }

    console.log('\n🎯 EMAIL SYSTEM TEST SUMMARY:');
    console.log('='.repeat(40));
    console.log('✅ Email service configuration: VERIFIED');
    console.log('✅ Commission transactions: FOUND');
    console.log('✅ Email notification: TESTED');
    console.log('✅ In-app notifications: CHECKED');
    
    console.log('\n📧 Next Steps:');
    console.log('1. Check your email (<EMAIL>) for the test commission notification');
    console.log('2. Approve another conversion request to test the full flow');
    console.log('3. Verify that both in-app and email notifications are working');
    
    console.log('\n🚀 The commission email system is ready for testing!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
};

testCommissionEmailSystem();
