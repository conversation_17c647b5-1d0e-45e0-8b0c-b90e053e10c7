const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupCertificatesTable() {
  try {
    console.log('🚀 Setting up certificates table...');
    
    // Read the SQL file (use simple version to avoid RLS issues)
    const sqlPath = path.join(__dirname, 'create-certificates-table-simple.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Execute the SQL
    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });
    
    if (error) {
      console.error('❌ Error creating certificates table:', error);
      
      // Try alternative method - execute SQL directly
      console.log('🔄 Trying alternative method...');
      
      // Split SQL into individual statements and execute them
      const statements = sql
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
      
      for (const statement of statements) {
        if (statement.trim()) {
          console.log(`Executing: ${statement.substring(0, 50)}...`);
          const { error: stmtError } = await supabase.rpc('exec_sql', { 
            sql_query: statement + ';' 
          });
          
          if (stmtError) {
            console.error(`❌ Error executing statement: ${stmtError.message}`);
            console.error(`Statement: ${statement.substring(0, 100)}...`);
          } else {
            console.log('✅ Statement executed successfully');
          }
        }
      }
    } else {
      console.log('✅ Certificates table created successfully');
    }
    
    // Verify the table was created
    const { data: tableInfo, error: tableError } = await supabase
      .from('certificates')
      .select('count(*)')
      .limit(1);
    
    if (tableError) {
      console.error('❌ Error verifying certificates table:', tableError);
    } else {
      console.log('✅ Certificates table verified and accessible');
    }
    
    console.log('🎉 Certificate table setup complete!');
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Alternative function to create table with basic SQL
async function createBasicCertificatesTable() {
  console.log('🔄 Creating basic certificates table...');
  
  const basicSQL = `
    CREATE TABLE IF NOT EXISTS certificates (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id INTEGER NOT NULL,
      purchase_id UUID,
      certificate_number VARCHAR(50) UNIQUE NOT NULL,
      shares_count INTEGER NOT NULL,
      issue_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      status VARCHAR(20) DEFAULT 'issued',
      certificate_data JSONB DEFAULT '{}'::jsonb,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `;
  
  try {
    const { error } = await supabase.rpc('exec_sql', { sql_query: basicSQL });
    
    if (error) {
      console.error('❌ Error creating basic table:', error);
    } else {
      console.log('✅ Basic certificates table created');
      
      // Create indexes
      const indexSQL = `
        CREATE INDEX IF NOT EXISTS idx_certificates_user_id ON certificates(user_id);
        CREATE INDEX IF NOT EXISTS idx_certificates_certificate_number ON certificates(certificate_number);
      `;
      
      const { error: indexError } = await supabase.rpc('exec_sql', { sql_query: indexSQL });
      
      if (indexError) {
        console.error('❌ Error creating indexes:', indexError);
      } else {
        console.log('✅ Indexes created successfully');
      }
    }
  } catch (error) {
    console.error('❌ Error in basic table creation:', error);
  }
}

// Test certificate creation
async function testCertificateCreation() {
  console.log('🧪 Testing certificate creation...');

  try {
    // First, check if we have any approved purchases
    const { data: purchases, error: purchasesError } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .eq('status', 'active')
      .limit(1);

    if (purchasesError) {
      console.error('❌ Error checking purchases:', purchasesError);
      return;
    }

    if (!purchases || purchases.length === 0) {
      console.log('ℹ️ No approved purchases found to create certificates for');
      return;
    }

    const purchase = purchases[0];
    console.log(`📋 Found purchase: ${purchase.id} for user ${purchase.user_id}`);

    // Test the admin certificate creation function
    const { data: certNumber, error: certError } = await supabase.rpc('admin_create_certificate', {
      p_user_id: purchase.user_id,
      p_purchase_id: purchase.id,
      p_shares_count: purchase.shares_purchased,
      p_certificate_data: {
        package_name: purchase.package_name,
        total_amount: purchase.total_amount,
        created_by: 'test_script'
      }
    });

    if (certError) {
      console.error('❌ Error creating test certificate:', certError);
    } else {
      console.log(`✅ Test certificate created: ${certNumber}`);

      // Verify the certificate was created
      const { data: cert, error: verifyError } = await supabase
        .from('certificates')
        .select('*')
        .eq('certificate_number', certNumber)
        .single();

      if (verifyError) {
        console.error('❌ Error verifying certificate:', verifyError);
      } else {
        console.log('✅ Certificate verified in database:', cert);
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the setup
if (require.main === module) {
  setupCertificatesTable()
    .then(() => {
      console.log('✅ Setup completed');
      return testCertificateCreation();
    })
    .then(() => {
      console.log('✅ All tests completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Setup failed:', error);
      console.log('🔄 Trying basic table creation...');
      createBasicCertificatesTable()
        .then(() => process.exit(0))
        .catch(() => process.exit(1));
    });
}

module.exports = { setupCertificatesTable, createBasicCertificatesTable };
