import { supabase } from './supabase.ts'

/**
 * Track a referral link click for analytics
 * @param {string} referrerUsername - The username of the referrer
 * @param {string} campaignSource - The campaign source (e.g., 'facebook', 'instagram')
 * @param {Object} metadata - Additional tracking metadata
 * @returns {Promise<Object>} - The tracking result
 */
export async function trackReferralClick(referrerUsername, campaignSource = null, metadata = {}) {
  try {
    console.log('📊 Tracking referral click:', { referrerUsername, campaignSource })

    // Extract metadata
    const {
      ipAddress = null,
      userAgent = null,
      refererUrl = null
    } = metadata

    // Insert click record
    const { data: clickData, error: clickError } = await supabase
      .from('referral_clicks')
      .insert({
        referrer_username: referrerUsername,
        campaign_source: campaignSource,
        ip_address: ipAddress,
        user_agent: userAgent,
        referer_url: refererUrl,
        clicked_at: new Date().toISOString()
      })
      .select()
      .single()

    if (clickError) {
      console.error('❌ Error tracking referral click:', clickError)
      return { success: false, error: clickError }
    }

    // Update daily analytics
    await updateDailyAnalytics(referrerUsername, campaignSource, 'click')

    console.log('✅ Referral click tracked successfully')
    return { success: true, data: clickData }

  } catch (error) {
    console.error('❌ Error in trackReferralClick:', error)
    return { success: false, error }
  }
}

/**
 * Track a referral conversion (successful registration)
 * @param {string} referrerUsername - The username of the referrer
 * @param {string} campaignSource - The campaign source
 * @param {number} conversionValue - The value of the conversion (optional)
 * @returns {Promise<Object>} - The tracking result
 */
export async function trackReferralConversion(referrerUsername, campaignSource = null, conversionValue = 0) {
  try {
    console.log('🎯 Tracking referral conversion:', { referrerUsername, campaignSource, conversionValue })

    // Find the most recent click from this referrer/campaign that hasn't been converted
    const { data: clickData, error: clickError } = await supabase
      .from('referral_clicks')
      .select('*')
      .eq('referrer_username', referrerUsername)
      .eq('campaign_source', campaignSource)
      .eq('converted', false)
      .order('clicked_at', { ascending: false })
      .limit(1)
      .single()

    if (clickError && clickError.code !== 'PGRST116') {
      console.error('❌ Error finding referral click:', clickError)
    }

    // Mark the click as converted if found
    if (clickData) {
      const { error: updateError } = await supabase
        .from('referral_clicks')
        .update({
          converted: true,
          converted_at: new Date().toISOString()
        })
        .eq('id', clickData.id)

      if (updateError) {
        console.error('❌ Error updating click conversion:', updateError)
      }
    }

    // Update daily analytics
    await updateDailyAnalytics(referrerUsername, campaignSource, 'conversion', conversionValue)

    console.log('✅ Referral conversion tracked successfully')
    return { success: true, clickData }

  } catch (error) {
    console.error('❌ Error in trackReferralConversion:', error)
    return { success: false, error }
  }
}

/**
 * Update daily analytics for a referrer and campaign
 * @param {string} referrerUsername - The username of the referrer
 * @param {string} campaignSource - The campaign source
 * @param {string} eventType - The type of event ('click', 'registration', 'conversion')
 * @param {number} value - The value associated with the event (optional)
 */
async function updateDailyAnalytics(referrerUsername, campaignSource, eventType, value = 0) {
  try {
    // Get referrer user ID
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('username', referrerUsername)
      .single()

    if (userError) {
      console.error('❌ Error finding referrer user:', userError)
      return
    }

    const referrerId = userData.id
    const today = new Date().toISOString().split('T')[0] // YYYY-MM-DD format
    const campaignName = campaignSource || 'direct'

    // Try to get existing analytics record for today
    const { data: existingAnalytics, error: analyticsError } = await supabase
      .from('referral_analytics')
      .select('*')
      .eq('referrer_id', referrerId)
      .eq('campaign_name', campaignName)
      .eq('date_tracked', today)
      .single()

    if (analyticsError && analyticsError.code !== 'PGRST116') {
      console.error('❌ Error fetching analytics:', analyticsError)
      return
    }

    if (existingAnalytics) {
      // Update existing record
      const updates = {}
      
      if (eventType === 'click') {
        updates.clicks = existingAnalytics.clicks + 1
      } else if (eventType === 'registration') {
        updates.registrations = existingAnalytics.registrations + 1
      } else if (eventType === 'conversion') {
        updates.conversions = existingAnalytics.conversions + 1
        updates.total_revenue = existingAnalytics.total_revenue + value
      }

      // Calculate conversion rate
      if (updates.clicks || updates.conversions) {
        const totalClicks = updates.clicks || existingAnalytics.clicks
        const totalConversions = updates.conversions || existingAnalytics.conversions
        updates.conversion_rate = totalClicks > 0 ? (totalConversions / totalClicks * 100) : 0
      }

      const { error: updateError } = await supabase
        .from('referral_analytics')
        .update(updates)
        .eq('id', existingAnalytics.id)

      if (updateError) {
        console.error('❌ Error updating analytics:', updateError)
      }
    } else {
      // Create new record
      const newRecord = {
        referrer_id: referrerId,
        campaign_name: campaignName,
        campaign_source: campaignSource,
        clicks: eventType === 'click' ? 1 : 0,
        registrations: eventType === 'registration' ? 1 : 0,
        conversions: eventType === 'conversion' ? 1 : 0,
        total_revenue: eventType === 'conversion' ? value : 0,
        date_tracked: today
      }

      // Calculate conversion rate
      newRecord.conversion_rate = newRecord.clicks > 0 ? (newRecord.conversions / newRecord.clicks * 100) : 0

      const { error: insertError } = await supabase
        .from('referral_analytics')
        .insert(newRecord)

      if (insertError) {
        console.error('❌ Error inserting analytics:', insertError)
      }
    }

  } catch (error) {
    console.error('❌ Error in updateDailyAnalytics:', error)
  }
}

/**
 * Get campaign analytics for a user
 * @param {number} userId - The user ID
 * @param {string} dateRange - The date range ('7d', '30d', '90d', 'all')
 * @returns {Promise<Object>} - The analytics data
 */
export async function getCampaignAnalytics(userId, dateRange = '30d') {
  try {
    let dateFilter = ''
    
    if (dateRange !== 'all') {
      const days = parseInt(dateRange.replace('d', ''))
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)
      dateFilter = startDate.toISOString().split('T')[0]
    }

    let query = supabase
      .from('referral_analytics')
      .select('*')
      .eq('referrer_id', userId)

    if (dateFilter) {
      query = query.gte('date_tracked', dateFilter)
    }

    const { data: analytics, error } = await query.order('date_tracked', { ascending: false })

    if (error) {
      console.error('❌ Error fetching campaign analytics:', error)
      return { success: false, error }
    }

    // Aggregate data by campaign
    const campaignSummary = {}
    let totalClicks = 0
    let totalConversions = 0
    let totalRevenue = 0

    analytics.forEach(record => {
      const campaign = record.campaign_name
      
      if (!campaignSummary[campaign]) {
        campaignSummary[campaign] = {
          campaign_name: campaign,
          campaign_source: record.campaign_source,
          clicks: 0,
          conversions: 0,
          revenue: 0,
          conversion_rate: 0
        }
      }

      campaignSummary[campaign].clicks += record.clicks
      campaignSummary[campaign].conversions += record.conversions
      campaignSummary[campaign].revenue += record.total_revenue

      totalClicks += record.clicks
      totalConversions += record.conversions
      totalRevenue += record.total_revenue
    })

    // Calculate conversion rates
    Object.values(campaignSummary).forEach(campaign => {
      campaign.conversion_rate = campaign.clicks > 0 ? (campaign.conversions / campaign.clicks * 100) : 0
    })

    const overallConversionRate = totalClicks > 0 ? (totalConversions / totalClicks * 100) : 0

    return {
      success: true,
      data: {
        campaigns: Object.values(campaignSummary),
        totals: {
          clicks: totalClicks,
          conversions: totalConversions,
          revenue: totalRevenue,
          conversion_rate: overallConversionRate
        },
        raw_analytics: analytics
      }
    }

  } catch (error) {
    console.error('❌ Error in getCampaignAnalytics:', error)
    return { success: false, error }
  }
}

/**
 * Get top performing referral codes by usage frequency
 * @param {number} userId - The user ID (optional, for user-specific data)
 * @param {number} limit - Number of top codes to return
 * @returns {Promise<Object>} - The top referral codes data
 */
export async function getTopReferralCodes(userId = null, limit = 10) {
  try {
    let query = supabase
      .from('referral_analytics')
      .select(`
        campaign_name,
        campaign_source,
        clicks,
        registrations,
        conversions,
        total_revenue,
        conversion_rate,
        referrer_id,
        users!referrer_id(username, full_name)
      `)

    if (userId) {
      query = query.eq('referrer_id', userId)
    }

    const { data: codes, error } = await query
      .order('clicks', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('❌ Error fetching top referral codes:', error)
      return { success: false, error }
    }

    // Aggregate data by campaign name for better insights
    const aggregatedCodes = codes.reduce((acc, item) => {
      const key = `${item.campaign_name}_${item.referrer_id}`
      if (!acc[key]) {
        acc[key] = {
          campaign_name: item.campaign_name,
          campaign_source: item.campaign_source,
          referrer_username: item.users?.username || 'Unknown',
          referrer_name: item.users?.full_name || 'Unknown',
          total_clicks: 0,
          total_registrations: 0,
          total_conversions: 0,
          total_revenue: 0,
          usage_frequency: 0
        }
      }

      acc[key].total_clicks += item.clicks || 0
      acc[key].total_registrations += item.registrations || 0
      acc[key].total_conversions += item.conversions || 0
      acc[key].total_revenue += item.total_revenue || 0
      acc[key].usage_frequency += 1

      return acc
    }, {})

    // Convert to array and sort by total clicks
    const sortedCodes = Object.values(aggregatedCodes)
      .sort((a, b) => b.total_clicks - a.total_clicks)
      .map(code => ({
        ...code,
        conversion_rate: code.total_clicks > 0 ?
          ((code.total_conversions / code.total_clicks) * 100).toFixed(2) : '0.00'
      }))

    return {
      success: true,
      data: sortedCodes,
      summary: {
        totalCodes: sortedCodes.length,
        totalClicks: sortedCodes.reduce((sum, code) => sum + code.total_clicks, 0),
        totalRevenue: sortedCodes.reduce((sum, code) => sum + code.total_revenue, 0)
      }
    }

  } catch (error) {
    console.error('❌ Error in getTopReferralCodes:', error)
    return { success: false, error }
  }
}

/**
 * Export referral performance data to CSV format
 * @param {number} userId - The user ID
 * @param {string} dateRange - The date range
 * @returns {Promise<Object>} - The CSV data
 */
export async function exportReferralData(userId, dateRange = '30d') {
  try {
    const analyticsResult = await getCampaignAnalytics(userId, dateRange)

    if (!analyticsResult.success) {
      return analyticsResult
    }

    const analytics = analyticsResult.raw_analytics || []

    // Create CSV headers
    const headers = [
      'Campaign Name',
      'Campaign Source',
      'Date',
      'Clicks',
      'Registrations',
      'Conversions',
      'Conversion Rate (%)',
      'Revenue ($)'
    ]

    // Create CSV rows
    const rows = analytics.map(item => [
      item.campaign_name || '',
      item.campaign_source || '',
      item.date_tracked || '',
      item.clicks || 0,
      item.registrations || 0,
      item.conversions || 0,
      item.conversion_rate || 0,
      (item.total_revenue || 0).toFixed(2)
    ])

    // Combine headers and rows
    const csvContent = [headers, ...rows]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n')

    return {
      success: true,
      data: csvContent,
      filename: `referral_analytics_${userId}_${dateRange}_${new Date().toISOString().split('T')[0]}.csv`
    }

  } catch (error) {
    console.error('❌ Error in exportReferralData:', error)
    return { success: false, error }
  }
}

/**
 * Track referral code usage when a link is clicked
 * @param {string} referralCode - The referral code used
 * @param {string} campaignSource - The campaign source
 * @param {Object} metadata - Additional tracking data
 * @returns {Promise<Object>} - The tracking result
 */
export async function trackReferralCodeUsage(referralCode, campaignSource = null, metadata = {}) {
  try {
    console.log('📊 Tracking referral code usage:', { referralCode, campaignSource })

    // Insert usage record
    const { data: usageData, error: usageError } = await supabase
      .from('referral_code_usage')
      .insert({
        referral_code: referralCode,
        campaign_source: campaignSource,
        ip_address: metadata.ipAddress || null,
        user_agent: metadata.userAgent || null,
        referer_url: metadata.refererUrl || null,
        used_at: new Date().toISOString()
      })
      .select()
      .single()

    if (usageError) {
      console.error('❌ Error tracking referral code usage:', usageError)
      return { success: false, error: usageError }
    }

    console.log('✅ Referral code usage tracked successfully')
    return { success: true, data: usageData }

  } catch (error) {
    console.error('❌ Error in trackReferralCodeUsage:', error)
    return { success: false, error }
  }
}
