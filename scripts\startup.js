#!/usr/bin/env node

/**
 * Startup Script for Aureus Africa
 * Ensures proper initialization and provides guided startup
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { exec, spawn } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function printHeader() {
  console.log(colorize('🚀 Aureus Africa - Development Startup', 'cyan'));
  console.log(colorize('=' .repeat(50), 'blue'));
}

function printSection(title) {
  console.log('\n' + colorize(title, 'yellow'));
  console.log(colorize('-'.repeat(title.length), 'yellow'));
}

async function checkPrerequisites() {
  printSection('📋 Checking Prerequisites');
  
  const checks = [
    {
      name: 'Node.js',
      command: 'node --version',
      minVersion: '18.0.0'
    },
    {
      name: 'npm',
      command: 'npm --version',
      minVersion: '8.0.0'
    }
  ];

  let allPassed = true;

  for (const check of checks) {
    try {
      const { stdout } = await execAsync(check.command);
      const version = stdout.trim();
      console.log(colorize(`✅ ${check.name}: ${version}`, 'green'));
    } catch (error) {
      console.log(colorize(`❌ ${check.name}: Not found`, 'red'));
      allPassed = false;
    }
  }

  return allPassed;
}

function checkProjectStructure() {
  printSection('📁 Checking Project Structure');
  
  const requiredFiles = [
    { path: 'package.json', description: 'Package configuration' },
    { path: '.env', description: 'Environment variables' },
    { path: 'vite.config.ts', description: 'Vite configuration' },
    { path: 'index.html', description: 'HTML entry point' },
    { path: 'App.tsx', description: 'React application root' }
  ];

  const requiredDirs = [
    { path: 'components', description: 'React components' },
    { path: 'lib', description: 'Utility libraries' },
    { path: 'api', description: 'API routes' },
    { path: 'node_modules', description: 'Dependencies' }
  ];

  let allPresent = true;

  // Check files
  requiredFiles.forEach(({ path: filePath, description }) => {
    const fullPath = path.join(__dirname, '..', filePath);
    if (fs.existsSync(fullPath)) {
      console.log(colorize(`✅ ${description}: ${filePath}`, 'green'));
    } else {
      console.log(colorize(`❌ ${description}: ${filePath} - Missing`, 'red'));
      allPresent = false;
    }
  });

  // Check directories
  requiredDirs.forEach(({ path: dirPath, description }) => {
    const fullPath = path.join(__dirname, '..', dirPath);
    if (fs.existsSync(fullPath) && fs.statSync(fullPath).isDirectory()) {
      console.log(colorize(`✅ ${description}: ${dirPath}/`, 'green'));
    } else {
      console.log(colorize(`❌ ${description}: ${dirPath}/ - Missing`, 'red'));
      allPresent = false;
    }
  });

  return allPresent;
}

async function checkDependencies() {
  printSection('📦 Checking Dependencies');
  
  const packageJsonPath = path.join(__dirname, '..', 'package.json');
  
  if (!fs.existsSync(packageJsonPath)) {
    console.log(colorize('❌ package.json not found', 'red'));
    return false;
  }

  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const hasNodeModules = fs.existsSync(path.join(__dirname, '..', 'node_modules'));
    
    if (hasNodeModules) {
      console.log(colorize('✅ Dependencies installed', 'green'));
      return true;
    } else {
      console.log(colorize('⚠️  Dependencies not installed', 'yellow'));
      console.log('Installing dependencies...');
      
      await execAsync('npm install', { cwd: path.dirname(__dirname) });
      console.log(colorize('✅ Dependencies installed successfully', 'green'));
      return true;
    }
  } catch (error) {
    console.log(colorize(`❌ Dependency check failed: ${error.message}`, 'red'));
    return false;
  }
}

function checkEnvironmentVariables() {
  printSection('🔧 Checking Environment Variables');
  
  const envPath = path.join(__dirname, '..', '.env');
  
  if (!fs.existsSync(envPath)) {
    console.log(colorize('❌ .env file not found', 'red'));
    console.log(colorize('Please create a .env file with required variables', 'yellow'));
    return false;
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const requiredVars = [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY',
    'VITE_RESEND_API_KEY',
    'VITE_RESEND_FROM_EMAIL'
  ];

  let allPresent = true;
  
  requiredVars.forEach(varName => {
    if (envContent.includes(`${varName}=`)) {
      console.log(colorize(`✅ ${varName}`, 'green'));
    } else {
      console.log(colorize(`❌ ${varName} - Missing`, 'red'));
      allPresent = false;
    }
  });

  return allPresent;
}

async function checkPorts() {
  printSection('🔌 Checking Port Availability');
  
  const net = await import('net');
  
  function checkPort(port) {
    return new Promise((resolve) => {
      const server = net.createServer();
      
      server.listen(port, () => {
        server.once('close', () => resolve(true));
        server.close();
      });
      
      server.on('error', () => resolve(false));
    });
  }

  const ports = [
    { port: 8000, service: 'Frontend (Vite)' },
    { port: 8002, service: 'Backend (Express)' }
  ];

  for (const { port, service } of ports) {
    const available = await checkPort(port);
    if (available) {
      console.log(colorize(`✅ Port ${port} (${service}): Available`, 'green'));
    } else {
      console.log(colorize(`⚠️  Port ${port} (${service}): In use`, 'yellow'));
    }
  }

  return true;
}

function printStartupOptions() {
  printSection('🚀 Startup Options');
  
  console.log(colorize('Choose how to start the application:', 'cyan'));
  console.log('');
  console.log(colorize('1. Full Development (Recommended)', 'green'));
  console.log('   npm run dev:full');
  console.log('   Starts both frontend and backend services');
  console.log('');
  console.log(colorize('2. Frontend Only', 'blue'));
  console.log('   npm run dev');
  console.log('   Starts Vite development server (port 8000)');
  console.log('');
  console.log(colorize('3. Backend Only', 'blue'));
  console.log('   npm run server');
  console.log('   Starts Express API server (port 8002)');
  console.log('');
  console.log(colorize('4. Production Build', 'magenta'));
  console.log('   npm run build');
  console.log('   Creates optimized production build');
  console.log('');
}

function printPostStartupInfo() {
  printSection('📚 Development Information');
  
  console.log(colorize('Service URLs:', 'cyan'));
  console.log('• Frontend: http://localhost:8000');
  console.log('• Backend:  http://localhost:8002');
  console.log('• Health:   http://localhost:8002/health');
  console.log('');
  
  console.log(colorize('Important Notes:', 'yellow'));
  console.log('• Both services must run for full functionality');
  console.log('• APIs will not work with frontend only');
  console.log('• Use Ctrl+C to stop services');
  console.log('');
  
  console.log(colorize('Useful Commands:', 'cyan'));
  console.log('• npm run health-check  - System diagnostics');
  console.log('• npm run build         - Production build');
  console.log('• npm run validate:all  - Code validation');
  console.log('');
}

async function runStartupChecks() {
  printHeader();
  
  const checks = [
    { name: 'Prerequisites', fn: checkPrerequisites },
    { name: 'Project Structure', fn: () => Promise.resolve(checkProjectStructure()) },
    { name: 'Dependencies', fn: checkDependencies },
    { name: 'Environment Variables', fn: () => Promise.resolve(checkEnvironmentVariables()) },
    { name: 'Port Availability', fn: checkPorts }
  ];

  let allPassed = true;
  
  for (const check of checks) {
    try {
      const result = await check.fn();
      if (!result) {
        allPassed = false;
      }
    } catch (error) {
      console.log(colorize(`❌ ${check.name}: ${error.message}`, 'red'));
      allPassed = false;
    }
  }

  console.log('\n' + colorize('='.repeat(50), 'blue'));
  
  if (allPassed) {
    console.log(colorize('✅ All startup checks passed!', 'green'));
    printStartupOptions();
    printPostStartupInfo();
    console.log(colorize('🎉 Ready to start development!', 'green'));
  } else {
    console.log(colorize('❌ Some startup checks failed!', 'red'));
    console.log(colorize('Please resolve the issues above before starting development.', 'yellow'));
    process.exit(1);
  }
}

// Run startup checks
if (import.meta.url === `file://${process.argv[1]}`) {
  runStartupChecks().catch(error => {
    console.error(colorize(`Fatal error: ${error.message}`, 'red'));
    process.exit(1);
  });
}

export { runStartupChecks };
