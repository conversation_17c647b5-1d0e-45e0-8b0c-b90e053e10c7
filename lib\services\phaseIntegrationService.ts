import { supabase, getServiceRoleClient } from '../supabase';
import { competitionService } from './competitionService';

export interface InvestmentPhase {
  id: number;
  phase_number: number;
  phase_name: string;
  price_per_share: number;
  total_shares_available: number;
  shares_sold: number;
  is_active: boolean;
  start_date: string;
  end_date?: string;
  description?: string;
}

class PhaseIntegrationService {
  private supabase = getServiceRoleClient();

  /**
   * Get all investment phases
   */
  async getAllPhases(): Promise<InvestmentPhase[]> {
    try {
      const { data, error } = await this.supabase
        .from('investment_phases')
        .select('*')
        .order('phase_number', { ascending: true });

      if (error) {
        console.error('Error fetching investment phases:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getAllPhases:', error);
      return [];
    }
  }

  /**
   * Get the currently active phase
   */
  async getActivePhase(): Promise<InvestmentPhase | null> {
    try {
      const { data, error } = await this.supabase
        .from('investment_phases')
        .select('*')
        .eq('is_active', true)
        .order('phase_number', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching active phase:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in getActivePhase:', error);
      return null;
    }
  }

  /**
   * Create a competition for a specific phase
   */
  async createCompetitionForPhase(
    phase: InvestmentPhase,
    customPrizePool?: number,
    customMinQualification?: number
  ): Promise<string | null> {
    try {
      // Check if competition already exists for this phase
      const { data: existingCompetition, error: checkError } = await this.supabase
        .from('competitions')
        .select('id')
        .eq('phase_id', phase.id)
        .eq('is_active', true)
        .limit(1)
        .single();

      if (!checkError && existingCompetition) {
        console.log(`Competition already exists for ${phase.phase_name}`);
        return existingCompetition.id;
      }

      // Create new competition
      const competitionName = `Gold Diggers Club - ${phase.phase_name}`;
      const description = `Network leader competition for ${phase.phase_name}. Compete based on referral sales volume to win cash prizes!`;
      const prizePool = customPrizePool || this.calculatePrizePoolForPhase(phase);
      const minQualification = customMinQualification || 2500;

      const competitionId = await competitionService.createCompetition(
        competitionName,
        description,
        phase.id,
        prizePool,
        minQualification
      );

      if (competitionId) {
        // Set up default prize tiers
        await competitionService.setupDefaultPrizeTiers(competitionId);
        console.log(`✅ Created competition for ${phase.phase_name}: ${competitionId}`);
        return competitionId;
      }

      return null;
    } catch (error) {
      console.error('Error creating competition for phase:', error);
      return null;
    }
  }

  /**
   * Calculate appropriate prize pool based on phase characteristics
   */
  private calculatePrizePoolForPhase(phase: InvestmentPhase): number {
    // Base prize pool
    let prizePool = 210000;

    // Adjust based on phase number (later phases might have higher prizes)
    if (phase.phase_number <= 5) {
      prizePool = 210000; // Early phases
    } else if (phase.phase_number <= 10) {
      prizePool = 300000; // Mid phases
    } else {
      prizePool = 500000; // Later phases
    }

    // Adjust based on share price (higher price phases get higher prizes)
    if (phase.price_per_share >= 2.0) {
      prizePool *= 1.5;
    } else if (phase.price_per_share >= 1.0) {
      prizePool *= 1.2;
    }

    return Math.round(prizePool);
  }

  /**
   * Handle phase transition - end current competition and create new one
   */
  async handlePhaseTransition(oldPhaseId: number, newPhaseId: number): Promise<boolean> {
    try {
      console.log(`🔄 Handling phase transition: ${oldPhaseId} → ${newPhaseId}`);

      // End competitions for old phase
      const { error: endError } = await this.supabase
        .from('competitions')
        .update({ 
          status: 'ended',
          is_active: false,
          end_date: new Date().toISOString()
        })
        .eq('phase_id', oldPhaseId)
        .eq('is_active', true);

      if (endError) {
        console.error('Error ending old phase competitions:', endError);
      } else {
        console.log(`✅ Ended competitions for phase ${oldPhaseId}`);
      }

      // Get new phase details
      const { data: newPhase, error: phaseError } = await this.supabase
        .from('investment_phases')
        .select('*')
        .eq('id', newPhaseId)
        .single();

      if (phaseError || !newPhase) {
        console.error('Error fetching new phase:', phaseError);
        return false;
      }

      // Create competition for new phase
      const competitionId = await this.createCompetitionForPhase(newPhase);
      
      if (competitionId) {
        console.log(`✅ Phase transition completed successfully`);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error handling phase transition:', error);
      return false;
    }
  }

  /**
   * Ensure all phases have competitions
   */
  async ensureAllPhasesHaveCompetitions(): Promise<void> {
    try {
      console.log('🔍 Checking all phases for competitions...');

      const phases = await this.getAllPhases();
      
      for (const phase of phases) {
        // Check if phase has an active competition
        const { data: competition, error } = await this.supabase
          .from('competitions')
          .select('id')
          .eq('phase_id', phase.id)
          .eq('is_active', true)
          .limit(1)
          .single();

        if (error && error.code === 'PGRST116') {
          // No competition exists for this phase
          console.log(`📝 Creating competition for ${phase.phase_name}...`);
          await this.createCompetitionForPhase(phase);
        } else if (competition) {
          console.log(`✅ ${phase.phase_name} already has competition: ${competition.id}`);
        }
      }

      console.log('✅ All phases checked for competitions');
    } catch (error) {
      console.error('Error ensuring phases have competitions:', error);
    }
  }

  /**
   * Get competition for a specific phase
   */
  async getCompetitionForPhase(phaseId: number): Promise<any> {
    try {
      const { data, error } = await this.supabase
        .from('competitions')
        .select(`
          *,
          competition_prize_tiers (*),
          investment_phases (*)
        `)
        .eq('phase_id', phaseId)
        .eq('is_active', true)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching competition for phase:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in getCompetitionForPhase:', error);
      return null;
    }
  }

  /**
   * Monitor phase changes and automatically handle competitions
   */
  async startPhaseMonitoring(): Promise<void> {
    console.log('🔍 Starting phase monitoring for automatic competition management...');

    // Set up real-time subscription to phase changes
    const subscription = this.supabase
      .channel('phase_changes')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'investment_phases',
          filter: 'is_active=eq.true'
        },
        async (payload) => {
          console.log('📊 Phase change detected:', payload);
          
          // Handle new active phase
          if (payload.new && payload.new.is_active) {
            const newPhase = payload.new as InvestmentPhase;
            await this.createCompetitionForPhase(newPhase);
          }
        }
      )
      .subscribe();

    console.log('✅ Phase monitoring started');
  }
}

export const phaseIntegrationService = new PhaseIntegrationService();
