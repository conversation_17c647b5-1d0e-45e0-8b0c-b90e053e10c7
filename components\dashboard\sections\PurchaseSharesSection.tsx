import React from 'react';
import { SharePurchaseForm } from '../../SharePurchaseForm';

interface PurchaseSharesSectionProps {
  user: any;
  onRefreshData: () => Promise<void>;
}

export const PurchaseSharesSection: React.FC<PurchaseSharesSectionProps> = ({
  user,
  onRefreshData
}) => {
  return (
    <div className="space-y-6">
      {/* Section Header */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h2 className="text-2xl font-bold text-white mb-2">Purchase Shares</h2>
        <p className="text-gray-400">
          Invest in Aureus Alliance Holdings and become part of our gold-backed impact ventures across Africa.
        </p>
      </div>

      {/* Share Purchase Form */}
      <SharePurchaseForm
        userId={user?.database_user?.id || user?.id}
        onPurchaseComplete={onRefreshData}
      />
    </div>
  );
};
