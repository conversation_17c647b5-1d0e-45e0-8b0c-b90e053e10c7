#!/usr/bin/env node

/**
 * Test User Authentication Flow
 * 
 * This script tests the user authentication and database connection
 * to identify why the dashboard is not loading data.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseAnonKey) {
  console.error('❌ Missing VITE_SUPABASE_ANON_KEY in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testUserAuth() {
  console.log('🔍 Testing User Authentication Flow\n');

  try {
    // Test 1: Check Supabase connection
    console.log('1. Testing Supabase connection...');
    const { data: testData, error: testError } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (testError) {
      console.error('❌ Supabase connection failed:', testError);
      return;
    } else {
      console.log('✅ Supabase connection successful');
    }

    // Test 2: Check if user ID 4 exists
    console.log('\n2. Checking user ID 4...');
    const { data: user4, error: user4Error } = await supabase
      .from('users')
      .select('*')
      .eq('id', 4)
      .single();

    if (user4Error) {
      console.error('❌ Error fetching user ID 4:', user4Error);
    } else {
      console.log('✅ User ID 4 found:', {
        id: user4.id,
        email: user4.email,
        username: user4.username,
        first_name: user4.first_name,
        is_active: user4.is_active
      });
    }

    // Test 3: Check commission balance for user 4
    console.log('\n3. Checking commission balance for user 4...');
    const { data: commission4, error: commissionError } = await supabase
      .from('commission_balances')
      .select('*')
      .eq('user_id', 4)
      .single();

    if (commissionError) {
      console.error('❌ Error fetching commission balance:', commissionError);
    } else {
      console.log('✅ Commission balance found:', {
        user_id: commission4.user_id,
        usdt_balance: commission4.usdt_balance,
        share_balance: commission4.share_balance,
        total_earned_usdt: commission4.total_earned_usdt,
        total_earned_shares: commission4.total_earned_shares,
        escrowed_amount: commission4.escrowed_amount
      });
    }

    // Test 4: Check share purchases for user 4
    console.log('\n4. Checking share purchases for user 4...');
    const { data: shares4, error: sharesError } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .eq('user_id', 4)
      .eq('status', 'active');

    if (sharesError) {
      console.error('❌ Error fetching share purchases:', sharesError);
    } else {
      console.log(`✅ Found ${shares4.length} active share purchases`);
      if (shares4.length > 0) {
        const totalShares = shares4.reduce((sum, purchase) => sum + purchase.shares_purchased, 0);
        const totalValue = shares4.reduce((sum, purchase) => sum + purchase.total_amount, 0);
        console.log(`   Total shares: ${totalShares}`);
        console.log(`   Total value: $${totalValue}`);
      }
    }

    // Test 5: Check referrals for user 4
    console.log('\n5. Checking referrals for user 4...');
    const { data: referrals4, error: referralsError } = await supabase
      .from('referrals')
      .select('id')
      .eq('referrer_id', 4);

    if (referralsError) {
      console.error('❌ Error fetching referrals:', referralsError);
    } else {
      console.log(`✅ Found ${referrals4.length} referrals`);
    }

    // Test 6: Check current investment phase
    console.log('\n6. Checking current investment phase...');
    const { data: currentPhase, error: phaseError } = await supabase
      .from('investment_phases')
      .select('*')
      .eq('is_active', true)
      .single();

    if (phaseError) {
      console.error('❌ Error fetching current phase:', phaseError);
    } else {
      console.log('✅ Current phase found:', {
        phase_name: currentPhase.phase_name,
        price_per_share: currentPhase.price_per_share,
        total_shares: currentPhase.total_shares,
        shares_sold: currentPhase.shares_sold
      });
    }

    console.log('\n🎯 SUMMARY:');
    console.log('');
    
    if (user4 && commission4) {
      console.log('✅ All required data exists for user 4');
      console.log('✅ Commission calculation should work');
      console.log('');
      console.log('📊 Expected Dashboard Values:');
      console.log(`• USDT Commissions: $${commission4.total_earned_usdt}`);
      console.log(`• Share Commissions: ${commission4.total_earned_shares} shares`);
      console.log(`• Available USDT: $${commission4.usdt_balance}`);
      console.log(`• Available Shares: ${commission4.share_balance} shares`);
      console.log(`• Escrowed Amount: $${commission4.escrowed_amount}`);
      
      if (shares4 && shares4.length > 0) {
        const totalShares = shares4.reduce((sum, purchase) => sum + purchase.shares_purchased, 0);
        const totalValue = shares4.reduce((sum, purchase) => sum + purchase.total_amount, 0);
        console.log(`• Owned Shares: ${totalShares} shares`);
        console.log(`• Share Value: $${totalValue}`);
      }
      
      console.log(`• Referrals: ${referrals4.length} users`);
      
      if (currentPhase) {
        const shareCommissionValue = commission4.total_earned_shares * currentPhase.price_per_share;
        console.log(`• Share Commission Value: $${shareCommissionValue.toFixed(2)}`);
        console.log(`• Total Commission Value: $${(commission4.total_earned_usdt + shareCommissionValue).toFixed(2)}`);
      }
    } else {
      console.log('❌ Missing required data - this explains why dashboard is not loading');
    }

    console.log('\n🔧 NEXT STEPS:');
    console.log('1. Check browser console for user authentication details');
    console.log('2. Verify getCurrentUser() is returning user with database_user.id = 4');
    console.log('3. If user auth is working, the dashboard should now display data correctly');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testUserAuth();
