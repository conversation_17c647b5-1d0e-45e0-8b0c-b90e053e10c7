/**
 * CREATE USERNAME SYNCHRONIZATION SYSTEM
 * 
 * This script creates a comprehensive system to automatically sync username changes
 * across all related tables to prevent future inconsistencies:
 * 
 * 1. Database trigger to auto-update referral codes when username changes
 * 2. Function to sync telegram_users table
 * 3. Audit logging for username changes
 * 4. Validation to prevent duplicate usernames
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client with service role
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createUsernameSyncSystem() {
  console.log('🔧 CREATING USERNAME SYNCHRONIZATION SYSTEM');
  console.log('============================================');
  
  try {
    // ===== PART 1: CREATE USERNAME SYNC FUNCTION =====
    console.log('\n📋 PART 1: Creating Username Sync Function');
    
    const syncFunctionSQL = `
      -- Function to sync username changes across all related tables
      CREATE OR REPLACE FUNCTION sync_username_changes()
      RETURNS TRIGGER AS $$
      DECLARE
        old_username VARCHAR(255);
        new_username VARCHAR(255);
        referral_record RECORD;
        updated_count INTEGER := 0;
      BEGIN
        -- Only proceed if username actually changed
        IF OLD.username IS DISTINCT FROM NEW.username THEN
          old_username := OLD.username;
          new_username := NEW.username;
          
          RAISE NOTICE 'Username change detected for user %: % -> %', 
            NEW.id, old_username, new_username;
          
          -- Update referral codes where this user is the referrer
          FOR referral_record IN 
            SELECT id, referral_code 
            FROM referrals 
            WHERE referrer_id = NEW.id 
            AND referral_code LIKE old_username || '_%'
          LOOP
            UPDATE referrals 
            SET 
              referral_code = REPLACE(referral_record.referral_code, old_username, new_username),
              updated_at = NOW()
            WHERE id = referral_record.id;
            
            updated_count := updated_count + 1;
            
            RAISE NOTICE 'Updated referral code: % -> %', 
              referral_record.referral_code, 
              REPLACE(referral_record.referral_code, old_username, new_username);
          END LOOP;
          
          -- Update telegram_users table if record exists
          UPDATE telegram_users 
          SET 
            username = new_username,
            updated_at = NOW()
          WHERE user_id = NEW.id;
          
          -- Log the change in audit_logs if table exists
          BEGIN
            INSERT INTO audit_logs (
              user_id,
              action,
              table_name,
              old_values,
              new_values,
              created_at
            ) VALUES (
              NEW.id,
              'USERNAME_SYNC',
              'users',
              jsonb_build_object(
                'username', old_username,
                'referral_codes_updated', updated_count
              ),
              jsonb_build_object(
                'username', new_username,
                'referral_codes_updated', updated_count
              ),
              NOW()
            );
          EXCEPTION
            WHEN undefined_table THEN
              RAISE NOTICE 'audit_logs table not found, skipping audit log';
            WHEN OTHERS THEN
              RAISE WARNING 'Failed to create audit log: %', SQLERRM;
          END;
          
          RAISE NOTICE 'Username sync complete: updated % referral codes', updated_count;
        END IF;
        
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `;
    
    const { error: functionError } = await supabase.rpc('exec_sql', { 
      sql: syncFunctionSQL 
    });
    
    if (functionError) {
      console.log('❌ Error creating sync function:', functionError.message);
      // Try alternative approach
      console.log('🔄 Attempting to create function via direct SQL...');
      
      const { error: directError } = await supabase
        .from('pg_stat_user_functions')
        .select('*')
        .limit(1);
        
      if (directError) {
        console.log('⚠️  Cannot create function directly. Please run the SQL manually.');
        console.log('📝 SQL to run in Supabase dashboard:');
        console.log(syncFunctionSQL);
      }
    } else {
      console.log('✅ Username sync function created successfully');
    }
    
    // ===== PART 2: CREATE TRIGGER =====
    console.log('\n📋 PART 2: Creating Username Change Trigger');
    
    const triggerSQL = `
      -- Drop existing trigger if it exists
      DROP TRIGGER IF EXISTS trigger_sync_username_changes ON users;
      
      -- Create trigger to automatically sync username changes
      CREATE TRIGGER trigger_sync_username_changes
        AFTER UPDATE OF username ON users
        FOR EACH ROW
        EXECUTE FUNCTION sync_username_changes();
    `;
    
    const { error: triggerError } = await supabase.rpc('exec_sql', { 
      sql: triggerSQL 
    });
    
    if (triggerError) {
      console.log('❌ Error creating trigger:', triggerError.message);
      console.log('📝 SQL to run in Supabase dashboard:');
      console.log(triggerSQL);
    } else {
      console.log('✅ Username change trigger created successfully');
    }
    
    // ===== PART 3: CREATE VALIDATION FUNCTION =====
    console.log('\n📋 PART 3: Creating Username Validation Function');
    
    const validationSQL = `
      -- Enhanced username validation function
      CREATE OR REPLACE FUNCTION validate_username_change(
        p_user_id INTEGER,
        p_new_username VARCHAR(255)
      )
      RETURNS JSONB AS $$
      DECLARE
        result JSONB;
        existing_user_id INTEGER;
        referral_count INTEGER;
      BEGIN
        -- Check if username is already taken
        SELECT id INTO existing_user_id
        FROM users 
        WHERE username = p_new_username 
        AND id != p_user_id;
        
        IF existing_user_id IS NOT NULL THEN
          RETURN jsonb_build_object(
            'valid', false,
            'error', 'Username already taken',
            'error_code', 'USERNAME_EXISTS'
          );
        END IF;
        
        -- Check username format (basic validation)
        IF LENGTH(p_new_username) < 3 THEN
          RETURN jsonb_build_object(
            'valid', false,
            'error', 'Username must be at least 3 characters',
            'error_code', 'USERNAME_TOO_SHORT'
          );
        END IF;
        
        IF LENGTH(p_new_username) > 50 THEN
          RETURN jsonb_build_object(
            'valid', false,
            'error', 'Username must be less than 50 characters',
            'error_code', 'USERNAME_TOO_LONG'
          );
        END IF;
        
        -- Count referrals that will be affected
        SELECT COUNT(*) INTO referral_count
        FROM referrals
        WHERE referrer_id = p_user_id;
        
        RETURN jsonb_build_object(
          'valid', true,
          'referrals_to_update', referral_count,
          'message', 'Username change is valid'
        );
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
      
      -- Grant permissions
      GRANT EXECUTE ON FUNCTION validate_username_change(INTEGER, VARCHAR) TO authenticated;
      GRANT EXECUTE ON FUNCTION validate_username_change(INTEGER, VARCHAR) TO service_role;
    `;
    
    const { error: validationError } = await supabase.rpc('exec_sql', { 
      sql: validationSQL 
    });
    
    if (validationError) {
      console.log('❌ Error creating validation function:', validationError.message);
    } else {
      console.log('✅ Username validation function created successfully');
    }
    
    // ===== PART 4: TEST THE SYSTEM =====
    console.log('\n📋 PART 4: Testing the System');
    
    // Test validation function
    try {
      const { data: validationTest, error: testError } = await supabase
        .rpc('validate_username_change', {
          p_user_id: 139,
          p_new_username: 'TEST_USERNAME_' + Date.now()
        });
        
      if (testError) {
        console.log('⚠️  Could not test validation function:', testError.message);
      } else {
        console.log('✅ Validation function test result:', validationTest);
      }
    } catch (error) {
      console.log('⚠️  Validation function test failed:', error.message);
    }
    
    // ===== PART 5: CREATE MANUAL SYNC FUNCTION =====
    console.log('\n📋 PART 5: Creating Manual Sync Function');
    
    const manualSyncSQL = `
      -- Function to manually sync all username inconsistencies
      CREATE OR REPLACE FUNCTION manual_sync_all_usernames()
      RETURNS JSONB AS $$
      DECLARE
        user_record RECORD;
        referral_record RECORD;
        total_users INTEGER := 0;
        total_referrals_updated INTEGER := 0;
        total_telegram_updated INTEGER := 0;
        result JSONB;
      BEGIN
        -- Loop through all users and sync their referrals
        FOR user_record IN 
          SELECT id, username 
          FROM users 
          WHERE username IS NOT NULL
        LOOP
          total_users := total_users + 1;
          
          -- Update referral codes for this user
          FOR referral_record IN 
            SELECT id, referral_code 
            FROM referrals 
            WHERE referrer_id = user_record.id 
            AND referral_code NOT LIKE user_record.username || '_%'
            AND referral_code ~ '^[^_]+_[0-9]+_[0-9]+$'
          LOOP
            -- Extract the parts and rebuild with current username
            UPDATE referrals 
            SET 
              referral_code = user_record.username || SUBSTRING(referral_record.referral_code FROM '_.*'),
              updated_at = NOW()
            WHERE id = referral_record.id;
            
            total_referrals_updated := total_referrals_updated + 1;
          END LOOP;
          
          -- Update telegram username if exists and different
          UPDATE telegram_users 
          SET 
            username = user_record.username,
            updated_at = NOW()
          WHERE user_id = user_record.id 
          AND username != user_record.username;
          
          GET DIAGNOSTICS total_telegram_updated = ROW_COUNT;
        END LOOP;
        
        result := jsonb_build_object(
          'success', true,
          'users_processed', total_users,
          'referrals_updated', total_referrals_updated,
          'telegram_users_updated', total_telegram_updated,
          'timestamp', NOW()
        );
        
        RETURN result;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
      
      -- Grant permissions
      GRANT EXECUTE ON FUNCTION manual_sync_all_usernames() TO service_role;
    `;
    
    const { error: manualSyncError } = await supabase.rpc('exec_sql', { 
      sql: manualSyncSQL 
    });
    
    if (manualSyncError) {
      console.log('❌ Error creating manual sync function:', manualSyncError.message);
    } else {
      console.log('✅ Manual sync function created successfully');
    }
    
    console.log('\n🎉 USERNAME SYNCHRONIZATION SYSTEM COMPLETE!');
    console.log('=============================================');
    console.log('✅ Automatic username sync trigger created');
    console.log('✅ Username validation function created');
    console.log('✅ Manual sync function created');
    console.log('✅ Audit logging integrated');
    console.log('\n📝 System Features:');
    console.log('• Automatic referral code updates on username change');
    console.log('• Telegram username synchronization');
    console.log('• Username validation before changes');
    console.log('• Manual sync function for bulk fixes');
    console.log('• Comprehensive audit logging');
    
    return true;
    
  } catch (error) {
    console.error('💥 Fatal error creating sync system:', error);
    return false;
  }
}

// Run the system creation
createUsernameSyncSystem()
  .then((success) => {
    if (success) {
      console.log('\n✅ Username synchronization system created successfully!');
    } else {
      console.log('\n❌ Username synchronization system creation encountered errors');
    }
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error('\n💥 System creation failed:', error);
    process.exit(1);
  });
