<!DOCTYPE html>
<html>
<head>
    <title>Environment Debug</title>
</head>
<body>
    <h1>Environment Variables Debug</h1>
    <div id="output"></div>
    
    <script type="module">
        const output = document.getElementById('output');
        
        // Check if we're in development or production
        const mode = import.meta.env.MODE;
        const isDev = import.meta.env.DEV;
        const isProd = import.meta.env.PROD;
        
        // Check Supabase environment variables
        const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
        const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
        const supabaseServiceKey = import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY;
        
        output.innerHTML = `
            <h2>Environment Info:</h2>
            <p><strong>Mode:</strong> ${mode}</p>
            <p><strong>Is Dev:</strong> ${isDev}</p>
            <p><strong>Is Prod:</strong> ${isProd}</p>
            
            <h2>Supabase Config:</h2>
            <p><strong>URL:</strong> ${supabaseUrl || 'NOT SET'}</p>
            <p><strong>Anon Key exists:</strong> ${!!supabaseAnonKey}</p>
            <p><strong>Anon Key length:</strong> ${supabaseAnonKey?.length || 0}</p>
            <p><strong>Service Key exists:</strong> ${!!supabaseServiceKey}</p>
            <p><strong>Service Key length:</strong> ${supabaseServiceKey?.length || 0}</p>
            
            <h2>All Environment Variables:</h2>
            <pre>${JSON.stringify(import.meta.env, null, 2)}</pre>
        `;
        
        // Test Supabase connection
        if (supabaseUrl && supabaseAnonKey) {
            try {
                const { createClient } = await import('@supabase/supabase-js');
                const supabase = createClient(supabaseUrl, supabaseAnonKey);
                
                const { data, error } = await supabase.from('users').select('count').limit(1);
                
                output.innerHTML += `
                    <h2>Supabase Connection Test:</h2>
                    <p><strong>Status:</strong> ${error ? 'FAILED' : 'SUCCESS'}</p>
                    <p><strong>Error:</strong> ${error ? error.message : 'None'}</p>
                    <p><strong>Data:</strong> ${JSON.stringify(data)}</p>
                `;
            } catch (err) {
                output.innerHTML += `
                    <h2>Supabase Connection Test:</h2>
                    <p><strong>Status:</strong> FAILED</p>
                    <p><strong>Error:</strong> ${err.message}</p>
                `;
            }
        } else {
            output.innerHTML += `
                <h2>Supabase Connection Test:</h2>
                <p><strong>Status:</strong> SKIPPED (Missing credentials)</p>
            `;
        }
    </script>
</body>
</html>
