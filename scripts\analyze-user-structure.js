import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function analyzeUserStructure() {
  console.log('🔍 Analyzing user table structure and relationships...\n');

  try {
    // 1. Analyze users table by querying sample data
    console.log('📋 1. USERS TABLE ANALYSIS');
    console.log('=' .repeat(50));

    const { data: sampleUsers, error: usersError } = await supabase
      .from('users')
      .select('*')
      .limit(1);

    if (usersError) {
      console.error('❌ Error fetching users table:', usersError);
    } else if (sampleUsers && sampleUsers.length > 0) {
      console.log('✅ Users table structure (from sample data):');
      const user = sampleUsers[0];
      Object.keys(user).forEach(key => {
        const value = user[key];
        const type = typeof value;
        const sample = value !== null ? ` (sample: ${String(value).substring(0, 30)}${String(value).length > 30 ? '...' : ''})` : ' (NULL)';
        console.log(`  • ${key}: ${type}${sample}`);
      });
    } else {
      console.log('⚠️ No users found in table');
    }

    // 2. Analyze telegram_users table structure
    console.log('\n📋 2. TELEGRAM_USERS TABLE ANALYSIS');
    console.log('=' .repeat(50));

    const { data: sampleTelegram, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .limit(1);

    if (telegramError) {
      console.error('❌ Error fetching telegram_users table:', telegramError);
    } else if (sampleTelegram && sampleTelegram.length > 0) {
      console.log('✅ Telegram_users table structure (from sample data):');
      const tgUser = sampleTelegram[0];
      Object.keys(tgUser).forEach(key => {
        const value = tgUser[key];
        const type = typeof value;
        const sample = value !== null ? ` (sample: ${String(value).substring(0, 30)}${String(value).length > 30 ? '...' : ''})` : ' (NULL)';
        console.log(`  • ${key}: ${type}${sample}`);
      });
    } else {
      console.log('⚠️ No telegram users found in table');
    }

    // 3. Analyze admin_users table structure
    console.log('\n📋 3. ADMIN_USERS TABLE ANALYSIS');
    console.log('=' .repeat(50));

    const { data: sampleAdmin, error: adminError } = await supabase
      .from('admin_users')
      .select('*')
      .limit(1);

    if (adminError) {
      console.error('❌ Error fetching admin_users table:', adminError);
    } else if (sampleAdmin && sampleAdmin.length > 0) {
      console.log('✅ Admin_users table structure (from sample data):');
      const admin = sampleAdmin[0];
      Object.keys(admin).forEach(key => {
        const value = admin[key];
        const type = typeof value;
        const sample = value !== null ? ` (sample: ${String(value).substring(0, 30)}${String(value).length > 30 ? '...' : ''})` : ' (NULL)';
        console.log(`  • ${key}: ${type}${sample}`);
      });
    } else {
      console.log('⚠️ No admin users found in table');
    }

    // 4. Test relationships by querying joined data
    console.log('\n📋 4. TABLE RELATIONSHIPS TEST');
    console.log('=' .repeat(50));

    // Test users -> telegram_users relationship
    const { data: usersWithTelegram, error: relError } = await supabase
      .from('users')
      .select(`
        id,
        username,
        email,
        telegram_users(
          id,
          telegram_id,
          username,
          first_name,
          last_name
        )
      `)
      .limit(3);

    if (relError) {
      console.error('❌ Error testing user-telegram relationship:', relError);
    } else {
      console.log('✅ Users with Telegram relationships:');
      usersWithTelegram.forEach((user, index) => {
        console.log(`  ${index + 1}. User: ${user.username} (${user.email})`);
        if (user.telegram_users && user.telegram_users.length > 0) {
          user.telegram_users.forEach(tg => {
            console.log(`     → Telegram: @${tg.username} (${tg.first_name} ${tg.last_name})`);
          });
        } else {
          console.log('     → No Telegram account linked');
        }
      });
    }

    // 5. Data counts and statistics
    console.log('\n📋 5. DATA STATISTICS');
    console.log('=' .repeat(50));

    // Users count
    const { count: usersCount, error: countError } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true });

    if (!countError) {
      console.log(`✅ Total users: ${usersCount || 0}`);
    }

    // Telegram users count
    const { count: telegramCount, error: telegramCountError } = await supabase
      .from('telegram_users')
      .select('*', { count: 'exact', head: true });

    if (!telegramCountError) {
      console.log(`✅ Total telegram users: ${telegramCount || 0}`);
    }

    // Admin users count
    const { count: adminCount, error: adminCountError } = await supabase
      .from('admin_users')
      .select('*', { count: 'exact', head: true });

    if (!adminCountError) {
      console.log(`✅ Total admin users: ${adminCount || 0}`);
    }

    // Sample detailed user data
    const { data: detailedUsers, error: detailedError } = await supabase
      .from('users')
      .select('id, username, email, full_name, phone, is_active, created_at')
      .limit(3);

    if (!detailedError && detailedUsers && detailedUsers.length > 0) {
      console.log('\n📊 Sample user details:');
      detailedUsers.forEach((user, index) => {
        console.log(`  ${index + 1}. ID: ${user.id}`);
        console.log(`     Username: ${user.username}`);
        console.log(`     Email: ${user.email}`);
        console.log(`     Full Name: ${user.full_name || 'Not set'}`);
        console.log(`     Phone: ${user.phone || 'Not set'}`);
        console.log(`     Active: ${user.is_active}`);
        console.log(`     Created: ${new Date(user.created_at).toLocaleDateString()}`);
        console.log('');
      });
    }

    console.log('\n🎯 ANALYSIS SUMMARY');
    console.log('=' .repeat(50));
    console.log('✅ Database structure analysis complete!');
    console.log('📋 Key findings:');
    console.log('  • Users table contains main user accounts');
    console.log('  • Telegram_users table links Telegram accounts to users');
    console.log('  • Admin_users table manages admin permissions');
    console.log('  • Foreign key relationships maintain data integrity');
    console.log('\n🚀 Ready to build user management interface!');

  } catch (error) {
    console.error('❌ Analysis failed:', error);
  }
}

analyzeUserStructure();
