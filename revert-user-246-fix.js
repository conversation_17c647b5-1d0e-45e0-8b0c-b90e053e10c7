import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const revertUser246Fix = async () => {
  try {
    console.log('🚨 EMERGENCY: REVERTING USER ID 246 PASSWORD CHANGE');
    console.log('==================================================');
    console.log('❌ MISTAKE: Replaced telegram_auth with bcrypt hash');
    console.log('✅ FIXING: Restoring telegram_auth for Telegram login');
    
    // Step 1: Check current state
    console.log('\n📋 Step 1: Checking current password hash...');
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, email, username, password_hash, migration_status')
      .eq('id', 246)
      .single();

    if (userError || !user) {
      console.log('❌ Error fetching user:', userError?.message);
      return;
    }

    console.log('✅ Current user data:');
    console.log('   ID:', user.id);
    console.log('   Email:', user.email);
    console.log('   Username:', user.username);
    console.log('   Current password_hash:', user.password_hash);
    console.log('   Migration status:', user.migration_status);

    // Step 2: Restore telegram_auth
    console.log('\n📋 Step 2: Restoring telegram_auth...');
    
    const { error: updateError } = await supabase
      .from('users')
      .update({
        password_hash: 'telegram_auth',
        updated_at: new Date().toISOString(),
        web_credentials_set_at: null // Clear this since it's not a web password
      })
      .eq('id', 246);

    if (updateError) {
      console.log('❌ Error restoring telegram_auth:', updateError.message);
      return;
    }

    console.log('✅ telegram_auth restored successfully');

    // Step 3: Verify the revert
    console.log('\n📋 Step 3: Verifying the revert...');
    
    const { data: revertedUser, error: verifyError } = await supabase
      .from('users')
      .select('id, email, password_hash, web_credentials_set_at')
      .eq('id', 246)
      .single();

    if (verifyError || !revertedUser) {
      console.log('❌ Error verifying revert:', verifyError?.message);
      return;
    }

    console.log('✅ Revert verification successful:');
    console.log('   Password hash:', revertedUser.password_hash);
    console.log('   Web credentials set at:', revertedUser.web_credentials_set_at);

    if (revertedUser.password_hash === 'telegram_auth') {
      console.log('\n🎉 SUCCESS! User ID 246 telegram_auth restored');
      console.log('==============================================');
      console.log('📧 Email:', user.email);
      console.log('👤 Username:', user.username);
      console.log('🔑 Password Hash: telegram_auth (CORRECT for Telegram users)');
      console.log('');
      console.log('📋 TELEGRAM LOGIN FLOW:');
      console.log('1. User uses Telegram bot authentication');
      console.log('2. After successful Telegram auth, they can set web password');
      console.log('3. telegram_auth allows Telegram login to work properly');
      console.log('4. User should NOT try to login with email/password until they set one');
    } else {
      console.log('\n❌ FAILED: Revert unsuccessful');
    }

  } catch (error) {
    console.error('❌ Revert script error:', error);
  }
};

revertUser246Fix();
