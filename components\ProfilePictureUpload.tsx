import React, { useState, useRef } from 'react';
import { supabase, getServiceRoleClient } from '../lib/supabase';

interface ProfilePictureUploadProps {
  currentImageUrl?: string;
  userId: number;
  onImageUpdate: (newImageUrl: string) => void;
  size?: 'small' | 'medium' | 'large';
  editable?: boolean;
  userInitials?: string;
  userName?: string;
  showSaveButton?: boolean;
  autoSave?: boolean;
}

export const ProfilePictureUpload: React.FC<ProfilePictureUploadProps> = ({
  currentImageUrl,
  userId,
  onImageUpdate,
  size = 'medium',
  editable = true,
  userInitials = 'U',
  userName = 'User',
  showSaveButton = true,
  autoSave = false
}) => {
  const [uploading, setUploading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [pendingImageUrl, setPendingImageUrl] = useState<string | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const sizeClasses = {
    small: { container: '48px', text: '18px' },
    medium: { container: '80px', text: '28px' },
    large: { container: '120px', text: '40px' }
  };

  const currentSize = sizeClasses[size];

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('Please select an image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('Image must be smaller than 5MB');
      return;
    }

    setError(null);
    setSelectedFile(file);

    // Create preview URL
    const previewUrl = URL.createObjectURL(file);
    setPendingImageUrl(previewUrl);
    setHasUnsavedChanges(true);

    // If autoSave is enabled, save immediately
    if (autoSave) {
      await handleSave();
    }
  };

  const handleSave = async () => {
    if (!selectedFile) return;

    setSaving(true);
    setError(null);

    try {
      // Create unique filename
      const fileExt = selectedFile.name.split('.').pop();
      const fileName = `profile-${userId}-${Date.now()}.${fileExt}`;

      // Upload to Supabase Storage using service role client to bypass RLS (same as proof of payment)
      const serviceClient = getServiceRoleClient();
      const { data: uploadData, error: uploadError } = await serviceClient.storage
        .from('profile-pictures')
        .upload(fileName, selectedFile, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        throw new Error(`Upload failed: ${uploadError.message}`);
      }

      // Get public URL
      const { data: { publicUrl } } = serviceClient.storage
        .from('profile-pictures')
        .getPublicUrl(uploadData.path);

      // Update user record in database using service role client to bypass RLS
      const { error: updateError } = await serviceClient
        .from('users')
        .update({
          profile_image_url: publicUrl,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (updateError) {
        throw new Error(`Database update failed: ${updateError.message}`);
      }

      console.log('✅ Profile picture updated in database:', publicUrl);

      // Delete old image if it exists
      if (currentImageUrl && currentImageUrl.includes('profile-pictures')) {
        const oldPath = currentImageUrl.split('/profile-pictures/')[1];
        if (oldPath) {
          await serviceClient.storage
            .from('profile-pictures')
            .remove([oldPath]);
        }
      }

      // Update localStorage with new profile picture URL and timestamp
      const currentTimestamp = new Date().toISOString();
      const storedUser = localStorage.getItem('aureus_user');
      if (storedUser) {
        const userData = JSON.parse(storedUser);
        userData.profile_image_url = publicUrl;
        userData.updated_at = currentTimestamp;
        localStorage.setItem('aureus_user', JSON.stringify(userData));
        console.log('✅ Profile picture URL updated in localStorage with timestamp');
      }

      const storedTelegramUser = localStorage.getItem('aureus_telegram_user');
      if (storedTelegramUser) {
        const telegramUserData = JSON.parse(storedTelegramUser);
        if (telegramUserData.database_user) {
          telegramUserData.database_user.profile_image_url = publicUrl;
          telegramUserData.database_user.updated_at = currentTimestamp;
        }
        localStorage.setItem('aureus_telegram_user', JSON.stringify(telegramUserData));
        console.log('✅ Profile picture URL updated in Telegram user localStorage with timestamp');
      }

      onImageUpdate(publicUrl);

      // Clear selected file and pending state
      setSelectedFile(null);
      setPendingImageUrl(null);
      setHasUnsavedChanges(false);
      setSuccess('Profile picture updated successfully!');

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);

    } catch (err: any) {
      console.error('Profile picture upload error:', err);
      setError(err.message || 'Failed to upload image');
    } finally {
      setSaving(false);
    }
  };

  const handleClick = () => {
    if (editable && !uploading) {
      fileInputRef.current?.click();
    }
  };



  return (
    <div style={{ position: 'relative', display: 'inline-block' }}>
      {/* Profile Picture Container */}
      <div
        onClick={handleClick}
        style={{
          width: currentSize.container,
          height: currentSize.container,
          borderRadius: '50%',
          overflow: 'hidden',
          cursor: editable ? 'pointer' : 'default',
          position: 'relative',
          background: currentImageUrl 
            ? 'transparent' 
            : 'linear-gradient(135deg, #FFD700 0%, #E6C200 100%)',
          border: '3px solid rgba(255, 215, 0, 0.3)',
          boxShadow: '0 0 18px rgba(255, 215, 0, 0.3)',
          transition: 'all 0.2s ease'
        }}
        onMouseEnter={(e) => {
          if (editable) {
            e.currentTarget.style.transform = 'scale(1.05)';
            e.currentTarget.style.boxShadow = '0 0 24px rgba(255, 215, 0, 0.5)';
          }
        }}
        onMouseLeave={(e) => {
          if (editable) {
            e.currentTarget.style.transform = 'scale(1)';
            e.currentTarget.style.boxShadow = '0 0 18px rgba(255, 215, 0, 0.3)';
          }
        }}
      >
        {(pendingImageUrl || currentImageUrl) ? (
          <img
            src={pendingImageUrl || currentImageUrl}
            alt={`${userName} Profile Picture`}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover'
            }}
          />
        ) : (
          <div style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#000000',
            fontSize: currentSize.text,
            fontWeight: 'bold'
          }}>
            {userInitials}
          </div>
        )}

        {/* Upload Overlay */}
        {editable && (
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            opacity: 0,
            transition: 'opacity 0.2s ease',
            borderRadius: '50%'
          }}
          className="upload-overlay"
          >
            <span style={{ color: 'white', fontSize: '14px' }}>
              {uploading ? '⏳' : '📷'}
            </span>
          </div>
        )}
      </div>

      {/* Loading Indicator */}
      {uploading && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          background: 'rgba(0, 0, 0, 0.8)',
          borderRadius: '50%',
          width: '40px',
          height: '40px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <div style={{
            width: '20px',
            height: '20px',
            border: '2px solid transparent',
            borderTop: '2px solid #FFD700',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }}></div>
        </div>
      )}

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        style={{ display: 'none' }}
      />

      {/* Save Button */}
      {showSaveButton && selectedFile && !uploading && (
        <div style={{
          position: 'absolute',
          top: '100%',
          left: '50%',
          transform: 'translateX(-50%)',
          marginTop: '8px',
          zIndex: 10
        }}>
          <button
            onClick={handleSave}
            disabled={saving}
            style={{
              background: saving ? '#666' : '#FFD700',
              color: saving ? '#ccc' : '#000',
              border: 'none',
              borderRadius: '6px',
              padding: '8px 16px',
              fontSize: '12px',
              fontWeight: 'bold',
              cursor: saving ? 'not-allowed' : 'pointer',
              transition: 'all 0.2s ease'
            }}
          >
            {saving ? 'Saving...' : 'Save Picture'}
          </button>
        </div>
      )}

      {/* Success Message */}
      {success && (
        <div style={{
          position: 'absolute',
          top: '100%',
          left: '50%',
          transform: 'translateX(-50%)',
          marginTop: showSaveButton && selectedFile ? '48px' : '8px',
          padding: '4px 8px',
          background: 'rgba(34, 197, 94, 0.9)',
          color: 'white',
          borderRadius: '4px',
          fontSize: '12px',
          whiteSpace: 'nowrap',
          zIndex: 10
        }}>
          {success}
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div style={{
          position: 'absolute',
          top: '100%',
          left: '50%',
          transform: 'translateX(-50%)',
          marginTop: showSaveButton && selectedFile ? '48px' : (success ? '28px' : '8px'),
          padding: '4px 8px',
          background: 'rgba(239, 68, 68, 0.9)',
          color: 'white',
          borderRadius: '4px',
          fontSize: '12px',
          whiteSpace: 'nowrap',
          zIndex: 10
        }}>
          {error}
        </div>
      )}

      <style jsx="true">{`
        .upload-overlay:hover {
          opacity: 1 !important;
        }
      `}</style>
    </div>
  );
};
