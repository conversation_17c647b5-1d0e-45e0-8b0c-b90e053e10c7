import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client with service role
const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { email, pin } = req.body;

    if (!email || !pin) {
      return res.status(400).json({ error: 'Email and PIN are required' });
    }

    console.log('🔐 Verifying reset PIN for:', email, 'PIN:', pin);

    // Find the reset PIN record
    const { data: pinRecord, error: pinError } = await supabase
      .from('password_reset_pins')
      .select('*')
      .eq('email', email.toLowerCase().trim())
      .eq('pin', pin)
      .eq('used', false)
      .single();

    if (pinError || !pinRecord) {
      console.log('❌ Invalid or expired PIN for:', email);
      return res.status(400).json({ error: 'Invalid or expired PIN' });
    }

    // Check if PIN has expired
    const now = new Date();
    const expiresAt = new Date(pinRecord.expires_at);
    
    if (now > expiresAt) {
      console.log('❌ PIN expired for:', email);
      return res.status(400).json({ error: 'PIN has expired. Please request a new one.' });
    }

    // Mark PIN as used
    const { error: updateError } = await supabase
      .from('password_reset_pins')
      .update({
        used: true,
        used_at: new Date().toISOString()
      })
      .eq('id', pinRecord.id);

    if (updateError) {
      console.error('❌ Error marking PIN as used:', updateError);
      return res.status(500).json({ error: 'Failed to verify PIN' });
    }

    // Also update the users table to mark PIN as verified for the existing password reset API
    const { error: userUpdateError } = await supabase
      .from('users')
      .update({
        reset_token: 'VERIFIED:' + new Date().toISOString(),
        reset_token_expires: new Date(Date.now() + 30 * 60 * 1000).toISOString() // 30 minutes
      })
      .eq('id', pinRecord.user_id);

    if (userUpdateError) {
      console.error('❌ Error updating user reset token:', userUpdateError);
      return res.status(500).json({ error: 'Failed to verify PIN' });
    }

    console.log('✅ PIN verified successfully for:', email);

    return res.status(200).json({
      success: true,
      message: 'PIN verified successfully. You can now reset your password.',
      userId: pinRecord.user_id
    });

  } catch (error) {
    console.error('❌ PIN verification error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
