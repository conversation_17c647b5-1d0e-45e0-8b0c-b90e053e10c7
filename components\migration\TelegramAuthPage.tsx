import { useEffect, useState } from 'react';
import { createClient } from '@supabase/supabase-js';

// Import validation utilities (will be used for migration form)
// Note: bcrypt will be used on the server-side API endpoint

// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

export default function TelegramAuthPage() {
  // Custom navigation function for Vite React SPA
  const navigate = (path: string) => {
    window.location.href = path;
  };

  // Get query parameters from URL
  const getQueryParams = () => {
    const params = new URLSearchParams(window.location.search);
    return {
      token: params.get('token'),
      user_id: params.get('user_id')
    };
  };
  const [status, setStatus] = useState<'loading' | 'success' | 'error' | 'migration'>('loading');
  const [message, setMessage] = useState('Authenticating...');
  const [userInfo, setUserInfo] = useState<any>(null);

  // Migration form state
  const [migrationForm, setMigrationForm] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    username: '',
    full_name: '',
    phone: '',
    country_of_residence: '',
    id_number: '',
    address: ''
  });
  const [migrationLoading, setMigrationLoading] = useState(false);
  const [migrationError, setMigrationError] = useState('');
  const [passwordStrength, setPasswordStrength] = useState({ score: 0, feedback: [] });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  useEffect(() => {
    const authenticateUser = async () => {
      try {
        const { token, user_id } = getQueryParams();

        if (!token || !user_id) {
          setStatus('error');
          setMessage('Invalid authentication link. Please try again from Telegram.');
          return;
        }

        console.log('🔐 Processing Telegram authentication...', { token, user_id });

        // Validate token format
        const tokenParts = (token as string).split('_');
        if (tokenParts.length !== 3 || tokenParts[0] !== user_id) {
          setStatus('error');
          setMessage('Invalid token format. Please try again from Telegram.');
          return;
        }

        // Call API to verify token and get user info
        const response = await fetch('/api/auth/telegram-verify', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            token,
            user_id: parseInt(user_id as string)
          })
        });

        const result = await response.json();

        if (!response.ok) {
          console.error('❌ Authentication failed:', result);
          setStatus('error');
          setMessage(result.error || 'Authentication failed. Please try again.');
          return;
        }

        console.log('✅ Authentication successful:', result);

        // Check if user needs migration (has telegram_auth password)
        if (result.user.password_hash === 'telegram_auth') {
          console.log('🔄 User needs migration to website login');
          setStatus('migration');
          setMessage('Account migration required');
          setUserInfo(result.user);
          
          // Pre-populate form with existing data
          setMigrationForm(prev => ({
            ...prev,
            email: result.user.email || '',
            username: result.user.username || '',
            full_name: result.user.full_name || '',
            phone: result.user.phone || '',
            country_of_residence: result.user.country_of_residence || '',
            id_number: result.user.id_number || '',
            address: result.user.address || ''
          }));
        } else {
          console.log('✅ User already migrated, logging in...');
          
          // Store user session
          localStorage.setItem('aureus_user', JSON.stringify(result.user));
          localStorage.setItem('aureus_session', JSON.stringify({
            token: result.session.token,
            expires: Date.now() + (7 * 24 * 60 * 60 * 1000) // 7 days
          }));

          setStatus('success');
          setMessage('Login successful! Redirecting to dashboard...');
          
          // Redirect to dashboard
          setTimeout(() => {
            navigate('/dashboard');
          }, 2000);
        }

      } catch (error) {
        console.error('❌ Authentication error:', error);
        setStatus('error');
        setMessage('An unexpected error occurred. Please try again.');
      }
    };

    // Run authentication immediately since we're not using Next.js router
    authenticateUser();
  }, []); // Empty dependency array since we don't need to wait for router

  // Initialize migration form when userInfo is available
  useEffect(() => {
    if (userInfo && status === 'migration') {
      // Auto-populate form fields based on existing data
      setMigrationForm(prev => ({
        ...prev,
        email: userInfo.email || '',
        username: userInfo.username || '',
        full_name: userInfo.full_name || '',
        phone: userInfo.phone || '',
        country_of_residence: userInfo.country_of_residence || '',
        id_number: userInfo.id_number || '',
        address: userInfo.address || ''
      }));
    }
  }, [userInfo, status]);

  // Password strength checker
  const checkPasswordStrength = (password: string) => {
    const feedback = [];
    let score = 0;

    if (password.length >= 8) score++;
    else feedback.push('At least 8 characters');

    if (/[A-Z]/.test(password)) score++;
    else feedback.push('One uppercase letter');

    if (/[a-z]/.test(password)) score++;
    else feedback.push('One lowercase letter');

    if (/\d/.test(password)) score++;
    else feedback.push('One number');

    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score++;
    else feedback.push('One special character');

    setPasswordStrength({ score, feedback });
  };

  const handleMigrationSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (migrationForm.password !== migrationForm.confirmPassword) {
      setMigrationError('Passwords do not match');
      return;
    }

    if (passwordStrength.score < 5) {
      setMigrationError('Password does not meet security requirements');
      return;
    }

    setMigrationLoading(true);
    setMigrationError('');

    try {
      console.log('🔄 Starting migration process...');

      const response = await fetch('/api/auth/migrate-telegram-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: userInfo.id,
          ...migrationForm
        })
      });

      const result = await response.json();

      if (!response.ok) {
        console.error('❌ Migration failed:', result);
        setMigrationError(result.error || 'Migration failed. Please try again.');
        return;
      }

      console.log('✅ Migration successful:', result);

      // Store user session
      localStorage.setItem('aureus_user', JSON.stringify(result.user));
      localStorage.setItem('aureus_session', JSON.stringify({
        token: result.session.token,
        expires: Date.now() + (7 * 24 * 60 * 60 * 1000) // 7 days
      }));

      setStatus('success');
      setMessage('Migration successful! Redirecting to dashboard...');
      
      // Redirect to dashboard
      setTimeout(() => {
        navigate('/dashboard');
      }, 2000);

    } catch (error) {
      console.error('❌ Migration error:', error);
      setMigrationError('An unexpected error occurred. Please try again.');
    } finally {
      setMigrationLoading(false);
    }
  };

  // Loading state
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center">
        <div className="text-center">
          <div className="relative">
            <div className="w-16 h-16 border-4 border-yellow-500/30 border-t-yellow-500 rounded-full animate-spin mx-auto mb-6"></div>
            <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-r-yellow-400 rounded-full animate-ping mx-auto"></div>
          </div>
          <h2 className="text-2xl font-bold text-white mb-2">Authenticating</h2>
          <p className="text-gray-400">{message}</p>
        </div>
      </div>
    );
  }

  // Success state
  if (status === 'success') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-6">
          <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-white mb-2">Success!</h2>
          <p className="text-gray-400">{message}</p>
        </div>
      </div>
    );
  }

  // Error state
  if (status === 'error') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-6">
          <div className="bg-red-500/20 border border-red-500 rounded-lg p-6">
            <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <p className="text-red-400 font-semibold">{message}</p>
            <div className="space-y-2">
              <button
                onClick={() => navigate('/')}
                className="w-full bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200"
              >
                Return to Homepage
              </button>
              <a
                href="https://t.me/aureusafricabot"
                target="_blank"
                rel="noopener noreferrer"
                className="block w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 text-center"
              >
                Try Again from Telegram
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Migration form state
  if (status === 'migration') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white flex items-center justify-center p-4">
        {/* Background Effects */}
        <div className="fixed inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-pulse" style={{animationDelay: '1s'}}></div>
        </div>

        <div className="relative z-10 w-full max-w-lg max-h-[90vh] overflow-y-auto">
          <div className="bg-gray-900/50 backdrop-blur-xl rounded-3xl p-8 border border-gray-700/30 shadow-2xl text-center">
            {/* Logo */}
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full mb-6 shadow-2xl">
              <img
                src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png?v=2"
                alt="Aureus Alliance Holdings Logo"
                className="w-12 h-12 object-contain"
              />
            </div>

            <h1 className="text-2xl font-bold text-white mb-6">
              Account Migration Required
            </h1>

            <div className="space-y-6">
              <div className="text-center">
                <div className="flex justify-center mb-4">
                  <svg className="w-12 h-12 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <p className="text-yellow-400 font-semibold mb-2">{message}</p>
                {userInfo && (
                  <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-3 mb-4">
                    <p className="text-sm text-yellow-300">
                      Authenticated as: {userInfo.first_name} {userInfo.last_name || ''}
                    </p>
                    <p className="text-xs text-yellow-400">
                      @{userInfo.username || 'No username'} • ID: {userInfo.telegram_id}
                    </p>
                  </div>
                )}
              </div>

              {/* Migration Form */}
              <form onSubmit={handleMigrationSubmit} className="space-y-4 text-left">
                {/* Username Field - Auto-populated, Read-only */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Username *
                  </label>
                  <input
                    type="text"
                    value={migrationForm.username}
                    readOnly
                    className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-gray-400 cursor-not-allowed"
                  />
                  <p className="text-xs text-gray-500 mt-1">Auto-generated from your Telegram data</p>
                </div>

                {/* Email Field */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    value={migrationForm.email}
                    onChange={(e) => setMigrationForm(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white focus:border-yellow-500 focus:ring-1 focus:ring-yellow-500"
                    required
                  />
                </div>

                {/* Password Field */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    New Password *
                  </label>
                  <div className="relative">
                    <input
                      type={showPassword ? "text" : "password"}
                      value={migrationForm.password}
                      onChange={(e) => {
                        setMigrationForm(prev => ({ ...prev, password: e.target.value }));
                        checkPasswordStrength(e.target.value);
                      }}
                      className="w-full px-3 py-2 pr-10 bg-gray-800/50 border border-gray-600 rounded-lg text-white focus:border-yellow-500 focus:ring-1 focus:ring-yellow-500"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200"
                    >
                      {showPassword ? (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                          <line x1="1" y1="1" x2="23" y2="23"/>
                        </svg>
                      ) : (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                          <circle cx="12" cy="12" r="3"/>
                        </svg>
                      )}
                    </button>
                  </div>

                  {/* Password Strength Indicator */}
                  {migrationForm.password && (
                    <div className="mt-2">
                      <div className="flex space-x-1 mb-1">
                        {[1, 2, 3, 4, 5].map((level) => (
                          <div
                            key={level}
                            className={`h-1 flex-1 rounded ${
                              passwordStrength.score >= level
                                ? level <= 2 ? 'bg-red-500' : level <= 3 ? 'bg-yellow-500' : 'bg-green-500'
                                : 'bg-gray-600'
                            }`}
                          />
                        ))}
                      </div>
                      {passwordStrength.feedback.length > 0 && (
                        <p className="text-xs text-gray-400">
                          Missing: {passwordStrength.feedback.join(', ')}
                        </p>
                      )}
                    </div>
                  )}
                </div>

                {/* Confirm Password Field */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Confirm Password *
                  </label>
                  <div className="relative">
                    <input
                      type={showConfirmPassword ? "text" : "password"}
                      value={migrationForm.confirmPassword}
                      onChange={(e) => setMigrationForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                      className="w-full px-3 py-2 pr-10 bg-gray-800/50 border border-gray-600 rounded-lg text-white focus:border-yellow-500 focus:ring-1 focus:ring-yellow-500"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200"
                    >
                      {showConfirmPassword ? (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                          <line x1="1" y1="1" x2="23" y2="23"/>
                        </svg>
                      ) : (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                          <circle cx="12" cy="12" r="3"/>
                        </svg>
                      )}
                    </button>
                  </div>
                  {migrationForm.confirmPassword && migrationForm.password !== migrationForm.confirmPassword && (
                    <p className="text-xs text-red-400 mt-1">Passwords do not match</p>
                  )}
                </div>

                {/* Error Display */}
                {migrationError && (
                  <div className="bg-red-500/20 border border-red-500 rounded-lg p-3">
                    <p className="text-red-400 text-sm">{migrationError}</p>
                  </div>
                )}

                {/* Submit Button */}
                <button
                  type="submit"
                  disabled={migrationLoading || passwordStrength.score < 5 || migrationForm.password !== migrationForm.confirmPassword}
                  className="w-full bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 disabled:from-gray-600 disabled:to-gray-700 text-black disabled:text-gray-400 font-bold py-3 px-4 rounded-lg transition-all duration-200 disabled:cursor-not-allowed"
                >
                  {migrationLoading ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-black mr-2"></div>
                      Migrating Account...
                    </div>
                  ) : (
                    'Complete Migration'
                  )}
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
}
