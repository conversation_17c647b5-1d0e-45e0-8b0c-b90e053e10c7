/**
 * PROGRESS TRACKING MANAGER
 * 
 * Comprehensive progress tracking and analytics system:
 * - Individual student progress monitoring
 * - Course completion statistics
 * - Learning path visualization
 * - Performance analytics and insights
 * - Engagement metrics and time tracking
 * - Certification and achievement tracking
 */

import React, { useState, useEffect } from 'react';
import { getServiceRoleClient } from '../../lib/supabase';

interface ProgressTrackingManagerProps {
  courseId?: number;
  userId?: number;
}

interface StudentProgress {
  user_id: number;
  user_name: string;
  user_email: string;
  user_type: string;
  enrollment_date: string;
  last_activity: string;
  overall_progress: number;
  lessons_completed: number;
  total_lessons: number;
  assessments_passed: number;
  total_assessments: number;
  time_spent: number;
  certificate_earned: boolean;
  course_rating?: number;
}

interface CourseAnalytics {
  total_enrollments: number;
  active_learners: number;
  completion_rate: number;
  average_progress: number;
  average_time_spent: number;
  average_rating: number;
  dropout_rate: number;
}

interface LessonProgress {
  lesson_id: number;
  lesson_title: string;
  completion_count: number;
  average_time_spent: number;
  difficulty_rating: number;
}

export const ProgressTrackingManager: React.FC<ProgressTrackingManagerProps> = ({
  courseId,
  userId
}) => {
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'students' | 'lessons' | 'analytics'>('overview');
  const [studentProgress, setStudentProgress] = useState<StudentProgress[]>([]);
  const [courseAnalytics, setCourseAnalytics] = useState<CourseAnalytics>({
    total_enrollments: 0,
    active_learners: 0,
    completion_rate: 0,
    average_progress: 0,
    average_time_spent: 0,
    average_rating: 0,
    dropout_rate: 0
  });
  const [lessonProgress, setLessonProgress] = useState<LessonProgress[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'progress' | 'activity' | 'name'>('progress');

  useEffect(() => {
    loadProgressData();
  }, [courseId, userId]);

  const loadProgressData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadStudentProgress(),
        loadCourseAnalytics(),
        loadLessonProgress()
      ]);
    } catch (error) {
      console.error('Error loading progress data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadStudentProgress = async () => {
    try {
      const serviceClient = getServiceRoleClient();
      
      let query = serviceClient
        .from('training_enrollments')
        .select(`
          *,
          users!training_enrollments_user_id_fkey(username, email, user_type),
          training_lesson_progress(lesson_id, completed_at, time_spent),
          training_assessment_attempts(assessment_id, score, passed)
        `);

      if (courseId) {
        query = query.eq('course_id', courseId);
      }
      if (userId) {
        query = query.eq('user_id', userId);
      }

      const { data, error } = await query;
      if (error) throw error;

      // Process and calculate progress metrics
      const processedProgress = data?.map(enrollment => {
        const lessonProgress = enrollment.training_lesson_progress || [];
        const assessmentAttempts = enrollment.training_assessment_attempts || [];
        
        const lessonsCompleted = lessonProgress.filter(lp => lp.completed_at).length;
        const assessmentsPassed = assessmentAttempts.filter(aa => aa.passed).length;
        const totalTimeSpent = lessonProgress.reduce((sum, lp) => sum + (lp.time_spent || 0), 0);
        
        return {
          user_id: enrollment.user_id,
          user_name: enrollment.users?.username || 'Unknown',
          user_email: enrollment.users?.email || '',
          user_type: enrollment.users?.user_type || 'user',
          enrollment_date: enrollment.enrolled_at,
          last_activity: enrollment.last_accessed_at || enrollment.enrolled_at,
          overall_progress: enrollment.progress_percentage || 0,
          lessons_completed: lessonsCompleted,
          total_lessons: 10, // TODO: Get actual lesson count
          assessments_passed: assessmentsPassed,
          total_assessments: 5, // TODO: Get actual assessment count
          time_spent: totalTimeSpent,
          certificate_earned: enrollment.completed_at !== null,
          course_rating: enrollment.rating
        };
      }) || [];

      setStudentProgress(processedProgress);
    } catch (error) {
      console.error('Error loading student progress:', error);
    }
  };

  const loadCourseAnalytics = async () => {
    try {
      const serviceClient = getServiceRoleClient();
      
      // Get enrollment statistics
      const { data: enrollments, error: enrollmentError } = await serviceClient
        .from('training_enrollments')
        .select('*')
        .eq('course_id', courseId || 0);

      if (enrollmentError) throw enrollmentError;

      const totalEnrollments = enrollments?.length || 0;
      const completedEnrollments = enrollments?.filter(e => e.completed_at).length || 0;
      const activeEnrollments = enrollments?.filter(e => {
        const lastAccess = new Date(e.last_accessed_at || e.enrolled_at);
        const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        return lastAccess > weekAgo;
      }).length || 0;

      const averageProgress = enrollments?.reduce((sum, e) => sum + (e.progress_percentage || 0), 0) / totalEnrollments || 0;
      const averageRating = enrollments?.filter(e => e.rating).reduce((sum, e) => sum + e.rating, 0) / enrollments?.filter(e => e.rating).length || 0;

      setCourseAnalytics({
        total_enrollments: totalEnrollments,
        active_learners: activeEnrollments,
        completion_rate: totalEnrollments > 0 ? (completedEnrollments / totalEnrollments) * 100 : 0,
        average_progress: averageProgress,
        average_time_spent: 0, // TODO: Calculate from lesson progress
        average_rating: averageRating,
        dropout_rate: totalEnrollments > 0 ? ((totalEnrollments - activeEnrollments - completedEnrollments) / totalEnrollments) * 100 : 0
      });
    } catch (error) {
      console.error('Error loading course analytics:', error);
    }
  };

  const loadLessonProgress = async () => {
    try {
      const serviceClient = getServiceRoleClient();
      
      const { data: lessons, error } = await serviceClient
        .from('training_lessons')
        .select(`
          *,
          training_lesson_progress(completed_at, time_spent)
        `)
        .eq('course_id', courseId || 0)
        .order('sort_order');

      if (error) throw error;

      const processedLessons = lessons?.map(lesson => {
        const progress = lesson.training_lesson_progress || [];
        const completions = progress.filter(p => p.completed_at).length;
        const averageTime = progress.reduce((sum, p) => sum + (p.time_spent || 0), 0) / progress.length || 0;

        return {
          lesson_id: lesson.id,
          lesson_title: lesson.title,
          completion_count: completions,
          average_time_spent: averageTime,
          difficulty_rating: 3 // TODO: Calculate from user feedback
        };
      }) || [];

      setLessonProgress(processedLessons);
    } catch (error) {
      console.error('Error loading lesson progress:', error);
    }
  };

  const formatTime = (minutes: number): string => {
    if (minutes < 60) {
      return `${Math.round(minutes)}m`;
    }
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return `${hours}h ${mins}m`;
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString();
  };

  const getProgressColor = (progress: number): string => {
    if (progress >= 80) return 'text-green-400';
    if (progress >= 50) return 'text-yellow-400';
    return 'text-red-400';
  };

  const filteredStudents = studentProgress.filter(student =>
    student.user_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    student.user_email.toLowerCase().includes(searchQuery.toLowerCase())
  ).sort((a, b) => {
    switch (sortBy) {
      case 'progress':
        return b.overall_progress - a.overall_progress;
      case 'activity':
        return new Date(b.last_activity).getTime() - new Date(a.last_activity).getTime();
      case 'name':
        return a.user_name.localeCompare(b.user_name);
      default:
        return 0;
    }
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-3 text-gray-400">Loading progress data...</span>
      </div>
    );
  }

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Enrollments</p>
              <p className="text-2xl font-bold text-white">{courseAnalytics.total_enrollments}</p>
            </div>
            <div className="text-3xl">👥</div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active Learners</p>
              <p className="text-2xl font-bold text-green-400">{courseAnalytics.active_learners}</p>
            </div>
            <div className="text-3xl">🎯</div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Completion Rate</p>
              <p className="text-2xl font-bold text-blue-400">{courseAnalytics.completion_rate.toFixed(1)}%</p>
            </div>
            <div className="text-3xl">✅</div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Average Rating</p>
              <p className="text-2xl font-bold text-yellow-400">{courseAnalytics.average_rating.toFixed(1)}/5</p>
            </div>
            <div className="text-3xl">⭐</div>
          </div>
        </div>
      </div>

      {/* Progress Chart Placeholder */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">📊 Progress Overview</h3>
        <div className="h-64 flex items-center justify-center text-gray-400">
          <div className="text-center">
            <div className="text-4xl mb-2">📈</div>
            <p>Progress charts coming soon...</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderStudentList = () => (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="bg-gray-800 rounded-lg p-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search students..."
              className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="progress">Sort by Progress</option>
            <option value="activity">Sort by Activity</option>
            <option value="name">Sort by Name</option>
          </select>
        </div>
      </div>

      {/* Student Progress Table */}
      <div className="bg-gray-800 rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Student
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Progress
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Lessons
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Assessments
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Time Spent
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Last Activity
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {filteredStudents.map((student) => (
                <tr key={student.user_id} className="hover:bg-gray-750">
                  <td className="px-6 py-4">
                    <div>
                      <div className="text-sm font-medium text-white">{student.user_name}</div>
                      <div className="text-sm text-gray-400">{student.user_email}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <div className="w-full bg-gray-600 rounded-full h-2 mr-3">
                        <div
                          className="bg-blue-500 h-2 rounded-full"
                          style={{ width: `${student.overall_progress}%` }}
                        />
                      </div>
                      <span className={`text-sm font-medium ${getProgressColor(student.overall_progress)}`}>
                        {student.overall_progress}%
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-300">
                    {student.lessons_completed}/{student.total_lessons}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-300">
                    {student.assessments_passed}/{student.total_assessments}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-300">
                    {formatTime(student.time_spent)}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-300">
                    {formatDate(student.last_activity)}
                  </td>
                  <td className="px-6 py-4">
                    {student.certificate_earned ? (
                      <span className="bg-green-600 text-green-100 text-xs px-2 py-1 rounded-full">
                        🏆 Completed
                      </span>
                    ) : student.overall_progress > 0 ? (
                      <span className="bg-blue-600 text-blue-100 text-xs px-2 py-1 rounded-full">
                        📚 In Progress
                      </span>
                    ) : (
                      <span className="bg-gray-600 text-gray-100 text-xs px-2 py-1 rounded-full">
                        ⏸️ Not Started
                      </span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">📊 Progress Tracking</h2>
        <button
          onClick={loadProgressData}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
        >
          🔄 Refresh Data
        </button>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-700">
        <nav className="flex space-x-8">
          {[
            { id: 'overview', name: 'Overview', icon: '📊' },
            { id: 'students', name: 'Students', icon: '👥' },
            { id: 'lessons', name: 'Lessons', icon: '📚' },
            { id: 'analytics', name: 'Analytics', icon: '📈' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
              }`}
            >
              <span>{tab.icon}</span>
              <span>{tab.name}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && renderOverview()}
      {activeTab === 'students' && renderStudentList()}
      {activeTab === 'lessons' && (
        <div className="text-center py-12 text-gray-400">
          <div className="text-4xl mb-4">📚</div>
          <p>Lesson analytics coming soon...</p>
        </div>
      )}
      {activeTab === 'analytics' && (
        <div className="text-center py-12 text-gray-400">
          <div className="text-4xl mb-4">📈</div>
          <p>Advanced analytics coming soon...</p>
        </div>
      )}
    </div>
  );
};
