/**
 * KYC ADMIN MANAGEMENT SERVICE
 *
 * Comprehensive service for managing KYC submissions in the admin dashboard.
 * Handles fetching, filtering, searching, status updates, and audit logging.
 *
 * Enhanced with document management capabilities for viewing and managing
 * uploaded KYC documents including ID cards, proof of address, and selfies.
 */

import { getServiceRoleClient } from '../supabase';
import { logAdminAction } from '../adminAuth';
import { kycNotificationService } from './kycNotificationService';
import { notificationService } from '../notificationService';

// Document interfaces
export interface KYCDocument {
  id: string;
  kyc_id: string;
  user_id: number;
  document_type: 'id_document_front' | 'id_document_back' | 'proof_of_address' | 'selfie_verification' | 'facial_recognition' | 'additional_document';
  document_category: 'identity_verification' | 'address_verification' | 'biometric_verification' | 'supplementary';
  file_name: string;
  file_path: string;
  storage_bucket: string;
  file_size_bytes?: number;
  mime_type?: string;
  file_hash?: string;
  verification_status: 'pending' | 'approved' | 'rejected' | 'requires_resubmission' | 'expired';
  quality_score?: number;
  confidence_score?: number;
  reviewed_by?: string;
  reviewed_at?: string;
  rejection_reason?: string;
  admin_notes?: string;
  metadata?: any;
  uploaded_at: string;
  created_at: string;
  updated_at: string;
}

export interface DocumentAccessLog {
  id: string;
  document_id: string;
  user_id?: number;
  access_type: 'view' | 'download' | 'approve' | 'reject';
  ip_address?: string;
  user_agent?: string;
  accessed_at: string;
}

export interface KYCSubmission {
  id: string;
  user_id: number;
  first_name: string;
  last_name: string;
  full_legal_name: string;
  id_type: 'national_id' | 'passport' | 'drivers_license';
  id_number_encrypted: string;
  phone_number: string;
  email_address: string;
  street_address: string;
  city: string;
  postal_code: string;
  country_code: string;
  country_name: string;
  data_consent_given: boolean;
  privacy_policy_accepted: boolean;
  kyc_status: 'pending' | 'completed' | 'rejected' | 'expired';
  kyc_completed_at: string;
  certificate_requested: boolean;
  certificate_generated_at?: string;
  certificate_sent_at?: string;
  created_at: string;
  updated_at: string;
  created_by_telegram_id?: number;
  last_modified_by?: string;
  rejection_reason?: string; // Added for rejection feedback
  // Associated documents
  documents?: KYCDocument[];
  // User information
  users?: {
    id: number;
    username: string;
    email: string;
    full_name: string;
    created_at: string;
    is_active: boolean;
  };
}

export interface KYCStatistics {
  total: number;
  pending: number;
  completed: number;
  rejected: number;
  expired: number;
  averageProcessingTime: number; // in hours
  completionRate: number; // percentage
  recentSubmissions: number; // last 7 days
}

export interface KYCFilters {
  status?: 'all' | 'pending' | 'completed' | 'rejected' | 'expired';
  dateRange?: {
    start: string;
    end: string;
  };
  country?: string;
  searchTerm?: string;
}

export interface KYCStatusUpdateData {
  kycId: string;
  newStatus: 'completed' | 'rejected';
  adminEmail: string;
  comments?: string;
  rejectionReason?: string;
}

export class KYCAdminService {
  private serviceClient = getServiceRoleClient();

  /**
   * Get all KYC submissions with optional filtering
   */
  async getKYCSubmissions(
    filters: KYCFilters = {},
    limit: number = 50,
    offset: number = 0
  ): Promise<{ submissions: KYCSubmission[]; total: number }> {
    try {
      console.log('📋 Fetching KYC submissions with filters:', filters);

      // Use inner join to ensure we only get KYC records with valid user data
      let query = this.serviceClient
        .from('kyc_information')
        .select(`
          *,
          users!inner(
            id,
            username,
            email,
            full_name,
            created_at,
            is_active
          )
        `)
        .order('created_at', { ascending: false });

      // Apply status filter
      if (filters.status && filters.status !== 'all') {
        query = query.eq('kyc_status', filters.status);
      }

      // Apply date range filter
      if (filters.dateRange) {
        query = query
          .gte('created_at', filters.dateRange.start)
          .lte('created_at', filters.dateRange.end);
      }

      // Apply country filter
      if (filters.country) {
        query = query.eq('country_code', filters.country);
      }

      // Apply search term (search in name, email, username)
      if (filters.searchTerm) {
        const searchTerm = `%${filters.searchTerm.toLowerCase()}%`;
        query = query.or(`
          first_name.ilike.${searchTerm},
          last_name.ilike.${searchTerm},
          email_address.ilike.${searchTerm},
          users.username.ilike.${searchTerm},
          users.email.ilike.${searchTerm}
        `);
      }

      // Get total count
      const { count } = await query.select('*', { count: 'exact', head: true });

      // Get paginated results
      const { data: submissions, error } = await query
        .range(offset, offset + limit - 1);

      if (error) {
        console.error('❌ Error fetching KYC submissions:', error);
        throw error;
      }

      console.log(`✅ Fetched ${submissions?.length || 0} KYC submissions`);

      // Diagnostic logging for missing user data
      if (submissions) {
        const submissionsWithoutUsers = submissions.filter(submission => !submission.users);
        if (submissionsWithoutUsers.length > 0) {
          console.warn(`⚠️ Found ${submissionsWithoutUsers.length} KYC submissions without user data:`);
          submissionsWithoutUsers.forEach(submission => {
            console.warn(`   - KYC ID: ${submission.id}, User ID: ${submission.user_id}`);
          });
        }
      }

      return {
        submissions: submissions || [],
        total: count || 0
      };

    } catch (error) {
      console.error('❌ Error in getKYCSubmissions:', error);
      throw error;
    }
  }

  /**
   * Get KYC statistics for dashboard overview
   */
  async getKYCStatistics(): Promise<KYCStatistics> {
    try {
      console.log('📊 Calculating KYC statistics...');

      // Get all KYC records
      const { data: allKyc, error } = await this.serviceClient
        .from('kyc_information')
        .select('kyc_status, created_at, kyc_completed_at');

      if (error) {
        console.error('❌ Error fetching KYC data for statistics:', error);
        throw error;
      }

      const total = allKyc?.length || 0;
      const pending = allKyc?.filter(k => k.kyc_status === 'pending').length || 0;
      const completed = allKyc?.filter(k => k.kyc_status === 'completed').length || 0;
      const rejected = allKyc?.filter(k => k.kyc_status === 'rejected').length || 0;
      const expired = allKyc?.filter(k => k.kyc_status === 'expired').length || 0;

      // Calculate average processing time (for completed/rejected submissions)
      const processedSubmissions = allKyc?.filter(k => 
        (k.kyc_status === 'completed' || k.kyc_status === 'rejected') && 
        k.kyc_completed_at
      ) || [];

      let averageProcessingTime = 0;
      if (processedSubmissions.length > 0) {
        const totalProcessingTime = processedSubmissions.reduce((sum, submission) => {
          const created = new Date(submission.created_at);
          const completed = new Date(submission.kyc_completed_at);
          const processingHours = (completed.getTime() - created.getTime()) / (1000 * 60 * 60);
          return sum + processingHours;
        }, 0);
        averageProcessingTime = totalProcessingTime / processedSubmissions.length;
      }

      // Calculate completion rate
      const completionRate = total > 0 ? ((completed + rejected) / total) * 100 : 0;

      // Count recent submissions (last 7 days)
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      const recentSubmissions = allKyc?.filter(k => 
        new Date(k.created_at) >= sevenDaysAgo
      ).length || 0;

      const statistics: KYCStatistics = {
        total,
        pending,
        completed,
        rejected,
        expired,
        averageProcessingTime: Math.round(averageProcessingTime * 100) / 100,
        completionRate: Math.round(completionRate * 100) / 100,
        recentSubmissions
      };

      console.log('✅ KYC statistics calculated:', statistics);
      return statistics;

    } catch (error) {
      console.error('❌ Error calculating KYC statistics:', error);
      return {
        total: 0,
        pending: 0,
        completed: 0,
        rejected: 0,
        expired: 0,
        averageProcessingTime: 0,
        completionRate: 0,
        recentSubmissions: 0
      };
    }
  }

  /**
   * Get a single KYC submission by ID
   */
  async getKYCSubmissionById(kycId: string): Promise<KYCSubmission | null> {
    try {
      console.log(`🔍 Fetching KYC submission: ${kycId}`);

      const { data: submission, error } = await this.serviceClient
        .from('kyc_information')
        .select(`
          *,
          users(
            id,
            username,
            email,
            full_name,
            created_at,
            is_active
          )
        `)
        .eq('id', kycId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          console.log('ℹ️ KYC submission not found');
          return null;
        }
        console.error('❌ Error fetching KYC submission:', error);
        throw error;
      }

      console.log('✅ KYC submission fetched successfully');
      return submission;

    } catch (error) {
      console.error('❌ Error in getKYCSubmissionById:', error);
      throw error;
    }
  }

  /**
   * Quick approve KYC submission (for inline actions)
   */
  async quickApproveKYC(kycId: string, adminEmail: string, comments?: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`⚡ Quick approving KYC: ${kycId}`);

      // Get current submission for audit logging
      const currentSubmission = await this.getKYCSubmissionById(kycId);
      if (!currentSubmission) {
        return { success: false, error: 'KYC submission not found' };
      }

      // Update status to completed
      const { data, error } = await this.serviceClient
        .from('kyc_information')
        .update({
          kyc_status: 'completed',
          updated_at: new Date().toISOString(),
          last_modified_by: adminEmail
        })
        .eq('id', kycId)
        .select()
        .single();

      if (error) {
        console.error('❌ Error updating KYC status:', error);
        return { success: false, error: error.message };
      }

      // Log admin action
      await logAdminAction(
        adminEmail,
        'kyc_approve',
        `Quick approved KYC submission for user ${currentSubmission.user_id}`,
        { kycId, userId: currentSubmission.user_id, comments }
      );

      // Send email notification
      const notificationResult = await kycNotificationService.sendKYCStatusNotification({
        kycId,
        userId: currentSubmission.user_id,
        status: 'approved',
        comments,
        adminEmail
      });

      if (!notificationResult.success) {
        console.warn('⚠️ Failed to send KYC notification email:', notificationResult.error);
      }

      // Create in-app notification
      const inAppNotification = await notificationService.createNotification({
        user_id: currentSubmission.user_id,
        type: 'account_update',
        title: '🎉 KYC Verification Approved!',
        message: `Congratulations! Your KYC verification has been approved. You can now purchase shares and access all platform features.${comments ? ` Admin note: ${comments}` : ''}`,
        action_url: '/dashboard',
        metadata: {
          kyc_id: kycId,
          admin_email: adminEmail,
          approval_date: new Date().toISOString()
        }
      });

      if (!inAppNotification) {
        console.warn('⚠️ Failed to create in-app notification for KYC approval');
      }

      console.log(`✅ KYC quick approved successfully: ${kycId}`);
      return { success: true };

    } catch (error) {
      console.error('❌ Error in quickApproveKYC:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Quick reject KYC submission (for inline actions)
   */
  async quickRejectKYC(
    kycId: string,
    adminEmail: string,
    rejectionReason: string,
    comments?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`⚡ Quick rejecting KYC: ${kycId}`);

      if (!rejectionReason.trim()) {
        return { success: false, error: 'Rejection reason is required' };
      }

      // Get current submission for audit logging
      const currentSubmission = await this.getKYCSubmissionById(kycId);
      if (!currentSubmission) {
        return { success: false, error: 'KYC submission not found' };
      }

      // Update status to rejected with reason
      const { data, error } = await this.serviceClient
        .from('kyc_information')
        .update({
          kyc_status: 'rejected',
          rejection_reason: rejectionReason.trim(),
          updated_at: new Date().toISOString(),
          last_modified_by: adminEmail
        })
        .eq('id', kycId)
        .select()
        .single();

      if (error) {
        console.error('❌ Error updating KYC status:', error);
        return { success: false, error: error.message };
      }

      // Log admin action
      await logAdminAction(
        adminEmail,
        'kyc_reject',
        `Quick rejected KYC submission for user ${currentSubmission.user_id}`,
        { kycId, userId: currentSubmission.user_id, rejectionReason, comments }
      );

      // Send email notification
      const notificationResult = await kycNotificationService.sendKYCStatusNotification({
        kycId,
        userId: currentSubmission.user_id,
        status: 'rejected',
        rejectionReason,
        comments,
        adminEmail
      });

      if (!notificationResult.success) {
        console.warn('⚠️ Failed to send KYC notification email:', notificationResult.error);
      }

      // Create in-app notification
      const inAppNotification = await notificationService.createNotification({
        user_id: currentSubmission.user_id,
        type: 'account_update',
        title: '❌ KYC Verification Requires Attention',
        message: `Your KYC verification was not approved. Reason: ${rejectionReason}. Please review and resubmit your documents.${comments ? ` Admin note: ${comments}` : ''}`,
        action_url: '/kyc',
        metadata: {
          kyc_id: kycId,
          admin_email: adminEmail,
          rejection_reason: rejectionReason,
          rejection_date: new Date().toISOString()
        }
      });

      if (!inAppNotification) {
        console.warn('⚠️ Failed to create in-app notification for KYC rejection');
      }

      console.log(`✅ KYC quick rejected successfully: ${kycId}`);
      return { success: true };

    } catch (error) {
      console.error('❌ Error in quickRejectKYC:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Approve KYC submission (legacy method)
   */
  async approveKYC(kycId: string, adminEmail: string, comments?: string): Promise<{ success: boolean; error?: string }> {
    return this.quickApproveKYC(kycId, adminEmail, comments);
  }

  /**
   * Reject KYC submission (legacy method)
   */
  async rejectKYC(kycId: string, adminEmail: string, rejectionReason: string, comments?: string): Promise<{ success: boolean; error?: string }> {
    return this.quickRejectKYC(kycId, adminEmail, rejectionReason, comments);
  }

  /**
   * Update KYC status (approve or reject)
   */
  async updateKYCStatus(updateData: KYCStatusUpdateData): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`🔄 Updating KYC status: ${updateData.kycId} -> ${updateData.newStatus}`);

      // First, get the current KYC submission for audit logging
      const currentSubmission = await this.getKYCSubmissionById(updateData.kycId);
      if (!currentSubmission) {
        return { success: false, error: 'KYC submission not found' };
      }

      // Prepare update data
      const updateFields: any = {
        kyc_status: updateData.newStatus,
        updated_at: new Date().toISOString(),
        last_modified_by: updateData.adminEmail
      };

      // Add rejection reason if rejecting
      if (updateData.newStatus === 'rejected' && updateData.rejectionReason) {
        // Note: We'll need to add a rejection_reason column to the table
        updateFields.rejection_reason = updateData.rejectionReason;
      }

      // Update the KYC record
      const { error: updateError } = await this.serviceClient
        .from('kyc_information')
        .update(updateFields)
        .eq('id', updateData.kycId);

      if (updateError) {
        console.error('❌ Error updating KYC status:', updateError);
        return { success: false, error: updateError.message };
      }

      // Log admin action for audit trail
      await logAdminAction(
        updateData.adminEmail,
        'KYC_STATUS_UPDATE',
        'kyc_information',
        updateData.kycId,
        {
          action: updateData.newStatus,
          comments: updateData.comments,
          rejection_reason: updateData.rejectionReason,
          user_id: currentSubmission.user_id,
          user_email: currentSubmission.users?.email
        },
        { kyc_status: currentSubmission.kyc_status },
        { kyc_status: updateData.newStatus }
      );

      // Send email notification to user
      const notificationResult = await kycNotificationService.sendKYCStatusNotification({
        kycId: updateData.kycId,
        userId: currentSubmission.user_id,
        status: updateData.newStatus === 'completed' ? 'approved' : 'rejected',
        rejectionReason: updateData.rejectionReason,
        comments: updateData.comments,
        adminEmail: updateData.adminEmail
      });

      if (!notificationResult.success) {
        console.warn('⚠️ Failed to send KYC notification email:', notificationResult.error);
      }

      // Create in-app notification
      const isApproved = updateData.newStatus === 'completed';
      const inAppNotification = await notificationService.createNotification({
        user_id: currentSubmission.user_id,
        type: 'account_update',
        title: isApproved ? '🎉 KYC Verification Approved!' : '❌ KYC Verification Requires Attention',
        message: isApproved
          ? `Congratulations! Your KYC verification has been approved. You can now purchase shares and access all platform features.${updateData.comments ? ` Admin note: ${updateData.comments}` : ''}`
          : `Your KYC verification was not approved. ${updateData.rejectionReason ? `Reason: ${updateData.rejectionReason}.` : ''} Please review and resubmit your documents.${updateData.comments ? ` Admin note: ${updateData.comments}` : ''}`,
        action_url: isApproved ? '/dashboard' : '/kyc',
        metadata: {
          kyc_id: updateData.kycId,
          admin_email: updateData.adminEmail,
          status: updateData.newStatus,
          rejection_reason: updateData.rejectionReason,
          update_date: new Date().toISOString()
        }
      });

      if (!inAppNotification) {
        console.warn('⚠️ Failed to create in-app notification for KYC status update');
      }

      console.log(`✅ KYC status updated successfully: ${updateData.newStatus}`);
      return { success: true };

    } catch (error) {
      console.error('❌ Error in updateKYCStatus:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Get available countries from KYC submissions for filtering
   */
  async getAvailableCountries(): Promise<Array<{ code: string; name: string; count: number }>> {
    try {
      console.log('🌍 Fetching available countries...');

      const { data: countries, error } = await this.serviceClient
        .from('kyc_information')
        .select('country_code, country_name')
        .order('country_name');

      if (error) {
        console.error('❌ Error fetching countries:', error);
        throw error;
      }

      // Group by country and count occurrences
      const countryMap = new Map<string, { name: string; count: number }>();
      
      countries?.forEach(item => {
        const existing = countryMap.get(item.country_code);
        if (existing) {
          existing.count++;
        } else {
          countryMap.set(item.country_code, {
            name: item.country_name,
            count: 1
          });
        }
      });

      const result = Array.from(countryMap.entries()).map(([code, data]) => ({
        code,
        name: data.name,
        count: data.count
      }));

      console.log(`✅ Found ${result.length} countries`);
      return result;

    } catch (error) {
      console.error('❌ Error in getAvailableCountries:', error);
      return [];
    }
  }

  /**
   * Batch approve multiple KYC submissions
   */
  async batchApproveKYC(kycIds: string[], adminEmail: string, comments?: string): Promise<{
    successful: number;
    failed: number;
    errors: Array<{ kycId: string; error: string }>;
  }> {
    console.log(`🔄 Batch approving ${kycIds.length} KYC submissions...`);

    let successful = 0;
    let failed = 0;
    const errors: Array<{ kycId: string; error: string }> = [];

    for (const kycId of kycIds) {
      try {
        const result = await this.approveKYC(kycId, adminEmail, comments);
        if (result.success) {
          successful++;
        } else {
          failed++;
          errors.push({ kycId, error: result.error || 'Unknown error' });
        }
      } catch (error) {
        failed++;
        errors.push({
          kycId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }

      // Add small delay to prevent overwhelming the system
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log(`✅ Batch approval completed: ${successful} successful, ${failed} failed`);
    return { successful, failed, errors };
  }

  /**
   * Batch reject multiple KYC submissions
   */
  async batchRejectKYC(
    rejections: Array<{ kycId: string; rejectionReason: string }>,
    adminEmail: string,
    comments?: string
  ): Promise<{
    successful: number;
    failed: number;
    errors: Array<{ kycId: string; error: string }>;
  }> {
    console.log(`🔄 Batch rejecting ${rejections.length} KYC submissions...`);

    let successful = 0;
    let failed = 0;
    const errors: Array<{ kycId: string; error: string }> = [];

    for (const rejection of rejections) {
      try {
        const result = await this.rejectKYC(
          rejection.kycId,
          adminEmail,
          rejection.rejectionReason,
          comments
        );
        if (result.success) {
          successful++;
        } else {
          failed++;
          errors.push({ kycId: rejection.kycId, error: result.error || 'Unknown error' });
        }
      } catch (error) {
        failed++;
        errors.push({
          kycId: rejection.kycId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }

      // Add small delay to prevent overwhelming the system
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log(`✅ Batch rejection completed: ${successful} successful, ${failed} failed`);
    return { successful, failed, errors };
  }

  /**
   * Get KYC submissions that need review (pending status)
   */
  async getPendingKYCSubmissions(limit: number = 20): Promise<KYCSubmission[]> {
    const result = await this.getKYCSubmissions(
      { status: 'pending' },
      limit,
      0
    );
    return result.submissions;
  }

  /**
   * Search KYC submissions by user information
   */
  async searchKYCSubmissions(searchTerm: string, limit: number = 20): Promise<KYCSubmission[]> {
    const result = await this.getKYCSubmissions(
      { searchTerm },
      limit,
      0
    );
    return result.submissions;
  }

  /**
   * Get KYC submission history for a specific user
   */
  async getUserKYCHistory(userId: number): Promise<KYCSubmission[]> {
    try {
      console.log(`📋 Fetching KYC history for user: ${userId}`);

      const { data: submissions, error } = await this.serviceClient
        .from('kyc_information')
        .select(`
          *,
          users(
            id,
            username,
            email,
            full_name,
            created_at,
            is_active
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('❌ Error fetching user KYC history:', error);
        throw error;
      }

      console.log(`✅ Found ${submissions?.length || 0} KYC submissions for user`);
      return submissions || [];

    } catch (error) {
      console.error('❌ Error in getUserKYCHistory:', error);
      return [];
    }
  }

  /**
   * Get documents for a specific KYC submission
   */
  async getKYCDocuments(kycId: string): Promise<KYCDocument[]> {
    try {
      console.log(`📄 Fetching documents for KYC: ${kycId}`);

      const { data: documents, error } = await this.serviceClient
        .from('kyc_documents')
        .select('*')
        .eq('kyc_id', kycId)
        .order('uploaded_at', { ascending: false });

      if (error) {
        console.error('❌ Error fetching KYC documents:', error);
        throw error;
      }

      console.log(`✅ Found ${documents?.length || 0} documents for KYC`);
      return documents || [];

    } catch (error) {
      console.error('❌ Error in getKYCDocuments:', error);
      return [];
    }
  }

  /**
   * Get secure signed URL for document access
   */
  async getDocumentSignedUrl(documentId: string, expiresIn: number = 3600): Promise<{ url?: string; error?: string }> {
    try {
      console.log(`🔗 Generating signed URL for document: ${documentId}`);

      // Get document information
      const { data: document, error: docError } = await this.serviceClient
        .from('kyc_documents')
        .select('file_path, storage_bucket')
        .eq('id', documentId)
        .single();

      if (docError || !document) {
        console.error('❌ Document not found:', docError);
        return { error: 'Document not found' };
      }

      // Generate signed URL
      const { data, error } = await this.serviceClient.storage
        .from(document.storage_bucket)
        .createSignedUrl(document.file_path, expiresIn);

      if (error) {
        console.error('❌ Error creating signed URL:', error);
        return { error: 'Failed to generate secure URL' };
      }

      console.log('✅ Signed URL generated successfully');
      return { url: data.signedUrl };

    } catch (error) {
      console.error('❌ Error in getDocumentSignedUrl:', error);
      return { error: 'Failed to generate secure URL' };
    }
  }

  /**
   * Log document access for audit trail
   */
  async logDocumentAccess(
    documentId: string,
    accessType: 'view' | 'download' | 'approve' | 'reject',
    userId?: number,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    try {
      console.log(`📝 Logging document access: ${documentId} - ${accessType}`);

      const { error } = await this.serviceClient
        .from('kyc_document_access_log')
        .insert({
          document_id: documentId,
          user_id: userId,
          access_type: accessType,
          ip_address: ipAddress,
          user_agent: userAgent
        });

      if (error) {
        console.error('❌ Error logging document access:', error);
      } else {
        console.log('✅ Document access logged successfully');
      }

    } catch (error) {
      console.error('❌ Error in logDocumentAccess:', error);
    }
  }

  /**
   * Update individual field approval status
   */
  async updateFieldApproval(
    kycId: string,
    fieldName: string,
    status: 'approved' | 'rejected',
    adminEmail: string,
    adminNotes?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`🔄 Updating field approval: ${kycId} -> ${fieldName} -> ${status}`);

      // Upsert field approval record
      const { error } = await this.serviceClient
        .from('kyc_field_approvals')
        .upsert({
          kyc_id: kycId,
          field_name: fieldName,
          approval_status: status,
          admin_email: adminEmail,
          admin_notes: adminNotes,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'kyc_id,field_name'
        });

      if (error) {
        console.error('❌ Error updating field approval:', error);
        return { success: false, error: error.message };
      }

      // Log admin action
      await logAdminAction(
        adminEmail,
        'kyc_field_approval',
        `${status === 'approved' ? 'Approved' : 'Rejected'} KYC field: ${fieldName}`,
        { kycId, fieldName, status, adminNotes }
      );

      console.log(`✅ Field approval updated successfully: ${fieldName} -> ${status}`);
      return { success: true };

    } catch (error) {
      console.error('❌ Error in updateFieldApproval:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Get field approvals for a KYC submission
   */
  async getFieldApprovals(kycId: string): Promise<Record<string, string>> {
    try {
      console.log(`📋 Loading field approvals for KYC: ${kycId}`);

      const { data, error } = await this.serviceClient
        .from('kyc_field_approvals')
        .select('field_name, approval_status')
        .eq('kyc_id', kycId);

      if (error) {
        console.error('❌ Error loading field approvals:', error);
        return {};
      }

      // Convert to object format
      const approvals: Record<string, string> = {};
      data?.forEach(approval => {
        approvals[approval.field_name] = approval.approval_status;
      });

      console.log(`✅ Loaded ${Object.keys(approvals).length} field approvals`);
      return approvals;

    } catch (error) {
      console.error('❌ Error in getFieldApprovals:', error);
      return {};
    }
  }

  /**
   * Update document verification status
   */
  async updateDocumentStatus(
    documentId: string,
    status: 'approved' | 'rejected' | 'requires_resubmission',
    adminEmail: string,
    rejectionReason?: string,
    adminNotes?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`🔄 Updating document status: ${documentId} -> ${status}`);

      const updateData: any = {
        verification_status: status,
        reviewed_by: adminEmail,
        reviewed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      if (rejectionReason) {
        updateData.rejection_reason = rejectionReason;
      }

      if (adminNotes) {
        updateData.admin_notes = adminNotes;
      }

      const { error } = await this.serviceClient
        .from('kyc_documents')
        .update(updateData)
        .eq('id', documentId);

      if (error) {
        console.error('❌ Error updating document status:', error);
        return { success: false, error: error.message };
      }

      // Log the action
      await this.logDocumentAccess(documentId, status === 'approved' ? 'approve' : 'reject');

      console.log('✅ Document status updated successfully');
      return { success: true };

    } catch (error) {
      console.error('❌ Error in updateDocumentStatus:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Get document access history for audit
   */
  async getDocumentAccessHistory(documentId: string): Promise<DocumentAccessLog[]> {
    try {
      console.log(`📋 Fetching access history for document: ${documentId}`);

      const { data: accessLogs, error } = await this.serviceClient
        .from('kyc_document_access_log')
        .select(`
          *,
          users(username, email)
        `)
        .eq('document_id', documentId)
        .order('accessed_at', { ascending: false });

      if (error) {
        console.error('❌ Error fetching document access history:', error);
        throw error;
      }

      console.log(`✅ Found ${accessLogs?.length || 0} access log entries`);
      return accessLogs || [];

    } catch (error) {
      console.error('❌ Error in getDocumentAccessHistory:', error);
      return [];
    }
  }

  /**
   * Enhanced method to get KYC submissions with documents
   */
  async getKYCSubmissionsWithDocuments(
    filters: KYCFilters = {},
    limit: number = 50,
    offset: number = 0
  ): Promise<{ submissions: KYCSubmission[]; total: number }> {
    try {
      // First get the basic submissions
      const result = await this.getKYCSubmissions(filters, limit, offset);

      // Then fetch documents for each submission
      const submissionsWithDocuments = await Promise.all(
        result.submissions.map(async (submission) => {
          const documents = await this.getKYCDocuments(submission.id);
          return {
            ...submission,
            documents
          };
        })
      );

      return {
        submissions: submissionsWithDocuments,
        total: result.total
      };

    } catch (error) {
      console.error('❌ Error in getKYCSubmissionsWithDocuments:', error);
      return { submissions: [], total: 0 };
    }
  }
}

// Export singleton instance
export const kycAdminService = new KYCAdminService();
