/**
 * BASE EMAIL TEMPLATE
 * 
 * Abstract base class for all email templates providing common
 * functionality, branding, and structure.
 */

import { EmailTemplate, EmailType, BaseEmailData } from '../types/EmailTypes';

export abstract class BaseEmailTemplate<T extends BaseEmailData = BaseEmailData> {
  protected readonly brandingConfig = {
    companyName: 'Aureus Alliance Holdings',
    tagline: 'Real Gold • Real Shares • Real Ownership',
    subtitle: 'Building Gold-Backed Impact Ventures Across Africa',
    logoUrl: 'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png',
    websiteUrl: 'https://aureus.africa',
    supportEmail: '<EMAIL>',
    colors: {
      primary: '#d4af37',      // Gold
      secondary: '#0f3460',    // Dark Blue
      background: '#1a1a1a',   // Dark Gray
      surface: '#2a2a2a',      // Medium Gray
      text: '#ffffff',         // White
      textSecondary: '#888888', // Light Gray
      success: '#10b981',      // Green
      warning: '#f59e0b',      // Yellow
      error: '#ef4444',        // Red
      info: '#3b82f6'          // Blue
    }
  };

  protected abstract emailType: EmailType;

  /**
   * Generate the complete email template
   */
  public generateTemplate(data: T): EmailTemplate {
    return {
      type: this.emailType,
      subject: this.generateSubject(data),
      htmlContent: this.generateHtmlContent(data),
      textContent: this.generateTextContent(data)
    };
  }

  /**
   * Generate email subject line
   */
  protected abstract generateSubject(data: T): string;

  /**
   * Generate HTML email content
   */
  protected generateHtmlContent(data: T): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${this.generateSubject(data)}</title>
        <style>
          ${this.getEmailStyles()}
        </style>
      </head>
      <body>
        <div class="email-container">
          ${this.generateHeader()}
          ${this.generateBody(data)}
          ${this.generateFooter()}
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate plain text email content
   */
  protected abstract generateTextContent(data: T): string;

  /**
   * Generate email body content (HTML)
   */
  protected abstract generateBody(data: T): string;

  /**
   * Generate email header with branding
   */
  protected generateHeader(): string {
    return `
      <div class="email-header">
        <div class="header-content">
          <img src="${this.brandingConfig.logoUrl}" alt="${this.brandingConfig.companyName}" class="logo">
          <h1 class="company-name">${this.brandingConfig.companyName}</h1>
        </div>
      </div>
    `;
  }

  /**
   * Generate email footer with company information
   */
  protected generateFooter(): string {
    return `
      <div class="email-footer">
        <div class="footer-content">
          <p class="tagline">${this.brandingConfig.tagline}</p>
          <p class="subtitle">${this.brandingConfig.subtitle}</p>
          <div class="footer-links">
            <a href="${this.brandingConfig.websiteUrl}" class="footer-link">Visit Website</a>
            <a href="${this.brandingConfig.websiteUrl}/support" class="footer-link">Support Center</a>
            <a href="${this.brandingConfig.websiteUrl}/unsubscribe" class="footer-link">Unsubscribe</a>
          </div>
          <p class="copyright">
            © ${new Date().getFullYear()} ${this.brandingConfig.companyName} (Pty) Ltd. All rights reserved.
          </p>
        </div>
      </div>
    `;
  }

  /**
   * Generate CSS styles for email
   */
  protected getEmailStyles(): string {
    return `
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: Arial, sans-serif;
        line-height: 1.6;
        color: ${this.brandingConfig.colors.text};
        background-color: ${this.brandingConfig.colors.background};
      }
      
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        background-color: ${this.brandingConfig.colors.background};
      }
      
      .email-header {
        text-align: center;
        padding: 30px 20px;
        background-color: ${this.brandingConfig.colors.background};
      }
      
      .header-content {
        display: inline-block;
      }
      
      .logo {
        height: 60px;
        width: auto;
        margin-bottom: 10px;
      }
      
      .company-name {
        color: ${this.brandingConfig.colors.primary};
        font-size: 24px;
        font-weight: bold;
        margin: 0;
      }
      
      .email-body {
        background-color: ${this.brandingConfig.colors.surface};
        padding: 25px;
        border-radius: 8px;
        margin: 0 20px 20px;
      }
      
      .email-footer {
        text-align: center;
        padding: 30px 20px;
        background-color: ${this.brandingConfig.colors.background};
        border-top: 1px solid #444;
      }
      
      .footer-content {
        font-size: 14px;
        color: ${this.brandingConfig.colors.textSecondary};
      }
      
      .tagline {
        font-weight: bold;
        margin-bottom: 5px;
      }
      
      .subtitle {
        margin-bottom: 15px;
      }
      
      .footer-links {
        margin: 15px 0;
      }
      
      .footer-link {
        color: ${this.brandingConfig.colors.primary};
        text-decoration: none;
        margin: 0 10px;
        font-size: 12px;
      }
      
      .footer-link:hover {
        text-decoration: underline;
      }
      
      .copyright {
        font-size: 12px;
        margin-top: 10px;
      }
      
      .btn {
        display: inline-block;
        padding: 12px 30px;
        text-decoration: none;
        border-radius: 5px;
        font-weight: bold;
        text-align: center;
        margin: 10px 5px;
      }
      
      .btn-primary {
        background-color: ${this.brandingConfig.colors.primary};
        color: #000000;
      }
      
      .btn-secondary {
        background-color: ${this.brandingConfig.colors.secondary};
        color: ${this.brandingConfig.colors.text};
      }
      
      .btn-success {
        background-color: ${this.brandingConfig.colors.success};
        color: ${this.brandingConfig.colors.text};
      }
      
      .alert {
        padding: 15px;
        border-radius: 8px;
        margin: 20px 0;
        border-left: 4px solid;
      }
      
      .alert-info {
        background-color: rgba(59, 130, 246, 0.1);
        border-color: ${this.brandingConfig.colors.info};
        color: #60a5fa;
      }
      
      .alert-success {
        background-color: rgba(16, 185, 129, 0.1);
        border-color: ${this.brandingConfig.colors.success};
        color: #34d399;
      }
      
      .alert-warning {
        background-color: rgba(245, 158, 11, 0.1);
        border-color: ${this.brandingConfig.colors.warning};
        color: #fbbf24;
      }
      
      .alert-error {
        background-color: rgba(239, 68, 68, 0.1);
        border-color: ${this.brandingConfig.colors.error};
        color: #f87171;
      }
      
      .table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
      }
      
      .table td {
        padding: 8px 0;
        border-bottom: 1px solid #444;
      }
      
      .table .label {
        color: ${this.brandingConfig.colors.textSecondary};
      }
      
      .table .value {
        text-align: right;
        font-weight: bold;
        color: ${this.brandingConfig.colors.text};
      }
      
      .highlight {
        color: ${this.brandingConfig.colors.primary};
        font-weight: bold;
      }
      
      .text-center {
        text-align: center;
      }
      
      .mb-4 {
        margin-bottom: 20px;
      }
      
      .mt-4 {
        margin-top: 20px;
      }
    `;
  }

  /**
   * Utility method to format currency
   */
  protected formatCurrency(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  }

  /**
   * Utility method to format numbers
   */
  protected formatNumber(number: number): string {
    return new Intl.NumberFormat('en-US').format(number);
  }

  /**
   * Utility method to format dates
   */
  protected formatDate(date: Date | string): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  /**
   * Utility method to strip HTML tags for text content
   */
  protected stripHtml(html: string): string {
    return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
  }
}
