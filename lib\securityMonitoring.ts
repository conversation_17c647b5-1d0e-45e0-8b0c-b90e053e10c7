/**
 * REAL-TIME SECURITY MONITORING SYSTEM
 * 
 * This module provides real-time security monitoring, alerting,
 * automated response capabilities, and comprehensive dashboards.
 */

import { supabase } from './supabase';
import { anomalyDetection } from './anomalyDetection';
import { threatIntelligence } from './threatIntelligence';

interface SecurityAlert {
  id: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  type: string;
  title: string;
  description: string;
  source: string;
  timestamp: Date;
  metadata: any;
  resolved: boolean;
  autoResolved: boolean;
  responseActions: string[];
}

interface SecurityMetrics {
  totalEvents: number;
  criticalAlerts: number;
  highAlerts: number;
  mediumAlerts: number;
  lowAlerts: number;
  resolvedAlerts: number;
  activeThreats: number;
  blockedIPs: number;
  suspiciousActivities: number;
  systemHealth: 'HEALTHY' | 'WARNING' | 'CRITICAL';
}

interface AutomatedResponse {
  trigger: string;
  action: string;
  parameters: any;
  executed: boolean;
  timestamp: Date;
  result: string;
}

class SecurityMonitoringSystem {
  private alerts: Map<string, SecurityAlert> = new Map();
  private metrics: SecurityMetrics = this.initializeMetrics();
  private blockedIPs: Set<string> = new Set();
  private suspiciousUsers: Set<number> = new Set();
  
  private readonly ALERT_THRESHOLDS = {
    FAILED_LOGINS_PER_HOUR: 10,
    ANOMALY_SCORE_CRITICAL: 85,
    THREAT_LEVEL_CRITICAL: 'CRITICAL',
    RAPID_REQUESTS_PER_MINUTE: 100,
    SUSPICIOUS_TRANSACTIONS_PER_HOUR: 5
  };

  /**
   * Initialize real-time monitoring
   */
  async initializeMonitoring(): Promise<void> {
    try {
      console.log('🔍 Initializing real-time security monitoring...');

      // Start monitoring loops
      this.startEventMonitoring();
      this.startMetricsCollection();
      this.startAutomatedResponse();
      this.startHealthChecks();

      // Load existing blocked IPs
      await this.loadBlockedIPs();

      console.log('✅ Real-time security monitoring initialized');

    } catch (error) {
      console.error('❌ Failed to initialize security monitoring:', error);
    }
  }

  /**
   * Process security event in real-time
   */
  async processSecurityEvent(event: {
    type: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    source: string;
    data: any;
    userId?: number;
    ipAddress?: string;
  }): Promise<void> {
    try {
      console.log(`🚨 Processing security event: ${event.type} (${event.severity})`);

      // Update metrics
      this.updateMetrics(event);

      // Check if this triggers an alert
      const shouldAlert = await this.shouldTriggerAlert(event);
      
      if (shouldAlert) {
        const alert = await this.createAlert(event);
        await this.processAlert(alert);
      }

      // Check for automated response triggers
      await this.checkAutomatedResponseTriggers(event);

      // Log the event
      await this.logSecurityEvent(event);

    } catch (error) {
      console.error('❌ Error processing security event:', error);
    }
  }

  /**
   * Create security alert
   */
  private async createAlert(event: any): Promise<SecurityAlert> {
    const alertId = `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const alert: SecurityAlert = {
      id: alertId,
      severity: event.severity,
      type: event.type,
      title: this.generateAlertTitle(event),
      description: this.generateAlertDescription(event),
      source: event.source,
      timestamp: new Date(),
      metadata: event.data,
      resolved: false,
      autoResolved: false,
      responseActions: []
    };

    this.alerts.set(alertId, alert);
    
    // Store in database
    await supabase
      .from('security_alerts')
      .insert({
        alert_id: alertId,
        severity: alert.severity,
        type: alert.type,
        title: alert.title,
        description: alert.description,
        source: alert.source,
        metadata: alert.metadata,
        resolved: false,
        created_at: alert.timestamp.toISOString()
      });

    console.log(`🚨 Alert created: ${alert.title} (${alert.severity})`);
    return alert;
  }

  /**
   * Process alert and determine response
   */
  private async processAlert(alert: SecurityAlert): Promise<void> {
    try {
      // Immediate response for critical alerts
      if (alert.severity === 'CRITICAL') {
        await this.handleCriticalAlert(alert);
      }

      // Send notifications
      await this.sendAlertNotifications(alert);

      // Update dashboard
      await this.updateSecurityDashboard();

    } catch (error) {
      console.error('❌ Error processing alert:', error);
    }
  }

  /**
   * Handle critical security alerts
   */
  private async handleCriticalAlert(alert: SecurityAlert): Promise<void> {
    try {
      console.log(`🚨 Handling critical alert: ${alert.title}`);

      const responses: string[] = [];

      // Auto-block IP if it's an attack
      if (alert.metadata.ipAddress && this.isAttackAlert(alert)) {
        await this.blockIP(alert.metadata.ipAddress, 'Critical security alert');
        responses.push('IP_BLOCKED');
      }

      // Suspend user if suspicious activity
      if (alert.metadata.userId && this.isSuspiciousUserAlert(alert)) {
        await this.flagSuspiciousUser(alert.metadata.userId);
        responses.push('USER_FLAGGED');
      }

      // Escalate to admin
      await this.escalateToAdmin(alert);
      responses.push('ESCALATED_TO_ADMIN');

      alert.responseActions = responses;
      await this.updateAlert(alert);

    } catch (error) {
      console.error('❌ Error handling critical alert:', error);
    }
  }

  /**
   * Block IP address
   */
  async blockIP(ipAddress: string, reason: string): Promise<void> {
    try {
      console.log(`🚫 Blocking IP address: ${ipAddress} (${reason})`);

      this.blockedIPs.add(ipAddress);

      // Store in database
      await supabase
        .from('blocked_ips')
        .insert({
          ip_address: ipAddress,
          reason,
          blocked_at: new Date().toISOString(),
          is_active: true
        });

      // Log the action
      await this.logSecurityAction('IP_BLOCKED', {
        ipAddress,
        reason,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('❌ Error blocking IP:', error);
    }
  }

  /**
   * Check if IP is blocked
   */
  isIPBlocked(ipAddress: string): boolean {
    return this.blockedIPs.has(ipAddress);
  }

  /**
   * Flag suspicious user
   */
  private async flagSuspiciousUser(userId: number): Promise<void> {
    try {
      console.log(`⚠️ Flagging suspicious user: ${userId}`);

      this.suspiciousUsers.add(userId);

      // Store in database
      await supabase
        .from('suspicious_users')
        .insert({
          user_id: userId,
          flagged_at: new Date().toISOString(),
          is_active: true,
          reason: 'Automated security system detection'
        });

      // Log the action
      await this.logSecurityAction('USER_FLAGGED', {
        userId,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('❌ Error flagging user:', error);
    }
  }

  /**
   * Start event monitoring loop
   */
  private startEventMonitoring(): void {
    setInterval(async () => {
      try {
        await this.monitorFailedLogins();
        await this.monitorAnomalousActivity();
        await this.monitorSuspiciousTransactions();
        await this.monitorRapidRequests();
      } catch (error) {
        console.error('❌ Event monitoring error:', error);
      }
    }, 60 * 1000); // Every minute
  }

  /**
   * Monitor failed login attempts
   */
  private async monitorFailedLogins(): Promise<void> {
    try {
      const { data: failedLogins } = await supabase
        .from('admin_audit_logs')
        .select('*')
        .like('action', '%FAILED%')
        .gte('created_at', new Date(Date.now() - 60 * 60 * 1000).toISOString());

      if (failedLogins && failedLogins.length > this.ALERT_THRESHOLDS.FAILED_LOGINS_PER_HOUR) {
        await this.processSecurityEvent({
          type: 'EXCESSIVE_FAILED_LOGINS',
          severity: 'HIGH',
          source: 'LOGIN_MONITOR',
          data: {
            failedLoginCount: failedLogins.length,
            timeWindow: '1 hour'
          }
        });
      }

    } catch (error) {
      console.error('❌ Failed login monitoring error:', error);
    }
  }

  /**
   * Monitor anomalous activity
   */
  private async monitorAnomalousActivity(): Promise<void> {
    try {
      const { data: anomalies } = await supabase
        .from('admin_audit_logs')
        .select('*')
        .like('action', 'ANOMALY_DETECTED_%')
        .gte('created_at', new Date(Date.now() - 60 * 60 * 1000).toISOString());

      if (anomalies) {
        for (const anomaly of anomalies) {
          const score = anomaly.metadata?.score || 0;
          
          if (score >= this.ALERT_THRESHOLDS.ANOMALY_SCORE_CRITICAL) {
            await this.processSecurityEvent({
              type: 'CRITICAL_ANOMALY',
              severity: 'CRITICAL',
              source: 'ANOMALY_DETECTOR',
              data: anomaly.metadata,
              userId: parseInt(anomaly.target_id),
              ipAddress: anomaly.metadata?.ipAddress
            });
          }
        }
      }

    } catch (error) {
      console.error('❌ Anomaly monitoring error:', error);
    }
  }

  /**
   * Start metrics collection
   */
  private startMetricsCollection(): void {
    setInterval(async () => {
      try {
        await this.collectSecurityMetrics();
        await this.updateSystemHealth();
      } catch (error) {
        console.error('❌ Metrics collection error:', error);
      }
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  /**
   * Collect security metrics
   */
  private async collectSecurityMetrics(): Promise<void> {
    try {
      const { data: events } = await supabase
        .from('admin_audit_logs')
        .select('action, metadata')
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

      const { data: alerts } = await supabase
        .from('security_alerts')
        .select('severity, resolved')
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

      this.metrics = {
        totalEvents: events?.length || 0,
        criticalAlerts: alerts?.filter(a => a.severity === 'CRITICAL').length || 0,
        highAlerts: alerts?.filter(a => a.severity === 'HIGH').length || 0,
        mediumAlerts: alerts?.filter(a => a.severity === 'MEDIUM').length || 0,
        lowAlerts: alerts?.filter(a => a.severity === 'LOW').length || 0,
        resolvedAlerts: alerts?.filter(a => a.resolved).length || 0,
        activeThreats: alerts?.filter(a => !a.resolved && (a.severity === 'HIGH' || a.severity === 'CRITICAL')).length || 0,
        blockedIPs: this.blockedIPs.size,
        suspiciousActivities: events?.filter(e => e.action.includes('SUSPICIOUS')).length || 0,
        systemHealth: this.calculateSystemHealth()
      };

    } catch (error) {
      console.error('❌ Metrics collection error:', error);
    }
  }

  /**
   * Calculate system health
   */
  private calculateSystemHealth(): 'HEALTHY' | 'WARNING' | 'CRITICAL' {
    if (this.metrics.criticalAlerts > 5 || this.metrics.activeThreats > 10) {
      return 'CRITICAL';
    }
    
    if (this.metrics.highAlerts > 10 || this.metrics.activeThreats > 5) {
      return 'WARNING';
    }
    
    return 'HEALTHY';
  }

  /**
   * Get current security metrics
   */
  getSecurityMetrics(): SecurityMetrics {
    return { ...this.metrics };
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(): SecurityAlert[] {
    return Array.from(this.alerts.values()).filter(alert => !alert.resolved);
  }

  /**
   * Helper methods
   */
  private initializeMetrics(): SecurityMetrics {
    return {
      totalEvents: 0,
      criticalAlerts: 0,
      highAlerts: 0,
      mediumAlerts: 0,
      lowAlerts: 0,
      resolvedAlerts: 0,
      activeThreats: 0,
      blockedIPs: 0,
      suspiciousActivities: 0,
      systemHealth: 'HEALTHY'
    };
  }

  private updateMetrics(event: any): void {
    this.metrics.totalEvents++;
    
    switch (event.severity) {
      case 'CRITICAL':
        this.metrics.criticalAlerts++;
        break;
      case 'HIGH':
        this.metrics.highAlerts++;
        break;
      case 'MEDIUM':
        this.metrics.mediumAlerts++;
        break;
      case 'LOW':
        this.metrics.lowAlerts++;
        break;
    }
  }

  private async shouldTriggerAlert(event: any): Promise<boolean> {
    // Define alert triggers based on event type and severity
    const alertTriggers = [
      'FAILED_LOGIN',
      'ANOMALY_DETECTED',
      'THREAT_DETECTED',
      'SUSPICIOUS_TRANSACTION',
      'BRUTE_FORCE_ATTEMPT',
      'MALICIOUS_IP'
    ];

    return alertTriggers.some(trigger => event.type.includes(trigger)) ||
           event.severity === 'CRITICAL' ||
           event.severity === 'HIGH';
  }

  private generateAlertTitle(event: any): string {
    const titles = {
      'FAILED_LOGIN': 'Failed Login Attempt',
      'ANOMALY_DETECTED': 'Anomalous Activity Detected',
      'THREAT_DETECTED': 'Security Threat Identified',
      'SUSPICIOUS_TRANSACTION': 'Suspicious Financial Transaction',
      'BRUTE_FORCE_ATTEMPT': 'Brute Force Attack Detected',
      'MALICIOUS_IP': 'Malicious IP Address Detected'
    };

    for (const [key, title] of Object.entries(titles)) {
      if (event.type.includes(key)) {
        return title;
      }
    }

    return `Security Event: ${event.type}`;
  }

  private generateAlertDescription(event: any): string {
    return `Security event of type ${event.type} detected from ${event.source}. Severity: ${event.severity}`;
  }

  private isAttackAlert(alert: SecurityAlert): boolean {
    const attackTypes = ['BRUTE_FORCE', 'MALICIOUS_IP', 'THREAT_DETECTED'];
    return attackTypes.some(type => alert.type.includes(type));
  }

  private isSuspiciousUserAlert(alert: SecurityAlert): boolean {
    const suspiciousTypes = ['ANOMALY_DETECTED', 'SUSPICIOUS_TRANSACTION'];
    return suspiciousTypes.some(type => alert.type.includes(type));
  }

  private async loadBlockedIPs(): Promise<void> {
    try {
      const { data: blockedIPs } = await supabase
        .from('blocked_ips')
        .select('ip_address')
        .eq('is_active', true);

      if (blockedIPs) {
        blockedIPs.forEach(ip => this.blockedIPs.add(ip.ip_address));
      }

    } catch (error) {
      console.error('❌ Error loading blocked IPs:', error);
    }
  }

  private async logSecurityEvent(event: any): Promise<void> {
    try {
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_email: 'security_monitoring_system',
          action: `SECURITY_EVENT_${event.type}`,
          target_type: 'security_monitoring',
          target_id: event.userId?.toString() || 'system',
          metadata: {
            ...event.data,
            severity: event.severity,
            source: event.source,
            ipAddress: event.ipAddress,
            timestamp: new Date().toISOString()
          },
          created_at: new Date().toISOString()
        });

    } catch (error) {
      console.error('❌ Failed to log security event:', error);
    }
  }

  private async logSecurityAction(action: string, metadata: any): Promise<void> {
    try {
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_email: 'security_monitoring_system',
          action: `SECURITY_ACTION_${action}`,
          target_type: 'security_action',
          target_id: 'automated',
          metadata: {
            ...metadata,
            automated: true
          },
          created_at: new Date().toISOString()
        });

    } catch (error) {
      console.error('❌ Failed to log security action:', error);
    }
  }

  // Placeholder methods for additional functionality
  private startAutomatedResponse(): void {
    // Implementation for automated response system
  }

  private startHealthChecks(): void {
    // Implementation for system health monitoring
  }

  private async checkAutomatedResponseTriggers(event: any): Promise<void> {
    // Implementation for automated response triggers
  }

  private async sendAlertNotifications(alert: SecurityAlert): Promise<void> {
    // Implementation for alert notifications
  }

  private async updateSecurityDashboard(): Promise<void> {
    // Implementation for dashboard updates
  }

  private async escalateToAdmin(alert: SecurityAlert): Promise<void> {
    // Implementation for admin escalation
  }

  private async updateAlert(alert: SecurityAlert): Promise<void> {
    // Implementation for alert updates
  }

  private async updateSystemHealth(): Promise<void> {
    // Implementation for system health updates
  }

  private async monitorSuspiciousTransactions(): Promise<void> {
    // Implementation for transaction monitoring
  }

  private async monitorRapidRequests(): Promise<void> {
    // Implementation for rapid request monitoring
  }
}

// Create singleton instance
export const securityMonitoring = new SecurityMonitoringSystem();

export default securityMonitoring;
