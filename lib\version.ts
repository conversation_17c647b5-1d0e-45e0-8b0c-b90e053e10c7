// Application Version System
// Update this version number with every deployment to track caching issues

export const APP_VERSION = "2.5.0";
export const BUILD_DATE = "2025-09-10";
export const BUILD_TIME = "00:25";

// Version history (keep last 10 versions for reference)
export const VERSION_HISTORY = [
  { version: "2.4.56", date: "2025-09-10", changes: "LOGO INTEGRATION: Added Aureus logo to PDF exports - both Transaction Report and Tax Summary now display the company logo instead of text-only header" },
  { version: "2.4.55", date: "2025-09-10", changes: "PDF EXPORT FIX: Removed green tax reporting button, implemented proper PDF generation using jsPDF for both PDF Report and Tax Summary exports" },
  { version: "2.4.54", date: "2025-09-10", changes: "CRITICAL FIXES: Fixed transaction export variable references, React infinite loop with useCallback, and JSX attribute warning" },
  { version: "2.4.53", date: "2025-09-10", changes: "TRANSACTION EXPORTS FIX: Added missing export functionality for PDF Report, Excel Export, and Tax Summary in portfolio transactions tab" },
  { version: "2.4.52", date: "2025-09-10", changes: "KYC ERROR FIX: Fixed base64 decoding error in KYCStatusCard component with proper error handling for encrypted ID numbers" },
  { version: "2.4.51", date: "2025-09-10", changes: "CURRENCY SELECTION FIX: Fixed country detection logic to properly identify South Africa/Namibia/Eswatini users for currency selection" },
  { version: "2.4.50", date: "2025-09-10", changes: "PURCHASE ENHANCEMENT: Added currency selection for South Africa/Namibia/Eswatini - USDT (USD) vs Bank Transfer (ZAR) with exchange rate conversion" },
  { version: "2.4.49", date: "2025-09-10", changes: "PORTFOLIO ENHANCEMENT: Added interactive calculator controls - Target Year, Land Size, Gold Price adjustments" },
  { version: "2.4.48", date: "2025-09-10", changes: "PORTFOLIO FIX: Projected Annual Dividend now synchronized with dashboard calculator - dynamic gold price integration" },
  { version: "2.4.47", date: "2025-09-10", changes: "DYNAMIC FIX: Future Dividends now auto-calculates with Dividend Calculator - real-time gold price synchronization" },
  { version: "2.4.46", date: "2025-09-10", changes: "CRITICAL FIX: Future Dividends calculation - synchronized gold price with Dividend Calculator (100000 → 109026)" },
  { version: "2.4.45", date: "2025-09-10", changes: "NEW FEATURE: Telegram User Reset system - secure password and email reset for Telegram users with comprehensive validation" },
  { version: "2.4.44", date: "2025-09-10", changes: "CRITICAL FIX: Mobile USDT payment processing - route mobile crypto payments to mobile-crypto-upload API" },
  { version: "2.4.43", date: "2025-09-10", changes: "HOTFIX: iOS Safari Purchase Shares navigation bug - comprehensive solution with mobile-specific fixes" },
  { version: "2.5.0", date: "2025-09-10", changes: "MAJOR CACHE BUST: Added timestamp to build files, no-cache headers, and version 2.5.0 to force complete cache invalidation" },
  { version: "2.4.38", date: "2025-09-10", changes: "FINAL FIX: Fixed ALL remaining localhost URLs in components - password reset, telegram link, password change" },
  { version: "2.4.37", date: "2025-09-10", changes: "CACHE BUST: Force browser cache invalidation to load fixed API URLs" },
  { version: "2.4.36", date: "2025-09-10", changes: "CRITICAL FIX: Deployed complete working local setup to Vercel with all API routes functional" },
  { version: "2.4.35", date: "2025-09-09", changes: "Fixed server.js API directory path to pages/api for proper route loading" },
  { version: "2.4.34", date: "2025-09-09", changes: "Fixed JSON parsing error in mobile auth manager - added proper error handling for localStorage data" },
  { version: "2.4.33", date: "2025-09-09", changes: "CRITICAL FIX: Removed problematic mobile auth redirect flag that was blocking login page access" },
  { version: "2.4.32", date: "2025-09-09", changes: "Added comprehensive debug logging to track mobile routing issues with Purchase Shares button" },
  { version: "2.4.31", date: "2025-09-09", changes: "CRITICAL FIX: Mobile login page regression - fixed aggressive redirect logic preventing login access" },
  { version: "2.4.30", date: "2025-09-09", changes: "Added copy button to second debug log section that was missing it" },
  { version: "2.4.29", date: "2025-09-09", changes: "Enhanced debug logging throughout authentication process to identify failure points" },
  { version: "2.4.28", date: "2025-09-09", changes: "Added copy button to debug log and fixed visibility check activation timing" },
  { version: "2.4.27", date: "2025-09-09", changes: "Enhanced mobile debug system with visibility change testing and always-visible debug log" },
  { version: "2.4.26", date: "2025-09-09", changes: "Added visible mobile debug log display for troubleshooting authentication flow" },
  { version: "2.4.25", date: "2025-09-09", changes: "Fixed mobile migration state variable and added comprehensive logging for debugging" },
  { version: "2.4.24", date: "2025-09-09", changes: "Fixed mobile authentication processing - direct token handling without double polling" },
  { version: "2.4.23", date: "2025-09-09", changes: "Fixed mobile app switching issue - prevent page reload during migration context and improve session persistence" },
  { version: "2.4.22", date: "2025-09-09", changes: "Fixed migration API data format and JSON parsing errors - proper error handling and data structure" },
  { version: "2.4.21", date: "2025-09-09", changes: "Fixed text input styling in migration form - ensure all user input text is black and readable" },
  { version: "2.4.20", date: "2025-09-09", changes: "Fixed password visibility toggle in migration form - replaced emoji icons with proper SVG icons" },
  { version: "2.4.19", date: "2025-09-09", changes: "Fixed Telegram authentication URL display - use window.location.href instead of pushState for proper navigation" },
  { version: "2.4.18", date: "2025-09-09", changes: "Fixed build error - renamed duplicate serviceClient variables to avoid conflicts" },
  { version: "2.4.17", date: "2025-09-09", changes: "Fixed build error - removed duplicate getServiceRoleClient declarations" },
  { version: "2.4.16", date: "2025-09-09", changes: "Fixed Telegram authentication database queries - use service role client to bypass RLS" },
  { version: "2.4.15", date: "2025-09-09", changes: "Fixed URL display issue in Telegram authentication - simplified navigation logic" },
  { version: "2.4.14", date: "2025-09-09", changes: "Fixed Telegram authentication redirect flow - improved navigation and mobile compatibility" },
  { version: "2.4.13", date: "2025-09-09", changes: "Fixed Next.js imports causing build failures - converted to Vite React patterns" },
  { version: "2.4.12", date: "2025-09-09", changes: "Fixed mobile Telegram-to-Website migration redirect issue by adding proper routing for migration pages" },
  { version: "2.4.2", date: "2025-09-07", changes: "Fixed date error, added version system to all pages, fixed profile picture cache issue" },
  { version: "2.4.1", date: "2025-09-07", changes: "Fixed profile picture upload, username propagation, and User 139 share commissions" },
  { version: "2.4.0", date: "2025-09-06", changes: "Removed red debug box from dashboard" },
  { version: "2.3.9", date: "2025-09-05", changes: "Dashboard switcher improvements" },
  { version: "2.3.8", date: "2025-09-04", changes: "Commission system enhancements" },
  { version: "2.3.7", date: "2025-09-03", changes: "User interface improvements" },
  { version: "2.3.6", date: "2025-09-02", changes: "Bug fixes and optimizations" },
  { version: "2.3.5", date: "2025-09-01", changes: "September updates" },
  { version: "2.3.4", date: "2025-08-31", changes: "End of August improvements" },
  { version: "2.3.3", date: "2025-08-30", changes: "Performance optimizations" },
  { version: "2.3.2", date: "2025-08-29", changes: "Security enhancements" }
];

// Helper function to get full version string
export const getFullVersion = () => {
  return `v${APP_VERSION} (${BUILD_DATE} ${BUILD_TIME})`;
};

// Helper function to check if user might have caching issues
export const getCacheInfo = () => {
  return {
    version: APP_VERSION,
    buildDate: BUILD_DATE,
    buildTime: BUILD_TIME,
    fullVersion: getFullVersion(),
    timestamp: Date.now()
  };
};
