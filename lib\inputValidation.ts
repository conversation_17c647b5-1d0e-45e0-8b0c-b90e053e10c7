/**
 * COMPREHENSIVE INPUT VALIDATION & SANITIZATION
 * 
 * This module provides secure input validation and sanitization
 * to prevent XSS, SQL injection, and other input-based attacks.
 */

import { z } from 'zod';

/**
 * Sanitize string input to prevent XSS and injection attacks
 */
export const sanitizeInput = (input: string): string => {
  if (typeof input !== 'string') {
    return '';
  }

  return input
    // Remove HTML tags and scripts
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<[^>]*>/g, '')
    // Remove dangerous characters
    .replace(/[<>{}$\x00-\x1f]/g, '')
    // Remove SQL injection patterns
    .replace(/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|UNION)\b)/gi, '')
    .replace(/['"]\s*(OR|AND)\s*['"]\d+['"]\s*=\s*['"]\d+['"]*/gi, '')
    .replace(/(;|--|\|\||&&)/g, '')
    // Remove JavaScript protocols
    .replace(/javascript:/gi, '')
    // Trim whitespace
    .trim();
};

/**
 * Sanitize object recursively
 */
export const sanitizeObject = (obj: any): any => {
  if (typeof obj === 'string') {
    return sanitizeInput(obj);
  }

  if (Array.isArray(obj)) {
    return obj.map(sanitizeObject);
  }

  if (obj && typeof obj === 'object') {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      sanitized[sanitizeInput(key)] = sanitizeObject(value);
    }
    return sanitized;
  }

  return obj;
};

/**
 * Validation schemas for different input types
 */
export const ValidationSchemas = {
  // Email validation
  email: z.string()
    .email('Invalid email format')
    .max(255, 'Email too long')
    .refine(
      (email) => !email.includes('<script>') && !email.includes('<') && !email.includes('>'),
      'Invalid email format'
    ),

  // Password validation
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password too long')
    .regex(/^(?=.*[a-z])/, 'Password must contain at least one lowercase letter')
    .regex(/^(?=.*[A-Z])/, 'Password must contain at least one uppercase letter')
    .regex(/^(?=.*\d)/, 'Password must contain at least one number')
    .regex(/^(?=.*[!@#$%^&*(),.?":{}|<>])/, 'Password must contain at least one special character'),

  // Username validation
  username: z.string()
    .min(2, 'Username must be at least 2 characters')
    .max(50, 'Username too long')
    .regex(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores'),

  // Full name validation
  fullName: z.string()
    .min(1, 'Full name is required')
    .max(100, 'Full name too long')
    .regex(/^[a-zA-Z\s\-']+$/, 'Full name can only contain letters, spaces, hyphens, and apostrophes'),

  // Phone number validation
  phone: z.string()
    .regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format')
    .max(20, 'Phone number too long'),

  // Telegram ID validation
  telegramId: z.union([
    z.string().regex(/^\d+$/, 'Telegram ID must be numeric').transform(Number),
    z.number().int().positive('Telegram ID must be a positive integer')
  ]),

  // Amount validation (financial)
  amount: z.number()
    .min(0.01, 'Amount must be greater than 0')
    .max(100000, 'Amount too large')
    .refine((val) => Number.isFinite(val), 'Invalid amount'),

  // Wallet address validation
  walletAddress: z.string()
    .regex(/^0x[a-fA-F0-9]{40}$/, 'Invalid wallet address format')
    .refine((addr) => addr !== '******************************************', 'Invalid wallet address'),

  // Transaction hash validation
  transactionHash: z.string()
    .regex(/^0x[a-fA-F0-9]{64}$/, 'Invalid transaction hash format'),

  // Country code validation
  countryCode: z.string()
    .length(3, 'Country code must be 3 characters')
    .regex(/^[A-Z]{3}$/, 'Invalid country code format'),
};

/**
 * Comprehensive validation schemas for API endpoints
 */
export const UserRegistrationSchema = z.object({
  username: ValidationSchemas.username,
  email: ValidationSchemas.email,
  password: ValidationSchemas.password,
  full_name: ValidationSchemas.fullName,
  country: z.string()
    .min(2, 'Country must be at least 2 characters')
    .max(100, 'Country must be less than 100 characters'),
  sponsor_username: z.string().optional()
});

export const ContactSchema = z.object({
  name: z.string()
    .min(1, 'Name is required')
    .max(50, 'Name must be less than 50 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'Name can only contain letters, spaces, hyphens, and apostrophes'),
  surname: z.string()
    .min(1, 'Surname is required')
    .max(50, 'Surname must be less than 50 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'Surname can only contain letters, spaces, hyphens, and apostrophes'),
  email: ValidationSchemas.email,
  message: z.string()
    .min(10, 'Message must be at least 10 characters')
    .max(2000, 'Message must be less than 2000 characters')
});

export const PaymentSchema = z.object({
  amount: z.number()
    .min(25, 'Minimum amount is $25')
    .max(10000, 'Maximum amount is $10,000'),
  shares_to_purchase: z.number()
    .int('Shares must be a whole number')
    .min(1, 'Must purchase at least 1 share')
    .max(400, 'Maximum 400 shares per transaction'),
  network: z.enum(['BSC', 'POLYGON', 'TRON', 'ETH'], {
    errorMap: () => ({ message: 'Invalid network' })
  }),
  currency: z.string()
    .min(2, 'Currency must be specified')
    .max(10, 'Invalid currency format'),
  sender_wallet: z.string()
    .min(26, 'Invalid wallet address')
    .max(100, 'Wallet address too long')
    .regex(/^[a-zA-Z0-9]+$/, 'Wallet address contains invalid characters'),
  receiver_wallet: z.string()
    .min(26, 'Invalid wallet address')
    .max(100, 'Wallet address too long')
    .regex(/^[a-zA-Z0-9]+$/, 'Wallet address contains invalid characters'),
  transaction_hash: z.string()
    .min(40, 'Invalid transaction hash')
    .max(100, 'Transaction hash too long')
    .regex(/^[a-zA-Z0-9]+$/, 'Transaction hash contains invalid characters'),
  screenshot_url: z.string()
    .url('Invalid screenshot URL')
    .optional(),
  transaction_notes: z.string()
    .max(500, 'Transaction notes must be less than 500 characters')
    .optional(),
  status: z.enum(['pending', 'confirmed', 'rejected']).optional()
});

// Removed duplicate validateRequest function - using the one below at line 294

  // Network validation
  network: z.enum(['ETH', 'BSC', 'POL', 'TRON'], {
    errorMap: () => ({ message: 'Invalid network' })
  }),

  // Currency validation
  currency: z.enum(['USDT', 'USD'], {
    errorMap: () => ({ message: 'Invalid currency' })
  })
};

/**
 * Registration form validation schema
 */
export const RegistrationSchema = z.object({
  email: ValidationSchemas.email,
  password: ValidationSchemas.password,
  confirmPassword: z.string(),
  fullName: ValidationSchemas.fullName,
  username: ValidationSchemas.username.optional(),
  phone: ValidationSchemas.phone,
  countryOfResidence: ValidationSchemas.countryCode,
  telegramId: ValidationSchemas.telegramId.optional(),
  sponsorUsername: z.string().max(50).optional(),
  hasTelegramAccount: z.boolean().optional()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
});

/**
 * Login form validation schema
 */
export const LoginSchema = z.object({
  email: ValidationSchemas.email.optional(),
  password: ValidationSchemas.password.optional(),
  telegramId: ValidationSchemas.telegramId.optional()
}).refine((data) => {
  return (data.email && data.password) || data.telegramId;
}, {
  message: "Either email/password or Telegram ID is required"
});

/**
 * Payment form validation schema
 */
export const PaymentSchema = z.object({
  amount: ValidationSchemas.amount,
  shares_to_purchase: z.number().int().min(1).max(10000),
  network: ValidationSchemas.network,
  currency: ValidationSchemas.currency,
  sender_wallet: ValidationSchemas.walletAddress,
  receiver_wallet: ValidationSchemas.walletAddress,
  transaction_hash: ValidationSchemas.transactionHash,
  screenshot_url: z.string().url().optional()
});

/**
 * Commission adjustment validation schema
 */
export const CommissionAdjustmentSchema = z.object({
  userId: z.number().int().positive(),
  usdtAdjustment: z.number().min(-10000).max(10000),
  shareAdjustment: z.number().min(-10000).max(10000),
  reason: z.string().min(10, 'Reason must be at least 10 characters').max(500)
});

/**
 * Validate and sanitize request data
 */
export const validateRequest = <T>(schema: z.ZodSchema<T>) => {
  return (data: unknown): { success: boolean; data?: T; errors?: string[] } => {
    try {
      // First sanitize the input
      const sanitizedData = sanitizeObject(data);
      
      // Then validate with schema
      const result = schema.safeParse(sanitizedData);
      
      if (result.success) {
        return { success: true, data: result.data };
      } else {
        const errors = result.error.errors.map(err => 
          `${err.path.join('.')}: ${err.message}`
        );
        return { success: false, errors };
      }
    } catch (error) {
      return { success: false, errors: ['Validation error occurred'] };
    }
  };
};

/**
 * Middleware for request validation
 */
export const createValidationMiddleware = <T>(schema: z.ZodSchema<T>) => {
  return (req: any, res: any, next: any) => {
    const validation = validateRequest(schema)(req.body);
    
    if (!validation.success) {
      return res.status(400).json({
        error: 'Validation failed',
        details: validation.errors
      });
    }
    
    // Replace request body with validated and sanitized data
    req.body = validation.data;
    next();
  };
};

/**
 * Check for malicious patterns in input
 */
export const detectMaliciousInput = (input: string): {
  isMalicious: boolean;
  patterns: string[];
} => {
  const maliciousPatterns = [
    // Enhanced SQL injection patterns
    { pattern: /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|TRUNCATE|EXEC|EXECUTE)\b)/i, name: 'SQL_INJECTION' },
    { pattern: /(UNION|OR|AND)\s+\d+\s*=\s*\d+/i, name: 'SQL_INJECTION' },
    { pattern: /['"]\s*(OR|AND)\s*['"]\d+['"]\s*=\s*['"]\d+['"]*/i, name: 'SQL_INJECTION' },
    { pattern: /['"][\s]*;[\s]*--/i, name: 'SQL_INJECTION' },
    { pattern: /['"][\s]*;[\s]*\/\*/i, name: 'SQL_INJECTION' },
    { pattern: /\b(WAITFOR|DELAY|SLEEP|BENCHMARK)\b/i, name: 'SQL_INJECTION' },
    { pattern: /\b(INFORMATION_SCHEMA|SYSOBJECTS|SYSCOLUMNS)\b/i, name: 'SQL_INJECTION' },
    { pattern: /\b(CAST|CONVERT|CHAR|ASCII|SUBSTRING)\s*\(/i, name: 'SQL_INJECTION' },
    { pattern: /\b(LOAD_FILE|INTO\s+OUTFILE|INTO\s+DUMPFILE)\b/i, name: 'SQL_INJECTION' },
    { pattern: /\b(sp_|xp_|cmdshell)\b/i, name: 'SQL_INJECTION' },

    // XSS patterns
    { pattern: /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, name: 'XSS' },
    { pattern: /javascript:/i, name: 'XSS' },
    { pattern: /on\w+\s*=/i, name: 'XSS' },
    { pattern: /<iframe/i, name: 'XSS' },
    { pattern: /<object/i, name: 'XSS' },
    { pattern: /<embed/i, name: 'XSS' },
    { pattern: /<link/i, name: 'XSS' },
    { pattern: /<meta/i, name: 'XSS' },
    { pattern: /vbscript:/i, name: 'XSS' },
    { pattern: /data:text\/html/i, name: 'XSS' },

    // Path traversal
    { pattern: /\.\.\//g, name: 'PATH_TRAVERSAL' },
    { pattern: /\.\.\\/g, name: 'PATH_TRAVERSAL' },
    { pattern: /\.\.%2f/gi, name: 'PATH_TRAVERSAL' },
    { pattern: /\.\.%5c/gi, name: 'PATH_TRAVERSAL' },

    // Command injection
    { pattern: /[;&|`$]/g, name: 'COMMAND_INJECTION' },
    { pattern: /\b(cat|ls|dir|type|copy|move|del|rm|mkdir|rmdir)\b/i, name: 'COMMAND_INJECTION' },
    { pattern: /\b(wget|curl|nc|netcat|telnet|ssh)\b/i, name: 'COMMAND_INJECTION' },

    // LDAP injection
    { pattern: /[()=*!&|]/g, name: 'LDAP_INJECTION' },

    // NoSQL injection
    { pattern: /\$where/i, name: 'NOSQL_INJECTION' },
    { pattern: /\$ne/i, name: 'NOSQL_INJECTION' },
    { pattern: /\$gt/i, name: 'NOSQL_INJECTION' },
    { pattern: /\$regex/i, name: 'NOSQL_INJECTION' }
  ];

  const detectedPatterns: string[] = [];
  
  for (const { pattern, name } of maliciousPatterns) {
    if (pattern.test(input)) {
      detectedPatterns.push(name);
    }
  }

  return {
    isMalicious: detectedPatterns.length > 0,
    patterns: detectedPatterns
  };
};

/**
 * Comprehensive input security check
 */
export const securityCheckInput = (input: any): {
  safe: boolean;
  issues: string[];
  sanitized: any;
} => {
  const issues: string[] = [];
  
  // Convert to string for analysis
  const inputString = typeof input === 'string' ? input : JSON.stringify(input);
  
  // Check for malicious patterns
  const maliciousCheck = detectMaliciousInput(inputString);
  if (maliciousCheck.isMalicious) {
    issues.push(`Malicious patterns detected: ${maliciousCheck.patterns.join(', ')}`);
  }
  
  // Check input length
  if (inputString.length > 10000) {
    issues.push('Input too long (potential DoS attack)');
  }
  
  // Check for null bytes
  if (inputString.includes('\x00')) {
    issues.push('Null bytes detected');
  }
  
  // Check for control characters
  if (/[\x00-\x1f\x7f-\x9f]/.test(inputString)) {
    issues.push('Control characters detected');
  }
  
  // Sanitize the input
  const sanitized = sanitizeObject(input);
  
  return {
    safe: issues.length === 0,
    issues,
    sanitized
  };
};

/**
 * File upload validation
 */
export const validateFileUpload = (file: any): {
  valid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];
  
  if (!file) {
    errors.push('No file provided');
    return { valid: false, errors };
  }
  
  // Check file size (max 5MB)
  if (file.size > 5 * 1024 * 1024) {
    errors.push('File too large (max 5MB)');
  }
  
  // Check file type
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
  if (!allowedTypes.includes(file.type)) {
    errors.push('Invalid file type');
  }
  
  // Check filename
  if (!/^[a-zA-Z0-9._-]+$/.test(file.name)) {
    errors.push('Invalid filename');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
};

export default {
  sanitizeInput,
  sanitizeObject,
  ValidationSchemas,
  RegistrationSchema,
  LoginSchema,
  PaymentSchema,
  CommissionAdjustmentSchema,
  validateRequest,
  createValidationMiddleware,
  detectMaliciousInput,
  securityCheckInput,
  validateFileUpload
};
