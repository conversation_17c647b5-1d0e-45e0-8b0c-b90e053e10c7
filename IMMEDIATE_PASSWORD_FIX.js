#!/usr/bin/env node

/**
 * IMMEDIATE PASSWORD FIX FOR USER **********
 * This script will fix the password and create a working login
 */

import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';

// Supabase configuration
const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

const supabase = createClient(supabaseUrl, supabaseKey);

const hashPassword = async (password) => {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
};

const immediatePasswordFix = async () => {
  try {
    console.log('🚨 IMMEDIATE PASSWORD FIX STARTING...\n');

    const telegramId = **********;
    const newPassword = 'Password123!'; // Simple, easy password

    console.log('📋 Step 1: Finding user account...');

    // Find the user in users table
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('telegram_id', telegramId)
      .single();

    if (userError || !user) {
      console.error('❌ User not found:', userError?.message);
      return;
    }

    console.log('✅ User found:', user.username, user.email);

    console.log('\n📋 Step 2: Setting new password...');
    console.log(`New password: ${newPassword}`);

    // Hash the new password
    const hashedPassword = await hashPassword(newPassword);
    console.log('✅ Password hashed successfully');

    // Update the user's password
    const { error: updateError } = await supabase
      .from('users')
      .update({
        password_hash: hashedPassword,
        updated_at: new Date().toISOString()
      })
      .eq('telegram_id', telegramId);

    if (updateError) {
      console.error('❌ Error updating password:', updateError);
      return;
    }

    console.log('✅ Password updated successfully!');

    console.log('\n📋 Step 3: Verifying the fix...');

    // Verify the password works
    const isValid = await bcrypt.compare(newPassword, hashedPassword);
    console.log(`Password verification: ${isValid ? '✅ SUCCESS' : '❌ FAILED'}`);

    if (isValid) {
      console.log('\n🎉 PASSWORD FIX COMPLETED SUCCESSFULLY!');
      console.log('==========================================');
      console.log('');
      console.log('🔑 YOUR NEW LOGIN CREDENTIALS:');
      console.log(`   Telegram ID: ${telegramId}`);
      console.log(`   Password: ${newPassword}`);
      console.log('');
      console.log('📋 HOW TO LOGIN:');
      console.log('1. Go to your login page');
      console.log('2. Select "Quick Login with Telegram ID"');
      console.log(`3. Enter Telegram ID: ${telegramId}`);
      console.log(`4. Enter Password: ${newPassword}`);
      console.log('5. Click "Access Account"');
      console.log('');
      console.log('✅ This should work immediately!');
    }

  } catch (error) {
    console.error('❌ Fix failed:', error);
  }
};

// Run the fix
immediatePasswordFix();
