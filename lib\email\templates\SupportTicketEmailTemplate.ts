/**
 * SUPPORT TICKET EMAIL TEMPLATES
 * 
 * Templates for support ticket notifications:
 * - New ticket notifications to support team
 * - Response notifications to users
 */

import { BaseEmailTemplate } from './BaseEmailTemplate';
import { BaseEmailData } from '../types/EmailTypes';

// Types for support ticket email data
export interface NewTicketNotificationData extends BaseEmailData {
  ticketNumber: string;
  userInfo: {
    name: string;
    email: string;
    userType: 'shareholder' | 'affiliate';
    userId: number;
  };
  ticket: {
    title: string;
    description: string;
    priority: 'low' | 'medium' | 'high' | 'urgent';
    category: string;
  };
  adminDashboardUrl: string;
}

export interface TicketResponseNotificationData extends BaseEmailData {
  ticketNumber: string;
  userInfo: {
    name: string;
    email: string;
  };
  ticket: {
    title: string;
    statusUpdate?: string;
    responseMessage?: string;
  };
  agentInfo?: {
    name: string;
    email: string;
  };
  dashboardUrl: string;
}

export class NewTicketNotificationTemplate extends BaseEmailTemplate<NewTicketNotificationData> {
  protected emailType = 'support_new_ticket' as const;

  protected generateSubject(data: NewTicketNotificationData): string {
    return `🎫 New Support Ticket: ${data.ticketNumber} - ${data.ticket.priority.toUpperCase()} Priority`;
  }

  protected generateBody(data: NewTicketNotificationData): string {
    const priorityColor = this.getPriorityColor(data.ticket.priority);
    const userTypeIcon = data.userInfo.userType === 'shareholder' ? '👤' : '🤝';
    
    return `
      <div class="email-body">
        <h2 style="color: ${this.brandingConfig.colors.primary}; margin-bottom: 20px; text-align: center;">
          🎫 New Support Ticket Created
        </h2>
        
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h3 style="color: ${this.brandingConfig.colors.text}; margin-top: 0;">
            Ticket Details
          </h3>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: ${this.brandingConfig.colors.text};">Ticket Number:</td>
              <td style="padding: 8px 0; color: ${this.brandingConfig.colors.text};">${data.ticketNumber}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: ${this.brandingConfig.colors.text};">Priority:</td>
              <td style="padding: 8px 0;">
                <span style="background-color: ${priorityColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold;">
                  ${data.ticket.priority.toUpperCase()}
                </span>
              </td>
            </tr>
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: ${this.brandingConfig.colors.text};">Category:</td>
              <td style="padding: 8px 0; color: ${this.brandingConfig.colors.text};">${data.ticket.category}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: ${this.brandingConfig.colors.text};">Title:</td>
              <td style="padding: 8px 0; color: ${this.brandingConfig.colors.text};">${data.ticket.title}</td>
            </tr>
          </table>
        </div>

        <div style="background-color: #e8f4fd; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h3 style="color: ${this.brandingConfig.colors.text}; margin-top: 0;">
            ${userTypeIcon} User Information
          </h3>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: ${this.brandingConfig.colors.text};">Name:</td>
              <td style="padding: 8px 0; color: ${this.brandingConfig.colors.text};">${data.userInfo.name}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: ${this.brandingConfig.colors.text};">Email:</td>
              <td style="padding: 8px 0; color: ${this.brandingConfig.colors.text};">${data.userInfo.email}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: ${this.brandingConfig.colors.text};">User Type:</td>
              <td style="padding: 8px 0; color: ${this.brandingConfig.colors.text};">
                <span style="background-color: ${data.userInfo.userType === 'shareholder' ? '#10b981' : '#3b82f6'}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold;">
                  ${data.userInfo.userType.toUpperCase()}
                </span>
              </td>
            </tr>
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: ${this.brandingConfig.colors.text};">User ID:</td>
              <td style="padding: 8px 0; color: ${this.brandingConfig.colors.text};">#${data.userInfo.userId}</td>
            </tr>
          </table>
        </div>

        <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
          <h3 style="color: ${this.brandingConfig.colors.text}; margin-top: 0;">
            📝 Ticket Description
          </h3>
          <div style="background-color: white; padding: 15px; border-radius: 4px; border-left: 4px solid ${this.brandingConfig.colors.primary};">
            <p style="margin: 0; color: ${this.brandingConfig.colors.text}; line-height: 1.6;">
              ${data.ticket.description.replace(/\n/g, '<br>')}
            </p>
          </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
          <a href="${data.adminDashboardUrl}" 
             style="background-color: ${this.brandingConfig.colors.primary}; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">
            🔧 View & Respond in Admin Dashboard
          </a>
        </div>

        <div style="background-color: #f1f5f9; padding: 15px; border-radius: 8px; margin-top: 30px;">
          <p style="margin: 0; color: ${this.brandingConfig.colors.textSecondary}; font-size: 14px; text-align: center;">
            <strong>Response Time Guidelines:</strong><br>
            🔴 Urgent: 1 hour | 🟡 High: 4 hours | 🟢 Medium: 24 hours | 🔵 Low: 48 hours
          </p>
        </div>
      </div>
    `;
  }

  protected generateTextContent(data: NewTicketNotificationData): string {
    return `
New Support Ticket Created - ${data.ticketNumber}

TICKET DETAILS:
- Ticket Number: ${data.ticketNumber}
- Priority: ${data.ticket.priority.toUpperCase()}
- Category: ${data.ticket.category}
- Title: ${data.ticket.title}

USER INFORMATION:
- Name: ${data.userInfo.name}
- Email: ${data.userInfo.email}
- User Type: ${data.userInfo.userType.toUpperCase()}
- User ID: #${data.userInfo.userId}

DESCRIPTION:
${data.ticket.description}

View and respond to this ticket in the admin dashboard:
${data.adminDashboardUrl}

Response Time Guidelines:
- Urgent: 1 hour
- High: 4 hours  
- Medium: 24 hours
- Low: 48 hours

This email was sent by ${this.brandingConfig.companyName} Support System.
    `;
  }

  private getPriorityColor(priority: string): string {
    switch (priority) {
      case 'urgent': return '#dc2626';
      case 'high': return '#ea580c';
      case 'medium': return '#ca8a04';
      case 'low': return '#059669';
      default: return '#6b7280';
    }
  }
}

export class TicketResponseNotificationTemplate extends BaseEmailTemplate<TicketResponseNotificationData> {
  protected emailType = 'support_response' as const;

  protected generateSubject(data: TicketResponseNotificationData): string {
    if (data.ticket.statusUpdate) {
      return `📋 Ticket Update: ${data.ticketNumber} - Status Changed`;
    }
    return `💬 New Response: ${data.ticketNumber} - Support Team Reply`;
  }

  protected generateBody(data: TicketResponseNotificationData): string {
    return `
      <div class="email-body">
        <h2 style="color: ${this.brandingConfig.colors.primary}; margin-bottom: 20px; text-align: center;">
          ${data.ticket.statusUpdate ? '📋 Support Ticket Update' : '💬 New Support Response'}
        </h2>
        
        <p style="margin-bottom: 15px; color: ${this.brandingConfig.colors.text}; font-size: 18px;">
          Hello ${data.userInfo.name},
        </p>
        
        <p style="margin-bottom: 20px; color: ${this.brandingConfig.colors.text};">
          ${data.ticket.statusUpdate 
            ? `Your support ticket has been updated with a new status.`
            : `Our support team has responded to your ticket.`
          }
        </p>

        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h3 style="color: ${this.brandingConfig.colors.text}; margin-top: 0;">
            🎫 Ticket Information
          </h3>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: ${this.brandingConfig.colors.text};">Ticket Number:</td>
              <td style="padding: 8px 0; color: ${this.brandingConfig.colors.text};">${data.ticketNumber}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: ${this.brandingConfig.colors.text};">Subject:</td>
              <td style="padding: 8px 0; color: ${this.brandingConfig.colors.text};">${data.ticket.title}</td>
            </tr>
            ${data.ticket.statusUpdate ? `
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: ${this.brandingConfig.colors.text};">New Status:</td>
              <td style="padding: 8px 0;">
                <span style="background-color: ${this.brandingConfig.colors.primary}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold;">
                  ${data.ticket.statusUpdate.toUpperCase()}
                </span>
              </td>
            </tr>
            ` : ''}
            ${data.agentInfo ? `
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: ${this.brandingConfig.colors.text};">Support Agent:</td>
              <td style="padding: 8px 0; color: ${this.brandingConfig.colors.text};">${data.agentInfo.name}</td>
            </tr>
            ` : ''}
          </table>
        </div>

        ${data.ticket.responseMessage ? `
        <div style="background-color: #e8f4fd; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
          <h3 style="color: ${this.brandingConfig.colors.text}; margin-top: 0;">
            💬 Support Team Response
          </h3>
          <div style="background-color: white; padding: 15px; border-radius: 4px; border-left: 4px solid ${this.brandingConfig.colors.primary};">
            <p style="margin: 0; color: ${this.brandingConfig.colors.text}; line-height: 1.6;">
              ${data.ticket.responseMessage.replace(/\n/g, '<br>')}
            </p>
          </div>
        </div>
        ` : ''}

        <div style="text-align: center; margin: 30px 0;">
          <a href="${data.dashboardUrl}" 
             style="background-color: ${this.brandingConfig.colors.primary}; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">
            📱 View Full Conversation
          </a>
        </div>

        <div style="background-color: #f1f5f9; padding: 15px; border-radius: 8px; margin-top: 30px;">
          <p style="margin: 0; color: ${this.brandingConfig.colors.textSecondary}; font-size: 14px; text-align: center;">
            Need additional help? Simply reply to your ticket through your dashboard.<br>
            Our support team is here to assist you every step of the way.
          </p>
        </div>
      </div>
    `;
  }

  protected generateTextContent(data: TicketResponseNotificationData): string {
    return `
${data.ticket.statusUpdate ? 'Support Ticket Update' : 'New Support Response'} - ${data.ticketNumber}

Hello ${data.userInfo.name},

${data.ticket.statusUpdate 
  ? `Your support ticket has been updated with a new status.`
  : `Our support team has responded to your ticket.`
}

TICKET INFORMATION:
- Ticket Number: ${data.ticketNumber}
- Subject: ${data.ticket.title}
${data.ticket.statusUpdate ? `- New Status: ${data.ticket.statusUpdate.toUpperCase()}` : ''}
${data.agentInfo ? `- Support Agent: ${data.agentInfo.name}` : ''}

${data.ticket.responseMessage ? `
SUPPORT TEAM RESPONSE:
${data.ticket.responseMessage}
` : ''}

View the full conversation in your dashboard:
${data.dashboardUrl}

Need additional help? Simply reply to your ticket through your dashboard.
Our support team is here to assist you every step of the way.

This email was sent by ${this.brandingConfig.companyName} Support System.
    `;
  }
}
