// API endpoint to book a meeting
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase configuration for meeting booking API');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Email validation regex
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Phone validation regex (international format)
const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { meetingId, attendeeName, attendeeEmail, phoneNumber, notes } = req.body;

    // Validation
    if (!meetingId || !attendeeName || !attendeeEmail) {
      return res.status(400).json({ 
        error: 'Missing required fields',
        details: 'Meeting ID, attendee name, and email are required'
      });
    }

    if (!emailRegex.test(attendeeEmail)) {
      return res.status(400).json({ 
        error: 'Invalid email format',
        details: 'Please provide a valid email address'
      });
    }

    if (phoneNumber && !phoneRegex.test(phoneNumber.replace(/[\s\-\(\)]/g, ''))) {
      return res.status(400).json({ 
        error: 'Invalid phone number format',
        details: 'Please provide a valid phone number'
      });
    }

    if (attendeeName.length < 2 || attendeeName.length > 255) {
      return res.status(400).json({ 
        error: 'Invalid name',
        details: 'Name must be between 2 and 255 characters'
      });
    }

    console.log(`🔍 Booking meeting ${meetingId} for ${attendeeEmail}...`);

    // Check if meeting exists and has capacity
    const { data: meeting, error: meetingError } = await supabase
      .from('meetings')
      .select('id, title, max_attendees, current_attendees, status, meeting_date, meeting_time')
      .eq('id', meetingId)
      .eq('status', 'scheduled')
      .single();

    if (meetingError || !meeting) {
      console.error('❌ Meeting not found:', meetingError);
      return res.status(404).json({ 
        error: 'Meeting not found',
        details: 'The requested meeting does not exist or is not available for booking'
      });
    }

    // Check if meeting is in the future
    const meetingDateTime = new Date(`${meeting.meeting_date}T${meeting.meeting_time}`);
    if (meetingDateTime <= new Date()) {
      return res.status(400).json({ 
        error: 'Meeting has passed',
        details: 'Cannot book a meeting that has already occurred'
      });
    }

    // Check capacity
    if (meeting.current_attendees >= meeting.max_attendees) {
      return res.status(400).json({ 
        error: 'Meeting is fully booked',
        details: 'This meeting has reached its maximum capacity'
      });
    }

    // Check for duplicate booking
    const { data: existingBooking, error: duplicateError } = await supabase
      .from('meeting_bookings')
      .select('id')
      .eq('meeting_id', meetingId)
      .eq('attendee_email', attendeeEmail.toLowerCase())
      .eq('status', 'confirmed')
      .single();

    if (existingBooking) {
      return res.status(400).json({ 
        error: 'Already booked',
        details: 'You have already booked this meeting'
      });
    }

    // Create the booking
    const { data: booking, error: bookingError } = await supabase
      .from('meeting_bookings')
      .insert({
        meeting_id: meetingId,
        attendee_name: attendeeName.trim(),
        attendee_email: attendeeEmail.toLowerCase().trim(),
        phone_number: phoneNumber?.trim() || null,
        notes: notes?.trim() || null,
        status: 'confirmed'
      })
      .select('id, confirmation_code')
      .single();

    if (bookingError) {
      console.error('❌ Error creating booking:', bookingError);
      return res.status(500).json({ 
        error: 'Failed to create booking',
        details: bookingError.message 
      });
    }

    console.log(`✅ Booking created successfully: ${booking.confirmation_code}`);

    // Get updated meeting info
    const { data: updatedMeeting } = await supabase
      .from('meetings')
      .select('current_attendees, max_attendees')
      .eq('id', meetingId)
      .single();

    return res.status(201).json({
      success: true,
      booking: {
        id: booking.id,
        confirmationCode: booking.confirmation_code,
        meetingTitle: meeting.title,
        attendeeName: attendeeName.trim(),
        attendeeEmail: attendeeEmail.toLowerCase().trim()
      },
      meeting: {
        currentAttendees: updatedMeeting?.current_attendees || meeting.current_attendees + 1,
        maxAttendees: meeting.max_attendees,
        availableSpots: meeting.max_attendees - (updatedMeeting?.current_attendees || meeting.current_attendees + 1)
      }
    });

  } catch (error) {
    console.error('❌ Unexpected error in meeting booking API:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      details: error.message 
    });
  }
}
