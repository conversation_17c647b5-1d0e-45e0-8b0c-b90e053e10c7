/**
 * <PERSON>Y<PERSON> FIELD RESUBMISSION COMPONENT
 * 
 * Allows users to resubmit specific rejected KYC fields
 * with detailed rejection reasons and targeted fixes.
 */

import React, { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';
import KYCDocumentResubmission from './KYCDocumentResubmission';

interface FieldRejection {
  id: string;
  field_name: string;
  approval_status: string;
  admin_notes: string;
  created_at: string;
  updated_at: string;
}

interface DocumentStatus {
  exists: boolean;
  fileName?: string;
  uploadedAt?: string;
  verificationStatus?: string;
}

interface KYCFieldResubmissionProps {
  userId: number;
  kycId: string;
  onResubmissionComplete?: () => void;
}

const KYCFieldResubmission: React.FC<KYCFieldResubmissionProps> = ({
  userId,
  kycId,
  onResubmissionComplete
}) => {
  const [rejectedFields, setRejectedFields] = useState<FieldRejection[]>([]);
  const [pendingFields, setPendingFields] = useState<FieldRejection[]>([]);
  const [allFields, setAllFields] = useState<FieldRejection[]>([]);
  const [documentStatuses, setDocumentStatuses] = useState<Record<string, DocumentStatus>>({});
  const [loading, setLoading] = useState(true);
  const [resubmitting, setResubmitting] = useState<string | null>(null);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [selectedField, setSelectedField] = useState<FieldRejection | null>(null);

  // Load rejected fields
  useEffect(() => {
    loadRejectedFields();
  }, [kycId]);

  const loadDocumentStatuses = async () => {
    try {
      console.log('📄 Loading document statuses for KYC ID:', kycId);

      const response = await fetch(`/api/kyc-document-status?kycId=${kycId}`);
      const result = await response.json();

      if (!response.ok) {
        console.error('❌ Error loading document statuses:', result.error);
        return;
      }

      if (result.success && result.documentStatuses) {
        setDocumentStatuses(result.documentStatuses);
        console.log('📄 Document statuses loaded:', result.documentStatuses);
      } else {
        console.error('❌ Invalid response format:', result);
      }
    } catch (error) {
      console.error('❌ Error loading document statuses:', error);
    }
  };

  const loadRejectedFields = async () => {
    try {
      setLoading(true);
      console.log('📋 Loading field approvals for KYC ID:', kycId);

      const response = await fetch(`/api/kyc-field-approvals?kycId=${kycId}`);
      const result = await response.json();

      if (!response.ok) {
        console.error('❌ Error loading field approvals:', result.error);
        return;
      }

      if (result.success && result.fieldApprovals) {
        const { rejected, pending, all } = result.fieldApprovals;
        setRejectedFields(rejected || []);
        setPendingFields(pending || []);
        setAllFields(all || []);

        console.log('📋 Loaded field approvals:', {
          total: all?.length || 0,
          rejected: rejected?.length || 0,
          pending: pending?.length || 0
        });

        // Also load document statuses
        await loadDocumentStatuses();
      } else {
        console.error('❌ Invalid response format:', result);
      }
    } catch (error) {
      console.error('❌ Error in loadRejectedFields:', error);
    } finally {
      setLoading(false);
    }
  };

  const getFieldDisplayName = (fieldName: string): string => {
    const fieldNames: Record<string, string> = {
      personal_info: 'Personal Information',
      address_info: 'Address Information',
      compliance_info: 'Compliance Information',
      id_document: 'ID Document',
      proof_address: 'Proof of Address',
      selfie_verification: 'Selfie Verification'
    };
    return fieldNames[fieldName] || fieldName;
  };

  const getFieldIcon = (fieldName: string): string => {
    const fieldIcons: Record<string, string> = {
      personal_info: '👤',
      address_info: '🏠',
      compliance_info: '✅',
      id_document: '🆔',
      proof_address: '📄',
      selfie_verification: '🤳'
    };
    return fieldIcons[fieldName] || '📋';
  };

  const isDocumentField = (fieldName: string): boolean => {
    return ['id_document', 'selfie_verification', 'proof_address'].includes(fieldName);
  };

  const handleOpenUploadModal = (field: FieldRejection) => {
    if (!isDocumentField(field.field_name)) {
      // For non-document fields, redirect to full KYC form
      window.location.href = '/dashboard?section=kyc&force_full_kyc=true';
      return;
    }

    setSelectedField(field);
    setShowUploadModal(true);
  };

  const handleCloseUploadModal = () => {
    setShowUploadModal(false);
    setSelectedField(null);
  };

  const handleUploadComplete = async () => {
    setShowUploadModal(false);
    setSelectedField(null);
    // Reload the document statuses to reflect the new upload
    await loadDocumentStatuses();
    if (onResubmissionComplete) {
      onResubmissionComplete();
    }
  };

  const handleResubmit = async (fieldName: string) => {
    setResubmitting(fieldName);

    try {
      console.log('🔄 Resubmitting field:', fieldName);

      const response = await fetch('/api/kyc-field-resubmit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          kycId,
          fieldName
        })
      });

      const result = await response.json();

      if (!response.ok) {
        console.error('❌ Error resubmitting field:', result.error);
        alert('Failed to resubmit field. Please try again.');
        return;
      }

      if (result.success) {
        console.log('✅ Field resubmitted successfully:', fieldName);

        // Reload fields to show the updated status
        loadRejectedFields();

        if (onResubmissionComplete) {
          onResubmissionComplete();
        }
      } else {
        console.error('❌ Resubmission failed:', result);
        alert('Failed to resubmit field. Please try again.');
      }

    } catch (error) {
      console.error('❌ Error in handleResubmit:', error);
      alert('Failed to resubmit field. Please try again.');
    } finally {
      setResubmitting(null);
    }
  };

  if (loading) {
    return (
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400"></div>
          <span className="ml-3 text-gray-300">Loading rejected fields...</span>
        </div>
      </div>
    );
  }

  if (rejectedFields.length === 0 && pendingFields.length === 0) {
    return (
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <div className="text-center py-8">
          <div className="text-green-400 text-4xl mb-4">✅</div>
          <h3 className="text-lg font-semibold text-white mb-2">All Fields Approved</h3>
          <p className="text-gray-400">No rejected fields found. Your KYC information looks good!</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* All Fields Section */}
      {allFields.length > 0 && (
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-blue-400 mb-2">
              📋 KYC Field Status
            </h3>
            <p className="text-gray-300 text-sm">
              Review the status of your KYC fields below. You can update documents for fields that need attention:
            </p>
          </div>

          <div className="space-y-4">
            {allFields.map((field) => {
              const documentStatus = documentStatuses[field.field_name];
              const isDocumentFieldType = isDocumentField(field.field_name);

              // Determine border color and status based on approval status
              const getBorderColor = (status: string) => {
                switch (status) {
                  case 'rejected': return 'border-red-600/30';
                  case 'pending': return 'border-yellow-600/30';
                  case 'approved': return 'border-green-600/30';
                  default: return 'border-gray-600/30';
                }
              };

              const getStatusBadge = (status: string) => {
                switch (status) {
                  case 'rejected': return <span className="px-2 py-1 bg-red-600/20 text-red-400 text-xs rounded-full">Rejected</span>;
                  case 'pending': return <span className="px-2 py-1 bg-yellow-600/20 text-yellow-400 text-xs rounded-full">Under Review</span>;
                  case 'approved': return <span className="px-2 py-1 bg-green-600/20 text-green-400 text-xs rounded-full">Approved</span>;
                  default: return <span className="px-2 py-1 bg-gray-600/20 text-gray-400 text-xs rounded-full">Unknown</span>;
                }
              };

              return (
                <div key={field.id} className={`bg-gray-900 rounded-lg ${getBorderColor(field.approval_status)} p-4`}>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <span className="text-2xl">{getFieldIcon(field.field_name)}</span>
                        <h4 className="text-lg font-semibold text-white">
                          {getFieldDisplayName(field.field_name)}
                        </h4>
                        {getStatusBadge(field.approval_status)}
                        {documentStatus?.exists && (
                          <span className="px-2 py-1 bg-green-600/20 text-green-400 text-xs rounded-full flex items-center gap-1">
                            📄 Document Uploaded
                          </span>
                        )}
                      </div>

                      {field.approval_status === 'rejected' && (
                        <div className="mb-3">
                          <p className="text-sm text-gray-400 mb-1">Rejection Reason:</p>
                          <p className="text-red-400 bg-red-600/10 p-3 rounded border border-red-600/20">
                            {field.admin_notes || 'No specific reason provided'}
                          </p>
                        </div>
                      )}

                      {field.approval_status === 'pending' && field.admin_notes && (
                        <div className="mb-3">
                          <p className="text-sm text-gray-400 mb-1">Admin Notes:</p>
                          <p className="text-yellow-400 bg-yellow-600/10 p-3 rounded border border-yellow-600/20">
                            {field.admin_notes}
                          </p>
                        </div>
                      )}

                      {field.approval_status === 'approved' && field.admin_notes && (
                        <div className="mb-3">
                          <p className="text-sm text-gray-400 mb-1">Approval Notes:</p>
                          <p className="text-green-400 bg-green-600/10 p-3 rounded border border-green-600/20">
                            {field.admin_notes}
                          </p>
                        </div>
                      )}

                      {documentStatus?.exists && (
                        <div className="mb-3 p-3 bg-green-600/10 border border-green-600/20 rounded">
                          <p className="text-sm text-green-400 mb-1">📄 Current Document:</p>
                          <p className="text-xs text-gray-300">{documentStatus.fileName}</p>
                          <p className="text-xs text-gray-500">
                            Uploaded: {new Date(documentStatus.uploadedAt!).toLocaleDateString()} at{' '}
                            {new Date(documentStatus.uploadedAt!).toLocaleTimeString()}
                          </p>
                        </div>
                      )}

                      <p className="text-xs text-gray-500">
                        Rejected on: {new Date(field.updated_at).toLocaleDateString()} at{' '}
                        {new Date(field.updated_at).toLocaleTimeString()}
                      </p>
                    </div>

                    <div className="ml-4 flex flex-col gap-2">
                      {field.approval_status === 'approved' ? (
                        <div className="px-4 py-2 bg-green-600/20 text-green-400 font-medium rounded-lg flex items-center gap-2">
                          ✅ Approved
                        </div>
                      ) : isDocumentFieldType ? (
                        <button
                          onClick={() => {
                            setSelectedField(field);
                            setShowUploadModal(true);
                          }}
                          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors flex items-center gap-2"
                        >
                          📤 {documentStatus?.exists ? 'Replace Document' : 'Upload New Document'}
                        </button>
                      ) : field.approval_status === 'rejected' ? (
                        <button
                          onClick={() => handleResubmit(field.field_name)}
                          disabled={resubmitting === field.field_name}
                          className="px-4 py-2 bg-yellow-600 hover:bg-yellow-500 disabled:bg-yellow-600/50 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors flex items-center gap-2"
                        >
                          {resubmitting === field.field_name ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                              Processing...
                            </>
                          ) : (
                            <>
                              🔄 Update Information
                            </>
                          )}
                        </button>
                      ) : (
                        <div className="px-4 py-2 bg-yellow-600/20 text-yellow-400 font-medium rounded-lg flex items-center gap-2">
                          ⏳ Under Review
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}



      {/* Tips Section */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <div className="flex items-start gap-3">
          <span className="text-blue-400 text-xl">💡</span>
          <div>
            <h4 className="text-blue-400 font-medium mb-1">Resubmission Tips</h4>
            <ul className="text-sm text-gray-300 space-y-1">
              <li>• Read the rejection reason carefully before resubmitting</li>
              <li>• Ensure all information is accurate and clearly visible</li>
              <li>• Use high-quality images for document uploads</li>
              <li>• Contact support if you need clarification on requirements</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Upload Modal */}
      {showUploadModal && selectedField && (
        <KYCDocumentResubmission
          kycId={kycId}
          fieldName={selectedField.field_name}
          fieldDisplayName={getFieldDisplayName(selectedField.field_name)}
          onComplete={handleUploadComplete}
          onCancel={handleCloseUploadModal}
        />
      )}
    </div>
  );
};

export { KYCFieldResubmission };
export default KYCFieldResubmission;
