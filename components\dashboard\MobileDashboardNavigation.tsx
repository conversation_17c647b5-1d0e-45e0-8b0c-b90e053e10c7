import React from 'react';
import { DashboardSection, useDashboardNavigation } from '../../hooks/useDashboardNavigation.tsx';
import { UserPermissions } from '../../hooks/useUserPermissions';
import { DashboardData } from '../../hooks/useDashboardData';

interface MobileDashboardNavigationProps {
  activeSection: DashboardSection;
  onSectionChange: (section: DashboardSection) => void;
  permissions: UserPermissions;
  dashboardData: DashboardData;
  isOpen: boolean;
  onClose: () => void;
}

export const MobileDashboardNavigation: React.FC<MobileDashboardNavigationProps> = ({
  activeSection,
  onSectionChange,
  permissions,
  dashboardData,
  isOpen,
  onClose
}) => {
  const { shareholderNavigationItems } = useDashboardNavigation();

  // Filter navigation items based on permissions
  const filteredNavigationItems = shareholderNavigationItems.filter(item => {
    switch (item.id) {
      case 'purchase-shares':
        return permissions.canPurchaseShares;
      case 'portfolio':
        return permissions.canViewPortfolio;
      case 'referrals':
        return permissions.canManageReferrals;
      case 'payments':
        return permissions.canViewPayments;
      case 'settings':
        return permissions.canAccessSettings;
      default:
        return true;
    }
  });

  const getBadgeCount = (sectionId: DashboardSection): number | undefined => {
    switch (sectionId) {
      case 'notifications':
        return dashboardData.notifications?.unread || 0;
      default:
        return undefined;
    }
  };

  const handleSectionChange = (section: DashboardSection) => {
    onSectionChange(section);
    onClose();
  };

  // Debug logging
  React.useEffect(() => {
    console.log('MobileDashboardNavigation isOpen:', isOpen);
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <>
      {/* Overlay */}
      <div 
        className="lg:hidden fixed inset-0 bg-black/50 z-40"
        onClick={onClose}
      />

      {/* Mobile Menu */}
      <div className={`lg:hidden fixed inset-y-0 left-0 w-80 bg-gray-800 z-50 transform transition-transform duration-300 ease-in-out ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <img 
              src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-s.png" 
              alt="Aureus Alliance Holdings" 
              className="h-8 w-8"
            />
            <div>
              <h2 className="text-white font-bold text-lg">Aureus Alliance</h2>
              <p className="text-gray-400 text-xs">Holdings Dashboard</p>
            </div>
          </div>
          
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white p-2 rounded-md hover:bg-gray-700 transition-colors"
            aria-label="Close navigation menu"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Quick Stats */}
        <div className="p-4 bg-gray-900/50 border-b border-gray-700">
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-white">
                {dashboardData.totalShares.toLocaleString()}
              </div>
              <div className="text-xs text-gray-400">Total Shares</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">
                ${dashboardData.currentValue.toLocaleString()}
              </div>
              <div className="text-xs text-gray-400">Portfolio Value</div>
            </div>
          </div>
          
          {permissions.canManageReferrals && (
            <div className="mt-3 text-center">
              <div className="text-xl font-bold text-blue-400">
                ${dashboardData.usdtCommissions?.available.toFixed(2) || '0.00'}
              </div>
              <div className="text-xs text-gray-400">USDT Balance</div>
            </div>
          )}
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
          {filteredNavigationItems.map((item) => {
            const isActive = activeSection === item.id;
            const badgeCount = getBadgeCount(item.id);
            const IconComponent = item.icon;

            return (
              <button
                key={item.id}
                onClick={() => handleSectionChange(item.id)}
                className={`w-full flex items-center space-x-4 px-4 py-4 rounded-xl text-left transition-all duration-200 ${
                  isActive
                    ? 'bg-gradient-to-r from-yellow-600 to-yellow-500 text-white shadow-lg'
                    : 'text-gray-300 hover:text-white hover:bg-gray-700/50'
                }`}
                title={item.description}
              >
                <div className={`p-2 rounded-lg ${
                  isActive ? 'bg-white/20' : 'bg-gray-700'
                }`}>
                  <IconComponent className="w-5 h-5" />
                </div>
                
                <div className="flex-1">
                  <div className="font-medium text-base">{item.label}</div>
                  {item.description && (
                    <div className="text-xs opacity-75 mt-1">{item.description}</div>
                  )}
                </div>
                
                {/* Badge for notifications, etc. */}
                {badgeCount !== undefined && badgeCount > 0 && (
                  <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold bg-red-500 text-white rounded-full min-w-[20px]">
                    {badgeCount > 99 ? '99+' : badgeCount}
                  </span>
                )}

                {/* New indicator */}
                {item.isNew && (
                  <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold bg-green-500 text-white rounded-full">
                    New
                  </span>
                )}

                {/* Arrow indicator for active item */}
                {isActive && (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                )}
              </button>
            );
          })}
        </nav>

        {/* KYC Status */}
        {dashboardData.kycStatus && (
          <div className="p-4 border-t border-gray-700 bg-gray-900/30">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`w-3 h-3 rounded-full ${
                  dashboardData.kycStatus === 'approved' 
                    ? 'bg-green-500' 
                    : dashboardData.kycStatus === 'pending'
                    ? 'bg-yellow-500'
                    : 'bg-gray-500'
                }`}></div>
                <span className="text-sm text-gray-300">
                  KYC Status: {dashboardData.kycStatus === 'approved' ? 'Verified' : 
                              dashboardData.kycStatus === 'pending' ? 'Pending' : 'Not Started'}
                </span>
              </div>
              
              {dashboardData.kycStatus !== 'approved' && (
                <button
                  onClick={() => handleSectionChange('settings')}
                  className="text-xs text-yellow-400 hover:text-yellow-300 font-medium"
                >
                  Complete →
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    </>
  );
};
