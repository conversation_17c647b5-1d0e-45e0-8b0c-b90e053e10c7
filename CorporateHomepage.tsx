import React, { useState, useEffect } from 'react';
import HomePage from './pages/HomePage';
import SharePhasesPage from './pages/InvestmentPhasesPage';
import CalculatorPage from './pages/CalculatorPage';
import FinancialDataPage from './pages/FinancialDataPage';
import MineProductionPage from './pages/MineProductionPage';
import GalleryPage from './pages/GalleryPage';
import CSRPage from './pages/CSRPage';
import { getCurrentUser } from './lib/supabase';
import { getFullVersion } from './lib/version';
import ContactForm from './components/ContactForm';

// Using unified aureus.css design system

// Modern Multi-Page Website for Aureus Alliance Holdings
const CorporateHomepage: React.FC = () => {
    const [currentPage, setCurrentPage] = useState<string>('home');
    const [activeModal, setActiveModal] = useState<string | null>(null);
    const [user, setUser] = useState<any>(null);

    // Check authentication status on component mount
    useEffect(() => {
        checkAuthStatus();
    }, []);

    const checkAuthStatus = async () => {
        try {
            const currentUser = await getCurrentUser();
            setUser(currentUser);
        } catch (error) {
            console.error('Error checking auth status:', error);
            setUser(null);
        }
    };

    const handleNavigate = (page: string) => {
        setCurrentPage(page);
        window.scrollTo(0, 0);
    };

    const handlePurchaseShares = () => {
        if (user) {
            // User is authenticated, proceed to purchase shares
            window.location.href = '/purchase-shares';
        } else {
            // User is not authenticated, redirect to login with purchase intent
            localStorage.setItem('aureus_post_login_redirect', '/purchase-shares');
            window.location.href = '/login';
        }
    };

    const openModal = (modalType: string) => {
        setActiveModal(modalType);
    };

    const closeModal = () => {
        setActiveModal(null);
    };

    return (
        <div className="page modern-website mobile-dashboard-content">
            {/* Header */}
            <header className="header">
                <div className="container">
                    <div className="header-content">
                        <div className="logo">
                            <img
                                src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-s.png"
                                alt="Aureus Alliance Holdings"
                                className="logo-image"
                            />
                        </div>
                        <nav className="nav-menu">
                            <div className="nav-main">
                                <button
                                    className={`nav-item ${currentPage === 'home' ? 'active' : ''}`}
                                    onClick={() => handleNavigate('home')}
                                >
                                    Home
                                </button>
                                <button
                                    className={`nav-item ${currentPage === 'investment-phases' ? 'active' : ''}`}
                                    onClick={() => handleNavigate('investment-phases')}
                                >
                                    Share Phases
                                </button>
                                <button
                                    className={`nav-item ${currentPage === 'calculator' ? 'active' : ''}`}
                                    onClick={() => handleNavigate('calculator')}
                                >
                                    Calculator
                                </button>
                                <button
                                    className={`nav-item ${currentPage === 'financial-data' ? 'active' : ''}`}
                                    onClick={() => handleNavigate('financial-data')}
                                >
                                    Financial Data
                                </button>
                                <button
                                    className={`nav-item ${currentPage === 'mine-production' ? 'active' : ''}`}
                                    onClick={() => handleNavigate('mine-production')}
                                >
                                    Mine Production
                                </button>
                                <button
                                    className={`nav-item ${currentPage === 'gallery' ? 'active' : ''}`}
                                    onClick={() => handleNavigate('gallery')}
                                >
                                    Gallery
                                </button>
                            </div>
                            <div className="nav-actions">
                                <button className="btn btn-primary" onClick={handlePurchaseShares}>
                                    Purchase Shares
                                </button>
                            </div>
                        </nav>
                    </div>
                </div>
            </header>

            {/* Page Content */}
            <main>
                {currentPage === 'home' && <HomePage onNavigate={handleNavigate} />}
                {currentPage === 'investment-phases' && <SharePhasesPage onNavigate={handleNavigate} />}
                {currentPage === 'calculator' && <CalculatorPage onNavigate={handleNavigate} />}
                {currentPage === 'financial-data' && <FinancialDataPage onNavigate={handleNavigate} />}
                {currentPage === 'mine-production' && <MineProductionPage onNavigate={handleNavigate} />}
                {currentPage === 'gallery' && <GalleryPage onNavigate={handleNavigate} />}
                {currentPage === 'csr' && <CSRPage onNavigate={handleNavigate} />}
                {currentPage === 'community-impact' && <CSRPage onNavigate={handleNavigate} />}
                {(currentPage === 'expansion-plan' || currentPage === 'company-info') && (
                    <div className="page">
                        <section className="page-header">
                            <div className="container">
                                <div className="breadcrumb">
                                    <button onClick={() => handleNavigate('home')} className="breadcrumb-link">
                                        Home
                                    </button>
                                    <span className="breadcrumb-separator">→</span>
                                    <span className="breadcrumb-current">
                                        {currentPage === 'expansion-plan' ? 'Expansion Plan' : 'Company Information'}
                                    </span>
                                </div>
                                <h1 className="page-title">
                                    {currentPage === 'expansion-plan' ? 'Expansion Plan' : 'Company Information'}
                                </h1>
                                <p className="page-subtitle">
                                    {currentPage === 'expansion-plan'
                                        ? '5-year growth strategy scaling to 200+ plants across Africa'
                                        : 'Corporate details, team, history, and operational background'
                                    }
                                </p>
                            </div>
                        </section>
                        <section className="content-section">
                            <div className="container">
                                <div className="coming-soon-card">
                                    <div className="coming-soon-icon">🚧</div>
                                    <h2>Coming Soon</h2>
                                    <p>This section is currently under development. Please check back soon for detailed information.</p>
                                    <button
                                        className="btn btn-primary"
                                        onClick={() => handleNavigate('home')}
                                    >
                                        Back to Home
                                    </button>
                                </div>
                            </div>
                        </section>
                    </div>
                )}
            </main>

            {/* Footer */}
            <footer className="clean-footer">
                <div className="container">
                    <div className="footer-content">
                        <div className="footer-info">
                            <img
                                src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-m.png"
                                alt="Aureus Alliance Holdings"
                                className="footer-logo"
                            />
                            <h3>Aureus Alliance Holdings (Pty) Ltd</h3>
                            <p>CIPC-registered gold mining company • Real shares backed by real gold production</p>
                        </div>

                        <div className="footer-contact">
                            <h4>Contact Us</h4>
                            <ContactForm
                                className="footer-contact-form"
                                onSuccess={() => {
                                    console.log('✅ Contact form submitted successfully from homepage');
                                }}
                                onError={(error) => {
                                    console.error('❌ Contact form error from homepage:', error);
                                }}
                            />
                        </div>

                        <div className="footer-links">
                            <button onClick={() => openModal('privacy')} className="footer-link-btn">
                                Privacy Policy
                            </button>
                            <button onClick={() => openModal('terms')} className="footer-link-btn">
                                Terms & Conditions
                            </button>
                            <button onClick={() => openModal('legal')} className="footer-link-btn">
                                Legal Disclaimer
                            </button>
                            <button
                                onClick={() => window.location.href = '/affiliate'}
                                className="footer-link-btn text-gold"
                            >
                                Become an Affiliate
                            </button>
                        </div>
                    </div>
                    <div className="footer-bottom">
                        <p>&copy; 2025 Aureus Alliance Holdings (Pty) Ltd. All rights reserved.</p>
                        <p style={{ color: '#666', fontSize: '11px', marginTop: '8px' }}>
                            Aureus Africa {getFullVersion()}
                        </p>
                    </div>
                </div>
            </footer>

            {/* Modal Overlay */}
            {activeModal && (
                <div className="modal-overlay" onClick={closeModal}>
                    <div className="modal-content" onClick={(e) => e.stopPropagation()}>
                        <div className="modal-header">
                            <h2>
                                {activeModal === 'privacy' && 'Privacy Policy'}
                                {activeModal === 'terms' && 'Terms & Conditions'}
                                {activeModal === 'legal' && 'Legal Disclaimer'}
                            </h2>
                            <button className="modal-close" onClick={closeModal}>×</button>
                        </div>
                        <div className="modal-body">
                            {activeModal === 'privacy' && (
                                <div className="legal-content">
                                    <h3>Information We Collect</h3>
                                    <p>Aureus Alliance Holdings (Pty) Ltd collects personal information necessary for share purchase transactions, KYC compliance, and communication purposes. This includes name, contact details, identification documents, and financial information.</p>

                                    <h3>How We Use Your Information</h3>
                                    <p>Your information is used to process share purchases, maintain shareholder records, comply with regulatory requirements, and provide updates about your shareholding and company operations.</p>

                                    <h3>Data Protection</h3>
                                    <p>We implement industry-standard security measures to protect your personal information. Data is stored securely and access is restricted to authorized personnel only.</p>

                                    <h3>Contact Information</h3>
                                    <p>For privacy-related inquiries, contact <NAME_EMAIL></p>
                                </div>
                            )}

                            {activeModal === 'terms' && (
                                <div className="legal-content">
                                    <h3>Share Purchase Agreement</h3>
                                    <p>By purchasing shares in Aureus Alliance Holdings (Pty) Ltd, you agree to the terms governing your shareholding, including dividend distribution policies and shareholder rights.</p>

                                    <h3>Shareholding Risks</h3>
                                    <p>Gold mining shareholding carries inherent risks including market volatility, operational challenges, and regulatory changes. Past dividend payments do not guarantee future dividend payments.</p>

                                    <h3>Shareholder Rights</h3>
                                    <p>Shareholders are entitled to dividend distributions based on company performance, voting rights on major decisions, and access to annual financial reports.</p>

                                    <h3>Dispute Resolution</h3>
                                    <p>Any disputes will be resolved through South African legal jurisdiction in accordance with company law.</p>
                                </div>
                            )}

                            {activeModal === 'legal' && (
                                <div className="legal-content">
                                    <h3>Shareholder Disclaimer</h3>
                                    <p>This website contains forward-looking statements about Aureus Alliance Holdings' business prospects. Actual results may differ materially from projections due to various factors beyond our control.</p>

                                    <h3>No Financial Advice</h3>
                                    <p>Information provided does not constitute financial advice. Prospective shareholders should conduct their own due diligence and consult with qualified financial advisors before making share purchase decisions.</p>

                                    <h3>Regulatory Compliance</h3>
                                    <p>Aureus Alliance Holdings (Pty) Ltd is registered with the Companies and Intellectual Property Commission (CIPC) of South Africa. All operations comply with applicable mining and securities regulations.</p>

                                    <h3>Limitation of Liability</h3>
                                    <p>The company's liability is limited to the extent permitted by law. Shareholders acknowledge the speculative nature of mining shareholding.</p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default CorporateHomepage;
