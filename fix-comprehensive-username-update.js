/**
 * COMPREHENSIVE USERNAME UPDATE FIX
 *
 * This script fixes the username change system to update ALL tables where usernames are stored.
 * Currently missing: user_share_holdings, competition_leaderboard, and other tables.
 *
 * PROBLEM: When users change their username, it only updates users.username and telegram_users.username
 * but leaves old usernames in user_share_holdings and other tables, causing referral link issues.
 */

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co'
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseServiceKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function fixComprehensiveUsernameUpdate() {
  console.log('🔧 COMPREHENSIVE USERNAME UPDATE FIX')
  console.log('=====================================')
  console.log('This will create a database function that updates ALL tables when username changes')

  // ===== PART 1: CREATE COMPREHENSIVE UPDATE FUNCTION =====
  console.log('\n📋 PART 1: Creating comprehensive username update function')

  const comprehensiveUpdateFunction = `
    -- Drop existing function if it exists
    DROP FUNCTION IF EXISTS update_username_comprehensive(INTEGER, VARCHAR);

    -- Create comprehensive username update function
    CREATE OR REPLACE FUNCTION update_username_comprehensive(
      p_user_id INTEGER,
      p_new_username VARCHAR(255)
    )
    RETURNS JSON AS $$
    DECLARE
      v_old_username VARCHAR(255);
      v_update_count INTEGER := 0;
      v_result JSON;
      v_tables_updated TEXT[] := ARRAY[]::TEXT[];
    BEGIN
      -- Get current username
      SELECT username INTO v_old_username
      FROM users
      WHERE id = p_user_id;

      IF v_old_username IS NULL THEN
        RAISE EXCEPTION 'User with ID % not found', p_user_id;
      END IF;

      -- Check if username is already taken
      IF EXISTS (
        SELECT 1 FROM users
        WHERE username = p_new_username
        AND id != p_user_id
      ) THEN
        RAISE EXCEPTION 'Username % is already taken', p_new_username;
      END IF;

      -- Start comprehensive updates
      RAISE NOTICE 'Updating username for user % from % to %', p_user_id, v_old_username, p_new_username;

      -- 1. Update users table
      UPDATE users
      SET
        username = p_new_username,
        updated_at = NOW()
      WHERE id = p_user_id;

      GET DIAGNOSTICS v_update_count = ROW_COUNT;
      IF v_update_count > 0 THEN
        v_tables_updated := array_append(v_tables_updated, 'users');
      END IF;

      -- 2. Update telegram_users table
      UPDATE telegram_users
      SET
        username = p_new_username,
        updated_at = NOW()
      WHERE user_id = p_user_id;

      GET DIAGNOSTICS v_update_count = ROW_COUNT;
      IF v_update_count > 0 THEN
        v_tables_updated := array_append(v_tables_updated, 'telegram_users');
      END IF;

      -- 3. Update user_share_holdings table (CRITICAL - this was missing!)
      UPDATE user_share_holdings
      SET username = p_new_username
      WHERE user_id = p_user_id;

      GET DIAGNOSTICS v_update_count = ROW_COUNT;
      IF v_update_count > 0 THEN
        v_tables_updated := array_append(v_tables_updated, 'user_share_holdings');
      END IF;

      -- 4. Update competition_leaderboard table
      UPDATE competition_leaderboard
      SET username = p_new_username
      WHERE user_id = p_user_id;

      GET DIAGNOSTICS v_update_count = ROW_COUNT;
      IF v_update_count > 0 THEN
        v_tables_updated := array_append(v_tables_updated, 'competition_leaderboard');
      END IF;

      -- 5. Update admin_commission_conversion_queue table
      UPDATE admin_commission_conversion_queue
      SET username = p_new_username
      WHERE user_id = p_user_id;

      GET DIAGNOSTICS v_update_count = ROW_COUNT;
      IF v_update_count > 0 THEN
        v_tables_updated := array_append(v_tables_updated, 'admin_commission_conversion_queue');
      END IF;

      -- 6. Update referral codes in referrals table
      UPDATE referrals
      SET
        referral_code = REPLACE(referral_code, v_old_username, p_new_username),
        updated_at = NOW()
      WHERE referrer_id = p_user_id
      AND referral_code LIKE v_old_username || '_%';

      GET DIAGNOSTICS v_update_count = ROW_COUNT;
      IF v_update_count > 0 THEN
        v_tables_updated := array_append(v_tables_updated, 'referrals');
      END IF;

      -- 7. Update email_sync_audit_log table
      UPDATE email_sync_audit_log
      SET username = p_new_username
      WHERE user_id = p_user_id;

      GET DIAGNOSTICS v_update_count = ROW_COUNT;
      IF v_update_count > 0 THEN
        v_tables_updated := array_append(v_tables_updated, 'email_sync_audit_log');
      END IF;

      -- 8. Update email_sync_backup table
      UPDATE email_sync_backup
      SET username = p_new_username
      WHERE user_id = p_user_id;

      GET DIAGNOSTICS v_update_count = ROW_COUNT;
      IF v_update_count > 0 THEN
        v_tables_updated := array_append(v_tables_updated, 'email_sync_backup');
      END IF;

      -- Build result JSON
      v_result := json_build_object(
        'success', true,
        'user_id', p_user_id,
        'old_username', v_old_username,
        'new_username', p_new_username,
        'tables_updated', v_tables_updated,
        'updated_at', NOW()
      );

      RAISE NOTICE 'Username update complete. Tables updated: %', array_to_string(v_tables_updated, ', ');

      RETURN v_result;

    EXCEPTION
      WHEN OTHERS THEN
        RAISE EXCEPTION 'Comprehensive username update failed for user %: %', p_user_id, SQLERRM;
    END;
    $$ LANGUAGE plpgsql SECURITY DEFINER;

    -- Grant permissions
    GRANT EXECUTE ON FUNCTION update_username_comprehensive(INTEGER, VARCHAR) TO authenticated;
    GRANT EXECUTE ON FUNCTION update_username_comprehensive(INTEGER, VARCHAR) TO service_role;
  `;

  try {
    const { error: functionError } = await supabase.rpc('exec_sql', {
      sql: comprehensiveUpdateFunction
    });

    if (functionError) {
      console.log('❌ Error creating comprehensive function:', functionError.message);
      console.log('📝 SQL to run manually in Supabase dashboard:');
      console.log(comprehensiveUpdateFunction);
      return false;
    } else {
      console.log('✅ Comprehensive username update function created successfully');
    }
  } catch (error) {
    console.log('❌ Error creating function:', error.message);
    return false;
  }

  // ===== PART 2: UPDATE EXISTING USERNAME EDITOR TO USE NEW FUNCTION =====
  console.log('\n📋 PART 2: The UsernameEditor component needs to be updated to use the new function');
  console.log('   → Replace update_username_atomic with update_username_comprehensive');
  console.log('   → This will ensure ALL tables are updated when username changes');

  // ===== PART 3: TEST THE FUNCTION =====
  console.log('\n📋 PART 3: Testing the comprehensive function');

  try {
    // Test with a non-existent user (should fail gracefully)
    const { error: testError } = await supabase.rpc('update_username_comprehensive', {
      p_user_id: 999999,
      p_new_username: 'test_username_' + Date.now()
    });

    if (testError && testError.message.includes('User with ID 999999 not found')) {
      console.log('✅ Function correctly handles non-existent users');
    } else {
      console.log('⚠️ Function test had unexpected result:', testError?.message);
    }

  } catch (error) {
    console.log('⚠️ Error testing function:', error.message);
  }

  return true;
}

// Run the fix
fixComprehensiveUsernameUpdate()
  .then((success) => {
    if (success) {
      console.log('\n🎉 COMPREHENSIVE USERNAME UPDATE FIX COMPLETE!');
      console.log('=====================================');
      console.log('✅ Created update_username_comprehensive function');
      console.log('✅ Function updates ALL username-related tables:');
      console.log('   • users.username');
      console.log('   • telegram_users.username');
      console.log('   • user_share_holdings.username (CRITICAL FIX!)');
      console.log('   • competition_leaderboard.username');
      console.log('   • admin_commission_conversion_queue.username');
      console.log('   • referrals.referral_code');
      console.log('   • email_sync_audit_log.username');
      console.log('   • email_sync_backup.username');
      console.log('');
      console.log('🔧 NEXT STEPS:');
      console.log('1. Update UsernameEditor.tsx to use update_username_comprehensive');
      console.log('2. Test with user ID 89 to verify all tables update correctly');
      console.log('3. Fix the existing data inconsistency for user 371 (Naashie1)');
    } else {
      console.log('\n❌ Fix failed - please run the SQL manually in Supabase dashboard');
    }
  })
  .catch((error) => {
    console.error('❌ Script failed:', error.message);
  });