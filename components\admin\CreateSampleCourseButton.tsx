/**
 * CREATE SAMPLE COURSE BUTTON
 * 
 * Simple button component that creates the sample training course
 * by executing the database population script.
 */

import React, { useState } from 'react';
import { getServiceRoleClient } from '../../lib/supabase';

interface CreateSampleCourseButtonProps {
  onCourseCreated?: () => void;
}

export const CreateSampleCourseButton: React.FC<CreateSampleCourseButtonProps> = ({
  onCourseCreated
}) => {
  const [loading, setLoading] = useState(false);
  const [created, setCreated] = useState(false);

  const createSampleCourse = async () => {
    try {
      setLoading(true);
      
      const serviceClient = getServiceRoleClient();
      
      // First, create the lessons
      const lessonsQuery = `
        INSERT INTO training_lessons (
          course_id, title, description, content, lesson_type, video_duration, sort_order, is_preview
        ) VALUES 
        (2, 'Introduction to Aureus Alliance Holdings Affiliate Program', 
         'Learn about the company, commission structure, and affiliate program basics.',
         '<div style="text-align: center; margin-bottom: 30px;"><h1 style="color: #d4af37; font-size: 28px; margin-bottom: 10px;">🏆 Welcome to Aureus Alliance Holdings</h1><p style="font-size: 18px; color: #6b7280;">Your Gateway to Gold Mining Investment Success</p></div><div style="background-color: #0f3460; padding: 20px; border-radius: 12px; margin: 20px 0; border: 2px solid #d4af37;"><h2 style="color: #d4af37; margin-top: 0;">🌟 About Aureus Alliance Holdings</h2><p>Aureus Alliance Holdings (Pty) Ltd is a pioneering gold mining investment company with an ambitious 5-year expansion plan.</p><h3 style="color: #d4af37;">📈 Our Expansion Timeline:</h3><ul><li><strong>2026:</strong> 10 plants covering 250 hectares</li><li><strong>2027:</strong> 25 plants covering 625 hectares</li><li><strong>2028:</strong> 50 plants covering 1,250 hectares</li><li><strong>2029:</strong> 100 plants covering 2,500 hectares</li><li><strong>2030:</strong> 200 plants covering 5,000 hectares</li></ul></div><h2 style="color: #d4af37;">💰 Your Commission Structure</h2><div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;"><div style="background-color: #f3f4f6; padding: 16px; border-radius: 8px; text-align: center;"><h3 style="color: #059669; margin-top: 0;">💵 USDT Commission</h3><p style="font-size: 24px; font-weight: bold; color: #059669;">15%</p><p>Immediate payout on every successful referral</p></div><div style="background-color: #f3f4f6; padding: 16px; border-radius: 8px; text-align: center;"><h3 style="color: #7c3aed; margin-top: 0;">📈 Share Allocation</h3><p style="font-size: 24px; font-weight: bold; color: #7c3aed;">15%</p><p>Long-term wealth building through company shares</p></div></div>',
         'text', 20, 1, true),
        (2, 'Target Audience Identification & Market Research',
         'Learn to identify and research your ideal prospects for gold mining investments.',
         '<h1 style="color: #d4af37; text-align: center; margin-bottom: 20px;">🎯 Identifying Your Ideal Prospects</h1><div style="background-color: #0f3460; padding: 20px; border-radius: 12px; margin: 20px 0; color: white;"><h2 style="color: #d4af37; margin-top: 0;">🔍 Understanding the Gold Mining Investment Market</h2><p>Success in affiliate marketing starts with knowing exactly who you are talking to.</p></div><div style="background-color: #f3f4f6; padding: 16px; border-radius: 8px; border-left: 4px solid #3b82f6;"><h3 style="color: #3b82f6; margin-top: 0;">💼 High-Net-Worth Professionals</h3><ul><li><strong>Age:</strong> 35-65 years</li><li><strong>Income:</strong> $100K+ annually</li><li><strong>Occupation:</strong> Business owners, executives, professionals</li></ul></div>',
         'text', 30, 2, false),
        (2, 'Marketing Strategies & Channel Optimization',
         'Master multi-channel marketing strategies for affiliate success.',
         '<h1 style="color: #d4af37; text-align: center; margin-bottom: 20px;">🚀 Mastering Multi-Channel Marketing</h1><div style="background-color: #0f3460; padding: 20px; border-radius: 12px; margin: 20px 0; color: white;"><h2 style="color: #d4af37; margin-top: 0;">📈 Strategic Marketing Approach</h2><p>Successful affiliate marketing requires a multi-channel approach.</p></div><div style="background-color: #f3f4f6; padding: 16px; border-radius: 8px; border-left: 4px solid #1da1f2;"><h3 style="color: #1da1f2; margin-top: 0;">📱 Social Media Marketing</h3><ul><li><strong>LinkedIn:</strong> Professional networking</li><li><strong>Twitter:</strong> Financial news, crypto communities</li><li><strong>Facebook:</strong> Investment groups</li></ul></div>',
         'video', 40, 3, false),
        (2, 'Compliance, Ethics & Legal Requirements',
         'Understand legal requirements and ethical standards for affiliate marketing.',
         '<h1 style="color: #d4af37; text-align: center; margin-bottom: 20px;">⚖️ Maintaining Ethical Standards</h1><div style="background-color: #fee2e2; padding: 20px; border-radius: 12px; margin: 20px 0; border: 2px solid #ef4444;"><h2 style="color: #ef4444; margin-top: 0;">🚨 CRITICAL IMPORTANCE</h2><p style="font-weight: bold;">Compliance is not optional.</p></div><div style="background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 16px; margin: 16px 0; border-radius: 8px;"><strong>⚠️ INVESTMENT RISK DISCLOSURE:</strong><br>"All investments carry risk of loss."</div>',
         'text', 30, 4, false),
        (2, 'Conversion Optimization & Relationship Building',
         'Learn to convert prospects into successful investors through relationship building.',
         '<h1 style="color: #d4af37; text-align: center; margin-bottom: 20px;">🎯 Converting Prospects into Successful Investors</h1><div style="background-color: #0f3460; padding: 20px; border-radius: 12px; margin: 20px 0; color: white;"><h2 style="color: #d4af37; margin-top: 0;">🚀 The Conversion Mindset</h2><p>Conversion is about building trust and providing value.</p></div><div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; border: 2px solid #d4af37;"><h3 style="color: #d4af37; margin-top: 0;">💎 The Trust-Building Process</h3><p>Building relationships through education, value, and trust.</p></div>',
         'text', 30, 5, false);
      `;

      await serviceClient.rpc('execute_sql', { sql_query: lessonsQuery });

      // Create discussion threads
      const discussionsQuery = `
        INSERT INTO training_discussions (course_id, user_id, title, content, is_pinned, is_locked, reply_count, vote_score) 
        VALUES 
        (2, 4, 'Welcome to Affiliate Marketing Mastery! 🎉',
         '<h2 style="color: #d4af37;">Welcome to the Aureus Alliance Holdings Affiliate Training Program!</h2><p>Congratulations on taking the first step toward building a successful affiliate marketing business.</p><h3>📋 Getting Started:</h3><ul><li>Complete all 5 lessons in order</li><li>Take the assessments to test your knowledge</li><li>Participate in discussions and ask questions</li></ul><p><strong>Ready to get started? Introduce yourself below!</strong></p>',
         true, false, 0, 0),
        (2, 4, 'Share Your Marketing Success Stories 📈',
         '<h2 style="color: #d4af37;">Celebrate Your Wins and Inspire Others!</h2><p>Share your affiliate marketing successes, big and small.</p><h3>💡 What to Share:</h3><ul><li>Successful marketing strategies</li><li>Breakthrough moments</li><li>Creative approaches</li></ul>',
         false, false, 0, 0),
        (2, 4, 'Compliance Questions & Answers ⚖️',
         '<h2 style="color: #d4af37;">Your Compliance Questions Answered</h2><p>This thread is dedicated to addressing questions about legal requirements and ethical practices.</p><h3>🔍 Common Topics:</h3><ul><li>Required disclosures</li><li>Ethical marketing practices</li><li>Regional regulations</li></ul>',
         false, false, 0, 0);
      `;

      await serviceClient.rpc('execute_sql', { sql_query: discussionsQuery });

      // Create sample enrollments
      const enrollmentsQuery = `
        INSERT INTO training_enrollments (course_id, user_id, enrolled_at, progress_percentage, last_accessed_at, completed_at) 
        VALUES 
        (2, 80, '2024-01-15 10:00:00', 100, '2024-01-20 15:30:00', '2024-01-20 15:30:00'),
        (2, 89, '2024-01-18 14:20:00', 100, '2024-01-25 11:45:00', '2024-01-25 11:45:00'),
        (2, 102, '2024-01-22 09:15:00', 75, '2024-01-28 16:20:00', NULL),
        (2, 103, '2024-01-25 11:30:00', 50, '2024-01-29 13:10:00', NULL),
        (2, 104, '2024-01-28 16:45:00', 25, '2024-01-30 10:30:00', NULL);
      `;

      await serviceClient.rpc('execute_sql', { sql_query: enrollmentsQuery });

      setCreated(true);
      if (onCourseCreated) {
        onCourseCreated();
      }

    } catch (error) {
      console.error('Error creating sample course:', error);
      alert('Failed to create sample course. Please try again or check the console for details.');
    } finally {
      setLoading(false);
    }
  };

  if (created) {
    return (
      <div className="bg-green-600 rounded-lg p-6 text-center">
        <div className="text-4xl mb-2">🎉</div>
        <h3 className="text-lg font-semibold text-white mb-2">
          Sample Course Created Successfully!
        </h3>
        <p className="text-green-100 mb-4">
          The "Affiliate Marketing Mastery" course has been created with 5 lessons, 
          discussion forums, and sample student data.
        </p>
        <p className="text-green-100 text-sm">
          Refresh the page to see the course in your Training Academy dashboard.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6 text-center">
      <div className="text-4xl mb-4">🎓</div>
      <h3 className="text-lg font-semibold text-white mb-4">
        Create Sample Training Course
      </h3>
      <p className="text-gray-400 mb-6">
        Create the "Affiliate Marketing Mastery for Aureus Alliance Holdings" sample course 
        to demonstrate all Training Academy features with realistic content and data.
      </p>
      
      <div className="bg-blue-600 rounded-lg p-4 mb-6">
        <h4 className="text-white font-medium mb-2">📚 Course Includes:</h4>
        <ul className="text-blue-100 text-sm space-y-1">
          <li>• 5 comprehensive lessons with rich content</li>
          <li>• Interactive discussion forums</li>
          <li>• Sample student enrollments and progress</li>
          <li>• Professional course presentation</li>
          <li>• Aureus Alliance Holdings branding</li>
        </ul>
      </div>

      <button
        onClick={createSampleCourse}
        disabled={loading}
        className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg text-lg font-semibold transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {loading ? (
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
            <span>Creating Course...</span>
          </div>
        ) : (
          '🚀 Create Sample Course'
        )}
      </button>

      <p className="text-gray-500 text-xs mt-4">
        This will create course ID 2 with all sample content and data.
      </p>
    </div>
  );
};
