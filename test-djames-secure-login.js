import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

dotenv.config();

console.log('🧪 TESTING DJAMES SECURE LOGIN');
console.log('===============================');

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

const testLogin = async () => {
  try {
    const email = '<EMAIL>';
    const password = 'Gunst0n5o0!@#';
    
    console.log(`📧 Testing login for: ${email}`);
    console.log(`🔑 With password: ${password}`);
    
    // Step 1: Get user from database
    console.log('\n📋 Step 1: Getting user from database...');
    const { data: dbUser, error: dbError } = await supabase
      .from('users')
      .select('id, username, email, password_hash, is_active, telegram_id')
      .eq('email', email.toLowerCase())
      .single();
    
    if (dbError) {
      console.error('❌ Database error:', dbError);
      return;
    }
    
    if (!dbUser) {
      console.error('❌ User not found');
      return;
    }
    
    console.log('✅ User found in database:');
    console.log(`   ID: ${dbUser.id}`);
    console.log(`   Username: ${dbUser.username}`);
    console.log(`   Email: ${dbUser.email}`);
    console.log(`   Active: ${dbUser.is_active}`);
    console.log(`   Telegram ID: ${dbUser.telegram_id}`);
    console.log(`   Has password hash: ${!!dbUser.password_hash}`);
    
    // Step 2: Verify password
    console.log('\n📋 Step 2: Verifying password...');
    const passwordValid = await bcrypt.compare(password, dbUser.password_hash);
    
    if (!passwordValid) {
      console.error('❌ Password verification FAILED');
      console.log('   This means the password is incorrect or hash is corrupted');
      return;
    }
    
    console.log('✅ Password verification SUCCESSFUL');
    
    // Step 3: Check account status
    console.log('\n📋 Step 3: Checking account status...');
    if (!dbUser.is_active) {
      console.error('❌ Account is not active');
      return;
    }
    
    console.log('✅ Account is active');
    
    // Step 4: Simulate successful login
    console.log('\n📋 Step 4: Login simulation...');
    const loginResult = {
      user: {
        id: dbUser.id,
        username: dbUser.username,
        email: dbUser.email,
        telegram_id: dbUser.telegram_id,
        is_active: dbUser.is_active
      },
      error: null
    };
    
    console.log('✅ LOGIN SUCCESSFUL!');
    console.log('\n🎉 DJAMES CAN NOW LOGIN SECURELY!');
    console.log('==================================');
    console.log('✅ No bypass needed');
    console.log('✅ Password works correctly');
    console.log('✅ Account is secure');
    console.log('✅ All authentication checks pass');
    
    return loginResult;
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
};

testLogin();
