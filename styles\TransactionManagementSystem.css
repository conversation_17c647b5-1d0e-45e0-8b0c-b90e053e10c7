/* Transaction Management System Styles */
.transaction-management-system {
  padding: 20px;
  background: var(--bg-primary, #1a1a1a);
  color: var(--text-primary, #ffffff);
  min-height: 100vh;
}

/* Header Section */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: var(--bg-secondary, #2a2a2a);
  border-radius: 12px;
  border: 1px solid var(--border-color, #333);
}

.title-section h2 {
  color: var(--gold-primary, #DAA520);
  margin: 0 0 5px 0;
  font-size: 24px;
}

.title-section p {
  color: var(--text-secondary, #cccccc);
  margin: 0;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

/* Filter Panel */
.filter-panel {
  background: var(--bg-secondary, #2a2a2a);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid var(--border-color, #333);
}

.filter-section h3 {
  color: var(--gold-primary, #DAA520);
  margin: 0 0 20px 0;
  font-size: 18px;
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.filter-item {
  display: flex;
  flex-direction: column;
}

.filter-item label {
  color: var(--text-secondary, #cccccc);
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: 5px;
}

.filter-input,
.filter-select {
  background: var(--bg-primary, #1a1a1a);
  border: 1px solid var(--border-color, #333);
  border-radius: 6px;
  padding: 8px 12px;
  color: var(--text-primary, #ffffff);
  font-size: 14px;
}

.filter-input:focus,
.filter-select:focus {
  outline: none;
  border-color: var(--gold-primary, #DAA520);
  box-shadow: 0 0 0 2px rgba(218, 165, 32, 0.2);
}

.filter-summary {
  display: flex;
  align-items: center;
  gap: 10px;
  padding-top: 15px;
  border-top: 1px solid var(--border-color, #333);
  color: var(--text-secondary, #cccccc);
  font-size: 14px;
}

.filter-tag {
  background: var(--gold-primary, #DAA520);
  color: var(--bg-primary, #1a1a1a);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

/* Pagination Controls */
.pagination-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px 20px;
  background: var(--bg-secondary, #2a2a2a);
  border-radius: 8px;
  border: 1px solid var(--border-color, #333);
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--text-secondary, #cccccc);
  font-size: 14px;
}

.page-size-select {
  background: var(--bg-primary, #1a1a1a);
  border: 1px solid var(--border-color, #333);
  border-radius: 4px;
  padding: 4px 8px;
  color: var(--text-primary, #ffffff);
}

.pagination-buttons {
  display: flex;
  align-items: center;
  gap: 15px;
}

.page-info {
  color: var(--text-secondary, #cccccc);
  font-size: 14px;
}

/* Transaction Table */
.transaction-table-container {
  background: var(--bg-secondary, #2a2a2a);
  border-radius: 12px;
  border: 1px solid var(--border-color, #333);
  overflow: hidden;
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-secondary, #cccccc);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color, #333);
  border-top: 3px solid var(--gold-primary, #DAA520);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.table-wrapper {
  overflow-x: auto;
}

.transaction-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.transaction-table th {
  background: var(--bg-primary, #1a1a1a);
  color: var(--text-secondary, #cccccc);
  padding: 15px 12px;
  text-align: left;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 12px;
  border-bottom: 1px solid var(--border-color, #333);
}

.transaction-table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: color 0.2s ease;
}

.transaction-table th.sortable:hover {
  color: var(--gold-primary, #DAA520);
}

.transaction-table td {
  padding: 15px 12px;
  border-bottom: 1px solid var(--border-color, #333);
  vertical-align: top;
}

.transaction-row:hover {
  background: rgba(218, 165, 32, 0.05);
}

/* Table Cell Styles */
.date-cell .date-info {
  display: flex;
  flex-direction: column;
}

.date-cell .date {
  font-weight: 600;
  color: var(--text-primary, #ffffff);
}

.date-cell .time {
  font-size: 12px;
  color: var(--text-secondary, #cccccc);
}

.user-cell .user-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-cell .name {
  font-weight: 600;
  color: var(--text-primary, #ffffff);
}

.user-cell .email {
  font-size: 12px;
  color: var(--text-secondary, #cccccc);
}

.user-cell .aureus-id {
  font-size: 11px;
  color: var(--gold-primary, #DAA520);
  font-weight: 500;
}

.payment-method-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.payment-method-badge.usdt {
  background: #28a745;
  color: white;
}

.payment-method-badge.bank_transfer {
  background: #007bff;
  color: white;
}

.network-info {
  display: block;
  font-size: 10px;
  color: var(--text-secondary, #cccccc);
  margin-top: 2px;
}

.amount-cell .amount-info {
  display: flex;
  flex-direction: column;
}

.amount-cell .amount {
  font-weight: 600;
  color: var(--text-primary, #ffffff);
}

.amount-cell .currency {
  font-size: 12px;
  color: var(--text-secondary, #cccccc);
}

.shares-cell .shares {
  font-weight: 600;
  color: var(--gold-primary, #DAA520);
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.pending {
  background: #ffc107;
  color: #000;
}

.status-badge.approved {
  background: #28a745;
  color: white;
}

.status-badge.rejected {
  background: #dc3545;
  color: white;
}

.kyc-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.kyc-badge.approved {
  background: #28a745;
  color: white;
}

.kyc-badge.pending {
  background: #ffc107;
  color: #000;
}

.kyc-badge.rejected,
.kyc-badge.unknown {
  background: #6c757d;
  color: white;
}

/* Button Styles */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--gold-primary, #DAA520);
  color: var(--bg-primary, #1a1a1a);
}

.btn-primary:hover:not(:disabled) {
  background: var(--gold-secondary, #B8860B);
}

.btn-secondary {
  background: var(--bg-primary, #1a1a1a);
  color: var(--text-primary, #ffffff);
  border: 1px solid var(--border-color, #333);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--border-color, #333);
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: var(--bg-secondary, #2a2a2a);
  border-radius: 12px;
  border: 1px solid var(--border-color, #333);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border-color, #333);
}

.modal-header h3 {
  color: var(--gold-primary, #DAA520);
  margin: 0;
  font-size: 20px;
}

.modal-close {
  background: none;
  border: none;
  color: var(--text-secondary, #cccccc);
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: var(--bg-primary, #1a1a1a);
  color: var(--text-primary, #ffffff);
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid var(--border-color, #333);
  display: flex;
  justify-content: flex-end;
}

/* Transaction Details */
.transaction-details {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.detail-section {
  background: var(--bg-primary, #1a1a1a);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid var(--border-color, #333);
}

.detail-section h4 {
  color: var(--gold-primary, #DAA520);
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.detail-item label {
  color: var(--text-secondary, #cccccc);
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.detail-item span {
  color: var(--text-primary, #ffffff);
  font-size: 14px;
  word-break: break-all;
}

.hash-text,
.address-text {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  background: var(--bg-secondary, #2a2a2a);
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid var(--border-color, #333);
}

.proof-link {
  color: var(--gold-primary, #DAA520);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.2s ease;
}

.proof-link:hover {
  color: var(--gold-secondary, #B8860B);
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  .transaction-management-system {
    padding: 10px;
  }

  .header-section {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .filter-grid {
    grid-template-columns: 1fr;
  }

  .pagination-controls {
    flex-direction: column;
    gap: 15px;
  }

  .transaction-table {
    font-size: 12px;
  }

  .transaction-table th,
  .transaction-table td {
    padding: 10px 8px;
  }

  .modal-content {
    margin: 10px;
    max-height: 95vh;
  }

  .detail-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .action-buttons {
    flex-direction: column;
    width: 100%;
  }

  .filter-summary {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .transaction-table th,
  .transaction-table td {
    padding: 8px 6px;
  }

  .user-cell .user-info,
  .date-cell .date-info,
  .amount-cell .amount-info {
    gap: 1px;
  }
}

/* Dark Theme Enhancements */
:root {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2a2a2a;
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --border-color: #333333;
  --gold-primary: #DAA520;
  --gold-secondary: #B8860B;
}

/* Light Theme Support */
@media (prefers-color-scheme: light) {
  :root {
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --text-primary: #333333;
    --text-secondary: #666666;
    --border-color: #dee2e6;
  }
}

/* Print Styles */
@media print {
  .transaction-management-system {
    background: white;
    color: black;
  }

  .filter-panel,
  .pagination-controls,
  .action-buttons,
  .actions-cell {
    display: none;
  }

  .transaction-table {
    border: 1px solid #000;
  }

  .transaction-table th,
  .transaction-table td {
    border: 1px solid #000;
    padding: 8px;
  }
}
