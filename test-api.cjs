const http = require('http');

async function testAPI() {
  console.log('🧪 Testing updated duplicate check API...');
  
  const postData = JSON.stringify({
    email: '<EMAIL>',
    username: 'brandnewusername123'
  });

  const options = {
    hostname: 'localhost',
    port: 8002,
    path: '/api/check-duplicates',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          console.log('📧 API Response:', result);
          
          if (res.statusCode === 200) {
            console.log('✅ API working correctly');
            console.log('📊 Results:', {
              emailDuplicate: result.emailDuplicate,
              usernameDuplicate: result.usernameDuplicate
            });
          } else {
            console.error('❌ API error:', result);
          }
          resolve(result);
        } catch (error) {
          console.error('❌ Parse error:', error.message);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.error('❌ Request error:', error.message);
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

testAPI().catch(console.error);
