const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function addContactFields() {
  console.log('🚀 Adding contact fields to users table...');

  try {
    // Add phone_number column
    console.log('📱 Adding phone_number column...');
    const { error: phoneError } = await supabase.rpc('exec_sql', {
      sql: 'ALTER TABLE users ADD COLUMN IF NOT EXISTS phone_number VARCHAR(20);'
    });

    if (phoneError) {
      console.error('❌ Error adding phone_number:', phoneError);
    } else {
      console.log('✅ phone_number column added successfully');
    }

    // Add telegram_username column
    console.log('💬 Adding telegram_username column...');
    const { error: telegramError } = await supabase.rpc('exec_sql', {
      sql: 'ALTER TABLE users ADD COLUMN IF NOT EXISTS telegram_username VARCHAR(50);'
    });

    if (telegramError) {
      console.error('❌ Error adding telegram_username:', telegramError);
    } else {
      console.log('✅ telegram_username column added successfully');
    }

    // Create index for better performance
    console.log('🔍 Creating index on telegram_username...');
    const { error: indexError } = await supabase.rpc('exec_sql', {
      sql: 'CREATE INDEX IF NOT EXISTS idx_users_telegram_username ON users(telegram_username);'
    });

    if (indexError) {
      console.error('❌ Error creating index:', indexError);
    } else {
      console.log('✅ Index created successfully');
    }

    // Test the query
    console.log('🧪 Testing payment query with new fields...');
    const { data: testData, error: testError } = await supabase
      .from('crypto_payment_transactions')
      .select(`
        id,
        amount,
        users!inner(
          id,
          username,
          email,
          full_name,
          phone_number,
          telegram_username
        )
      `)
      .limit(1);

    if (testError) {
      console.error('❌ Test query failed:', testError);
    } else {
      console.log('✅ Test query successful!');
      console.log(`📊 Found ${testData.length} test records`);
    }

    console.log('\n🎉 Contact fields added successfully!');
    console.log('📋 Summary:');
    console.log('✅ phone_number column added to users table');
    console.log('✅ telegram_username column added to users table');
    console.log('✅ Index created for performance');
    console.log('✅ Payment query tested and working');
    console.log('\n🚀 You can now refresh the payment management page!');

  } catch (error) {
    console.error('❌ Failed to add contact fields:', error);
  }
}

addContactFields();
