#!/usr/bin/env node

/**
 * Test signInWithTelegramId Function
 * 
 * This script directly tests the signInWithTelegramId function
 * to see what it returns for a specific user.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ Missing SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Replicate the signInWithTelegramId function exactly as it is in the app
async function testSignInWithTelegramId(telegramId) {
  try {
    console.log('🔐 Testing signInWithTelegramId for:', telegramId)

    const telegramIdNum = parseInt(telegramId)
    if (isNaN(telegramIdNum)) {
      return { success: false, error: 'Invalid Telegram ID format' }
    }

    // CRITICAL FIX: First check if user exists in users table with telegram_id (profile completed)
    console.log('🔍 Checking if profile is completed by looking for telegram_id in users table')

    const { data: userWithTelegramId, error: userLookupError } = await supabase
      .from('users')
      .select('*')
      .eq('telegram_id', telegramIdNum)
      .maybeSingle()

    if (userLookupError) {
      console.error('❌ Error looking up user by telegram_id:', userLookupError)
      return { success: false, error: 'Database error. Please try again.' }
    }

    if (userWithTelegramId) {
      // Profile is completed - user exists in users table with telegram_id
      console.log('✅ Profile completed - found user in users table:', userWithTelegramId.email)

      const user = userWithTelegramId

      // Check if user profile is complete - CRITICAL: Include country_selection_completed check
      const needsProfileCompletion = !user.email || 
                                   !user.password_hash || 
                                   !user.full_name || 
                                   !user.phone || 
                                   !user.country_of_residence ||
                                   !user.country_selection_completed

      console.log('🔍 Profile completion check result:', {
        needsProfileCompletion,
        email: !!user.email,
        password_hash: !!user.password_hash,
        full_name: !!user.full_name,
        phone: !!user.phone,
        country_of_residence: !!user.country_of_residence,
        country_selection_completed: !!user.country_selection_completed
      });

      if (needsProfileCompletion) {
        console.log('⚠️ User found but profile incomplete, needs profile completion')
        return {
          success: true,
          user: {
            id: `telegram_${telegramIdNum}`,
            email: user.email,
            database_user: user,
            account_type: 'telegram_direct',
            needsProfileCompletion: true,
            user_metadata: {
              telegram_id: telegramIdNum,
              full_name: user.full_name,
              username: user.username,
              profile_completion_required: true
            }
          }
        }
      }

      console.log('✅ Profile complete - user can login directly')

      // Create authenticated user object for completed profile
      const authenticatedUser = {
        id: `telegram_${telegramIdNum}`,
        email: user.email,
        database_user: user,
        account_type: 'telegram_direct',
        needsProfileCompletion: false,
        user_metadata: {
          telegram_id: telegramIdNum,
          full_name: user.full_name,
          username: user.username,
          profile_completion_required: false
        }
      }

      console.log('✅ Returning completed profile user:', {
        needsProfileCompletion: authenticatedUser.needsProfileCompletion,
        profile_completion_required: authenticatedUser.user_metadata.profile_completion_required,
        account_type: authenticatedUser.account_type
      });

      return { success: true, user: authenticatedUser }
    }

    // Profile not completed - check telegram_users table for initial registration
    console.log('🔍 Profile not completed - checking telegram_users table')

    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramIdNum)
      .single()

    if (telegramError || !telegramUser) {
      console.log('❌ Telegram user not found:', telegramId, telegramError?.message)
      return { success: false, error: 'Telegram ID not found. Please contact support.' }
    }

    console.log('✅ Found telegram user, needs profile completion')
    return { success: true, needsProfileCompletion: true, telegramUser }

  } catch (err) {
    console.error('❌ signInWithTelegramId error:', err)
    return { success: false, error: 'Login failed. Please try again.' }
  }
}

// Test with a specific Telegram ID
const testTelegramId = process.argv[2] || '1393852532'; // Default to TTTFOUNDER
console.log(`🧪 Testing signInWithTelegramId Function\n`);
testSignInWithTelegramId(testTelegramId).then(result => {
  console.log('\n🎯 Final Result:', JSON.stringify(result, null, 2));
});
