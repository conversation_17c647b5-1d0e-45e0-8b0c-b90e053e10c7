
// DIRECT LOGIN FIX FOR YOUR ACCOUNT
// Run this in browser console after login fails

const fixMyLogin = async () => {
  console.log('🔧 Fixing login for Telegram ID: **********');
  
  // Your complete session data
  const sessionData = {
  "userId": 89,
  "username": "<PERSON>",
  "email": "<EMAIL>",
  "fullName": "<PERSON>",
  "phone": "+27 74 449 3251",
  "address": null,
  "country": "ZAF",
  "isActive": true,
  "isVerified": true,
  "isAdmin": false,
  "telegramId": **********,
  "telegramUsername": "<PERSON>",
  "telegramConnected": true,
  "telegramRegistered": true,
  "loginMethod": "telegram",
  "sessionStart": "2025-07-28T13:13:01.364Z",
  "lastActivity": "2025-07-28T13:13:01.364Z"
};
  
  // Your user data
  const userData = {
  "id": 89,
  "username": "<PERSON><PERSON>",
  "email": "<EMAIL>",
  "password_hash": "$2b$12$MWs6j1hQdZjKwipl2Bgt5eGPMa0BbtfHRRs6c6TaYbKE5V.RsYcq6",
  "full_name": "Donovan",
  "phone": "+27 74 449 3251",
  "address": null,
  "is_active": true,
  "is_verified": true,
  "verification_token": null,
  "reset_token": "982a67a8bae1b230fa85912d4dc0c23ec2f1dd2f72b24bee3db3f5bf50cfdf9f",
  "reset_token_expires": "2025-07-28T11:52:26.956+00:00",
  "created_at": "2025-07-12T15:57:40.993302+00:00",
  "updated_at": "2025-07-28T09:40:20.652043+00:00",
  "telegram_id": **********,
  "country_of_residence": "ZAF",
  "country_name": "South Africa",
  "country_selected_at": "2025-07-12T15:57:55.218+00:00",
  "country_updated_at": "2025-07-12T15:57:55.218+00:00",
  "country_selection_completed": true,
  "role": "user",
  "sponsor_user_id": null,
  "is_admin": false,
  "auth_user_id": "3c6c8f51-b4d6-4559-99e4-c7e599886d45",
  "phone_number": null,
  "telegram_username": "Donovan_James",
  "first_name": "Donovan",
  "last_name": "James",
  "total_referrals": 0,
  "total_earnings": 0,
  "telegram_connected": true
};
  
  // Your Telegram data
  const telegramData = {
  "telegram_id": **********,
  "username": "Donovan_James",
  "user_id": 89,
  "is_registered": true,
  "connected": true
};
  
  // Store all data in localStorage
  localStorage.setItem('aureus_session', JSON.stringify(sessionData));
  localStorage.setItem('aureus_user', JSON.stringify(userData));
  localStorage.setItem('telegram_user', JSON.stringify(telegramData));
  localStorage.setItem('aureus_telegram_user', JSON.stringify({
    id: 'telegram_' + **********,
    email: '<EMAIL>',
    database_user: userData,
    account_type: 'telegram_direct',
    user_metadata: {
      telegram_id: **********,
      telegram_username: 'Donovan_James',
      full_name: 'Donovan',
      username: 'Donovan_James',
      telegram_connected: true,
      telegram_registered: true
    }
  }));
  
  console.log('✅ Login data fixed for your account');
  console.log('🔄 Refreshing page...');
  
  // Refresh to apply changes
  window.location.reload();
};

// Run the fix
fixMyLogin();
