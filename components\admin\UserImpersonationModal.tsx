import React, { useState } from 'react'
import { supabase, getServiceRoleClient } from '../../lib/supabase'
import { logAdminAction } from '../../lib/adminAuth'

interface User {
  id: number
  username: string
  email: string
  full_name: string | null
  is_active: boolean
  role: string
  created_at: string
}

interface UserImpersonationModalProps {
  user: User
  isOpen: boolean
  onClose: () => void
  adminUser?: any
}

export const UserImpersonationModal: React.FC<UserImpersonationModalProps> = ({
  user,
  isOpen,
  onClose,
  adminUser
}) => {
  const [loading, setLoading] = useState(false)
  const [confirmText, setConfirmText] = useState('')
  const [showConfirmation, setShowConfirmation] = useState(false)

  const handleImpersonateUser = async () => {
    if (!showConfirmation) {
      setShowConfirmation(true)
      return
    }

    if (confirmText !== user.username) {
      alert('Please type the username exactly to confirm impersonation')
      return
    }

    try {
      setLoading(true)

      // Log the impersonation attempt
      await logAdminAction(
        adminUser?.email || 'unknown_admin',
        'USER_IMPERSONATION_START',
        'user',
        user.id.toString(),
        {
          target_username: user.username,
          target_email: user.email,
          impersonation_reason: 'admin_support',
          timestamp: new Date().toISOString()
        }
      )

      // Store impersonation data in session storage for the new tab
      const impersonationData = {
        originalAdmin: {
          email: adminUser?.email,
          id: adminUser?.id
        },
        targetUser: {
          id: user.id,
          username: user.username,
          email: user.email,
          full_name: user.full_name
        },
        impersonationStartTime: new Date().toISOString(),
        sessionId: `imp_${Date.now()}_${user.id}`
      }

      // Store in localStorage for the new tab to access
      localStorage.setItem('admin_impersonation_data', JSON.stringify(impersonationData))

      // Add a small delay to ensure localStorage is written
      await new Promise(resolve => setTimeout(resolve, 100))

      // Create a special login URL for the user with encoded session data as backup
      const encodedSessionData = encodeURIComponent(JSON.stringify(impersonationData))
      const impersonationUrl = `/dashboard?admin_impersonate=${user.id}&session=${impersonationData.sessionId}&session_data=${encodedSessionData}`

      // Open in new tab
      const newTab = window.open(impersonationUrl, '_blank')
      
      if (!newTab) {
        alert('Please allow popups to use the impersonation feature')
        return
      }

      // Show success message
      alert(`Successfully initiated impersonation session for ${user.username}. Check the new tab.`)
      
      onClose()
    } catch (error: any) {
      console.error('Error starting impersonation:', error)
      alert('Failed to start impersonation: ' + error.message)
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    setShowConfirmation(false)
    setConfirmText('')
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-lg w-full max-w-2xl">
        {/* Header */}
        <div className="bg-red-900/20 border-b border-red-500/30 px-6 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-bold text-red-400">
                ⚠️ Admin User Impersonation
              </h2>
              <p className="text-red-300 text-sm">
                Login as: {user.full_name || user.username} ({user.email})
              </p>
            </div>
            <button
              onClick={handleCancel}
              className="text-gray-400 hover:text-white text-2xl"
            >
              ×
            </button>
          </div>
        </div>

        <div className="p-6">
          {/* Warning Section */}
          <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4 mb-6">
            <h3 className="text-red-400 font-semibold mb-2">🚨 Security Warning</h3>
            <ul className="text-red-300 text-sm space-y-1">
              <li>• This action will be logged in the audit trail</li>
              <li>• You will have full access to the user's account</li>
              <li>• Use this feature only for legitimate support purposes</li>
              <li>• The user will not be notified of this impersonation</li>
              <li>• Session will open in a new tab with clear admin indicators</li>
            </ul>
          </div>

          {/* User Information */}
          <div className="bg-gray-800 rounded-lg p-4 mb-6">
            <h3 className="text-white font-semibold mb-3">👤 Target User Information</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-400">User ID:</span>
                <span className="text-white ml-2">{user.id}</span>
              </div>
              <div>
                <span className="text-gray-400">Username:</span>
                <span className="text-white ml-2">@{user.username}</span>
              </div>
              <div>
                <span className="text-gray-400">Email:</span>
                <span className="text-white ml-2">{user.email}</span>
              </div>
              <div>
                <span className="text-gray-400">Full Name:</span>
                <span className="text-white ml-2">{user.full_name || 'Not set'}</span>
              </div>
              <div>
                <span className="text-gray-400">Role:</span>
                <span className="text-white ml-2">{user.role}</span>
              </div>
              <div>
                <span className="text-gray-400">Status:</span>
                <span className={`ml-2 ${user.is_active ? 'text-green-400' : 'text-red-400'}`}>
                  {user.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>
              <div className="col-span-2">
                <span className="text-gray-400">Created:</span>
                <span className="text-white ml-2">
                  {new Date(user.created_at).toLocaleDateString()} at {new Date(user.created_at).toLocaleTimeString()}
                </span>
              </div>
            </div>
          </div>

          {/* Confirmation Section */}
          {!showConfirmation ? (
            <div className="text-center">
              <p className="text-gray-300 mb-4">
                Click the button below to start impersonation session
              </p>
              <button
                onClick={handleImpersonateUser}
                className="px-6 py-3 bg-red-600 text-white font-medium rounded-lg hover:bg-red-500 transition-colors"
              >
                🔐 Start Impersonation
              </button>
            </div>
          ) : (
            <div>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Type the username "<strong>{user.username}</strong>" to confirm:
                </label>
                <input
                  type="text"
                  value={confirmText}
                  onChange={(e) => setConfirmText(e.target.value)}
                  placeholder={`Type "${user.username}" here`}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-red-500 focus:outline-none"
                  autoFocus
                />
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={handleCancel}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  onClick={handleImpersonateUser}
                  disabled={loading || confirmText !== user.username}
                  className={`px-6 py-2 font-medium rounded-lg transition-colors ${
                    loading || confirmText !== user.username
                      ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                      : 'bg-red-600 text-white hover:bg-red-500'
                  }`}
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white inline-block mr-2"></div>
                      Starting...
                    </>
                  ) : (
                    '🚀 Confirm Impersonation'
                  )}
                </button>
              </div>
            </div>
          )}

          {/* Admin Information */}
          <div className="mt-6 pt-4 border-t border-gray-700">
            <div className="text-xs text-gray-400">
              <strong>Admin:</strong> {adminUser?.email || 'Unknown'} | 
              <strong> Time:</strong> {new Date().toLocaleString()} |
              <strong> Action:</strong> USER_IMPERSONATION_START
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
