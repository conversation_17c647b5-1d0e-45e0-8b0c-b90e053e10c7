#!/usr/bin/env node

/**
 * CREATE PASSWORD RESET TOKENS TABLE
 * 
 * This script creates the password_reset_tokens table for secure password resets
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createPasswordResetTable() {
  console.log('🔐 Creating password_reset_tokens table...');
  
  try {
    // Test if table already exists
    const { data: existingTable, error: testError } = await supabase
      .from('password_reset_tokens')
      .select('id')
      .limit(1);

    if (!testError) {
      console.log('✅ password_reset_tokens table already exists');
      return true;
    }

    console.log('📝 Table does not exist, manual creation required...');
    console.log('\n🔧 MANUAL SETUP INSTRUCTIONS:');
    console.log('=====================================');
    console.log('1. Go to Supabase Dashboard > SQL Editor');
    console.log('2. Copy and paste the following SQL:');
    console.log('\n-- CREATE PASSWORD RESET TOKENS TABLE');
    console.log(`
-- Create password_reset_tokens table
CREATE TABLE IF NOT EXISTS public.password_reset_tokens (
    id SERIAL PRIMARY KEY,
    token VARCHAR(64) UNIQUE NOT NULL,
    user_id INTEGER NOT NULL,
    email VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_used BOOLEAN DEFAULT false,
    used_at TIMESTAMP WITH TIME ZONE,
    used_ip INET,
    ip_address INET,
    user_agent TEXT,
    
    -- Foreign key constraint
    CONSTRAINT fk_password_reset_tokens_user_id 
        FOREIGN KEY (user_id) 
        REFERENCES public.users(id) 
        ON DELETE CASCADE
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_token ON public.password_reset_tokens(token);
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_user_id ON public.password_reset_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_expires_at ON public.password_reset_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_email ON public.password_reset_tokens(email);

-- Enable Row Level Security
ALTER TABLE public.password_reset_tokens ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only see their own reset tokens
CREATE POLICY "password_reset_tokens_user_policy" ON public.password_reset_tokens
    FOR ALL USING (
        user_id = (auth.jwt() ->> 'sub')::integer OR
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE user_id = (auth.jwt() ->> 'sub')::integer 
            AND is_active = true
        ) OR
        current_setting('role') = 'service_role'
    );

-- Create function to automatically clean up expired tokens
CREATE OR REPLACE FUNCTION cleanup_expired_reset_tokens() RETURNS void AS $$
BEGIN
    UPDATE public.password_reset_tokens 
    SET is_used = true
    WHERE expires_at < NOW() AND is_used = false;
    
    -- Log cleanup activity
    INSERT INTO admin_audit_logs (
        admin_email,
        action,
        target_type,
        target_id,
        metadata,
        created_at
    ) VALUES (
        'token_cleanup',
        'EXPIRED_RESET_TOKENS_CLEANED',
        'password_reset_maintenance',
        'automatic',
        jsonb_build_object(
            'cleanup_time', NOW(),
            'expired_tokens_count', (
                SELECT COUNT(*) FROM public.password_reset_tokens 
                WHERE expires_at < NOW() AND is_used = true
                AND used_at > NOW() - INTERVAL '1 minute'
            )
        ),
        NOW()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON public.password_reset_tokens TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.password_reset_tokens TO service_role;
GRANT USAGE ON SEQUENCE password_reset_tokens_id_seq TO authenticated;
GRANT USAGE ON SEQUENCE password_reset_tokens_id_seq TO service_role;

-- Log table creation
INSERT INTO admin_audit_logs (
    admin_email,
    action,
    target_type,
    target_id,
    metadata,
    created_at
) VALUES (
    'security_system',
    'PASSWORD_RESET_TABLE_CREATED',
    'database_schema',
    'password_reset_tokens',
    jsonb_build_object(
        'table_name', 'password_reset_tokens',
        'creation_date', NOW(),
        'security_features', ARRAY['RLS_enabled', 'automatic_cleanup', 'secure_tokens'],
        'purpose', 'secure_password_reset'
    ),
    NOW()
);

COMMENT ON TABLE public.password_reset_tokens IS 'Secure password reset tokens with automatic expiration';
COMMENT ON COLUMN public.password_reset_tokens.token IS 'Cryptographically secure reset token (64 chars)';
COMMENT ON COLUMN public.password_reset_tokens.expires_at IS 'Token expiration time (1 hour from creation)';
COMMENT ON COLUMN public.password_reset_tokens.is_used IS 'Whether token has been used or expired';
    `);

    console.log('\n3. Execute the SQL script');
    console.log('4. Re-run this script to verify creation');
    console.log('\n⚠️ This table is required for secure password reset functionality');

    return false;

  } catch (error) {
    console.error('❌ Password reset table creation failed:', error);
    return false;
  }
}

// Test password reset functionality
async function testPasswordResetSystem() {
  console.log('\n🧪 Testing password reset system...');
  
  try {
    // Test table access
    const { data: tokenTest, error: tokenError } = await supabase
      .from('password_reset_tokens')
      .select('count')
      .limit(0);

    if (tokenError) {
      console.log('❌ Cannot access password_reset_tokens table');
      return false;
    }

    console.log('✅ password_reset_tokens table accessible');

    // Test token generation
    const crypto = await import('crypto');
    const token1 = crypto.randomBytes(32).toString('hex');
    const token2 = crypto.randomBytes(32).toString('hex');

    if (token1.length !== 64 || token2.length !== 64 || token1 === token2) {
      console.log('❌ Token generation test failed');
      return false;
    }

    console.log('✅ Token generation working');

    // Test rate limiting import
    try {
      const { passwordResetRateLimiter } = await import('./lib/rateLimiting.js');
      const rateLimitTest = passwordResetRateLimiter.checkRateLimit('<EMAIL>', false);
      
      if (rateLimitTest.allowed) {
        console.log('✅ Rate limiting integration working');
      } else {
        console.log('⚠️ Rate limiting may be too restrictive');
      }
    } catch (importError) {
      console.log('⚠️ Rate limiting integration not available');
    }

    console.log('✅ Password reset system test completed');
    return true;

  } catch (error) {
    console.error('❌ Password reset system test failed:', error);
    return false;
  }
}

// Run the setup
console.log('🚀 Starting password reset system setup...\n');

createPasswordResetTable()
  .then(success => {
    if (success) {
      return testPasswordResetSystem();
    } else {
      console.log('\n❌ Table creation required before testing');
      return false;
    }
  })
  .then(testSuccess => {
    if (testSuccess) {
      console.log('\n✅ Password reset system ready!');
      console.log('📋 Features available:');
      console.log('   ✅ Secure token generation');
      console.log('   ✅ Rate limiting protection');
      console.log('   ✅ Automatic token expiration');
      console.log('   ✅ Security event logging');
      process.exit(0);
    } else {
      console.log('\n⚠️ Password reset system needs manual setup');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  });
