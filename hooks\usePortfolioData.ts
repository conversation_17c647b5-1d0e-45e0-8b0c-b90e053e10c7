/**
 * PORTFOLIO DATA HOOK
 * 
 * Manages portfolio data fetching, calculations, and state management
 * for comprehensive portfolio display and analysis.
 */

import { useState, useEffect, useCallback } from 'react';
import { supabase, getServiceRoleClient } from '../lib/supabase';

export interface ShareHolding {
  id: string;
  shares_purchased: number;
  total_amount: number;
  price_per_share: number;
  phase_name: string;
  phase_number: number;
  purchase_date: string;
  status: string;
  transaction_reference?: string;
}

export interface PortfolioSummary {
  totalShares: number;
  totalInvested: number;
  currentValue: number;
  unrealizedGains: number;
  unrealizedGainsPercent: number;
  averageCostPerShare: number;
  projectedAnnualDividend: number;
}

export interface KYCStatus {
  status: string;
  verification_level: string;
  documents_submitted: boolean;
  facial_recognition_completed: boolean;
  manual_review_required: boolean;
  rejection_reason?: string;
}

export interface UsePortfolioDataReturn {
  holdings: ShareHolding[];
  summary: PortfolioSummary;
  kycStatus: KYCStatus | null;
  currentPhase: any;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const usePortfolioData = (userId: number): UsePortfolioDataReturn => {
  const [holdings, setHoldings] = useState<ShareHolding[]>([]);
  const [summary, setSummary] = useState<PortfolioSummary>({
    totalShares: 0,
    totalInvested: 0,
    currentValue: 0,
    unrealizedGains: 0,
    unrealizedGainsPercent: 0,
    averageCostPerShare: 0,
    projectedAnnualDividend: 0
  });
  const [kycStatus, setKycStatus] = useState<KYCStatus | null>(null);
  const [currentPhase, setCurrentPhase] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadPortfolioData = useCallback(async () => {
    if (!userId) return;

    try {
      setLoading(true);
      setError(null);

      const serviceClient = getServiceRoleClient();

      // Load share holdings
      const { data: holdingsData, error: holdingsError } = await serviceClient
        .from('aureus_share_purchases')
        .select(`
          id,
          shares_purchased,
          total_amount,
          status,
          created_at
        `)
        .eq('user_id', userId)
        .eq('status', 'active')
        .order('created_at', { ascending: false });

      if (holdingsError) {
        throw new Error(`Failed to load holdings: ${holdingsError.message}`);
      }

      // Format holdings data
      const formattedHoldings: ShareHolding[] = holdingsData?.map(holding => ({
        id: holding.id,
        shares_purchased: holding.shares_purchased,
        total_amount: holding.total_amount,
        price_per_share: holding.price_per_share,
        phase_name: holding.investment_phases?.phase_name || 'Unknown Phase',
        phase_number: holding.investment_phases?.phase_number || 0,
        purchase_date: holding.purchase_date,
        status: holding.status,
        transaction_reference: holding.transaction_reference
      })) || [];

      setHoldings(formattedHoldings);

      // Load current active phase
      const { data: phaseData, error: phaseError } = await serviceClient
        .from('investment_phases')
        .select('*')
        .eq('is_active', true)
        .single();

      if (phaseError && phaseError.code !== 'PGRST116') {
        console.warn('No active phase found:', phaseError);
      } else {
        setCurrentPhase(phaseData);
      }

      // Load KYC status
      const { data: kycData, error: kycError } = await serviceClient
        .from('kyc_information')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (kycError && kycError.code !== 'PGRST116') {
        console.warn('No KYC data found:', kycError);
        setKycStatus(null);
      } else if (kycData) {
        setKycStatus({
          status: kycData.verification_status || 'not_started',
          verification_level: kycData.verification_level || 'none',
          documents_submitted: Boolean(kycData.id_document_url || kycData.proof_of_address_url),
          facial_recognition_completed: Boolean(kycData.facial_recognition_data),
          manual_review_required: kycData.manual_review_required || false,
          rejection_reason: kycData.rejection_reason
        });
      }

      // Calculate portfolio summary
      const totalShares = formattedHoldings.reduce((sum, holding) => sum + holding.shares_purchased, 0);
      const totalInvested = formattedHoldings.reduce((sum, holding) => sum + holding.total_amount, 0);
      const currentSharePrice = phaseData?.price_per_share || 5.00;
      const currentValue = totalShares * currentSharePrice;
      const unrealizedGains = currentValue - totalInvested;
      const unrealizedGainsPercent = totalInvested > 0 ? (unrealizedGains / totalInvested) * 100 : 0;
      const averageCostPerShare = totalShares > 0 ? totalInvested / totalShares : 0;

      // Simple dividend projection (this would be more complex in reality)
      const projectedAnnualDividend = totalShares * 0.5; // Placeholder calculation

      setSummary({
        totalShares,
        totalInvested,
        currentValue,
        unrealizedGains,
        unrealizedGainsPercent,
        averageCostPerShare,
        projectedAnnualDividend
      });

    } catch (err: any) {
      console.error('Portfolio data loading error:', err);
      setError(err.message || 'Failed to load portfolio data');
    } finally {
      setLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    loadPortfolioData();
  }, [loadPortfolioData]);

  return {
    holdings,
    summary,
    kycStatus,
    currentPhase,
    loading,
    error,
    refetch: loadPortfolioData
  };
};
