/**
 * VERIFICATION EMAIL TEMPLATE
 * 
 * Template for email verification codes used in registration,
 * password reset, and account security operations.
 */

import { BaseEmailTemplate } from './BaseEmailTemplate';
import { EmailVerificationData } from '../types/EmailTypes';

export class VerificationEmailTemplate extends BaseEmailTemplate<EmailVerificationData> {
  protected emailType = 'verification' as const;

  protected generateSubject(data: EmailVerificationData): string {
    const purposeMap = {
      registration: 'Verify Your Email Address',
      account_update: 'Verify Account Changes',
      withdrawal: 'Verify Withdrawal Request',
      password_reset: 'Reset Your Password',
      telegram_connection: 'Verify Telegram Connection'
    };

    return `${purposeMap[data.purpose]} - ${this.brandingConfig.companyName}`;
  }

  protected generateBody(data: EmailVerificationData): string {
    const purposeContent = this.getPurposeContent(data);
    const expiryMinutes = data.expiryMinutes || 15;

    return `
      <div class="email-body">
        <h2 style="color: ${this.brandingConfig.colors.primary}; margin-bottom: 20px;">
          ${purposeContent.title}
        </h2>
        
        <p style="margin-bottom: 15px; color: ${this.brandingConfig.colors.text};">
          Hello ${data.fullName || data.userName || 'User'},
        </p>
        
        <p style="margin-bottom: 20px; color: ${this.brandingConfig.colors.text};">
          ${purposeContent.description}
        </p>
        
        <div class="alert alert-info">
          <h3 style="margin-top: 0; margin-bottom: 15px; color: #60a5fa;">
            Your Verification Code
          </h3>
          <div style="text-align: center; margin: 20px 0;">
            <span style="
              font-size: 32px; 
              font-weight: bold; 
              letter-spacing: 8px; 
              color: ${this.brandingConfig.colors.primary};
              background-color: rgba(212, 175, 55, 0.1);
              padding: 15px 25px;
              border-radius: 8px;
              display: inline-block;
              font-family: monospace;
            ">
              ${data.code}
            </span>
          </div>
          <p style="margin: 0; color: #60a5fa; text-align: center; font-size: 14px;">
            Enter this code to complete your ${data.purpose.replace('_', ' ')}
          </p>
        </div>
        
        <div class="alert alert-warning">
          <h4 style="margin-top: 0; margin-bottom: 10px; color: #fbbf24;">
            Important Security Information
          </h4>
          <ul style="margin: 0; padding-left: 20px; color: #fbbf24;">
            <li style="margin-bottom: 8px;">This code expires in ${expiryMinutes} minutes</li>
            <li style="margin-bottom: 8px;">Never share this code with anyone</li>
            <li style="margin-bottom: 8px;">We will never ask for this code via phone or email</li>
            <li style="margin-bottom: 8px;">If you didn't request this, please ignore this email</li>
          </ul>
        </div>
        
        ${purposeContent.additionalInfo ? `
          <div style="margin-top: 25px; padding: 15px; background-color: ${this.brandingConfig.colors.background}; border-radius: 8px;">
            <p style="margin: 0; color: ${this.brandingConfig.colors.text}; font-size: 14px;">
              ${purposeContent.additionalInfo}
            </p>
          </div>
        ` : ''}
        
        <div class="text-center mt-4">
          <a href="${this.brandingConfig.websiteUrl}" class="btn btn-primary">
            Continue to Website
          </a>
        </div>
      </div>
    `;
  }

  protected generateTextContent(data: EmailVerificationData): string {
    const purposeContent = this.getPurposeContent(data);
    const expiryMinutes = data.expiryMinutes || 15;

    return `
${purposeContent.title}

Hello ${data.fullName || data.userName || 'User'},

${purposeContent.description}

Your Verification Code: ${data.code}

Important Security Information:
- This code expires in ${expiryMinutes} minutes
- Never share this code with anyone
- We will never ask for this code via phone or email
- If you didn't request this, please ignore this email

${purposeContent.additionalInfo || ''}

Continue to website: ${this.brandingConfig.websiteUrl}

${this.brandingConfig.tagline}
${this.brandingConfig.companyName}
${this.brandingConfig.subtitle}
    `.trim();
  }

  private getPurposeContent(data: EmailVerificationData): {
    title: string;
    description: string;
    additionalInfo?: string;
  } {
    switch (data.purpose) {
      case 'registration':
        return {
          title: 'Welcome to Aureus Alliance Holdings!',
          description: 'Thank you for joining our gold-backed investment platform. Please verify your email address to complete your registration and start your investment journey.',
          additionalInfo: 'Once verified, you\'ll have access to our investment phases, portfolio tracking, and referral program.'
        };

      case 'password_reset':
        return {
          title: 'Password Reset Request',
          description: 'We received a request to reset your password. Use the verification code below to create a new password for your account.',
          additionalInfo: 'If you didn\'t request a password reset, your account is still secure and you can ignore this email.'
        };

      case 'withdrawal':
        return {
          title: 'Verify Withdrawal Request',
          description: 'You\'ve requested to withdraw funds from your account. Please verify this request using the code below to ensure the security of your transaction.',
          additionalInfo: 'This verification helps protect your account from unauthorized withdrawals.'
        };

      case 'account_update':
        return {
          title: 'Verify Account Changes',
          description: 'You\'ve made changes to your account information. Please verify these changes using the code below to ensure account security.',
          additionalInfo: 'This verification helps protect your account from unauthorized modifications.'
        };

      case 'telegram_connection':
        return {
          title: 'Verify Telegram Connection',
          description: 'You\'re connecting your Telegram account to your Aureus Alliance Holdings account. Please verify this connection using the code below.',
          additionalInfo: 'This will enable you to access your account through both our website and Telegram bot.'
        };

      default:
        return {
          title: 'Email Verification Required',
          description: 'Please verify your email address using the code below to continue.',
        };
    }
  }
}
