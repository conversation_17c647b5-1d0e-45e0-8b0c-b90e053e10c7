#!/usr/bin/env node

/**
 * Fix Database Inconsistencies
 * 
 * This script addresses serious database issues:
 * 1. Orphaned telegram_users with no user_id link
 * 2. Negative share purchases
 * 3. User/telegram_user count mismatches
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ Missing SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixDatabaseInconsistencies() {
  try {
    console.log('🔧 Fixing Database Inconsistencies\n');
    
    // Step 1: Analyze the scope of issues
    console.log('📊 Step 1: Analyzing Database Issues');
    
    const { count: telegramUsersCount } = await supabase
      .from('telegram_users')
      .select('*', { count: 'exact', head: true });
    
    const { count: usersCount } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true });
    
    console.log(`   Total telegram_users: ${telegramUsersCount}`);
    console.log(`   Total users: ${usersCount}`);
    console.log(`   Difference: ${usersCount - telegramUsersCount} (users has more)`);
    
    // Step 2: Find orphaned telegram_users
    const { data: orphanedTelegram, error: orphanError } = await supabase
      .from('telegram_users')
      .select('*')
      .is('user_id', null);
    
    if (orphanError) {
      console.error('❌ Error finding orphaned telegram users:', orphanError);
      return;
    }
    
    console.log(`\n👻 Step 2: Orphaned Telegram Users: ${orphanedTelegram.length}`);
    orphanedTelegram.forEach((tg, index) => {
      console.log(`   ${index + 1}. Telegram ID: ${tg.telegram_id}, Username: ${tg.username}`);
      console.log(`      Name: ${tg.first_name} ${tg.last_name}`);
      console.log(`      Registered: ${tg.is_registered}, Step: ${tg.registration_step}`);
    });
    
    // Step 3: Find negative share purchases
    const { data: negativeShares, error: negativeError } = await supabase
      .from('aureus_share_purchases')
      .select(`
        *,
        users(id, username, full_name, email)
      `)
      .lt('shares_purchased', 0)
      .order('created_at', { ascending: false });
    
    if (negativeError) {
      console.error('❌ Error finding negative shares:', negativeError);
      return;
    }
    
    console.log(`\n📉 Step 3: Negative Share Purchases: ${negativeShares.length}`);
    negativeShares.forEach((purchase, index) => {
      console.log(`   ${index + 1}. User: ${purchase.users?.full_name || purchase.users?.username} (ID: ${purchase.user_id})`);
      console.log(`      Shares: ${purchase.shares_purchased}, Amount: $${purchase.total_amount}`);
      console.log(`      Date: ${purchase.created_at}`);
      console.log(`      Payment Method: ${purchase.payment_method}`);
      console.log('');
    });
    
    // Step 4: Find users without telegram_users link
    const { data: usersWithoutTelegram, error: noTelegramError } = await supabase
      .from('users')
      .select(`
        id, username, full_name, email,
        telegram_users(telegram_id, username)
      `)
      .is('telegram_users.telegram_id', null)
      .limit(20);
    
    if (noTelegramError) {
      console.error('❌ Error finding users without telegram:', noTelegramError);
    } else {
      console.log(`\n📱 Step 4: Users without Telegram link: ${usersWithoutTelegram.length} (showing first 20)`);
      usersWithoutTelegram.forEach((user, index) => {
        console.log(`   ${index + 1}. User: ${user.full_name || user.username} (ID: ${user.id})`);
        console.log(`      Email: ${user.email}`);
      });
    }
    
    // Step 5: Check for duplicate users
    const { data: duplicateEmails, error: dupError } = await supabase
      .from('users')
      .select('email, count(*)')
      .not('email', 'is', null)
      .group('email')
      .having('count(*)', 'gt', 1);
    
    if (dupError) {
      console.error('❌ Error checking duplicate emails:', dupError);
    } else {
      console.log(`\n👥 Step 5: Duplicate Email Addresses: ${duplicateEmails.length}`);
      for (const dup of duplicateEmails) {
        console.log(`   Email: ${dup.email} (${dup.count} users)`);
        
        // Show the duplicate users
        const { data: dupUsers, error: dupUsersError } = await supabase
          .from('users')
          .select('id, username, full_name, created_at')
          .eq('email', dup.email)
          .order('created_at');
        
        if (!dupUsersError) {
          dupUsers.forEach((user, index) => {
            console.log(`      ${index + 1}. ID: ${user.id}, Username: ${user.username}, Name: ${user.full_name}`);
            console.log(`         Created: ${user.created_at}`);
          });
        }
        console.log('');
      }
    }
    
    // Step 6: Summary and recommendations
    console.log('\n📋 Step 6: Summary and Recommendations');
    console.log('');
    console.log('🚨 CRITICAL ISSUES FOUND:');
    console.log(`   • ${orphanedTelegram.length} orphaned telegram_users (no user_id link)`);
    console.log(`   • ${negativeShares.length} negative share purchases (data corruption)`);
    console.log(`   • ${usersWithoutTelegram.length}+ users without telegram links`);
    console.log(`   • ${duplicateEmails.length} duplicate email addresses`);
    console.log('');
    console.log('💡 RECOMMENDED ACTIONS:');
    console.log('   1. Clean up orphaned telegram_users (safe to remove if not registered)');
    console.log('   2. Investigate negative share purchases (possible admin transfers)');
    console.log('   3. Link users to telegram_users where possible');
    console.log('   4. Resolve duplicate email addresses');
    console.log('');
    console.log('⚠️  These issues could cause:');
    console.log('   • Commission calculation errors');
    console.log('   • Referral network inconsistencies');
    console.log('   • Authentication problems');
    console.log('   • Data integrity violations');
    
    // Step 7: Safe cleanup of orphaned telegram_users
    console.log('\n🧹 Step 7: Safe Cleanup Options');
    
    const unregisteredOrphans = orphanedTelegram.filter(tg => !tg.is_registered || tg.registration_step === 'start');
    console.log(`   • ${unregisteredOrphans.length} unregistered orphaned telegram_users (safe to remove)`);
    
    const registeredOrphans = orphanedTelegram.filter(tg => tg.is_registered && tg.registration_step !== 'start');
    console.log(`   • ${registeredOrphans.length} registered orphaned telegram_users (need investigation)`);
    
    if (unregisteredOrphans.length > 0) {
      console.log('\n   Unregistered orphans that can be safely removed:');
      unregisteredOrphans.forEach((tg, index) => {
        console.log(`      ${index + 1}. ${tg.first_name} ${tg.last_name} (@${tg.username}) - Step: ${tg.registration_step}`);
      });
    }
    
    if (registeredOrphans.length > 0) {
      console.log('\n   Registered orphans that need investigation:');
      registeredOrphans.forEach((tg, index) => {
        console.log(`      ${index + 1}. ${tg.first_name} ${tg.last_name} (@${tg.username}) - Step: ${tg.registration_step}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Analysis failed:', error);
  }
}

fixDatabaseInconsistencies();
