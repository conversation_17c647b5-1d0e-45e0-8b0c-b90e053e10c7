/**
 * EMAIL NOTIFICATION TRIGGERS
 * 
 * Centralized service for triggering email notifications when database events occur.
 * This service should be called after successful database operations to send
 * appropriate email notifications to users.
 */

import { EmailTriggerService } from './emailTriggerService';

export class EmailNotificationTriggers {
  private static instance: EmailNotificationTriggers;
  private emailTriggerService: EmailTriggerService;

  private constructor() {
    this.emailTriggerService = EmailTriggerService.getInstance();
  }

  public static getInstance(): EmailNotificationTriggers {
    if (!EmailNotificationTriggers.instance) {
      EmailNotificationTriggers.instance = new EmailNotificationTriggers();
    }
    return EmailNotificationTriggers.instance;
  }

  /**
   * Trigger email notification for new commission transaction
   */
  public async triggerCommissionNotification(transactionId: string): Promise<void> {
    try {
      console.log('📧 Triggering commission notification for transaction:', transactionId);
      await this.emailTriggerService.handleNewCommission(transactionId);
    } catch (error) {
      console.error('❌ Failed to trigger commission notification:', error);
      // Don't throw - email failures shouldn't break the main flow
    }
  }

  /**
   * Trigger email notification for new referral
   */
  public async triggerReferralNotification(referralId: string): Promise<void> {
    try {
      console.log('📧 Triggering referral notification for referral:', referralId);
      await this.emailTriggerService.handleNewReferral(referralId);
    } catch (error) {
      console.error('❌ Failed to trigger referral notification:', error);
      // Don't throw - email failures shouldn't break the main flow
    }
  }

  /**
   * Trigger email notification for share purchase
   */
  public async triggerSharePurchaseNotification(purchaseId: string): Promise<void> {
    try {
      console.log('📧 Triggering share purchase notification for purchase:', purchaseId);
      await this.emailTriggerService.handleSharePurchase(purchaseId);
    } catch (error) {
      console.error('❌ Failed to trigger share purchase notification:', error);
      // Don't throw - email failures shouldn't break the main flow
    }
  }

  /**
   * Trigger email notification for share transfer
   */
  public async triggerShareTransferNotification(transferId: string): Promise<void> {
    try {
      console.log('📧 Triggering share transfer notification for transfer:', transferId);
      await this.emailTriggerService.handleShareTransfer(transferId);
    } catch (error) {
      console.error('❌ Failed to trigger share transfer notification:', error);
      // Don't throw - email failures shouldn't break the main flow
    }
  }

  /**
   * Trigger email notification for new internal message
   */
  public async triggerMessageNotification(messageId: string): Promise<void> {
    try {
      console.log('📧 Triggering message notification for message:', messageId);
      await this.emailTriggerService.handleNewMessage(messageId);
    } catch (error) {
      console.error('❌ Failed to trigger message notification:', error);
      // Don't throw - email failures shouldn't break the main flow
    }
  }

  /**
   * Trigger multiple notifications in parallel (for efficiency)
   */
  public async triggerMultipleNotifications(notifications: Array<{
    type: 'commission' | 'referral' | 'share_purchase' | 'share_transfer' | 'message';
    id: string;
  }>): Promise<void> {
    const promises = notifications.map(notification => {
      switch (notification.type) {
        case 'commission':
          return this.triggerCommissionNotification(notification.id);
        case 'referral':
          return this.triggerReferralNotification(notification.id);
        case 'share_purchase':
          return this.triggerSharePurchaseNotification(notification.id);
        case 'share_transfer':
          return this.triggerShareTransferNotification(notification.id);
        case 'message':
          return this.triggerMessageNotification(notification.id);
        default:
          console.warn('Unknown notification type:', notification.type);
          return Promise.resolve();
      }
    });

    try {
      await Promise.allSettled(promises);
      console.log(`📧 Triggered ${notifications.length} email notifications`);
    } catch (error) {
      console.error('❌ Error triggering multiple notifications:', error);
    }
  }

  /**
   * Trigger notification with delay (useful for avoiding rate limits)
   */
  public async triggerDelayedNotification(
    type: 'commission' | 'referral' | 'share_purchase' | 'share_transfer' | 'message',
    id: string,
    delayMs: number = 1000
  ): Promise<void> {
    setTimeout(() => {
      switch (type) {
        case 'commission':
          this.triggerCommissionNotification(id);
          break;
        case 'referral':
          this.triggerReferralNotification(id);
          break;
        case 'share_purchase':
          this.triggerSharePurchaseNotification(id);
          break;
        case 'share_transfer':
          this.triggerShareTransferNotification(id);
          break;
        case 'message':
          this.triggerMessageNotification(id);
          break;
      }
    }, delayMs);
  }
}

// Export singleton instance
export const emailNotificationTriggers = EmailNotificationTriggers.getInstance();
