import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Simple hash function for password (same as in lib/supabase.ts)
async function hashPassword(password) {
  const encoder = new TextEncoder();
  const data = encoder.encode(password + 'aureus_salt_2024');
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

async function testRegistration() {
  try {
    console.log('🧪 Testing user registration functionality...');
    
    const testUser = {
      email: '<EMAIL>',
      password: 'TestPassword123',
      fullName: 'Test User',
      sponsorUsername: 'TTTFOUNDER'
    };
    
    console.log('📧 Test user data:', {
      email: testUser.email,
      fullName: testUser.fullName,
      sponsorUsername: testUser.sponsorUsername
    });
    
    // Check if test user already exists
    const { data: existingUser, error: checkError } = await supabase
      .from('users')
      .select('*')
      .eq('email', testUser.email)
      .single();
    
    if (existingUser) {
      console.log('🗑️ Test user already exists, deleting...');
      
      // Delete existing referrals first
      await supabase
        .from('referrals')
        .delete()
        .eq('referred_id', existingUser.id);
      
      // Delete the user
      await supabase
        .from('users')
        .delete()
        .eq('id', existingUser.id);
      
      console.log('✅ Existing test user deleted');
    }
    
    // Get sponsor user
    const { data: sponsor, error: sponsorError } = await supabase
      .from('users')
      .select('*')
      .eq('username', testUser.sponsorUsername)
      .single();
    
    if (sponsorError || !sponsor) {
      console.error('❌ Sponsor not found:', sponsorError);
      return;
    }
    
    console.log('✅ Sponsor found:', sponsor.username, '(ID:', sponsor.id + ')');
    
    // Hash password
    const passwordHash = await hashPassword(testUser.password);
    console.log('🔐 Password hashed successfully');
    
    // Create user
    const { data: newUser, error: userError } = await supabase
      .from('users')
      .insert({
        username: testUser.email.split('@')[0],
        email: testUser.email,
        password_hash: passwordHash,
        full_name: testUser.fullName,
        is_active: true,
        is_verified: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (userError) {
      console.error('❌ Error creating user:', userError);
      return;
    }
    
    console.log('✅ User created successfully:', newUser.username, '(ID:', newUser.id + ')');
    
    // Create referral relationship
    const referralCode = `${sponsor.username}_${newUser.id}_${Date.now()}`;
    
    const { data: referral, error: referralError } = await supabase
      .from('referrals')
      .insert({
        referrer_id: sponsor.id,
        referred_id: newUser.id,
        referral_code: referralCode,
        commission_rate: 15.00,
        status: 'active',
        created_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (referralError) {
      console.error('❌ Error creating referral:', referralError);
      return;
    }
    
    console.log('✅ Referral relationship created successfully:', referralCode);
    
    // Test Supabase auth user creation
    console.log('🔐 Testing Supabase auth user creation...');
    
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email: testUser.email,
      password: testUser.password,
      email_confirm: true,
      user_metadata: {
        full_name: testUser.fullName,
        username: newUser.username,
        user_id: newUser.id,
        is_email_user: true
      }
    });
    
    if (authError) {
      console.warn('⚠️ Auth user creation failed (this might be expected):', authError.message);
    } else {
      console.log('✅ Supabase auth user created successfully');
    }
    
    console.log('\n🎉 Registration test completed successfully!');
    console.log('📊 Summary:');
    console.log('  - User created in users table ✅');
    console.log('  - Sponsor relationship created ✅');
    console.log('  - Password hashing working ✅');
    console.log('  - Database operations successful ✅');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

testRegistration();
