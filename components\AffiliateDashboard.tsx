import React, { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../lib/supabase';
import { DashboardSwitcher } from './DashboardSwitcher';
import { ReferralCenter } from './referrals/ReferralCenter';

import { USDTToSharesConverter } from './affiliate/USDTToSharesConverter';
import { CommissionWithdrawal } from './user/CommissionWithdrawal';
import { ShareTransferSystem } from './affiliate/ShareTransferSystem';
import { ComprehensiveAffiliateManager } from './affiliate/ComprehensiveAffiliateManager';
import { AffiliateTrainingCenter } from './affiliate/AffiliateTrainingCenter';
import { MarketingToolsManager } from './affiliate/MarketingToolsManager';
import { getFullVersion } from '../lib/version';

interface AffiliateDashboardProps {
  user: any;
  onLogout: () => void;
  onSwitchDashboard: (dashboard: 'shareholder' | 'affiliate') => void;
}

type AffiliateSection =
  | 'overview'
  | 'referrals'
  | 'commissions'
  | 'marketing-tools'
  | 'team'
  | 'training'
  | 'settings';

const affiliateNavigation = [
  {
    id: 'overview' as AffiliateSection,
    label: 'Overview',
    icon: '📊',
    description: 'Commission summary and performance'
  },
  {
    id: 'referrals' as AffiliateSection,
    label: 'Referrals',
    icon: '🤝',
    description: 'Manage referral links and track conversions'
  },
  {
    id: 'commissions' as AffiliateSection,
    label: 'Commissions',
    icon: '💰',
    description: 'Track USDT and share commissions'
  },
  {
    id: 'marketing-tools' as AffiliateSection,
    label: 'Marketing Tools',
    icon: '🎯',
    description: 'Banners, links, and promotional materials'
  },
  {
    id: 'team' as AffiliateSection,
    label: 'My Team',
    icon: '👥',
    description: 'View your referral network'
  },
  {
    id: 'training' as AffiliateSection,
    label: 'Training',
    icon: '🎓',
    description: 'Learn effective networking strategies'
  },
  {
    id: 'settings' as AffiliateSection,
    label: 'Settings',
    icon: '⚙️',
    description: 'Account preferences'
  }
];

export const AffiliateDashboard: React.FC<AffiliateDashboardProps> = ({
  user,
  onLogout,
  onSwitchDashboard
}) => {
  const [activeSection, setActiveSection] = useState<AffiliateSection>('overview');
  const [commissionData, setCommissionData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    loadCommissionData();
  }, [user]);

  const loadCommissionData = async () => {
    if (!user?.database_user?.id) return;

    try {
      const serviceClient = getServiceRoleClient();

      // Load commission balances
      const { data: commissions } = await serviceClient
        .from('commission_balances')
        .select('*')
        .eq('user_id', user.database_user.id)
        .single();

      // Load referral count
      const { data: referrals } = await serviceClient
        .from('referrals')
        .select('id')
        .eq('referrer_id', user.database_user.id);

      // Load share purchases (excluding commission conversions to avoid double-counting)
      const { data: sharePurchases } = await serviceClient
        .from('aureus_share_purchases')
        .select('shares_purchased, total_amount, status, payment_method')
        .eq('user_id', user.database_user.id)
        .eq('status', 'active');

      // Load approved commission conversions (USDT converted to shares)
      const { data: conversions } = await serviceClient
        .from('commission_conversions')
        .select('shares_requested, usdt_amount, status')
        .eq('user_id', user.database_user.id)
        .eq('status', 'approved');

      // Load share transfers (both sent and received)
      const { data: sentTransfers } = await serviceClient
        .from('share_transfers')
        .select('shares_transferred, status, completed_at')
        .eq('sender_user_id', user.database_user.id)
        .eq('status', 'completed');

      const { data: receivedTransfers } = await serviceClient
        .from('share_transfers')
        .select('shares_transferred, status, completed_at')
        .eq('recipient_user_id', user.database_user.id)
        .eq('status', 'completed');

      // Calculate purchased shares (matching Telegram bot logic exactly)
      const purchasedShares = sharePurchases?.reduce((sum, purchase) => {
        // Exclude only 'Commission Conversion' (title case) as per Telegram bot
        if (purchase.status === 'active' && purchase.payment_method !== 'Commission Conversion') {
          return sum + (purchase.shares_purchased || 0);
        }
        return sum;
      }, 0) || 0;

      // Calculate total converted shares
      const totalConvertedShares = conversions?.reduce((sum, conversion) => sum + conversion.shares_requested, 0) || 0;
      const totalUsdtConverted = conversions?.reduce((sum, conversion) => sum + parseFloat(conversion.usdt_amount), 0) || 0;

      // Calculate net transferred shares (received - sent)
      const totalSharesSent = sentTransfers?.reduce((sum, transfer) => sum + parseFloat(transfer.shares_transferred), 0) || 0;
      const totalSharesReceived = receivedTransfers?.reduce((sum, transfer) => sum + parseFloat(transfer.shares_transferred), 0) || 0;
      const netTransferredShares = totalSharesReceived - totalSharesSent;

      // Calculate total shares owned (purchased + commission + converted + net transferred)
      const commissionShares = commissions?.share_balance || 0;
      const totalSharesOwned = purchasedShares + commissionShares + totalConvertedShares + netTransferredShares;

      setCommissionData({
        ...commissions,
        referral_count: referrals?.length || 0,
        // Add purchased shares data
        purchased_shares: purchasedShares,
        // Add conversion data
        total_converted_shares: totalConvertedShares,
        total_usdt_converted: totalUsdtConverted,
        // Add transfer data
        shares_sent: totalSharesSent,
        shares_received: totalSharesReceived,
        net_transferred_shares: netTransferredShares,
        // Update share balance to include all share types
        total_shares_owned: totalSharesOwned,
        commission_shares_only: commissionShares
      });
    } catch (error) {
      console.error('Error loading commission data:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Welcome Header - Mobile Optimized */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-xl p-4 sm:p-6 text-white shadow-lg">
        <h2 className="text-xl sm:text-2xl font-bold mb-2">
          Welcome to Your Affiliate Dashboard! 🤝
        </h2>
        <p className="text-blue-100 text-sm sm:text-base">
          Build your network, earn commissions, and grow your income with Aureus Alliance Holdings.
        </p>
      </div>

      {/* Key Metrics Cards - Mobile First Grid */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
        {/* USDT Balance - Mobile Optimized */}
        <div className="bg-gray-800 rounded-xl border border-gray-700 p-3 sm:p-4 lg:p-6 shadow-lg">
          <div className="flex flex-col space-y-2 sm:space-y-0 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex-1">
              <p className="text-gray-400 text-xs sm:text-sm font-medium">USDT Balance</p>
              <p className="text-lg sm:text-xl lg:text-2xl font-bold text-green-400">
                ${commissionData?.usdt_balance?.toFixed(2) || '0.00'}
              </p>
            </div>
            <div className="bg-green-600 p-2 sm:p-3 rounded-lg self-start sm:self-auto">
              <svg className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
          </div>
          <div className="mt-2 sm:mt-3 lg:mt-4">
            <p className="text-gray-400 text-xs sm:text-sm">
              Available for withdrawal
            </p>
          </div>
        </div>

        {/* Total Earned - Mobile Optimized */}
        <div className="bg-gray-800 rounded-xl border border-gray-700 p-3 sm:p-4 lg:p-6 shadow-lg">
          <div className="flex flex-col space-y-2 sm:space-y-0 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex-1">
              <p className="text-gray-400 text-xs sm:text-sm font-medium">Total Earned</p>
              <p className="text-lg sm:text-xl lg:text-2xl font-bold text-blue-400">
                ${commissionData?.total_earned_usdt?.toFixed(2) || '0.00'}
              </p>
            </div>
            <div className="bg-blue-600 p-2 sm:p-3 rounded-lg self-start sm:self-auto">
              <svg className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
          </div>
          <div className="mt-2 sm:mt-3 lg:mt-4">
            <p className="text-gray-400 text-xs sm:text-sm">
              Lifetime USDT earnings
            </p>
          </div>
        </div>

        {/* Commission Shares - Mobile Optimized */}
        <div className="bg-gray-800 rounded-xl border border-gray-700 p-3 sm:p-4 lg:p-6 shadow-lg">
          <div className="flex flex-col space-y-2 sm:space-y-0 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex-1">
              <p className="text-gray-400 text-xs sm:text-sm font-medium">Bonus Shares</p>
              <p className="text-lg sm:text-xl lg:text-2xl font-bold text-yellow-400">
                {commissionData?.commission_shares_only?.toFixed(2) || '0.00'}
              </p>
            </div>
            <div className="bg-yellow-600 p-2 sm:p-3 rounded-lg self-start sm:self-auto">
              <svg className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
          <div className="mt-2 sm:mt-3 lg:mt-4">
            <p className="text-gray-400 text-xs sm:text-sm">
              From referrals
            </p>
          </div>
        </div>

        {/* Referral Count - Mobile Optimized */}
        <div className="bg-gray-800 rounded-xl border border-gray-700 p-3 sm:p-4 lg:p-6 shadow-lg">
          <div className="flex flex-col space-y-2 sm:space-y-0 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex-1">
              <p className="text-gray-400 text-xs sm:text-sm font-medium">Referrals</p>
              <p className="text-lg sm:text-xl lg:text-2xl font-bold text-purple-400">
                {commissionData?.referral_count || 0}
              </p>
            </div>
            <div className="bg-purple-600 p-2 sm:p-3 rounded-lg self-start sm:self-auto">
              <svg className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
          </div>
          <div className="mt-2 sm:mt-3 lg:mt-4">
            <p className="text-gray-400 text-xs sm:text-sm">
              People referred
            </p>
          </div>
        </div>
      </div>

      {/* Share Portfolio Breakdown - Mobile Optimized */}
      <div className="bg-gray-800 rounded-xl border border-gray-700 p-4 lg:p-6 shadow-lg">
        <div className="mb-4">
          <div className="text-base lg:text-lg font-bold text-yellow-400 mb-2 flex items-center gap-2">
            <span className="text-xl">💎</span>
            TOTAL SHARE HOLDINGS: {commissionData?.total_shares_owned?.toFixed(2) || '0.00'} shares
          </div>
          <div className="text-xs lg:text-sm text-gray-400 ml-6">
            (All active)
          </div>
        </div>

        <div className="text-sm lg:text-base font-bold text-white mb-3 flex items-center gap-2">
          <span className="text-lg">📈</span>
          SHARE BREAKDOWN:
        </div>

        <div className="space-y-3 lg:space-y-4 ml-4">
          {/* Shares Purchased */}
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-gray-300 text-sm lg:text-base">• Shares Purchased:</span>
              <span className="text-blue-400 font-semibold text-sm lg:text-base">
                {commissionData?.purchased_shares?.toFixed(2) || '0.00'} shares
              </span>
            </div>
            <div className="text-gray-400 text-xs lg:text-sm ml-4">
              └ ${((commissionData?.purchased_shares || 0) * 5).toFixed(2)} invested
            </div>
          </div>

          {/* Commission Converted to Shares */}
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-gray-300 text-sm lg:text-base">• Commission Converted to Shares:</span>
              <span className="text-purple-400 font-semibold text-sm lg:text-base">
                {commissionData?.total_converted_shares?.toFixed(2) || '0.00'} shares
              </span>
            </div>
            <div className="text-gray-400 text-xs lg:text-sm ml-4">
              └ ${commissionData?.total_usdt_converted?.toFixed(2) || '0.00'} commission converted
            </div>
          </div>

          {/* Shares Earned (Direct) */}
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-gray-300 text-sm lg:text-base">• Shares Earned (Direct):</span>
              <span className="text-yellow-400 font-semibold text-sm lg:text-base">
                {commissionData?.commission_shares_only?.toFixed(2) || '0.00'} shares
              </span>
            </div>
            <div className="text-gray-400 text-xs lg:text-sm ml-4">
              └ Direct share commissions from referrals
            </div>
          </div>

          {/* Share Transfers */}
          {((commissionData?.shares_received || 0) > 0 || (commissionData?.shares_sent || 0) > 0) && (
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <span className="text-gray-300 text-sm lg:text-base">• Net Transferred Shares:</span>
                <span className={`font-semibold text-sm lg:text-base ${
                  (commissionData?.net_transferred_shares || 0) >= 0 ? 'text-green-400' : 'text-red-400'
                }`}>
                  {(commissionData?.net_transferred_shares || 0) >= 0 ? '+' : ''}{commissionData?.net_transferred_shares?.toFixed(2) || '0.00'} shares
                </span>
              </div>
              <div className="text-gray-400 text-xs lg:text-sm ml-4 space-y-1">
                <div>└ Received: {commissionData?.shares_received?.toFixed(2) || '0.00'} shares</div>
                <div>└ Sent: {commissionData?.shares_sent?.toFixed(2) || '0.00'} shares</div>
              </div>
            </div>
          )}
        </div>

        {/* Account Balance Section */}
        <div className="mt-4 lg:mt-6 pt-4 border-t border-gray-600">
          <div className="text-sm lg:text-base font-bold text-white mb-2 flex items-center gap-2">
            <span className="text-lg">💰</span>
            COMMISSION STATUS:
          </div>
          <div className="space-y-2 ml-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-300 text-sm lg:text-base">• Available USDT Commission:</span>
              <span className="text-green-400 font-semibold text-sm lg:text-base">
                ${commissionData?.usdt_balance?.toFixed(2) || '0.00'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-300 text-sm lg:text-base">• Total USDT Earned:</span>
              <span className="text-blue-400 font-semibold text-sm lg:text-base">
                ${commissionData?.total_earned_usdt?.toFixed(2) || '0.00'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Referral Link Section - Mobile Optimized */}
      <div className="bg-gray-800 rounded-xl border border-gray-700 p-4 lg:p-6 shadow-lg">
        <h3 className="text-base lg:text-lg font-semibold text-white mb-3 lg:mb-4 flex items-center gap-2">
          <span className="text-xl">🔗</span>
          Your Referral Link
        </h3>
        <div className="bg-gradient-to-r from-yellow-600 to-yellow-500 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <span className="text-white text-lg">🎯</span>
              <span className="text-white text-sm font-semibold">Personal Landing Page</span>
            </div>
            <button
              onClick={() => {
                const link = `https://aureus.africa/${user?.database_user?.username || user?.username || `user${user?.database_user?.id || user?.id}`}`;
                navigator.clipboard.writeText(link);
              }}
              className="bg-white text-yellow-600 hover:bg-gray-100 px-4 py-2 rounded text-sm font-medium transition-colors"
            >
              Copy Link
            </button>
          </div>
          <div className="text-white text-xs font-mono break-all bg-black/20 p-3 rounded mb-3">
            https://aureus.africa/{user?.database_user?.username || user?.username || `user${user?.database_user?.id || user?.id}`}
          </div>
          <div className="text-white text-xs">
            ✨ Professional landing page with your profile, company info, calculator, and auto-registration
          </div>
        </div>
      </div>
      {/* Quick Actions - Mobile Optimized */}
      <div className="bg-gray-800 rounded-xl border border-gray-700 p-4 lg:p-6 shadow-lg">
        <h3 className="text-base lg:text-lg font-semibold text-white mb-3 lg:mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 lg:gap-4">
          <button
            onClick={() => setActiveSection('referrals')}
            className="bg-blue-600 hover:bg-blue-700 active:bg-blue-800 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 text-sm lg:text-base touch-manipulation"
          >
            Manage Referrals
          </button>

          <button
            onClick={() => setActiveSection('commissions')}
            className="bg-green-600 hover:bg-green-700 active:bg-green-800 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 text-sm lg:text-base touch-manipulation"
          >
            Withdraw USDT
          </button>

          <button
            onClick={() => setActiveSection('marketing-tools')}
            className="bg-purple-600 hover:bg-purple-700 active:bg-purple-800 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 text-sm lg:text-base touch-manipulation"
          >
            Marketing Tools
          </button>

          <button
            onClick={() => setActiveSection('training')}
            className="bg-yellow-600 hover:bg-yellow-700 active:bg-yellow-800 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 text-sm lg:text-base touch-manipulation"
          >
            Training Center
          </button>
        </div>
      </div>

      {/* Pro Tip - Mobile Optimized */}
      <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-xl p-4 lg:p-6 shadow-lg">
        <div className="flex items-start space-x-3">
          <div className="text-yellow-500 text-xl lg:text-2xl flex-shrink-0">💡</div>
          <div className="flex-1 min-w-0">
            <h3 className="text-yellow-400 font-semibold mb-2 text-sm lg:text-base">Pro Tip</h3>
            <p className="text-yellow-300 text-sm lg:text-base">
              Want to invest in shares too? Switch to the Shareholder Dashboard using the switcher above to purchase your own shares and build your portfolio!
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSectionContent = () => {
    switch (activeSection) {
      case 'overview':
        return renderOverview();
      case 'referrals':
        return (
          <ReferralCenter
            userId={user?.database_user?.id || 0}
            className="bg-gray-800 rounded-lg border border-gray-700 p-6"
          />
        );

      case 'commissions':
        return (
          <ComprehensiveAffiliateManager
            userId={user?.database_user?.id || 0}
            onDataRefresh={loadCommissionData}
          />
        );
      case 'marketing-tools':
        return (
          <MarketingToolsManager
            userId={user?.database_user?.id || 0}
            affiliateData={{
              user: user?.database_user,
              referralCount: commissionData?.referral_count || 0,
              commissionBalance: commissionData
            }}
          />
        );
      case 'team':
        return (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-white mb-4">👥 My Team</h2>
            <p className="text-gray-400">Team management features coming soon...</p>
          </div>
        );
      case 'training':
        return (
          <AffiliateTrainingCenter
            userId={user?.database_user?.id || 0}
            className="bg-gray-800 rounded-lg border border-gray-700 p-6"
          />
        );
      case 'settings':
        return (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-white mb-4">⚙️ Settings</h2>
            <p className="text-gray-400">Affiliate settings coming soon...</p>
          </div>
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 flex">
      {/* Sidebar */}
      <div className={`${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'} lg:translate-x-0 fixed lg:static inset-y-0 left-0 z-50 w-64 bg-gray-800 border-r border-gray-700 transition-transform duration-300 ease-in-out`}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="p-4 border-b border-gray-700">
            <DashboardSwitcher
              currentDashboard="affiliate"
              onSwitch={onSwitchDashboard}
              user={user}
            />
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2">
            {affiliateNavigation.map((item) => (
              <button
                key={item.id}
                onClick={() => {
                  setActiveSection(item.id);
                  setIsMobileMenuOpen(false);
                }}
                className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeSection === item.id
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                <span className="text-xl">{item.icon}</span>
                <div>
                  <div className="font-medium">{item.label}</div>
                  <div className="text-xs opacity-75">{item.description}</div>
                </div>
              </button>
            ))}
          </nav>

          {/* User Info */}
          <div className="p-4 border-t border-gray-700">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">
                  {(user?.database_user?.full_name || user?.email)?.charAt(0).toUpperCase()}
                </span>
              </div>
              <div>
                <p className="text-white text-sm font-medium">
                  {user?.database_user?.full_name || user?.email?.split('@')[0]}
                </p>
                <p className="text-gray-400 text-xs">Affiliate</p>
              </div>
            </div>
            <button
              onClick={onLogout}
              className="w-full bg-gray-700 hover:bg-gray-600 text-white py-2 px-3 rounded-lg text-sm transition-colors"
            >
              Logout
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 lg:ml-0">
        {/* Mobile Header */}
        <div className="lg:hidden bg-gray-800 border-b border-gray-700 p-4">
          <button
            onClick={() => setIsMobileMenuOpen(true)}
            className="text-white"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {renderSectionContent()}
        </div>

        {/* Version Footer */}
        <div className="border-t border-gray-700 p-4 text-center">
          <p className="text-gray-500 text-xs">
            Aureus Africa {getFullVersion()} • {new Date().getFullYear()} Aureus Alliance Holdings (Pty) Ltd
          </p>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black/50 z-40"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}
    </div>
  );
};
