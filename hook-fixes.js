/**
 * HOOK.JS FIXES
 * 
 * Specific fixes for the telegram user lookup errors in hook.js
 */

// FIXED TELEGRAM USER LOOKUP FUNCTION
async function safeLookupTelegramUser(telegramId) {
  try {
    console.log('🔍 Safe telegram lookup for ID:', telegramId);
    
    // Input validation
    if (!telegramId || telegramId === 'null' || telegramId === null || telegramId === undefined) {
      console.log('⚠️ Invalid telegram_id provided:', telegramId);
      return null;
    }

    // Convert to string and clean
    const cleanId = String(telegramId).trim();
    if (cleanId === '' || cleanId === 'null' || cleanId === 'undefined') {
      console.log('⚠️ Empty or invalid telegram_id after cleaning:', cleanId);
      return null;
    }

    // Perform safe Supabase query
    const { data, error } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', cleanId)
      .maybeSingle(); // Use maybeSingle() instead of single() to handle no results gracefully

    if (error) {
      console.error('❌ Supabase query error:', error);
      return null;
    }

    if (!data) {
      console.log('ℹ️ No telegram user found for ID:', cleanId);
      return null;
    }

    console.log('✅ Telegram user found:', data.user_id);
    return data;

  } catch (error) {
    console.error('❌ Telegram user lookup error:', error);
    
    // Log error to database for tracking
    try {
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_email: 'hook_error_handler',
          action: 'TELEGRAM_LOOKUP_ERROR',
          target_type: 'error_log',
          target_id: String(telegramId || 'null'),
          metadata: {
            error: error.message,
            stack: error.stack,
            telegramId: telegramId,
            timestamp: new Date().toISOString()
          },
          created_at: new Date().toISOString()
        });
    } catch (logError) {
      console.error('❌ Failed to log error:', logError);
    }
    
    return null;
  }
}

// FIXED QUERY FOR USERS WITHOUT TELEGRAM
async function getUsersWithoutTelegram() {
  try {
    console.log('🔍 Finding users without telegram_id...');

    const { data, error } = await supabase
      .from('telegram_users')
      .select('*')
      .is('telegram_id', null); // Correct syntax for null check

    if (error) {
      console.error('❌ Query error:', error);
      return [];
    }

    console.log(`✅ Found ${data?.length || 0} users without telegram_id`);
    return data || [];

  } catch (error) {
    console.error('❌ Query failed:', error);
    return [];
  }
}

// SAFE SUPABASE QUERY WRAPPER
async function safeSupabaseQuery(queryBuilder, context = 'unknown') {
  try {
    const { data, error } = await queryBuilder;
    
    if (error) {
      console.error(`❌ Supabase error in ${context}:`, error);
      return { success: false, data: null, error: error.message };
    }
    
    return { success: true, data, error: null };
    
  } catch (error) {
    console.error(`❌ Query exception in ${context}:`, error);
    return { success: false, data: null, error: error.message };
  }
}

// ENHANCED ERROR HANDLER FOR HOOK.JS
function handleHookError(context, error, additionalData = {}) {
  const errorInfo = {
    timestamp: new Date().toISOString(),
    context,
    error: {
      message: error.message || 'Unknown error',
      name: error.name || 'Error',
      stack: error.stack
    },
    additionalData
  };

  console.error(`🚨 Hook Error [${context}]:`, errorInfo);

  // Try to log to database (non-blocking)
  supabase
    .from('admin_audit_logs')
    .insert({
      admin_email: 'hook_error_system',
      action: 'HOOK_ERROR',
      target_type: 'javascript_error',
      target_id: context,
      metadata: errorInfo,
      created_at: new Date().toISOString()
    })
    .then(() => console.log('✅ Error logged to database'))
    .catch(logError => console.error('❌ Failed to log error:', logError));

  return errorInfo;
}

// REPLACEMENT FUNCTIONS FOR HOOK.JS

// Replace existing lookupTelegramUser function with this:
window.safeLookupTelegramUser = safeLookupTelegramUser;

// Replace existing error handling with this:
window.handleHookError = handleHookError;

// Replace existing query wrapper with this:
window.safeSupabaseQuery = safeSupabaseQuery;

// EXAMPLE USAGE IN HOOK.JS:

/*
// OLD CODE (causing errors):
const user = await supabase
  .from('telegram_users')
  .select('*')
  .eq('telegram_id', null) // This causes 400 error
  .single();

// NEW CODE (fixed):
const user = await safeLookupTelegramUser(telegramId);

// OR for null checks:
const usersWithoutTelegram = await getUsersWithoutTelegram();

// OR with safe wrapper:
const result = await safeSupabaseQuery(
  supabase
    .from('telegram_users')
    .select('*')
    .eq('user_id', userId),
  'telegram_user_lookup'
);

if (result.success) {
  console.log('User found:', result.data);
} else {
  console.error('Query failed:', result.error);
}
*/

console.log('🔧 Hook.js fixes loaded successfully!');
console.log('📋 Available functions:');
console.log('  - safeLookupTelegramUser(telegramId)');
console.log('  - getUsersWithoutTelegram()');
console.log('  - safeSupabaseQuery(queryBuilder, context)');
console.log('  - handleHookError(context, error, additionalData)');
