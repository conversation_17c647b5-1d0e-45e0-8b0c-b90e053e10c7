import React, { useState } from 'react';
import { SimpleTelegramMigration } from '../components/auth/SimpleTelegramMigration';

export default function MigrateFromTelegram() {
  const [migrationComplete, setMigrationComplete] = useState(false);
  const [migratedUser, setMigratedUser] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleMigrationComplete = (userData: any) => {
    console.log('✅ Migration completed:', userData);
    setMigratedUser(userData);
    setMigrationComplete(true);
  };

  const handleError = (errorMessage: string) => {
    console.error('❌ Migration error:', errorMessage);
    setError(errorMessage);
  };

  const handleLoginRedirect = () => {
    window.location.href = '/';
  };

  if (migrationComplete) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            
            <h1 className="text-2xl font-bold text-gray-800 mb-2">
              Migration Successful!
            </h1>
            
            <p className="text-gray-600 mb-4">
              Your Telegram account has been successfully migrated to website access.
            </p>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <h3 className="font-semibold text-blue-800 mb-2">Your Login Details:</h3>
              <p className="text-sm text-blue-700">
                <strong>Username:</strong> {migratedUser?.username}
              </p>
              <p className="text-sm text-blue-700">
                <strong>Email:</strong> {migratedUser?.email}
              </p>
              <p className="text-sm text-blue-600 mt-2">
                Use these credentials to login from now on.
              </p>
            </div>
            
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <h3 className="font-semibold text-yellow-800 mb-2">Important Notice:</h3>
              <p className="text-sm text-yellow-700">
                Your Telegram bot access has been disabled. You must now use the website login with your username and password.
              </p>
            </div>
            
            <button
              onClick={handleLoginRedirect}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 font-medium"
            >
              Go to Login Page
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            Migrate Your Telegram Account
          </h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            We're transitioning from Telegram bot access to website-only access. 
            Please migrate your account to continue using our services.
          </p>
        </div>

        {error && (
          <div className="max-w-md mx-auto mb-6">
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              <strong>Error:</strong> {error}
            </div>
          </div>
        )}

        <div className="mb-8">
          <SimpleTelegramMigration
            onMigrationComplete={handleMigrationComplete}
            onError={handleError}
          />
        </div>

        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              Migration Process
            </h2>
            
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                  <span className="text-blue-600 font-semibold text-sm">1</span>
                </div>
                <div>
                  <h3 className="font-medium text-gray-800">Authenticate with Telegram</h3>
                  <p className="text-sm text-gray-600">
                    Click the Telegram login button to verify your identity.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                  <span className="text-blue-600 font-semibold text-sm">2</span>
                </div>
                <div>
                  <h3 className="font-medium text-gray-800">Create Website Password</h3>
                  <p className="text-sm text-gray-600">
                    Set up a secure password for website access.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                  <span className="text-blue-600 font-semibold text-sm">3</span>
                </div>
                <div>
                  <h3 className="font-medium text-gray-800">Migration Complete</h3>
                  <p className="text-sm text-gray-600">
                    Your account will be migrated and Telegram access disabled.
                  </p>
                </div>
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-semibold text-yellow-800 mb-2">⚠️ Important Notes:</h3>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• After migration, you cannot use Telegram bot access anymore</li>
                <li>• All your data (shares, commissions, referrals) will be preserved</li>
                <li>• You must use your username and password to login from now on</li>
                <li>• This process is irreversible</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="text-center mt-8">
          <p className="text-sm text-gray-500">
            Need help? Contact our support team through the official channels.
          </p>
        </div>
      </div>
    </div>
  );
}
