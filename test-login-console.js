// PASTE THIS INTO BROWSER CONSOLE TO TEST LOGIN
// ===============================================

console.log('🔍 STARTING LOGIN TEST');
console.log('======================');

// Test the login function directly
async function testLogin() {
  const email = '<EMAIL>';
  const password = 'your_password_here'; // Replace with actual password
  
  console.log('🔐 Testing login with:', { email, passwordProvided: !!password });
  
  try {
    // Check if the function exists
    if (typeof signInWithEmailEnhanced === 'undefined') {
      console.log('❌ signInWithEmailEnhanced function not found');
      console.log('Available functions:', Object.keys(window).filter(k => k.includes('sign') || k.includes('login')));
      return;
    }
    
    // Call the login function
    const result = await signInWithEmailEnhanced(email, password);
    
    console.log('📊 LOGIN RESULT:', result);
    
    if (result.error) {
      console.log('❌ LOGIN FAILED:', result.error.message);
    } else {
      console.log('✅ LOGIN SUCCESSFUL:', result.user);
    }
    
  } catch (error) {
    console.log('❌ LOGIN ERROR:', error);
  }
}

// Test database connection
async function testDatabase() {
  console.log('🗄️ Testing database connection...');
  
  if (typeof supabase === 'undefined') {
    console.log('❌ Supabase client not found');
    return;
  }
  
  try {
    // Test users table
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email, username')
      .limit(1);
    
    console.log('📊 Users table test:', { 
      hasData: !!users, 
      hasError: !!usersError,
      errorMessage: usersError?.message 
    });
    
    // Test telegram_users table
    const { data: telegramUsers, error: telegramError } = await supabase
      .from('telegram_users')
      .select('id, email, temp_email, username')
      .limit(1);
    
    console.log('📊 Telegram users table test:', { 
      hasData: !!telegramUsers, 
      hasError: !!telegramError,
      errorMessage: telegramError?.message 
    });
    
  } catch (error) {
    console.log('❌ Database test error:', error);
  }
}

// Test specific user lookup
async function testUserLookup() {
  const email = '<EMAIL>';
  console.log('👤 Testing user lookup for:', email);
  
  try {
    // Check users table
    const { data: webUser, error: webError } = await supabase
      .from('users')
      .select('*')
      .eq('email', email.toLowerCase().trim())
      .single();
    
    console.log('📊 Users table lookup:', {
      found: !!webUser,
      error: webError?.message,
      user: webUser ? {
        id: webUser.id,
        email: webUser.email,
        username: webUser.username,
        hasPasswordHash: !!webUser.password_hash,
        isActive: webUser.is_active
      } : null
    });
    
    // Check telegram_users table
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('temp_email', email.toLowerCase().trim())
      .single();
    
    console.log('📊 Telegram users table lookup:', {
      found: !!telegramUser,
      error: telegramError?.message,
      user: telegramUser ? {
        id: telegramUser.id,
        email: telegramUser.email,
        temp_email: telegramUser.temp_email,
        username: telegramUser.username,
        hasPasswordHash: !!telegramUser.password_hash,
        isActive: telegramUser.is_active
      } : null
    });
    
  } catch (error) {
    console.log('❌ User lookup error:', error);
  }
}

// Run all tests
console.log('🚀 Running comprehensive login tests...');
console.log('=======================================');

testDatabase().then(() => {
  console.log('\n');
  return testUserLookup();
}).then(() => {
  console.log('\n');
  console.log('💡 To test actual login, update the password in testLogin() and run:');
  console.log('   testLogin()');
}).catch(error => {
  console.log('❌ Test suite error:', error);
});

// Make functions available globally
window.testLogin = testLogin;
window.testDatabase = testDatabase;
window.testUserLookup = testUserLookup;
