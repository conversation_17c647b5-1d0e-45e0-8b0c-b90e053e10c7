/**
 * TELEGRAM BOT SPONSOR MANAGEMENT COMMANDS
 * 
 * Add these functions to your Telegram bot to enable sponsor management
 * through the bot interface for admins.
 */

// Import the sponsor change system
import { changeSponsor, resolveUser, getCurrentSponsor } from './sponsor-change-system.js'

/**
 * Handle admin sponsor change command
 * Usage: /changesponsor username newSponsor reason
 */
async function handleChangeSponsorCommand(ctx) {
  const user = ctx.from
  
  // Check admin authorization
  if (user.username !== 'TTTFOUNDER') {
    await ctx.answerCbQuery('❌ Access denied - Admin only')
    return
  }

  const args = ctx.message.text.split(' ').slice(1)
  
  if (args.length < 2) {
    const helpMessage = `🤝 **SPONSOR CHANGE COMMAND**

**Usage:**
\`/changesponsor <username> <newSponsor> [reason]\`

**Examples:**
• \`/changesponsor john123 mary456 User requested change\`
• \`/changesponsor <EMAIL> TTTFOUNDER Admin correction\`

**Parameters:**
• \`username\` - Username or email of user to change
• \`newSponsor\` - Username of new sponsor
• \`reason\` - Optional reason for the change

**Note:** This command updates all related database tables safely.`

    await ctx.replyWithMarkdown(helpMessage)
    return
  }

  const [targetUser, newSponsorUsername, ...reasonParts] = args
  const reason = reasonParts.join(' ') || 'Admin sponsor change via Telegram'

  try {
    await ctx.reply('🔄 Processing sponsor change...')

    const result = await changeSponsor(targetUser, newSponsorUsername, user.username, reason)

    if (result.success) {
      const successMessage = `✅ **SPONSOR CHANGE SUCCESSFUL**

**User:** ${targetUser}
**New Sponsor:** ${newSponsorUsername}
**Reason:** ${reason}

**Tables Updated:**
${result.tablesUpdated.map(table => `• ${table}`).join('\n')}

**Audit Log ID:** ${result.auditLogId || 'N/A'}`

      await ctx.replyWithMarkdown(successMessage)
    } else {
      await ctx.replyWithMarkdown(`❌ **SPONSOR CHANGE FAILED**\n\n${result.error || result.message}`)
    }

  } catch (error) {
    console.error('Telegram sponsor change error:', error)
    await ctx.replyWithMarkdown(`❌ **ERROR**\n\n${error.message}`)
  }
}

/**
 * Handle view sponsor relationships command
 * Usage: /viewsponsors [username]
 */
async function handleViewSponsorsCommand(ctx) {
  const user = ctx.from
  
  // Check admin authorization
  if (user.username !== 'TTTFOUNDER') {
    await ctx.answerCbQuery('❌ Access denied - Admin only')
    return
  }

  const args = ctx.message.text.split(' ').slice(1)
  const targetUsername = args[0]

  try {
    if (targetUsername) {
      // View specific user's sponsor
      const targetUser = await resolveUser(targetUsername)
      if (!targetUser) {
        await ctx.replyWithMarkdown(`❌ **USER NOT FOUND**\n\nUser "${targetUsername}" not found in database.`)
        return
      }

      const currentSponsor = await getCurrentSponsor(targetUser.id)
      
      const sponsorMessage = `👤 **USER SPONSOR INFO**

**User:** ${targetUser.username}
**Full Name:** ${targetUser.full_name || 'N/A'}
**Email:** ${targetUser.email}

**Current Sponsor:** ${currentSponsor ? currentSponsor.referrer_username : 'None'}
**Sponsor Since:** ${currentSponsor ? new Date(currentSponsor.created_at).toLocaleDateString() : 'N/A'}
**Commission Rate:** ${currentSponsor ? currentSponsor.commission_rate + '%' : 'N/A'}
**Total Commission:** $${currentSponsor ? currentSponsor.total_commission.toFixed(2) : '0.00'}`

      await ctx.replyWithMarkdown(sponsorMessage, {
        reply_markup: {
          inline_keyboard: [
            [{ text: "🔄 Change Sponsor", callback_data: `change_sponsor_${targetUser.id}` }],
            [{ text: "🔙 Back to Admin", callback_data: "admin_panel" }]
          ]
        }
      })

    } else {
      // Show general sponsor statistics
      await showSponsorStatistics(ctx)
    }

  } catch (error) {
    console.error('View sponsors error:', error)
    await ctx.replyWithMarkdown(`❌ **ERROR**\n\n${error.message}`)
  }
}

/**
 * Show sponsor statistics
 */
async function showSponsorStatistics(ctx) {
  try {
    // This would require database queries to get statistics
    const statsMessage = `📊 **SPONSOR STATISTICS**

**System Overview:**
• Total Active Users: Loading...
• Users with Sponsors: Loading...
• Users without Sponsors: Loading...
• Total Referral Relationships: Loading...

**Top Sponsors:**
• Loading sponsor rankings...

**Recent Changes:**
• Loading recent sponsor changes...

**Commands:**
• \`/viewsponsors username\` - View specific user
• \`/changesponsor user newSponsor reason\` - Change sponsor
• \`/sponsorstats\` - Detailed statistics`

    await ctx.replyWithMarkdown(statsMessage, {
      reply_markup: {
        inline_keyboard: [
          [{ text: "🔄 Refresh Stats", callback_data: "refresh_sponsor_stats" }],
          [{ text: "🔙 Back to Admin", callback_data: "admin_panel" }]
        ]
      }
    })

  } catch (error) {
    console.error('Sponsor statistics error:', error)
    await ctx.replyWithMarkdown(`❌ **ERROR**\n\n${error.message}`)
  }
}

/**
 * Handle sponsor change callback from inline keyboard
 */
async function handleChangeSponsorCallback(ctx, callbackData) {
  const user = ctx.from
  
  // Check admin authorization
  if (user.username !== 'TTTFOUNDER') {
    await ctx.answerCbQuery('❌ Access denied')
    return
  }

  const userId = callbackData.replace('change_sponsor_', '')
  
  try {
    const targetUser = await resolveUser(parseInt(userId))
    if (!targetUser) {
      await ctx.answerCbQuery('❌ User not found')
      return
    }

    const currentSponsor = await getCurrentSponsor(targetUser.id)

    const changeMessage = `🔄 **CHANGE SPONSOR**

**User:** ${targetUser.username}
**Current Sponsor:** ${currentSponsor ? currentSponsor.referrer_username : 'None'}

**To change sponsor, use:**
\`/changesponsor ${targetUser.username} <newSponsorUsername> [reason]\`

**Example:**
\`/changesponsor ${targetUser.username} TTTFOUNDER Admin correction\``

    await ctx.replyWithMarkdown(changeMessage, {
      reply_markup: {
        inline_keyboard: [
          [{ text: "📋 View User Details", callback_data: `user_details_${userId}` }],
          [{ text: "🔙 Back to Admin", callback_data: "admin_panel" }]
        ]
      }
    })

  } catch (error) {
    console.error('Change sponsor callback error:', error)
    await ctx.answerCbQuery('❌ Error processing request')
  }
}

/**
 * Add these to your bot's command handlers
 */
const sponsorCommands = {
  '/changesponsor': handleChangeSponsorCommand,
  '/viewsponsors': handleViewSponsorsCommand,
  '/sponsorstats': handleViewSponsorsCommand, // Alias for general stats
}

const sponsorCallbacks = {
  'change_sponsor_': handleChangeSponsorCallback,
  'refresh_sponsor_stats': showSponsorStatistics,
}

/**
 * Integration example for existing bot
 */
function integrateSponsorCommands(bot) {
  // Add command handlers
  Object.entries(sponsorCommands).forEach(([command, handler]) => {
    bot.command(command.replace('/', ''), handler)
  })

  // Add callback handlers
  bot.on('callback_query', async (ctx) => {
    const callbackData = ctx.callbackQuery.data
    
    for (const [prefix, handler] of Object.entries(sponsorCallbacks)) {
      if (callbackData.startsWith(prefix)) {
        await handler(ctx, callbackData)
        return
      }
    }
  })
}

// Export for use in main bot file
export {
  handleChangeSponsorCommand,
  handleViewSponsorsCommand,
  handleChangeSponsorCallback,
  showSponsorStatistics,
  sponsorCommands,
  sponsorCallbacks,
  integrateSponsorCommands
}

// Usage instructions
console.log(`
🤖 TELEGRAM SPONSOR COMMANDS READY

Add these commands to your bot:

1. Import the functions:
   import { integrateSponsorCommands } from './telegram-sponsor-commands.js'

2. Integrate with your bot:
   integrateSponsorCommands(bot)

3. Available commands:
   • /changesponsor <user> <newSponsor> [reason]
   • /viewsponsors [username]
   • /sponsorstats

4. Admin access required (TTTFOUNDER only)
`)
