# Aureus Alliance Holdings - Unified Design System Documentation

## Overview

This document describes the unified design system implemented for Aureus Alliance Holdings, consolidating all styling into a professional, consistent, and maintainable system.

## Files Structure

### Core Files
- **`aureus.css`** - Master CSS file containing the complete unified design system
- **`aureus.js`** - JavaScript file for theme switching and interactive features
- **`index.css`** - Main stylesheet that imports aureus.css and provides legacy compatibility

### Key Features
- ✅ **Premium Color Palette** - Exact implementation from `aureus_ui_color_palette_premium.md`
- ✅ **Dark/Light Theme Support** - Seamless theme switching with `[data-theme]` attributes
- ✅ **Professional Corporate Design** - Investor-grade visual presentation
- ✅ **WCAG 2.1 AA Accessibility** - Full accessibility compliance
- ✅ **Mobile-First Responsive** - Perfect display on all devices
- ✅ **Component Library** - Comprehensive reusable components
- ✅ **Legacy Compatibility** - Existing components continue to work

## Color Palette

### Light Theme (Default)
```css
--bg: #FAFAFA           /* Background */
--surface: #FFFFFF      /* Cards, modals */
--text: #111111         /* Primary text */
--text-muted: #4E4E4E   /* Secondary text */
--gold: #FFD700         /* Primary gold */
--blue: #007BFF         /* Primary blue */
--emerald: #27AE60      /* Success green */
--copper: #B75E2A       /* Warning orange */
--border: #E2E2E2       /* Borders */
```

### Dark Theme
```css
--bg: #050505           /* Background */
--surface: #121212      /* Cards, modals */
--text: #FFFFFF         /* Primary text */
--text-muted: #B0B0B0   /* Secondary text */
--gold: #FFD700         /* Primary gold */
--blue: #00BFFF         /* Primary blue */
--emerald: #2ECC71      /* Success green */
--copper: #E07B39       /* Warning orange */
--border: #1E1E1E       /* Borders */
```

## Typography System

### Font Sizes (Mobile-First)
```css
--font-size-xs: 0.75rem     /* 12px */
--font-size-sm: 0.875rem    /* 14px */
--font-size-base: 1rem      /* 16px */
--font-size-lg: 1.125rem    /* 18px */
--font-size-xl: 1.25rem     /* 20px */
--font-size-2xl: 1.5rem     /* 24px */
--font-size-3xl: 1.875rem   /* 30px */
--font-size-4xl: 2.25rem    /* 36px */
--font-size-5xl: 3rem       /* 48px */
```

### Font Weights
```css
--font-weight-normal: 400
--font-weight-medium: 500
--font-weight-semibold: 600
--font-weight-bold: 700
--font-weight-extrabold: 800
```

## Spacing System

### Spacing Scale
```css
--space-xs: 0.25rem    /* 4px */
--space-sm: 0.5rem     /* 8px */
--space-md: 0.75rem    /* 12px */
--space-lg: 1rem       /* 16px */
--space-xl: 1.25rem    /* 20px */
--space-2xl: 1.5rem    /* 24px */
--space-3xl: 2rem      /* 32px */
--space-4xl: 2.5rem    /* 40px */
--space-5xl: 3rem      /* 48px */
--space-6xl: 4rem      /* 64px */
```

## Component Library

### Buttons
```html
<!-- Primary Button -->
<button class="btn btn-primary">Primary Action</button>

<!-- Secondary Button -->
<button class="btn btn-secondary">Secondary Action</button>

<!-- Success Button -->
<button class="btn btn-success">Success Action</button>

<!-- Button Sizes -->
<button class="btn btn-primary btn-sm">Small</button>
<button class="btn btn-primary">Default</button>
<button class="btn btn-primary btn-lg">Large</button>
```

### Cards
```html
<!-- Basic Card -->
<div class="card">
  <div class="card-header">
    <h3 class="card-title">Card Title</h3>
    <p class="card-subtitle">Card subtitle</p>
  </div>
  <div class="card-content">
    <p>Card content goes here.</p>
  </div>
  <div class="card-footer">
    <button class="btn btn-primary">Action</button>
  </div>
</div>

<!-- Metric Card -->
<div class="metric-card">
  <div class="metric-value">$1,234,567</div>
  <div class="metric-label">Total Value</div>
</div>
```

### Forms
```html
<div class="form-group">
  <label class="form-label" for="email">Email Address</label>
  <input type="email" id="email" class="form-input" placeholder="Enter your email">
</div>

<div class="form-group">
  <label class="form-label" for="message">Message</label>
  <textarea id="message" class="form-textarea" placeholder="Enter your message"></textarea>
</div>
```

### Navigation
```html
<header class="header">
  <div class="container">
    <div class="header-content">
      <a href="/" class="logo">Aureus Alliance</a>
      <nav class="nav-menu">
        <a href="/" class="nav-item active">Home</a>
        <a href="/about" class="nav-item">About</a>
        <a href="/contact" class="nav-item">Contact</a>
      </nav>
      <button class="theme-toggle" aria-label="Toggle theme">🌙</button>
    </div>
  </div>
</header>
```

## Layout Components

### Container
```html
<div class="container">
  <!-- Content automatically centered with responsive padding -->
</div>
```

### Grid System
```html
<!-- Responsive Grid -->
<div class="grid grid-responsive">
  <div class="card">Item 1</div>
  <div class="card">Item 2</div>
  <div class="card">Item 3</div>
</div>

<!-- Fixed Columns -->
<div class="grid grid-cols-3">
  <div>Column 1</div>
  <div>Column 2</div>
  <div>Column 3</div>
</div>
```

### Flexbox Utilities
```html
<div class="flex items-center justify-between">
  <div>Left content</div>
  <div>Right content</div>
</div>
```

## Theme Switching

### JavaScript API
```javascript
// Theme is automatically managed by aureus.js
// Manual theme switching:
document.documentElement.setAttribute('data-theme', 'dark');
document.documentElement.setAttribute('data-theme', 'light');

// Listen for theme changes:
window.addEventListener('themeChanged', (e) => {
  console.log('Theme changed to:', e.detail.theme);
});
```

### CSS Theme Usage
```css
/* Styles automatically adapt to theme */
.my-component {
  background-color: var(--surface);
  color: var(--text);
  border: 1px solid var(--border);
}
```

## Responsive Design

### Breakpoints
```css
/* Mobile First Approach */
/* Base styles: 0px and up */

@media (min-width: 480px) { /* Mobile Large */ }
@media (min-width: 768px) { /* Tablet */ }
@media (min-width: 1024px) { /* Desktop */ }
@media (min-width: 1280px) { /* Large Desktop */ }
```

### Responsive Classes
```html
<!-- Grid responsive to screen size -->
<div class="grid-responsive">
  <!-- Automatically adjusts columns based on screen size -->
</div>

<!-- Manual responsive control -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
  <!-- 1 column on mobile, 2 on tablet, 3 on desktop -->
</div>
```

## Accessibility Features

### Focus Management
- All interactive elements have visible focus indicators
- Keyboard navigation fully supported
- Screen reader optimized with proper ARIA labels

### Color Contrast
- All color combinations meet WCAG 2.1 AA standards
- High contrast mode support included

### Reduced Motion
- Respects user's motion preferences
- Animations disabled for users who prefer reduced motion

## Utility Classes

### Text Utilities
```html
<p class="text-center text-gold">Centered gold text</p>
<p class="text-muted">Muted text</p>
```

### Spacing Utilities
```html
<div class="m-0 p-lg">No margin, large padding</div>
```

### Display Utilities
```html
<div class="flex items-center justify-center">Centered content</div>
<div class="hidden">Hidden element</div>
```

## Animation System

### Built-in Animations
```html
<div class="animate-fade-in">Fades in on load</div>
<div class="animate-slide-up">Slides up on load</div>
<div class="loading">Shows loading shimmer</div>
```

### Custom Animations
```css
@keyframes customAnimation {
  from { opacity: 0; }
  to { opacity: 1; }
}

.custom-animate {
  animation: customAnimation 0.5s ease-in-out;
}
```

## Legacy Compatibility

### Variable Mappings
The system maintains backward compatibility by mapping old variables to new ones:

```css
/* Old variables still work */
--gold-primary → var(--gold)
--theme-bg → var(--bg)
--theme-text → var(--text)
```

### Component Migration
Existing components continue to work without modification while gradually adopting the new system.

## Performance Optimizations

### CSS Custom Properties
- Efficient theme switching without JavaScript
- Reduced CSS bundle size through variable reuse

### Responsive Images
- Automatic lazy loading support
- Optimized for different screen densities

### Reduced Motion
- Respects user preferences for better performance
- Minimal animations on low-power devices

## Maintenance Guidelines

### Adding New Components
1. Use existing CSS custom properties
2. Follow the established naming conventions
3. Ensure responsive design from mobile-first
4. Test in both light and dark themes
5. Verify accessibility compliance

### Color Usage
- Always use CSS custom properties, never hardcoded colors
- Test color combinations for proper contrast
- Ensure colors work in both themes

### Typography
- Use the established font size scale
- Maintain consistent line heights
- Ensure readability across all devices

## Browser Support

### Supported Browsers
- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

### Fallbacks
- CSS custom properties with fallback values
- Progressive enhancement for advanced features
- Graceful degradation for older browsers

## Future Enhancements

### Planned Features
- Additional color themes
- More animation presets
- Enhanced component library
- Advanced accessibility features

### Extensibility
The system is designed to be easily extended with:
- New color themes
- Additional components
- Custom animations
- Enhanced responsive features

---

## Quick Start

1. **Import the system**: Already included in `index.css`
2. **Use components**: Apply classes from the component library
3. **Theme switching**: Automatic with the theme toggle button
4. **Customize**: Modify CSS custom properties as needed

For questions or issues, refer to the component examples in the codebase or consult this documentation.
