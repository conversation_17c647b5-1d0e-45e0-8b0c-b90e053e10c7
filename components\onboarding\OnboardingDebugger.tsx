import React, { useState, useEffect } from 'react';
import { useOnboarding } from '../../lib/hooks/useOnboarding';
import { onboardingService } from '../../lib/services/onboardingService';
import { supabase } from '../../lib/supabase';

interface OnboardingDebuggerProps {
  userId: number;
}

export const OnboardingDebugger: React.FC<OnboardingDebuggerProps> = ({ userId }) => {
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [userEmail, setUserEmail] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const { status, loading: hookLoading, error } = useOnboarding({ userId, autoLoad: true });

  useEffect(() => {
    const fetchDebugInfo = async () => {
      setLoading(true);
      try {
        // Get user email
        const { data: userData } = await supabase
          .from('users')
          .select('email, full_name, kyc_status, created_at')
          .eq('id', userId)
          .single();

        if (userData?.email) {
          setUserEmail(userData.email);
        }

        // Get onboarding progress directly from database
        const { data: progressData } = await supabase
          .from('user_onboarding_progress')
          .select('*')
          .eq('user_id', userId)
          .order('step_id');

        // Get all available steps from service
        const allSteps = await onboardingService.getAllSteps();

        setDebugInfo({
          userData,
          progressData: progressData || [],
          allSteps,
          hookStatus: status,
          hookLoading,
          hookError: error
        });

      } catch (error) {
        console.error('Debug fetch error:', error);
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchDebugInfo();
    }
  }, [userId, status]);

  const handleResetProgress = async () => {
    if (confirm('Reset all onboarding progress for this user?')) {
      try {
        await supabase
          .from('user_onboarding_progress')
          .delete()
          .eq('user_id', userId);
        
        window.location.reload();
      } catch (error) {
        console.error('Reset error:', error);
      }
    }
  };

  const handleStartEmailVerification = async () => {
    try {
      const success = await onboardingService.startStep(userId, 'email_verification');
      console.log('Start email verification result:', success);
      window.location.reload();
    } catch (error) {
      console.error('Start email verification error:', error);
    }
  };

  const handleCompleteEmailVerification = async () => {
    try {
      const success = await onboardingService.completeStep(userId, 'email_verification', {
        verified_at: new Date().toISOString(),
        email: userEmail
      });
      console.log('Complete email verification result:', success);
      window.location.reload();
    } catch (error) {
      console.error('Complete email verification error:', error);
    }
  };

  if (loading || hookLoading) {
    return (
      <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-6">
        <h3 className="text-red-400 font-bold mb-4">🔧 Onboarding Debugger - Loading...</h3>
        <div className="animate-pulse">Loading debug information...</div>
      </div>
    );
  }

  return (
    <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-6 mb-6">
      <h3 className="text-red-400 font-bold mb-4">🔧 Onboarding Debugger</h3>
      
      {/* User Info */}
      <div className="mb-4">
        <h4 className="text-white font-semibold mb-2">User Information</h4>
        <div className="bg-gray-800 rounded p-3 text-sm">
          <p><strong>User ID:</strong> {userId}</p>
          <p><strong>Email:</strong> {userEmail || 'Not found'}</p>
          <p><strong>Full Name:</strong> {debugInfo?.userData?.full_name || 'Not set'}</p>
          <p><strong>KYC Status:</strong> {debugInfo?.userData?.kyc_status || 'Not set'}</p>
        </div>
      </div>

      {/* Hook Status */}
      <div className="mb-4">
        <h4 className="text-white font-semibold mb-2">Hook Status</h4>
        <div className="bg-gray-800 rounded p-3 text-sm">
          <p><strong>Loading:</strong> {hookLoading ? 'Yes' : 'No'}</p>
          <p><strong>Error:</strong> {error || 'None'}</p>
          <p><strong>Overall Progress:</strong> {status?.overall_progress || 0}%</p>
          <p><strong>Completed Steps:</strong> {status?.completed_steps || 0}/{status?.total_steps || 0}</p>
          <p><strong>Current Step:</strong> {status?.current_step?.name || 'None'}</p>
          <p><strong>Next Steps Count:</strong> {status?.next_steps?.length || 0}</p>
        </div>
      </div>

      {/* Next Steps */}
      {status?.next_steps && status.next_steps.length > 0 && (
        <div className="mb-4">
          <h4 className="text-white font-semibold mb-2">Next Steps ({status.next_steps.length})</h4>
          <div className="bg-gray-800 rounded p-3 text-sm space-y-2">
            {status.next_steps.map(step => (
              <div key={step.id} className="flex items-center justify-between">
                <span>{step.icon} {step.name}</span>
                <span className="text-gray-400">{step.id}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Database Progress */}
      <div className="mb-4">
        <h4 className="text-white font-semibold mb-2">Database Progress</h4>
        <div className="bg-gray-800 rounded p-3 text-sm">
          {debugInfo?.progressData?.length > 0 ? (
            <div className="space-y-1">
              {debugInfo.progressData.map((progress: any) => (
                <div key={progress.step_id} className="flex items-center justify-between">
                  <span>{progress.step_id}</span>
                  <span className={`px-2 py-1 rounded text-xs ${
                    progress.status === 'completed' ? 'bg-green-600' :
                    progress.status === 'in_progress' ? 'bg-blue-600' :
                    'bg-gray-600'
                  }`}>
                    {progress.status}
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-400">No progress records found</p>
          )}
        </div>
      </div>

      {/* All Available Steps */}
      <div className="mb-4">
        <h4 className="text-white font-semibold mb-2">All Available Steps</h4>
        <div className="bg-gray-800 rounded p-3 text-sm max-h-40 overflow-y-auto">
          {debugInfo?.allSteps?.map((step: any) => (
            <div key={step.id} className="flex items-center justify-between py-1">
              <span>{step.icon} {step.name}</span>
              <div className="flex items-center gap-2">
                <span className="text-gray-400 text-xs">{step.id}</span>
                {step.required && <span className="text-red-400 text-xs">Required</span>}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Debug Actions */}
      <div className="space-y-2">
        <h4 className="text-white font-semibold mb-2">Debug Actions</h4>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={handleResetProgress}
            className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
          >
            Reset All Progress
          </button>
          <button
            onClick={handleStartEmailVerification}
            className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
          >
            Start Email Verification
          </button>
          <button
            onClick={handleCompleteEmailVerification}
            className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
          >
            Complete Email Verification
          </button>
          <button
            onClick={() => window.location.reload()}
            className="px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Email Verification Test */}
      {userEmail && (
        <div className="mt-4 p-3 bg-blue-900/20 border border-blue-500/30 rounded">
          <h5 className="text-blue-400 font-semibold mb-2">Email Verification Test</h5>
          <p className="text-sm text-gray-300 mb-2">
            Email: {userEmail}
          </p>
          <p className="text-xs text-gray-400">
            This will test the actual email verification flow with Resend API
          </p>
        </div>
      )}
    </div>
  );
};
