import React from 'react';
import { ReferralCenter } from '../../referrals/ReferralCenter';
import { CommissionWithdrawal } from '../../user/CommissionWithdrawal';
import { USDTToSharesConverter } from '../../affiliate/USDTToSharesConverter';
import { ShareTransferSystem } from '../../affiliate/ShareTransferSystem';
import { DashboardData } from '../../../hooks/useDashboardData';

interface ReferralsSectionProps {
  user: any;
  dashboardData: DashboardData;
  onRefreshData: () => Promise<void>;
}

export const ReferralsSection: React.FC<ReferralsSectionProps> = ({
  user,
  dashboardData,
  onRefreshData
}) => {
  return (
    <div className="space-y-6">
      {/* Section Header */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h2 className="text-2xl font-bold text-white mb-2">Referrals & Commissions</h2>
        <p className="text-gray-400">
          Manage your referrals, track commissions, and withdraw earnings.
        </p>
      </div>

      {/* Referral Center */}
      <ReferralCenter
        userId={user?.database_user?.id || user?.id}
        className="bg-gray-800 rounded-lg border border-gray-700 p-6"
      />

      {/* Commission Management */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* USDT to Shares Converter */}
        <USDTToSharesConverter
          userId={user?.database_user?.id || user?.id}
          availableUSDT={dashboardData.usdtCommissions?.available || 0}
          onConversionComplete={onRefreshData}
        />

        {/* Commission Withdrawal */}
        <CommissionWithdrawal
          userId={user?.database_user?.id || user?.id}
          availableUSDT={dashboardData.usdtCommissions?.available || 0}
          availableShares={dashboardData.shareCommissions?.totalShares || 0}
          currentSharePrice={dashboardData.currentSharePrice}
          onWithdrawalRequest={onRefreshData}
        />
      </div>

      {/* Share Transfer System */}
      <ShareTransferSystem
        userId={user?.database_user?.id || user?.id}
        availableShares={dashboardData.shareCommissions?.totalShares || 0}
        onTransferComplete={onRefreshData}
      />
    </div>
  );
};
