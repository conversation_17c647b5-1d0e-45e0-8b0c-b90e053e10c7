import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with service role key
const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function completeResetUser4() {
  console.log('🔄 COMPLETE RESET OF USER ID 4 FOR MIGRATION TESTING');
  console.log('====================================================');
  
  try {
    // Step 1: Check current state
    console.log('📋 Step 1: Current state...');
    const { data: currentUser, error: fetchError } = await supabase
      .from('users')
      .select('id, email, username, password_hash, migration_status, web_credentials_set_at, is_verified')
      .eq('id', 4)
      .single();

    if (fetchError) {
      console.error('❌ Error fetching user:', fetchError);
      return;
    }

    console.log('   Email:', currentUser.email);
    console.log('   Username:', currentUser.username);
    console.log('   Password Hash:', currentUser.password_hash);
    console.log('   Migration Status:', currentUser.migration_status);
    console.log('   Is Verified:', currentUser.is_verified);

    // Step 2: Complete reset to pure Telegram user
    console.log('\n📋 Step 2: Resetting to pure Telegram user state...');
    const { error: resetError } = await supabase
      .from('users')
      .update({
        // Core authentication
        password_hash: 'telegram_auth',
        
        // Migration fields
        migration_status: null,
        web_credentials_set_at: null,
        migration_completed_at: null,
        
        // Verification status
        is_verified: false,
        
        // Email - keep real email for testing
        email: '<EMAIL>',
        
        // Update timestamp
        updated_at: new Date().toISOString()
      })
      .eq('id', 4);

    if (resetError) {
      console.error('❌ Error resetting user:', resetError);
      return;
    }

    console.log('✅ User reset to pure Telegram state');

    // Step 3: Clear any cached sessions or data
    console.log('\n📋 Step 3: Clearing cached data...');
    
    // Clear email verification codes
    const { error: codeError } = await supabase
      .from('email_verification_codes')
      .delete()
      .eq('user_id', 4);

    if (codeError) {
      console.log('⚠️ Could not clear email codes:', codeError.message);
    } else {
      console.log('✅ Email verification codes cleared');
    }

    // Step 4: Verify the reset
    console.log('\n📋 Step 4: Verifying complete reset...');
    const { data: verifyUser, error: verifyError } = await supabase
      .from('users')
      .select('id, email, username, password_hash, migration_status, web_credentials_set_at, is_verified')
      .eq('id', 4)
      .single();

    if (verifyError) {
      console.error('❌ Error verifying reset:', verifyError);
      return;
    }

    console.log('✅ Final state verification:');
    console.log('   Email:', verifyUser.email);
    console.log('   Username:', verifyUser.username);
    console.log('   Password Hash:', verifyUser.password_hash);
    console.log('   Migration Status:', verifyUser.migration_status);
    console.log('   Web Credentials Set:', verifyUser.web_credentials_set_at);
    console.log('   Is Verified:', verifyUser.is_verified);

    console.log('\n🎉 COMPLETE RESET SUCCESSFUL!');
    console.log('==============================');
    console.log('📱 User ID 4 (TTTFOUNDER) is now:');
    console.log('   ✅ Pure Telegram user (password_hash: telegram_auth)');
    console.log('   ✅ Not migrated (migration_status: null)');
    console.log('   ✅ No web credentials (web_credentials_set_at: null)');

    console.log('   ✅ Not verified (requires migration)');
    console.log('   ✅ Real email for testing: <EMAIL>');
    console.log('');
    console.log('🧪 TESTING INSTRUCTIONS:');
    console.log('1. Clear browser cache/localStorage');
    console.log('2. Login with Telegram (should work)');
    console.log('3. System should detect need for migration');
    console.log('4. Visit /migrate or get redirected automatically');
    console.log('5. Complete step-by-step migration');
    console.log('6. Test web login with email/password');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the complete reset
completeResetUser4();
