import { supabase, getServiceRoleClient } from './supabase';

export interface KYCValidationResult {
  isValid: boolean;
  status: 'not_started' | 'pending' | 'approved' | 'rejected' | 'expired';
  message: string;
  canWithdraw: boolean;
  canReceiveDividends: boolean;
}

/**
 * Validate if user has completed KYC for financial operations
 */
export const validateKYCForFinancialOperations = async (userId: number): Promise<KYCValidationResult> => {
  try {
    console.log(`🔍 Validating KYC for user ${userId} for financial operations...`);

    const serviceClient = getServiceRoleClient();
    const { data: kycData, error } = await serviceClient
      .from('kyc_information')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('❌ Error fetching KYC data:', error);
      return {
        isValid: false,
        status: 'not_started',
        message: 'Error checking KYC status. Please try again.',
        canWithdraw: false,
        canReceiveDividends: false
      };
    }

    // No KYC record found
    if (!kycData) {
      return {
        isValid: false,
        status: 'not_started',
        message: 'KYC verification required for commission withdrawals and dividend payments.',
        canWithdraw: false,
        canReceiveDividends: false
      };
    }

    // Check if KYC has expired
    const isExpired = kycData.expires_at && new Date(kycData.expires_at) < new Date();
    if (isExpired) {
      return {
        isValid: false,
        status: 'expired',
        message: 'Your KYC verification has expired. Please update your information.',
        canWithdraw: false,
        canReceiveDividends: false
      };
    }

    // Check KYC status - handle both verification_status and kyc_status fields
    const status = kycData.verification_status || kycData.kyc_status;
    switch (status) {
      case 'approved':
      case 'completed':
        return {
          isValid: true,
          status: 'approved',
          message: 'KYC verification complete. You can withdraw commissions and receive dividends.',
          canWithdraw: true,
          canReceiveDividends: true
        };

      case 'pending_review':
      case 'pending':
        return {
          isValid: false,
          status: 'pending',
          message: 'Your KYC is under review. Please wait for approval before withdrawing commissions or receiving dividends.',
          canWithdraw: false,
          canReceiveDividends: false
        };

      case 'rejected':
        return {
          isValid: false,
          status: 'rejected',
          message: 'Your KYC was rejected. Please update your information and resubmit.',
          canWithdraw: false,
          canReceiveDividends: false
        };

      default:
        return {
          isValid: false,
          status: 'not_started',
          message: 'Please complete KYC verification to withdraw commissions and receive dividends.',
          canWithdraw: false,
          canReceiveDividends: false
        };
    }

  } catch (error) {
    console.error('❌ KYC validation error:', error);
    return {
      isValid: false,
      status: 'not_started',
      message: 'Error validating KYC status. Please try again.',
      canWithdraw: false,
      canReceiveDividends: false
    };
  }
};

/**
 * Get user-friendly KYC status message
 */
export const getKYCStatusMessage = (status: string): string => {
  switch (status) {
    case 'not_started':
      return '📋 Complete KYC verification to unlock commission withdrawals and dividend payments.';
    case 'pending':
    case 'pending_review':
      return '⏳ Your KYC is under review. You\'ll be able to withdraw once approved.';
    case 'approved':
      return '✅ KYC verified! You can withdraw commissions and receive dividends.';
    case 'rejected':
      return '❌ KYC rejected. Please update your information and resubmit.';
    case 'expired':
      return '⚠️ KYC expired. Please update your verification to continue withdrawing.';
    default:
      return '📋 KYC verification required for financial operations.';
  }
};

/**
 * Check if user can perform specific financial operation
 */
export const canPerformFinancialOperation = async (
  userId: number, 
  operation: 'withdraw_commission' | 'receive_dividend'
): Promise<{ allowed: boolean; message: string }> => {
  const validation = await validateKYCForFinancialOperations(userId);
  
  if (operation === 'withdraw_commission') {
    return {
      allowed: validation.canWithdraw,
      message: validation.canWithdraw 
        ? 'You can withdraw your commissions.' 
        : validation.message
    };
  }
  
  if (operation === 'receive_dividend') {
    return {
      allowed: validation.canReceiveDividends,
      message: validation.canReceiveDividends 
        ? 'You can receive dividend payments.' 
        : validation.message
    };
  }
  
  return {
    allowed: false,
    message: 'Unknown operation type.'
  };
};
