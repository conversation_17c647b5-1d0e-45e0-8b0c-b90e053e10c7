import React, { useState, useEffect } from 'react'
import { supabase, getServiceRoleClient } from '../../lib/supabase'
import { UserEditModal } from './UserEditModal'
import { UserFinancialModal } from './UserFinancialModal'
import { FinancialActionsModal } from './FinancialActionsModal'
import { CommunicationModal } from './CommunicationModal'
import { BulkActionsModal } from './BulkActionsModal'
import { AddUserModal } from './AddUserModal'
import { ReferralLinksModal } from './ReferralLinksModal'
import { UserImpersonationModal } from './UserImpersonationModal'
import { useConfirmationDialog } from './ConfirmationDialog'
import { withAdminAuth, logAdminAction } from '../../lib/adminAuth'

interface SharePurchase {
  id: string
  package_name: string
  shares_purchased: number
  total_amount: number
  commission_used: number
  remaining_payment: number
  payment_method: string
  status: string
  created_at: string
  updated_at: string
}

interface PaymentTransaction {
  id: string
  amount: number
  currency: string
  network: string
  status: string
  created_at: string
  payment_method?: string
}

interface CommissionBalance {
  usdt_balance: number
  share_balance: number
  total_earned_usdt: number
  total_earned_shares: number
  total_withdrawn_usdt: number
}

interface ReferralData {
  id: string
  referral_code: string
  commission_rate: number
  total_commission: number
  status: string
  created_at: string
  referrer?: {
    id: number
    username: string
    full_name: string | null
  }
  referred?: {
    id: number
    username: string
    full_name: string | null
  }
}

interface KYCInformation {
  id: string
  first_name: string
  last_name: string
  full_legal_name: string
  id_type: string
  phone_number: string
  email_address: string
  street_address: string
  city: string
  postal_code: string
  country_code: string
  country_name: string
  data_consent_given: boolean
  privacy_policy_accepted: boolean
  kyc_completed_at: string
  kyc_status: string
  certificate_requested: boolean
  certificate_generated_at: string | null
  certificate_sent_at: string | null
  created_at: string
}

interface User {
  id: number
  username: string
  email: string
  password_hash: string
  full_name: string | null
  phone: string | null
  address: string | null
  is_active: boolean
  is_verified: boolean
  telegram_id: number | null
  country_of_residence: string | null
  role: string
  is_admin: boolean
  created_at: string
  updated_at: string
  telegram_users?: {
    id: string
    telegram_id: number
    username: string
    first_name: string
    last_name: string
  }[]
  // Financial data
  share_purchases?: SharePurchase[]
  payment_transactions?: PaymentTransaction[]
  commission_balances?: CommissionBalance
  // Referral data
  referrals_made?: ReferralData[]
  referred_by?: ReferralData
  // KYC data
  kyc_information?: KYCInformation
  // Calculated fields
  total_shares?: number
  total_invested?: number
  total_commissions?: number
  total_referrals?: number
  // Activity indicators
  last_activity?: string
  activity_score?: number
}

interface UserManagerProps {
  currentUser?: any
  adminUser?: any
  permissions?: any
}

const UserManagerComponent: React.FC<UserManagerProps> = ({ currentUser, adminUser, permissions }) => {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const { showConfirmation, ConfirmationDialog } = useConfirmationDialog()
  const [searchTerm, setSearchTerm] = useState('')
  const [filterActive, setFilterActive] = useState<'all' | 'active' | 'inactive'>('all')
  const [filterRole, setFilterRole] = useState<'all' | 'user' | 'admin'>('all')
  const [filterKYC, setFilterKYC] = useState<'all' | 'completed' | 'pending' | 'none'>('all')
  const [filterInvestment, setFilterInvestment] = useState<'all' | 'investors' | 'non-investors'>('all')
  const [filterReferrals, setFilterReferrals] = useState<'all' | 'has-referrals' | 'no-referrals'>('all')
  const [filterActivity, setFilterActivity] = useState<'all' | 'high' | 'medium' | 'low'>('all')
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showFinancialModal, setShowFinancialModal] = useState(false)
  const [showFinancialActionsModal, setShowFinancialActionsModal] = useState(false)
  const [showCommunicationModal, setShowCommunicationModal] = useState(false)
  const [showBulkActionsModal, setShowBulkActionsModal] = useState(false)
  const [showAddUserModal, setShowAddUserModal] = useState(false)
  const [selectedUserIds, setSelectedUserIds] = useState<Set<number>>(new Set())
  const [showReferralLinksModal, setShowReferralLinksModal] = useState(false)
  const [showImpersonationModal, setShowImpersonationModal] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [usersPerPage] = useState(10)
  const [sortField, setSortField] = useState<'username' | 'created_at' | 'total_shares' | 'total_invested' | 'total_commissions' | 'total_referrals'>('created_at')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  useEffect(() => {
    loadUsers()
  }, [])

  const loadUsers = async () => {
    try {
      setLoading(true)
      setError('')

      // Load users with all related financial data using service role client
      const serviceClient = getServiceRoleClient()
      const { data: usersData, error: usersError } = await serviceClient
        .from('users')
        .select(`
          *,
          telegram_users(
            id,
            telegram_id,
            username,
            first_name,
            last_name
          )
        `)
        .order('created_at', { ascending: false })

      if (usersError) {
        throw usersError
      }

      // Get all user IDs for batch queries
      const userIds = (usersData || []).map(user => user.id)

      // Load all financial data in batch queries using the same service role client
      const [
        { data: allSharePurchases },
        { data: allPaymentTransactions },
        { data: allCommissionBalances },
        { data: allReferrals },
        { data: allKycInformation }
      ] = await Promise.all([
        serviceClient
          .from('aureus_share_purchases')
          .select('*')
          .in('user_id', userIds)
          .order('created_at', { ascending: false }),
        serviceClient
          .from('crypto_payment_transactions')
          .select('*')
          .in('user_id', userIds)
          .order('created_at', { ascending: false }),
        serviceClient
          .from('commission_balances')
          .select('*')
          .in('user_id', userIds),
        serviceClient
          .from('referrals')
          .select(`
            *,
            referrer:referrer_id(id, username, full_name),
            referred:referred_id(id, username, full_name)
          `)
          .or(`referrer_id.in.(${userIds.join(',')}),referred_id.in.(${userIds.join(',')})`),
        serviceClient
          .from('kyc_information')
          .select('*')
          .in('user_id', userIds)
      ])

      // Process users with their financial data
      const usersWithFinancialData = (usersData || []).map((user) => {
        try {
          // Get user's data from batch queries
          const sharePurchases = (allSharePurchases || []).filter(sp => sp.user_id === user.id)
          const paymentTransactions = (allPaymentTransactions || []).filter(pt => pt.user_id === user.id)
          const commissionBalances = (allCommissionBalances || []).find(cb => cb.user_id === user.id)
          const referralsMade = (allReferrals || []).filter(r => r.referrer_id === user.id)
          const referredBy = (allReferrals || []).find(r => r.referred_id === user.id)
          const kycInformation = (allKycInformation || []).find(kyc => kyc.user_id === user.id)

          // Calculate totals - include both purchased shares AND commission share balance
          const purchasedShares = sharePurchases?.reduce((sum, purchase) =>
            sum + (purchase.shares_purchased || 0), 0) || 0
          const commissionShares = commissionBalances?.share_balance || 0
          const totalShares = purchasedShares + commissionShares

          const totalInvested = sharePurchases?.reduce((sum, purchase) =>
            sum + (purchase.total_amount || 0), 0) || 0

          // Calculate total commissions (USDT + share value at $25 per share)
          const usdtCommissions = commissionBalances?.total_earned_usdt || 0
          const shareCommissionsValue = (commissionBalances?.total_earned_shares || 0) * 25
          const totalCommissions = usdtCommissions + shareCommissionsValue
          const totalReferrals = referralsMade?.length || 0

          // Calculate activity indicators
          const activities = [
            ...(sharePurchases || []).map(p => new Date(p.created_at)),
            ...(paymentTransactions || []).map(p => new Date(p.created_at)),
            ...(referralsMade || []).map(r => new Date(r.created_at)),
            kycInformation ? new Date(kycInformation.created_at) : null
          ].filter(Boolean).sort((a, b) => b.getTime() - a.getTime())

          const lastActivity = activities.length > 0 ? activities[0].toISOString() : user.created_at
          const activityScore = Math.min(100, (activities.length * 10) + (totalShares > 0 ? 20 : 0) + (totalReferrals > 0 ? 15 : 0) + (kycInformation ? 25 : 0))

          return {
            ...user,
            share_purchases: sharePurchases || [],
            payment_transactions: paymentTransactions || [],
            commission_balances: commissionBalances,
            referrals_made: referralsMade || [],
            referred_by: referredBy,
            kyc_information: kycInformation,
            total_shares: totalShares,
            total_invested: totalInvested,
            total_commissions: totalCommissions,
            total_referrals: totalReferrals,
            last_activity: lastActivity,
            activity_score: activityScore
          }
          } catch (err) {
            console.error(`Error loading financial data for user ${user.id}:`, err)
            return {
              ...user,
              share_purchases: [],
              payment_transactions: [],
              commission_balances: null,
              referrals_made: [],
              referred_by: null,
              kyc_information: null,
              total_shares: 0,
              total_invested: 0,
              total_commissions: 0,
              total_referrals: 0,
              last_activity: user.created_at,
              activity_score: 0
            }
          }
        })

      setUsers(usersWithFinancialData)
    } catch (err: any) {
      console.error('Error loading users:', err)
      setError(err.message || 'Failed to load users')
    } finally {
      setLoading(false)
    }
  }

  // Filter and search users
  const filteredUsers = users.filter(user => {
    const matchesSearch =
      user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (user.full_name && user.full_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (user.kyc_information?.full_legal_name && user.kyc_information.full_legal_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (user.referred_by?.referrer?.username && user.referred_by.referrer.username.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (user.referred_by?.referrer?.full_name && user.referred_by.referrer.full_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (user.referred_by?.referrer?.id && user.referred_by.referrer.id.toString().includes(searchTerm))

    const matchesActiveFilter =
      filterActive === 'all' ||
      (filterActive === 'active' && user.is_active) ||
      (filterActive === 'inactive' && !user.is_active)

    const matchesRoleFilter =
      filterRole === 'all' ||
      (filterRole === 'admin' && user.is_admin) ||
      (filterRole === 'user' && !user.is_admin)

    const matchesKYCFilter =
      filterKYC === 'all' ||
      (filterKYC === 'completed' && user.kyc_information?.kyc_status === 'completed') ||
      (filterKYC === 'pending' && user.kyc_information?.kyc_status === 'pending') ||
      (filterKYC === 'none' && !user.kyc_information)

    const matchesInvestmentFilter =
      filterInvestment === 'all' ||
      (filterInvestment === 'investors' && (user.total_invested || 0) > 0) ||
      (filterInvestment === 'non-investors' && (user.total_invested || 0) === 0)

    const matchesReferralFilter =
      filterReferrals === 'all' ||
      (filterReferrals === 'has-referrals' && (user.total_referrals || 0) > 0) ||
      (filterReferrals === 'no-referrals' && (user.total_referrals || 0) === 0)

    const matchesActivityFilter =
      filterActivity === 'all' ||
      (filterActivity === 'high' && (user.activity_score || 0) >= 70) ||
      (filterActivity === 'medium' && (user.activity_score || 0) >= 30 && (user.activity_score || 0) < 70) ||
      (filterActivity === 'low' && (user.activity_score || 0) < 30)

    return matchesSearch && matchesActiveFilter && matchesRoleFilter && matchesKYCFilter && matchesInvestmentFilter && matchesReferralFilter && matchesActivityFilter
  }).sort((a, b) => {
    let aValue: any = a[sortField]
    let bValue: any = b[sortField]

    // Handle null/undefined values
    if (aValue == null) aValue = 0
    if (bValue == null) bValue = 0

    // Handle string vs number comparison
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      aValue = aValue.toLowerCase()
      bValue = bValue.toLowerCase()
    }

    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
    } else {
      return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
    }
  })

  // Pagination
  const indexOfLastUser = currentPage * usersPerPage
  const indexOfFirstUser = indexOfLastUser - usersPerPage
  const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser)
  const totalPages = Math.ceil(filteredUsers.length / usersPerPage)

  const handleEditUser = (user: User) => {
    setSelectedUser(user)
    setShowEditModal(true)
  }

  const handleViewFinancials = (user: User) => {
    setSelectedUser(user)
    setShowFinancialModal(true)
  }

  const handleFinancialActions = (user: User) => {
    setSelectedUser(user)
    setShowFinancialActionsModal(true)
  }

  const handleCommunication = (user: User) => {
    setSelectedUser(user)
    setShowCommunicationModal(true)
  }

  const handleViewReferralLinks = (user: User) => {
    setSelectedUser(user)
    setShowReferralLinksModal(true)
  }

  const handleLoginAsUser = async (user: User) => {
    setSelectedUser(user)
    setShowImpersonationModal(true)
  }

  const handleUserSelection = (userId: number, selected: boolean) => {
    setSelectedUserIds(prev => {
      const newSet = new Set(prev)
      if (selected) {
        newSet.add(userId)
      } else {
        newSet.delete(userId)
      }
      return newSet
    })
  }

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedUserIds(new Set(filteredUsers.map(u => u.id)))
    } else {
      setSelectedUserIds(new Set())
    }
  }

  const handleBulkActions = () => {
    if (selectedUserIds.size === 0) {
      alert('Please select users first')
      return
    }
    setShowBulkActionsModal(true)
  }

  const clearSelection = () => {
    setSelectedUserIds(new Set())
  }

  const selectedUsers = users.filter(u => selectedUserIds.has(u.id))

  const handleSort = (field: typeof sortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('desc')
    }
  }

  const getSortIcon = (field: typeof sortField) => {
    if (sortField !== field) return '↕️'
    return sortDirection === 'asc' ? '↑' : '↓'
  }

  const handleDeleteUser = async (userId: number) => {
    const user = users.find(u => u.id === userId)
    if (!user) return

    showConfirmation({
      title: 'Delete User',
      message: `Are you sure you want to delete user "${user.username}"? This action cannot be undone and will permanently remove all user data.`,
      type: 'danger',
      confirmText: 'Delete User',
      onConfirm: async () => {
        try {
          // Use service role client to bypass RLS policies for admin operations
          const { getServiceRoleClient } = await import('../../lib/supabase')
          const serviceRoleClient = getServiceRoleClient()

          console.log('🗑️ Starting comprehensive user deletion process for user ID:', userId)

          // Track what was actually deleted for user feedback
          const deletionSummary = {
            tablesProcessed: 0,
            recordsDeleted: 0,
            tablesWithData: [] as string[],
            tablesNotFound: [] as string[],
            errors: [] as string[]
          }

          // Step 1: Delete critical records that have foreign key constraints
          // Delete notification_log records first (this was causing the original error)
          console.log('🗑️ Deleting notification_log records...')
          try {
            const { data: deletedNotificationLogs, error: notificationLogError } = await serviceRoleClient
              .from('notification_log')
              .delete()
              .eq('user_id', userId)
              .select('id')

            if (notificationLogError) {
              if (notificationLogError.code === '42P01') {
                deletionSummary.tablesNotFound.push('notification_log')
              } else {
                console.error('Error deleting notification_log records:', notificationLogError)
                throw new Error(`Failed to delete notification logs: ${notificationLogError.message}`)
              }
            } else if (deletedNotificationLogs && deletedNotificationLogs.length > 0) {
              deletionSummary.recordsDeleted += deletedNotificationLogs.length
              deletionSummary.tablesWithData.push(`notification_log (${deletedNotificationLogs.length} records)`)
            }
            deletionSummary.tablesProcessed++
          } catch (err: any) {
            deletionSummary.errors.push(`notification_log: ${err.message}`)
          }

          // Step 2: Delete user_notifications records (if they exist)
          console.log('🗑️ Deleting user_notifications records...')
          try {
            const { data: deletedUserNotifications, error: userNotificationsError } = await serviceRoleClient
              .from('user_notifications')
              .delete()
              .eq('user_id', userId)
              .select('id')

            if (userNotificationsError) {
              if (userNotificationsError.code === '42P01') {
                deletionSummary.tablesNotFound.push('user_notifications')
              } else {
                console.error('Error deleting user_notifications records:', userNotificationsError)
                throw new Error(`Failed to delete user notifications: ${userNotificationsError.message}`)
              }
            } else if (deletedUserNotifications && deletedUserNotifications.length > 0) {
              deletionSummary.recordsDeleted += deletedUserNotifications.length
              deletionSummary.tablesWithData.push(`user_notifications (${deletedUserNotifications.length} records)`)
            }
            deletionSummary.tablesProcessed++
          } catch (err: any) {
            deletionSummary.errors.push(`user_notifications: ${err.message}`)
          }

          // Step 3: Delete notification_preferences records (if they exist)
          console.log('🗑️ Deleting notification_preferences records...')
          try {
            const { data: deletedNotificationPrefs, error: notificationPrefsError } = await serviceRoleClient
              .from('notification_preferences')
              .delete()
              .eq('user_id', userId)
              .select('id')

            if (notificationPrefsError) {
              if (notificationPrefsError.code === '42P01') {
                deletionSummary.tablesNotFound.push('notification_preferences')
              } else {
                console.error('Error deleting notification_preferences records:', notificationPrefsError)
                throw new Error(`Failed to delete notification preferences: ${notificationPrefsError.message}`)
              }
            } else if (deletedNotificationPrefs && deletedNotificationPrefs.length > 0) {
              deletionSummary.recordsDeleted += deletedNotificationPrefs.length
              deletionSummary.tablesWithData.push(`notification_preferences (${deletedNotificationPrefs.length} records)`)
            }
            deletionSummary.tablesProcessed++
          } catch (err: any) {
            deletionSummary.errors.push(`notification_preferences: ${err.message}`)
          }

          // Step 4: Delete other related records that might exist
          // Based on schema analysis, these tables have foreign key references to users(id)
          const relatedTables = [
            'telegram_users',
            'aureus_share_purchases',
            'commission_balances',
            'commission_transactions',
            'referrals',
            'crypto_payment_transactions',
            'terms_acceptance',
            'kyc_information',
            'commission_withdrawals',
            'email_verification_codes',
            'account_change_logs',
            'newsletter_subscriptions',
            'user_payment_methods',
            'competition_participants',
            'certificates',
            'user_balances',
            'smart_notifications',
            'marketing_content_generated',
            'marketing_training_progress',
            'user_messages', // Both sender_id and recipient_id
            'suspicious_users',
            'withdrawal_requests',
            'share_transfers', // Both sender_user_id and recipient_user_id
            'commission_distributions', // Both user_id and source_user_id
            'admin_actions', // admin_user_id
            'competitions' // created_by
          ]

          console.log('🗑️ Processing related tables...')
          for (const table of relatedTables) {
            try {
              console.log(`🗑️ Processing ${table}...`)
              deletionSummary.tablesProcessed++

              let deletedRecords = 0

              // Handle special cases for tables with different column names
              if (table === 'user_messages') {
                // Delete messages where user is sender
                const { data: senderMessages, error: senderError } = await serviceRoleClient
                  .from(table)
                  .delete()
                  .eq('sender_id', userId)
                  .select('id')

                if (senderError && senderError.code !== '42P01') {
                  throw senderError
                } else if (senderMessages) {
                  deletedRecords += senderMessages.length
                }

                // Delete messages where user is recipient
                const { data: recipientMessages, error: recipientError } = await serviceRoleClient
                  .from(table)
                  .delete()
                  .eq('recipient_id', userId)
                  .select('id')

                if (recipientError && recipientError.code !== '42P01') {
                  throw recipientError
                } else if (recipientMessages) {
                  deletedRecords += recipientMessages.length
                }
              } else if (table === 'commission_transactions') {
                // Delete where user is referrer
                const { data: referrerCommissions, error: referrerError } = await serviceRoleClient
                  .from(table)
                  .delete()
                  .eq('referrer_id', userId)
                  .select('id')

                if (referrerError && referrerError.code !== '42P01') {
                  throw referrerError
                } else if (referrerCommissions) {
                  deletedRecords += referrerCommissions.length
                }

                // Delete where user is referred
                const { data: referredCommissions, error: referredError } = await serviceRoleClient
                  .from(table)
                  .delete()
                  .eq('referred_id', userId)
                  .select('id')

                if (referredError && referredError.code !== '42P01') {
                  throw referredError
                } else if (referredCommissions) {
                  deletedRecords += referredCommissions.length
                }
              } else if (table === 'referrals') {
                // Delete where user is referrer
                const { data: referrerReferrals, error: referrerRefError } = await serviceRoleClient
                  .from(table)
                  .delete()
                  .eq('referrer_id', userId)
                  .select('id')

                if (referrerRefError && referrerRefError.code !== '42P01') {
                  throw referrerRefError
                } else if (referrerReferrals) {
                  deletedRecords += referrerReferrals.length
                }

                // Delete where user is referred
                const { data: referredReferrals, error: referredRefError } = await serviceRoleClient
                  .from(table)
                  .delete()
                  .eq('referred_id', userId)
                  .select('id')

                if (referredRefError && referredRefError.code !== '42P01') {
                  throw referredRefError
                } else if (referredReferrals) {
                  deletedRecords += referredReferrals.length
                }
              } else if (table === 'share_transfers') {
                // Delete where user is sender
                const { data: senderTransfers, error: senderTransferError } = await serviceRoleClient
                  .from(table)
                  .delete()
                  .eq('sender_user_id', userId)
                  .select('id')

                if (senderTransferError && senderTransferError.code !== '42P01') {
                  throw senderTransferError
                } else if (senderTransfers) {
                  deletedRecords += senderTransfers.length
                }

                // Delete where user is recipient
                const { data: recipientTransfers, error: recipientTransferError } = await serviceRoleClient
                  .from(table)
                  .delete()
                  .eq('recipient_user_id', userId)
                  .select('id')

                if (recipientTransferError && recipientTransferError.code !== '42P01') {
                  throw recipientTransferError
                } else if (recipientTransfers) {
                  deletedRecords += recipientTransfers.length
                }
              } else if (table === 'commission_distributions') {
                // Delete where user is the recipient
                const { data: recipientDistributions, error: recipientDistError } = await serviceRoleClient
                  .from(table)
                  .delete()
                  .eq('user_id', userId)
                  .select('id')

                if (recipientDistError && recipientDistError.code !== '42P01') {
                  throw recipientDistError
                } else if (recipientDistributions) {
                  deletedRecords += recipientDistributions.length
                }

                // Delete where user is the source
                const { data: sourceDistributions, error: sourceDistError } = await serviceRoleClient
                  .from(table)
                  .delete()
                  .eq('source_user_id', userId)
                  .select('id')

                if (sourceDistError && sourceDistError.code !== '42P01') {
                  throw sourceDistError
                } else if (sourceDistributions) {
                  deletedRecords += sourceDistributions.length
                }
              } else if (table === 'admin_actions') {
                // Delete where user is the admin who performed actions
                const { data: adminActions, error: adminActionsError } = await serviceRoleClient
                  .from(table)
                  .delete()
                  .eq('admin_user_id', userId)
                  .select('id')

                if (adminActionsError && adminActionsError.code !== '42P01') {
                  throw adminActionsError
                } else if (adminActions) {
                  deletedRecords += adminActions.length
                }
              } else if (table === 'competitions') {
                // Delete competitions created by this user
                const { data: competitions, error: competitionsError } = await serviceRoleClient
                  .from(table)
                  .delete()
                  .eq('created_by', userId)
                  .select('id')

                if (competitionsError && competitionsError.code !== '42P01') {
                  throw competitionsError
                } else if (competitions) {
                  deletedRecords += competitions.length
                }
              } else {
                // Standard user_id column
                const { data: standardRecords, error: tableError } = await serviceRoleClient
                  .from(table)
                  .delete()
                  .eq('user_id', userId)
                  .select('id')

                if (tableError) {
                  if (tableError.code === '42P01') {
                    deletionSummary.tablesNotFound.push(table)
                  } else {
                    console.warn(`Warning: Could not delete from ${table}:`, tableError.message)
                    deletionSummary.errors.push(`${table}: ${tableError.message}`)
                  }
                } else if (standardRecords) {
                  deletedRecords += standardRecords.length
                }
              }

              // Track results
              if (deletedRecords > 0) {
                deletionSummary.recordsDeleted += deletedRecords
                deletionSummary.tablesWithData.push(`${table} (${deletedRecords} records)`)
              }
            } catch (err: any) {
              console.warn(`Warning: Error deleting from ${table}:`, err)
              deletionSummary.errors.push(`${table}: ${err.message}`)
              // Continue with deletion process
            }
          }

          // Step 5: Finally delete the user
          console.log('🗑️ Deleting user record...')
          const { error: userError } = await serviceRoleClient
            .from('users')
            .delete()
            .eq('id', userId)

          if (userError) throw userError

          // Step 6: Log admin action (but don't fail if audit logging fails)
          try {
            await logAdminAction(
              adminUser?.email || 'unknown',
              'DELETE_USER',
              'user',
              userId.toString(),
              {
                reason: 'Admin deletion',
                username: user.username,
                deletionSummary: deletionSummary
              }
            )
          } catch (auditError) {
            console.warn('Failed to log admin action (user still deleted):', auditError)
          }

          await loadUsers()

          // Show comprehensive deletion summary
          let summaryMessage = `✅ User "${user.username}" deleted successfully!\n\n`
          summaryMessage += `📊 DELETION SUMMARY:\n`
          summaryMessage += `• Tables processed: ${deletionSummary.tablesProcessed}\n`
          summaryMessage += `• Total records deleted: ${deletionSummary.recordsDeleted}\n\n`

          if (deletionSummary.tablesWithData.length > 0) {
            summaryMessage += `🗑️ DATA REMOVED FROM:\n`
            deletionSummary.tablesWithData.forEach(table => {
              summaryMessage += `• ${table}\n`
            })
            summaryMessage += `\n`
          }

          if (deletionSummary.tablesNotFound.length > 0) {
            summaryMessage += `ℹ️ TABLES NOT FOUND (expected):\n`
            summaryMessage += `• ${deletionSummary.tablesNotFound.join(', ')}\n\n`
          }

          if (deletionSummary.errors.length > 0) {
            summaryMessage += `⚠️ WARNINGS:\n`
            deletionSummary.errors.forEach(error => {
              summaryMessage += `• ${error}\n`
            })
          }

          summaryMessage += `\n🔗 All user connections, referrals, purchases, commissions, and related data have been permanently removed.`

          alert(summaryMessage)
        } catch (error: any) {
          console.error('Error deleting user:', error)
          alert('Failed to delete user: ' + error.message)
        }
      }
    })
  }

  const toggleUserStatus = async (userId: number, currentStatus: boolean) => {
    try {
      // Use service role client to bypass RLS policies for admin operations
      const serviceRoleClient = getServiceRoleClient()

      const { error } = await serviceRoleClient
        .from('users')
        .update({ is_active: !currentStatus })
        .eq('id', userId)

      if (error) throw error

      // Log admin action
      await logAdminAction(
        adminUser?.email || 'unknown',
        currentStatus ? 'DEACTIVATE_USER' : 'ACTIVATE_USER',
        'user',
        userId.toString(),
        { previous_status: currentStatus, new_status: !currentStatus }
      )

      await loadUsers()
    } catch (err: any) {
      console.error('Error updating user status:', err)
      alert('Failed to update user status: ' + err.message)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400"></div>
        <span className="ml-3 text-gray-300">Loading users...</span>
      </div>
    )
  }

  // Calculate summary statistics
  const totalUsers = users.length
  const activeUsers = users.filter(u => u.is_active).length
  const totalSharesAllUsers = users.reduce((sum, user) => sum + (user.total_shares || 0), 0)
  const totalInvestedAllUsers = users.reduce((sum, user) => sum + (user.total_invested || 0), 0)
  const totalCommissionsAllUsers = users.reduce((sum, user) => sum + (user.total_commissions || 0), 0)
  const totalReferralsAllUsers = users.reduce((sum, user) => sum + (user.total_referrals || 0), 0)
  const usersWithInvestments = users.filter(u => (u.total_invested || 0) > 0).length
  const usersWithReferrals = users.filter(u => (u.total_referrals || 0) > 0).length
  const usersWithKYC = users.filter(u => u.kyc_information).length
  const kycCompleted = users.filter(u => u.kyc_information?.kyc_status === 'completed').length

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-red-500">🔴 UPDATED USER MANAGEMENT 🔴</h2>
          <p className="text-gray-400 mt-1">
            Manage user accounts, permissions, and profile information
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-400">
            Total Users: {users.length} | Filtered: {filteredUsers.length}
          </div>
          <button
            onClick={() => setShowAddUserModal(true)}
            className="px-4 py-2 bg-yellow-500 text-black font-medium rounded-lg hover:bg-yellow-400 transition-colors flex items-center space-x-2"
          >
            <span>➕</span>
            <span>Add User</span>
          </button>
        </div>
      </div>

      {/* Enhanced Financial Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        <div className="glass-card p-6 border border-gray-700/50 hover:border-gray-600/50 transition-all duration-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400 uppercase tracking-wider">Total Users</p>
              <p className="text-3xl font-bold text-white mt-2">{totalUsers.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                <p className="text-sm text-green-400 font-medium">{activeUsers} active</p>
              </div>
            </div>
            <div className="text-4xl opacity-80">👥</div>
          </div>
        </div>

        <div className="glass-card p-6 border border-yellow-500/20 hover:border-yellow-500/40 transition-all duration-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400 uppercase tracking-wider">Total Shares</p>
              <p className="text-3xl font-bold text-yellow-400 mt-2">{totalSharesAllUsers.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                <div className="w-2 h-2 bg-yellow-400 rounded-full mr-2"></div>
                <p className="text-sm text-yellow-400 font-medium">{usersWithInvestments} shareholders</p>
              </div>
            </div>
            <div className="text-4xl opacity-80">📈</div>
          </div>
        </div>

        <div className="glass-card p-6 border border-green-500/20 hover:border-green-500/40 transition-all duration-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400 uppercase tracking-wider">Total Invested</p>
              <p className="text-3xl font-bold text-green-400 mt-2">${totalInvestedAllUsers.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                <p className="text-sm text-green-400 font-medium">All users combined</p>
              </div>
            </div>
            <div className="text-4xl opacity-80">💰</div>
          </div>
        </div>

        <div className="glass-card p-6 border border-blue-500/20 hover:border-blue-500/40 transition-all duration-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400 uppercase tracking-wider">Total Commissions</p>
              <p className="text-3xl font-bold text-blue-400 mt-2">${totalCommissionsAllUsers.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full mr-2"></div>
                <p className="text-sm text-blue-400 font-medium">Earned by users</p>
              </div>
            </div>
            <div className="text-4xl opacity-80">🤝</div>
          </div>
        </div>

        <div className="glass-card p-6 border border-purple-500/20 hover:border-purple-500/40 transition-all duration-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400 uppercase tracking-wider">Total Referrals</p>
              <p className="text-3xl font-bold text-purple-400 mt-2">{totalReferralsAllUsers.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                <div className="w-2 h-2 bg-purple-400 rounded-full mr-2"></div>
                <p className="text-sm text-purple-400 font-medium">{usersWithReferrals} active referrers</p>
              </div>
            </div>
            <div className="text-4xl opacity-80">👥</div>
          </div>
        </div>

        <div className="glass-card p-6 border border-orange-500/20 hover:border-orange-500/40 transition-all duration-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400 uppercase tracking-wider">KYC Status</p>
              <p className="text-3xl font-bold text-orange-400 mt-2">{kycCompleted}/{usersWithKYC}</p>
              <div className="flex items-center mt-2">
                <div className="w-2 h-2 bg-orange-400 rounded-full mr-2"></div>
                <p className="text-sm text-orange-400 font-medium">Completed/Total</p>
              </div>
            </div>
            <div className="text-4xl opacity-80">🆔</div>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
          <p className="text-red-400">❌ {error}</p>
        </div>
      )}

      {/* Bulk Actions Bar */}
      {selectedUserIds.size > 0 && (
        <div className="glass-card p-4 bg-blue-900/20 border border-blue-500/20">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-blue-400 font-medium">
                {selectedUserIds.size} users selected
              </span>
              <button
                onClick={clearSelection}
                className="text-gray-400 hover:text-white text-sm"
              >
                Clear Selection
              </button>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={handleBulkActions}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium"
              >
                🔧 Bulk Actions
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Filters and Search */}
      <div className="glass-card p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-7 gap-4">
          {/* Search */}
          <div className="md:col-span-2 xl:col-span-2">
            <label className="block text-sm font-medium text-gray-300 mb-2">
              🔍 Search Users
            </label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search by username, email, name, or sponsor..."
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-yellow-500"
            />
          </div>

          {/* Active Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              📊 Status
            </label>
            <select
              value={filterActive}
              onChange={(e) => setFilterActive(e.target.value as any)}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
            >
              <option value="all">All Users</option>
              <option value="active">Active Only</option>
              <option value="inactive">Inactive Only</option>
            </select>
          </div>

          {/* Role Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              👑 Role
            </label>
            <select
              value={filterRole}
              onChange={(e) => setFilterRole(e.target.value as any)}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
            >
              <option value="all">All Roles</option>
              <option value="user">Users Only</option>
              <option value="admin">Admins Only</option>
            </select>
          </div>

          {/* KYC Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              🆔 KYC Status
            </label>
            <select
              value={filterKYC}
              onChange={(e) => setFilterKYC(e.target.value as any)}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
            >
              <option value="all">All KYC</option>
              <option value="completed">Completed</option>
              <option value="pending">Pending</option>
              <option value="none">No KYC</option>
            </select>
          </div>

          {/* Share Purchase Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              💰 Share Purchase
            </label>
            <select
              value={filterInvestment}
              onChange={(e) => setFilterInvestment(e.target.value as any)}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
            >
              <option value="all">All Users</option>
              <option value="investors">Shareholders</option>
              <option value="non-investors">Non-Shareholders</option>
            </select>
          </div>

          {/* Referrals Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              👥 Referrals
            </label>
            <select
              value={filterReferrals}
              onChange={(e) => setFilterReferrals(e.target.value as any)}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
            >
              <option value="all">All Users</option>
              <option value="has-referrals">Has Referrals</option>
              <option value="no-referrals">No Referrals</option>
            </select>
          </div>

          {/* Activity Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              📊 Activity
            </label>
            <select
              value={filterActivity}
              onChange={(e) => setFilterActivity(e.target.value as any)}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
            >
              <option value="all">All Levels</option>
              <option value="high">High (70%+)</option>
              <option value="medium">Medium (30-70%)</option>
              <option value="low">Low (&lt;30%)</option>
            </select>
          </div>
        </div>
      </div>

      {/* Users Table - Desktop */}
      <div className="glass-card overflow-hidden hidden md:block">
        <div className="overflow-x-auto bg-gray-900/50 rounded-lg border border-gray-700">
          <table className="w-full min-w-[1400px]">
            <thead className="bg-gray-800/70 border-b border-gray-600">
              <tr>
                <th className="px-4 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider w-16">
                  <input
                    type="checkbox"
                    checked={selectedUserIds.size === filteredUsers.length && filteredUsers.length > 0}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="rounded border-gray-600 bg-gray-800 text-yellow-500 focus:ring-yellow-500"
                  />
                </th>
                <th
                  className="px-4 py-4 text-left text-sm font-semibold text-gray-200 uppercase tracking-wider w-64 cursor-pointer hover:bg-gray-700/50 transition-colors"
                  onClick={() => handleSort('username')}
                >
                  <div className="flex items-center space-x-1">
                    <span>👤 User</span>
                    <span className="text-yellow-400">{getSortIcon('username')}</span>
                  </div>
                </th>
                <th className="px-4 py-4 text-left text-sm font-semibold text-gray-200 uppercase tracking-wider w-28">
                  <div className="flex items-center space-x-1">
                    <span>📊 Status</span>
                  </div>
                </th>
                <th
                  className="px-4 py-4 text-left text-sm font-semibold text-gray-200 uppercase tracking-wider w-28 cursor-pointer hover:bg-gray-700/50 transition-colors"
                  onClick={() => handleSort('total_shares')}
                >
                  <div className="flex items-center space-x-1">
                    <span>📈 Shares</span>
                    <span className="text-yellow-400">{getSortIcon('total_shares')}</span>
                  </div>
                </th>
                <th
                  className="px-4 py-4 text-left text-sm font-semibold text-gray-200 uppercase tracking-wider w-32 cursor-pointer hover:bg-gray-700/50 transition-colors"
                  onClick={() => handleSort('total_invested')}
                >
                  <div className="flex items-center space-x-1">
                    <span>💰 Invested</span>
                    <span className="text-yellow-400">{getSortIcon('total_invested')}</span>
                  </div>
                </th>
                <th
                  className="px-4 py-4 text-left text-sm font-semibold text-gray-200 uppercase tracking-wider w-32 cursor-pointer hover:bg-gray-700/50 transition-colors"
                  onClick={() => handleSort('total_commissions')}
                >
                  <div className="flex items-center space-x-1">
                    <span>🤝 Commissions</span>
                    <span className="text-yellow-400">{getSortIcon('total_commissions')}</span>
                  </div>
                </th>
                <th
                  className="px-4 py-4 text-left text-sm font-semibold text-gray-200 uppercase tracking-wider w-28 cursor-pointer hover:bg-gray-700/50 transition-colors"
                  onClick={() => handleSort('total_referrals')}
                >
                  <div className="flex items-center space-x-1">
                    <span>👥 Referrals</span>
                    <span className="text-yellow-400">{getSortIcon('total_referrals')}</span>
                  </div>
                </th>
                <th className="px-4 py-4 text-left text-sm font-semibold text-gray-200 uppercase tracking-wider w-24">
                  <div className="flex items-center space-x-1">
                    <span>🆔 KYC</span>
                  </div>
                </th>
                <th className="px-4 py-4 text-left text-sm font-semibold text-gray-200 uppercase tracking-wider w-32">
                  <div className="flex items-center space-x-1">
                    <span>🤝 Sponsor</span>
                  </div>
                </th>
                <th
                  className="px-4 py-4 text-left text-sm font-semibold text-gray-200 uppercase tracking-wider w-28 cursor-pointer hover:bg-gray-700/50 transition-colors"
                  onClick={() => handleSort('created_at')}
                >
                  <div className="flex items-center space-x-1">
                    <span>📅 Created</span>
                    <span className="text-yellow-400">{getSortIcon('created_at')}</span>
                  </div>
                </th>
                <th className="px-4 py-4 text-left text-sm font-semibold text-gray-200 uppercase tracking-wider w-48">
                  <div className="flex items-center space-x-1">
                    <span>⚡ Actions</span>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700/50 bg-gray-900/30">
              {currentUsers.map((user) => (
                <tr key={user.id} className="hover:bg-gray-800/40 transition-all duration-200 border-b border-gray-700/30">
                  <td className="px-4 py-5 w-16">
                    <input
                      type="checkbox"
                      checked={selectedUserIds.has(user.id)}
                      onChange={(e) => handleUserSelection(user.id, e.target.checked)}
                      className="rounded border-gray-600 bg-gray-800 text-yellow-500 focus:ring-yellow-500 w-4 h-4"
                    />
                  </td>
                  <td className="px-4 py-5 w-64">
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <div className="text-base font-semibold text-white truncate max-w-[180px]">
                          {user.full_name || user.username}
                        </div>
                        {user.is_admin && (
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-500/20 text-yellow-400">
                            👑 Admin
                          </span>
                        )}
                      </div>
                      <div className="text-sm text-gray-400">@{user.username}</div>
                      <div className="text-xs text-gray-500">ID: {user.id}</div>
                      <div className="text-sm text-gray-300 truncate max-w-[200px]">{user.email}</div>
                      {user.phone && (
                        <div className="text-xs text-gray-400 truncate">{user.phone}</div>
                      )}
                      {user.telegram_users && user.telegram_users.length > 0 ? (
                        <div className="text-xs text-blue-400 truncate max-w-[200px]">
                          📱 @{user.telegram_users[0].username} ({user.telegram_users[0].first_name} {user.telegram_users[0].last_name})
                        </div>
                      ) : (
                        <div className="text-xs text-gray-500">📱 No Telegram</div>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-5 w-28">
                    <div className="flex flex-col space-y-2">
                      <span className={`inline-flex items-center px-3 py-1.5 text-sm font-medium rounded-full w-fit ${
                        user.is_active
                          ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                          : 'bg-red-500/20 text-red-400 border border-red-500/30'
                      }`}>
                        <span className="w-2 h-2 rounded-full mr-2 ${user.is_active ? 'bg-green-400' : 'bg-red-400'}"></span>
                        {user.is_active ? 'Active' : 'Inactive'}
                      </span>
                      {user.is_verified && (
                        <span className="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-blue-500/20 text-blue-400 border border-blue-500/30 w-fit">
                          ✓ Verified
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-5 w-28">
                    <div className="text-center">
                      <div className="text-lg font-bold text-yellow-400">
                        {(user.total_shares || 0).toLocaleString()}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {(() => {
                          const purchased = user.share_purchases?.reduce((sum, p) => sum + (p.shares_purchased || 0), 0) || 0;
                          const commission = user.commission_balances?.share_balance || 0;
                          if (purchased > 0 && commission > 0) {
                            return `📈 ${purchased} bought + 🎁 ${commission} earned`;
                          } else if (purchased > 0) {
                            return `📈 ${user.share_purchases?.length || 0} purchases`;
                          } else if (commission > 0) {
                            return `🎁 ${commission} earned/transferred`;
                          } else {
                            return '📈 No shares';
                          }
                        })()}
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-5 w-32">
                    <div className="text-center">
                      <div className="text-lg font-bold text-green-400">
                        ${(user.total_invested || 0).toLocaleString()}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        💰 {user.payment_transactions?.length || 0} payments
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-5 w-32">
                    <div className="text-center">
                      <div className="text-lg font-bold text-blue-400">
                        ${(user.total_commissions || 0).toLocaleString()}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        🤝 {user.commission_balances ? 'Active' : 'None'}
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-5 w-28">
                    <div className="text-center">
                      <div className="text-lg font-bold text-purple-400">
                        {(user.total_referrals || 0).toLocaleString()}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        👥 {user.referred_by ? 'Referred' : 'Direct'}
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-5 w-24">
                    <div className="text-center">
                      {user.kyc_information ? (
                        <span className={`inline-flex items-center px-3 py-1.5 text-sm font-medium rounded-full border ${
                          user.kyc_information.kyc_status === 'completed'
                            ? 'bg-green-500/20 text-green-400 border-green-500/30'
                            : user.kyc_information.kyc_status === 'pending'
                            ? 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
                            : 'bg-red-500/20 text-red-400 border-red-500/30'
                        }`}>
                          {user.kyc_information.kyc_status === 'completed' ? '✅' :
                           user.kyc_information.kyc_status === 'pending' ? '⏳' : '❌'}
                          <span className="ml-1 capitalize">{user.kyc_information.kyc_status}</span>
                        </span>
                      ) : (
                        <span className="text-sm text-gray-500 bg-gray-700/30 px-3 py-1.5 rounded-full border border-gray-600/30">
                          🆔 None
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-5 w-32">
                    <div className="text-center">
                      {user.referred_by ? (
                        <div className="space-y-1">
                          <div className="text-sm font-medium text-blue-400">
                            {user.referred_by.referrer?.username || 'Unknown'}
                          </div>
                          <div className="text-xs text-gray-500">
                            ID: {user.referred_by.referrer?.id || 'N/A'}
                          </div>
                          {user.referred_by.referrer?.full_name && (
                            <div className="text-xs text-gray-400 truncate max-w-[120px]">
                              {user.referred_by.referrer.full_name}
                            </div>
                          )}
                        </div>
                      ) : (
                        <span className="text-sm text-gray-500 bg-gray-700/30 px-3 py-1.5 rounded-full border border-gray-600/30">
                          🤝 None
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-5 text-sm text-gray-300 w-28">
                    <div className="text-center">
                      <div className="font-medium">
                        {new Date(user.created_at).toLocaleDateString()}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {new Date(user.created_at).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-5 w-48">
                    <div className="flex flex-wrap gap-2">
                      <button
                        onClick={() => handleViewReferralLinks(user)}
                        className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-lg bg-cyan-500/20 text-cyan-400 border border-cyan-500/30 hover:bg-cyan-500/30 transition-colors"
                      >
                        🔗 Links
                      </button>
                      <button
                        onClick={() => handleLoginAsUser(user)}
                        className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-lg bg-orange-500/20 text-orange-400 border border-orange-500/30 hover:bg-orange-500/30 transition-colors"
                      >
                        👤 Login As
                      </button>
                      <button
                        onClick={() => handleViewFinancials(user)}
                        className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-lg bg-yellow-500/20 text-yellow-400 border border-yellow-500/30 hover:bg-yellow-500/30 transition-colors"
                      >
                        💰 Financials
                      </button>
                      <button
                        onClick={() => handleFinancialActions(user)}
                        className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-lg bg-green-500/20 text-green-400 border border-green-500/30 hover:bg-green-500/30 transition-colors"
                      >
                        ⚙️ Manage
                      </button>
                      <button
                        onClick={() => handleCommunication(user)}
                        className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-lg bg-purple-500/20 text-purple-400 border border-purple-500/30 hover:bg-purple-500/30 transition-colors"
                      >
                        📨 Message
                      </button>
                      <button
                        onClick={() => handleEditUser(user)}
                        className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-lg bg-blue-500/20 text-blue-400 border border-blue-500/30 hover:bg-blue-500/30 transition-colors"
                      >
                        ✏️ Edit
                      </button>
                      <button
                        onClick={() => toggleUserStatus(user.id, user.is_active)}
                        className={`inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-lg border transition-colors ${
                          user.is_active
                            ? 'bg-red-500/20 text-red-400 border-red-500/30 hover:bg-red-500/30'
                            : 'bg-green-500/20 text-green-400 border-green-500/30 hover:bg-green-500/30'
                        }`}
                      >
                        {user.is_active ? '🔴 Deactivate' : '🟢 Activate'}
                      </button>
                      <button
                        onClick={() => handleDeleteUser(user.id)}
                        className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-lg bg-red-600/20 text-red-400 border border-red-600/30 hover:bg-red-600/30 transition-colors"
                      >
                        🗑️ Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Enhanced Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 bg-gray-800/30 border-t border-gray-600 flex items-center justify-between rounded-b-lg">
            <div className="text-sm text-gray-300 font-medium">
              📊 Showing <span className="text-yellow-400 font-bold">{indexOfFirstUser + 1}</span> to <span className="text-yellow-400 font-bold">{Math.min(indexOfLastUser, filteredUsers.length)}</span> of <span className="text-yellow-400 font-bold">{filteredUsers.length}</span> users
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="inline-flex items-center px-4 py-2 text-sm font-medium bg-gray-700 text-gray-300 rounded-lg border border-gray-600 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600 hover:border-gray-500 transition-colors"
              >
                ← Previous
              </button>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-400">Page</span>
                <span className="px-3 py-1 text-sm font-bold text-yellow-400 bg-gray-800 rounded-lg border border-gray-600">
                  {currentPage}
                </span>
                <span className="text-sm text-gray-400">of</span>
                <span className="px-3 py-1 text-sm font-bold text-gray-300 bg-gray-800 rounded-lg border border-gray-600">
                  {totalPages}
                </span>
              </div>
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="inline-flex items-center px-4 py-2 text-sm font-medium bg-gray-700 text-gray-300 rounded-lg border border-gray-600 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600 hover:border-gray-500 transition-colors"
              >
                Next →
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Users Cards - Mobile/Tablet */}
      <div className="md:hidden space-y-4">
        {currentUsers.map((user) => (
          <div key={user.id} className="glass-card p-4">
            <div className="flex justify-between items-start mb-3">
              <div className="flex-1">
                <h3 className="text-lg font-medium text-white">
                  {user.full_name || user.username}
                </h3>
                <p className="text-sm text-gray-400">@{user.username}</p>
                <p className="text-xs text-gray-500">ID: {user.id}</p>
              </div>
              <div className="flex flex-col space-y-1">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full w-fit ${
                  user.is_active
                    ? 'bg-green-500/20 text-green-400'
                    : 'bg-red-500/20 text-red-400'
                }`}>
                  {user.is_active ? 'Active' : 'Inactive'}
                </span>
                {user.is_admin && (
                  <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-500/20 text-yellow-400 w-fit">
                    Admin
                  </span>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-4">
              <div>
                <p className="text-xs text-gray-400 uppercase tracking-wider">Contact</p>
                <p className="text-sm text-gray-300">{user.email}</p>
                {user.phone && (
                  <p className="text-sm text-gray-400">{user.phone}</p>
                )}
              </div>

              <div>
                <p className="text-xs text-gray-400 uppercase tracking-wider">Telegram</p>
                {user.telegram_users && user.telegram_users.length > 0 ? (
                  <div className="text-sm">
                    <p className="text-blue-400">@{user.telegram_users[0].username}</p>
                    <p className="text-gray-400 text-xs">
                      {user.telegram_users[0].first_name} {user.telegram_users[0].last_name}
                    </p>
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">Not connected</p>
                )}
              </div>
            </div>

            {/* Sponsor Information */}
            <div className="mb-4 p-3 bg-blue-900/20 rounded-lg border border-blue-500/20">
              <p className="text-xs text-blue-400 uppercase tracking-wider mb-2">🤝 Sponsor</p>
              {user.referred_by ? (
                <div>
                  <p className="text-sm font-medium text-blue-300">
                    {user.referred_by.referrer?.full_name || user.referred_by.referrer?.username || 'Unknown'}
                  </p>
                  <p className="text-xs text-gray-400">
                    ID: {user.referred_by.referrer?.id || 'N/A'}
                  </p>
                </div>
              ) : (
                <p className="text-sm text-gray-500">No sponsor (Direct signup)</p>
              )}
            </div>

            {/* Financial Information */}
            <div className="grid grid-cols-2 gap-3 mb-4 p-3 bg-gray-800/30 rounded-lg">
              <div className="text-center">
                <p className="text-xs text-gray-400 uppercase tracking-wider">Shares</p>
                <p className="text-lg font-bold text-yellow-400">{(user.total_shares || 0).toLocaleString()}</p>
                <p className="text-xs text-gray-500">
                  {(() => {
                    const purchased = user.share_purchases?.reduce((sum, p) => sum + (p.shares_purchased || 0), 0) || 0;
                    const commission = user.commission_balances?.share_balance || 0;
                    if (purchased > 0 && commission > 0) {
                      return `${purchased} bought + ${commission} earned`;
                    } else if (purchased > 0) {
                      return `${user.share_purchases?.length || 0} purchases`;
                    } else if (commission > 0) {
                      return `${commission} earned/transferred`;
                    } else {
                      return 'No shares';
                    }
                  })()}
                </p>
              </div>
              <div className="text-center">
                <p className="text-xs text-gray-400 uppercase tracking-wider">Invested</p>
                <p className="text-lg font-bold text-green-400">${(user.total_invested || 0).toLocaleString()}</p>
                <p className="text-xs text-gray-500">{user.payment_transactions?.length || 0} payments</p>
              </div>
              <div className="text-center">
                <p className="text-xs text-gray-400 uppercase tracking-wider">Commissions</p>
                <p className="text-lg font-bold text-blue-400">${(user.total_commissions || 0).toLocaleString()}</p>
                <p className="text-xs text-gray-500">{user.commission_balances ? 'Active' : 'None'}</p>
              </div>
              <div className="text-center">
                <p className="text-xs text-gray-400 uppercase tracking-wider">Referrals</p>
                <p className="text-lg font-bold text-purple-400">{(user.total_referrals || 0).toLocaleString()}</p>
                <p className="text-xs text-gray-500">{user.referred_by ? 'Referred' : 'Direct'}</p>
              </div>
            </div>

            {/* Referral Information */}
            {(user.referred_by || (user.total_referrals || 0) > 0) && (
              <div className="mb-4 p-3 bg-purple-900/20 rounded-lg border border-purple-500/20">
                <p className="text-xs text-purple-400 uppercase tracking-wider mb-2">Referral Network</p>
                {user.referred_by && (
                  <div className="mb-2">
                    <p className="text-xs text-gray-400">Referred by:</p>
                    <p className="text-sm text-purple-300">
                      {user.referred_by.referrer?.full_name || user.referred_by.referrer?.username}
                    </p>
                  </div>
                )}
                {(user.total_referrals || 0) > 0 && (
                  <div>
                    <p className="text-xs text-gray-400">Has referred {user.total_referrals} users</p>
                  </div>
                )}
              </div>
            )}

            {/* KYC Information */}
            {user.kyc_information && (
              <div className="mb-4 p-3 bg-orange-900/20 rounded-lg border border-orange-500/20">
                <p className="text-xs text-orange-400 uppercase tracking-wider mb-2">KYC Status</p>
                <div className="flex justify-between items-center">
                  <div>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.kyc_information.kyc_status === 'completed'
                        ? 'bg-green-500/20 text-green-400'
                        : user.kyc_information.kyc_status === 'pending'
                        ? 'bg-yellow-500/20 text-yellow-400'
                        : 'bg-red-500/20 text-red-400'
                    }`}>
                      {user.kyc_information.kyc_status}
                    </span>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-gray-400">
                      {user.kyc_information.full_legal_name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {user.kyc_information.country_name}
                    </p>
                  </div>
                </div>
              </div>
            )}

            <div className="flex justify-between items-center pt-3 border-t border-gray-700">
              <div>
                <p className="text-xs text-gray-400">
                  Created: {new Date(user.created_at).toLocaleDateString()}
                </p>
                <p className="text-xs text-gray-500">
                  Last Activity: {new Date(user.last_activity || user.created_at).toLocaleDateString()}
                </p>
                <div className="flex items-center mt-1">
                  <span className="text-xs text-gray-400 mr-2">Activity:</span>
                  <div className="w-16 h-2 bg-gray-700 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 rounded-full"
                      style={{ width: `${user.activity_score || 0}%` }}
                    ></div>
                  </div>
                  <span className="text-xs text-gray-400 ml-2">{user.activity_score || 0}%</span>
                </div>
              </div>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => alert('TEST BUTTON WORKS!')}
                  className="bg-red-500 text-white px-6 py-3 rounded text-lg font-bold border-4 border-yellow-400 shadow-lg"
                  style={{ minWidth: '120px', minHeight: '50px' }}
                >
                  🔴 BIG TEST 🔴
                </button>
                <button
                  onClick={() => handleViewFinancials(user)}
                  className="text-yellow-400 hover:text-yellow-300 text-sm font-medium"
                >
                  💰 Financials
                </button>
                <button
                  onClick={() => handleViewReferralLinks(user)}
                  className="text-cyan-400 hover:text-cyan-300 text-sm font-medium"
                >
                  🔗 Links
                </button>
                <button
                  onClick={() => handleLoginAsUser(user)}
                  className="text-orange-400 hover:text-orange-300 text-sm font-medium"
                >
                  👤 Login As
                </button>
                <button
                  onClick={() => handleFinancialActions(user)}
                  className="text-green-400 hover:text-green-300 text-sm font-medium"
                >
                  ⚙️ Manage
                </button>
                <button
                  onClick={() => window.open(`#financial-audit?userId=${user.id}`, '_blank')}
                  className="text-yellow-400 hover:text-yellow-300 text-sm font-medium"
                  title="Generate Financial Audit Report"
                >
                  📊 Audit
                </button>
                <button
                  onClick={() => handleCommunication(user)}
                  className="text-purple-400 hover:text-purple-300 text-sm font-medium"
                >
                  📨 Message
                </button>
                <button
                  onClick={() => handleEditUser(user)}
                  className="text-blue-400 hover:text-blue-300 text-sm font-medium"
                >
                  Edit
                </button>
                <button
                  onClick={() => toggleUserStatus(user.id, user.is_active)}
                  className={`text-sm font-medium ${
                    user.is_active
                      ? 'text-red-400 hover:text-red-300'
                      : 'text-green-400 hover:text-green-300'
                  }`}
                >
                  {user.is_active ? 'Deactivate' : 'Activate'}
                </button>
                <button
                  onClick={() => handleDeleteUser(user.id)}
                  className="text-red-400 hover:text-red-300 text-sm font-medium"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        ))}

        {/* Mobile Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between pt-4">
            <div className="text-sm text-gray-400">
              Page {currentPage} of {totalPages}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Edit Modal */}
      {showEditModal && selectedUser && (
        <UserEditModal
          user={selectedUser}
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false)
            setSelectedUser(null)
          }}
          onSave={() => {
            loadUsers()
            setShowEditModal(false)
            setSelectedUser(null)
          }}
          adminUser={adminUser}
        />
      )}

      {/* Financial Modal */}
      {showFinancialModal && selectedUser && (
        <UserFinancialModal
          user={selectedUser}
          isOpen={showFinancialModal}
          onClose={() => {
            setShowFinancialModal(false)
            setSelectedUser(null)
          }}
        />
      )}

      {/* Financial Actions Modal */}
      {showFinancialActionsModal && selectedUser && (
        <FinancialActionsModal
          user={selectedUser}
          isOpen={showFinancialActionsModal}
          onClose={() => {
            setShowFinancialActionsModal(false)
            setSelectedUser(null)
          }}
          adminUser={adminUser}
          onUpdate={() => {
            loadUsers()
          }}
        />
      )}

      {/* Communication Modal */}
      {showCommunicationModal && selectedUser && (
        <CommunicationModal
          user={selectedUser}
          isOpen={showCommunicationModal}
          onClose={() => {
            setShowCommunicationModal(false)
            setSelectedUser(null)
          }}
          adminUser={adminUser}
          onUpdate={() => {
            loadUsers()
          }}
        />
      )}

      {/* Bulk Actions Modal */}
      {showBulkActionsModal && (
        <BulkActionsModal
          isOpen={showBulkActionsModal}
          onClose={() => {
            setShowBulkActionsModal(false)
          }}
          selectedUsers={selectedUsers}
          adminUser={adminUser}
          onUpdate={() => {
            loadUsers()
          }}
          onClearSelection={clearSelection}
        />
      )}

      {/* Add User Modal */}
      <AddUserModal
        isOpen={showAddUserModal}
        onClose={() => setShowAddUserModal(false)}
        onUserAdded={() => {
          loadUsers()
          setShowAddUserModal(false)
        }}
        adminUser={adminUser}
      />

      {/* Referral Links Modal */}
      {selectedUser && (
        <ReferralLinksModal
          user={selectedUser}
          isOpen={showReferralLinksModal}
          onClose={() => {
            setShowReferralLinksModal(false)
            setSelectedUser(null)
          }}
        />
      )}

      {/* User Impersonation Modal */}
      {selectedUser && (
        <UserImpersonationModal
          user={selectedUser}
          isOpen={showImpersonationModal}
          onClose={() => {
            setShowImpersonationModal(false)
            setSelectedUser(null)
          }}
          adminUser={adminUser}
        />
      )}

      {/* Confirmation Dialog */}
      <ConfirmationDialog />
    </div>
  )
}

// Export with admin authentication protection
export const UserManager = withAdminAuth(UserManagerComponent, 'canManageUsers')
