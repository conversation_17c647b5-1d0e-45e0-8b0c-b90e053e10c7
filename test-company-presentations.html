<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Company Presentations Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .presentations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        .presentation-card {
            background-color: #374151;
            border-radius: 12px;
            padding: 24px;
            text-align: center;
            transition: background-color 0.3s ease;
            border: 1px solid #4b5563;
        }
        .presentation-card:hover {
            background-color: #4b5563;
        }
        .flag {
            font-size: 48px;
            margin-bottom: 16px;
        }
        .card-title {
            font-size: 20px;
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 12px;
        }
        .card-description {
            color: #d1d5db;
            font-size: 14px;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        .download-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background-color: #2563eb;
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.3s ease;
        }
        .download-btn:hover {
            background-color: #1d4ed8;
        }
        .test-section {
            background-color: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #444;
        }
        .test-result {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .test-success {
            background-color: #1a4a1a;
            border: 1px solid #4a8a4a;
            color: #8fff8f;
        }
        .test-pending {
            background-color: #4a4a1a;
            border: 1px solid #8a8a4a;
            color: #ffff8f;
        }
        .url-display {
            font-family: monospace;
            background-color: #1a1a1a;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            word-break: break-all;
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📋 Company Presentations Test</h1>
        <p>Testing the updated Company Presentation page with multilingual downloads</p>
    </div>

    <div class="test-section">
        <h2>🧪 Link Verification Test</h2>
        <div id="test-results">
            <div class="test-pending">⏳ Testing presentation links...</div>
        </div>
    </div>

    <div class="presentations-grid">
        <div class="presentation-card">
            <div class="flag">🇬🇧</div>
            <h3 class="card-title">English Presentation</h3>
            <p class="card-description">Complete company overview and mining operations details</p>
            <div class="url-display">
                https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/marketing-materials/Aureus%20Presentation%20Plan%20-%20Eng.pdf
            </div>
            <a
                href="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/marketing-materials/Aureus%20Presentation%20Plan%20-%20Eng.pdf"
                target="_blank"
                rel="noopener noreferrer"
                class="download-btn"
                data-lang="English"
            >
                📄 Download PDF
            </a>
        </div>

        <div class="presentation-card">
            <div class="flag">🇮🇳</div>
            <h3 class="card-title">Hindi Presentation</h3>
            <p class="card-description">हिंदी में कंपनी की पूरी जानकारी और खनन संचालन विवरण</p>
            <div class="url-display">
                https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/marketing-materials/Aureus%20Presentation%20Plan%20-%20Hindi.pdf
            </div>
            <a
                href="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/marketing-materials/Aureus%20Presentation%20Plan%20-%20Hindi.pdf"
                target="_blank"
                rel="noopener noreferrer"
                class="download-btn"
                data-lang="Hindi"
            >
                📄 Download PDF
            </a>
        </div>

        <div class="presentation-card">
            <div class="flag">🇮🇩</div>
            <h3 class="card-title">Indonesian Presentation</h3>
            <p class="card-description">Gambaran lengkap perusahaan dan detail operasi pertambangan</p>
            <div class="url-display">
                https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/marketing-materials/Aureus%20Presentation%20Plan%20-%20Indonesian.pdf
            </div>
            <a
                href="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/marketing-materials/Aureus%20Presentation%20Plan%20-%20Indonesian.pdf"
                target="_blank"
                rel="noopener noreferrer"
                class="download-btn"
                data-lang="Indonesian"
            >
                📄 Download PDF
            </a>
        </div>
    </div>

    <div class="test-section">
        <h2>📋 Implementation Details</h2>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
                <h4 style="color: #8fff8f;">✅ Features Implemented</h4>
                <ul style="color: #8fff8f;">
                    <li>3-column responsive grid layout</li>
                    <li>Multilingual presentation downloads</li>
                    <li>Flag emojis for language identification</li>
                    <li>Hover effects on cards and buttons</li>
                    <li>External links opening in new tabs</li>
                    <li>Professional styling matching dashboard</li>
                    <li>Mobile-responsive design</li>
                </ul>
            </div>
            <div>
                <h4 style="color: #8fff8f;">🎯 Languages Supported</h4>
                <ul style="color: #8fff8f;">
                    <li>🇬🇧 English - International users</li>
                    <li>🇮🇳 Hindi - Indian market</li>
                    <li>🇮🇩 Indonesian - Southeast Asian market</li>
                </ul>
                <h4 style="color: #8fff8f;">📱 Responsive Breakpoints</h4>
                <ul style="color: #8fff8f;">
                    <li>Desktop: 3-column grid</li>
                    <li>Tablet: 2-column grid</li>
                    <li>Mobile: Single column stack</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Test presentation links
        async function testPresentationLinks() {
            const testResults = document.getElementById('test-results');
            const links = [
                {
                    name: 'English Presentation',
                    url: 'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/marketing-materials/Aureus%20Presentation%20Plan%20-%20Eng.pdf'
                },
                {
                    name: 'Hindi Presentation',
                    url: 'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/marketing-materials/Aureus%20Presentation%20Plan%20-%20Hindi.pdf'
                },
                {
                    name: 'Indonesian Presentation',
                    url: 'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/marketing-materials/Aureus%20Presentation%20Plan%20-%20Indonesian.pdf'
                }
            ];

            testResults.innerHTML = '<div class="test-pending">⏳ Testing presentation links...</div>';

            let results = [];
            
            for (const link of links) {
                try {
                    const response = await fetch(link.url, { method: 'HEAD' });
                    if (response.ok) {
                        results.push(`<div class="test-success">✅ ${link.name}: Link accessible (${response.status})</div>`);
                    } else {
                        results.push(`<div class="test-result" style="background-color: #4a1a1a; border: 1px solid #8a4a4a; color: #ff8f8f;">❌ ${link.name}: HTTP ${response.status}</div>`);
                    }
                } catch (error) {
                    results.push(`<div class="test-result" style="background-color: #4a1a1a; border: 1px solid #8a4a4a; color: #ff8f8f;">❌ ${link.name}: ${error.message}</div>`);
                }
            }

            testResults.innerHTML = results.join('');
        }

        // Add click tracking
        document.querySelectorAll('.download-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const lang = this.getAttribute('data-lang');
                console.log(`📄 ${lang} presentation download clicked`);
            });
        });

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📋 Company Presentations Test Page Loaded');
            console.log('🔧 Version: 3.3.5');
            console.log('✅ Features: Multilingual presentation downloads');
            
            // Test links after a short delay
            setTimeout(testPresentationLinks, 1000);
        });
    </script>
</body>
</html>
