#!/usr/bin/env node

/**
 * Investigate User 87 (<PERSON><PERSON>)
 * 
 * This script investigates the user ID 87 who appeared in the
 * missing commission report with $281.25 missing.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ Missing SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function investigateUser87() {
  try {
    console.log('🔍 Investigating User 87 (<PERSON><PERSON> Case)\n');
    
    // Find User 87
    const { data: user87, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', 87)
      .single();
    
    if (userError) {
      console.error('❌ User 87 not found:', userError);
      return;
    }
    
    console.log(`👤 User 87 Details:`);
    console.log(`   ID: ${user87.id}`);
    console.log(`   Username: ${user87.username}`);
    console.log(`   Full Name: ${user87.full_name}`);
    console.log(`   Email: ${user87.email}`);
    console.log(`   Created: ${user87.created_at}`);
    console.log(`   Active: ${user87.is_active}`);
    
    // Get ALL share purchases for User 87
    const { data: purchases, error: purchaseError } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .eq('user_id', 87)
      .order('created_at', { ascending: false });
    
    if (purchaseError) {
      console.error('❌ Error fetching User 87 purchases:', purchaseError);
      return;
    }
    
    console.log(`\n📈 User 87's Share Purchases (${purchases.length} total):`);
    purchases.forEach((purchase, index) => {
      console.log(`   ${index + 1}. Purchase ID: ${purchase.id}`);
      console.log(`      Shares: ${purchase.shares_purchased}`);
      console.log(`      Amount: $${purchase.total_amount}`);
      console.log(`      Price per share: $${purchase.price_per_share || 'N/A'}`);
      console.log(`      Status: ${purchase.status}`);
      console.log(`      Created: ${purchase.created_at}`);
      console.log(`      Payment Method: ${purchase.payment_method || 'N/A'}`);
      console.log('');
    });
    
    // Get referral relationships
    const { data: referrals, error: refError } = await supabase
      .from('referrals')
      .select(`
        *,
        referrer:users!referrer_id(id, username, full_name)
      `)
      .eq('referred_id', 87);
    
    if (refError) {
      console.error('❌ Error fetching User 87 referrals:', refError);
    } else {
      console.log(`🔗 User 87's Referral Relationships (${referrals.length} total):`);
      referrals.forEach((ref, index) => {
        console.log(`   ${index + 1}. Referrer: ${ref.referrer?.full_name || ref.referrer?.username} (ID: ${ref.referrer_id})`);
        console.log(`      Status: ${ref.status}, Rate: ${ref.commission_rate}%`);
        console.log(`      Created: ${ref.created_at}`);
        console.log('');
      });
    }
    
    // Get ALL commission transactions for User 87
    const { data: commissions, error: commError } = await supabase
      .from('commission_transactions')
      .select(`
        *,
        referrer:users!referrer_id(id, username, full_name)
      `)
      .eq('referred_id', 87)
      .order('payment_date', { ascending: false });
    
    if (commError) {
      console.error('❌ Error fetching User 87 commissions:', commError);
    } else {
      console.log(`💸 Commission Transactions for User 87 (${commissions.length} total):`);
      if (commissions.length === 0) {
        console.log('   NO commission transactions found!');
      } else {
        commissions.forEach((comm, index) => {
          console.log(`   ${index + 1}. To: ${comm.referrer?.full_name || comm.referrer?.username} (ID: ${comm.referrer_id})`);
          console.log(`      USDT: $${comm.usdt_commission}, Shares: ${comm.share_commission}`);
          console.log(`      Purchase Amount: $${comm.share_purchase_amount}`);
          console.log(`      Status: ${comm.status}`);
          console.log(`      Date: ${comm.payment_date}`);
          console.log('');
        });
      }
    }
    
    // Get payment transactions
    const { data: payments, error: payError } = await supabase
      .from('crypto_payment_transactions')
      .select('*')
      .eq('user_id', 87)
      .order('created_at', { ascending: false });
    
    if (payError) {
      console.error('❌ Error fetching User 87 payments:', payError);
    } else {
      console.log(`💳 Payment Transactions for User 87 (${payments.length} total):`);
      if (payments.length === 0) {
        console.log('   NO payment transactions found!');
      } else {
        payments.forEach((payment, index) => {
          console.log(`   ${index + 1}. Amount: $${payment.amount}, Status: ${payment.status}`);
          console.log(`      Network: ${payment.network}, Currency: ${payment.currency}`);
          console.log(`      Created: ${payment.created_at}`);
          console.log(`      Approved: ${payment.approved_at || 'Not approved'}`);
          console.log('');
        });
      }
    }
    
    // Detailed Analysis
    console.log('🔍 Detailed Analysis:');
    
    const positivePurchases = purchases.filter(p => p.total_amount > 0);
    const totalPurchaseAmount = positivePurchases.reduce((sum, p) => sum + p.total_amount, 0);
    const totalShares = positivePurchases.reduce((sum, p) => sum + p.shares_purchased, 0);
    const totalCommissionCoverage = commissions.reduce((sum, c) => sum + (c.share_purchase_amount || 0), 0);
    
    console.log(`   Positive Purchases: ${positivePurchases.length}`);
    console.log(`   Total Purchase Amount: $${totalPurchaseAmount}`);
    console.log(`   Total Shares: ${totalShares}`);
    console.log(`   Commission Coverage: $${totalCommissionCoverage}`);
    console.log(`   Missing Coverage: $${totalPurchaseAmount - totalCommissionCoverage}`);
    
    if (totalPurchaseAmount - totalCommissionCoverage > 0) {
      const expectedUSDTCommission = (totalPurchaseAmount - totalCommissionCoverage) * 0.15;
      const expectedShareCommission = (totalShares - (totalCommissionCoverage / 2.5)) * 0.15; // Assuming $2.5 per share average
      
      console.log(`\n⚠️  Missing Commission Analysis:`);
      console.log(`   Expected USDT Commission (15%): $${expectedUSDTCommission.toFixed(2)}`);
      console.log(`   Expected Share Commission (15%): ${expectedShareCommission.toFixed(2)}`);
      
      if (Math.abs(expectedUSDTCommission - 281.25) < 1) {
        console.log(`   ✅ This matches the $281.25 from the backfill report!`);
      }
    }
    
    // Check if this is a legitimate case
    console.log(`\n🤔 Is this a legitimate missing commission case?`);
    
    if (positivePurchases.length > 0 && commissions.length === 0 && referrals.length > 0) {
      console.log(`   ✅ YES - User has purchases, has sponsor, but NO commissions distributed`);
      console.log(`   This appears to be a legitimate missing commission case.`);
    } else if (positivePurchases.length === 0) {
      console.log(`   ❌ NO - User has no positive share purchases`);
    } else if (referrals.length === 0) {
      console.log(`   ❌ NO - User has no sponsor assigned`);
    } else if (commissions.length > 0) {
      console.log(`   ❌ NO - User already has commission transactions`);
    } else {
      console.log(`   🤷 UNCLEAR - Need manual review`);
    }
    
  } catch (error) {
    console.error('❌ Investigation failed:', error);
  }
}

investigateUser87();
