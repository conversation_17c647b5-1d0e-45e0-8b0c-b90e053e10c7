/**
 * SEND VERIFICATION CODE API
 *
 * Handles sending 6-digit PIN verification codes for various purposes:
 * - Registration verification
 * - Password reset
 * - Telegram account connection
 * - Account updates
 */

import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import dotenv from 'dotenv';
import { backendResendEmailService } from '../../lib/backend/resendEmailService.js';

// Load environment variables
dotenv.config();

// Initialize Supabase client with service role
const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Email service is now handled by backendResendEmailService

// Configuration
const VERIFICATION_CODE_LENGTH = 6;
const VERIFICATION_EXPIRY_MINUTES = 15;
const MAX_VERIFICATION_ATTEMPTS = 3;

// Helper functions
const generateSecureCode = () => {
  return crypto.randomInt(100000, 999999).toString();
};

const hashCode = async (code) => {
  const saltRounds = 12;
  return await bcrypt.hash(code, saltRounds);
};

const generateVerificationEmailContent = (code, purpose, userName, expiryMinutes = 15) => {
  const greeting = userName ? `Hello ${userName}` : 'Hello';
  const purposeText = {
    registration: 'complete your account registration',
    account_update: 'confirm your account changes',
    withdrawal: 'authorize your withdrawal request',
    password_reset: 'reset your password',
    telegram_connection: 'connect your Telegram account'
  }[purpose] || 'verify your email';

  const subject = `Your Aureus Alliance verification code: ${code}`;

  const html = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <title>Email Verification</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #D4AF37;">Aureus Alliance Holdings</h1>
          </div>

          <h2>Email Verification Required</h2>

          <p>${greeting},</p>

          <p>You need to verify your email address to ${purposeText}.</p>

          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; margin: 20px 0;">
            <h3 style="margin: 0; color: #D4AF37;">Your Verification Code</h3>
            <div style="font-size: 32px; font-weight: bold; letter-spacing: 8px; margin: 15px 0; color: #333;">
              ${code}
            </div>
            <p style="margin: 0; color: #666; font-size: 14px;">
              This code expires in ${expiryMinutes} minutes
            </p>
          </div>

          <p><strong>Security Notice:</strong></p>
          <ul>
            <li>Never share this code with anyone</li>
            <li>Aureus Alliance will never ask for this code via phone or email</li>
            <li>If you didn't request this verification, please ignore this email</li>
          </ul>

          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">

          <p style="font-size: 12px; color: #666; text-align: center;">
            This email was sent by Aureus Alliance Holdings. If you have questions,
            please contact our support team.
          </p>
        </div>
      </body>
    </html>
  `;

  const text = `
${greeting},

You need to verify your email address to ${purposeText}.

Your verification code: ${code}

This code expires in ${expiryMinutes} minutes.

Security Notice:
- Never share this code with anyone
- Aureus Alliance will never ask for this code via phone or email
- If you didn't request this verification, please ignore this email

This email was sent by Aureus Alliance Holdings.
  `.trim();

  return { subject, html, text };
};

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { userId, email, purpose, ipAddress, userAgent } = req.body;

    // Validate required fields
    if (!userId || !email || !purpose) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: userId, email, and purpose are required'
      });
    }

    // Validate purpose
    const validPurposes = ['registration', 'password_reset', 'telegram_connection', 'account_update', 'withdrawal'];
    if (!validPurposes.includes(purpose)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid purpose. Must be one of: ' + validPurposes.join(', ')
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid email format'
      });
    }

    console.log(`📧 Sending verification code for ${purpose} to ${email} (User ID: ${userId})`);

    // Generate 6-digit code
    const code = generateSecureCode();
    const codeHash = await hashCode(code);

    // Calculate expiry time
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + VERIFICATION_EXPIRY_MINUTES);

    // Store verification code in database
    const { data: verificationData, error: dbError } = await supabase
      .from('email_verification_codes')
      .insert({
        user_id: parseInt(userId),
        email: email,
        code_hash: codeHash,
        purpose: purpose,
        expires_at: expiresAt.toISOString(),
        attempts: 0
      })
      .select()
      .single();

    if (dbError) {
      console.error('❌ Error storing verification code:', dbError);
      console.error('❌ Full error details:', JSON.stringify(dbError, null, 2));
      console.error('❌ Insert data was:', {
        user_id: parseInt(userId),
        email: email,
        code_hash: codeHash,
        purpose: purpose,
        expires_at: expiresAt.toISOString(),
        attempts: 0
      });
      return res.status(500).json({
        success: false,
        message: 'Failed to generate verification code',
        error: dbError.message || 'Database error'
      });
    }

    // Get user name for personalized email
    const { data: userData } = await supabase
      .from('users')
      .select('full_name, username')
      .eq('id', parseInt(userId))
      .single();

    const userName = userData?.full_name || userData?.username;

    // Send email via backend Resend service
    const emailResult = await backendResendEmailService.sendVerificationCode({
      email,
      code,
      purpose,
      userName,
      expiryMinutes: VERIFICATION_EXPIRY_MINUTES
    });

    if (!emailResult.success) {
      console.error('❌ Backend email service error:', emailResult.error);
      // Clean up database record if email failed
      await supabase
        .from('email_verification_codes')
        .delete()
        .eq('id', verificationData.id);

      return res.status(500).json({
        success: false,
        message: 'Failed to send verification email',
        error: emailResult.error
      });
    }

    console.log(`✅ Verification code sent successfully for ${purpose}`);
    return res.status(200).json({
      success: true,
      message: 'Verification code sent successfully',
      expiryMinutes: VERIFICATION_EXPIRY_MINUTES
    });

  } catch (error) {
    console.error('❌ Send verification code error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
}
