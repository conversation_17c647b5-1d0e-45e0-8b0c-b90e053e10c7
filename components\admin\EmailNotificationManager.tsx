import React, { useState, useEffect } from 'react';
import { getServiceRoleClient } from '../../lib/supabase';
import { EmailProcessingService } from '../../lib/services/emailProcessingService';

interface NotificationStats {
  pending: number;
  sent: number;
  failed: number;
  total: number;
}

interface EmailNotification {
  id: string;
  user_id: number;
  notification_type: string;
  email_address: string;
  subject: string;
  status: string;
  sent_at: string | null;
  error_message: string | null;
  retry_count: number;
  created_at: string;
  updated_at: string;
}

export const EmailNotificationManager: React.FC = () => {
  const [stats, setStats] = useState<NotificationStats>({ pending: 0, sent: 0, failed: 0, total: 0 });
  const [notifications, setNotifications] = useState<EmailNotification[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'pending' | 'failed' | 'history'>('overview');
  const [processing, setProcessing] = useState(false);

  const emailProcessingService = EmailProcessingService.getInstance();

  useEffect(() => {
    loadData();
    // Auto-refresh every 30 seconds
    const interval = setInterval(loadData, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Load stats
      const statsData = await emailProcessingService.getNotificationStats();
      setStats(statsData);

      // Load notifications based on active tab
      await loadNotifications();
    } catch (error) {
      console.error('Error loading email notification data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadNotifications = async () => {
    try {
      const serviceClient = getServiceRoleClient();
      let query = serviceClient
        .from('email_notifications')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(100);

      if (activeTab === 'pending') {
        query = query.eq('status', 'pending');
      } else if (activeTab === 'failed') {
        query = query.eq('status', 'failed');
      }

      const { data, error } = await query;
      
      if (error) throw error;
      setNotifications(data || []);
    } catch (error) {
      console.error('Error loading notifications:', error);
    }
  };

  useEffect(() => {
    if (!loading) {
      loadNotifications();
    }
  }, [activeTab]);

  const handleProcessPending = async () => {
    try {
      setProcessing(true);
      await emailProcessingService.triggerImmediateProcessing();
      await loadData();
    } catch (error) {
      console.error('Error processing pending notifications:', error);
    } finally {
      setProcessing(false);
    }
  };

  const handleRetryFailed = async () => {
    try {
      setProcessing(true);
      await emailProcessingService.retryFailedNotifications();
      await loadData();
    } catch (error) {
      console.error('Error retrying failed notifications:', error);
    } finally {
      setProcessing(false);
    }
  };

  const handleCleanupOld = async () => {
    if (!confirm('Are you sure you want to delete old email notifications (older than 30 days)?')) {
      return;
    }

    try {
      setProcessing(true);
      await emailProcessingService.cleanupOldNotifications();
      await loadData();
    } catch (error) {
      console.error('Error cleaning up old notifications:', error);
    } finally {
      setProcessing(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent': return 'text-green-400';
      case 'failed': return 'text-red-400';
      case 'pending': return 'text-yellow-400';
      case 'processing': return 'text-blue-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent': return '✅';
      case 'failed': return '❌';
      case 'pending': return '⏳';
      case 'processing': return '🔄';
      default: return '❓';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getNotificationTypeIcon = (type: string) => {
    switch (type) {
      case 'message': return '💬';
      case 'commission': return '💰';
      case 'referral': return '🎉';
      case 'share_transfer': return '📊';
      case 'share_purchase': return '✅';
      default: return '📧';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-3 text-gray-400">Loading email notifications...</span>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-white text-2xl font-bold">📧 Email Notification Manager</h2>
          <p className="text-gray-400">Monitor and manage automated email notifications</p>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={handleProcessPending}
            disabled={processing}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded disabled:opacity-50"
          >
            {processing ? '🔄 Processing...' : '⚡ Process Pending'}
          </button>
          <button
            onClick={handleRetryFailed}
            disabled={processing}
            className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded disabled:opacity-50"
          >
            🔄 Retry Failed
          </button>
          <button
            onClick={handleCleanupOld}
            disabled={processing}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded disabled:opacity-50"
          >
            🗑️ Cleanup Old
          </button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Notifications</p>
              <p className="text-white text-2xl font-bold">{stats.total}</p>
            </div>
            <div className="text-3xl">📧</div>
          </div>
        </div>
        <div className="bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Pending</p>
              <p className="text-yellow-400 text-2xl font-bold">{stats.pending}</p>
            </div>
            <div className="text-3xl">⏳</div>
          </div>
        </div>
        <div className="bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Sent</p>
              <p className="text-green-400 text-2xl font-bold">{stats.sent}</p>
            </div>
            <div className="text-3xl">✅</div>
          </div>
        </div>
        <div className="bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Failed</p>
              <p className="text-red-400 text-2xl font-bold">{stats.failed}</p>
            </div>
            <div className="text-3xl">❌</div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex bg-gray-700 rounded-lg p-1 mb-6">
        {[
          { id: 'overview', label: 'Overview', icon: '📊' },
          { id: 'pending', label: `Pending (${stats.pending})`, icon: '⏳' },
          { id: 'failed', label: `Failed (${stats.failed})`, icon: '❌' },
          { id: 'history', label: 'History', icon: '📜' }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex-1 px-4 py-2 rounded text-sm font-medium transition-colors ${
              activeTab === tab.id ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'
            }`}
          >
            {tab.icon} {tab.label}
          </button>
        ))}
      </div>

      {/* Content */}
      {activeTab === 'overview' ? (
        <div className="space-y-4">
          <div className="bg-gray-700 rounded-lg p-4">
            <h3 className="text-white text-lg font-semibold mb-2">📈 System Status</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-gray-400 text-sm">Success Rate</p>
                <p className="text-white text-xl">
                  {stats.total > 0 ? ((stats.sent / stats.total) * 100).toFixed(1) : 0}%
                </p>
              </div>
              <div>
                <p className="text-gray-400 text-sm">Processing Status</p>
                <p className="text-white text-xl">
                  {stats.pending > 0 ? '🔄 Active' : '✅ Idle'}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-700 rounded-lg p-4">
            <h3 className="text-white text-lg font-semibold mb-2">🔧 Quick Actions</h3>
            <div className="space-y-2">
              <p className="text-gray-400 text-sm">
                • Process Pending: Immediately process all pending notifications
              </p>
              <p className="text-gray-400 text-sm">
                • Retry Failed: Retry failed notifications that haven't exceeded retry limit
              </p>
              <p className="text-gray-400 text-sm">
                • Cleanup Old: Remove notifications older than 30 days
              </p>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-gray-700 rounded-lg">
          {notifications.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-4xl mb-4">📭</div>
              <h3 className="text-white text-lg mb-2">No Notifications</h3>
              <p className="text-gray-400">
                {activeTab === 'pending' ? 'No pending notifications to process.' :
                 activeTab === 'failed' ? 'No failed notifications found.' :
                 'No notification history available.'}
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-600">
              {notifications.map((notification) => (
                <div key={notification.id} className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="text-xl">
                          {getNotificationTypeIcon(notification.notification_type)}
                        </span>
                        <span className="text-white font-medium">
                          {notification.notification_type.replace('_', ' ').toUpperCase()}
                        </span>
                        <span className={`text-sm ${getStatusColor(notification.status)}`}>
                          {getStatusIcon(notification.status)} {notification.status.toUpperCase()}
                        </span>
                      </div>
                      <p className="text-gray-300 text-sm mb-1">{notification.subject}</p>
                      <p className="text-gray-400 text-xs">
                        To: {notification.email_address} | User ID: {notification.user_id}
                      </p>
                      <p className="text-gray-400 text-xs">
                        Created: {formatDate(notification.created_at)}
                        {notification.sent_at && ` | Sent: ${formatDate(notification.sent_at)}`}
                      </p>
                      {notification.error_message && (
                        <p className="text-red-400 text-xs mt-1">
                          Error: {notification.error_message}
                        </p>
                      )}
                    </div>
                    <div className="text-right">
                      <p className="text-gray-400 text-xs">
                        Retries: {notification.retry_count}/3
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
