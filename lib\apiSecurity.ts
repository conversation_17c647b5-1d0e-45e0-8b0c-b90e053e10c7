/**
 * API SECURITY MIDDLEWARE
 * 
 * This module provides comprehensive API security including
 * authorization, rate limiting, input validation, and error handling.
 */

import { authRateLimiter } from './rateLimiting';
import { validateRequest } from './inputValidation';
import { supabase } from './supabase';

interface ApiRequest extends Request {
  user?: any;
  session?: any;
  rateLimiter?: any;
  rateLimitIdentifier?: string;
  isBotRequest?: boolean;
  validatedBody?: any;
  securityContext?: any;
}

interface ApiResponse extends Response {
  setHeader: (name: string, value: string | number) => void;
  status: (code: number) => ApiResponse;
  json: (data: any) => ApiResponse;
}

interface SecurityConfig {
  requireAuth: boolean;
  requireAdmin: boolean;
  allowBot: boolean;
  rateLimit: boolean;
  validateInput: boolean;
  logRequests: boolean;
}

/**
 * Create secure API middleware with configurable security features
 */
export const createSecureApiMiddleware = (config: Partial<SecurityConfig> = {}) => {
  const securityConfig: SecurityConfig = {
    requireAuth: true,
    requireAdmin: false,
    allowBot: true,
    rateLimit: true,
    validateInput: true,
    logRequests: true,
    ...config
  };

  return async (req: ApiRequest, res: ApiResponse, next: () => void) => {
    try {
      console.log(`🔐 Securing API request: ${req.method} ${req.url}`);

      // 1. Identify request type and client
      const clientInfo = await identifyClient(req);
      req.isBotRequest = clientInfo.isBot;
      req.securityContext = clientInfo;

      // 2. Apply rate limiting (if enabled)
      if (securityConfig.rateLimit) {
        const rateLimitResult = await applyRateLimit(req, res, clientInfo);
        if (!rateLimitResult.allowed) {
          return; // Response already sent
        }
      }

      // 3. Validate authentication (if required)
      if (securityConfig.requireAuth) {
        const authResult = await validateAuthentication(req, res, clientInfo);
        if (!authResult.authenticated) {
          return; // Response already sent
        }
        req.user = authResult.user;
      }

      // 4. Validate admin permissions (if required)
      if (securityConfig.requireAdmin) {
        const adminResult = await validateAdminPermissions(req, res);
        if (!adminResult.authorized) {
          return; // Response already sent
        }
      }

      // 5. Validate input (if enabled)
      if (securityConfig.validateInput && req.body) {
        const inputResult = await validateApiInput(req, res);
        if (!inputResult.valid) {
          return; // Response already sent
        }
      }

      // 6. Add security headers
      addSecurityHeaders(res);

      // 7. Log request (if enabled)
      if (securityConfig.logRequests) {
        await logApiRequest(req, clientInfo);
      }

      console.log(`✅ API security checks passed for ${req.method} ${req.url}`);
      next();

    } catch (error) {
      console.error('❌ API security middleware error:', error);
      return res.status(500).json({ 
        error: 'Security validation failed',
        message: 'An internal security error occurred'
      });
    }
  };
};

/**
 * Identify client type and gather security context
 */
async function identifyClient(req: ApiRequest): Promise<{
  isBot: boolean;
  isAdmin: boolean;
  ipAddress: string;
  userAgent: string;
  identifier: string;
}> {
  const userAgent = req.headers.get('user-agent') || 'unknown';
  const ipAddress = req.headers.get('x-forwarded-for')?.split(',')[0] || 
                   req.headers.get('x-real-ip') || 
                   'unknown';

  // Check if this is a bot request
  const isBot = req.headers.get('authorization')?.includes('service_role') ||
               userAgent.includes('aureus-bot') ||
               req.headers.get('x-bot-request') === 'true';

  // Check if user is admin (will be determined later in auth validation)
  const isAdmin = false; // Will be updated after auth validation

  const identifier = isBot ? 'bot:service' : `ip:${ipAddress}`;

  return {
    isBot,
    isAdmin,
    ipAddress,
    userAgent,
    identifier
  };
}

/**
 * Apply rate limiting based on client type
 */
async function applyRateLimit(
  req: ApiRequest, 
  res: ApiResponse, 
  clientInfo: any
): Promise<{ allowed: boolean }> {
  try {
    const rateLimitResult = authRateLimiter.checkRateLimit(
      clientInfo.identifier,
      clientInfo.isBot
    );

    // Set rate limit headers
    res.setHeader('X-RateLimit-Limit', '100'); // API rate limit
    res.setHeader('X-RateLimit-Remaining', rateLimitResult.remaining.toString());
    res.setHeader('X-RateLimit-Reset', new Date(rateLimitResult.resetTime).toISOString());

    if (!rateLimitResult.allowed) {
      console.log(`🚫 API rate limit exceeded for ${clientInfo.identifier}`);
      
      res.setHeader('Retry-After', rateLimitResult.retryAfter?.toString() || '60');
      res.status(429).json({
        error: 'Rate limit exceeded',
        message: 'Too many API requests. Please slow down.',
        retryAfter: rateLimitResult.retryAfter
      });

      return { allowed: false };
    }

    return { allowed: true };

  } catch (error) {
    console.error('❌ Rate limiting error:', error);
    return { allowed: true }; // Allow on error to prevent blocking
  }
}

/**
 * Validate authentication
 */
async function validateAuthentication(
  req: ApiRequest, 
  res: ApiResponse, 
  clientInfo: any
): Promise<{ authenticated: boolean; user?: any }> {
  try {
    // Bot requests with service role are always authenticated
    if (clientInfo.isBot) {
      console.log('✅ Bot request authenticated via service role');
      return { 
        authenticated: true, 
        user: { id: 0, email: 'bot@system', isBot: true } 
      };
    }

    // Check for session or JWT token
    const authHeader = req.headers.get('authorization');
    const sessionId = req.headers.get('x-session-id');

    if (authHeader && authHeader.startsWith('Bearer ')) {
      // JWT token authentication
      const token = authHeader.substring(7);
      const { data: { user }, error } = await supabase.auth.getUser(token);

      if (error || !user) {
        res.status(401).json({
          error: 'Invalid authentication token',
          message: 'Please log in again'
        });
        return { authenticated: false };
      }

      return { authenticated: true, user };

    } else if (sessionId) {
      // Session-based authentication
      const { sessionSecurity } = await import('./sessionSecurity');
      const sessionValidation = await sessionSecurity.validateSession(sessionId);

      if (!sessionValidation.valid) {
        res.status(401).json({
          error: 'Invalid or expired session',
          message: 'Please log in again'
        });
        return { authenticated: false };
      }

      return { 
        authenticated: true, 
        user: { 
          id: sessionValidation.sessionData?.userId,
          email: sessionValidation.sessionData?.userEmail 
        }
      };

    } else {
      res.status(401).json({
        error: 'Authentication required',
        message: 'Please provide valid authentication'
      });
      return { authenticated: false };
    }

  } catch (error) {
    console.error('❌ Authentication validation error:', error);
    res.status(500).json({
      error: 'Authentication validation failed',
      message: 'Internal authentication error'
    });
    return { authenticated: false };
  }
}

/**
 * Validate admin permissions
 */
async function validateAdminPermissions(
  req: ApiRequest, 
  res: ApiResponse
): Promise<{ authorized: boolean }> {
  try {
    // Bot requests are always authorized for admin operations
    if (req.isBotRequest) {
      console.log('✅ Bot request authorized for admin operations');
      return { authorized: true };
    }

    if (!req.user) {
      res.status(401).json({
        error: 'Authentication required',
        message: 'Admin access requires authentication'
      });
      return { authorized: false };
    }

    // Check if user is admin
    const { data: adminUser, error } = await supabase
      .from('admin_users')
      .select('id, email, role, is_active')
      .eq('user_id', req.user.id)
      .eq('is_active', true)
      .single();

    if (error || !adminUser) {
      res.status(403).json({
        error: 'Admin access required',
        message: 'You do not have admin permissions'
      });
      return { authorized: false };
    }

    // Add admin info to request
    req.user.adminRole = adminUser.role;
    req.user.isAdmin = true;

    return { authorized: true };

  } catch (error) {
    console.error('❌ Admin validation error:', error);
    res.status(500).json({
      error: 'Admin validation failed',
      message: 'Internal authorization error'
    });
    return { authorized: false };
  }
}

/**
 * Validate API input
 */
async function validateApiInput(
  req: ApiRequest, 
  res: ApiResponse
): Promise<{ valid: boolean }> {
  try {
    const { securityCheckInput } = await import('./inputValidation');
    
    const securityCheck = securityCheckInput(req.body);
    
    if (!securityCheck.safe) {
      console.log(`🚫 Malicious input detected: ${securityCheck.issues.join(', ')}`);
      
      // Log security incident
      await logSecurityIncident('MALICIOUS_INPUT', req, {
        issues: securityCheck.issues,
        input: JSON.stringify(req.body).substring(0, 200)
      });

      res.status(400).json({
        error: 'Invalid input detected',
        message: 'Request contains potentially harmful content'
      });
      return { valid: false };
    }

    // Replace request body with sanitized version
    req.validatedBody = securityCheck.sanitized;

    return { valid: true };

  } catch (error) {
    console.error('❌ Input validation error:', error);
    return { valid: true }; // Allow on error to prevent blocking
  }
}

/**
 * Add security headers to response
 */
function addSecurityHeaders(res: ApiResponse): void {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate');
  res.setHeader('Pragma', 'no-cache');
}

/**
 * Log API requests for monitoring
 */
async function logApiRequest(req: ApiRequest, clientInfo: any): Promise<void> {
  try {
    await supabase
      .from('admin_audit_logs')
      .insert({
        admin_email: req.user?.email || 'anonymous',
        action: `API_REQUEST_${req.method}`,
        target_type: 'api_endpoint',
        target_id: req.url || 'unknown',
        metadata: {
          method: req.method,
          url: req.url,
          userAgent: clientInfo.userAgent,
          ipAddress: clientInfo.ipAddress,
          isBot: clientInfo.isBot,
          userId: req.user?.id,
          timestamp: new Date().toISOString()
        },
        created_at: new Date().toISOString()
      });

  } catch (error) {
    console.error('❌ Failed to log API request:', error);
  }
}

/**
 * Log security incidents
 */
async function logSecurityIncident(
  incidentType: string,
  req: ApiRequest,
  metadata: any
): Promise<void> {
  try {
    await supabase
      .from('admin_audit_logs')
      .insert({
        admin_email: 'security_system',
        action: `SECURITY_INCIDENT_${incidentType}`,
        target_type: 'security_incident',
        target_id: req.securityContext?.identifier || 'unknown',
        metadata: {
          ...metadata,
          method: req.method,
          url: req.url,
          userAgent: req.securityContext?.userAgent,
          ipAddress: req.securityContext?.ipAddress,
          timestamp: new Date().toISOString(),
          severity: 'HIGH'
        },
        created_at: new Date().toISOString()
      });

  } catch (error) {
    console.error('❌ Failed to log security incident:', error);
  }
}

/**
 * Predefined middleware configurations
 */
export const apiSecurityPresets = {
  // Public API endpoints (no auth required)
  public: createSecureApiMiddleware({
    requireAuth: false,
    requireAdmin: false,
    allowBot: true,
    rateLimit: true,
    validateInput: true,
    logRequests: false
  }),

  // Authenticated user endpoints
  authenticated: createSecureApiMiddleware({
    requireAuth: true,
    requireAdmin: false,
    allowBot: true,
    rateLimit: true,
    validateInput: true,
    logRequests: true
  }),

  // Admin-only endpoints
  admin: createSecureApiMiddleware({
    requireAuth: true,
    requireAdmin: true,
    allowBot: true,
    rateLimit: true,
    validateInput: true,
    logRequests: true
  }),

  // High-security financial endpoints
  financial: createSecureApiMiddleware({
    requireAuth: true,
    requireAdmin: true,
    allowBot: true,
    rateLimit: true,
    validateInput: true,
    logRequests: true
  })
};

export default createSecureApiMiddleware;
