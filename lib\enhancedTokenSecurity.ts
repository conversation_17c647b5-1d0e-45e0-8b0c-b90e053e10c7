/**
 * ENHANCED TOKEN SECURITY SYSTEM
 * 
 * This module provides cryptographically secure token generation,
 * validation, and management with proper expiration enforcement.
 */

import { supabase } from './supabase';

interface TokenConfig {
  length: number;
  expiryMinutes: number;
  tokenType: string;
  allowRevocation: boolean;
}

interface TokenData {
  token: string;
  userId: number;
  tokenType: string;
  createdAt: Date;
  expiresAt: Date;
  isRevoked: boolean;
  metadata?: any;
}

interface TokenValidationResult {
  valid: boolean;
  tokenData?: TokenData;
  error?: string;
  remainingTime?: number;
}

class EnhancedTokenManager {
  private readonly defaultConfigs: { [key: string]: TokenConfig } = {
    'auth': {
      length: 32,
      expiryMinutes: 60, // 1 hour
      tokenType: 'auth',
      allowRevocation: true
    },
    'password_reset': {
      length: 32,
      expiryMinutes: 60, // 1 hour
      tokenType: 'password_reset',
      allowRevocation: true
    },
    'email_verification': {
      length: 32,
      expiryMinutes: 1440, // 24 hours
      tokenType: 'email_verification',
      allowRevocation: true
    },
    'api_key': {
      length: 40,
      expiryMinutes: 525600, // 1 year
      tokenType: 'api_key',
      allowRevocation: true
    },
    'session': {
      length: 32,
      expiryMinutes: 1440, // 24 hours
      tokenType: 'session',
      allowRevocation: true
    }
  };

  /**
   * Generate cryptographically secure token
   */
  async generateSecureToken(
    tokenType: string,
    userId: number,
    metadata: any = {}
  ): Promise<{ success: boolean; token?: string; expiresAt?: Date; error?: string }> {
    try {
      console.log(`🔐 Generating secure ${tokenType} token for user ${userId}`);

      const config = this.defaultConfigs[tokenType];
      if (!config) {
        return { success: false, error: 'Invalid token type' };
      }

      // Generate cryptographically secure token
      const token = await this.generateCryptoToken(config.length);
      const now = new Date();
      const expiresAt = new Date(now.getTime() + config.expiryMinutes * 60 * 1000);

      // Store token in database
      const { error: dbError } = await supabase
        .from('secure_tokens')
        .insert({
          token,
          user_id: userId,
          token_type: tokenType,
          created_at: now.toISOString(),
          expires_at: expiresAt.toISOString(),
          is_revoked: false,
          metadata: metadata || {}
        });

      if (dbError) {
        console.error('❌ Failed to store secure token:', dbError);
        return { success: false, error: 'Failed to store token' };
      }

      // Log token generation
      await this.logTokenEvent('TOKEN_GENERATED', userId, tokenType, {
        tokenLength: token.length,
        expiryMinutes: config.expiryMinutes,
        metadata
      });

      console.log(`✅ Secure ${tokenType} token generated for user ${userId}`);
      return { success: true, token, expiresAt };

    } catch (error) {
      console.error('❌ Token generation error:', error);
      return { success: false, error: 'Token generation failed' };
    }
  }

  /**
   * Validate token and check expiration
   */
  async validateToken(
    token: string,
    expectedType?: string
  ): Promise<TokenValidationResult> {
    try {
      if (!token || token.length < 32) {
        return { valid: false, error: 'Invalid token format' };
      }

      // Fetch token from database
      const { data: tokenData, error } = await supabase
        .from('secure_tokens')
        .select('*')
        .eq('token', token)
        .eq('is_revoked', false)
        .single();

      if (error || !tokenData) {
        await this.logTokenEvent('TOKEN_VALIDATION_FAILED', 0, 'unknown', {
          reason: 'token_not_found',
          token: token.substring(0, 8) + '...'
        });
        return { valid: false, error: 'Token not found or revoked' };
      }

      // Check token type if specified
      if (expectedType && tokenData.token_type !== expectedType) {
        await this.logTokenEvent('TOKEN_VALIDATION_FAILED', tokenData.user_id, tokenData.token_type, {
          reason: 'type_mismatch',
          expected: expectedType,
          actual: tokenData.token_type
        });
        return { valid: false, error: 'Token type mismatch' };
      }

      // Check expiration
      const now = new Date();
      const expiresAt = new Date(tokenData.expires_at);

      if (now > expiresAt) {
        // Auto-revoke expired token
        await this.revokeToken(token);
        
        await this.logTokenEvent('TOKEN_EXPIRED', tokenData.user_id, tokenData.token_type, {
          expiredAt: expiresAt.toISOString(),
          checkedAt: now.toISOString()
        });
        
        return { valid: false, error: 'Token has expired' };
      }

      // Calculate remaining time
      const remainingTime = expiresAt.getTime() - now.getTime();

      const result: TokenData = {
        token: tokenData.token,
        userId: tokenData.user_id,
        tokenType: tokenData.token_type,
        createdAt: new Date(tokenData.created_at),
        expiresAt: expiresAt,
        isRevoked: tokenData.is_revoked,
        metadata: tokenData.metadata
      };

      await this.logTokenEvent('TOKEN_VALIDATED', tokenData.user_id, tokenData.token_type, {
        remainingMinutes: Math.floor(remainingTime / (60 * 1000))
      });

      return { 
        valid: true, 
        tokenData: result,
        remainingTime: remainingTime
      };

    } catch (error) {
      console.error('❌ Token validation error:', error);
      return { valid: false, error: 'Token validation failed' };
    }
  }

  /**
   * Revoke token
   */
  async revokeToken(token: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`🔒 Revoking token: ${token.substring(0, 8)}...`);

      // Get token info for logging
      const { data: tokenData } = await supabase
        .from('secure_tokens')
        .select('user_id, token_type')
        .eq('token', token)
        .single();

      // Mark token as revoked
      const { error } = await supabase
        .from('secure_tokens')
        .update({
          is_revoked: true,
          revoked_at: new Date().toISOString()
        })
        .eq('token', token);

      if (error) {
        console.error('❌ Failed to revoke token:', error);
        return { success: false, error: 'Failed to revoke token' };
      }

      // Log token revocation
      if (tokenData) {
        await this.logTokenEvent('TOKEN_REVOKED', tokenData.user_id, tokenData.token_type, {
          token: token.substring(0, 8) + '...'
        });
      }

      console.log(`✅ Token revoked: ${token.substring(0, 8)}...`);
      return { success: true };

    } catch (error) {
      console.error('❌ Token revocation error:', error);
      return { success: false, error: 'Token revocation failed' };
    }
  }

  /**
   * Revoke all tokens for a user
   */
  async revokeAllUserTokens(
    userId: number,
    tokenType?: string
  ): Promise<{ success: boolean; count: number; error?: string }> {
    try {
      console.log(`🔒 Revoking all ${tokenType || 'all'} tokens for user ${userId}`);

      let query = supabase
        .from('secure_tokens')
        .update({
          is_revoked: true,
          revoked_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .eq('is_revoked', false);

      if (tokenType) {
        query = query.eq('token_type', tokenType);
      }

      const { data: revokedTokens, error } = await query.select('token');

      if (error) {
        console.error('❌ Failed to revoke user tokens:', error);
        return { success: false, count: 0, error: 'Failed to revoke tokens' };
      }

      const count = revokedTokens?.length || 0;

      // Log bulk revocation
      await this.logTokenEvent('TOKENS_BULK_REVOKED', userId, tokenType || 'all', {
        tokenCount: count,
        tokenType: tokenType || 'all'
      });

      console.log(`✅ Revoked ${count} tokens for user ${userId}`);
      return { success: true, count };

    } catch (error) {
      console.error('❌ Bulk token revocation error:', error);
      return { success: false, count: 0, error: 'Bulk revocation failed' };
    }
  }

  /**
   * Clean up expired tokens
   */
  async cleanupExpiredTokens(): Promise<number> {
    try {
      console.log('🧹 Cleaning up expired tokens...');

      const { data: expiredTokens, error } = await supabase
        .from('secure_tokens')
        .update({ is_revoked: true })
        .lt('expires_at', new Date().toISOString())
        .eq('is_revoked', false)
        .select('token');

      if (error) {
        console.error('❌ Failed to cleanup expired tokens:', error);
        return 0;
      }

      const cleanedCount = expiredTokens?.length || 0;
      
      if (cleanedCount > 0) {
        console.log(`✅ Cleaned up ${cleanedCount} expired tokens`);
        
        // Log cleanup activity
        await this.logTokenEvent('TOKENS_CLEANUP', 0, 'system', {
          cleanedCount,
          cleanupTime: new Date().toISOString()
        });
      }

      return cleanedCount;

    } catch (error) {
      console.error('❌ Token cleanup error:', error);
      return 0;
    }
  }

  /**
   * Generate cryptographically secure token
   */
  private async generateCryptoToken(length: number): Promise<string> {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Check for predictable token patterns
   */
  private hasWeakPatterns(token: string): boolean {
    // Check for predictable patterns
    const weakPatterns = [
      /^webauth_\d+$/,
      /^token_\d+$/,
      /^auth_\d+$/,
      /(.)\1{4,}/, // Repeated characters
      /123456|abcdef|qwerty/i, // Common sequences
    ];

    return weakPatterns.some(pattern => pattern.test(token));
  }

  /**
   * Log token events
   */
  private async logTokenEvent(
    eventType: string,
    userId: number,
    tokenType: string,
    metadata: any
  ): Promise<void> {
    try {
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_email: 'token_security_system',
          action: `TOKEN_${eventType}`,
          target_type: 'token_security',
          target_id: userId.toString(),
          metadata: {
            ...metadata,
            tokenType,
            timestamp: new Date().toISOString()
          },
          created_at: new Date().toISOString()
        });

    } catch (error) {
      console.error('❌ Failed to log token event:', error);
    }
  }

  /**
   * Get token statistics
   */
  async getTokenStats(): Promise<any> {
    try {
      const { data: tokens, error } = await supabase
        .from('secure_tokens')
        .select('token_type, is_revoked, created_at, expires_at')
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

      if (error) {
        throw error;
      }

      const now = new Date();
      const stats = {
        totalTokens: tokens.length,
        activeTokens: tokens.filter(t => !t.is_revoked && new Date(t.expires_at) > now).length,
        revokedTokens: tokens.filter(t => t.is_revoked).length,
        expiredTokens: tokens.filter(t => !t.is_revoked && new Date(t.expires_at) <= now).length,
        byType: {} as any
      };

      // Group by token type
      tokens.forEach(token => {
        if (!stats.byType[token.token_type]) {
          stats.byType[token.token_type] = {
            total: 0,
            active: 0,
            revoked: 0,
            expired: 0
          };
        }
        
        stats.byType[token.token_type].total++;
        
        if (token.is_revoked) {
          stats.byType[token.token_type].revoked++;
        } else if (new Date(token.expires_at) <= now) {
          stats.byType[token.token_type].expired++;
        } else {
          stats.byType[token.token_type].active++;
        }
      });

      return stats;

    } catch (error) {
      console.error('❌ Failed to get token stats:', error);
      return null;
    }
  }
}

// Create singleton instance
export const enhancedTokenSecurity = new EnhancedTokenManager();

// Set up automatic cleanup (every 30 minutes)
if (typeof setInterval !== 'undefined') {
  setInterval(() => {
    enhancedTokenSecurity.cleanupExpiredTokens().catch(console.error);
  }, 30 * 60 * 1000);
}

export default enhancedTokenSecurity;
