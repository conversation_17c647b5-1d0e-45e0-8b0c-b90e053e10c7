/**
 * FINANCIAL AUDIT SYSTEM TEST
 * 
 * Test script to verify the Financial Audit Report Generation System
 * works correctly with real user data.
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

const supabaseUrl = process.env.VITE_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

/**
 * Test the financial audit system with User ID 144 (Reena)
 */
async function testFinancialAuditSystem() {
  console.log('🔍 Testing Financial Audit Report Generation System...\n')

  const testUserId = 144

  try {
    // Test 1: Verify user exists
    console.log('📋 TEST 1: User Profile Retrieval')
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, username, email, full_name, created_at, sponsor_user_id, total_referrals, total_earnings')
      .eq('id', testUserId)
      .single()

    if (userError) {
      throw new Error(`User retrieval failed: ${userError.message}`)
    }

    console.log(`✅ User found: ${user.full_name || user.username} (ID: ${user.id})`)
    console.log(`   Email: ${user.email}`)
    console.log(`   Referrals: ${user.total_referrals}`)

    // Test 2: Share purchases
    console.log('\n📋 TEST 2: Share Purchases Analysis')
    const { data: sharePurchases, error: sharesError } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .eq('user_id', testUserId)
      .order('created_at')

    if (sharesError) {
      throw new Error(`Share purchases retrieval failed: ${sharesError.message}`)
    }

    const totalDirectShares = sharePurchases
      .filter(p => p.status === 'active')
      .reduce((sum, p) => sum + p.shares_purchased, 0)

    console.log(`✅ Share purchases found: ${sharePurchases.length} transactions`)
    console.log(`   Total direct shares: ${totalDirectShares}`)
    console.log(`   Total amount spent: $${sharePurchases.reduce((sum, p) => sum + parseFloat(p.total_amount), 0)}`)

    // Test 3: Commission transactions
    console.log('\n📋 TEST 3: Commission Transactions Analysis')
    const { data: commissionTransactions, error: commissionsError } = await supabase
      .from('commission_transactions')
      .select('*')
      .or(`referrer_id.eq.${testUserId},referred_id.eq.${testUserId}`)
      .order('created_at')

    if (commissionsError) {
      throw new Error(`Commission transactions retrieval failed: ${commissionsError.message}`)
    }

    const totalUsdtCommission = commissionTransactions
      .filter(t => t.status === 'approved' && t.referrer_id === testUserId)
      .reduce((sum, t) => sum + parseFloat(t.usdt_commission), 0)

    const totalShareCommission = commissionTransactions
      .filter(t => t.status === 'approved' && t.referrer_id === testUserId)
      .reduce((sum, t) => sum + parseFloat(t.share_commission), 0)

    console.log(`✅ Commission transactions found: ${commissionTransactions.length} transactions`)
    console.log(`   Total USDT commission earned: $${totalUsdtCommission}`)
    console.log(`   Total share commission earned: ${totalShareCommission} shares`)

    // Test 4: Commission balance verification
    console.log('\n📋 TEST 4: Commission Balance Verification')
    const { data: commissionBalance, error: balanceError } = await supabase
      .from('commission_balances')
      .select('*')
      .eq('user_id', testUserId)
      .single()

    if (balanceError && balanceError.code !== 'PGRST116') {
      throw new Error(`Commission balance retrieval failed: ${balanceError.message}`)
    }

    if (commissionBalance) {
      console.log(`✅ Commission balance found:`)
      console.log(`   Current USDT balance: $${commissionBalance.usdt_balance}`)
      console.log(`   Current share balance: ${commissionBalance.share_balance} shares`)
      console.log(`   Total earned USDT: $${commissionBalance.total_earned_usdt}`)
      console.log(`   Total earned shares: ${commissionBalance.total_earned_shares} shares`)

      // Verify balance consistency
      const usdtDiscrepancy = Math.abs(totalUsdtCommission - parseFloat(commissionBalance.total_earned_usdt))
      const shareDiscrepancy = Math.abs(totalShareCommission - parseFloat(commissionBalance.total_earned_shares))

      if (usdtDiscrepancy > 0.01) {
        console.log(`⚠️  USDT balance discrepancy: ${usdtDiscrepancy}`)
      } else {
        console.log(`✅ USDT balance verified: No discrepancies`)
      }

      if (shareDiscrepancy > 0.01) {
        console.log(`⚠️  Share balance discrepancy: ${shareDiscrepancy}`)
      } else {
        console.log(`✅ Share balance verified: No discrepancies`)
      }
    } else {
      console.log(`⚠️  No commission balance record found`)
    }

    // Test 5: Referral relationships
    console.log('\n📋 TEST 5: Referral Relationships')
    const { data: referrals, error: referralsError } = await supabase
      .from('referrals')
      .select('*')
      .or(`referrer_id.eq.${testUserId},referred_id.eq.${testUserId}`)
      .order('created_at')

    if (referralsError) {
      throw new Error(`Referrals retrieval failed: ${referralsError.message}`)
    }

    const asReferrer = referrals.filter(r => r.referrer_id === testUserId)
    const asReferred = referrals.filter(r => r.referred_id === testUserId)

    console.log(`✅ Referral relationships found: ${referrals.length} total`)
    console.log(`   As referrer: ${asReferrer.length} referrals`)
    console.log(`   As referred: ${asReferred.length} relationships`)

    // Test 6: Portfolio calculation
    console.log('\n📋 TEST 6: Portfolio Valuation')
    const { data: currentPhase, error: phaseError } = await supabase
      .from('investment_phases')
      .select('*')
      .eq('is_active', true)
      .single()

    if (phaseError) {
      throw new Error(`Investment phase retrieval failed: ${phaseError.message}`)
    }

    const currentSharePrice = parseFloat(currentPhase.price_per_share)
    const totalSharesOwned = totalDirectShares + totalShareCommission
    const portfolioValue = totalSharesOwned * currentSharePrice

    console.log(`✅ Portfolio calculation:`)
    console.log(`   Current share price: $${currentSharePrice}`)
    console.log(`   Total shares owned: ${totalSharesOwned} (${totalDirectShares} direct + ${totalShareCommission} commission)`)
    console.log(`   Portfolio value: $${portfolioValue.toLocaleString()}`)

    // Test 7: Audit status determination
    console.log('\n📋 TEST 7: Audit Status Determination')
    let auditStatus = 'PASSED'
    const auditFindings = []

    if (commissionBalance) {
      const usdtDiscrepancy = Math.abs(totalUsdtCommission - parseFloat(commissionBalance.total_earned_usdt))
      const shareDiscrepancy = Math.abs(totalShareCommission - parseFloat(commissionBalance.total_earned_shares))

      if (usdtDiscrepancy > 0.01 || shareDiscrepancy > 0.01) {
        auditStatus = 'FAILED'
        auditFindings.push('Commission balance discrepancies detected')
      }
    }

    const inactiveShares = sharePurchases.filter(p => p.status !== 'active')
    if (inactiveShares.length > 0) {
      auditStatus = auditStatus === 'PASSED' ? 'WARNING' : auditStatus
      auditFindings.push(`${inactiveShares.length} inactive share purchases found`)
    }

    const unapprovedCommissions = commissionTransactions.filter(t => t.status !== 'approved')
    if (unapprovedCommissions.length > 0) {
      auditStatus = auditStatus === 'PASSED' ? 'WARNING' : auditStatus
      auditFindings.push(`${unapprovedCommissions.length} unapproved commission transactions found`)
    }

    if (auditStatus === 'PASSED') {
      auditFindings.push('All financial records verified and accurate')
    }

    console.log(`✅ Audit Status: ${auditStatus}`)
    auditFindings.forEach(finding => {
      console.log(`   • ${finding}`)
    })

    // Final summary
    console.log('\n🎯 FINANCIAL AUDIT SYSTEM TEST SUMMARY')
    console.log('=' .repeat(50))
    console.log(`User: ${user.full_name || user.username} (ID: ${user.id})`)
    console.log(`Total Shares Owned: ${totalSharesOwned.toLocaleString()} shares`)
    console.log(`Portfolio Value: $${portfolioValue.toLocaleString()}`)
    console.log(`USDT Commission Earned: $${totalUsdtCommission.toLocaleString()}`)
    console.log(`Current USDT Balance: $${commissionBalance ? commissionBalance.usdt_balance : '0.00'}`)
    console.log(`Audit Status: ${auditStatus}`)
    console.log('=' .repeat(50))
    console.log('✅ Financial Audit System Test COMPLETED SUCCESSFULLY')

  } catch (error) {
    console.error('❌ Financial Audit System Test FAILED:', error.message)
    process.exit(1)
  }
}

// Run the test
testFinancialAuditSystem()
