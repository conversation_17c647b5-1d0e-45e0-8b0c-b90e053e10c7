#!/usr/bin/env node

/**
 * FUNCTIONAL TESTING SCRIPT FOR REGISTRATION AND LOGIN
 * 
 * This script tests the complete registration and login flow
 * with the new bcrypt password security implementation.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import bcrypt from 'bcryptjs';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

class RegistrationLoginTester {
  constructor() {
    this.testResults = {
      totalTests: 0,
      passed: 0,
      failed: 0,
      errors: []
    };
    
    // Test user data
    this.testUser = {
      email: `test.user.${Date.now()}@example.com`,
      password: 'TestPassword123!',
      fullName: 'Test User',
      username: `testuser${Date.now()}`,
      phone: '+1234567890',
      countryOfResidence: 'ZAF',
      hasTelegram: false,
      sponsorUsername: 'admin',
      confirmPassword: 'TestPassword123!'
    };
  }

  async runFunctionalTests() {
    console.log('🧪 FUNCTIONAL REGISTRATION & LOGIN TESTING');
    console.log('==========================================\n');

    try {
      await this.testPasswordHashing();
      await this.testUserRegistration();
      await this.testUserLogin();
      await this.testPasswordVerification();
      await this.cleanupTestUser();
      
      this.generateTestReport();
      
    } catch (error) {
      console.error('❌ Functional test suite failed:', error);
    }
  }

  async testPasswordHashing() {
    console.log('🔐 Testing password hashing functionality...');
    this.testResults.totalTests++;

    try {
      // Test the hashPassword function from our implementation
      const { hashPassword, verifyPassword, validatePasswordStrength } = await import('./lib/passwordSecurity.ts');
      
      const testPassword = this.testUser.password;
      
      // Test password strength validation
      const strengthValidation = validatePasswordStrength(testPassword);
      if (!strengthValidation.valid) {
        throw new Error(`Password strength validation failed: ${strengthValidation.errors.join(', ')}`);
      }
      
      // Test password hashing
      const hash = await hashPassword(testPassword);
      if (!hash || hash.length !== 60 || !hash.startsWith('$2b$')) {
        throw new Error('Password hashing produced invalid hash format');
      }
      
      // Test password verification
      const isValid = await verifyPassword(testPassword, hash);
      if (!isValid) {
        throw new Error('Password verification failed for correct password');
      }
      
      const isInvalid = await verifyPassword('WrongPassword', hash);
      if (isInvalid) {
        throw new Error('Password verification incorrectly accepted wrong password');
      }
      
      console.log('✅ Password hashing functionality test PASSED');
      console.log(`   ✓ Password strength validation working`);
      console.log(`   ✓ Password hashing produces bcrypt format`);
      console.log(`   ✓ Password verification working correctly`);
      
      this.testResults.passed++;
    } catch (error) {
      console.log('❌ Password hashing functionality test FAILED:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Password hashing: ${error.message}`);
    }
  }

  async testUserRegistration() {
    console.log('👤 Testing user registration...');
    this.testResults.totalTests++;

    try {
      // Import the registration function
      const { registerUserWithEmail } = await import('./lib/supabase.ts');
      
      console.log(`   Registering user: ${this.testUser.email}`);
      
      // Test user registration
      const result = await registerUserWithEmail(this.testUser);
      
      if (result.error) {
        throw new Error(`Registration failed: ${result.error.message}`);
      }
      
      if (!result.user) {
        throw new Error('Registration succeeded but no user returned');
      }
      
      // Verify user was created in database
      const { data: dbUser, error: dbError } = await supabase
        .from('users')
        .select('*')
        .eq('email', this.testUser.email)
        .single();
      
      if (dbError || !dbUser) {
        throw new Error('User not found in database after registration');
      }
      
      // Verify password hash format
      if (!dbUser.password_hash || !dbUser.password_hash.startsWith('$2b$')) {
        throw new Error('User password hash is not in bcrypt format');
      }
      
      // Verify password can be verified
      const passwordValid = await bcrypt.compare(this.testUser.password, dbUser.password_hash);
      if (!passwordValid) {
        throw new Error('Stored password hash does not verify correctly');
      }
      
      console.log('✅ User registration test PASSED');
      console.log(`   ✓ User created successfully: ${dbUser.id}`);
      console.log(`   ✓ Password stored in bcrypt format`);
      console.log(`   ✓ Password hash verifies correctly`);
      console.log(`   ✓ User data stored correctly`);
      
      this.testResults.passed++;
    } catch (error) {
      console.log('❌ User registration test FAILED:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`User registration: ${error.message}`);
    }
  }

  async testUserLogin() {
    console.log('🔑 Testing user login...');
    this.testResults.totalTests++;

    try {
      // Import the login function
      const { signInWithEmailEnhanced } = await import('./lib/supabase.ts');
      
      console.log(`   Logging in user: ${this.testUser.email}`);
      
      // Test successful login
      const loginResult = await signInWithEmailEnhanced(this.testUser.email, this.testUser.password);
      
      if (loginResult.error) {
        throw new Error(`Login failed: ${loginResult.error.message}`);
      }
      
      if (!loginResult.user) {
        throw new Error('Login succeeded but no user returned');
      }
      
      // Test failed login with wrong password
      const failedLoginResult = await signInWithEmailEnhanced(this.testUser.email, 'WrongPassword123!');
      
      if (!failedLoginResult.error) {
        throw new Error('Login with wrong password should have failed');
      }
      
      console.log('✅ User login test PASSED');
      console.log(`   ✓ Correct password login successful`);
      console.log(`   ✓ Wrong password login correctly rejected`);
      console.log(`   ✓ User data returned correctly`);
      
      this.testResults.passed++;
    } catch (error) {
      console.log('❌ User login test FAILED:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`User login: ${error.message}`);
    }
  }

  async testPasswordVerification() {
    console.log('🔍 Testing password verification edge cases...');
    this.testResults.totalTests++;

    try {
      // Get the user from database
      const { data: dbUser, error: dbError } = await supabase
        .from('users')
        .select('password_hash')
        .eq('email', this.testUser.email)
        .single();
      
      if (dbError || !dbUser) {
        throw new Error('Could not retrieve test user from database');
      }
      
      // Test various password verification scenarios
      const testCases = [
        { password: this.testUser.password, expected: true, description: 'Correct password' },
        { password: 'WrongPassword123!', expected: false, description: 'Wrong password' },
        { password: '', expected: false, description: 'Empty password' },
        { password: this.testUser.password.toLowerCase(), expected: false, description: 'Case-sensitive password' },
        { password: this.testUser.password + ' ', expected: false, description: 'Password with trailing space' },
        { password: ' ' + this.testUser.password, expected: false, description: 'Password with leading space' },
      ];
      
      for (const testCase of testCases) {
        const result = await bcrypt.compare(testCase.password, dbUser.password_hash);
        if (result !== testCase.expected) {
          throw new Error(`${testCase.description}: expected ${testCase.expected}, got ${result}`);
        }
        console.log(`   ✓ ${testCase.description}: ${result ? 'accepted' : 'rejected'} (correct)`);
      }
      
      console.log('✅ Password verification edge cases test PASSED');
      
      this.testResults.passed++;
    } catch (error) {
      console.log('❌ Password verification edge cases test FAILED:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Password verification: ${error.message}`);
    }
  }

  async cleanupTestUser() {
    console.log('🧹 Cleaning up test user...');
    
    try {
      // Delete test user from users table
      const { error: deleteError } = await supabase
        .from('users')
        .delete()
        .eq('email', this.testUser.email);
      
      if (deleteError) {
        console.log(`   ⚠️ Warning: Could not delete test user: ${deleteError.message}`);
      } else {
        console.log(`   ✓ Test user cleaned up successfully`);
      }
      
      // Try to delete from Supabase auth as well
      try {
        const { data: authUsers } = await supabase.auth.admin.listUsers();
        const testAuthUser = authUsers.users.find(u => u.email === this.testUser.email);
        
        if (testAuthUser) {
          await supabase.auth.admin.deleteUser(testAuthUser.id);
          console.log(`   ✓ Test auth user cleaned up successfully`);
        }
      } catch (authError) {
        console.log(`   ⚠️ Warning: Could not delete test auth user: ${authError.message}`);
      }
      
    } catch (error) {
      console.log(`   ⚠️ Warning: Cleanup failed: ${error.message}`);
    }
  }

  generateTestReport() {
    console.log('\n📋 FUNCTIONAL TEST REPORT');
    console.log('==========================');
    console.log(`Total Tests: ${this.testResults.totalTests}`);
    console.log(`Passed: ${this.testResults.passed} ✅`);
    console.log(`Failed: ${this.testResults.failed} ❌`);
    console.log(`Success Rate: ${((this.testResults.passed / this.testResults.totalTests) * 100).toFixed(1)}%`);

    if (this.testResults.errors.length > 0) {
      console.log('\n❌ ERRORS FOUND:');
      this.testResults.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }

    if (this.testResults.failed === 0) {
      console.log('\n🎉 ALL FUNCTIONAL TESTS PASSED!');
      console.log('✅ User registration with bcrypt working correctly');
      console.log('✅ User login with password verification working correctly');
      console.log('✅ Password security implementation is fully functional');
      console.log('\n🔐 TASK 1.1 COMPLETED SUCCESSFULLY!');
      console.log('📋 Ready to proceed with password migration (Task 1.2)');
    } else {
      console.log('\n⚠️ FUNCTIONAL ISSUES DETECTED!');
      console.log('Please fix the failed tests before proceeding.');
    }
  }
}

// Run the functional test suite
const tester = new RegistrationLoginTester();
tester.runFunctionalTests().catch(console.error);
