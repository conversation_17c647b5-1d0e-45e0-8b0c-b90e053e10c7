/**
 * AUTHENTICATION TABS
 * 
 * Tab navigation component for switching between different
 * authentication methods (Telegram, Web Login, Web Register).
 */

import React from 'react';
import { AuthTab } from '../../hooks/useAuthState';

interface AuthTabsProps {
  activeTab: AuthTab;
  onTabChange: (tab: AuthTab) => void;
  userType: 'shareholder' | 'affiliate';
}

export const AuthTabs: React.FC<AuthTabsProps> = ({
  activeTab,
  onTabChange,
  userType
}) => {
  const tabs = [
    {
      id: 'web-register' as AuthTab,
      label: 'Create Account',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
        </svg>
      ),
      description: 'New to Aureus Alliance? Create your account'
    },
    {
      id: 'web-login' as AuthTab,
      label: 'Sign In',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
        </svg>
      ),
      description: 'Already have an account? Sign in here'
    },
    {
      id: 'telegram' as AuthTab,
      label: 'Telegram',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
        </svg>
      ),
      description: 'Continue with your Telegram account'
    }
  ];

  return (
    <div className="space-y-4">
      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-700 p-1 rounded-lg">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`flex-1 flex items-center justify-center space-x-2 py-2.5 px-3 rounded-md text-sm font-medium transition-colors ${
              activeTab === tab.id
                ? 'bg-yellow-600 text-white shadow-sm'
                : 'text-gray-300 hover:text-white hover:bg-gray-600'
            }`}
          >
            {tab.icon}
            <span className="hidden sm:inline">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Tab Description */}
      <div className="text-center">
        <p className="text-gray-400 text-sm">
          {tabs.find(tab => tab.id === activeTab)?.description}
        </p>
      </div>

      {/* Special Notice for Telegram Tab */}
      {activeTab === 'telegram' && (
        <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <div className="text-blue-400 text-xl">🤖</div>
            <div>
              <h4 className="text-blue-400 font-semibold mb-2">Telegram Bot Authentication</h4>
              <p className="text-blue-300 text-sm mb-3">
                Use our Telegram bot to authenticate quickly and securely. This method is perfect for existing Telegram users.
              </p>
              <div className="space-y-2 text-blue-300 text-sm">
                <div className="flex items-center space-x-2">
                  <span className="text-blue-400">1.</span>
                  <span>Click "Start Authentication" below</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-blue-400">2.</span>
                  <span>Send the PIN to our Telegram bot</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-blue-400">3.</span>
                  <span>Complete authentication in Telegram</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Migration Notice for Web Login */}
      {activeTab === 'web-login' && (
        <div className="bg-purple-900/20 border border-purple-500/30 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="text-purple-400 text-xl">🔄</div>
            <div>
              <h4 className="text-purple-400 font-semibold mb-1">Have a Telegram Account?</h4>
              <p className="text-purple-300 text-sm">
                If you have an existing Telegram account with us, you can{' '}
                <a 
                  href="/migrate-telegram" 
                  className="text-purple-400 hover:text-purple-300 underline"
                >
                  migrate it to website access here
                </a>
                .
              </p>
            </div>
          </div>
        </div>
      )}

      {/* User Type Indicator */}
      <div className="text-center">
        <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-600/20 text-yellow-400 border border-yellow-600/30">
          {userType === 'shareholder' ? '👤 Shareholder Access' : '🤝 Affiliate Access'}
        </span>
      </div>
    </div>
  );
};
