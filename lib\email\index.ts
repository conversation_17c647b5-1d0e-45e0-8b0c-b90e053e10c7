/**
 * EMAIL SERVICE INDEX
 * 
 * Main entry point for the refactored email service.
 * Provides a singleton instance and backwards compatibility
 * with the original resendEmailService.ts interface.
 */

import { EmailService } from './EmailService';
import { EmailServiceConfig } from './types/EmailTypes';

// Environment configuration
const RESEND_API_KEY = import.meta.env.VITE_RESEND_API_KEY || process.env.RESEND_API_KEY || '';
const RESEND_FROM_EMAIL = import.meta.env.VITE_RESEND_FROM_EMAIL || process.env.RESEND_FROM_EMAIL || '<EMAIL>';
const RESEND_FROM_NAME = import.meta.env.VITE_RESEND_FROM_NAME || process.env.RESEND_FROM_NAME || 'Aureus Alliance Holdings';

// Default configuration
const defaultConfig: EmailServiceConfig = {
  apiKey: RESEND_API_KEY,
  fromEmail: RESEND_FROM_EMAIL,
  fromName: RESEND_FROM_NAME,
  maxRetries: 3,
  retryDelay: 1000,
  batchSize: 10,
  rateLimitPerMinute: 30,
  enableAnalytics: true,
  enableQueue: false,
  defaultTemplateData: {
    companyName: 'Aureus Alliance Holdings',
    websiteUrl: 'https://aureus.africa',
    supportEmail: '<EMAIL>'
  }
};

// Singleton instance
let emailServiceInstance: EmailService | null = null;

/**
 * Get the singleton email service instance
 */
export function getEmailService(config?: Partial<EmailServiceConfig>): EmailService {
  if (!emailServiceInstance) {
    const finalConfig = { ...defaultConfig, ...config };
    emailServiceInstance = new EmailService(finalConfig);
  }
  return emailServiceInstance;
}

/**
 * Reset the singleton instance (useful for testing)
 */
export function resetEmailService(): void {
  emailServiceInstance = null;
}

// Export the default instance for backwards compatibility
export const emailService = getEmailService();

// Re-export types and classes for direct usage
export * from './types/EmailTypes';
export { EmailService } from './EmailService';
export { EmailClient } from './EmailClient';
export { BaseEmailTemplate } from './templates/BaseEmailTemplate';
export { VerificationEmailTemplate } from './templates/VerificationEmailTemplate';
export { WelcomeEmailTemplate } from './templates/WelcomeEmailTemplate';
export { EmailValidator } from './utils/EmailValidator';
export { EmailLogger } from './utils/EmailLogger';

// Backwards compatibility functions
export const sendVerificationEmail = (data: any) => emailService.sendVerificationEmail(data);
export const sendWelcomeEmail = (data: any) => emailService.sendWelcomeEmail(data);
export const sendSharePurchaseConfirmation = (data: any) => emailService.sendSharePurchaseConfirmation(data);
export const sendCommissionEarnedNotification = (data: any) => emailService.sendCommissionEarnedNotification(data);
export const sendMigrationConfirmation = (data: any) => emailService.sendMigrationConfirmation(data);
export const sendBulkEmails = (data: any) => emailService.sendBulkEmails(data);

// Service status
export const getEmailServiceStatus = () => emailService.getServiceStatus();
