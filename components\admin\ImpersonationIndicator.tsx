import React from 'react'
import { useImpersonationSession, endImpersonationSession } from '../../lib/adminImpersonation'

export const ImpersonationIndicator: React.FC = () => {
  const { session, isImpersonating, endSession } = useImpersonationSession()

  const handleEndImpersonation = async () => {
    if (confirm('Are you sure you want to end the impersonation session and return to admin dashboard?')) {
      await endSession()
      // Redirect to admin dashboard
      window.location.href = '/admin'
    }
  }

  const handleReturnToAdmin = () => {
    // Open admin dashboard in new tab
    window.open('/admin', '_blank')
  }

  if (!isImpersonating || !session) {
    return null
  }

  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-red-600 text-white shadow-lg">
      <div className="container mx-auto px-4 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <span className="text-lg">⚠️</span>
              <span className="font-semibold">ADMIN IMPERSONATION ACTIVE</span>
            </div>
            
            <div className="text-sm opacity-90">
              <span>Admin: </span>
              <span className="font-medium">{session.originalAdmin.email}</span>
              <span className="mx-2">|</span>
              <span>Viewing as: </span>
              <span className="font-medium">
                {session.targetUser.full_name || session.targetUser.username} ({session.targetUser.email})
              </span>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <div className="text-xs opacity-75">
              Started: {new Date(session.impersonationStartTime).toLocaleTimeString()}
            </div>
            
            <button
              onClick={handleReturnToAdmin}
              className="px-3 py-1 bg-red-700 hover:bg-red-800 rounded text-sm font-medium transition-colors"
            >
              🔧 Admin Dashboard
            </button>
            
            <button
              onClick={handleEndImpersonation}
              className="px-3 py-1 bg-red-800 hover:bg-red-900 rounded text-sm font-medium transition-colors"
            >
              ❌ End Session
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

// Higher-order component to add impersonation indicator to any page
export const withImpersonationIndicator = <P extends object>(
  WrappedComponent: React.ComponentType<P>
) => {
  return function ImpersonationWrappedComponent(props: P) {
    return (
      <>
        <ImpersonationIndicator />
        <div style={{ paddingTop: '60px' }}>
          <WrappedComponent {...props} />
        </div>
      </>
    )
  }
}
