// Test Login in Browser
// Run this in the browser console to test the login

const testLogin = async () => {
  console.log('🧪 Testing login with correct credentials...');
  
  const telegramId = '1270124602';
  const password = 'Gunst0n5o0!@#';
  
  console.log(`Testing with Telegram ID: ${telegramId}`);
  console.log(`Testing with Password: ${password}`);
  
  try {
    // Check if supabase is available
    if (typeof supabase === 'undefined') {
      console.error('❌ Supabase client not available');
      return;
    }
    
    // Step 1: Find telegram user
    console.log('🔍 Step 1: Finding telegram user...');
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', parseInt(telegramId))
      .single();
    
    if (telegramError || !telegramUser) {
      console.error('❌ Telegram user not found:', telegramError?.message);
      return;
    }
    
    console.log('✅ Telegram user found:', telegramUser.username);
    
    // Step 2: Find linked user
    console.log('🔍 Step 2: Finding linked user...');
    const { data: linkedUser, error: linkedError } = await supabase
      .from('users')
      .select('*')
      .eq('id', telegramUser.user_id)
      .single();
    
    if (linkedError || !linkedUser) {
      console.error('❌ Linked user not found:', linkedError?.message);
      return;
    }
    
    console.log('✅ Linked user found:', linkedUser.email);
    
    // Step 3: Test password verification (we can't do this in browser without bcrypt)
    console.log('🔍 Step 3: Password hash exists:', !!linkedUser.password_hash);
    
    // Step 4: Create session data (simulate successful login)
    console.log('🔍 Step 4: Creating session data...');
    
    const authenticatedUser = {
      id: `telegram_${telegramUser.telegram_id}`,
      email: linkedUser.email,
      database_user: {
        ...linkedUser,
        telegram_id: telegramUser.telegram_id,
        telegram_username: telegramUser.username,
        telegram_connected: true
      },
      account_type: 'telegram_direct',
      user_metadata: {
        telegram_id: telegramUser.telegram_id,
        telegram_username: telegramUser.username,
        full_name: linkedUser.full_name,
        username: linkedUser.username,
        telegram_connected: true,
        telegram_registered: telegramUser.is_registered
      }
    };
    
    console.log('✅ Session data created:', authenticatedUser);
    
    // Store in localStorage
    localStorage.setItem('aureus_telegram_user', JSON.stringify(authenticatedUser));
    
    console.log('✅ Login test completed successfully!');
    console.log('🔄 You should now be able to access the dashboard');
    
    // Trigger a page refresh to apply the login
    if (confirm('Login test successful! Refresh page to apply login?')) {
      window.location.reload();
    }
    
  } catch (error) {
    console.error('❌ Login test failed:', error);
  }
};

// Auto-run the test
console.log('🚀 Starting login test...');
testLogin();
