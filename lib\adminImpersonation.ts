import React from 'react'
import { supabase, getServiceRoleClient } from './supabase'
import { logAdminAction } from './adminAuth'

export interface ImpersonationSession {
  originalAdmin: {
    email: string
    id: string
  }
  targetUser: {
    id: number
    username: string
    email: string
    full_name: string | null
  }
  impersonationStartTime: string
  sessionId: string
}

export interface ImpersonationResult {
  success: boolean
  error?: string
  sessionData?: ImpersonationSession
}

/**
 * Start an admin impersonation session
 */
export const startImpersonationSession = async (
  adminEmail: string,
  targetUserId: number
): Promise<ImpersonationResult> => {
  try {
    const serviceClient = getServiceRoleClient()

    // Get target user details
    const { data: targetUser, error: userError } = await serviceClient
      .from('users')
      .select('id, username, email, full_name, is_active')
      .eq('id', targetUserId)
      .single()

    if (userError || !targetUser) {
      return {
        success: false,
        error: 'Target user not found'
      }
    }

    if (!targetUser.is_active) {
      return {
        success: false,
        error: 'Cannot impersonate inactive user'
      }
    }

    // Create impersonation session data
    const sessionData: ImpersonationSession = {
      originalAdmin: {
        email: adminEmail,
        id: 'admin_' + Date.now()
      },
      targetUser: {
        id: targetUser.id,
        username: targetUser.username,
        email: targetUser.email,
        full_name: targetUser.full_name
      },
      impersonationStartTime: new Date().toISOString(),
      sessionId: `imp_${Date.now()}_${targetUser.id}`
    }

    // Log the impersonation start
    await logAdminAction(
      adminEmail,
      'USER_IMPERSONATION_START',
      'user',
      targetUser.id.toString(),
      {
        target_username: targetUser.username,
        target_email: targetUser.email,
        session_id: sessionData.sessionId,
        impersonation_reason: 'admin_support'
      }
    )

    return {
      success: true,
      sessionData
    }

  } catch (error: any) {
    console.error('Error starting impersonation session:', error)
    return {
      success: false,
      error: error.message || 'Failed to start impersonation session'
    }
  }
}

/**
 * End an admin impersonation session
 */
export const endImpersonationSession = async (
  sessionData: ImpersonationSession
): Promise<boolean> => {
  try {
    // Log the impersonation end
    await logAdminAction(
      sessionData.originalAdmin.email,
      'USER_IMPERSONATION_END',
      'user',
      sessionData.targetUser.id.toString(),
      {
        target_username: sessionData.targetUser.username,
        session_id: sessionData.sessionId,
        session_duration: Date.now() - new Date(sessionData.impersonationStartTime).getTime(),
        end_time: new Date().toISOString()
      }
    )

    // Clear session data from localStorage
    localStorage.removeItem('admin_impersonation_data')

    return true

  } catch (error: any) {
    console.error('Error ending impersonation session:', error)
    return false
  }
}

/**
 * Get current impersonation session if active
 */
export const getCurrentImpersonationSession = (): ImpersonationSession | null => {
  try {
    if (typeof window === 'undefined') return null

    const sessionData = localStorage.getItem('admin_impersonation_data')
    if (!sessionData) return null

    const parsed = JSON.parse(sessionData) as ImpersonationSession

    // Check if session is still valid (max 4 hours)
    const sessionAge = Date.now() - new Date(parsed.impersonationStartTime).getTime()
    const maxSessionAge = 4 * 60 * 60 * 1000 // 4 hours

    if (sessionAge > maxSessionAge) {
      localStorage.removeItem('admin_impersonation_data')
      return null
    }

    return parsed

  } catch (error) {
    console.error('Error getting impersonation session:', error)
    localStorage.removeItem('admin_impersonation_data')
    return null
  }
}

/**
 * Check if current session is an impersonation session
 */
export const isImpersonationSession = (): boolean => {
  return getCurrentImpersonationSession() !== null
}

/**
 * Get impersonated user data for authentication
 */
export const getImpersonatedUserAuth = async (
  userId: number
): Promise<{ user: any; error?: string } | null> => {
  try {
    const serviceClient = getServiceRoleClient()

    // Get user data including related information
    const { data: user, error } = await serviceClient
      .from('users')
      .select(`
        *,
        telegram_users (
          id,
          telegram_id,
          username,
          first_name,
          last_name
        ),
        kyc_information (
          id,
          status,
          full_name,
          id_number,
          phone_number,
          email_address
        )
      `)
      .eq('id', userId)
      .single()

    if (error || !user) {
      return {
        user: null,
        error: 'User not found for impersonation'
      }
    }

    return { user }

  } catch (error: any) {
    console.error('Error getting impersonated user auth:', error)
    return {
      user: null,
      error: error.message || 'Failed to get user data'
    }
  }
}

/**
 * Validate impersonation session and get user data
 */
export const validateImpersonationSession = async (
  sessionId: string,
  userId: number
): Promise<{ valid: boolean; user?: any; error?: string }> => {
  try {
    const currentSession = getCurrentImpersonationSession()

    if (!currentSession) {
      return {
        valid: false,
        error: 'No active impersonation session'
      }
    }

    if (currentSession.sessionId !== sessionId) {
      return {
        valid: false,
        error: 'Invalid session ID'
      }
    }

    if (currentSession.targetUser.id !== userId) {
      return {
        valid: false,
        error: 'User ID mismatch'
      }
    }

    // Get fresh user data
    const userResult = await getImpersonatedUserAuth(userId)
    if (!userResult || userResult.error) {
      return {
        valid: false,
        error: userResult?.error || 'Failed to get user data'
      }
    }

    return {
      valid: true,
      user: userResult.user
    }

  } catch (error: any) {
    console.error('Error validating impersonation session:', error)
    return {
      valid: false,
      error: error.message || 'Session validation failed'
    }
  }
}

/**
 * React hook for impersonation session management
 */
export const useImpersonationSession = () => {
  const [session, setSession] = React.useState<ImpersonationSession | null>(null)
  const [isImpersonating, setIsImpersonating] = React.useState(false)

  React.useEffect(() => {
    const currentSession = getCurrentImpersonationSession()
    setSession(currentSession)
    setIsImpersonating(!!currentSession)

    // Listen for session changes
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'admin_impersonation_data') {
        const newSession = getCurrentImpersonationSession()
        setSession(newSession)
        setIsImpersonating(!!newSession)
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [])

  const endSession = async () => {
    if (session) {
      await endImpersonationSession(session)
      setSession(null)
      setIsImpersonating(false)
    }
  }

  return {
    session,
    isImpersonating,
    endSession
  }
}
