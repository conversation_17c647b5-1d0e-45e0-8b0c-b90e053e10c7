import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';

interface SavedPaymentMethod {
  id: string;
  method_type: 'crypto' | 'bank_transfer';
  method_name: string;
  network_id?: string;
  network_name?: string;
  network_technical?: string;
  wallet_address?: string;
  bank_name?: string;
  is_default: boolean;
  is_active: boolean;
  created_at: string;
  last_used_at?: string;
}

interface SavedPaymentMethodsProps {
  userId: number;
  onMethodSelect?: (method: SavedPaymentMethod) => void;
  showAddButton?: boolean;
}

export const SavedPaymentMethods: React.FC<SavedPaymentMethodsProps> = ({
  userId,
  onMethodSelect,
  showAddButton = true
}) => {
  const [methods, setMethods] = useState<SavedPaymentMethod[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newMethod, setNewMethod] = useState({
    method_type: 'crypto' as 'crypto' | 'bank_transfer',
    method_name: '',
    network_id: 'BSC',
    network_name: 'Binance Smart Chain',
    network_technical: 'BEP-20',
    wallet_address: '',
    bank_name: '',
    is_default: false
  });

  useEffect(() => {
    loadPaymentMethods();
  }, [userId]);

  const loadPaymentMethods = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('user_payment_methods')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('is_default', { ascending: false })
        .order('last_used_at', { ascending: false });

      if (error) throw error;
      setMethods(data || []);
    } catch (error) {
      console.error('Error loading payment methods:', error);
    } finally {
      setLoading(false);
    }
  };

  const savePaymentMethod = async () => {
    try {
      const methodData = {
        user_id: userId,
        method_type: newMethod.method_type,
        method_name: newMethod.method_name,
        ...(newMethod.method_type === 'crypto' && {
          network_id: newMethod.network_id,
          network_name: newMethod.network_name,
          network_technical: newMethod.network_technical,
          wallet_address: newMethod.wallet_address
        }),
        ...(newMethod.method_type === 'bank_transfer' && {
          bank_name: newMethod.bank_name
        }),
        is_default: newMethod.is_default
      };

      const { error } = await supabase
        .from('user_payment_methods')
        .insert([methodData]);

      if (error) throw error;

      setShowAddForm(false);
      setNewMethod({
        method_type: 'crypto',
        method_name: '',
        network_id: 'BSC',
        network_name: 'Binance Smart Chain',
        network_technical: 'BEP-20',
        wallet_address: '',
        bank_name: '',
        is_default: false
      });
      loadPaymentMethods();
    } catch (error) {
      console.error('Error saving payment method:', error);
    }
  };

  const setAsDefault = async (methodId: string) => {
    try {
      const { error } = await supabase
        .from('user_payment_methods')
        .update({ is_default: true })
        .eq('id', methodId);

      if (error) throw error;
      loadPaymentMethods();
    } catch (error) {
      console.error('Error setting default method:', error);
    }
  };

  const deleteMethod = async (methodId: string) => {
    try {
      const { error } = await supabase
        .from('user_payment_methods')
        .update({ is_active: false })
        .eq('id', methodId);

      if (error) throw error;
      loadPaymentMethods();
    } catch (error) {
      console.error('Error deleting payment method:', error);
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '20px' }}>
        <div style={{ color: '#9ca3af' }}>Loading payment methods...</div>
      </div>
    );
  }

  return (
    <div style={{
      backgroundColor: 'rgba(31, 41, 55, 0.9)',
      borderRadius: '12px',
      padding: '24px',
      border: '1px solid #374151'
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h3 style={{ color: 'white', fontSize: '18px', fontWeight: 'bold', margin: 0 }}>
          💳 Saved Payment Methods
        </h3>
        {showAddButton && (
          <button
            onClick={() => setShowAddForm(true)}
            style={{
              padding: '8px 16px',
              backgroundColor: 'rgba(59, 130, 246, 0.2)',
              border: '1px solid #3b82f6',
              borderRadius: '8px',
              color: '#60a5fa',
              fontSize: '14px',
              cursor: 'pointer'
            }}
          >
            + Add Method
          </button>
        )}
      </div>

      {methods.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '20px', color: '#9ca3af' }}>
          No saved payment methods yet. Add one for faster purchases!
        </div>
      ) : (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
          {methods.map(method => (
            <div
              key={method.id}
              style={{
                padding: '16px',
                backgroundColor: 'rgba(55, 65, 81, 0.5)',
                borderRadius: '8px',
                border: method.is_default ? '2px solid #10b981' : '1px solid #4b5563',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <span style={{ fontSize: '20px' }}>
                  {method.method_type === 'crypto' ? '💳' : '🏦'}
                </span>
                <div>
                  <div style={{ color: 'white', fontWeight: '600' }}>
                    {method.method_name}
                    {method.is_default && (
                      <span style={{ 
                        marginLeft: '8px', 
                        padding: '2px 6px', 
                        backgroundColor: 'rgba(16, 185, 129, 0.2)',
                        color: '#10b981',
                        fontSize: '10px',
                        borderRadius: '4px'
                      }}>
                        DEFAULT
                      </span>
                    )}
                  </div>
                  <div style={{ color: '#9ca3af', fontSize: '12px' }}>
                    {method.method_type === 'crypto' 
                      ? `${method.network_name} (${method.network_technical})`
                      : method.bank_name
                    }
                  </div>
                </div>
              </div>
              
              <div style={{ display: 'flex', gap: '8px' }}>
                {onMethodSelect && (
                  <button
                    onClick={() => onMethodSelect(method)}
                    style={{
                      padding: '6px 12px',
                      backgroundColor: 'rgba(16, 185, 129, 0.2)',
                      border: '1px solid #10b981',
                      borderRadius: '6px',
                      color: '#10b981',
                      fontSize: '12px',
                      cursor: 'pointer'
                    }}
                  >
                    Use
                  </button>
                )}
                {!method.is_default && (
                  <button
                    onClick={() => setAsDefault(method.id)}
                    style={{
                      padding: '6px 12px',
                      backgroundColor: 'rgba(59, 130, 246, 0.2)',
                      border: '1px solid #3b82f6',
                      borderRadius: '6px',
                      color: '#60a5fa',
                      fontSize: '12px',
                      cursor: 'pointer'
                    }}
                  >
                    Set Default
                  </button>
                )}
                <button
                  onClick={() => deleteMethod(method.id)}
                  style={{
                    padding: '6px 12px',
                    backgroundColor: 'rgba(239, 68, 68, 0.2)',
                    border: '1px solid #ef4444',
                    borderRadius: '6px',
                    color: '#f87171',
                    fontSize: '12px',
                    cursor: 'pointer'
                  }}
                >
                  Delete
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Add Payment Method Form */}
      {showAddForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.95)',
            borderRadius: '12px',
            padding: '24px',
            border: '1px solid #374151',
            maxWidth: '500px',
            width: '90%',
            maxHeight: '80vh',
            overflow: 'auto'
          }}>
            <h3 style={{ color: 'white', fontSize: '18px', fontWeight: 'bold', marginBottom: '20px' }}>
              Add Payment Method
            </h3>
            
            {/* Form content would go here - truncated for space */}
            <div style={{ display: 'flex', gap: '12px', marginTop: '20px' }}>
              <button
                onClick={savePaymentMethod}
                style={{
                  flex: 1,
                  padding: '12px',
                  backgroundColor: '#3b82f6',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'white',
                  fontWeight: '600',
                  cursor: 'pointer'
                }}
              >
                Save Method
              </button>
              <button
                onClick={() => setShowAddForm(false)}
                style={{
                  flex: 1,
                  padding: '12px',
                  backgroundColor: 'transparent',
                  border: '1px solid #4b5563',
                  borderRadius: '8px',
                  color: '#9ca3af',
                  cursor: 'pointer'
                }}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
