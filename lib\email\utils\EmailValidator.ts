/**
 * EMAIL VALIDATOR
 * 
 * Validation utilities for email data and content
 * to ensure data integrity before sending emails.
 */

import {
  EmailValidationResult,
  EmailVerificationData,
  WelcomeEmailData,
  SharePurchaseConfirmationData,
  CommissionEarnedData,
  MigrationConfirmationData
} from '../types/EmailTypes';

export class EmailValidator {
  
  /**
   * Validate email address format
   */
  public validateEmailAddress(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate verification email data
   */
  public validateVerificationData(data: EmailVerificationData): EmailValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required fields
    if (!data.email) {
      errors.push('Email address is required');
    } else if (!this.validateEmailAddress(data.email)) {
      errors.push('Invalid email address format');
    }

    if (!data.code) {
      errors.push('Verification code is required');
    } else if (data.code.length < 4 || data.code.length > 8) {
      errors.push('Verification code must be between 4 and 8 characters');
    }

    if (!data.purpose) {
      errors.push('Purpose is required');
    } else if (!['registration', 'account_update', 'withdrawal', 'password_reset', 'telegram_connection'].includes(data.purpose)) {
      errors.push('Invalid purpose specified');
    }

    // Optional field validation
    if (data.expiryMinutes && (data.expiryMinutes < 1 || data.expiryMinutes > 1440)) {
      warnings.push('Expiry minutes should be between 1 and 1440 (24 hours)');
    }

    if (data.fullName && data.fullName.length > 100) {
      warnings.push('Full name is quite long');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate welcome email data
   */
  public validateWelcomeData(data: WelcomeEmailData): EmailValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required fields
    if (!data.email) {
      errors.push('Email address is required');
    } else if (!this.validateEmailAddress(data.email)) {
      errors.push('Invalid email address format');
    }

    if (!data.fullName) {
      errors.push('Full name is required');
    } else if (data.fullName.length < 2) {
      errors.push('Full name must be at least 2 characters');
    } else if (data.fullName.length > 100) {
      errors.push('Full name must be less than 100 characters');
    }

    if (!data.username) {
      errors.push('Username is required');
    } else if (data.username.length < 2) {
      errors.push('Username must be at least 2 characters');
    } else if (data.username.length > 50) {
      errors.push('Username must be less than 50 characters');
    }

    // Optional field validation
    if (data.sponsorUsername && data.sponsorUsername.length > 50) {
      warnings.push('Sponsor username is quite long');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate share purchase data
   */
  public validateSharePurchaseData(data: SharePurchaseConfirmationData): EmailValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required fields
    if (!data.email) {
      errors.push('Email address is required');
    } else if (!this.validateEmailAddress(data.email)) {
      errors.push('Invalid email address format');
    }

    if (!data.fullName) {
      errors.push('Full name is required');
    }

    if (!data.sharesPurchased || data.sharesPurchased <= 0) {
      errors.push('Shares purchased must be greater than 0');
    }

    if (!data.totalAmount || data.totalAmount <= 0) {
      errors.push('Total amount must be greater than 0');
    }

    if (!data.sharePrice || data.sharePrice <= 0) {
      errors.push('Share price must be greater than 0');
    }

    if (!data.phaseName) {
      errors.push('Phase name is required');
    }

    if (!data.transactionId) {
      errors.push('Transaction ID is required');
    }

    // Validation checks
    const calculatedTotal = data.sharesPurchased * data.sharePrice;
    if (Math.abs(calculatedTotal - data.totalAmount) > 0.01) {
      warnings.push('Total amount does not match shares × price calculation');
    }

    if (data.sharesPurchased > 100000) {
      warnings.push('Large share purchase - please verify amount');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate commission data
   */
  public validateCommissionData(data: CommissionEarnedData): EmailValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required fields
    if (!data.email) {
      errors.push('Email address is required');
    } else if (!this.validateEmailAddress(data.email)) {
      errors.push('Invalid email address format');
    }

    if (!data.fullName) {
      errors.push('Full name is required');
    }

    if (data.usdtCommission < 0) {
      errors.push('USDT commission cannot be negative');
    }

    if (data.shareCommission < 0) {
      errors.push('Share commission cannot be negative');
    }

    if (data.usdtCommission === 0 && data.shareCommission === 0) {
      errors.push('At least one commission type must be greater than 0');
    }

    if (!data.referredUserName) {
      errors.push('Referred user name is required');
    }

    if (!data.purchaseAmount || data.purchaseAmount <= 0) {
      errors.push('Purchase amount must be greater than 0');
    }

    if (!data.transactionId) {
      errors.push('Transaction ID is required');
    }

    // Validation warnings
    if (data.usdtCommission > data.purchaseAmount * 0.5) {
      warnings.push('USDT commission seems unusually high compared to purchase amount');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate migration data
   */
  public validateMigrationData(data: MigrationConfirmationData): EmailValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required fields
    if (!data.email) {
      errors.push('Email address is required');
    } else if (!this.validateEmailAddress(data.email)) {
      errors.push('Invalid email address format');
    }

    if (!data.fullName) {
      errors.push('Full name is required');
    }

    if (!data.username) {
      errors.push('Username is required');
    }

    if (!data.telegramUsername) {
      errors.push('Telegram username is required');
    }

    // Format validation
    if (data.username && (data.username.length < 3 || data.username.length > 50)) {
      errors.push('Username must be between 3 and 50 characters');
    }

    if (data.telegramUsername && !data.telegramUsername.startsWith('@')) {
      warnings.push('Telegram username should start with @');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate HTML content for potential issues
   */
  public validateHtmlContent(htmlContent: string): EmailValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!htmlContent || htmlContent.trim().length === 0) {
      errors.push('HTML content is required');
      return { isValid: false, errors, warnings };
    }

    // Check for potentially problematic content
    const problematicTags = ['script', 'iframe', 'object', 'embed', 'form'];
    for (const tag of problematicTags) {
      if (htmlContent.toLowerCase().includes(`<${tag}`)) {
        warnings.push(`Contains potentially problematic tag: ${tag}`);
      }
    }

    // Check for inline styles vs CSS classes
    const inlineStyleCount = (htmlContent.match(/style\s*=/gi) || []).length;
    if (inlineStyleCount > 20) {
      warnings.push('High number of inline styles - consider using CSS classes');
    }

    // Check content length
    if (htmlContent.length > 100000) {
      warnings.push('HTML content is quite large - may cause delivery issues');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate subject line
   */
  public validateSubjectLine(subject: string): EmailValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!subject || subject.trim().length === 0) {
      errors.push('Subject line is required');
    } else {
      if (subject.length > 78) {
        warnings.push('Subject line is longer than recommended (78 characters)');
      }

      if (subject.length < 10) {
        warnings.push('Subject line is quite short');
      }

      // Check for spam trigger words
      const spamWords = ['free', 'urgent', 'act now', 'limited time', 'click here'];
      const lowerSubject = subject.toLowerCase();
      for (const word of spamWords) {
        if (lowerSubject.includes(word)) {
          warnings.push(`Subject contains potential spam trigger word: "${word}"`);
        }
      }

      // Check for excessive punctuation
      if ((subject.match(/[!?]/g) || []).length > 2) {
        warnings.push('Subject line contains excessive punctuation');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}
