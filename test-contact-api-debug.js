/**
 * Debug script to test contact API functionality
 */

import axios from 'axios';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const BASE_URL = 'http://localhost:8002';

async function testContactAPI() {
  console.log('🧪 Testing Contact API Debug...');
  
  // Test 1: Normal input
  console.log('\n1. Testing normal input:');
  try {
    const response = await axios.post(`${BASE_URL}/api/contact`, {
      name: '<PERSON>',
      surname: '<PERSON><PERSON>', 
      email: '<EMAIL>',
      message: 'This is a test message'
    }, {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.log('Status:', error.response?.status || 'ERROR');
    console.log('Response:', JSON.stringify(error.response?.data || error.message, null, 2));
  }
  
  // Test 2: Check environment variables
  console.log('\n2. Environment Variables Check:');
  console.log('SUPABASE_URL:', process.env.SUPABASE_URL ? 'SET' : 'NOT SET');
  console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? 'SET' : 'NOT SET');
  console.log('RESEND_API_KEY:', process.env.RESEND_API_KEY ? 'SET' : 'NOT SET');
  
  console.log('\n🎯 Contact API debug completed!');
}

testContactAPI().catch(console.error);
