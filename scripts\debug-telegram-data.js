import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function debugTelegramData() {
  console.log('🔍 Debugging Telegram data relationships...');

  try {
    // First, let's check the telegram_users table structure and data
    console.log('\n📋 1. Checking telegram_users table...');
    const { data: telegramUsers, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .limit(5);

    if (telegramError) {
      console.error('❌ Error fetching telegram_users:', telegramError);
    } else {
      console.log(`✅ Found ${telegramUsers.length} telegram users:`);
      telegramUsers.forEach((user, index) => {
        console.log(`  ${index + 1}. ID: ${user.id}, Username: ${user.username}, User ID: ${user.user_id}`);
      });
    }

    // Check users table and their telegram connections
    console.log('\n📋 2. Checking users with telegram connections...');
    const { data: usersWithTelegram, error: usersError } = await supabase
      .from('users')
      .select(`
        id,
        username,
        email,
        full_name,
        telegram_users(
          username,
          first_name,
          last_name
        )
      `)
      .limit(5);

    if (usersError) {
      console.error('❌ Error fetching users with telegram:', usersError);
    } else {
      console.log(`✅ Found ${usersWithTelegram.length} users:`);
      usersWithTelegram.forEach((user, index) => {
        console.log(`  ${index + 1}. ${user.full_name || user.username || user.email}`);
        console.log(`     Telegram: ${user.telegram_users?.username || 'Not connected'}`);
      });
    }

    // Now let's test the exact query used in PaymentManager
    console.log('\n📋 3. Testing PaymentManager query...');
    const { data: payments, error: paymentsError } = await supabase
      .from('crypto_payment_transactions')
      .select(`
        *,
        users!inner(
          id,
          username,
          email,
          full_name,
          phone_number,
          telegram_users(
            username,
            first_name,
            last_name
          )
        )
      `)
      .limit(3);

    if (paymentsError) {
      console.error('❌ Error with PaymentManager query:', paymentsError);
    } else {
      console.log(`✅ PaymentManager query successful! Found ${payments.length} payments:`);
      payments.forEach((payment, index) => {
        console.log(`\n  Payment ${index + 1}:`);
        console.log(`    💎 Amount: $${payment.amount}`);
        console.log(`    👤 User: ${payment.users?.full_name || payment.users?.username || 'Unknown'}`);
        console.log(`    📧 Email: ${payment.users?.email || 'No email'}`);
        console.log(`    📱 Phone: ${payment.users?.phone_number || 'No phone'}`);
        console.log(`    💬 Telegram: ${payment.users?.telegram_users?.username || 'No Telegram'}`);
        
        // Show the full structure for debugging
        console.log(`    🔍 Full telegram_users data:`, JSON.stringify(payment.users?.telegram_users, null, 2));
      });
    }

    // Let's also check if there's a direct relationship we can use
    console.log('\n📋 4. Checking payment user IDs against telegram_users...');
    const { data: paymentUserIds, error: paymentIdsError } = await supabase
      .from('crypto_payment_transactions')
      .select('user_id')
      .limit(5);

    if (!paymentIdsError && paymentUserIds.length > 0) {
      const userIds = paymentUserIds.map(p => p.user_id);
      console.log(`🔍 Payment user IDs: ${userIds.join(', ')}`);
      
      const { data: telegramForUsers, error: telegramForUsersError } = await supabase
        .from('telegram_users')
        .select('user_id, username, first_name, last_name')
        .in('user_id', userIds);

      if (telegramForUsersError) {
        console.error('❌ Error checking telegram for payment users:', telegramForUsersError);
      } else {
        console.log(`✅ Telegram accounts for payment users:`);
        telegramForUsers.forEach(tg => {
          console.log(`  User ID ${tg.user_id}: @${tg.username} (${tg.first_name} ${tg.last_name})`);
        });
      }
    }

    console.log('\n🎯 Summary:');
    console.log('- If telegram_users data shows up in step 2 but not step 3, there might be a relationship issue');
    console.log('- If no telegram_users data in step 2, the relationship might not be set up correctly');
    console.log('- Check the foreign key relationship between users and telegram_users tables');

  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

debugTelegramData();
