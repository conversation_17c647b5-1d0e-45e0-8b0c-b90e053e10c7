import React, { useState, useEffect } from 'react';
import SharePurchaseFlow from '../SharePurchaseFlow';

interface MobilePurchaseFlowTestProps {
  user?: any;
}

export const MobilePurchaseFlowTest: React.FC<MobilePurchaseFlowTestProps> = ({ user }) => {
  const [showPurchaseFlow, setShowPurchaseFlow] = useState(false);
  const [deviceInfo, setDeviceInfo] = useState({
    isMobile: false,
    isTablet: false,
    isDesktop: false,
    screenWidth: 0,
    screenHeight: 0,
    userAgent: '',
    touchSupport: false
  });

  useEffect(() => {
    const updateDeviceInfo = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      const userAgent = navigator.userAgent;
      const touchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

      setDeviceInfo({
        isMobile: width <= 768,
        isTablet: width > 768 && width <= 1024,
        isDesktop: width > 1024,
        screenWidth: width,
        screenHeight: height,
        userAgent,
        touchSupport
      });
    };

    updateDeviceInfo();
    window.addEventListener('resize', updateDeviceInfo);
    window.addEventListener('orientationchange', updateDeviceInfo);

    return () => {
      window.removeEventListener('resize', updateDeviceInfo);
      window.removeEventListener('orientationchange', updateDeviceInfo);
    };
  }, []);

  const testScenarios = [
    {
      name: 'Quick Purchase - $100',
      amount: '100',
      description: 'Test quick amount selection'
    },
    {
      name: 'Large Purchase - $5000',
      amount: '5000',
      description: 'Test large amount handling'
    },
    {
      name: 'Custom Amount - $1337',
      amount: '1337',
      description: 'Test custom amount input'
    }
  ];

  return (
    <div style={{
      padding: '20px',
      backgroundColor: 'rgba(17, 24, 39, 0.95)',
      minHeight: '100vh',
      color: 'white'
    }}>
      <div style={{
        maxWidth: '800px',
        margin: '0 auto'
      }}>
        <h1 style={{
          fontSize: '24px',
          fontWeight: 'bold',
          marginBottom: '20px',
          textAlign: 'center',
          color: '#f59e0b'
        }}>
          📱 Mobile Purchase Flow Test
        </h1>

        {/* Device Information */}
        <div style={{
          backgroundColor: 'rgba(31, 41, 55, 0.9)',
          borderRadius: '12px',
          padding: '20px',
          marginBottom: '20px',
          border: '1px solid #374151'
        }}>
          <h2 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px', color: '#60a5fa' }}>
            🔍 Device Information
          </h2>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '12px',
            marginBottom: '16px'
          }}>
            <div style={{
              padding: '12px',
              backgroundColor: deviceInfo.isMobile ? 'rgba(16, 185, 129, 0.2)' : 'rgba(55, 65, 81, 0.5)',
              borderRadius: '8px',
              border: deviceInfo.isMobile ? '1px solid #10b981' : '1px solid #4b5563'
            }}>
              <div style={{ fontSize: '14px', fontWeight: '600', color: deviceInfo.isMobile ? '#10b981' : '#9ca3af' }}>
                📱 Mobile
              </div>
              <div style={{ fontSize: '12px', color: '#9ca3af' }}>
                ≤ 768px
              </div>
            </div>

            <div style={{
              padding: '12px',
              backgroundColor: deviceInfo.isTablet ? 'rgba(16, 185, 129, 0.2)' : 'rgba(55, 65, 81, 0.5)',
              borderRadius: '8px',
              border: deviceInfo.isTablet ? '1px solid #10b981' : '1px solid #4b5563'
            }}>
              <div style={{ fontSize: '14px', fontWeight: '600', color: deviceInfo.isTablet ? '#10b981' : '#9ca3af' }}>
                📟 Tablet
              </div>
              <div style={{ fontSize: '12px', color: '#9ca3af' }}>
                769px - 1024px
              </div>
            </div>

            <div style={{
              padding: '12px',
              backgroundColor: deviceInfo.isDesktop ? 'rgba(16, 185, 129, 0.2)' : 'rgba(55, 65, 81, 0.5)',
              borderRadius: '8px',
              border: deviceInfo.isDesktop ? '1px solid #10b981' : '1px solid #4b5563'
            }}>
              <div style={{ fontSize: '14px', fontWeight: '600', color: deviceInfo.isDesktop ? '#10b981' : '#9ca3af' }}>
                🖥️ Desktop
              </div>
              <div style={{ fontSize: '12px', color: '#9ca3af' }}>
                > 1024px
              </div>
            </div>
          </div>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
            gap: '12px',
            fontSize: '14px',
            color: '#d1d5db'
          }}>
            <div>
              <strong>Screen:</strong> {deviceInfo.screenWidth} × {deviceInfo.screenHeight}
            </div>
            <div>
              <strong>Touch:</strong> {deviceInfo.touchSupport ? '✅ Supported' : '❌ Not Supported'}
            </div>
          </div>
        </div>

        {/* Test Scenarios */}
        <div style={{
          backgroundColor: 'rgba(31, 41, 55, 0.9)',
          borderRadius: '12px',
          padding: '20px',
          marginBottom: '20px',
          border: '1px solid #374151'
        }}>
          <h2 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px', color: '#60a5fa' }}>
            🧪 Test Scenarios
          </h2>
          
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {testScenarios.map((scenario, index) => (
              <button
                key={index}
                onClick={() => setShowPurchaseFlow(true)}
                style={{
                  padding: '16px',
                  backgroundColor: 'rgba(59, 130, 246, 0.1)',
                  border: '2px solid rgba(59, 130, 246, 0.3)',
                  borderRadius: '12px',
                  color: '#60a5fa',
                  fontSize: '16px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  textAlign: 'left',
                  transition: 'all 0.2s ease',
                  minHeight: '56px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
              >
                <div>
                  <div style={{ fontWeight: 'bold' }}>{scenario.name}</div>
                  <div style={{ fontSize: '12px', color: '#9ca3af' }}>{scenario.description}</div>
                </div>
                <span style={{ fontSize: '20px' }}>▶️</span>
              </button>
            ))}
          </div>
        </div>

        {/* Mobile Optimization Checklist */}
        <div style={{
          backgroundColor: 'rgba(31, 41, 55, 0.9)',
          borderRadius: '12px',
          padding: '20px',
          marginBottom: '20px',
          border: '1px solid #374151'
        }}>
          <h2 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px', color: '#60a5fa' }}>
            ✅ Mobile Optimization Checklist
          </h2>
          
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', fontSize: '14px' }}>
            {[
              'Touch-friendly button sizes (min 44px height)',
              'Responsive grid layouts',
              'Optimized input fields (no zoom on iOS)',
              'Smooth scrolling and animations',
              'Proper viewport meta tag',
              'Fast loading and minimal bundle size',
              'Accessible focus indicators',
              'Gesture support (swipe, pinch)',
              'Orientation change handling',
              'Safe area considerations (notch, etc.)'
            ].map((item, index) => (
              <div key={index} style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                color: '#10b981'
              }}>
                <span>✅</span>
                <span>{item}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Launch Test Button */}
        <button
          onClick={() => setShowPurchaseFlow(true)}
          style={{
            width: '100%',
            padding: '20px',
            backgroundColor: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
            border: 'none',
            borderRadius: '16px',
            color: 'white',
            fontSize: '20px',
            fontWeight: 'bold',
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            minHeight: '64px'
          }}
        >
          🚀 Launch Mobile Purchase Flow Test
        </button>

        {/* Performance Metrics */}
        <div style={{
          backgroundColor: 'rgba(31, 41, 55, 0.9)',
          borderRadius: '12px',
          padding: '20px',
          marginTop: '20px',
          border: '1px solid #374151'
        }}>
          <h2 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px', color: '#60a5fa' }}>
            📊 Performance Metrics
          </h2>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
            gap: '12px',
            fontSize: '14px'
          }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ color: '#10b981', fontSize: '20px', fontWeight: 'bold' }}>
                &lt; 3s
              </div>
              <div style={{ color: '#9ca3af', fontSize: '12px' }}>Load Time</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ color: '#10b981', fontSize: '20px', fontWeight: 'bold' }}>
                60fps
              </div>
              <div style={{ color: '#9ca3af', fontSize: '12px' }}>Animations</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ color: '#10b981', fontSize: '20px', fontWeight: 'bold' }}>
                &lt; 100ms
              </div>
              <div style={{ color: '#9ca3af', fontSize: '12px' }}>Touch Response</div>
            </div>
          </div>
        </div>
      </div>

      {/* Purchase Flow Modal */}
      {showPurchaseFlow && (
        <SharePurchaseFlow
          user={user}
          onClose={() => setShowPurchaseFlow(false)}
        />
      )}
    </div>
  );
};
