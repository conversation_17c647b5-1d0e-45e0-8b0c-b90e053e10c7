import React, { useState } from 'react';
import { ComprehensiveDividendsCalculator } from '../components/dividends/ComprehensiveDividendsCalculator';

interface CalculatorPageProps {
  onNavigate: (page: string) => void;
}

const CalculatorPage: React.FC<CalculatorPageProps> = ({ onNavigate }) => {
  const [activeTab, setActiveTab] = useState<string>('calculator');

  // Constants for proper dividend calculations
  const TOTAL_SHARES = 1400000;
  const HECTARES_PER_PLANT = 25;
  const PLANT_CAPACITY_TPH = 200;
  const EFFECTIVE_HOURS_PER_DAY = 20;
  const OPERATING_DAYS_PER_YEAR = 330;
  const BULK_DENSITY_T_PER_M3 = 1.8;
  const GOLD_PRICE_USD_PER_KG = 120000;
  const OPEX_PERCENT = 45;
  const IN_SITU_GRADE = 0.9;
  const RECOVERY_FACTOR = 70;

  // Expansion plan
  const EXPANSION_PLAN = {
    2026: { plants: 10, hectares: 250 },
    2027: { plants: 25, hectares: 625 },
    2028: { plants: 50, hectares: 1250 },
    2029: { plants: 100, hectares: 2500 },
    2030: { plants: 200, hectares: 5000 }
  };

  // Function to calculate dividends for a given number of shares and year
  const calculateDividends = (shares: number, year: number) => {
    const planData = EXPANSION_PLAN[year];
    if (!planData) return { annual: 0, total5Year: 0, roi: 0 };

    const numPlants = planData.plants;
    const annualThroughputT = numPlants * PLANT_CAPACITY_TPH * EFFECTIVE_HOURS_PER_DAY * OPERATING_DAYS_PER_YEAR;
    const annualGoldKg = (annualThroughputT * (IN_SITU_GRADE / BULK_DENSITY_T_PER_M3) * (RECOVERY_FACTOR / 100)) / 1000;
    const annualRevenue = annualGoldKg * GOLD_PRICE_USD_PER_KG;
    const annualOperatingCost = annualRevenue * (OPEX_PERCENT / 100);
    const annualEbit = annualRevenue - annualOperatingCost;
    const dividendPerShare = TOTAL_SHARES > 0 ? annualEbit / TOTAL_SHARES : 0;
    const userAnnualDividend = dividendPerShare * shares;

    return {
      annual: userAnnualDividend,
      dividendPerShare: dividendPerShare,
      revenue: annualRevenue,
      ebit: annualEbit,
      goldKg: annualGoldKg
    };
  };

  // Calculate 5-year totals for scenarios
  const calculateScenario = (shares: number, investment: number) => {
    const years = [2026, 2027, 2028, 2029, 2030];
    let totalDividends = 0;
    const yearlyDividends = {};

    years.forEach(year => {
      const result = calculateDividends(shares, year);
      totalDividends += result.annual;
      yearlyDividends[year] = result.annual;
    });

    const roi = investment > 0 ? (totalDividends / investment) * 100 : 0;

    return {
      totalDividends,
      yearlyDividends,
      roi,
      firstYear: yearlyDividends[2026],
      lastYear: yearlyDividends[2030]
    };
  };

  // Calculate scenarios
  const conservativeScenario = calculateScenario(200, 1000);
  const growthScenario = calculateScenario(1000, 5000);
  const seriousScenario = calculateScenario(5000, 25000);

  const tabs = [
    { id: 'calculator', label: 'Dividend Calculator', icon: '🧮' },
    { id: 'scenarios', label: 'Shareholder Scenarios', icon: '📊' },
    { id: 'projections', label: '5-Year Projections', icon: '📈' },
    { id: 'comparison', label: 'Compare Options', icon: '⚖️' },
    { id: 'next-steps', label: 'Next Steps', icon: '🚀' }
  ];

  return (
    <div className="page calculator-page-tabbed">
      {/* Tabbed Page Header */}
      <section className="tabbed-page-header">
        <div className="container">
          <div className="breadcrumb">
            <button onClick={() => onNavigate('home')} className="breadcrumb-link">
              Home
            </button>
            <span className="breadcrumb-separator">→</span>
            <span className="breadcrumb-current">Returns Calculator</span>
          </div>
          
          <div className="header-content">
            <div className="header-text">
              <h1 className="page-title">Dividend Calculator</h1>
              <p className="page-subtitle">
                Professional analysis tools for calculating your potential dividends on profits from gold mining operations
              </p>
            </div>
            <div className="header-stats">
              <div className="stat-item">
                <span className="stat-value">$120,000</span>
                <span className="stat-label">Gold Price/kg</span>
              </div>
              <div className="stat-item">
                <span className="stat-value">200</span>
                <span className="stat-label">Max Plants 2030</span>
              </div>
              <div className="stat-item">
                <span className="stat-value">91.2%</span>
                <span className="stat-label">5-Year ROI</span>
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="tab-navigation">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
                onClick={() => setActiveTab(tab.id)}
              >
                <span className="tab-icon">{tab.icon}</span>
                <span className="tab-label">{tab.label}</span>
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Tab Content */}
      <section className="tab-content-section">
        <div className="container">
          <div className="tab-content">
            
            {/* Calculator Tab */}
            {activeTab === 'calculator' && (
              <div className="tab-panel calculator-panel">
                <div className="panel-header">
                  <h2>Interactive Dividend Calculator</h2>
                  <p>Calculate your potential dividends on profits based on share ownership and production forecasts</p>
                </div>
                <ComprehensiveDividendsCalculator userShares={1000} />
              </div>
            )}

            {/* Professional Scenarios Tab */}
            {activeTab === 'scenarios' && (
              <div className="tab-panel scenarios-panel">
                <div className="panel-header">
                  <h2>Shareholder Portfolio Analysis</h2>
                  <p>Professional shareholder scenarios with comprehensive dividend projections and analysis</p>
                </div>

                {/* Share Purchase Overview */}
                <div className="investment-overview">
                  <div className="overview-header">
                    <div className="overview-icon">📊</div>
                    <div className="overview-content">
                      <h3>Shareholder Portfolio Overview</h3>
                      <p>Choose your share ownership approach based on capital allocation and dividend objectives</p>
                    </div>
                  </div>

                  <div className="overview-metrics">
                    <div className="overview-metric">
                      <div className="metric-icon">🎯</div>
                      <div className="metric-content">
                        <div className="metric-value">High</div>
                        <div className="metric-label">Dividend Growth</div>
                        <div className="metric-description">Conservative projection</div>
                      </div>
                    </div>

                    <div className="overview-metric">
                      <div className="metric-icon">💰</div>
                      <div className="metric-content">
                        <div className="metric-value">$5.00</div>
                        <div className="metric-label">Current Price</div>
                        <div className="metric-description">Presale opportunity</div>
                      </div>
                    </div>

                    <div className="overview-metric">
                      <div className="metric-icon">📈</div>
                      <div className="metric-content">
                        <div className="metric-value">20x</div>
                        <div className="metric-label">Growth Multiple</div>
                        <div className="metric-description">Operational scaling</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Professional Share Purchase Scenarios */}
                <div className="professional-scenarios">
                  <div className="scenarios-header">
                    <h3>Shareholder Strategy Comparison</h3>
                    <p>Detailed analysis of three distinct share ownership approaches tailored to different shareholder profiles</p>
                  </div>

                  <div className="professional-scenarios-grid">
                    {/* Conservative Portfolio */}
                    <div className="professional-scenario-card conservative">
                      <div className="scenario-header">
                        <div className="scenario-badge conservative">
                          <div className="badge-icon">🛡️</div>
                          <div className="badge-content">
                            <div className="badge-title">Conservative</div>
                            <div className="badge-subtitle">Low Risk • Steady Growth</div>
                          </div>
                        </div>
                        <div className="risk-indicator low">
                          <div className="risk-label">Risk Level</div>
                          <div className="risk-bars">
                            <div className="risk-bar active"></div>
                            <div className="risk-bar"></div>
                            <div className="risk-bar"></div>
                          </div>
                        </div>
                      </div>

                      <div className="scenario-investment">
                        <div className="investment-header">
                          <div className="investment-title">SHARE PURCHASE</div>
                          <div className="investment-amount">$1,000</div>
                        </div>
                        <div className="investment-details">
                          <div className="detail-item">
                            <span className="detail-label">Share Count:</span>
                            <span className="detail-value">200 shares</span>
                          </div>
                          <div className="detail-item">
                            <span className="detail-label">Price per Share:</span>
                            <span className="detail-value">$5.00</span>
                          </div>
                          <div className="detail-item">
                            <span className="detail-label">Portfolio Weight:</span>
                            <span className="detail-value">0.014%</span>
                          </div>
                        </div>
                      </div>

                      <div className="scenario-projections">
                        <div className="projections-header">
                          <h4>Dividend Calculation Formula</h4>
                          <p className="formula-explanation">
                            Annual Profit ÷ 1,400,000 Total Shares = Dividend Per Share<br/>
                            Your Dividend = Dividend Per Share × Your Shares (200)
                          </p>
                        </div>

                        <div className="dividend-breakdown">
                          <div className="breakdown-year">
                            <div className="year-header">2026 (10 Plants)</div>
                            <div className="dividend-calculation">
                              <div className="calc-step">Dividend Per Share: ${(conservativeScenario.firstYear / 200).toFixed(2)}</div>
                              <div className="calc-step">Your 200 Shares: ${Math.round(conservativeScenario.firstYear).toLocaleString()}</div>
                            </div>
                          </div>

                          <div className="breakdown-year">
                            <div className="year-header">2030 (200 Plants)</div>
                            <div className="dividend-calculation">
                              <div className="calc-step">Dividend Per Share: ${(conservativeScenario.lastYear / 200).toFixed(2)}</div>
                              <div className="calc-step">Your 200 Shares: ${Math.round(conservativeScenario.lastYear).toLocaleString()}</div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="scenario-benefits">
                        <div className="benefits-header">Key Benefits</div>
                        <div className="benefits-list">
                          <div className="benefit-item">
                            <span className="benefit-icon">✓</span>
                            <span className="benefit-text">Low share purchase barrier</span>
                          </div>
                          <div className="benefit-item">
                            <span className="benefit-icon">✓</span>
                            <span className="benefit-text">Conservative shareholding</span>
                          </div>
                          <div className="benefit-item">
                            <span className="benefit-icon">✓</span>
                            <span className="benefit-text">Steady dividend growth</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Growth Portfolio */}
                    <div className="professional-scenario-card growth featured">
                      <div className="featured-badge">
                        <div className="featured-icon">⭐</div>
                        <div className="featured-text">Most Popular Choice</div>
                      </div>

                      <div className="scenario-header">
                        <div className="scenario-badge growth">
                          <div className="badge-icon">🚀</div>
                          <div className="badge-content">
                            <div className="badge-title">Growth</div>
                            <div className="badge-subtitle">Balanced Risk • High Potential</div>
                          </div>
                        </div>
                        <div className="risk-indicator medium">
                          <div className="risk-label">Risk Level</div>
                          <div className="risk-bars">
                            <div className="risk-bar active"></div>
                            <div className="risk-bar active"></div>
                            <div className="risk-bar"></div>
                          </div>
                        </div>
                      </div>

                      <div className="scenario-investment">
                        <div className="investment-header">
                          <div className="investment-title">SHARE PURCHASE</div>
                          <div className="investment-amount">$5,000</div>
                        </div>
                        <div className="investment-details">
                          <div className="detail-item">
                            <span className="detail-label">Share Count:</span>
                            <span className="detail-value">1,000 shares</span>
                          </div>
                          <div className="detail-item">
                            <span className="detail-label">Price per Share:</span>
                            <span className="detail-value">$5.00</span>
                          </div>
                          <div className="detail-item">
                            <span className="detail-label">Portfolio Weight:</span>
                            <span className="detail-value">0.071%</span>
                          </div>
                        </div>
                      </div>

                      <div className="scenario-projections">
                        <div className="projections-header">
                          <h4>Dividend Calculation Formula</h4>
                          <p className="formula-explanation">
                            Annual Profit ÷ 1,400,000 Total Shares = Dividend Per Share<br/>
                            Your Dividend = Dividend Per Share × Your Shares (1,000)
                          </p>
                        </div>

                        <div className="dividend-breakdown">
                          <div className="breakdown-year">
                            <div className="year-header">2026 (10 Plants)</div>
                            <div className="dividend-calculation">
                              <div className="calc-step">Dividend Per Share: ${(growthScenario.firstYear / 1000).toFixed(2)}</div>
                              <div className="calc-step">Your 1,000 Shares: ${Math.round(growthScenario.firstYear).toLocaleString()}</div>
                            </div>
                          </div>

                          <div className="breakdown-year">
                            <div className="year-header">2030 (200 Plants)</div>
                            <div className="dividend-calculation">
                              <div className="calc-step">Dividend Per Share: ${(growthScenario.lastYear / 1000).toFixed(2)}</div>
                              <div className="calc-step">Your 1,000 Shares: ${Math.round(growthScenario.lastYear).toLocaleString()}</div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="scenario-benefits">
                        <div className="benefits-header">Key Benefits</div>
                        <div className="benefits-list">
                          <div className="benefit-item">
                            <span className="benefit-icon">✓</span>
                            <span className="benefit-text">Balanced shareholding approach</span>
                          </div>
                          <div className="benefit-item">
                            <span className="benefit-icon">✓</span>
                            <span className="benefit-text">Significant dividend income</span>
                          </div>
                          <div className="benefit-item">
                            <span className="benefit-icon">✓</span>
                            <span className="benefit-text">Equity portfolio growth</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Serious Portfolio */}
                    <div className="professional-scenario-card serious">
                      <div className="scenario-header">
                        <div className="scenario-badge serious">
                          <div className="badge-icon">💎</div>
                          <div className="badge-content">
                            <div className="badge-title">Serious</div>
                            <div className="badge-subtitle">High Commitment • Maximum Dividends</div>
                          </div>
                        </div>
                        <div className="risk-indicator high">
                          <div className="risk-label">Risk Level</div>
                          <div className="risk-bars">
                            <div className="risk-bar active"></div>
                            <div className="risk-bar active"></div>
                            <div className="risk-bar active"></div>
                          </div>
                        </div>
                      </div>

                      <div className="scenario-investment">
                        <div className="investment-header">
                          <div className="investment-title">SHARE PURCHASE</div>
                          <div className="investment-amount">$25,000</div>
                        </div>
                        <div className="investment-details">
                          <div className="detail-item">
                            <span className="detail-label">Share Count:</span>
                            <span className="detail-value">5,000 shares</span>
                          </div>
                          <div className="detail-item">
                            <span className="detail-label">Price per Share:</span>
                            <span className="detail-value">$5.00</span>
                          </div>
                          <div className="detail-item">
                            <span className="detail-label">Portfolio Weight:</span>
                            <span className="detail-value">0.357%</span>
                          </div>
                        </div>
                      </div>

                      <div className="scenario-projections">
                        <div className="projections-header">
                          <h4>Dividend Calculation Formula</h4>
                          <p className="formula-explanation">
                            Annual Profit ÷ 1,400,000 Total Shares = Dividend Per Share<br/>
                            Your Dividend = Dividend Per Share × Your Shares (5,000)
                          </p>
                        </div>

                        <div className="dividend-breakdown">
                          <div className="breakdown-year">
                            <div className="year-header">2026 (10 Plants)</div>
                            <div className="dividend-calculation">
                              <div className="calc-step">Dividend Per Share: ${(seriousScenario.firstYear / 5000).toFixed(2)}</div>
                              <div className="calc-step">Your 5,000 Shares: ${Math.round(seriousScenario.firstYear).toLocaleString()}</div>
                            </div>
                          </div>

                          <div className="breakdown-year">
                            <div className="year-header">2030 (200 Plants)</div>
                            <div className="dividend-calculation">
                              <div className="calc-step">Dividend Per Share: ${(seriousScenario.lastYear / 5000).toFixed(2)}</div>
                              <div className="calc-step">Your 5,000 Shares: ${Math.round(seriousScenario.lastYear).toLocaleString()}</div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="scenario-benefits">
                        <div className="benefits-header">Key Benefits</div>
                        <div className="benefits-list">
                          <div className="benefit-item">
                            <span className="benefit-icon">✓</span>
                            <span className="benefit-text">Maximum equity ownership</span>
                          </div>
                          <div className="benefit-item">
                            <span className="benefit-icon">✓</span>
                            <span className="benefit-text">Substantial dividend income</span>
                          </div>
                          <div className="benefit-item">
                            <span className="benefit-icon">✓</span>
                            <span className="benefit-text">Long-term shareholder benefits</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Enhanced Projections Tab */}
            {activeTab === 'projections' && (
              <div className="tab-panel projections-panel">
                <div className="panel-header">
                  <h2>5-Year Growth Projections</h2>
                  <p>Comprehensive analysis of our strategic expansion plan and dividend on profits growth trajectory</p>
                </div>

                {/* Executive Summary */}
                <div className="projections-executive-summary">
                  <div className="summary-header">
                    <div className="summary-icon">📊</div>
                    <div className="summary-content">
                      <h3>Executive Summary</h3>
                      <p>Aureus Alliance Holdings' aggressive yet conservative expansion strategy targeting 20x operational growth</p>
                    </div>
                  </div>

                  <div className="summary-metrics">
                    <div className="metric-card">
                      <div className="metric-icon">🏭</div>
                      <div className="metric-content">
                        <div className="metric-value">200</div>
                        <div className="metric-label">Target Plants by 2030</div>
                        <div className="metric-growth">+1,900% growth</div>
                      </div>
                    </div>

                    <div className="metric-card">
                      <div className="metric-icon">🗺️</div>
                      <div className="metric-content">
                        <div className="metric-value">5,000</div>
                        <div className="metric-label">Total Hectares</div>
                        <div className="metric-growth">+1,900% expansion</div>
                      </div>
                    </div>

                    <div className="metric-card">
                      <div className="metric-icon">💰</div>
                      <div className="metric-content">
                        <div className="metric-value">$1.45</div>
                        <div className="metric-label">Peak Dividend/Share</div>
                        <div className="metric-growth">+1,900% increase</div>
                      </div>
                    </div>

                    <div className="metric-card">
                      <div className="metric-icon">📈</div>
                      <div className="metric-content">
                        <div className="metric-value">91.2%</div>
                        <div className="metric-label">5-Year ROI</div>
                        <div className="metric-growth">Conservative estimate</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Enhanced Timeline */}
                <div className="enhanced-projections-timeline">
                  <div className="timeline-header">
                    <div className="timeline-title">
                      <h3>Strategic Expansion Timeline</h3>
                      <p>Phase-by-phase operational scaling with detailed financial projections</p>
                    </div>
                    <div className="timeline-legend">
                      <div className="legend-item">
                        <div className="legend-color foundation"></div>
                        <span>Foundation Phase</span>
                      </div>
                      <div className="legend-item">
                        <div className="legend-color growth"></div>
                        <span>Growth Phase</span>
                      </div>
                      <div className="legend-item">
                        <div className="legend-color scale"></div>
                        <span>Scale Phase</span>
                      </div>
                      <div className="legend-item">
                        <div className="legend-color maturity"></div>
                        <span>Maturity Phase</span>
                      </div>
                    </div>
                  </div>

                  <div className="professional-timeline-grid">
                    {/* 2026 - Foundation */}
                    <div className="professional-timeline-year foundation">
                      <div className="year-badge">
                        <div className="year-number">2026</div>
                        <div className="year-phase">Foundation</div>
                      </div>

                      <div className="year-content">
                        <div className="operational-metrics">
                          <div className="metric-row">
                            <div className="metric-item">
                              <span className="metric-icon">🏭</span>
                              <span className="metric-text">10 Plants</span>
                            </div>
                            <div className="metric-item">
                              <span className="metric-icon">🗺️</span>
                              <span className="metric-text">250 Hectares</span>
                            </div>
                          </div>
                          <div className="metric-row">
                            <div className="metric-item">
                              <span className="metric-icon">🥇</span>
                              <span className="metric-text">4,620 kg Gold</span>
                            </div>
                            <div className="metric-item">
                              <span className="metric-icon">💵</span>
                              <span className="metric-text">$503.7M Revenue</span>
                            </div>
                          </div>
                        </div>

                        <div className="dividend-highlight">
                          <div className="dividend-label">Dividend per Share</div>
                          <div className="dividend-value">$0.0726</div>
                          <div className="dividend-description">Initial operational returns</div>
                        </div>

                        <div className="phase-description">
                          <p>Establishing operational foundation with proven mining technology and initial market presence</p>
                        </div>
                      </div>
                    </div>

                    {/* 2027 - Early Growth */}
                    <div className="professional-timeline-year growth">
                      <div className="year-badge">
                        <div className="year-number">2027</div>
                        <div className="year-phase">Early Growth</div>
                      </div>

                      <div className="year-content">
                        <div className="operational-metrics">
                          <div className="metric-row">
                            <div className="metric-item">
                              <span className="metric-icon">🏭</span>
                              <span className="metric-text">25 Plants</span>
                            </div>
                            <div className="metric-item">
                              <span className="metric-icon">🗺️</span>
                              <span className="metric-text">625 Hectares</span>
                            </div>
                          </div>
                          <div className="metric-row">
                            <div className="metric-item">
                              <span className="metric-icon">🥇</span>
                              <span className="metric-text">11,550 kg Gold</span>
                            </div>
                            <div className="metric-item">
                              <span className="metric-icon">💵</span>
                              <span className="metric-text">$1.26B Revenue</span>
                            </div>
                          </div>
                        </div>

                        <div className="dividend-highlight">
                          <div className="dividend-label">Dividend per Share</div>
                          <div className="dividend-value">$0.1815</div>
                          <div className="dividend-description">2.5x operational scaling</div>
                        </div>

                        <div className="phase-description">
                          <p>Rapid expansion phase leveraging proven operational model across multiple sites</p>
                        </div>
                      </div>
                    </div>

                    {/* 2028 - Acceleration */}
                    <div className="professional-timeline-year growth">
                      <div className="year-badge">
                        <div className="year-number">2028</div>
                        <div className="year-phase">Acceleration</div>
                      </div>

                      <div className="year-content">
                        <div className="operational-metrics">
                          <div className="metric-row">
                            <div className="metric-item">
                              <span className="metric-icon">🏭</span>
                              <span className="metric-text">50 Plants</span>
                            </div>
                            <div className="metric-item">
                              <span className="metric-icon">🗺️</span>
                              <span className="metric-text">1,250 Hectares</span>
                            </div>
                          </div>
                          <div className="metric-row">
                            <div className="metric-item">
                              <span className="metric-icon">🥇</span>
                              <span className="metric-text">23,100 kg Gold</span>
                            </div>
                            <div className="metric-item">
                              <span className="metric-icon">💵</span>
                              <span className="metric-text">$2.52B Revenue</span>
                            </div>
                          </div>
                        </div>

                        <div className="dividend-highlight">
                          <div className="dividend-label">Dividend per Share</div>
                          <div className="dividend-value">$0.363</div>
                          <div className="dividend-description">Double capacity milestone</div>
                        </div>

                        <div className="phase-description">
                          <p>Major scaling acceleration with optimized operations and enhanced efficiency protocols</p>
                        </div>
                      </div>
                    </div>

                    {/* 2029 - Scale */}
                    <div className="professional-timeline-year scale">
                      <div className="year-badge">
                        <div className="year-number">2029</div>
                        <div className="year-phase">Scale</div>
                      </div>

                      <div className="year-content">
                        <div className="operational-metrics">
                          <div className="metric-row">
                            <div className="metric-item">
                              <span className="metric-icon">🏭</span>
                              <span className="metric-text">100 Plants</span>
                            </div>
                            <div className="metric-item">
                              <span className="metric-icon">🗺️</span>
                              <span className="metric-text">2,500 Hectares</span>
                            </div>
                          </div>
                          <div className="metric-row">
                            <div className="metric-item">
                              <span className="metric-icon">🥇</span>
                              <span className="metric-text">46,200 kg Gold</span>
                            </div>
                            <div className="metric-item">
                              <span className="metric-icon">💵</span>
                              <span className="metric-text">$5.04B Revenue</span>
                            </div>
                          </div>
                        </div>

                        <div className="dividend-highlight">
                          <div className="dividend-label">Dividend per Share</div>
                          <div className="dividend-value">$0.726</div>
                          <div className="dividend-description">100-plant milestone achieved</div>
                        </div>

                        <div className="phase-description">
                          <p>Major operational milestone with industrial-scale gold production and market leadership</p>
                        </div>
                      </div>
                    </div>

                    {/* 2030 - Maturity */}
                    <div className="professional-timeline-year maturity">
                      <div className="year-badge">
                        <div className="year-number">2030</div>
                        <div className="year-phase">Maturity</div>
                      </div>

                      <div className="year-content">
                        <div className="operational-metrics">
                          <div className="metric-row">
                            <div className="metric-item">
                              <span className="metric-icon">🏭</span>
                              <span className="metric-text">200 Plants</span>
                            </div>
                            <div className="metric-item">
                              <span className="metric-icon">🗺️</span>
                              <span className="metric-text">5,000 Hectares</span>
                            </div>
                          </div>
                          <div className="metric-row">
                            <div className="metric-item">
                              <span className="metric-icon">🥇</span>
                              <span className="metric-text">92,400 kg Gold</span>
                            </div>
                            <div className="metric-item">
                              <span className="metric-icon">💵</span>
                              <span className="metric-text">$10.08B Revenue</span>
                            </div>
                          </div>
                        </div>

                        <div className="dividend-highlight">
                          <div className="dividend-label">Dividend per Share</div>
                          <div className="dividend-value">$1.452</div>
                          <div className="dividend-description">Peak operational capacity</div>
                        </div>

                        <div className="phase-description">
                          <p>Full operational maturity with maximum planned capacity and industry-leading returns</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Growth Analysis */}
                <div className="growth-analysis">
                  <div className="analysis-header">
                    <h3>Growth Analysis & Key Insights</h3>
                    <p>Comprehensive breakdown of expansion metrics and shareholder value creation</p>
                  </div>

                  <div className="analysis-grid">
                    <div className="analysis-card operational">
                      <div className="card-header">
                        <div className="card-icon">🏭</div>
                        <h4>Operational Scaling</h4>
                      </div>
                      <div className="card-content">
                        <div className="growth-stat">
                          <span className="stat-label">Plant Growth Rate:</span>
                          <span className="stat-value">20x increase</span>
                        </div>
                        <div className="growth-stat">
                          <span className="stat-label">Land Expansion:</span>
                          <span className="stat-value">5,000 hectares</span>
                        </div>
                        <div className="growth-stat">
                          <span className="stat-label">Production Capacity:</span>
                          <span className="stat-value">92.4 tonnes gold/year</span>
                        </div>
                      </div>
                    </div>

                    <div className="analysis-card financial">
                      <div className="card-header">
                        <div className="card-icon">💰</div>
                        <h4>Financial Performance</h4>
                      </div>
                      <div className="card-content">
                        <div className="growth-stat">
                          <span className="stat-label">Revenue Growth:</span>
                          <span className="stat-value">$10.08B by 2030</span>
                        </div>
                        <div className="growth-stat">
                          <span className="stat-label">Dividend Scaling:</span>
                          <span className="stat-value">20x multiplier</span>
                        </div>
                        <div className="growth-stat">
                          <span className="stat-label">5-Year ROI:</span>
                          <span className="stat-value">91.2% total return</span>
                        </div>
                      </div>
                    </div>

                    <div className="analysis-card strategic">
                      <div className="card-header">
                        <div className="card-icon">🎯</div>
                        <h4>Strategic Advantages</h4>
                      </div>
                      <div className="card-content">
                        <div className="growth-stat">
                          <span className="stat-label">Market Position:</span>
                          <span className="stat-value">Industry leader</span>
                        </div>
                        <div className="growth-stat">
                          <span className="stat-label">Operational Efficiency:</span>
                          <span className="stat-value">Proven scalability</span>
                        </div>
                        <div className="growth-stat">
                          <span className="stat-label">Risk Management:</span>
                          <span className="stat-value">Conservative estimates</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Comparison Tab */}
            {activeTab === 'comparison' && (
              <div className="tab-panel comparison-panel">
                <div className="panel-header">
                  <h2>Share Purchase Comparison</h2>
                  <p>Side-by-side comparison of all shareholder scenarios</p>
                </div>

                <div className="scenarios-comparison">
                  <h3 className="comparison-title">Shareholder Scenarios Comparison</h3>
                  <div className="comparison-table">
                    <div className="comparison-header">
                      <div className="comparison-cell">Scenario</div>
                      <div className="comparison-cell">Share Purchase</div>
                      <div className="comparison-cell">Shares</div>
                      <div className="comparison-cell">2026 Dividend</div>
                      <div className="comparison-cell">2030 Dividend</div>
                      <div className="comparison-cell">5-Year Total</div>
                    </div>

                    <div className="comparison-row conservative">
                      <div className="comparison-cell">Conservative</div>
                      <div className="comparison-cell">$1,000</div>
                      <div className="comparison-cell">200</div>
                      <div className="comparison-cell">${conservativeScenario.firstYear.toLocaleString('en-US', { maximumFractionDigits: 0 })}</div>
                      <div className="comparison-cell">${conservativeScenario.lastYear.toLocaleString('en-US', { maximumFractionDigits: 0 })}</div>
                      <div className="comparison-cell">${conservativeScenario.totalDividends.toLocaleString('en-US', { maximumFractionDigits: 0 })}</div>
                    </div>

                    <div className="comparison-row growth">
                      <div className="comparison-cell">Growth</div>
                      <div className="comparison-cell">$5,000</div>
                      <div className="comparison-cell">1,000</div>
                      <div className="comparison-cell">${growthScenario.firstYear.toLocaleString('en-US', { maximumFractionDigits: 0 })}</div>
                      <div className="comparison-cell">${growthScenario.lastYear.toLocaleString('en-US', { maximumFractionDigits: 0 })}</div>
                      <div className="comparison-cell">${growthScenario.totalDividends.toLocaleString('en-US', { maximumFractionDigits: 0 })}</div>
                    </div>

                    <div className="comparison-row serious">
                      <div className="comparison-cell">Serious</div>
                      <div className="comparison-cell">$25,000</div>
                      <div className="comparison-cell">5,000</div>
                      <div className="comparison-cell">${seriousScenario.firstYear.toLocaleString('en-US', { maximumFractionDigits: 0 })}</div>
                      <div className="comparison-cell">${seriousScenario.lastYear.toLocaleString('en-US', { maximumFractionDigits: 0 })}</div>
                      <div className="comparison-cell">${seriousScenario.totalDividends.toLocaleString('en-US', { maximumFractionDigits: 0 })}</div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Next Steps Tab */}
            {activeTab === 'next-steps' && (
              <div className="tab-panel next-steps-panel">
                <div className="panel-header">
                  <h2>Ready to Proceed?</h2>
                  <p>Take the next step in your gold mining share purchase journey</p>
                </div>

                <div className="enhanced-next-steps">
                  <div className="enhanced-next-steps-grid">
                    <div className="next-step-card-wrapper">
                      <button
                        className="enhanced-next-step-card explore"
                        onClick={() => onNavigate('financial-data')}
                      >
                        <div className="card-header">
                          <div className="step-icon">📊</div>
                          <div className="step-badge">Explore</div>
                        </div>
                        <div className="card-content">
                          <h3>View Financial Data</h3>
                          <p>Comprehensive financial tables and production forecasts</p>
                        </div>
                        <div className="card-footer">
                          <span className="action-text">Explore Data</span>
                          <span className="action-arrow">→</span>
                        </div>
                      </button>
                    </div>

                    <div className="next-step-card-wrapper">
                      <button
                        className="enhanced-next-step-card review"
                        onClick={() => onNavigate('investment-phases')}
                      >
                        <div className="card-header">
                          <div className="step-icon">📈</div>
                          <div className="step-badge">Review</div>
                        </div>
                        <div className="card-content">
                          <h3>Review Share Phases</h3>
                          <p>Complete breakdown of all share phases</p>
                        </div>
                        <div className="card-footer">
                          <span className="action-text">Review Phases</span>
                          <span className="action-arrow">→</span>
                        </div>
                      </button>
                    </div>

                    <div className="next-step-card-wrapper featured">
                      <div className="featured-badge">Recommended</div>
                      <button
                        className="enhanced-next-step-card invest featured"
                        onClick={() => alert('Coming Soon - Registration')}
                      >
                        <div className="card-header">
                          <div className="step-icon">🚀</div>
                          <div className="step-badge">Purchase</div>
                        </div>
                        <div className="card-content">
                          <h3>Start Your Share Purchase</h3>
                          <p>Begin your shareholder registration</p>
                        </div>
                        <div className="card-footer">
                          <span className="action-text">Start Purchasing</span>
                          <span className="action-arrow">🚀</span>
                        </div>
                      </button>
                    </div>
                  </div>

                  <div className="next-steps-footer">
                    <div className="footer-content">
                      <div className="footer-stats">
                        <div className="stat-item">
                          <span className="stat-value">$5.00</span>
                          <span className="stat-label">Current Share Price</span>
                        </div>
                        <div className="stat-item">
                          <span className="stat-value">200</span>
                          <span className="stat-label">Max Plants by 2030</span>
                        </div>
                        <div className="stat-item">
                          <span className="stat-value">91.2%</span>
                          <span className="stat-label">5-Year ROI</span>
                        </div>
                      </div>
                      <div className="footer-cta">
                        <p className="cta-text">
                          Join thousands of shareholders building wealth through real gold mining operations
                        </p>
                        <div className="urgency-indicator">
                          <span className="urgency-icon">⏰</span>
                          <span className="urgency-text">Presale pricing available for limited time</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

          </div>
        </div>
      </section>

      {/* Enhanced Disclaimer */}
      <section className="enhanced-disclaimer">
        <div className="container">
          <div className="disclaimer-header">
            <div className="disclaimer-icon">⚠️</div>
            <h3 className="disclaimer-title">Important Shareholder Disclaimer</h3>
          </div>

          <div className="disclaimer-content">
            <p>
              <strong>Shareholder Notice:</strong> All calculations and projections are based on current
              operational data, market conditions, and conservative estimates. Past dividend payments do not
              guarantee future dividend payments.
            </p>

            <div className="risk-factors">
              <h5>Key Risk Factors:</h5>
              <ul>
                <li>Gold prices are subject to market volatility and may fluctuate significantly</li>
                <li>Mining operations face inherent risks including weather, equipment, and regulatory changes</li>
                <li>Dividend payments depend on operational profitability and company performance</li>
                <li>Dividend payments are not guaranteed and may vary from projections</li>
              </ul>
            </div>

            <div className="disclaimer-footer">
              <p>
                <strong>Professional Advice:</strong> Please consult with qualified financial advisors
                before making share purchase decisions. This calculator is for informational purposes only.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default CalculatorPage;
