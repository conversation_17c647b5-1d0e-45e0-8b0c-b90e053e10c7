/**
 * MIGRATION EMAIL TEMPLATE
 * 
 * Template for Telegram-to-Website migration confirmation emails
 * sent after successful account migration.
 */

import { BaseEmailTemplate } from './BaseEmailTemplate';
import { MigrationConfirmationData } from '../types/EmailTypes';

export class MigrationTemplate extends BaseEmailTemplate<MigrationConfirmationData> {
  protected emailType = 'migration_confirmation' as const;

  protected generateSubject(data: MigrationConfirmationData): string {
    return `Account Migration Successful - Welcome to the Website!`;
  }

  protected generateBody(data: MigrationConfirmationData): string {
    return `
      <div class="email-body">
        <h2 style="color: ${this.brandingConfig.colors.primary}; margin-bottom: 20px; text-align: center;">
          🎉 Migration Successful!
        </h2>
        
        <p style="margin-bottom: 15px; color: ${this.brandingConfig.colors.text}; font-size: 18px;">
          Hello ${data.fullName},
        </p>
        
        <p style="margin-bottom: 20px; color: ${this.brandingConfig.colors.text};">
          Congratulations! Your Telegram account has been successfully migrated to our website platform. You now have access to both our Telegram bot and the full website dashboard.
        </p>
        
        <div class="alert alert-success">
          <h3 style="margin-top: 0; margin-bottom: 15px; color: #34d399;">
            ✅ Migration Complete
          </h3>
          <table class="table">
            <tr>
              <td class="label">Website Username:</td>
              <td class="value">${data.username}</td>
            </tr>
            <tr>
              <td class="label">Email Address:</td>
              <td class="value">${data.email}</td>
            </tr>
            <tr>
              <td class="label">Telegram Account:</td>
              <td class="value">${data.telegramUsername}</td>
            </tr>
            <tr>
              <td class="label">Migration Date:</td>
              <td class="value">${this.formatDate(new Date())}</td>
            </tr>
          </table>
        </div>
        
        <div class="alert alert-info">
          <h4 style="margin-top: 0; margin-bottom: 15px; color: #60a5fa;">
            🚀 What's New on the Website?
          </h4>
          <ul style="margin: 0; padding-left: 20px; color: #60a5fa;">
            <li style="margin-bottom: 8px;"><strong>Enhanced Portfolio Management:</strong> Detailed portfolio analytics and performance tracking</li>
            <li style="margin-bottom: 8px;"><strong>Advanced Dividend Calculator:</strong> Interactive tools to project your returns</li>
            <li style="margin-bottom: 8px;"><strong>Digital Certificates:</strong> Download and print your share certificates</li>
            <li style="margin-bottom: 8px;"><strong>KYC Verification:</strong> Complete verification for commission withdrawals</li>
            <li style="margin-bottom: 8px;"><strong>Referral Dashboard:</strong> Advanced referral tracking and management</li>
            <li style="margin-bottom: 8px;"><strong>Real-time Notifications:</strong> Stay updated with all account activities</li>
          </ul>
        </div>
        
        <div style="margin: 25px 0; padding: 20px; background-color: rgba(59, 130, 246, 0.1); border-radius: 8px; border-left: 4px solid ${this.brandingConfig.colors.info};">
          <h4 style="margin-top: 0; margin-bottom: 10px; color: ${this.brandingConfig.colors.info};">
            🔗 Dual Platform Access
          </h4>
          <p style="margin: 0; color: ${this.brandingConfig.colors.text}; font-size: 14px;">
            Your account is now synchronized across both platforms. You can continue using the Telegram bot for quick actions 
            and use the website for detailed management and advanced features. All your data, including commission balances 
            and referral relationships, has been preserved.
          </p>
        </div>
        
        <div style="margin: 25px 0; padding: 20px; background-color: rgba(212, 175, 55, 0.1); border-radius: 8px; border-left: 4px solid ${this.brandingConfig.colors.primary};">
          <h4 style="margin-top: 0; margin-bottom: 15px; color: ${this.brandingConfig.colors.primary};">
            🔐 Important Security Information
          </h4>
          <ul style="margin: 0; padding-left: 20px; color: ${this.brandingConfig.colors.text}; font-size: 14px;">
            <li style="margin-bottom: 8px;">Your website password is separate from your Telegram access</li>
            <li style="margin-bottom: 8px;">Both platforms use the same account data and balances</li>
            <li style="margin-bottom: 8px;">You can log in to the website using your email and the password you set during migration</li>
            <li style="margin-bottom: 8px;">Your Telegram bot access remains unchanged</li>
          </ul>
        </div>
        
        <h3 style="color: ${this.brandingConfig.colors.primary}; margin: 30px 0 20px;">
          🎯 Next Steps
        </h3>
        
        <div style="margin-bottom: 25px;">
          <div style="margin-bottom: 15px;">
            <strong style="color: ${this.brandingConfig.colors.primary};">1. Explore Your Dashboard</strong><br>
            <span style="color: ${this.brandingConfig.colors.textSecondary}; font-size: 14px;">
              Log in to your website dashboard and explore all the new features available to you.
            </span>
          </div>
          
          <div style="margin-bottom: 15px;">
            <strong style="color: ${this.brandingConfig.colors.primary};">2. Complete KYC Verification</strong><br>
            <span style="color: ${this.brandingConfig.colors.textSecondary}; font-size: 14px;">
              Unlock commission withdrawals and dividend payments by completing your KYC verification.
            </span>
          </div>
          
          <div style="margin-bottom: 15px;">
            <strong style="color: ${this.brandingConfig.colors.primary};">3. Download Your Certificates</strong><br>
            <span style="color: ${this.brandingConfig.colors.textSecondary}; font-size: 14px;">
              Generate and download digital certificates for all your share holdings.
            </span>
          </div>
          
          <div style="margin-bottom: 15px;">
            <strong style="color: ${this.brandingConfig.colors.primary};">4. Set Up Notifications</strong><br>
            <span style="color: ${this.brandingConfig.colors.textSecondary}; font-size: 14px;">
              Configure your notification preferences to stay informed about your investments.
            </span>
          </div>
        </div>
        
        <div class="text-center mt-4">
          <a href="${this.brandingConfig.websiteUrl}/dashboard" class="btn btn-primary" style="margin-right: 10px;">
            Access Website Dashboard
          </a>
          <a href="${this.brandingConfig.websiteUrl}/kyc" class="btn btn-secondary">
            Complete KYC
          </a>
        </div>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #444; text-align: center;">
          <p style="margin: 0; color: ${this.brandingConfig.colors.textSecondary}; font-size: 14px;">
            Need help with your migrated account? Our support team is here to assist you.
          </p>
          <a href="mailto:${this.brandingConfig.supportEmail}" style="color: ${this.brandingConfig.colors.primary}; text-decoration: none;">
            ${this.brandingConfig.supportEmail}
          </a>
        </div>
      </div>
    `;
  }

  protected generateTextContent(data: MigrationConfirmationData): string {
    return `
Migration Successful!

Hello ${data.fullName},

Congratulations! Your Telegram account has been successfully migrated to our website platform. You now have access to both our Telegram bot and the full website dashboard.

Migration Complete:
- Website Username: ${data.username}
- Email Address: ${data.email}
- Telegram Account: ${data.telegramUsername}
- Migration Date: ${this.formatDate(new Date())}

What's New on the Website?
- Enhanced Portfolio Management: Detailed portfolio analytics and performance tracking
- Advanced Dividend Calculator: Interactive tools to project your returns
- Digital Certificates: Download and print your share certificates
- KYC Verification: Complete verification for commission withdrawals
- Referral Dashboard: Advanced referral tracking and management
- Real-time Notifications: Stay updated with all account activities

Dual Platform Access:
Your account is now synchronized across both platforms. You can continue using the Telegram bot for quick actions and use the website for detailed management and advanced features. All your data, including commission balances and referral relationships, has been preserved.

Important Security Information:
- Your website password is separate from your Telegram access
- Both platforms use the same account data and balances
- You can log in to the website using your email and the password you set during migration
- Your Telegram bot access remains unchanged

Next Steps:
1. Explore Your Dashboard - Log in to your website dashboard and explore all the new features
2. Complete KYC Verification - Unlock commission withdrawals and dividend payments
3. Download Your Certificates - Generate and download digital certificates for your shares
4. Set Up Notifications - Configure your notification preferences

Access Website Dashboard: ${this.brandingConfig.websiteUrl}/dashboard
Complete KYC: ${this.brandingConfig.websiteUrl}/kyc

Need help? Contact: ${this.brandingConfig.supportEmail}

${this.brandingConfig.tagline}
${this.brandingConfig.companyName}
    `.trim();
  }
}
