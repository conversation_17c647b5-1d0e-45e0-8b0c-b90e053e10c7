/**
 * ADD PROFILE PICTURE COLUMN TO USERS TABLE
 * 
 * This script adds the profile_image_url column to the users table
 * and creates the profile-pictures storage bucket.
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with service role key for admin operations
const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function addProfilePictureColumn() {
  console.log('📷 Adding profile picture column to users table...\n');

  try {
    // Step 1: Add the profile_image_url column to users table
    console.log('1️⃣ Adding profile_image_url column...');
    
    const { error: alterError } = await supabase.rpc('exec_sql', {
      sql: `
        ALTER TABLE public.users 
        ADD COLUMN IF NOT EXISTS profile_image_url TEXT;
        
        COMMENT ON COLUMN public.users.profile_image_url IS 'URL to user profile picture stored in Supabase Storage';
      `
    });

    if (alterError) {
      console.error('❌ Error adding profile_image_url column:', alterError);
      return;
    }

    console.log('✅ Successfully added profile_image_url column to users table');

    // Step 2: Verify the column was added by testing a query
    console.log('\n2️⃣ Verifying column addition...');
    
    const { data: testData, error: testError } = await supabase
      .from('users')
      .select('id, profile_image_url')
      .limit(1);

    if (testError) {
      console.error('❌ Error verifying column:', testError);
      return;
    }

    console.log('✅ Column verification successful - profile_image_url is accessible');

    // Step 3: Create the profile-pictures storage bucket
    console.log('\n3️⃣ Creating profile-pictures storage bucket...');
    
    const { data: bucketData, error: bucketError } = await supabase.storage
      .createBucket('profile-pictures', {
        public: true,
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        fileSizeLimit: 5242880 // 5MB in bytes
      });

    if (bucketError) {
      if (bucketError.message.includes('already exists')) {
        console.log('✅ Profile pictures bucket already exists');
      } else {
        console.error('❌ Error creating bucket:', bucketError);
        return;
      }
    } else {
      console.log('✅ Profile pictures bucket created successfully');
    }

    // Step 4: Test bucket access
    console.log('\n4️⃣ Testing bucket access...');
    
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();
    
    if (listError) {
      console.error('❌ Error listing buckets:', listError);
      return;
    }

    const profileBucket = buckets.find(bucket => bucket.name === 'profile-pictures');
    if (profileBucket) {
      console.log('✅ Profile pictures bucket found and accessible');
      console.log('   Bucket ID:', profileBucket.id);
      console.log('   Public:', profileBucket.public);
    } else {
      console.log('❌ Profile pictures bucket not found');
      return;
    }

    console.log('\n🎉 Profile picture setup complete!');
    console.log('\n📝 Manual steps still required:');
    console.log('   1. Go to Supabase Dashboard > Storage > Policies');
    console.log('   2. Create storage policies for the profile-pictures bucket:');
    console.log('      - INSERT policy for authenticated users');
    console.log('      - SELECT policy for public access');
    console.log('      - DELETE policy for users to delete their own images');
    console.log('\n🔗 Storage URL format:');
    console.log(`   ${supabaseUrl}/storage/v1/object/public/profile-pictures/[filename]`);

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the setup
addProfilePictureColumn();
