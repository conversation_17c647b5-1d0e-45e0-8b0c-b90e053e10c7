// Debug script to check User ID 4 authentication flow
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!serviceRoleKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, serviceRoleKey);

async function debugUserAuthFlow() {
  console.log('🔍 DEBUGGING USER ID 4 AUTHENTICATION FLOW\n');

  try {
    // Check users table
    console.log('1️⃣ CHECKING USERS TABLE:');
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', 4)
      .single();

    if (userError) {
      console.error('❌ Error fetching user:', userError);
      return;
    }

    console.log('👤 User ID 4 Details:');
    console.log('   - ID:', user.id);
    console.log('   - Username:', user.username);
    console.log('   - Email:', user.email);
    console.log('   - Password Hash:', user.password_hash);
    console.log('   - Is Verified:', user.is_verified);
    console.log('   - Migration Status:', user.migration_status);
    console.log('   - Web Credentials Set:', user.web_credentials_set);
    console.log('   - Migration Completed:', user.migration_completed);

    // Check telegram_users table
    console.log('\n2️⃣ CHECKING TELEGRAM_USERS TABLE:');
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('user_id', 4)
      .single();

    if (telegramError) {
      console.error('❌ Error fetching telegram user:', telegramError);
    } else {
      console.log('📱 Telegram User Details:');
      console.log('   - User ID:', telegramUser.user_id);
      console.log('   - Telegram ID:', telegramUser.telegram_id);
      console.log('   - Username:', telegramUser.username);
      console.log('   - First Name:', telegramUser.first_name);
      console.log('   - Last Name:', telegramUser.last_name);
    }

    // Migration logic check
    console.log('\n3️⃣ MIGRATION LOGIC CHECK:');
    const needsMigration = user.password_hash === 'telegram_auth';
    const hasWebCredentials = user.web_credentials_set === true;
    const isVerified = user.is_verified === true;

    console.log('🔍 Migration Decision Logic:');
    console.log('   - Password Hash === "telegram_auth":', needsMigration);
    console.log('   - Has Web Credentials:', hasWebCredentials);
    console.log('   - Is Verified:', isVerified);
    console.log('   - Should Show Migration Form:', needsMigration);
    console.log('   - Should Allow Login:', !needsMigration && hasWebCredentials && isVerified);

    // Expected behavior
    console.log('\n4️⃣ EXPECTED BEHAVIOR:');
    if (needsMigration) {
      console.log('✅ SHOULD SHOW MIGRATION FORM');
      console.log('   - User has telegram_auth password hash');
      console.log('   - System should force migration before login');
    } else {
      console.log('✅ SHOULD ALLOW LOGIN');
      console.log('   - User has proper password hash');
      console.log('   - System should allow normal login');
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

debugUserAuthFlow();
