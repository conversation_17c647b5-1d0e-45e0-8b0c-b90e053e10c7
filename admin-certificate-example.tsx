// Example of how to integrate the Certificate Management component into your admin dashboard

import React from 'react';
import CertificateManagement from './components/admin/CertificateManagement';

// Add this to your admin dashboard or create a dedicated certificates page
export const AdminCertificatesPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-white">Admin Dashboard</h1>
          <p className="text-gray-400">Manage share certificates and user purchases</p>
        </div>
        
        <CertificateManagement />
      </div>
    </div>
  );
};

// Or add as a tab in existing admin dashboard:
/*
const AdminDashboard = () => {
  const [activeTab, setActiveTab] = useState('overview');
  
  return (
    <div className="admin-dashboard">
      <nav className="admin-nav">
        <button 
          onClick={() => setActiveTab('certificates')}
          className={activeTab === 'certificates' ? 'active' : ''}
        >
          📜 Certificates
        </button>
        // ... other tabs
      </nav>
      
      <div className="admin-content">
        {activeTab === 'certificates' && <CertificateManagement />}
        // ... other tab content
      </div>
    </div>
  );
};
*/

export default AdminCertificatesPage;
