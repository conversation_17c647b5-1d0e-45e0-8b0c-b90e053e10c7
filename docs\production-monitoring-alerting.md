# Production Monitoring & Alerting Configuration - Phase 7.1

## Overview
Comprehensive monitoring and alerting system for the Aureus Alliance Web Dashboard production environment, ensuring 24/7 visibility into system performance, security, and user experience.

## Monitoring Architecture

### Monitoring Stack Components
| Component | Purpose | Data Retention | High Availability |
|-----------|---------|----------------|-------------------|
| Prometheus | Metrics collection & storage | 30 days | ✅ Clustered |
| Grafana | Visualization & dashboards | N/A | ✅ Load balanced |
| AlertManager | Alert routing & management | 7 days | ✅ Redundant |
| Loki | Log aggregation | 14 days | ✅ Distributed |
| Jaeger | Distributed tracing | 7 days | ✅ Elasticsearch backend |
| Uptime Robot | External monitoring | 2 years | ✅ Multi-region |

### Metrics Collection Setup

#### Application Metrics
```javascript
// metrics.js - Application performance metrics
import prometheus from 'prom-client';

// Create a Registry to register the metrics
const register = new prometheus.Registry();

// Default metrics (CPU, memory, etc.)
prometheus.collectDefaultMetrics({ register });

// Custom business metrics
const httpRequestDuration = new prometheus.Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10]
});

const httpRequestTotal = new prometheus.Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code']
});

const activeUsers = new prometheus.Gauge({
  name: 'active_users_total',
  help: 'Total number of active users',
  labelNames: ['user_type']
});

const databaseConnections = new prometheus.Gauge({
  name: 'database_connections_active',
  help: 'Number of active database connections'
});

const businessMetrics = {
  totalInvestments: new prometheus.Gauge({
    name: 'total_investments_amount',
    help: 'Total investment amount in system',
    labelNames: ['currency']
  }),
  
  userRegistrations: new prometheus.Counter({
    name: 'user_registrations_total',
    help: 'Total number of user registrations',
    labelNames: ['registration_type']
  }),
  
  commissionsCalculated: new prometheus.Counter({
    name: 'commissions_calculated_total',
    help: 'Total number of commissions calculated',
    labelNames: ['commission_type']
  }),
  
  notificationsSent: new prometheus.Counter({
    name: 'notifications_sent_total',
    help: 'Total number of notifications sent',
    labelNames: ['notification_type', 'channel']
  })
};

// Register all metrics
register.registerMetric(httpRequestDuration);
register.registerMetric(httpRequestTotal);
register.registerMetric(activeUsers);
register.registerMetric(databaseConnections);
Object.values(businessMetrics).forEach(metric => register.registerMetric(metric));

// Middleware to track HTTP requests
export const metricsMiddleware = (req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = (Date.now() - start) / 1000;
    const route = req.route?.path || req.path;
    
    httpRequestDuration
      .labels(req.method, route, res.statusCode)
      .observe(duration);
    
    httpRequestTotal
      .labels(req.method, route, res.statusCode)
      .inc();
  });
  
  next();
};

// Metrics endpoint
export const metricsHandler = async (req, res) => {
  res.set('Content-Type', register.contentType);
  res.end(await register.metrics());
};

export { businessMetrics, activeUsers, databaseConnections };
```

#### System Health Checks
```javascript
// health-check.js - Comprehensive health monitoring
import { Pool } from 'pg';
import Redis from 'redis';

const dbPool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

const redisClient = Redis.createClient({
  url: process.env.REDIS_URL,
  retry_delay_on_failover: 100,
  max_attempts: 3
});

class HealthChecker {
  constructor() {
    this.checks = new Map();
    this.setupChecks();
  }

  setupChecks() {
    // Database connectivity check
    this.checks.set('database', async () => {
      try {
        const client = await dbPool.connect();
        const result = await client.query('SELECT 1');
        client.release();
        return {
          status: 'healthy',
          responseTime: Date.now(),
          details: { connections: dbPool.totalCount }
        };
      } catch (error) {
        return {
          status: 'unhealthy',
          error: error.message,
          responseTime: Date.now()
        };
      }
    });

    // Redis connectivity check
    this.checks.set('redis', async () => {
      try {
        const start = Date.now();
        await redisClient.ping();
        const responseTime = Date.now() - start;
        
        return {
          status: 'healthy',
          responseTime,
          details: { connected: redisClient.connected }
        };
      } catch (error) {
        return {
          status: 'unhealthy',
          error: error.message,
          responseTime: Date.now()
        };
      }
    });

    // External API dependencies
    this.checks.set('supabase', async () => {
      try {
        const start = Date.now();
        const response = await fetch(`${process.env.VITE_SUPABASE_URL}/rest/v1/`, {
          method: 'HEAD',
          headers: {
            'apikey': process.env.VITE_SUPABASE_ANON_KEY,
            'Authorization': `Bearer ${process.env.VITE_SUPABASE_ANON_KEY}`
          }
        });
        
        const responseTime = Date.now() - start;
        
        return {
          status: response.ok ? 'healthy' : 'unhealthy',
          responseTime,
          details: { statusCode: response.status }
        };
      } catch (error) {
        return {
          status: 'unhealthy',
          error: error.message,
          responseTime: Date.now()
        };
      }
    });

    // Disk space check
    this.checks.set('disk_space', async () => {
      try {
        const { exec } = require('child_process');
        const { promisify } = require('util');
        const execAsync = promisify(exec);
        
        const { stdout } = await execAsync("df -h / | tail -1 | awk '{print $5}' | sed 's/%//'");
        const usagePercent = parseInt(stdout.trim());
        
        return {
          status: usagePercent < 85 ? 'healthy' : 'unhealthy',
          responseTime: Date.now(),
          details: { 
            usagePercent,
            threshold: 85,
            message: usagePercent >= 85 ? 'Disk usage is high' : 'Disk usage is normal'
          }
        };
      } catch (error) {
        return {
          status: 'unhealthy',
          error: error.message,
          responseTime: Date.now()
        };
      }
    });

    // Memory usage check
    this.checks.set('memory', async () => {
      const memUsage = process.memoryUsage();
      const totalMemory = require('os').totalmem();
      const freeMemory = require('os').freemem();
      const usagePercent = ((totalMemory - freeMemory) / totalMemory) * 100;
      
      return {
        status: usagePercent < 90 ? 'healthy' : 'unhealthy',
        responseTime: Date.now(),
        details: {
          heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
          heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
          systemUsagePercent: Math.round(usagePercent),
          threshold: 90
        }
      };
    });
  }

  async runCheck(checkName) {
    const check = this.checks.get(checkName);
    if (!check) {
      return { status: 'unknown', error: 'Check not found' };
    }

    try {
      return await check();
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        responseTime: Date.now()
      };
    }
  }

  async runAllChecks() {
    const results = {};
    const promises = Array.from(this.checks.keys()).map(async (checkName) => {
      results[checkName] = await this.runCheck(checkName);
    });

    await Promise.all(promises);

    const overallStatus = Object.values(results).every(check => 
      check.status === 'healthy'
    ) ? 'healthy' : 'unhealthy';

    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      checks: results
    };
  }
}

export const healthChecker = new HealthChecker();

// Health check endpoints
export const healthEndpoints = {
  // Simple health check
  basic: (req, res) => {
    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version
    });
  },

  // Detailed health check
  detailed: async (req, res) => {
    try {
      const health = await healthChecker.runAllChecks();
      const statusCode = health.status === 'healthy' ? 200 : 503;
      res.status(statusCode).json(health);
    } catch (error) {
      res.status(500).json({
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  },

  // Readiness probe (for Kubernetes)
  ready: async (req, res) => {
    try {
      const dbCheck = await healthChecker.runCheck('database');
      const redisCheck = await healthChecker.runCheck('redis');
      
      const isReady = dbCheck.status === 'healthy' && redisCheck.status === 'healthy';
      
      res.status(isReady ? 200 : 503).json({
        status: isReady ? 'ready' : 'not ready',
        checks: { database: dbCheck, redis: redisCheck }
      });
    } catch (error) {
      res.status(503).json({
        status: 'not ready',
        error: error.message
      });
    }
  }
};
```

### Grafana Dashboard Configuration

#### Main Application Dashboard
```json
{
  "dashboard": {
    "id": null,
    "title": "Aureus Alliance - Production Dashboard",
    "tags": ["aureus", "production"],
    "timezone": "browser",
    "refresh": "30s",
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "panels": [
      {
        "id": 1,
        "title": "System Overview",
        "type": "stat",
        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0},
        "targets": [
          {
            "expr": "up{job=\"aureus-alliance\"}",
            "legendFormat": "Service Status"
          },
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "Request Rate"
          },
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th %ile Response Time"
          },
          {
            "expr": "rate(http_requests_total{status_code=~\"5..\"}[5m]) * 100",
            "legendFormat": "Error Rate %"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {"mode": "palette-classic"},
            "unit": "short",
            "thresholds": {
              "steps": [
                {"color": "green", "value": null},
                {"color": "yellow", "value": 0.8},
                {"color": "red", "value": 0.9}
              ]
            }
          }
        }
      },
      {
        "id": 2,
        "title": "Request Rate & Response Time",
        "type": "graph",
        "gridPos": {"h": 9, "w": 12, "x": 0, "y": 8},
        "targets": [
          {
            "expr": "rate(http_requests_total[1m])",
            "legendFormat": "{{method}} {{route}}"
          }
        ],
        "yAxes": [
          {"label": "Requests/sec", "min": 0},
          {"label": "Response Time (s)", "min": 0}
        ]
      },
      {
        "id": 3,
        "title": "Error Rates",
        "type": "graph",
        "gridPos": {"h": 9, "w": 12, "x": 12, "y": 8},
        "targets": [
          {
            "expr": "rate(http_requests_total{status_code=~\"4..\"}[5m])",
            "legendFormat": "4xx Errors"
          },
          {
            "expr": "rate(http_requests_total{status_code=~\"5..\"}[5m])",
            "legendFormat": "5xx Errors"
          }
        ],
        "yAxes": [
          {"label": "Errors/sec", "min": 0}
        ]
      },
      {
        "id": 4,
        "title": "Business Metrics",
        "type": "stat",
        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 17},
        "targets": [
          {
            "expr": "active_users_total",
            "legendFormat": "Active Users"
          },
          {
            "expr": "total_investments_amount",
            "legendFormat": "Total Investments"
          },
          {
            "expr": "rate(user_registrations_total[1h])",
            "legendFormat": "Registrations/hour"
          },
          {
            "expr": "rate(commissions_calculated_total[1h])",
            "legendFormat": "Commissions/hour"
          }
        ]
      },
      {
        "id": 5,
        "title": "Database Performance",
        "type": "graph",
        "gridPos": {"h": 9, "w": 12, "x": 0, "y": 25},
        "targets": [
          {
            "expr": "database_connections_active",
            "legendFormat": "Active Connections"
          },
          {
            "expr": "rate(pg_stat_database_tup_returned[1m])",
            "legendFormat": "Rows Returned/sec"
          }
        ]
      },
      {
        "id": 6,
        "title": "System Resources",
        "type": "graph",
        "gridPos": {"h": 9, "w": 12, "x": 12, "y": 25},
        "targets": [
          {
            "expr": "process_resident_memory_bytes / 1024 / 1024",
            "legendFormat": "Memory Usage (MB)"
          },
          {
            "expr": "rate(process_cpu_seconds_total[1m]) * 100",
            "legendFormat": "CPU Usage %"
          }
        ]
      }
    ]
  }
}
```

#### Security Monitoring Dashboard
```json
{
  "dashboard": {
    "title": "Aureus Alliance - Security Monitoring",
    "panels": [
      {
        "title": "Authentication Events",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(authentication_attempts_total[5m])",
            "legendFormat": "{{status}} - {{method}}"
          }
        ]
      },
      {
        "title": "Failed Login Attempts",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(authentication_attempts_total{status=\"failed\"}[1h])",
            "legendFormat": "Failed Logins/hour"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "thresholds": {
              "steps": [
                {"color": "green", "value": 0},
                {"color": "yellow", "value": 10},
                {"color": "red", "value": 50}
              ]
            }
          }
        }
      },
      {
        "title": "Security Events",
        "type": "logs",
        "targets": [
          {
            "expr": "{job=\"aureus-alliance\"} |= \"security\" | json"
          }
        ]
      },
      {
        "title": "Rate Limiting",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(rate_limit_exceeded_total[5m])",
            "legendFormat": "Rate Limit Exceeded"
          }
        ]
      }
    ]
  }
}
```

### Alert Configuration

#### Critical Alerts
```yaml
# critical-alerts.yml
groups:
  - name: critical-alerts
    rules:
      - alert: ServiceDown
        expr: up{job="aureus-alliance"} == 0
        for: 1m
        labels:
          severity: critical
          team: platform
        annotations:
          summary: "Service {{ $labels.instance }} is down"
          description: "{{ $labels.instance }} of job {{ $labels.job }} has been down for more than 1 minute."
          runbook_url: "https://wiki.aureusalliance.com/runbooks/service-down"

      - alert: HighErrorRate
        expr: rate(http_requests_total{status_code=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
          team: platform
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} which is above the threshold of 10%."

      - alert: DatabaseConnectionsHigh
        expr: database_connections_active / database_connections_max > 0.9
        for: 5m
        labels:
          severity: critical
          team: database
        annotations:
          summary: "Database connections critically high"
          description: "Database connections are at {{ $value | humanizePercentage }} of maximum capacity."

      - alert: DiskSpaceCritical
        expr: (node_filesystem_free_bytes / node_filesystem_size_bytes) < 0.05
        for: 2m
        labels:
          severity: critical
          team: infrastructure
        annotations:
          summary: "Critical disk space shortage"
          description: "Only {{ $value | humanizePercentage }} disk space remaining on {{ $labels.instance }}."

      - alert: MemoryUsageCritical
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) > 0.95
        for: 5m
        labels:
          severity: critical
          team: infrastructure
        annotations:
          summary: "Critical memory usage"
          description: "Memory usage is at {{ $value | humanizePercentage }} on {{ $labels.instance }}."

  - name: warning-alerts
    rules:
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s"

      - alert: UnusualUserActivity
        expr: rate(user_registrations_total[1h]) > 100
        for: 10m
        labels:
          severity: warning
          team: security
        annotations:
          summary: "Unusual user registration activity"
          description: "User registration rate is {{ $value }} per hour, which may indicate suspicious activity."

      - alert: FailedAuthenticationSpike
        expr: rate(authentication_attempts_total{status="failed"}[5m]) > 10
        for: 5m
        labels:
          severity: warning
          team: security
        annotations:
          summary: "High rate of failed authentication attempts"
          description: "Failed authentication rate is {{ $value }} per second."

  - name: business-alerts
    rules:
      - alert: LowInvestmentActivity
        expr: rate(total_investments_amount[1h]) < 1000
        for: 30m
        labels:
          severity: warning
          team: business
        annotations:
          summary: "Low investment activity"
          description: "Investment activity is below normal levels at {{ $value }} per hour."

      - alert: CommissionCalculationFailures
        expr: rate(commission_calculation_errors_total[5m]) > 0.01
        for: 5m
        labels:
          severity: warning
          team: business
        annotations:
          summary: "Commission calculation failures detected"
          description: "Commission calculation error rate is {{ $value }} per second."
```

#### AlertManager Configuration
```yaml
# alertmanager.yml
global:
  smtp_smarthost: 'smtp.aureusalliance.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: '${SMTP_PASSWORD}'

route:
  group_by: ['alertname', 'severity']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 12h
  receiver: 'default'
  routes:
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 10s
      repeat_interval: 1h
    
    - match:
        team: security
      receiver: 'security-team'
      group_wait: 15s
      repeat_interval: 2h
    
    - match:
        team: business
      receiver: 'business-team'
      group_wait: 1m
      repeat_interval: 6h

receivers:
  - name: 'default'
    email_configs:
      - to: '<EMAIL>'
        subject: '{{ .Status | toUpper }}: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Instance: {{ .Labels.instance }}
          {{ end }}

  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '🚨 CRITICAL: {{ .GroupLabels.alertname }}'
        body: |
          CRITICAL ALERT TRIGGERED
          
          {{ range .Alerts }}
          Summary: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Runbook: {{ .Annotations.runbook_url }}
          Started: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ end }}
    
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#critical-alerts'
        title: '🚨 CRITICAL: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          {{ .Annotations.summary }}
          {{ .Annotations.description }}
          {{ end }}
        color: 'danger'

  - name: 'security-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '🔒 SECURITY: {{ .GroupLabels.alertname }}'
    
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#security-alerts'
        color: 'warning'

  - name: 'business-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '📊 BUSINESS: {{ .GroupLabels.alertname }}'

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'instance']
```

### Log Management

#### Structured Logging Configuration
```javascript
// logger.js - Centralized logging configuration
import winston from 'winston';
import 'winston-daily-rotate-file';

const logFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.metadata({ fillExcept: ['message', 'level', 'timestamp'] })
);

// Log rotation transport
const fileRotateTransport = new winston.transports.DailyRotateFile({
  filename: 'logs/aureus-alliance-%DATE%.log',
  datePattern: 'YYYY-MM-DD',
  maxSize: '20m',
  maxFiles: '14d',
  format: logFormat
});

// Error log transport
const errorFileTransport = new winston.transports.DailyRotateFile({
  filename: 'logs/aureus-alliance-error-%DATE%.log',
  datePattern: 'YYYY-MM-DD',
  level: 'error',
  maxSize: '20m',
  maxFiles: '30d',
  format: logFormat
});

// Console transport for development
const consoleTransport = new winston.transports.Console({
  format: winston.format.combine(
    winston.format.colorize(),
    winston.format.simple()
  )
});

// Create logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: {
    service: 'aureus-alliance',
    environment: process.env.NODE_ENV,
    version: process.env.npm_package_version
  },
  transports: [
    fileRotateTransport,
    errorFileTransport,
    ...(process.env.NODE_ENV !== 'production' ? [consoleTransport] : [])
  ],
  exceptionHandlers: [
    new winston.transports.File({ filename: 'logs/exceptions.log' })
  ],
  rejectionHandlers: [
    new winston.transports.File({ filename: 'logs/rejections.log' })
  ]
});

// Security audit logger
export const auditLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: {
    service: 'aureus-alliance-audit',
    type: 'security_audit'
  },
  transports: [
    new winston.transports.DailyRotateFile({
      filename: 'logs/security-audit-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      maxSize: '50m',
      maxFiles: '90d'
    })
  ]
});

// Performance logger
export const performanceLogger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  defaultMeta: {
    service: 'aureus-alliance-performance',
    type: 'performance'
  },
  transports: [
    new winston.transports.DailyRotateFile({
      filename: 'logs/performance-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      maxSize: '30m',
      maxFiles: '7d'
    })
  ]
});

export default logger;
```

#### Log Aggregation with Loki
```yaml
# promtail-config.yml
server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  - job_name: aureus-alliance-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: aureus-alliance
          environment: production
          __path__: /var/log/aureus-alliance/*.log
    
    pipeline_stages:
      - json:
          expressions:
            timestamp: timestamp
            level: level
            message: message
            service: service
            metadata: metadata
      
      - timestamp:
          source: timestamp
          format: RFC3339Nano
      
      - labels:
          level:
          service:
      
      - output:
          source: message

  - job_name: nginx-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: nginx
          environment: production
          __path__: /var/log/nginx/*.log
    
    pipeline_stages:
      - regex:
          expression: '^(?P<remote_addr>\S+) \S+ \S+ \[(?P<timestamp>[^\]]+)\] "(?P<method>\S+) (?P<path>\S+) \S+" (?P<status>\d+) (?P<bytes_sent>\d+) "(?P<referer>[^"]*)" "(?P<user_agent>[^"]*)"'
      
      - timestamp:
          source: timestamp
          format: '02/Jan/2006:15:04:05 -0700'
      
      - labels:
          method:
          status:
          path:
```

### External Monitoring

#### Uptime Monitoring
```javascript
// uptime-monitoring.js - External uptime checks
const uptimeChecks = [
  {
    name: 'Main Dashboard',
    url: 'https://dashboard.aureusalliance.com',
    method: 'GET',
    expectedStatus: 200,
    timeout: 30000,
    interval: 300000 // 5 minutes
  },
  {
    name: 'API Health',
    url: 'https://api.aureusalliance.com/health',
    method: 'GET',
    expectedStatus: 200,
    timeout: 10000,
    interval: 60000 // 1 minute
  },
  {
    name: 'User Authentication',
    url: 'https://dashboard.aureusalliance.com/api/auth/status',
    method: 'GET',
    expectedStatus: 200,
    timeout: 15000,
    interval: 180000 // 3 minutes
  },
  {
    name: 'Database Connectivity',
    url: 'https://dashboard.aureusalliance.com/health/detailed',
    method: 'GET',
    expectedStatus: 200,
    timeout: 20000,
    interval: 300000, // 5 minutes
    keywords: ['database', 'healthy']
  }
];

// Synthetic transaction monitoring
const syntheticTransactions = [
  {
    name: 'User Registration Flow',
    steps: [
      { action: 'navigate', url: 'https://dashboard.aureusalliance.com/register' },
      { action: 'fill', selector: '#email', value: '<EMAIL>' },
      { action: 'fill', selector: '#password', value: 'TestPassword123!' },
      { action: 'click', selector: '#register-button' },
      { action: 'wait', selector: '#verification-message', timeout: 10000 }
    ],
    frequency: '1h',
    alerts: {
      responseTime: 10000,
      availability: 99
    }
  },
  {
    name: 'Investment Creation Flow',
    steps: [
      { action: 'login', credentials: 'test-user' },
      { action: 'navigate', url: '/investments/new' },
      { action: 'select', selector: '#package-select', value: 'basic' },
      { action: 'fill', selector: '#amount', value: '1000' },
      { action: 'click', selector: '#preview-button' },
      { action: 'wait', selector: '#investment-preview', timeout: 5000 }
    ],
    frequency: '2h',
    alerts: {
      responseTime: 15000,
      availability: 98
    }
  }
];
```

## Status: Monitoring & Alerting Configured ✅

Comprehensive monitoring and alerting system has been configured:

- ✅ **Application Metrics**: Custom business and performance metrics
- ✅ **Health Checks**: Multi-level health monitoring with detailed diagnostics
- ✅ **Grafana Dashboards**: Real-time visualization of system and business metrics
- ✅ **Alert Rules**: Critical, warning, and business alert configurations
- ✅ **Structured Logging**: Centralized logging with rotation and audit trails
- ✅ **External Monitoring**: Uptime checks and synthetic transaction monitoring
- ✅ **Notification Channels**: Email, Slack, and escalation procedures

The monitoring system provides 24/7 visibility into:
- System performance and availability
- Business metrics and user activity
- Security events and anomalies
- Infrastructure resource utilization
- Error rates and response times

---
*Monitoring & alerting setup completed on: ${new Date().toISOString().split('T')[0]}*
*Coverage: 360° observability with proactive alerting*
*Retention: Metrics (30d), Logs (14-90d), Traces (7d)*
*SLA Monitoring: Real-time compliance tracking*
*Project: Aureus Alliance Web Dashboard*
*Phase: 7.1 Production Preparation*
