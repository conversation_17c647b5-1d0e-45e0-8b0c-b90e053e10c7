/**
 * TRAINING COURSE DEMONSTRATION
 * 
 * Interactive demonstration component that showcases the complete
 * Training Academy system using the sample course data.
 * This component guides administrators through all features and capabilities.
 */

import React, { useState, useEffect } from 'react';
import { getServiceRoleClient } from '../../lib/supabase';

interface TrainingCourseDemonstrationProps {
  onClose: () => void;
}

interface DemoStep {
  id: string;
  title: string;
  description: string;
  component: string;
  features: string[];
  completed: boolean;
}

export const TrainingCourseDemonstration: React.FC<TrainingCourseDemonstrationProps> = ({
  onClose
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [sampleCourseCreated, setSampleCourseCreated] = useState(false);

  const demoSteps: DemoStep[] = [
    {
      id: 'overview',
      title: '🎯 Training Academy Overview',
      description: 'Welcome to the comprehensive Training Academy demonstration. This walkthrough will show you all the powerful features available for creating and managing affiliate training programs.',
      component: 'overview',
      features: [
        'Complete course creation workflow',
        'Interactive assessment builder',
        'Discussion forum management',
        'Progress tracking and analytics',
        'Multi-media content integration',
        'Professional course presentation'
      ],
      completed: false
    },
    {
      id: 'course-creation',
      title: '📚 Course Creation Wizard',
      description: 'Experience the step-by-step course creation process using our sample "Affiliate Marketing Mastery" course.',
      component: 'CourseCreationWizard',
      features: [
        'Multi-step wizard interface',
        'Rich text content editor',
        'Video upload and management',
        'File attachment system',
        'Course metadata configuration',
        'Professional thumbnail upload'
      ],
      completed: false
    },
    {
      id: 'content-tools',
      title: '🛠️ Content Creation Tools',
      description: 'Explore the powerful content creation tools including rich text editor, video upload, and file management.',
      component: 'ContentCreationTools',
      features: [
        'WYSIWYG rich text editor',
        'Video upload with thumbnails',
        'Multi-format file attachments',
        'Image upload and embedding',
        'Content formatting options',
        'Media organization system'
      ],
      completed: false
    },
    {
      id: 'assessments',
      title: '📊 Interactive Assessments',
      description: 'Build comprehensive quizzes and assessments with multiple question types and automatic grading.',
      component: 'QuizBuilder',
      features: [
        'Multiple choice questions',
        'True/false questions',
        'Short answer questions',
        'Drag & drop questions',
        'Automatic scoring system',
        'Detailed explanations'
      ],
      completed: false
    },
    {
      id: 'discussions',
      title: '💬 Discussion Forums',
      description: 'Manage course discussions, Q&A threads, and community engagement with powerful moderation tools.',
      component: 'DiscussionForumManager',
      features: [
        'Thread creation and management',
        'Pinning and locking threads',
        'Search and filtering',
        'Voting and engagement metrics',
        'Moderation tools',
        'User role identification'
      ],
      completed: false
    },
    {
      id: 'progress-tracking',
      title: '📈 Progress Tracking',
      description: 'Monitor student progress, analyze course performance, and generate detailed analytics reports.',
      component: 'ProgressTrackingManager',
      features: [
        'Individual student progress',
        'Course completion statistics',
        'Engagement analytics',
        'Performance insights',
        'Time tracking',
        'Certification status'
      ],
      completed: false
    },
    {
      id: 'integration',
      title: '🔗 System Integration',
      description: 'See how the Training Academy integrates seamlessly with the existing admin dashboard and affiliate system.',
      component: 'SystemIntegration',
      features: [
        'Admin dashboard integration',
        'Affiliate dashboard connection',
        'User authentication system',
        'Database relationships',
        'Notification system',
        'Reporting capabilities'
      ],
      completed: false
    }
  ];

  const createSampleCourse = async () => {
    try {
      setLoading(true);
      
      // In a real implementation, this would execute the SQL script
      // For now, we'll simulate the process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setSampleCourseCreated(true);
      
      // Mark first step as completed
      demoSteps[0].completed = true;
      
    } catch (error) {
      console.error('Error creating sample course:', error);
      alert('Failed to create sample course. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const nextStep = () => {
    if (currentStep < demoSteps.length - 1) {
      demoSteps[currentStep].completed = true;
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const renderStepContent = () => {
    const step = demoSteps[currentStep];

    switch (step.id) {
      case 'overview':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <div className="text-6xl mb-4">🎓</div>
              <h2 className="text-2xl font-bold text-white mb-4">
                Welcome to the Training Academy Demonstration
              </h2>
              <p className="text-gray-400 text-lg max-w-2xl mx-auto">
                This comprehensive demonstration will showcase all the powerful features of the 
                Aureus Alliance Holdings Training Academy system using a real sample course.
              </p>
            </div>

            <div className="bg-gray-700 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">
                📋 What You'll Experience:
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {step.features.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <div className="text-green-400">✅</div>
                    <span className="text-gray-300">{feature}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-blue-600 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">
                🚀 Sample Course: "Affiliate Marketing Mastery for Aureus Alliance Holdings"
              </h3>
              <p className="text-blue-100 mb-4">
                We'll create a comprehensive training course that demonstrates every feature of the 
                Training Academy system. This course includes:
              </p>
              <ul className="text-blue-100 space-y-2">
                <li>• 5 detailed lessons with rich multimedia content</li>
                <li>• Interactive assessments with multiple question types</li>
                <li>• Discussion forums with sample conversations</li>
                <li>• Downloadable resources and templates</li>
                <li>• Progress tracking with sample student data</li>
                <li>• Professional course presentation</li>
              </ul>
            </div>

            {!sampleCourseCreated && (
              <div className="text-center">
                <button
                  onClick={createSampleCourse}
                  disabled={loading}
                  className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg text-lg font-semibold transition-colors disabled:opacity-50"
                >
                  {loading ? (
                    <div className="flex items-center space-x-2">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      <span>Creating Sample Course...</span>
                    </div>
                  ) : (
                    '🎯 Create Sample Course & Begin Demo'
                  )}
                </button>
              </div>
            )}

            {sampleCourseCreated && (
              <div className="bg-green-600 rounded-lg p-6 text-center">
                <div className="text-4xl mb-2">🎉</div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  Sample Course Created Successfully!
                </h3>
                <p className="text-green-100">
                  The "Affiliate Marketing Mastery" course has been created with all sample data. 
                  Ready to explore the Training Academy features!
                </p>
              </div>
            )}
          </div>
        );

      default:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <div className="text-4xl mb-4">🚧</div>
              <h2 className="text-xl font-bold text-white mb-4">{step.title}</h2>
              <p className="text-gray-400 max-w-2xl mx-auto">{step.description}</p>
            </div>

            <div className="bg-gray-700 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">
                🎯 Features Demonstrated:
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {step.features.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <div className="text-blue-400">🔹</div>
                    <span className="text-gray-300">{feature}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-blue-600 rounded-lg p-6 text-center">
              <h3 className="text-lg font-semibold text-white mb-2">
                Interactive Component: {step.component}
              </h3>
              <p className="text-blue-100 mb-4">
                This step would demonstrate the {step.component} component with live interaction 
                using the sample course data.
              </p>
              <div className="bg-blue-700 rounded-lg p-4 text-blue-100">
                <p className="text-sm">
                  💡 In the full implementation, this would show the actual {step.component} 
                  component loaded with sample data for hands-on exploration.
                </p>
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gray-700 px-6 py-4 border-b border-gray-600">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-white">
              🎓 Training Academy System Demonstration
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white text-2xl"
            >
              ×
            </button>
          </div>
        </div>

        {/* Progress Indicator */}
        <div className="bg-gray-700 px-6 py-3 border-b border-gray-600">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-300">
              Step {currentStep + 1} of {demoSteps.length}
            </span>
            <span className="text-sm text-gray-300">
              {Math.round(((currentStep + 1) / demoSteps.length) * 100)}% Complete
            </span>
          </div>
          <div className="w-full bg-gray-600 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentStep + 1) / demoSteps.length) * 100}%` }}
            />
          </div>
        </div>

        {/* Step Navigation */}
        <div className="bg-gray-700 px-6 py-2 border-b border-gray-600">
          <div className="flex items-center space-x-4 overflow-x-auto">
            {demoSteps.map((step, index) => (
              <button
                key={step.id}
                onClick={() => setCurrentStep(index)}
                className={`flex items-center space-x-2 px-3 py-1 rounded-lg whitespace-nowrap text-sm ${
                  index === currentStep
                    ? 'bg-blue-600 text-white'
                    : step.completed
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                }`}
              >
                <span>
                  {step.completed ? '✅' : index === currentStep ? '🔄' : '⏸️'}
                </span>
                <span>{step.title.split(' ')[0]}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {renderStepContent()}
        </div>

        {/* Footer */}
        <div className="bg-gray-700 px-6 py-4 border-t border-gray-600 flex justify-between">
          <button
            onClick={prevStep}
            disabled={currentStep === 0}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            ← Previous
          </button>

          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
            >
              Exit Demo
            </button>
            
            {currentStep < demoSteps.length - 1 ? (
              <button
                onClick={nextStep}
                disabled={!sampleCourseCreated && currentStep === 0}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next →
              </button>
            ) : (
              <button
                onClick={onClose}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
              >
                🎉 Complete Demo
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
