/**
 * Referral Link Persistence System
 * Ensures referral information persists throughout the user's browsing session
 */

interface ReferralData {
  referrerUsername: string;
  referralCode?: string;
  campaignSource?: string;
  landingPage: string;
  timestamp: number;
  expiresAt: number;
}

const REFERRAL_STORAGE_KEY = 'aureus_referral_data';
const REFERRAL_EXPIRY_HOURS = 24; // Referral data expires after 24 hours

/**
 * Store referral information in session storage
 */
export const storeReferralData = (
  referrerUsername: string,
  options: {
    referralCode?: string;
    campaignSource?: string;
    landingPage?: string;
  } = {}
): void => {
  try {
    const now = Date.now();
    const expiresAt = now + (REFERRAL_EXPIRY_HOURS * 60 * 60 * 1000);

    const referralData: ReferralData = {
      referrerUsername,
      referralCode: options.referralCode || referrerUsername,
      campaignSource: options.campaignSource || 'direct',
      landingPage: options.landingPage || window.location.pathname,
      timestamp: now,
      expiresAt
    };

    // Store in both sessionStorage (for current session) and localStorage (for persistence)
    sessionStorage.setItem(REFERRAL_STORAGE_KEY, JSON.stringify(referralData));
    localStorage.setItem(REFERRAL_STORAGE_KEY, JSON.stringify(referralData));

    console.log('✅ Referral data stored:', referralData);
  } catch (error) {
    console.error('❌ Failed to store referral data:', error);
  }
};

/**
 * Retrieve referral information from storage
 */
export const getReferralData = (): ReferralData | null => {
  try {
    // Try sessionStorage first, then localStorage
    let storedData = sessionStorage.getItem(REFERRAL_STORAGE_KEY);
    if (!storedData) {
      storedData = localStorage.getItem(REFERRAL_STORAGE_KEY);
    }

    if (!storedData) {
      return null;
    }

    const referralData: ReferralData = JSON.parse(storedData);

    // Check if data has expired
    if (Date.now() > referralData.expiresAt) {
      clearReferralData();
      return null;
    }

    return referralData;
  } catch (error) {
    console.error('❌ Failed to retrieve referral data:', error);
    return null;
  }
};

/**
 * Clear referral information from storage
 */
export const clearReferralData = (): void => {
  try {
    sessionStorage.removeItem(REFERRAL_STORAGE_KEY);
    localStorage.removeItem(REFERRAL_STORAGE_KEY);
    console.log('✅ Referral data cleared');
  } catch (error) {
    console.error('❌ Failed to clear referral data:', error);
  }
};

/**
 * Check if user has active referral data
 */
export const hasActiveReferral = (): boolean => {
  return getReferralData() !== null;
};

/**
 * Process URL for referral information and store it
 */
export const processReferralUrl = (pathname: string, searchParams?: URLSearchParams): void => {
  try {
    // Handle direct username referral links (e.g., /jprademeyer84)
    if (pathname && pathname.length > 1 && !pathname.includes('/')) {
      const username = pathname.substring(1); // Remove leading slash
      
      // Exclude reserved paths
      const reservedPaths = [
        'dashboard', 'admin', 'api', 'static', '_next', 'favicon.ico',
        'complete-profile', 'login', 'register', 'purchase-shares', 'affiliate',
        'migrate-from-telegram', 'migrate', 'sync', 'reset-password',
        'privacy-policy', 'terms-conditions'
      ];
      
      if (!reservedPaths.includes(username.toLowerCase())) {
        console.log('🎯 Processing username referral link:', username);
        
        storeReferralData(username, {
          landingPage: pathname,
          campaignSource: 'direct_link'
        });
        
        // Track referral click
        trackReferralClick(username, 'direct_link');
        return;
      }
    }

    // Handle /ref/[code] format
    if (pathname.startsWith('/ref/')) {
      const referralCode = pathname.substring(5); // Remove '/ref/'
      if (referralCode) {
        console.log('🎯 Processing referral code link:', referralCode);
        
        storeReferralData(referralCode, {
          referralCode,
          landingPage: pathname,
          campaignSource: 'referral_code'
        });
        
        // Track referral click
        trackReferralClick(referralCode, 'referral_code');
        return;
      }
    }

    // Handle URL parameters (e.g., ?ref=username&campaign=facebook)
    if (searchParams) {
      const refParam = searchParams.get('ref');
      const campaignParam = searchParams.get('campaign');
      
      if (refParam) {
        console.log('🎯 Processing URL parameter referral:', refParam);
        
        storeReferralData(refParam, {
          referralCode: refParam,
          landingPage: pathname,
          campaignSource: campaignParam || 'url_param'
        });
        
        // Track referral click
        trackReferralClick(refParam, campaignParam || 'url_param');
        return;
      }
    }

    console.log('ℹ️ No referral information found in URL');
  } catch (error) {
    console.error('❌ Failed to process referral URL:', error);
  }
};

/**
 * Apply stored referral data to registration form
 */
export const applyReferralToRegistration = (): {
  sponsorUsername?: string;
  campaignSource?: string;
} => {
  const referralData = getReferralData();
  
  if (!referralData) {
    return {};
  }

  console.log('✅ Applying stored referral data to registration:', referralData);
  
  return {
    sponsorUsername: referralData.referrerUsername,
    campaignSource: referralData.campaignSource
  };
};

/**
 * Track referral click (imported from referralTracking.js)
 */
const trackReferralClick = async (referrerUsername: string, campaignSource?: string) => {
  try {
    const { trackReferralClick: trackClick } = await import('./referralTracking');
    await trackClick(referrerUsername, campaignSource, {
      userAgent: navigator.userAgent,
      refererUrl: document.referrer
    });
  } catch (error) {
    console.warn('⚠️ Failed to track referral click:', error);
  }
};

/**
 * Initialize referral tracking on page load
 */
export const initializeReferralTracking = (): void => {
  try {
    // Process current URL for referral information
    const pathname = window.location.pathname;
    const searchParams = new URLSearchParams(window.location.search);
    
    processReferralUrl(pathname, searchParams);
    
    // Log current referral status
    const currentReferral = getReferralData();
    if (currentReferral) {
      console.log('🎯 Active referral session:', currentReferral);
    } else {
      console.log('ℹ️ No active referral session');
    }
  } catch (error) {
    console.error('❌ Failed to initialize referral tracking:', error);
  }
};

export default {
  storeReferralData,
  getReferralData,
  clearReferralData,
  hasActiveReferral,
  processReferralUrl,
  applyReferralToRegistration,
  initializeReferralTracking
};
