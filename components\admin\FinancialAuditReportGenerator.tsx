/**
 * FINANCIAL AUDIT REPORT GENERATOR
 * 
 * Professional admin interface for generating comprehensive financial
 * audit reports for any user in the system with PDF export capability.
 */

import React, { useState, useEffect } from 'react'
import { financialAuditService, AuditResult } from '../../lib/services/financialAuditService'
import { auditPdfService, PdfGenerationOptions } from '../../lib/services/auditPdfService'
import { logAdminAction } from '../../lib/adminAuth'
import { supabase } from '../../lib/supabase'

interface FinancialAuditReportGeneratorProps {
  currentUser?: any
}

interface UserSearchResult {
  id: number
  username: string
  email: string
  full_name: string | null
}

export const FinancialAuditReportGenerator: React.FC<FinancialAuditReportGeneratorProps> = ({
  currentUser
}) => {
  const [userId, setUserId] = useState<string>('')
  const [searchResults, setSearchResults] = useState<UserSearchResult[]>([])
  const [selectedUser, setSelectedUser] = useState<UserSearchResult | null>(null)
  const [auditResult, setAuditResult] = useState<AuditResult | null>(null)
  const [loading, setLoading] = useState(false)
  const [generating, setGenerating] = useState(false)
  const [error, setError] = useState<string>('')
  const [success, setSuccess] = useState<string>('')

  // PDF generation options
  const [pdfOptions, setPdfOptions] = useState<PdfGenerationOptions>({
    includeCompanyLogo: true,
    includeDetailedTransactions: true,
    includeRecommendations: true,
    generatedBy: currentUser?.adminUser?.full_name || currentUser?.adminUser?.username || 'Admin',
    generatedByEmail: currentUser?.adminUser?.email || '<EMAIL>'
  })

  /**
   * Search for users by ID, username, or email
   */
  const searchUsers = async (searchTerm: string) => {
    if (!searchTerm.trim()) {
      setSearchResults([])
      return
    }

    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, username, email, full_name')
        .or(`id.eq.${searchTerm},username.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%,full_name.ilike.%${searchTerm}%`)
        .limit(10)

      if (error) throw error

      setSearchResults(data || [])
    } catch (err: any) {
      console.error('Error searching users:', err)
      setError(`Search failed: ${err.message}`)
    }
  }

  /**
   * Generate financial audit for selected user
   */
  const generateAudit = async () => {
    if (!selectedUser) {
      setError('Please select a user first')
      return
    }

    setLoading(true)
    setError('')
    setSuccess('')
    setAuditResult(null)

    try {
      console.log(`🔍 Generating financial audit for user ${selectedUser.id}`)

      const result = await financialAuditService.generateAudit(selectedUser.id)
      setAuditResult(result)

      // Log admin action
      await logAdminAction(
        currentUser?.adminUser?.id || 0,
        currentUser?.adminUser?.username || 'admin',
        'GENERATE_FINANCIAL_AUDIT',
        'user_audit',
        selectedUser.id.toString(),
        {},
        { 
          user_id: selectedUser.id,
          audit_status: result.auditStatus,
          total_shares: result.calculations.totalSharesOwned,
          portfolio_value: result.calculations.portfolioValue
        }
      )

      setSuccess(`Financial audit completed successfully for ${selectedUser.username}`)

    } catch (err: any) {
      console.error('Error generating audit:', err)
      setError(`Audit generation failed: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }

  /**
   * Generate and download PDF report
   */
  const generatePdfReport = async () => {
    if (!auditResult) {
      setError('Please generate an audit first')
      return
    }

    setGenerating(true)
    setError('')

    try {
      console.log('🔄 Generating PDF audit report...')

      const pdfBlob = await auditPdfService.generateAuditPdf(auditResult, pdfOptions)

      // Create download link
      const url = URL.createObjectURL(pdfBlob)
      const link = document.createElement('a')
      link.href = url
      link.download = `Financial_Audit_Report_${auditResult.user.username}_${new Date().toISOString().split('T')[0]}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      // Log admin action
      await logAdminAction(
        currentUser?.adminUser?.id || 0,
        currentUser?.adminUser?.username || 'admin',
        'DOWNLOAD_AUDIT_PDF',
        'audit_report',
        auditResult.user.id.toString(),
        {},
        { 
          user_id: auditResult.user.id,
          report_type: 'financial_audit_pdf',
          file_name: link.download
        }
      )

      setSuccess('PDF report generated and downloaded successfully!')

    } catch (err: any) {
      console.error('Error generating PDF:', err)
      setError(`PDF generation failed: ${err.message}`)
    } finally {
      setGenerating(false)
    }
  }

  /**
   * Handle user input change
   */
  const handleUserIdChange = (value: string) => {
    setUserId(value)
    setSelectedUser(null)
    setAuditResult(null)
    searchUsers(value)
  }

  /**
   * Select user from search results
   */
  const selectUser = (user: UserSearchResult) => {
    setSelectedUser(user)
    setUserId(user.id.toString())
    setSearchResults([])
    setAuditResult(null)
  }

  /**
   * Format currency values
   */
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  /**
   * Get status color
   */
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PASSED': return 'text-green-400'
      case 'WARNING': return 'text-yellow-400'
      case 'FAILED': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-yellow-500">📊 Financial Audit Report Generator</h2>
          <p className="text-gray-400 mt-1">
            Generate comprehensive financial audit reports for any user with professional PDF export
          </p>
        </div>
      </div>

      {/* User Search Section */}
      <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
        <h3 className="text-lg font-semibold text-white mb-4">🔍 Select User for Audit</h3>
        
        <div className="space-y-4">
          <div className="relative">
            <input
              type="text"
              value={userId}
              onChange={(e) => handleUserIdChange(e.target.value)}
              placeholder="Enter User ID, username, email, or full name..."
              className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-yellow-500"
            />
            
            {/* Search Results Dropdown */}
            {searchResults.length > 0 && (
              <div className="absolute top-full left-0 right-0 bg-gray-700 border border-gray-600 rounded-lg mt-1 max-h-60 overflow-y-auto z-10">
                {searchResults.map((user) => (
                  <div
                    key={user.id}
                    onClick={() => selectUser(user)}
                    className="px-4 py-3 hover:bg-gray-600 cursor-pointer border-b border-gray-600 last:border-b-0"
                  >
                    <div className="text-white font-medium">{user.full_name || user.username}</div>
                    <div className="text-gray-400 text-sm">ID: {user.id} | {user.email}</div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Selected User Display */}
          {selectedUser && (
            <div className="bg-gray-700 p-4 rounded-lg border border-gray-600">
              <h4 className="text-white font-medium mb-2">Selected User:</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">ID:</span>
                  <span className="text-white ml-2">{selectedUser.id}</span>
                </div>
                <div>
                  <span className="text-gray-400">Username:</span>
                  <span className="text-white ml-2">{selectedUser.username}</span>
                </div>
                <div>
                  <span className="text-gray-400">Full Name:</span>
                  <span className="text-white ml-2">{selectedUser.full_name || 'Not provided'}</span>
                </div>
                <div>
                  <span className="text-gray-400">Email:</span>
                  <span className="text-white ml-2">{selectedUser.email}</span>
                </div>
              </div>
            </div>
          )}

          {/* Generate Audit Button */}
          <button
            onClick={generateAudit}
            disabled={!selectedUser || loading}
            className="w-full px-6 py-3 bg-yellow-500 text-black font-medium rounded-lg hover:bg-yellow-400 disabled:bg-gray-600 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            {loading ? '🔄 Generating Audit...' : '📊 Generate Financial Audit'}
          </button>
        </div>
      </div>

      {/* Error/Success Messages */}
      {error && (
        <div className="bg-red-500/10 border border-red-500 rounded-lg p-4">
          <p className="text-red-400">❌ {error}</p>
        </div>
      )}

      {success && (
        <div className="bg-green-500/10 border border-green-500 rounded-lg p-4">
          <p className="text-green-400">✅ {success}</p>
        </div>
      )}

      {/* Audit Results */}
      {auditResult && (
        <div className="space-y-6">
          {/* Audit Summary */}
          <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-white">📋 Audit Summary</h3>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(auditResult.auditStatus)}`}>
                {auditResult.auditStatus}
              </span>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-gray-700 p-4 rounded-lg">
                <div className="text-gray-400 text-sm">Total Shares</div>
                <div className="text-white text-xl font-bold">{auditResult.calculations.totalSharesOwned.toLocaleString()}</div>
                <div className="text-gray-500 text-xs mt-1">
                  {auditResult.calculations.totalDirectShares} direct + {auditResult.calculations.totalCommissionShares} commission + {auditResult.calculations.totalConvertedShares} converted
                </div>
              </div>
              <div className="bg-gray-700 p-4 rounded-lg">
                <div className="text-gray-400 text-sm">Portfolio Value</div>
                <div className="text-white text-xl font-bold">{formatCurrency(auditResult.calculations.portfolioValue)}</div>
              </div>
              <div className="bg-gray-700 p-4 rounded-lg">
                <div className="text-gray-400 text-sm">USDT Commission</div>
                <div className="text-white text-xl font-bold">{formatCurrency(auditResult.calculations.totalUsdtCommissionEarned)}</div>
                <div className="text-gray-500 text-xs mt-1">
                  {formatCurrency(auditResult.calculations.totalUsdtConverted)} converted to shares
                </div>
              </div>
              <div className="bg-gray-700 p-4 rounded-lg">
                <div className="text-gray-400 text-sm">Current Balance</div>
                <div className="text-white text-xl font-bold">{formatCurrency(auditResult.calculations.currentUsdtBalance)}</div>
              </div>
            </div>
          </div>

          {/* Audit Findings */}
          <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
            <h3 className="text-lg font-semibold text-white mb-4">🔍 Audit Findings</h3>
            <div className="space-y-2">
              {auditResult.auditFindings.map((finding, index) => (
                <div key={index} className="flex items-start space-x-2">
                  <span className="text-yellow-500 mt-1">•</span>
                  <span className="text-gray-300">{finding}</span>
                </div>
              ))}
            </div>
          </div>

          {/* PDF Generation Options */}
          <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
            <h3 className="text-lg font-semibold text-white mb-4">📄 PDF Report Options</h3>
            
            <div className="grid grid-cols-2 gap-4 mb-6">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={pdfOptions.includeDetailedTransactions}
                  onChange={(e) => setPdfOptions(prev => ({ ...prev, includeDetailedTransactions: e.target.checked }))}
                  className="rounded border-gray-600 bg-gray-700 text-yellow-500 focus:ring-yellow-500"
                />
                <span className="text-gray-300">Include Detailed Transactions</span>
              </label>
              
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={pdfOptions.includeRecommendations}
                  onChange={(e) => setPdfOptions(prev => ({ ...prev, includeRecommendations: e.target.checked }))}
                  className="rounded border-gray-600 bg-gray-700 text-yellow-500 focus:ring-yellow-500"
                />
                <span className="text-gray-300">Include Recommendations</span>
              </label>
            </div>

            <button
              onClick={generatePdfReport}
              disabled={generating}
              className="w-full px-6 py-3 bg-green-500 text-white font-medium rounded-lg hover:bg-green-400 disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors"
            >
              {generating ? '🔄 Generating PDF...' : '📄 Generate & Download PDF Report'}
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default FinancialAuditReportGenerator
