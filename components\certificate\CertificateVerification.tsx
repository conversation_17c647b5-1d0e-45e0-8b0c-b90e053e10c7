import React, { useState, useEffect } from 'react';
import { certificateService } from '../../lib/certificateService';

interface CertificateVerificationProps {
  certificateNumber?: string;
}

export const CertificateVerification: React.FC<CertificateVerificationProps> = ({ 
  certificateNumber: initialCertificateNumber 
}) => {
  const [certificateNumber, setCertificateNumber] = useState(initialCertificateNumber || '');
  const [verificationResult, setVerificationResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (initialCertificateNumber) {
      handleVerify();
    }
  }, [initialCertificateNumber]);

  const handleVerify = async () => {
    if (!certificateNumber.trim()) {
      setError('Please enter a certificate number');
      return;
    }

    setLoading(true);
    setError(null);
    setVerificationResult(null);

    try {
      const result = await certificateService.verifyCertificate(certificateNumber.trim());
      
      if (result.valid) {
        setVerificationResult(result.certificate);
      } else {
        setError(result.error || 'Certificate not found or invalid');
      }
    } catch (error) {
      setError('Verification failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleVerify();
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">🔐 Certificate Verification</h1>
          <p className="text-gray-400">
            Verify the authenticity of Aureus Africa share certificates
          </p>
        </div>

        {/* Verification Input */}
        <div className="mb-8">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Certificate Number
          </label>
          <div className="flex space-x-4">
            <input
              type="text"
              value={certificateNumber}
              onChange={(e) => setCertificateNumber(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Enter certificate number (e.g., AUR-2025-123456)"
              className="flex-1 bg-gray-700 text-white rounded-lg px-4 py-3 border border-gray-600 focus:border-blue-500 focus:outline-none"
            />
            <button
              onClick={handleVerify}
              disabled={loading}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
            >
              {loading ? 'Verifying...' : 'Verify'}
            </button>
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
            <p className="text-gray-400">Verifying certificate...</p>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-900/30 border border-red-700 rounded-lg p-6 mb-6">
            <div className="flex items-center space-x-3">
              <span className="text-red-400 text-2xl">❌</span>
              <div>
                <h3 className="text-red-400 font-semibold">Verification Failed</h3>
                <p className="text-red-200">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Verification Success */}
        {verificationResult && (
          <div className="bg-green-900/30 border border-green-700 rounded-lg p-6">
            <div className="flex items-center space-x-3 mb-6">
              <span className="text-green-400 text-3xl">✅</span>
              <div>
                <h3 className="text-green-400 font-bold text-xl">Certificate Verified</h3>
                <p className="text-green-200">This certificate is authentic and valid</p>
              </div>
            </div>

            {/* Certificate Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Certificate Number
                  </label>
                  <p className="text-white font-mono text-lg">
                    {verificationResult.certificate_number}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Shareholder Name
                  </label>
                  <p className="text-white font-semibold">
                    {verificationResult.user?.full_name}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Email Address
                  </label>
                  <p className="text-white">
                    {verificationResult.user?.email}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Shares Quantity
                  </label>
                  <p className="text-white font-bold text-xl">
                    {verificationResult.shares_count?.toLocaleString()} shares
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Issue Date
                  </label>
                  <p className="text-white">
                    {new Date(verificationResult.issue_date).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Purchase Date
                  </label>
                  <p className="text-white">
                    {verificationResult.purchase?.created_at && 
                      new Date(verificationResult.purchase.created_at).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })
                    }
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Investment Amount
                  </label>
                  <p className="text-white font-bold">
                    ${verificationResult.purchase?.total_amount?.toLocaleString()}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Status
                  </label>
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-900/30 text-green-400 border border-green-700">
                    ✓ {verificationResult.status?.toUpperCase()}
                  </span>
                </div>
              </div>
            </div>

            {/* Security Information */}
            <div className="mt-6 pt-6 border-t border-gray-600">
              <h4 className="text-white font-semibold mb-3">🔒 Security Information</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <label className="block text-gray-400 mb-1">Generation Date</label>
                  <p className="text-white">
                    {verificationResult.certificate_data?.generated_at &&
                      new Date(verificationResult.certificate_data.generated_at).toLocaleString()
                    }
                  </p>
                </div>
                <div>
                  <label className="block text-gray-400 mb-1">Security Hash</label>
                  <p className="text-white font-mono text-xs">
                    {verificationResult.certificate_data?.security_hash || 'N/A'}
                  </p>
                </div>
                <div>
                  <label className="block text-gray-400 mb-1">File Size</label>
                  <p className="text-white">
                    {verificationResult.certificate_data?.file_size 
                      ? `${(verificationResult.certificate_data.file_size / 1024).toFixed(1)} KB`
                      : 'N/A'
                    }
                  </p>
                </div>
              </div>
            </div>

            {/* Download Certificate */}
            {verificationResult.certificate_data?.file_url && (
              <div className="mt-6 pt-6 border-t border-gray-600">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-white font-semibold">📄 Certificate Document</h4>
                    <p className="text-gray-400 text-sm">Download the official certificate PDF</p>
                  </div>
                  <a
                    href={verificationResult.certificate_data.file_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium"
                  >
                    📥 Download PDF
                  </a>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Information Section */}
        <div className="mt-8 bg-blue-900/20 border border-blue-700 rounded-lg p-6">
          <h3 className="text-blue-400 font-semibold mb-3">ℹ️ About Certificate Verification</h3>
          <div className="text-blue-200 text-sm space-y-2">
            <p>
              • All Aureus Africa certificates are digitally signed and contain security features
            </p>
            <p>
              • Certificate numbers follow the format: AUR-YYYY-XXXXXX
            </p>
            <p>
              • Each certificate represents legal ownership of shares in Aureus Africa
            </p>
            <p>
              • For support or questions, contact our <NAME_EMAIL>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CertificateVerification;
