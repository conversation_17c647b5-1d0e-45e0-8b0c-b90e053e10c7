import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'https://fgubaqoftdeefcakejwu.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA'
);

async function fixTelegramMapping() {
  try {
    console.log('🔍 Checking current Telegram ID mapping...');
    
    const telegramId = 1393852532;
    
    // Check current users with this Telegram ID
    const { data: currentUsers, error: currentError } = await supabase
      .from('users')
      .select('id, username, email, telegram_id, is_admin')
      .eq('telegram_id', telegramId);
      
    if (currentError) {
      console.error('❌ Error checking current users:', currentError);
      return;
    }
    
    console.log('📋 Current users with Telegram ID:', currentUsers);
    
    // Check TTTFOUNDER user
    const { data: tttfounder, error: tttError } = await supabase
      .from('users')
      .select('*')
      .eq('username', 'TTTFOUNDER');
      
    if (tttError) {
      console.error('❌ Error finding TTTFOUNDER:', tttError);
      return;
    }
    
    console.log('📋 TTTFOUNDER user:', tttfounder);
    
    if (!tttfounder || tttfounder.length === 0) {
      console.error('❌ TTTFOUNDER user not found!');
      return;
    }
    
    const tttfounderUser = tttfounder[0];
    
    // Remove Telegram ID from current users (admin user)
    if (currentUsers && currentUsers.length > 0) {
      for (const user of currentUsers) {
        if (user.username !== 'TTTFOUNDER') {
          console.log(`🔧 Removing Telegram ID from user: ${user.username}`);
          const { error: removeError } = await supabase
            .from('users')
            .update({ telegram_id: null })
            .eq('id', user.id);
            
          if (removeError) {
            console.error(`❌ Error removing Telegram ID from ${user.username}:`, removeError);
          } else {
            console.log(`✅ Removed Telegram ID from ${user.username}`);
          }
        }
      }
    }
    
    // Assign Telegram ID to TTTFOUNDER
    console.log('🔧 Assigning Telegram ID to TTTFOUNDER...');
    const { data: updateData, error: updateError } = await supabase
      .from('users')
      .update({ 
        telegram_id: telegramId,
        updated_at: new Date().toISOString()
      })
      .eq('username', 'TTTFOUNDER')
      .select();
      
    if (updateError) {
      console.error('❌ Error updating TTTFOUNDER:', updateError);
      return;
    }
    
    console.log('✅ Successfully assigned Telegram ID to TTTFOUNDER:', updateData);
    
    // Verify the fix
    console.log('🔍 Verifying the fix...');
    const { data: verifyData, error: verifyError } = await supabase
      .from('users')
      .select('id, username, email, telegram_id, is_admin')
      .eq('telegram_id', telegramId);
      
    if (verifyError) {
      console.error('❌ Error verifying fix:', verifyError);
      return;
    }
    
    console.log('📋 Final verification - users with Telegram ID:', verifyData);
    
    if (verifyData && verifyData.length === 1 && verifyData[0].username === 'TTTFOUNDER') {
      console.log('🎉 SUCCESS! Telegram ID is now correctly mapped to TTTFOUNDER');
    } else {
      console.error('❌ Fix verification failed!');
    }
    
  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

fixTelegramMapping();
