import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Read from standard server envs, with fallback to Vite-prefixed names used in Vercel dashboard
const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !serviceKey) {
  throw new Error('Missing Supabase environment variables');
}

const supabase = createClient(supabaseUrl, serviceKey);

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { userId, type = 'summary' } = req.query;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    if (type === 'summary') {
      // Get notification summary
      const { data, error } = await supabase
        .from('user_notification_summary')
        .select('*')
        .eq('user_id', parseInt(userId))
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('❌ Error fetching notification summary:', error);
        return res.status(500).json({ error: 'Failed to fetch notification summary' });
      }

      return res.status(200).json({
        success: true,
        data: data || {
          user_id: parseInt(userId),
          total_notifications: 0,
          unread_count: 0,
          payment_approved_count: 0,
          payment_rejected_count: 0,
          commission_count: 0,
          last_notification_date: null
        }
      });

    } else if (type === 'preferences') {
      // Get notification preferences
      const { data, error } = await supabase
        .from('user_notification_preferences')
        .select('*')
        .eq('user_id', parseInt(userId))
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('❌ Error fetching notification preferences:', error);
        return res.status(500).json({ error: 'Failed to fetch notification preferences' });
      }

      return res.status(200).json({
        success: true,
        data: data || {
          user_id: parseInt(userId),
          telegram_id: 0,
          audio_enabled: true,
          notification_volume: 'medium',
          payment_approval_audio: true,
          commission_earned_audio: true,
          security_alert_audio: true,
          marketing_update_audio: false,
          in_app_notifications: true,
          email_notifications: true,
          telegram_notifications: true,
          notification_frequency: 'immediate',
          quiet_hours_enabled: false,
          quiet_hours_start: '22:00',
          quiet_hours_end: '08:00',
          payment_confirmations: true,
          security_alerts: true,
          commission_updates: true,
          marketing_emails: false,
          sound_notifications: false
        }
      });

    } else if (type === 'list') {
      // Get notification list
      const limit = parseInt(req.query.limit) || 20;
      const offset = parseInt(req.query.offset) || 0;

      const { data, error, count } = await supabase
        .from('user_notifications')
        .select('*', { count: 'exact' })
        .eq('user_id', parseInt(userId))
        .eq('is_archived', false)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        console.error('❌ Error fetching notifications:', error);
        return res.status(500).json({ error: 'Failed to fetch notifications' });
      }

      return res.status(200).json({
        success: true,
        data: data || [],
        total_count: count || 0
      });
    }

    return res.status(400).json({ error: 'Invalid type parameter' });

  } catch (error) {
    console.error('❌ API Error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
