/**
 * INTELLIGENT COMMISSION FILTERING AND SEARCH SYSTEM
 * 
 * Advanced filtering and search capabilities for commission management:
 * - Multi-criteria filtering with complex conditions
 * - Smart natural language search
 * - Saved filter presets for common scenarios
 * - Export functionality in multiple formats
 * - Real-time filter application
 * - Advanced search operators
 */

import React, { useState, useEffect, useCallback } from 'react'
import { createClient } from '@supabase/supabase-js'

interface FilterCriteria {
  severity?: ('CRITICAL' | 'MODERATE' | 'MINOR')[]
  discrepancyType?: string[]
  financialImpactRange?: { min: number; max: number }
  userRegistrationDateRange?: { start: string; end: string }
  purchaseDateRange?: { start: string; end: string }
  purchaseAmountRange?: { min: number; max: number }
  userActivityStatus?: ('ACTIVE' | 'INACTIVE')[]
  referrerPerformance?: ('HIGH' | 'MEDIUM' | 'LOW')[]
  geographicLocation?: string[]
  userTier?: string[]
  commissionStatus?: string[]
  searchQuery?: string
}

interface FilterPreset {
  id: string
  name: string
  description: string
  criteria: FilterCriteria
  isDefault?: boolean
  createdBy: string
  createdAt: string
  usageCount: number
}

interface ExportOptions {
  format: 'CSV' | 'PDF' | 'EXCEL' | 'JSON'
  includeFields: string[]
  dateRange?: { start: string; end: string }
  maxRecords?: number
}

interface CommissionFilteringSystemProps {
  discrepancies: any[]
  onFilterChange: (filteredData: any[]) => void
  onExport?: (data: any[], options: ExportOptions) => void
}

const CommissionFilteringSystem: React.FC<CommissionFilteringSystemProps> = ({
  discrepancies,
  onFilterChange,
  onExport
}) => {
  // State Management
  const [currentFilters, setCurrentFilters] = useState<FilterCriteria>({})
  const [savedPresets, setSavedPresets] = useState<FilterPreset[]>([])
  const [activePreset, setActivePreset] = useState<string | null>(null)
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [filteredData, setFilteredData] = useState(discrepancies)
  const [showExportModal, setShowExportModal] = useState(false)
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'CSV',
    includeFields: ['username', 'email', 'type', 'severity', 'financial_impact', 'detected_at']
  })

  // Default filter presets
  const defaultPresets: FilterPreset[] = [
    {
      id: 'critical-issues',
      name: 'Critical Issues',
      description: 'High-impact commission discrepancies requiring immediate attention',
      criteria: {
        severity: ['CRITICAL'],
        financialImpactRange: { min: 50, max: 10000 }
      },
      isDefault: true,
      createdBy: 'system',
      createdAt: new Date().toISOString(),
      usageCount: 0
    },
    {
      id: 'new-users',
      name: 'New User Issues',
      description: 'Commission problems for users registered in the last 30 days',
      criteria: {
        userRegistrationDateRange: {
          start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          end: new Date().toISOString().split('T')[0]
        }
      },
      isDefault: true,
      createdBy: 'system',
      createdAt: new Date().toISOString(),
      usageCount: 0
    },
    {
      id: 'high-value-discrepancies',
      name: 'High Value Discrepancies',
      description: 'Commission issues with financial impact over $100',
      criteria: {
        financialImpactRange: { min: 100, max: 10000 }
      },
      isDefault: true,
      createdBy: 'system',
      createdAt: new Date().toISOString(),
      usageCount: 0
    },
    {
      id: 'missing-share-commissions',
      name: 'Missing Share Commissions',
      description: 'Users who received USDT but missing share commissions',
      criteria: {
        discrepancyType: ['PARTIAL_MISSING'],
        searchQuery: 'missing share'
      },
      isDefault: true,
      createdBy: 'system',
      createdAt: new Date().toISOString(),
      usageCount: 0
    },
    {
      id: 'recent-purchases',
      name: 'Recent Purchase Issues',
      description: 'Commission problems from purchases in the last 7 days',
      criteria: {
        purchaseDateRange: {
          start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          end: new Date().toISOString().split('T')[0]
        }
      },
      isDefault: true,
      createdBy: 'system',
      createdAt: new Date().toISOString(),
      usageCount: 0
    }
  ]

  // Initialize presets
  useEffect(() => {
    setSavedPresets(defaultPresets)
  }, [])

  // Apply filters whenever criteria change
  useEffect(() => {
    applyFilters()
  }, [currentFilters, discrepancies])

  // Smart search functionality
  const parseSearchQuery = (query: string): FilterCriteria => {
    const criteria: FilterCriteria = {}
    const lowerQuery = query.toLowerCase()

    // Natural language parsing
    if (lowerQuery.includes('missing share')) {
      criteria.discrepancyType = ['PARTIAL_MISSING']
    }
    
    if (lowerQuery.includes('critical')) {
      criteria.severity = ['CRITICAL']
    }
    
    if (lowerQuery.includes('over') || lowerQuery.includes('>')) {
      const match = lowerQuery.match(/over\s*\$?(\d+)|>\s*\$?(\d+)/)
      if (match) {
        const amount = parseInt(match[1] || match[2])
        criteria.financialImpactRange = { min: amount, max: 10000 }
      }
    }
    
    if (lowerQuery.includes('under') || lowerQuery.includes('<')) {
      const match = lowerQuery.match(/under\s*\$?(\d+)|<\s*\$?(\d+)/)
      if (match) {
        const amount = parseInt(match[1] || match[2])
        criteria.financialImpactRange = { min: 0, max: amount }
      }
    }

    // Email or username search
    if (lowerQuery.includes('@') || lowerQuery.match(/^[a-zA-Z0-9_]+$/)) {
      criteria.searchQuery = query
    }

    return criteria
  }

  // Apply all active filters
  const applyFilters = useCallback(() => {
    let filtered = [...discrepancies]

    // Apply severity filter
    if (currentFilters.severity && currentFilters.severity.length > 0) {
      filtered = filtered.filter(item => 
        currentFilters.severity!.includes(item.severity)
      )
    }

    // Apply discrepancy type filter
    if (currentFilters.discrepancyType && currentFilters.discrepancyType.length > 0) {
      filtered = filtered.filter(item => 
        currentFilters.discrepancyType!.includes(item.type)
      )
    }

    // Apply financial impact range filter
    if (currentFilters.financialImpactRange) {
      const { min, max } = currentFilters.financialImpactRange
      filtered = filtered.filter(item => 
        item.financial_impact >= min && item.financial_impact <= max
      )
    }

    // Apply purchase date range filter
    if (currentFilters.purchaseDateRange) {
      const { start, end } = currentFilters.purchaseDateRange
      filtered = filtered.filter(item => {
        const purchaseDate = new Date(item.purchase_date)
        return purchaseDate >= new Date(start) && purchaseDate <= new Date(end)
      })
    }

    // Apply purchase amount range filter
    if (currentFilters.purchaseAmountRange) {
      const { min, max } = currentFilters.purchaseAmountRange
      filtered = filtered.filter(item => 
        item.purchase_amount >= min && item.purchase_amount <= max
      )
    }

    // Apply search query filter
    if (currentFilters.searchQuery) {
      const query = currentFilters.searchQuery.toLowerCase()
      filtered = filtered.filter(item => 
        item.username.toLowerCase().includes(query) ||
        item.email.toLowerCase().includes(query) ||
        (item.referrer_username && item.referrer_username.toLowerCase().includes(query)) ||
        item.type.toLowerCase().includes(query) ||
        item.severity.toLowerCase().includes(query)
      )
    }

    setFilteredData(filtered)
    onFilterChange(filtered)
  }, [currentFilters, discrepancies, onFilterChange])

  // Handle search input
  const handleSearchChange = (query: string) => {
    setSearchQuery(query)
    
    if (query.trim()) {
      const parsedCriteria = parseSearchQuery(query)
      setCurrentFilters(prev => ({
        ...prev,
        ...parsedCriteria,
        searchQuery: query
      }))
    } else {
      setCurrentFilters(prev => {
        const { searchQuery, ...rest } = prev
        return rest
      })
    }
  }

  // Apply preset filter
  const applyPreset = (preset: FilterPreset) => {
    setCurrentFilters(preset.criteria)
    setActivePreset(preset.id)
    setSearchQuery(preset.criteria.searchQuery || '')
    
    // Update usage count
    setSavedPresets(prev => 
      prev.map(p => 
        p.id === preset.id 
          ? { ...p, usageCount: p.usageCount + 1 }
          : p
      )
    )
  }

  // Clear all filters
  const clearAllFilters = () => {
    setCurrentFilters({})
    setActivePreset(null)
    setSearchQuery('')
  }

  // Save current filters as preset
  const saveCurrentFiltersAsPreset = (name: string, description: string) => {
    const newPreset: FilterPreset = {
      id: `custom_${Date.now()}`,
      name,
      description,
      criteria: currentFilters,
      createdBy: 'admin', // Would be actual user ID
      createdAt: new Date().toISOString(),
      usageCount: 0
    }
    
    setSavedPresets(prev => [...prev, newPreset])
  }

  // Export filtered data
  const handleExport = () => {
    if (onExport) {
      onExport(filteredData, exportOptions)
    }
    setShowExportModal(false)
  }

  return (
    <div className="commission-filtering-system bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Search Bar */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => handleSearchChange(e.target.value)}
              placeholder="Smart search: 'missing share commissions over $100', 'critical issues', username, email..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
            />
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
          
          <button
            onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
            className={`px-4 py-2 rounded-lg font-medium ${
              showAdvancedFilters 
                ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Advanced Filters
          </button>
          
          <button
            onClick={() => setShowExportModal(true)}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium"
          >
            Export ({filteredData.length})
          </button>
        </div>
      </div>

      {/* Filter Presets */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-gray-700">Quick Filters</h3>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">
              {filteredData.length} of {discrepancies.length} results
            </span>
            {Object.keys(currentFilters).length > 0 && (
              <button
                onClick={clearAllFilters}
                className="text-sm text-red-600 hover:text-red-700 font-medium"
              >
                Clear All
              </button>
            )}
          </div>
        </div>
        
        <div className="flex flex-wrap gap-2">
          {savedPresets.map((preset) => (
            <button
              key={preset.id}
              onClick={() => applyPreset(preset)}
              className={`px-3 py-1 rounded-full text-sm font-medium ${
                activePreset === preset.id
                  ? 'bg-yellow-100 text-yellow-800 border border-yellow-300'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300'
              }`}
              title={preset.description}
            >
              {preset.name}
              {preset.usageCount > 0 && (
                <span className="ml-1 text-xs opacity-75">({preset.usageCount})</span>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Advanced Filters */}
      {showAdvancedFilters && (
        <div className="p-4 bg-gray-50 border-b border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Severity Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Severity</label>
              <div className="space-y-2">
                {['CRITICAL', 'MODERATE', 'MINOR'].map((severity) => (
                  <label key={severity} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={currentFilters.severity?.includes(severity as any) || false}
                      onChange={(e) => {
                        const newSeverity = currentFilters.severity || []
                        if (e.target.checked) {
                          setCurrentFilters(prev => ({
                            ...prev,
                            severity: [...newSeverity, severity as any]
                          }))
                        } else {
                          setCurrentFilters(prev => ({
                            ...prev,
                            severity: newSeverity.filter(s => s !== severity)
                          }))
                        }
                      }}
                      className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700">{severity}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Financial Impact Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Financial Impact ($)</label>
              <div className="flex space-x-2">
                <input
                  type="number"
                  placeholder="Min"
                  value={currentFilters.financialImpactRange?.min || ''}
                  onChange={(e) => {
                    const min = parseFloat(e.target.value) || 0
                    setCurrentFilters(prev => ({
                      ...prev,
                      financialImpactRange: {
                        min,
                        max: prev.financialImpactRange?.max || 10000
                      }
                    }))
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                />
                <input
                  type="number"
                  placeholder="Max"
                  value={currentFilters.financialImpactRange?.max || ''}
                  onChange={(e) => {
                    const max = parseFloat(e.target.value) || 10000
                    setCurrentFilters(prev => ({
                      ...prev,
                      financialImpactRange: {
                        min: prev.financialImpactRange?.min || 0,
                        max
                      }
                    }))
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                />
              </div>
            </div>

            {/* Purchase Date Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Purchase Date Range</label>
              <div className="flex space-x-2">
                <input
                  type="date"
                  value={currentFilters.purchaseDateRange?.start || ''}
                  onChange={(e) => {
                    setCurrentFilters(prev => ({
                      ...prev,
                      purchaseDateRange: {
                        start: e.target.value,
                        end: prev.purchaseDateRange?.end || new Date().toISOString().split('T')[0]
                      }
                    }))
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                />
                <input
                  type="date"
                  value={currentFilters.purchaseDateRange?.end || ''}
                  onChange={(e) => {
                    setCurrentFilters(prev => ({
                      ...prev,
                      purchaseDateRange: {
                        start: prev.purchaseDateRange?.start || '2024-01-01',
                        end: e.target.value
                      }
                    }))
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Export Modal */}
      {showExportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Export Commission Data</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Format</label>
                <select
                  value={exportOptions.format}
                  onChange={(e) => setExportOptions(prev => ({ ...prev, format: e.target.value as any }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="CSV">CSV</option>
                  <option value="EXCEL">Excel</option>
                  <option value="PDF">PDF</option>
                  <option value="JSON">JSON</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Records to Export: {filteredData.length}
                </label>
                <p className="text-sm text-gray-600">
                  Current filters will be applied to the export
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowExportModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                Cancel
              </button>
              <button
                onClick={handleExport}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Export
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default CommissionFilteringSystem
