// Debug investment phases data access
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

console.log('🔍 DEBUGGING INVESTMENT PHASES DATA ACCESS\n');

// Test with service role (admin access)
console.log('1️⃣ Testing with SERVICE ROLE (admin access)...');
const serviceClient = createClient(supabaseUrl, supabaseServiceKey);

try {
  const { data: serviceData, error: serviceError } = await serviceClient
    .from('investment_phases')
    .select('id, phase_number, phase_name, price_per_share, is_active')
    .order('phase_number');

  if (serviceError) {
    console.log('❌ Service role error:', serviceError.message);
  } else {
    console.log(`✅ Service role found ${serviceData.length} phases:`);
    serviceData.slice(0, 5).forEach(phase => {
      console.log(`   - ${phase.phase_name}: $${phase.price_per_share} ${phase.is_active ? '(ACTIVE)' : ''}`);
    });
    if (serviceData.length > 5) {
      console.log(`   ... and ${serviceData.length - 5} more phases`);
    }
  }
} catch (err) {
  console.log('❌ Service role error:', err.message);
}

console.log('\n2️⃣ Testing with ANONYMOUS CLIENT (public access)...');
const anonClient = createClient(supabaseUrl, supabaseAnonKey);

try {
  const { data: anonData, error: anonError } = await anonClient
    .from('investment_phases')
    .select('id, phase_number, phase_name, price_per_share, is_active')
    .order('phase_number');

  if (anonError) {
    console.log('❌ Anonymous error:', anonError.message);
    console.log('   Error code:', anonError.code);
    console.log('   This means RLS policy is blocking public access');
  } else {
    console.log(`✅ Anonymous client found ${anonData.length} phases:`);
    if (anonData.length === 0) {
      console.log('   ⚠️ No data returned - this could be:');
      console.log('     a) RLS policy is filtering out all rows');
      console.log('     b) Table is actually empty');
      console.log('     c) Different database/schema issue');
    } else {
      anonData.slice(0, 5).forEach(phase => {
        console.log(`   - ${phase.phase_name}: $${phase.price_per_share} ${phase.is_active ? '(ACTIVE)' : ''}`);
      });
    }
  }
} catch (err) {
  console.log('❌ Anonymous client error:', err.message);
}

console.log('\n3️⃣ Testing table existence and structure...');
try {
  const { data: tableInfo, error: tableError } = await serviceClient
    .from('information_schema.tables')
    .select('table_name')
    .eq('table_name', 'investment_phases')
    .eq('table_schema', 'public');

  if (tableError) {
    console.log('❌ Could not check table existence:', tableError.message);
  } else {
    console.log(`✅ Table exists: ${tableInfo.length > 0 ? 'YES' : 'NO'}`);
  }
} catch (err) {
  console.log('⚠️ Could not check table existence:', err.message);
}

console.log('\n📊 DIAGNOSIS:');
if (serviceData && serviceData.length > 0) {
  console.log('✅ Data exists in the table (service role can see it)');
  if (anonData && anonData.length === 0) {
    console.log('❌ RLS policy is blocking anonymous access');
    console.log('💡 SOLUTION: Add public read policy for investment_phases table');
  } else {
    console.log('✅ Anonymous access is working');
  }
} else {
  console.log('❌ No data in the table (even with admin access)');
  console.log('💡 SOLUTION: Run the setup script to populate the table');
}
