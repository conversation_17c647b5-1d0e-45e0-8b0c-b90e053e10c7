import React from 'react';
import { PaymentHistory } from '../../PaymentHistory';
import { DashboardData } from '../../../hooks/useDashboardData';

interface PaymentsSectionProps {
  user: any;
  dashboardData: DashboardData;
}

export const PaymentsSection: React.FC<PaymentsSectionProps> = ({
  user,
  dashboardData
}) => {
  return (
    <div className="space-y-6">
      {/* Section Header */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h2 className="text-2xl font-bold text-white mb-2">Payment History</h2>
        <p className="text-gray-400">
          View your transaction history, share purchases, and commission payments.
        </p>
      </div>

      {/* Payment Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm font-medium">Total Invested</p>
              <p className="text-2xl font-bold text-white">
                ${dashboardData.totalInvested.toLocaleString()}
              </p>
            </div>
            <div className="bg-blue-600 p-3 rounded-lg">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm font-medium">USDT Commissions</p>
              <p className="text-2xl font-bold text-blue-400">
                ${dashboardData.usdtCommissions?.total.toFixed(2) || '0.00'}
              </p>
            </div>
            <div className="bg-green-600 p-3 rounded-lg">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm font-medium">Share Commissions</p>
              <p className="text-2xl font-bold text-yellow-400">
                {dashboardData.shareCommissions?.totalShares.toLocaleString() || '0'}
              </p>
            </div>
            <div className="bg-yellow-600 p-3 rounded-lg">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Payment History Component */}
      <PaymentHistory
        userId={user?.database_user?.id || user?.id}
      />
    </div>
  );
};
