/**
 * REJECTION REASON MODAL COMPONENT
 * 
 * Professional modal for KYC rejection with predefined reasons and custom input.
 * Features:
 * - Predefined rejection reasons dropdown
 * - Conditional custom reason text area
 * - Optional admin notes
 * - Form validation
 * - Loading states
 */

import React, { useState } from 'react';

interface RejectionReasonModalProps {
  isOpen: boolean;
  onClose: () => void;
  onReject: (reason: string, adminNotes?: string) => Promise<void>;
  submissionName: string;
  isProcessing: boolean;
}

const PREDEFINED_REASONS = [
  'Blurry/unclear documents',
  'Invalid/expired ID document',
  'Proof of address expired/invalid',
  'Selfie verification failed',
  'Documents don\'t match personal information',
  'Missing required documents',
  'Document quality too poor to verify',
  'Suspicious or fraudulent documents',
  'Incomplete information provided',
  'Other (specify below)'
];

const RejectionReasonModal: React.FC<RejectionReasonModalProps> = ({
  isOpen,
  onClose,
  onReject,
  submissionName,
  isProcessing
}) => {
  const [selectedReason, setSelectedReason] = useState('');
  const [customReason, setCustomReason] = useState('');
  const [adminNotes, setAdminNotes] = useState('');
  const [errors, setErrors] = useState<{ reason?: string; custom?: string }>({});

  const handleClose = () => {
    if (isProcessing) return;
    
    setSelectedReason('');
    setCustomReason('');
    setAdminNotes('');
    setErrors({});
    onClose();
  };

  const validateForm = () => {
    const newErrors: { reason?: string; custom?: string } = {};

    if (!selectedReason) {
      newErrors.reason = 'Please select a rejection reason';
    }

    if (selectedReason === 'Other (specify below)' && !customReason.trim()) {
      newErrors.custom = 'Please specify the custom rejection reason';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    const finalReason = selectedReason === 'Other (specify below)' 
      ? customReason.trim() 
      : selectedReason;

    try {
      await onReject(finalReason, adminNotes.trim() || undefined);
      handleClose();
    } catch (error) {
      console.error('Error rejecting KYC:', error);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-semibold text-white">
            ❌ Reject KYC Submission
          </h3>
          <button
            onClick={handleClose}
            disabled={isProcessing}
            className="text-gray-400 hover:text-white text-2xl disabled:opacity-50"
          >
            ✕
          </button>
        </div>

        {/* Submission Info */}
        <div className="bg-gray-700/50 rounded-lg p-3 mb-6">
          <p className="text-gray-300 text-sm">
            <span className="text-gray-400">Rejecting submission for:</span>
            <br />
            <span className="text-white font-medium">{submissionName}</span>
          </p>
        </div>

        {/* Form */}
        <div className="space-y-4">
          {/* Rejection Reason Dropdown */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Rejection Reason *
            </label>
            <select
              value={selectedReason}
              onChange={(e) => {
                setSelectedReason(e.target.value);
                setErrors(prev => ({ ...prev, reason: undefined }));
              }}
              disabled={isProcessing}
              className={`w-full px-3 py-2 bg-gray-700 border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent disabled:opacity-50 ${
                errors.reason ? 'border-red-500' : 'border-gray-600'
              }`}
            >
              <option value="">Select a reason...</option>
              {PREDEFINED_REASONS.map((reason) => (
                <option key={reason} value={reason}>
                  {reason}
                </option>
              ))}
            </select>
            {errors.reason && (
              <p className="text-red-400 text-xs mt-1">{errors.reason}</p>
            )}
          </div>

          {/* Custom Reason Text Area (conditional) */}
          {selectedReason === 'Other (specify below)' && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Custom Rejection Reason *
              </label>
              <textarea
                value={customReason}
                onChange={(e) => {
                  setCustomReason(e.target.value);
                  setErrors(prev => ({ ...prev, custom: undefined }));
                }}
                placeholder="Please provide a detailed reason for rejection..."
                disabled={isProcessing}
                className={`w-full px-3 py-2 bg-gray-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent disabled:opacity-50 ${
                  errors.custom ? 'border-red-500' : 'border-gray-600'
                }`}
                rows={4}
                maxLength={500}
              />
              <div className="flex justify-between items-center mt-1">
                {errors.custom && (
                  <p className="text-red-400 text-xs">{errors.custom}</p>
                )}
                <p className="text-gray-500 text-xs ml-auto">
                  {customReason.length}/500
                </p>
              </div>
            </div>
          )}

          {/* Admin Notes (Optional) */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Internal Admin Notes (Optional)
            </label>
            <textarea
              value={adminNotes}
              onChange={(e) => setAdminNotes(e.target.value)}
              placeholder="Internal notes for other administrators..."
              disabled={isProcessing}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent disabled:opacity-50"
              rows={3}
              maxLength={300}
            />
            <p className="text-gray-500 text-xs mt-1">
              {adminNotes.length}/300 • These notes are for internal use only
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3 mt-8">
          <button
            onClick={handleClose}
            disabled={isProcessing}
            className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded-lg transition-colors disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isProcessing || !selectedReason}
            className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-500 text-white rounded-lg transition-colors disabled:opacity-50 font-medium"
          >
            {isProcessing ? (
              <span className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Rejecting...
              </span>
            ) : (
              'Reject KYC'
            )}
          </button>
        </div>

        {/* Warning Notice */}
        <div className="mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
          <p className="text-red-300 text-xs">
            ⚠️ <strong>Important:</strong> The user will receive an email with the rejection reason and instructions for resubmission. Make sure the reason is clear and actionable.
          </p>
        </div>
      </div>
    </div>
  );
};

export default RejectionReasonModal;
