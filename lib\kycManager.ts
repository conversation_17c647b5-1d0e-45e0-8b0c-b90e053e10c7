/**
 * KYC MANAGEMENT SYSTEM
 * 
 * Comprehensive KYC management with database integration,
 * status tracking, and admin review capabilities.
 */

import { supabase, getServiceRoleClient } from './supabase';
import { facialRecognition } from './facialRecognition';
import { documentVerification } from './documentVerification';

interface KYCStatus {
  userId: number;
  status: 'not_started' | 'in_progress' | 'pending_review' | 'approved' | 'rejected' | 'expired';
  completionPercentage: number;
  submittedAt?: Date;
  reviewedAt?: Date;
  expiresAt?: Date;
  reviewedBy?: string;
  rejectionReason?: string;
}

interface KYCSubmission {
  userId: number;
  personalInfo: {
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    nationality: string;
    idNumber: string;
    phoneNumber?: string;
    address?: string;
  };
  documents: {
    idDocumentUrl?: string;
    proofOfAddressUrl?: string;
    selfieUrl?: string;
  };
  verification: {
    faceMatchConfidence: number;
    livenessCheckPassed: boolean;
    documentValid: boolean;
    overallScore: number;
  };
}

interface KYCReview {
  kycId: number;
  reviewerId: string;
  decision: 'approve' | 'reject' | 'request_resubmission';
  comments?: string;
  requiredActions?: string[];
}

interface KYCStatistics {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  expired: number;
  averageProcessingTime: number; // in hours
  approvalRate: number; // percentage
}

class KYCManager {
  private readonly KYC_EXPIRY_DAYS = 365; // KYC valid for 1 year
  private readonly APPROVAL_THRESHOLD = 0.8; // 80% confidence required for auto-approval
  private readonly REVIEW_THRESHOLD = 0.6; // Below 60% gets auto-rejected

  /**
   * Get KYC status for user
   */
  async getKYCStatus(userId: number): Promise<KYCStatus> {
    try {
      console.log(`📋 Getting KYC status for user ${userId}...`);

      const serviceClient = getServiceRoleClient()
      const { data: kycData, error } = await serviceClient
        .from('kyc_information')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.error('❌ Error fetching KYC data:', error);
        throw error;
      }

      if (!kycData) {
        return {
          userId,
          status: 'not_started',
          completionPercentage: 0
        };
      }

      // Calculate completion percentage
      const completionPercentage = this.calculateCompletionPercentage(kycData);

      // Check if KYC has expired
      const isExpired = kycData.expires_at && new Date(kycData.expires_at) < new Date();

      const status: KYCStatus = {
        userId,
        status: isExpired ? 'expired' : kycData.verification_status,
        completionPercentage,
        submittedAt: kycData.submitted_at ? new Date(kycData.submitted_at) : undefined,
        reviewedAt: kycData.reviewed_at ? new Date(kycData.reviewed_at) : undefined,
        expiresAt: kycData.expires_at ? new Date(kycData.expires_at) : undefined,
        reviewedBy: kycData.reviewed_by,
        rejectionReason: kycData.rejection_reason
      };

      console.log(`✅ KYC status retrieved: ${status.status} (${status.completionPercentage}%)`);
      return status;

    } catch (error) {
      console.error('❌ Error getting KYC status:', error);
      return {
        userId,
        status: 'not_started',
        completionPercentage: 0
      };
    }
  }

  /**
   * Submit KYC for verification
   */
  async submitKYC(submission: KYCSubmission): Promise<{ success: boolean; kycId?: number; error?: string }> {
    try {
      console.log(`📤 Submitting KYC for user ${submission.userId}...`);

      // Calculate expiry date
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + this.KYC_EXPIRY_DAYS);

      // Determine initial status based on verification scores
      let initialStatus = 'pending_review';
      if (submission.verification.overallScore >= this.APPROVAL_THRESHOLD) {
        initialStatus = 'approved'; // Auto-approve high confidence submissions
      } else if (submission.verification.overallScore < this.REVIEW_THRESHOLD) {
        initialStatus = 'rejected'; // Auto-reject low confidence submissions
      }

      // Insert or update KYC information
      const { data: kycData, error: kycError } = await supabase
        .from('kyc_information')
        .upsert({
          user_id: submission.userId,
          first_name: submission.personalInfo.firstName,
          last_name: submission.personalInfo.lastName,
          date_of_birth: submission.personalInfo.dateOfBirth,
          nationality: submission.personalInfo.nationality,
          id_number: submission.personalInfo.idNumber,
          phone_number: submission.personalInfo.phoneNumber,
          address: submission.personalInfo.address,
          id_document_url: submission.documents.idDocumentUrl,
          proof_of_address_url: submission.documents.proofOfAddressUrl,
          selfie_url: submission.documents.selfieUrl,
          verification_status: initialStatus,
          face_match_confidence: submission.verification.faceMatchConfidence,
          liveness_check_passed: submission.verification.livenessCheckPassed,
          document_valid: submission.verification.documentValid,
          overall_verification_score: submission.verification.overallScore,
          submitted_at: new Date().toISOString(),
          expires_at: expiresAt.toISOString(),
          updated_at: new Date().toISOString()
        })
        .select('id')
        .single();

      if (kycError) {
        console.error('❌ Error submitting KYC:', kycError);
        return { success: false, error: kycError.message };
      }

      // Log KYC submission
      await this.logKYCEvent(submission.userId, 'KYC_SUBMITTED', {
        initialStatus,
        overallScore: submission.verification.overallScore,
        autoProcessed: initialStatus !== 'pending_review'
      });

      // If auto-approved, update user verification status
      if (initialStatus === 'approved') {
        await this.updateUserVerificationStatus(submission.userId, true);
      }

      console.log(`✅ KYC submitted successfully: ${initialStatus}`);
      return { success: true, kycId: kycData.id };

    } catch (error) {
      console.error('❌ KYC submission error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Review KYC submission (admin function)
   */
  async reviewKYC(review: KYCReview): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`👨‍💼 Reviewing KYC ID ${review.kycId}...`);

      // Get current KYC data
      const { data: kycData, error: fetchError } = await supabase
        .from('kyc_information')
        .select('*')
        .eq('id', review.kycId)
        .single();

      if (fetchError || !kycData) {
        return { success: false, error: 'KYC record not found' };
      }

      // Update KYC status
      const newStatus = review.decision === 'approve' ? 'approved' : 
                       review.decision === 'reject' ? 'rejected' : 'pending_review';

      const updateData: any = {
        verification_status: newStatus,
        reviewed_by: review.reviewerId,
        reviewed_at: new Date().toISOString(),
        review_comments: review.comments,
        updated_at: new Date().toISOString()
      };

      if (review.decision === 'reject') {
        updateData.rejection_reason = review.comments;
      }

      if (review.decision === 'request_resubmission') {
        updateData.required_actions = review.requiredActions;
      }

      const { error: updateError } = await supabase
        .from('kyc_information')
        .update(updateData)
        .eq('id', review.kycId);

      if (updateError) {
        console.error('❌ Error updating KYC review:', updateError);
        return { success: false, error: updateError.message };
      }

      // Update user verification status if approved
      if (review.decision === 'approve') {
        await this.updateUserVerificationStatus(kycData.user_id, true);
      }

      // Log review action
      await this.logKYCEvent(kycData.user_id, `KYC_${review.decision.toUpperCase()}`, {
        reviewerId: review.reviewerId,
        comments: review.comments,
        kycId: review.kycId
      });

      console.log(`✅ KYC review completed: ${review.decision}`);
      return { success: true };

    } catch (error) {
      console.error('❌ KYC review error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get pending KYC submissions for admin review
   */
  async getPendingKYCSubmissions(limit: number = 50): Promise<any[]> {
    try {
      console.log('📋 Fetching pending KYC submissions...');

      const { data: pendingKYCs, error } = await supabase
        .from('kyc_information')
        .select(`
          *,
          users!inner(id, email, username, created_at)
        `)
        .eq('verification_status', 'pending_review')
        .order('submitted_at', { ascending: true })
        .limit(limit);

      if (error) {
        console.error('❌ Error fetching pending KYCs:', error);
        return [];
      }

      console.log(`✅ Found ${pendingKYCs?.length || 0} pending KYC submissions`);
      return pendingKYCs || [];

    } catch (error) {
      console.error('❌ Error fetching pending KYCs:', error);
      return [];
    }
  }

  /**
   * Get KYC statistics
   */
  async getKYCStatistics(): Promise<KYCStatistics> {
    try {
      console.log('📊 Calculating KYC statistics...');

      const { data: allKYCs, error } = await supabase
        .from('kyc_information')
        .select('verification_status, submitted_at, reviewed_at');

      if (error) {
        console.error('❌ Error fetching KYC statistics:', error);
        return {
          total: 0,
          pending: 0,
          approved: 0,
          rejected: 0,
          expired: 0,
          averageProcessingTime: 0,
          approvalRate: 0
        };
      }

      const kycs = allKYCs || [];
      const total = kycs.length;
      const pending = kycs.filter(k => k.verification_status === 'pending_review').length;
      const approved = kycs.filter(k => k.verification_status === 'approved').length;
      const rejected = kycs.filter(k => k.verification_status === 'rejected').length;
      const expired = kycs.filter(k => k.verification_status === 'expired').length;

      // Calculate average processing time
      const processedKYCs = kycs.filter(k => k.submitted_at && k.reviewed_at);
      let averageProcessingTime = 0;
      
      if (processedKYCs.length > 0) {
        const totalProcessingTime = processedKYCs.reduce((sum, kyc) => {
          const submitted = new Date(kyc.submitted_at).getTime();
          const reviewed = new Date(kyc.reviewed_at).getTime();
          return sum + (reviewed - submitted);
        }, 0);
        
        averageProcessingTime = totalProcessingTime / processedKYCs.length / (1000 * 60 * 60); // Convert to hours
      }

      // Calculate approval rate
      const reviewedKYCs = approved + rejected;
      const approvalRate = reviewedKYCs > 0 ? (approved / reviewedKYCs) * 100 : 0;

      const statistics: KYCStatistics = {
        total,
        pending,
        approved,
        rejected,
        expired,
        averageProcessingTime: Math.round(averageProcessingTime * 100) / 100,
        approvalRate: Math.round(approvalRate * 100) / 100
      };

      console.log('✅ KYC statistics calculated');
      return statistics;

    } catch (error) {
      console.error('❌ Error calculating KYC statistics:', error);
      return {
        total: 0,
        pending: 0,
        approved: 0,
        rejected: 0,
        expired: 0,
        averageProcessingTime: 0,
        approvalRate: 0
      };
    }
  }

  /**
   * Check if user KYC is valid and not expired
   */
  async isKYCValid(userId: number): Promise<boolean> {
    try {
      const status = await this.getKYCStatus(userId);
      return (status.status === 'approved' || status.status === 'completed') &&
             (!status.expiresAt || status.expiresAt > new Date());
    } catch (error) {
      console.error('❌ Error checking KYC validity:', error);
      return false;
    }
  }

  /**
   * Expire old KYC records (maintenance function)
   */
  async expireOldKYCs(): Promise<{ expired: number }> {
    try {
      console.log('🧹 Expiring old KYC records...');

      const { data: expiredKYCs, error } = await supabase
        .from('kyc_information')
        .update({ 
          verification_status: 'expired',
          updated_at: new Date().toISOString()
        })
        .lt('expires_at', new Date().toISOString())
        .neq('verification_status', 'expired')
        .select('user_id');

      if (error) {
        console.error('❌ Error expiring KYCs:', error);
        return { expired: 0 };
      }

      const expiredCount = expiredKYCs?.length || 0;

      // Update user verification status for expired KYCs
      if (expiredKYCs && expiredKYCs.length > 0) {
        for (const kyc of expiredKYCs) {
          await this.updateUserVerificationStatus(kyc.user_id, false);
          await this.logKYCEvent(kyc.user_id, 'KYC_EXPIRED', {});
        }
      }

      console.log(`✅ Expired ${expiredCount} KYC records`);
      return { expired: expiredCount };

    } catch (error) {
      console.error('❌ Error expiring KYCs:', error);
      return { expired: 0 };
    }
  }

  /**
   * Helper methods
   */
  private calculateCompletionPercentage(kycData: any): number {
    let completed = 0;
    const total = 8; // Total required fields/steps

    if (kycData.first_name) completed++;
    if (kycData.last_name) completed++;
    if (kycData.date_of_birth) completed++;
    if (kycData.id_number) completed++;
    if (kycData.id_document_url) completed++;
    if (kycData.selfie_url) completed++;
    if (kycData.face_match_confidence > 0) completed++;
    if (kycData.liveness_check_passed) completed++;

    return Math.round((completed / total) * 100);
  }

  private async updateUserVerificationStatus(userId: number, isVerified: boolean): Promise<void> {
    try {
      const { error } = await supabase
        .from('users')
        .update({ 
          is_verified: isVerified,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) {
        console.error('❌ Error updating user verification status:', error);
      } else {
        console.log(`✅ User ${userId} verification status updated: ${isVerified}`);
      }

    } catch (error) {
      console.error('❌ Error updating user verification status:', error);
    }
  }

  private async logKYCEvent(userId: number, eventType: string, metadata: any): Promise<void> {
    try {
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_email: 'kyc_system',
          action: eventType,
          target_type: 'kyc_verification',
          target_id: userId.toString(),
          metadata: {
            ...metadata,
            timestamp: new Date().toISOString()
          },
          created_at: new Date().toISOString()
        });

    } catch (error) {
      console.error('❌ Failed to log KYC event:', error);
    }
  }
}

// Create singleton instance
export const kycManager = new KYCManager();

export default kycManager;
