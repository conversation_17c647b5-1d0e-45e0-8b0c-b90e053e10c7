import { supabase, getServiceRoleClient } from '../supabase';
import { competitionService } from './competitionService';

export interface ReferralData {
  userId: number; // The referrer (network leader) ID
  totalReferralVolume: number; // Total USD volume of shares sold through their referral network
  directReferralsCount: number; // Number of people they directly referred
  qualifiedReferralsCount: number; // Number of referrals who bought >= $2,500
}

class ReferralSyncService {
  private supabase = getServiceRoleClient();

  /**
   * Sync referral data from the bot system to the competition system
   */
  async syncReferralData(): Promise<boolean> {
    try {
      console.log('🔄 Starting referral data sync...');

      // Get current active competition
      const competition = await competitionService.getCurrentCompetition();
      if (!competition) {
        console.log('⚠️ No active competition found, skipping sync');
        return false;
      }

      // Get referral data from the bot system
      const referralData = await this.getReferralDataFromBot();
      if (!referralData || referralData.length === 0) {
        console.log('ℹ️ No referral data found to sync');
        return true;
      }

      // Update competition participants
      let syncedCount = 0;
      for (const data of referralData) {
        const success = await competitionService.updateParticipant(
          competition.id,
          data.userId,
          data.totalReferralVolume,
          data.directReferralsCount,
          data.qualifiedReferralsCount
        );

        if (success) {
          syncedCount++;
        }
      }

      console.log(`✅ Synced ${syncedCount}/${referralData.length} participants`);
      return true;

    } catch (error) {
      console.error('❌ Error syncing referral data:', error);
      return false;
    }
  }

  /**
   * Get referral data from the bot system
   * This queries the existing referrals and share purchases tables
   */
  private async getReferralDataFromBot(): Promise<ReferralData[]> {
    try {
      // Query to get referral statistics for each user
      const { data, error } = await this.supabase.rpc('get_referral_statistics');

      if (error) {
        console.error('Error fetching referral statistics:', error);
        
        // Fallback: manual calculation
        return await this.calculateReferralDataManually();
      }

      return data || [];
    } catch (error) {
      console.error('Error in getReferralDataFromBot:', error);
      return await this.calculateReferralDataManually();
    }
  }

  /**
   * Manually calculate referral data from existing tables
   */
  private async calculateReferralDataManually(): Promise<ReferralData[]> {
    try {
      // Get all referrals with their associated purchases
      const { data: referrals, error: referralsError } = await this.supabase
        .from('referrals')
        .select(`
          referrer_id,
          referred_id,
          users!referrals_referred_id_fkey (
            id,
            aureus_share_purchases (
              total_amount_usd,
              status
            )
          )
        `)
        .eq('status', 'active');

      if (referralsError) {
        console.error('Error fetching referrals:', referralsError);
        return [];
      }

      // Group by referrer and calculate totals
      const referrerStats = new Map<number, ReferralData>();

      for (const referral of referrals || []) {
        const referrerId = referral.referrer_id;
        const referredUser = referral.users;

        if (!referredUser) continue;

        // Initialize referrer stats if not exists
        if (!referrerStats.has(referrerId)) {
          referrerStats.set(referrerId, {
            userId: referrerId,
            totalReferralVolume: 0,
            directReferralsCount: 0,
            qualifiedReferralsCount: 0
          });
        }

        const stats = referrerStats.get(referrerId)!;
        stats.directReferralsCount++;

        // Calculate total purchase volume for this referred user
        const userPurchases = referredUser.aureus_share_purchases || [];
        const userTotalVolume = userPurchases
          .filter(purchase => purchase.status === 'completed')
          .reduce((sum, purchase) => sum + (purchase.total_amount_usd || 0), 0);

        stats.totalReferralVolume += userTotalVolume;

        // Check if this referral qualifies (minimum $2,500)
        if (userTotalVolume >= 2500) {
          stats.qualifiedReferralsCount++;
        }
      }

      return Array.from(referrerStats.values());

    } catch (error) {
      console.error('Error calculating referral data manually:', error);
      return [];
    }
  }

  /**
   * Create the database function for efficient referral statistics calculation
   * This function calculates network leader performance based on REFERRAL SALES, not personal purchases
   */
  async createReferralStatisticsFunction(): Promise<boolean> {
    try {
      const functionSQL = `
        CREATE OR REPLACE FUNCTION get_referral_statistics()
        RETURNS TABLE (
          userId INTEGER,                    -- The network leader (referrer) ID
          totalReferralVolume DECIMAL(15,2), -- Total USD volume sold through their network
          directReferralsCount INTEGER,      -- Number of people they directly referred
          qualifiedReferralsCount INTEGER    -- Number of referrals who bought >= $2,500
        ) AS $$
        BEGIN
          -- This query calculates performance for NETWORK LEADERS based on their referral sales
          -- NOT based on their own personal purchases
          RETURN QUERY
          SELECT
            r.referrer_id as userId,
            -- Sum of all purchases made by people they referred (their network sales volume)
            COALESCE(SUM(asp.total_amount_usd), 0) as totalReferralVolume,
            -- Count of unique people they directly referred
            COUNT(DISTINCT r.referred_id)::INTEGER as directReferralsCount,
            -- Count of referrals who made qualifying purchases (>= $2,500)
            COUNT(DISTINCT CASE
              WHEN user_totals.total_volume >= 2500 THEN r.referred_id
              ELSE NULL
            END)::INTEGER as qualifiedReferralsCount
          FROM referrals r
          LEFT JOIN (
            -- Calculate total purchase volume per user
            SELECT
              user_id,
              SUM(total_amount_usd) as total_volume
            FROM aureus_share_purchases
            WHERE status = 'completed'
            GROUP BY user_id
          ) user_totals ON r.referred_id = user_totals.user_id
          -- Join with actual purchases made by referred users
          LEFT JOIN aureus_share_purchases asp ON r.referred_id = asp.user_id AND asp.status = 'completed'
          WHERE r.status = 'active'
          GROUP BY r.referrer_id;
        END;
        $$ LANGUAGE plpgsql;
      `;

      const { error } = await this.supabase.rpc('exec_sql', { sql: functionSQL });

      if (error) {
        console.error('Error creating referral statistics function:', error);
        return false;
      }

      console.log('✅ Referral statistics function created successfully');
      return true;

    } catch (error) {
      console.error('Error in createReferralStatisticsFunction:', error);
      return false;
    }
  }

  /**
   * Get leaderboard data for display
   */
  async getLeaderboardData(limit: number = 10) {
    try {
      const competition = await competitionService.getCurrentCompetition();
      if (!competition) {
        return [];
      }

      return await competitionService.getLeaderboard(competition.id, limit);
    } catch (error) {
      console.error('Error getting leaderboard data:', error);
      return [];
    }
  }

  /**
   * Get competition statistics for display
   */
  async getCompetitionStats() {
    try {
      const competition = await competitionService.getCurrentCompetition();
      if (!competition) {
        return {
          totalParticipants: 0,
          qualifiedParticipants: 0,
          leadingVolume: 0,
          totalPrizePool: 0,
          minimumQualification: 2500
        };
      }

      return await competitionService.getCompetitionStats(competition.id);
    } catch (error) {
      console.error('Error getting competition stats:', error);
      return {
        totalParticipants: 0,
        qualifiedParticipants: 0,
        leadingVolume: 0,
        totalPrizePool: 0,
        minimumQualification: 2500
      };
    }
  }

  /**
   * Schedule automatic sync (call this from a cron job or similar)
   */
  async scheduleSync(intervalMinutes: number = 30): Promise<void> {
    console.log(`🕐 Scheduling referral sync every ${intervalMinutes} minutes`);
    
    // Initial sync
    await this.syncReferralData();

    // Set up interval
    setInterval(async () => {
      await this.syncReferralData();
    }, intervalMinutes * 60 * 1000);
  }
}

export const referralSyncService = new ReferralSyncService();
