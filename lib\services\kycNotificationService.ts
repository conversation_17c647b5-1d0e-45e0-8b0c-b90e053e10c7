/**
 * KYC NOTIFICATION SERVICE
 * 
 * Service for sending KYC status change notifications via email
 * using the existing Resend email infrastructure.
 */

import { KYCStatusEmailTemplate, KYCStatusEmailData } from '../email/templates/KYCStatusEmailTemplate';
import { getServiceRoleClient } from '../supabase';
import { resendEmailService } from '../resendEmailService';

export interface KYCNotificationData {
  kycId: string;
  userId: number;
  status: 'approved' | 'rejected';
  rejectionReason?: string;
  comments?: string;
  adminEmail: string;
}

export class KYCNotificationService {
  private emailTemplate: KYCStatusEmailTemplate;
  private serviceClient = getServiceRoleClient();

  constructor() {
    // Initialize email template
    this.emailTemplate = new KYCStatusEmailTemplate();
    console.log('✅ KYC Notification Service initialized with existing email service');
  }

  /**
   * Send KYC status notification email to user
   */
  async sendKYCStatusNotification(notificationData: KYCNotificationData): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`📧 Sending KYC ${notificationData.status} notification for user ${notificationData.userId}`);

      // Get user and KYC information
      const userInfo = await this.getUserAndKYCInfo(notificationData.userId, notificationData.kycId);
      if (!userInfo) {
        return { success: false, error: 'User or KYC information not found' };
      }

      // Prepare email data
      const emailData: KYCStatusEmailData = {
        fullName: userInfo.kyc.full_legal_name || `${userInfo.kyc.first_name} ${userInfo.kyc.last_name}`,
        email: userInfo.user.email,
        username: userInfo.user.username,
        status: notificationData.status,
        rejectionReason: notificationData.rejectionReason,
        comments: notificationData.comments,
        submissionDate: userInfo.kyc.created_at,
        reviewDate: new Date().toISOString(),
        nextSteps: this.getNextSteps(notificationData.status)
      };

      // Send email using existing ResendEmailService
      const emailResult = await this.sendKYCEmailDirectly({
        email: userInfo.user.email,
        fullName: emailData.fullName,
        status: notificationData.status,
        rejectionReason: notificationData.rejectionReason,
        comments: notificationData.comments,
        submissionDate: emailData.submissionDate,
        reviewDate: emailData.reviewDate,
        nextSteps: emailData.nextSteps
      });

      if (!emailResult.success) {
        console.error('❌ Failed to send KYC notification email:', emailResult.error);
        return { success: false, error: emailResult.error || 'Failed to send email' };
      }

      // Log the notification
      await this.logNotification(notificationData, emailResult.messageId || null);

      console.log(`✅ KYC ${notificationData.status} notification sent successfully`);
      return { success: true };

    } catch (error) {
      console.error('❌ Error sending KYC notification:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Send KYC notification email directly using ResendEmailService
   */
  private async sendKYCEmailDirectly(emailData: {
    email: string;
    fullName: string;
    status: 'approved' | 'rejected';
    rejectionReason?: string;
    comments?: string;
    submissionDate: string;
    reviewDate: string;
    nextSteps: string;
  }): Promise<{ success: boolean; error?: string; messageId?: string }> {
    try {
      // Generate email content
      const subject = emailData.status === 'approved'
        ? '🎉 KYC Verification Approved - Welcome to Aureus Alliance!'
        : '❌ KYC Verification Update Required';

      const htmlContent = this.generateKYCEmailHTML(emailData);
      const textContent = this.generateKYCEmailText(emailData);

      // Use the backend API endpoint for sending emails
      const result = await this.sendKYCEmailViaBackendAPI({
        email: emailData.email,
        fullName: emailData.fullName,
        status: emailData.status,
        rejectionReason: emailData.rejectionReason,
        comments: emailData.comments,
        submissionDate: emailData.submissionDate,
        reviewDate: emailData.reviewDate,
        nextSteps: emailData.nextSteps
      });

      if (!result.success) {
        console.error('❌ KYC email service error:', result.error);
        return { success: false, error: result.error || 'Failed to send email' };
      }

      console.log('✅ KYC notification email sent directly:', result.messageId);
      return {
        success: true,
        messageId: result.messageId
      };

    } catch (error) {
      console.error('❌ Error sending KYC email directly:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get user and KYC information for notification
   */
  private async getUserAndKYCInfo(userId: number, kycId: string) {
    try {
      // Get user information
      const { data: user, error: userError } = await this.serviceClient
        .from('users')
        .select('id, username, email, full_name')
        .eq('id', userId)
        .single();

      if (userError || !user) {
        console.error('❌ Error fetching user information:', userError);
        return null;
      }

      // Get KYC information
      const { data: kyc, error: kycError } = await this.serviceClient
        .from('kyc_information')
        .select('*')
        .eq('id', kycId)
        .single();

      if (kycError || !kyc) {
        console.error('❌ Error fetching KYC information:', kycError);
        return null;
      }

      return { user, kyc };

    } catch (error) {
      console.error('❌ Error in getUserAndKYCInfo:', error);
      return null;
    }
  }

  /**
   * Get next steps based on KYC status
   */
  private getNextSteps(status: 'approved' | 'rejected'): string {
    if (status === 'approved') {
      return 'Your account is now fully verified. You can access all platform features including share purchases, commission withdrawals, and certificate requests.';
    } else {
      return 'Please review the feedback provided, make necessary corrections, and resubmit your KYC verification through your dashboard.';
    }
  }

  /**
   * Log notification for audit trail
   */
  private async logNotification(notificationData: KYCNotificationData, messageId: string | null) {
    try {
      // Create notification log entry
      const { error } = await this.serviceClient
        .from('notification_logs')
        .insert({
          user_id: notificationData.userId,
          notification_type: 'kyc_status_change',
          status: 'sent',
          email_message_id: messageId,
          metadata: {
            kyc_id: notificationData.kycId,
            kyc_status: notificationData.status,
            admin_email: notificationData.adminEmail,
            rejection_reason: notificationData.rejectionReason,
            comments: notificationData.comments
          },
          created_at: new Date().toISOString()
        });

      if (error) {
        console.warn('⚠️ Failed to log KYC notification:', error);
      } else {
        console.log('✅ KYC notification logged successfully');
      }

    } catch (error) {
      console.warn('⚠️ Error logging KYC notification:', error);
    }
  }

  /**
   * Send bulk KYC notifications (for batch processing)
   */
  async sendBulkKYCNotifications(notifications: KYCNotificationData[]): Promise<{
    successful: number;
    failed: number;
    errors: Array<{ userId: number; error: string }>;
  }> {
    console.log(`📧 Sending ${notifications.length} bulk KYC notifications...`);

    let successful = 0;
    let failed = 0;
    const errors: Array<{ userId: number; error: string }> = [];

    // Process notifications in batches to avoid rate limiting
    const batchSize = 5;
    for (let i = 0; i < notifications.length; i += batchSize) {
      const batch = notifications.slice(i, i + batchSize);
      
      const promises = batch.map(async (notification) => {
        const result = await this.sendKYCStatusNotification(notification);
        if (result.success) {
          successful++;
        } else {
          failed++;
          errors.push({
            userId: notification.userId,
            error: result.error || 'Unknown error'
          });
        }
      });

      await Promise.all(promises);

      // Add delay between batches to respect rate limits
      if (i + batchSize < notifications.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    console.log(`✅ Bulk KYC notifications completed: ${successful} successful, ${failed} failed`);
    return { successful, failed, errors };
  }

  /**
   * Send KYC email via backend API endpoint
   */
  private async sendKYCEmailViaBackendAPI(emailData: {
    email: string;
    fullName: string;
    status: 'approved' | 'rejected';
    rejectionReason?: string;
    comments?: string;
    submissionDate: string;
    reviewDate: string;
    nextSteps: string;
  }): Promise<{ success: boolean; error?: string; messageId?: string }> {
    try {
      const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8002';

      console.log('📧 Sending KYC notification via backend API to:', emailData.email);

      const response = await fetch(`${API_BASE_URL}/api/send-kyc-notification`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(emailData)
      });

      const result = await response.json();

      if (!response.ok) {
        console.error('❌ Backend API error for KYC notification:', result);
        return {
          success: false,
          error: result.error || result.message || 'Failed to send email via backend API'
        };
      }

      console.log('✅ KYC notification email sent via backend API:', result.messageId);
      return {
        success: true,
        messageId: result.messageId
      };

    } catch (error) {
      console.error('❌ Error calling backend API for KYC notification:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error calling backend API'
      };
    }
  }

  /**
   * Generate HTML email content for KYC notifications
   */
  private generateKYCEmailHTML(emailData: {
    email: string;
    fullName: string;
    status: 'approved' | 'rejected';
    rejectionReason?: string;
    comments?: string;
    submissionDate: string;
    reviewDate: string;
    nextSteps: string;
  }): string {
    const isApproved = emailData.status === 'approved';
    const statusColor = isApproved ? '#10B981' : '#EF4444';
    const statusIcon = isApproved ? '🎉' : '❌';
    const statusText = isApproved ? 'APPROVED' : 'REQUIRES UPDATE';

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>KYC Verification ${statusText}</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8fafc;">
        <div style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">

          <!-- Header -->
          <div style="background: linear-gradient(135deg, #1f2937 0%, #374151 100%); padding: 40px 30px; text-align: center;">
            <img src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-s.png" alt="Aureus Alliance" style="height: 60px; margin-bottom: 20px;">
            <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">KYC Verification ${statusText}</h1>
          </div>

          <!-- Content -->
          <div style="padding: 40px 30px;">
            <div style="text-align: center; margin-bottom: 30px;">
              <div style="display: inline-block; background-color: ${statusColor}; color: white; padding: 12px 24px; border-radius: 25px; font-weight: bold; font-size: 16px;">
                ${statusIcon} ${statusText}
              </div>
            </div>

            <p style="font-size: 16px; color: #374151; margin-bottom: 20px;">Dear ${emailData.fullName},</p>

            ${isApproved ? `
              <p style="font-size: 16px; color: #374151; line-height: 1.6; margin-bottom: 20px;">
                Congratulations! Your KYC (Know Your Customer) verification has been <strong style="color: ${statusColor};">approved</strong>.
                You now have full access to all Aureus Alliance Holdings platform features.
              </p>
            ` : `
              <p style="font-size: 16px; color: #374151; line-height: 1.6; margin-bottom: 20px;">
                Your KYC (Know Your Customer) verification requires some updates. Please review the information below and resubmit your documents.
              </p>
            `}

            ${emailData.rejectionReason ? `
              <div style="background-color: #fef2f2; border-left: 4px solid #ef4444; padding: 16px; margin: 20px 0; border-radius: 4px;">
                <h3 style="color: #dc2626; margin: 0 0 8px 0; font-size: 16px;">Reason for Update Request:</h3>
                <p style="color: #7f1d1d; margin: 0; font-size: 14px;">${emailData.rejectionReason}</p>
              </div>
            ` : ''}

            ${emailData.comments ? `
              <div style="background-color: #f0f9ff; border-left: 4px solid #3b82f6; padding: 16px; margin: 20px 0; border-radius: 4px;">
                <h3 style="color: #1d4ed8; margin: 0 0 8px 0; font-size: 16px;">Admin Comments:</h3>
                <p style="color: #1e40af; margin: 0; font-size: 14px;">${emailData.comments}</p>
              </div>
            ` : ''}

            <div style="background-color: #f9fafb; padding: 20px; border-radius: 8px; margin: 30px 0;">
              <h3 style="color: #374151; margin: 0 0 15px 0; font-size: 18px;">Next Steps:</h3>
              <p style="color: #6b7280; margin: 0; font-size: 14px; line-height: 1.6;">${emailData.nextSteps}</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="https://www.aureus.africa/dashboard" style="display: inline-block; background-color: #d97706; color: white; padding: 14px 28px; text-decoration: none; border-radius: 6px; font-weight: bold; font-size: 16px;">
                Access Your Dashboard
              </a>
            </div>

            <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; margin-top: 30px;">
              <p style="font-size: 12px; color: #9ca3af; margin: 0; text-align: center;">
                Submission Date: ${new Date(emailData.submissionDate).toLocaleDateString()}<br>
                Review Date: ${new Date(emailData.reviewDate).toLocaleDateString()}
              </p>
            </div>
          </div>

          <!-- Footer -->
          <div style="background-color: #f9fafb; padding: 20px 30px; text-align: center; border-top: 1px solid #e5e7eb;">
            <p style="font-size: 12px; color: #6b7280; margin: 0;">
              © 2024 Aureus Alliance Holdings (Pty) Ltd. All rights reserved.<br>
              This is an automated message. Please do not reply to this email.
            </p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate text email content for KYC notifications
   */
  private generateKYCEmailText(emailData: {
    email: string;
    fullName: string;
    status: 'approved' | 'rejected';
    rejectionReason?: string;
    comments?: string;
    submissionDate: string;
    reviewDate: string;
    nextSteps: string;
  }): string {
    const isApproved = emailData.status === 'approved';
    const statusText = isApproved ? 'APPROVED' : 'REQUIRES UPDATE';

    return `
AUREUS ALLIANCE HOLDINGS
KYC Verification ${statusText}

Dear ${emailData.fullName},

${isApproved
  ? `Congratulations! Your KYC (Know Your Customer) verification has been APPROVED. You now have full access to all Aureus Alliance Holdings platform features.`
  : `Your KYC (Know Your Customer) verification requires some updates. Please review the information below and resubmit your documents.`
}

${emailData.rejectionReason ? `
REASON FOR UPDATE REQUEST:
${emailData.rejectionReason}
` : ''}

${emailData.comments ? `
ADMIN COMMENTS:
${emailData.comments}
` : ''}

NEXT STEPS:
${emailData.nextSteps}

Access your dashboard: https://www.aureus.africa/dashboard

---
Submission Date: ${new Date(emailData.submissionDate).toLocaleDateString()}
Review Date: ${new Date(emailData.reviewDate).toLocaleDateString()}

© 2024 Aureus Alliance Holdings (Pty) Ltd. All rights reserved.
This is an automated message. Please do not reply to this email.
    `.trim();
  }

  /**
   * Test email configuration
   */
  async testEmailConfiguration(): Promise<{ success: boolean; error?: string }> {
    try {
      // Test using existing email service
      const testResult = await resendEmailService.sendCustomEmail({
        to: '<EMAIL>',
        subject: 'KYC Notification Service Test',
        htmlContent: '<p>This is a test email from the KYC Notification Service.</p>',
        textContent: 'This is a test email from the KYC Notification Service.',
        category: 'test'
      });

      return testResult;

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}

// Export singleton instance
export const kycNotificationService = new KYCNotificationService();
