#!/usr/bin/env node

/**
 * CERTIFICATE DATA MIGRATION SCRIPT
 * 
 * This script migrates existing certificates to the new dual numbering system:
 * 1. Assigns REF NO based on cumulative share allocation order
 * 2. Assigns CERT NO based on creation order
 * 3. Updates certificate_counters to reflect current state
 * 
 * IMPORTANT: This script should be run ONCE after the new system is deployed
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function getExistingCertificates() {
  console.log('🔍 Fetching existing certificates...');
  
  const { data, error } = await supabase
    .from('certificates')
    .select('id, shares_count, created_at, certificate_number')
    .is('cert_no', null) // Only get certificates that haven't been migrated
    .order('created_at', { ascending: true });

  if (error) {
    console.error('❌ Error fetching certificates:', error.message);
    return null;
  }

  console.log(`📋 Found ${data.length} certificates to migrate`);
  return data;
}

async function resetCounters() {
  console.log('🔄 Resetting certificate counters...');
  
  const { error } = await supabase
    .from('certificate_counters')
    .update({
      total_shares_allocated: 0,
      next_cert_number: 1
    })
    .eq('id', 1);

  if (error) {
    console.error('❌ Error resetting counters:', error.message);
    return false;
  }

  console.log('✅ Counters reset successfully');
  return true;
}

async function migrateCertificate(certificate, certNumber, cumulativeShares) {
  const sharesStart = cumulativeShares + 1;
  const sharesEnd = cumulativeShares + certificate.shares_count;
  const certNo = `0-000-${String(certNumber).padStart(3, '0')}`;
  const refNo = `${sharesStart}-${sharesEnd}`;

  console.log(`🔄 Migrating certificate ${certificate.id}: ${certNo} (${refNo})`);

  const { error } = await supabase
    .from('certificates')
    .update({
      cert_no: certNo,
      ref_no: refNo,
      cumulative_shares_start: sharesStart,
      cumulative_shares_end: sharesEnd
    })
    .eq('id', certificate.id);

  if (error) {
    console.error(`❌ Error migrating certificate ${certificate.id}:`, error.message);
    return false;
  }

  return true;
}

async function updateFinalCounters(totalShares, nextCertNumber) {
  console.log('🔄 Updating final counters...');
  
  const { error } = await supabase
    .from('certificate_counters')
    .update({
      total_shares_allocated: totalShares,
      next_cert_number: nextCertNumber
    })
    .eq('id', 1);

  if (error) {
    console.error('❌ Error updating final counters:', error.message);
    return false;
  }

  console.log(`✅ Final counters updated: ${totalShares} shares, next cert: ${nextCertNumber}`);
  return true;
}

async function verifyMigration() {
  console.log('🧪 Verifying migration...');
  
  // Check that all certificates have the new fields
  const { data: unmigrated, error: unmigratedError } = await supabase
    .from('certificates')
    .select('id')
    .is('cert_no', null);

  if (unmigratedError) {
    console.error('❌ Error checking unmigrated certificates:', unmigratedError.message);
    return false;
  }

  if (unmigrated.length > 0) {
    console.error(`❌ ${unmigrated.length} certificates still unmigrated`);
    return false;
  }

  // Check for duplicate cert_no values
  const { data: duplicates, error: duplicatesError } = await supabase
    .from('certificates')
    .select('cert_no')
    .not('cert_no', 'is', null);

  if (duplicatesError) {
    console.error('❌ Error checking duplicates:', duplicatesError.message);
    return false;
  }

  const certNos = duplicates.map(d => d.cert_no);
  const uniqueCertNos = [...new Set(certNos)];
  
  if (certNos.length !== uniqueCertNos.length) {
    console.error('❌ Duplicate cert_no values found');
    return false;
  }

  // Check counters match reality
  const { data: counters, error: countersError } = await supabase
    .from('certificate_counters')
    .select('*')
    .eq('id', 1)
    .single();

  if (countersError) {
    console.error('❌ Error checking counters:', countersError.message);
    return false;
  }

  const { data: totalShares, error: totalError } = await supabase
    .from('certificates')
    .select('shares_count');

  if (totalError) {
    console.error('❌ Error calculating total shares:', totalError.message);
    return false;
  }

  const actualTotal = totalShares.reduce((sum, cert) => sum + cert.shares_count, 0);
  
  if (actualTotal !== counters.total_shares_allocated) {
    console.error(`❌ Counter mismatch: actual ${actualTotal}, counter ${counters.total_shares_allocated}`);
    return false;
  }

  console.log('✅ Migration verification successful');
  console.log(`📊 Total certificates: ${certNos.length}`);
  console.log(`📊 Total shares: ${actualTotal}`);
  console.log(`📊 Next cert number: ${counters.next_cert_number}`);
  
  return true;
}

async function main() {
  console.log('🚀 CERTIFICATE MIGRATION SCRIPT');
  console.log('===============================');
  console.log('');
  console.log('This will migrate existing certificates to the dual numbering system:');
  console.log('• Assign REF NO based on cumulative share allocation');
  console.log('• Assign CERT NO based on creation order');
  console.log('• Update global counters');
  console.log('');

  try {
    // Step 1: Get existing certificates
    const certificates = await getExistingCertificates();
    if (!certificates) {
      throw new Error('Failed to fetch existing certificates');
    }

    if (certificates.length === 0) {
      console.log('✅ No certificates to migrate - all already have dual numbering');
      return;
    }

    // Step 2: Reset counters to start fresh
    if (!await resetCounters()) {
      throw new Error('Failed to reset counters');
    }

    // Step 3: Migrate each certificate
    let cumulativeShares = 0;
    let certNumber = 1;
    let successCount = 0;

    for (const certificate of certificates) {
      if (await migrateCertificate(certificate, certNumber, cumulativeShares)) {
        cumulativeShares += certificate.shares_count;
        certNumber++;
        successCount++;
      } else {
        console.error(`❌ Failed to migrate certificate ${certificate.id}`);
      }
    }

    // Step 4: Update final counters
    if (!await updateFinalCounters(cumulativeShares, certNumber)) {
      throw new Error('Failed to update final counters');
    }

    // Step 5: Verify migration
    if (!await verifyMigration()) {
      throw new Error('Migration verification failed');
    }

    console.log('');
    console.log('🎉 MIGRATION COMPLETED SUCCESSFULLY!');
    console.log('====================================');
    console.log(`✅ Migrated ${successCount}/${certificates.length} certificates`);
    console.log(`✅ Total shares allocated: ${cumulativeShares}`);
    console.log(`✅ Next certificate number: ${certNumber}`);
    console.log('');
    console.log('🔄 The system is now ready for new certificate creation');

  } catch (error) {
    console.error('');
    console.error('❌ MIGRATION FAILED:', error.message);
    console.error('');
    console.error('⚠️  Please check the database state and run again if needed');
    process.exit(1);
  }
}

// Run the migration
main().catch(console.error);
