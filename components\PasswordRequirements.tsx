import React from 'react';

interface PasswordRequirementsProps {
  password: string;
  requirements?: {
    minLength: boolean;
    hasLowercase: boolean;
    hasUppercase: boolean;
    hasNumber: boolean;
    hasSpecialChar: boolean;
  };
  showRequirements?: boolean;
}

export const PasswordRequirements: React.FC<PasswordRequirementsProps> = ({
  password,
  requirements,
  showRequirements = true
}) => {
  // Calculate requirements if not provided
  const reqs = requirements || {
    minLength: password.length >= 8,
    hasLowercase: /[a-z]/.test(password),
    hasUppercase: /[A-Z]/.test(password),
    hasNumber: /[0-9]/.test(password),
    hasSpecialChar: /[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)
  };

  if (!showRequirements) return null;

  const requirementItems = [
    {
      key: 'minLength',
      text: 'At least 8 characters',
      met: reqs.minLength
    },
    {
      key: 'hasLowercase',
      text: 'One lowercase letter (a-z)',
      met: reqs.hasLowercase
    },
    {
      key: 'hasUppercase',
      text: 'One uppercase letter (A-Z)',
      met: reqs.hasUppercase
    },
    {
      key: 'hasNumber',
      text: 'One number (0-9)',
      met: reqs.hasNumber
    },
    {
      key: 'hasSpecialChar',
      text: 'One special character (!@#$%^&*()_+-=[]{}|;:,.<>?)',
      met: reqs.hasSpecialChar
    }
  ];

  return (
    <div className="password-requirements">
      <div className="requirements-header">
        <span className="requirements-title">Password Requirements:</span>
      </div>
      <ul className="requirements-list">
        {requirementItems.map((item) => (
          <li
            key={item.key}
            className={`requirement-item ${item.met ? 'met' : 'unmet'}`}
          >
            <span className={`requirement-icon ${item.met ? 'met' : 'unmet'}`}>
              {item.met ? '✓' : '○'}
            </span>
            <span className="requirement-text">{item.text}</span>
          </li>
        ))}
      </ul>

      <style jsx>{`
        .password-requirements {
          margin-top: 8px;
          padding: 12px;
          background-color: #1a1a1a;
          border: 1px solid #333;
          border-radius: 6px;
          font-size: 14px;
        }

        .requirements-header {
          margin-bottom: 8px;
        }

        .requirements-title {
          color: #ffc107;
          font-weight: 600;
          font-size: 13px;
        }

        .requirements-list {
          list-style: none;
          padding: 0;
          margin: 0;
        }

        .requirement-item {
          display: flex;
          align-items: center;
          margin-bottom: 4px;
          transition: all 0.2s ease;
        }

        .requirement-item.met {
          color: #10b981;
        }

        .requirement-item.unmet {
          color: #6b7280;
        }

        .requirement-icon {
          width: 16px;
          height: 16px;
          margin-right: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: bold;
        }

        .requirement-icon.met {
          color: #10b981;
        }

        .requirement-icon.unmet {
          color: #6b7280;
        }

        .requirement-text {
          font-size: 13px;
          line-height: 1.3;
        }

        /* Animation for when requirements are met */
        .requirement-item.met .requirement-icon {
          animation: checkmark 0.3s ease-in-out;
        }

        @keyframes checkmark {
          0% {
            transform: scale(0.8);
          }
          50% {
            transform: scale(1.2);
          }
          100% {
            transform: scale(1);
          }
        }
      `}</style>
    </div>
  );
};

export default PasswordRequirements;
