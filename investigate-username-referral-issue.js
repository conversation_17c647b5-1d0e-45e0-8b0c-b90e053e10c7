/**
 * INVESTIGATE USERNAME CHANGE AND REFERRAL CODE ISSUE
 * 
 * This script investigates the issue where:
 * 1. User 139 (Naashie1 -> MIRAMAR) changed username
 * 2. User 367 was supposed to receive shares from user 139 but didn't
 * 3. Referral codes in referrals table still show old username
 * 4. Share transfers may have failed due to username mismatch
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client with service role
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function investigateUsernameReferralIssue() {
  console.log('🔍 INVESTIGATING USERNAME CHANGE AND REFERRAL CODE ISSUE');
  console.log('=======================================================');
  
  try {
    // ===== PART 1: CHECK USER 139 (Naashie1 -> MIRAMAR) =====
    console.log('\n📋 PART 1: Checking User 139 (Naashie1 -> MIRAMAR)');
    
    const { data: user139, error: user139Error } = await supabase
      .from('users')
      .select('*')
      .eq('id', 139)
      .single();
      
    if (user139Error) {
      console.log('❌ Error fetching user 139:', user139Error.message);
      return;
    }
    
    console.log('✅ User 139 current data:');
    console.log('   → ID:', user139.id);
    console.log('   → Username:', user139.username);
    console.log('   → Full Name:', user139.full_name);
    console.log('   → Email:', user139.email);
    console.log('   → Updated At:', user139.updated_at);
    
    // ===== PART 2: CHECK USER 367 (recipient) =====
    console.log('\n📋 PART 2: Checking User 367 (recipient)');
    
    const { data: user367, error: user367Error } = await supabase
      .from('users')
      .select('*')
      .eq('id', 367)
      .single();
      
    if (user367Error) {
      console.log('❌ Error fetching user 367:', user367Error.message);
      return;
    }
    
    console.log('✅ User 367 current data:');
    console.log('   → ID:', user367.id);
    console.log('   → Username:', user367.username);
    console.log('   → Full Name:', user367.full_name);
    console.log('   → Email:', user367.email);
    
    // ===== PART 3: CHECK REFERRALS TABLE =====
    console.log('\n📋 PART 3: Checking Referrals Table for User 139');
    
    const { data: referrals139, error: referralsError } = await supabase
      .from('referrals')
      .select('*')
      .eq('referrer_id', 139);
      
    if (referralsError) {
      console.log('❌ Error fetching referrals:', referralsError.message);
    } else {
      console.log(`✅ Found ${referrals139.length} referrals by user 139:`);
      referrals139.forEach((ref, index) => {
        console.log(`   ${index + 1}. Referral ID: ${ref.id}`);
        console.log(`      → Referred User ID: ${ref.referred_id}`);
        console.log(`      → Referral Code: ${ref.referral_code}`);
        console.log(`      → Status: ${ref.status}`);
        console.log(`      → Created: ${ref.created_at}`);
        
        // Check if referral code contains old username
        if (ref.referral_code && ref.referral_code.includes('Naashie1')) {
          console.log('      ⚠️  ISSUE: Referral code contains old username "Naashie1"');
        }
      });
    }
    
    // ===== PART 4: CHECK SHARE TRANSFERS =====
    console.log('\n📋 PART 4: Checking Share Transfers involving Users 139 and 367');
    
    const { data: transfers, error: transfersError } = await supabase
      .from('share_transfers')
      .select('*')
      .or('sender_user_id.eq.139,recipient_user_id.eq.139,sender_user_id.eq.367,recipient_user_id.eq.367');
      
    if (transfersError) {
      console.log('❌ Error fetching share transfers:', transfersError.message);
    } else {
      console.log(`✅ Found ${transfers.length} share transfers:`);
      transfers.forEach((transfer, index) => {
        console.log(`   ${index + 1}. Transfer ID: ${transfer.id}`);
        console.log(`      → Sender: ${transfer.sender_user_id}`);
        console.log(`      → Recipient: ${transfer.recipient_user_id}`);
        console.log(`      → Shares: ${transfer.shares_transferred}`);
        console.log(`      → Status: ${transfer.status}`);
        console.log(`      → Created: ${transfer.created_at}`);
        console.log(`      → Completed: ${transfer.completed_at}`);
        
        if (transfer.sender_user_id === 139 && transfer.recipient_user_id === 367) {
          console.log('      🎯 FOUND: Transfer from user 139 to user 367!');
        }
      });
    }
    
    // ===== PART 5: CHECK COMMISSION BALANCES =====
    console.log('\n📋 PART 5: Checking Commission Balances');
    
    const { data: commissions, error: commissionsError } = await supabase
      .from('commission_balances')
      .select('*')
      .in('user_id', [139, 367]);
      
    if (commissionsError) {
      console.log('❌ Error fetching commission balances:', commissionsError.message);
    } else {
      console.log('✅ Commission balances:');
      commissions.forEach(comm => {
        const userName = comm.user_id === 139 ? 'User 139 (MIRAMAR)' : 'User 367';
        console.log(`   → ${userName}:`);
        console.log(`     • USDT Balance: $${comm.usdt_balance}`);
        console.log(`     • Share Balance: ${comm.share_balance} shares`);
        console.log(`     • Updated: ${comm.updated_at}`);
      });
    }
    
    // ===== PART 6: CHECK SHARE PURCHASES =====
    console.log('\n📋 PART 6: Checking Share Purchases');
    
    const { data: purchases, error: purchasesError } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .in('user_id', [139, 367])
      .order('created_at', { ascending: false });
      
    if (purchasesError) {
      console.log('❌ Error fetching share purchases:', purchasesError.message);
    } else {
      console.log('✅ Recent share purchases:');
      purchases.forEach(purchase => {
        const userName = purchase.user_id === 139 ? 'User 139 (MIRAMAR)' : 'User 367';
        console.log(`   → ${userName}:`);
        console.log(`     • Shares: ${purchase.shares_purchased}`);
        console.log(`     • Amount: $${purchase.total_amount}`);
        console.log(`     • Status: ${purchase.status}`);
        console.log(`     • Date: ${purchase.created_at}`);
      });
    }
    
    // ===== PART 7: ANALYSIS AND RECOMMENDATIONS =====
    console.log('\n📋 PART 7: Analysis and Recommendations');
    console.log('=====================================');
    
    // Check if referral codes need updating
    const outdatedReferrals = referrals139.filter(ref => 
      ref.referral_code && ref.referral_code.includes('Naashie1')
    );
    
    if (outdatedReferrals.length > 0) {
      console.log('⚠️  ISSUE IDENTIFIED: Outdated referral codes found');
      console.log(`   → ${outdatedReferrals.length} referral codes still contain "Naashie1"`);
      console.log('   → These should be updated to "MIRAMAR"');
      
      console.log('\n🔧 RECOMMENDED FIXES:');
      console.log('1. Update referral codes to use new username');
      console.log('2. Check if share transfer failed due to username mismatch');
      console.log('3. Verify user 367 received the intended shares');
      console.log('4. Update any cached username references');
    } else {
      console.log('✅ No outdated referral codes found');
    }
    
    // Check for failed transfers
    const failedTransfers = transfers.filter(t => 
      t.status === 'failed' || t.status === 'cancelled'
    );
    
    if (failedTransfers.length > 0) {
      console.log(`\n⚠️  FAILED TRANSFERS: ${failedTransfers.length} transfers failed or cancelled`);
      failedTransfers.forEach(transfer => {
        console.log(`   → Transfer ${transfer.id}: ${transfer.status}`);
      });
    }
    
    console.log('\n✅ Investigation complete!');
    
  } catch (error) {
    console.error('💥 Fatal error during investigation:', error);
  }
}

// Run the investigation
investigateUsernameReferralIssue()
  .then(() => {
    console.log('\n🎉 Investigation completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Investigation failed:', error);
    process.exit(1);
  });
