@tailwind base;
@tailwind components;
@tailwind utilities;

/* Basic styling for the app */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #000;
  color: #fff;
}

/* Loading spinner animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Basic layout components */
.page {
  min-height: 100vh;
  background: #000;
  color: #fff;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.hero {
  padding: 4rem 0;
  text-align: center;
}

.hero-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #fbbf24;
  margin-bottom: 1rem;
}

.hero-subtitle {
  font-size: 1.125rem;
  color: #e5e7eb;
  margin-bottom: 2rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin: 2rem 0;
}

.metric-card {
  background: rgba(31, 41, 55, 0.6);
  border: 1px solid rgba(107, 114, 128, 0.3);
  border-radius: 0.5rem;
  padding: 1rem;
  text-align: center;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #fbbf24;
}

.metric-label {
  font-size: 0.875rem;
  color: #9ca3af;
  margin-top: 0.25rem;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 2rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  border: none;
  transition: all 0.2s;
}

.btn-primary {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: #fff;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(251, 191, 36, 0.3);
}

.btn-secondary {
  background: rgba(31, 41, 55, 0.6);
  color: #fff;
  border: 1px solid rgba(107, 114, 128, 0.3);
}

.btn-secondary:hover {
  background: rgba(55, 65, 81, 0.8);
}

.overview {
  padding: 4rem 0;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-header h2 {
  font-size: 2rem;
  font-weight: bold;
  color: #fbbf24;
  margin-bottom: 0.5rem;
}

.section-header p {
  color: #9ca3af;
  font-size: 1.125rem;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.overview-card {
  background: rgba(31, 41, 55, 0.6);
  border: 1px solid rgba(107, 114, 128, 0.3);
  border-radius: 0.75rem;
  padding: 2rem;
  transition: all 0.3s;
}

.overview-card:hover {
  border-color: rgba(251, 191, 36, 0.3);
  transform: translateY(-2px);
}

.card-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.overview-card h3 {
  color: #fbbf24;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.overview-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.overview-card li {
  color: #e5e7eb;
  margin-bottom: 0.5rem;
  padding-left: 0;
}

.overview-card strong {
  color: #fbbf24;
}
