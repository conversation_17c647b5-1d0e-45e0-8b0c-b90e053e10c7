/**
 * COMMISSION SAFEGUARD SERVICE
 * 
 * This service provides bulletproof commission processing with multiple
 * layers of validation and error handling to prevent silent failures.
 * 
 * CRITICAL SAFEGUARDS:
 * 1. Input validation for all parameters
 * 2. Calculation verification
 * 3. Database transaction integrity
 * 4. Automatic error detection and reporting
 * 5. Commission balance consistency checks
 * 6. Comprehensive logging and audit trails
 */

import { supabase, getServiceRoleClient } from '../supabase'

export interface CommissionCalculationData {
  userId: number
  amount: number
  shares: number
  currentPhase: any
  transactionId?: string
  adminProcessed?: boolean
}

export interface CommissionResult {
  success: boolean
  error?: string
  commissionTransactionId?: string
  usdtCommission?: number
  shareCommission?: number
  referrerId?: number
  validationErrors?: string[]
}

export class CommissionSafeguardService {
  
  /**
   * BULLETPROOF COMMISSION PROCESSING
   * This is the main function that should be used for ALL commission processing
   */
  static async processCommissionWithSafeguards(data: CommissionCalculationData): Promise<CommissionResult> {
    const startTime = Date.now()
    const logPrefix = `🛡️  COMMISSION SAFEGUARD [User ${data.userId}]:`
    
    console.log(`${logPrefix} Starting bulletproof commission processing`)
    console.log(`${logPrefix} Input data:`, {
      userId: data.userId,
      amount: data.amount,
      shares: data.shares,
      currentPhase: data.currentPhase?.id,
      transactionId: data.transactionId
    })
    
    try {
      // STEP 1: Input Validation
      const validationResult = this.validateInputs(data)
      if (!validationResult.isValid) {
        console.error(`${logPrefix} Input validation failed:`, validationResult.errors)
        return {
          success: false,
          error: 'Input validation failed',
          validationErrors: validationResult.errors
        }
      }
      
      // STEP 2: Get Referral Information
      const referralResult = await this.getReferralInformation(data.userId)
      if (!referralResult.success) {
        console.log(`${logPrefix} No referrer found - no commission to process`)
        return {
          success: true,
          error: 'No referrer found'
        }
      }
      
      // STEP 3: Calculate Commissions
      const calculationResult = this.calculateCommissions(data.amount, data.shares)
      console.log(`${logPrefix} Commission calculation:`, calculationResult)
      
      // STEP 4: Verify Calculations
      const verificationResult = this.verifyCalculations(calculationResult, data.amount, data.shares)
      if (!verificationResult.isValid) {
        console.error(`${logPrefix} Calculation verification failed:`, verificationResult.errors)
        return {
          success: false,
          error: 'Commission calculation verification failed',
          validationErrors: verificationResult.errors
        }
      }
      
      // STEP 5: Create Commission Transaction
      const transactionResult = await this.createCommissionTransaction({
        referrerId: referralResult.referrerId,
        referredId: data.userId,
        amount: data.amount,
        shares: data.shares,
        usdtCommission: calculationResult.usdtCommission,
        shareCommission: calculationResult.shareCommission,
        currentPhase: data.currentPhase,
        transactionId: data.transactionId
      })
      
      if (!transactionResult.success) {
        console.error(`${logPrefix} Commission transaction creation failed:`, transactionResult.error)
        return transactionResult
      }
      
      // STEP 6: Update Commission Balance
      const balanceResult = await this.updateCommissionBalance(
        referralResult.referrerId,
        calculationResult.usdtCommission,
        calculationResult.shareCommission
      )
      
      if (!balanceResult.success) {
        console.error(`${logPrefix} Commission balance update failed:`, balanceResult.error)
        // This is critical - we need to rollback the transaction
        await this.rollbackCommissionTransaction(transactionResult.commissionTransactionId!)
        return {
          success: false,
          error: 'Commission balance update failed - transaction rolled back'
        }
      }
      
      // STEP 7: Final Verification
      const finalVerification = await this.verifyCommissionIntegrity(
        transactionResult.commissionTransactionId!,
        referralResult.referrerId,
        calculationResult
      )
      
      if (!finalVerification.success) {
        console.error(`${logPrefix} Final verification failed:`, finalVerification.error)
        return {
          success: false,
          error: 'Commission integrity verification failed'
        }
      }
      
      const processingTime = Date.now() - startTime
      console.log(`${logPrefix} ✅ Commission processed successfully in ${processingTime}ms`)
      console.log(`${logPrefix} Commission ID: ${transactionResult.commissionTransactionId}`)
      console.log(`${logPrefix} USDT Commission: $${calculationResult.usdtCommission.toFixed(2)}`)
      console.log(`${logPrefix} Share Commission: ${calculationResult.shareCommission.toFixed(4)} shares`)
      
      return {
        success: true,
        commissionTransactionId: transactionResult.commissionTransactionId,
        usdtCommission: calculationResult.usdtCommission,
        shareCommission: calculationResult.shareCommission,
        referrerId: referralResult.referrerId
      }
      
    } catch (error) {
      const processingTime = Date.now() - startTime
      console.error(`${logPrefix} ❌ Critical error after ${processingTime}ms:`, error)
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }
  
  /**
   * Validate all input parameters
   */
  private static validateInputs(data: CommissionCalculationData): { isValid: boolean; errors: string[] } {
    const errors: string[] = []
    
    if (!data.userId || typeof data.userId !== 'number' || data.userId <= 0) {
      errors.push('Invalid user ID')
    }
    
    if (!data.amount || typeof data.amount !== 'number' || data.amount <= 0) {
      errors.push('Invalid amount - must be positive number')
    }
    
    if (typeof data.shares !== 'number' || data.shares < 0) {
      errors.push('Invalid shares - must be non-negative number')
    }
    
    if (!data.currentPhase || !data.currentPhase.id) {
      errors.push('Invalid current phase - phase information required')
    }
    
    if (data.amount > 10000) {
      errors.push('Amount exceeds maximum allowed ($10,000)')
    }
    
    if (data.shares > 2000) {
      errors.push('Shares exceed maximum allowed (2,000)')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }
  
  /**
   * Get referral information for the user
   */
  private static async getReferralInformation(userId: number): Promise<{ success: boolean; referrerId?: number; error?: string }> {
    try {
      const serviceClient = getServiceRoleClient()
      
      const { data: referralData, error: referralError } = await serviceClient
        .from('referrals')
        .select('referrer_id, referred_id, commission_rate, status')
        .eq('referred_id', userId)
        .eq('status', 'active')
        .single()
      
      if (referralError || !referralData) {
        return { success: false, error: 'No active referrer found' }
      }
      
      return {
        success: true,
        referrerId: referralData.referrer_id
      }
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get referral information'
      }
    }
  }
  
  /**
   * Calculate commissions with standard rates
   */
  private static calculateCommissions(amount: number, shares: number): { usdtCommission: number; shareCommission: number } {
    const COMMISSION_RATE = 0.15 // 15%
    
    return {
      usdtCommission: amount * COMMISSION_RATE,
      shareCommission: shares * COMMISSION_RATE
    }
  }
  
  /**
   * Verify commission calculations are correct
   */
  private static verifyCalculations(
    calculation: { usdtCommission: number; shareCommission: number },
    amount: number,
    shares: number
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = []
    const COMMISSION_RATE = 0.15
    
    const expectedUsdtCommission = amount * COMMISSION_RATE
    const expectedShareCommission = shares * COMMISSION_RATE
    
    if (Math.abs(calculation.usdtCommission - expectedUsdtCommission) > 0.01) {
      errors.push(`USDT commission calculation error: expected ${expectedUsdtCommission.toFixed(2)}, got ${calculation.usdtCommission.toFixed(2)}`)
    }
    
    if (Math.abs(calculation.shareCommission - expectedShareCommission) > 0.001) {
      errors.push(`Share commission calculation error: expected ${expectedShareCommission.toFixed(4)}, got ${calculation.shareCommission.toFixed(4)}`)
    }
    
    if (calculation.usdtCommission < 0 || calculation.shareCommission < 0) {
      errors.push('Commission amounts cannot be negative')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }
  
  /**
   * Create commission transaction with full validation
   */
  private static async createCommissionTransaction(data: {
    referrerId: number
    referredId: number
    amount: number
    shares: number
    usdtCommission: number
    shareCommission: number
    currentPhase: any
    transactionId?: string
  }): Promise<{ success: boolean; commissionTransactionId?: string; error?: string }> {
    try {
      const serviceClient = getServiceRoleClient()
      
      const commissionData = {
        referrer_id: data.referrerId,
        referred_id: data.referredId,
        share_purchase_id: data.transactionId || null,
        commission_rate: 15.00,
        share_purchase_amount: data.amount,
        usdt_commission: data.usdtCommission,
        share_commission: data.shareCommission,
        phase_id: data.currentPhase?.id || null,
        status: 'approved',
        payment_date: new Date().toISOString(),
        created_at: new Date().toISOString()
      }
      
      console.log('🛡️  Creating commission transaction:', commissionData)
      
      const { data: commissionTransaction, error: transactionError } = await serviceClient
        .from('commission_transactions')
        .insert(commissionData)
        .select('id')
        .single()
      
      if (transactionError) {
        throw new Error(`Database error: ${transactionError.message}`)
      }
      
      if (!commissionTransaction?.id) {
        throw new Error('No commission transaction ID returned')
      }
      
      return {
        success: true,
        commissionTransactionId: commissionTransaction.id
      }
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create commission transaction'
      }
    }
  }
  
  /**
   * Update commission balance with atomic operations
   */
  private static async updateCommissionBalance(
    userId: number,
    usdtAmount: number,
    shareAmount: number
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const serviceClient = getServiceRoleClient()
      
      // Get current balance
      const { data: currentBalance, error: balanceError } = await serviceClient
        .from('commission_balances')
        .select('*')
        .eq('user_id', userId)
        .single()
      
      if (balanceError && balanceError.code !== 'PGRST116') {
        throw new Error(`Failed to get current balance: ${balanceError.message}`)
      }
      
      const newUsdtBalance = (parseFloat(currentBalance?.usdt_balance || '0')) + usdtAmount
      const newShareBalance = (parseFloat(currentBalance?.share_balance || '0')) + shareAmount
      const newTotalUsdtEarned = (parseFloat(currentBalance?.total_earned_usdt || '0')) + usdtAmount
      const newTotalSharesEarned = (parseFloat(currentBalance?.total_earned_shares || '0')) + shareAmount
      
      const balanceData = {
        user_id: userId,
        usdt_balance: newUsdtBalance,
        share_balance: newShareBalance,
        total_earned_usdt: newTotalUsdtEarned,
        total_earned_shares: newTotalSharesEarned,
        last_updated: new Date().toISOString()
      }
      
      const { error: updateError } = await serviceClient
        .from('commission_balances')
        .upsert(balanceData, { onConflict: 'user_id' })
      
      if (updateError) {
        throw new Error(`Failed to update balance: ${updateError.message}`)
      }
      
      console.log(`🛡️  Updated commission balance for user ${userId}:`, {
        usdtBalance: newUsdtBalance.toFixed(2),
        shareBalance: newShareBalance.toFixed(4)
      })
      
      return { success: true }
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update commission balance'
      }
    }
  }
  
  /**
   * Rollback commission transaction in case of errors
   */
  private static async rollbackCommissionTransaction(commissionTransactionId: string): Promise<void> {
    try {
      const serviceClient = getServiceRoleClient()
      
      await serviceClient
        .from('commission_transactions')
        .update({
          status: 'rolled_back',
          updated_at: new Date().toISOString()
        })
        .eq('id', commissionTransactionId)
      
      console.log(`🛡️  Rolled back commission transaction: ${commissionTransactionId}`)
      
    } catch (error) {
      console.error(`🛡️  Failed to rollback commission transaction ${commissionTransactionId}:`, error)
    }
  }
  
  /**
   * Verify commission integrity after processing
   */
  private static async verifyCommissionIntegrity(
    commissionTransactionId: string,
    referrerId: number,
    expectedCalculation: { usdtCommission: number; shareCommission: number }
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const serviceClient = getServiceRoleClient()
      
      // Verify commission transaction was created correctly
      const { data: transaction, error: transactionError } = await serviceClient
        .from('commission_transactions')
        .select('*')
        .eq('id', commissionTransactionId)
        .single()
      
      if (transactionError || !transaction) {
        return { success: false, error: 'Commission transaction not found after creation' }
      }
      
      // Verify amounts are correct
      if (Math.abs(parseFloat(transaction.usdt_commission) - expectedCalculation.usdtCommission) > 0.01) {
        return { success: false, error: 'USDT commission amount mismatch in database' }
      }
      
      if (Math.abs(parseFloat(transaction.share_commission) - expectedCalculation.shareCommission) > 0.001) {
        return { success: false, error: 'Share commission amount mismatch in database' }
      }
      
      // Verify commission balance was updated
      const { data: balance, error: balanceError } = await serviceClient
        .from('commission_balances')
        .select('*')
        .eq('user_id', referrerId)
        .single()
      
      if (balanceError || !balance) {
        return { success: false, error: 'Commission balance not found after update' }
      }
      
      console.log(`🛡️  Commission integrity verified for transaction ${commissionTransactionId}`)
      
      return { success: true }
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Commission integrity verification failed'
      }
    }
  }
}
