// EMERGENCY LOGIN BYPASS
// Copy and paste this entire script into the browser console on the login page

const emergencyLogin = async () => {
  console.log('🚨 EMERGENCY LOGIN BYPASS ACTIVATED');
  console.log('===================================');
  
  try {
    // Your account data (from database)
    const telegramId = **********;
    const userData = {
      id: 89,
      username: "<PERSON><PERSON><PERSON>",
      email: "<EMAIL>",
      password_hash: "$2b$12$MWs6j1hQdZjKwipl2Bgt5eGPMa0BbtfHRRs6c6TaYbKE5V.RsYcq6",
      full_name: "<PERSON>",
      phone: "+27 74 449 3251",
      address: null,
      is_active: true,
      is_verified: true,
      telegram_id: telegramId,
      country_of_residence: "ZAF",
      country_name: "South Africa",
      role: "user",
      is_admin: false,
      telegram_username: "<PERSON><PERSON><PERSON>",
      first_name: "<PERSON>",
      last_name: "<PERSON>",
      telegram_connected: true
    };
    
    const telegramData = {
      telegram_id: telegramId,
      username: "<PERSON>_<PERSON>",
      user_id: 89,
      is_registered: true,
      connected: true
    };
    
    // Create complete session data
    const sessionData = {
      userId: userData.id,
      username: userData.username,
      email: userData.email,
      fullName: userData.full_name,
      phone: userData.phone,
      address: userData.address,
      country: userData.country_of_residence,
      isActive: userData.is_active,
      isVerified: userData.is_verified,
      isAdmin: userData.is_admin,
      telegramId: telegramId,
      telegramUsername: userData.telegram_username,
      telegramConnected: true,
      telegramRegistered: true,
      loginMethod: 'emergency_bypass',
      sessionStart: new Date().toISOString(),
      lastActivity: new Date().toISOString()
    };
    
    // Create authenticated user object
    const authenticatedUser = {
      id: `telegram_${telegramId}`,
      email: userData.email,
      database_user: userData,
      account_type: 'telegram_direct',
      user_metadata: {
        telegram_id: telegramId,
        telegram_username: userData.telegram_username,
        full_name: userData.full_name,
        username: userData.username,
        telegram_connected: true,
        telegram_registered: true
      }
    };
    
    // Store all data in localStorage
    localStorage.setItem('aureus_session', JSON.stringify(sessionData));
    localStorage.setItem('aureus_user', JSON.stringify(userData));
    localStorage.setItem('telegram_user', JSON.stringify(telegramData));
    localStorage.setItem('aureus_telegram_user', JSON.stringify(authenticatedUser));
    
    console.log('✅ Emergency login data stored successfully!');
    console.log('📋 Account Details:');
    console.log(`   Name: ${userData.full_name}`);
    console.log(`   Email: ${userData.email}`);
    console.log(`   Telegram ID: ${telegramId}`);
    console.log(`   Username: ${userData.username}`);
    
    console.log('🔄 Redirecting to dashboard...');
    
    // Redirect to dashboard
    window.location.href = '/';
    
  } catch (error) {
    console.error('❌ Emergency login failed:', error);
    alert('Emergency login failed: ' + error.message);
  }
};

// Run the emergency login
emergencyLogin();
