#!/usr/bin/env node

/**
 * GMAIL TO @AUREUS.AFRICA DELIVERY DIAGNOSTIC
 * 
 * This script diagnoses why emails from Gmail (and other external providers)
 * are not being delivered to @aureus.africa email addresses.
 */

import dns from 'dns';
import { promisify } from 'util';
import net from 'net';

const resolveMx = promisify(dns.resolveMx);
const resolveTxt = promisify(dns.resolveTxt);
const resolveA = promisify(dns.resolve4);

const DOMAIN = 'aureus.africa';

async function testSmtpConnection(host, port = 25) {
  return new Promise((resolve) => {
    const socket = new net.Socket();
    const timeout = setTimeout(() => {
      socket.destroy();
      resolve({ success: false, error: 'Connection timeout' });
    }, 10000);

    socket.connect(port, host, () => {
      clearTimeout(timeout);
      socket.destroy();
      resolve({ success: true });
    });

    socket.on('error', (error) => {
      clearTimeout(timeout);
      resolve({ success: false, error: error.message });
    });
  });
}

async function diagnoseGmailDelivery() {
  console.log('🔍 GMAIL → @AUREUS.AFRICA DELIVERY DIAGNOSTIC');
  console.log('==============================================\n');
  
  console.log(`📧 Analyzing incoming email delivery for: ${DOMAIN}\n`);
  
  // Step 1: Check MX Records (for incoming email)
  console.log('1️⃣ CHECKING MX RECORDS (Incoming Email)');
  console.log('----------------------------------------');
  try {
    const mxRecords = await resolveMx(DOMAIN);
    if (mxRecords && mxRecords.length > 0) {
      console.log('✅ MX records found:');
      mxRecords.forEach((record, index) => {
        console.log(`   ${index + 1}. ${record.exchange} (priority: ${record.priority})`);
      });
      
      // Test SMTP connectivity to MX servers
      console.log('\n🔌 Testing SMTP connectivity to MX servers:');
      for (const mx of mxRecords) {
        console.log(`   Testing ${mx.exchange}:25...`);
        const result = await testSmtpConnection(mx.exchange);
        if (result.success) {
          console.log(`   ✅ ${mx.exchange} - SMTP port 25 is accessible`);
        } else {
          console.log(`   ❌ ${mx.exchange} - SMTP connection failed: ${result.error}`);
        }
      }
    } else {
      console.log('❌ No MX records found - incoming email will fail');
    }
  } catch (error) {
    console.log('❌ Error checking MX records:', error.message);
  }
  console.log('');
  
  // Step 2: Check if MX points to correct server
  console.log('2️⃣ CHECKING MX SERVER CONFIGURATION');
  console.log('-----------------------------------');
  try {
    const mxRecords = await resolveMx(DOMAIN);
    if (mxRecords && mxRecords.length > 0) {
      for (const mx of mxRecords) {
        console.log(`📍 Analyzing MX server: ${mx.exchange}`);
        
        // Check if MX points to the domain itself (potential issue)
        if (mx.exchange === DOMAIN) {
          console.log('⚠️ MX record points to domain itself - this may cause issues');
          console.log('   This configuration requires the domain to run its own mail server');
          
          // Check if there's an A record for the domain
          try {
            const aRecords = await resolveA(DOMAIN);
            console.log(`   A record for ${DOMAIN}: ${aRecords.join(', ')}`);
            
            // Test if mail server is running on the domain
            for (const ip of aRecords) {
              console.log(`   Testing mail server on ${ip}:25...`);
              const result = await testSmtpConnection(ip);
              if (result.success) {
                console.log(`   ✅ Mail server responding on ${ip}:25`);
              } else {
                console.log(`   ❌ No mail server on ${ip}:25 - ${result.error}`);
                console.log('   🚨 This is likely the root cause of Gmail delivery issues!');
              }
            }
          } catch (error) {
            console.log(`   ❌ Error resolving A record: ${error.message}`);
          }
        } else {
          // MX points to external server
          console.log(`   ✅ MX points to external server: ${mx.exchange}`);
        }
      }
    }
  } catch (error) {
    console.log('❌ Error analyzing MX configuration:', error.message);
  }
  console.log('');
  
  // Step 3: Check reverse DNS
  console.log('3️⃣ CHECKING REVERSE DNS (PTR RECORDS)');
  console.log('-------------------------------------');
  try {
    const mxRecords = await resolveMx(DOMAIN);
    if (mxRecords && mxRecords.length > 0) {
      for (const mx of mxRecords) {
        if (mx.exchange === DOMAIN) {
          const aRecords = await resolveA(DOMAIN);
          for (const ip of aRecords) {
            console.log(`🔍 Checking reverse DNS for ${ip}...`);
            // Note: Reverse DNS lookup is complex and may require additional libraries
            console.log('   ℹ️ Reverse DNS check requires manual verification');
            console.log(`   Check: https://mxtoolbox.com/ReverseLookup.aspx?ip=${ip}`);
          }
        }
      }
    }
  } catch (error) {
    console.log('❌ Error checking reverse DNS:', error.message);
  }
  console.log('');
  
  // Step 4: Recommendations
  console.log('4️⃣ GMAIL DELIVERY ISSUE DIAGNOSIS');
  console.log('----------------------------------');
  console.log('Based on the analysis above:\n');
  
  console.log('🔍 LIKELY ROOT CAUSES:');
  console.log('1. MX record points to aureus.africa itself');
  console.log('2. No mail server running on aureus.africa:25');
  console.log('3. Gmail cannot deliver emails because there\'s no receiving mail server\n');
  
  console.log('🔧 SOLUTIONS:');
  console.log('Option A - Use Email Hosting Service:');
  console.log('  • Set up email hosting (Google Workspace, Microsoft 365, etc.)');
  console.log('  • Update MX records to point to hosting provider');
  console.log('  • Example: mx1.google.com, mx2.google.com for Google Workspace\n');
  
  console.log('Option B - Set up Mail Server:');
  console.log('  • Install and configure mail server software (Postfix, etc.)');
  console.log('  • Configure firewall to allow port 25');
  console.log('  • Set up proper reverse DNS (PTR records)\n');
  
  console.log('Option C - Email Forwarding:');
  console.log('  • Set up email forwarding service');
  console.log('  • Forward <EMAIL> to working email address');
  console.log('  • Update MX records to forwarding service\n');
  
  console.log('📋 IMMEDIATE TESTING:');
  console.log('1. Try sending email from <NAME_EMAIL>');
  console.log('2. Check Gmail for bounce-back messages');
  console.log('3. Look for error messages like "Mail server not found" or "Connection refused"');
  console.log('4. Use online tools: https://mxtoolbox.com/domain/aureus.africa');
}

// Run the diagnostic
diagnoseGmailDelivery().catch(console.error);
