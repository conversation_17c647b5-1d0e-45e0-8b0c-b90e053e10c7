import React, { useEffect } from 'react';
import { DashboardLayout } from './dashboard/DashboardLayout';
import { useDashboardData } from '../hooks/useDashboardData';
import { useDashboardNavigation, DashboardSection } from '../hooks/useDashboardNavigation.tsx';
import { useUserPermissions } from '../hooks/useUserPermissions';

interface UserDashboardRefactoredProps {
  user: any;
  onLogout: () => void;
  onSwitchDashboard?: (dashboard: 'shareholder' | 'affiliate') => void;
}

export const UserDashboardRefactored: React.FC<UserDashboardRefactoredProps> = ({
  user,
  onLogout,
  onSwitchDashboard
}) => {
  const userId = user?.database_user?.id || user?.id || 0;

  // Custom hooks for data and state management
  const { dashboardData, loading, error, refetch } = useDashboardData(userId);
  const { permissions, impersonationSession, loading: permissionsLoading } = useUserPermissions(user);

  // Navigation state (simplified without URL sync for now)
  const { activeSection, setActiveSection } = useDashboardNavigation('overview');

  // Handle section changes
  const handleSectionChange = (section: DashboardSection) => {
    setActiveSection(section);
  };

  // Handle data refresh
  const handleRefreshData = async () => {
    await refetch();
  };

  // Show loading state while permissions are loading
  if (permissionsLoading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  // Show error state if user is not available
  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 mb-4">
            <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-white mb-2">Authentication Required</h3>
          <p className="text-gray-400 mb-4">Please log in to access your dashboard.</p>
          <button
            onClick={onLogout}
            className="bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-6 rounded-lg transition-colors"
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  return (
    <DashboardLayout
      user={user}
      activeSection={activeSection}
      onSectionChange={handleSectionChange}
      onLogout={onLogout}
      onSwitchDashboard={onSwitchDashboard}
      permissions={permissions}
      impersonationSession={impersonationSession}
      dashboardData={dashboardData}
      loading={loading}
      error={error}
      onRefreshData={handleRefreshData}
    />
  );
};
