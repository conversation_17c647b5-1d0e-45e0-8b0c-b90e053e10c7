import React, { useState, useEffect } from 'react';
import { competitionService, Competition, PrizeTier, LeaderboardEntry, CompetitionStats } from '../../lib/services/competitionService';
import { referralSyncService } from '../../lib/services/referralSyncService';
import { supabase } from '../../lib/supabase';

interface InvestmentPhase {
  id: number;
  phase_number: number;
  phase_name: string;
  price_per_share: number;
  is_active: boolean;
  total_shares_available: number;
  shares_sold: number;
}

interface NewCompetitionForm {
  name: string;
  description: string;
  phaseId: number;
  totalPrizePool: number;
  minimumQualification: number;
  prizes: {
    first: number;
    second: number;
    third: number;
    fourth_to_tenth: number;
  };
}

const PhaseCompetitionManager: React.FC = () => {
  const [competitions, setCompetitions] = useState<Competition[]>([]);
  const [selectedCompetition, setSelectedCompetition] = useState<Competition | null>(null);
  const [prizeTiers, setPrizeTiers] = useState<PrizeTier[]>([]);
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [investmentPhases, setInvestmentPhases] = useState<InvestmentPhase[]>([]);
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState(false);
  const [activeTab, setActiveTab] = useState<'phases' | 'competitions' | 'leaderboard'>('phases');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedPhaseId, setSelectedPhaseId] = useState<number | null>(null);
  const [newCompetition, setNewCompetition] = useState<NewCompetitionForm>({
    name: '',
    description: 'Build your network by referring new investors. Each qualified referral counts toward your ranking.',
    phaseId: 0,
    totalPrizePool: 150000,
    minimumQualification: 2500,
    prizes: {
      first: 60000,
      second: 30000,
      third: 18000,
      fourth_to_tenth: 6000
    }
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      // Load investment phases
      const { data: phases, error: phasesError } = await supabase
        .from('investment_phases')
        .select('*')
        .order('phase_number');

      if (phasesError) throw phasesError;
      setInvestmentPhases(phases || []);

      // Load competitions
      const { data: comps, error: compsError } = await supabase
        .from('competitions')
        .select('*')
        .order('created_at', { ascending: false });

      if (compsError) throw compsError;
      setCompetitions(comps || []);

    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSyncData = async () => {
    setSyncing(true);
    try {
      await referralSyncService.syncReferralData();
      await loadData();
    } catch (error) {
      console.error('Error syncing data:', error);
    } finally {
      setSyncing(false);
    }
  };

  const handleDeleteCompetition = async (competitionId: string, competitionName: string) => {
    if (!confirm(`Are you sure you want to delete "${competitionName}"?\n\nThis will permanently remove the competition and all its prize tiers. This action cannot be undone.`)) {
      return;
    }

    try {
      // Delete prize tiers first (due to foreign key constraint)
      const { error: prizeTiersError } = await supabase
        .from('competition_prize_tiers')
        .delete()
        .eq('competition_id', competitionId);

      if (prizeTiersError) {
        throw prizeTiersError;
      }

      // Delete the competition
      const { error: competitionError } = await supabase
        .from('competitions')
        .delete()
        .eq('id', competitionId);

      if (competitionError) {
        throw competitionError;
      }

      alert(`Competition "${competitionName}" has been deleted successfully.`);
      await loadData(); // Refresh the data

    } catch (error) {
      console.error('Error deleting competition:', error);
      alert('Error deleting competition. Please try again.');
    }
  };

  const handleCreateCompetition = async () => {
    if (!newCompetition.name || !newCompetition.phaseId) {
      alert('Please fill in all required fields');
      return;
    }

    try {
      const phase = investmentPhases.find(p => p.id === newCompetition.phaseId);
      if (!phase) {
        alert('Selected phase not found');
        return;
      }

      // Create competition
      const { data: competition, error: compError } = await supabase
        .from('competitions')
        .insert({
          name: newCompetition.name,
          description: newCompetition.description,
          phase_id: newCompetition.phaseId,
          start_date: new Date().toISOString(),
          minimum_qualification_amount: newCompetition.minimumQualification,
          total_prize_pool: newCompetition.prizes.first + newCompetition.prizes.second + newCompetition.prizes.third + (newCompetition.prizes.fourth_to_tenth * 7),
          status: 'active',
          is_active: true
        })
        .select()
        .single();

      if (compError) throw compError;

      // Create prize tiers based on user input - TOP 10 PRIZES
      const prizeTiers = [
        { tier_name: '1st Place', tier_rank_start: 1, tier_rank_end: 1, prize_amount: newCompetition.prizes.first, display_order: 1, emoji: '🥇' },
        { tier_name: '2nd Place', tier_rank_start: 2, tier_rank_end: 2, prize_amount: newCompetition.prizes.second, display_order: 2, emoji: '🥈' },
        { tier_name: '3rd Place', tier_rank_start: 3, tier_rank_end: 3, prize_amount: newCompetition.prizes.third, display_order: 3, emoji: '🥉' },
        { tier_name: '4th Place', tier_rank_start: 4, tier_rank_end: 4, prize_amount: newCompetition.prizes.fourth_to_tenth, display_order: 4, emoji: '🏆' },
        { tier_name: '5th Place', tier_rank_start: 5, tier_rank_end: 5, prize_amount: newCompetition.prizes.fourth_to_tenth, display_order: 5, emoji: '🏆' },
        { tier_name: '6th Place', tier_rank_start: 6, tier_rank_end: 6, prize_amount: newCompetition.prizes.fourth_to_tenth, display_order: 6, emoji: '🏆' },
        { tier_name: '7th Place', tier_rank_start: 7, tier_rank_end: 7, prize_amount: newCompetition.prizes.fourth_to_tenth, display_order: 7, emoji: '🏆' },
        { tier_name: '8th Place', tier_rank_start: 8, tier_rank_end: 8, prize_amount: newCompetition.prizes.fourth_to_tenth, display_order: 8, emoji: '🏆' },
        { tier_name: '9th Place', tier_rank_start: 9, tier_rank_end: 9, prize_amount: newCompetition.prizes.fourth_to_tenth, display_order: 9, emoji: '🏆' },
        { tier_name: '10th Place', tier_rank_start: 10, tier_rank_end: 10, prize_amount: newCompetition.prizes.fourth_to_tenth, display_order: 10, emoji: '🏆' }
      ];

      const { error: prizeTiersError } = await supabase
        .from('competition_prize_tiers')
        .insert(
          prizeTiers.map(tier => ({
            competition_id: competition.id,
            ...tier
          }))
        );

      if (prizeTiersError) throw prizeTiersError;

      alert(`Competition created successfully for ${phase.phase_name}!`);
      setShowCreateForm(false);
      setNewCompetition({
        name: '',
        description: 'Build your network by referring new investors. Each qualified referral counts toward your ranking.',
        phaseId: 0,
        totalPrizePool: 150000,
        minimumQualification: 2500,
        prizes: {
          first: 60000,
          second: 30000,
          third: 18000,
          fourth_to_tenth: 6000
        }
      });
      await loadData();

    } catch (error) {
      console.error('Error creating competition:', error);
      alert('Error creating competition. Please try again.');
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-white">🏆 Phase Competition Management</h2>
        <div className="glass-card p-6">
          <div className="text-center py-8 text-gray-400">Loading...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-white">🏆 Phase Competition Management</h2>
          <p className="text-gray-400 mt-1">
            Create and manage competitions for each investment phase
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={handleSyncData}
            disabled={syncing}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 text-white px-4 py-2 rounded-lg font-medium flex items-center space-x-2"
          >
            <span>{syncing ? '🔄' : '🔄'}</span>
            <span>{syncing ? 'Syncing...' : 'Sync Referral Data'}</span>
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-700">
        <nav className="flex space-x-8">
          {[
            { id: 'phases', name: 'Phase Overview', icon: '📊' },
            { id: 'competitions', name: 'All Competitions', icon: '🏆' },
            { id: 'leaderboard', name: 'Leaderboard', icon: '🥇' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-yellow-500 text-yellow-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Competition Creation Form */}
      {showCreateForm && (
        <div className="glass-card p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Create New Competition</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Competition Name *
              </label>
              <input
                type="text"
                value={newCompetition.name}
                onChange={(e) => setNewCompetition(prev => ({ ...prev, name: e.target.value }))}
                className="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white"
                placeholder="Gold Diggers Club - Phase X"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Investment Phase *
              </label>
              <select
                value={newCompetition.phaseId}
                onChange={(e) => {
                  const phaseId = parseInt(e.target.value);
                  const phase = investmentPhases.find(p => p.id === phaseId);
                  setNewCompetition(prev => ({
                    ...prev,
                    phaseId,
                    name: phase ? `Gold Diggers Club - ${phase.phase_name}` : prev.name
                  }));
                }}
                className="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white"
              >
                <option value={0}>Select Phase</option>
                {investmentPhases.map(phase => (
                  <option key={phase.id} value={phase.id}>
                    {phase.phase_name} (${phase.price_per_share}/share)
                    {phase.is_active ? ' - ACTIVE' : ''}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Total Prize Pool ($)
              </label>
              <input
                type="number"
                value={newCompetition.totalPrizePool}
                onChange={(e) => setNewCompetition(prev => ({ ...prev, totalPrizePool: parseFloat(e.target.value) || 0 }))}
                className="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white"
                min="0"
                step="1000"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Minimum Qualification ($)
              </label>
              <input
                type="number"
                value={newCompetition.minimumQualification}
                onChange={(e) => setNewCompetition(prev => ({ ...prev, minimumQualification: parseFloat(e.target.value) || 0 }))}
                className="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white"
                min="0"
                step="500"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Description
              </label>
              <textarea
                value={newCompetition.description}
                onChange={(e) => setNewCompetition(prev => ({ ...prev, description: e.target.value }))}
                className="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white"
                rows={3}
              />
            </div>

            {/* Prize Amount Inputs */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Prize Amounts (Top 10 Winners)
              </label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-xs text-gray-400 mb-1">🥇 1st Place ($)</label>
                  <input
                    type="number"
                    value={newCompetition.prizes.first}
                    onChange={(e) => setNewCompetition(prev => ({
                      ...prev,
                      prizes: { ...prev.prizes, first: parseFloat(e.target.value) || 0 }
                    }))}
                    className="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white text-sm"
                    min="0"
                    step="1000"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-400 mb-1">🥈 2nd Place ($)</label>
                  <input
                    type="number"
                    value={newCompetition.prizes.second}
                    onChange={(e) => setNewCompetition(prev => ({
                      ...prev,
                      prizes: { ...prev.prizes, second: parseFloat(e.target.value) || 0 }
                    }))}
                    className="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white text-sm"
                    min="0"
                    step="1000"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-400 mb-1">🥉 3rd Place ($)</label>
                  <input
                    type="number"
                    value={newCompetition.prizes.third}
                    onChange={(e) => setNewCompetition(prev => ({
                      ...prev,
                      prizes: { ...prev.prizes, third: parseFloat(e.target.value) || 0 }
                    }))}
                    className="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white text-sm"
                    min="0"
                    step="1000"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-400 mb-1">🏆 4th-10th Each ($)</label>
                  <input
                    type="number"
                    value={newCompetition.prizes.fourth_to_tenth}
                    onChange={(e) => setNewCompetition(prev => ({
                      ...prev,
                      prizes: { ...prev.prizes, fourth_to_tenth: parseFloat(e.target.value) || 0 }
                    }))}
                    className="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white text-sm"
                    min="0"
                    step="1000"
                  />
                </div>
              </div>
              <div className="mt-2 text-sm text-gray-400">
                Total Prize Pool: ${(newCompetition.prizes.first + newCompetition.prizes.second + newCompetition.prizes.third + (newCompetition.prizes.fourth_to_tenth * 7)).toLocaleString()}
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-2 mt-4">
            <button
              onClick={() => setShowCreateForm(false)}
              className="px-4 py-2 text-gray-300 border border-gray-600 rounded hover:bg-gray-800"
            >
              Cancel
            </button>
            <button
              onClick={handleCreateCompetition}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              Create Competition
            </button>
          </div>
        </div>
      )}

      {/* Tab Content */}
      {activeTab === 'phases' && (
        <div className="space-y-6">
          {/* Phase Overview Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {investmentPhases.map(phase => {
              const phaseCompetitions = competitions.filter(c => c.phase_id === phase.id);
              const activeCompetition = phaseCompetitions.find(c => c.is_active && c.status === 'active');

              return (
                <div key={phase.id} className="glass-card p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-white">
                        {phase.phase_name}
                      </h3>
                      <p className="text-gray-400 text-sm">
                        ${phase.price_per_share}/share
                      </p>
                      <p className="text-gray-500 text-xs">
                        {phase.shares_sold?.toLocaleString() || 0} / {phase.total_shares_available?.toLocaleString() || 0} shares sold
                      </p>
                    </div>
                    <div className={`px-2 py-1 rounded text-xs font-medium ${
                      phase.is_active
                        ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                        : 'bg-gray-500/20 text-gray-400 border border-gray-500/30'
                    }`}>
                      {phase.is_active ? 'ACTIVE' : 'INACTIVE'}
                    </div>
                  </div>

                  {/* Competition Status */}
                  <div className="mb-4">
                    {activeCompetition ? (
                      <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-green-400 font-medium">🏆 Competition Active</span>
                          <span className="text-xs text-gray-400">
                            {activeCompetition.total_participants || 0} participants
                          </span>
                        </div>
                        <p className="text-sm text-gray-300">
                          Prize Pool: {formatCurrency(activeCompetition.total_prize_pool)}
                        </p>
                        <p className="text-xs text-gray-400">
                          Min Qualification: {formatCurrency(activeCompetition.minimum_qualification_amount)}
                        </p>
                      </div>
                    ) : (
                      <div className="bg-gray-500/10 border border-gray-500/30 rounded-lg p-3">
                        <span className="text-gray-400">No active competition</span>
                      </div>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    {activeCompetition ? (
                      <>
                        <button
                          onClick={() => {
                            setSelectedCompetition(activeCompetition);
                            setActiveTab('leaderboard');
                          }}
                          className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm font-medium"
                        >
                          View Leaderboard
                        </button>
                        <button
                          onClick={() => {
                            setSelectedPhaseId(phase.id);
                            setShowCreateForm(true);
                          }}
                          className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded text-sm"
                          title="Manage Competition"
                        >
                          ⚙️
                        </button>
                      </>
                    ) : (
                      <button
                        onClick={() => {
                          setSelectedPhaseId(phase.id);
                          setNewCompetition(prev => ({
                            ...prev,
                            phaseId: phase.id,
                            name: `Gold Diggers Club - ${phase.phase_name}`,
                            prizes: {
                              first: 60000,
                              second: 30000,
                              third: 18000,
                              fourth_to_tenth: 6000
                            }
                          }));
                          setShowCreateForm(true);
                        }}
                        className="w-full bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded text-sm font-medium"
                      >
                        Create Competition
                      </button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Summary Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="glass-card p-4">
              <div className="text-2xl font-bold text-white">{investmentPhases.length}</div>
              <div className="text-sm text-gray-400">Total Phases</div>
            </div>
            <div className="glass-card p-4">
              <div className="text-2xl font-bold text-green-400">
                {investmentPhases.filter(p => p.is_active).length}
              </div>
              <div className="text-sm text-gray-400">Active Phases</div>
            </div>
            <div className="glass-card p-4">
              <div className="text-2xl font-bold text-yellow-400">
                {competitions.filter(c => c.is_active && c.status === 'active').length}
              </div>
              <div className="text-sm text-gray-400">Active Competitions</div>
            </div>
            <div className="glass-card p-4">
              <div className="text-2xl font-bold text-blue-400">
                {formatCurrency(competitions.reduce((sum, c) => sum + (c.total_prize_pool || 0), 0))}
              </div>
              <div className="text-sm text-gray-400">Total Prize Pool</div>
            </div>
          </div>
        </div>
      )}

      {/* All Competitions Tab */}
      {activeTab === 'competitions' && (
        <div className="space-y-6">
          <div className="glass-card p-6">
            <h3 className="text-lg font-semibold text-white mb-4">All Competitions</h3>
            <div className="space-y-4">
              {competitions.length === 0 ? (
                <div className="text-center py-8 text-gray-400">
                  No competitions found. Create competitions for your investment phases.
                </div>
              ) : (
                competitions.map(competition => {
                  const phase = investmentPhases.find(p => p.id === competition.phase_id);
                  return (
                    <div key={competition.id} className="bg-gray-800/50 border border-gray-700 rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-semibold text-white">{competition.name}</h4>
                          <p className="text-sm text-gray-400">
                            {phase ? `${phase.phase_name} (${formatCurrency(phase.price_per_share)}/share)` : 'Unknown Phase'}
                          </p>
                          <p className="text-sm text-gray-300 mt-1">
                            Prize Pool: {formatCurrency(competition.total_prize_pool)} |
                            Min Qualification: {formatCurrency(competition.minimum_qualification_amount)}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className={`px-2 py-1 rounded text-xs font-medium ${
                            competition.status === 'active' ? 'bg-green-500/20 text-green-400' :
                            competition.status === 'upcoming' ? 'bg-yellow-500/20 text-yellow-400' :
                            'bg-gray-500/20 text-gray-400'
                          }`}>
                            {competition.status?.toUpperCase() || 'UNKNOWN'}
                          </div>
                          <button
                            onClick={() => {
                              setSelectedCompetition(competition);
                              setActiveTab('leaderboard');
                            }}
                            className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm"
                          >
                            View
                          </button>
                          <button
                            onClick={() => handleDeleteCompetition(competition.id, competition.name)}
                            className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm"
                            title="Delete Competition"
                          >
                            Delete
                          </button>
                        </div>
                      </div>
                    </div>
                  );
                })
              )}
            </div>
          </div>
        </div>
      )}

      {/* Leaderboard Tab */}
      {activeTab === 'leaderboard' && (
        <div className="space-y-6">
          <div className="glass-card p-6">
            <h3 className="text-lg font-semibold text-white mb-4">
              {selectedCompetition ? `${selectedCompetition.name} - Leaderboard` : 'Select a Competition'}
            </h3>
            {selectedCompetition ? (
              <div className="text-center py-8 text-gray-400">
                Leaderboard functionality will be implemented here.
                <br />
                Competition: {selectedCompetition.name}
                <br />
                Prize Pool: {formatCurrency(selectedCompetition.total_prize_pool)}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-400">
                Please select a competition from the Phase Overview or All Competitions tab to view its leaderboard.
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default PhaseCompetitionManager;
