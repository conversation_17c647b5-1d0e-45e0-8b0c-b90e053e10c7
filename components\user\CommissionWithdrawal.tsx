import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { validateKYCForFinancialOperations, KYCValidationResult } from '../../lib/kycValidation';

interface CommissionWithdrawalProps {
  userId: number;
  availableUSDT: number;
  availableShares: number;
  currentSharePrice: number;
  onWithdrawalRequest: () => void;
}

export const CommissionWithdrawal: React.FC<CommissionWithdrawalProps> = ({
  userId,
  availableUSDT,
  availableShares,
  currentSharePrice,
  onWithdrawalRequest
}) => {
  const [kycValidation, setKycValidation] = useState<KYCValidationResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [withdrawalType, setWithdrawalType] = useState<'usdt' | 'shares'>('usdt');
  const [withdrawalAmount, setWithdrawalAmount] = useState('');
  const [walletAddress, setWalletAddress] = useState('');
  const [network, setNetwork] = useState('USDT-TRC20');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Load KYC status on component mount
  useEffect(() => {
    const checkKYCStatus = async () => {
      setLoading(true);
      try {
        const validation = await validateKYCForFinancialOperations(userId);
        setKycValidation(validation);
      } catch (error) {
        console.error('Error checking KYC status:', error);
        setKycValidation({
          isValid: false,
          status: 'not_started',
          message: 'Error checking KYC status. Please try again.',
          canWithdraw: false,
          canReceiveDividends: false
        });
      } finally {
        setLoading(false);
      }
    };

    checkKYCStatus();
  }, [userId]);

  // Handle withdrawal request submission
  const handleWithdrawalSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!kycValidation?.canWithdraw) {
      setError('KYC verification required for withdrawals.');
      return;
    }

    const amount = parseFloat(withdrawalAmount);
    if (!amount || amount <= 0) {
      setError('Please enter a valid withdrawal amount.');
      return;
    }

    const maxAmount = withdrawalType === 'usdt' ? availableUSDT : availableShares;
    if (amount > maxAmount) {
      setError(`Insufficient balance. Maximum available: ${maxAmount.toFixed(2)}`);
      return;
    }

    if (withdrawalType === 'usdt' && !walletAddress.trim()) {
      setError('Please enter your wallet address.');
      return;
    }

    setIsSubmitting(true);

    try {
      // Create withdrawal request
      const { error: insertError } = await supabase
        .from('commission_withdrawal_requests')
        .insert({
          user_id: userId,
          withdrawal_amount: amount,
          currency: withdrawalType === 'usdt' ? 'USDT' : 'SHARES',
          wallet_address: withdrawalType === 'usdt' ? walletAddress : null,
          network: withdrawalType === 'usdt' ? network : null,
          status: 'pending',
          withdrawal_method: withdrawalType === 'usdt' ? 'crypto' : 'shares_conversion',
          created_at: new Date().toISOString()
        });

      if (insertError) {
        throw insertError;
      }

      setSuccess(true);
      setWithdrawalAmount('');
      setWalletAddress('');
      onWithdrawalRequest();

      // Reset success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);

    } catch (error: any) {
      console.error('Error submitting withdrawal request:', error);
      setError(error.message || 'Failed to submit withdrawal request. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500"></div>
          <span className="ml-3 text-gray-300">Checking KYC status...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
      <h3 className="text-xl font-bold text-white mb-4">💸 Commission Withdrawal</h3>

      {/* KYC Status Display */}
      <div className={`rounded-lg p-4 mb-6 ${
        kycValidation?.canWithdraw 
          ? 'bg-green-900/20 border border-green-500/30' 
          : 'bg-yellow-900/20 border border-yellow-500/30'
      }`}>
        <div className="flex items-start space-x-3">
          <div className="text-2xl">
            {kycValidation?.canWithdraw ? '✅' : '⚠️'}
          </div>
          <div>
            <h4 className="font-semibold text-white mb-1">
              {kycValidation?.canWithdraw ? 'KYC Verified' : 'KYC Required'}
            </h4>
            <p className={`text-sm ${
              kycValidation?.canWithdraw ? 'text-green-300' : 'text-yellow-300'
            }`}>
              {kycValidation?.message}
            </p>
            {!kycValidation?.canWithdraw && (
              <button
                onClick={() => window.location.href = '#kyc'}
                className="mt-2 bg-yellow-600 hover:bg-yellow-700 text-white text-sm px-3 py-1 rounded transition-colors"
              >
                Complete KYC
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Available Balances */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="bg-gray-700 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-300 mb-1">Available USDT</h4>
          <p className="text-2xl font-bold text-blue-400">${availableUSDT.toFixed(2)}</p>
        </div>
        <div className="bg-gray-700 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-300 mb-1">Available Shares</h4>
          <p className="text-2xl font-bold text-yellow-400">{availableShares.toLocaleString()}</p>
          <p className="text-sm text-gray-400">≈ ${(availableShares * currentSharePrice).toFixed(2)}</p>
        </div>
      </div>

      {/* Withdrawal Form */}
      {kycValidation?.canWithdraw ? (
        <form onSubmit={handleWithdrawalSubmit} className="space-y-4">
          {/* Withdrawal Type */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Withdrawal Type
            </label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  value="usdt"
                  checked={withdrawalType === 'usdt'}
                  onChange={(e) => setWithdrawalType(e.target.value as 'usdt')}
                  className="mr-2"
                />
                <span className="text-white">USDT</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  value="shares"
                  checked={withdrawalType === 'shares'}
                  onChange={(e) => setWithdrawalType(e.target.value as 'shares')}
                  className="mr-2"
                />
                <span className="text-white">Convert to Shares</span>
              </label>
            </div>
          </div>

          {/* Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Amount {withdrawalType === 'usdt' ? '(USDT)' : '(Shares)'}
            </label>
            <input
              type="number"
              value={withdrawalAmount}
              onChange={(e) => setWithdrawalAmount(e.target.value)}
              placeholder={`Enter amount (max: ${
                withdrawalType === 'usdt' 
                  ? availableUSDT.toFixed(2) 
                  : availableShares.toFixed(0)
              })`}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-yellow-500"
              step="0.01"
              min="0"
              max={withdrawalType === 'usdt' ? availableUSDT : availableShares}
            />
          </div>

          {/* Wallet Address (only for USDT) */}
          {withdrawalType === 'usdt' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Network
                </label>
                <select
                  value={network}
                  onChange={(e) => setNetwork(e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-yellow-500"
                >
                  <option value="USDT-TRC20">USDT (TRC20)</option>
                  <option value="USDT-ERC20">USDT (ERC20)</option>
                  <option value="USDT-BEP20">USDT (BEP20)</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Your Wallet Address
                </label>
                <input
                  type="text"
                  value={walletAddress}
                  onChange={(e) => setWalletAddress(e.target.value)}
                  placeholder="Enter your wallet address"
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-yellow-500"
                />
              </div>
            </>
          )}

          {/* Error Message */}
          {error && (
            <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-3">
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          )}

          {/* Success Message */}
          {success && (
            <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-3">
              <p className="text-green-400 text-sm">
                ✅ Withdrawal request submitted successfully! It will be reviewed by our team.
              </p>
            </div>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isSubmitting || !kycValidation?.canWithdraw}
            className="w-full bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-colors"
          >
            {isSubmitting ? 'Submitting...' : `Request ${withdrawalType === 'usdt' ? 'USDT' : 'Share'} Withdrawal`}
          </button>
        </form>
      ) : (
        <div className="text-center py-8">
          <p className="text-gray-400 mb-4">Complete KYC verification to withdraw your commissions.</p>
          <button
            onClick={() => window.location.href = '#kyc'}
            className="bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-6 rounded-lg transition-colors"
          >
            Start KYC Verification
          </button>
        </div>
      )}
    </div>
  );
};
