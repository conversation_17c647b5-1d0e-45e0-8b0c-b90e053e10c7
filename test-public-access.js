// Test public access to investment_phases table
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🧪 Testing Public Access to Investment Phases...\n');

// Create anonymous client (same as what the frontend uses)
const anonClient = createClient(supabaseUrl, supabaseAnonKey);

console.log('🔍 Attempting to fetch investment phases with anonymous client...');

try {
  const { data, error } = await anonClient
    .from('investment_phases')
    .select('id, phase_number, phase_name, price_per_share, is_active')
    .order('phase_number');

  if (error) {
    console.log('❌ Error (this is the problem):', error.message);
    console.log('   Error code:', error.code);
    console.log('   Error details:', error.details);
    
    console.log('\n💡 SOLUTION NEEDED:');
    console.log('   The investment_phases table needs a public read policy');
    console.log('   Current policy only allows authenticated users');
    console.log('   But the affiliate landing page should be public');
    
    console.log('\n🔧 MANUAL FIX INSTRUCTIONS:');
    console.log('   1. Go to https://supabase.com/dashboard');
    console.log('   2. Select your Aureus project');
    console.log('   3. Go to Authentication > Policies');
    console.log('   4. Find the "investment_phases" table');
    console.log('   5. Click "New Policy"');
    console.log('   6. Choose "Get started quickly" > "Enable read access for all users"');
    console.log('   7. Name it: "Public read access for investment phases"');
    console.log('   8. The policy should be: USING (true)');
    console.log('   9. Save the policy');
    
  } else {
    console.log('✅ SUCCESS! Public access is working');
    console.log(`   Found ${data.length} investment phases:`);
    data.forEach(phase => {
      console.log(`   - ${phase.phase_name}: $${phase.price_per_share} ${phase.is_active ? '(ACTIVE)' : ''}`);
    });
    console.log('\n🎉 Your affiliate landing page should work now!');
  }
  
} catch (err) {
  console.log('❌ Unexpected error:', err.message);
}

console.log('\n🔄 After fixing the policy, refresh your affiliate page to test.');
