import { createClient } from "@supabase/supabase-js";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log("🔍 Testing Supabase Connection...");
console.log("=".repeat(50));

// Test basic configuration
console.log("📋 Configuration Check:");
console.log(`URL: ${supabaseUrl}`);
console.log(`Anon Key: ${supabaseAnonKey ? "✅ Present" : "❌ Missing"}`);
console.log(`Service Key: ${supabaseServiceKey ? "✅ Present" : "❌ Missing"}`);
console.log("");

// Create clients
const anonClient = createClient(supabaseUrl, supabaseAnonKey);
const serviceClient = createClient(supabaseUrl, supabaseServiceKey);

async function testConnection() {
  try {
    console.log("🔌 Testing Anonymous Client Connection...");

    // Test basic connection with anon client
    const { data: anonData, error: anonError } = await anonClient
      .from("users")
      .select("count", { count: "exact", head: true });

    if (anonError) {
      console.log("❌ Anonymous client error:", anonError.message);
    } else {
      console.log("✅ Anonymous client connected successfully");
      console.log(`📊 Users table accessible (count query worked)`);
    }

    console.log("");
    console.log("🔧 Testing Service Role Client Connection...");

    // Test service role client
    const { data: serviceData, error: serviceError } = await serviceClient
      .from("users")
      .select("count", { count: "exact", head: true });

    if (serviceError) {
      console.log("❌ Service client error:", serviceError.message);
    } else {
      console.log("✅ Service role client connected successfully");
      console.log(`📊 Full database access confirmed`);
    }

    console.log("");
    console.log("📋 Testing Database Tables...");

    // Test key tables exist
    const tables = [
      "users",
      "telegram_users",
      "aureus_share_purchases",
      "crypto_payment_transactions",
      "referrals",
      "commission_balances",
      "investment_phases",
    ];

    for (const table of tables) {
      try {
        const { error } = await serviceClient.from(table).select("*").limit(1);

        if (error) {
          console.log(`❌ ${table}: ${error.message}`);
        } else {
          console.log(`✅ ${table}: Accessible`);
        }
      } catch (err) {
        console.log(`❌ ${table}: ${err.message}`);
      }
    }

    console.log("");
    console.log("🔐 Testing Authentication...");

    // Test auth functionality
    try {
      const { data: authData, error: authError } =
        await anonClient.auth.getSession();
      console.log("✅ Auth system accessible");
      console.log(
        `📱 Current session: ${authData.session ? "Active" : "None"}`
      );
    } catch (authErr) {
      console.log("❌ Auth system error:", authErr.message);
    }

    console.log("");
    console.log("💾 Testing Storage...");

    // Test storage buckets
    try {
      const { data: buckets, error: storageError } =
        await serviceClient.storage.listBuckets();

      if (storageError) {
        console.log("❌ Storage error:", storageError.message);
      } else {
        console.log("✅ Storage system accessible");
        console.log(
          `📁 Available buckets: ${buckets.map((b) => b.name).join(", ")}`
        );
      }
    } catch (storageErr) {
      console.log("❌ Storage system error:", storageErr.message);
    }

    console.log("");
    console.log("=".repeat(50));
    console.log("🎉 Supabase Connection Test Complete!");
  } catch (error) {
    console.error("❌ Connection test failed:", error);
  }
}

// Run the test
testConnection();
