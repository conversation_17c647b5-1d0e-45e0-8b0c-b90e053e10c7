Full Table & Column Schema

[
  {
    "table_schema": "auth",
    "table_name": "audit_log_entries",
    "column_name": "instance_id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "audit_log_entries",
    "column_name": "id",
    "ordinal_position": 2,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "audit_log_entries",
    "column_name": "payload",
    "ordinal_position": 3,
    "data_type": "json",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "audit_log_entries",
    "column_name": "created_at",
    "ordinal_position": 4,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "audit_log_entries",
    "column_name": "ip_address",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": "''::character varying",
    "character_maximum_length": 64,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "flow_state",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "flow_state",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "flow_state",
    "column_name": "auth_code",
    "ordinal_position": 3,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "flow_state",
    "column_name": "code_challenge_method",
    "ordinal_position": 4,
    "data_type": "USER-DEFINED",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "flow_state",
    "column_name": "code_challenge",
    "ordinal_position": 5,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "flow_state",
    "column_name": "provider_type",
    "ordinal_position": 6,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "flow_state",
    "column_name": "provider_access_token",
    "ordinal_position": 7,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "flow_state",
    "column_name": "provider_refresh_token",
    "ordinal_position": 8,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "flow_state",
    "column_name": "created_at",
    "ordinal_position": 9,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "flow_state",
    "column_name": "updated_at",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "flow_state",
    "column_name": "authentication_method",
    "ordinal_position": 11,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "flow_state",
    "column_name": "auth_code_issued_at",
    "ordinal_position": 12,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "identities",
    "column_name": "provider_id",
    "ordinal_position": 1,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "identities",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "identities",
    "column_name": "identity_data",
    "ordinal_position": 3,
    "data_type": "jsonb",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "identities",
    "column_name": "provider",
    "ordinal_position": 4,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "identities",
    "column_name": "last_sign_in_at",
    "ordinal_position": 5,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "identities",
    "column_name": "created_at",
    "ordinal_position": 6,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "identities",
    "column_name": "updated_at",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "identities",
    "column_name": "email",
    "ordinal_position": 8,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Auth: Email is a generated column that references the optional email property in the identity_data"
  },
  {
    "table_schema": "auth",
    "table_name": "identities",
    "column_name": "id",
    "ordinal_position": 9,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "instances",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "instances",
    "column_name": "uuid",
    "ordinal_position": 2,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "instances",
    "column_name": "raw_base_config",
    "ordinal_position": 3,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "instances",
    "column_name": "created_at",
    "ordinal_position": 4,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "instances",
    "column_name": "updated_at",
    "ordinal_position": 5,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "mfa_amr_claims",
    "column_name": "session_id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "mfa_amr_claims",
    "column_name": "created_at",
    "ordinal_position": 2,
    "data_type": "timestamp with time zone",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "mfa_amr_claims",
    "column_name": "updated_at",
    "ordinal_position": 3,
    "data_type": "timestamp with time zone",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "mfa_amr_claims",
    "column_name": "authentication_method",
    "ordinal_position": 4,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "mfa_amr_claims",
    "column_name": "id",
    "ordinal_position": 5,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "mfa_challenges",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "mfa_challenges",
    "column_name": "factor_id",
    "ordinal_position": 2,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "mfa_challenges",
    "column_name": "created_at",
    "ordinal_position": 3,
    "data_type": "timestamp with time zone",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "mfa_challenges",
    "column_name": "verified_at",
    "ordinal_position": 4,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "mfa_challenges",
    "column_name": "ip_address",
    "ordinal_position": 5,
    "data_type": "inet",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "mfa_challenges",
    "column_name": "otp_code",
    "ordinal_position": 6,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "mfa_challenges",
    "column_name": "web_authn_session_data",
    "ordinal_position": 7,
    "data_type": "jsonb",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "mfa_factors",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "mfa_factors",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "mfa_factors",
    "column_name": "friendly_name",
    "ordinal_position": 3,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "mfa_factors",
    "column_name": "factor_type",
    "ordinal_position": 4,
    "data_type": "USER-DEFINED",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "mfa_factors",
    "column_name": "status",
    "ordinal_position": 5,
    "data_type": "USER-DEFINED",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "mfa_factors",
    "column_name": "created_at",
    "ordinal_position": 6,
    "data_type": "timestamp with time zone",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "mfa_factors",
    "column_name": "updated_at",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "mfa_factors",
    "column_name": "secret",
    "ordinal_position": 8,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "mfa_factors",
    "column_name": "phone",
    "ordinal_position": 9,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "mfa_factors",
    "column_name": "last_challenged_at",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "mfa_factors",
    "column_name": "web_authn_credential",
    "ordinal_position": 11,
    "data_type": "jsonb",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "mfa_factors",
    "column_name": "web_authn_aaguid",
    "ordinal_position": 12,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "one_time_tokens",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "one_time_tokens",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "one_time_tokens",
    "column_name": "token_type",
    "ordinal_position": 3,
    "data_type": "USER-DEFINED",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "one_time_tokens",
    "column_name": "token_hash",
    "ordinal_position": 4,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "one_time_tokens",
    "column_name": "relates_to",
    "ordinal_position": 5,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "one_time_tokens",
    "column_name": "created_at",
    "ordinal_position": 6,
    "data_type": "timestamp without time zone",
    "is_nullable": "NO",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "one_time_tokens",
    "column_name": "updated_at",
    "ordinal_position": 7,
    "data_type": "timestamp without time zone",
    "is_nullable": "NO",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "refresh_tokens",
    "column_name": "instance_id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "refresh_tokens",
    "column_name": "id",
    "ordinal_position": 2,
    "data_type": "bigint",
    "is_nullable": "NO",
    "column_default": "nextval('auth.refresh_tokens_id_seq'::regclass)",
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "refresh_tokens",
    "column_name": "token",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "refresh_tokens",
    "column_name": "user_id",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "refresh_tokens",
    "column_name": "revoked",
    "ordinal_position": 5,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "refresh_tokens",
    "column_name": "created_at",
    "ordinal_position": 6,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "refresh_tokens",
    "column_name": "updated_at",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "refresh_tokens",
    "column_name": "parent",
    "ordinal_position": 8,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "refresh_tokens",
    "column_name": "session_id",
    "ordinal_position": 9,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "saml_providers",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "saml_providers",
    "column_name": "sso_provider_id",
    "ordinal_position": 2,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "saml_providers",
    "column_name": "entity_id",
    "ordinal_position": 3,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "saml_providers",
    "column_name": "metadata_xml",
    "ordinal_position": 4,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "saml_providers",
    "column_name": "metadata_url",
    "ordinal_position": 5,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "saml_providers",
    "column_name": "attribute_mapping",
    "ordinal_position": 6,
    "data_type": "jsonb",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "saml_providers",
    "column_name": "created_at",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "saml_providers",
    "column_name": "updated_at",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "saml_providers",
    "column_name": "name_id_format",
    "ordinal_position": 9,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "saml_relay_states",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "saml_relay_states",
    "column_name": "sso_provider_id",
    "ordinal_position": 2,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "saml_relay_states",
    "column_name": "request_id",
    "ordinal_position": 3,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "saml_relay_states",
    "column_name": "for_email",
    "ordinal_position": 4,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "saml_relay_states",
    "column_name": "redirect_to",
    "ordinal_position": 5,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "saml_relay_states",
    "column_name": "created_at",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "saml_relay_states",
    "column_name": "updated_at",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "saml_relay_states",
    "column_name": "flow_state_id",
    "ordinal_position": 9,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "schema_migrations",
    "column_name": "version",
    "ordinal_position": 1,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "sessions",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "sessions",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "sessions",
    "column_name": "created_at",
    "ordinal_position": 3,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "sessions",
    "column_name": "updated_at",
    "ordinal_position": 4,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "sessions",
    "column_name": "factor_id",
    "ordinal_position": 5,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "sessions",
    "column_name": "aal",
    "ordinal_position": 6,
    "data_type": "USER-DEFINED",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "sessions",
    "column_name": "not_after",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Auth: Not after is a nullable column that contains a timestamp after which the session should be regarded as expired."
  },
  {
    "table_schema": "auth",
    "table_name": "sessions",
    "column_name": "refreshed_at",
    "ordinal_position": 8,
    "data_type": "timestamp without time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "sessions",
    "column_name": "user_agent",
    "ordinal_position": 9,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "sessions",
    "column_name": "ip",
    "ordinal_position": 10,
    "data_type": "inet",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "sessions",
    "column_name": "tag",
    "ordinal_position": 11,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "sso_domains",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "sso_domains",
    "column_name": "sso_provider_id",
    "ordinal_position": 2,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "sso_domains",
    "column_name": "domain",
    "ordinal_position": 3,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "sso_domains",
    "column_name": "created_at",
    "ordinal_position": 4,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "sso_domains",
    "column_name": "updated_at",
    "ordinal_position": 5,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "sso_providers",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "sso_providers",
    "column_name": "resource_id",
    "ordinal_position": 2,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Auth: Uniquely identifies a SSO provider according to a user-chosen resource ID (case insensitive), useful in infrastructure as code."
  },
  {
    "table_schema": "auth",
    "table_name": "sso_providers",
    "column_name": "created_at",
    "ordinal_position": 3,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "sso_providers",
    "column_name": "updated_at",
    "ordinal_position": 4,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "instance_id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "id",
    "ordinal_position": 2,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "aud",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "role",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "email",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "encrypted_password",
    "ordinal_position": 6,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "email_confirmed_at",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "invited_at",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "confirmation_token",
    "ordinal_position": 9,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "confirmation_sent_at",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "recovery_token",
    "ordinal_position": 11,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "recovery_sent_at",
    "ordinal_position": 12,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "email_change_token_new",
    "ordinal_position": 13,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "email_change",
    "ordinal_position": 14,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "email_change_sent_at",
    "ordinal_position": 15,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "last_sign_in_at",
    "ordinal_position": 16,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "raw_app_meta_data",
    "ordinal_position": 17,
    "data_type": "jsonb",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "raw_user_meta_data",
    "ordinal_position": 18,
    "data_type": "jsonb",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "is_super_admin",
    "ordinal_position": 19,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "created_at",
    "ordinal_position": 20,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "updated_at",
    "ordinal_position": 21,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "phone",
    "ordinal_position": 22,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": "NULL::character varying",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "phone_confirmed_at",
    "ordinal_position": 23,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "phone_change",
    "ordinal_position": 24,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": "''::character varying",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "phone_change_token",
    "ordinal_position": 25,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "''::character varying",
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "phone_change_sent_at",
    "ordinal_position": 26,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "confirmed_at",
    "ordinal_position": 27,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "email_change_token_current",
    "ordinal_position": 28,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "''::character varying",
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "email_change_confirm_status",
    "ordinal_position": 29,
    "data_type": "smallint",
    "is_nullable": "YES",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 16,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "banned_until",
    "ordinal_position": 30,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "reauthentication_token",
    "ordinal_position": 31,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "''::character varying",
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "reauthentication_sent_at",
    "ordinal_position": 32,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "is_sso_user",
    "ordinal_position": 33,
    "data_type": "boolean",
    "is_nullable": "NO",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Auth: Set this column to true when the account comes from SSO. These accounts can have duplicate emails."
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "deleted_at",
    "ordinal_position": 34,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "auth",
    "table_name": "users",
    "column_name": "is_anonymous",
    "ordinal_position": 35,
    "data_type": "boolean",
    "is_nullable": "NO",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "userid",
    "ordinal_position": 1,
    "data_type": "oid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "dbid",
    "ordinal_position": 2,
    "data_type": "oid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "toplevel",
    "ordinal_position": 3,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "queryid",
    "ordinal_position": 4,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "query",
    "ordinal_position": 5,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "plans",
    "ordinal_position": 6,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "total_plan_time",
    "ordinal_position": 7,
    "data_type": "double precision",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 53,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "min_plan_time",
    "ordinal_position": 8,
    "data_type": "double precision",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 53,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "max_plan_time",
    "ordinal_position": 9,
    "data_type": "double precision",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 53,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "mean_plan_time",
    "ordinal_position": 10,
    "data_type": "double precision",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 53,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "stddev_plan_time",
    "ordinal_position": 11,
    "data_type": "double precision",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 53,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "calls",
    "ordinal_position": 12,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "total_exec_time",
    "ordinal_position": 13,
    "data_type": "double precision",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 53,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "min_exec_time",
    "ordinal_position": 14,
    "data_type": "double precision",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 53,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "max_exec_time",
    "ordinal_position": 15,
    "data_type": "double precision",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 53,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "mean_exec_time",
    "ordinal_position": 16,
    "data_type": "double precision",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 53,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "stddev_exec_time",
    "ordinal_position": 17,
    "data_type": "double precision",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 53,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "rows",
    "ordinal_position": 18,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "shared_blks_hit",
    "ordinal_position": 19,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "shared_blks_read",
    "ordinal_position": 20,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "shared_blks_dirtied",
    "ordinal_position": 21,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "shared_blks_written",
    "ordinal_position": 22,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "local_blks_hit",
    "ordinal_position": 23,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "local_blks_read",
    "ordinal_position": 24,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "local_blks_dirtied",
    "ordinal_position": 25,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "local_blks_written",
    "ordinal_position": 26,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "temp_blks_read",
    "ordinal_position": 27,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "temp_blks_written",
    "ordinal_position": 28,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "shared_blk_read_time",
    "ordinal_position": 29,
    "data_type": "double precision",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 53,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "shared_blk_write_time",
    "ordinal_position": 30,
    "data_type": "double precision",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 53,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "local_blk_read_time",
    "ordinal_position": 31,
    "data_type": "double precision",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 53,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "local_blk_write_time",
    "ordinal_position": 32,
    "data_type": "double precision",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 53,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "temp_blk_read_time",
    "ordinal_position": 33,
    "data_type": "double precision",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 53,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "temp_blk_write_time",
    "ordinal_position": 34,
    "data_type": "double precision",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 53,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "wal_records",
    "ordinal_position": 35,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "wal_fpi",
    "ordinal_position": 36,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "wal_bytes",
    "ordinal_position": 37,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "jit_functions",
    "ordinal_position": 38,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "jit_generation_time",
    "ordinal_position": 39,
    "data_type": "double precision",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 53,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "jit_inlining_count",
    "ordinal_position": 40,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "jit_inlining_time",
    "ordinal_position": 41,
    "data_type": "double precision",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 53,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "jit_optimization_count",
    "ordinal_position": 42,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "jit_optimization_time",
    "ordinal_position": 43,
    "data_type": "double precision",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 53,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "jit_emission_count",
    "ordinal_position": 44,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "jit_emission_time",
    "ordinal_position": 45,
    "data_type": "double precision",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 53,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "jit_deform_count",
    "ordinal_position": 46,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "jit_deform_time",
    "ordinal_position": 47,
    "data_type": "double precision",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 53,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "stats_since",
    "ordinal_position": 48,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements",
    "column_name": "minmax_stats_since",
    "ordinal_position": 49,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements_info",
    "column_name": "dealloc",
    "ordinal_position": 1,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "extensions",
    "table_name": "pg_stat_statements_info",
    "column_name": "stats_reset",
    "ordinal_position": 2,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_links",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_links",
    "column_name": "web_user_id",
    "ordinal_position": 2,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_links",
    "column_name": "database_user_id",
    "ordinal_position": 3,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_links",
    "column_name": "telegram_id",
    "ordinal_position": 4,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_links",
    "column_name": "link_type",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_links",
    "column_name": "link_status",
    "ordinal_position": 6,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": "'pending'::character varying",
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_links",
    "column_name": "linked_at",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_links",
    "column_name": "confirmed_at",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_links",
    "column_name": "linked_by_user_id",
    "ordinal_position": 9,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_links",
    "column_name": "data_merged",
    "ordinal_position": 10,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_links",
    "column_name": "merge_summary",
    "ordinal_position": 11,
    "data_type": "jsonb",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_links",
    "column_name": "created_at",
    "ordinal_position": 12,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_links",
    "column_name": "updated_at",
    "ordinal_position": 13,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_merge_log",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_merge_log",
    "column_name": "link_id",
    "ordinal_position": 2,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_merge_log",
    "column_name": "source_user_id",
    "ordinal_position": 3,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_merge_log",
    "column_name": "target_user_id",
    "ordinal_position": 4,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_merge_log",
    "column_name": "merge_type",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_merge_log",
    "column_name": "records_affected",
    "ordinal_position": 6,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_merge_log",
    "column_name": "data_summary",
    "ordinal_position": 7,
    "data_type": "jsonb",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_merge_log",
    "column_name": "merge_status",
    "ordinal_position": 8,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": "'pending'::character varying",
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_merge_log",
    "column_name": "error_message",
    "ordinal_position": 9,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_merge_log",
    "column_name": "performed_by",
    "ordinal_position": 10,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_merge_log",
    "column_name": "performed_at",
    "ordinal_position": 11,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "account_merge_log",
    "column_name": "completed_at",
    "ordinal_position": 12,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "active_marketing_materials",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "active_marketing_materials",
    "column_name": "title",
    "ordinal_position": 2,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "active_marketing_materials",
    "column_name": "description",
    "ordinal_position": 3,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "active_marketing_materials",
    "column_name": "file_url",
    "ordinal_position": 4,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "active_marketing_materials",
    "column_name": "file_type",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "active_marketing_materials",
    "column_name": "file_size",
    "ordinal_position": 6,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "active_marketing_materials",
    "column_name": "language",
    "ordinal_position": 7,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 10,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "active_marketing_materials",
    "column_name": "category",
    "ordinal_position": 8,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "active_marketing_materials",
    "column_name": "is_active",
    "ordinal_position": 9,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "active_marketing_materials",
    "column_name": "created_at",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "active_marketing_materials",
    "column_name": "updated_at",
    "ordinal_position": 11,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "active_marketing_materials",
    "column_name": "language_name",
    "ordinal_position": 12,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_audit_logs",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_audit_logs",
    "column_name": "admin_telegram_id",
    "ordinal_position": 2,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": "Telegram ID of admin who performed action. NULL for automated system actions."
  },
  {
    "table_schema": "public",
    "table_name": "admin_audit_logs",
    "column_name": "admin_username",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_audit_logs",
    "column_name": "action",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_audit_logs",
    "column_name": "target_type",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_audit_logs",
    "column_name": "target_id",
    "ordinal_position": 6,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_audit_logs",
    "column_name": "details",
    "ordinal_position": 7,
    "data_type": "jsonb",
    "is_nullable": "YES",
    "column_default": "'{}'::jsonb",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_audit_logs",
    "column_name": "ip_address",
    "ordinal_position": 8,
    "data_type": "inet",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_audit_logs",
    "column_name": "user_agent",
    "ordinal_position": 9,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_audit_logs",
    "column_name": "timestamp",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_commission_conversion_queue",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_commission_conversion_queue",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_commission_conversion_queue",
    "column_name": "username",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_commission_conversion_queue",
    "column_name": "full_name",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_commission_conversion_queue",
    "column_name": "usdt_amount",
    "ordinal_position": 5,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_commission_conversion_queue",
    "column_name": "shares_requested",
    "ordinal_position": 6,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_commission_conversion_queue",
    "column_name": "share_price",
    "ordinal_position": 7,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_commission_conversion_queue",
    "column_name": "phase_number",
    "ordinal_position": 8,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_commission_conversion_queue",
    "column_name": "status",
    "ordinal_position": 9,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_commission_conversion_queue",
    "column_name": "created_at",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_commission_conversion_queue",
    "column_name": "usdt_balance",
    "ordinal_position": 11,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_commission_conversion_queue",
    "column_name": "escrowed_amount",
    "ordinal_position": 12,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_commission_conversion_queue",
    "column_name": "available_balance",
    "ordinal_position": 13,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_commission_conversion_queue",
    "column_name": "conversion_status",
    "ordinal_position": 14,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_notification_preferences",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": "nextval('admin_notification_preferences_id_seq'::regclass)",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_notification_preferences",
    "column_name": "admin_user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_notification_preferences",
    "column_name": "telegram_id",
    "ordinal_position": 3,
    "data_type": "bigint",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_notification_preferences",
    "column_name": "audio_enabled",
    "ordinal_position": 4,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_notification_preferences",
    "column_name": "notification_volume",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'high'::character varying",
    "character_maximum_length": 10,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_notification_preferences",
    "column_name": "new_payment_audio",
    "ordinal_position": 6,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_notification_preferences",
    "column_name": "new_withdrawal_request_audio",
    "ordinal_position": 7,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_notification_preferences",
    "column_name": "new_commission_conversion_audio",
    "ordinal_position": 8,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_notification_preferences",
    "column_name": "system_error_audio",
    "ordinal_position": 9,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_notification_preferences",
    "column_name": "user_registration_audio",
    "ordinal_position": 10,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_notification_preferences",
    "column_name": "high_value_transaction_audio",
    "ordinal_position": 11,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_notification_preferences",
    "column_name": "critical_alerts_audio",
    "ordinal_position": 12,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_notification_preferences",
    "column_name": "high_priority_audio",
    "ordinal_position": 13,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_notification_preferences",
    "column_name": "medium_priority_audio",
    "ordinal_position": 14,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_notification_preferences",
    "column_name": "low_priority_audio",
    "ordinal_position": 15,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_notification_preferences",
    "column_name": "high_value_threshold",
    "ordinal_position": 16,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": "1000.00",
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_notification_preferences",
    "column_name": "quiet_hours_enabled",
    "ordinal_position": 17,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_notification_preferences",
    "column_name": "quiet_hours_start",
    "ordinal_position": 18,
    "data_type": "time without time zone",
    "is_nullable": "YES",
    "column_default": "'23:00:00'::time without time zone",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_notification_preferences",
    "column_name": "quiet_hours_end",
    "ordinal_position": 19,
    "data_type": "time without time zone",
    "is_nullable": "YES",
    "column_default": "'07:00:00'::time without time zone",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_notification_preferences",
    "column_name": "timezone",
    "ordinal_position": 20,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'UTC'::character varying",
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_notification_preferences",
    "column_name": "escalation_enabled",
    "ordinal_position": 21,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_notification_preferences",
    "column_name": "escalation_delay_minutes",
    "ordinal_position": 22,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": "30",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_notification_preferences",
    "column_name": "created_at",
    "ordinal_position": 23,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_notification_preferences",
    "column_name": "updated_at",
    "ordinal_position": 24,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_users",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_users",
    "column_name": "email",
    "ordinal_position": 2,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_users",
    "column_name": "role",
    "ordinal_position": 3,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": "'admin'::text",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_users",
    "column_name": "created_at",
    "ordinal_position": 4,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "admin_users",
    "column_name": "updated_at",
    "ordinal_position": 5,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "aureus_share_purchases",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "aureus_share_purchases",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "aureus_share_purchases",
    "column_name": "package_id",
    "ordinal_position": 3,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "aureus_share_purchases",
    "column_name": "package_name",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "aureus_share_purchases",
    "column_name": "shares_purchased",
    "ordinal_position": 5,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "aureus_share_purchases",
    "column_name": "total_amount",
    "ordinal_position": 6,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "aureus_share_purchases",
    "column_name": "commission_used",
    "ordinal_position": 7,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "aureus_share_purchases",
    "column_name": "remaining_payment",
    "ordinal_position": 8,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "aureus_share_purchases",
    "column_name": "payment_method",
    "ordinal_position": 9,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'crypto'::character varying",
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "aureus_share_purchases",
    "column_name": "status",
    "ordinal_position": 10,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'pending'::character varying",
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "aureus_share_purchases",
    "column_name": "created_at",
    "ordinal_position": 11,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "aureus_share_purchases",
    "column_name": "updated_at",
    "ordinal_position": 12,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "aureus_share_purchases",
    "column_name": "transfer_reference_id",
    "ordinal_position": 13,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "auth_tokens",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "auth_tokens",
    "column_name": "token",
    "ordinal_position": 2,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "auth_tokens",
    "column_name": "telegram_id",
    "ordinal_position": 3,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "auth_tokens",
    "column_name": "user_data",
    "ordinal_position": 4,
    "data_type": "jsonb",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "auth_tokens",
    "column_name": "confirmed",
    "ordinal_position": 5,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "auth_tokens",
    "column_name": "cancelled",
    "ordinal_position": 6,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "auth_tokens",
    "column_name": "created_at",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "auth_tokens",
    "column_name": "expires_at",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "(now() + '00:10:00'::interval)",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "auth_tokens",
    "column_name": "updated_at",
    "ordinal_position": 9,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "auth_tokens",
    "column_name": "user_status",
    "ordinal_position": 10,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "bank_transfer_payments",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "bank_transfer_payments",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "bank_transfer_payments",
    "column_name": "usd_amount",
    "ordinal_position": 3,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "bank_transfer_payments",
    "column_name": "zar_amount",
    "ordinal_position": 4,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "bank_transfer_payments",
    "column_name": "proof_file_id",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "bank_transfer_payments",
    "column_name": "bank_account",
    "ordinal_position": 6,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "bank_transfer_payments",
    "column_name": "status",
    "ordinal_position": 7,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "bank_transfer_payments",
    "column_name": "created_at",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "bank_transfer_payments",
    "column_name": "updated_at",
    "ordinal_position": 9,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "bank_transfer_payments",
    "column_name": "approved_at",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "bank_transfer_payments",
    "column_name": "approved_by_admin_id",
    "ordinal_position": 11,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "certificate_generation_queue",
    "column_name": "kyc_id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "certificate_generation_queue",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "certificate_generation_queue",
    "column_name": "full_legal_name",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 200,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "certificate_generation_queue",
    "column_name": "email_address",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "certificate_generation_queue",
    "column_name": "kyc_completed_at",
    "ordinal_position": 5,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "certificate_generation_queue",
    "column_name": "certificate_requested",
    "ordinal_position": 6,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "certificate_generation_queue",
    "column_name": "certificate_generated_at",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "certificate_generation_queue",
    "column_name": "certificate_sent_at",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "certificate_generation_queue",
    "column_name": "telegram_username",
    "ordinal_position": 9,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "certificate_generation_queue",
    "column_name": "total_shares",
    "ordinal_position": 10,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "certificate_generation_queue",
    "column_name": "latest_purchase_date",
    "ordinal_position": 11,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "certificates",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "certificates",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "certificates",
    "column_name": "purchase_id",
    "ordinal_position": 3,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "certificates",
    "column_name": "certificate_number",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Unique certificate number in format AUR-YYYY-NNNNNN"
  },
  {
    "table_schema": "public",
    "table_name": "certificates",
    "column_name": "shares_count",
    "ordinal_position": 5,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "certificates",
    "column_name": "issue_date",
    "ordinal_position": 6,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "certificates",
    "column_name": "status",
    "ordinal_position": 7,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'issued'::character varying",
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "certificates",
    "column_name": "certificate_data",
    "ordinal_position": 8,
    "data_type": "jsonb",
    "is_nullable": "YES",
    "column_default": "'{}'::jsonb",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Additional certificate metadata stored as JSON"
  },
  {
    "table_schema": "public",
    "table_name": "certificates",
    "column_name": "created_at",
    "ordinal_position": 9,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "certificates",
    "column_name": "updated_at",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_balances",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_balances",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_balances",
    "column_name": "usdt_balance",
    "ordinal_position": 3,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_balances",
    "column_name": "share_balance",
    "ordinal_position": 4,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_balances",
    "column_name": "total_earned_usdt",
    "ordinal_position": 5,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_balances",
    "column_name": "total_earned_shares",
    "ordinal_position": 6,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_balances",
    "column_name": "total_withdrawn",
    "ordinal_position": 7,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_balances",
    "column_name": "last_updated",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_balances",
    "column_name": "created_at",
    "ordinal_position": 9,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_balances",
    "column_name": "escrowed_amount",
    "ordinal_position": 10,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": "0.00",
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_conversions",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_conversions",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_conversions",
    "column_name": "shares_requested",
    "ordinal_position": 3,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_conversions",
    "column_name": "usdt_amount",
    "ordinal_position": 4,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_conversions",
    "column_name": "share_price",
    "ordinal_position": 5,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_conversions",
    "column_name": "phase_id",
    "ordinal_position": 6,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_conversions",
    "column_name": "phase_number",
    "ordinal_position": 7,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_conversions",
    "column_name": "status",
    "ordinal_position": 8,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'pending'::character varying",
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_conversions",
    "column_name": "approved_by_admin_id",
    "ordinal_position": 9,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_conversions",
    "column_name": "approved_at",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_conversions",
    "column_name": "rejected_by_admin_id",
    "ordinal_position": 11,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_conversions",
    "column_name": "rejected_at",
    "ordinal_position": 12,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_conversions",
    "column_name": "rejection_reason",
    "ordinal_position": 13,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_conversions",
    "column_name": "created_at",
    "ordinal_position": 14,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_conversions",
    "column_name": "updated_at",
    "ordinal_position": 15,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_escrow",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_escrow",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_escrow",
    "column_name": "amount",
    "ordinal_position": 3,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 10,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_escrow",
    "column_name": "status",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'escrowed'::character varying",
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_escrow",
    "column_name": "source_type",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'commission'::character varying",
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_escrow",
    "column_name": "source_id",
    "ordinal_position": 6,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_escrow",
    "column_name": "created_at",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_escrow",
    "column_name": "cleared_at",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_escrow",
    "column_name": "cleared_by_admin_id",
    "ordinal_position": 9,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_escrow",
    "column_name": "expires_at",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "(now() + '30 days'::interval)",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_escrow",
    "column_name": "notes",
    "ordinal_position": 11,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_transactions",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_transactions",
    "column_name": "referrer_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_transactions",
    "column_name": "referred_id",
    "ordinal_position": 3,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_transactions",
    "column_name": "share_purchase_id",
    "ordinal_position": 4,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_transactions",
    "column_name": "commission_rate",
    "ordinal_position": 5,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": "15.00",
    "character_maximum_length": null,
    "numeric_precision": 5,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_transactions",
    "column_name": "share_purchase_amount",
    "ordinal_position": 6,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_transactions",
    "column_name": "usdt_commission",
    "ordinal_position": 7,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_transactions",
    "column_name": "share_commission",
    "ordinal_position": 8,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_transactions",
    "column_name": "status",
    "ordinal_position": 9,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'approved'::character varying",
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_transactions",
    "column_name": "payment_date",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_transactions",
    "column_name": "created_at",
    "ordinal_position": 11,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_usage",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_usage",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_usage",
    "column_name": "share_purchase_id",
    "ordinal_position": 3,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_usage",
    "column_name": "commission_amount_used",
    "ordinal_position": 4,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_usage",
    "column_name": "remaining_payment_amount",
    "ordinal_position": 5,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_usage",
    "column_name": "created_at",
    "ordinal_position": 6,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawal_requests",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawal_requests",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawal_requests",
    "column_name": "withdrawal_amount",
    "ordinal_position": 3,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawal_requests",
    "column_name": "wallet_address",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawal_requests",
    "column_name": "network",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawal_requests",
    "column_name": "currency",
    "ordinal_position": 6,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'USDT'::character varying",
    "character_maximum_length": 10,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawal_requests",
    "column_name": "status",
    "ordinal_position": 7,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'pending'::character varying",
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawal_requests",
    "column_name": "admin_notes",
    "ordinal_position": 8,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawal_requests",
    "column_name": "transaction_hash",
    "ordinal_position": 9,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawal_requests",
    "column_name": "processed_at",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawal_requests",
    "column_name": "created_at",
    "ordinal_position": 11,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawals",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawals",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": "Reference to users table"
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawals",
    "column_name": "withdrawal_type",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Type of withdrawal: usdt or shares"
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawals",
    "column_name": "amount",
    "ordinal_position": 4,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": "Amount to withdraw"
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawals",
    "column_name": "wallet_address",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "User wallet address for USDT withdrawals"
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawals",
    "column_name": "status",
    "ordinal_position": 6,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'pending'::character varying",
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Withdrawal status: pending, approved, rejected, completed"
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawals",
    "column_name": "admin_notes",
    "ordinal_position": 7,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Admin notes for withdrawal processing"
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawals",
    "column_name": "processed_by",
    "ordinal_position": 8,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": "Admin user who processed the withdrawal"
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawals",
    "column_name": "processed_at",
    "ordinal_position": 9,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawals",
    "column_name": "created_at",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawals",
    "column_name": "updated_at",
    "ordinal_position": 11,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawals",
    "column_name": "rejected_at",
    "ordinal_position": 12,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawals",
    "column_name": "rejected_by_admin_id",
    "ordinal_position": 13,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawals",
    "column_name": "approved_by_admin_id",
    "ordinal_position": 14,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawals",
    "column_name": "approved_at",
    "ordinal_position": 15,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawals",
    "column_name": "transaction_hash",
    "ordinal_position": 16,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawals",
    "column_name": "completed_at",
    "ordinal_position": 17,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawals",
    "column_name": "network",
    "ordinal_position": 18,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 10,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawals",
    "column_name": "proof_of_payment_url",
    "ordinal_position": 19,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 500,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawals",
    "column_name": "user_confirmed_receipt",
    "ordinal_position": 20,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawals",
    "column_name": "confirmation_timestamp",
    "ordinal_position": 21,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawals",
    "column_name": "rejection_reason",
    "ordinal_position": 22,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawals",
    "column_name": "transaction_fee",
    "ordinal_position": 23,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": "2.00",
    "character_maximum_length": null,
    "numeric_precision": 10,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "commission_withdrawals",
    "column_name": "net_amount",
    "ordinal_position": 24,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 10,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "company_wallets",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": "nextval('company_wallets_id_seq'::regclass)",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "company_wallets",
    "column_name": "network",
    "ordinal_position": 2,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "company_wallets",
    "column_name": "currency",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 10,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "company_wallets",
    "column_name": "wallet_address",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "company_wallets",
    "column_name": "is_active",
    "ordinal_position": 5,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "company_wallets",
    "column_name": "created_at",
    "ordinal_position": 6,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "company_wallets",
    "column_name": "updated_at",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_leaderboard",
    "column_name": "competition_id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_leaderboard",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_leaderboard",
    "column_name": "username",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_leaderboard",
    "column_name": "full_name",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_leaderboard",
    "column_name": "total_referral_volume",
    "ordinal_position": 5,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_leaderboard",
    "column_name": "direct_referrals_count",
    "ordinal_position": 6,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_leaderboard",
    "column_name": "qualified_referrals_count",
    "ordinal_position": 7,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_leaderboard",
    "column_name": "is_qualified",
    "ordinal_position": 8,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_leaderboard",
    "column_name": "current_rank",
    "ordinal_position": 9,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_leaderboard",
    "column_name": "joined_at",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_leaderboard",
    "column_name": "calculated_rank",
    "ordinal_position": 11,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_participants",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_participants",
    "column_name": "competition_id",
    "ordinal_position": 2,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_participants",
    "column_name": "user_id",
    "ordinal_position": 3,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_participants",
    "column_name": "total_referral_volume",
    "ordinal_position": 4,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": "0.00",
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_participants",
    "column_name": "direct_referrals_count",
    "ordinal_position": 5,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_participants",
    "column_name": "qualified_referrals_count",
    "ordinal_position": 6,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_participants",
    "column_name": "is_qualified",
    "ordinal_position": 7,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_participants",
    "column_name": "current_rank",
    "ordinal_position": 8,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_participants",
    "column_name": "final_rank",
    "ordinal_position": 9,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_participants",
    "column_name": "joined_at",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_participants",
    "column_name": "last_updated",
    "ordinal_position": 11,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_prize_tiers",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_prize_tiers",
    "column_name": "competition_id",
    "ordinal_position": 2,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_prize_tiers",
    "column_name": "tier_name",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_prize_tiers",
    "column_name": "tier_rank_start",
    "ordinal_position": 4,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_prize_tiers",
    "column_name": "tier_rank_end",
    "ordinal_position": 5,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_prize_tiers",
    "column_name": "prize_amount",
    "ordinal_position": 6,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_prize_tiers",
    "column_name": "prize_type",
    "ordinal_position": 7,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'usd'::character varying",
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_prize_tiers",
    "column_name": "display_order",
    "ordinal_position": 8,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": "1",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_prize_tiers",
    "column_name": "emoji",
    "ordinal_position": 9,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'🏆'::character varying",
    "character_maximum_length": 10,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competition_prize_tiers",
    "column_name": "created_at",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competitions",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competitions",
    "column_name": "name",
    "ordinal_position": 2,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competitions",
    "column_name": "description",
    "ordinal_position": 3,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competitions",
    "column_name": "phase_id",
    "ordinal_position": 4,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competitions",
    "column_name": "start_date",
    "ordinal_position": 5,
    "data_type": "timestamp with time zone",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competitions",
    "column_name": "end_date",
    "ordinal_position": 6,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competitions",
    "column_name": "is_active",
    "ordinal_position": 7,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competitions",
    "column_name": "minimum_qualification_amount",
    "ordinal_position": 8,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": "2500.00",
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competitions",
    "column_name": "total_prize_pool",
    "ordinal_position": 9,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competitions",
    "column_name": "max_participants",
    "ordinal_position": 10,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competitions",
    "column_name": "status",
    "ordinal_position": 11,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'upcoming'::character varying",
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competitions",
    "column_name": "total_participants",
    "ordinal_position": 12,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competitions",
    "column_name": "qualified_participants",
    "ordinal_position": 13,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competitions",
    "column_name": "created_at",
    "ordinal_position": 14,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competitions",
    "column_name": "updated_at",
    "ordinal_position": 15,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "competitions",
    "column_name": "created_by",
    "ordinal_position": 16,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "country_change_log",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "country_change_log",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "country_change_log",
    "column_name": "old_country_code",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 3,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "country_change_log",
    "column_name": "old_country_name",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "country_change_log",
    "column_name": "new_country_code",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 3,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "country_change_log",
    "column_name": "new_country_name",
    "ordinal_position": 6,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "country_change_log",
    "column_name": "changed_by_telegram_id",
    "ordinal_position": 7,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "country_change_log",
    "column_name": "changed_by_username",
    "ordinal_position": 8,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "country_change_log",
    "column_name": "change_reason",
    "ordinal_position": 9,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "country_change_log",
    "column_name": "changed_at",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "country_statistics",
    "column_name": "country_of_residence",
    "ordinal_position": 1,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 3,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "country_statistics",
    "column_name": "country_name",
    "ordinal_position": 2,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "country_statistics",
    "column_name": "flag_emoji",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 10,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "country_statistics",
    "column_name": "user_count",
    "ordinal_position": 4,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "country_statistics",
    "column_name": "new_users_30d",
    "ordinal_position": 5,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "country_statistics",
    "column_name": "first_selection_date",
    "ordinal_position": 6,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "country_statistics",
    "column_name": "latest_selection_date",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "crypto_payment_transactions",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "crypto_payment_transactions",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "crypto_payment_transactions",
    "column_name": "investment_id",
    "ordinal_position": 3,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "crypto_payment_transactions",
    "column_name": "amount",
    "ordinal_position": 4,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "crypto_payment_transactions",
    "column_name": "currency",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'USDT'::character varying",
    "character_maximum_length": 10,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "crypto_payment_transactions",
    "column_name": "network",
    "ordinal_position": 6,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "crypto_payment_transactions",
    "column_name": "sender_wallet",
    "ordinal_position": 7,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "crypto_payment_transactions",
    "column_name": "receiver_wallet",
    "ordinal_position": 8,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "crypto_payment_transactions",
    "column_name": "transaction_hash",
    "ordinal_position": 9,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "crypto_payment_transactions",
    "column_name": "screenshot_url",
    "ordinal_position": 10,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 500,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "crypto_payment_transactions",
    "column_name": "status",
    "ordinal_position": 11,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'pending'::character varying",
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "crypto_payment_transactions",
    "column_name": "admin_notes",
    "ordinal_position": 12,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "crypto_payment_transactions",
    "column_name": "created_at",
    "ordinal_position": 13,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "crypto_payment_transactions",
    "column_name": "updated_at",
    "ordinal_position": 14,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "crypto_payment_transactions",
    "column_name": "approved_by_admin_id",
    "ordinal_position": 15,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "crypto_payment_transactions",
    "column_name": "approved_at",
    "ordinal_position": 16,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "crypto_payment_transactions",
    "column_name": "rejected_by_admin_id",
    "ordinal_position": 17,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "crypto_payment_transactions",
    "column_name": "rejected_at",
    "ordinal_position": 18,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "crypto_payment_transactions",
    "column_name": "verification_status",
    "ordinal_position": 19,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'pending'::character varying",
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "crypto_payment_transactions",
    "column_name": "rejection_reason",
    "ordinal_position": 20,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "crypto_payment_transactions",
    "column_name": "transaction_notes",
    "ordinal_position": 21,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Bank reference number for bank transfers, additional notes for other payment types"
  },
  {
    "table_schema": "public",
    "table_name": "crypto_payment_transactions",
    "column_name": "shares_to_purchase",
    "ordinal_position": 22,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "document_access_logs",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "document_access_logs",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "document_access_logs",
    "column_name": "document_type",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Type of document accessed (cipc, sars, fnb, placer_report)"
  },
  {
    "table_schema": "public",
    "table_name": "document_access_logs",
    "column_name": "document_url",
    "ordinal_position": 4,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "document_access_logs",
    "column_name": "accessed_at",
    "ordinal_position": 5,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "document_access_logs",
    "column_name": "telegram_user_id",
    "ordinal_position": 6,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "document_access_logs",
    "column_name": "username",
    "ordinal_position": 7,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "document_access_logs",
    "column_name": "has_nda_acceptance",
    "ordinal_position": 8,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Whether user had NDA acceptance at time of access"
  },
  {
    "table_schema": "public",
    "table_name": "document_access_logs",
    "column_name": "created_at",
    "ordinal_position": 9,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_delivery_log",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_delivery_log",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_delivery_log",
    "column_name": "email_address",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_delivery_log",
    "column_name": "email_type",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_delivery_log",
    "column_name": "resend_message_id",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_delivery_log",
    "column_name": "subject",
    "ordinal_position": 6,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_delivery_log",
    "column_name": "delivery_status",
    "ordinal_position": 7,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'sent'::character varying",
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_delivery_log",
    "column_name": "bounce_reason",
    "ordinal_position": 8,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_delivery_log",
    "column_name": "opened_at",
    "ordinal_position": 9,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_delivery_log",
    "column_name": "clicked_at",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_delivery_log",
    "column_name": "created_at",
    "ordinal_position": 11,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_delivery_log",
    "column_name": "updated_at",
    "ordinal_position": 12,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_preferences",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_preferences",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_preferences",
    "column_name": "welcome_emails",
    "ordinal_position": 3,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_preferences",
    "column_name": "commission_notifications",
    "ordinal_position": 4,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_preferences",
    "column_name": "share_purchase_confirmations",
    "ordinal_position": 5,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_preferences",
    "column_name": "password_reset_emails",
    "ordinal_position": 6,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_preferences",
    "column_name": "admin_newsletters",
    "ordinal_position": 7,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_preferences",
    "column_name": "upline_communications",
    "ordinal_position": 8,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_preferences",
    "column_name": "withdrawal_confirmations",
    "ordinal_position": 9,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_preferences",
    "column_name": "conversion_confirmations",
    "ordinal_position": 10,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_preferences",
    "column_name": "frequency",
    "ordinal_position": 11,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'immediate'::character varying",
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_preferences",
    "column_name": "created_at",
    "ordinal_position": 12,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_preferences",
    "column_name": "updated_at",
    "ordinal_position": 13,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_preferences",
    "column_name": "investment_confirmations",
    "ordinal_position": 14,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_preferences",
    "column_name": "dividend_notifications",
    "ordinal_position": 15,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_preferences",
    "column_name": "security_alerts",
    "ordinal_position": 16,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_preferences",
    "column_name": "system_announcements",
    "ordinal_position": 17,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_preferences",
    "column_name": "marketing_emails",
    "ordinal_position": 18,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_preferences",
    "column_name": "educational_content",
    "ordinal_position": 19,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue",
    "column_name": "email_address",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue",
    "column_name": "email_type",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue",
    "column_name": "subject",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue",
    "column_name": "html_content",
    "ordinal_position": 6,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue",
    "column_name": "text_content",
    "ordinal_position": 7,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue",
    "column_name": "priority",
    "ordinal_position": 8,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": "5",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue",
    "column_name": "scheduled_at",
    "ordinal_position": 9,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue",
    "column_name": "attempts",
    "ordinal_position": 10,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue",
    "column_name": "max_attempts",
    "ordinal_position": 11,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": "3",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue",
    "column_name": "status",
    "ordinal_position": 12,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'pending'::character varying",
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue",
    "column_name": "error_message",
    "ordinal_position": 13,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue",
    "column_name": "sent_at",
    "ordinal_position": 14,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue",
    "column_name": "campaign_id",
    "ordinal_position": 15,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue",
    "column_name": "metadata",
    "ordinal_position": 16,
    "data_type": "jsonb",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue",
    "column_name": "created_at",
    "ordinal_position": 17,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue",
    "column_name": "updated_at",
    "ordinal_position": 18,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue_processing_log",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue_processing_log",
    "column_name": "queue_id",
    "ordinal_position": 2,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue_processing_log",
    "column_name": "processing_started_at",
    "ordinal_position": 3,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue_processing_log",
    "column_name": "processing_completed_at",
    "ordinal_position": 4,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue_processing_log",
    "column_name": "status",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue_processing_log",
    "column_name": "error_message",
    "ordinal_position": 6,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue_processing_log",
    "column_name": "resend_message_id",
    "ordinal_position": 7,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_queue_processing_log",
    "column_name": "created_at",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_templates",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_templates",
    "column_name": "template_name",
    "ordinal_position": 2,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_templates",
    "column_name": "template_type",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_templates",
    "column_name": "subject_line",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_templates",
    "column_name": "html_content",
    "ordinal_position": 5,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_templates",
    "column_name": "text_content",
    "ordinal_position": 6,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_templates",
    "column_name": "description",
    "ordinal_position": 7,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_templates",
    "column_name": "is_active",
    "ordinal_position": 8,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_templates",
    "column_name": "created_by",
    "ordinal_position": 9,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_templates",
    "column_name": "created_at",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_templates",
    "column_name": "updated_at",
    "ordinal_position": 11,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_verification_pins",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_verification_pins",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_verification_pins",
    "column_name": "email",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_verification_pins",
    "column_name": "pin_code",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 6,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_verification_pins",
    "column_name": "pin_type",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_verification_pins",
    "column_name": "expires_at",
    "ordinal_position": 6,
    "data_type": "timestamp with time zone",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_verification_pins",
    "column_name": "used_at",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_verification_pins",
    "column_name": "attempts",
    "ordinal_position": 8,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "email_verification_pins",
    "column_name": "created_at",
    "ordinal_position": 9,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "financial_audit_log",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "financial_audit_log",
    "column_name": "activity_type",
    "ordinal_position": 2,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "financial_audit_log",
    "column_name": "section_name",
    "ordinal_position": 3,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "financial_audit_log",
    "column_name": "amount",
    "ordinal_position": 4,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "financial_audit_log",
    "column_name": "admin_id",
    "ordinal_position": 5,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "financial_audit_log",
    "column_name": "metadata",
    "ordinal_position": 6,
    "data_type": "jsonb",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "financial_audit_log",
    "column_name": "created_at",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_categories",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_categories",
    "column_name": "name",
    "ordinal_position": 2,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_categories",
    "column_name": "description",
    "ordinal_position": 3,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_categories",
    "column_name": "slug",
    "ordinal_position": 4,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_categories",
    "column_name": "display_order",
    "ordinal_position": 5,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_categories",
    "column_name": "is_active",
    "ordinal_position": 6,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_categories",
    "column_name": "created_at",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_categories",
    "column_name": "updated_at",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_categories",
    "column_name": "updated_by",
    "ordinal_position": 9,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": "'system'::text",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_images",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_images",
    "column_name": "title",
    "ordinal_position": 2,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_images",
    "column_name": "description",
    "ordinal_position": 3,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_images",
    "column_name": "image_url",
    "ordinal_position": 4,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_images",
    "column_name": "thumbnail_url",
    "ordinal_position": 5,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_images",
    "column_name": "category_id",
    "ordinal_position": 6,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_images",
    "column_name": "alt_text",
    "ordinal_position": 7,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_images",
    "column_name": "file_size",
    "ordinal_position": 8,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_images",
    "column_name": "width",
    "ordinal_position": 9,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_images",
    "column_name": "height",
    "ordinal_position": 10,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_images",
    "column_name": "display_order",
    "ordinal_position": 11,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_images",
    "column_name": "is_featured",
    "ordinal_position": 12,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_images",
    "column_name": "is_active",
    "ordinal_position": 13,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_images",
    "column_name": "created_at",
    "ordinal_position": 14,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_images",
    "column_name": "updated_at",
    "ordinal_position": 15,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "gallery_images",
    "column_name": "updated_by",
    "ordinal_position": 16,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "investment_phases",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": "nextval('investment_phases_id_seq'::regclass)",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "investment_phases",
    "column_name": "phase_number",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "investment_phases",
    "column_name": "phase_name",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "investment_phases",
    "column_name": "price_per_share",
    "ordinal_position": 4,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 10,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "investment_phases",
    "column_name": "total_shares_available",
    "ordinal_position": 5,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "investment_phases",
    "column_name": "shares_sold",
    "ordinal_position": 6,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "investment_phases",
    "column_name": "is_active",
    "ordinal_position": 7,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "investment_phases",
    "column_name": "start_date",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "investment_phases",
    "column_name": "end_date",
    "ordinal_position": 9,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "investment_phases",
    "column_name": "created_at",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "investment_phases",
    "column_name": "updated_at",
    "ordinal_position": 11,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_audit_log",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_audit_log",
    "column_name": "kyc_id",
    "ordinal_position": 2,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_audit_log",
    "column_name": "user_id",
    "ordinal_position": 3,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_audit_log",
    "column_name": "action",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_audit_log",
    "column_name": "field_changed",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_audit_log",
    "column_name": "old_value_hash",
    "ordinal_position": 6,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 64,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_audit_log",
    "column_name": "new_value_hash",
    "ordinal_position": 7,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 64,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_audit_log",
    "column_name": "performed_by_telegram_id",
    "ordinal_position": 8,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_audit_log",
    "column_name": "performed_by_username",
    "ordinal_position": 9,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_audit_log",
    "column_name": "ip_address",
    "ordinal_position": 10,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 45,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_audit_log",
    "column_name": "user_agent",
    "ordinal_position": 11,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_audit_log",
    "column_name": "performed_at",
    "ordinal_position": 12,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "first_name",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "last_name",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "full_legal_name",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 200,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Auto-generated full name for certificates"
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "id_type",
    "ordinal_position": 6,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "id_number_encrypted",
    "ordinal_position": 7,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Encrypted government ID or passport number"
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "id_number_hash",
    "ordinal_position": 8,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 64,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "SHA-256 hash of ID number for duplicate detection"
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "phone_number",
    "ordinal_position": 9,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "email_address",
    "ordinal_position": 10,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "street_address",
    "ordinal_position": 11,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "city",
    "ordinal_position": 12,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "postal_code",
    "ordinal_position": 13,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "country_code",
    "ordinal_position": 14,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 3,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "ISO 3166-1 alpha-3 country code"
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "country_name",
    "ordinal_position": 15,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "data_consent_given",
    "ordinal_position": 16,
    "data_type": "boolean",
    "is_nullable": "NO",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "privacy_policy_accepted",
    "ordinal_position": 17,
    "data_type": "boolean",
    "is_nullable": "NO",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "kyc_completed_at",
    "ordinal_position": 18,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "kyc_status",
    "ordinal_position": 19,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": "'completed'::character varying",
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "created_at",
    "ordinal_position": 20,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "updated_at",
    "ordinal_position": 21,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "created_by_telegram_id",
    "ordinal_position": 22,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "last_modified_by",
    "ordinal_position": 23,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "certificate_requested",
    "ordinal_position": 24,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "certificate_generated_at",
    "ordinal_position": 25,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "kyc_information",
    "column_name": "certificate_sent_at",
    "ordinal_position": 26,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "marketing_content_generated",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "marketing_content_generated",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "marketing_content_generated",
    "column_name": "platform",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "marketing_content_generated",
    "column_name": "content_type",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "marketing_content_generated",
    "column_name": "template_id",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "marketing_content_generated",
    "column_name": "generated_content",
    "ordinal_position": 6,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "marketing_content_generated",
    "column_name": "hashtags",
    "ordinal_position": 7,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "marketing_content_generated",
    "column_name": "referral_link",
    "ordinal_position": 8,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 500,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "marketing_content_generated",
    "column_name": "character_count",
    "ordinal_position": 9,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "marketing_content_generated",
    "column_name": "image_url",
    "ordinal_position": 10,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 500,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "marketing_content_generated",
    "column_name": "created_at",
    "ordinal_position": 11,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "marketing_materials",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Unique identifier for the marketing material"
  },
  {
    "table_schema": "public",
    "table_name": "marketing_materials",
    "column_name": "title",
    "ordinal_position": 2,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Title of the marketing material"
  },
  {
    "table_schema": "public",
    "table_name": "marketing_materials",
    "column_name": "description",
    "ordinal_position": 3,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Description of the marketing material"
  },
  {
    "table_schema": "public",
    "table_name": "marketing_materials",
    "column_name": "file_url",
    "ordinal_position": 4,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "URL to the uploaded file in Supabase Storage"
  },
  {
    "table_schema": "public",
    "table_name": "marketing_materials",
    "column_name": "file_type",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "MIME type of the uploaded file"
  },
  {
    "table_schema": "public",
    "table_name": "marketing_materials",
    "column_name": "file_size",
    "ordinal_position": 6,
    "data_type": "bigint",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": "Size of the file in bytes"
  },
  {
    "table_schema": "public",
    "table_name": "marketing_materials",
    "column_name": "language",
    "ordinal_position": 7,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": "'en'::character varying",
    "character_maximum_length": 10,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Language code (en, af, zu, xh, fr, pt, es)"
  },
  {
    "table_schema": "public",
    "table_name": "marketing_materials",
    "column_name": "category",
    "ordinal_position": 8,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Category of the material (Presentations, Brochures, etc.)"
  },
  {
    "table_schema": "public",
    "table_name": "marketing_materials",
    "column_name": "is_active",
    "ordinal_position": 9,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Whether the material is active and visible to users"
  },
  {
    "table_schema": "public",
    "table_name": "marketing_materials",
    "column_name": "created_at",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Timestamp when the material was created"
  },
  {
    "table_schema": "public",
    "table_name": "marketing_materials",
    "column_name": "updated_at",
    "ordinal_position": 11,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Timestamp when the material was last updated"
  },
  {
    "table_schema": "public",
    "table_name": "marketing_training_progress",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "marketing_training_progress",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "marketing_training_progress",
    "column_name": "module_id",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "marketing_training_progress",
    "column_name": "progress_percentage",
    "ordinal_position": 4,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "marketing_training_progress",
    "column_name": "completed",
    "ordinal_position": 5,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "marketing_training_progress",
    "column_name": "started_at",
    "ordinal_position": 6,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "marketing_training_progress",
    "column_name": "completed_at",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "marketing_training_progress",
    "column_name": "updated_at",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "nda_acceptances",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "nda_acceptances",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": "Reference to users table"
  },
  {
    "table_schema": "public",
    "table_name": "nda_acceptances",
    "column_name": "accepted_at",
    "ordinal_position": 3,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Timestamp when NDA was accepted"
  },
  {
    "table_schema": "public",
    "table_name": "nda_acceptances",
    "column_name": "ip_address",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 45,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "IP address when NDA was accepted (if available)"
  },
  {
    "table_schema": "public",
    "table_name": "nda_acceptances",
    "column_name": "user_agent",
    "ordinal_position": 5,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "User agent when NDA was accepted (if available)"
  },
  {
    "table_schema": "public",
    "table_name": "nda_acceptances",
    "column_name": "telegram_user_id",
    "ordinal_position": 6,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "nda_acceptances",
    "column_name": "username",
    "ordinal_position": 7,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "nda_acceptances",
    "column_name": "full_name",
    "ordinal_position": 8,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "nda_acceptances",
    "column_name": "created_at",
    "ordinal_position": 9,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "notification_log",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": "nextval('notification_log_id_seq'::regclass)",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "notification_log",
    "column_name": "telegram_id",
    "ordinal_position": 2,
    "data_type": "bigint",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "notification_log",
    "column_name": "user_id",
    "ordinal_position": 3,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "notification_log",
    "column_name": "notification_type",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "notification_log",
    "column_name": "audio_type",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "notification_log",
    "column_name": "message_preview",
    "ordinal_position": 6,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "notification_log",
    "column_name": "audio_enabled",
    "ordinal_position": 7,
    "data_type": "boolean",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "notification_log",
    "column_name": "delivery_status",
    "ordinal_position": 8,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'sent'::character varying",
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "notification_log",
    "column_name": "error_message",
    "ordinal_position": 9,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "notification_log",
    "column_name": "sent_at",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "notification_log",
    "column_name": "delivered_at",
    "ordinal_position": 11,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "notification_log",
    "column_name": "user_read",
    "ordinal_position": 12,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "notification_log",
    "column_name": "user_interacted",
    "ordinal_position": 13,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "notification_log",
    "column_name": "is_admin_notification",
    "ordinal_position": 14,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "notification_log",
    "column_name": "priority_level",
    "ordinal_position": 15,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'medium'::character varying",
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "notification_sound_types",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": "nextval('notification_sound_types_id_seq'::regclass)",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "notification_sound_types",
    "column_name": "sound_name",
    "ordinal_position": 2,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "notification_sound_types",
    "column_name": "sound_emoji",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 10,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "notification_sound_types",
    "column_name": "sound_description",
    "ordinal_position": 4,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "notification_sound_types",
    "column_name": "category",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 30,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "notification_sound_types",
    "column_name": "is_active",
    "ordinal_position": 6,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "notification_sound_types",
    "column_name": "created_at",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "payment_admin_notes",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "payment_admin_notes",
    "column_name": "payment_id",
    "ordinal_position": 2,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "payment_admin_notes",
    "column_name": "admin_telegram_id",
    "ordinal_position": 3,
    "data_type": "bigint",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "payment_admin_notes",
    "column_name": "admin_username",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "payment_admin_notes",
    "column_name": "note_type",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'general'::character varying",
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "payment_admin_notes",
    "column_name": "note_text",
    "ordinal_position": 6,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "payment_admin_notes",
    "column_name": "is_internal",
    "ordinal_position": 7,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "payment_admin_notes",
    "column_name": "created_at",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "payment_allocations",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "payment_allocations",
    "column_name": "transaction_id",
    "ordinal_position": 2,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "payment_allocations",
    "column_name": "user_id",
    "ordinal_position": 3,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "payment_allocations",
    "column_name": "total_amount_usd",
    "ordinal_position": 4,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "payment_allocations",
    "column_name": "aureus_amount",
    "ordinal_position": 5,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "payment_allocations",
    "column_name": "sun_amount",
    "ordinal_position": 6,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "payment_allocations",
    "column_name": "uhh_amount",
    "ordinal_position": 7,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "payment_allocations",
    "column_name": "affiliates_amount",
    "ordinal_position": 8,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "payment_allocations",
    "column_name": "gold_diggers_amount",
    "ordinal_position": 9,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "payment_allocations",
    "column_name": "allocation_date",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "payment_allocations",
    "column_name": "created_by_admin_id",
    "ordinal_position": 11,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "payment_allocations",
    "column_name": "created_at",
    "ordinal_position": 12,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "phase_statistics",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "phase_statistics",
    "column_name": "phase_name",
    "ordinal_position": 2,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "phase_statistics",
    "column_name": "phase_number",
    "ordinal_position": 3,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "phase_statistics",
    "column_name": "price_per_share",
    "ordinal_position": 4,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 10,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "phase_statistics",
    "column_name": "total_shares_available",
    "ordinal_position": 5,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "phase_statistics",
    "column_name": "shares_sold",
    "ordinal_position": 6,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "phase_statistics",
    "column_name": "total_purchases",
    "ordinal_position": 7,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "phase_statistics",
    "column_name": "total_revenue",
    "ordinal_position": 8,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "phase_statistics",
    "column_name": "completion_percentage",
    "ordinal_position": 9,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "phase_statistics",
    "column_name": "is_active",
    "ordinal_position": 10,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "phase_statistics",
    "column_name": "start_date",
    "ordinal_position": 11,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "phase_statistics",
    "column_name": "end_date",
    "ordinal_position": 12,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "referrals",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "referrals",
    "column_name": "referrer_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "referrals",
    "column_name": "referred_id",
    "ordinal_position": 3,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "referrals",
    "column_name": "referral_code",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "referrals",
    "column_name": "commission_rate",
    "ordinal_position": 5,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": "5.00",
    "character_maximum_length": null,
    "numeric_precision": 5,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "referrals",
    "column_name": "total_commission",
    "ordinal_position": 6,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "referrals",
    "column_name": "status",
    "ordinal_position": 7,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'active'::character varying",
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "referrals",
    "column_name": "created_at",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "referrals",
    "column_name": "updated_at",
    "ordinal_position": 9,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "referrals",
    "column_name": "campaign_source",
    "ordinal_position": 10,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "section_balances",
    "column_name": "section_name",
    "ordinal_position": 1,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "section_balances",
    "column_name": "current_balance",
    "ordinal_position": 2,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "section_balances",
    "column_name": "total_allocated",
    "ordinal_position": 3,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "section_balances",
    "column_name": "total_withdrawn",
    "ordinal_position": 4,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "section_balances",
    "column_name": "last_updated",
    "ordinal_position": 5,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "share_transfers",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "share_transfers",
    "column_name": "sender_user_id",
    "ordinal_position": 2,
    "data_type": "bigint",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "share_transfers",
    "column_name": "recipient_user_id",
    "ordinal_position": 3,
    "data_type": "bigint",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "share_transfers",
    "column_name": "shares_transferred",
    "ordinal_position": 4,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 10,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "share_transfers",
    "column_name": "transfer_reason",
    "ordinal_position": 5,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "share_transfers",
    "column_name": "status",
    "ordinal_position": 6,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'pending'::character varying",
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "share_transfers",
    "column_name": "recipient_confirmed",
    "ordinal_position": 7,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "share_transfers",
    "column_name": "recipient_confirmed_at",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "share_transfers",
    "column_name": "confirmation_reminder_sent_at",
    "ordinal_position": 9,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "share_transfers",
    "column_name": "initiated_by_telegram_id",
    "ordinal_position": 10,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "share_transfers",
    "column_name": "approved_by_admin_id",
    "ordinal_position": 11,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "share_transfers",
    "column_name": "reversed_by_admin_id",
    "ordinal_position": 12,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "share_transfers",
    "column_name": "reversal_reason",
    "ordinal_position": 13,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "share_transfers",
    "column_name": "created_at",
    "ordinal_position": 14,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "share_transfers",
    "column_name": "completed_at",
    "ordinal_position": 15,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "share_transfers",
    "column_name": "reversed_at",
    "ordinal_position": 16,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "share_transfers",
    "column_name": "transfer_metadata",
    "ordinal_position": 17,
    "data_type": "jsonb",
    "is_nullable": "YES",
    "column_default": "'{}'::jsonb",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "site_content",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "site_content",
    "column_name": "section",
    "ordinal_position": 2,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "site_content",
    "column_name": "key",
    "ordinal_position": 3,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "site_content",
    "column_name": "value",
    "ordinal_position": 4,
    "data_type": "jsonb",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "site_content",
    "column_name": "updated_at",
    "ordinal_position": 5,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "site_content",
    "column_name": "updated_by",
    "ordinal_position": 6,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "supported_countries",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": "nextval('supported_countries_id_seq'::regclass)",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "supported_countries",
    "column_name": "country_code",
    "ordinal_position": 2,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 3,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "supported_countries",
    "column_name": "country_name",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "supported_countries",
    "column_name": "flag_emoji",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 10,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "supported_countries",
    "column_name": "is_primary",
    "ordinal_position": 5,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "supported_countries",
    "column_name": "is_active",
    "ordinal_position": 6,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "supported_countries",
    "column_name": "display_order",
    "ordinal_position": 7,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": "999",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "supported_countries",
    "column_name": "created_at",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "supported_countries",
    "column_name": "updated_at",
    "ordinal_position": 9,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "sync_notifications",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "sync_notifications",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "sync_notifications",
    "column_name": "notification_type",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 30,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "sync_notifications",
    "column_name": "title",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "sync_notifications",
    "column_name": "message",
    "ordinal_position": 5,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "sync_notifications",
    "column_name": "action_url",
    "ordinal_position": 6,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 500,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "sync_notifications",
    "column_name": "action_text",
    "ordinal_position": 7,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "sync_notifications",
    "column_name": "is_read",
    "ordinal_position": 8,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "sync_notifications",
    "column_name": "is_dismissed",
    "ordinal_position": 9,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "sync_notifications",
    "column_name": "priority",
    "ordinal_position": 10,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'normal'::character varying",
    "character_maximum_length": 10,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "sync_notifications",
    "column_name": "metadata",
    "ordinal_position": 11,
    "data_type": "jsonb",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "sync_notifications",
    "column_name": "expires_at",
    "ordinal_position": 12,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "sync_notifications",
    "column_name": "created_at",
    "ordinal_position": 13,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "sync_notifications",
    "column_name": "read_at",
    "ordinal_position": 14,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "sync_notifications",
    "column_name": "dismissed_at",
    "ordinal_position": 15,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "system_settings",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": "nextval('system_settings_id_seq'::regclass)",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "system_settings",
    "column_name": "setting_name",
    "ordinal_position": 2,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "system_settings",
    "column_name": "setting_value",
    "ordinal_position": 3,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "system_settings",
    "column_name": "description",
    "ordinal_position": 4,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "system_settings",
    "column_name": "updated_by_admin_id",
    "ordinal_position": 5,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "system_settings",
    "column_name": "updated_at",
    "ordinal_position": 6,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "system_settings",
    "column_name": "created_at",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_sync_requests",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_sync_requests",
    "column_name": "telegram_id",
    "ordinal_position": 2,
    "data_type": "bigint",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_sync_requests",
    "column_name": "telegram_username",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_sync_requests",
    "column_name": "sync_token",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_sync_requests",
    "column_name": "status",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": "'pending'::character varying",
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_sync_requests",
    "column_name": "linked_web_user_id",
    "ordinal_position": 6,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_sync_requests",
    "column_name": "completed_at",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_sync_requests",
    "column_name": "expires_at",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_sync_requests",
    "column_name": "created_at",
    "ordinal_position": 9,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_sync_requests",
    "column_name": "user_agent",
    "ordinal_position": 10,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_sync_requests",
    "column_name": "ip_address",
    "ordinal_position": 11,
    "data_type": "inet",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_users",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_users",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_users",
    "column_name": "telegram_id",
    "ordinal_position": 3,
    "data_type": "bigint",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_users",
    "column_name": "username",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_users",
    "column_name": "first_name",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_users",
    "column_name": "last_name",
    "ordinal_position": 6,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_users",
    "column_name": "is_registered",
    "ordinal_position": 7,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_users",
    "column_name": "registration_step",
    "ordinal_position": 8,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'start'::character varying",
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_users",
    "column_name": "registration_mode",
    "ordinal_position": 9,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'login'::character varying",
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_users",
    "column_name": "temp_email",
    "ordinal_position": 10,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_users",
    "column_name": "temp_password",
    "ordinal_position": 11,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_users",
    "column_name": "created_at",
    "ordinal_position": 12,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_users",
    "column_name": "updated_at",
    "ordinal_position": 13,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "telegram_users",
    "column_name": "last_activity",
    "ordinal_position": 17,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "terms_acceptance",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "terms_acceptance",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "terms_acceptance",
    "column_name": "telegram_id",
    "ordinal_position": 3,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "terms_acceptance",
    "column_name": "terms_type",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "terms_acceptance",
    "column_name": "version",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'1.0'::character varying",
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "terms_acceptance",
    "column_name": "accepted_at",
    "ordinal_position": 6,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "terms_acceptance",
    "column_name": "created_at",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "terms_acceptance",
    "column_name": "updated_at",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "test_connection",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": "nextval('test_connection_id_seq'::regclass)",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "test_connection",
    "column_name": "name",
    "ordinal_position": 2,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "test_connection",
    "column_name": "description",
    "ordinal_position": 3,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "test_connection",
    "column_name": "created_at",
    "ordinal_position": 4,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "test_connection",
    "column_name": "updated_at",
    "ordinal_position": 5,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "test_user_exclusion_audit",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "test_user_exclusion_audit",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": "ID of the user being added/removed from exclusion"
  },
  {
    "table_schema": "public",
    "table_name": "test_user_exclusion_audit",
    "column_name": "action",
    "ordinal_position": 3,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Action performed: ADDED or REMOVED"
  },
  {
    "table_schema": "public",
    "table_name": "test_user_exclusion_audit",
    "column_name": "performed_by",
    "ordinal_position": 4,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Admin user who performed the action"
  },
  {
    "table_schema": "public",
    "table_name": "test_user_exclusion_audit",
    "column_name": "performed_at",
    "ordinal_position": 5,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Timestamp when action was performed"
  },
  {
    "table_schema": "public",
    "table_name": "test_user_exclusion_audit",
    "column_name": "reason",
    "ordinal_position": 6,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Reason provided for the action"
  },
  {
    "table_schema": "public",
    "table_name": "test_user_exclusion_audit",
    "column_name": "impact_summary",
    "ordinal_position": 7,
    "data_type": "jsonb",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "JSON object containing impact details (shares, payments, allocations affected)"
  },
  {
    "table_schema": "public",
    "table_name": "test_user_exclusion_audit",
    "column_name": "created_at",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "test_user_exclusions",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": "nextval('test_user_exclusions_id_seq'::regclass)",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "test_user_exclusions",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "test_user_exclusions",
    "column_name": "reason",
    "ordinal_position": 3,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "test_user_exclusions",
    "column_name": "excluded_by",
    "ordinal_position": 4,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "test_user_exclusions",
    "column_name": "is_active",
    "ordinal_position": 5,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "test_user_exclusions",
    "column_name": "created_at",
    "ordinal_position": 6,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "test_user_exclusions",
    "column_name": "removed_by",
    "ordinal_position": 7,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "test_user_exclusions",
    "column_name": "removed_at",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "test_user_exclusions",
    "column_name": "removal_reason",
    "ordinal_position": 9,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_messages",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_messages",
    "column_name": "sender_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_messages",
    "column_name": "recipient_id",
    "ordinal_position": 3,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_messages",
    "column_name": "subject",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_messages",
    "column_name": "message",
    "ordinal_position": 5,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_messages",
    "column_name": "status",
    "ordinal_position": 6,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'sent'::character varying",
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_messages",
    "column_name": "created_at",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_messages",
    "column_name": "updated_at",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_preferences",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": "nextval('user_notification_preferences_id_seq'::regclass)",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_preferences",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_preferences",
    "column_name": "telegram_id",
    "ordinal_position": 3,
    "data_type": "bigint",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_preferences",
    "column_name": "audio_enabled",
    "ordinal_position": 4,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_preferences",
    "column_name": "notification_volume",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'medium'::character varying",
    "character_maximum_length": 10,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_preferences",
    "column_name": "payment_approval_audio",
    "ordinal_position": 6,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_preferences",
    "column_name": "payment_rejection_audio",
    "ordinal_position": 7,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_preferences",
    "column_name": "withdrawal_approval_audio",
    "ordinal_position": 8,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_preferences",
    "column_name": "withdrawal_rejection_audio",
    "ordinal_position": 9,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_preferences",
    "column_name": "commission_update_audio",
    "ordinal_position": 10,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_preferences",
    "column_name": "referral_bonus_audio",
    "ordinal_position": 11,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_preferences",
    "column_name": "system_announcement_audio",
    "ordinal_position": 12,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_preferences",
    "column_name": "quiet_hours_enabled",
    "ordinal_position": 13,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_preferences",
    "column_name": "quiet_hours_start",
    "ordinal_position": 14,
    "data_type": "time without time zone",
    "is_nullable": "YES",
    "column_default": "'22:00:00'::time without time zone",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_preferences",
    "column_name": "quiet_hours_end",
    "ordinal_position": 15,
    "data_type": "time without time zone",
    "is_nullable": "YES",
    "column_default": "'08:00:00'::time without time zone",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_preferences",
    "column_name": "timezone",
    "ordinal_position": 16,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'UTC'::character varying",
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_preferences",
    "column_name": "custom_sound_enabled",
    "ordinal_position": 17,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_preferences",
    "column_name": "notification_frequency",
    "ordinal_position": 18,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'all'::character varying",
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_preferences",
    "column_name": "created_at",
    "ordinal_position": 19,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_preferences",
    "column_name": "updated_at",
    "ordinal_position": 20,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_summary",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": "nextval('user_notification_summary_id_seq'::regclass)",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_summary",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_summary",
    "column_name": "unread_count",
    "ordinal_position": 3,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_summary",
    "column_name": "last_notification_at",
    "ordinal_position": 4,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_summary",
    "column_name": "created_at",
    "ordinal_position": 5,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notification_summary",
    "column_name": "updated_at",
    "ordinal_position": 6,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notifications",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notifications",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notifications",
    "column_name": "title",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notifications",
    "column_name": "message",
    "ordinal_position": 4,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notifications",
    "column_name": "type",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'info'::character varying",
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notifications",
    "column_name": "is_read",
    "ordinal_position": 6,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notifications",
    "column_name": "is_archived",
    "ordinal_position": 7,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notifications",
    "column_name": "action_url",
    "ordinal_position": 8,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 500,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notifications",
    "column_name": "metadata",
    "ordinal_position": 9,
    "data_type": "jsonb",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notifications",
    "column_name": "created_at",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_notifications",
    "column_name": "updated_at",
    "ordinal_position": 11,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_payment_methods",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": "nextval('user_payment_methods_id_seq'::regclass)",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_payment_methods",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_payment_methods",
    "column_name": "method_type",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_payment_methods",
    "column_name": "method_name",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_payment_methods",
    "column_name": "method_details",
    "ordinal_position": 5,
    "data_type": "jsonb",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_payment_methods",
    "column_name": "is_active",
    "ordinal_position": 6,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_payment_methods",
    "column_name": "is_verified",
    "ordinal_position": 7,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_payment_methods",
    "column_name": "created_at",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_payment_methods",
    "column_name": "updated_at",
    "ordinal_position": 9,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_preferences",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_preferences",
    "column_name": "user_id",
    "ordinal_position": 2,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_preferences",
    "column_name": "preference_type",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Type of preference: presentation_language, notification_settings, etc."
  },
  {
    "table_schema": "public",
    "table_name": "user_preferences",
    "column_name": "preference_value",
    "ordinal_position": 4,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Value of the preference: english, hindi, french, bengali, etc."
  },
  {
    "table_schema": "public",
    "table_name": "user_preferences",
    "column_name": "created_at",
    "ordinal_position": 5,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_preferences",
    "column_name": "updated_at",
    "ordinal_position": 6,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_sessions",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_sessions",
    "column_name": "telegram_id",
    "ordinal_position": 2,
    "data_type": "bigint",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_sessions",
    "column_name": "session_state",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_sessions",
    "column_name": "session_data",
    "ordinal_position": 4,
    "data_type": "jsonb",
    "is_nullable": "YES",
    "column_default": "'{}'::jsonb",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_sessions",
    "column_name": "expires_at",
    "ordinal_position": 5,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "(now() + '24:00:00'::interval)",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_sessions",
    "column_name": "created_at",
    "ordinal_position": 6,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_sessions",
    "column_name": "updated_at",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_share_holdings",
    "column_name": "user_id",
    "ordinal_position": 1,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_share_holdings",
    "column_name": "username",
    "ordinal_position": 2,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_share_holdings",
    "column_name": "email",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_share_holdings",
    "column_name": "full_name",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_share_holdings",
    "column_name": "first_name",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_share_holdings",
    "column_name": "last_name",
    "ordinal_position": 6,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_share_holdings",
    "column_name": "total_purchases",
    "ordinal_position": 7,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_share_holdings",
    "column_name": "total_shares",
    "ordinal_position": 8,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_share_holdings",
    "column_name": "total_invested",
    "ordinal_position": 9,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_share_holdings",
    "column_name": "total_commission_used",
    "ordinal_position": 10,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "user_share_holdings",
    "column_name": "last_purchase_date",
    "ordinal_position": 11,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": "nextval('users_id_seq'::regclass)",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "username",
    "ordinal_position": 2,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "email",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "password_hash",
    "ordinal_position": 4,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "full_name",
    "ordinal_position": 5,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "phone",
    "ordinal_position": 6,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "address",
    "ordinal_position": 7,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "is_active",
    "ordinal_position": 8,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "is_verified",
    "ordinal_position": 9,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "verification_token",
    "ordinal_position": 10,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "reset_token",
    "ordinal_position": 11,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 255,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "reset_token_expires",
    "ordinal_position": 12,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "created_at",
    "ordinal_position": 13,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "updated_at",
    "ordinal_position": 14,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "telegram_id",
    "ordinal_position": 15,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "country_of_residence",
    "ordinal_position": 16,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 3,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "ISO 3166-1 alpha-3 country code (e.g., ZAF, USA, GBR)"
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "country_name",
    "ordinal_position": 17,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Full country name for display purposes"
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "country_selected_at",
    "ordinal_position": 18,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Timestamp when user first selected their country"
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "country_updated_at",
    "ordinal_position": 19,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Timestamp when country was last updated"
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "country_selection_completed",
    "ordinal_position": 20,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Whether user has completed country selection"
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "role",
    "ordinal_position": 21,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": "'user'::text",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "sponsor_user_id",
    "ordinal_position": 22,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "is_admin",
    "ordinal_position": 23,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "auth_user_id",
    "ordinal_position": 24,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "phone_number",
    "ordinal_position": 25,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "telegram_username",
    "ordinal_position": 26,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 50,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "first_name",
    "ordinal_position": 27,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "last_name",
    "ordinal_position": 28,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "total_referrals",
    "ordinal_position": 29,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "total_earnings",
    "ordinal_position": 30,
    "data_type": "numeric",
    "is_nullable": "YES",
    "column_default": "0.00",
    "character_maximum_length": null,
    "numeric_precision": 10,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "registration_source",
    "ordinal_position": 31,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'web'::character varying",
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "account_status",
    "ordinal_position": 32,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'active'::character varying",
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "sync_status",
    "ordinal_position": 33,
    "data_type": "character varying",
    "is_nullable": "YES",
    "column_default": "'none'::character varying",
    "character_maximum_length": 20,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "users",
    "column_name": "last_sync_at",
    "ordinal_position": 34,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "withdrawal_invoices",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "withdrawal_invoices",
    "column_name": "invoice_number",
    "ordinal_position": 2,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "withdrawal_invoices",
    "column_name": "section_name",
    "ordinal_position": 3,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "withdrawal_invoices",
    "column_name": "withdrawal_amount",
    "ordinal_position": 4,
    "data_type": "numeric",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 15,
    "numeric_scale": 2,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "withdrawal_invoices",
    "column_name": "purpose",
    "ordinal_position": 5,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "withdrawal_invoices",
    "column_name": "status",
    "ordinal_position": 6,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": "'pending'::text",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "withdrawal_invoices",
    "column_name": "requested_by_admin_id",
    "ordinal_position": 7,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "withdrawal_invoices",
    "column_name": "approved_by_admin_id",
    "ordinal_position": 8,
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "withdrawal_invoices",
    "column_name": "created_at",
    "ordinal_position": 9,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "withdrawal_invoices",
    "column_name": "approved_at",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "withdrawal_invoices",
    "column_name": "completed_at",
    "ordinal_position": 11,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "withdrawal_invoices",
    "column_name": "notes",
    "ordinal_position": 12,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "withdrawal_invoices",
    "column_name": "pdf_generated",
    "ordinal_position": 13,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "public",
    "table_name": "withdrawal_invoices",
    "column_name": "pdf_path",
    "ordinal_position": 14,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "realtime",
    "table_name": "messages",
    "column_name": "topic",
    "ordinal_position": 3,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "realtime",
    "table_name": "messages",
    "column_name": "extension",
    "ordinal_position": 4,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "realtime",
    "table_name": "messages",
    "column_name": "payload",
    "ordinal_position": 5,
    "data_type": "jsonb",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "realtime",
    "table_name": "messages",
    "column_name": "event",
    "ordinal_position": 6,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "realtime",
    "table_name": "messages",
    "column_name": "private",
    "ordinal_position": 7,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "realtime",
    "table_name": "messages",
    "column_name": "updated_at",
    "ordinal_position": 8,
    "data_type": "timestamp without time zone",
    "is_nullable": "NO",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "realtime",
    "table_name": "messages",
    "column_name": "inserted_at",
    "ordinal_position": 9,
    "data_type": "timestamp without time zone",
    "is_nullable": "NO",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "realtime",
    "table_name": "messages",
    "column_name": "id",
    "ordinal_position": 10,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "realtime",
    "table_name": "schema_migrations",
    "column_name": "version",
    "ordinal_position": 1,
    "data_type": "bigint",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "realtime",
    "table_name": "schema_migrations",
    "column_name": "inserted_at",
    "ordinal_position": 2,
    "data_type": "timestamp without time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "realtime",
    "table_name": "subscription",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "bigint",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "realtime",
    "table_name": "subscription",
    "column_name": "subscription_id",
    "ordinal_position": 2,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "realtime",
    "table_name": "subscription",
    "column_name": "entity",
    "ordinal_position": 4,
    "data_type": "regclass",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "realtime",
    "table_name": "subscription",
    "column_name": "filters",
    "ordinal_position": 5,
    "data_type": "ARRAY",
    "is_nullable": "NO",
    "column_default": "'{}'::realtime.user_defined_filter[]",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "realtime",
    "table_name": "subscription",
    "column_name": "claims",
    "ordinal_position": 7,
    "data_type": "jsonb",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "realtime",
    "table_name": "subscription",
    "column_name": "claims_role",
    "ordinal_position": 8,
    "data_type": "regrole",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "realtime",
    "table_name": "subscription",
    "column_name": "created_at",
    "ordinal_position": 9,
    "data_type": "timestamp without time zone",
    "is_nullable": "NO",
    "column_default": "timezone('utc'::text, now())",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "buckets",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "buckets",
    "column_name": "name",
    "ordinal_position": 2,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "buckets",
    "column_name": "owner",
    "ordinal_position": 3,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Field is deprecated, use owner_id instead"
  },
  {
    "table_schema": "storage",
    "table_name": "buckets",
    "column_name": "created_at",
    "ordinal_position": 4,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "buckets",
    "column_name": "updated_at",
    "ordinal_position": 5,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "buckets",
    "column_name": "public",
    "ordinal_position": 6,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "buckets",
    "column_name": "avif_autodetection",
    "ordinal_position": 7,
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "buckets",
    "column_name": "file_size_limit",
    "ordinal_position": 8,
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "buckets",
    "column_name": "allowed_mime_types",
    "ordinal_position": 9,
    "data_type": "ARRAY",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "buckets",
    "column_name": "owner_id",
    "ordinal_position": 10,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "migrations",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "migrations",
    "column_name": "name",
    "ordinal_position": 2,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 100,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "migrations",
    "column_name": "hash",
    "ordinal_position": 3,
    "data_type": "character varying",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": 40,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "migrations",
    "column_name": "executed_at",
    "ordinal_position": 4,
    "data_type": "timestamp without time zone",
    "is_nullable": "YES",
    "column_default": "CURRENT_TIMESTAMP",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "objects",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "objects",
    "column_name": "bucket_id",
    "ordinal_position": 2,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "objects",
    "column_name": "name",
    "ordinal_position": 3,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "objects",
    "column_name": "owner",
    "ordinal_position": 4,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": "Field is deprecated, use owner_id instead"
  },
  {
    "table_schema": "storage",
    "table_name": "objects",
    "column_name": "created_at",
    "ordinal_position": 5,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "objects",
    "column_name": "updated_at",
    "ordinal_position": 6,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "objects",
    "column_name": "last_accessed_at",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "objects",
    "column_name": "metadata",
    "ordinal_position": 8,
    "data_type": "jsonb",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "objects",
    "column_name": "path_tokens",
    "ordinal_position": 9,
    "data_type": "ARRAY",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "objects",
    "column_name": "version",
    "ordinal_position": 10,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "objects",
    "column_name": "owner_id",
    "ordinal_position": 11,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "objects",
    "column_name": "user_metadata",
    "ordinal_position": 12,
    "data_type": "jsonb",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "s3_multipart_uploads",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "s3_multipart_uploads",
    "column_name": "in_progress_size",
    "ordinal_position": 2,
    "data_type": "bigint",
    "is_nullable": "NO",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "s3_multipart_uploads",
    "column_name": "upload_signature",
    "ordinal_position": 3,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "s3_multipart_uploads",
    "column_name": "bucket_id",
    "ordinal_position": 4,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "s3_multipart_uploads",
    "column_name": "key",
    "ordinal_position": 5,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "s3_multipart_uploads",
    "column_name": "version",
    "ordinal_position": 6,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "s3_multipart_uploads",
    "column_name": "owner_id",
    "ordinal_position": 7,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "s3_multipart_uploads",
    "column_name": "created_at",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "NO",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "s3_multipart_uploads",
    "column_name": "user_metadata",
    "ordinal_position": 9,
    "data_type": "jsonb",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "s3_multipart_uploads_parts",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "s3_multipart_uploads_parts",
    "column_name": "upload_id",
    "ordinal_position": 2,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "s3_multipart_uploads_parts",
    "column_name": "size",
    "ordinal_position": 3,
    "data_type": "bigint",
    "is_nullable": "NO",
    "column_default": "0",
    "character_maximum_length": null,
    "numeric_precision": 64,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "s3_multipart_uploads_parts",
    "column_name": "part_number",
    "ordinal_position": 4,
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": 32,
    "numeric_scale": 0,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "s3_multipart_uploads_parts",
    "column_name": "bucket_id",
    "ordinal_position": 5,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "s3_multipart_uploads_parts",
    "column_name": "key",
    "ordinal_position": 6,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "s3_multipart_uploads_parts",
    "column_name": "etag",
    "ordinal_position": 7,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "s3_multipart_uploads_parts",
    "column_name": "owner_id",
    "ordinal_position": 8,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "s3_multipart_uploads_parts",
    "column_name": "version",
    "ordinal_position": 9,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "storage",
    "table_name": "s3_multipart_uploads_parts",
    "column_name": "created_at",
    "ordinal_position": 10,
    "data_type": "timestamp with time zone",
    "is_nullable": "NO",
    "column_default": "now()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "vault",
    "table_name": "decrypted_secrets",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "vault",
    "table_name": "decrypted_secrets",
    "column_name": "name",
    "ordinal_position": 2,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "vault",
    "table_name": "decrypted_secrets",
    "column_name": "description",
    "ordinal_position": 3,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "vault",
    "table_name": "decrypted_secrets",
    "column_name": "secret",
    "ordinal_position": 4,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "vault",
    "table_name": "decrypted_secrets",
    "column_name": "decrypted_secret",
    "ordinal_position": 5,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "vault",
    "table_name": "decrypted_secrets",
    "column_name": "key_id",
    "ordinal_position": 6,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "vault",
    "table_name": "decrypted_secrets",
    "column_name": "nonce",
    "ordinal_position": 7,
    "data_type": "bytea",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "vault",
    "table_name": "decrypted_secrets",
    "column_name": "created_at",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "vault",
    "table_name": "decrypted_secrets",
    "column_name": "updated_at",
    "ordinal_position": 9,
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "vault",
    "table_name": "secrets",
    "column_name": "id",
    "ordinal_position": 1,
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "vault",
    "table_name": "secrets",
    "column_name": "name",
    "ordinal_position": 2,
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "vault",
    "table_name": "secrets",
    "column_name": "description",
    "ordinal_position": 3,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": "''::text",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "vault",
    "table_name": "secrets",
    "column_name": "secret",
    "ordinal_position": 4,
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "vault",
    "table_name": "secrets",
    "column_name": "key_id",
    "ordinal_position": 5,
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "vault",
    "table_name": "secrets",
    "column_name": "nonce",
    "ordinal_position": 6,
    "data_type": "bytea",
    "is_nullable": "YES",
    "column_default": "vault._crypto_aead_det_noncegen()",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "vault",
    "table_name": "secrets",
    "column_name": "created_at",
    "ordinal_position": 7,
    "data_type": "timestamp with time zone",
    "is_nullable": "NO",
    "column_default": "CURRENT_TIMESTAMP",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  },
  {
    "table_schema": "vault",
    "table_name": "secrets",
    "column_name": "updated_at",
    "ordinal_position": 8,
    "data_type": "timestamp with time zone",
    "is_nullable": "NO",
    "column_default": "CURRENT_TIMESTAMP",
    "character_maximum_length": null,
    "numeric_precision": null,
    "numeric_scale": null,
    "column_description": null
  }
]

RLS Policies

[
  {
    "schema_name": "public",
    "table_name": "account_links",
    "policy_name": "Users can create their own account links",
    "command": "a",
    "using_expression": null,
    "with_check_expression": "(database_user_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "account_links",
    "policy_name": "Users can view their own account links",
    "command": "r",
    "using_expression": "(database_user_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "account_merge_log",
    "policy_name": "Users can view their own merge history",
    "command": "r",
    "using_expression": "((target_user_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid()))) OR (source_user_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid()))))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "admin_audit_logs",
    "policy_name": "Admins can access audit logs",
    "command": "*",
    "using_expression": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "admin_audit_logs",
    "policy_name": "Service role can manage admin_audit_logs",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "admin_notification_preferences",
    "policy_name": "Admins can manage admin notification preferences",
    "command": "*",
    "using_expression": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "admin_notification_preferences",
    "policy_name": "Service role can manage admin_notification_preferences",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "admin_users",
    "policy_name": "Authenticated users can read admin_users",
    "command": "r",
    "using_expression": "(auth.role() = 'authenticated'::text)",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "admin_users",
    "policy_name": "Service role can manage admin_users",
    "command": "*",
    "using_expression": "(auth.role() = 'service_role'::text)",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "aureus_share_purchases",
    "policy_name": "Users can insert own share purchases",
    "command": "a",
    "using_expression": null,
    "with_check_expression": "(user_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "aureus_share_purchases",
    "policy_name": "Users can view own share purchases",
    "command": "r",
    "using_expression": "(user_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "auth_tokens",
    "policy_name": "Allow public access to auth_tokens",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "certificates",
    "policy_name": "Service role can manage certificates",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "certificates",
    "policy_name": "Users can access own certificates",
    "command": "*",
    "using_expression": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "commission_balances",
    "policy_name": "Service role can manage commission_balances",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "commission_balances",
    "policy_name": "Users can access own commission balances",
    "command": "*",
    "using_expression": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "commission_balances",
    "policy_name": "Users can update own commission balance",
    "command": "w",
    "using_expression": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (auth.uid() = users.auth_user_id)))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "commission_balances",
    "policy_name": "Users can view own commission balance",
    "command": "r",
    "using_expression": "(user_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "commission_balances",
    "policy_name": "Users can view their own commission balance",
    "command": "r",
    "using_expression": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.id = commission_balances.user_id) AND (users.auth_user_id = auth.uid()))))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "commission_conversions",
    "policy_name": "Service role can manage commission_conversions",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "commission_conversions",
    "policy_name": "Users can access own commission conversions",
    "command": "*",
    "using_expression": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "commission_escrow",
    "policy_name": "Service role can manage commission_escrow",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "commission_escrow",
    "policy_name": "Users can access own commission escrow",
    "command": "*",
    "using_expression": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "commission_transactions",
    "policy_name": "Users can view own commission transactions",
    "command": "r",
    "using_expression": "(referrer_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "commission_usage",
    "policy_name": "Users can view own commission usage",
    "command": "r",
    "using_expression": "(user_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "commission_withdrawal_requests",
    "policy_name": "Users can create own withdrawal requests",
    "command": "a",
    "using_expression": null,
    "with_check_expression": "(user_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "commission_withdrawal_requests",
    "policy_name": "Users can view own withdrawal requests",
    "command": "r",
    "using_expression": "(user_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "commission_withdrawals",
    "policy_name": "Service role can manage commission_withdrawals",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "commission_withdrawals",
    "policy_name": "Users can access own commission withdrawals",
    "command": "*",
    "using_expression": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "company_wallets",
    "policy_name": "Admins can access company wallets",
    "command": "*",
    "using_expression": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "company_wallets",
    "policy_name": "Service role can manage company_wallets",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "country_change_log",
    "policy_name": "Admins can access country change log",
    "command": "*",
    "using_expression": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "country_change_log",
    "policy_name": "Service role can manage country_change_log",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "crypto_payment_transactions",
    "policy_name": "Service role can manage crypto_payment_transactions",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "crypto_payment_transactions",
    "policy_name": "Users can access own payment transactions",
    "command": "*",
    "using_expression": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "document_access_logs",
    "policy_name": "Users can view their own document access logs",
    "command": "r",
    "using_expression": "((auth.uid())::text = (user_id)::text)",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "financial_audit_log",
    "policy_name": "Admins can access financial audit log",
    "command": "*",
    "using_expression": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "financial_audit_log",
    "policy_name": "Service role can manage financial_audit_log",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "gallery_categories",
    "policy_name": "Admin full access for gallery_categories",
    "command": "*",
    "using_expression": "((auth.jwt() ->> 'email'::text) = ANY (ARRAY['<EMAIL>'::text, '<EMAIL>'::text]))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "gallery_categories",
    "policy_name": "Public read access for gallery_categories",
    "command": "r",
    "using_expression": "(is_active = true)",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "gallery_images",
    "policy_name": "Admin full access for gallery_images",
    "command": "*",
    "using_expression": "((auth.jwt() ->> 'email'::text) = ANY (ARRAY['<EMAIL>'::text, '<EMAIL>'::text]))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "gallery_images",
    "policy_name": "Public read access for gallery_images",
    "command": "r",
    "using_expression": "(is_active = true)",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "investment_phases",
    "policy_name": "Authenticated users can read investment phases",
    "command": "r",
    "using_expression": "(auth.role() = 'authenticated'::text)",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "investment_phases",
    "policy_name": "Service role can manage investment_phases",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "kyc_audit_log",
    "policy_name": "Users can view their own KYC audit logs",
    "command": "r",
    "using_expression": "((auth.uid())::text = (user_id)::text)",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "kyc_information",
    "policy_name": "Users can insert their own KYC data",
    "command": "a",
    "using_expression": null,
    "with_check_expression": "((auth.uid())::text = (user_id)::text)",
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "kyc_information",
    "policy_name": "Users can update their own KYC data",
    "command": "w",
    "using_expression": "((auth.uid())::text = (user_id)::text)",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "kyc_information",
    "policy_name": "Users can view their own KYC data",
    "command": "r",
    "using_expression": "((auth.uid())::text = (user_id)::text)",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "marketing_content_generated",
    "policy_name": "Users can create marketing content",
    "command": "a",
    "using_expression": null,
    "with_check_expression": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (auth.uid() = users.auth_user_id)))",
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "marketing_content_generated",
    "policy_name": "Users can view own marketing content",
    "command": "r",
    "using_expression": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (auth.uid() = users.auth_user_id)))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "marketing_materials",
    "policy_name": "Admins can manage marketing materials",
    "command": "*",
    "using_expression": "(EXISTS ( SELECT 1\n   FROM admin_users\n  WHERE (admin_users.email = (auth.jwt() ->> 'email'::text))))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "marketing_materials",
    "policy_name": "Users can view active marketing materials",
    "command": "r",
    "using_expression": "((is_active = true) AND (auth.role() = 'authenticated'::text))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "marketing_training_progress",
    "policy_name": "Users can update own training progress",
    "command": "*",
    "using_expression": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (auth.uid() = users.auth_user_id)))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "marketing_training_progress",
    "policy_name": "Users can view own training progress",
    "command": "r",
    "using_expression": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (auth.uid() = users.auth_user_id)))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "nda_acceptances",
    "policy_name": "Users can view their own NDA acceptance",
    "command": "r",
    "using_expression": "((auth.uid())::text = (user_id)::text)",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "notification_log",
    "policy_name": "Admins can access notification log",
    "command": "*",
    "using_expression": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "notification_log",
    "policy_name": "Service role can manage notification_log",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "notification_sound_types",
    "policy_name": "Authenticated users can read notification sound types",
    "command": "r",
    "using_expression": "(auth.role() = 'authenticated'::text)",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "notification_sound_types",
    "policy_name": "Service role can manage notification_sound_types",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "payment_admin_notes",
    "policy_name": "Admins can access payment admin notes",
    "command": "*",
    "using_expression": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "payment_admin_notes",
    "policy_name": "Service role can manage payment_admin_notes",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "payment_allocations",
    "policy_name": "Admins can manage payment allocations",
    "command": "*",
    "using_expression": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "payment_allocations",
    "policy_name": "Service role can manage payment_allocations",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "referrals",
    "policy_name": "Users can view own referrals",
    "command": "r",
    "using_expression": "(referrer_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "section_balances",
    "policy_name": "Admins can manage section balances",
    "command": "*",
    "using_expression": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "section_balances",
    "policy_name": "Service role can manage section_balances",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "site_content",
    "policy_name": "Admin users can manage site content",
    "command": "*",
    "using_expression": "((auth.jwt() ->> 'email'::text) IN ( SELECT admin_users.email\n   FROM admin_users))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "site_content",
    "policy_name": "Anyone can view site content",
    "command": "r",
    "using_expression": "true",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "supported_countries",
    "policy_name": "Admins can delete supported countries",
    "command": "d",
    "using_expression": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "supported_countries",
    "policy_name": "Admins can insert supported countries",
    "command": "a",
    "using_expression": null,
    "with_check_expression": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "supported_countries",
    "policy_name": "Admins can update supported countries",
    "command": "w",
    "using_expression": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check_expression": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "supported_countries",
    "policy_name": "Authenticated users can read supported countries",
    "command": "r",
    "using_expression": "(auth.role() = 'authenticated'::text)",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "supported_countries",
    "policy_name": "Service role can manage supported_countries",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "sync_notifications",
    "policy_name": "Users can view their own sync notifications",
    "command": "*",
    "using_expression": "(user_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "system_settings",
    "policy_name": "Admins can manage system settings",
    "command": "*",
    "using_expression": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "system_settings",
    "policy_name": "Service role can manage system_settings",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "telegram_sync_requests",
    "policy_name": "Users can view their own sync requests",
    "command": "r",
    "using_expression": "((linked_web_user_id = auth.uid()) OR (telegram_id IN ( SELECT users.telegram_id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid()))))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "telegram_users",
    "policy_name": "Service role can manage telegram_users",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "telegram_users",
    "policy_name": "Users can access own telegram data",
    "command": "*",
    "using_expression": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "terms_acceptance",
    "policy_name": "Service role can manage terms_acceptance",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "terms_acceptance",
    "policy_name": "Users can manage own terms acceptance",
    "command": "*",
    "using_expression": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "test_connection",
    "policy_name": "Service role can access test connection",
    "command": "*",
    "using_expression": "(auth.role() = 'service_role'::text)",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "test_user_exclusion_audit",
    "policy_name": "Authenticated users can read audit logs",
    "command": "r",
    "using_expression": "(auth.role() = 'authenticated'::text)",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "test_user_exclusion_audit",
    "policy_name": "Service role can manage audit logs",
    "command": "*",
    "using_expression": "(auth.role() = 'service_role'::text)",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "user_messages",
    "policy_name": "Users can send messages",
    "command": "a",
    "using_expression": null,
    "with_check_expression": "(sender_id = ( SELECT users.id\n   FROM users\n  WHERE (auth.uid() = users.auth_user_id)))",
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "user_messages",
    "policy_name": "Users can view own messages",
    "command": "r",
    "using_expression": "((sender_id = ( SELECT users.id\n   FROM users\n  WHERE (auth.uid() = users.auth_user_id))) OR (recipient_id = ( SELECT users.id\n   FROM users\n  WHERE (auth.uid() = users.auth_user_id))))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "user_notification_preferences",
    "policy_name": "Service role can manage user_notification_preferences",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "user_notification_preferences",
    "policy_name": "Users can manage own notification preferences",
    "command": "*",
    "using_expression": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "user_notification_summary",
    "policy_name": "Users can view their own notification summary",
    "command": "r",
    "using_expression": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.id = user_notification_summary.user_id) AND (users.auth_user_id = auth.uid()))))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "user_notifications",
    "policy_name": "Users can update own notifications",
    "command": "w",
    "using_expression": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (auth.uid() = users.auth_user_id)))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "user_notifications",
    "policy_name": "Users can view own notifications",
    "command": "r",
    "using_expression": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (auth.uid() = users.auth_user_id)))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "user_payment_methods",
    "policy_name": "Users can manage their own payment methods",
    "command": "*",
    "using_expression": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.id = user_payment_methods.user_id) AND (users.auth_user_id = auth.uid()))))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "user_preferences",
    "policy_name": "Service role can manage user_preferences",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "user_preferences",
    "policy_name": "Users can access own preferences",
    "command": "*",
    "using_expression": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "user_sessions",
    "policy_name": "Service role can manage user_sessions",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "user_sessions",
    "policy_name": "Users can manage own sessions",
    "command": "*",
    "using_expression": "(telegram_id = ( SELECT users.telegram_id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "users",
    "policy_name": "Service role can manage users",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "public",
    "table_name": "users",
    "policy_name": "Users can access own data",
    "command": "*",
    "using_expression": "(auth_user_id = auth.uid())",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "withdrawal_invoices",
    "policy_name": "Admins can manage withdrawal invoices",
    "command": "*",
    "using_expression": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "public",
    "table_name": "withdrawal_invoices",
    "policy_name": "Service role can manage withdrawal_invoices",
    "command": "*",
    "using_expression": "true",
    "with_check_expression": "true",
    "roles": [
      16480
    ]
  },
  {
    "schema_name": "storage",
    "table_name": "objects",
    "policy_name": "Admins can manage marketing materials",
    "command": "*",
    "using_expression": "((bucket_id = 'marketing-materials'::text) AND (EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true)))))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "storage",
    "table_name": "objects",
    "policy_name": "Admins can upload marketing materials",
    "command": "a",
    "using_expression": null,
    "with_check_expression": "((bucket_id = 'marketing-materials'::text) AND (EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true)))))",
    "roles": [
      0
    ]
  },
  {
    "schema_name": "storage",
    "table_name": "objects",
    "policy_name": "Allow authenticated users to upload to proof bucket",
    "command": "a",
    "using_expression": null,
    "with_check_expression": "((bucket_id = 'proof'::text) AND (auth.role() = 'authenticated'::text))",
    "roles": [
      0
    ]
  },
  {
    "schema_name": "storage",
    "table_name": "objects",
    "policy_name": "Anyone can view marketing materials",
    "command": "r",
    "using_expression": "(bucket_id = 'marketing-materials'::text)",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "storage",
    "table_name": "objects",
    "policy_name": "Anyone can view proof files",
    "command": "r",
    "using_expression": "(bucket_id = 'proof'::text)",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "storage",
    "table_name": "objects",
    "policy_name": "Authenticated users can delete 1bqp9qb_0",
    "command": "d",
    "using_expression": "(bucket_id = 'assets'::text)",
    "with_check_expression": null,
    "roles": [
      16479
    ]
  },
  {
    "schema_name": "storage",
    "table_name": "objects",
    "policy_name": "Authenticated users can update 1bqp9qb_0",
    "command": "w",
    "using_expression": "(bucket_id = 'assets'::text)",
    "with_check_expression": null,
    "roles": [
      16479
    ]
  },
  {
    "schema_name": "storage",
    "table_name": "objects",
    "policy_name": "Authenticated users can upload 1bqp9qb_0",
    "command": "a",
    "using_expression": null,
    "with_check_expression": "((bucket_id = 'assets'::text) AND ((storage.foldername(name))[1] = 'gallery'::text) AND ((auth.jwt() ->> 'email'::text) = '<EMAIL>'::text))",
    "roles": [
      16479
    ]
  },
  {
    "schema_name": "storage",
    "table_name": "objects",
    "policy_name": "Public read access 1bqp9qb_0",
    "command": "r",
    "using_expression": "(bucket_id = 'assets'::text)",
    "with_check_expression": null,
    "roles": [
      0
    ]
  },
  {
    "schema_name": "storage",
    "table_name": "objects",
    "policy_name": "Users can update their proof files",
    "command": "w",
    "using_expression": "((bucket_id = 'proof'::text) AND (auth.role() = 'authenticated'::text))",
    "with_check_expression": null,
    "roles": [
      0
    ]
  }
]