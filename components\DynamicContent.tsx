import React from 'react';
import { useContent } from '../contexts/SiteContentContext';

interface DynamicContentProps {
  section: string;
  field: string;
  defaultValue?: any;
  as?: 'span' | 'div' | 'h1' | 'h2' | 'h3' | 'p' | 'strong';
  className?: string;
  children?: React.ReactNode;
}

export const DynamicContent: React.FC<DynamicContentProps> = ({
  section,
  field,
  defaultValue = '',
  as: Component = 'span',
  className = '',
  children
}) => {
  const content = useContent(section, field, defaultValue);
  
  // If content is empty and there are children, render children as fallback
  const displayContent = content || children || defaultValue;
  
  return (
    <Component className={className}>
      {displayContent}
    </Component>
  );
};

// Specialized components for common use cases
export const DynamicTitle: React.FC<Omit<DynamicContentProps, 'as'> & { level?: 1 | 2 | 3 }> = ({
  level = 1,
  ...props
}) => {
  const Component = `h${level}` as 'h1' | 'h2' | 'h3';
  return <DynamicContent {...props} as={Component} />;
};

export const DynamicText: React.FC<Omit<DynamicContentProps, 'as'>> = (props) => {
  return <DynamicContent {...props} as="p" />;
};

export const DynamicSpan: React.FC<Omit<DynamicContentProps, 'as'>> = (props) => {
  return <DynamicContent {...props} as="span" />;
};

// Hook for getting numeric values with proper parsing
export const useNumericContent = (section: string, field: string, defaultValue: number = 0): number => {
  const content = useContent(section, field, defaultValue.toString());
  const parsed = parseFloat(content);
  return isNaN(parsed) ? defaultValue : parsed;
};

// Hook for getting boolean values
export const useBooleanContent = (section: string, field: string, defaultValue: boolean = false): boolean => {
  const content = useContent(section, field, defaultValue.toString());
  if (typeof content === 'boolean') return content;
  if (typeof content === 'string') {
    return content.toLowerCase() === 'true' || content === '1';
  }
  return defaultValue;
};
