import { supabase, getServiceRoleClient } from './supabase'
import { hashPassword, validatePasswordStrength } from './passwordSecurity'
import { passwordResetRateLimiter } from './rateLimiting'
import { resendEmailService } from './resendEmailService'
import crypto from 'crypto'

/**
 * SECURE PASSWORD RESET SYSTEM
 *
 * This module provides secure password reset functionality with
 * rate limiting, secure tokens, and comprehensive security logging.
 */

export interface PasswordResetResult {
  success: boolean
  message: string
  error?: string
}

/**
 * Generate a secure password reset token
 */
export const generateResetToken = (): string => {
  return crypto.randomBytes(32).toString('hex')
}

/**
 * Create a password reset request with rate limiting and security
 */
export const createPasswordResetRequest = async (
  email: string,
  ipAddress: string = 'unknown'
): Promise<PasswordResetResult> => {
  try {
    console.log(`🔄 Creating password reset request for: ${email}`)

    // Apply rate limiting
    const rateLimitResult = passwordResetRateLimiter.checkRateLimit(
      `email:${email}`,
      false // Never bypass for password reset
    );

    if (!rateLimitResult.allowed) {
      console.log(`🚫 Password reset rate limited for ${email}`);
      await logPasswordResetEvent('RATE_LIMITED', email, {
        ipAddress,
        retryAfter: rateLimitResult.retryAfter
      });

      return {
        success: false,
        message: 'Too many password reset attempts. Please try again later.',
        error: 'RATE_LIMITED'
      };
    }

    // Check if user exists (using service role for elevated permissions)
    const serviceClient = getServiceRoleClient()
    const { data: user, error: userError } = await serviceClient
      .from('users')
      .select('id, email, username')
      .eq('email', email)
      .single()

    if (userError || !user) {
      // Don't reveal if email exists or not (security best practice)
      console.log(`⚠️ Password reset requested for non-existent email: ${email}`);
      await logPasswordResetEvent('INVALID_EMAIL', email, { ipAddress });

      return {
        success: true, // Always return success to not reveal email existence
        message: 'If an account with this email exists, a reset link has been sent.',
        error: 'User not found'
      }
    }

    // Generate reset token
    const resetToken = generateResetToken()
    const resetExpires = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours

    // Update user with reset token
    const { error: updateError } = await serviceClient
      .from('users')
      .update({
        reset_token: resetToken,
        reset_token_expires: resetExpires.toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id)

    if (updateError) {
      console.error('Failed to create reset token:', updateError)
      return {
        success: false,
        message: 'Failed to create password reset request',
        error: updateError.message
      }
    }

    console.log(`✅ Reset token created for: ${email}`)

    // Send password reset email
    try {
      const emailResult = await resendEmailService.sendPasswordResetEmail({
        email: user.email,
        resetToken,
        userName: user.username || user.full_name || 'User'
      })

      if (!emailResult.success) {
        console.error('Failed to send password reset email:', emailResult.error)
        // Don't fail the request - still return success for security
        await logPasswordResetEvent('EMAIL_FAILED', email, {
          ipAddress,
          error: emailResult.error
        })
      } else {
        console.log(`✅ Password reset email sent to: ${email}`)
        await logPasswordResetEvent('EMAIL_SENT', email, {
          ipAddress,
          messageId: emailResult.messageId
        })
      }
    } catch (emailError: any) {
      console.error('Password reset email error:', emailError)
      await logPasswordResetEvent('EMAIL_ERROR', email, {
        ipAddress,
        error: emailError.message
      })
    }

    return {
      success: true,
      message: 'If an account with this email exists, a reset link has been sent.'
    }

  } catch (error: any) {
    console.error('Password reset request failed:', error)
    return {
      success: false,
      message: 'Failed to process password reset request',
      error: error.message
    }
  }
}

/**
 * Validate a password reset token
 */
export const validateResetToken = async (token: string): Promise<{
  valid: boolean
  user?: any
  error?: string
}> => {
  try {
    if (!token || token.length < 32) {
      return {
        valid: false,
        error: 'Invalid reset token format'
      }
    }

    // Find user with this reset token (using service role for elevated permissions)
    const serviceClient = getServiceRoleClient()
    const { data: user, error: userError } = await serviceClient
      .from('users')
      .select('id, email, username, reset_token, reset_token_expires')
      .eq('reset_token', token)
      .single()

    if (userError || !user) {
      return {
        valid: false,
        error: 'Invalid or expired reset token'
      }
    }

    // Check if token has expired
    const now = new Date()
    const expiresAt = new Date(user.reset_token_expires)

    if (now > expiresAt) {
      return {
        valid: false,
        error: 'Reset token has expired'
      }
    }

    return {
      valid: true,
      user: {
        id: user.id,
        email: user.email,
        username: user.username
      }
    }

  } catch (error: any) {
    console.error('Reset token validation failed:', error)
    return {
      valid: false,
      error: 'Failed to validate reset token'
    }
  }
}

/**
 * Reset password using a valid token
 */
export const resetPasswordWithToken = async (
  token: string, 
  newPassword: string
): Promise<PasswordResetResult> => {
  try {
    console.log(`🔄 Attempting password reset with token: ${token.substring(0, 16)}...`)

    // Validate the token first
    const tokenValidation = await validateResetToken(token)
    if (!tokenValidation.valid || !tokenValidation.user) {
      return {
        success: false,
        message: 'Invalid or expired reset token',
        error: tokenValidation.error
      }
    }

    // Validate new password strength
    const passwordValidation = validatePasswordStrength(newPassword)
    if (!passwordValidation.valid) {
      return {
        success: false,
        message: passwordValidation.errors[0],
        error: 'Password validation failed'
      }
    }

    // Hash the new password
    const hashedPassword = await hashPassword(newPassword)

    // Update user with new password and clear reset token (using service role for elevated permissions)
    const serviceClient = getServiceRoleClient()
    const { error: updateError } = await serviceClient
      .from('users')
      .update({
        password_hash: hashedPassword,
        reset_token: null,
        reset_token_expires: null,
        updated_at: new Date().toISOString()
      })
      .eq('id', tokenValidation.user.id)

    if (updateError) {
      console.error('Failed to update password:', updateError)
      return {
        success: false,
        message: 'Failed to update password',
        error: updateError.message
      }
    }

    console.log(`✅ Password reset successful for: ${tokenValidation.user.email}`)

    return {
      success: true,
      message: 'Password has been reset successfully. You can now login with your new password.'
    }

  } catch (error: any) {
    console.error('Password reset failed:', error)
    return {
      success: false,
      message: 'Failed to reset password',
      error: error.message
    }
  }
}

/**
 * Check if a user needs password reset (migrated user)
 */
export const checkUserNeedsPasswordReset = async (email: string): Promise<{
  needsReset: boolean
  hasResetToken: boolean
  user?: any
}> => {
  try {
    const serviceClient = getServiceRoleClient()
    const { data: user, error } = await serviceClient
      .from('users')
      .select('id, email, password_hash, reset_token, reset_token_expires')
      .eq('email', email)
      .single()

    if (error || !user) {
      return {
        needsReset: false,
        hasResetToken: false
      }
    }

    // User needs reset if they have no password hash (migrated user)
    const needsReset = !user.password_hash || user.password_hash === null

    // Check if they have a valid reset token
    const hasResetToken = !!(user.reset_token && 
      user.reset_token_expires && 
      new Date(user.reset_token_expires) > new Date())

    return {
      needsReset,
      hasResetToken,
      user: {
        id: user.id,
        email: user.email
      }
    }

  } catch (error: any) {
    console.error('Failed to check user reset status:', error)
    return {
      needsReset: false,
      hasResetToken: false
    }
  }
}

/**
 * Test the password reset system
 */
export const testPasswordResetSystem = async (): Promise<boolean> => {
  try {
    console.log('🧪 Testing password reset system...')

    // Test token generation
    const token1 = generateResetToken()
    const token2 = generateResetToken()

    if (token1.length !== 64 || token2.length !== 64 || token1 === token2) {
      console.error('❌ Token generation test failed')
      return false
    }

    console.log('✅ Token generation test passed')

    // Test password validation
    const weakPassword = 'password123'
    const strongPassword = 'SecurePassword123!'

    const weakValidation = validatePasswordStrength(weakPassword)
    const strongValidation = validatePasswordStrength(strongPassword)

    if (weakValidation.valid || !strongValidation.valid) {
      console.error('❌ Password validation test failed')
      return false
    }

    console.log('✅ Password validation test passed')
    console.log('✅ Password reset system test completed successfully')

    return true

  } catch (error: any) {
    console.error('❌ Password reset system test failed:', error)
    return false
  }
}

/**
 * Log password reset events for security monitoring
 */
const logPasswordResetEvent = async (
  eventType: string,
  email: string,
  metadata: any
): Promise<void> => {
  try {
    const serviceClient = getServiceRoleClient()
    await serviceClient
      .from('admin_audit_logs')
      .insert({
        admin_email: 'password_reset_system',
        action: `PASSWORD_RESET_${eventType}`,
        target_type: 'password_reset',
        target_id: email,
        metadata: {
          ...metadata,
          timestamp: new Date().toISOString()
        },
        created_at: new Date().toISOString()
      });

  } catch (error) {
    console.error('❌ Failed to log password reset event:', error);
  }
}
