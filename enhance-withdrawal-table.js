import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function enhanceWithdrawalTable() {
  console.log('🔧 Enhancing commission_withdrawal_requests table...')

  try {
    // Check current table structure
    const { data: columns, error: columnsError } = await supabase
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_name', 'commission_withdrawal_requests')
      .eq('table_schema', 'public')

    if (columnsError) {
      console.error('❌ Error checking table structure:', columnsError)
      return
    }

    const existingColumns = columns.map(col => col.column_name)
    console.log('📋 Existing columns:', existingColumns)

    // Define new columns to add
    const newColumns = [
      {
        name: 'company_wallet_address',
        definition: 'VARCHAR(255)',
        description: 'Company wallet address used to send funds'
      },
      {
        name: 'proof_of_payment',
        definition: 'TEXT',
        description: 'URL or reference to proof of payment document'
      },
      {
        name: 'withdrawal_method',
        definition: 'VARCHAR(50) DEFAULT \'crypto\'',
        description: 'Method used for withdrawal (crypto, bank_transfer, etc.)'
      },
      {
        name: 'processing_notes',
        definition: 'TEXT',
        description: 'Internal processing notes for admins'
      },
      {
        name: 'approved_by_admin_id',
        definition: 'VARCHAR(255)',
        description: 'ID of admin who approved the withdrawal'
      },
      {
        name: 'approved_at',
        definition: 'TIMESTAMP WITH TIME ZONE',
        description: 'Timestamp when withdrawal was approved'
      }
    ]

    // Add missing columns
    for (const column of newColumns) {
      if (!existingColumns.includes(column.name)) {
        console.log(`➕ Adding column: ${column.name}`)
        
        const sql = `
          ALTER TABLE commission_withdrawal_requests 
          ADD COLUMN IF NOT EXISTS ${column.name} ${column.definition};
          
          COMMENT ON COLUMN commission_withdrawal_requests.${column.name} 
          IS '${column.description}';
        `

        try {
          const { error } = await supabase.rpc('exec_sql', { sql_query: sql })
          
          if (error) {
            console.error(`❌ Error adding ${column.name}:`, error)
          } else {
            console.log(`✅ Added column: ${column.name}`)
          }
        } catch (err) {
          console.error(`❌ Exception adding ${column.name}:`, err)
        }
      } else {
        console.log(`ℹ️ Column ${column.name} already exists`)
      }
    }

    // Create indexes for better performance
    console.log('📊 Creating indexes...')
    
    const indexes = [
      {
        name: 'idx_withdrawal_requests_status',
        sql: 'CREATE INDEX IF NOT EXISTS idx_withdrawal_requests_status ON commission_withdrawal_requests(status);'
      },
      {
        name: 'idx_withdrawal_requests_user_id',
        sql: 'CREATE INDEX IF NOT EXISTS idx_withdrawal_requests_user_id ON commission_withdrawal_requests(user_id);'
      },
      {
        name: 'idx_withdrawal_requests_created_at',
        sql: 'CREATE INDEX IF NOT EXISTS idx_withdrawal_requests_created_at ON commission_withdrawal_requests(created_at);'
      },
      {
        name: 'idx_withdrawal_requests_processed_at',
        sql: 'CREATE INDEX IF NOT EXISTS idx_withdrawal_requests_processed_at ON commission_withdrawal_requests(processed_at);'
      }
    ]

    for (const index of indexes) {
      try {
        const { error } = await supabase.rpc('exec_sql', { sql_query: index.sql })
        
        if (error) {
          console.error(`❌ Error creating ${index.name}:`, error)
        } else {
          console.log(`✅ Created index: ${index.name}`)
        }
      } catch (err) {
        console.log(`ℹ️ Index ${index.name} might already exist`)
      }
    }

    // Update RLS policies if needed
    console.log('🔒 Updating RLS policies...')
    
    const rlsPolicies = [
      {
        name: 'Service role can manage withdrawal requests',
        sql: `
          CREATE POLICY IF NOT EXISTS "Service role can manage withdrawal requests" 
          ON commission_withdrawal_requests FOR ALL 
          USING (auth.role() = 'service_role')
          WITH CHECK (auth.role() = 'service_role');
        `
      },
      {
        name: 'Admins can manage withdrawal requests',
        sql: `
          CREATE POLICY IF NOT EXISTS "Admins can manage withdrawal requests" 
          ON commission_withdrawal_requests FOR ALL 
          USING (
            EXISTS (
              SELECT 1 FROM admin_users 
              WHERE email = auth.email()
            )
          )
          WITH CHECK (
            EXISTS (
              SELECT 1 FROM admin_users 
              WHERE email = auth.email()
            )
          );
        `
      }
    ]

    for (const policy of rlsPolicies) {
      try {
        const { error } = await supabase.rpc('exec_sql', { sql_query: policy.sql })
        
        if (error) {
          console.error(`❌ Error creating policy ${policy.name}:`, error)
        } else {
          console.log(`✅ Created/updated policy: ${policy.name}`)
        }
      } catch (err) {
        console.log(`ℹ️ Policy ${policy.name} might already exist`)
      }
    }

    console.log('\n🎉 Withdrawal table enhancement completed!')
    console.log('📋 New columns added for better withdrawal tracking')
    console.log('📊 Performance indexes created')
    console.log('🔒 RLS policies updated')

  } catch (error) {
    console.error('❌ Enhancement failed:', error)
  }
}

// Run the enhancement
enhanceWithdrawalTable()
