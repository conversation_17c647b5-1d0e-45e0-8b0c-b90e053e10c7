#!/usr/bin/env node

/**
 * Test verifyTelegramId Function Logic
 * 
 * This script replicates the verifyTelegramId function logic
 * to test if it correctly identifies profile completion status.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ Missing SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Replicate the verifyTelegramId function logic
async function testVerifyTelegramId(telegramId) {
  try {
    console.log('🔍 Testing verifyTelegramId for:', telegramId);
    
    const telegramIdNum = parseInt(telegramId);
    if (isNaN(telegramIdNum)) {
      return { error: 'Invalid Telegram ID format' };
    }

    // Step 1: Check telegram_users table first
    console.log('🔍 Checking telegram_users table for telegram_id:', telegramIdNum);

    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramIdNum)
      .maybeSingle();

    console.log('🔍 Telegram user query result:', { data: telegramUser, error: telegramError });

    if (telegramError || !telegramUser) {
      console.log('❌ Telegram ID not found in telegram_users table');
      return {
        verified: false,
        error: 'Telegram ID not found. Please check your ID or contact support.',
        needsProfileCompletion: false
      };
    }

    console.log('✅ Found user in telegram_users table:', telegramUser);

    // Step 2: Check if this telegram user has a completed profile in users table
    let userWithCompleteProfile = null;
    
    if (telegramUser.user_id) {
      console.log('🔗 Telegram user has linked user_id, checking users table:', telegramUser.user_id);

      const { data: linkedUser, error: linkedUserError } = await supabase
        .from('users')
        .select('*')
        .eq('id', telegramUser.user_id)
        .maybeSingle();

      if (!linkedUserError && linkedUser) {
        userWithCompleteProfile = linkedUser;
        console.log('✅ Found linked user in users table:', linkedUser.email);
      }
    } else {
      // Also check if user exists in users table by telegram_id (new linking method)
      console.log('🔍 No user_id link, checking users table by telegram_id');

      const { data: userByTelegramId, error: userByTelegramIdError } = await supabase
        .from('users')
        .select('*')
        .eq('telegram_id', telegramIdNum)
        .maybeSingle();

      if (!userByTelegramIdError && userByTelegramId) {
        userWithCompleteProfile = userByTelegramId;
        console.log('✅ Found user in users table by telegram_id:', userByTelegramId.email);
      }
    }

    // Step 3: Determine which user data to use and if profile completion is needed
    let userToUse, needsCompletion;

    if (userWithCompleteProfile) {
      // Use the complete profile from users table
      userToUse = userWithCompleteProfile;

      // Check if profile is actually complete - CRITICAL: Include country_selection_completed check
      needsCompletion = !userToUse.email ||
                       !userToUse.password_hash ||
                       !userToUse.full_name ||
                       !userToUse.phone ||
                       !userToUse.country_of_residence ||
                       !userToUse.country_selection_completed;

      console.log('🔍 Profile completion analysis:', {
        needsCompletion,
        email: !!userToUse.email,
        password_hash: !!userToUse.password_hash,
        full_name: !!userToUse.full_name,
        phone: !!userToUse.phone,
        country_of_residence: !!userToUse.country_of_residence,
        country_selection_completed: !!userToUse.country_selection_completed
      });

      if (needsCompletion) {
        const missing = [];
        if (!userToUse.email) missing.push('email');
        if (!userToUse.password_hash) missing.push('password_hash');
        if (!userToUse.full_name) missing.push('full_name');
        if (!userToUse.phone) missing.push('phone');
        if (!userToUse.country_of_residence) missing.push('country_of_residence');
        if (!userToUse.country_selection_completed) missing.push('country_selection_completed');
        console.log('❌ Missing fields:', missing.join(', '));
      }

      console.log('🔍 Using complete profile, needs completion:', needsCompletion);
    } else {
      // Use telegram_users data, profile completion needed
      userToUse = {
        id: telegramUser.telegram_id,
        username: telegramUser.username || `telegram_${telegramIdNum}`,
        email: telegramUser.temp_email || `telegram_${telegramIdNum}@temp.local`,
        telegram_id: telegramUser.telegram_id,
        password_hash: null,
        created_at: telegramUser.created_at
      };
      needsCompletion = true;
      console.log('🔍 Using telegram_users data, needs completion: true');
    }

    const result = {
      verified: true,
      error: '',
      telegramUser: userToUse,
      needsProfileCompletion: needsCompletion
    };

    console.log('\n🎯 Final verifyTelegramId result:', {
      verified: result.verified,
      needsProfileCompletion: result.needsProfileCompletion,
      userEmail: result.telegramUser.email,
      userName: result.telegramUser.full_name || result.telegramUser.username
    });

    console.log('\n🎯 Expected App Behavior:');
    if (needsCompletion) {
      console.log('   → Should show: Profile Completion Form');
    } else {
      console.log('   → Should show: Password field and direct login');
    }

    return result;

  } catch (error) {
    console.error('❌ verifyTelegramId test failed:', error);
    return {
      verified: false,
      error: 'Verification failed. Please try again.',
      needsProfileCompletion: false
    };
  }
}

// Test with a specific Telegram ID
const testTelegramId = process.argv[2] || '1393852532'; // Default to TTTFOUNDER
console.log(`🧪 Testing verifyTelegramId Function\n`);
testVerifyTelegramId(testTelegramId);
