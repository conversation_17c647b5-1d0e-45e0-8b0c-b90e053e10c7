import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkSystemwideShareCommissionIssue() {
  console.log('🔍 CHECKING SYSTEMWIDE SHARE COMMISSION ISSUE\n');

  // Get ALL commission transactions to see if this is a widespread problem
  const { data: allCommissions, error: commissionError } = await supabase
    .from('commission_transactions')
    .select('*')
    .order('created_at', { ascending: false });

  if (commissionError) {
    console.log('❌ Error getting all commissions:', commissionError.message);
    return;
  }

  console.log(`📊 SYSTEMWIDE COMMISSION ANALYSIS:`);
  console.log(`Total commission transactions: ${allCommissions.length}\n`);

  // Analyze share commission distribution
  const commissionsWithShares = allCommissions.filter(c => (c.share_commission || 0) > 0);
  const commissionsWithoutShares = allCommissions.filter(c => (c.share_commission || 0) === 0);
  const commissionsWithUSDT = allCommissions.filter(c => (c.usdt_commission || 0) > 0);

  console.log(`🔍 COMMISSION BREAKDOWN:`);
  console.log(`• Transactions with USDT commission: ${commissionsWithUSDT.length}`);
  console.log(`• Transactions with Share commission: ${commissionsWithShares.length}`);
  console.log(`• Transactions with NO Share commission: ${commissionsWithoutShares.length}`);

  if (commissionsWithShares.length === 0) {
    console.log(`\n❌ CRITICAL ISSUE: NO commission transactions have share commissions!`);
    console.log(`   This confirms the commission system is broken for share payments.`);
  } else {
    console.log(`\n✅ Some transactions do have share commissions.`);
    console.log(`   Sample transactions with shares:`);
    commissionsWithShares.slice(0, 5).forEach((commission, index) => {
      console.log(`   ${index + 1}. ID: ${commission.id} - Shares: ${commission.share_commission}`);
    });
  }

  // Check commission balances to see if anyone has share balances
  const { data: allBalances, error: balanceError } = await supabase
    .from('commission_balances')
    .select('*')
    .gt('share_balance', 0);

  if (balanceError) {
    console.log('❌ Error getting commission balances:', balanceError.message);
  } else {
    console.log(`\n💰 USERS WITH SHARE BALANCES:`);
    console.log(`Found ${allBalances.length} users with share balances > 0`);
    
    if (allBalances.length > 0) {
      console.log(`Sample users with share balances:`);
      allBalances.slice(0, 10).forEach((balance, index) => {
        console.log(`   ${index + 1}. User ${balance.user_id}: ${balance.share_balance} shares`);
      });
    }
  }

  // Calculate total missing share commissions
  console.log(`\n🧮 CALCULATING TOTAL MISSING SHARE COMMISSIONS:`);
  
  let totalMissingShares = 0;
  let processedCount = 0;
  
  for (const commission of commissionsWithoutShares) {
    if (commission.share_purchase_amount && commission.share_purchase_amount > 0) {
      // Get the actual purchase to find shares purchased
      const { data: purchase, error: purchaseError } = await supabase
        .from('aureus_share_purchases')
        .select('shares_purchased')
        .eq('user_id', commission.referred_id)
        .eq('total_amount', commission.share_purchase_amount)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (!purchaseError && purchase && purchase.shares_purchased) {
        const expectedShareCommission = purchase.shares_purchased * 0.15;
        totalMissingShares += expectedShareCommission;
        processedCount++;
      }
    }
    
    // Limit processing to avoid timeout
    if (processedCount >= 50) {
      console.log(`   (Processed first 50 transactions for estimation...)`);
      break;
    }
  }

  console.log(`📊 MISSING SHARE COMMISSION ESTIMATE:`);
  console.log(`• Processed ${processedCount} transactions`);
  console.log(`• Estimated missing shares: ${totalMissingShares.toFixed(2)}`);
  console.log(`• If this rate applies to all ${commissionsWithoutShares.length} transactions,`);
  console.log(`  total missing shares could be: ${(totalMissingShares * commissionsWithoutShares.length / processedCount).toFixed(2)}`);

  // Check the commission calculation code
  console.log(`\n🔍 NEXT STEPS TO FIX:`);
  console.log(`1. ✅ Confirmed: Share commissions are NOT being calculated/paid`);
  console.log(`2. 🔧 Need to fix the commission calculation code`);
  console.log(`3. 💰 Need to backfill missing share commissions for all users`);
  console.log(`4. 🧪 Need to test the fix with new purchases`);

  console.log('\n✅ Systemwide analysis complete!');
}

// Run the analysis
checkSystemwideShareCommissionIssue().catch(console.error);
