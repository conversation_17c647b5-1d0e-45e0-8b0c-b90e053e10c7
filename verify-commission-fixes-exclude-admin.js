import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

const supabase = createClient(supabaseUrl, supabaseKey);

// Admin/Test accounts that should be excluded from commission calculations
const EXCLUDED_USER_IDS = [4, 80, 89, 102, 103, 104, 105, 106, 107, 111, 113, 115, 123];

async function verifyCommissionFixesExcludeAdmin() {
  console.log('🔍 VERIFYING COMMISSION FIXES - EXCLUDING ADMIN ACCOUNTS\n');
  console.log('❌ EXCLUDED USER IDs:', EXCLUDED_USER_IDS.join(', '));

  // Check if User ID 4 (admin) was incorrectly given commissions
  console.log('\n🚨 CHECKING IF ADMIN ACCOUNTS RECEIVED COMMISSIONS:');
  
  for (const adminId of EXCLUDED_USER_IDS) {
    const { data: adminCommissions, error } = await supabase
      .from('commission_transactions')
      .select(`
        *,
        referrer:users!referrer_id(username, full_name)
      `)
      .eq('referrer_id', adminId);

    if (!error && adminCommissions && adminCommissions.length > 0) {
      console.log(`\n⚠️ ADMIN USER ${adminId} HAS ${adminCommissions.length} COMMISSION TRANSACTIONS:`);
      
      let totalUSDT = 0;
      let totalShares = 0;
      
      adminCommissions.forEach((comm, index) => {
        totalUSDT += comm.usdt_commission || 0;
        totalShares += comm.share_commission || 0;
        
        if (index < 3) { // Show first 3 transactions
          console.log(`   ${index + 1}. $${comm.share_purchase_amount} purchase → $${comm.usdt_commission || 0} USDT + ${comm.share_commission || 0} shares`);
        }
      });
      
      if (adminCommissions.length > 3) {
        console.log(`   ... and ${adminCommissions.length - 3} more transactions`);
      }
      
      console.log(`   📊 TOTAL: $${totalUSDT.toFixed(2)} USDT + ${totalShares.toFixed(4)} shares`);
      
      // Check commission balance
      const { data: balance } = await supabase
        .from('commission_balances')
        .select('*')
        .eq('user_id', adminId)
        .single();
        
      if (balance) {
        console.log(`   💰 BALANCE: $${balance.usdt_balance || 0} USDT + ${balance.share_balance || 0} shares`);
      }
    }
  }

  // Get all legitimate users who should receive commissions
  console.log('\n✅ LEGITIMATE USERS WITH COMMISSIONS:');
  
  const { data: legitimateCommissions, error: legitError } = await supabase
    .from('commission_transactions')
    .select(`
      referrer_id,
      referrer:users!referrer_id(username, full_name),
      count:referrer_id.count(),
      usdt_total:usdt_commission.sum(),
      shares_total:share_commission.sum()
    `)
    .not('referrer_id', 'in', `(${EXCLUDED_USER_IDS.join(',')})`)
    .not('referrer_id', 'is', null);

  if (!legitError && legitimateCommissions) {
    // Group by referrer_id to get totals
    const userTotals = {};
    
    for (const comm of legitimateCommissions) {
      if (!userTotals[comm.referrer_id]) {
        userTotals[comm.referrer_id] = {
          id: comm.referrer_id,
          username: comm.referrer?.username,
          full_name: comm.referrer?.full_name,
          transactions: 0,
          totalUSDT: 0,
          totalShares: 0
        };
      }
    }
    
    // Get actual totals
    const { data: actualTotals, error: totalsError } = await supabase
      .from('commission_transactions')
      .select(`
        referrer_id,
        usdt_commission,
        share_commission,
        referrer:users!referrer_id(username, full_name)
      `)
      .not('referrer_id', 'in', `(${EXCLUDED_USER_IDS.join(',')})`)
      .not('referrer_id', 'is', null);

    if (!totalsError && actualTotals) {
      const userSummary = {};
      
      actualTotals.forEach(comm => {
        if (!userSummary[comm.referrer_id]) {
          userSummary[comm.referrer_id] = {
            id: comm.referrer_id,
            username: comm.referrer?.username,
            full_name: comm.referrer?.full_name,
            transactions: 0,
            totalUSDT: 0,
            totalShares: 0
          };
        }
        
        userSummary[comm.referrer_id].transactions++;
        userSummary[comm.referrer_id].totalUSDT += comm.usdt_commission || 0;
        userSummary[comm.referrer_id].totalShares += comm.share_commission || 0;
      });
      
      // Sort by total shares descending
      const sortedUsers = Object.values(userSummary)
        .sort((a, b) => b.totalShares - a.totalShares);
      
      console.log(`\n📊 LEGITIMATE COMMISSION RECIPIENTS (${sortedUsers.length} users):`);
      console.log('='.repeat(80));
      
      sortedUsers.forEach((user, index) => {
        console.log(`${index + 1}. ${user.full_name || user.username} (ID: ${user.id})`);
        console.log(`   Transactions: ${user.transactions}`);
        console.log(`   USDT: $${user.totalUSDT.toFixed(2)}`);
        console.log(`   Shares: ${user.totalShares.toFixed(4)}`);
        console.log('');
      });
      
      const grandTotalUSDT = sortedUsers.reduce((sum, user) => sum + user.totalUSDT, 0);
      const grandTotalShares = sortedUsers.reduce((sum, user) => sum + user.totalShares, 0);
      const grandTotalTransactions = sortedUsers.reduce((sum, user) => sum + user.transactions, 0);
      
      console.log('📈 GRAND TOTALS (EXCLUDING ADMIN ACCOUNTS):');
      console.log(`• Total Users: ${sortedUsers.length}`);
      console.log(`• Total Transactions: ${grandTotalTransactions}`);
      console.log(`• Total USDT Commissions: $${grandTotalUSDT.toFixed(2)}`);
      console.log(`• Total Share Commissions: ${grandTotalShares.toFixed(4)}`);
    }
  }

  // Check if we need to reverse admin commissions
  console.log('\n🔧 RECOMMENDATION:');
  console.log('If admin accounts (especially User ID 4) received commissions, they should be:');
  console.log('1. Removed from commission_transactions (or marked as admin)');
  console.log('2. Commission balances reset to 0');
  console.log('3. Those commission amounts redistributed to legitimate users if applicable');

  console.log('\n✅ Verification complete!');
}

// Run the verification
verifyCommissionFixesExcludeAdmin().catch(console.error);
