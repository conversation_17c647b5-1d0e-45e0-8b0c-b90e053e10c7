#!/usr/bin/env node

/**
 * SERVER MAIL CONFIGURATION DIAGNOSTIC
 * 
 * This script checks if your actual server (*************) has a mail server
 * and diagnoses the MX record mismatch issue.
 */

import dns from 'dns';
import { promisify } from 'util';
import net from 'net';

const resolveMx = promisify(dns.resolveMx);
const resolveA = promisify(dns.resolve4);

const DOMAIN = 'aureus.africa';
const VERCEL_IP = '************';
const YOUR_SERVER_IP = '*************';

async function testSmtpConnection(host, port = 25) {
  return new Promise((resolve) => {
    const socket = new net.Socket();
    const timeout = setTimeout(() => {
      socket.destroy();
      resolve({ success: false, error: 'Connection timeout' });
    }, 10000);

    socket.connect(port, host, () => {
      clearTimeout(timeout);
      socket.destroy();
      resolve({ success: true });
    });

    socket.on('error', (error) => {
      clearTimeout(timeout);
      resolve({ success: false, error: error.message });
    });
  });
}

async function diagnoseServerMail() {
  console.log('🔍 SERVER MAIL CONFIGURATION DIAGNOSTIC');
  console.log('========================================\n');
  
  console.log('📋 SERVER INFORMATION:');
  console.log(`   Vercel IP: ${VERCEL_IP} (website hosting)`);
  console.log(`   Your Server IP: ${YOUR_SERVER_IP} (your actual server)`);
  console.log(`   Domain: ${DOMAIN}\n`);
  
  // Step 1: Check current DNS configuration
  console.log('1️⃣ CURRENT DNS CONFIGURATION');
  console.log('-----------------------------');
  try {
    const aRecords = await resolveA(DOMAIN);
    console.log(`A Record for ${DOMAIN}: ${aRecords.join(', ')}`);
    
    if (aRecords.includes(VERCEL_IP)) {
      console.log('✅ Domain points to Vercel (correct for website)');
    }
    if (aRecords.includes(YOUR_SERVER_IP)) {
      console.log('✅ Domain also points to your server');
    }
    
    const mxRecords = await resolveMx(DOMAIN);
    console.log('\nMX Records:');
    mxRecords.forEach((record, index) => {
      console.log(`   ${index + 1}. ${record.exchange} (priority: ${record.priority})`);
    });
    
    if (mxRecords.some(mx => mx.exchange === DOMAIN)) {
      console.log('\n🚨 PROBLEM IDENTIFIED:');
      console.log('   MX record points to aureus.africa');
      console.log('   aureus.africa resolves to Vercel IP');
      console.log('   Vercel does not run mail servers');
      console.log('   Gmail cannot deliver emails to Vercel');
    }
  } catch (error) {
    console.log('❌ Error checking DNS:', error.message);
  }
  console.log('');
  
  // Step 2: Test mail server on your actual server
  console.log('2️⃣ TESTING YOUR SERVER MAIL CAPABILITY');
  console.log('--------------------------------------');
  console.log(`🔌 Testing SMTP on your server (${YOUR_SERVER_IP}:25)...`);
  
  const serverResult = await testSmtpConnection(YOUR_SERVER_IP);
  if (serverResult.success) {
    console.log('✅ SMTP server is running on your server!');
    console.log('   Your server CAN receive emails');
    console.log('   The issue is that MX record points to Vercel instead');
  } else {
    console.log(`❌ No SMTP server on your server: ${serverResult.error}`);
    console.log('   Your server is not configured to receive emails');
  }
  console.log('');
  
  // Step 3: Test Vercel (should fail)
  console.log('3️⃣ TESTING VERCEL MAIL CAPABILITY');
  console.log('---------------------------------');
  console.log(`🔌 Testing SMTP on Vercel (${VERCEL_IP}:25)...`);
  
  const vercelResult = await testSmtpConnection(VERCEL_IP);
  if (vercelResult.success) {
    console.log('✅ Vercel has SMTP server (unexpected)');
  } else {
    console.log(`❌ No SMTP server on Vercel: ${vercelResult.error}`);
    console.log('   This confirms Vercel cannot receive emails');
  }
  console.log('');
  
  // Step 4: Solutions
  console.log('4️⃣ SOLUTIONS');
  console.log('-------------');
  
  if (serverResult.success) {
    console.log('🎯 SOLUTION A: Point MX to Your Server (RECOMMENDED)');
    console.log('   Your server already has a mail server running!');
    console.log('   Update your MX record to point directly to your server:');
    console.log('');
    console.log('   Current MX: aureus.africa (points to Vercel)');
    console.log(`   New MX: mail.aureus.africa (points to ${YOUR_SERVER_IP})`);
    console.log('');
    console.log('   Steps:');
    console.log('   1. Create A record: mail.aureus.africa → *************');
    console.log('   2. Update MX record: 10 mail.aureus.africa');
    console.log('   3. Test email delivery');
    console.log('');
  } else {
    console.log('🎯 SOLUTION A: Set Up Mail Server on Your Server');
    console.log('   Install and configure mail server (Postfix/Dovecot)');
    console.log('   Then update MX records to point to your server');
    console.log('');
  }
  
  console.log('🎯 SOLUTION B: Use Email Hosting Service');
  console.log('   Set up Google Workspace, Microsoft 365, or similar');
  console.log('   Update MX records to point to hosting provider');
  console.log('');
  
  console.log('🎯 SOLUTION C: Email Forwarding');
  console.log('   Use Cloudflare Email Routing or similar service');
  console.log('   Forward <EMAIL> to existing email');
  console.log('');
  
  // Step 5: Immediate action
  console.log('5️⃣ IMMEDIATE ACTION NEEDED');
  console.log('--------------------------');
  console.log('The root cause is clear:');
  console.log('❌ MX record points to Vercel (no mail server)');
  console.log('✅ Need to point MX record to server with mail capability');
  console.log('');
  
  if (serverResult.success) {
    console.log('🚀 QUICK FIX (Your server has mail server):');
    console.log('1. Add DNS A record: mail.aureus.africa → *************');
    console.log('2. Update MX record: 10 mail.aureus.africa');
    console.log('3. Wait for DNS propagation (1-2 hours)');
    console.log('4. Test Gmail → <EMAIL> delivery');
  } else {
    console.log('🔧 SETUP REQUIRED (No mail server on your server):');
    console.log('1. Install mail server on *************, OR');
    console.log('2. Use email hosting service, OR');
    console.log('3. Set up email forwarding');
  }
}

// Run the diagnostic
diagnoseServerMail().catch(console.error);
