/**
 * TRAINING ACADEMY MANAGER
 * 
 * Comprehensive admin interface for managing the affiliate training academy:
 * - Course creation and management
 * - Lesson content management
 * - Assessment builder
 * - Analytics and reporting
 * - User enrollment management
 */

import React, { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';
import { CourseCreationWizard } from './CourseCreationWizard';
import { CreateSampleCourseButton } from './CreateSampleCourseButton';

interface TrainingAcademyManagerProps {
  currentUser: any;
}

interface TrainingCategory {
  id: number;
  name: string;
  description: string;
  icon: string;
  color: string;
  sort_order: number;
  is_active: boolean;
}

interface TrainingCourse {
  id: number;
  title: string;
  slug: string;
  description: string;
  category_id: number;
  difficulty_level: 'Beginner' | 'Intermediate' | 'Advanced';
  estimated_duration: number;
  thumbnail_url?: string;
  status: 'draft' | 'under_review' | 'published' | 'archived';
  version: number;
  created_by: number;
  approved_by?: number;
  published_at?: string;
  enrollment_count: number;
  completion_count: number;
  average_rating: number;
  created_at: string;
  updated_at: string;
  category?: TrainingCategory;
}

type TabType = 'overview' | 'courses' | 'categories' | 'analytics' | 'enrollments';

export const TrainingAcademyManager: React.FC<TrainingAcademyManagerProps> = ({ currentUser }) => {
  const [activeTab, setActiveTab] = useState<TabType>('overview');
  const [loading, setLoading] = useState(true);
  const [courses, setCourses] = useState<TrainingCourse[]>([]);
  const [categories, setCategories] = useState<TrainingCategory[]>([]);
  const [showCourseWizard, setShowCourseWizard] = useState(false);
  const [editingCourse, setEditingCourse] = useState<TrainingCourse | null>(null);
  const [stats, setStats] = useState({
    totalCourses: 0,
    publishedCourses: 0,
    totalEnrollments: 0,
    totalCompletions: 0,
    averageRating: 0
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadCategories(),
        loadCourses(),
        loadStats()
      ]);
    } catch (error) {
      console.error('Error loading training data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const serviceClient = getServiceRoleClient();
      const { data, error } = await serviceClient
        .from('training_categories')
        .select('*')
        .order('sort_order', { ascending: true });

      if (error) throw error;
      setCategories(data || []);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const loadCourses = async () => {
    try {
      const serviceClient = getServiceRoleClient();
      const { data, error } = await serviceClient
        .from('training_courses')
        .select(`
          *,
          category:training_categories(*)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setCourses(data || []);
    } catch (error) {
      console.error('Error loading courses:', error);
    }
  };

  const loadStats = async () => {
    try {
      const serviceClient = getServiceRoleClient();
      
      // Get course statistics
      const { data: courseStats, error: courseError } = await serviceClient
        .from('training_courses')
        .select('status, enrollment_count, completion_count, average_rating');

      if (courseError) throw courseError;

      const totalCourses = courseStats?.length || 0;
      const publishedCourses = courseStats?.filter(c => c.status === 'published').length || 0;
      const totalEnrollments = courseStats?.reduce((sum, c) => sum + (c.enrollment_count || 0), 0) || 0;
      const totalCompletions = courseStats?.reduce((sum, c) => sum + (c.completion_count || 0), 0) || 0;
      const averageRating = courseStats?.length > 0 
        ? courseStats.reduce((sum, c) => sum + (c.average_rating || 0), 0) / courseStats.length 
        : 0;

      setStats({
        totalCourses,
        publishedCourses,
        totalEnrollments,
        totalCompletions,
        averageRating: Math.round(averageRating * 100) / 100
      });
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return '#10b981';
      case 'under_review': return '#f59e0b';
      case 'draft': return '#6b7280';
      case 'archived': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'published': return '✅';
      case 'under_review': return '⏳';
      case 'draft': return '📝';
      case 'archived': return '📦';
      default: return '❓';
    }
  };

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case 'Beginner': return '#10b981';
      case 'Intermediate': return '#f59e0b';
      case 'Advanced': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-xl p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">🎓 Training Academy Management</h1>
        <p className="text-blue-100">
          Manage courses, track progress, and analyze learning outcomes for the affiliate training program.
        </p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Courses</p>
              <p className="text-2xl font-bold text-white">{stats.totalCourses}</p>
            </div>
            <div className="text-2xl">📚</div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Published</p>
              <p className="text-2xl font-bold text-green-400">{stats.publishedCourses}</p>
            </div>
            <div className="text-2xl">✅</div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Enrollments</p>
              <p className="text-2xl font-bold text-blue-400">{stats.totalEnrollments}</p>
            </div>
            <div className="text-2xl">👥</div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Completions</p>
              <p className="text-2xl font-bold text-purple-400">{stats.totalCompletions}</p>
            </div>
            <div className="text-2xl">🎯</div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Avg Rating</p>
              <p className="text-2xl font-bold text-yellow-400">{stats.averageRating.toFixed(1)}</p>
            </div>
            <div className="text-2xl">⭐</div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => setShowCourseWizard(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg transition-colors flex items-center justify-center space-x-2"
          >
            <span>➕</span>
            <span>Create New Course</span>
          </button>

          <button
            onClick={() => setActiveTab('categories')}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded-lg transition-colors flex items-center justify-center space-x-2"
          >
            <span>🏷️</span>
            <span>Manage Categories</span>
          </button>

          <button
            onClick={() => setActiveTab('analytics')}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-lg transition-colors flex items-center justify-center space-x-2"
          >
            <span>📊</span>
            <span>View Analytics</span>
          </button>
        </div>
      </div>

      {/* Sample Course Creation */}
      <CreateSampleCourseButton onCourseCreated={loadData} />

      {/* Recent Courses */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Recent Courses</h3>
        <div className="space-y-3">
          {courses.slice(0, 5).map((course) => (
            <div key={course.id} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="text-lg">{course.category?.icon || '📚'}</div>
                <div>
                  <h4 className="text-white font-medium">{course.title}</h4>
                  <p className="text-gray-400 text-sm">
                    {course.category?.name} • {course.difficulty_level} • {course.estimated_duration} min
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <span 
                  className="px-2 py-1 rounded text-xs font-medium text-white"
                  style={{ backgroundColor: getStatusColor(course.status) }}
                >
                  {getStatusIcon(course.status)} {course.status.replace('_', ' ').toUpperCase()}
                </span>
                <span className="text-gray-400 text-sm">{course.enrollment_count} enrolled</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderCourses = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">📚 Course Management</h2>
        <button
          onClick={() => setShowCourseWizard(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
        >
          ➕ Create New Course
        </button>
      </div>

      {/* Course List */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Course</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Category</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Enrollments</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Rating</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {courses.map((course) => (
                <tr key={course.id} className="hover:bg-gray-700">
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-3">
                      <div className="text-lg">{course.category?.icon || '📚'}</div>
                      <div>
                        <div className="text-white font-medium">{course.title}</div>
                        <div className="text-gray-400 text-sm">
                          <span 
                            className="px-2 py-1 rounded text-xs font-medium text-white mr-2"
                            style={{ backgroundColor: getDifficultyColor(course.difficulty_level) }}
                          >
                            {course.difficulty_level}
                          </span>
                          {course.estimated_duration} minutes
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 text-gray-300">{course.category?.name || 'Uncategorized'}</td>
                  <td className="px-6 py-4">
                    <span 
                      className="px-2 py-1 rounded text-xs font-medium text-white"
                      style={{ backgroundColor: getStatusColor(course.status) }}
                    >
                      {getStatusIcon(course.status)} {course.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-gray-300">{course.enrollment_count}</td>
                  <td className="px-6 py-4 text-gray-300">
                    {course.average_rating > 0 ? (
                      <div className="flex items-center space-x-1">
                        <span>⭐</span>
                        <span>{course.average_rating.toFixed(1)}</span>
                      </div>
                    ) : (
                      <span className="text-gray-500">No ratings</span>
                    )}
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => {
                          setEditingCourse(course);
                          setShowCourseWizard(true);
                        }}
                        className="text-blue-400 hover:text-blue-300 text-sm"
                      >
                        Edit
                      </button>
                      <button className="text-green-400 hover:text-green-300 text-sm">Lessons</button>
                      <button className="text-purple-400 hover:text-purple-300 text-sm">Analytics</button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverview();
      case 'courses':
        return renderCourses();
      case 'categories':
        return <div className="text-white">Categories management coming soon...</div>;
      case 'analytics':
        return <div className="text-white">Analytics dashboard coming soon...</div>;
      case 'enrollments':
        return <div className="text-white">Enrollment management coming soon...</div>;
      default:
        return renderOverview();
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-white">Loading training academy data...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="border-b border-gray-700">
        <nav className="flex space-x-8">
          {[
            { id: 'overview', name: 'Overview', icon: '📊' },
            { id: 'courses', name: 'Courses', icon: '📚' },
            { id: 'categories', name: 'Categories', icon: '🏷️' },
            { id: 'analytics', name: 'Analytics', icon: '📈' },
            { id: 'enrollments', name: 'Enrollments', icon: '👥' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as TabType)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
              }`}
            >
              <span>{tab.icon}</span>
              <span>{tab.name}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {renderTabContent()}

      {/* Course Creation Wizard Modal */}
      {showCourseWizard && (
        <CourseCreationWizard
          onClose={() => {
            setShowCourseWizard(false);
            setEditingCourse(null);
          }}
          onCourseCreated={(courseId) => {
            console.log('Course created/updated:', courseId);
            loadCourses(); // Refresh course list
            loadStats(); // Refresh statistics
          }}
          editingCourse={editingCourse}
        />
      )}
    </div>
  );
};
