#!/usr/bin/env node

/**
 * Setup Telegram ID for User 4
 * 
 * This script sets up the telegram_id field for user 4 so they can
 * use the Telegram ID login functionality.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ Missing SUPABASE_SERVICE_ROLE_KEY in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function setupTelegramIdForUser4() {
  console.log('🔧 Setting up Telegram ID for User 4\n');

  try {
    // 1. Check current user 4 data
    console.log('1. Checking current user 4 data...');
    const { data: user4, error: user4Error } = await supabase
      .from('users')
      .select('*')
      .eq('id', 4)
      .single();

    if (user4Error) {
      console.error('❌ Error fetching user 4:', user4Error);
      return;
    }

    console.log('✅ User 4 found:', {
      id: user4.id,
      email: user4.email,
      username: user4.username,
      telegram_id: user4.telegram_id
    });

    // 2. Check if telegram_id is already set
    if (user4.telegram_id) {
      console.log(`✅ User 4 already has telegram_id: ${user4.telegram_id}`);
      console.log('\n🎯 READY TO TEST:');
      console.log(`Use Telegram ID: ${user4.telegram_id}`);
      return;
    }

    // 3. Look for a suitable telegram_id from telegram_users table
    console.log('\n2. Looking for telegram_users records...');
    const { data: telegramUsers, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .limit(5);

    if (telegramError) {
      console.error('❌ Error fetching telegram_users:', telegramError);
    } else {
      console.log(`✅ Found ${telegramUsers.length} telegram_users records`);
      if (telegramUsers.length > 0) {
        console.log('Sample telegram_users:');
        telegramUsers.forEach((tu, index) => {
          console.log(`   ${index + 1}. ID: ${tu.telegram_id}, Username: ${tu.username}, Name: ${tu.first_name}`);
        });
      }
    }

    // 4. Set a test telegram_id for user 4
    const testTelegramId = 123456789; // Use a test ID
    console.log(`\n3. Setting telegram_id ${testTelegramId} for user 4...`);

    const { data: updatedUser, error: updateError } = await supabase
      .from('users')
      .update({ telegram_id: testTelegramId })
      .eq('id', 4)
      .select()
      .single();

    if (updateError) {
      console.error('❌ Error updating user 4:', updateError);
      return;
    }

    console.log('✅ User 4 updated successfully:', {
      id: updatedUser.id,
      email: updatedUser.email,
      username: updatedUser.username,
      telegram_id: updatedUser.telegram_id
    });

    console.log('\n🎯 TELEGRAM ID LOGIN SETUP COMPLETE!');
    console.log('');
    console.log('📋 TEST INSTRUCTIONS:');
    console.log('1. Open browser at: http://localhost:8001');
    console.log('2. Click "Sign In" button');
    console.log('3. Select "🚀 Quick Login with Telegram ID"');
    console.log(`4. Enter Telegram ID: ${testTelegramId}`);
    console.log('5. Click "Access Account"');
    console.log('');
    console.log('🎉 EXPECTED RESULT:');
    console.log('• User will be logged in directly');
    console.log('• Dashboard will show commission data:');
    console.log('  - USDT Commissions: $3,582.75');
    console.log('  - Share Commissions: 716 shares');
    console.log('  - Total Commission Value: $7,165.50');
    console.log('  - 13 referrals');
    console.log('');
    console.log('✅ The commission calculation fix will be visible!');

  } catch (error) {
    console.error('❌ Setup failed:', error);
  }
}

setupTelegramIdForUser4();
