# Admin User Management Features

## Overview
The Admin User Management system has been enhanced with two powerful new features for improved user support and administration:

1. **Referral Link Display** - View and copy user referral links directly from the admin interface
2. **User Account Impersonation** - Securely log into any user's account for troubleshooting and support

## 1. Referral Link Display

### Purpose
Allows administrators to quickly view and share referral links with users without needing to log into their accounts.

### Features
- **Personal Landing Pages**: Shows username-based landing pages with full company presentation
- **Copy-to-Clipboard**: One-click copying of referral links
- **Generated Codes History**: View all system-generated referral codes for the user
- **Link Status**: See active/inactive status and campaign sources
- **Usage Instructions**: Built-in help for referral links

### How to Use
1. Navigate to Admin Dashboard → User Management
2. Find the user in the user list
3. Click the **🔗 Links** button in the Actions column
4. View available referral links and copy as needed

### Link Types Displayed
- **Personal Landing Page**: `https://aureus.africa/[username]` - Professional landing page with profile, company info, and calculator
- **Generated Codes**: System-created referral codes with tracking information

## 2. User Account Impersonation

### Purpose
Enables administrators to securely log into user accounts for troubleshooting, support, and testing purposes.

### Security Features
- **Audit Logging**: All impersonation sessions are logged with timestamps and admin details
- **Confirmation Required**: Admin must type the username to confirm impersonation
- **Session Indicators**: Clear visual indicators when in impersonation mode
- **Time Limits**: Sessions automatically expire after 4 hours
- **Admin Tracking**: Original admin information is preserved throughout the session

### How to Use
1. Navigate to Admin Dashboard → User Management
2. Find the user in the user list
3. Click the **👤 Login As** button in the Actions column
4. Review the security warning and user information
5. Click "Start Impersonation"
6. Type the username exactly to confirm
7. Click "Confirm Impersonation"
8. A new tab will open with the user's dashboard

### Impersonation Session Features
- **Visual Indicator**: Red banner at top of page showing impersonation status
- **Admin Information**: Shows original admin email and target user details
- **Quick Actions**: 
  - "Admin Dashboard" button to open admin panel in new tab
  - "End Session" button to terminate impersonation
- **Session Time**: Shows when impersonation started
- **Automatic Cleanup**: Session data is cleared when ended

### Security Considerations
- All impersonation activities are logged in the audit trail
- Sessions have a maximum duration of 4 hours
- Only active users can be impersonated
- Confirmation dialog prevents accidental impersonation
- Clear visual indicators prevent confusion about current session

## Technical Implementation

### Components Added
- `ReferralLinksModal.tsx` - Modal for displaying referral links
- `UserImpersonationModal.tsx` - Modal for starting impersonation sessions
- `ImpersonationIndicator.tsx` - Visual indicator for active impersonation
- `adminImpersonation.ts` - Utility functions for session management

### Database Integration
- Uses service role client for admin operations
- Integrates with existing audit logging system
- Reads from `referrals` table for referral code history
- Validates user permissions and status

### Session Management
- Uses localStorage for session persistence
- URL parameters for session validation
- Automatic session cleanup and expiration
- Cross-tab session synchronization

## Best Practices

### For Referral Links
1. Use the personal landing page link for all referral sharing
2. Landing pages include complete company presentation and automatic referral assignment
3. Check referral code history to understand user's referral activity

### For User Impersonation
1. Only use impersonation for legitimate support purposes
2. Always inform users when troubleshooting has been completed
3. End sessions promptly when support is complete
4. Document the reason for impersonation in support tickets
5. Be aware that all actions taken during impersonation are logged

## Troubleshooting

### Common Issues
1. **Referral Links Not Loading**: Check user permissions and database connectivity
2. **Impersonation Session Invalid**: Verify session hasn't expired (4-hour limit)
3. **Copy Function Not Working**: Ensure browser supports clipboard API
4. **Modal Not Opening**: Check for JavaScript errors in browser console

### Error Messages
- "Target user not found" - User ID doesn't exist in database
- "Cannot impersonate inactive user" - User account is disabled
- "Invalid impersonation session" - Session expired or corrupted
- "Session validation failed" - Security check failed

## Audit Trail

All activities are logged in the `admin_audit_logs` table with the following actions:
- `USER_IMPERSONATION_START` - When impersonation begins
- `USER_IMPERSONATION_END` - When impersonation ends
- `VIEW_REFERRAL_LINKS` - When referral links are accessed (optional)

Each log entry includes:
- Admin email
- Target user information
- Timestamp
- Session details
- Action metadata

## Future Enhancements

Potential improvements for future versions:
1. **Bulk Referral Link Export** - Export multiple users' referral links
2. **Impersonation Notifications** - Optional user notifications
3. **Session Recording** - Log specific actions during impersonation
4. **Link Analytics** - Track referral link performance
5. **Custom Link Generation** - Create campaign-specific referral links
