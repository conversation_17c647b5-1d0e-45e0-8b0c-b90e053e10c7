import { getServiceRoleClient } from '../supabase';
import { resendEmailService } from '../resendEmailService';

export interface EmailNotificationData {
  userId: number;
  emailAddress: string;
  notificationType: 'message' | 'commission' | 'referral' | 'share_transfer' | 'share_purchase';
  subject: string;
  content: string;
  templateData?: Record<string, any>;
}

export interface MessageNotificationData {
  messageId: string;
  senderName: string;
  senderUsername: string;
  subject: string;
  messagePreview: string;
  recipientEmail: string;
  recipientId: number;
}

export interface CommissionNotificationData {
  commissionType: 'USDT' | 'shares';
  amount: number;
  sourceUserName: string;
  sourceUsername: string;
  newBalance: number;
  recipientEmail: string;
  recipientId: number;
}

export interface ReferralNotificationData {
  newReferralName: string;
  newReferralUsername: string;
  registrationDate: string;
  newReferralId: number;
  referrerEmail: string;
  referrerId: number;
}

export interface ShareTransferNotificationData {
  shareAmount: number;
  otherPartyName: string;
  otherPartyUsername: string;
  transactionId: string;
  newBalance: number;
  isRecipient: boolean;
  userEmail: string;
  userId: number;
}

export interface SharePurchaseNotificationData {
  packageName: string;
  shareQuantity: number;
  totalAmount: number;
  paymentMethod: string;
  referenceId: string;
  certificateStatus: string;
  userEmail: string;
  userId: number;
}

export class EmailNotificationService {
  private static instance: EmailNotificationService;
  private baseUrl: string;

  private constructor() {
    this.baseUrl = process.env.VITE_APP_URL || 'https://aureusalliance.com';
  }

  public static getInstance(): EmailNotificationService {
    if (!EmailNotificationService.instance) {
      EmailNotificationService.instance = new EmailNotificationService();
    }
    return EmailNotificationService.instance;
  }

  private async logNotification(data: EmailNotificationData, status: 'sent' | 'failed', errorMessage?: string): Promise<void> {
    try {
      const serviceClient = getServiceRoleClient();
      await serviceClient
        .from('email_notifications')
        .insert({
          user_id: data.userId,
          notification_type: data.notificationType,
          email_address: data.emailAddress,
          subject: data.subject,
          content: data.content,
          status,
          sent_at: status === 'sent' ? new Date().toISOString() : null,
          error_message: errorMessage
        });
    } catch (error) {
      console.error('Failed to log email notification:', error);
    }
  }

  private async retryFailedNotification(notificationId: string): Promise<boolean> {
    try {
      const serviceClient = getServiceRoleClient();
      
      // Get the failed notification
      const { data: notification, error } = await serviceClient
        .from('email_notifications')
        .select('*')
        .eq('id', notificationId)
        .eq('status', 'failed')
        .single();

      if (error || !notification || notification.retry_count >= 3) {
        return false;
      }

      // Attempt to resend
      const emailData: EmailNotificationData = {
        userId: notification.user_id,
        emailAddress: notification.email_address,
        notificationType: notification.notification_type as any,
        subject: notification.subject,
        content: notification.content
      };

      const success = await this.sendEmail(emailData);
      
      // Update retry count
      await serviceClient
        .from('email_notifications')
        .update({
          retry_count: notification.retry_count + 1,
          status: success ? 'sent' : 'failed',
          sent_at: success ? new Date().toISOString() : null,
          updated_at: new Date().toISOString()
        })
        .eq('id', notificationId);

      return success;
    } catch (error) {
      console.error('Failed to retry notification:', error);
      return false;
    }
  }

  private async sendEmail(data: EmailNotificationData): Promise<boolean> {
    try {
      await resendEmailService.sendEmail({
        to: data.emailAddress,
        subject: data.subject,
        html: data.content
      });

      await this.logNotification(data, 'sent');
      return true;
    } catch (error) {
      console.error('Failed to send email:', error);
      await this.logNotification(data, 'failed', error instanceof Error ? error.message : 'Unknown error');
      return false;
    }
  }

  public async sendMessageNotification(data: MessageNotificationData): Promise<boolean> {
    const loginLink = `${this.baseUrl}/dashboard?tab=messages&messageId=${data.messageId}`;
    
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Message - Aureus Alliance Holdings</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
          .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
          .header { background: linear-gradient(135deg, #1f2937 0%, #374151 100%); color: white; padding: 20px; border-radius: 10px 10px 0 0; text-align: center; }
          .content { padding: 20px; }
          .message-preview { background: #f8f9fa; border-left: 4px solid #3b82f6; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .cta-button { display: inline-block; background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { text-align: center; color: #666; font-size: 12px; margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>💬 New Message Received</h1>
            <p>You have received a new message in your Aureus Alliance Holdings dashboard</p>
          </div>
          <div class="content">
            <h2>Message Details</h2>
            <p><strong>From:</strong> ${data.senderName} (@${data.senderUsername})</p>
            <p><strong>Subject:</strong> ${data.subject}</p>
            
            <div class="message-preview">
              <h3>Message Preview:</h3>
              <p>${data.messagePreview}${data.messagePreview.length >= 150 ? '...' : ''}</p>
            </div>
            
            <p>To read the full message and reply, please log in to your dashboard:</p>
            <a href="${loginLink}" class="cta-button">📬 Read Full Message</a>
            
            <p><small>This link will take you directly to your messages inbox where you can read the full message and reply.</small></p>
          </div>
          <div class="footer">
            <p>© 2024 Aureus Alliance Holdings (Pty) Ltd. All rights reserved.</p>
            <p>This is an automated notification. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const emailData: EmailNotificationData = {
      userId: data.recipientId,
      emailAddress: data.recipientEmail,
      notificationType: 'message',
      subject: `💬 New Message from ${data.senderName}`,
      content: htmlContent
    };

    return await this.sendEmail(emailData);
  }

  public async sendCommissionNotification(data: CommissionNotificationData): Promise<boolean> {
    const dashboardLink = `${this.baseUrl}/dashboard?tab=commissions`;
    const formattedAmount = data.commissionType === 'USDT' 
      ? `$${data.amount.toFixed(2)}` 
      : `${data.amount.toFixed(2)} shares`;
    const formattedBalance = data.commissionType === 'USDT' 
      ? `$${data.newBalance.toFixed(2)}` 
      : `${data.newBalance.toFixed(2)} shares`;

    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Commission Earned - Aureus Alliance Holdings</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
          .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
          .header { background: linear-gradient(135deg, #059669 0%, #10b981 100%); color: white; padding: 20px; border-radius: 10px 10px 0 0; text-align: center; }
          .content { padding: 20px; }
          .commission-details { background: #f0fdf4; border: 2px solid #10b981; padding: 20px; margin: 15px 0; border-radius: 10px; text-align: center; }
          .amount { font-size: 24px; font-weight: bold; color: #059669; margin: 10px 0; }
          .cta-button { display: inline-block; background: #059669; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { text-align: center; color: #666; font-size: 12px; margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>💰 Commission Earned!</h1>
            <p>Congratulations! You've earned a new commission</p>
          </div>
          <div class="content">
            <div class="commission-details">
              <h2>Commission Details</h2>
              <div class="amount">${formattedAmount}</div>
              <p><strong>Commission Type:</strong> ${data.commissionType}</p>
              <p><strong>From:</strong> ${data.sourceUserName} (@${data.sourceUsername})</p>
              <p><strong>Updated Balance:</strong> ${formattedBalance}</p>
            </div>
            
            <p>This commission has been added to your account and is now available in your dashboard.</p>
            <a href="${dashboardLink}" class="cta-button">💼 View Commission Details</a>
            
            <p><small>Log in to your dashboard to view your complete commission history and withdrawal options.</small></p>
          </div>
          <div class="footer">
            <p>© 2024 Aureus Alliance Holdings (Pty) Ltd. All rights reserved.</p>
            <p>This is an automated notification. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const emailData: EmailNotificationData = {
      userId: data.recipientId,
      emailAddress: data.recipientEmail,
      notificationType: 'commission',
      subject: `💰 Commission Earned: ${formattedAmount}`,
      content: htmlContent
    };

    return await this.sendEmail(emailData);
  }

  public async sendReferralNotification(data: ReferralNotificationData): Promise<boolean> {
    const teamLink = `${this.baseUrl}/dashboard?tab=team&userId=${data.newReferralId}`;

    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Team Member - Aureus Alliance Holdings</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
          .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
          .header { background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%); color: white; padding: 20px; border-radius: 10px 10px 0 0; text-align: center; }
          .content { padding: 20px; }
          .referral-details { background: #faf5ff; border: 2px solid #a855f7; padding: 20px; margin: 15px 0; border-radius: 10px; }
          .cta-button { display: inline-block; background: #7c3aed; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { text-align: center; color: #666; font-size: 12px; margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 New Team Member!</h1>
            <p>Someone has joined your team using your referral code</p>
          </div>
          <div class="content">
            <div class="referral-details">
              <h2>New Team Member Details</h2>
              <p><strong>Name:</strong> ${data.newReferralName}</p>
              <p><strong>Username:</strong> @${data.newReferralUsername}</p>
              <p><strong>Registration Date:</strong> ${data.registrationDate}</p>
            </div>
            
            <p>This new team member represents a commission opportunity! When they make purchases or refer others, you'll earn commissions based on our compensation plan.</p>
            
            <a href="${teamLink}" class="cta-button">👥 View Your Team</a>
            
            <p><small>Visit your team dashboard to see your complete downline and track commission opportunities.</small></p>
          </div>
          <div class="footer">
            <p>© 2024 Aureus Alliance Holdings (Pty) Ltd. All rights reserved.</p>
            <p>This is an automated notification. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const emailData: EmailNotificationData = {
      userId: data.referrerId,
      emailAddress: data.referrerEmail,
      notificationType: 'referral',
      subject: `🎉 New Team Member: ${data.newReferralName} joined your team!`,
      content: htmlContent
    };

    return await this.sendEmail(emailData);
  }

  public async sendShareTransferNotification(data: ShareTransferNotificationData): Promise<boolean> {
    const transfersLink = `${this.baseUrl}/dashboard?tab=transfers`;
    const direction = data.isRecipient ? 'received' : 'sent';
    const actionText = data.isRecipient ? 'received from' : 'sent to';

    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Share Transfer ${data.isRecipient ? 'Received' : 'Sent'} - Aureus Alliance Holdings</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
          .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
          .header { background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%); color: white; padding: 20px; border-radius: 10px 10px 0 0; text-align: center; }
          .content { padding: 20px; }
          .transfer-details { background: #fef2f2; border: 2px solid #ef4444; padding: 20px; margin: 15px 0; border-radius: 10px; }
          .share-amount { font-size: 24px; font-weight: bold; color: #dc2626; margin: 10px 0; }
          .cta-button { display: inline-block; background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { text-align: center; color: #666; font-size: 12px; margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>📊 Share Transfer ${data.isRecipient ? 'Received' : 'Completed'}</h1>
            <p>Your share transfer has been processed</p>
          </div>
          <div class="content">
            <div class="transfer-details">
              <h2>Transfer Details</h2>
              <div class="share-amount">${data.shareAmount.toFixed(2)} shares</div>
              <p><strong>You ${direction}:</strong> ${data.shareAmount.toFixed(2)} shares</p>
              <p><strong>${data.isRecipient ? 'From' : 'To'}:</strong> ${data.otherPartyName} (@${data.otherPartyUsername})</p>
              <p><strong>Transaction ID:</strong> ${data.transactionId}</p>
              <p><strong>Updated Balance:</strong> ${data.newBalance.toFixed(2)} shares</p>
            </div>

            <p>This share transfer has been completed and your balance has been updated accordingly.</p>
            <a href="${transfersLink}" class="cta-button">📈 View Transfer History</a>

            <p><small>Visit your dashboard to view your complete transfer history and current share balance.</small></p>
          </div>
          <div class="footer">
            <p>© 2024 Aureus Alliance Holdings (Pty) Ltd. All rights reserved.</p>
            <p>This is an automated notification. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const emailData: EmailNotificationData = {
      userId: data.userId,
      emailAddress: data.userEmail,
      notificationType: 'share_transfer',
      subject: `📊 Share Transfer ${data.isRecipient ? 'Received' : 'Sent'}: ${data.shareAmount.toFixed(2)} shares`,
      content: htmlContent
    };

    return await this.sendEmail(emailData);
  }

  public async sendSharePurchaseConfirmation(data: SharePurchaseNotificationData): Promise<boolean> {
    const portfolioLink = `${this.baseUrl}/dashboard?tab=portfolio`;

    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Share Purchase Confirmation - Aureus Alliance Holdings</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
          .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
          .header { background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%); color: white; padding: 20px; border-radius: 10px 10px 0 0; text-align: center; }
          .content { padding: 20px; }
          .purchase-details { background: #fffbeb; border: 2px solid #fbbf24; padding: 20px; margin: 15px 0; border-radius: 10px; }
          .share-amount { font-size: 24px; font-weight: bold; color: #f59e0b; margin: 10px 0; }
          .cta-button { display: inline-block; background: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { text-align: center; color: #666; font-size: 12px; margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>✅ Share Purchase Confirmed</h1>
            <p>Thank you for your purchase! Your shares have been allocated to your account.</p>
          </div>
          <div class="content">
            <div class="purchase-details">
              <h2>Purchase Details</h2>
              <div class="share-amount">${data.shareQuantity.toFixed(2)} shares</div>
              <p><strong>Package:</strong> ${data.packageName}</p>
              <p><strong>Share Quantity:</strong> ${data.shareQuantity.toFixed(2)} shares</p>
              <p><strong>Total Amount:</strong> $${data.totalAmount.toFixed(2)}</p>
              <p><strong>Payment Method:</strong> ${data.paymentMethod}</p>
              <p><strong>Reference ID:</strong> ${data.referenceId}</p>
              <p><strong>Certificate Status:</strong> ${data.certificateStatus}</p>
            </div>

            <p>Your shares have been successfully allocated to your account. ${data.certificateStatus === 'pending' ? 'Your share certificate is being generated and will be available for download shortly.' : 'Your share certificate is ready for download.'}</p>

            <a href="${portfolioLink}" class="cta-button">📊 View Portfolio</a>

            <p><small>Visit your portfolio to view your complete share holdings and download your certificates.</small></p>
          </div>
          <div class="footer">
            <p>© 2024 Aureus Alliance Holdings (Pty) Ltd. All rights reserved.</p>
            <p>This is an automated notification. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const emailData: EmailNotificationData = {
      userId: data.userId,
      emailAddress: data.userEmail,
      notificationType: 'share_purchase',
      subject: `✅ Share Purchase Confirmed: ${data.shareQuantity.toFixed(2)} shares`,
      content: htmlContent
    };

    return await this.sendEmail(emailData);
  }

  // Rate limiting functionality
  public async checkRateLimit(userId: number): Promise<boolean> {
    try {
      const serviceClient = getServiceRoleClient();
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();

      const { count, error } = await serviceClient
        .from('email_notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .gte('created_at', oneHourAgo);

      if (error) {
        console.error('Error checking rate limit:', error);
        return true; // Allow on error to avoid blocking legitimate emails
      }

      return (count || 0) < 10; // Maximum 10 emails per hour per user
    } catch (error) {
      console.error('Error checking rate limit:', error);
      return true;
    }
  }

  // Retry failed notifications
  public async retryFailedNotifications(): Promise<void> {
    try {
      const serviceClient = getServiceRoleClient();

      // Get failed notifications that haven't exceeded retry limit
      const { data: failedNotifications, error } = await serviceClient
        .from('email_notifications')
        .select('id')
        .eq('status', 'failed')
        .lt('retry_count', 3)
        .order('created_at', { ascending: true })
        .limit(50); // Process in batches

      if (error || !failedNotifications) {
        console.error('Error fetching failed notifications:', error);
        return;
      }

      for (const notification of failedNotifications) {
        await this.retryFailedNotification(notification.id);
        // Small delay between retries to avoid overwhelming the email service
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } catch (error) {
      console.error('Error retrying failed notifications:', error);
    }
  }
}
