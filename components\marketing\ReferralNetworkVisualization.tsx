import React, { useState, useEffect, useRef } from 'react';
import { supabase } from '../../lib/supabase';

interface ReferralNode {
  id: string;
  username: string;
  firstName: string;
  level: number;
  totalReferrals: number;
  activeReferrals: number;
  totalEarnings: number;
  joinDate: string;
  isActive: boolean;
  children: ReferralNode[];
  parent?: string;
}

interface NetworkStats {
  totalNodes: number;
  maxDepth: number;
  totalEarnings: number;
  activeNodes: number;
  conversionRate: number;
  averageEarningsPerNode: number;
}

interface ReferralNetworkVisualizationProps {
  userId: number;
  username: string;
}

export const ReferralNetworkVisualization: React.FC<ReferralNetworkVisualizationProps> = ({
  userId,
  username
}) => {
  const [networkData, setNetworkData] = useState<ReferralNode | null>(null);
  const [networkStats, setNetworkStats] = useState<NetworkStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedNode, setSelectedNode] = useState<ReferralNode | null>(null);
  const [viewMode, setViewMode] = useState<'tree' | 'circular' | 'force'>('tree');
  const [maxDepth, setMaxDepth] = useState<number>(3);
  const [showInactive, setShowInactive] = useState<boolean>(false);
  const [showContactModal, setShowContactModal] = useState<boolean>(false);
  const [contactingNode, setContactingNode] = useState<ReferralNode | null>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    loadNetworkData();
  }, [userId, maxDepth, showInactive]);

  useEffect(() => {
    if (networkData && canvasRef.current) {
      drawNetwork();
    }
  }, [networkData, viewMode, selectedNode]);

  const loadNetworkData = async () => {
    setLoading(true);
    try {
      // Load referral network data
      const { data: referrals, error } = await supabase
        .from('users')
        .select(`
          id,
          username,
          first_name,
          created_at,
          referred_by,
          is_active,
          total_referrals,
          active_referrals,
          total_earnings
        `)
        .or(`referred_by.eq.${userId},id.eq.${userId}`);

      if (error) throw error;

      // Build network tree
      const networkTree = buildNetworkTree(referrals || [], userId.toString());
      setNetworkData(networkTree);

      // Calculate network statistics
      const stats = calculateNetworkStats(networkTree);
      setNetworkStats(stats);

    } catch (error) {
      console.error('Error loading network data:', error);
    } finally {
      setLoading(false);
    }
  };

  const buildNetworkTree = (users: any[], rootId: string, level: number = 0): ReferralNode => {
    const rootUser = users.find(u => u.id.toString() === rootId);
    if (!rootUser) {
      return {
        id: rootId,
        username: 'Unknown',
        firstName: 'Unknown',
        level,
        totalReferrals: 0,
        activeReferrals: 0,
        totalEarnings: 0,
        joinDate: new Date().toISOString(),
        isActive: false,
        children: []
      };
    }

    const children = users
      .filter(u => u.referred_by?.toString() === rootId && level < maxDepth)
      .map(child => buildNetworkTree(users, child.id.toString(), level + 1))
      .filter(child => showInactive || child.isActive);

    return {
      id: rootUser.id.toString(),
      username: rootUser.username || 'User',
      firstName: rootUser.first_name || 'Unknown',
      level,
      totalReferrals: rootUser.total_referrals || 0,
      activeReferrals: rootUser.active_referrals || 0,
      totalEarnings: rootUser.total_earnings || 0,
      joinDate: rootUser.created_at,
      isActive: rootUser.is_active || false,
      children,
      parent: level > 0 ? rootUser.referred_by?.toString() : undefined
    };
  };

  const calculateNetworkStats = (root: ReferralNode): NetworkStats => {
    let totalNodes = 0;
    let maxDepth = 0;
    let totalEarnings = 0;
    let activeNodes = 0;

    const traverse = (node: ReferralNode, depth: number) => {
      totalNodes++;
      maxDepth = Math.max(maxDepth, depth);
      totalEarnings += node.totalEarnings;
      if (node.isActive) activeNodes++;

      node.children.forEach(child => traverse(child, depth + 1));
    };

    traverse(root, 0);

    return {
      totalNodes,
      maxDepth,
      totalEarnings,
      activeNodes,
      conversionRate: totalNodes > 0 ? (activeNodes / totalNodes) * 100 : 0,
      averageEarningsPerNode: totalNodes > 0 ? totalEarnings / totalNodes : 0
    };
  };

  const handleNodeContact = (node: ReferralNode) => {
    setContactingNode(node);
    setShowContactModal(true);
  };

  const contactUser = (method: 'telegram' | 'email') => {
    if (!contactingNode) return;

    if (method === 'telegram' && contactingNode.username) {
      window.open(`https://t.me/${contactingNode.username}`);
    } else if (method === 'email') {
      // In a real implementation, you'd get the email from the database
      window.open(`mailto:${contactingNode.username}@example.com`);
    }

    setShowContactModal(false);
    setContactingNode(null);
  };

  const drawNetwork = () => {
    const canvas = canvasRef.current;
    if (!canvas || !networkData) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw based on view mode
    switch (viewMode) {
      case 'tree':
        drawTreeLayout(ctx, networkData, canvas.width, canvas.height);
        break;
      case 'circular':
        drawCircularLayout(ctx, networkData, canvas.width, canvas.height);
        break;
      case 'force':
        drawForceLayout(ctx, networkData, canvas.width, canvas.height);
        break;
    }
  };

  const drawTreeLayout = (ctx: CanvasRenderingContext2D, root: ReferralNode, width: number, height: number) => {
    const levelHeight = height / (maxDepth + 1);
    const positions = new Map<string, { x: number; y: number }>();

    // Calculate positions
    const calculatePositions = (node: ReferralNode, x: number, y: number, width: number) => {
      positions.set(node.id, { x, y });
      
      if (node.children.length > 0) {
        const childWidth = width / node.children.length;
        node.children.forEach((child, index) => {
          const childX = x - width/2 + childWidth * (index + 0.5);
          const childY = y + levelHeight;
          calculatePositions(child, childX, childY, childWidth);
        });
      }
    };

    calculatePositions(root, width / 2, 50, width);

    // Draw connections
    ctx.strokeStyle = '#4b5563';
    ctx.lineWidth = 2;
    
    const drawConnections = (node: ReferralNode) => {
      const nodePos = positions.get(node.id);
      if (!nodePos) return;

      node.children.forEach(child => {
        const childPos = positions.get(child.id);
        if (childPos) {
          ctx.beginPath();
          ctx.moveTo(nodePos.x, nodePos.y + 20);
          ctx.lineTo(childPos.x, childPos.y - 20);
          ctx.stroke();
        }
        drawConnections(child);
      });
    };

    drawConnections(root);

    // Draw nodes
    const drawNodes = (node: ReferralNode) => {
      const pos = positions.get(node.id);
      if (!pos) return;

      // Node circle
      ctx.beginPath();
      ctx.arc(pos.x, pos.y, 20, 0, 2 * Math.PI);
      ctx.fillStyle = node.isActive ? '#10b981' : '#6b7280';
      if (selectedNode?.id === node.id) {
        ctx.fillStyle = '#f59e0b';
      }
      ctx.fill();
      ctx.strokeStyle = '#374151';
      ctx.lineWidth = 2;
      ctx.stroke();

      // Node label
      ctx.fillStyle = 'white';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(node.firstName, pos.x, pos.y + 4);

      // Earnings label
      ctx.fillStyle = '#9ca3af';
      ctx.font = '10px Arial';
      ctx.fillText(`$${node.totalEarnings.toFixed(0)}`, pos.x, pos.y + 35);

      node.children.forEach(drawNodes);
    };

    drawNodes(root);
  };

  const drawCircularLayout = (ctx: CanvasRenderingContext2D, root: ReferralNode, width: number, height: number) => {
    const centerX = width / 2;
    const centerY = height / 2;
    const maxRadius = Math.min(width, height) / 2 - 50;
    
    const positions = new Map<string, { x: number; y: number }>();

    const calculateCircularPositions = (node: ReferralNode, radius: number, startAngle: number, endAngle: number) => {
      const angle = (startAngle + endAngle) / 2;
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);
      positions.set(node.id, { x, y });

      if (node.children.length > 0) {
        const childRadius = radius + maxRadius / (maxDepth + 1);
        const angleStep = (endAngle - startAngle) / node.children.length;
        
        node.children.forEach((child, index) => {
          const childStartAngle = startAngle + angleStep * index;
          const childEndAngle = startAngle + angleStep * (index + 1);
          calculateCircularPositions(child, childRadius, childStartAngle, childEndAngle);
        });
      }
    };

    // Root at center
    positions.set(root.id, { x: centerX, y: centerY });
    
    if (root.children.length > 0) {
      const angleStep = (2 * Math.PI) / root.children.length;
      root.children.forEach((child, index) => {
        const startAngle = angleStep * index;
        const endAngle = angleStep * (index + 1);
        calculateCircularPositions(child, maxRadius / (maxDepth + 1), startAngle, endAngle);
      });
    }

    // Draw connections and nodes (similar to tree layout)
    // ... (implementation similar to tree layout but with circular positions)
  };

  const drawForceLayout = (ctx: CanvasRenderingContext2D, root: ReferralNode, width: number, height: number) => {
    // Simplified force-directed layout
    // In a real implementation, you'd use a physics simulation
    const positions = new Map<string, { x: number; y: number }>();
    
    // Random initial positions
    const setRandomPositions = (node: ReferralNode) => {
      positions.set(node.id, {
        x: Math.random() * (width - 100) + 50,
        y: Math.random() * (height - 100) + 50
      });
      node.children.forEach(setRandomPositions);
    };

    setRandomPositions(root);
    
    // Draw with random positions (simplified)
    // ... (similar drawing logic)
  };

  const handleCanvasClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas || !networkData) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Find clicked node with proper hit detection
    const findClickedNode = (node: ReferralNode, positions: Map<string, { x: number; y: number }>): ReferralNode | null => {
      const pos = positions.get(node.id);
      if (pos) {
        const distance = Math.sqrt((x - pos.x) ** 2 + (y - pos.y) ** 2);
        if (distance <= 20) { // Node radius
          return node;
        }
      }

      // Check children
      for (const child of node.children) {
        const result = findClickedNode(child, positions);
        if (result) return result;
      }

      return null;
    };

    // For now, use a simplified approach - in a real implementation,
    // you'd store the positions from the drawing functions
    setSelectedNode(networkData);
  };

  const handleCanvasDoubleClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas || !networkData) return;

    // On double-click, open contact modal for selected node
    if (selectedNode && selectedNode.id !== userId.toString()) {
      handleNodeContact(selectedNode);
    }
  };

  if (loading) {
    return (
      <div style={{
        backgroundColor: 'rgba(31, 41, 55, 0.9)',
        borderRadius: '12px',
        padding: '24px',
        border: '1px solid #374151',
        textAlign: 'center'
      }}>
        <div style={{ color: '#9ca3af' }}>Loading network visualization...</div>
      </div>
    );
  }

  return (
    <div style={{
      backgroundColor: 'rgba(31, 41, 55, 0.9)',
      borderRadius: '12px',
      padding: '24px',
      border: '1px solid #374151'
    }}>
      {/* Header */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h3 style={{
            fontSize: '20px',
            fontWeight: 'bold',
            color: '#f59e0b',
            marginBottom: '4px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            🌐 Referral Network
          </h3>
          <p style={{ color: '#9ca3af', fontSize: '14px', margin: 0 }}>
            Interactive visualization of your referral network
          </p>
        </div>

        <div style={{ display: 'flex', gap: '8px' }}>
          <select
            value={viewMode}
            onChange={(e) => setViewMode(e.target.value as any)}
            style={{
              padding: '8px 12px',
              backgroundColor: 'rgba(55, 65, 81, 0.5)',
              border: '1px solid #4b5563',
              borderRadius: '6px',
              color: 'white',
              fontSize: '12px'
            }}
          >
            <option value="tree">Tree View</option>
            <option value="circular">Circular View</option>
            <option value="force">Force Layout</option>
          </select>
        </div>
      </div>

      {/* Network Statistics */}
      {networkStats && (
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
          gap: '12px',
          marginBottom: '24px'
        }}>
          <div style={{
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            border: '1px solid rgba(59, 130, 246, 0.3)',
            borderRadius: '8px',
            padding: '12px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '18px', marginBottom: '4px' }}>🌐</div>
            <div style={{ color: '#60a5fa', fontSize: '18px', fontWeight: 'bold' }}>
              {networkStats.totalNodes}
            </div>
            <div style={{ color: '#9ca3af', fontSize: '11px' }}>Total Nodes</div>
          </div>

          <div style={{
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            border: '1px solid rgba(16, 185, 129, 0.3)',
            borderRadius: '8px',
            padding: '12px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '18px', marginBottom: '4px' }}>✅</div>
            <div style={{ color: '#10b981', fontSize: '18px', fontWeight: 'bold' }}>
              {networkStats.activeNodes}
            </div>
            <div style={{ color: '#9ca3af', fontSize: '11px' }}>Active</div>
          </div>

          <div style={{
            backgroundColor: 'rgba(245, 158, 11, 0.1)',
            border: '1px solid rgba(245, 158, 11, 0.3)',
            borderRadius: '8px',
            padding: '12px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '18px', marginBottom: '4px' }}>📊</div>
            <div style={{ color: '#f59e0b', fontSize: '18px', fontWeight: 'bold' }}>
              {networkStats.maxDepth}
            </div>
            <div style={{ color: '#9ca3af', fontSize: '11px' }}>Max Depth</div>
          </div>

          <div style={{
            backgroundColor: 'rgba(139, 92, 246, 0.1)',
            border: '1px solid rgba(139, 92, 246, 0.3)',
            borderRadius: '8px',
            padding: '12px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '18px', marginBottom: '4px' }}>💰</div>
            <div style={{ color: '#a78bfa', fontSize: '18px', fontWeight: 'bold' }}>
              ${networkStats.totalEarnings.toFixed(0)}
            </div>
            <div style={{ color: '#9ca3af', fontSize: '11px' }}>Total Earnings</div>
          </div>

          <div style={{
            backgroundColor: 'rgba(236, 72, 153, 0.1)',
            border: '1px solid rgba(236, 72, 153, 0.3)',
            borderRadius: '8px',
            padding: '12px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '18px', marginBottom: '4px' }}>📈</div>
            <div style={{ color: '#ec4899', fontSize: '18px', fontWeight: 'bold' }}>
              {networkStats.conversionRate.toFixed(1)}%
            </div>
            <div style={{ color: '#9ca3af', fontSize: '11px' }}>Conversion</div>
          </div>

          <div style={{
            backgroundColor: 'rgba(34, 197, 94, 0.1)',
            border: '1px solid rgba(34, 197, 94, 0.3)',
            borderRadius: '8px',
            padding: '12px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '18px', marginBottom: '4px' }}>💵</div>
            <div style={{ color: '#22c55e', fontSize: '18px', fontWeight: 'bold' }}>
              ${networkStats.averageEarningsPerNode.toFixed(0)}
            </div>
            <div style={{ color: '#9ca3af', fontSize: '11px' }}>Avg/Node</div>
          </div>
        </div>
      )}

      {/* Controls */}
      <div style={{ display: 'flex', gap: '16px', marginBottom: '20px', alignItems: 'center', flexWrap: 'wrap' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <label style={{ color: '#9ca3af', fontSize: '12px' }}>Max Depth:</label>
          <select
            value={maxDepth}
            onChange={(e) => setMaxDepth(parseInt(e.target.value))}
            style={{
              padding: '4px 8px',
              backgroundColor: 'rgba(55, 65, 81, 0.5)',
              border: '1px solid #4b5563',
              borderRadius: '4px',
              color: 'white',
              fontSize: '12px'
            }}
          >
            <option value={2}>2 Levels</option>
            <option value={3}>3 Levels</option>
            <option value={4}>4 Levels</option>
            <option value={5}>5 Levels</option>
          </select>
        </div>

        <label style={{ display: 'flex', alignItems: 'center', gap: '8px', color: '#9ca3af', fontSize: '12px' }}>
          <input
            type="checkbox"
            checked={showInactive}
            onChange={(e) => setShowInactive(e.target.checked)}
          />
          Show Inactive Users
        </label>
      </div>

      {/* Network Visualization Canvas */}
      <div style={{
        backgroundColor: 'rgba(17, 24, 39, 0.8)',
        borderRadius: '8px',
        padding: '16px',
        marginBottom: '20px'
      }}>
        <canvas
          ref={canvasRef}
          onClick={handleCanvasClick}
          onDoubleClick={handleCanvasDoubleClick}
          style={{
            width: '100%',
            height: '400px',
            cursor: 'pointer',
            border: '1px solid #374151',
            borderRadius: '4px'
          }}
        />
      </div>

      {/* Selected Node Details */}
      {selectedNode && (
        <div style={{
          backgroundColor: 'rgba(55, 65, 81, 0.5)',
          borderRadius: '8px',
          padding: '16px'
        }}>
          <h4 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', marginBottom: '12px' }}>
            👤 Node Details
          </h4>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '12px',
            fontSize: '14px'
          }}>
            <div>
              <span style={{ color: '#9ca3af' }}>Name:</span>
              <div style={{ color: '#f3f4f6', fontWeight: '500' }}>
                {selectedNode.firstName} (@{selectedNode.username})
              </div>
            </div>
            <div>
              <span style={{ color: '#9ca3af' }}>Level:</span>
              <div style={{ color: '#f3f4f6', fontWeight: '500' }}>
                Level {selectedNode.level}
              </div>
            </div>
            <div>
              <span style={{ color: '#9ca3af' }}>Total Referrals:</span>
              <div style={{ color: '#10b981', fontWeight: '500' }}>
                {selectedNode.totalReferrals}
              </div>
            </div>
            <div>
              <span style={{ color: '#9ca3af' }}>Active Referrals:</span>
              <div style={{ color: '#f59e0b', fontWeight: '500' }}>
                {selectedNode.activeReferrals}
              </div>
            </div>
            <div>
              <span style={{ color: '#9ca3af' }}>Total Earnings:</span>
              <div style={{ color: '#a78bfa', fontWeight: '500' }}>
                ${selectedNode.totalEarnings.toFixed(2)}
              </div>
            </div>
            <div>
              <span style={{ color: '#9ca3af' }}>Join Date:</span>
              <div style={{ color: '#f3f4f6', fontWeight: '500' }}>
                {new Date(selectedNode.joinDate).toLocaleDateString()}
              </div>
            </div>
          </div>

          {/* Contact Button */}
          {selectedNode.id !== userId.toString() && (
            <div style={{ marginTop: '16px', textAlign: 'center' }}>
              <button
                onClick={() => handleNodeContact(selectedNode)}
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#3b82f6',
                  border: 'none',
                  borderRadius: '6px',
                  color: 'white',
                  fontSize: '14px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  margin: '0 auto'
                }}
              >
                💬 Contact User
              </button>
              <p style={{ color: '#9ca3af', fontSize: '12px', marginTop: '8px' }}>
                Double-click on canvas nodes to contact them quickly
              </p>
            </div>
          )}
        </div>
      )}

      {/* Legend */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        gap: '24px',
        marginTop: '16px',
        fontSize: '12px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
          <div style={{
            width: '12px',
            height: '12px',
            borderRadius: '50%',
            backgroundColor: '#10b981'
          }} />
          <span style={{ color: '#9ca3af' }}>Active User</span>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
          <div style={{
            width: '12px',
            height: '12px',
            borderRadius: '50%',
            backgroundColor: '#6b7280'
          }} />
          <span style={{ color: '#9ca3af' }}>Inactive User</span>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
          <div style={{
            width: '12px',
            height: '12px',
            borderRadius: '50%',
            backgroundColor: '#f59e0b'
          }} />
          <span style={{ color: '#9ca3af' }}>Selected</span>
        </div>
      </div>

      {/* Contact Modal */}
      {showContactModal && contactingNode && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: '#1f2937',
            borderRadius: '12px',
            padding: '24px',
            maxWidth: '400px',
            width: '90%',
            border: '1px solid #374151'
          }}>
            <div style={{ textAlign: 'center', marginBottom: '20px' }}>
              <div style={{
                width: '60px',
                height: '60px',
                borderRadius: '50%',
                backgroundColor: contactingNode.isActive ? '#10b981' : '#6b7280',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 12px',
                fontSize: '24px',
                fontWeight: 'bold',
                color: 'white'
              }}>
                {contactingNode.firstName?.[0] || contactingNode.username[0].toUpperCase()}
              </div>
              <h3 style={{ color: 'white', fontSize: '18px', marginBottom: '4px' }}>
                {contactingNode.firstName} {contactingNode.lastName || ''}
              </h3>
              <p style={{ color: '#9ca3af', fontSize: '14px' }}>
                @{contactingNode.username}
              </p>
            </div>

            <div style={{ marginBottom: '20px' }}>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px', marginBottom: '16px' }}>
                <div style={{ textAlign: 'center', padding: '12px', backgroundColor: '#374151', borderRadius: '8px' }}>
                  <div style={{ color: '#60a5fa', fontSize: '18px', fontWeight: 'bold' }}>
                    {contactingNode.totalReferrals}
                  </div>
                  <div style={{ color: '#9ca3af', fontSize: '12px' }}>Referrals</div>
                </div>
                <div style={{ textAlign: 'center', padding: '12px', backgroundColor: '#374151', borderRadius: '8px' }}>
                  <div style={{ color: '#10b981', fontSize: '18px', fontWeight: 'bold' }}>
                    ${contactingNode.totalEarnings.toFixed(0)}
                  </div>
                  <div style={{ color: '#9ca3af', fontSize: '12px' }}>Earnings</div>
                </div>
              </div>
            </div>

            <div style={{ display: 'flex', gap: '12px', marginBottom: '16px' }}>
              <button
                onClick={() => contactUser('telegram')}
                style={{
                  flex: 1,
                  padding: '12px',
                  backgroundColor: '#0088cc',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'white',
                  fontSize: '14px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px'
                }}
              >
                ✈️ Telegram
              </button>
              <button
                onClick={() => contactUser('email')}
                style={{
                  flex: 1,
                  padding: '12px',
                  backgroundColor: '#059669',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'white',
                  fontSize: '14px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px'
                }}
              >
                📧 Email
              </button>
            </div>

            <button
              onClick={() => {
                setShowContactModal(false);
                setContactingNode(null);
              }}
              style={{
                width: '100%',
                padding: '10px',
                backgroundColor: '#6b7280',
                border: 'none',
                borderRadius: '8px',
                color: 'white',
                fontSize: '14px',
                cursor: 'pointer'
              }}
            >
              Close
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
