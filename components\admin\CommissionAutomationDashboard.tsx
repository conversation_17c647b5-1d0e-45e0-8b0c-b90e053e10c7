import React, { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';

interface CommissionRule {
  id: string;
  rule_name: string;
  commission_type: 'purchase' | 'referral' | 'phase_transition' | 'bonus';
  usdt_percentage: number;
  shares_percentage: number;
  level_multipliers: number[];
  max_levels: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface CommissionStats {
  totalDistributed: number;
  usdtDistributed: number;
  sharesDistributed: number;
  activeRules: number;
  pendingDistributions: number;
  lastDistribution: string;
}

interface CommissionAutomationDashboardProps {
  isAdmin: boolean;
}

export const CommissionAutomationDashboard: React.FC<CommissionAutomationDashboardProps> = ({
  isAdmin
}) => {
  const [commissionRules, setCommissionRules] = useState<CommissionRule[]>([]);
  const [commissionStats, setCommissionStats] = useState<CommissionStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedRule, setSelectedRule] = useState<CommissionRule | null>(null);
  const [showCreateRule, setShowCreateRule] = useState(false);
  const [automationEnabled, setAutomationEnabled] = useState(true);

  useEffect(() => {
    if (isAdmin) {
      loadCommissionData();
    }
  }, [isAdmin]);

  const loadCommissionData = async () => {
    setLoading(true);
    try {
      const serviceClient = getServiceRoleClient()

      // Load commission rules
      const { data: rules, error: rulesError } = await serviceClient
        .from('commission_automation_rules')
        .select('*')
        .order('created_at', { ascending: false });

      if (rulesError) throw rulesError;
      setCommissionRules(rules || []);

      // Load commission statistics
      const { data: stats, error: statsError } = await serviceClient
        .rpc('get_commission_statistics');

      if (!statsError && stats) {
        setCommissionStats(stats);
      }

      // Check automation status
      const { data: settings, error: settingsError } = await serviceClient
        .from('system_settings')
        .select('value')
        .eq('key', 'commission_automation_enabled')
        .single();

      if (!settingsError && settings) {
        setAutomationEnabled(settings.value === 'true');
      }

    } catch (error) {
      console.error('Error loading commission data:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleAutomation = async () => {
    try {
      const serviceClient = getServiceRoleClient()
      const newStatus = !automationEnabled;

      await serviceClient
        .from('system_settings')
        .upsert({
          key: 'commission_automation_enabled',
          value: newStatus.toString(),
          updated_at: new Date().toISOString()
        });

      setAutomationEnabled(newStatus);

      // Log the change
      await serviceClient.from('admin_actions').insert({
        action_type: 'commission_automation_toggle',
        description: `Commission automation ${newStatus ? 'enabled' : 'disabled'}`,
        performed_at: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error toggling automation:', error);
    }
  };

  const createCommissionRule = async (ruleData: Partial<CommissionRule>) => {
    try {
      const { data, error } = await supabase
        .from('commission_automation_rules')
        .insert({
          ...ruleData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      setCommissionRules(prev => [data, ...prev]);
      setShowCreateRule(false);

    } catch (error) {
      console.error('Error creating commission rule:', error);
    }
  };

  const updateCommissionRule = async (ruleId: string, updates: Partial<CommissionRule>) => {
    try {
      const { data, error } = await supabase
        .from('commission_automation_rules')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', ruleId)
        .select()
        .single();

      if (error) throw error;

      setCommissionRules(prev => 
        prev.map(rule => rule.id === ruleId ? data : rule)
      );

    } catch (error) {
      console.error('Error updating commission rule:', error);
    }
  };

  const processManualDistribution = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase.rpc('process_pending_commissions');
      
      if (error) throw error;

      // Refresh data
      await loadCommissionData();
      
      alert(`Successfully processed ${data?.processed_count || 0} commission distributions`);

    } catch (error) {
      console.error('Error processing manual distribution:', error);
      alert('Error processing commissions. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!isAdmin) {
    return (
      <div style={{
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        border: '1px solid rgba(239, 68, 68, 0.3)',
        borderRadius: '12px',
        padding: '24px',
        textAlign: 'center'
      }}>
        <div style={{ fontSize: '48px', marginBottom: '16px' }}>🔒</div>
        <h3 style={{ color: '#ef4444', fontSize: '18px', marginBottom: '8px' }}>
          Access Denied
        </h3>
        <p style={{ color: '#9ca3af', fontSize: '14px' }}>
          This dashboard is only accessible to administrators.
        </p>
      </div>
    );
  }

  if (loading) {
    return (
      <div style={{
        backgroundColor: 'rgba(31, 41, 55, 0.9)',
        borderRadius: '12px',
        padding: '24px',
        border: '1px solid #374151',
        textAlign: 'center'
      }}>
        <div style={{ color: '#9ca3af' }}>Loading commission automation dashboard...</div>
      </div>
    );
  }

  return (
    <div style={{
      backgroundColor: 'rgba(31, 41, 55, 0.9)',
      borderRadius: '12px',
      padding: '24px',
      border: '1px solid #374151'
    }}>
      {/* Header */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h3 style={{
            fontSize: '20px',
            fontWeight: 'bold',
            color: '#f59e0b',
            marginBottom: '4px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            ⚙️ Commission Automation Dashboard
          </h3>
          <p style={{ color: '#9ca3af', fontSize: '14px', margin: 0 }}>
            Manage automated commission distribution rules and settings
          </p>
        </div>

        <div style={{ display: 'flex', gap: '12px' }}>
          <button
            onClick={toggleAutomation}
            style={{
              padding: '8px 16px',
              backgroundColor: automationEnabled ? 'rgba(16, 185, 129, 0.2)' : 'rgba(239, 68, 68, 0.2)',
              border: automationEnabled ? '1px solid #10b981' : '1px solid #ef4444',
              borderRadius: '8px',
              color: automationEnabled ? '#10b981' : '#ef4444',
              fontSize: '12px',
              cursor: 'pointer'
            }}
          >
            {automationEnabled ? '✅ Automation ON' : '❌ Automation OFF'}
          </button>

          <button
            onClick={() => setShowCreateRule(true)}
            style={{
              padding: '8px 16px',
              backgroundColor: 'rgba(59, 130, 246, 0.2)',
              border: '1px solid #3b82f6',
              borderRadius: '8px',
              color: '#60a5fa',
              fontSize: '12px',
              cursor: 'pointer'
            }}
          >
            ➕ New Rule
          </button>
        </div>
      </div>

      {/* Commission Statistics */}
      {commissionStats && (
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '16px',
          marginBottom: '24px'
        }}>
          <div style={{
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            border: '1px solid rgba(59, 130, 246, 0.3)',
            borderRadius: '8px',
            padding: '16px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>💰</div>
            <div style={{ color: '#60a5fa', fontSize: '20px', fontWeight: 'bold' }}>
              ${commissionStats.totalDistributed.toFixed(2)}
            </div>
            <div style={{ color: '#9ca3af', fontSize: '12px' }}>Total Distributed</div>
          </div>

          <div style={{
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            border: '1px solid rgba(16, 185, 129, 0.3)',
            borderRadius: '8px',
            padding: '16px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>💵</div>
            <div style={{ color: '#10b981', fontSize: '20px', fontWeight: 'bold' }}>
              ${commissionStats.usdtDistributed.toFixed(2)}
            </div>
            <div style={{ color: '#9ca3af', fontSize: '12px' }}>USDT Distributed</div>
          </div>

          <div style={{
            backgroundColor: 'rgba(245, 158, 11, 0.1)',
            border: '1px solid rgba(245, 158, 11, 0.3)',
            borderRadius: '8px',
            padding: '16px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>📊</div>
            <div style={{ color: '#f59e0b', fontSize: '20px', fontWeight: 'bold' }}>
              {commissionStats.sharesDistributed.toLocaleString()}
            </div>
            <div style={{ color: '#9ca3af', fontSize: '12px' }}>Shares Distributed</div>
          </div>

          <div style={{
            backgroundColor: 'rgba(139, 92, 246, 0.1)',
            border: '1px solid rgba(139, 92, 246, 0.3)',
            borderRadius: '8px',
            padding: '16px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>⚙️</div>
            <div style={{ color: '#a78bfa', fontSize: '20px', fontWeight: 'bold' }}>
              {commissionStats.activeRules}
            </div>
            <div style={{ color: '#9ca3af', fontSize: '12px' }}>Active Rules</div>
          </div>

          <div style={{
            backgroundColor: 'rgba(236, 72, 153, 0.1)',
            border: '1px solid rgba(236, 72, 153, 0.3)',
            borderRadius: '8px',
            padding: '16px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>⏳</div>
            <div style={{ color: '#ec4899', fontSize: '20px', fontWeight: 'bold' }}>
              {commissionStats.pendingDistributions}
            </div>
            <div style={{ color: '#9ca3af', fontSize: '12px' }}>Pending</div>
          </div>

          <div style={{
            backgroundColor: 'rgba(34, 197, 94, 0.1)',
            border: '1px solid rgba(34, 197, 94, 0.3)',
            borderRadius: '8px',
            padding: '16px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>🕒</div>
            <div style={{ color: '#22c55e', fontSize: '14px', fontWeight: 'bold' }}>
              {commissionStats.lastDistribution ? 
                new Date(commissionStats.lastDistribution).toLocaleDateString() : 
                'Never'
              }
            </div>
            <div style={{ color: '#9ca3af', fontSize: '12px' }}>Last Distribution</div>
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div style={{
        backgroundColor: 'rgba(55, 65, 81, 0.5)',
        borderRadius: '8px',
        padding: '16px',
        marginBottom: '24px'
      }}>
        <h4 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', marginBottom: '12px' }}>
          🚀 Quick Actions
        </h4>
        
        <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
          <button
            onClick={processManualDistribution}
            disabled={loading}
            style={{
              padding: '10px 16px',
              backgroundColor: loading ? 'rgba(55, 65, 81, 0.5)' : 'rgba(16, 185, 129, 0.2)',
              border: '1px solid #10b981',
              borderRadius: '6px',
              color: '#10b981',
              fontSize: '14px',
              cursor: loading ? 'not-allowed' : 'pointer'
            }}
          >
            {loading ? '⏳ Processing...' : '💰 Process Pending Commissions'}
          </button>

          <button
            onClick={() => loadCommissionData()}
            style={{
              padding: '10px 16px',
              backgroundColor: 'rgba(59, 130, 246, 0.2)',
              border: '1px solid #3b82f6',
              borderRadius: '6px',
              color: '#60a5fa',
              fontSize: '14px',
              cursor: 'pointer'
            }}
          >
            🔄 Refresh Data
          </button>

          <button
            onClick={() => window.open('/admin/commission-reports', '_blank')}
            style={{
              padding: '10px 16px',
              backgroundColor: 'rgba(245, 158, 11, 0.2)',
              border: '1px solid #f59e0b',
              borderRadius: '6px',
              color: '#f59e0b',
              fontSize: '14px',
              cursor: 'pointer'
            }}
          >
            📊 View Reports
          </button>
        </div>
      </div>

      {/* Commission Rules */}
      <div style={{
        backgroundColor: 'rgba(55, 65, 81, 0.5)',
        borderRadius: '8px',
        padding: '16px'
      }}>
        <h4 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', marginBottom: '16px' }}>
          📋 Commission Rules
        </h4>

        {commissionRules.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '40px', color: '#9ca3af' }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>⚙️</div>
            <h3 style={{ fontSize: '18px', marginBottom: '8px' }}>No Commission Rules</h3>
            <p style={{ fontSize: '14px' }}>
              Create your first commission rule to start automating distributions.
            </p>
          </div>
        ) : (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {commissionRules.map(rule => (
              <div key={rule.id} style={{
                backgroundColor: 'rgba(75, 85, 99, 0.3)',
                borderRadius: '6px',
                padding: '16px',
                border: '1px solid #6b7280'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                    <h5 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', margin: 0 }}>
                      {rule.rule_name}
                    </h5>
                    <span style={{
                      padding: '4px 8px',
                      backgroundColor: rule.is_active ? 'rgba(16, 185, 129, 0.2)' : 'rgba(239, 68, 68, 0.2)',
                      border: rule.is_active ? '1px solid #10b981' : '1px solid #ef4444',
                      borderRadius: '4px',
                      color: rule.is_active ? '#10b981' : '#ef4444',
                      fontSize: '10px',
                      fontWeight: '600'
                    }}>
                      {rule.is_active ? 'ACTIVE' : 'INACTIVE'}
                    </span>
                  </div>

                  <div style={{ display: 'flex', gap: '8px' }}>
                    <button
                      onClick={() => setSelectedRule(rule)}
                      style={{
                        padding: '6px 12px',
                        backgroundColor: 'rgba(59, 130, 246, 0.2)',
                        border: '1px solid #3b82f6',
                        borderRadius: '4px',
                        color: '#60a5fa',
                        fontSize: '12px',
                        cursor: 'pointer'
                      }}
                    >
                      Edit
                    </button>

                    <button
                      onClick={() => updateCommissionRule(rule.id, { is_active: !rule.is_active })}
                      style={{
                        padding: '6px 12px',
                        backgroundColor: rule.is_active ? 'rgba(239, 68, 68, 0.2)' : 'rgba(16, 185, 129, 0.2)',
                        border: rule.is_active ? '1px solid #ef4444' : '1px solid #10b981',
                        borderRadius: '4px',
                        color: rule.is_active ? '#ef4444' : '#10b981',
                        fontSize: '12px',
                        cursor: 'pointer'
                      }}
                    >
                      {rule.is_active ? 'Disable' : 'Enable'}
                    </button>
                  </div>
                </div>

                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
                  gap: '12px',
                  fontSize: '14px'
                }}>
                  <div>
                    <span style={{ color: '#9ca3af' }}>Type:</span>
                    <div style={{ color: '#f3f4f6', fontWeight: '500', textTransform: 'capitalize' }}>
                      {rule.commission_type.replace('_', ' ')}
                    </div>
                  </div>
                  <div>
                    <span style={{ color: '#9ca3af' }}>USDT:</span>
                    <div style={{ color: '#10b981', fontWeight: '500' }}>
                      {rule.usdt_percentage}%
                    </div>
                  </div>
                  <div>
                    <span style={{ color: '#9ca3af' }}>Shares:</span>
                    <div style={{ color: '#f59e0b', fontWeight: '500' }}>
                      {rule.shares_percentage}%
                    </div>
                  </div>
                  <div>
                    <span style={{ color: '#9ca3af' }}>Max Levels:</span>
                    <div style={{ color: '#60a5fa', fontWeight: '500' }}>
                      {rule.max_levels}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Default Commission Structure Display */}
      <div style={{
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        border: '1px solid rgba(16, 185, 129, 0.3)',
        borderRadius: '8px',
        padding: '20px',
        marginTop: '24px'
      }}>
        <h4 style={{ color: '#10b981', fontSize: '16px', fontWeight: '600', marginBottom: '12px' }}>
          💎 Standard Commission Structure (15% USDT + 15% Shares)
        </h4>
        
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '12px',
          fontSize: '14px'
        }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ color: '#10b981', fontSize: '18px', fontWeight: 'bold' }}>Level 1</div>
            <div style={{ color: '#d1d5db' }}>15% USDT + 15% Shares</div>
            <div style={{ color: '#9ca3af', fontSize: '12px' }}>Direct Referrals</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ color: '#f59e0b', fontSize: '18px', fontWeight: 'bold' }}>Level 2</div>
            <div style={{ color: '#d1d5db' }}>7.5% USDT + 7.5% Shares</div>
            <div style={{ color: '#9ca3af', fontSize: '12px' }}>2nd Level</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ color: '#60a5fa', fontSize: '18px', fontWeight: 'bold' }}>Level 3</div>
            <div style={{ color: '#d1d5db' }}>4.5% USDT + 4.5% Shares</div>
            <div style={{ color: '#9ca3af', fontSize: '12px' }}>3rd Level</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ color: '#a78bfa', fontSize: '18px', fontWeight: 'bold' }}>Level 4</div>
            <div style={{ color: '#d1d5db' }}>3% USDT + 3% Shares</div>
            <div style={{ color: '#9ca3af', fontSize: '12px' }}>4th Level</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ color: '#ec4899', fontSize: '18px', fontWeight: 'bold' }}>Level 5</div>
            <div style={{ color: '#d1d5db' }}>1.5% USDT + 1.5% Shares</div>
            <div style={{ color: '#9ca3af', fontSize: '12px' }}>5th Level</div>
          </div>
        </div>
      </div>
    </div>
  );
};
