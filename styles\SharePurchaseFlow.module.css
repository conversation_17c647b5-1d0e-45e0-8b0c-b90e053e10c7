/* Mobile-Responsive Styles for SharePurchaseFlow */

.purchaseFlowContainer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 1000;
  padding: 16px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
  /* Enhanced scrollbar visibility */
  scrollbar-width: thin;
  scrollbar-color: #FFD700 rgba(255, 255, 255, 0.1);
}

/* Container scrollbar styling */
.purchaseFlowContainer::-webkit-scrollbar {
  width: 12px;
  display: block;
}

.purchaseFlowContainer::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.purchaseFlowContainer::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #FFD700, #E6C200);
  border-radius: 6px;
  border: 1px solid rgba(255, 215, 0, 0.3);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.purchaseFlowContainer::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #FFE55C, #FFD700);
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.4);
}

.purchaseFlowModal {
  background-color: rgba(17, 24, 39, 0.95);
  border-radius: 16px;
  border: 1px solid #374151;
  width: 100%;
  max-width: 600px;
  max-height: calc(100vh - 32px);
  overflow-y: auto;
  position: relative;
  display: flex;
  flex-direction: column;
  margin: auto;
  min-height: auto;
  -webkit-overflow-scrolling: touch;
  /* Enhanced scrollbar visibility */
  scrollbar-width: thin;
  scrollbar-color: #FFD700 rgba(255, 255, 255, 0.1);
}

/* Purchase modal scrollbar styling */
.purchaseFlowModal::-webkit-scrollbar {
  width: 12px;
  display: block;
}

.purchaseFlowModal::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.purchaseFlowModal::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #FFD700, #E6C200);
  border-radius: 6px;
  border: 1px solid rgba(255, 215, 0, 0.3);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.purchaseFlowModal::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #FFE55C, #FFD700);
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.4);
}

.purchaseFlowModal::-webkit-scrollbar-thumb:active {
  background: #FFD700;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .purchaseFlowContainer {
    padding: 8px;
    align-items: flex-start;
    padding-top: 20px;
    padding-bottom: 20px;
  }

  .purchaseFlowModal {
    max-height: none;
    min-height: auto;
    border-radius: 12px;
    margin-top: 0;
    margin-bottom: 0;
  }
}

@media (max-width: 480px) {
  .purchaseFlowContainer {
    padding: 4px;
    padding-top: 10px;
    padding-bottom: 20px;
  }

  .purchaseFlowModal {
    max-height: none;
    min-height: auto;
    height: auto;
  }
}

/* Additional mobile scrolling fixes */
@media (max-width: 768px) {
  body.modal-open {
    overflow: hidden;
    position: fixed;
    width: 100%;
  }

  .purchaseFlowContainer {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
    overscroll-behavior: contain !important;
    padding: 8px !important;
  }

  .purchaseFlowModal {
    position: relative !important;
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    border-radius: 12px !important;
    max-height: calc(100vh - 16px) !important;
    overflow-y: auto !important;
    display: flex !important;
    flex-direction: column !important;
    -webkit-overflow-scrolling: touch !important;
  }
}

  .purchaseFlowModal {
    border-radius: 8px;
    max-height: 98vh;
  }

/* Touch-Optimized Buttons */
.quickAmountButton {
  padding: 16px 12px;
  background-color: rgba(55, 65, 81, 0.5);
  border: 1px solid #4b5563;
  border-radius: 8px;
  color: #d1d5db;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 48px; /* Touch-friendly minimum */
  display: flex;
  align-items: center;
  justify-content: center;
}

.quickAmountButton:hover {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: #3b82f6;
  color: #60a5fa;
}

.quickAmountButton.active {
  background-color: rgba(59, 130, 246, 0.2);
  border: 2px solid #3b82f6;
  color: #60a5fa;
}

.quickAmountButton:active {
  transform: scale(0.98);
}

/* Mobile Grid Adjustments */
.quickAmountGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 8px;
}

@media (max-width: 480px) {
  .quickAmountGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
  }
  
  .quickAmountButton {
    padding: 14px 8px;
    font-size: 13px;
    min-height: 44px;
  }
}

/* Input Field Optimizations */
.customAmountInput {
  width: 100%;
  padding: 16px 16px 16px 40px;
  background-color: rgba(31, 41, 55, 0.8);
  border: 2px solid #374151;
  border-radius: 12px;
  color: white;
  font-size: 18px;
  font-weight: 600;
  transition: border-color 0.2s ease;
}

.customAmountInput:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

@media (max-width: 480px) {
  .customAmountInput {
    padding: 14px 14px 14px 36px;
    font-size: 16px; /* Prevents zoom on iOS */
    border-radius: 8px;
  }
}

/* Purchase Preview Cards */
.purchasePreviewGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

@media (max-width: 480px) {
  .purchasePreviewGrid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

.previewCard {
  text-align: center;
  padding: 16px;
  background-color: rgba(55, 65, 81, 0.3);
  border-radius: 8px;
  border: 1px solid #4b5563;
}

@media (max-width: 480px) {
  .previewCard {
    padding: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;
  }
}

/* One-Click Purchase Buttons */
.oneClickButton {
  padding: 16px;
  background-color: rgba(16, 185, 129, 0.1);
  border: 2px solid rgba(16, 185, 129, 0.3);
  border-radius: 12px;
  color: #10b981;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.2s ease;
  min-height: 56px; /* Touch-friendly */
}

.oneClickButton:hover {
  background-color: rgba(16, 185, 129, 0.2);
  border-color: rgba(16, 185, 129, 0.5);
}

.oneClickButton:active {
  transform: scale(0.98);
}

@media (max-width: 480px) {
  .oneClickButton {
    padding: 14px;
    font-size: 15px;
    min-height: 52px;
    border-radius: 8px;
  }
}

/* Continue Button */
.continueButton {
  width: 100%;
  padding: 16px;
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 56px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.continueButton:disabled {
  background: rgba(55, 65, 81, 0.5);
  cursor: not-allowed;
}

.continueButton:not(:disabled):hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.continueButton:not(:disabled):active {
  transform: translateY(0);
}

@media (max-width: 480px) {
  .continueButton {
    padding: 14px;
    font-size: 16px;
    min-height: 52px;
    border-radius: 8px;
  }
}

/* Real-time Price Display */
.priceDisplay {
  background-color: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  animation: priceUpdate 0.3s ease;
}

@keyframes priceUpdate {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

@media (max-width: 480px) {
  .priceDisplay {
    padding: 12px;
    border-radius: 8px;
  }
}

/* Loading States */
.loadingSpinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #374151;
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Accessibility Improvements */
.srOnly {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus Indicators */
.focusable:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Smooth Scrolling */
.purchaseFlowModal {
  scroll-behavior: smooth;
}

/* iOS Safari Specific Fixes */
@supports (-webkit-touch-callout: none) {
  .customAmountInput {
    font-size: 16px; /* Prevents zoom */
  }

  .purchaseFlowModal {
    -webkit-overflow-scrolling: touch;
  }
}

/* Ensure modal content is scrollable and purchase button is accessible */
.purchaseFlowModal > * {
  flex-shrink: 0;
}

/* Force scrollable content area */
.purchaseFlowModal {
  scroll-behavior: smooth;
}

/* Ensure buttons are always accessible */
.purchaseFlowModal button[type="submit"],
.purchaseFlowModal .purchase-button,
.purchaseFlowModal .continue-button {
  margin-top: auto;
  margin-bottom: 16px;
}
