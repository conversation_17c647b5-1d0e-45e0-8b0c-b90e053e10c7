#!/usr/bin/env node

/**
 * Health Check Script
 * Validates system dependencies and configuration
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function checkNodeVersion() {
  try {
    const { stdout } = await execAsync('node --version');
    const version = stdout.trim();
    const majorVersion = parseInt(version.replace('v', '').split('.')[0]);
    
    if (majorVersion >= 18) {
      console.log(`✅ Node.js version: ${version} (supported)`);
      return true;
    } else {
      console.log(`❌ Node.js version: ${version} (requires v18 or higher)`);
      return false;
    }
  } catch (error) {
    console.log('❌ Node.js: Not found or not accessible');
    return false;
  }
}

async function checkNpmVersion() {
  try {
    const { stdout } = await execAsync('npm --version');
    const version = stdout.trim();
    console.log(`✅ npm version: ${version}`);
    return true;
  } catch (error) {
    console.log('❌ npm: Not found or not accessible');
    return false;
  }
}

function checkProjectStructure() {
  const requiredFiles = [
    'package.json',
    'vite.config.ts',
    'tsconfig.json',
    'tailwind.config.js',
    'index.html',
    'src/App.tsx',
    'src/index.tsx'
  ];

  const requiredDirectories = [
    'src',
    'components',
    'lib',
    'hooks',
    'api'
  ];

  let allPresent = true;

  console.log('\n📁 Checking project structure:');
  
  // Check files
  requiredFiles.forEach(file => {
    const filePath = path.join(__dirname, '..', file);
    if (fs.existsSync(filePath)) {
      console.log(`  ✅ ${file}`);
    } else {
      console.log(`  ❌ ${file} - Missing`);
      allPresent = false;
    }
  });

  // Check directories
  requiredDirectories.forEach(dir => {
    const dirPath = path.join(__dirname, '..', dir);
    if (fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory()) {
      console.log(`  ✅ ${dir}/`);
    } else {
      console.log(`  ❌ ${dir}/ - Missing`);
      allPresent = false;
    }
  });

  return allPresent;
}

function checkEnvironmentFile() {
  const envPath = path.join(__dirname, '..', '.env');
  
  if (!fs.existsSync(envPath)) {
    console.log('❌ .env file: Not found');
    return false;
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const hasSupabaseUrl = envContent.includes('VITE_SUPABASE_URL');
  const hasSupabaseKey = envContent.includes('VITE_SUPABASE_ANON_KEY');
  
  if (hasSupabaseUrl && hasSupabaseKey) {
    console.log('✅ .env file: Present with required variables');
    return true;
  } else {
    console.log('❌ .env file: Missing required Supabase configuration');
    return false;
  }
}

async function checkDependencies() {
  const packageJsonPath = path.join(__dirname, '..', 'package.json');
  
  if (!fs.existsSync(packageJsonPath)) {
    console.log('❌ package.json: Not found');
    return false;
  }

  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
    
    const criticalDeps = [
      'react',
      'react-dom',
      'vite',
      '@vitejs/plugin-react',
      'typescript',
      '@supabase/supabase-js',
      'tailwindcss'
    ];

    let allPresent = true;
    
    console.log('\n📦 Checking critical dependencies:');
    
    criticalDeps.forEach(dep => {
      if (dependencies[dep]) {
        console.log(`  ✅ ${dep}: ${dependencies[dep]}`);
      } else {
        console.log(`  ❌ ${dep}: Missing`);
        allPresent = false;
      }
    });

    return allPresent;
  } catch (error) {
    console.log('❌ package.json: Invalid JSON format');
    return false;
  }
}

async function checkPortAvailability() {
  const net = await import('net');
  
  function checkPort(port) {
    return new Promise((resolve) => {
      const server = net.createServer();
      
      server.listen(port, () => {
        server.once('close', () => {
          resolve(true); // Port is available
        });
        server.close();
      });
      
      server.on('error', () => {
        resolve(false); // Port is in use
      });
    });
  }

  const frontendPort = 8000;
  const backendPort = 8002;
  
  console.log('\n🔌 Checking port availability:');
  
  const frontendAvailable = await checkPort(frontendPort);
  const backendAvailable = await checkPort(backendPort);
  
  if (frontendAvailable) {
    console.log(`  ✅ Frontend port ${frontendPort}: Available`);
  } else {
    console.log(`  ⚠️  Frontend port ${frontendPort}: In use (will need to stop existing process)`);
  }
  
  if (backendAvailable) {
    console.log(`  ✅ Backend port ${backendPort}: Available`);
  } else {
    console.log(`  ⚠️  Backend port ${backendPort}: In use (will need to stop existing process)`);
  }
  
  return frontendAvailable && backendAvailable;
}

async function runHealthCheck() {
  console.log('🏥 Aureus Africa - System Health Check');
  console.log('=' .repeat(50));
  
  const checks = [
    { name: 'Node.js Version', fn: checkNodeVersion },
    { name: 'npm Version', fn: checkNpmVersion },
    { name: 'Project Structure', fn: () => Promise.resolve(checkProjectStructure()) },
    { name: 'Environment Configuration', fn: () => Promise.resolve(checkEnvironmentFile()) },
    { name: 'Dependencies', fn: checkDependencies },
    { name: 'Port Availability', fn: checkPortAvailability }
  ];

  let allPassed = true;
  
  for (const check of checks) {
    try {
      const result = await check.fn();
      if (!result) {
        allPassed = false;
      }
    } catch (error) {
      console.log(`❌ ${check.name}: Error - ${error.message}`);
      allPassed = false;
    }
  }

  console.log('\n' + '='.repeat(50));
  
  if (allPassed) {
    console.log('✅ All health checks passed!');
    console.log('🚀 System is ready for development.');
    console.log('\nQuick start commands:');
    console.log('  • Start development: npm run dev:full');
    console.log('  • Build for production: npm run build');
    console.log('  • Preview production build: npm run preview');
  } else {
    console.log('❌ Some health checks failed!');
    console.log('Please resolve the issues above before proceeding.');
    console.log('\nCommon solutions:');
    console.log('  • Update Node.js: https://nodejs.org/');
    console.log('  • Install dependencies: npm install');
    console.log('  • Create .env file: Copy from .env.example');
  }
  
  return allPassed;
}

// Run the health check
if (import.meta.url === `file://${process.argv[1]}`) {
  runHealthCheck().then(success => {
    if (!success) {
      process.exit(1);
    }
  });
}

export { runHealthCheck };
