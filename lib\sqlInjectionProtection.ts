/**
 * COMPREHENSIVE SQL INJECTION PROTECTION
 * 
 * This module provides advanced SQL injection protection specifically
 * designed for the Aureus Africa application using Supabase.
 */

import { supabase, getServiceRoleClient } from './supabase';
import { detectMaliciousInput, sanitizeInput } from './inputValidation';

/**
 * Security audit log entry
 */
interface SecurityAuditLog {
  timestamp: string;
  event_type: 'SQL_INJECTION_ATTEMPT' | 'XSS_ATTEMPT' | 'MALICIOUS_INPUT' | 'RATE_LIMIT_EXCEEDED';
  user_id?: number;
  ip_address?: string;
  user_agent?: string;
  input_data: string;
  patterns_detected: string[];
  endpoint: string;
  blocked: boolean;
}

/**
 * Secure database query wrapper that prevents SQL injection
 */
export class SecureDatabase {
  private static serviceClient = getServiceRoleClient();

  /**
   * Log security events to database
   */
  private static async logSecurityEvent(event: SecurityAuditLog): Promise<void> {
    try {
      await this.serviceClient
        .from('security_audit_log')
        .insert([event]);
    } catch (error) {
      console.error('Failed to log security event:', error);
      // Don't throw - logging failure shouldn't break the application
    }
  }

  /**
   * Validate and sanitize user input before database operations
   */
  private static validateInput(input: any, context: string): {
    isValid: boolean;
    sanitized: any;
    issues: string[];
  } {
    if (input === null || input === undefined) {
      return { isValid: true, sanitized: input, issues: [] };
    }

    const inputString = typeof input === 'string' ? input : JSON.stringify(input);
    const maliciousCheck = detectMaliciousInput(inputString);
    
    if (maliciousCheck.isMalicious) {
      // Log the security event
      this.logSecurityEvent({
        timestamp: new Date().toISOString(),
        event_type: 'SQL_INJECTION_ATTEMPT',
        input_data: inputString.substring(0, 500), // Limit log size
        patterns_detected: maliciousCheck.patterns,
        endpoint: context,
        blocked: true
      });

      return {
        isValid: false,
        sanitized: null,
        issues: [`Malicious patterns detected: ${maliciousCheck.patterns.join(', ')}`]
      };
    }

    return {
      isValid: true,
      sanitized: typeof input === 'string' ? sanitizeInput(input) : input,
      issues: []
    };
  }

  /**
   * Secure user lookup by email (parameterized query)
   */
  static async getUserByEmail(email: string): Promise<{ data: any; error: any }> {
    const validation = this.validateInput(email, 'getUserByEmail');
    if (!validation.isValid) {
      return { data: null, error: { message: 'Invalid input detected', details: validation.issues } };
    }

    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('email', validation.sanitized) // Supabase automatically parameterizes
        .single();

      return { data, error };
    } catch (error) {
      console.error('Database query error:', error);
      return { data: null, error: { message: 'Database query failed' } };
    }
  }

  /**
   * Secure user lookup by username (parameterized query)
   */
  static async getUserByUsername(username: string): Promise<{ data: any; error: any }> {
    const validation = this.validateInput(username, 'getUserByUsername');
    if (!validation.isValid) {
      return { data: null, error: { message: 'Invalid input detected', details: validation.issues } };
    }

    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('username', validation.sanitized)
        .single();

      return { data, error };
    } catch (error) {
      console.error('Database query error:', error);
      return { data: null, error: { message: 'Database query failed' } };
    }
  }

  /**
   * Secure payment creation (parameterized query with validation)
   */
  static async createPayment(paymentData: {
    user_id: number;
    amount: number;
    shares_to_purchase: number;
    network: string;
    currency: string;
    sender_wallet: string;
    receiver_wallet: string;
    transaction_hash: string;
    screenshot_url?: string;
    transaction_notes?: string;
  }): Promise<{ data: any; error: any }> {
    // Validate all input fields
    const validations = {
      user_id: this.validateInput(paymentData.user_id, 'createPayment.user_id'),
      amount: this.validateInput(paymentData.amount, 'createPayment.amount'),
      network: this.validateInput(paymentData.network, 'createPayment.network'),
      currency: this.validateInput(paymentData.currency, 'createPayment.currency'),
      sender_wallet: this.validateInput(paymentData.sender_wallet, 'createPayment.sender_wallet'),
      receiver_wallet: this.validateInput(paymentData.receiver_wallet, 'createPayment.receiver_wallet'),
      transaction_hash: this.validateInput(paymentData.transaction_hash, 'createPayment.transaction_hash'),
      screenshot_url: this.validateInput(paymentData.screenshot_url, 'createPayment.screenshot_url'),
      transaction_notes: this.validateInput(paymentData.transaction_notes, 'createPayment.transaction_notes')
    };

    // Check for validation failures
    const invalidFields = Object.entries(validations)
      .filter(([_, validation]) => !validation.isValid)
      .map(([field, validation]) => `${field}: ${validation.issues.join(', ')}`);

    if (invalidFields.length > 0) {
      return { 
        data: null, 
        error: { 
          message: 'Invalid input detected', 
          details: invalidFields 
        } 
      };
    }

    // Additional business logic validation
    if (typeof paymentData.user_id !== 'number' || paymentData.user_id <= 0) {
      return { data: null, error: { message: 'Invalid user ID' } };
    }

    if (typeof paymentData.amount !== 'number' || paymentData.amount < 25 || paymentData.amount > 10000) {
      return { data: null, error: { message: 'Invalid amount (must be between $25 and $10,000)' } };
    }

    if (!['BSC', 'POLYGON', 'TRON', 'ETH'].includes(paymentData.network)) {
      return { data: null, error: { message: 'Invalid network' } };
    }

    try {
      const sanitizedData = {
        user_id: paymentData.user_id,
        amount: paymentData.amount,
        shares_to_purchase: paymentData.shares_to_purchase,
        network: validations.network.sanitized,
        currency: validations.currency.sanitized,
        sender_wallet: validations.sender_wallet.sanitized,
        receiver_wallet: validations.receiver_wallet.sanitized,
        transaction_hash: validations.transaction_hash.sanitized,
        screenshot_url: validations.screenshot_url.sanitized,
        transaction_notes: validations.transaction_notes.sanitized,
        status: 'pending',
        created_at: new Date().toISOString()
      };

      const { data, error } = await this.serviceClient
        .from('crypto_payment_transactions')
        .insert([sanitizedData])
        .select()
        .single();

      return { data, error };
    } catch (error) {
      console.error('Payment creation error:', error);
      return { data: null, error: { message: 'Failed to create payment transaction' } };
    }
  }

  /**
   * Secure user search with filters (parameterized queries)
   */
  static async searchUsers(filters: {
    search?: string;
    country?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ data: any[]; error: any; count?: number }> {
    // Validate all filter inputs
    const validations = {
      search: this.validateInput(filters.search, 'searchUsers.search'),
      country: this.validateInput(filters.country, 'searchUsers.country'),
      status: this.validateInput(filters.status, 'searchUsers.status')
    };

    // Check for validation failures
    const invalidFields = Object.entries(validations)
      .filter(([_, validation]) => !validation.isValid)
      .map(([field, validation]) => `${field}: ${validation.issues.join(', ')}`);

    if (invalidFields.length > 0) {
      return { 
        data: [], 
        error: { 
          message: 'Invalid search parameters', 
          details: invalidFields 
        } 
      };
    }

    try {
      let query = this.serviceClient
        .from('users')
        .select('id, username, email, full_name, country, is_active, created_at', { count: 'exact' });

      // Apply filters using parameterized queries
      if (filters.search && validations.search.sanitized) {
        const searchTerm = validations.search.sanitized;
        query = query.or(`username.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%,full_name.ilike.%${searchTerm}%`);
      }

      if (filters.country && validations.country.sanitized) {
        query = query.eq('country', validations.country.sanitized);
      }

      if (filters.status && validations.status.sanitized) {
        const isActive = validations.status.sanitized === 'active';
        query = query.eq('is_active', isActive);
      }

      // Apply pagination
      const limit = Math.min(filters.limit || 50, 100); // Max 100 results
      const offset = Math.max(filters.offset || 0, 0);
      
      query = query.range(offset, offset + limit - 1);

      const { data, error, count } = await query;

      return { data: data || [], error, count };
    } catch (error) {
      console.error('User search error:', error);
      return { data: [], error: { message: 'Search failed' } };
    }
  }

  /**
   * Secure contact form submission
   */
  static async createContactSubmission(contactData: {
    name: string;
    surname: string;
    email: string;
    message: string;
    ip_address?: string;
    user_agent?: string;
  }): Promise<{ data: any; error: any }> {
    // Validate all input fields
    const validations = {
      name: this.validateInput(contactData.name, 'createContact.name'),
      surname: this.validateInput(contactData.surname, 'createContact.surname'),
      email: this.validateInput(contactData.email, 'createContact.email'),
      message: this.validateInput(contactData.message, 'createContact.message'),
      ip_address: this.validateInput(contactData.ip_address, 'createContact.ip_address'),
      user_agent: this.validateInput(contactData.user_agent, 'createContact.user_agent')
    };

    // Check for validation failures
    const invalidFields = Object.entries(validations)
      .filter(([_, validation]) => !validation.isValid)
      .map(([field, validation]) => `${field}: ${validation.issues.join(', ')}`);

    if (invalidFields.length > 0) {
      return { 
        data: null, 
        error: { 
          message: 'Invalid contact form data', 
          details: invalidFields 
        } 
      };
    }

    try {
      const sanitizedData = {
        name: validations.name.sanitized,
        surname: validations.surname.sanitized,
        email: validations.email.sanitized,
        message: validations.message.sanitized,
        ip_address: validations.ip_address.sanitized,
        user_agent: validations.user_agent.sanitized,
        timestamp: new Date().toISOString(),
        status: 'new'
      };

      const { data, error } = await this.serviceClient
        .from('contact_submissions')
        .insert([sanitizedData])
        .select()
        .single();

      return { data, error };
    } catch (error) {
      console.error('Contact submission error:', error);
      return { data: null, error: { message: 'Failed to submit contact form' } };
    }
  }
}

export default SecureDatabase;
