/**
 * TEST USERNAME CHANGE WITH REFERRAL CODE UPDATES
 * 
 * This script tests the complete username change functionality to ensure
 * that when a user changes their username in settings, it properly updates:
 * 1. users table
 * 2. telegram_users table (if exists)
 * 3. referrals table (referral codes)
 * 4. All related systems remain consistent
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client with service role
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testUsernameChangeWithReferrals() {
  console.log('🧪 TESTING USERNAME CHANGE WITH REFERRAL CODE UPDATES');
  console.log('=====================================================');
  
  try {
    // ===== PART 1: FIND A TEST USER =====
    console.log('\n📋 PART 1: Finding a suitable test user');
    
    // Find a user with referrals to test with
    const { data: usersWithReferrals, error: usersError } = await supabase
      .from('referrals')
      .select(`
        referrer_id,
        users!referrer_id (
          id,
          username,
          full_name
        )
      `)
      .limit(5);
      
    if (usersError) {
      console.log('❌ Error finding users with referrals:', usersError.message);
      return false;
    }
    
    if (!usersWithReferrals || usersWithReferrals.length === 0) {
      console.log('⚠️ No users with referrals found for testing');
      return false;
    }
    
    // Use the first user with referrals (but not user 139 since we just fixed that)
    const testUser = usersWithReferrals.find(ur => ur.users.id !== 139);
    if (!testUser) {
      console.log('⚠️ No suitable test user found (excluding user 139)');
      return false;
    }
    
    const userId = testUser.users.id;
    const currentUsername = testUser.users.username;
    const testUsername = `TEST_${currentUsername}_${Date.now()}`;
    
    console.log('✅ Selected test user:');
    console.log(`   → User ID: ${userId}`);
    console.log(`   → Current Username: ${currentUsername}`);
    console.log(`   → Test Username: ${testUsername}`);
    
    // ===== PART 2: GET BASELINE DATA =====
    console.log('\n📋 PART 2: Getting baseline data before username change');
    
    // Get current referrals
    const { data: beforeReferrals, error: beforeReferralsError } = await supabase
      .from('referrals')
      .select('id, referral_code, referred_id')
      .eq('referrer_id', userId);
      
    if (beforeReferralsError) {
      console.log('❌ Error getting baseline referrals:', beforeReferralsError.message);
      return false;
    }
    
    console.log(`✅ Found ${beforeReferrals.length} referrals before username change:`);
    beforeReferrals.forEach((ref, index) => {
      console.log(`   ${index + 1}. ${ref.referral_code} (referred user: ${ref.referred_id})`);
    });
    
    // ===== PART 3: TEST ATOMIC FUNCTION (if available) =====
    console.log('\n📋 PART 3: Testing atomic username update function');
    
    let atomicFunctionWorks = false;
    try {
      const { error: atomicError } = await supabase.rpc('update_username_atomic', {
        p_user_id: userId,
        p_new_username: testUsername
      });
      
      if (!atomicError) {
        atomicFunctionWorks = true;
        console.log('✅ Atomic function executed successfully');
      } else {
        console.log('⚠️ Atomic function failed:', atomicError.message);
      }
    } catch (error) {
      console.log('⚠️ Atomic function not available:', error.message);
    }
    
    // ===== PART 4: VERIFY UPDATES (if atomic function worked) =====
    if (atomicFunctionWorks) {
      console.log('\n📋 PART 4: Verifying atomic function updates');
      
      // Check users table
      const { data: updatedUser, error: userError } = await supabase
        .from('users')
        .select('username')
        .eq('id', userId)
        .single();
        
      if (userError) {
        console.log('❌ Error checking updated user:', userError.message);
      } else if (updatedUser.username === testUsername) {
        console.log('✅ Users table updated correctly');
      } else {
        console.log('❌ Users table not updated correctly');
      }
      
      // Check referrals table
      const { data: afterReferrals, error: afterReferralsError } = await supabase
        .from('referrals')
        .select('id, referral_code, referred_id')
        .eq('referrer_id', userId);
        
      if (afterReferralsError) {
        console.log('❌ Error checking updated referrals:', afterReferralsError.message);
      } else {
        console.log(`✅ Found ${afterReferrals.length} referrals after username change:`);
        
        let correctlyUpdated = 0;
        afterReferrals.forEach((ref, index) => {
          console.log(`   ${index + 1}. ${ref.referral_code} (referred user: ${ref.referred_id})`);
          
          if (ref.referral_code.includes(testUsername)) {
            correctlyUpdated++;
          }
        });
        
        if (correctlyUpdated === afterReferrals.length) {
          console.log('✅ All referral codes updated correctly');
        } else {
          console.log(`⚠️ Only ${correctlyUpdated}/${afterReferrals.length} referral codes updated`);
        }
      }
      
      // ===== PART 5: RESTORE ORIGINAL USERNAME =====
      console.log('\n📋 PART 5: Restoring original username');
      
      try {
        const { error: restoreError } = await supabase.rpc('update_username_atomic', {
          p_user_id: userId,
          p_new_username: currentUsername
        });
        
        if (!restoreError) {
          console.log('✅ Original username restored successfully');
        } else {
          console.log('⚠️ Error restoring username:', restoreError.message);
          
          // Manual restore
          const { error: manualRestoreError } = await supabase
            .from('users')
            .update({ username: currentUsername })
            .eq('id', userId);
            
          if (!manualRestoreError) {
            console.log('✅ Username restored manually');
          } else {
            console.log('❌ Failed to restore username manually');
          }
        }
      } catch (error) {
        console.log('❌ Error during username restoration:', error.message);
      }
      
    } else {
      // ===== FALLBACK: TEST MANUAL UPDATE =====
      console.log('\n📋 PART 4: Testing manual username update (fallback method)');
      
      // This simulates what the UsernameEditor component does
      const { error: manualUpdateError } = await supabase
        .from('users')
        .update({
          username: testUsername,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);
        
      if (manualUpdateError) {
        console.log('❌ Manual username update failed:', manualUpdateError.message);
        return false;
      }
      
      console.log('✅ Manual username update successful');
      
      // Test referral code updates (simulating the new UsernameEditor logic)
      let updatedReferrals = 0;
      for (const referral of beforeReferrals) {
        if (referral.referral_code && referral.referral_code.includes(currentUsername)) {
          const newReferralCode = referral.referral_code.replace(currentUsername, testUsername);
          
          const { error: referralUpdateError } = await supabase
            .from('referrals')
            .update({
              referral_code: newReferralCode,
              updated_at: new Date().toISOString()
            })
            .eq('id', referral.id);

          if (!referralUpdateError) {
            updatedReferrals++;
            console.log(`✅ Updated referral: ${referral.referral_code} → ${newReferralCode}`);
          } else {
            console.log(`❌ Failed to update referral ${referral.id}:`, referralUpdateError.message);
          }
        }
      }
      
      console.log(`✅ Updated ${updatedReferrals} referral codes manually`);
      
      // Restore original username
      await supabase
        .from('users')
        .update({ username: currentUsername })
        .eq('id', userId);
        
      console.log('✅ Original username restored');
    }
    
    // ===== FINAL VERIFICATION =====
    console.log('\n📋 FINAL VERIFICATION: Checking system consistency');
    
    const { data: finalUser, error: finalUserError } = await supabase
      .from('users')
      .select('username')
      .eq('id', userId)
      .single();
      
    const { data: finalReferrals, error: finalReferralsError } = await supabase
      .from('referrals')
      .select('referral_code')
      .eq('referrer_id', userId);
      
    if (finalUserError || finalReferralsError) {
      console.log('❌ Error in final verification');
      return false;
    }
    
    const usernameRestored = finalUser.username === currentUsername;
    const referralsRestored = finalReferrals.every(ref => 
      ref.referral_code.includes(currentUsername)
    );
    
    if (usernameRestored && referralsRestored) {
      console.log('✅ System consistency verified - all data restored correctly');
      return true;
    } else {
      console.log('⚠️ System consistency issues detected');
      return false;
    }
    
  } catch (error) {
    console.error('💥 Fatal error during testing:', error);
    return false;
  }
}

// Run the test
testUsernameChangeWithReferrals()
  .then((success) => {
    if (success) {
      console.log('\n🎉 USERNAME CHANGE WITH REFERRALS TEST PASSED!');
      console.log('✅ Username changes will now properly update referral codes');
      console.log('✅ System maintains consistency across all tables');
    } else {
      console.log('\n⚠️ USERNAME CHANGE TEST HAD ISSUES');
      console.log('❌ Manual intervention may be required');
    }
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error('\n💥 Test failed:', error);
    process.exit(1);
  });
