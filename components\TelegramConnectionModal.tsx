import React, { useState } from 'react';

interface TelegramConnectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConnect: (telegramId: string) => Promise<void>;
  loading?: boolean;
}

const TelegramConnectionModal: React.FC<TelegramConnectionModalProps> = ({
  isOpen,
  onClose,
  onConnect,
  loading = false
}) => {
  const [telegramId, setTelegramId] = useState('');
  const [error, setError] = useState('');
  const [isValidating, setIsValidating] = useState(false);

  if (!isOpen) return null;

  const validateTelegramId = (id: string): boolean => {
    // Telegram IDs are numeric and typically 8-10 digits long
    const telegramIdRegex = /^\d{8,12}$/;
    return telegramIdRegex.test(id);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!telegramId.trim()) {
      setError('Please enter your Telegram ID');
      return;
    }

    if (!validateTelegramId(telegramId.trim())) {
      setError('Please enter a valid Telegram ID (8-12 digits)');
      return;
    }

    setIsValidating(true);
    try {
      await onConnect(telegramId.trim());
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to connect to Telegram');
    } finally {
      setIsValidating(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, ''); // Only allow digits
    setTelegramId(value);
    if (error) setError(''); // Clear error when user starts typing
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      backdropFilter: 'blur(4px)'
    }}>
      <div style={{
        backgroundColor: '#1f2937',
        borderRadius: '16px',
        padding: '32px',
        maxWidth: '500px',
        width: '90%',
        maxHeight: '90vh',
        overflow: 'auto',
        border: '1px solid #374151',
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.5)'
      }}>
        {/* Header */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <div style={{
              width: '40px',
              height: '40px',
              backgroundColor: '#0088cc',
              borderRadius: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '20px'
            }}>
              📱
            </div>
            <h2 style={{ color: 'white', fontSize: '24px', fontWeight: 'bold', margin: 0 }}>
              Connect to Telegram
            </h2>
          </div>
          <button
            onClick={onClose}
            disabled={loading || isValidating}
            style={{
              background: 'none',
              border: 'none',
              color: '#9ca3af',
              fontSize: '24px',
              cursor: 'pointer',
              padding: '4px',
              borderRadius: '4px'
            }}
          >
            ×
          </button>
        </div>

        {/* Instructions */}
        <div style={{
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          border: '1px solid rgba(59, 130, 246, 0.3)',
          borderRadius: '12px',
          padding: '20px',
          marginBottom: '24px'
        }}>
          <h3 style={{ color: '#60a5fa', fontWeight: 'bold', margin: '0 0 12px 0', fontSize: '16px' }}>
            📋 How to find your Telegram ID:
          </h3>
          <ol style={{ color: '#d1d5db', fontSize: '14px', lineHeight: '1.6', margin: 0, paddingLeft: '20px' }}>
            <li>Open our Telegram bot: <strong>@AureusAllianceBot</strong></li>
            <li>Click the <strong>"Connect to Website"</strong> button</li>
            <li>Your numeric Telegram ID will be displayed</li>
            <li>Copy and paste it in the field below</li>
          </ol>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit}>
          <div style={{ marginBottom: '20px' }}>
            <label style={{ 
              display: 'block', 
              color: '#d1d5db', 
              marginBottom: '8px', 
              fontSize: '14px',
              fontWeight: '500'
            }}>
              Telegram ID (numeric only)
            </label>
            <input
              type="text"
              value={telegramId}
              onChange={handleInputChange}
              placeholder="e.g. 123456789"
              disabled={loading || isValidating}
              style={{
                width: '100%',
                padding: '12px 16px',
                backgroundColor: 'rgba(55, 65, 81, 0.5)',
                border: `2px solid ${error ? '#ef4444' : '#374151'}`,
                borderRadius: '8px',
                color: 'white',
                fontSize: '16px',
                outline: 'none',
                transition: 'border-color 0.2s'
              }}
              onFocus={(e) => {
                e.target.style.borderColor = '#3b82f6';
              }}
              onBlur={(e) => {
                e.target.style.borderColor = error ? '#ef4444' : '#374151';
              }}
            />
            {telegramId && validateTelegramId(telegramId) && (
              <p style={{ color: '#10b981', fontSize: '12px', marginTop: '4px', margin: '4px 0 0 0' }}>
                ✅ Valid Telegram ID format
              </p>
            )}
          </div>

          {/* Error Display */}
          {error && (
            <div style={{
              backgroundColor: 'rgba(239, 68, 68, 0.1)',
              border: '1px solid rgba(239, 68, 68, 0.3)',
              borderRadius: '8px',
              padding: '12px',
              marginBottom: '20px',
              color: '#f87171'
            }}>
              ❌ {error}
            </div>
          )}

          {/* Action Buttons */}
          <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
            <button
              type="button"
              onClick={onClose}
              disabled={loading || isValidating}
              style={{
                padding: '12px 24px',
                backgroundColor: 'transparent',
                border: '2px solid #374151',
                borderRadius: '8px',
                color: '#9ca3af',
                fontSize: '14px',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s'
              }}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!telegramId || !validateTelegramId(telegramId) || loading || isValidating}
              style={{
                padding: '12px 24px',
                backgroundColor: loading || isValidating ? '#6b7280' : '#0088cc',
                border: 'none',
                borderRadius: '8px',
                color: 'white',
                fontSize: '14px',
                fontWeight: '500',
                cursor: loading || isValidating ? 'not-allowed' : 'pointer',
                transition: 'all 0.2s',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}
            >
              {(loading || isValidating) && (
                <div style={{
                  width: '16px',
                  height: '16px',
                  border: '2px solid transparent',
                  borderTop: '2px solid white',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }} />
              )}
              {isValidating ? 'Connecting...' : 'Connect Account'}
            </button>
          </div>
        </form>

        {/* Warning */}
        <div style={{
          backgroundColor: 'rgba(245, 158, 11, 0.1)',
          border: '1px solid rgba(245, 158, 11, 0.3)',
          borderRadius: '8px',
          padding: '16px',
          marginTop: '20px'
        }}>
          <p style={{ color: '#fbbf24', fontSize: '14px', margin: 0, lineHeight: '1.5' }}>
            <strong>⚠️ Important:</strong> Make sure you enter the correct Telegram ID. 
            This will sync all your existing Telegram bot data with your web account.
          </p>
        </div>
      </div>

      {/* CSS Animation */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default TelegramConnectionModal;
