import React from 'react';
import GoldPriceChart from '../components/GoldPriceChart';

interface HomePageProps {
  onNavigate: (page: string) => void;
}

const HomePage: React.FC<HomePageProps> = ({ onNavigate }) => {
  // Key metrics from aureus.md
  const keyMetrics = [
    { value: "1.4M", label: "Total Shares Available" },
    { value: "$5.00", label: "Presale Price" },
    { value: "200K", label: "Presale Shares" },
    { value: "2026", label: "Operations Start" },
    { value: "$15-$50", label: "First Dividend April 2026" }
  ];

  return (
    <div className="page">
      {/* Hero Section */}
      <section className="hero">
        <div className="container">
          <div className="hero-content">
            <div className="content">
              <h1 className="hero-title">
                Real Gold • Real Shares • Real Ownership
              </h1>
              <p className="hero-subtitle">
                Aureus Alliance Holdings offers real gold equity through CIPC-registered shares,
                giving shareholders full legal ownership in a structured, professionally managed
                mining venture built on trust, security, and long-term value.
              </p>

              {/* Key Metrics */}
              <div className="metrics-grid">
                {keyMetrics.map((metric, index) => (
                  <div key={index} className="metric-card">
                    <div className="metric-value">{metric.value}</div>
                    <div className="metric-label">{metric.label}</div>
                  </div>
                ))}
              </div>

              {/* CTA Buttons */}
              <div className="cta-buttons">
                <button
                  className="btn btn-primary"
                  onClick={() => onNavigate('investment-phases')}
                >
                  View Share Phases
                </button>
                <button
                  className="btn btn-secondary"
                  onClick={() => onNavigate('calculator')}
                >
                  Calculate Dividends
                </button>
              </div>
            </div>

            <div className="logo" style={{ transition: 'none' }}>
              <img
                alt="Aureus Alliance Holdings"
                className="hero-logo"
                src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-m.png"
                style={{ transition: 'none' }}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Gold Price Chart Section */}
      <section className="gold-price-section">
        <div className="container">
          <div className="section-header">
            <h2>Live Gold Price Performance</h2>
            <p>Track the historical growth of gold prices and understand the value behind your shares</p>
          </div>

          <div className="chart-wrapper">
            <GoldPriceChart height={450} showControls={true} />
          </div>

          {/* Gold Price Insights */}
          <div className="gold-insights mt-6">
            <div className="insights-grid">
              <div className="insight-card">
                <div className="insight-icon">📈</div>
                <h3>Long-term Growth</h3>
                <p>Gold has shown consistent long-term appreciation, making it a reliable store of value for shareholders.</p>
              </div>

              <div className="insight-card">
                <div className="insight-icon">🏆</div>
                <h3>Current Market</h3>
                <p>At $120,000/kg, gold prices reflect strong market confidence and economic uncertainty hedging.</p>
              </div>

              <div className="insight-card">
                <div className="insight-icon">🔮</div>
                <h3>Future Outlook</h3>
                <p>Mining operations starting 2026 position shareholders to benefit from continued gold price appreciation.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Company Overview */}
      <section className="overview">
        <div className="container">
          <div className="section-header">
            <h2>About Aureus Alliance Holdings</h2>
            <p>Building gold-backed impact ventures across Africa</p>
          </div>

          <div className="overview-grid">
            <div className="overview-card">
              <div className="card-icon">🏗️</div>
              <h3>Operations Timeline</h3>
              <ul>
                <li><strong>January 2026:</strong> Operations begin with 2 wash plants</li>
                <li><strong>April 2026:</strong> First dividend payout ($15-$50 per share)</li>
                <li><strong>June 2026:</strong> Operations scale to 10 wash plants</li>
                <li><strong>2030:</strong> 200+ plants across 6,000 hectares</li>
                <li><strong>Multi-Country:</strong> Zimbabwe, Zambia, Ghana, Tanzania, South Africa</li>
              </ul>
            </div>

            <div className="overview-card">
              <div className="card-icon">💰</div>
              <h3>Financial Projections</h3>
              <ul>
                <li><strong>2026:</strong> 3.5 tons gold output, $145/share dividend</li>
                <li><strong>2030:</strong> 108+ tons gold output, $4,000/share dividend</li>
                <li><strong>Recovery Rate:</strong> 70% projected, targeting 95%</li>
                <li><strong>Gold Price:</strong> $120,000 per kg current</li>
              </ul>
            </div>

            <div className="overview-card">
              <div className="card-icon">🤝</div>
              <h3>Community Impact</h3>
              <ul>
                <li><strong>1,000,000+</strong> children fed through programs</li>
                <li><strong>30,000+</strong> school scholarships funded</li>
                <li><strong>200+</strong> water boreholes installed</li>
                <li><strong>50+</strong> rural schools built</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Trust Indicators */}
      <section className="trust-section">
        <div className="container">
          <div className="section-header">
            <h2>Why Choose Aureus Alliance Holdings</h2>
            <p>Established credibility and proven commitment</p>
          </div>

          <div className="trust-grid">
            <div className="trust-item">
              <div className="trust-icon">🏛️</div>
              <div className="trust-content">
                <h4>CIPC Registered</h4>
                <p>Legally registered company with full regulatory compliance</p>
              </div>
            </div>
            <div className="trust-item">
              <div className="trust-icon">🔒</div>
              <div className="trust-content">
                <h4>Blockchain Secured</h4>
                <p>NFT share certificates for transparent ownership verification</p>
              </div>
            </div>
            <div className="trust-item">
              <div className="trust-icon">🌍</div>
              <div className="trust-content">
                <h4>6+ Years Experience</h4>
                <p>Proven track record in Zimbabwe gold mining operations</p>
              </div>
            </div>
            <div className="trust-item">
              <div className="trust-icon">♻️</div>
              <div className="trust-content">
                <h4>Eco-Friendly Operations</h4>
                <p>Renewable energy powered sustainable mining practices</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery & Video Section */}
      <section className="gallery-section">
        <div className="container">
          <div className="section-header">
            <h2>See Our Operations in Action</h2>
            <p>Visual proof of our commitment to sustainable gold mining across Africa</p>
          </div>

          <div className="media-showcase">
            {/* Featured Video Section */}
            <div className="video-showcase">
              <div className="video-description">
                <div className="video-badge">
                  <span className="badge-icon">🏭</span>
                  <span className="badge-text">Wash Plant Technology</span>
                </div>
                <h3>Advanced Gold Processing Operations</h3>
                <div className="washplant-info">
                  <p className="intro-text">
                    Our state-of-the-art wash plants represent the pinnacle of sustainable gold mining technology,
                    combining efficiency with environmental responsibility.
                  </p>

                  <div className="process-details">
                    <h4>What is a Wash Plant?</h4>
                    <p>
                      A wash plant is a sophisticated gold processing facility that uses water and gravity separation
                      to extract gold from alluvial deposits. Our eco-friendly systems minimize environmental impact
                      while maximizing gold recovery rates.
                    </p>

                    <div className="efficiency-stats">
                      <div className="stat-item">
                        <span className="stat-number">70-95%</span>
                        <span className="stat-label">Recovery Rate</span>
                      </div>
                      <div className="stat-item">
                        <span className="stat-number">100%</span>
                        <span className="stat-label">Renewable Energy</span>
                      </div>
                      <div className="stat-item">
                        <span className="stat-number">90%</span>
                        <span className="stat-label">Water Recycled</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Video Player */}
              <div style={{ textAlign: 'center', marginBottom: 'var(--space-xl)' }}>
                <div style={{
                  position: 'relative',
                  paddingBottom: '56.25%',
                  height: 0,
                  overflow: 'hidden',
                  maxWidth: '800px',
                  margin: '0 auto',
                  borderRadius: '12px',
                  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)'
                }}>
                  <video
                    src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/yagden.mp4"
                    title="200 TPH WashPlant Yagden Engineering Zimbabwe"
                    controls
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '100%',
                      border: 'none',
                      borderRadius: '12px'
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Video Title and How It Works Section - Outside the grid */}
            <div style={{ textAlign: 'center', marginTop: 'var(--space-4xl)', marginBottom: 'var(--space-xl)' }}>
              <h2 style={{
                color: 'var(--gold)',
                fontSize: 'var(--font-size-2xl)',
                fontWeight: 'var(--font-weight-bold)',
                marginBottom: 'var(--space-2xl)'
              }}>
                200 TPH WashPlant Yagden Engineering Zimbabwe
              </h2>

              <div style={{ marginBottom: 'var(--space-xl)' }}>
                <h4 style={{ color: 'var(--gold)', marginBottom: 'var(--space-md)' }}>How It Works:</h4>
                <ul className="process-list" style={{ textAlign: 'left', maxWidth: '1200px', margin: '0 auto' }}>
                  <li><strong>Material Feed:</strong> Raw gold-bearing material is fed into the system</li>
                  <li><strong>Water Separation:</strong> High-pressure water breaks down clay and sediment</li>
                  <li><strong>Gravity Recovery:</strong> Gold settles due to its higher density</li>
                  <li><strong>Clean Collection:</strong> Pure gold is collected while waste is filtered</li>
                </ul>
              </div>
            </div>

            {/* Operations & Impact Section */}
            <div style={{ marginTop: 'var(--space-6xl)', marginBottom: 'var(--space-4xl)' }}>
              <div className="section-header" style={{ textAlign: 'center', marginBottom: 'var(--space-4xl)' }}>
                <h3 style={{ color: 'var(--gold)', fontSize: 'var(--font-size-2xl)', marginBottom: 'var(--space-md)' }}>
                  Our Operations & Community Impact
                </h3>
                <p style={{ color: 'var(--text-muted)', fontSize: 'var(--font-size-lg)', maxWidth: '600px', margin: '0 auto' }}>
                  Sustainable mining operations that benefit communities and restore the environment
                </p>
              </div>

              {/* Gallery Grid */}
              <div className="operations-gallery">
              <div className="gallery-card">
                <div className="gallery-icon">🏭</div>
                <div className="gallery-content">
                  <h4>Wash Plant Operations</h4>
                  <p>Advanced processing facilities utilizing renewable energy and water recycling systems for maximum efficiency.</p>
                </div>
              </div>

              <div className="gallery-card">
                <div className="gallery-icon">🌱</div>
                <div className="gallery-content">
                  <h4>Land Rehabilitation</h4>
                  <p>Comprehensive restoration programs returning mined areas to productive agricultural and natural habitats.</p>
                </div>
              </div>

              <div className="gallery-card">
                <div className="gallery-icon">👥</div>
                <div className="gallery-content">
                  <h4>Community Engagement</h4>
                  <p>Active partnerships with local communities ensuring mutual benefit and sustainable development initiatives.</p>
                </div>
              </div>

              <div className="gallery-card">
                <div className="gallery-icon">🏥</div>
                <div className="gallery-content">
                  <h4>Healthcare Initiatives</h4>
                  <p>Mobile clinics and health programs providing essential medical services to remote mining communities.</p>
                </div>
              </div>

              <div className="gallery-card">
                <div className="gallery-icon">🎓</div>
                <div className="gallery-content">
                  <h4>Education Programs</h4>
                  <p>Scholarship funds and school construction projects supporting over 30,000 students across operational regions.</p>
                </div>
              </div>

              <div className="gallery-card">
                <div className="gallery-icon">💧</div>
                <div className="gallery-content">
                  <h4>Clean Water Projects</h4>
                  <p>Borehole installations and water purification systems bringing clean water access to rural communities.</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="gallery-actions">
            <button
              className="btn btn-primary"
              onClick={() => onNavigate('mine-production')}
            >
              Learn About Our Technology
            </button>
            <button
              className="btn btn-secondary"
              onClick={() => onNavigate('gallery')}
            >
              View Full Gallery
            </button>
          </div>
        </div>
      </section>

      {/* Quick Links */}
      <section className="quick-links">
        <div className="container">
          <div className="section-header">
            <h2>Explore Our Offering</h2>
            <p>Comprehensive information about our mining venture</p>
          </div>

          <div className="links-grid">
            <div className="link-card">
              <div className="link-icon">📊</div>
              <div className="link-content">
                <h3>Share Phases</h3>
                <p>Complete breakdown of all 20 phases from $5 to $1,000 per share</p>
                <button
                  className="btn-link-gold"
                  onClick={() => onNavigate('investment-phases')}
                >
                  More Info
                </button>
              </div>
            </div>

            <div className="link-card">
              <div className="link-icon">🚀</div>
              <div className="link-content">
                <h3>Expansion Plan</h3>
                <p>5-year growth strategy scaling to 200+ plants across Africa</p>
                <button
                  className="btn-link-gold"
                  onClick={() => onNavigate('expansion-plan')}
                >
                  More Info
                </button>
              </div>
            </div>

            <div className="link-card">
              <div className="link-icon">🌍</div>
              <div className="link-content">
                <h3>Community Impact</h3>
                <p>CSR initiatives feeding 1M+ children and building infrastructure</p>
                <button
                  className="btn-link-gold"
                  onClick={() => onNavigate('community-impact')}
                >
                  More Info
                </button>
              </div>
            </div>

            <div className="link-card">
              <div className="link-icon">🧮</div>
              <div className="link-content">
                <h3>Dividend Calculator</h3>
                <p>Calculate your potential dividends on profits from shareholding</p>
                <button
                  className="btn-link-gold"
                  onClick={() => onNavigate('calculator')}
                >
                  More Info
                </button>
              </div>
            </div>

            <div className="link-card">
              <div className="link-icon">📈</div>
              <div className="link-content">
                <h3>Financial Data</h3>
                <p>Detailed projections, yield tables, and production forecasts</p>
                <button
                  className="btn-link-gold"
                  onClick={() => onNavigate('financial-data')}
                >
                  More Info
                </button>
              </div>
            </div>

            <div className="link-card">
              <div className="link-icon">🏢</div>
              <div className="link-content">
                <h3>Company Information</h3>
                <p>Corporate details, team, history, and operational background</p>
                <button
                  className="btn-link-gold"
                  onClick={() => onNavigate('company-info')}
                >
                  More Info
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

    </div>
  );
};

export default HomePage;
