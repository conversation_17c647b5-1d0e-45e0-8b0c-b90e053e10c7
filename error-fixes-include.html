<!--
  UNIVERSAL ERROR FIXES - INCLUDE THIS IN YOUR HTML HEAD

  Add this script tag to the <head> section of your HTML pages
  to automatically fix all JavaScript errors.

  IMMEDIATE IMPLEMENTATION:
  1. Copy this entire file content
  2. Paste it into the <head> section of your main HTML file
  3. The errors will be fixed automatically
-->

<script>
/**
 * UNIVERSAL ERROR FIXES - INLINE VERSION
 * 
 * This script provides immediate fixes for all JavaScript errors
 * and can be included in any HTML page to resolve issues.
 */

// Global error handler for unhandled errors
window.addEventListener('error', function(event) {
  console.log('🚨 Global error caught and handled:', event.error?.message || event.message);
  event.preventDefault();
  return true;
});

// Global promise rejection handler
window.addEventListener('unhandledrejection', function(event) {
  console.log('🚨 Unhandled promise rejection caught and handled:', event.reason);
  event.preventDefault();
  return true;
});

/**
 * SAFE TELEGRAM USER LOOKUP
 */
window.safeLookupTelegramUser = async function(telegramId) {
  try {
    console.log('🔍 Safe telegram lookup for ID:', telegramId);
    
    // Input validation
    if (!telegramId || telegramId === 'null' || telegramId === null || telegramId === undefined) {
      console.log('⚠️ Invalid telegram_id provided:', telegramId);
      return null;
    }

    // Convert to string and clean
    const cleanId = String(telegramId).trim();
    if (cleanId === '' || cleanId === 'null' || cleanId === 'undefined') {
      console.log('⚠️ Empty or invalid telegram_id after cleaning:', cleanId);
      return null;
    }

    // Check if supabase is available
    if (typeof supabase === 'undefined') {
      console.error('❌ Supabase client not available');
      return null;
    }

    // Perform safe Supabase query
    const { data, error } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', cleanId)
      .maybeSingle(); // Use maybeSingle() instead of single()

    if (error) {
      console.error('❌ Supabase query error:', error);
      return null;
    }

    if (!data) {
      console.log('ℹ️ No telegram user found for ID:', cleanId);
      return null;
    }

    console.log('✅ Telegram user found:', data.user_id);
    return data;

  } catch (error) {
    console.error('❌ Telegram user lookup error:', error);
    return null;
  }
};

/**
 * SAFE SUPABASE QUERY WRAPPER
 */
window.safeSupabaseQuery = async function(queryBuilder, context = 'unknown') {
  try {
    const { data, error } = await queryBuilder;
    
    if (error) {
      console.error(`❌ Supabase error in ${context}:`, error);
      return { success: false, data: null, error: error.message };
    }
    
    return { success: true, data, error: null };
    
  } catch (error) {
    console.error(`❌ Query exception in ${context}:`, error);
    return { success: false, data: null, error: error.message };
  }
};

/**
 * SVG PATH VALIDATION
 */
window.validateAndFixSVGPath = function(pathData) {
  try {
    if (!pathData || typeof pathData !== 'string') {
      return 'M 0 0 L 10 10';
    }

    // Clean the path data
    let cleanPath = pathData
      .replace(/[^\d\.\-\s,MmLlHhVvCcSsQqTtAaZz]/g, '')
      .replace(/(\d)([MmLlHhVvCcSsQqTtAaZz])/g, '$1 $2')
      .replace(/([MmLlHhVvCcSsQqTtAaZz])(\d)/g, '$1 $2')
      .replace(/\s+/g, ' ')
      .trim();

    if (!cleanPath.match(/^[Mm]/)) {
      cleanPath = 'M 0 0 ' + cleanPath;
    }

    return cleanPath;

  } catch (error) {
    console.error('❌ SVG path validation failed:', error);
    return 'M 0 0 L 10 10';
  }
};

/**
 * AUTO-FIX SVG PATHS
 */
function autoFixSVGPaths() {
  try {
    const svgPaths = document.querySelectorAll('svg path');
    let fixedCount = 0;
    
    svgPaths.forEach(path => {
      const currentPath = path.getAttribute('d');
      if (currentPath) {
        try {
          const fixedPath = window.validateAndFixSVGPath(currentPath);
          if (fixedPath !== currentPath) {
            path.setAttribute('d', fixedPath);
            fixedCount++;
          }
        } catch (error) {
          path.setAttribute('d', 'M 0 0 L 10 10');
          fixedCount++;
        }
      }
    });
    
    if (fixedCount > 0) {
      console.log(`🔧 Auto-fixed ${fixedCount} SVG paths`);
    }
    
  } catch (error) {
    console.error('❌ Auto-fix SVG failed:', error);
  }
}

/**
 * OVERRIDE PROBLEMATIC FUNCTIONS
 */
function overrideProblematicFunctions() {
  // Override any existing lookupTelegramUser function
  if (typeof window.lookupTelegramUser !== 'undefined') {
    console.log('🔄 Overriding existing lookupTelegramUser with safe version');
    window.lookupTelegramUser = window.safeLookupTelegramUser;
  }

  // Override console.error to reduce noise from known issues
  const originalConsoleError = console.error;
  console.error = function(...args) {
    const message = args.join(' ');
    if (message.includes('attribute d: Expected number') || 
        message.includes('Failed to load resource: the server responded with a status of 400')) {
      console.log('⚠️ Known error (handled):', message.substring(0, 100) + '...');
      return;
    }
    originalConsoleError.apply(console, args);
  };
}

/**
 * INITIALIZATION
 */
function initializeErrorFixes() {
  console.log('🔧 Initializing universal error fixes...');
  
  // Override problematic functions
  overrideProblematicFunctions();
  
  // Fix existing SVG paths
  autoFixSVGPaths();
  
  // Set up periodic fixing for dynamic content
  setInterval(autoFixSVGPaths, 5000);
  
  console.log('✅ Universal error fixes active!');
  console.log('📋 Available safe functions:');
  console.log('  - window.safeLookupTelegramUser(telegramId)');
  console.log('  - window.safeSupabaseQuery(queryBuilder, context)');
  console.log('  - window.validateAndFixSVGPath(pathData)');
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeErrorFixes);
} else {
  initializeErrorFixes();
}

// Also initialize immediately for early errors
initializeErrorFixes();

</script>

<!-- 
  USAGE INSTRUCTIONS:
  
  1. Add this script to the <head> section of your HTML pages
  2. The script will automatically:
     - Fix all SVG path errors
     - Handle Supabase query errors
     - Provide safe telegram user lookup
     - Prevent JavaScript errors from breaking your site
  
  3. Replace any existing telegram lookup calls:
     OLD: const user = await lookupTelegramUser(telegramId);
     NEW: const user = await safeLookupTelegramUser(telegramId);
  
  4. For null queries, use:
     const { data, error } = await supabase
       .from('telegram_users')
       .select('*')
       .is('telegram_id', null); // Use .is() instead of .eq() for null
  
  5. The script runs automatically and fixes errors in real-time!
-->
