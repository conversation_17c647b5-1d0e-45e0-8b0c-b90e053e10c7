import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Simple hash function for password (same as in lib/supabase.ts)
async function hashPassword(password) {
  const encoder = new TextEncoder();
  const data = encoder.encode(password + 'aureus_salt_2024');
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

async function fixTestUserPassword() {
  try {
    console.log('🔧 Fixing test user password hash...');
    
    const testPassword = 'TestPassword123';
    const correctHash = await hashPassword(testPassword);
    
    console.log('New hash:', correctHash);
    
    const { data, error } = await supabase
      .from('users')
      .update({
        password_hash: correctHash,
        updated_at: new Date().toISOString()
      })
      .eq('email', '<EMAIL>')
      .select();
    
    if (error) {
      console.error('❌ Error updating password:', error);
      return;
    }
    
    console.log('✅ Test user password hash updated successfully');
    
    // Verify the fix
    const { data: user } = await supabase
      .from('users')
      .select('password_hash')
      .eq('email', '<EMAIL>')
      .single();
    
    console.log('Verification - stored hash:', user.password_hash);
    console.log('Verification - computed hash:', correctHash);
    console.log('Verification - match:', user.password_hash === correctHash);
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

fixTestUserPassword();
