/**
 * CORRECTED FINANCIAL AUDIT SYSTEM TEST
 * 
 * Test script to verify the corrected Financial Audit System
 * now properly includes commission conversions.
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

const supabaseUrl = process.env.VITE_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

/**
 * Test the corrected financial audit system with commission conversions
 */
async function testCorrectedAuditSystem() {
  console.log('🔍 Testing CORRECTED Financial Audit System (with Commission Conversions)...\n')

  const testUserId = 144

  try {
    // Test 1: User profile
    console.log('📋 TEST 1: User Profile')
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, username, email, full_name')
      .eq('id', testUserId)
      .single()

    if (userError) throw new Error(`User retrieval failed: ${userError.message}`)
    console.log(`✅ User: ${user.full_name} (${user.username})`)

    // Test 2: Direct share purchases
    console.log('\n📋 TEST 2: Direct Share Purchases')
    const { data: sharePurchases, error: sharesError } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .eq('user_id', testUserId)
      .eq('status', 'active')

    if (sharesError) throw new Error(`Share purchases failed: ${sharesError.message}`)
    
    const totalDirectShares = sharePurchases.reduce((sum, p) => sum + p.shares_purchased, 0)
    console.log(`✅ Direct purchases: ${sharePurchases.length} transactions`)
    console.log(`   Total direct shares: ${totalDirectShares}`)

    // Test 3: Commission transactions
    console.log('\n📋 TEST 3: Commission Transactions')
    const { data: commissionTransactions, error: commissionsError } = await supabase
      .from('commission_transactions')
      .select('*')
      .eq('referrer_id', testUserId)
      .eq('status', 'approved')

    if (commissionsError) throw new Error(`Commission transactions failed: ${commissionsError.message}`)

    const totalShareCommission = commissionTransactions.reduce((sum, t) => sum + parseFloat(t.share_commission), 0)
    const totalUsdtCommission = commissionTransactions.reduce((sum, t) => sum + parseFloat(t.usdt_commission), 0)
    
    console.log(`✅ Commission transactions: ${commissionTransactions.length} approved`)
    console.log(`   Total share commission: ${totalShareCommission} shares`)
    console.log(`   Total USDT commission: $${totalUsdtCommission}`)

    // Test 4: Commission conversions (THE MISSING PIECE!)
    console.log('\n📋 TEST 4: Commission Conversions (CORRECTED)')
    const { data: commissionConversions, error: conversionsError } = await supabase
      .from('commission_conversions')
      .select('*')
      .eq('user_id', testUserId)
      .eq('status', 'approved')

    if (conversionsError) throw new Error(`Commission conversions failed: ${conversionsError.message}`)

    const totalConvertedShares = commissionConversions.reduce((sum, c) => sum + c.shares_requested, 0)
    const totalUsdtConverted = commissionConversions.reduce((sum, c) => sum + parseFloat(c.usdt_amount), 0)

    console.log(`✅ Commission conversions: ${commissionConversions.length} approved`)
    console.log(`   Total converted shares: ${totalConvertedShares} shares`)
    console.log(`   Total USDT converted: $${totalUsdtConverted}`)

    // Show conversion details
    commissionConversions.forEach((conversion, index) => {
      console.log(`   Conversion ${index + 1}: $${conversion.usdt_amount} → ${conversion.shares_requested} shares (${new Date(conversion.created_at).toLocaleDateString()})`)
    })

    // Test 5: Corrected total calculation
    console.log('\n📋 TEST 5: CORRECTED Total Share Calculation')
    const totalSharesOwned = totalDirectShares + totalShareCommission + totalConvertedShares
    
    console.log(`✅ CORRECTED share ownership breakdown:`)
    console.log(`   Direct purchases: ${totalDirectShares} shares`)
    console.log(`   Commission shares: ${totalShareCommission} shares`)
    console.log(`   Converted shares: ${totalConvertedShares} shares`)
    console.log(`   TOTAL SHARES OWNED: ${totalSharesOwned} shares`)

    // Test 6: Portfolio valuation
    console.log('\n📋 TEST 6: CORRECTED Portfolio Valuation')
    const { data: currentPhase, error: phaseError } = await supabase
      .from('investment_phases')
      .select('*')
      .eq('is_active', true)
      .single()

    if (phaseError) throw new Error(`Investment phase failed: ${phaseError.message}`)

    const currentSharePrice = parseFloat(currentPhase.price_per_share)
    const portfolioValue = totalSharesOwned * currentSharePrice

    console.log(`✅ CORRECTED portfolio valuation:`)
    console.log(`   Current share price: $${currentSharePrice}`)
    console.log(`   Total shares: ${totalSharesOwned}`)
    console.log(`   Portfolio value: $${portfolioValue.toLocaleString()}`)

    // Test 7: USDT balance verification
    console.log('\n📋 TEST 7: USDT Balance Verification')
    const { data: commissionBalance, error: balanceError } = await supabase
      .from('commission_balances')
      .select('*')
      .eq('user_id', testUserId)
      .single()

    if (balanceError) throw new Error(`Commission balance failed: ${balanceError.message}`)

    console.log(`✅ USDT balance verification:`)
    console.log(`   Total USDT earned: $${commissionBalance.total_earned_usdt}`)
    console.log(`   USDT converted to shares: $${totalUsdtConverted}`)
    console.log(`   Current USDT balance: $${commissionBalance.usdt_balance}`)
    
    const expectedBalance = parseFloat(commissionBalance.total_earned_usdt) - totalUsdtConverted
    const actualBalance = parseFloat(commissionBalance.usdt_balance)
    const balanceMatch = Math.abs(expectedBalance - actualBalance) < 0.01

    console.log(`   Expected remaining: $${expectedBalance.toFixed(2)}`)
    console.log(`   Actual remaining: $${actualBalance.toFixed(2)}`)
    console.log(`   Balance verification: ${balanceMatch ? '✅ CORRECT' : '❌ MISMATCH'}`)

    // Test 8: Compare with original audit
    console.log('\n📋 TEST 8: Comparison with Original Audit')
    const originalTotal = 119.95 // From original audit
    const correctedTotal = totalSharesOwned
    const difference = correctedTotal - originalTotal

    console.log(`✅ Audit comparison:`)
    console.log(`   Original audit total: ${originalTotal} shares`)
    console.log(`   Corrected audit total: ${correctedTotal} shares`)
    console.log(`   Difference: +${difference} shares`)
    console.log(`   Missing component: Commission conversions (${totalConvertedShares} shares)`)

    // Final summary
    console.log('\n🎯 CORRECTED FINANCIAL AUDIT SUMMARY')
    console.log('=' .repeat(60))
    console.log(`User: ${user.full_name} (ID: ${user.id})`)
    console.log(`Direct Shares: ${totalDirectShares} shares`)
    console.log(`Commission Shares: ${totalShareCommission} shares`)
    console.log(`Converted Shares: ${totalConvertedShares} shares (from $${totalUsdtConverted} USDT)`)
    console.log(`TOTAL SHARES: ${totalSharesOwned} shares`)
    console.log(`Portfolio Value: $${portfolioValue.toLocaleString()}`)
    console.log(`Current USDT Balance: $${commissionBalance.usdt_balance}`)
    console.log('=' .repeat(60))
    console.log('✅ CORRECTED Financial Audit System Test COMPLETED SUCCESSFULLY')
    console.log('🔧 Commission conversions now properly included in audit calculations!')

  } catch (error) {
    console.error('❌ Corrected Financial Audit System Test FAILED:', error.message)
    process.exit(1)
  }
}

// Test User 145 (Niall) as well
async function testUser145() {
  console.log('\n\n🔍 Testing User 145 (Niall Andrews) for Similar Issues...\n')

  const testUserId = 145

  try {
    // Get user info
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, username, email, full_name')
      .eq('id', testUserId)
      .single()

    if (userError) throw new Error(`User retrieval failed: ${userError.message}`)
    console.log(`✅ User: ${user.full_name} (${user.username})`)

    // Check conversions
    const { data: conversions, error: conversionsError } = await supabase
      .from('commission_conversions')
      .select('*')
      .eq('user_id', testUserId)
      .eq('status', 'approved')

    if (conversionsError) throw new Error(`Conversions failed: ${conversionsError.message}`)

    const totalConvertedShares = conversions.reduce((sum, c) => sum + c.shares_requested, 0)
    const totalUsdtConverted = conversions.reduce((sum, c) => sum + parseFloat(c.usdt_amount), 0)

    console.log(`✅ Niall's commission conversions:`)
    console.log(`   Total conversions: ${conversions.length}`)
    console.log(`   Total converted shares: ${totalConvertedShares} shares`)
    console.log(`   Total USDT converted: $${totalUsdtConverted}`)

    conversions.forEach((conversion, index) => {
      console.log(`   Conversion ${index + 1}: $${conversion.usdt_amount} → ${conversion.shares_requested} shares (${new Date(conversion.created_at).toLocaleDateString()})`)
    })

    if (conversions.length > 0) {
      console.log('⚠️  Niall also has commission conversions that need to be included in his audit!')
    }

  } catch (error) {
    console.error('❌ User 145 test failed:', error.message)
  }
}

// Run both tests
async function runAllTests() {
  await testCorrectedAuditSystem()
  await testUser145()
}

runAllTests()
