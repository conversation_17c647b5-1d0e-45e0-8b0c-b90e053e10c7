#!/usr/bin/env node

/**
 * Fix Missing Commission
 * 
 * This script calculates and distributes the missing commission
 * for <PERSON>'s recent $5 share purchase.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ Missing SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixMissingCommission() {
  try {
    console.log('🔧 Fixing Missing Commission\n');
    
    // Find <PERSON>'s user ID
    const { data: jeanPierre, error: jpError } = await supabase
      .from('users')
      .select('*')
      .eq('username', 'TTTFOUNDER')
      .single();
    
    if (jpError || !jeanPierre) {
      console.error('❌ <PERSON> (TTTFOUNDER) not found:', jpError);
      return;
    }
    
    console.log(`👤 <PERSON> Pierre ID: ${jeanPierre.id}`);
    
    // Find the recent $5 share purchase that needs commission
    const { data: recentPurchase, error: purchaseError } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .eq('user_id', jeanPierre.id)
      .eq('total_amount', 5)
      .eq('shares_purchased', 1)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();
    
    if (purchaseError) {
      console.error('❌ Recent $5 purchase not found:', purchaseError);
      return;
    }
    
    console.log('📈 Found Share Purchase:');
    console.log(`   Purchase ID: ${recentPurchase.id}`);
    console.log(`   Amount: $${recentPurchase.total_amount}`);
    console.log(`   Shares: ${recentPurchase.shares_purchased}`);
    console.log(`   Date: ${recentPurchase.created_at}`);
    
    // Check if commission already exists for this purchase
    const { data: existingCommission, error: commError } = await supabase
      .from('commission_transactions')
      .select('*')
      .eq('referred_id', jeanPierre.id)
      .eq('share_purchase_amount', 5)
      .gte('payment_date', recentPurchase.created_at);
    
    if (commError) {
      console.error('❌ Error checking existing commission:', commError);
      return;
    }
    
    if (existingCommission.length > 0) {
      console.log('✅ Commission already exists for this purchase');
      console.log(`   Commission ID: ${existingCommission[0].id}`);
      console.log(`   USDT: $${existingCommission[0].usdt_commission}`);
      console.log(`   Shares: ${existingCommission[0].share_commission}`);
      return;
    }
    
    // Find Jean Pierre's sponsor (should be himself as TTTFOUNDER)
    const { data: referral, error: refError } = await supabase
      .from('referrals')
      .select(`
        *,
        referrer:users!referrer_id(id, username, full_name)
      `)
      .eq('referred_id', jeanPierre.id)
      .eq('status', 'active')
      .single();
    
    if (refError) {
      console.error('❌ No active referral found:', refError);
      return;
    }
    
    console.log(`\n🔗 Found Sponsor: ${referral.referrer.full_name || referral.referrer.username} (ID: ${referral.referrer_id})`);
    
    // Calculate commission (15% USDT + 15% shares)
    const purchaseAmount = recentPurchase.total_amount;
    const sharesPurchased = recentPurchase.shares_purchased;
    const usdtCommission = purchaseAmount * 0.15;
    const shareCommission = sharesPurchased * 0.15;
    
    console.log(`\n💰 Calculating Commission:`);
    console.log(`   Purchase Amount: $${purchaseAmount}`);
    console.log(`   Shares Purchased: ${sharesPurchased}`);
    console.log(`   USDT Commission (15%): $${usdtCommission}`);
    console.log(`   Share Commission (15%): ${shareCommission}`);
    
    // Get current active phase
    const { data: currentPhase, error: phaseError } = await supabase
      .from('investment_phases')
      .select('id, phase_name')
      .eq('is_active', true)
      .single();
    
    if (phaseError) {
      console.error('❌ No active phase found:', phaseError);
      return;
    }
    
    console.log(`   Phase: ${currentPhase.phase_name} (ID: ${currentPhase.id})`);
    
    // Create commission transaction
    const { data: commissionTransaction, error: transactionError } = await supabase
      .from('commission_transactions')
      .insert({
        referrer_id: referral.referrer_id,
        referred_id: jeanPierre.id,
        commission_rate: 15.00,
        share_purchase_amount: purchaseAmount,
        usdt_commission: usdtCommission,
        share_commission: shareCommission,
        phase_id: currentPhase.id,
        status: 'approved',
        payment_date: new Date().toISOString()
      })
      .select()
      .single();
    
    if (transactionError) {
      console.error('❌ Error creating commission transaction:', transactionError);
      return;
    }
    
    console.log(`\n✅ Commission Transaction Created:`);
    console.log(`   Transaction ID: ${commissionTransaction.id}`);
    
    // Update sponsor's commission balance
    const { error: balanceError } = await supabase
      .from('commission_balances')
      .upsert({
        user_id: referral.referrer_id,
        usdt_balance: supabase.raw(`COALESCE(usdt_balance, 0) + ${usdtCommission}`),
        share_balance: supabase.raw(`COALESCE(share_balance, 0) + ${shareCommission}`),
        total_earned_usdt: supabase.raw(`COALESCE(total_earned_usdt, 0) + ${usdtCommission}`),
        total_earned_shares: supabase.raw(`COALESCE(total_earned_shares, 0) + ${shareCommission}`),
        last_updated: new Date().toISOString()
      }, {
        onConflict: 'user_id'
      });
    
    if (balanceError) {
      console.error('❌ Error updating commission balance:', balanceError);
      return;
    }
    
    console.log(`✅ Commission Balance Updated for ${referral.referrer.full_name || referral.referrer.username}`);
    
    // Verify the update
    const { data: updatedBalance, error: verifyError } = await supabase
      .from('commission_balances')
      .select('*')
      .eq('user_id', referral.referrer_id)
      .single();
    
    if (verifyError) {
      console.error('❌ Error verifying balance update:', verifyError);
    } else {
      console.log(`\n📊 Updated Commission Balance:`);
      console.log(`   USDT Balance: $${updatedBalance.usdt_balance}`);
      console.log(`   Share Balance: ${updatedBalance.share_balance}`);
      console.log(`   Total Earned USDT: $${updatedBalance.total_earned_usdt}`);
      console.log(`   Total Earned Shares: ${updatedBalance.total_earned_shares}`);
    }
    
    console.log(`\n🎉 Commission successfully distributed!`);
    
  } catch (error) {
    console.error('❌ Fix failed:', error);
  }
}

fixMissingCommission();
