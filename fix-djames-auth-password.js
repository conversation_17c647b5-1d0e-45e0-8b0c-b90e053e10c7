import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

console.log('🔧 FIXING DJAMES AUTH PASSWORD');
console.log('==============================');

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

const fixAuthPassword = async () => {
  try {
    const email = '<EMAIL>';
    const password = 'Gunst0n5o0!@#';
    
    console.log(`📧 Email: ${email}`);
    console.log(`🔑 Password: ${password}`);
    
    // First, get the auth user ID
    const { data: { users }, error: listError } = await supabase.auth.admin.listUsers();
    
    if (listError) {
      console.log('❌ Error listing users:', listError);
      return;
    }
    
    const authUser = users.find(user => user.email === email);
    
    if (!authUser) {
      console.log('❌ Auth user not found');
      return;
    }
    
    console.log(`✅ Found auth user ID: ${authUser.id}`);
    
    // Update the auth user's password
    console.log('🔧 Updating auth user password...');
    
    const { data: updateData, error: updateError } = await supabase.auth.admin.updateUserById(
      authUser.id,
      {
        password: password
      }
    );
    
    if (updateError) {
      console.log('❌ Error updating password:', updateError);
      return;
    }
    
    console.log('✅ Password updated successfully!');
    console.log(`   User ID: ${updateData.user.id}`);
    console.log(`   Email: ${updateData.user.email}`);
    
    // Test the login immediately
    console.log('\n🧪 Testing login with updated password...');
    
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    
    if (loginError) {
      console.log('❌ Login still failing:', loginError.message);
    } else {
      console.log('🎉 LOGIN SUCCESSFUL!');
      console.log(`   User ID: ${loginData.user.id}`);
      console.log(`   Email: ${loginData.user.email}`);
      console.log(`   Session: ${loginData.session ? 'Created' : 'None'}`);
    }
    
    // Sign out to clean up
    await supabase.auth.signOut();
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
};

fixAuthPassword();
