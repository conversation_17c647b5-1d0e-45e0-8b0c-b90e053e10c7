// SVG-based Certificate Generator for Aureus Alliance Holdings
// Generates certificates using the official SVG template with KYC data integration

import { supabase } from './supabase';

export interface CertificateData {
  certificateNumber: string; // Keep for backward compatibility
  certNo: string; // New sequential certificate number (e.g., "0-000-001")
  refNo: string; // New cumulative share range (e.g., "1-700")
  userName: string;
  userIdNumber: string;
  userAddress: {
    line1: string;
    line2: string;
    city: string;
    country: string;
    postalCode: string;
  };
  sunId: string;
  sharesCount: number;
  refNumber: string; // Keep for backward compatibility
  issueDate: string;
  purchaseDate: string;
}

export interface CertificateTemplateElement {
  id: string;
  element_id: string;
  element_type: 'text' | 'image' | 'line' | 'rectangle' | 'watermark';
  template_version: string;
  x_position: number;
  y_position: number;
  width?: number;
  height?: number;
  font_family?: string;
  font_size?: number;
  font_weight?: string;
  font_color?: string;
  text_anchor?: string;
  static_content?: string;
  data_field?: string;
  is_dynamic: boolean;
  opacity?: number;
  rotation?: number;
  z_index?: number;
  description?: string;
  is_active: boolean;
}

export interface KYCData {
  full_name: string;
  id_number?: string;
  passport_number?: string;
  street_address?: string;
  city?: string;
  country?: string;
  postal_code?: string;
  email_address: string;
}

export class SVGCertificateGenerator {
  private svgTemplate: string = '';

  constructor() {
    this.loadTemplate();
  }

  /**
   * Load the SVG template from Supabase storage
   */
  async loadTemplate(): Promise<void> {
    try {
      const response = await fetch('https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/Aureus_Template.svg');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const svgContent = await response.text();

      // Validate SVG content
      if (!svgContent || !svgContent.includes('<svg') || !svgContent.includes('</svg>')) {
        throw new Error('Invalid SVG content received');
      }

      // Clean up any malformed path data
      const cleanedSvg = svgContent.replace(/d="[^"]*tc0\.2[^"]*"/g, 'd="M 0 0 L 100 0 L 100 100 L 0 100 Z"');

      this.svgTemplate = cleanedSvg;
      console.log('✅ SVG template loaded successfully from Supabase storage');
    } catch (error) {
      console.error('Error loading SVG template from Supabase:', error);
      // Fallback to embedded template if file not found
      this.svgTemplate = this.getEmbeddedTemplate();
      console.log('⚠️ Using fallback embedded template');
    }
  }

  /**
   * Load certificate template elements from database
   */
  async loadTemplateElements(templateVersion: string = 'v1.0'): Promise<CertificateTemplateElement[]> {
    try {
      const { data, error } = await supabase
        .from('certificate_template_elements')
        .select('*')
        .eq('template_version', templateVersion)
        .eq('is_active', true)
        .order('z_index', { ascending: true });

      if (error) {
        console.error('Error loading template elements:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error loading template elements:', error);
      return [];
    }
  }

  /**
   * Generate certificate number in format: 0-000-001
   */
  private async generateCertificateNumber(): Promise<string> {
    try {
      // Get all existing certificate numbers to find the highest
      const { data, error } = await supabase
        .from('certificates')
        .select('certificate_number')
        .order('certificate_number', { ascending: false });

      if (error) throw error;

      let nextNumber = 1;
      if (data && data.length > 0) {
        // Find the highest existing number
        const numbers = data
          .map(cert => {
            const match = cert.certificate_number.match(/0-000-(\d+)/);
            return match ? parseInt(match[1]) : 0;
          })
          .filter(num => num > 0)
          .sort((a, b) => b - a);

        if (numbers.length > 0) {
          nextNumber = numbers[0] + 1;
        }
      }

      // Generate the certificate number
      const certNumber = `0-000-${nextNumber.toString().padStart(3, '0')}`;

      // Double-check it doesn't exist (race condition protection)
      const { data: existing } = await supabase
        .from('certificates')
        .select('id')
        .eq('certificate_number', certNumber)
        .limit(1);

      if (existing && existing.length > 0) {
        // If it exists, try the next number
        return `0-000-${(nextNumber + 1).toString().padStart(3, '0')}`;
      }

      return certNumber;
    } catch (error) {
      console.error('Error generating certificate number:', error);
      // Fallback to timestamp-based number to avoid duplicates
      const timestamp = Date.now().toString().slice(-6);
      return `0-000-${timestamp.slice(-3)}`;
    }
  }

  /**
   * Calculate reference number range based on existing shares
   */
  private async calculateRefNumber(sharesCount: number): Promise<string> {
    try {
      const { data, error } = await supabase
        .from('aureus_share_purchases')
        .select('shares_purchased')
        .eq('status', 'approved');

      if (error) throw error;

      const totalExistingShares = data?.reduce((sum, purchase) => sum + purchase.shares_purchased, 0) || 0;
      const startRange = totalExistingShares + 1;
      const endRange = totalExistingShares + sharesCount;

      return `${startRange} - ${endRange}`;
    } catch (error) {
      console.error('Error calculating ref number:', error);
      return `1 - ${sharesCount}`;
    }
  }

  /**
   * Get KYC data for user
   */
  private async getKYCData(userId: number): Promise<KYCData | null> {
    try {
      const { data, error } = await supabase
        .from('kyc_information')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching KYC data:', error);
      return null;
    }
  }

  /**
   * Get user data
   */
  private async getUserData(userId: number): Promise<any> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching user data:', error);
      return null;
    }
  }

  /**
   * Generate certificate data from purchase
   */
  async generateCertificateData(purchaseId: string): Promise<CertificateData | null> {
    try {
      // Get purchase data
      const { data: purchase, error: purchaseError } = await supabase
        .from('aureus_share_purchases')
        .select('*')
        .eq('id', purchaseId)
        .single();

      if (purchaseError) throw purchaseError;

      // Get user and KYC data
      const userData = await this.getUserData(purchase.user_id);
      const kycData = await this.getKYCData(purchase.user_id);

      if (!userData) {
        throw new Error('User data not found');
      }

      // Use the new dual numbering system to create certificate
      const { data: certNumbers, error: certError } = await supabase
        .rpc('admin_create_certificate_dual', {
          p_user_id: purchase.user_id,
          p_purchase_id: purchaseId,
          p_shares_count: purchase.shares_purchased,
          p_certificate_data: {
            package_name: purchase.package_name,
            total_amount: purchase.total_amount,
            created_by: 'svg_generator'
          }
        });

      if (certError) {
        console.error('Error creating certificate with dual numbering:', certError);
        throw certError;
      }

      const certNo = certNumbers[0]?.cert_no;
      const refNo = certNumbers[0]?.ref_no;

      if (!certNo || !refNo) {
        throw new Error('Failed to generate certificate numbers');
      }

      // Format address
      const addressLine1 = kycData?.street_address || userData.country_of_residence || 'Address not provided';
      const addressLine2 = kycData ?
        `${kycData.city || ''}, ${kycData.country || ''}, ${kycData.postal_code || ''}`.replace(/^,\s*|,\s*$/g, '') :
        '';

      return {
        certificateNumber: certNo, // Use certNo for backward compatibility
        certNo: certNo, // Sequential certificate number (e.g., "0-000-007")
        refNo: refNo, // Cumulative share range (e.g., "24-523")
        userName: kycData?.full_name || userData.full_name || userData.username,
        userIdNumber: kycData?.id_number || kycData?.passport_number || 'Not provided',
        userAddress: {
          line1: addressLine1,
          line2: addressLine2,
          city: kycData?.city || '',
          country: kycData?.country || userData.country_of_residence || '',
          postalCode: kycData?.postal_code || ''
        },
        sunId: userData.id.toString(),
        sharesCount: purchase.shares_purchased,
        refNumber: refNo, // Keep for backward compatibility
        issueDate: new Date().toLocaleDateString('en-GB'),
        purchaseDate: new Date(purchase.created_at).toLocaleDateString('en-GB')
      };
    } catch (error) {
      console.error('Error generating certificate data:', error);
      return null;
    }
  }

  /**
   * Generate certificate and return PNG blob for download
   */
  async generateCertificatePNG(purchaseId: string, saveToDatabase: boolean = false): Promise<{
    pngBlob: Blob | null;
    certificateData: CertificateData | null;
    pngUrl?: string;
  }> {
    try {
      if (!this.svgTemplate) {
        await this.loadTemplate();
      }

      const certData = await this.generateCertificateData(purchaseId);
      if (!certData) {
        throw new Error('Failed to generate certificate data');
      }

      // Replace placeholders in SVG template
      let svgCertificate = this.svgTemplate;

      // Add text overlays to the existing beautiful template
      svgCertificate = await this.addDynamicTextOverlays(svgCertificate, certData);

      // Convert to high-quality PNG
      const pngBlob = await this.svgToHighQualityPNG(svgCertificate);
      if (!pngBlob) {
        throw new Error('Failed to convert certificate to PNG');
      }

      let pngUrl: string | undefined;

      // Save to database if requested
      if (saveToDatabase) {
        pngUrl = await this.saveCertificateToDatabase(purchaseId, certData, svgCertificate) || undefined;
      }

      return {
        pngBlob,
        certificateData: certData,
        pngUrl
      };
    } catch (error) {
      console.error('Error generating certificate PNG:', error);
      return {
        pngBlob: null,
        certificateData: null
      };
    }
  }

  /**
   * Generate SVG certificate for preview only (not saved)
   */
  async generateCertificatePreview(purchaseId: string): Promise<string | null> {
    try {
      if (!this.svgTemplate) {
        await this.loadTemplate();
      }

      const certData = await this.generateCertificateData(purchaseId);
      if (!certData) {
        throw new Error('Failed to generate certificate data');
      }

      // Replace placeholders in SVG template
      let certificate = this.svgTemplate;

      // Add text overlays to the existing beautiful template
      certificate = await this.addDynamicTextOverlays(certificate, certData);

      return certificate;
    } catch (error) {
      console.error('Error generating certificate preview:', error);
      return null;
    }
  }

  /**
   * Generate SVG certificate for test purposes with custom data
   */
  async generateTestCertificate(testData: {
    userName: string;
    userIdNumber: string;
    addressLine1: string;
    addressLine2: string;
    sharesCount: number;
    certificateNumber: string;
    sunId: string;
    refNumber: string;
    issueDate: string;
  }): Promise<string | null> {
    try {
      if (!this.svgTemplate) {
        await this.loadTemplate();
      }

      // Replace placeholders in SVG template
      let certificate = this.svgTemplate;

      // Create certificate data object for overlay
      const certData: CertificateData = {
        certificateNumber: testData.certificateNumber, // Backward compatibility
        certNo: testData.certificateNumber, // Use same for test
        refNo: testData.refNumber, // Use provided refNumber
        userName: testData.userName,
        userIdNumber: testData.userIdNumber,
        userAddress: {
          line1: testData.addressLine1,
          line2: testData.addressLine2,
          city: '',
          country: '',
          postalCode: ''
        },
        sunId: testData.sunId,
        sharesCount: testData.sharesCount,
        refNumber: testData.refNumber, // Backward compatibility
        issueDate: testData.issueDate,
        purchaseDate: testData.issueDate
      };

      // Add text overlays to the existing beautiful template
      certificate = await this.addDynamicTextOverlays(certificate, certData);

      return certificate;
    } catch (error) {
      console.error('Error generating test certificate:', error);
      return null;
    }
  }

  /**
   * Save certificate to database with PNG file
   */
  private async saveCertificateToDatabase(
    purchaseId: string,
    certData: CertificateData,
    svgContent: string
  ): Promise<string | null> {
    try {
      // Get purchase data to get user_id
      const { data: purchase, error: purchaseError } = await supabase
        .from('aureus_share_purchases')
        .select('user_id')
        .eq('id', purchaseId)
        .single();

      if (purchaseError) throw purchaseError;

      // Convert SVG to high-quality PNG
      const pngBlob = await this.svgToHighQualityPNG(svgContent);
      if (!pngBlob) {
        throw new Error('Failed to convert certificate to PNG');
      }

      // Upload PNG to storage
      const pngUrl = await this.uploadCertificatePNG(
        certData.certificateNumber,
        purchase.user_id,
        pngBlob
      );

      if (!pngUrl) {
        throw new Error('Failed to upload certificate PNG to storage');
      }

      // Call the database function to create certificate record
      const { data, error } = await supabase.rpc('create_png_certificate', {
        p_user_id: purchase.user_id,
        p_purchase_id: purchaseId,
        p_certificate_number: certData.certificateNumber,
        p_shares_count: certData.sharesCount,
        p_png_url: pngUrl,
        p_file_size: pngBlob.size,
        p_certificate_data: {
          ref_number: certData.refNumber,
          issue_date: certData.issueDate,
          purchase_date: certData.purchaseDate,
          user_address: certData.userAddress,
          generation_timestamp: new Date().toISOString(),
          file_format: 'PNG',
          dpi: 300,
          dimensions: '3000x2121' // 1000x707 at 300 DPI
        }
      });

      if (error) {
        console.error('Error saving certificate to database:', error);
        throw error;
      }

      console.log('✅ Certificate PNG saved to database:', data);
      return pngUrl;
    } catch (error) {
      console.error('Error saving certificate to database:', error);
      throw error;
    }
  }

  /**
   * Convert SVG to high-quality PNG (300 DPI)
   */
  async svgToHighQualityPNG(svgString: string): Promise<Blob | null> {
    try {
      // Create a canvas element with high DPI
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) throw new Error('Canvas context not available');

      // Set canvas dimensions for 300 DPI (3x scale for high quality)
      const scaleFactor = 3; // 300 DPI
      canvas.width = 1000 * scaleFactor;
      canvas.height = 707 * scaleFactor;

      // Enable high-quality rendering
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = 'high';

      // Create image from SVG
      const img = new Image();
      const svgBlob = new Blob([svgString], { type: 'image/svg+xml;charset=utf-8' });
      const url = URL.createObjectURL(svgBlob);

      return new Promise((resolve, reject) => {
        img.onload = () => {
          // Fill with white background to ensure no transparency issues
          ctx.fillStyle = '#FFFFFF';
          ctx.fillRect(0, 0, canvas.width, canvas.height);

          // Draw the SVG image at high quality
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
          URL.revokeObjectURL(url);

          // Convert to PNG with maximum quality (1.0)
          canvas.toBlob((blob) => {
            if (blob) {
              console.log(`✅ High-quality PNG generated: ${(blob.size / 1024 / 1024).toFixed(2)}MB`);
              resolve(blob);
            } else {
              reject(new Error('Failed to create PNG blob'));
            }
          }, 'image/png', 1.0); // Maximum quality
        };

        img.onerror = () => {
          URL.revokeObjectURL(url);
          reject(new Error('Failed to load SVG for PNG conversion'));
        };

        img.src = url;
      });
    } catch (error) {
      console.error('Error converting SVG to high-quality PNG:', error);
      return null;
    }
  }

  /**
   * Upload PNG certificate to Supabase storage
   */
  async uploadCertificatePNG(
    certificateNumber: string,
    userId: number,
    pngBlob: Blob
  ): Promise<string | null> {
    try {
      const fileName = `certificate-${certificateNumber}-user-${userId}.png`;
      const filePath = `certificates/${fileName}`;

      // Try to upload to certificates bucket first, fallback to assets bucket
      let bucketName = 'certificates';
      let { data, error } = await supabase.storage
        .from(bucketName)
        .upload(filePath, pngBlob, {
          contentType: 'image/png',
          upsert: true
        });

      // If certificates bucket doesn't exist, use assets bucket
      if (error && error.message.includes('not found')) {
        console.log('⚠️ Certificates bucket not found, using assets bucket');
        bucketName = 'assets';
        const assetsFilePath = `certificates/${fileName}`;

        const uploadResult = await supabase.storage
          .from(bucketName)
          .upload(assetsFilePath, pngBlob, {
            contentType: 'image/png',
            upsert: true
          });

        data = uploadResult.data;
        error = uploadResult.error;
      }

      if (error) {
        console.error('Error uploading certificate PNG:', error);
        return null;
      }

      // Get public URL
      const { data: publicUrlData } = supabase.storage
        .from(bucketName)
        .getPublicUrl(data?.path || filePath);

      console.log('✅ Certificate PNG uploaded to storage:', publicUrlData.publicUrl);
      return publicUrlData.publicUrl;
    } catch (error) {
      console.error('Error uploading certificate PNG:', error);
      return null;
    }
  }

  /**
   * Add dynamic text overlays using database template elements
   */
  private async addDynamicTextOverlays(svgContent: string, certData: CertificateData): Promise<string> {
    // Find the closing </svg> tag and insert text elements before it
    const closingSvgIndex = svgContent.lastIndexOf('</svg>');
    if (closingSvgIndex === -1) {
      console.error('Invalid SVG: no closing </svg> tag found');
      return svgContent;
    }

    // Load template elements from database
    const templateElements = await this.loadTemplateElements();
    if (templateElements.length === 0) {
      console.warn('No template elements found, falling back to hardcoded positioning');
      return this.addTextOverlays(svgContent, certData);
    }

    // Enhanced helper function to wrap text within a specified width
    const wrapText = (text: string, maxWidth: number, fontSize: number): string[] => {
      if (!text || maxWidth <= 0) return [text || ''];

      const words = text.split(' ');
      const lines: string[] = [];
      let currentLine = '';

      // More accurate character width estimation for Arial font
      // Different characters have different widths, but this is a reasonable average
      const avgCharWidth = fontSize * 0.55; // Slightly more accurate than 0.6

      // Account for some padding/margin
      const usableWidth = maxWidth - (fontSize * 0.2);
      const maxCharsPerLine = Math.floor(usableWidth / avgCharWidth);

      for (const word of words) {
        const testLine = currentLine ? `${currentLine} ${word}` : word;
        const estimatedWidth = testLine.length * avgCharWidth;

        if (estimatedWidth <= usableWidth && testLine.length <= maxCharsPerLine) {
          currentLine = testLine;
        } else {
          if (currentLine) {
            lines.push(currentLine.trim());
            currentLine = word;
          } else {
            // Word is too long for the line, try to break it intelligently
            if (word.length > maxCharsPerLine) {
              // Break long words at reasonable points (commas, hyphens, etc.)
              const breakPoints = [',', '-', '.'];
              let broken = false;

              for (const breakPoint of breakPoints) {
                const breakIndex = word.lastIndexOf(breakPoint, maxCharsPerLine);
                if (breakIndex > 0 && breakIndex < word.length - 1) {
                  lines.push(word.substring(0, breakIndex + 1).trim());
                  currentLine = word.substring(breakIndex + 1).trim();
                  broken = true;
                  break;
                }
              }

              if (!broken) {
                // Force break if no natural break point found
                lines.push(word.substring(0, maxCharsPerLine).trim());
                currentLine = word.substring(maxCharsPerLine).trim();
              }
            } else {
              currentLine = word;
            }
          }
        }
      }

      if (currentLine.trim()) {
        lines.push(currentLine.trim());
      }

      // Ensure we don't return empty lines
      return lines.filter(line => line.length > 0);
    };

    // Helper function to get content for an element
    const getElementContent = (element: CertificateTemplateElement): string => {
      if (!element.is_dynamic) {
        return element.static_content || '';
      }

      // Map data fields to certificate data
      switch (element.data_field) {
        case 'certificateNumber':
          return certData.certificateNumber; // Backward compatibility
        case 'certNo':
          return certData.certNo; // New sequential certificate number
        case 'refNo':
          return certData.refNo; // New cumulative share range
        case 'userName':
          return certData.userName;
        case 'userIdNumber':
          return `ID: ${certData.userIdNumber}`;
        case 'userAddress':
          return `Address: ${certData.userAddress.line1}, ${certData.userAddress.line2}`;
        case 'sunId':
          return `Aureus ID: ${certData.sunId}`;
        case 'sharesCount':
          return certData.sharesCount.toString();
        case 'refNumber':
          return certData.refNumber; // Backward compatibility
        case 'issueDate':
          return certData.issueDate;
        default:
          return element.static_content || '';
      }
    };

    // Generate text overlays from template elements
    let textOverlays = '\n  <!-- Dynamic text overlays from database template -->\n';

    for (const element of templateElements) {
      if (element.element_type !== 'text') continue;

      const content = getElementContent(element);
      if (!content) continue;

      // Handle special cases for multi-line content
      if (element.element_id === 'user_address') {
        const fullAddress = `${certData.userAddress.line1}, ${certData.userAddress.line2}`;
        // Use the element's actual width for text wrapping, with fallback to 200px
        const elementWidth = element.width || 200;
        const wrappedAddress = wrapText(fullAddress, elementWidth, element.font_size || 12);

        // First line with "Address:" prefix
        textOverlays += `  <text x="${element.x_position}" y="${element.y_position}" font-family="${element.font_family}" font-size="${element.font_size}" font-weight="${element.font_weight}" fill="${element.font_color}" text-anchor="${element.text_anchor}">Address: ${wrappedAddress[0] || ''}</text>\n`;

        // Additional lines without prefix
        wrappedAddress.slice(1).forEach((line, index) => {
          const lineSpacing = element.font_size ? element.font_size * 1.2 : 12;
          textOverlays += `  <text x="${element.x_position}" y="${element.y_position + lineSpacing + (index * lineSpacing)}" font-family="${element.font_family}" font-size="${element.font_size}" font-weight="${element.font_weight}" fill="${element.font_color}" text-anchor="${element.text_anchor}">${line}</text>\n`;
        });
      } else if (element.width && content.length > 0) {
        // For other elements with width constraints, check if text wrapping is needed
        const elementWidth = element.width;
        const fontSize = element.font_size || 12;
        const estimatedTextWidth = content.length * fontSize * 0.6; // Rough estimate

        if (estimatedTextWidth > elementWidth) {
          // Text is too wide, wrap it
          const wrappedLines = wrapText(content, elementWidth, fontSize);
          wrappedLines.forEach((line, index) => {
            const lineSpacing = fontSize * 1.2;
            textOverlays += `  <text x="${element.x_position}" y="${element.y_position + (index * lineSpacing)}" font-family="${element.font_family}" font-size="${element.font_size}" font-weight="${element.font_weight}" fill="${element.font_color}" text-anchor="${element.text_anchor}">${line}</text>\n`;
          });
        } else {
          // Text fits in one line
          textOverlays += `  <text x="${element.x_position}" y="${element.y_position}" font-family="${element.font_family}" font-size="${element.font_size}" font-weight="${element.font_weight}" fill="${element.font_color}" text-anchor="${element.text_anchor}">${content}</text>\n`;
        }
      } else {
        // Standard single-line text element without width constraints
        textOverlays += `  <text x="${element.x_position}" y="${element.y_position}" font-family="${element.font_family}" font-size="${element.font_size}" font-weight="${element.font_weight}" fill="${element.font_color}" text-anchor="${element.text_anchor}">${content}</text>\n`;
      }
    }

    textOverlays += '  <!-- End dynamic text overlays -->\n';

    // Insert the text overlays before the closing </svg> tag
    return svgContent.slice(0, closingSvgIndex) + textOverlays + svgContent.slice(closingSvgIndex);
  }

  /**
   * Add text overlays to the existing beautiful SVG template (legacy method)
   */
  private addTextOverlays(svgContent: string, certData: CertificateData): string {
    // Find the closing </svg> tag and insert text elements before it
    const closingSvgIndex = svgContent.lastIndexOf('</svg>');
    if (closingSvgIndex === -1) {
      console.error('Invalid SVG: no closing </svg> tag found');
      return svgContent;
    }

    // Helper function to wrap text within a specified width
    const wrapText = (text: string, maxWidth: number, fontSize: number): string[] => {
      const words = text.split(' ');
      const lines: string[] = [];
      let currentLine = '';

      // Approximate character width based on font size (rough estimate)
      const charWidth = fontSize * 0.6;
      const maxCharsPerLine = Math.floor(maxWidth / charWidth);

      for (const word of words) {
        const testLine = currentLine ? `${currentLine} ${word}` : word;
        if (testLine.length <= maxCharsPerLine) {
          currentLine = testLine;
        } else {
          if (currentLine) {
            lines.push(currentLine);
            currentLine = word;
          } else {
            // Word is too long, break it
            lines.push(word);
          }
        }
      }

      if (currentLine) {
        lines.push(currentLine);
      }

      return lines;
    };

    // Wrap the address text to fit in the cell (max width ~200px for the NAME AND ADDRESS cell)
    const fullAddress = `${certData.userAddress.line1}, ${certData.userAddress.line2}`;
    const wrappedAddress = wrapText(fullAddress, 200, 8);

    // Create text overlay elements positioned exactly in the certificate table cells
    const textOverlays = `
  <!-- Text overlays for certificate data positioned in table cells -->

  <!-- Certificate Number (top left) -->
  <text x="97" y="43" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#000000">${certData.certificateNumber}</text>

  <!-- Number of Shares (top right) -->
  <text x="900" y="43" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#000000">${certData.sharesCount}</text>

  <!-- NAME AND ADDRESS cell - positioned in first column (x: 25-270) -->
  <text x="25" y="335" font-family="Arial, sans-serif" font-size="12" fill="#000000">${certData.userName}</text>
  <text x="25" y="355" font-family="Arial, sans-serif" font-size="12" fill="#000000">ID: ${certData.userIdNumber}</text>
  <text x="25" y="375" font-family="Arial, sans-serif" font-size="12" fill="#000000">Address: ${wrappedAddress[0] || ''}</text>
  ${wrappedAddress.slice(1).map((line, index) =>
    `<text x="25" y="${387 + (index * 12)}" font-family="Arial, sans-serif" font-size="12" fill="#000000">${line}</text>`
  ).join('\n  ')}
  <text x="25" y="${387 + ((wrappedAddress.length - 1) * 12) + 25}" font-family="Arial, sans-serif" font-size="12" fill="#000000">Aureus ID: ${certData.sunId}</text>

  <!-- CLASS OF SHARE cell - positioned in second column (x: 270-405) -->
  <text x="337" y="390" font-family="Arial, sans-serif" font-size="9" fill="#000000" text-anchor="middle">NO PAR VALUE</text>
  <text x="337" y="405" font-family="Arial, sans-serif" font-size="9" fill="#000000" text-anchor="middle">SHARES</text>

  <!-- REF NO. cell - positioned in third column (x: 405-520) -->
  <text x="462" y="400" font-family="Arial, sans-serif" font-size="9" fill="#000000" text-anchor="middle">${certData.refNumber}</text>

  <!-- DATE cell - positioned in fourth column (x: 520-680) -->
  <text x="600" y="400" font-family="Arial, sans-serif" font-size="9" fill="#000000" text-anchor="middle">${certData.issueDate}</text>

  <!-- CERT NO cell - positioned in fifth column (x: 680-840) -->
  <text x="760" y="400" font-family="Arial, sans-serif" font-size="9" fill="#000000" text-anchor="middle">${certData.certificateNumber}</text>

  <!-- NO OF SHARES cell - positioned in sixth column (x: 840-1000) -->
  <text x="920" y="400" font-family="Arial, sans-serif" font-size="9" fill="#000000" text-anchor="middle">${certData.sharesCount}</text>
`;

    // Insert the text overlays before the closing </svg> tag
    return svgContent.slice(0, closingSvgIndex) + textOverlays + svgContent.slice(closingSvgIndex);
  }

  /**
   * Get template with placeholders - creates a version of the beautiful template with placeholders
   */
  private getTemplateWithPlaceholders(): string {
    // This is a modified version of the beautiful template with placeholders added
    return `<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="1123" zoomAndPan="magnify" viewBox="0 0 842.25 595.499986" height="794" preserveAspectRatio="xMidYMid meet" version="1.0">
  <!-- Beautiful certificate design with placeholders -->
  <defs>
    <filter x="0%" y="0%" width="100%" height="100%" id="9d7ee0dda2">
      <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0" color-interpolation-filters="sRGB"/>
    </filter>
    <clipPath id="dfc30fe964">
      <path d="M 0 0.015625 L 841.5 0.015625 L 841.5 594.984375 L 0 594.984375 Z M 0 0.015625 " clip-rule="nonzero"/>
    </clipPath>
  </defs>

  <!-- Background -->
  <g clip-path="url(#dfc30fe964)">
    <path fill="#ffffff" d="M 0 0.015625 L 841.5 0.015625 L 841.5 594.984375 L 0 594.984375 Z M 0 0.015625 " fill-opacity="1" fill-rule="nonzero"/>
  </g>

  <!-- Certificate border and design elements -->
  <rect x="20" y="20" width="802.25" height="555.5" fill="none" stroke="#D4AF37" stroke-width="3"/>
  <rect x="40" y="40" width="762.25" height="515.5" fill="none" stroke="#D4AF37" stroke-width="1"/>

  <!-- Header -->
  <text x="421" y="80" font-family="serif" font-size="36" font-weight="bold" fill="#8B4513" text-anchor="middle">SHARE CERTIFICATE</text>
  <text x="421" y="110" font-family="serif" font-size="18" fill="#2F4F4F" text-anchor="middle">AUREUS ALLIANCE HOLDINGS (PTY) LTD</text>
  <text x="421" y="130" font-family="serif" font-size="14" fill="#2F4F4F" text-anchor="middle">Incorporated in the Republic of South Africa</text>
  <text x="421" y="145" font-family="serif" font-size="14" fill="#2F4F4F" text-anchor="middle">Registration No: 2021/847817/07</text>

  <!-- Certificate Number (top left) -->
  <text x="60" y="60" font-family="Arial" font-size="12" font-weight="bold" fill="#000">Certificate No: {{CERTIFICATE_NUMBER}}</text>

  <!-- Main content area -->
  <text x="421" y="200" font-family="serif" font-size="16" fill="#2F4F4F" text-anchor="middle">This is to certify that</text>

  <!-- User Name -->
  <text x="421" y="240" font-family="serif" font-size="24" font-weight="bold" fill="#8B4513" text-anchor="middle">{{USER_NAME}}</text>

  <!-- ID Number -->
  <text x="421" y="265" font-family="serif" font-size="14" fill="#2F4F4F" text-anchor="middle">ID Number: {{USER_ID_NUMBER}}</text>

  <!-- Address -->
  <text x="421" y="285" font-family="serif" font-size="12" fill="#2F4F4F" text-anchor="middle">{{USER_ADDRESS_LINE1}}</text>
  <text x="421" y="300" font-family="serif" font-size="12" fill="#2F4F4F" text-anchor="middle">{{USER_ADDRESS_LINE2}}</text>

  <!-- Ownership text -->
  <text x="421" y="340" font-family="serif" font-size="16" fill="#2F4F4F" text-anchor="middle">is the registered owner of</text>

  <!-- Share count -->
  <text x="421" y="380" font-family="serif" font-size="28" font-weight="bold" fill="#8B4513" text-anchor="middle">{{SHARES_COUNT}} SHARES</text>
  <text x="421" y="405" font-family="serif" font-size="14" fill="#2F4F4F" text-anchor="middle">NO PAR VALUE SHARES</text>

  <!-- Bottom section with details -->
  <line x1="80" y1="450" x2="762" y2="450" stroke="#D4AF37" stroke-width="1"/>

  <!-- Reference details -->
  <text x="100" y="480" font-family="Arial" font-size="10" fill="#000">SUN ID: {{SUN_ID}}</text>
  <text x="300" y="480" font-family="Arial" font-size="10" fill="#000">Ref: {{REF_NUMBER}}</text>
  <text x="500" y="480" font-family="Arial" font-size="10" fill="#000">Issue Date: {{ISSUE_DATE}}</text>

  <!-- Signature areas -->
  <text x="150" y="520" font-family="Arial" font-size="10" fill="#000" text-anchor="middle">_________________</text>
  <text x="150" y="535" font-family="Arial" font-size="8" fill="#000" text-anchor="middle">DC James - Director</text>

  <text x="650" y="520" font-family="Arial" font-size="10" fill="#000" text-anchor="middle">_________________</text>
  <text x="650" y="535" font-family="Arial" font-size="8" fill="#000" text-anchor="middle">JP Rademeyer - Director</text>

  <!-- Company seal area -->
  <circle cx="421" cy="520" r="30" fill="none" stroke="#D4AF37" stroke-width="2"/>
  <text x="421" y="520" font-family="Arial" font-size="8" fill="#8B4513" text-anchor="middle">COMPANY</text>
  <text x="421" y="530" font-family="Arial" font-size="8" fill="#8B4513" text-anchor="middle">SEAL</text>
</svg>`;
  }

  /**
   * Embedded SVG template fallback
   */
  private getEmbeddedTemplate(): string {
    return this.getTemplateWithPlaceholders();
  }
}

export const svgCertificateGenerator = new SVGCertificateGenerator();
