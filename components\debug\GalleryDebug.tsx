import React from 'react';
import { useGalleryImages, useGalleryCategories, useFeaturedImages } from '../../hooks/useGallery';

export const GalleryDebug: React.FC = () => {
  const allImages = useGalleryImages();
  const featuredImages = useFeaturedImages(6);
  const categories = useGalleryCategories();

  return (
    <div className="p-6 bg-gray-100 dark:bg-gray-800 rounded-lg">
      <h3 className="text-lg font-bold mb-4">🐛 Gallery Debug Info</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* All Images */}
        <div className="bg-white dark:bg-gray-700 p-4 rounded">
          <h4 className="font-semibold mb-2">All Images</h4>
          <p>Loading: {allImages.loading ? 'Yes' : 'No'}</p>
          <p>Error: {allImages.error || 'None'}</p>
          <p>Count: {allImages.images.length}</p>
          <div className="mt-2 text-sm">
            {allImages.images.map(img => (
              <div key={img.id} className="border-b py-1">
                <div className="font-medium">{img.title}</div>
                <div className="text-gray-600">Featured: {img.is_featured ? 'Yes' : 'No'}</div>
                <div className="text-gray-600">Category: {img.category?.name || 'None'}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Featured Images */}
        <div className="bg-white dark:bg-gray-700 p-4 rounded">
          <h4 className="font-semibold mb-2">Featured Images</h4>
          <p>Loading: {featuredImages.loading ? 'Yes' : 'No'}</p>
          <p>Error: {featuredImages.error || 'None'}</p>
          <p>Count: {featuredImages.images.length}</p>
          <div className="mt-2 text-sm">
            {featuredImages.images.map(img => (
              <div key={img.id} className="border-b py-1">
                <div className="font-medium">{img.title}</div>
                <div className="text-gray-600">URL: {img.image_url.substring(0, 50)}...</div>
              </div>
            ))}
          </div>
        </div>

        {/* Categories */}
        <div className="bg-white dark:bg-gray-700 p-4 rounded">
          <h4 className="font-semibold mb-2">Categories</h4>
          <p>Loading: {categories.loading ? 'Yes' : 'No'}</p>
          <p>Error: {categories.error || 'None'}</p>
          <p>Count: {categories.categories.length}</p>
          <div className="mt-2 text-sm">
            {categories.categories.map(cat => (
              <div key={cat.id} className="border-b py-1">
                <div className="font-medium">{cat.name}</div>
                <div className="text-gray-600">Slug: {cat.slug}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
