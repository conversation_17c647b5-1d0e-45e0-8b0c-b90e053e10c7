const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  console.error('Required: REACT_APP_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runUserContactMigration() {
  console.log('🚀 Starting user contact fields migration...');

  try {
    // First, check current table structure
    console.log('🔍 Checking current users table structure...');
    
    const { data: currentColumns, error: columnsError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable')
      .eq('table_name', 'users')
      .eq('table_schema', 'public')
      .order('column_name');

    if (columnsError) {
      console.error('❌ Error checking table structure:', columnsError);
      return;
    }

    console.log('📋 Current users table columns:');
    currentColumns.forEach(col => {
      console.log(`  • ${col.column_name} (${col.data_type})`);
    });

    // Check if the required columns exist
    const hasPhoneNumber = currentColumns.some(col => col.column_name === 'phone_number');
    const hasTelegramUsername = currentColumns.some(col => col.column_name === 'telegram_username');

    console.log(`\n📱 phone_number column exists: ${hasPhoneNumber ? '✅' : '❌'}`);
    console.log(`💬 telegram_username column exists: ${hasTelegramUsername ? '✅' : '❌'}`);

    if (hasPhoneNumber && hasTelegramUsername) {
      console.log('\n🎉 All required columns already exist! No migration needed.');
      return;
    }

    // Read and execute the migration SQL
    console.log('\n⚡ Running migration to add missing columns...');
    
    const sqlPath = path.join(__dirname, 'add-user-contact-fields.sql');
    const sqlContent = fs.readFileSync(sqlPath, 'utf8');

    // Execute the SQL using a raw query
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: sqlContent
    });

    if (error) {
      console.error('❌ Error executing migration:', error);
      
      // Try alternative approach - add columns individually
      console.log('🔄 Trying alternative approach...');
      
      if (!hasPhoneNumber) {
        console.log('📱 Adding phone_number column...');
        const { error: phoneError } = await supabase.rpc('exec_sql', {
          sql: 'ALTER TABLE users ADD COLUMN IF NOT EXISTS phone_number VARCHAR(20);'
        });
        
        if (phoneError) {
          console.error('❌ Error adding phone_number:', phoneError);
        } else {
          console.log('✅ phone_number column added successfully');
        }
      }
      
      if (!hasTelegramUsername) {
        console.log('💬 Adding telegram_username column...');
        const { error: telegramError } = await supabase.rpc('exec_sql', {
          sql: 'ALTER TABLE users ADD COLUMN IF NOT EXISTS telegram_username VARCHAR(50);'
        });
        
        if (telegramError) {
          console.error('❌ Error adding telegram_username:', telegramError);
        } else {
          console.log('✅ telegram_username column added successfully');
        }
      }
    } else {
      console.log('✅ Migration executed successfully');
    }

    // Verify the migration worked
    console.log('\n🔍 Verifying migration results...');
    
    const { data: updatedColumns, error: verifyError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable')
      .eq('table_name', 'users')
      .eq('table_schema', 'public')
      .in('column_name', ['phone_number', 'telegram_username']);

    if (verifyError) {
      console.error('❌ Error verifying migration:', verifyError);
    } else {
      console.log('📋 New columns added:');
      updatedColumns.forEach(col => {
        console.log(`  ✅ ${col.column_name} (${col.data_type}) - ${col.is_nullable === 'YES' ? 'nullable' : 'not null'}`);
      });
    }

    // Test the updated query
    console.log('\n🧪 Testing updated payment query...');
    
    const { data: testPayments, error: testError } = await supabase
      .from('crypto_payment_transactions')
      .select(`
        id,
        amount,
        users!inner(
          id,
          username,
          email,
          full_name,
          phone_number,
          telegram_username
        )
      `)
      .limit(1);

    if (testError) {
      console.error('❌ Test query failed:', testError);
    } else {
      console.log('✅ Test query successful!');
      if (testPayments.length > 0) {
        const payment = testPayments[0];
        console.log('📋 Sample payment data:');
        console.log(`  💎 Amount: $${payment.amount}`);
        console.log(`  👤 User: ${payment.users?.full_name || payment.users?.username || 'Unknown'}`);
        console.log(`  📧 Email: ${payment.users?.email || 'Not provided'}`);
        console.log(`  📱 Phone: ${payment.users?.phone_number || 'Not provided'}`);
        console.log(`  💬 Telegram: ${payment.users?.telegram_username || 'Not provided'}`);
      }
    }

    console.log('\n🎉 User contact fields migration completed successfully!');
    console.log('\n📋 Migration Summary:');
    console.log('✅ Added phone_number column to users table');
    console.log('✅ Added telegram_username column to users table');
    console.log('✅ Created index on telegram_username for performance');
    console.log('✅ Updated permissions for authenticated users');
    console.log('✅ Verified payment query works with new fields');
    console.log('\n🚀 The payment management system can now display contact information!');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
runUserContactMigration();
