#!/usr/bin/env node

/**
 * TEST SHAREHOLDER SPONSOR ASSIGNMENT
 * 
 * This script tests the automatic TTTFOUNDER sponsor assignment for shareholders
 */

require('dotenv').config()
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function testShareholderSponsor() {
  console.log('🧪 TESTING SHAREHOLDER SPONSOR ASSIGNMENT')
  console.log('=' .repeat(50))

  try {
    // Step 1: Check if TTTFOUNDER exists
    console.log('1️⃣ Checking TTTFOUNDER exists...')
    
    const { data: tttfounder, error: founderError } = await supabase
      .from('users')
      .select('id, username, email')
      .eq('username', 'TTTFOUNDER')
      .eq('is_active', true)
      .single()

    if (founderError || !tttfounder) {
      console.error('❌ TTTFOUNDER not found:', founderError)
      return
    }

    console.log('✅ TTTFOUNDER found:', {
      id: tttfounder.id,
      username: tttfounder.username,
      email: tttfounder.email
    })

    // Step 2: Find a test shareholder user (without existing referral)
    console.log('\n2️⃣ Finding test shareholder user...')
    
    const { data: testUsers, error: usersError } = await supabase
      .from('users')
      .select('id, username, email')
      .eq('is_active', true)
      .neq('username', 'TTTFOUNDER')
      .limit(5)

    if (usersError || !testUsers?.length) {
      console.error('❌ No test users found:', usersError)
      return
    }

    // Find a user without existing referral
    let testUser = null
    for (const user of testUsers) {
      const { data: existingReferral } = await supabase
        .from('referrals')
        .select('id')
        .eq('referred_id', user.id)
        .eq('status', 'active')
        .single()

      if (!existingReferral) {
        testUser = user
        break
      }
    }

    if (!testUser) {
      console.log('⚠️ All test users already have sponsors, creating referral anyway for demo')
      testUser = testUsers[0]
    }

    console.log('✅ Test user selected:', {
      id: testUser.id,
      username: testUser.username,
      email: testUser.email
    })

    // Step 3: Test the ensureShareholderSponsor logic
    console.log('\n3️⃣ Testing sponsor assignment...')
    
    // Check existing referral
    const { data: beforeReferral } = await supabase
      .from('referrals')
      .select('id, referrer_id, status')
      .eq('referred_id', testUser.id)
      .eq('status', 'active')
      .single()

    console.log('📋 Before - Existing referral:', beforeReferral || 'None')

    // Create referral relationship with TTTFOUNDER (simulate the logic)
    if (!beforeReferral) {
      const referralCode = `TTTFOUNDER_${testUser.id}_${Date.now()}`
      
      const { data: newReferral, error: referralError } = await supabase
        .from('referrals')
        .insert({
          referrer_id: tttfounder.id,
          referred_id: testUser.id,
          referral_code: referralCode,
          commission_rate: 15.00,
          status: 'active',
          campaign_source: 'shareholder_default',
          created_at: new Date().toISOString()
        })
        .select()
        .single()

      if (referralError) {
        console.error('❌ Failed to create referral:', referralError)
        return
      }

      console.log('✅ TTTFOUNDER referral created:', {
        id: newReferral.id,
        referrer_id: newReferral.referrer_id,
        referred_id: newReferral.referred_id,
        commission_rate: newReferral.commission_rate
      })
    } else {
      console.log('ℹ️ User already has sponsor, no action needed')
    }

    // Step 4: Verify the referral chain
    console.log('\n4️⃣ Verifying referral chain...')
    
    const { data: finalReferral, error: finalError } = await supabase
      .from('referrals')
      .select(`
        id,
        referrer_id,
        referred_id,
        commission_rate,
        status,
        referrer:users!referrals_referrer_id_fkey(username, email)
      `)
      .eq('referred_id', testUser.id)
      .eq('status', 'active')
      .single()

    if (finalError || !finalReferral) {
      console.error('❌ Failed to verify referral:', finalError)
      return
    }

    console.log('✅ Final referral verification:', {
      referral_id: finalReferral.id,
      sponsor: finalReferral.referrer.username,
      sponsor_email: finalReferral.referrer.email,
      commission_rate: finalReferral.commission_rate + '%',
      status: finalReferral.status
    })

    // Step 5: Test commission calculation simulation
    console.log('\n5️⃣ Testing commission calculation...')
    
    const testPurchaseAmount = 1000 // $1000 purchase
    const testShares = 200 // 200 shares
    
    const usdtCommission = testPurchaseAmount * 0.15 // 15% USDT
    const shareCommission = testShares * 0.15 // 15% shares
    
    console.log('💰 Commission calculation for $1000 purchase (200 shares):')
    console.log(`   USDT Commission: $${usdtCommission} (15%)`)
    console.log(`   Share Commission: ${shareCommission} shares (15%)`)
    console.log(`   Total Commission Value: $${usdtCommission + (shareCommission * 5)} (assuming $5/share)`)

    console.log('\n🎉 SHAREHOLDER SPONSOR TEST COMPLETED SUCCESSFULLY!')
    console.log('=' .repeat(50))
    console.log('✅ TTTFOUNDER will automatically earn commissions from all shareholder purchases')
    console.log('✅ Shareholders will not be notified about this commission structure')
    console.log('✅ Commission rates: 15% USDT + 15% Shares')

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the test
testShareholderSponsor()
