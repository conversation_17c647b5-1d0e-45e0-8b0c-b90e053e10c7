import { useState, useEffect } from 'react';
import { termsAcceptanceService, TermsAcceptanceRequest } from '../services/termsAcceptanceService';

interface UseTermsAcceptanceOptions {
  userId?: number;
  termsType?: 'registration' | 'share_purchase' | 'privacy_policy' | 'general';
  autoCheck?: boolean;
}

interface UseTermsAcceptanceReturn {
  // State
  acceptedTerms: boolean;
  acceptedPrivacy: boolean;
  hasAcceptedCurrentTerms: boolean;
  needsUpdate: boolean;
  loading: boolean;
  error: string | null;
  
  // Actions
  setAcceptedTerms: (accepted: boolean) => void;
  setAcceptedPrivacy: (accepted: boolean) => void;
  recordAcceptance: (request: Omit<TermsAcceptanceRequest, 'userId'>) => Promise<boolean>;
  checkCurrentAcceptance: () => Promise<void>;
  validateAcceptance: () => { isValid: boolean; error?: string };
  
  // Computed
  canProceed: boolean;
  validationError: string | null;
}

export const useTermsAcceptance = (options: UseTermsAcceptanceOptions = {}): UseTermsAcceptanceReturn => {
  const {
    userId,
    termsType = 'general',
    autoCheck = true
  } = options;

  // State
  const [acceptedTerms, setAcceptedTerms] = useState(false);
  const [acceptedPrivacy, setAcceptedPrivacy] = useState(false);
  const [hasAcceptedCurrentTerms, setHasAcceptedCurrentTerms] = useState(false);
  const [needsUpdate, setNeedsUpdate] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Auto-check current acceptance status
  useEffect(() => {
    if (autoCheck && userId) {
      checkCurrentAcceptance();
    }
  }, [userId, termsType, autoCheck]);

  /**
   * Check if user has accepted current terms
   */
  const checkCurrentAcceptance = async () => {
    if (!userId) return;

    setLoading(true);
    setError(null);

    try {
      console.log(`🔍 Checking current terms acceptance for user ${userId}`);

      // Check terms acceptance
      const hasAcceptedTerms = await termsAcceptanceService.hasAcceptedCurrentTerms(userId, termsType);
      
      // Check privacy policy acceptance if needed
      const hasAcceptedPrivacyPolicy = termsType !== 'privacy_policy' 
        ? await termsAcceptanceService.hasAcceptedCurrentTerms(userId, 'privacy_policy')
        : true;

      // Check if update is needed
      const termsUpdateCheck = await termsAcceptanceService.needsTermsUpdate(userId, termsType);
      const privacyUpdateCheck = termsType !== 'privacy_policy'
        ? await termsAcceptanceService.needsTermsUpdate(userId, 'privacy_policy')
        : { needsUpdate: false, currentVersion: '1.5' };

      setHasAcceptedCurrentTerms(hasAcceptedTerms && hasAcceptedPrivacyPolicy);
      setNeedsUpdate(termsUpdateCheck.needsUpdate || privacyUpdateCheck.needsUpdate);
      setAcceptedTerms(hasAcceptedTerms);
      setAcceptedPrivacy(hasAcceptedPrivacyPolicy);

      console.log(`✅ Terms acceptance check complete:`, {
        hasAcceptedTerms,
        hasAcceptedPrivacyPolicy,
        needsTermsUpdate: termsUpdateCheck.needsUpdate,
        needsPrivacyUpdate: privacyUpdateCheck.needsUpdate
      });

    } catch (err: any) {
      console.error('❌ Failed to check terms acceptance:', err);
      setError(err.message || 'Failed to check terms acceptance');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Record terms acceptance
   */
  const recordAcceptance = async (request: Omit<TermsAcceptanceRequest, 'userId'>): Promise<boolean> => {
    if (!userId) {
      setError('User ID is required to record acceptance');
      return false;
    }

    setLoading(true);
    setError(null);

    try {
      console.log(`📝 Recording terms acceptance for user ${userId}`);

      const fullRequest: TermsAcceptanceRequest = {
        ...request,
        userId
      };

      await termsAcceptanceService.recordTermsAcceptance(fullRequest);

      // If recording privacy policy separately
      if (request.termsType !== 'privacy_policy' && acceptedPrivacy) {
        await termsAcceptanceService.recordTermsAcceptance({
          ...fullRequest,
          termsType: 'privacy_policy'
        });
      }

      // Update state
      setHasAcceptedCurrentTerms(true);
      setNeedsUpdate(false);

      console.log('✅ Terms acceptance recorded successfully');
      return true;

    } catch (err: any) {
      console.error('❌ Failed to record terms acceptance:', err);
      setError(err.message || 'Failed to record terms acceptance');
      return false;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Validate current acceptance state
   */
  const validateAcceptance = (): { isValid: boolean; error?: string } => {
    if (!acceptedTerms) {
      return {
        isValid: false,
        error: 'You must accept the Terms & Conditions to continue'
      };
    }

    if (termsType !== 'privacy_policy' && !acceptedPrivacy) {
      return {
        isValid: false,
        error: 'You must accept the Privacy Policy to continue'
      };
    }

    return { isValid: true };
  };

  // Computed values
  const canProceed = acceptedTerms && (termsType === 'privacy_policy' || acceptedPrivacy);
  const validationError = !canProceed ? validateAcceptance().error || null : null;

  return {
    // State
    acceptedTerms,
    acceptedPrivacy,
    hasAcceptedCurrentTerms,
    needsUpdate,
    loading,
    error,
    
    // Actions
    setAcceptedTerms,
    setAcceptedPrivacy,
    recordAcceptance,
    checkCurrentAcceptance,
    validateAcceptance,
    
    // Computed
    canProceed,
    validationError
  };
};
