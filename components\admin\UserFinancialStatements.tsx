import React, { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';
import FinancialStatementReport from '../reports/FinancialStatementReport';

interface User {
  id: number;
  username: string;
  full_name: string;
  email: string;
  created_at: string;
}

interface UserFinancialStatementsProps {
  className?: string;
}

const UserFinancialStatements: React.FC<UserFinancialStatementsProps> = ({ className = '' }) => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showStatement, setShowStatement] = useState(false);

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      const serviceClient = getServiceRoleClient();
      const { data, error } = await serviceClient
        .from('users')
        .select('id, username, full_name, email, created_at')
        .order('created_at', { ascending: false })
        .limit(100);

      if (error) throw error;
      setUsers(data || []);
    } catch (error) {
      console.error('Error loading users:', error);
      setError(error instanceof Error ? error.message : 'Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const filteredUsers = users.filter(user =>
    user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleGenerateStatement = (user: User) => {
    setSelectedUser(user);
    setShowStatement(true);
  };

  if (loading) {
    return (
      <div className={`${className} flex items-center justify-center py-12`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto mb-4"></div>
          <p className="text-white">Loading users...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`${className} text-center py-12`}>
        <div className="text-red-400 text-4xl mb-4">⚠️</div>
        <h3 className="text-white text-lg font-semibold mb-2">Error Loading Users</h3>
        <p className="text-gray-400 mb-4">{error}</p>
        <button
          onClick={loadUsers}
          className="bg-yellow-600 hover:bg-yellow-700 text-black px-4 py-2 rounded-lg font-medium"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-white mb-2">User Financial Statements</h2>
        <p className="text-gray-400">Generate comprehensive financial statements for any user</p>
      </div>

      {/* Search */}
      <div className="mb-6">
        <input
          type="text"
          placeholder="Search users by username, name, or email..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full bg-gray-700 text-white px-4 py-3 rounded-lg border border-gray-600 focus:border-yellow-400 focus:outline-none"
        />
      </div>

      {/* Users Table */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-700">
              <tr>
                <th className="text-left text-gray-300 font-medium p-4">User ID</th>
                <th className="text-left text-gray-300 font-medium p-4">Username</th>
                <th className="text-left text-gray-300 font-medium p-4">Full Name</th>
                <th className="text-left text-gray-300 font-medium p-4">Email</th>
                <th className="text-left text-gray-300 font-medium p-4">Joined</th>
                <th className="text-center text-gray-300 font-medium p-4">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredUsers.length === 0 ? (
                <tr>
                  <td colSpan={6} className="text-center py-12">
                    <div className="text-gray-400">
                      <div className="text-4xl mb-4">👥</div>
                      <p className="text-lg font-semibold mb-2">No Users Found</p>
                      <p>No users match your search criteria.</p>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredUsers.map((user, index) => (
                  <tr key={user.id} className={`${index % 2 === 0 ? 'bg-gray-800' : 'bg-gray-750'} hover:bg-gray-700`}>
                    <td className="p-4 text-white font-mono">{user.id}</td>
                    <td className="p-4 text-white font-medium">{user.username}</td>
                    <td className="p-4 text-white">{user.full_name || '-'}</td>
                    <td className="p-4 text-gray-300">{user.email || '-'}</td>
                    <td className="p-4 text-gray-400">
                      {new Date(user.created_at).toLocaleDateString()}
                    </td>
                    <td className="p-4 text-center">
                      <button
                        onClick={() => handleGenerateStatement(user)}
                        className="bg-yellow-600 hover:bg-yellow-700 text-black px-4 py-2 rounded-lg font-medium text-sm flex items-center gap-2 mx-auto"
                      >
                        📄 Generate Statement
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Results Summary */}
      <div className="mt-4 text-center text-gray-400 text-sm">
        Showing {filteredUsers.length} of {users.length} users
      </div>

      {/* Financial Statement Modal */}
      {showStatement && selectedUser && (
        <FinancialStatementReport
          userId={selectedUser.id}
          userName={selectedUser.full_name || selectedUser.username}
          isAdmin={true}
          onClose={() => {
            setShowStatement(false);
            setSelectedUser(null);
          }}
        />
      )}
    </div>
  );
};

export default UserFinancialStatements;
