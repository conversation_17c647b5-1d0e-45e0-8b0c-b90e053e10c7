/**
 * ADD KYC REJECTION REASON COLUMN
 * 
 * Adds a rejection_reason column to the kyc_information table
 * to store detailed reasons when KYC submissions are rejected.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing required environment variables');
  console.error('Required: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Initialize Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function executeSQL(sql, description) {
  try {
    console.log(`🔄 ${description}...`);
    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });
    
    if (error) {
      console.error(`❌ ${description} failed:`, error);
      return false;
    }
    
    console.log(`✅ ${description} completed successfully`);
    return true;
  } catch (err) {
    console.error(`❌ ${description} failed:`, err);
    return false;
  }
}

async function addKYCRejectionReasonColumn() {
  console.log('🚀 Starting KYC rejection reason column addition...');

  // Step 1: Add rejection_reason column
  const addColumnSQL = `
    ALTER TABLE kyc_information 
    ADD COLUMN IF NOT EXISTS rejection_reason TEXT;
  `;

  if (!await executeSQL(addColumnSQL, 'Adding rejection_reason column')) {
    return false;
  }

  // Step 2: Add comment to the column
  const addCommentSQL = `
    COMMENT ON COLUMN kyc_information.rejection_reason IS 'Detailed reason provided when KYC submission is rejected';
  `;

  if (!await executeSQL(addCommentSQL, 'Adding column comment')) {
    return false;
  }

  // Step 3: Create index for better query performance
  const createIndexSQL = `
    CREATE INDEX IF NOT EXISTS idx_kyc_information_rejection_reason 
    ON kyc_information(rejection_reason) 
    WHERE rejection_reason IS NOT NULL;
  `;

  if (!await executeSQL(createIndexSQL, 'Creating rejection_reason index')) {
    return false;
  }

  console.log('✅ KYC rejection reason column addition completed successfully!');
  return true;
}

// Execute the migration
addKYCRejectionReasonColumn()
  .then((success) => {
    if (success) {
      console.log('🎉 Migration completed successfully!');
      process.exit(0);
    } else {
      console.error('💥 Migration failed!');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('💥 Migration failed with error:', error);
    process.exit(1);
  });
