import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Simple hash function for password (same as in lib/supabase.ts)
async function hashPassword(password) {
  const encoder = new TextEncoder();
  const data = encoder.encode(password + 'aureus_salt_2024');
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

// Verify password hash
async function verifyPassword(password, hash) {
  try {
    const computedHash = await hashPassword(password);
    return computedHash === hash;
  } catch (error) {
    console.error('Error verifying password:', error);
    return false;
  }
}

async function testLogin() {
  try {
    console.log('🧪 Testing user login functionality...');
    
    const testCredentials = {
      email: '<EMAIL>',
      password: 'TestPassword123'
    };
    
    console.log('📧 Testing login for:', testCredentials.email);
    
    // Get user from database
    const { data: dbUser, error: dbError } = await supabase
      .from('users')
      .select('*')
      .eq('email', testCredentials.email)
      .single();
    
    if (dbError || !dbUser) {
      console.error('❌ User not found in database:', dbError);
      return;
    }
    
    console.log('✅ User found in database:', dbUser.username, '(ID:', dbUser.id + ')');
    
    // Verify password
    const passwordValid = await verifyPassword(testCredentials.password, dbUser.password_hash);
    
    if (!passwordValid) {
      console.error('❌ Password verification failed');
      return;
    }
    
    console.log('✅ Password verification successful');
    
    // Check if user is active
    if (!dbUser.is_active) {
      console.error('❌ User account is deactivated');
      return;
    }
    
    console.log('✅ User account is active');
    
    // Test Supabase auth login
    console.log('🔐 Testing Supabase auth login...');
    
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: testCredentials.email,
      password: testCredentials.password
    });
    
    if (authError) {
      console.warn('⚠️ Supabase auth login failed:', authError.message);
      console.log('   This might be expected for users created directly in database');
    } else {
      console.log('✅ Supabase auth login successful');
      console.log('   User ID:', authData.user.id);
      console.log('   Email:', authData.user.email);
    }
    
    // Check referral relationship
    console.log('🔗 Checking referral relationship...');
    
    const { data: referrals, error: referralError } = await supabase
      .from('referrals')
      .select(`
        *,
        referrer:referrer_id(username, full_name)
      `)
      .eq('referred_id', dbUser.id);
    
    if (referralError) {
      console.error('❌ Error checking referrals:', referralError);
    } else if (referrals && referrals.length > 0) {
      console.log('✅ Referral relationship found:');
      referrals.forEach(ref => {
        console.log(`   Sponsor: ${ref.referrer.username} (${ref.referrer.full_name})`);
        console.log(`   Code: ${ref.referral_code}`);
        console.log(`   Commission Rate: ${ref.commission_rate}%`);
      });
    } else {
      console.log('⚠️ No referral relationships found');
    }
    
    console.log('\n🎉 Login test completed successfully!');
    console.log('📊 Summary:');
    console.log('  - User found in database ✅');
    console.log('  - Password verification working ✅');
    console.log('  - User account active ✅');
    console.log('  - Referral relationship exists ✅');
    console.log('  - Database operations successful ✅');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

testLogin();
