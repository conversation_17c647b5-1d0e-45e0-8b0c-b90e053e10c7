import React, { useState, useEffect } from 'react';
import { useOnboarding } from '../../lib/hooks/useOnboarding';

export interface FeatureRequirement {
  type: 'step' | 'kyc' | 'purchase' | 'referral' | 'time';
  value: string | number;
  description: string;
}

export interface Feature {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'basic' | 'financial' | 'social' | 'advanced';
  requirements: FeatureRequirement[];
  unlocked: boolean;
  comingSoon?: boolean;
}

interface FeatureUnlockSystemProps {
  userId: number;
  userKycStatus?: 'pending' | 'approved' | 'rejected';
  userShareCount?: number;
  userReferralCount?: number;
  userRegistrationDate?: string;
}

export const FeatureUnlockSystem: React.FC<FeatureUnlockSystemProps> = ({
  userId,
  userKycStatus = 'pending',
  userShareCount = 0,
  userReferralCount = 0,
  userRegistrationDate
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showUnlockedOnly, setShowUnlockedOnly] = useState(false);

  const { status } = useOnboarding({ userId, autoLoad: true });

  const features: Feature[] = [
    // Basic Features
    {
      id: 'email_notifications',
      name: 'Email Notifications',
      description: 'Receive important updates and alerts via email',
      icon: '📧',
      category: 'basic',
      requirements: [
        { type: 'step', value: 'email_verification', description: 'Verify your email address' }
      ],
      unlocked: false
    },
    {
      id: 'profile_customization',
      name: 'Profile Customization',
      description: 'Personalize your profile with photo and preferences',
      icon: '👤',
      category: 'basic',
      requirements: [
        { type: 'step', value: 'profile_completion', description: 'Complete your profile' }
      ],
      unlocked: false
    },
    {
      id: 'dashboard_themes',
      name: 'Dashboard Themes',
      description: 'Customize your dashboard appearance',
      icon: '🎨',
      category: 'basic',
      requirements: [
        { type: 'step', value: 'dashboard_customization', description: 'Complete dashboard setup' }
      ],
      unlocked: false
    },

    // Financial Features
    {
      id: 'share_purchases',
      name: 'Share Purchases',
      description: 'Buy Aureus Africa shares and start earning',
      icon: '💰',
      category: 'financial',
      requirements: [
        { type: 'step', value: 'terms_acceptance', description: 'Accept terms and conditions' },
        { type: 'step', value: 'country_selection', description: 'Select your country' }
      ],
      unlocked: false
    },
    {
      id: 'dividend_calculator',
      name: 'Dividend Calculator',
      description: 'Calculate potential dividend earnings',
      icon: '📊',
      category: 'financial',
      requirements: [
        { type: 'step', value: 'first_share_purchase', description: 'Purchase your first shares' }
      ],
      unlocked: false
    },
    {
      id: 'portfolio_analytics',
      name: 'Portfolio Analytics',
      description: 'Advanced portfolio tracking and insights',
      icon: '📈',
      category: 'financial',
      requirements: [
        { type: 'purchase', value: 100, description: 'Own at least 100 shares' }
      ],
      unlocked: false
    },
    {
      id: 'dividend_payments',
      name: 'Dividend Payments',
      description: 'Receive quarterly dividend payments',
      icon: '💎',
      category: 'financial',
      requirements: [
        { type: 'kyc', value: 'approved', description: 'Complete KYC verification' },
        { type: 'step', value: 'first_share_purchase', description: 'Own shares' }
      ],
      unlocked: false
    },
    {
      id: 'commission_withdrawals',
      name: 'Commission Withdrawals',
      description: 'Withdraw your referral commissions',
      icon: '💸',
      category: 'financial',
      requirements: [
        { type: 'kyc', value: 'approved', description: 'Complete KYC verification' }
      ],
      unlocked: false
    },

    // Social Features
    {
      id: 'referral_program',
      name: 'Referral Program',
      description: 'Earn commissions by referring friends',
      icon: '🔗',
      category: 'social',
      requirements: [
        { type: 'step', value: 'referral_setup', description: 'Set up your referral link' }
      ],
      unlocked: false
    },
    {
      id: 'team_dashboard',
      name: 'Team Dashboard',
      description: 'Manage and track your referral network',
      icon: '👥',
      category: 'social',
      requirements: [
        { type: 'referral', value: 1, description: 'Refer at least 1 person' }
      ],
      unlocked: false
    },
    {
      id: 'marketing_tools',
      name: 'Marketing Tools',
      description: 'Access promotional materials and resources',
      icon: '📢',
      category: 'social',
      requirements: [
        { type: 'step', value: 'first_referral', description: 'Make your first referral' }
      ],
      unlocked: false
    },
    {
      id: 'leaderboards',
      name: 'Leaderboards',
      description: 'Compete with other affiliates',
      icon: '🏆',
      category: 'social',
      requirements: [
        { type: 'referral', value: 5, description: 'Refer at least 5 people' }
      ],
      unlocked: false
    },

    // Advanced Features
    {
      id: 'api_access',
      name: 'API Access',
      description: 'Integrate with third-party applications',
      icon: '🔌',
      category: 'advanced',
      requirements: [
        { type: 'kyc', value: 'approved', description: 'Complete KYC verification' },
        { type: 'purchase', value: 500, description: 'Own at least 500 shares' }
      ],
      unlocked: false,
      comingSoon: true
    },
    {
      id: 'advanced_analytics',
      name: 'Advanced Analytics',
      description: 'Detailed performance metrics and insights',
      icon: '📊',
      category: 'advanced',
      requirements: [
        { type: 'step', value: 'training_completion', description: 'Complete training modules' },
        { type: 'time', value: 30, description: 'Account active for 30+ days' }
      ],
      unlocked: false
    },
    {
      id: 'priority_support',
      name: 'Priority Support',
      description: 'Get faster response times from support',
      icon: '🎧',
      category: 'advanced',
      requirements: [
        { type: 'purchase', value: 1000, description: 'Own at least 1000 shares' }
      ],
      unlocked: false
    }
  ];

  // Check feature unlock status
  useEffect(() => {
    features.forEach(feature => {
      feature.unlocked = checkFeatureUnlocked(feature);
    });
  }, [status, userKycStatus, userShareCount, userReferralCount, userRegistrationDate]);

  const checkFeatureUnlocked = (feature: Feature): boolean => {
    if (feature.comingSoon) return false;

    return feature.requirements.every(requirement => {
      switch (requirement.type) {
        case 'step':
          // Check if onboarding step is completed
          // This would need to be implemented based on actual progress data
          return status?.completed_steps > 0; // Simplified check
          
        case 'kyc':
          return userKycStatus === requirement.value;
          
        case 'purchase':
          return userShareCount >= (requirement.value as number);
          
        case 'referral':
          return userReferralCount >= (requirement.value as number);
          
        case 'time':
          if (!userRegistrationDate) return false;
          const daysSinceRegistration = Math.floor(
            (Date.now() - new Date(userRegistrationDate).getTime()) / (1000 * 60 * 60 * 24)
          );
          return daysSinceRegistration >= (requirement.value as number);
          
        default:
          return false;
      }
    });
  };

  const categories = [
    { id: 'all', name: 'All Features', icon: '🌟' },
    { id: 'basic', name: 'Basic', icon: '⚡' },
    { id: 'financial', name: 'Financial', icon: '💰' },
    { id: 'social', name: 'Social', icon: '👥' },
    { id: 'advanced', name: 'Advanced', icon: '🚀' }
  ];

  const filteredFeatures = features.filter(feature => {
    const categoryMatch = selectedCategory === 'all' || feature.category === selectedCategory;
    const unlockedMatch = !showUnlockedOnly || feature.unlocked;
    return categoryMatch && unlockedMatch;
  });

  const unlockedCount = features.filter(f => f.unlocked).length;
  const totalCount = features.length;

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gold mb-2">Feature Unlock System</h1>
        <p className="text-gray-300">
          Complete onboarding steps and activities to unlock powerful features.
        </p>
      </div>

      {/* Progress Overview */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-white">Your Progress</h2>
          <span className="text-sm text-gray-400">
            {unlockedCount} of {totalCount} features unlocked
          </span>
        </div>
        
        <div className="w-full bg-gray-700 rounded-full h-3 mb-4">
          <div 
            className="bg-gradient-to-r from-gold to-yellow-500 h-3 rounded-full transition-all duration-500"
            style={{ width: `${(unlockedCount / totalCount) * 100}%` }}
          />
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-green-400">{features.filter(f => f.unlocked && f.category === 'basic').length}</div>
            <div className="text-sm text-gray-400">Basic</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-400">{features.filter(f => f.unlocked && f.category === 'financial').length}</div>
            <div className="text-sm text-gray-400">Financial</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-purple-400">{features.filter(f => f.unlocked && f.category === 'social').length}</div>
            <div className="text-sm text-gray-400">Social</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-orange-400">{features.filter(f => f.unlocked && f.category === 'advanced').length}</div>
            <div className="text-sm text-gray-400">Advanced</div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-4 mb-6">
        <div className="flex gap-2">
          {categories.map(category => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                selectedCategory === category.id
                  ? 'bg-gold text-black'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              <span className="mr-2">{category.icon}</span>
              {category.name}
            </button>
          ))}
        </div>
        
        <label className="flex items-center gap-2 text-gray-300">
          <input
            type="checkbox"
            checked={showUnlockedOnly}
            onChange={(e) => setShowUnlockedOnly(e.target.checked)}
            className="w-4 h-4 text-gold bg-gray-700 border-gray-600 rounded focus:ring-gold"
          />
          Show unlocked only
        </label>
      </div>

      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredFeatures.map(feature => (
          <div
            key={feature.id}
            className={`rounded-lg border p-6 transition-all ${
              feature.unlocked
                ? 'bg-green-900/20 border-green-500/30'
                : feature.comingSoon
                ? 'bg-gray-800/50 border-gray-600/50'
                : 'bg-gray-800 border-gray-700 hover:border-gray-600'
            }`}
          >
            <div className="flex items-start gap-4">
              <span className="text-3xl">{feature.icon}</span>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="text-lg font-semibold text-white">{feature.name}</h3>
                  {feature.unlocked && <span className="text-green-400">✓</span>}
                  {feature.comingSoon && (
                    <span className="px-2 py-1 bg-blue-900/30 text-blue-400 text-xs rounded">
                      Coming Soon
                    </span>
                  )}
                </div>
                
                <p className="text-gray-400 text-sm mb-4">{feature.description}</p>
                
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-300">Requirements:</h4>
                  {feature.requirements.map((req, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <span className={`w-4 h-4 rounded-full flex items-center justify-center text-xs ${
                        checkRequirementMet(req) ? 'bg-green-500 text-white' : 'bg-gray-600 text-gray-400'
                      }`}>
                        {checkRequirementMet(req) ? '✓' : '○'}
                      </span>
                      <span className={checkRequirementMet(req) ? 'text-green-400' : 'text-gray-400'}>
                        {req.description}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredFeatures.length === 0 && (
        <div className="text-center py-12">
          <span className="text-4xl mb-4 block">🔍</span>
          <h3 className="text-xl font-semibold text-white mb-2">No features found</h3>
          <p className="text-gray-400">Try adjusting your filters to see more features.</p>
        </div>
      )}
    </div>
  );

  function checkRequirementMet(requirement: FeatureRequirement): boolean {
    switch (requirement.type) {
      case 'step':
        return status?.completed_steps > 0; // Simplified check
      case 'kyc':
        return userKycStatus === requirement.value;
      case 'purchase':
        return userShareCount >= (requirement.value as number);
      case 'referral':
        return userReferralCount >= (requirement.value as number);
      case 'time':
        if (!userRegistrationDate) return false;
        const daysSinceRegistration = Math.floor(
          (Date.now() - new Date(userRegistrationDate).getTime()) / (1000 * 60 * 60 * 24)
        );
        return daysSinceRegistration >= (requirement.value as number);
      default:
        return false;
    }
  }
};
