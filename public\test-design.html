<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aureus Design System - Live Test</title>
    <style>
        /* Import our unified design system directly */
        @import url('../aureus.css');
        
        /* Ensure our styles take precedence */
        body {
            background-color: var(--bg) !important;
            color: var(--text) !important;
            font-family: 'Inter', 'Segoe UI', 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif !important;
            line-height: 1.6 !important;
            margin: 0 !important;
            padding: 0 !important;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: var(--surface);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow);
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .test-card {
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: var(--radius-lg);
            padding: 2rem;
            box-shadow: var(--shadow);
            transition: all 0.2s ease;
        }
        
        .test-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .test-button {
            background: var(--gold);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: var(--radius-md);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .test-button:hover {
            background: var(--cta-hover-bg);
            transform: translateY(-1px);
        }
        
        .color-swatch {
            width: 100%;
            height: 60px;
            border-radius: var(--radius-md);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0,0,0,0.5);
        }
        
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--surface);
            border: 2px solid var(--border);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 1.5rem;
            box-shadow: var(--shadow);
            transition: all 0.2s ease;
        }
        
        .theme-toggle:hover {
            border-color: var(--gold);
            transform: scale(1.1);
        }
        
        /* Dark theme styles */
        [data-theme='dark'] {
            --bg: #050505;
            --surface: #121212;
            --text: #FFFFFF;
            --text-muted: #B0B0B0;
            --border: #1E1E1E;
            --shadow: 0px 0px 24px rgba(255, 215, 0, 0.15);
        }
        
        [data-theme='dark'] .test-card:hover {
            box-shadow: 0 8px 25px rgba(255, 215, 0, 0.1);
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()" aria-label="Toggle theme">🌙</button>
    
    <div class="test-container">
        <div class="test-header">
            <h1 style="color: var(--gold); font-size: 3rem; margin-bottom: 1rem;">Aureus Alliance Holdings</h1>
            <h2 style="color: var(--text); font-size: 1.5rem; margin-bottom: 1rem;">Unified Design System - Live Test</h2>
            <p style="color: var(--text-muted); font-size: 1.125rem;">Testing our premium color palette and professional styling</p>
            <button class="test-button" style="margin-top: 1rem;">Test Button</button>
        </div>
        
        <div class="test-grid">
            <div class="test-card">
                <h3 style="color: var(--text); margin-bottom: 1rem;">Premium Gold</h3>
                <div class="color-swatch" style="background: var(--gold);">#FFD700</div>
                <p style="color: var(--text-muted);">Primary brand color from premium palette</p>
            </div>
            
            <div class="test-card">
                <h3 style="color: var(--text); margin-bottom: 1rem;">Professional Blue</h3>
                <div class="color-swatch" style="background: var(--blue);">#007BFF</div>
                <p style="color: var(--text-muted);">Secondary brand color for accents</p>
            </div>
            
            <div class="test-card">
                <h3 style="color: var(--text); margin-bottom: 1rem;">Success Emerald</h3>
                <div class="color-swatch" style="background: var(--emerald);">#27AE60</div>
                <p style="color: var(--text-muted);">Success states and positive actions</p>
            </div>
            
            <div class="test-card">
                <h3 style="color: var(--text); margin-bottom: 1rem;">Warning Copper</h3>
                <div class="color-swatch" style="background: var(--copper);">#B75E2A</div>
                <p style="color: var(--text-muted);">Warning states and attention</p>
            </div>
        </div>
        
        <div class="test-card">
            <h3 style="color: var(--text); margin-bottom: 1rem;">Design System Features</h3>
            <ul style="color: var(--text); line-height: 1.8;">
                <li>✅ Premium color palette from aureus_ui_color_palette_premium.md</li>
                <li>✅ Professional corporate styling</li>
                <li>✅ Dark/Light theme switching (click the moon/sun button)</li>
                <li>✅ Responsive design with mobile-first approach</li>
                <li>✅ Consistent spacing and typography</li>
                <li>✅ Smooth animations and transitions</li>
                <li>✅ Accessibility-compliant focus states</li>
                <li>✅ CSS custom properties for easy maintenance</li>
            </ul>
            <div style="margin-top: 2rem;">
                <button class="test-button">Primary Action</button>
                <button class="test-button" style="background: var(--blue); margin-left: 1rem;">Secondary Action</button>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 3rem; padding: 2rem; background: var(--surface); border-radius: var(--radius-lg); box-shadow: var(--shadow);">
            <h2 style="color: var(--gold); margin-bottom: 1rem;">🎉 Design System Working!</h2>
            <p style="color: var(--text); font-size: 1.125rem;">If you can see this styled page with the premium colors, the unified design system is successfully loaded and working.</p>
        </div>
    </div>
    
    <script>
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
            
            const toggle = document.querySelector('.theme-toggle');
            toggle.textContent = newTheme === 'dark' ? '☀️' : '🌙';
        }
        
        // Initialize theme
        document.documentElement.setAttribute('data-theme', 'light');
    </script>
</body>
</html>
