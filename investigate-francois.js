#!/usr/bin/env node

/**
 * Investigate <PERSON><PERSON>
 * 
 * This script investigates why <PERSON><PERSON> appears to be missing
 * $281.25 in commissions when he should have TTTFOUNDER as sponsor.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ Missing SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function investigateFrancois() {
  try {
    console.log('🔍 Investigating Francois Case\n');
    
    // Find Francois
    const { data: francois, error: francoisError } = await supabase
      .from('users')
      .select('*')
      .eq('username', '<PERSON><PERSON>')
      .single();
    
    if (francoisError) {
      console.error('❌ Francois not found:', francoisError);
      return;
    }
    
    console.log(`👤 Francois Details:`);
    console.log(`   ID: ${francois.id}`);
    console.log(`   Username: ${francois.username}`);
    console.log(`   Full Name: ${francois.full_name}`);
    console.log(`   Email: ${francois.email}`);
    console.log(`   Created: ${francois.created_at}`);
    
    // Get ALL of Francois's share purchases
    const { data: purchases, error: purchaseError } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .eq('user_id', francois.id)
      .order('created_at', { ascending: false });
    
    if (purchaseError) {
      console.error('❌ Error fetching Francois purchases:', purchaseError);
      return;
    }
    
    console.log(`\n📈 Francois's Share Purchases (${purchases.length} total):`);
    purchases.forEach((purchase, index) => {
      console.log(`   ${index + 1}. Purchase ID: ${purchase.id}`);
      console.log(`      Shares: ${purchase.shares_purchased}`);
      console.log(`      Amount: $${purchase.total_amount}`);
      console.log(`      Price per share: $${purchase.price_per_share || 'N/A'}`);
      console.log(`      Status: ${purchase.status}`);
      console.log(`      Created: ${purchase.created_at}`);
      console.log(`      Payment Method: ${purchase.payment_method || 'N/A'}`);
      console.log('');
    });
    
    // Get Francois's referral relationships
    const { data: referrals, error: refError } = await supabase
      .from('referrals')
      .select(`
        *,
        referrer:users!referrer_id(id, username, full_name)
      `)
      .eq('referred_id', francois.id);
    
    if (refError) {
      console.error('❌ Error fetching Francois referrals:', refError);
    } else {
      console.log(`🔗 Francois's Referral Relationships (${referrals.length} total):`);
      referrals.forEach((ref, index) => {
        console.log(`   ${index + 1}. Referrer: ${ref.referrer?.full_name || ref.referrer?.username} (ID: ${ref.referrer_id})`);
        console.log(`      Status: ${ref.status}, Rate: ${ref.commission_rate}%`);
        console.log(`      Created: ${ref.created_at}`);
        console.log('');
      });
    }
    
    // Get ALL commission transactions for Francois
    const { data: commissions, error: commError } = await supabase
      .from('commission_transactions')
      .select(`
        *,
        referrer:users!referrer_id(id, username, full_name)
      `)
      .eq('referred_id', francois.id)
      .order('payment_date', { ascending: false });
    
    if (commError) {
      console.error('❌ Error fetching Francois commissions:', commError);
    } else {
      console.log(`💸 Commission Transactions for Francois (${commissions.length} total):`);
      commissions.forEach((comm, index) => {
        console.log(`   ${index + 1}. To: ${comm.referrer?.full_name || comm.referrer?.username} (ID: ${comm.referrer_id})`);
        console.log(`      USDT: $${comm.usdt_commission}, Shares: ${comm.share_commission}`);
        console.log(`      Purchase Amount: $${comm.share_purchase_amount}`);
        console.log(`      Status: ${comm.status}`);
        console.log(`      Date: ${comm.payment_date}`);
        console.log('');
      });
    }
    
    // Check payment transactions for Francois
    const { data: payments, error: payError } = await supabase
      .from('crypto_payment_transactions')
      .select('*')
      .eq('user_id', francois.id)
      .order('created_at', { ascending: false });
    
    if (payError) {
      console.error('❌ Error fetching Francois payments:', payError);
    } else {
      console.log(`💳 Payment Transactions for Francois (${payments.length} total):`);
      payments.forEach((payment, index) => {
        console.log(`   ${index + 1}. Amount: $${payment.amount}, Status: ${payment.status}`);
        console.log(`      Network: ${payment.network}, Currency: ${payment.currency}`);
        console.log(`      Created: ${payment.created_at}`);
        console.log(`      Approved: ${payment.approved_at || 'Not approved'}`);
        console.log('');
      });
    }
    
    // Analysis
    console.log('🔍 Analysis:');
    
    const totalPurchaseAmount = purchases.reduce((sum, p) => sum + (p.total_amount || 0), 0);
    const totalShares = purchases.reduce((sum, p) => sum + (p.shares_purchased || 0), 0);
    const totalCommissionAmount = commissions.reduce((sum, c) => sum + (c.share_purchase_amount || 0), 0);
    
    console.log(`   Total Purchase Amount: $${totalPurchaseAmount}`);
    console.log(`   Total Shares: ${totalShares}`);
    console.log(`   Total Commission Coverage: $${totalCommissionAmount}`);
    console.log(`   Missing Commission Coverage: $${totalPurchaseAmount - totalCommissionAmount}`);
    
    if (totalPurchaseAmount - totalCommissionAmount > 0) {
      console.log(`\n⚠️  Expected Commission (15%): $${(totalPurchaseAmount - totalCommissionAmount) * 0.15}`);
      console.log(`   Expected Shares (15%): ${(totalShares - commissions.reduce((sum, c) => sum + (c.share_purchase_amount / 5), 0)) * 0.15}`);
    }
    
    // Check the specific $1875 purchase
    const largePurchase = purchases.find(p => p.total_amount === 1875);
    if (largePurchase) {
      console.log(`\n🎯 Specific $1875 Purchase Analysis:`);
      console.log(`   Purchase Date: ${largePurchase.created_at}`);
      console.log(`   Payment Method: ${largePurchase.payment_method}`);
      
      // Check if there's a commission for this specific purchase
      const matchingCommission = commissions.find(c => 
        Math.abs(c.share_purchase_amount - 1875) < 0.01 &&
        new Date(c.payment_date) >= new Date(largePurchase.created_at)
      );
      
      if (matchingCommission) {
        console.log(`   ✅ Commission EXISTS for this purchase`);
        console.log(`   Commission: $${matchingCommission.usdt_commission} USDT + ${matchingCommission.share_commission} shares`);
        console.log(`   To: ${matchingCommission.referrer?.full_name || matchingCommission.referrer?.username}`);
      } else {
        console.log(`   ❌ NO commission found for this $1875 purchase`);
        console.log(`   Expected commission: $${1875 * 0.15} USDT + ${750 * 0.15} shares`);
      }
    }
    
  } catch (error) {
    console.error('❌ Investigation failed:', error);
  }
}

investigateFrancois();
