// Admin API endpoint for meeting management (CRUD operations)
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase configuration for admin meetings API');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Check if user is admin
async function checkAdminAccess(req) {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { isAdmin: false, error: 'No authorization token provided' };
  }

  const token = authHeader.substring(7);
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      return { isAdmin: false, error: 'Invalid token' };
    }

    // Check if user is admin
    const { data: adminUser, error: adminError } = await supabase
      .from('admin_users')
      .select('id, role, is_active')
      .eq('user_id', user.id)
      .eq('is_active', true)
      .single();

    if (adminError || !adminUser) {
      return { isAdmin: false, error: 'Admin access required' };
    }

    return { isAdmin: true, user, adminUser };
  } catch (error) {
    return { isAdmin: false, error: 'Authentication failed' };
  }
}

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Check admin access
  const { isAdmin, user, adminUser, error } = await checkAdminAccess(req);
  if (!isAdmin) {
    return res.status(401).json({ error: error || 'Admin access required' });
  }

  try {
    switch (req.method) {
      case 'GET':
        return await handleGetMeetings(req, res);
      case 'POST':
        return await handleCreateMeeting(req, res, user);
      case 'PUT':
        return await handleUpdateMeeting(req, res, user);
      case 'DELETE':
        return await handleDeleteMeeting(req, res);
      default:
        return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('❌ Admin meetings API error:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      details: error.message 
    });
  }
}

async function handleGetMeetings(req, res) {
  const { status, limit = 50, offset = 0 } = req.query;

  let query = supabase
    .from('meetings')
    .select(`
      id,
      title,
      topic,
      description,
      meeting_date,
      meeting_time,
      timezone,
      duration_minutes,
      max_attendees,
      current_attendees,
      meeting_url,
      meeting_password,
      status,
      created_by,
      created_at,
      updated_at
    `)
    .order('meeting_date', { ascending: false })
    .order('meeting_time', { ascending: false })
    .range(offset, offset + limit - 1);

  if (status && status !== 'all') {
    query = query.eq('status', status);
  }

  const { data: meetings, error } = await query;

  if (error) {
    console.error('❌ Error fetching meetings:', error);
    return res.status(500).json({ 
      error: 'Failed to fetch meetings',
      details: error.message 
    });
  }

  return res.status(200).json({
    success: true,
    meetings: meetings || [],
    count: meetings?.length || 0
  });
}

async function handleCreateMeeting(req, res, user) {
  const {
    title,
    topic,
    description,
    meetingDate,
    meetingTime,
    timezone = 'SAST',
    durationMinutes = 60,
    maxAttendees,
    meetingUrl,
    meetingPassword
  } = req.body;

  // Validation
  if (!title || !topic || !meetingDate || !meetingTime || !maxAttendees) {
    return res.status(400).json({ 
      error: 'Missing required fields',
      details: 'Title, topic, date, time, and max attendees are required'
    });
  }

  if (maxAttendees < 1 || maxAttendees > 1000) {
    return res.status(400).json({ 
      error: 'Invalid max attendees',
      details: 'Max attendees must be between 1 and 1000'
    });
  }

  // Check if meeting is in the future
  const meetingDateTime = new Date(`${meetingDate}T${meetingTime}`);
  if (meetingDateTime <= new Date()) {
    return res.status(400).json({ 
      error: 'Invalid meeting time',
      details: 'Meeting must be scheduled for a future date and time'
    });
  }

  const { data: meeting, error } = await supabase
    .from('meetings')
    .insert({
      title: title.trim(),
      topic: topic.trim(),
      description: description?.trim() || null,
      meeting_date: meetingDate,
      meeting_time: meetingTime,
      timezone,
      duration_minutes: durationMinutes,
      max_attendees: maxAttendees,
      meeting_url: meetingUrl?.trim() || null,
      meeting_password: meetingPassword?.trim() || null,
      created_by: user.id,
      status: 'scheduled'
    })
    .select()
    .single();

  if (error) {
    console.error('❌ Error creating meeting:', error);
    return res.status(500).json({ 
      error: 'Failed to create meeting',
      details: error.message 
    });
  }

  console.log(`✅ Meeting created successfully: ${meeting.title}`);

  return res.status(201).json({
    success: true,
    meeting
  });
}

async function handleUpdateMeeting(req, res, user) {
  const { id, ...updateData } = req.body;

  if (!id) {
    return res.status(400).json({ 
      error: 'Missing meeting ID',
      details: 'Meeting ID is required for updates'
    });
  }

  // Remove undefined values
  const cleanUpdateData = Object.fromEntries(
    Object.entries(updateData).filter(([_, value]) => value !== undefined)
  );

  const { data: meeting, error } = await supabase
    .from('meetings')
    .update(cleanUpdateData)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error('❌ Error updating meeting:', error);
    return res.status(500).json({ 
      error: 'Failed to update meeting',
      details: error.message 
    });
  }

  console.log(`✅ Meeting updated successfully: ${meeting.title}`);

  return res.status(200).json({
    success: true,
    meeting
  });
}

async function handleDeleteMeeting(req, res) {
  const { id } = req.query;

  if (!id) {
    return res.status(400).json({ 
      error: 'Missing meeting ID',
      details: 'Meeting ID is required for deletion'
    });
  }

  const { error } = await supabase
    .from('meetings')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('❌ Error deleting meeting:', error);
    return res.status(500).json({ 
      error: 'Failed to delete meeting',
      details: error.message 
    });
  }

  console.log(`✅ Meeting deleted successfully: ${id}`);

  return res.status(200).json({
    success: true,
    message: 'Meeting deleted successfully'
  });
}
