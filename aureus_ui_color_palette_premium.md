# 🎨 Aureus Alliance Holdings — Premium Investor-Grade UI Palette

Luxury, corporate-professional design with **neon gold** and **neon blue** highlights. Optimized for compact UI and seamless dark/light switching.

---

## Brand Colors (Dark & Light)

| Usage | Dark Theme | Light Theme | Notes |
|---|---|---|---|
| Primary Background | `#050505` (Cinematic Black) | `#FAFAFA` (Luxury Ivory) | Core canvas |
| Secondary Background / Cards | `#121212` (Deep Charcoal) | `#FFFFFF` (Crystal White) | Surfaces & cards |
| Glass Overlay | `rgba(255,255,255,0.04)` | `rgba(0,0,0,0.02)` | Subtle gloss |
| Primary Text | `#FFFFFF` | `#111111` | Headlines, KPIs |
| Secondary Text | `#B0B0B0` | `#4E4E4E` | Body, meta |
| Accent Gold (neon) | `#FFD700` | `#FFD700` | Consistent brand gold |
| Accent Blue (neon) | `#00BFFF` | `#007BFF` | Active states, links |
| Accent Emerald | `#2ECC71` | `#27AE60` | Positive deltas |
| Accent Copper | `#E07B39` | `#B75E2A` | Warnings/neutral |
| CTA Button | Text `#FFD700` on bg `#121212` | Text `#FFFFFF` on bg `#FFD700` | Primary calls to action |
| Borders & Dividers | `#1E1E1E` | `#E2E2E2` | Hairlines |
| Shadows | `0px 0px 24px rgba(255, 215, 0, 0.15)` | `0 4px 12px rgba(0,0,0,0.08)` | Depth cues |
| Glow FX | `0 0 18px #FFD70066` | `0 0 10px #FFD70033` | Highlights & focus |

---

## Accessibility — Contrast Ratios

| Pairing | FG | BG | Ratio | WCAG |
|---|---|---|---|---|
| Dark • Primary text on primary bg | `#FFFFFF` | `#050505` | **20.38** | AAA (normal), AA (normal), AAA (large), AA (large) |
| Dark • Secondary text on primary bg | `#B0B0B0` | `#050505` | **9.40** | AAA (normal), AA (normal), AAA (large), AA (large) |
| Dark • Gold text on secondary bg (CTA) | `#FFD700` | `#121212` | **13.36** | AAA (normal), AA (normal), AAA (large), AA (large) |
| Dark • Blue link on secondary bg | `#00BFFF` | `#121212` | **8.83** | AAA (normal), AA (normal), AAA (large), AA (large) |
| Light • Primary text on primary bg | `#111111` | `#FAFAFA` | **18.09** | AAA (normal), AA (normal), AAA (large), AA (large) |
| Light • Secondary text on primary bg | `#4E4E4E` | `#FAFAFA` | **7.97** | AAA (normal), AA (normal), AAA (large), AA (large) |
| Light • CTA (white text on gold bg) | `#FFFFFF` | `#FFD700` | **1.40** | — |
| Light • Blue link on white | `#007BFF` | `#FFFFFF` | **3.98** | AA (large) |
| Light • Gold text on ivory | `#FFD700` | `#FAFAFA` | **1.34** | — |

> Notes: **AA (normal)** ≥ 4.5, **AAA (normal)** ≥ 7.0, **AA (large)** ≥ 3.0, **AAA (large)** ≥ 4.5.

---

## Component Guidelines (Compact UI)

- **Typography**: 16/24 for H1, 20/28 for KPI numerals; 14/20 body; 12/16 micro.

- **Buttons**: 36–40px height, 12–16px horizontal padding; compact radii (8–10px).

- **Cards/KPIs**: Tight padding (12–16px), use **glass overlay** and **gold/blue glows** sparingly.

- **Tables**: Dense rows (40–44px), hairline dividers, row hover with **neon blue** ring.

- **Charts**: Dark canvas with gold line + subtle blue highlights; light mode invert line tones.

---

## Implementation — CSS Variables

```css

:root {

  /* Light theme */

  --bg: #FAFAFA;

  --surface: #FFFFFF;

  --text: #111111;

  --text-muted: #4E4E4E;

  --gold: #FFD700;

  --blue: #007BFF;

  --emerald: #27AE60;

  --copper: #B75E2A;

  --border: #E2E2E2;

  --shadow: 0 4px 12px rgba(0,0,0,0.08);

  --glow: 0 0 10px #FFD70033;

}


[data-theme='dark'] {

  --bg: #050505;

  --surface: #121212;

  --text: #FFFFFF;

  --text-muted: #B0B0B0;

  --gold: #FFD700;

  --blue: #00BFFF;

  --emerald: #2ECC71;

  --copper: #E07B39;

  --border: #1E1E1E;

  --shadow: 0px 0px 24px rgba(255, 215, 0, 0.15);

  --glow: 0 0 18px #FFD70066;

}


/* Example CTA (dark) */

.btn-primary { background: var(--surface); color: var(--gold); box-shadow: var(--glow); border: 1px solid var(--gold); }

.btn-primary:hover { filter: brightness(1.1); box-shadow: 0 0 28px #FFD70088; }

```

---

## Glow & Focus Tokens

- **Gold Focus Ring**: `0 0 0 2px rgba(255,215,0,.35)`

- **Blue Hover Ring**: `0 0 0 2px rgba(0,191,255,.35)`

- **KPI Emphasis**: `text-shadow: 0 0 12px #FFD70055` (use sparingly)
