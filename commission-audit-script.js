/**
 * CRITICAL COMMISSION AUDIT SCRIPT
 * 
 * This script performs a comprehensive audit of all commission transactions
 * to identify missing share commissions and calculate corrective actions.
 * 
 * AUDIT OBJECTIVES:
 * 1. Cross-reference all aureus_share_purchases with commission_transactions
 * 2. Identify missing share commissions (should be 15% of shares purchased)
 * 3. Calculate exact amounts owed to each referrer
 * 4. Generate corrective commission entries
 * 5. Update commission balances with proper audit trail
 */

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co'
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseServiceKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required')
  console.error('Please set the environment variable and run again')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

class CommissionAuditor {
  constructor() {
    this.auditResults = {
      totalPurchases: 0,
      purchasesWithReferrers: 0,
      commissionsFound: 0,
      missingShareCommissions: [],
      correctCommissions: [],
      totalMissingShareCommissions: 0,
      affectedReferrers: new Set(),
      errors: [],
      warnings: []
    }
  }

  /**
   * Run comprehensive commission audit
   */
  async runCommissionAudit() {
    console.log('🔍 CRITICAL COMMISSION AUDIT - STARTING')
    console.log('=' .repeat(60))
    console.log('Objective: Identify ALL missing share commissions in the system')
    console.log('Expected: 15% USDT + 15% shares for every purchase with referrer')
    console.log('=' .repeat(60))

    try {
      // Step 1: Get all share purchases
      await this.getAllSharePurchases()
      
      // Step 2: Cross-reference with commission transactions
      await this.crossReferenceCommissions()
      
      // Step 3: Identify missing share commissions
      await this.identifyMissingCommissions()
      
      // Step 4: Generate audit report
      this.generateAuditReport()
      
    } catch (error) {
      console.error('❌ COMMISSION AUDIT FAILED:', error)
      this.auditResults.errors.push(`Critical audit failure: ${error.message}`)
      throw error
    }
  }

  /**
   * Get all share purchases with referrer information
   */
  async getAllSharePurchases() {
    console.log('\n📊 STEP 1: Retrieving All Share Purchases')
    
    try {
      const { data: purchases, error } = await supabase
        .from('aureus_share_purchases')
        .select(`
          id,
          user_id,
          shares_purchased,
          total_amount,
          created_at,
          status,
          purchase_date
        `)
        .eq('status', 'active')
        .order('created_at', { ascending: true })

      if (error) {
        throw new Error(`Failed to get share purchases: ${error.message}`)
      }

      console.log(`📊 Found ${purchases.length} active share purchases`)
      this.auditResults.totalPurchases = purchases.length

      // Get referrer information for each purchase
      for (const purchase of purchases) {
        const { data: referralData, error: referralError } = await supabase
          .from('referrals')
          .select(`
            referrer_id,
            referred_id,
            status,
            referrer:users!referrer_id(id, username, email),
            referred:users!referred_id(id, username, email)
          `)
          .eq('referred_id', purchase.user_id)
          .eq('status', 'active')
          .single()

        if (!referralError && referralData) {
          purchase.referrer_info = referralData
          this.auditResults.purchasesWithReferrers++
        }
      }

      this.purchases = purchases
      console.log(`📊 ${this.auditResults.purchasesWithReferrers} purchases have active referrers`)
      
    } catch (error) {
      this.auditResults.errors.push(`Failed to get share purchases: ${error.message}`)
      throw error
    }
  }

  /**
   * Cross-reference purchases with commission transactions
   */
  async crossReferenceCommissions() {
    console.log('\n🔍 STEP 2: Cross-Referencing Commission Transactions')
    
    try {
      for (const purchase of this.purchases) {
        if (!purchase.referrer_info) continue

        console.log(`🔍 Checking purchase ${purchase.id} (User: ${purchase.user_id}, Amount: $${purchase.total_amount}, Shares: ${purchase.shares_purchased})`)

        // Look for existing commission transactions for this purchase
        const { data: commissions, error } = await supabase
          .from('commission_transactions')
          .select('*')
          .eq('referred_id', purchase.user_id)
          .eq('referrer_id', purchase.referrer_info.referrer_id)

        if (error) {
          this.auditResults.warnings.push(`Could not check commissions for purchase ${purchase.id}: ${error.message}`)
          continue
        }

        purchase.commission_transactions = commissions || []
        
        if (commissions && commissions.length > 0) {
          this.auditResults.commissionsFound++
          console.log(`   ✅ Found ${commissions.length} commission transaction(s)`)
          
          // Check each commission transaction
          for (const commission of commissions) {
            console.log(`   📊 Commission: USDT=$${commission.usdt_commission}, Shares=${commission.share_commission}`)
          }
        } else {
          console.log(`   ❌ NO commission transactions found`)
        }
      }
      
    } catch (error) {
      this.auditResults.errors.push(`Failed to cross-reference commissions: ${error.message}`)
      throw error
    }
  }

  /**
   * Identify missing share commissions
   */
  async identifyMissingCommissions() {
    console.log('\n🚨 STEP 3: Identifying Missing Share Commissions')
    
    try {
      for (const purchase of this.purchases) {
        if (!purchase.referrer_info) continue

        const expectedUsdtCommission = parseFloat(purchase.total_amount) * 0.15
        const expectedShareCommission = parseFloat(purchase.shares_purchased) * 0.15
        
        console.log(`\n🔍 ANALYZING PURCHASE ${purchase.id}`)
        console.log(`   Buyer: ${purchase.referrer_info.referred.username} (ID: ${purchase.user_id})`)
        console.log(`   Referrer: ${purchase.referrer_info.referrer.username} (ID: ${purchase.referrer_info.referrer_id})`)
        console.log(`   Purchase: $${purchase.total_amount} for ${purchase.shares_purchased} shares`)
        console.log(`   Expected USDT Commission: $${expectedUsdtCommission.toFixed(2)}`)
        console.log(`   Expected Share Commission: ${expectedShareCommission.toFixed(4)} shares`)

        if (!purchase.commission_transactions || purchase.commission_transactions.length === 0) {
          // No commission transactions at all - completely missing
          console.log(`   ❌ COMPLETELY MISSING: No commission transactions found`)
          
          this.auditResults.missingShareCommissions.push({
            type: 'COMPLETELY_MISSING',
            purchase_id: purchase.id,
            user_id: purchase.user_id,
            referrer_id: purchase.referrer_info.referrer_id,
            buyer_username: purchase.referrer_info.referred.username,
            buyer_email: purchase.referrer_info.referred.email,
            referrer_username: purchase.referrer_info.referrer.username,
            referrer_email: purchase.referrer_info.referrer.email,
            purchase_amount: parseFloat(purchase.total_amount),
            shares_purchased: parseFloat(purchase.shares_purchased),
            missing_usdt_commission: expectedUsdtCommission,
            missing_share_commission: expectedShareCommission,
            purchase_date: purchase.created_at
          })
          
          this.auditResults.totalMissingShareCommissions += expectedShareCommission
          this.auditResults.affectedReferrers.add(purchase.referrer_info.referrer_id)
          
        } else {
          // Check if share commissions are missing or incorrect
          let totalUsdtCommission = 0
          let totalShareCommission = 0
          
          for (const commission of purchase.commission_transactions) {
            totalUsdtCommission += parseFloat(commission.usdt_commission || '0')
            totalShareCommission += parseFloat(commission.share_commission || '0')
          }
          
          console.log(`   Found USDT Commission: $${totalUsdtCommission.toFixed(2)}`)
          console.log(`   Found Share Commission: ${totalShareCommission.toFixed(4)} shares`)
          
          const usdtDifference = Math.abs(totalUsdtCommission - expectedUsdtCommission)
          const shareDifference = Math.abs(totalShareCommission - expectedShareCommission)
          
          if (usdtDifference > 0.01 || shareDifference > 0.001) {
            console.log(`   ❌ INCORRECT COMMISSIONS DETECTED`)
            console.log(`   USDT Difference: $${(expectedUsdtCommission - totalUsdtCommission).toFixed(2)}`)
            console.log(`   Share Difference: ${(expectedShareCommission - totalShareCommission).toFixed(4)} shares`)
            
            this.auditResults.missingShareCommissions.push({
              type: 'INCORRECT_AMOUNTS',
              purchase_id: purchase.id,
              user_id: purchase.user_id,
              referrer_id: purchase.referrer_info.referrer_id,
              buyer_username: purchase.referrer_info.referred.username,
              buyer_email: purchase.referrer_info.referred.email,
              referrer_username: purchase.referrer_info.referrer.username,
              referrer_email: purchase.referrer_info.referrer.email,
              purchase_amount: parseFloat(purchase.total_amount),
              shares_purchased: parseFloat(purchase.shares_purchased),
              expected_usdt_commission: expectedUsdtCommission,
              expected_share_commission: expectedShareCommission,
              actual_usdt_commission: totalUsdtCommission,
              actual_share_commission: totalShareCommission,
              missing_usdt_commission: expectedUsdtCommission - totalUsdtCommission,
              missing_share_commission: expectedShareCommission - totalShareCommission,
              purchase_date: purchase.created_at,
              existing_commission_ids: purchase.commission_transactions.map(c => c.id)
            })
            
            this.auditResults.totalMissingShareCommissions += (expectedShareCommission - totalShareCommission)
            this.auditResults.affectedReferrers.add(purchase.referrer_info.referrer_id)
            
          } else {
            console.log(`   ✅ CORRECT: Commissions match expected amounts`)
            
            this.auditResults.correctCommissions.push({
              purchase_id: purchase.id,
              user_id: purchase.user_id,
              referrer_id: purchase.referrer_info.referrer_id,
              usdt_commission: totalUsdtCommission,
              share_commission: totalShareCommission
            })
          }
        }
      }
      
    } catch (error) {
      this.auditResults.errors.push(`Failed to identify missing commissions: ${error.message}`)
      throw error
    }
  }

  /**
   * Generate comprehensive audit report
   */
  generateAuditReport() {
    console.log('\n📋 COMPREHENSIVE COMMISSION AUDIT REPORT')
    console.log('=' .repeat(70))
    
    console.log(`\n📊 AUDIT SUMMARY:`)
    console.log(`Total Share Purchases: ${this.auditResults.totalPurchases}`)
    console.log(`Purchases with Referrers: ${this.auditResults.purchasesWithReferrers}`)
    console.log(`Commission Transactions Found: ${this.auditResults.commissionsFound}`)
    console.log(`Correct Commissions: ${this.auditResults.correctCommissions.length}`)
    console.log(`Missing/Incorrect Commissions: ${this.auditResults.missingShareCommissions.length}`)
    console.log(`Affected Referrers: ${this.auditResults.affectedReferrers.size}`)
    console.log(`Total Missing Share Commissions: ${this.auditResults.totalMissingShareCommissions.toFixed(4)} shares`)

    if (this.auditResults.missingShareCommissions.length > 0) {
      console.log(`\n🚨 MISSING COMMISSION DETAILS:`)
      console.log('=' .repeat(70))
      
      let totalMissingUsdtCommissions = 0
      let totalMissingShareCommissions = 0
      
      this.auditResults.missingShareCommissions.forEach((missing, index) => {
        console.log(`\n${index + 1}. ${missing.type}`)
        console.log(`   Purchase ID: ${missing.purchase_id}`)
        console.log(`   Buyer: ${missing.buyer_username} (${missing.buyer_email})`)
        console.log(`   Referrer: ${missing.referrer_username} (${missing.referrer_email})`)
        console.log(`   Purchase: $${missing.purchase_amount} for ${missing.shares_purchased} shares`)
        console.log(`   Purchase Date: ${missing.purchase_date}`)
        
        if (missing.type === 'COMPLETELY_MISSING') {
          console.log(`   Missing USDT Commission: $${missing.missing_usdt_commission.toFixed(2)}`)
          console.log(`   Missing Share Commission: ${missing.missing_share_commission.toFixed(4)} shares`)
          totalMissingUsdtCommissions += missing.missing_usdt_commission
          totalMissingShareCommissions += missing.missing_share_commission
        } else {
          console.log(`   Expected USDT: $${missing.expected_usdt_commission.toFixed(2)}, Actual: $${missing.actual_usdt_commission.toFixed(2)}`)
          console.log(`   Expected Shares: ${missing.expected_share_commission.toFixed(4)}, Actual: ${missing.actual_share_commission.toFixed(4)}`)
          console.log(`   Missing USDT Commission: $${missing.missing_usdt_commission.toFixed(2)}`)
          console.log(`   Missing Share Commission: ${missing.missing_share_commission.toFixed(4)} shares`)
          totalMissingUsdtCommissions += missing.missing_usdt_commission
          totalMissingShareCommissions += missing.missing_share_commission
        }
      })
      
      console.log(`\n💰 TOTAL CORRECTIONS NEEDED:`)
      console.log(`Total Missing USDT Commissions: $${totalMissingUsdtCommissions.toFixed(2)}`)
      console.log(`Total Missing Share Commissions: ${totalMissingShareCommissions.toFixed(4)} shares`)
    }

    if (this.auditResults.affectedReferrers.size > 0) {
      console.log(`\n👥 AFFECTED REFERRERS:`)
      console.log('=' .repeat(70))
      
      const referrerSummary = {}
      
      this.auditResults.missingShareCommissions.forEach(missing => {
        if (!referrerSummary[missing.referrer_id]) {
          referrerSummary[missing.referrer_id] = {
            username: missing.referrer_username,
            email: missing.referrer_email,
            missing_usdt: 0,
            missing_shares: 0,
            affected_purchases: 0
          }
        }
        
        referrerSummary[missing.referrer_id].missing_usdt += (missing.missing_usdt_commission || 0)
        referrerSummary[missing.referrer_id].missing_shares += (missing.missing_share_commission || 0)
        referrerSummary[missing.referrer_id].affected_purchases++
      })
      
      Object.entries(referrerSummary).forEach(([referrerId, summary]) => {
        console.log(`\n👤 ${summary.username} (ID: ${referrerId})`)
        console.log(`   Email: ${summary.email}`)
        console.log(`   Affected Purchases: ${summary.affected_purchases}`)
        console.log(`   Missing USDT Commission: $${summary.missing_usdt.toFixed(2)}`)
        console.log(`   Missing Share Commission: ${summary.missing_shares.toFixed(4)} shares`)
      })
    }

    if (this.auditResults.errors.length > 0) {
      console.log(`\n❌ ERRORS ENCOUNTERED:`)
      this.auditResults.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`)
      })
    }

    if (this.auditResults.warnings.length > 0) {
      console.log(`\n⚠️  WARNINGS:`)
      this.auditResults.warnings.forEach((warning, index) => {
        console.log(`   ${index + 1}. ${warning}`)
      })
    }

    console.log(`\n🎯 AUDIT CONCLUSION:`)
    if (this.auditResults.missingShareCommissions.length === 0) {
      console.log(`✅ NO MISSING COMMISSIONS FOUND - System is mathematically consistent`)
    } else {
      console.log(`🚨 ${this.auditResults.missingShareCommissions.length} COMMISSION DISCREPANCIES IDENTIFIED`)
      console.log(`🚨 IMMEDIATE CORRECTIVE ACTION REQUIRED`)
      console.log(`🚨 Total users affected: ${this.auditResults.affectedReferrers.size}`)
      console.log(`🚨 Total missing share commissions: ${this.auditResults.totalMissingShareCommissions.toFixed(4)} shares`)
    }

    console.log(`\n📋 NEXT STEPS:`)
    console.log(`1. 🔧 Create corrective commission_transactions entries`)
    console.log(`2. 💰 Update commission_balances for affected referrers`)
    console.log(`3. 📧 Send notification emails to affected users`)
    console.log(`4. 📊 Verify mathematical consistency after corrections`)
    console.log(`5. 🔍 Implement monitoring to prevent future discrepancies`)
  }

  /**
   * Export audit results for correction script
   */
  exportAuditResults() {
    return {
      missingCommissions: this.auditResults.missingShareCommissions,
      affectedReferrers: Array.from(this.auditResults.affectedReferrers),
      totalMissingShareCommissions: this.auditResults.totalMissingShareCommissions,
      auditSummary: {
        totalPurchases: this.auditResults.totalPurchases,
        purchasesWithReferrers: this.auditResults.purchasesWithReferrers,
        commissionsFound: this.auditResults.commissionsFound,
        correctCommissions: this.auditResults.correctCommissions.length,
        missingCommissions: this.auditResults.missingShareCommissions.length
      }
    }
  }
}

/**
 * Main execution
 */
async function runCommissionAudit() {
  const auditor = new CommissionAuditor()
  await auditor.runCommissionAudit()
  return auditor.exportAuditResults()
}

// Export for use in other modules
export { CommissionAuditor, runCommissionAudit }

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runCommissionAudit().catch(console.error)
}
