// Certificate Service - Database and Storage Integration
// Handles certificate generation, storage, and database operations

import { supabase } from './supabase';
import CertificateGenerator, { GenerationResult, GenerationOptions } from './certificateGenerator';
import { CertificateData } from './certificateTemplate';
import { PROFESSIONAL_CERTIFICATE_TEMPLATE } from './professionalCertificateTemplate';
import { notificationService } from './notificationService';

export interface CertificateRecord {
  id: string;
  user_id: number;
  purchase_id: string;
  certificate_number: string;
  shares_count: number;
  issue_date: string;
  status: 'issued' | 'revoked' | 'transferred';
  certificate_data: {
    file_url: string;
    generation_metadata: any;
    generation_options: GenerationOptions;
    generated_at: string;
    file_size: number;
    security_hash: string;
  };
  created_at: string;
  updated_at: string;
}

export interface BatchGenerationRequest {
  purchaseIds: string[];
  options: GenerationOptions;
  notifyUsers: boolean;
  generateZip: boolean;
}

export interface BatchGenerationResult {
  success: boolean;
  totalRequested: number;
  successfullyGenerated: number;
  failed: number;
  certificates: CertificateRecord[];
  errors: string[];
  zipFileUrl?: string;
  executionTime: number;
}

export class CertificateService {
  private generator: CertificateGenerator;

  constructor() {
    this.generator = new CertificateGenerator(); // Use default template for testing
  }

  /**
   * Generate certificate for a single purchase
   */
  async generateCertificateForPurchase(
    purchaseId: string,
    options: GenerationOptions = {
      format: 'pdf',
      quality: 'high',
      includeMetadata: true,
      watermark: true,
      compression: true
    }
  ): Promise<{ success: boolean; certificate?: CertificateRecord; error?: string }> {
    try {
      // Get purchase data with user and KYC information
      const purchaseData = await this.getPurchaseWithUserData(purchaseId);
      if (!purchaseData) {
        return { success: false, error: 'Purchase not found' };
      }

      // Check if certificate already exists
      const existingCertificate = await this.getCertificateByPurchaseId(purchaseId);
      if (existingCertificate) {
        return { success: false, error: 'Certificate already exists for this purchase' };
      }

      // Generate unique certificate number
      const certificateNumber = await this.generateUniqueCertificateNumber();

      // Prepare certificate data
      const certificateData: CertificateData = {
        certificateNumber,
        userFullName: purchaseData.user.full_name,
        sharesQuantity: purchaseData.shares_purchased,
        purchaseAmount: purchaseData.total_amount,
        pricePerShare: purchaseData.total_amount / purchaseData.shares_purchased,
        issueDate: new Date().toISOString(),
        kycVerified: purchaseData.kyc_info?.kyc_status === 'completed',
        userAddress: purchaseData.user.country_of_residence || 'Not specified',
        userId: purchaseData.user.id,
        purchaseId: purchaseData.id,
        verificationUrl: this.generateVerificationUrl(certificateNumber, purchaseData.user.id)
      };

      // Generate certificate
      const generationResult = await this.generator.generateCertificate(certificateData, options);
      
      if (!generationResult.success || !generationResult.pdfBuffer) {
        return { success: false, error: generationResult.error || 'Certificate generation failed' };
      }

      // Upload to storage
      const fileUrl = await this.uploadCertificateFile(
        certificateNumber,
        purchaseData.user.id,
        generationResult.pdfBuffer,
        'pdf'
      );

      if (!fileUrl) {
        return { success: false, error: 'Failed to upload certificate file' };
      }

      // Save to database
      const certificateRecord = await this.saveCertificateToDatabase({
        user_id: purchaseData.user.id,
        purchase_id: purchaseId,
        certificate_number: certificateNumber,
        shares_count: purchaseData.shares_purchased,
        file_url: fileUrl,
        generation_metadata: generationResult.metadata,
        generation_options: options,
        file_size: generationResult.fileSize,
        security_hash: generationResult.metadata.securityHash
      });

      if (!certificateRecord) {
        return { success: false, error: 'Failed to save certificate to database' };
      }

      // Send notification to user
      await this.notifyUserCertificateReady(purchaseData.user.id, certificateNumber, fileUrl);

      return { success: true, certificate: certificateRecord };

    } catch (error) {
      console.error('Error generating certificate:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error occurred' 
      };
    }
  }

  /**
   * Generate certificates for multiple purchases (batch processing)
   */
  async generateBatchCertificates(request: BatchGenerationRequest): Promise<BatchGenerationResult> {
    const startTime = performance.now();
    const result: BatchGenerationResult = {
      success: false,
      totalRequested: request.purchaseIds.length,
      successfullyGenerated: 0,
      failed: 0,
      certificates: [],
      errors: [],
      executionTime: 0
    };

    try {
      // Process each purchase
      for (const purchaseId of request.purchaseIds) {
        try {
          const certificateResult = await this.generateCertificateForPurchase(purchaseId, request.options);
          
          if (certificateResult.success && certificateResult.certificate) {
            result.certificates.push(certificateResult.certificate);
            result.successfullyGenerated++;
          } else {
            result.failed++;
            result.errors.push(`Purchase ${purchaseId}: ${certificateResult.error}`);
          }
        } catch (error) {
          result.failed++;
          result.errors.push(`Purchase ${purchaseId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Generate ZIP file if requested and we have successful certificates
      if (request.generateZip && result.certificates.length > 0) {
        result.zipFileUrl = await this.createCertificateZip(result.certificates);
      }

      const endTime = performance.now();
      result.executionTime = endTime - startTime;
      result.success = result.successfullyGenerated > 0;

      return result;

    } catch (error) {
      const endTime = performance.now();
      result.executionTime = endTime - startTime;
      result.errors.push(`Batch processing error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return result;
    }
  }

  /**
   * Get purchase data with user and KYC information
   */
  private async getPurchaseWithUserData(purchaseId: string): Promise<any> {
    // Get purchase data
    const { data: purchaseData, error: purchaseError } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .eq('id', purchaseId)
      .single();

    if (purchaseError) {
      console.error('Error fetching purchase data:', purchaseError);
      return null;
    }

    // Get user data
    const { data: userData } = await supabase
      .from('users')
      .select('id, full_name, email, phone, country_of_residence')
      .eq('id', purchaseData.user_id)
      .single();

    // Get KYC data
    const { data: kycData } = await supabase
      .from('kyc_information')
      .select('kyc_status, kyc_completed_at')
      .eq('user_id', purchaseData.user_id)
      .single();

    return {
      ...purchaseData,
      user: userData || { id: purchaseData.user_id, full_name: 'Unknown', email: 'Unknown' },
      kyc_info: kycData
    };
  }

  /**
   * Check if certificate already exists for purchase
   */
  private async getCertificateByPurchaseId(purchaseId: string): Promise<CertificateRecord | null> {
    const { data, error } = await supabase
      .from('certificates')
      .select('*')
      .eq('purchase_id', purchaseId)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error checking existing certificate:', error);
    }

    return data;
  }

  /**
   * Generate unique certificate number
   */
  private async generateUniqueCertificateNumber(): Promise<string> {
    const year = new Date().getFullYear();
    let attempts = 0;
    const maxAttempts = 100;

    while (attempts < maxAttempts) {
      const randomNumber = Math.floor(Math.random() * 999999);
      const certificateNumber = `AUR-${year}-${String(randomNumber).padStart(6, '0')}`;

      // Check if this number already exists
      const { data, error } = await supabase
        .from('certificates')
        .select('id')
        .eq('certificate_number', certificateNumber)
        .single();

      if (error && error.code === 'PGRST116') {
        // No existing certificate with this number
        return certificateNumber;
      }

      attempts++;
    }

    throw new Error('Unable to generate unique certificate number after maximum attempts');
  }

  /**
   * Generate verification URL
   */
  private generateVerificationUrl(certificateNumber: string, userId: number): string {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://aureusafrica.com';
    return `${baseUrl}/verify-certificate/${certificateNumber}?user=${userId}`;
  }

  /**
   * Upload certificate file to storage
   */
  private async uploadCertificateFile(
    certificateNumber: string,
    userId: number,
    buffer: Buffer,
    format: string
  ): Promise<string | null> {
    try {
      const fileName = `certificates/certificate_${certificateNumber}_${userId}.${format}`;
      
      const { data, error } = await supabase.storage
        .from('proof')
        .upload(fileName, buffer, {
          cacheControl: '31536000', // 1 year cache
          upsert: true,
          contentType: format === 'pdf' ? 'application/pdf' : 'image/png'
        });

      if (error) {
        console.error('Storage upload error:', error);
        return null;
      }

      const { data: { publicUrl } } = supabase.storage
        .from('proof')
        .getPublicUrl(fileName);

      return publicUrl;

    } catch (error) {
      console.error('Error uploading certificate file:', error);
      return null;
    }
  }

  /**
   * Save certificate record to database
   */
  private async saveCertificateToDatabase(data: {
    user_id: number;
    purchase_id: string;
    certificate_number: string;
    shares_count: number;
    file_url: string;
    generation_metadata: any;
    generation_options: GenerationOptions;
    file_size: number;
    security_hash: string;
  }): Promise<CertificateRecord | null> {
    try {
      const { data: certificate, error } = await supabase
        .from('certificates')
        .insert({
          user_id: data.user_id,
          purchase_id: data.purchase_id,
          certificate_number: data.certificate_number,
          shares_count: data.shares_count,
          status: 'issued',
          certificate_data: {
            file_url: data.file_url,
            generation_metadata: data.generation_metadata,
            generation_options: data.generation_options,
            generated_at: new Date().toISOString(),
            file_size: data.file_size,
            security_hash: data.security_hash
          }
        })
        .select()
        .single();

      if (error) {
        console.error('Database save error:', error);
        return null;
      }

      return certificate;

    } catch (error) {
      console.error('Error saving certificate to database:', error);
      return null;
    }
  }

  /**
   * Create ZIP file containing multiple certificates
   */
  private async createCertificateZip(certificates: CertificateRecord[]): Promise<string | null> {
    try {
      // This would require implementing ZIP creation and upload
      // For now, return null - can be implemented based on specific requirements
      return null;
    } catch (error) {
      console.error('Error creating certificate ZIP:', error);
      return null;
    }
  }

  /**
   * Send notification to user when certificate is ready
   */
  private async notifyUserCertificateReady(
    userId: number,
    certificateNumber: string,
    fileUrl: string
  ): Promise<void> {
    try {
      await notificationService.createNotification({
        user_id: userId,
        notification_type: 'share_purchase',
        title: '📜 Your Share Certificate is Ready!',
        message: `Your share certificate ${certificateNumber} has been generated and is now available for download. This certificate represents your legal ownership of shares in Aureus Africa.`,
        metadata: {
          certificate_number: certificateNumber,
          file_url: fileUrl,
          certificate_type: 'share_certificate'
        },
        priority: 'normal'
      });
    } catch (error) {
      console.error('Error sending certificate notification:', error);
    }
  }

  /**
   * Verify certificate authenticity
   */
  async verifyCertificate(certificateNumber: string): Promise<{
    valid: boolean;
    certificate?: CertificateRecord;
    error?: string;
  }> {
    try {
      const { data, error } = await supabase
        .from('certificates')
        .select(`
          *,
          user:users(full_name, email),
          purchase:aureus_share_purchases(total_amount, created_at)
        `)
        .eq('certificate_number', certificateNumber)
        .eq('status', 'issued')
        .single();

      if (error) {
        return { valid: false, error: 'Certificate not found' };
      }

      return { valid: true, certificate: data };

    } catch (error) {
      return { 
        valid: false, 
        error: error instanceof Error ? error.message : 'Verification failed' 
      };
    }
  }

  /**
   * Get certificate statistics
   */
  async getCertificateStats(): Promise<{
    total: number;
    issued: number;
    revoked: number;
    totalShares: number;
    totalValue: number;
  }> {
    try {
      const { data: certificates, error } = await supabase
        .from('certificates')
        .select('status, shares_count, purchase_id');

      if (error) {
        throw error;
      }

      // Get purchase amounts separately
      const purchaseIds = certificates.map(c => c.purchase_id).filter(Boolean);
      let totalValue = 0;

      if (purchaseIds.length > 0) {
        const { data: purchases } = await supabase
          .from('aureus_share_purchases')
          .select('id, total_amount')
          .in('id', purchaseIds);

        const purchaseMap = new Map(purchases?.map(p => [p.id, p.total_amount]) || []);
        totalValue = certificates.reduce((sum, c) => sum + (purchaseMap.get(c.purchase_id) || 0), 0);
      }

      const stats = {
        total: certificates.length,
        issued: certificates.filter(c => c.status === 'issued').length,
        revoked: certificates.filter(c => c.status === 'revoked').length,
        totalShares: certificates.reduce((sum, c) => sum + c.shares_count, 0),
        totalValue
      };

      return stats;

    } catch (error) {
      console.error('Error getting certificate stats:', error);
      return {
        total: 0,
        issued: 0,
        revoked: 0,
        totalShares: 0,
        totalValue: 0
      };
    }
  }
}

// Export singleton instance
export const certificateService = new CertificateService();
export default certificateService;
