/**
 * TEST: Commission Processing for Conversion Request Approvals
 * 
 * This test verifies that when an admin approves a conversion request,
 * the system automatically processes referral commissions (15% USDT + 15% shares)
 * to the sponsor, just like regular share purchases.
 */

import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const testConversionCommissionProcessing = async () => {
  console.log('🧪 TESTING CONVERSION COMMISSION PROCESSING');
  console.log('===========================================\n');

  try {
    // Step 1: Create test users (sponsor and referred user)
    console.log('1️⃣ Setting up test users...');
    
    const testTimestamp = Date.now();
    const sponsorEmail = `sponsor_${testTimestamp}@test.com`;
    const referredEmail = `referred_${testTimestamp}@test.com`;
    
    // Create sponsor user
    const { data: sponsor, error: sponsorError } = await supabase
      .from('users')
      .insert({
        email: sponsorEmail,
        username: `sponsor_${testTimestamp}`,
        full_name: 'Test Sponsor',
        password_hash: 'test_hash',
        is_active: true,
        email_verified: true
      })
      .select()
      .single();

    if (sponsorError) throw sponsorError;

    // Create referred user
    const { data: referredUser, error: referredError } = await supabase
      .from('users')
      .insert({
        email: referredEmail,
        username: `referred_${testTimestamp}`,
        full_name: 'Test Referred User',
        password_hash: 'test_hash',
        is_active: true,
        email_verified: true
      })
      .select()
      .single();

    if (referredError) throw referredError;

    console.log(`✅ Created sponsor: ${sponsor.username} (ID: ${sponsor.id})`);
    console.log(`✅ Created referred user: ${referredUser.username} (ID: ${referredUser.id})`);

    // Step 2: Create referral relationship
    console.log('\n2️⃣ Creating referral relationship...');
    
    const { data: referral, error: referralError } = await supabase
      .from('referrals')
      .insert({
        referrer_id: sponsor.id,
        referred_id: referredUser.id,
        referral_code: `TEST_${testTimestamp}`,
        commission_rate: 15.00,
        total_commission: 0,
        status: 'active',
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (referralError) throw referralError;
    console.log(`✅ Created referral relationship: ${sponsor.username} → ${referredUser.username}`);

    // Step 3: Create commission balances for both users
    console.log('\n3️⃣ Setting up commission balances...');
    
    const balanceData = [
      {
        user_id: sponsor.id,
        usdt_balance: 100.00, // Starting balance
        share_balance: 50.00,
        total_earned_usdt: 100.00,
        total_earned_shares: 50.00,
        last_updated: new Date().toISOString()
      },
      {
        user_id: referredUser.id,
        usdt_balance: 200.00, // User has USDT to convert
        share_balance: 0.00,
        total_earned_usdt: 200.00,
        total_earned_shares: 0.00,
        last_updated: new Date().toISOString()
      }
    ];

    const { error: balanceError } = await supabase
      .from('commission_balances')
      .insert(balanceData);

    if (balanceError) throw balanceError;
    console.log(`✅ Created commission balances for both users`);

    // Step 4: Get current active phase
    console.log('\n4️⃣ Getting current investment phase...');
    
    const { data: currentPhase, error: phaseError } = await supabase
      .from('investment_phases')
      .select('*')
      .eq('is_active', true)
      .single();

    if (phaseError || !currentPhase) {
      console.log('⚠️ No active phase found, using fallback');
      // Create a test phase
      const { data: testPhase, error: testPhaseError } = await supabase
        .from('investment_phases')
        .insert({
          phase_name: 'Test Phase',
          price_per_share: 5.00,
          max_shares: 10000,
          shares_sold: 0,
          is_active: true,
          start_date: new Date().toISOString(),
          end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
        })
        .select()
        .single();

      if (testPhaseError) throw testPhaseError;
      currentPhase = testPhase;
    }

    console.log(`✅ Using phase: ${currentPhase.phase_name} ($${currentPhase.price_per_share}/share)`);

    // Step 5: Create a conversion request
    console.log('\n5️⃣ Creating conversion request...');
    
    const conversionAmount = 100.00; // $100 USDT to convert
    const sharesRequested = Math.floor(conversionAmount / currentPhase.price_per_share);
    
    const { data: conversionRequest, error: conversionError } = await supabase
      .from('commission_conversions')
      .insert({
        user_id: referredUser.id,
        shares_requested: sharesRequested,
        usdt_amount: conversionAmount,
        share_price: currentPhase.price_per_share,
        phase_id: currentPhase.id,
        phase_number: currentPhase.phase_number || 1,
        status: 'pending',
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (conversionError) throw conversionError;
    
    console.log(`✅ Created conversion request:`);
    console.log(`   • ID: ${conversionRequest.id}`);
    console.log(`   • User: ${referredUser.username}`);
    console.log(`   • Amount: $${conversionAmount}`);
    console.log(`   • Shares: ${sharesRequested}`);
    console.log(`   • Status: ${conversionRequest.status}`);

    // Step 6: Get initial commission balances
    console.log('\n6️⃣ Recording initial commission balances...');
    
    const { data: initialSponsorBalance } = await supabase
      .from('commission_balances')
      .select('*')
      .eq('user_id', sponsor.id)
      .single();

    console.log(`📊 Initial sponsor balance:`);
    console.log(`   • USDT: $${initialSponsorBalance.usdt_balance}`);
    console.log(`   • Shares: ${initialSponsorBalance.share_balance}`);

    console.log('\n🎯 TEST SETUP COMPLETE');
    console.log('=====================================');
    console.log('✅ Sponsor created with referral relationship');
    console.log('✅ Referred user created with commission balance');
    console.log('✅ Conversion request created and ready for approval');
    console.log('✅ Initial balances recorded for comparison');
    
    console.log('\n📋 NEXT STEPS FOR MANUAL TESTING:');
    console.log('1. Open the Admin Dashboard');
    console.log('2. Navigate to Commission & Withdrawals → Commission Conversions');
    console.log(`3. Find conversion request ID: ${conversionRequest.id}`);
    console.log(`4. Approve the conversion for user: ${referredUser.username}`);
    console.log('5. Verify that commission is processed to sponsor');
    
    console.log('\n💰 EXPECTED COMMISSION CALCULATION:');
    console.log(`   • Purchase Amount: $${conversionAmount}`);
    console.log(`   • Shares: ${sharesRequested}`);
    console.log(`   • USDT Commission (15%): $${(conversionAmount * 0.15).toFixed(2)}`);
    console.log(`   • Share Commission (15%): ${(sharesRequested * 0.15).toFixed(4)} shares`);
    
    console.log('\n🔍 VERIFICATION QUERIES:');
    console.log(`-- Check sponsor's updated balance:`);
    console.log(`SELECT * FROM commission_balances WHERE user_id = ${sponsor.id};`);
    console.log(`-- Check commission transaction:`);
    console.log(`SELECT * FROM commission_transactions WHERE referrer_id = ${sponsor.id} ORDER BY created_at DESC LIMIT 1;`);
    console.log(`-- Check notifications:`);
    console.log(`SELECT * FROM notifications WHERE user_id = ${sponsor.id} ORDER BY created_at DESC LIMIT 1;`);

    return {
      sponsor,
      referredUser,
      conversionRequest,
      expectedCommission: {
        usdt: conversionAmount * 0.15,
        shares: sharesRequested * 0.15
      }
    };

  } catch (error) {
    console.error('❌ Test setup failed:', error);
    throw error;
  }
};

// Run the test
testConversionCommissionProcessing()
  .then((result) => {
    console.log('\n🎉 Test setup completed successfully!');
    console.log('Ready for manual testing of conversion approval commission processing.');
  })
  .catch((error) => {
    console.error('\n💥 Test failed:', error);
    process.exit(1);
  });
