import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

const supabase = createClient(supabaseUrl, supabaseKey);

async function deepDiveUser139() {
  console.log('🔍 DEEP DIVE: User 139 Commission Analysis\n');

  // Get detailed commission transactions with referred user info
  const { data: commissions, error: commissionError } = await supabase
    .from('commission_transactions')
    .select(`
      *,
      referred_user:users!referred_id(id, username, full_name, email)
    `)
    .eq('referrer_id', 139)
    .order('created_at', { ascending: false });

  if (commissionError) {
    console.log('❌ Error getting detailed commissions:', commissionError.message);
    return;
  }

  console.log(`💸 Detailed Commission Analysis for User 139:`);
  console.log(`Found ${commissions.length} commission transactions\n`);

  for (let i = 0; i < commissions.length; i++) {
    const commission = commissions[i];
    console.log(`${i + 1}. Commission Transaction Details:`);
    console.log(`   Transaction ID: ${commission.id}`);
    console.log(`   Referred User: ${commission.referred_user?.full_name || commission.referred_user?.username || 'Unknown'} (ID: ${commission.referred_id})`);
    console.log(`   USDT Commission: $${commission.usdt_commission || 0}`);
    console.log(`   Share Commission: ${commission.share_commission || 0}`);
    console.log(`   Purchase Amount: $${commission.share_purchase_amount || 0}`);
    console.log(`   Commission Rate: ${commission.commission_rate || 0}%`);
    console.log(`   Status: ${commission.status}`);
    console.log(`   Created: ${new Date(commission.created_at).toLocaleString()}`);
    console.log(`   Payment Date: ${commission.payment_date ? new Date(commission.payment_date).toLocaleString() : 'Not set'}`);
    
    // Check if there's a corresponding purchase
    if (commission.referred_id) {
      const { data: purchases, error: purchaseError } = await supabase
        .from('aureus_share_purchases')
        .select('*')
        .eq('user_id', commission.referred_id)
        .order('created_at', { ascending: false })
        .limit(5);

      if (!purchaseError && purchases && purchases.length > 0) {
        console.log(`   📊 Recent purchases by this user:`);
        purchases.forEach((purchase, idx) => {
          console.log(`      ${idx + 1}. $${purchase.total_amount} (${purchase.shares_purchased} shares) - ${purchase.status} - ${new Date(purchase.created_at).toLocaleDateString()}`);
        });
      } else {
        console.log(`   ⚠️  No purchases found for this referred user`);
      }
    }
    console.log('');
  }

  // Check if there are any purchases from downline that might not be approved yet
  console.log(`\n🔍 Checking ALL purchases (including pending/rejected) from downline:`);
  
  const { data: downline, error: downlineError } = await supabase
    .from('referrals')
    .select('referred_id')
    .eq('referrer_id', 139)
    .eq('status', 'active');

  if (!downlineError && downline && downline.length > 0) {
    const downlineIds = downline.map(ref => ref.referred_id);
    
    const { data: allPurchases, error: allPurchaseError } = await supabase
      .from('aureus_share_purchases')
      .select(`
        *,
        users!user_id(id, username, full_name)
      `)
      .in('user_id', downlineIds)
      .order('created_at', { ascending: false });

    if (!allPurchaseError && allPurchases) {
      console.log(`Found ${allPurchases.length} total purchases from downline (all statuses):`);
      
      const statusCounts = {};
      allPurchases.forEach(purchase => {
        statusCounts[purchase.status] = (statusCounts[purchase.status] || 0) + 1;
        
        console.log(`   • ${purchase.users.full_name || purchase.users.username}`);
        console.log(`     $${purchase.total_amount} (${purchase.shares_purchased} shares)`);
        console.log(`     Status: ${purchase.status}`);
        console.log(`     Date: ${new Date(purchase.created_at).toLocaleDateString()}`);
        console.log('');
      });
      
      console.log(`📊 Purchase Status Summary:`);
      Object.entries(statusCounts).forEach(([status, count]) => {
        console.log(`   ${status}: ${count} purchases`);
      });
    }
  }

  // Check if user 139 might be getting commissions from other sources
  console.log(`\n🔍 Checking if User 139 has multiple referral relationships:`);
  
  const { data: allReferrals, error: allRefError } = await supabase
    .from('referrals')
    .select(`
      *,
      referred:users!referred_id(id, username, full_name)
    `)
    .eq('referrer_id', 139)
    .order('created_at', { ascending: false });

  if (!allRefError && allReferrals) {
    console.log(`Total referral relationships: ${allReferrals.length}`);
    allReferrals.forEach((ref, idx) => {
      console.log(`   ${idx + 1}. ${ref.referred.full_name || ref.referred.username} (ID: ${ref.referred_id})`);
      console.log(`      Status: ${ref.status}`);
      console.log(`      Created: ${new Date(ref.created_at).toLocaleDateString()}`);
    });
  }

  // Check for any manual commission adjustments
  console.log(`\n🔍 Checking for manual commission adjustments:`);
  
  const manualCommissions = commissions.filter(c => 
    c.commission_rate === 0 || 
    c.share_purchase_amount === 0 || 
    c.referred_id === c.referrer_id
  );

  if (manualCommissions.length > 0) {
    console.log(`Found ${manualCommissions.length} potential manual adjustments:`);
    manualCommissions.forEach((commission, idx) => {
      console.log(`   ${idx + 1}. Transaction ID: ${commission.id}`);
      console.log(`      Amount: $${commission.usdt_commission}`);
      console.log(`      Rate: ${commission.commission_rate}%`);
      console.log(`      Purchase Amount: $${commission.share_purchase_amount}`);
      console.log(`      Date: ${new Date(commission.created_at).toLocaleDateString()}`);
    });
  } else {
    console.log(`No manual adjustments found.`);
  }

  console.log('\n✅ Deep dive analysis complete!');
}

// Run the deep dive
deepDiveUser139().catch(console.error);
