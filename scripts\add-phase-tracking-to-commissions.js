import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function addPhaseTrackingToCommissions() {
  console.log('🔧 ADDING PHASE TRACKING TO COMMISSION SYSTEM\n');

  try {
    // Step 1: Check current commission_transactions table structure
    console.log('1️⃣ Checking current commission_transactions table structure...');
    
    const { data: sampleCommission, error: sampleError } = await supabase
      .from('commission_transactions')
      .select('*')
      .limit(1);

    if (sampleError) {
      console.error('❌ Error checking commission_transactions:', sampleError);
      return;
    }

    if (sampleCommission && sampleCommission.length > 0) {
      console.log('✅ Current commission_transactions columns:');
      Object.keys(sampleCommission[0]).forEach(col => {
        console.log(`   • ${col}`);
      });
      
      if (sampleCommission[0].hasOwnProperty('phase_id')) {
        console.log('✅ phase_id column already exists!');
        return;
      }
    }

    // Step 2: Add phase_id column to commission_transactions
    console.log('\n2️⃣ Adding phase_id column to commission_transactions...');
    console.log('⚠️  Manual SQL required - please run this in Supabase SQL Editor:');
    console.log(`
    -- Add phase_id column to commission_transactions
    ALTER TABLE commission_transactions
    ADD COLUMN IF NOT EXISTS phase_id INTEGER REFERENCES investment_phases(id);

    -- Create indexes for better performance
    CREATE INDEX IF NOT EXISTS idx_commission_transactions_phase_id
    ON commission_transactions(phase_id);

    CREATE INDEX IF NOT EXISTS idx_commission_transactions_referrer_phase
    ON commission_transactions(referrer_id, phase_id);
    `);

    console.log('\n✅ Please run the above SQL in Supabase SQL Editor, then continue...');
    console.log('📋 After running the SQL, the system will be ready for phase-specific commissions');

    // Step 3: Backfill existing commission records with phase data
    console.log('\n3️⃣ Backfilling existing commissions with phase data...');
    
    // Get all commission transactions that need phase_id
    const { data: commissionsToUpdate, error: fetchError } = await supabase
      .from('commission_transactions')
      .select('id, share_purchase_id, payment_date')
      .is('phase_id', null);

    if (fetchError) {
      console.error('❌ Error fetching commissions to update:', fetchError);
      return;
    }

    console.log(`📊 Found ${commissionsToUpdate?.length || 0} commissions to backfill`);

    // Update commissions with phase data based on share purchase or date
    for (const commission of commissionsToUpdate || []) {
      let phaseId = null;

      // Try to get phase from linked share purchase
      if (commission.share_purchase_id) {
        const { data: purchase } = await supabase
          .from('aureus_share_purchases')
          .select('phase_id')
          .eq('id', commission.share_purchase_id)
          .single();
        
        if (purchase?.phase_id) {
          phaseId = purchase.phase_id;
        }
      }

      // Fallback: determine phase by payment date
      if (!phaseId && commission.payment_date) {
        const { data: phaseByDate } = await supabase
          .from('investment_phases')
          .select('id')
          .lte('start_date', commission.payment_date)
          .order('start_date', { ascending: false })
          .limit(1)
          .single();
        
        if (phaseByDate?.id) {
          phaseId = phaseByDate.id;
        }
      }

      // Final fallback: use current active phase
      if (!phaseId) {
        const { data: activePhase } = await supabase
          .from('investment_phases')
          .select('id')
          .eq('is_active', true)
          .single();
        
        if (activePhase?.id) {
          phaseId = activePhase.id;
        }
      }

      // Update the commission record
      if (phaseId) {
        await supabase
          .from('commission_transactions')
          .update({ phase_id: phaseId })
          .eq('id', commission.id);
      }
    }

    console.log('✅ Backfill completed');

    // Step 4: Update PaymentManager to include phase_id in new commissions
    console.log('\n4️⃣ Commission system is now phase-aware!');
    console.log('📋 Next steps:');
    console.log('   • Update PaymentManager.ts to include phase_id when creating commissions');
    console.log('   • Update competition leaderboards to use real phase-specific data');
    console.log('   • Add phase-specific commission views for users');

    // Step 5: Test the new structure
    console.log('\n5️⃣ Testing new phase-specific commission queries...');
    
    const { data: phaseCommissions, error: testError } = await supabase
      .from('commission_transactions')
      .select(`
        referrer_id,
        usdt_commission,
        share_commission,
        phase_id,
        investment_phases(phase_name, phase_number)
      `)
      .not('phase_id', 'is', null)
      .limit(5);

    if (testError) {
      console.error('❌ Error testing phase-specific queries:', testError);
    } else {
      console.log('✅ Phase-specific commission query successful:');
      phaseCommissions?.forEach(comm => {
        console.log(`   • User ${comm.referrer_id}: $${comm.usdt_commission} in ${comm.investment_phases?.phase_name}`);
      });
    }

    console.log('\n🎉 Phase tracking successfully added to commission system!');

  } catch (error) {
    console.error('❌ Script failed:', error);
  }
}

// Run the script
addPhaseTrackingToCommissions();
