import React, { useState } from 'react';
import { useUserSettings } from '../lib/hooks/useUserSettings';
import { SecurePasswordChangeForm } from './user/SecurePasswordChangeForm';
import { UsernameEditor } from './user/UsernameEditor';

interface UserSettingsDashboardProps {
  userId: number;
  userEmail: string;
  currentUsername: string;
  onRefreshData?: () => void;
}

type SettingsTab = 'general' | 'security' | 'notifications' | 'privacy' | 'advanced';

export const UserSettingsDashboard: React.FC<UserSettingsDashboardProps> = ({
  userId,
  userEmail,
  currentUsername,
  onRefreshData
}) => {
  const [activeTab, setActiveTab] = useState<SettingsTab>('general');
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [showUsernameModal, setShowUsernameModal] = useState(false);
  const [showResetConfirm, setShowResetConfirm] = useState(false);

  const {
    settings,
    notifications,
    loading,
    saving,
    error,
    updateSetting,
    updateNotificationSetting,
    resetToDefaults,
    exportSettings,
    importSettings
  } = useUserSettings({ userId, autoLoad: true });

  const tabs = [
    { id: 'general', name: 'General', icon: '⚙️' },
    { id: 'security', name: 'Security', icon: '🔒' },
    { id: 'notifications', name: 'Notifications', icon: '🔔' },
    { id: 'privacy', name: 'Privacy', icon: '🛡️' },
    { id: 'advanced', name: 'Advanced', icon: '🔧' }
  ];

  const handleExportSettings = async () => {
    const data = await exportSettings();
    if (data) {
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `aureus-settings-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const handleImportSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const data = JSON.parse(e.target?.result as string);
          await importSettings(data);
        } catch (err) {
          console.error('Failed to import settings:', err);
        }
      };
      reader.readAsText(file);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gold"></div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gold mb-2">Settings</h1>
        <p className="text-gray-300">
          Manage your account preferences, security settings, and notifications.
        </p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-6 bg-red-900/20 border border-red-500/30 rounded-lg p-4">
          <p className="text-red-300">❌ {error}</p>
        </div>
      )}

      {/* Tabs */}
      <div className="flex flex-wrap gap-2 mb-8 border-b border-gray-700">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as SettingsTab)}
            className={`px-4 py-3 font-medium transition-colors border-b-2 ${
              activeTab === tab.id
                ? 'text-gold border-gold bg-gray-800/50'
                : 'text-gray-400 border-transparent hover:text-white hover:border-gray-600'
            }`}
          >
            <span className="mr-2">{tab.icon}</span>
            {tab.name}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'general' && (
          <div className="space-y-6">
            {/* Theme Settings */}
            <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Appearance</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Theme
                  </label>
                  <select
                    value={settings?.theme || 'dark'}
                    onChange={(e) => updateSetting('theme', e.target.value)}
                    disabled={saving}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gold"
                  >
                    <option value="dark">Dark</option>
                    <option value="light">Light</option>
                    <option value="auto">Auto (System)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Language
                  </label>
                  <select
                    value={settings?.language || 'en'}
                    onChange={(e) => updateSetting('language', e.target.value)}
                    disabled={saving}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gold"
                  >
                    <option value="en">English</option>
                    <option value="fr">Français</option>
                    <option value="es">Español</option>
                    <option value="pt">Português</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Currency Display
                  </label>
                  <select
                    value={settings?.currency_display || 'USD'}
                    onChange={(e) => updateSetting('currency_display', e.target.value)}
                    disabled={saving}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gold"
                  >
                    <option value="USD">USD ($)</option>
                    <option value="ZAR">ZAR (R)</option>
                    <option value="EUR">EUR (€)</option>
                    <option value="GBP">GBP (£)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Timezone
                  </label>
                  <select
                    value={settings?.timezone || 'UTC'}
                    onChange={(e) => updateSetting('timezone', e.target.value)}
                    disabled={saving}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gold"
                  >
                    <option value="UTC">UTC</option>
                    <option value="Africa/Johannesburg">South Africa (SAST)</option>
                    <option value="Europe/London">London (GMT)</option>
                    <option value="America/New_York">New York (EST)</option>
                    <option value="America/Los_Angeles">Los Angeles (PST)</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Dashboard Settings */}
            <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Dashboard Preferences</h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-300">Compact View</label>
                    <p className="text-xs text-gray-400">Show more information in less space</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings?.compact_view || false}
                      onChange={(e) => updateSetting('compact_view', e.target.checked)}
                      disabled={saving}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-gold/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gold"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-300">Auto Refresh</label>
                    <p className="text-xs text-gray-400">Automatically refresh dashboard data</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings?.auto_refresh || false}
                      onChange={(e) => updateSetting('auto_refresh', e.target.checked)}
                      disabled={saving}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-gold/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gold"></div>
                  </label>
                </div>

                {settings?.auto_refresh && (
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Refresh Interval (seconds)
                    </label>
                    <input
                      type="number"
                      min="10"
                      max="300"
                      value={settings?.refresh_interval || 30}
                      onChange={(e) => updateSetting('refresh_interval', parseInt(e.target.value))}
                      disabled={saving}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gold"
                    />
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Default Dashboard
                  </label>
                  <select
                    value={settings?.default_dashboard || 'shareholder'}
                    onChange={(e) => updateSetting('default_dashboard', e.target.value)}
                    disabled={saving}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gold"
                  >
                    <option value="shareholder">Shareholder Dashboard</option>
                    <option value="affiliate">Affiliate Dashboard</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'security' && (
          <div className="space-y-6">
            {/* Account Security */}
            <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Account Security</h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-gray-700/30 rounded-lg">
                  <div>
                    <h4 className="font-medium text-white">Password</h4>
                    <p className="text-sm text-gray-400">Change your account password</p>
                  </div>
                  <button
                    onClick={() => setShowPasswordModal(true)}
                    className="px-4 py-2 bg-gold text-black font-medium rounded-lg hover:bg-yellow-500 transition-colors"
                  >
                    Change Password
                  </button>
                </div>

                <div className="flex items-center justify-between p-4 bg-gray-700/30 rounded-lg">
                  <div>
                    <h4 className="font-medium text-white">Username</h4>
                    <p className="text-sm text-gray-400">Current: {currentUsername}</p>
                  </div>
                  <button
                    onClick={() => setShowUsernameModal(true)}
                    className="px-4 py-2 bg-gray-600 text-white font-medium rounded-lg hover:bg-gray-500 transition-colors"
                  >
                    Change Username
                  </button>
                </div>
              </div>
            </div>

            {/* Security Preferences */}
            <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Security Preferences</h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-300">Login Notifications</label>
                    <p className="text-xs text-gray-400">Get notified when someone logs into your account</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings?.login_notifications || false}
                      onChange={(e) => updateSetting('login_notifications', e.target.checked)}
                      disabled={saving}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-gold/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gold"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-300">Require Password for Sensitive Actions</label>
                    <p className="text-xs text-gray-400">Require password confirmation for withdrawals and transfers</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings?.require_password_for_sensitive || false}
                      onChange={(e) => updateSetting('require_password_for_sensitive', e.target.checked)}
                      disabled={saving}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-gold/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gold"></div>
                  </label>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Session Timeout (minutes)
                  </label>
                  <input
                    type="number"
                    min="15"
                    max="480"
                    value={settings?.session_timeout || 60}
                    onChange={(e) => updateSetting('session_timeout', parseInt(e.target.value))}
                    disabled={saving}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gold"
                  />
                  <p className="text-xs text-gray-400 mt-1">
                    Automatically log out after this period of inactivity
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'notifications' && (
          <div className="space-y-6">
            {/* Email Notifications */}
            <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Email Notifications</h3>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-300">Email Notifications</label>
                    <p className="text-xs text-gray-400">Receive important account notifications via email</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings?.email_notifications || false}
                      onChange={(e) => updateSetting('email_notifications', e.target.checked)}
                      disabled={saving}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-gold/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gold"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-300">Marketing Emails</label>
                    <p className="text-xs text-gray-400">Receive promotional offers and updates</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings?.marketing_emails || false}
                      onChange={(e) => updateSetting('marketing_emails', e.target.checked)}
                      disabled={saving}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-gold/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gold"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-300">Newsletter Subscription</label>
                    <p className="text-xs text-gray-400">Receive our monthly newsletter with company updates</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings?.newsletter_subscription || false}
                      onChange={(e) => updateSetting('newsletter_subscription', e.target.checked)}
                      disabled={saving}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-gold/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gold"></div>
                  </label>
                </div>
              </div>
            </div>

            {/* Audio Notifications */}
            {notifications && (
              <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
                <h3 className="text-lg font-semibold text-white mb-4">Audio Notifications</h3>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-300">Audio Enabled</label>
                      <p className="text-xs text-gray-400">Enable sound notifications</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={notifications.audio_enabled || false}
                        onChange={(e) => updateNotificationSetting('audio_enabled', e.target.checked)}
                        disabled={saving}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-gold/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gold"></div>
                    </label>
                  </div>

                  {notifications.audio_enabled && (
                    <>
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Notification Volume
                        </label>
                        <select
                          value={notifications.notification_volume || 'medium'}
                          onChange={(e) => updateNotificationSetting('notification_volume', e.target.value)}
                          disabled={saving}
                          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gold"
                        >
                          <option value="low">Low</option>
                          <option value="medium">Medium</option>
                          <option value="high">High</option>
                        </select>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="flex items-center justify-between">
                          <label className="text-sm text-gray-300">Payment Approvals</label>
                          <input
                            type="checkbox"
                            checked={notifications.payment_approval_audio || false}
                            onChange={(e) => updateNotificationSetting('payment_approval_audio', e.target.checked)}
                            disabled={saving}
                            className="w-4 h-4 text-gold bg-gray-700 border-gray-600 rounded focus:ring-gold"
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <label className="text-sm text-gray-300">Payment Rejections</label>
                          <input
                            type="checkbox"
                            checked={notifications.payment_rejection_audio || false}
                            onChange={(e) => updateNotificationSetting('payment_rejection_audio', e.target.checked)}
                            disabled={saving}
                            className="w-4 h-4 text-gold bg-gray-700 border-gray-600 rounded focus:ring-gold"
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <label className="text-sm text-gray-300">Commission Updates</label>
                          <input
                            type="checkbox"
                            checked={notifications.commission_update_audio || false}
                            onChange={(e) => updateNotificationSetting('commission_update_audio', e.target.checked)}
                            disabled={saving}
                            className="w-4 h-4 text-gold bg-gray-700 border-gray-600 rounded focus:ring-gold"
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <label className="text-sm text-gray-300">Referral Bonuses</label>
                          <input
                            type="checkbox"
                            checked={notifications.referral_bonus_audio || false}
                            onChange={(e) => updateNotificationSetting('referral_bonus_audio', e.target.checked)}
                            disabled={saving}
                            className="w-4 h-4 text-gold bg-gray-700 border-gray-600 rounded focus:ring-gold"
                          />
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            )}

            {/* Quiet Hours */}
            {notifications && (
              <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
                <h3 className="text-lg font-semibold text-white mb-4">Quiet Hours</h3>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-300">Enable Quiet Hours</label>
                      <p className="text-xs text-gray-400">Disable notifications during specified hours</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={notifications.quiet_hours_enabled || false}
                        onChange={(e) => updateNotificationSetting('quiet_hours_enabled', e.target.checked)}
                        disabled={saving}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-gold/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gold"></div>
                    </label>
                  </div>

                  {notifications.quiet_hours_enabled && (
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Start Time
                        </label>
                        <input
                          type="time"
                          value={notifications.quiet_hours_start || '22:00'}
                          onChange={(e) => updateNotificationSetting('quiet_hours_start', e.target.value)}
                          disabled={saving}
                          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gold"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          End Time
                        </label>
                        <input
                          type="time"
                          value={notifications.quiet_hours_end || '08:00'}
                          onChange={(e) => updateNotificationSetting('quiet_hours_end', e.target.value)}
                          disabled={saving}
                          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gold"
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'privacy' && (
          <div className="space-y-6">
            {/* Profile Privacy */}
            <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Profile Privacy</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Profile Visibility
                  </label>
                  <select
                    value={settings?.profile_visibility || 'private'}
                    onChange={(e) => updateSetting('profile_visibility', e.target.value)}
                    disabled={saving}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gold"
                  >
                    <option value="private">Private</option>
                    <option value="contacts_only">Contacts Only</option>
                    <option value="public">Public</option>
                  </select>
                  <p className="text-xs text-gray-400 mt-1">
                    Control who can see your profile information
                  </p>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-300">Show Earnings</label>
                    <p className="text-xs text-gray-400">Display your earnings information to others</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings?.show_earnings || false}
                      onChange={(e) => updateSetting('show_earnings', e.target.checked)}
                      disabled={saving}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-gold/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gold"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-300">Show Referrals</label>
                    <p className="text-xs text-gray-400">Display your referral network to others</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings?.show_referrals || false}
                      onChange={(e) => updateSetting('show_referrals', e.target.checked)}
                      disabled={saving}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-gold/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gold"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-300">Allow Contact</label>
                    <p className="text-xs text-gray-400">Allow other users to contact you</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings?.allow_contact || false}
                      onChange={(e) => updateSetting('allow_contact', e.target.checked)}
                      disabled={saving}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-gold/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gold"></div>
                  </label>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'advanced' && (
          <div className="space-y-6">
            {/* Data Management */}
            <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Data Management</h3>

              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-gray-700/30 rounded-lg">
                  <div>
                    <h4 className="font-medium text-white">Export Settings</h4>
                    <p className="text-sm text-gray-400">Download your settings as a JSON file</p>
                  </div>
                  <button
                    onClick={handleExportSettings}
                    disabled={saving}
                    className="px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-500 transition-colors disabled:opacity-50"
                  >
                    Export
                  </button>
                </div>

                <div className="flex items-center justify-between p-4 bg-gray-700/30 rounded-lg">
                  <div>
                    <h4 className="font-medium text-white">Import Settings</h4>
                    <p className="text-sm text-gray-400">Upload a settings file to restore preferences</p>
                  </div>
                  <label className="px-4 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-500 transition-colors cursor-pointer">
                    Import
                    <input
                      type="file"
                      accept=".json"
                      onChange={handleImportSettings}
                      className="hidden"
                    />
                  </label>
                </div>

                <div className="flex items-center justify-between p-4 bg-red-900/20 border border-red-500/30 rounded-lg">
                  <div>
                    <h4 className="font-medium text-red-300">Reset All Settings</h4>
                    <p className="text-sm text-red-400">Restore all settings to their default values</p>
                  </div>
                  <button
                    onClick={() => setShowResetConfirm(true)}
                    disabled={saving}
                    className="px-4 py-2 bg-red-600 text-white font-medium rounded-lg hover:bg-red-500 transition-colors disabled:opacity-50"
                  >
                    Reset
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      {showPasswordModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="max-w-md w-full">
            <SecurePasswordChangeForm
              userId={userId}
              userEmail={userEmail}
              onPasswordChanged={() => {
                setShowPasswordModal(false);
                onRefreshData?.();
              }}
              onClose={() => setShowPasswordModal(false)}
            />
          </div>
        </div>
      )}

      {showUsernameModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="max-w-md w-full bg-gray-800 rounded-lg border border-gray-700 p-6">
            <UsernameEditor
              currentUsername={currentUsername}
              userId={userId}
              onUsernameUpdate={(newUsername) => {
                console.log('🔄 Username updated successfully:', newUsername);
                setShowUsernameModal(false);

                // Force a data refresh to update the UI
                if (onRefreshData) {
                  console.log('🔄 Triggering data refresh...');
                  onRefreshData();
                } else {
                  console.log('⚠️ No refresh callback available');
                  // Force a page reload as fallback
                  setTimeout(() => {
                    window.location.reload();
                  }, 1000);
                }
              }}
              onClose={() => setShowUsernameModal(false)}
            />
          </div>
        </div>
      )}

      {/* Reset Confirmation Modal */}
      {showResetConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="max-w-md w-full bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Reset All Settings</h3>
            <p className="text-gray-300 mb-6">
              Are you sure you want to reset all settings to their default values? This action cannot be undone.
            </p>

            <div className="flex gap-3">
              <button
                onClick={() => setShowResetConfirm(false)}
                disabled={saving}
                className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                onClick={async () => {
                  const success = await resetToDefaults();
                  if (success) {
                    setShowResetConfirm(false);
                  }
                }}
                disabled={saving}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-500 transition-colors disabled:opacity-50 flex items-center justify-center gap-2"
              >
                {saving ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Resetting...
                  </>
                ) : (
                  'Reset All'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
