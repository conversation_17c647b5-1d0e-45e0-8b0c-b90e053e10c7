/**
 * FINANCIAL DATA SECURITY SYSTEM
 * 
 * This module provides comprehensive security for financial operations
 * including commission balances, share purchases, and payment transactions.
 */

import { supabase } from './supabase';

interface FinancialSecurityContext {
  userId: number;
  userEmail: string;
  isAdmin: boolean;
  isBotRequest: boolean;
  requestId: string;
}

interface FinancialOperation {
  type: 'commission_view' | 'commission_adjust' | 'share_purchase' | 'payment_approve' | 'balance_update';
  targetUserId: number;
  amount?: number;
  metadata?: any;
}

class FinancialSecurityManager {
  /**
   * Validate financial operation authorization
   */
  async validateFinancialAccess(
    context: FinancialSecurityContext,
    operation: FinancialOperation
  ): Promise<{ authorized: boolean; reason?: string }> {
    try {
      console.log(`🔐 Validating financial access: ${operation.type} for user ${operation.targetUserId}`);

      // Bot requests with service role are always authorized
      if (context.isBotRequest) {
        console.log('✅ Bot request authorized (service role)');
        await this.logFinancialAccess(context, operation, 'AUTHORIZED', 'Bot service role');
        return { authorized: true };
      }

      // Check if user is accessing their own data
      if (context.userId === operation.targetUserId) {
        console.log('✅ User accessing own financial data');
        await this.logFinancialAccess(context, operation, 'AUTHORIZED', 'Own data access');
        return { authorized: true };
      }

      // Check admin permissions for other users' data
      if (context.isAdmin) {
        const adminValidation = await this.validateAdminAccess(context, operation);
        if (adminValidation.authorized) {
          console.log('✅ Admin access authorized');
          await this.logFinancialAccess(context, operation, 'AUTHORIZED', 'Admin access');
          return { authorized: true };
        } else {
          console.log('❌ Admin access denied:', adminValidation.reason);
          await this.logFinancialAccess(context, operation, 'DENIED', adminValidation.reason);
          return adminValidation;
        }
      }

      // Unauthorized access attempt
      const reason = 'Insufficient permissions for financial data access';
      console.log('❌ Financial access denied:', reason);
      await this.logFinancialAccess(context, operation, 'DENIED', reason);
      
      return { authorized: false, reason };

    } catch (error) {
      console.error('❌ Financial access validation error:', error);
      await this.logFinancialAccess(context, operation, 'ERROR', error.message);
      return { authorized: false, reason: 'Security validation error' };
    }
  }

  /**
   * Validate admin access to financial operations
   */
  private async validateAdminAccess(
    context: FinancialSecurityContext,
    operation: FinancialOperation
  ): Promise<{ authorized: boolean; reason?: string }> {
    try {
      // Verify admin status in database
      const { data: adminUser, error } = await supabase
        .from('admin_users')
        .select('id, email, role, is_active, permissions')
        .eq('email', context.userEmail)
        .eq('is_active', true)
        .single();

      if (error || !adminUser) {
        return { authorized: false, reason: 'Admin user not found or inactive' };
      }

      // Check role-based permissions
      const requiredPermissions = this.getRequiredPermissions(operation.type);
      const hasPermission = this.checkAdminPermissions(adminUser, requiredPermissions);

      if (!hasPermission) {
        return { 
          authorized: false, 
          reason: `Insufficient admin permissions for ${operation.type}` 
        };
      }

      // Additional validation for high-value operations
      if (operation.amount && operation.amount > 1000) {
        const highValueValidation = await this.validateHighValueOperation(context, operation);
        if (!highValueValidation.authorized) {
          return highValueValidation;
        }
      }

      return { authorized: true };

    } catch (error) {
      console.error('❌ Admin access validation error:', error);
      return { authorized: false, reason: 'Admin validation error' };
    }
  }

  /**
   * Get required permissions for operation type
   */
  private getRequiredPermissions(operationType: string): string[] {
    const permissionMap = {
      'commission_view': ['view_commissions'],
      'commission_adjust': ['manage_commissions', 'financial_operations'],
      'share_purchase': ['manage_shares', 'financial_operations'],
      'payment_approve': ['approve_payments', 'financial_operations'],
      'balance_update': ['manage_balances', 'financial_operations']
    };

    return permissionMap[operationType] || ['financial_operations'];
  }

  /**
   * Check if admin has required permissions
   */
  private checkAdminPermissions(adminUser: any, requiredPermissions: string[]): boolean {
    // Super admin has all permissions
    if (adminUser.role === 'super_admin') {
      return true;
    }

    // Check specific permissions
    const userPermissions = adminUser.permissions || [];
    return requiredPermissions.some(permission => 
      userPermissions.includes(permission) || userPermissions.includes('all')
    );
  }

  /**
   * Validate high-value financial operations
   */
  private async validateHighValueOperation(
    context: FinancialSecurityContext,
    operation: FinancialOperation
  ): Promise<{ authorized: boolean; reason?: string }> {
    try {
      // Check for recent high-value operations by this admin
      const { data: recentOps, error } = await supabase
        .from('admin_audit_logs')
        .select('*')
        .eq('admin_email', context.userEmail)
        .gte('created_at', new Date(Date.now() - 60 * 60 * 1000).toISOString()) // Last hour
        .like('action', '%FINANCIAL%');

      if (error) {
        console.error('❌ Error checking recent operations:', error);
        return { authorized: false, reason: 'Unable to validate operation history' };
      }

      // Count high-value operations in the last hour
      const highValueOps = recentOps?.filter(op => 
        op.metadata?.amount && parseFloat(op.metadata.amount) > 500
      ) || [];

      if (highValueOps.length >= 5) {
        return { 
          authorized: false, 
          reason: 'Too many high-value operations in the last hour' 
        };
      }

      // For very high amounts, require additional validation
      if (operation.amount && operation.amount > 5000) {
        return { 
          authorized: false, 
          reason: 'Operations over $5000 require manual approval' 
        };
      }

      return { authorized: true };

    } catch (error) {
      console.error('❌ High-value operation validation error:', error);
      return { authorized: false, reason: 'High-value validation error' };
    }
  }

  /**
   * Secure commission balance access
   */
  async getCommissionBalance(
    context: FinancialSecurityContext,
    targetUserId: number
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const validation = await this.validateFinancialAccess(context, {
        type: 'commission_view',
        targetUserId
      });

      if (!validation.authorized) {
        return { success: false, error: validation.reason };
      }

      const { data, error } = await supabase
        .from('commission_balances')
        .select('*')
        .eq('user_id', targetUserId)
        .single();

      if (error) {
        return { success: false, error: 'Failed to fetch commission balance' };
      }

      return { success: true, data };

    } catch (error) {
      console.error('❌ Commission balance access error:', error);
      return { success: false, error: 'Commission balance access failed' };
    }
  }

  /**
   * Secure commission adjustment
   */
  async adjustCommissionBalance(
    context: FinancialSecurityContext,
    targetUserId: number,
    usdtAdjustment: number,
    shareAdjustment: number,
    reason: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const validation = await this.validateFinancialAccess(context, {
        type: 'commission_adjust',
        targetUserId,
        amount: Math.abs(usdtAdjustment) + Math.abs(shareAdjustment * 5), // Approximate value
        metadata: { reason }
      });

      if (!validation.authorized) {
        return { success: false, error: validation.reason };
      }

      // Get current balance
      const { data: currentBalance, error: balanceError } = await supabase
        .from('commission_balances')
        .select('*')
        .eq('user_id', targetUserId)
        .single();

      if (balanceError) {
        return { success: false, error: 'Failed to fetch current balance' };
      }

      // Calculate new balances
      const newUsdtBalance = Math.max(0, currentBalance.usdt_balance + usdtAdjustment);
      const newShareBalance = Math.max(0, currentBalance.share_balance + shareAdjustment);

      // Update balance
      const { error: updateError } = await supabase
        .from('commission_balances')
        .update({
          usdt_balance: newUsdtBalance,
          share_balance: newShareBalance,
          total_earned_usdt: currentBalance.total_earned_usdt + Math.max(0, usdtAdjustment),
          total_earned_shares: currentBalance.total_earned_shares + Math.max(0, shareAdjustment),
          last_updated: new Date().toISOString()
        })
        .eq('user_id', targetUserId);

      if (updateError) {
        return { success: false, error: 'Failed to update commission balance' };
      }

      // Log the adjustment
      await this.logFinancialOperation(context, {
        type: 'commission_adjust',
        targetUserId,
        amount: Math.abs(usdtAdjustment) + Math.abs(shareAdjustment * 5),
        metadata: {
          usdtAdjustment,
          shareAdjustment,
          reason,
          previousUsdtBalance: currentBalance.usdt_balance,
          previousShareBalance: currentBalance.share_balance,
          newUsdtBalance,
          newShareBalance
        }
      });

      return { success: true };

    } catch (error) {
      console.error('❌ Commission adjustment error:', error);
      return { success: false, error: 'Commission adjustment failed' };
    }
  }

  /**
   * Log financial access attempts
   */
  private async logFinancialAccess(
    context: FinancialSecurityContext,
    operation: FinancialOperation,
    result: 'AUTHORIZED' | 'DENIED' | 'ERROR',
    reason: string
  ): Promise<void> {
    try {
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_email: context.userEmail,
          action: `FINANCIAL_ACCESS_${result}`,
          target_type: 'financial_security',
          target_id: operation.targetUserId.toString(),
          metadata: {
            operation_type: operation.type,
            result,
            reason,
            amount: operation.amount,
            is_bot_request: context.isBotRequest,
            is_admin: context.isAdmin,
            request_id: context.requestId,
            timestamp: new Date().toISOString()
          },
          created_at: new Date().toISOString()
        });

    } catch (error) {
      console.error('❌ Failed to log financial access:', error);
    }
  }

  /**
   * Log financial operations
   */
  private async logFinancialOperation(
    context: FinancialSecurityContext,
    operation: FinancialOperation
  ): Promise<void> {
    try {
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_email: context.userEmail,
          action: `FINANCIAL_OPERATION_${operation.type.toUpperCase()}`,
          target_type: 'financial_operation',
          target_id: operation.targetUserId.toString(),
          metadata: {
            ...operation.metadata,
            operation_type: operation.type,
            amount: operation.amount,
            is_bot_request: context.isBotRequest,
            request_id: context.requestId,
            timestamp: new Date().toISOString()
          },
          created_at: new Date().toISOString()
        });

    } catch (error) {
      console.error('❌ Failed to log financial operation:', error);
    }
  }

  /**
   * Get financial security statistics
   */
  async getSecurityStats(): Promise<any> {
    try {
      const { data: logs, error } = await supabase
        .from('admin_audit_logs')
        .select('*')
        .like('action', 'FINANCIAL_%')
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      const stats = {
        totalOperations: logs.length,
        authorizedOperations: logs.filter(log => log.action.includes('AUTHORIZED')).length,
        deniedOperations: logs.filter(log => log.action.includes('DENIED')).length,
        errorOperations: logs.filter(log => log.action.includes('ERROR')).length,
        botOperations: logs.filter(log => log.metadata?.is_bot_request).length,
        adminOperations: logs.filter(log => log.metadata?.is_admin && !log.metadata?.is_bot_request).length,
        highValueOperations: logs.filter(log => log.metadata?.amount && parseFloat(log.metadata.amount) > 1000).length
      };

      return stats;

    } catch (error) {
      console.error('❌ Failed to get security stats:', error);
      return null;
    }
  }
}

// Create singleton instance
export const financialSecurity = new FinancialSecurityManager();

/**
 * Middleware for financial endpoints
 */
export const financialSecurityMiddleware = (operationType: FinancialOperation['type']) => {
  return async (req: any, res: any, next: any) => {
    try {
      // Extract security context
      const context: FinancialSecurityContext = {
        userId: req.user?.id || 0,
        userEmail: req.user?.email || 'unknown',
        isAdmin: req.user?.isAdmin || false,
        isBotRequest: req.headers.authorization?.includes('service_role') || false,
        requestId: req.headers['x-request-id'] || `req_${Date.now()}`
      };

      // Get target user ID from request
      const targetUserId = parseInt(req.params.userId || req.body.userId || req.query.userId);
      
      if (!targetUserId) {
        return res.status(400).json({ error: 'User ID required' });
      }

      // Validate access
      const validation = await financialSecurity.validateFinancialAccess(context, {
        type: operationType,
        targetUserId,
        amount: parseFloat(req.body.amount || 0),
        metadata: req.body
      });

      if (!validation.authorized) {
        return res.status(403).json({ 
          error: 'Access denied', 
          reason: validation.reason 
        });
      }

      // Add security context to request
      req.financialSecurityContext = context;
      req.targetUserId = targetUserId;

      next();

    } catch (error) {
      console.error('❌ Financial security middleware error:', error);
      return res.status(500).json({ error: 'Security validation error' });
    }
  };
};

export default financialSecurity;
