import React, { useState } from 'react';
import { signInWithEmailEnhanced } from '../lib/supabase';

import { TermsAndConditions } from './TermsAndConditions';
import { PrivacyPolicy } from './PrivacyPolicy';
import { EmailRegistrationFormProgressive } from './EmailRegistrationFormProgressive';
import { TelegramUserResetModal } from './auth/TelegramUserResetModal';
import { getFullVersion } from '../lib/version';


interface UnifiedAuthPageProps {
  onAuthSuccess: (user: any) => void;
  onBack: () => void;
  userType: 'shareholder' | 'affiliate';
}

export const UnifiedAuthPageClean: React.FC<UnifiedAuthPageProps> = ({
  onAuthSuccess,
  onBack,
  userType = 'shareholder'
}) => {
  const [activeTab, setActiveTab] = useState<'web-login' | 'web-register'>('web-register');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [loginForm, setLoginForm] = useState({ email: '', password: '' });
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [showTelegramUserReset, setShowTelegramUserReset] = useState(false);
  const [forgotPasswordEmail, setForgotPasswordEmail] = useState('');
  const [resetPin, setResetPin] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmNewPassword, setConfirmNewPassword] = useState('');
  const [resetStep, setResetStep] = useState<'email' | 'pin' | 'password'>('email');
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmNewPassword, setShowConfirmNewPassword] = useState(false);















  const handleWebLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      console.log('🔐 Starting web login...');

      if (!loginForm.email || !loginForm.password) {
        setError('Please enter both email and password');
        return;
      }

      // Use the enhanced sign-in function
      const { user, error } = await signInWithEmailEnhanced(loginForm.email, loginForm.password);

      if (error) {
        console.error('❌ Login error:', error);
        setError(error.message || 'Login failed');
        return;
      }

      if (!user) {
        setError('Login failed - no user returned');
        return;
      }

      console.log('✅ Login successful:', user.email);
      setSuccess('Login successful! Redirecting...');

      // Store user data in localStorage for dashboard access
      if (user.database_user) {
        console.log('💾 Storing user data in localStorage...');

        // Clear any existing user data first
        localStorage.removeItem('aureus_telegram_user');
        localStorage.removeItem('aureus_test_user');

        // Store the database user data
        localStorage.setItem('aureus_user', JSON.stringify(user.database_user));
        localStorage.setItem('aureus_user_type', userType);
        localStorage.setItem('aureus_auth_timestamp', Date.now().toString()); // Add auth timestamp

        // Store session info
        const sessionData = {
          user_id: user.database_user.id,
          email: user.email,
          account_type: user.account_type || 'web',
          user_type: userType,
          logged_in_at: new Date().toISOString()
        };
        localStorage.setItem('aureus_session', JSON.stringify(sessionData));

        console.log('✅ User data stored successfully:', {
          user_id: user.database_user.id,
          username: user.database_user.username,
          email: user.database_user.email,
          full_name: user.database_user.full_name
        });
      }

      // Format user data for the dashboard
      const formattedUser = {
        id: user.id,
        email: user.email,
        database_user: user.database_user,
        account_type: user.account_type || 'web',
        user_type: userType,
        user_metadata: {
          ...user.user_metadata,
          user_type: userType,
          account_type: 'web'
        }
      };

      // Call success callback immediately for better mobile experience
      console.log('🔄 Calling onAuthSuccess for web login...');
      onAuthSuccess(formattedUser);

    } catch (err: any) {
      console.error('❌ Web login exception:', err);
      setError(err.message || 'Login failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLoginInputChange = (field: string, value: string) => {
    setLoginForm(prev => ({ ...prev, [field]: value }));
  };

  const handleSendResetPin = async () => {
    if (!forgotPasswordEmail || !forgotPasswordEmail.trim()) {
      setError('Please enter your email address');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      console.log('🚀 Sending reset PIN to:', forgotPasswordEmail);

      const response = await fetch('/api/send-reset-pin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: forgotPasswordEmail.trim()
        }),
      });

      console.log('📡 Response status:', response.status);
      const data = await response.json();
      console.log('📦 Response data:', data);

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send reset PIN');
      }

      setSuccess(data.message || 'Reset PIN sent to your email address. Please check your inbox.');

      setResetStep('pin');
    } catch (err: any) {
      console.error('❌ Reset PIN error:', err);
      setError(err.message || 'Failed to send reset PIN. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyPin = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      console.log('Verifying PIN:', resetPin);

      const response = await fetch('/api/verify-reset-pin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: forgotPasswordEmail.trim(),
          pin: resetPin.trim()
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to verify PIN');
      }

      setSuccess(data.message || 'PIN verified successfully. Please enter your new password.');
      setResetStep('password');
    } catch (err: any) {
      console.error('PIN verification error:', err);
      setError(err.message || 'Invalid or expired PIN. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetPassword = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      console.log('Resetting password for:', forgotPasswordEmail);

      const response = await fetch('/api/password-reset', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: forgotPasswordEmail.trim(),
          newPassword: newPassword
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to reset password');
      }

      setSuccess(data.message || 'Password reset successfully! You can now log in with your new password.');

      // Reset form and close modal
      setTimeout(() => {
        setShowForgotPassword(false);
        setResetStep('email');
        setForgotPasswordEmail('');
        setResetPin('');
        setNewPassword('');
        setConfirmNewPassword('');
      }, 2000);
    } catch (err: any) {
      console.error('Password reset error:', err);
      setError(err.message || 'Failed to reset password. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };









  return (
    <div className="modern-website premium-auth-page">
      {/* Header */}
      <header className="header premium-header">
        <div className="container">
          <div className="header-content">
            <div className="logo">
              <img
                src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-s.png"
                alt="Aureus Alliance Holdings"
                className="logo-image"
              />
            </div>
            <nav className="nav-menu">
              <button className="nav-item premium-nav-item" onClick={onBack}>
                Home
              </button>
              <button className="btn btn-primary premium-btn">
                Purchase Shares
              </button>
            </nav>
          </div>
        </div>
      </header>



      {/* Main Content */}
      <main className="content-section premium-content">
        <div className="container">
          <div className="auth-container premium-auth-container">


            {/* Tab Navigation */}
            <div className="tab-navigation premium-tab-navigation">
              <button
                onClick={() => setActiveTab('web-login')}
                className={`tab-button premium-tab-button ${activeTab === 'web-login' ? 'active' : ''}`}
              >
                <span className="tab-icon">🔐</span>
                Web Login
              </button>
              <button
                onClick={() => setActiveTab('web-register')}
                className={`tab-button premium-tab-button ${activeTab === 'web-register' ? 'active' : ''}`}
              >
                <span className="tab-icon">✨</span>
                {userType === 'shareholder' ? 'Create Account' : 'Register'}
              </button>
            </div>

            {/* Error/Success Messages - Hide login errors since they show under password field */}
            {error && activeTab !== 'web-login' && (
              <div className="alert alert-error">
                <div className="alert-content">
                  <span className="alert-icon">⚠️</span>
                  <p>{error}</p>
                </div>
              </div>
            )}

            {success && (
              <div className="alert alert-success">
                <div className="alert-content">
                  <span className="alert-icon">✅</span>
                  <p>{success}</p>
                </div>
              </div>
            )}

            {/* Content Cards */}
            <div className="auth-content premium-auth-content">

              {activeTab === 'web-login' && (
                <div className="card premium-card">
                  <div className="card-header premium-card-header">
                    <h2 className="card-title premium-card-title">Web Login</h2>
                    <p className="card-subtitle premium-card-subtitle">
                      Sign in to your existing account using your credentials.
                    </p>
                  </div>
                  <div className="card-content premium-card-content">
                    <form onSubmit={handleWebLogin} className="form">
                      <div className="form-group">
                        <label className="form-label">Email or Username</label>
                        <input
                          type="text"
                          required
                          value={loginForm.email}
                          onChange={(e) => handleLoginInputChange('email', e.target.value)}
                          className="form-input"
                          placeholder="Enter your email or username"
                        />
                      </div>
                      <div className="form-group">
                        <label className="form-label">Password</label>
                        <div className="password-input-container">
                          <input
                            type={showPassword ? "text" : "password"}
                            required
                            value={loginForm.password}
                            onChange={(e) => handleLoginInputChange('password', e.target.value)}
                            className="form-input password-input"
                            placeholder="Enter your password"
                          />
                          <button
                            type="button"
                            className="password-toggle-btn"
                            onClick={() => setShowPassword(!showPassword)}
                            aria-label={showPassword ? "Hide password" : "Show password"}
                          >
                            {showPassword ? (
                              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                                <line x1="1" y1="1" x2="23" y2="23"/>
                              </svg>
                            ) : (
                              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                <circle cx="12" cy="12" r="3"/>
                              </svg>
                            )}
                          </button>
                        </div>
                        {/* Password-specific error message */}
                        {error && activeTab === 'web-login' && (
                          <div className="field-error-message">
                            <span className="error-icon">⚠️</span>
                            <span>{error}</span>
                          </div>
                        )}
                      </div>
                      <button
                        type="submit"
                        disabled={isLoading}
                        className="btn btn-primary btn-lg"
                      >
                        {isLoading ? 'Signing In...' : 'Sign In'}
                      </button>

                      <div className="forgot-password-link">
                        <div className="flex justify-center items-center gap-4">
                          <button
                            type="button"
                            onClick={() => {
                              // Reset all form states when opening modal
                              setResetStep('email');
                              setForgotPasswordEmail('');
                              setResetPin('');
                              setNewPassword('');
                              setConfirmNewPassword('');
                              setError(null);
                              setSuccess(null);
                              setShowForgotPassword(true);
                            }}
                            className="link-button"
                          >
                            Forgot Password?
                          </button>
                          <span className="text-gray-500">|</span>
                          <button
                            type="button"
                            onClick={() => {
                              setError(null);
                              setSuccess(null);
                              setShowTelegramUserReset(true);
                            }}
                            className="link-button text-purple-400 hover:text-purple-300"
                          >
                            📱 Telegram User Reset
                          </button>
                        </div>
                      </div>


                    </form>
                  </div>
                </div>
              )}

              {activeTab === 'web-register' && (
                <div className="card premium-card">
                  <div className="card-header premium-card-header">
                    <h2 className="card-title premium-card-title">
                      {userType === 'shareholder' ? 'Create Shareholder Account' : 'Create Account'}
                    </h2>
                    <p className="card-subtitle premium-card-subtitle">
                      {userType === 'shareholder'
                        ? 'Join Aureus Alliance Holdings and start your gold share ownership journey.'
                        : 'Create a new account to access all features and benefits.'
                      }
                    </p>
                  </div>
                  <div className="card-content premium-card-content">
                    <EmailRegistrationFormProgressive
                      onRegistrationSuccess={onAuthSuccess}
                      userType={userType}
                      onSwitchToLogin={() => setActiveTab('web-login')}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>

      {/* Forgot Password Modal */}
      {showForgotPassword && (
        <div className="modal-overlay" onClick={() => {
          setShowForgotPassword(false);
          setResetStep('email');
          setForgotPasswordEmail('');
          setResetPin('');
          setNewPassword('');
          setConfirmNewPassword('');
          setError(null);
          setSuccess(null);
        }}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>Reset Password</h2>
              <button onClick={() => {
                setShowForgotPassword(false);
                setResetStep('email');
                setForgotPasswordEmail('');
                setResetPin('');
                setNewPassword('');
                setConfirmNewPassword('');
                setError(null);
                setSuccess(null);
              }} className="modal-close">
                ×
              </button>
            </div>
            <div className="modal-body">
              {/* Debug info - remove in production */}
              <div style={{ fontSize: '12px', color: '#666', marginBottom: '10px' }}>
                Current step: {resetStep}
              </div>

              {resetStep === 'email' && (
                <div className="reset-step">
                  <p>Enter your email address and we'll send you a PIN to reset your password.</p>
                  <div className="form-group">
                    <label className="form-label">Email Address</label>
                    <input
                      type="email"
                      value={forgotPasswordEmail}
                      onChange={(e) => setForgotPasswordEmail(e.target.value)}
                      className="form-input"
                      placeholder="Enter your email address"
                    />
                  </div>
                  <button
                    onClick={() => handleSendResetPin()}
                    disabled={isLoading || !forgotPasswordEmail}
                    className="btn btn-primary"
                  >
                    {isLoading ? 'Sending...' : 'Send Reset PIN'}
                  </button>
                </div>
              )}

              {resetStep === 'pin' && (
                <div className="reset-step">
                  <p>Enter the 6-digit PIN sent to <strong>{forgotPasswordEmail}</strong></p>
                  <div className="form-group">
                    <label className="form-label">Reset PIN</label>
                    <input
                      type="text"
                      value={resetPin}
                      onChange={(e) => setResetPin(e.target.value.replace(/\D/g, ''))}
                      className="form-input pin-input"
                      placeholder="000000"
                      maxLength={6}
                      style={{
                        textAlign: 'center',
                        fontSize: '18px',
                        letterSpacing: '4px',
                        fontFamily: 'monospace'
                      }}
                    />
                  </div>
                  <div className="button-group">
                    <button
                      onClick={() => setResetStep('email')}
                      className="btn btn-secondary"
                      style={{ marginRight: '10px' }}
                    >
                      Back
                    </button>
                    <button
                      onClick={() => handleVerifyPin()}
                      disabled={isLoading || resetPin.length !== 6}
                      className="btn btn-primary"
                    >
                      {isLoading ? 'Verifying...' : 'Verify PIN'}
                    </button>
                  </div>
                </div>
              )}

              {resetStep === 'password' && (
                <div className="reset-step">
                  <p>Enter your new password.</p>
                  <div className="form-group">
                    <label className="form-label">New Password</label>
                    <div className="password-input-container">
                      <input
                        type={showNewPassword ? "text" : "password"}
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        className="form-input password-input"
                        placeholder="Enter new password"
                      />
                      <button
                        type="button"
                        className="password-toggle-btn"
                        onClick={() => setShowNewPassword(!showNewPassword)}
                        aria-label={showNewPassword ? "Hide password" : "Show password"}
                      >
                        {showNewPassword ? (
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                            <line x1="1" y1="1" x2="23" y2="23"/>
                          </svg>
                        ) : (
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                            <circle cx="12" cy="12" r="3"/>
                          </svg>
                        )}
                      </button>
                    </div>
                  </div>
                  <div className="form-group">
                    <label className="form-label">Confirm New Password</label>
                    <div className="password-input-container">
                      <input
                        type={showConfirmNewPassword ? "text" : "password"}
                        value={confirmNewPassword}
                        onChange={(e) => setConfirmNewPassword(e.target.value)}
                        className="form-input password-input"
                        placeholder="Confirm new password"
                      />
                      <button
                        type="button"
                        className="password-toggle-btn"
                        onClick={() => setShowConfirmNewPassword(!showConfirmNewPassword)}
                        aria-label={showConfirmNewPassword ? "Hide password" : "Show password"}
                      >
                        {showConfirmNewPassword ? (
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                            <line x1="1" y1="1" x2="23" y2="23"/>
                          </svg>
                        ) : (
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                            <circle cx="12" cy="12" r="3"/>
                          </svg>
                        )}
                      </button>
                    </div>
                  </div>
                  <button
                    onClick={() => handleResetPassword()}
                    disabled={isLoading || !newPassword || newPassword !== confirmNewPassword}
                    className="btn btn-primary"
                  >
                    {isLoading ? 'Resetting...' : 'Reset Password'}
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Terms and Conditions Modal */}
      {showTermsModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h2>Terms and Conditions</h2>
              <button onClick={() => setShowTermsModal(false)} className="modal-close">
                ×
              </button>
            </div>
            <div className="modal-body">
              <TermsAndConditions />
            </div>
          </div>
        </div>
      )}

      {/* Privacy Policy Modal */}
      {showPrivacyModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h2>Privacy Policy</h2>
              <button onClick={() => setShowPrivacyModal(false)} className="modal-close">
                ×
              </button>
            </div>
            <div className="modal-body">
              <PrivacyPolicy />
            </div>
          </div>
        </div>
      )}

      {/* Telegram User Reset Modal */}
      {showTelegramUserReset && (
        <TelegramUserResetModal
          isOpen={showTelegramUserReset}
          onClose={() => setShowTelegramUserReset(false)}
          onError={setError}
          onSuccess={setSuccess}
          onLoading={setIsLoading}
        />
      )}

      {/* Password Toggle Styles */}
      <style>{`
        .password-input-container {
          position: relative;
          display: flex;
          align-items: center;
        }

        .password-input {
          padding-right: 45px !important;
        }

        .password-toggle-btn {
          position: absolute;
          right: 12px;
          top: 50%;
          transform: translateY(-50%);
          background: none;
          border: none;
          color: #9ca3af;
          cursor: pointer;
          padding: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          transition: all 0.2s ease;
        }

        .password-toggle-btn:hover {
          color: #ffc107;
          background-color: rgba(255, 193, 7, 0.1);
        }

        .password-toggle-btn:focus {
          outline: none;
          color: #ffc107;
          background-color: rgba(255, 193, 7, 0.1);
        }

        .password-toggle-btn svg {
          width: 18px;
          height: 18px;
        }

        .field-error-message {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-top: 8px;
          padding: 8px 12px;
          background-color: rgba(239, 68, 68, 0.1);
          border: 1px solid rgba(239, 68, 68, 0.3);
          border-radius: 6px;
          color: #fca5a5;
          font-size: 14px;
        }

        .error-icon {
          color: #ef4444;
          font-size: 16px;
        }

        .forgot-password-link {
          text-align: center;
          margin-top: 16px;
        }

        .link-button {
          background: none;
          border: none;
          color: #ffc107;
          cursor: pointer;
          font-size: 14px;
          text-decoration: underline;
          padding: 4px 8px;
          border-radius: 4px;
          transition: all 0.2s ease;
        }

        .link-button:hover {
          color: #ffcd39;
          background-color: rgba(255, 193, 7, 0.1);
        }

        .reset-step {
          padding: 16px 0;
        }

        .reset-step p {
          margin-bottom: 16px;
          color: #e5e7eb;
          font-size: 14px;
          line-height: 1.5;
        }

        .button-group {
          display: flex;
          gap: 10px;
          align-items: center;
        }

        .btn-secondary {
          background-color: #374151;
          color: #e5e7eb;
          border: 1px solid #4b5563;
        }

        .btn-secondary:hover {
          background-color: #4b5563;
          color: #ffffff;
        }

        .pin-input {
          background-color: #1f2937 !important;
          border: 2px solid #374151 !important;
          color: #ffffff !important;
        }

        .pin-input:focus {
          border-color: #ffc107 !important;
          box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.1) !important;
        }

        /* Mobile-specific styles for better touch interaction */
        .mobile-alert {
          font-size: 14px;
          padding: 12px;
          border-radius: 8px;
          margin-bottom: 16px;
        }

        .mobile-alert .alert-content {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
        }

        .mobile-alert .alert-dismiss {
          min-width: 24px;
          min-height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        @media (max-width: 768px) {
          .btn {
            min-height: 48px !important;
            font-size: 16px !important;
            padding: 12px 24px !important;
            touch-action: manipulation;
          }

          .mobile-notice {
            font-size: 13px !important;
            padding: 10px !important;
            margin-bottom: 12px !important;
          }



          .pin-display-section {
            margin-bottom: 16px !important;
          }

          .alert {
            font-size: 14px !important;
            padding: 12px !important;
          }

          .card-content {
            padding: 16px !important;
          }

          .auth-tabs {
            margin-bottom: 16px !important;
          }

          .auth-tabs button {
            min-height: 44px !important;
            font-size: 14px !important;
          }
        }
      `}</style>

      {/* Version Footer */}
      <div style={{
        padding: '20px',
        textAlign: 'center',
        borderTop: '1px solid rgba(255, 215, 0, 0.1)',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        marginTop: '20px'
      }}>
        <p style={{
          color: '#666',
          fontSize: '12px',
          margin: 0
        }}>
          Aureus Africa {getFullVersion()} • {new Date().getFullYear()} Aureus Alliance Holdings (Pty) Ltd
        </p>
      </div>
    </div>
  );
};

export default UnifiedAuthPageClean;
