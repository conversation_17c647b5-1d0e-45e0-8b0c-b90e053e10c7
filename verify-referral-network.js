#!/usr/bin/env node

/**
 * Verify Referral Network
 * 
 * This script verifies that User 106 now appears correctly
 * in the referral network and checks overall network health.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ Missing SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function verifyReferralNetwork() {
  try {
    console.log('🔍 Verifying Referral Network\n');
    
    // Step 1: Check User 106's referral status
    console.log('👤 Step 1: User 106 Referral Status');
    
    const { data: user106Referrals, error: ref106Error } = await supabase
      .from('referrals')
      .select(`
        *,
        referrer:users!referrer_id(id, username, full_name),
        referred:users!referred_id(id, username, full_name, email)
      `)
      .eq('referred_id', 106);
    
    if (ref106Error) {
      console.error('❌ Error checking User 106 referrals:', ref106Error);
      return;
    }
    
    console.log(`   User 106 referral relationships: ${user106Referrals.length}`);
    user106Referrals.forEach(ref => {
      console.log(`   • Sponsor: ${ref.referrer?.full_name || ref.referrer?.username} (ID: ${ref.referrer_id})`);
      console.log(`     Status: ${ref.status}, Rate: ${ref.commission_rate}%`);
      console.log(`     Created: ${ref.created_at}`);
    });
    
    // Step 2: Check User 106's commission status
    console.log('\n💰 Step 2: User 106 Commission Status');
    
    const { data: user106Commissions, error: comm106Error } = await supabase
      .from('commission_transactions')
      .select(`
        *,
        referrer:users!referrer_id(id, username, full_name)
      `)
      .eq('referred_id', 106);
    
    if (comm106Error) {
      console.error('❌ Error checking User 106 commissions:', comm106Error);
    } else {
      console.log(`   Commission transactions for User 106: ${user106Commissions.length}`);
      user106Commissions.forEach(comm => {
        console.log(`   • To: ${comm.referrer?.full_name || comm.referrer?.username}`);
        console.log(`     USDT: $${comm.usdt_commission}, Shares: ${comm.share_commission}`);
        console.log(`     Amount: $${comm.share_purchase_amount}, Status: ${comm.status}`);
        console.log(`     Date: ${comm.payment_date}`);
      });
    }
    
    // Step 3: Check TTTFOUNDER's updated commission balance
    console.log('\n👑 Step 3: TTTFOUNDER Commission Balance');
    
    const { data: tttfounderBalance, error: balanceError } = await supabase
      .from('commission_balances')
      .select('*')
      .eq('user_id', 4) // TTTFOUNDER ID
      .single();
    
    if (balanceError) {
      console.error('❌ Error checking TTTFOUNDER balance:', balanceError);
    } else {
      console.log(`   USDT Balance: $${tttfounderBalance.usdt_balance || 0}`);
      console.log(`   Share Balance: ${tttfounderBalance.share_balance || 0}`);
      console.log(`   Total Earned USDT: $${tttfounderBalance.total_earned_usdt || 0}`);
      console.log(`   Total Earned Shares: ${tttfounderBalance.total_earned_shares || 0}`);
      console.log(`   Last Updated: ${tttfounderBalance.last_updated}`);
    }
    
    // Step 4: Overall referral network health
    console.log('\n🌐 Step 4: Referral Network Health Check');
    
    const { count: totalReferrals } = await supabase
      .from('referrals')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'active');
    
    const { count: totalUsers } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true);
    
    console.log(`   Total active users: ${totalUsers}`);
    console.log(`   Total active referrals: ${totalReferrals}`);
    console.log(`   Coverage: ${((totalReferrals / totalUsers) * 100).toFixed(1)}%`);
    
    // Step 5: Check for users without sponsors
    const { data: usersWithoutSponsors, error: noSponsorError } = await supabase
      .from('users')
      .select(`
        id, username, full_name, email,
        referrals_as_referred:referrals!referred_id(referrer_id)
      `)
      .eq('is_active', true)
      .is('referrals_as_referred.referrer_id', null)
      .limit(10);
    
    if (noSponsorError) {
      console.error('❌ Error checking users without sponsors:', noSponsorError);
    } else {
      console.log(`\n👥 Users without sponsors (showing first 10): ${usersWithoutSponsors.length}`);
      usersWithoutSponsors.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.full_name || user.username} (ID: ${user.id})`);
        console.log(`      Email: ${user.email}`);
      });
      
      if (usersWithoutSponsors.length > 0) {
        console.log('\n💡 These users should have TTTFOUNDER as their sponsor');
        console.log('   according to the automatic sponsor assignment system.');
      }
    }
    
    // Step 6: Recent commission activity
    console.log('\n📊 Step 6: Recent Commission Activity');
    
    const { data: recentCommissions, error: recentError } = await supabase
      .from('commission_transactions')
      .select(`
        *,
        referrer:users!referrer_id(id, username, full_name),
        referred:users!referred_id(id, username, full_name)
      `)
      .eq('status', 'approved')
      .order('payment_date', { ascending: false })
      .limit(5);
    
    if (recentError) {
      console.error('❌ Error checking recent commissions:', recentError);
    } else {
      console.log(`   Recent commission transactions: ${recentCommissions.length}`);
      recentCommissions.forEach((comm, index) => {
        console.log(`   ${index + 1}. ${comm.referred?.full_name || comm.referred?.username} → ${comm.referrer?.full_name || comm.referrer?.username}`);
        console.log(`      USDT: $${comm.usdt_commission}, Shares: ${comm.share_commission}`);
        console.log(`      Purchase: $${comm.share_purchase_amount}, Date: ${comm.payment_date}`);
        console.log('');
      });
    }
    
    // Step 7: Summary
    console.log('📋 Step 7: Summary');
    console.log('');
    
    const user106HasSponsor = user106Referrals.length > 0;
    const user106HasCommission = user106Commissions.length > 0;
    
    if (user106HasSponsor && user106HasCommission) {
      console.log('✅ SUCCESS: User 106 issues have been resolved!');
      console.log('   • User 106 now has TTTFOUNDER as sponsor');
      console.log('   • Commission has been distributed');
      console.log('   • User 106 should now appear in referral network');
    } else {
      console.log('⚠️  PARTIAL: Some issues may remain:');
      if (!user106HasSponsor) console.log('   • User 106 still has no sponsor');
      if (!user106HasCommission) console.log('   • User 106 commission not distributed');
    }
    
    console.log('\n🎯 Next Steps:');
    console.log('   1. Test the referral network display in admin panel');
    console.log('   2. Verify User 106 appears in the network');
    console.log('   3. Check that future share purchases trigger commissions automatically');
    console.log('   4. Consider implementing automatic sponsor assignment for existing users');
    
  } catch (error) {
    console.error('❌ Verification failed:', error);
  }
}

verifyReferralNetwork();
