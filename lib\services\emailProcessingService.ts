import { getServiceRoleClient } from '../supabase';
import { EmailTriggerService } from './emailTriggerService';

export class EmailProcessingService {
  private static instance: EmailProcessingService;
  private triggerService: EmailTriggerService;
  private isProcessing: boolean = false;

  private constructor() {
    this.triggerService = EmailTriggerService.getInstance();
  }

  public static getInstance(): EmailProcessingService {
    if (!EmailProcessingService.instance) {
      EmailProcessingService.instance = new EmailProcessingService();
    }
    return EmailProcessingService.instance;
  }

  // Process pending email notifications
  public async processPendingNotifications(): Promise<void> {
    if (this.isProcessing) {
      console.log('Email processing already in progress, skipping...');
      return;
    }

    this.isProcessing = true;
    
    try {
      const serviceClient = getServiceRoleClient();
      
      // Get pending notifications
      const { data: pendingNotifications, error } = await serviceClient
        .from('email_notifications')
        .select('*')
        .eq('status', 'pending')
        .order('created_at', { ascending: true })
        .limit(50); // Process in batches

      if (error) {
        console.error('Error fetching pending notifications:', error);
        return;
      }

      if (!pendingNotifications || pendingNotifications.length === 0) {
        console.log('No pending email notifications to process');
        return;
      }

      console.log(`Processing ${pendingNotifications.length} pending email notifications`);

      for (const notification of pendingNotifications) {
        try {
          await this.processNotification(notification);
          
          // Small delay between processing to avoid overwhelming the email service
          await new Promise(resolve => setTimeout(resolve, 500));
        } catch (error) {
          console.error(`Error processing notification ${notification.id}:`, error);
          
          // Mark as failed
          await serviceClient
            .from('email_notifications')
            .update({
              status: 'failed',
              error_message: error instanceof Error ? error.message : 'Unknown error',
              updated_at: new Date().toISOString()
            })
            .eq('id', notification.id);
        }
      }
    } catch (error) {
      console.error('Error in processPendingNotifications:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  private async processNotification(notification: any): Promise<void> {
    const serviceClient = getServiceRoleClient();
    
    try {
      // Mark as processing
      await serviceClient
        .from('email_notifications')
        .update({
          status: 'processing',
          updated_at: new Date().toISOString()
        })
        .eq('id', notification.id);

      // Process based on notification type
      switch (notification.notification_type) {
        case 'message':
          await this.triggerService.handleNewMessage(notification.content);
          break;
        case 'commission':
          await this.triggerService.handleNewCommission(notification.content);
          break;
        case 'referral':
          await this.triggerService.handleNewReferral(notification.content);
          break;
        case 'share_transfer':
          await this.triggerService.handleShareTransfer(notification.content);
          break;
        case 'share_purchase':
          await this.triggerService.handleSharePurchase(notification.content);
          break;
        default:
          throw new Error(`Unknown notification type: ${notification.notification_type}`);
      }

      // Mark as completed
      await serviceClient
        .from('email_notifications')
        .update({
          status: 'sent',
          sent_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', notification.id);

    } catch (error) {
      // Mark as failed
      await serviceClient
        .from('email_notifications')
        .update({
          status: 'failed',
          error_message: error instanceof Error ? error.message : 'Unknown error',
          updated_at: new Date().toISOString()
        })
        .eq('id', notification.id);
      
      throw error;
    }
  }

  // Start automatic processing (call this on app startup)
  public startAutomaticProcessing(): void {
    // Process immediately
    this.processPendingNotifications();
    
    // Set up interval to process every 30 seconds
    setInterval(() => {
      this.processPendingNotifications();
    }, 30000);

    console.log('Email processing service started - checking every 30 seconds');
  }

  // Manual trigger for immediate processing
  public async triggerImmediateProcessing(): Promise<void> {
    console.log('Triggering immediate email processing...');
    await this.processPendingNotifications();
  }

  // Get notification statistics
  public async getNotificationStats(): Promise<{
    pending: number;
    sent: number;
    failed: number;
    total: number;
  }> {
    try {
      const serviceClient = getServiceRoleClient();
      
      const [pendingResult, sentResult, failedResult, totalResult] = await Promise.all([
        serviceClient
          .from('email_notifications')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'pending'),
        serviceClient
          .from('email_notifications')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'sent'),
        serviceClient
          .from('email_notifications')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'failed'),
        serviceClient
          .from('email_notifications')
          .select('*', { count: 'exact', head: true })
      ]);

      return {
        pending: pendingResult.count || 0,
        sent: sentResult.count || 0,
        failed: failedResult.count || 0,
        total: totalResult.count || 0
      };
    } catch (error) {
      console.error('Error getting notification stats:', error);
      return { pending: 0, sent: 0, failed: 0, total: 0 };
    }
  }

  // Clean up old notifications (older than 30 days)
  public async cleanupOldNotifications(): Promise<void> {
    try {
      const serviceClient = getServiceRoleClient();
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
      
      const { error } = await serviceClient
        .from('email_notifications')
        .delete()
        .lt('created_at', thirtyDaysAgo)
        .in('status', ['sent', 'failed']);

      if (error) {
        console.error('Error cleaning up old notifications:', error);
      } else {
        console.log('Successfully cleaned up old email notifications');
      }
    } catch (error) {
      console.error('Error in cleanupOldNotifications:', error);
    }
  }

  // Retry failed notifications
  public async retryFailedNotifications(): Promise<void> {
    try {
      const serviceClient = getServiceRoleClient();
      
      // Reset failed notifications that haven't exceeded retry limit back to pending
      const { error } = await serviceClient
        .from('email_notifications')
        .update({
          status: 'pending',
          updated_at: new Date().toISOString()
        })
        .eq('status', 'failed')
        .lt('retry_count', 3);

      if (error) {
        console.error('Error retrying failed notifications:', error);
      } else {
        console.log('Reset failed notifications to pending for retry');
        // Process them immediately
        await this.processPendingNotifications();
      }
    } catch (error) {
      console.error('Error in retryFailedNotifications:', error);
    }
  }

  // Get recent notification history for a user
  public async getUserNotificationHistory(userId: number, limit: number = 20): Promise<any[]> {
    try {
      const serviceClient = getServiceRoleClient();
      
      const { data, error } = await serviceClient
        .from('email_notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching user notification history:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getUserNotificationHistory:', error);
      return [];
    }
  }
}
