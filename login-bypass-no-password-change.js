
// LOGIN BYPASS - NO PASSWORD CHANGE NEEDED
// This bypasses the password check entirely

const bypassLogin = async () => {
  console.log('🔧 Bypassing login for Telegram ID: **********');
  
  try {
    // Get user data directly from database
    const { data: telegramUser } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', **********)
      .single();
    
    if (!telegramUser) {
      alert('Telegram user not found');
      return;
    }
    
    const { data: linkedUser } = await supabase
      .from('users')
      .select('*')
      .eq('id', telegramUser.user_id)
      .single();
    
    if (!linkedUser) {
      alert('User account not found');
      return;
    }
    
    // Create complete session without password verification
    const sessionData = {
      userId: linkedUser.id,
      username: linkedUser.username,
      email: linkedUser.email,
      fullName: linkedUser.full_name || linkedUser.username,
      phone: linkedUser.phone,
      address: linkedUser.address,
      country: linkedUser.country_of_residence,
      isActive: linkedUser.is_active,
      isVerified: linkedUser.is_verified,
      isAdmin: linkedUser.is_admin,
      telegramId: telegramUser.telegram_id,
      telegramUsername: telegramUser.username,
      telegramConnected: true,
      telegramRegistered: telegramUser.is_registered,
      loginMethod: 'telegram_bypass',
      sessionStart: new Date().toISOString(),
      lastActivity: new Date().toISOString()
    };
    
    // Store complete session data
    localStorage.setItem('aureus_session', JSON.stringify(sessionData));
    localStorage.setItem('aureus_user', JSON.stringify({
      ...linkedUser,
      telegram_id: telegramUser.telegram_id,
      telegram_username: telegramUser.username,
      telegram_connected: true
    }));
    localStorage.setItem('telegram_user', JSON.stringify({
      telegram_id: telegramUser.telegram_id,
      username: telegramUser.username,
      user_id: telegramUser.user_id,
      is_registered: telegramUser.is_registered,
      connected: true
    }));
    localStorage.setItem('aureus_telegram_user', JSON.stringify({
      id: 'telegram_' + telegramUser.telegram_id,
      email: linkedUser.email,
      database_user: {
        ...linkedUser,
        telegram_connected: true
      },
      account_type: 'telegram_direct',
      user_metadata: {
        telegram_id: telegramUser.telegram_id,
        telegram_username: telegramUser.username,
        full_name: linkedUser.full_name,
        username: linkedUser.username,
        telegram_connected: true,
        telegram_registered: telegramUser.is_registered
      }
    }));
    
    console.log('✅ Login bypass successful - no password needed');
    console.log('🔄 Redirecting to dashboard...');
    
    // Redirect to dashboard
    window.location.href = '/dashboard';
    
  } catch (error) {
    console.error('❌ Login bypass failed:', error);
    alert('Login bypass failed: ' + error.message);
  }
};

// Run the bypass
bypassLogin();
