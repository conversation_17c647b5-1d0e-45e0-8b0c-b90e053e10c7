import React, { useState } from 'react';
import { useOnboarding } from '../../lib/hooks/useOnboarding';
import { OnboardingStep } from '../../lib/services/onboardingService';
import { OnboardingDebugger } from './OnboardingDebugger';

interface OnboardingDashboardProps {
  userId: number;
  onStepComplete?: (stepId: string) => void;
  onComplete?: () => void;
  compact?: boolean;
}

export const OnboardingDashboard: React.FC<OnboardingDashboardProps> = ({
  userId,
  onStepComplete,
  onComplete,
  compact = false
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showAchievements, setShowAchievements] = useState(false);

  const {
    status,
    loading,
    error,
    startStep,
    completeStep,
    skipStep,
    isComplete,
    progressPercentage,
    estimatedTimeRemaining,
    achievements
  } = useOnboarding({ userId, autoLoad: true });

  // Debug logging
  console.log('🔍 OnboardingDashboard status:', status);
  console.log('🔍 Current step:', status?.current_step);



  const categories = [
    { id: 'all', name: 'All Steps', icon: '📋' },
    { id: 'account', name: 'Account', icon: '👤' },
    { id: 'verification', name: 'Verification', icon: '🆔' },
    { id: 'financial', name: 'Financial', icon: '💰' },
    { id: 'social', name: 'Social', icon: '👥' },
    { id: 'advanced', name: 'Advanced', icon: '🚀' }
  ];

  const handleStepAction = async (step: OnboardingStep, action: 'start' | 'complete' | 'skip') => {
    let success = false;

    switch (action) {
      case 'start':
        success = await startStep(step.id);
        break;
      case 'complete':
        success = await completeStep(step.id);
        break;
      case 'skip':
        success = await skipStep(step.id);
        break;
    }

    if (success) {
      onStepComplete?.(step.id);

      if (isComplete) {
        onComplete?.();
      }
    }
  };



  const getStepButtonText = (step: OnboardingStep) => {
    if (status?.current_step?.id === step.id) return 'Continue';
    if (status?.next_steps.some(s => s.id === step.id)) return 'Start';
    return 'Completed';
  };

  const getStepButtonColor = (step: OnboardingStep) => {
    if (status?.current_step?.id === step.id) return 'bg-blue-600 hover:bg-blue-500';
    if (status?.next_steps.some(s => s.id === step.id)) return 'bg-gold hover:bg-yellow-500';
    return 'bg-green-600 hover:bg-green-500';
  };

  const filteredSteps = status?.next_steps.filter(step => 
    selectedCategory === 'all' || step.category === selectedCategory
  ) || [];

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gold"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-6">
        <p className="text-red-300">❌ {error}</p>
      </div>
    );
  }

  if (compact) {
    return (
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Getting Started</h3>
          <span className="text-sm text-gray-400">
            {status?.completed_steps}/{status?.total_steps} completed
          </span>
        </div>

        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex justify-between text-sm text-gray-400 mb-1">
            <span>Progress</span>
            <span>{progressPercentage}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div 
              className="bg-gold h-2 rounded-full transition-all duration-300"
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
        </div>

        {/* Next Steps */}
        {status?.next_steps.slice(0, 2).map(step => (
          <div key={step.id} className="flex items-center justify-between py-2 border-b border-gray-700 last:border-b-0">
            <div className="flex items-center gap-3">
              <span className="text-lg">{step.icon}</span>
              <div>
                <p className="text-white text-sm font-medium">{step.name}</p>
                <p className="text-gray-400 text-xs">{step.estimatedTime} min</p>
              </div>
            </div>
            <button
              onClick={() => handleStepAction(step, 'start')}
              className="px-3 py-1 bg-gold text-black text-xs font-medium rounded hover:bg-yellow-500 transition-colors"
            >
              Start
            </button>
          </div>
        ))}

        {status?.next_steps.length === 0 && isComplete && (
          <div className="text-center py-4">
            <span className="text-2xl">🎉</span>
            <p className="text-green-400 font-medium mt-2">All done!</p>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Debug Information */}
      <OnboardingDebugger userId={userId} />

      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gold mb-2">Getting Started</h1>
        <p className="text-gray-300">
          Complete these steps to unlock all features and maximize your Aureus Africa experience.
        </p>
      </div>

      {/* Progress Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-gold mb-2">{progressPercentage}%</div>
            <div className="text-gray-300">Complete</div>
            <div className="w-full bg-gray-700 rounded-full h-2 mt-3">
              <div 
                className="bg-gold h-2 rounded-full transition-all duration-300"
                style={{ width: `${progressPercentage}%` }}
              ></div>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-400 mb-2">{status?.completed_steps}</div>
            <div className="text-gray-300">Steps Done</div>
            <div className="text-sm text-gray-400 mt-1">
              of {status?.total_steps} total
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-green-400 mb-2">{achievements.length}</div>
            <div className="text-gray-300">Achievements</div>
            <button
              onClick={() => setShowAchievements(true)}
              className="text-sm text-gold hover:text-yellow-500 mt-1"
            >
              View All
            </button>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-400 mb-2">{estimatedTimeRemaining}</div>
            <div className="text-gray-300">Minutes Left</div>
            <div className="text-sm text-gray-400 mt-1">
              estimated time
            </div>
          </div>
        </div>
      </div>

      {/* Category Filter */}
      <div className="flex flex-wrap gap-2 mb-6">
        {categories.map(category => (
          <button
            key={category.id}
            onClick={() => setSelectedCategory(category.id)}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              selectedCategory === category.id
                ? 'bg-gold text-black'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            <span className="mr-2">{category.icon}</span>
            {category.name}
          </button>
        ))}
      </div>

      {/* Current Step Highlight */}
      {status?.current_step && (
        <div className="bg-gradient-to-r from-blue-900/50 to-purple-900/50 rounded-lg border border-blue-500/30 p-6 mb-6">

          <div className="flex items-start gap-4">
            <span className="text-3xl">{status.current_step.icon}</span>
            <div className="flex-1">
              <h3 className="text-xl font-semibold text-white mb-2">
                Continue: {status.current_step.name}
              </h3>
              <p className="text-gray-300 mb-4">{status.current_step.description}</p>
              <div className="flex items-center gap-4">
                <button
                  onClick={() => handleStepAction(status.current_step!, 'complete')}
                  className="px-6 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-500 transition-colors"
                >
                  Continue Step
                </button>
                <span className="text-sm text-gray-400">
                  ⏱️ {status.current_step.estimatedTime} minutes
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Available Steps */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredSteps.map(step => {

          // Regular step rendering for all other steps
          return (
            <div key={step.id} className="bg-gray-800 rounded-lg border border-gray-700 p-6 hover:border-gray-600 transition-colors">
              <div className="flex items-start gap-4">
                <span className="text-2xl">{step.icon}</span>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-white mb-2">{step.name}</h3>
                  <p className="text-gray-400 text-sm mb-4">{step.description}</p>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4 text-sm text-gray-400">
                    <span>⏱️ {step.estimatedTime} min</span>
                    {step.required && <span className="text-red-400">Required</span>}
                  </div>
                </div>

                {step.unlocks && step.unlocks.length > 0 && (
                  <div className="mt-3">
                    <p className="text-xs text-gray-500 mb-1">Unlocks:</p>
                    <div className="flex flex-wrap gap-1">
                      {step.unlocks.slice(0, 2).map(unlock => (
                        <span key={unlock} className="px-2 py-1 bg-green-900/30 text-green-400 text-xs rounded">
                          {unlock.replace('_', ' ')}
                        </span>
                      ))}
                      {step.unlocks.length > 2 && (
                        <span className="px-2 py-1 bg-gray-700 text-gray-400 text-xs rounded">
                          +{step.unlocks.length - 2} more
                        </span>
                      )}
                    </div>
                  </div>
                )}

                <div className="flex gap-2 mt-4">
                  <button
                    onClick={() => handleStepAction(step, 'start')}
                    className={`flex-1 px-4 py-2 text-white font-medium rounded-lg transition-colors ${getStepButtonColor(step)}`}
                  >
                    {getStepButtonText(step)}
                  </button>
                  {!step.required && (
                    <button
                      onClick={() => handleStepAction(step, 'skip')}
                      className="px-3 py-2 bg-gray-600 text-gray-300 font-medium rounded-lg hover:bg-gray-500 transition-colors"
                      title="Skip this step"
                    >
                      Skip
                    </button>
                  )}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {filteredSteps.length === 0 && !isComplete && (
        <div className="text-center py-12">
          <span className="text-4xl mb-4 block">🎯</span>
          <h3 className="text-xl font-semibold text-white mb-2">No steps available</h3>
          <p className="text-gray-400">Complete the current step to unlock more options.</p>
        </div>
      )}

      {isComplete && (
        <div className="text-center py-12">
          <span className="text-6xl mb-4 block">🎉</span>
          <h3 className="text-2xl font-bold text-gold mb-2">Congratulations!</h3>
          <p className="text-gray-300 mb-6">
            You've completed your onboarding journey. Welcome to Aureus Africa!
          </p>
          <div className="flex justify-center gap-4">
            <button
              onClick={() => setShowAchievements(true)}
              className="px-6 py-3 bg-gold text-black font-medium rounded-lg hover:bg-yellow-500 transition-colors"
            >
              View Achievements
            </button>
          </div>
        </div>
      )}

      {/* Achievements Modal */}
      {showAchievements && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="max-w-2xl w-full bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-white">Your Achievements</h3>
              <button
                onClick={() => setShowAchievements(false)}
                className="text-gray-400 hover:text-white"
              >
                ✕
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {achievements.map(achievement => (
                <div key={achievement.id} className="bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-2">
                    <span className="text-2xl">{achievement.icon}</span>
                    <div>
                      <h4 className="font-semibold text-white">{achievement.name}</h4>
                      <p className="text-sm text-gray-400">{achievement.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {achievements.length === 0 && (
              <div className="text-center py-8">
                <span className="text-4xl mb-4 block">🏆</span>
                <p className="text-gray-400">Complete steps to earn achievements!</p>
              </div>
            )}
          </div>
        </div>
      )}


    </div>
  );
};
