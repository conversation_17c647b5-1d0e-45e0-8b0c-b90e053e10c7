#!/usr/bin/env node

/**
 * ANALYZE EXISTING PASSWORD HASHES
 * 
 * This script analyzes the current password hashes in the database
 * to determine which users need password migration.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

class PasswordAnalyzer {
  constructor() {
    this.analysis = {
      totalUsers: 0,
      oldHashFormat: 0,
      newHashFormat: 0,
      nullHashes: 0,
      unknownFormat: 0,
      usersNeedingMigration: []
    };
  }

  async analyzePasswords() {
    console.log('🔍 ANALYZING EXISTING PASSWORD HASHES');
    console.log('=====================================\n');

    try {
      // Get all users with their password hashes
      const { data: users, error } = await supabase
        .from('users')
        .select('id, email, username, password_hash, created_at, updated_at')
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(`Failed to fetch users: ${error.message}`);
      }

      this.analysis.totalUsers = users.length;
      console.log(`📊 Found ${users.length} total users in database`);

      // Analyze each user's password hash
      for (const user of users) {
        this.analyzeUserHash(user);
      }

      this.generateAnalysisReport();
      this.generateMigrationPlan();

    } catch (error) {
      console.error('❌ Password analysis failed:', error);
    }
  }

  analyzeUserHash(user) {
    const hash = user.password_hash;

    if (!hash || hash === null) {
      this.analysis.nullHashes++;
      console.log(`⚠️ NULL hash: ${user.email} (ID: ${user.id})`);
      return;
    }

    // Check for old SHA-256 format (64 characters, hex)
    if (hash.length === 64 && /^[a-f0-9]+$/.test(hash)) {
      this.analysis.oldHashFormat++;
      this.analysis.usersNeedingMigration.push({
        id: user.id,
        email: user.email,
        username: user.username,
        hashType: 'SHA-256',
        created_at: user.created_at
      });
      console.log(`🔴 OLD HASH: ${user.email} - SHA-256 format (${hash.substring(0, 16)}...)`);
      return;
    }

    // Check for new bcrypt format
    if (/^\$2[aby]\$/.test(hash)) {
      this.analysis.newHashFormat++;
      console.log(`✅ NEW HASH: ${user.email} - bcrypt format`);
      return;
    }

    // Check for Telegram auth placeholder
    if (hash === 'telegram_auth') {
      console.log(`📱 TELEGRAM: ${user.email} - Telegram authentication`);
      return;
    }

    // Unknown format
    this.analysis.unknownFormat++;
    console.log(`❓ UNKNOWN: ${user.email} - Unknown format (${hash.substring(0, 20)}...)`);
  }

  generateAnalysisReport() {
    console.log('\n📋 PASSWORD HASH ANALYSIS REPORT');
    console.log('=================================');
    console.log(`Total Users: ${this.analysis.totalUsers}`);
    console.log(`Old SHA-256 Hashes: ${this.analysis.oldHashFormat} 🔴`);
    console.log(`New bcrypt Hashes: ${this.analysis.newHashFormat} ✅`);
    console.log(`NULL/Empty Hashes: ${this.analysis.nullHashes} ⚠️`);
    console.log(`Unknown Format: ${this.analysis.unknownFormat} ❓`);

    const migrationNeeded = this.analysis.oldHashFormat;
    const migrationPercentage = ((migrationNeeded / this.analysis.totalUsers) * 100).toFixed(1);

    console.log(`\n🚨 MIGRATION REQUIRED: ${migrationNeeded} users (${migrationPercentage}%)`);

    if (migrationNeeded > 0) {
      console.log('\n⚠️ CRITICAL: Users with old password hashes cannot login!');
      console.log('📋 ACTION REQUIRED: Run password migration immediately');
    } else {
      console.log('\n✅ GOOD: All users have secure password hashes');
      console.log('📋 NO MIGRATION NEEDED: System is secure');
    }
  }

  generateMigrationPlan() {
    if (this.analysis.usersNeedingMigration.length === 0) {
      console.log('\n🎉 NO MIGRATION NEEDED - ALL PASSWORDS SECURE');
      return;
    }

    console.log('\n📋 MIGRATION PLAN');
    console.log('=================');
    console.log('Due to security requirements, users with old password hashes');
    console.log('will need to reset their passwords to continue using the system.\n');

    console.log('🔄 MIGRATION STRATEGY:');
    console.log('1. Mark old hash users for password reset');
    console.log('2. Generate secure reset tokens');
    console.log('3. Send password reset emails');
    console.log('4. Clear old vulnerable hashes');
    console.log('5. Users create new secure passwords\n');

    console.log('👥 USERS REQUIRING MIGRATION:');
    console.log('==============================');
    
    this.analysis.usersNeedingMigration.forEach((user, index) => {
      const createdDate = new Date(user.created_at).toLocaleDateString();
      console.log(`${index + 1}. ${user.email} (${user.username}) - Created: ${createdDate}`);
    });

    console.log(`\n📊 MIGRATION STATISTICS:`);
    console.log(`Total users needing migration: ${this.analysis.usersNeedingMigration.length}`);
    console.log(`Estimated migration time: ${Math.ceil(this.analysis.usersNeedingMigration.length / 10)} minutes`);
    console.log(`User impact: Temporary login disruption until password reset`);

    console.log('\n🚨 NEXT STEPS:');
    console.log('1. Run: node scripts/migrate-passwords.js');
    console.log('2. Monitor migration progress');
    console.log('3. Send password reset notifications to users');
    console.log('4. Verify all users can login with new passwords');
  }
}

// Run the password analysis
const analyzer = new PasswordAnalyzer();
analyzer.analyzePasswords().catch(console.error);
