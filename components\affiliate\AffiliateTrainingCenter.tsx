/**
 * AFFILIATE TRAINING CENTER
 * 
 * Student-facing interface for the Training Academy system.
 * Displays available courses, tracks progress, and provides access to learning materials.
 */

import React, { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';

interface AffiliateTrainingCenterProps {
  userId: number;
  className?: string;
}

interface TrainingCourse {
  id: number;
  title: string;
  description: string;
  category_id: number;
  difficulty_level: 'Beginner' | 'Intermediate' | 'Advanced';
  estimated_duration: number;
  thumbnail_url?: string;
  status: 'draft' | 'under_review' | 'published' | 'archived';
  is_featured: boolean;
  enrollment_count: number;
  completion_count: number;
  average_rating: number;
  created_at: string;
  category?: {
    id: number;
    name: string;
    description: string;
    icon: string;
    color: string;
  };
}

interface UserEnrollment {
  id: number;
  course_id: number;
  user_id: number;
  enrolled_at: string;
  progress_percentage: number;
  last_accessed_at?: string;
  completed_at?: string;
}

export const AffiliateTrainingCenter: React.FC<AffiliateTrainingCenterProps> = ({
  userId,
  className = ""
}) => {
  const [courses, setCourses] = useState<TrainingCourse[]>([]);
  const [enrollments, setEnrollments] = useState<UserEnrollment[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedCourse, setSelectedCourse] = useState<TrainingCourse | null>(null);

  useEffect(() => {
    loadTrainingData();
  }, [userId]);

  const loadTrainingData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadCourses(),
        loadUserEnrollments()
      ]);
    } catch (error) {
      console.error('Error loading training data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadCourses = async () => {
    try {
      const serviceClient = getServiceRoleClient();
      const { data, error } = await serviceClient
        .from('training_courses')
        .select(`
          *,
          category:training_categories(*)
        `)
        .eq('status', 'published')
        .order('is_featured', { ascending: false })
        .order('created_at', { ascending: false });

      if (error) throw error;
      setCourses(data || []);
    } catch (error) {
      console.error('Error loading courses:', error);
    }
  };

  const loadUserEnrollments = async () => {
    try {
      const serviceClient = getServiceRoleClient();
      const { data, error } = await serviceClient
        .from('training_enrollments')
        .select('*')
        .eq('user_id', userId);

      if (error) throw error;
      setEnrollments(data || []);
    } catch (error) {
      console.error('Error loading enrollments:', error);
    }
  };

  const enrollInCourse = async (courseId: number) => {
    try {
      const serviceClient = getServiceRoleClient();
      const { error } = await serviceClient
        .from('training_enrollments')
        .insert({
          course_id: courseId,
          user_id: userId,
          enrolled_at: new Date().toISOString(),
          progress_percentage: 0
        });

      if (error) throw error;

      // Reload enrollments
      await loadUserEnrollments();

      // Show success message
      alert('Successfully enrolled in course!');
    } catch (error) {
      console.error('Error enrolling in course:', error);
      alert('Failed to enroll in course. Please try again.');
    }
  };

  const continueCourse = (course: TrainingCourse) => {
    setSelectedCourse(course);
  };

  const closeCourse = () => {
    setSelectedCourse(null);
  };

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case 'Beginner': return '#10b981';
      case 'Intermediate': return '#f59e0b';
      case 'Advanced': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getUserEnrollment = (courseId: number) => {
    return enrollments.find(e => e.course_id === courseId);
  };

  const filteredCourses = courses.filter(course => {
    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || 
                           course.category?.name.toLowerCase() === selectedCategory.toLowerCase();
    return matchesSearch && matchesCategory;
  });

  const categories = Array.from(new Set(courses.map(c => c.category?.name).filter(Boolean)));

  if (loading) {
    return (
      <div className={`${className} flex items-center justify-center h-64`}>
        <div className="text-white">Loading training courses...</div>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-xl p-6 text-white mb-6">
        <h1 className="text-2xl font-bold mb-2">🎓 Affiliate Training Center</h1>
        <p className="text-blue-100">
          Access training materials designed specifically for affiliates
        </p>
        <div className="mt-4 flex items-center space-x-4 text-sm">
          <div className="bg-white/10 rounded-lg px-3 py-1">
            <span className="font-medium">{enrollments.length}</span> Enrolled
          </div>
          <div className="bg-white/10 rounded-lg px-3 py-1">
            <span className="font-medium">
              {enrollments.filter(e => e.completed_at).length}
            </span> Completed
          </div>
          <div className="bg-white/10 rounded-lg px-3 py-1">
            <span className="font-medium">
              {enrollments.length > 0 
                ? Math.round(enrollments.reduce((sum, e) => sum + e.progress_percentage, 0) / enrollments.length)
                : 0}%
            </span> Average Progress
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="flex-1">
          <input
            type="text"
            placeholder="Search training content..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg font-medium transition-colors"
        >
          <option value="all">All Categories</option>
          {categories.map(category => (
            <option key={category} value={category}>{category}</option>
          ))}
        </select>
      </div>

      {/* Course Grid */}
      {filteredCourses.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">📚</div>
          <h3 className="text-xl font-semibold text-white mb-2">No Training Content</h3>
          <p className="text-gray-400">
            {searchTerm || selectedCategory !== 'all' 
              ? 'No courses match your search criteria.'
              : 'No training content available for your user type.'}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCourses.map((course) => {
            const enrollment = getUserEnrollment(course.id);
            const isEnrolled = !!enrollment;
            const isCompleted = enrollment?.completed_at;
            const progress = enrollment?.progress_percentage || 0;

            return (
              <div key={course.id} className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden hover:border-blue-500 transition-colors">
                {/* Course Thumbnail */}
                <div className="h-48 bg-gradient-to-br from-blue-600 to-indigo-700 flex items-center justify-center">
                  <div className="text-4xl">{course.category?.icon || '📚'}</div>
                </div>

                <div className="p-6">
                  {/* Course Header */}
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="text-lg font-semibold text-white line-clamp-2">
                      {course.title}
                    </h3>
                    {course.is_featured && (
                      <span className="bg-yellow-600 text-white text-xs px-2 py-1 rounded-full font-medium">
                        ⭐ Featured
                      </span>
                    )}
                  </div>

                  {/* Course Info */}
                  <div className="flex items-center space-x-4 mb-3 text-sm text-gray-400">
                    <span 
                      className="px-2 py-1 rounded text-xs font-medium text-white"
                      style={{ backgroundColor: getDifficultyColor(course.difficulty_level) }}
                    >
                      {course.difficulty_level}
                    </span>
                    <span>⏱️ {course.estimated_duration} min</span>
                    <span>👥 {course.enrollment_count}</span>
                  </div>

                  {/* Description */}
                  <div 
                    className="text-gray-300 text-sm mb-4 line-clamp-3"
                    dangerouslySetInnerHTML={{ 
                      __html: course.description.replace(/<[^>]*>/g, '').substring(0, 120) + '...' 
                    }}
                  />

                  {/* Progress Bar (if enrolled) */}
                  {isEnrolled && (
                    <div className="mb-4">
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-400">Progress</span>
                        <span className="text-blue-400">{progress}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progress}%` }}
                        />
                      </div>
                    </div>
                  )}

                  {/* Action Button */}
                  <div className="flex items-center justify-between">
                    {isCompleted ? (
                      <button className="bg-green-600 text-white px-4 py-2 rounded-lg font-medium flex items-center space-x-2">
                        <span>✅</span>
                        <span>Completed</span>
                      </button>
                    ) : isEnrolled ? (
                      <button
                        onClick={() => continueCourse(course)}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                      >
                        Continue Learning
                      </button>
                    ) : (
                      <button
                        onClick={() => enrollInCourse(course.id)}
                        className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                      >
                        Enroll Now
                      </button>
                    )}

                    {course.average_rating > 0 && (
                      <div className="flex items-center space-x-1 text-yellow-400">
                        <span>⭐</span>
                        <span className="text-sm">{course.average_rating.toFixed(1)}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Course Viewer Modal */}
      {selectedCourse && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden border border-gray-700">
            {/* Modal Header */}
            <div className="p-6 border-b border-gray-700">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-xl font-semibold text-white">
                    {selectedCourse.title}
                  </h3>
                  <div className="flex items-center space-x-4 mt-2">
                    <span
                      className="px-2 py-1 text-xs font-medium text-white rounded"
                      style={{ backgroundColor: getDifficultyColor(selectedCourse.difficulty_level) }}
                    >
                      {selectedCourse.difficulty_level}
                    </span>
                    <span className="text-sm text-gray-400">
                      ⏱️ {selectedCourse.estimated_duration} min
                    </span>
                    <span className="text-sm text-gray-400">
                      👥 {selectedCourse.enrollment_count} enrolled
                    </span>
                  </div>
                </div>
                <button
                  onClick={closeCourse}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  <span className="text-2xl">×</span>
                </button>
              </div>
            </div>

            {/* Modal Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              {/* Course Description */}
              <div className="mb-6">
                <h4 className="text-lg font-medium text-white mb-2">Course Description</h4>
                <div
                  className="text-gray-300 leading-relaxed"
                  style={{
                    lineHeight: '1.6'
                  }}
                  dangerouslySetInnerHTML={{
                    __html: selectedCourse.description
                      .replace(/<p>/g, '<p style="margin-bottom: 12px;">')
                      .replace(/<br>/g, '<br>')
                      .replace(/<strong>/g, '<strong style="color: #ffffff; font-weight: 600;">')
                      .replace(/<h3[^>]*>/g, '<h3 style="color: #ffffff; font-size: 18px; font-weight: 600; margin: 16px 0 8px 0;">')
                      .replace(/<ul[^>]*>/g, '<ul style="margin: 12px 0; padding-left: 20px;">')
                      .replace(/<li>/g, '<li style="margin-bottom: 6px;">')
                      .replace(/<div[^>]*>/g, '<div style="margin: 12px 0;">')
                  }}
                />
              </div>

              {/* Course Actions */}
              <div className="mb-6">
                {selectedCourse.content_url ? (
                  <div className="flex flex-col sm:flex-row gap-4">
                    <a
                      href={selectedCourse.content_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium py-4 px-6 rounded-lg transition-all duration-200 text-center inline-flex items-center justify-center"
                    >
                      <span className="mr-2">🚀</span>
                      Start Course
                    </a>
                    <button
                      onClick={() => {
                        // Add to favorites or bookmark functionality
                        alert('Course bookmarked! (Feature coming soon)')
                      }}
                      className="bg-gray-700 hover:bg-gray-600 text-white font-medium py-4 px-6 rounded-lg transition-colors inline-flex items-center justify-center"
                    >
                      <span className="mr-2">⭐</span>
                      Bookmark
                    </button>
                  </div>
                ) : (
                  <div className="bg-gray-700 rounded-lg p-6 text-center">
                    <div className="text-4xl mb-4">🚧</div>
                    <h5 className="text-lg font-medium text-white mb-2">
                      Coming Soon
                    </h5>
                    <p className="text-gray-400 mb-4">
                      The training materials for this course are being prepared and will be available soon.
                    </p>
                    <button
                      onClick={() => {
                        alert('You will be notified when this course becomes available!')
                      }}
                      className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors inline-flex items-center"
                    >
                      <span className="mr-2">🔔</span>
                      Notify Me
                    </button>
                  </div>
                )}
              </div>

              {/* Learning Objectives */}
              <div className="mb-6">
                <h4 className="text-lg font-medium text-white mb-3">What You'll Learn</h4>
                <div className="bg-gray-700 rounded-lg p-4">
                  <ul className="space-y-2 text-gray-300">
                    <li className="flex items-start">
                      <span className="text-green-400 mr-2">✓</span>
                      Master the fundamentals of affiliate marketing for gold mining investments
                    </li>
                    <li className="flex items-start">
                      <span className="text-green-400 mr-2">✓</span>
                      Learn effective communication strategies for potential investors
                    </li>
                    <li className="flex items-start">
                      <span className="text-green-400 mr-2">✓</span>
                      Understand the gold mining industry and market dynamics
                    </li>
                    <li className="flex items-start">
                      <span className="text-green-400 mr-2">✓</span>
                      Develop skills to build and maintain client relationships
                    </li>
                    <li className="flex items-start">
                      <span className="text-green-400 mr-2">✓</span>
                      Maximize your commission potential through proven techniques
                    </li>
                  </ul>
                </div>
              </div>

              {/* Progress Section */}
              {(() => {
                const enrollment = getUserEnrollment(selectedCourse.id);
                const progress = enrollment?.progress_percentage || 0;

                return (
                  <div className="mb-6">
                    <h4 className="text-lg font-medium text-white mb-3">Your Progress</h4>
                    <div className="bg-gray-700 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-gray-300">Course Progress</span>
                        <span className="text-white font-medium">{progress}%</span>
                      </div>
                      <div className="w-full bg-gray-600 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progress}%` }}
                        ></div>
                      </div>
                      {progress === 0 && selectedCourse.content_url && (
                        <div className="mt-3 text-center">
                          <p className="text-gray-400 text-sm mb-2">
                            Ready to start your learning journey?
                          </p>
                          <a
                            href={selectedCourse.content_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-4 rounded transition-colors"
                          >
                            <span className="mr-1">▶️</span>
                            Begin Course
                          </a>
                        </div>
                      )}
                      {progress === 0 && !selectedCourse.content_url && (
                        <p className="text-gray-400 text-sm mt-2">
                          Course content will be available soon
                        </p>
                      )}
                      {progress > 0 && progress < 100 && (
                        <div className="mt-3 text-center">
                          <p className="text-gray-400 text-sm mb-2">
                            Continue where you left off
                          </p>
                          <a
                            href={selectedCourse.content_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-4 rounded transition-colors"
                          >
                            <span className="mr-1">📚</span>
                            Continue Course
                          </a>
                        </div>
                      )}
                      {progress === 100 && (
                        <div className="mt-3 text-center">
                          <p className="text-green-400 text-sm mb-2">
                            🎉 Congratulations! Course completed
                          </p>
                          <a
                            href={selectedCourse.content_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-4 rounded transition-colors"
                          >
                            <span className="mr-1">🔄</span>
                            Review Course
                          </a>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })()}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
