/**
 * INPUT VALIDATION AND SANITIZATION (JavaScript version for API endpoints)
 * 
 * This module provides comprehensive input validation and sanitization
 * to prevent SQL injection, XSS, and other security threats.
 */

import { z } from 'zod';

/**
 * Sanitize input string by removing potentially dangerous characters
 */
export function sanitizeInput(input) {
  if (typeof input !== 'string') {
    return input;
  }

  return input
    // Remove HTML tags
    .replace(/<[^>]*>/g, '')
    // Remove SQL injection patterns
    .replace(/['";\\]/g, '')
    // Remove script tags and javascript
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
    // Remove path traversal attempts
    .replace(/\.\.\//g, '')
    .replace(/\.\.\\/g, '')
    // Remove command injection attempts
    .replace(/[;&|`$]/g, '')
    // Trim whitespace
    .trim();
}

/**
 * Recursively sanitize an object
 */
export function sanitizeObject(obj) {
  if (typeof obj === 'string') {
    return sanitizeInput(obj);
  }

  if (Array.isArray(obj)) {
    return obj.map(sanitizeObject);
  }

  if (obj && typeof obj === 'object') {
    const sanitized = {};
    for (const [key, value] of Object.entries(obj)) {
      sanitized[sanitizeInput(key)] = sanitizeObject(value);
    }
    return sanitized;
  }

  return obj;
}

/**
 * Check for malicious patterns in input
 */
export function detectMaliciousInput(input) {
  const maliciousPatterns = [
    // Enhanced SQL injection patterns
    { pattern: /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|TRUNCATE|EXEC|EXECUTE)\b)/i, name: 'SQL_INJECTION' },
    { pattern: /(UNION|OR|AND)\s+\d+\s*=\s*\d+/i, name: 'SQL_INJECTION' },
    { pattern: /['"]\s*(OR|AND)\s*['"]\d+['"]\s*=\s*['"]\d+['"]*/i, name: 'SQL_INJECTION' },
    { pattern: /['"][\s]*;[\s]*--/i, name: 'SQL_INJECTION' },
    { pattern: /['"][\s]*;[\s]*\/\*/i, name: 'SQL_INJECTION' },
    { pattern: /\b(WAITFOR|DELAY|SLEEP|BENCHMARK)\b/i, name: 'SQL_INJECTION' },
    { pattern: /\b(INFORMATION_SCHEMA|SYSOBJECTS|SYSCOLUMNS)\b/i, name: 'SQL_INJECTION' },
    { pattern: /\b(CAST|CONVERT|CHAR|ASCII|SUBSTRING)\s*\(/i, name: 'SQL_INJECTION' },
    { pattern: /\b(LOAD_FILE|INTO\s+OUTFILE|INTO\s+DUMPFILE)\b/i, name: 'SQL_INJECTION' },
    { pattern: /\b(sp_|xp_|cmdshell)\b/i, name: 'SQL_INJECTION' },
    
    // XSS patterns
    { pattern: /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, name: 'XSS' },
    { pattern: /javascript:/i, name: 'XSS' },
    { pattern: /on\w+\s*=/i, name: 'XSS' },
    { pattern: /<iframe/i, name: 'XSS' },
    { pattern: /<object/i, name: 'XSS' },
    { pattern: /<embed/i, name: 'XSS' },
    { pattern: /<link/i, name: 'XSS' },
    { pattern: /<meta/i, name: 'XSS' },
    { pattern: /vbscript:/i, name: 'XSS' },
    { pattern: /data:text\/html/i, name: 'XSS' },
    
    // Path traversal
    { pattern: /\.\.\//g, name: 'PATH_TRAVERSAL' },
    { pattern: /\.\.\\/g, name: 'PATH_TRAVERSAL' },
    { pattern: /\.\.%2f/gi, name: 'PATH_TRAVERSAL' },
    { pattern: /\.\.%5c/gi, name: 'PATH_TRAVERSAL' },
    
    // Command injection
    { pattern: /[;&|`$]/g, name: 'COMMAND_INJECTION' },
    { pattern: /\b(cat|ls|dir|type|copy|move|del|rm|mkdir|rmdir)\b/i, name: 'COMMAND_INJECTION' },
    { pattern: /\b(wget|curl|nc|netcat|telnet|ssh)\b/i, name: 'COMMAND_INJECTION' },
    
    // LDAP injection
    { pattern: /[()=*!&|]/g, name: 'LDAP_INJECTION' },
    
    // NoSQL injection
    { pattern: /\$where/i, name: 'NOSQL_INJECTION' },
    { pattern: /\$ne/i, name: 'NOSQL_INJECTION' },
    { pattern: /\$gt/i, name: 'NOSQL_INJECTION' },
    { pattern: /\$regex/i, name: 'NOSQL_INJECTION' }
  ];

  const detectedPatterns = [];
  const inputString = typeof input === 'string' ? input : JSON.stringify(input);

  for (const { pattern, name } of maliciousPatterns) {
    if (pattern.test(inputString)) {
      detectedPatterns.push(name);
    }
  }

  return {
    isMalicious: detectedPatterns.length > 0,
    patterns: [...new Set(detectedPatterns)] // Remove duplicates
  };
}

/**
 * Comprehensive security check for input data
 */
export function securityCheckInput(data) {
  if (data === null || data === undefined) {
    return { safe: true, sanitized: data, issues: [] };
  }

  const inputString = typeof data === 'string' ? data : JSON.stringify(data);
  const maliciousCheck = detectMaliciousInput(inputString);
  
  if (maliciousCheck.isMalicious) {
    return {
      safe: false,
      sanitized: null,
      issues: [`Malicious patterns detected: ${maliciousCheck.patterns.join(', ')}`]
    };
  }

  return {
    safe: true,
    sanitized: sanitizeObject(data),
    issues: []
  };
}

/**
 * Validation schemas using Zod
 */
export const ContactSchema = z.object({
  name: z.string()
    .min(1, 'Name is required')
    .max(50, 'Name must be less than 50 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'Name can only contain letters, spaces, hyphens, and apostrophes'),
  surname: z.string()
    .min(1, 'Surname is required')
    .max(50, 'Surname must be less than 50 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'Surname can only contain letters, spaces, hyphens, and apostrophes'),
  email: z.string()
    .email('Invalid email format')
    .max(255, 'Email must be less than 255 characters'),
  message: z.string()
    .min(10, 'Message must be at least 10 characters')
    .max(2000, 'Message must be less than 2000 characters')
});

export const PaymentSchema = z.object({
  amount: z.number()
    .min(25, 'Minimum amount is $25')
    .max(10000, 'Maximum amount is $10,000'),
  shares_to_purchase: z.number()
    .int('Shares must be a whole number')
    .min(1, 'Must purchase at least 1 share')
    .max(400, 'Maximum 400 shares per transaction'),
  network: z.enum(['BSC', 'POLYGON', 'TRON', 'ETH'], {
    errorMap: () => ({ message: 'Invalid network' })
  }),
  currency: z.string()
    .min(2, 'Currency must be specified')
    .max(10, 'Invalid currency format'),
  sender_wallet: z.string()
    .min(26, 'Invalid wallet address')
    .max(100, 'Wallet address too long')
    .regex(/^[a-zA-Z0-9]+$/, 'Wallet address contains invalid characters'),
  receiver_wallet: z.string()
    .min(26, 'Invalid wallet address')
    .max(100, 'Wallet address too long')
    .regex(/^[a-zA-Z0-9]+$/, 'Wallet address contains invalid characters'),
  transaction_hash: z.string()
    .min(40, 'Invalid transaction hash')
    .max(100, 'Transaction hash too long')
    .regex(/^[a-zA-Z0-9]+$/, 'Transaction hash contains invalid characters'),
  screenshot_url: z.string()
    .url('Invalid screenshot URL')
    .optional(),
  transaction_notes: z.string()
    .max(500, 'Transaction notes must be less than 500 characters')
    .optional(),
  status: z.enum(['pending', 'confirmed', 'rejected']).optional()
});

/**
 * Generic request validation function
 */
export function validateRequest(schema) {
  return (data) => {
    try {
      // First check for malicious input
      const securityCheck = securityCheckInput(data);
      if (!securityCheck.safe) {
        return {
          success: false,
          errors: securityCheck.issues
        };
      }

      // Then validate with schema
      const result = schema.safeParse(securityCheck.sanitized);
      if (result.success) {
        return {
          success: true,
          data: result.data
        };
      } else {
        return {
          success: false,
          errors: result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
        };
      }
    } catch (error) {
      return {
        success: false,
        errors: ['Validation error occurred']
      };
    }
  };
}

export default {
  sanitizeInput,
  sanitizeObject,
  detectMaliciousInput,
  securityCheckInput,
  validateRequest,
  ContactSchema,
  PaymentSchema
};
