import React, { useState, useRef, useEffect, useCallback } from 'react';
import { supabase } from '../../lib/supabase';
import { advancedFacialRecognition, LivenessDetectionResult, BiometricTemplate } from '../../lib/advancedFacialRecognition';

interface AdvancedFacialRecognitionKYCProps {
  userId: number;
  onComplete: (results: AdvancedFacialRecognitionResults) => void;
  onCancel: () => void;
}

interface AdvancedFacialRecognitionResults {
  confidence_score: number;
  verification_photo_url: string;
  biometric_template_id: string;
  liveness_checks: {
    head_movement: boolean;
    smile_detection: boolean;
    eye_gaze_tracking: boolean;
    mouth_movement: boolean;
  };
  security_analysis: {
    spoofing_detected: boolean;
    spoofing_type?: string;
    depth_analysis_passed: boolean;
    texture_analysis_passed: boolean;
  };
  landmark_analysis: {
    landmarks_detected: number;
    landmark_quality: number;
    geometric_consistency: number;
  };
  metadata: {
    session_id: string;
    timestamp: string;
    device_info: string;
    verification_steps: VerificationStep[];
    processing_time_ms: number;
  };
}

interface VerificationStep {
  step: string;
  instruction: string;
  completed: boolean;
  confidence: number;
  timestamp: string;
  challenge_type: 'liveness' | 'landmark' | 'security' | 'quality';
}

const AdvancedFacialRecognitionKYC: React.FC<AdvancedFacialRecognitionKYCProps> = ({
  userId,
  onComplete,
  onCancel
}) => {
  // State management
  const [currentStep, setCurrentStep] = useState(0);
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [faceDetected, setFaceDetected] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<VerificationStep[]>([]);
  const [livenessResults, setLivenessResults] = useState<LivenessDetectionResult | null>(null);
  const [biometricTemplate, setBiometricTemplate] = useState<BiometricTemplate | null>(null);
  const [finalCapturedImage, setFinalCapturedImage] = useState<string | null>(null);
  // Debug overlay state
  const [debugEAR, setDebugEAR] = useState<number | null>(null);
  const [debugFrame, setDebugFrame] = useState<number>(0);
  const [debugEarlyBlink, setDebugEarlyBlink] = useState<boolean>(false);
  const [stepImages, setStepImages] = useState<{[key: string]: string}>({});
  // Listen to library-level debug updates (fallback if direct callback not wired)
  useEffect(() => {
    const onDebug = (e: Event) => {
      const detail = (e as CustomEvent).detail;
      if (!detail) return;
      setDebugEAR(detail.ear);
      if (typeof detail.frameCount === 'number') setDebugFrame(detail.frameCount);
      if (typeof detail.earlyBlink === 'boolean') setDebugEarlyBlink(detail.earlyBlink);
    };
    window.addEventListener('afr:blink_debug', onDebug as EventListener);
    return () => window.removeEventListener('afr:blink_debug', onDebug as EventListener);
  }, []);
  const [verificationFailed, setVerificationFailed] = useState(false);
  const [failureReason, setFailureReason] = useState<string>('');

  // Refs
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  // Session management
  // Debug overlay UI (top-left)
  const DebugOverlay: React.FC = () => (
    <div style={{
      position: 'absolute', top: 8, left: 8, zIndex: 50,
      background: 'rgba(0,0,0,0.55)', border: '1px solid #22c55e',
      borderRadius: 8, padding: '8px 10px', color: '#e5e7eb',
      fontSize: 12, lineHeight: 1.2, pointerEvents: 'none'
    }}>
      <div style={{ color: '#86efac' }}>EAR: {debugEAR?.toFixed(3) ?? '—'}</div>
      <div>frame: {debugFrame}</div>
      <div>blink: {debugEarlyBlink ? 'yes' : 'no'}</div>
    </div>
  );
  const [sessionId] = useState(() => `advanced_kyc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  const [startTime] = useState(Date.now());

  // Full advanced 9-step verification with real AI
  const verificationSteps = [
    { step: 'face_detection', instruction: 'Position your face within the frame for detection.', challenge_type: 'landmark' as const, duration: 2000 },
    { step: 'landmark_mapping', instruction: 'Hold still while we map your facial landmarks.', challenge_type: 'landmark' as const, duration: 2000 },
    { step: 'blink_detection', instruction: 'Blink twice to verify liveness.', challenge_type: 'liveness' as const, duration: 2500 },
    { step: 'head_left', instruction: 'Turn your head slightly to the LEFT.', challenge_type: 'liveness' as const, duration: 2500 },
    { step: 'head_right', instruction: 'Turn your head slightly to the RIGHT.', challenge_type: 'liveness' as const, duration: 2500 },
    { step: 'smile_detection', instruction: 'Smile naturally to verify facial muscles.', challenge_type: 'liveness' as const, duration: 2500 },
    { step: 'eye_gaze_tracking', instruction: 'Look straight at the camera.', challenge_type: 'liveness' as const, duration: 2500 },
    { step: 'mouth_movement', instruction: 'Open and close your mouth slowly.', challenge_type: 'liveness' as const, duration: 2500 },
    { step: 'security_checks', instruction: 'Running anti-spoofing, depth, and texture checks.', challenge_type: 'security' as const, duration: 2000 },
    { step: 'quality_assessment', instruction: 'Assessing image quality and generating biometric template.', challenge_type: 'quality' as const, duration: 2000 },
  ];

  // Initialize REAL facial recognition (face-api + MediaPipe)
  const initializeFacialRecognition = useCallback(async () => {
    try {
      console.log('🚀 Initializing advanced facial recognition...');
      await advancedFacialRecognition.initializeModels();
      console.log('✅ Advanced models ready');
    } catch (e) {
      console.error('❌ Advanced model init failed:', e);
      setError('Failed to initialize facial recognition. Please refresh and try again.');
    }
  }, []);

  // Initialize camera with high quality settings
  const initializeCamera = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1920, min: 1280 },
          height: { ideal: 1080, min: 720 },
          facingMode: 'user',
          frameRate: { ideal: 30 }
        },
        audio: false
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        streamRef.current = stream;
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      setError('Unable to access camera. Please ensure camera permissions are granted and try again.');
    }
  }, []);

  // Cleanup camera
  const cleanupCamera = useCallback(() => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
  }, []);

  // Advanced face detection with landmark mapping
  const detectFaceWithLandmarks = useCallback(async () => {
    try {
      if (!videoRef.current || !canvasRef.current) return false;
      const video = videoRef.current;
      const canvas = canvasRef.current;

      // Copy current frame to canvas
      const ctx = canvas.getContext('2d');
      if (!ctx) return false;
      if (video.videoWidth === 0 || video.videoHeight === 0) return false;
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Ask advanced system to detect landmarks (MediaPipe primary, face-api backup)
      const landmarks = await advancedFacialRecognition.detectFacialLandmarks(canvas);
      const detected = !!landmarks;
      setFaceDetected(detected);
      return detected;
    } catch (e) {
      console.error('Face detection failed:', e);
      return false;
    }
  }, []);

  // Simple face detection function using basic image analysis
  const detectFaceInImageData = useCallback(async (imageData: ImageData): Promise<boolean> => {
    try {
      const { data, width, height } = imageData;

      // Simple skin tone detection
      let skinPixels = 0;
      let totalPixels = 0;
      let brightPixels = 0;

      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];

        totalPixels++;

        // Check for brightness (avoid completely dark images)
        if (r + g + b > 100) {
          brightPixels++;
        }

        // Simple skin tone detection (basic heuristic)
        if (r > 95 && g > 40 && b > 20 &&
            Math.max(r, g, b) - Math.min(r, g, b) > 15 &&
            Math.abs(r - g) > 15 && r > g && r > b) {
          skinPixels++;
        }
      }

      const skinRatio = skinPixels / totalPixels;
      const brightRatio = brightPixels / totalPixels;

      // Face detected if we have enough skin-like pixels and the image isn't too dark
      const faceDetected = skinRatio > 0.015 && brightRatio > 0.3; // At least 1.5% skin-like pixels and 30% bright pixels

      console.log(`🔍 Face detection analysis: skin=${(skinRatio * 100).toFixed(2)}%, bright=${(brightRatio * 100).toFixed(2)}%, detected=${faceDetected}`);

      return faceDetected;

    } catch (error) {
      console.error('❌ Simple face detection error:', error);
      return false;
    }
  }, []);

  // Helper: repeatedly attempt a specific liveness challenge until success or timeout
  const attemptLiveness = useCallback(async (
    challenge: 'blink' | 'head_movement' | 'smile' | 'eye_gaze' | 'mouth_movement',
    timeoutMs = 12000,
    intervalMs = 700
  ): Promise<{ success: boolean; confidence: number }> => {
    if (!videoRef.current) return { success: false, confidence: 0 };
    const start = Date.now();
    let tries = 0;
    while (Date.now() - start < timeoutMs) {
      const res = await advancedFacialRecognition.performAdvancedLivenessDetection(videoRef.current, [challenge]);
      // Update latest liveness snapshot
      setLivenessResults(prev => prev ?? res);
      let completed = false;
      let conf = 0;
      if (challenge === 'blink') { completed = res.challenges.blinkDetection.completed; conf = res.challenges.blinkDetection.confidence; }
      if (challenge === 'head_movement') { completed = res.challenges.headMovement.completed; conf = res.challenges.headMovement.confidence; }
      if (challenge === 'smile') { completed = res.challenges.smileDetection.completed; conf = res.challenges.smileDetection.confidence; }
      if (challenge === 'eye_gaze') { completed = res.challenges.eyeGazeTracking.completed; conf = res.challenges.eyeGazeTracking.confidence; }
      if (challenge === 'mouth_movement') { completed = res.challenges.mouthMovement.completed; conf = res.challenges.mouthMovement.confidence; }
      if (completed) return { success: true, confidence: conf };
      tries++;
      await new Promise(r => setTimeout(r, intervalMs));
    }
    return { success: false, confidence: 0 };
  }, []);

  // Process current verification step
  const processCurrentStep = useCallback(async () => {
    if (currentStep >= verificationSteps.length || isRecording) return;

    const step = verificationSteps[currentStep];
    setError(null);

    try {
      console.log(`🔄 Processing step: ${step.step} (${step.challenge_type})`);

      // Show countdown before starting step (except for first step)
      if (currentStep > 0) {
        for (let i = 3; i > 0; i--) {
          setCountdown(i);
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
        setCountdown(0);
      }

      setIsRecording(true);
      let success = false;
      let confidence = 0;

      // Advanced step processing with real AI
      switch (step.challenge_type) {
        case 'landmark':
          if (step.step === 'face_detection') {
            console.log('👤 Starting face detection...');
            success = await detectFaceWithLandmarks();
            confidence = success ? 0.95 : 0.2;
          } else if (step.step === 'landmark_mapping') {
            console.log('🗺️ Mapping facial landmarks...');
            success = await detectFaceWithLandmarks();
            confidence = success ? 0.92 : 0.2;
          }
          break;
        case 'liveness':
          if (!videoRef.current) break;
          if (step.step === 'blink_detection') {
            // Blink with on-screen debug overlay
            const res = await attemptLiveness('blink');
            success = res.success; confidence = res.confidence;
          } else if (step.step === 'head_left' || step.step === 'head_right') {
            const res = await attemptLiveness('head_movement');
            success = res.success; confidence = res.confidence;
          } else if (step.step === 'smile_detection') {
            const res = await attemptLiveness('smile');
            success = res.success; confidence = res.confidence;
          } else if (step.step === 'eye_gaze_tracking') {
            const res = await attemptLiveness('eye_gaze');
            success = res.success; confidence = res.confidence;
          } else if (step.step === 'mouth_movement') {
            const res = await attemptLiveness('mouth_movement');
            success = res.success; confidence = res.confidence;
          }
          break;
        case 'security':
          if (!videoRef.current) break;
          // Run combined anti-spoofing/depth/texture analysis via liveness full run
          const sec = await advancedFacialRecognition.performAdvancedLivenessDetection(videoRef.current, ['blink','head_movement','smile','eye_gaze','mouth_movement']);
          success = !sec.spoofingDetected && sec.depthAnalysis.hasDepth && sec.textureAnalysis.skinTexture > 0.7;
          confidence = success ? 0.96 : 0.4;
          setLivenessResults(sec);
          break;
        case 'quality':
          // Create REAL biometric template
          if (canvasRef.current) {
            const template = await advancedFacialRecognition.createSecureBiometricTemplate(userId, canvasRef.current);
            if (template) { setBiometricTemplate(template); success = true; confidence = template.confidence; }
            else { success = false; confidence = 0.1; }
          }
          break;
      }

      if (step.challenge_type === 'liveness' && !success) {
        // Do not mark failure or set error; allow continuous retry
        console.log(`⏳ Challenge ongoing: ${step.step}. Waiting for clear action...`);
        return;
      }

      // Record step completion
      const completedStep: VerificationStep = {
        step: step.step,
        instruction: step.instruction,
        completed: success,
        confidence,
        timestamp: new Date().toISOString(),
        challenge_type: step.challenge_type
      };

      setCompletedSteps(prev => [...prev, completedStep]);

      if (success) {
        // Capture image for this successful step
        captureImage(step.step);

        console.log(`✅ Step completed: ${step.step} (confidence: ${confidence.toFixed(3)})`);
        console.log(`⏱️ Waiting ${step.duration}ms before next step...`);

        // Wait for step duration
        await new Promise(resolve => setTimeout(resolve, step.duration));

        console.log(`➡️ Moving from step ${currentStep} to ${currentStep + 1}`);
        // Move to next step
        setCurrentStep(prev => {
          const nextStep = prev + 1;
          console.log(`📍 Step transition: ${prev} -> ${nextStep}`);
          return nextStep;
        });
      } else {
        console.log(`❌ Step failed: ${step.step} (confidence: ${confidence.toFixed(3)})`);
        setError(`Failed to complete ${step.instruction.toLowerCase()}. Please try again.`);
      }

    } catch (error) {
      console.error(`Error processing step ${step.step}:`, error);
      setError(`Error during ${step.instruction.toLowerCase()}. Please try again.`);
    } finally {
      setIsRecording(false);
    }
  }, [currentStep, isRecording, userId, detectFaceWithLandmarks]);

  // Initialize simple camera system
  useEffect(() => {
    const initialize = async () => {
      console.log('🚀 Starting simple KYC initialization...');
      await initializeFacialRecognition();
      await initializeCamera();
      console.log('✅ Simple KYC system ready!');
    };

    initialize();
    return cleanupCamera;
  }, [initializeFacialRecognition, initializeCamera, cleanupCamera]);

  // Continuous face detection loop (advanced)
  useEffect(() => {
    let detectionInterval: NodeJS.Timeout;

    const startContinuousDetection = () => {
      detectionInterval = setInterval(async () => {
        if (!isRecording && !isProcessing && videoRef.current && canvasRef.current) {
          try {
            await detectFaceWithLandmarks();
          } catch (error) {
            console.error('❌ Continuous face detection error:', error);
          }
        }
      }, 500);
    };

    const startTimeout = setTimeout(startContinuousDetection, 2000);

    return () => {
      clearTimeout(startTimeout);
      if (detectionInterval) clearInterval(detectionInterval);
    };
  }, [detectFaceWithLandmarks, isRecording, isProcessing]);

  // Auto-start verification when camera is ready
  useEffect(() => {
    const video = videoRef.current;
    if (video) {
      console.log(`📹 Camera state check: readyState=${video.readyState}, currentStep=${currentStep}, isRecording=${isRecording}`);
      console.log(`📐 Video dimensions: ${video.videoWidth}x${video.videoHeight}`);

      if (video.readyState >= 2 && currentStep === 0 && !isRecording) {
        console.log('🎥 Camera ready, starting verification process in 1 second...');
        setTimeout(() => {
          console.log('⚡ Starting first step now!');
          processCurrentStep();
        }, 1000);
      }
    }
  }, [videoRef.current?.readyState, currentStep, isRecording, processCurrentStep]);

  // Auto-progress through steps
  useEffect(() => {
    console.log(`🔄 Auto-progress check: step=${currentStep}, isRecording=${isRecording}, error=${!!error}, totalSteps=${verificationSteps.length}`);

    if (currentStep > 0 && currentStep < verificationSteps.length && !isRecording && !error) {
      console.log(`⏰ Setting timer to process step ${currentStep} in 500ms...`);
      const timer = setTimeout(() => {
        console.log(`⚡ Timer fired - processing step ${currentStep}`);
        processCurrentStep();
      }, 500);
      return () => {
        console.log(`🚫 Clearing timer for step ${currentStep}`);
        clearTimeout(timer);
      };
    }
  }, [currentStep, isRecording, error, processCurrentStep]);

  // Complete verification when all steps are done
  useEffect(() => {
    if (currentStep >= verificationSteps.length && !isProcessing) {
      checkVerificationResults();
    }
  }, [currentStep, isProcessing]);

  // Capture image from video
  const captureImage = useCallback((stepName?: string) => {
    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');

      if (ctx && video.videoWidth > 0 && video.videoHeight > 0) {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

        // Convert to base64 image
        const imageDataUrl = canvas.toDataURL('image/jpeg', 0.8);

        if (stepName) {
          // Store step-specific image
          setStepImages(prev => ({
            ...prev,
            [stepName]: imageDataUrl
          }));
          console.log(`📸 Step image captured for: ${stepName}`);
        } else {
          // Store as final image
          setFinalCapturedImage(imageDataUrl);
          console.log('📸 Final image captured');
        }

        return imageDataUrl;
      }
    }
    return null;
  }, []);

  // Capture final image from video
  const captureFinalImage = useCallback(() => {
    return captureImage();
  }, [captureImage]);

  // Check verification results and determine success/failure
  const checkVerificationResults = useCallback(async () => {
    console.log('🔍 Checking verification results...');

    // Capture final image before processing
    captureFinalImage();

    const failedSteps = completedSteps.filter(step => !step.completed);
    const successfulSteps = completedSteps.filter(step => step.completed);
    const totalSteps = verificationSteps.length;

    console.log(`📊 Results: ${successfulSteps.length}/${totalSteps} steps passed, ${failedSteps.length} failed`);

    // Determine if verification failed (more than 30% failure rate)
    const failureRate = failedSteps.length / totalSteps;
    const criticalStepsFailed = failedSteps.some(step =>
      step.step === 'face_detection' ||
      step.step === 'quality_assessment' ||
      step.step === 'landmark_mapping'
    );

    if (failureRate > 0.3 || criticalStepsFailed) {
      console.log('❌ Verification failed - too many failed steps or critical failures');
      setVerificationFailed(true);

      if (criticalStepsFailed) {
        setFailureReason('Critical verification steps failed. Please ensure your face is clearly visible and try again.');
      } else {
        setFailureReason(`Verification incomplete. ${failedSteps.length} out of ${totalSteps} steps failed. Please try again in better lighting conditions.`);
      }

      return;
    }

    // Proceed with successful completion
    completeVerification();
  }, [completedSteps, captureFinalImage]);

  const completeVerification = async () => {
    try {
      setIsProcessing(true);
      console.log('🔄 Completing advanced facial recognition verification...');

      // Ensure required data; attempt auto-recovery if missing
      if (!livenessResults && videoRef.current) {
        console.log('ℹ️ Liveness results missing, performing full liveness detection now...');
        const lr = await advancedFacialRecognition.performAdvancedLivenessDetection(
          videoRef.current,
          ['blink','head_movement','smile','eye_gaze','mouth_movement']
        );
        setLivenessResults(lr);
      }

      if (!biometricTemplate && canvasRef.current) {
        console.log('ℹ️ Biometric template missing, creating now...');
        const tpl = await advancedFacialRecognition.createSecureBiometricTemplate(userId, canvasRef.current);
        if (tpl) setBiometricTemplate(tpl);
      }

      if (!canvasRef.current || !livenessResults || !biometricTemplate) {
        throw new Error('Missing verification data');
      }

      // Capture final verification photo
      const canvas = canvasRef.current;
      const blob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => {
          if (blob) resolve(blob);
        }, 'image/jpeg', 0.95);
      });

      // Upload verification photo
      const fileName = `advanced_facial_verification_${userId}_${sessionId}.jpg`;
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('proof')
        .upload(fileName, blob, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        throw new Error(`Upload failed: ${uploadError.message}`);
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('proof')
        .getPublicUrl(fileName);

      // Calculate overall confidence
      const stepConfidences = completedSteps.filter(s => s.completed).map(s => s.confidence);
      const overallConfidence = stepConfidences.length > 0
        ? stepConfidences.reduce((sum, conf) => sum + conf, 0) / stepConfidences.length
        : 0;

      // Calculate processing time
      const processingTime = Date.now() - startTime;

      // Prepare advanced results
      const results: AdvancedFacialRecognitionResults = {
        confidence_score: overallConfidence,
        verification_photo_url: publicUrl,
        biometric_template_id: biometricTemplate.templateId,
        liveness_checks: {
          head_movement: livenessResults.challenges.headMovement?.completed || false,
          smile_detection: livenessResults.challenges.smileDetection?.completed || false,
          eye_gaze_tracking: livenessResults.challenges.eyeGazeTracking?.completed || false,
          mouth_movement: livenessResults.challenges.mouthMovement?.completed || false
        },
        security_analysis: {
          spoofing_detected: livenessResults.spoofingDetected,
          spoofing_type: livenessResults.spoofingType,
          depth_analysis_passed: livenessResults.depthAnalysis.hasDepth,
          texture_analysis_passed: livenessResults.textureAnalysis.skinTexture > 0.7
        },
        landmark_analysis: {
          landmarks_detected: 468,
          landmark_quality: biometricTemplate.qualityScore,
          geometric_consistency: biometricTemplate.confidence
        },
        metadata: {
          session_id: sessionId,
          timestamp: new Date().toISOString(),
          device_info: navigator.userAgent,
          verification_steps: completedSteps,
          processing_time_ms: processingTime
        }
      };

      // Cleanup camera before completing
      cleanupCamera();

      console.log('✅ Advanced facial recognition verification completed successfully');
      onComplete(results);

    } catch (error) {
      console.error('Error completing verification:', error);
      setError(`Verification completion failed: ${error.message}`);
    } finally {
      setIsProcessing(false);
    }
  };

  const getStepIcon = (step: typeof verificationSteps[0], index: number) => {
    if (index < currentStep) return '✅';
    if (index === currentStep) return '🔄';
    return '⏳';
  };

  const getProgressPercentage = () => {
    return Math.min(100, (currentStep / verificationSteps.length) * 100);
  };

  // Retry verification
  const retryVerification = useCallback(() => {
    console.log('🔄 Retrying verification...');

    // Reset all state
    setCurrentStep(0);
    setCompletedSteps([]);
    setError(null);
    setVerificationFailed(false);
    setFailureReason('');
    setFinalCapturedImage(null);
    setStepImages({});
    setIsProcessing(false);
    setIsRecording(false);
    setBiometricTemplate(null);
    setLivenessResults(null);

    // Restart camera
    initializeCamera();
  }, [initializeCamera]);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50">
      <div className="bg-gray-900 rounded-xl p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              🔐
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">Advanced Biometric Verification</h2>
              <p className="text-gray-400 text-sm">Bank-level facial recognition security</p>
            </div>
          </div>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-white transition-colors"
            disabled={isProcessing}
          >
            ✕
          </button>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-400">
              Step {Math.min(currentStep + 1, verificationSteps.length)} of {verificationSteps.length}
            </span>
            <span className="text-sm text-gray-400">
              {getProgressPercentage().toFixed(0)}% Complete
            </span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-500"
              style={{ width: `${getProgressPercentage()}%` }}
            />
          </div>
        </div>

        {/* Verification Failed Screen */}
        {verificationFailed && (
          <div className="text-center py-8">
            <div className="bg-red-600 rounded-xl p-8 mb-6">
              <div className="text-6xl mb-4">❌</div>
              <h3 className="text-2xl font-bold text-white mb-4">Verification Failed</h3>
              <p className="text-red-100 mb-6 max-w-md mx-auto">
                {failureReason}
              </p>

              {/* Show final captured image if available */}
              {finalCapturedImage && (
                <div className="mb-6">
                  <h4 className="text-white font-semibold mb-3">Captured Image:</h4>
                  <div className="inline-block bg-gray-800 p-2 rounded-lg">
                    <img
                      src={finalCapturedImage}
                      alt="Captured verification image"
                      className="w-48 h-60 object-cover rounded-lg"
                    />
                  </div>
                </div>
              )}

              {/* Failed Steps Summary */}
              <div className="bg-red-700 rounded-lg p-4 mb-6 max-w-md mx-auto">
                <h4 className="text-white font-semibold mb-2">Failed Steps:</h4>
                <div className="space-y-1">
                  {completedSteps.filter(step => !step.completed).map(step => (
                    <div key={step.step} className="flex items-center justify-between text-red-100 text-sm">
                      <span>{step.instruction}</span>
                      <span className="text-red-300">✗</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex gap-4 justify-center">
                <button
                  onClick={retryVerification}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
                >
                  🔄 Try Again
                </button>
                <button
                  onClick={onCancel}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Main Content - Two Column Layout: Portrait Video + Steps */}
        {!verificationFailed && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column: Portrait Video Feed */}
          <div className="flex flex-col items-center space-y-4">
            <div className="relative bg-gradient-to-br from-gray-900 to-black rounded-2xl overflow-hidden shadow-2xl"
                 style={{ width: '400px', height: '500px' }}>
              <video
                ref={videoRef}
                autoPlay
                playsInline
                muted
                className="w-full h-full object-cover"
              />
              <canvas
                ref={canvasRef}
                className="hidden"
              />


                  {/* Debug overlay */}
                  <DebugOverlay />
              {/* Face Detection Frame - Portrait optimized with black lines */}
              <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                <div
                  className={`w-72 h-96 border-4 rounded-full transition-all duration-500 ${
                    faceDetected
                      ? 'border-black shadow-lg shadow-black/50'
                      : 'border-black border-dashed animate-pulse'
                  }`}
                  style={{
                    background: faceDetected
                      ? 'rgba(0, 0, 0, 0.1)'
                      : 'rgba(0, 0, 0, 0.05)',
                    boxShadow: faceDetected
                      ? '0 0 30px rgba(0, 0, 0, 0.8), inset 0 0 20px rgba(0, 0, 0, 0.3)'
                      : '0 0 20px rgba(0, 0, 0, 0.5)'
                  }}
                >
                  {/* Corner guides for better positioning */}
                  <div className="absolute top-4 left-4 w-8 h-8 border-l-4 border-t-4 border-black rounded-tl-lg"></div>
                  <div className="absolute top-4 right-4 w-8 h-8 border-r-4 border-t-4 border-black rounded-tr-lg"></div>
                  <div className="absolute bottom-4 left-4 w-8 h-8 border-l-4 border-b-4 border-black rounded-bl-lg"></div>
                  <div className="absolute bottom-4 right-4 w-8 h-8 border-r-4 border-b-4 border-black rounded-br-lg"></div>

                  {/* Center crosshair */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-1 h-8 bg-black opacity-50"></div>
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-8 h-1 bg-black opacity-50"></div>
                  </div>

                  {/* Face positioning guide */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className={`text-8xl transition-opacity duration-300 ${
                      faceDetected ? 'opacity-0' : 'opacity-40'
                    }`} style={{ filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.8))' }}>
                      👤
                    </div>
                  </div>
                </div>
              </div>

              {/* Face detected indicator - moved to top */}
              {faceDetected && (
                <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-10">
                  <div className="bg-green-500 text-white px-4 py-2 rounded-full text-sm font-bold flex items-center space-x-2 shadow-lg">
                    <span className="text-lg">✓</span>
                    <span>FACE DETECTED</span>
                  </div>
                </div>
              )}

              {/* Countdown Timer - Improved design */}
              {countdown > 0 && (
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-80 z-20 backdrop-blur-sm">
                  <div className="text-center">
                    <div className="text-9xl font-black text-white mb-6 animate-bounce"
                         style={{ textShadow: '0 0 20px rgba(255,255,255,0.5)' }}>
                      {countdown}
                    </div>
                    <div className="text-2xl text-white font-semibold">
                      Get Ready...
                    </div>
                  </div>
                </div>
              )}

              {/* Recording indicator - Better positioned */}
              {isRecording && (
                <div className="absolute bottom-4 right-4 z-20 flex items-center space-x-3 bg-red-600 px-4 py-2 rounded-full shadow-lg">
                  <div className="w-4 h-4 bg-white rounded-full animate-pulse" />
                  <span className="text-white font-bold text-sm">RECORDING</span>
                </div>
              )}
            </div>

            {/* Status indicator only */}
            {currentStep < verificationSteps.length && (
              <div className="w-full max-w-md bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-xl shadow-xl">
                <div className="text-center">
                  <h3 className="text-xl font-bold mb-2">
                    Step {currentStep + 1} of {verificationSteps.length}
                  </h3>
                  {isRecording && (
                    <div className="flex items-center justify-center space-x-2 text-green-200">
                      <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse" />
                      <span className="font-medium">Processing...</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Face positioning help text - Below video */}
            {!faceDetected && !countdown && (
              <div className="bg-yellow-500 text-black px-6 py-3 rounded-xl font-semibold shadow-lg">
                <div className="flex items-center justify-center space-x-2">
                  <span className="text-xl">⚠️</span>
                  <span>Position your face in the oval frame above</span>
                </div>
              </div>
            )}

            {/* Error Display - Below video */}
            {error && (
              <div className="w-full max-w-md bg-red-600 border-2 border-red-400 text-white p-4 rounded-xl shadow-lg">
                <div className="flex items-center justify-center space-x-3">
                  <span className="text-2xl">⚠️</span>
                  <span className="font-semibold">{error}</span>
                </div>
              </div>
            )}
          </div>

          {/* Right Column: Steps List and Progress */}
          <div className="space-y-6">
            <div>
              <h3 className="text-xl font-bold text-white mb-6">Verification Steps</h3>

              <div className="space-y-3 max-h-96 overflow-y-auto">
                {verificationSteps.map((step, index) => {
                  const completed = completedSteps.find(s => s.step === step.step);
                  const isCurrent = index === currentStep;
                  const isPending = index > currentStep;

                  return (
                    <div
                      key={step.step}
                      className={`p-4 rounded-xl border-2 transition-all duration-300 ${
                        completed?.completed
                          ? 'bg-green-600 border-green-400 text-white shadow-lg shadow-green-600/30'
                          : isCurrent
                          ? 'bg-blue-600 border-blue-400 text-white shadow-lg shadow-blue-600/30 animate-pulse'
                          : isPending
                          ? 'bg-gray-700 border-gray-500 text-gray-300'
                          : 'bg-red-600 border-red-400 text-white shadow-lg shadow-red-600/30'
                      }`}
                    >
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <span className="text-2xl">
                              {getStepIcon(step, index)}
                            </span>
                            <div>
                              <h4 className="font-bold text-sm">
                                {step.step.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                              </h4>
                              <p className="text-xs opacity-75">
                                {step.challenge_type.toUpperCase()} CHALLENGE
                              </p>
                            </div>
                          </div>
                          {completed && (
                            <div className="text-right">
                              <div className="text-sm font-bold">
                                {(completed.confidence * 100).toFixed(1)}%
                              </div>
                              <div className="text-xs opacity-75">
                                Confidence
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Detailed instruction for current or upcoming steps */}
                        {(isCurrent || isPending) && (
                          <div className="bg-black bg-opacity-20 rounded-lg p-3 border-l-4 border-yellow-400">
                            <p className="text-sm font-medium text-yellow-100 mb-1">
                              📋 What you need to do:
                            </p>
                            <p className="text-xs text-yellow-200">
                              {step.instruction}
                            </p>
                            {isCurrent && (
                              <div className="mt-2 flex items-center space-x-2 text-green-300">
                                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                                <span className="text-xs font-medium">ACTIVE NOW</span>
                              </div>
                            )}
                          </div>
                        )}

                        {/* Success message for completed steps */}
                        {completed?.completed && (
                          <div className="bg-green-900 bg-opacity-30 rounded-lg p-2 border-l-4 border-green-400">
                            <p className="text-xs text-green-200">
                              ✅ Successfully completed with {(completed.confidence * 100).toFixed(1)}% confidence
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Security Features - In right column */}
            <div className="bg-gradient-to-r from-gray-800 to-gray-700 rounded-xl p-6 shadow-xl">
              <h4 className="text-white font-bold text-lg mb-4 text-center">🛡️ Security Features Active</h4>

              {/* Detection Mode Indicator */}
              <div className="mb-4 p-3 bg-green-600 bg-opacity-20 rounded-lg border border-green-500">
                <div className="flex items-center justify-center space-x-2">
                  <span className="text-green-400 text-sm">🤖</span>
                  <span className="text-green-200 text-xs font-medium">REAL AI DETECTION</span>
                </div>
                <p className="text-green-100 text-xs text-center mt-1">
                  Using MediaPipe + face-api.js models for actual facial recognition
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="flex flex-col items-center space-y-2 text-green-400">
                  <span className="text-xl">✓</span>
                  <span className="text-xs font-semibold text-center">Anti-Spoofing</span>
                </div>
                <div className="flex flex-col items-center space-y-2 text-green-400">
                  <span className="text-xl">✓</span>
                  <span className="text-xs font-semibold text-center">Liveness Detection</span>
                </div>
                <div className="flex flex-col items-center space-y-2 text-green-400">
                  <span className="text-xl">✓</span>
                  <span className="text-xs font-semibold text-center">Landmark Mapping</span>
                </div>
                <div className="flex flex-col items-center space-y-2 text-green-400">
                  <span className="text-xl">✓</span>
                  <span className="text-xs font-semibold text-center">Depth Analysis</span>
                </div>
                <div className="flex flex-col items-center space-y-2 text-green-400">
                  <span className="text-xl">✓</span>
                  <span className="text-xs font-semibold text-center">Texture Analysis</span>
                </div>
                <div className="flex flex-col items-center space-y-2 text-green-400">
                  <span className="text-xl">✓</span>
                  <span className="text-xs font-semibold text-center">Biometric Template</span>
                </div>
              </div>
            </div>

            {/* Step Images Gallery - Show all captured step images */}
            {currentStep >= verificationSteps.length && Object.keys(stepImages).length > 0 && (
              <div className="bg-gradient-to-r from-green-800 to-blue-800 rounded-xl p-6 shadow-xl">
                <h4 className="text-white font-bold text-lg mb-4 text-center">📷 Verification Steps Gallery</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {Object.entries(stepImages).map(([stepName, imageUrl]) => {
                    const step = verificationSteps.find(s => s.step === stepName);
                    return (
                      <div key={stepName} className="bg-gray-800 p-2 rounded-lg">
                        <img
                          src={imageUrl}
                          alt={`${stepName} verification step`}
                          className="w-full h-32 object-cover rounded-lg mb-2"
                        />
                        <p className="text-center text-green-100 text-xs font-semibold">
                          {step?.instruction || stepName.replace('_', ' ').toUpperCase()}
                        </p>
                      </div>
                    );
                  })}
                </div>
                <p className="text-center text-green-100 text-sm mt-3">
                  Images captured during each verification step
                </p>
              </div>
            )}

            {/* Final Captured Image - Show in completion screen */}
            {currentStep >= verificationSteps.length && finalCapturedImage && (
              <div className="bg-gradient-to-r from-blue-800 to-purple-800 rounded-xl p-6 shadow-xl">
                <h4 className="text-white font-bold text-lg mb-4 text-center">📸 Final Verification Photo</h4>
                <div className="flex justify-center">
                  <div className="bg-gray-800 p-2 rounded-lg">
                    <img
                      src={finalCapturedImage}
                      alt="Final verification capture"
                      className="w-48 h-60 object-cover rounded-lg"
                    />
                  </div>
                </div>
                <p className="text-center text-blue-100 text-sm mt-3">
                  Final image captured at completion
                </p>
              </div>
            )}
          </div>
        </div>
        )}

        {/* Processing Overlay */}
        {isProcessing && (
          <div className="absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center rounded-xl">
            <div className="text-center text-white">
              <div className="animate-spin w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Processing Verification Results</h3>
              <p className="text-gray-300">Creating secure biometric template...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdvancedFacialRecognitionKYC;
