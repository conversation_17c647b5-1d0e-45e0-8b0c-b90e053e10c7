/**
 * SETUP KYC MANAGEMENT SYSTEM
 * 
 * Comprehensive setup script for the KYC management system.
 * Runs all necessary database migrations and setup tasks.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing required environment variables');
  console.error('Required: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Initialize Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function executeSQL(sql, description) {
  try {
    console.log(`🔄 ${description}...`);
    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });
    
    if (error) {
      console.error(`❌ ${description} failed:`, error);
      return false;
    }
    
    console.log(`✅ ${description} completed successfully`);
    return true;
  } catch (err) {
    console.error(`❌ ${description} failed:`, err);
    return false;
  }
}

async function setupKYCManagement() {
  console.log('🚀 Starting KYC Management System setup...');
  console.log('=====================================');

  // Step 1: Add rejection_reason column to kyc_information
  console.log('\n📋 Step 1: Adding rejection_reason column to kyc_information table');
  const addRejectionReasonSQL = `
    ALTER TABLE kyc_information 
    ADD COLUMN IF NOT EXISTS rejection_reason TEXT;
    
    COMMENT ON COLUMN kyc_information.rejection_reason IS 'Detailed reason provided when KYC submission is rejected';
    
    CREATE INDEX IF NOT EXISTS idx_kyc_information_rejection_reason 
    ON kyc_information(rejection_reason) 
    WHERE rejection_reason IS NOT NULL;
  `;

  if (!await executeSQL(addRejectionReasonSQL, 'Adding rejection_reason column and index')) {
    return false;
  }

  // Step 2: Create notification_logs table
  console.log('\n📧 Step 2: Creating notification_logs table');
  const createNotificationLogsSQL = `
    CREATE TABLE IF NOT EXISTS notification_logs (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      notification_type VARCHAR(50) NOT NULL,
      status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'bounced')),
      email_message_id VARCHAR(255),
      subject VARCHAR(255),
      recipient_email VARCHAR(255),
      metadata JSONB,
      error_message TEXT,
      sent_at TIMESTAMP WITH TIME ZONE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `;

  if (!await executeSQL(createNotificationLogsSQL, 'Creating notification_logs table')) {
    return false;
  }

  // Step 3: Create notification_logs indexes
  console.log('\n🔍 Step 3: Creating notification_logs indexes');
  const createNotificationIndexesSQL = `
    CREATE INDEX IF NOT EXISTS idx_notification_logs_user_id ON notification_logs(user_id);
    CREATE INDEX IF NOT EXISTS idx_notification_logs_type ON notification_logs(notification_type);
    CREATE INDEX IF NOT EXISTS idx_notification_logs_status ON notification_logs(status);
    CREATE INDEX IF NOT EXISTS idx_notification_logs_created_at ON notification_logs(created_at);
    CREATE INDEX IF NOT EXISTS idx_notification_logs_email_message_id ON notification_logs(email_message_id) WHERE email_message_id IS NOT NULL;
  `;

  if (!await executeSQL(createNotificationIndexesSQL, 'Creating notification_logs indexes')) {
    return false;
  }

  // Step 4: Add notification_logs comments
  console.log('\n📝 Step 4: Adding notification_logs table comments');
  const addNotificationCommentsSQL = `
    COMMENT ON TABLE notification_logs IS 'Log of all email notifications sent by the system';
    COMMENT ON COLUMN notification_logs.user_id IS 'ID of the user who received the notification';
    COMMENT ON COLUMN notification_logs.notification_type IS 'Type of notification (e.g., kyc_status_change, payment_confirmation)';
    COMMENT ON COLUMN notification_logs.status IS 'Status of the notification delivery';
    COMMENT ON COLUMN notification_logs.email_message_id IS 'Message ID from the email service provider';
    COMMENT ON COLUMN notification_logs.metadata IS 'Additional data related to the notification';
    COMMENT ON COLUMN notification_logs.error_message IS 'Error message if notification failed';
  `;

  if (!await executeSQL(addNotificationCommentsSQL, 'Adding notification_logs comments')) {
    return false;
  }

  // Step 5: Create RLS policies for notification_logs
  console.log('\n🔒 Step 5: Creating RLS policies for notification_logs');
  const createNotificationPoliciesSQL = `
    ALTER TABLE notification_logs ENABLE ROW LEVEL SECURITY;
    
    DROP POLICY IF EXISTS "Users can view their own notification logs" ON notification_logs;
    CREATE POLICY "Users can view their own notification logs" ON notification_logs
      FOR SELECT USING (auth.uid()::text = user_id::text);
    
    DROP POLICY IF EXISTS "Service role can manage all notification logs" ON notification_logs;
    CREATE POLICY "Service role can manage all notification logs" ON notification_logs
      FOR ALL USING (auth.role() = 'service_role');
  `;

  if (!await executeSQL(createNotificationPoliciesSQL, 'Creating notification_logs RLS policies')) {
    return false;
  }

  // Step 6: Create updated_at trigger for notification_logs
  console.log('\n⚡ Step 6: Creating updated_at trigger for notification_logs');
  const createNotificationTriggerSQL = `
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
    END;
    $$ language 'plpgsql';

    DROP TRIGGER IF EXISTS update_notification_logs_updated_at ON notification_logs;
    CREATE TRIGGER update_notification_logs_updated_at
        BEFORE UPDATE ON notification_logs
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
  `;

  if (!await executeSQL(createNotificationTriggerSQL, 'Creating notification_logs updated_at trigger')) {
    return false;
  }

  // Step 7: Verify KYC table structure
  console.log('\n✅ Step 7: Verifying KYC table structure');
  const verifyKYCTableSQL = `
    SELECT column_name, data_type, is_nullable, column_default
    FROM information_schema.columns 
    WHERE table_name = 'kyc_information' 
    AND table_schema = 'public'
    ORDER BY ordinal_position;
  `;

  const { data: kycColumns, error: kycError } = await supabase.rpc('exec_sql', { sql_query: verifyKYCTableSQL });
  if (kycError) {
    console.error('❌ Failed to verify KYC table structure:', kycError);
    return false;
  }

  console.log('✅ KYC table structure verified');
  console.log('   Columns:', kycColumns?.length || 0);

  // Step 8: Verify notification_logs table structure
  console.log('\n✅ Step 8: Verifying notification_logs table structure');
  const verifyNotificationTableSQL = `
    SELECT column_name, data_type, is_nullable, column_default
    FROM information_schema.columns 
    WHERE table_name = 'notification_logs' 
    AND table_schema = 'public'
    ORDER BY ordinal_position;
  `;

  const { data: notificationColumns, error: notificationError } = await supabase.rpc('exec_sql', { sql_query: verifyNotificationTableSQL });
  if (notificationError) {
    console.error('❌ Failed to verify notification_logs table structure:', notificationError);
    return false;
  }

  console.log('✅ Notification logs table structure verified');
  console.log('   Columns:', notificationColumns?.length || 0);

  console.log('\n=====================================');
  console.log('✅ KYC Management System setup completed successfully!');
  console.log('\nFeatures enabled:');
  console.log('  🔍 KYC submission management');
  console.log('  ✅ Approve/reject functionality');
  console.log('  📧 Email notifications');
  console.log('  📊 Statistics and reporting');
  console.log('  🔍 Search and filtering');
  console.log('  📋 Audit trail logging');
  console.log('  🔄 Batch operations');
  
  return true;
}

// Execute the setup
setupKYCManagement()
  .then((success) => {
    if (success) {
      console.log('\n🎉 Setup completed successfully!');
      console.log('You can now access the KYC Management section in the admin dashboard.');
      process.exit(0);
    } else {
      console.error('\n💥 Setup failed!');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('\n💥 Setup failed with error:', error);
    process.exit(1);
  });
