#!/usr/bin/env node

/**
 * DEBUG USER PASSWORD HASH FORMAT
 * 
 * This script checks the password hash format for the specific user
 * having login issues (Telegram ID: 1393852532)
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import bcrypt from 'bcryptjs';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const debugUserPassword = async () => {
  try {
    console.log('🔍 Debugging user password hash format...\n');

    // First, let's check the telegram_users table schema
    console.log('📋 Checking telegram_users table schema...');
    const { data: schemaData, error: schemaError } = await supabase
      .from('telegram_users')
      .select('*')
      .limit(1);

    if (!schemaError && schemaData && schemaData.length > 0) {
      console.log('   Available columns:', Object.keys(schemaData[0]));
    }

    // Find the user by Telegram ID
    const telegramId = '1393852532';
    
    // First check telegram_users table
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramId)
      .single();

    if (telegramError) {
      console.error('❌ Error finding telegram user:', telegramError);
      return;
    }

    console.log('📋 Telegram User Found:');
    console.log(`   Username: ${telegramUser.username}`);
    console.log(`   First Name: ${telegramUser.first_name}`);
    console.log(`   Email: ${telegramUser.email || 'Not set'}`);
    console.log(`   Password Hash: ${telegramUser.password_hash || 'Not set'}`);
    
    if (telegramUser.password_hash) {
      const hash = telegramUser.password_hash;
      console.log(`   Hash Length: ${hash.length}`);
      
      // Check hash format
      if (hash.length === 64 && /^[a-f0-9]+$/.test(hash)) {
        console.log('   🔴 Hash Format: OLD SHA-256 (VULNERABLE)');
      } else if (/^\$2[aby]\$/.test(hash)) {
        console.log('   ✅ Hash Format: NEW BCRYPT (SECURE)');
      } else {
        console.log('   ❓ Hash Format: UNKNOWN');
      }
    }

    // Check for linked user in users table by user_id
    if (telegramUser.user_id) {
      console.log(`\n📋 Checking linked user by user_id: ${telegramUser.user_id}`);
      const { data: linkedUser, error: linkedError } = await supabase
        .from('users')
        .select('*')
        .eq('id', telegramUser.user_id)
        .single();

      if (!linkedError && linkedUser) {
        console.log('📋 Linked User Found (by user_id):');
        console.log(`   ID: ${linkedUser.id}`);
        console.log(`   Username: ${linkedUser.username}`);
        console.log(`   Email: ${linkedUser.email}`);
        console.log(`   Password Hash: ${linkedUser.password_hash || 'Not set'}`);

        if (linkedUser.password_hash) {
          const hash = linkedUser.password_hash;
          console.log(`   Hash Length: ${hash.length}`);

          // Check hash format
          if (hash.length === 64 && /^[a-f0-9]+$/.test(hash)) {
            console.log('   🔴 Hash Format: OLD SHA-256 (VULNERABLE)');
          } else if (/^\$2[aby]\$/.test(hash)) {
            console.log('   ✅ Hash Format: NEW BCRYPT (SECURE)');
          } else {
            console.log('   ❓ Hash Format: UNKNOWN');
          }
        }
      } else {
        console.log('❌ No linked user found by user_id');
      }
    }

    // Also check for user by telegram_id in users table
    console.log(`\n📋 Checking users table by telegram_id: ${telegramId}`);
    const { data: userByTelegramId, error: userByTelegramIdError } = await supabase
      .from('users')
      .select('*')
      .eq('telegram_id', telegramId)
      .single();

    if (!userByTelegramIdError && userByTelegramId) {
      console.log('📋 User Found (by telegram_id):');
      console.log(`   ID: ${userByTelegramId.id}`);
      console.log(`   Username: ${userByTelegramId.username}`);
      console.log(`   Email: ${userByTelegramId.email}`);
      console.log(`   Password Hash: ${userByTelegramId.password_hash || 'Not set'}`);

      if (userByTelegramId.password_hash) {
        const hash = userByTelegramId.password_hash;
        console.log(`   Hash Length: ${hash.length}`);

        // Check hash format
        if (hash.length === 64 && /^[a-f0-9]+$/.test(hash)) {
          console.log('   🔴 Hash Format: OLD SHA-256 (VULNERABLE)');
        } else if (/^\$2[aby]\$/.test(hash)) {
          console.log('   ✅ Hash Format: NEW BCRYPT (SECURE)');
        } else {
          console.log('   ❓ Hash Format: UNKNOWN');
        }
      }
    } else {
      console.log('❌ No user found by telegram_id in users table');
    }

    // Test the password that was entered
    const testPassword = 'Gunst0n5o0!@#';
    console.log(`\n🧪 Testing password: "${testPassword}"`);

    // Test against the linked user's password hash (this is what should work)
    if (telegramUser.user_id) {
      const { data: linkedUser } = await supabase
        .from('users')
        .select('password_hash')
        .eq('id', telegramUser.user_id)
        .single();

      if (linkedUser && linkedUser.password_hash) {
        console.log('\n🔍 Testing against linked user password hash:');
        try {
          const bcryptResult = await bcrypt.compare(testPassword, linkedUser.password_hash);
          console.log(`   Bcrypt verification: ${bcryptResult ? '✅ SUCCESS' : '❌ FAILED'}`);

          if (bcryptResult) {
            console.log('\n🎉 PASSWORD VERIFICATION SUCCESSFUL!');
            console.log('The password hash is correct and the password works.');
            console.log('The issue must be in the login flow logic.');
          } else {
            console.log('\n❌ Password verification failed.');
            console.log('The stored hash does NOT match the password the admin tried to set.');
            console.log('This means the admin password change did not update the correct user.');

            // Test some common passwords that might be stored
            const commonPasswords = ['admin123', 'password', 'admin', 'Gunst0n5o0!'];
            console.log('\n🔍 Testing common passwords against stored hash:');

            for (const pwd of commonPasswords) {
              try {
                const result = await bcrypt.compare(pwd, linkedUser.password_hash);
                console.log(`   "${pwd}": ${result ? '✅ MATCH' : '❌ No match'}`);
                if (result) {
                  console.log(`\n🎯 FOUND IT! The stored password is: "${pwd}"`);
                  break;
                }
              } catch (error) {
                console.log(`   "${pwd}": ❌ ERROR`);
              }
            }
          }
        } catch (error) {
          console.log(`   Bcrypt verification: ❌ ERROR - ${error.message}`);
        }
      }
    }

    // Also test if telegram user has password_hash (it shouldn't)
    if (telegramUser.password_hash) {
      console.log('\n⚠️ Telegram user has password_hash (unexpected):');
      const hash = telegramUser.password_hash;

      try {
        const bcryptResult = await bcrypt.compare(testPassword, hash);
        console.log(`   Bcrypt verification: ${bcryptResult ? '✅ SUCCESS' : '❌ FAILED'}`);
      } catch (error) {
        console.log(`   Bcrypt verification: ❌ ERROR - ${error.message}`);
      }
    }

  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
};

debugUserPassword();
