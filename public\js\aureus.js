/**
 * AUREUS ALLIANCE HOLDINGS - UNIFIED DESIGN SYSTEM JAVASCRIPT
 * Professional Corporate Design System Interactive Features
 * Version: 1.0.0
 * 
 * This file provides JavaScript functionality for:
 * - Theme switching (dark/light mode)
 * - Interactive styling behaviors
 * - Accessibility enhancements
 * - Smooth animations and transitions
 * - Dynamic styling features
 */

(function() {
  'use strict';

  // ========================================
  // THEME MANAGEMENT SYSTEM
  // ========================================

  class AureusThemeManager {
    constructor() {
      this.currentTheme = 'dark'; // Fixed to dark theme only
      this.init();
    }

    init() {
      // Force dark theme - no theme switching
      this.currentTheme = 'dark';
      this.applyTheme(this.currentTheme);
      // Remove theme toggle and system listener setup
    }

    applyTheme(theme) {
      // Force dark theme only
      document.documentElement.setAttribute('data-theme', 'dark');
      this.currentTheme = 'dark';
      localStorage.setItem('aureus-theme', 'dark');

      // Dispatch custom event for theme change
      window.dispatchEvent(new CustomEvent('themeChanged', {
        detail: { theme: 'dark' }
      }));
    }
  }

  // ========================================
  // SMOOTH SCROLLING SYSTEM
  // ========================================

  class AureusSmoothScroll {
    constructor() {
      this.init();
    }

    init() {
      this.setupSmoothScrolling();
      this.setupScrollToTop();
    }

    setupSmoothScrolling() {
      // Smooth scrolling for anchor links
      document.addEventListener('click', (e) => {
        const target = e.target.closest('a[href^="#"]');
        if (target) {
          e.preventDefault();
          const targetId = target.getAttribute('href').substring(1);
          const targetElement = document.getElementById(targetId);
          
          if (targetElement) {
            targetElement.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        }
      });
    }

    setupScrollToTop() {
      // Create scroll to top button
      const scrollButton = document.createElement('button');
      scrollButton.innerHTML = '↑';
      scrollButton.className = 'scroll-to-top';
      scrollButton.setAttribute('aria-label', 'Scroll to top');
      
      // Style the button
      Object.assign(scrollButton.style, {
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        width: '50px',
        height: '50px',
        borderRadius: '50%',
        border: 'none',
        backgroundColor: 'var(--primary-color, #d4af37)',
        color: 'var(--text-color, #ffffff)',
        fontSize: '20px',
        cursor: 'pointer',
        opacity: '0',
        visibility: 'hidden',
        transition: 'all 0.3s ease',
        zIndex: '1000'
      });

      document.body.appendChild(scrollButton);

      // Show/hide button based on scroll position
      window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
          scrollButton.style.opacity = '1';
          scrollButton.style.visibility = 'visible';
        } else {
          scrollButton.style.opacity = '0';
          scrollButton.style.visibility = 'hidden';
        }
      });

      // Scroll to top functionality
      scrollButton.addEventListener('click', () => {
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      });
    }
  }

  // ========================================
  // ACCESSIBILITY ENHANCEMENTS
  // ========================================

  class AureusAccessibility {
    constructor() {
      this.init();
    }

    init() {
      this.setupKeyboardNavigation();
      this.setupFocusManagement();
      this.setupAriaLabels();
    }

    setupKeyboardNavigation() {
      // Enhanced keyboard navigation
      document.addEventListener('keydown', (e) => {
        // Skip to main content with Alt+M
        if (e.altKey && e.key === 'm') {
          e.preventDefault();
          const main = document.querySelector('main') || document.querySelector('[role="main"]');
          if (main) {
            main.focus();
            main.scrollIntoView({ behavior: 'smooth' });
          }
        }

        // Enhanced tab navigation
        if (e.key === 'Tab') {
          document.body.classList.add('keyboard-navigation');
        }
      });

      // Remove keyboard navigation class on mouse use
      document.addEventListener('mousedown', () => {
        document.body.classList.remove('keyboard-navigation');
      });
    }

    setupFocusManagement() {
      // Improve focus visibility
      const style = document.createElement('style');
      style.textContent = `
        .keyboard-navigation *:focus {
          outline: 2px solid var(--primary-color, #d4af37) !important;
          outline-offset: 2px !important;
        }
        
        .keyboard-navigation button:focus,
        .keyboard-navigation a:focus,
        .keyboard-navigation input:focus,
        .keyboard-navigation select:focus,
        .keyboard-navigation textarea:focus {
          box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.3) !important;
        }
      `;
      document.head.appendChild(style);
    }

    setupAriaLabels() {
      // Auto-add aria labels to common elements
      const buttons = document.querySelectorAll('button:not([aria-label]):not([aria-labelledby])');
      buttons.forEach(button => {
        if (button.textContent.trim()) {
          button.setAttribute('aria-label', button.textContent.trim());
        }
      });

      // Add role attributes where missing
      const navs = document.querySelectorAll('nav:not([role])');
      navs.forEach(nav => nav.setAttribute('role', 'navigation'));

      const mains = document.querySelectorAll('main:not([role])');
      mains.forEach(main => main.setAttribute('role', 'main'));
    }
  }

  // ========================================
  // ANIMATION SYSTEM
  // ========================================

  class AureusAnimations {
    constructor() {
      this.init();
    }

    init() {
      this.setupScrollAnimations();
      this.setupHoverEffects();
    }

    setupScrollAnimations() {
      // Intersection Observer for scroll animations
      const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      };

      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-in');
          }
        });
      }, observerOptions);

      // Observe elements with animation classes
      const animatedElements = document.querySelectorAll('.fade-in, .slide-up, .slide-in');
      animatedElements.forEach(el => observer.observe(el));
    }

    setupHoverEffects() {
      // Enhanced hover effects for interactive elements
      const interactiveElements = document.querySelectorAll('button, .card, .interactive');
      
      interactiveElements.forEach(element => {
        element.addEventListener('mouseenter', () => {
          element.style.transform = 'translateY(-2px)';
          element.style.transition = 'transform 0.2s ease';
        });

        element.addEventListener('mouseleave', () => {
          element.style.transform = 'translateY(0)';
        });
      });
    }
  }

  // ========================================
  // RESPONSIVE UTILITIES
  // ========================================

  class AureusResponsive {
    constructor() {
      this.init();
    }

    init() {
      this.setupBreakpointDetection();
      this.setupMobileOptimizations();
    }

    setupBreakpointDetection() {
      // Add CSS classes based on screen size
      const updateBreakpoint = () => {
        const width = window.innerWidth;
        const body = document.body;
        
        // Remove existing breakpoint classes
        body.classList.remove('mobile', 'tablet', 'desktop', 'large-desktop');
        
        // Add appropriate class
        if (width < 768) {
          body.classList.add('mobile');
        } else if (width < 1024) {
          body.classList.add('tablet');
        } else if (width < 1440) {
          body.classList.add('desktop');
        } else {
          body.classList.add('large-desktop');
        }
      };

      updateBreakpoint();
      window.addEventListener('resize', updateBreakpoint);
    }

    setupMobileOptimizations() {
      // Mobile-specific optimizations
      if ('ontouchstart' in window) {
        document.body.classList.add('touch-device');
        
        // Improve touch targets
        const style = document.createElement('style');
        style.textContent = `
          .touch-device button,
          .touch-device a,
          .touch-device input,
          .touch-device select {
            min-height: 44px;
            min-width: 44px;
          }
        `;
        document.head.appendChild(style);
      }
    }
  }

  // ========================================
  // PERFORMANCE OPTIMIZATIONS
  // ========================================

  class AureusPerformance {
    constructor() {
      this.init();
    }

    init() {
      this.setupDebouncing();
      this.setupPreloading();
    }

    setupDebouncing() {
      // Debounce scroll and resize events
      let scrollTimeout;
      let resizeTimeout;

      window.addEventListener('scroll', () => {
        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(() => {
          window.dispatchEvent(new CustomEvent('scrollEnd'));
        }, 100);
      });

      window.addEventListener('resize', () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
          window.dispatchEvent(new CustomEvent('resizeEnd'));
        }, 250);
      });
    }

    setupPreloading() {
      // Preload critical resources
      const preloadLinks = document.querySelectorAll('link[rel="preload"]');
      preloadLinks.forEach(link => {
        if (link.as === 'image') {
          const img = new Image();
          img.src = link.href;
        }
      });
    }
  }

  // ========================================
  // INITIALIZATION
  // ========================================

  // Initialize all systems when DOM is ready
  function initializeAureus() {
    new AureusThemeManager();
    new AureusSmoothScroll();
    new AureusAccessibility();
    new AureusAnimations();
    new AureusResponsive();
    new AureusPerformance();

    // Dispatch initialization complete event
    window.dispatchEvent(new CustomEvent('aureusInitialized'));
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAureus);
  } else {
    initializeAureus();
  }

  // Export for external use if needed
  window.Aureus = {
    version: '1.0.0',
    initialized: true
  };

})();
