# 🚀 Aureus Alliance Setup Guide

## 📋 Quick Setup Steps

### Step 1: Database Upgrades (Optional)
Run the SQL script in your Supabase SQL editor:
```sql
-- Copy and paste the contents of: sql/missing_tables_for_upgrades.sql
```

This adds:
- ✅ Helpful views for better data access
- ✅ Optional columns for enhanced functionality
- ✅ No impact on existing Telegram bot

### Step 2: Fix Storage Policies (Required for Web Uploads)
Run the storage policy fix script in your Supabase SQL editor:
```sql
-- Copy and paste the contents of: sql/fix_storage_policies.sql
```

This fixes:
- ✅ Allows web app users to upload to `proof` bucket
- ✅ Creates `marketing-materials` bucket if needed
- ✅ Configures proper RLS policies for web integration

### Step 3: Create Missing Tables (Required for Full Functionality)
Run the missing tables script in your Supabase SQL editor:
```sql
-- Copy and paste the contents of: sql/create_missing_tables.sql
```

This creates:
- ✅ `user_notification_summary` table for notification counts
- ✅ `user_payment_methods` table for saved payment methods
- ✅ Fixes RLS policies on `commission_balances` table
- ✅ Resolves 404 errors in browser console

### Step 3: Test Features
All features work immediately with your existing data:

1. **✅ View My Shares** - Shows real data from `aureus_share_purchases`
2. **✅ File Uploads** - Payment proofs upload to Supabase Storage
3. **✅ Marketing Toolkit** - Real analytics from `referrals` table
4. **✅ Admin Materials** - Uses existing `marketing_materials` table

## 🔧 Troubleshooting

### File Upload Issues
If you see "Storage permission denied" or "row-level security policy" error:
1. **Run the fix script**: Execute `sql/fix_storage_policies.sql` in Supabase SQL editor
2. **Check bucket exists**: Verify `proof` bucket exists in Storage dashboard
3. **Verify policies**: Ensure RLS policies allow authenticated users to upload

### No Share Data Showing
If "View My Shares" shows no data:
1. Check that `aureus_share_purchases` table has data
2. Verify the user has `status = 'active'` purchases
3. Confirm user authentication is working

### Marketing Analytics Empty
If referral analytics show no data:
1. Check that `referrals` table has data with `status = 'active'`
2. Verify referral relationships are properly linked
3. Confirm user has made referrals through the Telegram bot

## 📊 Database Structure

### Existing Tables Used
- `investment_phases` - Phase management
- `aureus_share_purchases` - User share purchases
- `marketing_materials` - Admin marketing materials
- `referrals` - Referral tracking
- `users` - User management
- `crypto_payment_transactions` - Payment tracking
- `commission_transactions` - Commission tracking

### New Views Added
- `user_share_holdings` - Summary of user holdings
- `phase_statistics` - Investment phase statistics

## 🔒 Security Features

### Row Level Security (RLS)
- Users can only see their own data
- Admins have full access to all data
- Storage buckets have proper access controls

### File Upload Security
- File type restrictions (images, PDFs)
- File size limits (5MB for proofs, 50MB for materials)
- Authenticated upload requirements

## 🎯 Feature Overview

### For Users
- **Share Purchase Flow** - Buy shares with crypto or bank transfer
- **View My Shares** - See all share purchases and holdings
- **File Upload** - Upload payment proofs securely
- **Marketing Toolkit** - Generate referral links and view analytics

### For Admins
- **Marketing Materials** - Upload and manage promotional content
- **User Management** - View and manage all users
- **Payment Review** - Approve/reject payment submissions
- **Analytics Dashboard** - View system-wide statistics

## 📞 Support

If you encounter any issues:
1. Check the browser console for error messages
2. Verify database connections in Supabase
3. Confirm all SQL scripts were executed successfully
4. Test with different user accounts (regular user vs admin)

## ✅ Success Checklist

After setup, verify these work:
- [ ] User can log in and see dashboard
- [ ] "View My Shares" shows existing purchase data
- [ ] File upload works for payment proofs
- [ ] Marketing toolkit shows referral analytics
- [ ] Admin can access marketing materials management
- [ ] All existing Telegram bot functionality still works

## 🚀 You're Ready!

Your Aureus Alliance web interface is now fully integrated with your existing Telegram bot database. All features work with real data while maintaining complete compatibility with your current system.
