const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function executeSQL(sql, description) {
  console.log(`🔄 ${description}...`);
  try {
    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });
    if (error) {
      console.error(`❌ ${description} failed:`, error.message);
      return false;
    } else {
      console.log(`✅ ${description} completed`);
      return true;
    }
  } catch (err) {
    console.error(`❌ ${description} error:`, err.message);
    return false;
  }
}

async function setupCompleteSystem() {
  console.log('🚀 Setting up complete Aureus Africa system...');
  console.log('');

  // Step 1: Verify core tables exist
  console.log('📋 Step 1: Verifying core tables...');
  
  const coreTables = ['users', 'aureus_share_purchases', 'kyc_information'];
  for (const table of coreTables) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('count(*)')
        .limit(1);
      
      if (error) {
        console.error(`❌ Table ${table} not accessible:`, error.message);
      } else {
        console.log(`✅ Table ${table} exists and accessible`);
      }
    } catch (err) {
      console.error(`❌ Error checking table ${table}:`, err.message);
    }
  }

  // Step 2: Setup notification system
  console.log('');
  console.log('📋 Step 2: Setting up notification system...');
  
  const notificationTableSQL = `
    CREATE TABLE IF NOT EXISTS user_notifications (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      notification_type VARCHAR(50) NOT NULL,
      title VARCHAR(255) NOT NULL,
      message TEXT NOT NULL,
      metadata JSONB DEFAULT '{}',
      is_read BOOLEAN DEFAULT FALSE,
      is_archived BOOLEAN DEFAULT FALSE,
      priority VARCHAR(20) DEFAULT 'normal',
      payment_id UUID,
      commission_id UUID,
      referral_id UUID,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      read_at TIMESTAMP WITH TIME ZONE,
      archived_at TIMESTAMP WITH TIME ZONE,
      CONSTRAINT valid_notification_type CHECK (
        notification_type IN (
          'payment_approved', 'payment_rejected', 'commission_earned', 
          'system', 'referral', 'withdrawal', 'share_purchase', 
          'phase_change', 'account_update', 'security_alert'
        )
      ),
      CONSTRAINT valid_priority CHECK (priority IN ('low', 'normal', 'high', 'urgent'))
    );

    CREATE INDEX IF NOT EXISTS idx_user_notifications_user_id ON user_notifications(user_id);
    CREATE INDEX IF NOT EXISTS idx_user_notifications_type ON user_notifications(notification_type);
    CREATE INDEX IF NOT EXISTS idx_user_notifications_read ON user_notifications(is_read);
    CREATE INDEX IF NOT EXISTS idx_user_notifications_created_at ON user_notifications(created_at);
  `;

  await executeSQL(notificationTableSQL, 'Creating notification system');

  // Step 3: Setup certificates table
  console.log('');
  console.log('📋 Step 3: Setting up certificates system...');
  
  const certificatesTableSQL = `
    CREATE TABLE IF NOT EXISTS certificates (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      purchase_id UUID REFERENCES aureus_share_purchases(id) ON DELETE SET NULL,
      certificate_number VARCHAR(50) UNIQUE NOT NULL,
      shares_count INTEGER NOT NULL,
      issue_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      status VARCHAR(20) DEFAULT 'issued' CHECK (status IN ('issued', 'revoked', 'transferred')),
      certificate_data JSONB DEFAULT '{}'::jsonb,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    CREATE INDEX IF NOT EXISTS idx_certificates_user_id ON certificates(user_id);
    CREATE INDEX IF NOT EXISTS idx_certificates_purchase_id ON certificates(purchase_id);
    CREATE INDEX IF NOT EXISTS idx_certificates_certificate_number ON certificates(certificate_number);
    CREATE INDEX IF NOT EXISTS idx_certificates_status ON certificates(status);

    CREATE OR REPLACE FUNCTION generate_certificate_number()
    RETURNS VARCHAR(50) AS $$
    DECLARE
      new_number VARCHAR(50);
      counter INTEGER := 1;
    BEGIN
      LOOP
        new_number := 'AUR-' || EXTRACT(YEAR FROM NOW()) || '-' || LPAD(counter::TEXT, 6, '0');
        IF NOT EXISTS (SELECT 1 FROM certificates WHERE certificate_number = new_number) THEN
          RETURN new_number;
        END IF;
        counter := counter + 1;
        IF counter > 999999 THEN
          RAISE EXCEPTION 'Unable to generate unique certificate number';
        END IF;
      END LOOP;
    END;
    $$ LANGUAGE plpgsql;

    CREATE OR REPLACE FUNCTION admin_create_certificate(
      p_user_id INTEGER,
      p_purchase_id UUID,
      p_shares_count INTEGER,
      p_certificate_data JSONB DEFAULT '{}'::jsonb
    )
    RETURNS VARCHAR(50) AS $$
    DECLARE
      new_cert_number VARCHAR(50);
    BEGIN
      new_cert_number := generate_certificate_number();
      INSERT INTO certificates (
        user_id, purchase_id, certificate_number, shares_count, certificate_data
      ) VALUES (
        p_user_id, p_purchase_id, new_cert_number, p_shares_count, p_certificate_data
      );
      RETURN new_cert_number;
    END;
    $$ LANGUAGE plpgsql;
  `;

  await executeSQL(certificatesTableSQL, 'Creating certificates system');

  // Step 4: Update KYC table for facial recognition
  console.log('');
  console.log('📋 Step 4: Updating KYC system for facial recognition...');
  
  const kycUpdateSQL = `
    -- Ensure KYC table supports all ID types including driver's license
    ALTER TABLE kyc_information 
    DROP CONSTRAINT IF EXISTS kyc_information_id_type_check;
    
    ALTER TABLE kyc_information 
    ADD CONSTRAINT kyc_information_id_type_check 
    CHECK (id_type IN ('national_id', 'passport', 'drivers_license'));

    -- Add facial recognition fields if they don't exist
    ALTER TABLE kyc_information 
    ADD COLUMN IF NOT EXISTS facial_recognition_completed BOOLEAN DEFAULT FALSE;
    
    ALTER TABLE kyc_information 
    ADD COLUMN IF NOT EXISTS facial_recognition_confidence DECIMAL(3,2);
    
    ALTER TABLE kyc_information 
    ADD COLUMN IF NOT EXISTS facial_recognition_session_id VARCHAR(100);

    -- Update certificate_data to support facial recognition data
    COMMENT ON COLUMN kyc_information.certificate_data IS 'JSON data containing documents, facial_recognition results, and other verification metadata';
  `;

  await executeSQL(kycUpdateSQL, 'Updating KYC system for facial recognition');

  // Step 5: Ensure storage bucket exists
  console.log('');
  console.log('📋 Step 5: Verifying storage bucket...');
  
  try {
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (!bucketsError) {
      const proofBucket = buckets.find(bucket => bucket.name === 'proof');
      if (!proofBucket) {
        console.log('📦 Creating proof bucket...');
        const { error: createError } = await supabase.storage.createBucket('proof', {
          public: false,
          allowedMimeTypes: ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'],
          fileSizeLimit: 10 * 1024 * 1024 // 10MB
        });
        
        if (createError) {
          console.error('❌ Failed to create proof bucket:', createError.message);
        } else {
          console.log('✅ Proof bucket created successfully');
        }
      } else {
        console.log('✅ Proof bucket already exists');
      }
    }
  } catch (bucketError) {
    console.error('⚠️ Could not verify storage bucket:', bucketError.message);
  }

  // Step 6: Create sample data for testing
  console.log('');
  console.log('📋 Step 6: Creating sample data...');
  
  try {
    // Get existing users
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, username, email')
      .limit(5);
    
    if (!usersError && users && users.length > 0) {
      console.log(`📋 Found ${users.length} users for sample data creation`);
      
      for (const user of users) {
        // Create welcome notification if none exists
        const { data: existingNotifications } = await supabase
          .from('user_notifications')
          .select('id')
          .eq('user_id', user.id)
          .limit(1);
        
        if (!existingNotifications || existingNotifications.length === 0) {
          await supabase
            .from('user_notifications')
            .insert({
              user_id: user.id,
              notification_type: 'system',
              title: '🎉 Welcome to Aureus Africa!',
              message: `Welcome ${user.username || user.email}! Your account is ready. Complete your KYC verification to start purchasing shares and earning dividends.`,
              metadata: { welcome: true, created_by: 'system_setup' },
              priority: 'normal'
            });
          
          console.log(`✅ Created welcome notification for user ${user.id}`);
        }
      }
    }
  } catch (error) {
    console.error('⚠️ Error creating sample data:', error.message);
  }

  // Step 7: Set permissions
  console.log('');
  console.log('📋 Step 7: Setting permissions...');
  
  const permissionsSQL = `
    GRANT SELECT, INSERT, UPDATE ON user_notifications TO authenticated;
    GRANT SELECT, INSERT, UPDATE ON certificates TO authenticated;
    GRANT SELECT, INSERT, UPDATE ON kyc_information TO authenticated;
  `;

  await executeSQL(permissionsSQL, 'Setting table permissions');

  // Step 8: Final system test
  console.log('');
  console.log('📋 Step 8: Testing complete system...');
  
  const testResults = {
    notifications: false,
    certificates: false,
    kyc: false,
    storage: false
  };

  try {
    // Test notifications
    const { error: notifError } = await supabase
      .from('user_notifications')
      .select('count(*)')
      .limit(1);
    testResults.notifications = !notifError;

    // Test certificates
    const { error: certError } = await supabase
      .from('certificates')
      .select('count(*)')
      .limit(1);
    testResults.certificates = !certError;

    // Test KYC
    const { error: kycError } = await supabase
      .from('kyc_information')
      .select('count(*)')
      .limit(1);
    testResults.kyc = !kycError;

    // Test storage
    const { data: buckets, error: storageError } = await supabase.storage.listBuckets();
    testResults.storage = !storageError && buckets.some(b => b.name === 'proof');

  } catch (error) {
    console.error('❌ System test error:', error.message);
  }

  console.log('');
  console.log('🎯 System Test Results:');
  console.log(`📧 Notifications: ${testResults.notifications ? '✅ Working' : '❌ Failed'}`);
  console.log(`📜 Certificates: ${testResults.certificates ? '✅ Working' : '❌ Failed'}`);
  console.log(`🔐 KYC System: ${testResults.kyc ? '✅ Working' : '❌ Failed'}`);
  console.log(`💾 Storage: ${testResults.storage ? '✅ Working' : '❌ Failed'}`);

  const allWorking = Object.values(testResults).every(result => result);

  console.log('');
  if (allWorking) {
    console.log('🎉 Complete system setup successful!');
    console.log('');
    console.log('📋 What\'s now available:');
    console.log('1. ✅ Portfolio data loading with enhanced error handling');
    console.log('2. ✅ Notification system with real-time updates');
    console.log('3. ✅ Advanced facial recognition KYC verification');
    console.log('4. ✅ Document upload system with proof storage');
    console.log('5. ✅ Certificate generation and management');
    console.log('');
    console.log('🚀 Next steps:');
    console.log('• Users can now complete full KYC with facial recognition');
    console.log('• Portfolio will display real share purchase data');
    console.log('• Notifications will show payment and commission updates');
    console.log('• Admins can generate and manage certificates');
    return true;
  } else {
    console.log('⚠️ Some components may not be fully functional');
    console.log('Please check the individual error messages above');
    return false;
  }
}

// Main execution
if (require.main === module) {
  setupCompleteSystem()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('❌ Setup failed:', error);
      process.exit(1);
    });
}

module.exports = { setupCompleteSystem };
