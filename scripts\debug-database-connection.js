#!/usr/bin/env node

/**
 * Debug Database Connection Issues
 * 
 * This script helps identify and fix the 400 errors when accessing
 * user data and commission balances.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('🔍 Debugging Database Connection Issues\n');

async function debugDatabaseConnection() {
  try {
    console.log('1. Testing with ANON key (same as web app)...');
    
    if (!supabaseAnonKey) {
      console.error('❌ Missing VITE_SUPABASE_ANON_KEY');
      return;
    }

    const anonClient = createClient(supabaseUrl, supabaseAnonKey);

    // Test the exact same query that's failing in the web app
    console.log('   Testing users table query...');
    const { data: userData, error: userError } = await anonClient
      .from('users')
      .select('telegram_id, username, first_name')
      .eq('id', 4);

    if (userError) {
      console.error('   ❌ Users query failed:', userError);
      console.error('   Error details:', {
        code: userError.code,
        message: userError.message,
        details: userError.details,
        hint: userError.hint
      });
    } else {
      console.log('   ✅ Users query successful:', userData);
    }

    console.log('\n   Testing commission_balances table query...');
    const { data: commissionData, error: commissionError } = await anonClient
      .from('commission_balances')
      .select('usdt_balance, share_balance, total_earned_usdt, total_earned_shares, escrowed_amount, total_withdrawn_usdt')
      .eq('user_id', 4);

    if (commissionError) {
      console.error('   ❌ Commission balance query failed:', commissionError);
      console.error('   Error details:', {
        code: commissionError.code,
        message: commissionError.message,
        details: commissionError.details,
        hint: commissionError.hint
      });
    } else {
      console.log('   ✅ Commission balance query successful:', commissionData);
    }

    // Test with service key if anon key fails
    if ((userError || commissionError) && supabaseServiceKey) {
      console.log('\n2. Testing with SERVICE key...');
      
      const serviceClient = createClient(supabaseUrl, supabaseServiceKey, {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      });

      console.log('   Testing users table with service key...');
      const { data: serviceUserData, error: serviceUserError } = await serviceClient
        .from('users')
        .select('telegram_id, username, first_name')
        .eq('id', 4);

      if (serviceUserError) {
        console.error('   ❌ Service users query failed:', serviceUserError);
      } else {
        console.log('   ✅ Service users query successful:', serviceUserData);
      }

      console.log('   Testing commission_balances with service key...');
      const { data: serviceCommissionData, error: serviceCommissionError } = await serviceClient
        .from('commission_balances')
        .select('usdt_balance, share_balance, total_earned_usdt, total_earned_shares, escrowed_amount, total_withdrawn_usdt')
        .eq('user_id', 4);

      if (serviceCommissionError) {
        console.error('   ❌ Service commission query failed:', serviceCommissionError);
      } else {
        console.log('   ✅ Service commission query successful:', serviceCommissionData);
      }
    }

    console.log('\n🔧 DIAGNOSIS:');
    
    if (userError || commissionError) {
      console.log('\n❌ ANON KEY QUERIES FAILING - Possible causes:');
      console.log('1. Row Level Security (RLS) policies are too restrictive');
      console.log('2. Tables don\'t allow anonymous access');
      console.log('3. User ID 4 doesn\'t exist or is not accessible');
      console.log('4. Database permissions are misconfigured');
      
      console.log('\n🛠️ SOLUTIONS:');
      console.log('1. Check RLS policies on users and commission_balances tables');
      console.log('2. Ensure anon role has SELECT permissions');
      console.log('3. Verify user ID 4 exists and is active');
      console.log('4. Consider temporarily disabling RLS for testing');
      
      console.log('\n📋 NEXT STEPS:');
      console.log('1. Run: SELECT * FROM users WHERE id = 4; in Supabase SQL editor');
      console.log('2. Check RLS policies in Supabase dashboard');
      console.log('3. Verify anon role permissions');
      console.log('4. Test queries directly in Supabase SQL editor');
    } else {
      console.log('\n✅ DATABASE QUERIES WORKING');
      console.log('The issue might be in the web application authentication flow.');
      console.log('Check that the user object has the correct database_user.id value.');
    }

  } catch (error) {
    console.error('❌ Debug script failed:', error);
  }
}

debugDatabaseConnection();
