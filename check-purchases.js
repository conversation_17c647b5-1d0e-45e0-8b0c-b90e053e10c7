import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_SERVICE_ROLE_KEY
);

async function checkPurchases() {
  console.log('🔍 Checking database for share purchases...\n');
  
  try {
    // Check all purchases
    const { data: allPurchases, error: allError } = await supabase
      .from('aureus_share_purchases')
      .select('*');
      
    if (allError) {
      console.error('❌ Error accessing aureus_share_purchases:', allError);
      return;
    }
    
    console.log(`✅ Total purchases in database: ${allPurchases.length}`);
    
    if (allPurchases.length > 0) {
      console.log('\n📊 Purchase status breakdown:');
      const statusCounts = {};
      allPurchases.forEach(p => {
        statusCounts[p.status] = (statusCounts[p.status] || 0) + 1;
      });
      
      Object.entries(statusCounts).forEach(([status, count]) => {
        console.log(`   ${status}: ${count}`);
      });
      
      console.log('\n📋 Sample purchases:');
      allPurchases.slice(0, 3).forEach((purchase, i) => {
        console.log(`   ${i + 1}. ID: ${purchase.id}`);
        console.log(`      User: ${purchase.user_id}`);
        console.log(`      Status: ${purchase.status}`);
        console.log(`      Shares: ${purchase.shares_purchased}`);
        console.log(`      Amount: $${purchase.total_amount}`);
        console.log(`      Created: ${purchase.created_at}`);
        console.log('');
      });
    }
    
    // Check active purchases specifically
    const { data: activePurchases, error: activeError } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .eq('status', 'active');
      
    if (activeError) {
      console.error('❌ Error getting active purchases:', activeError);
    } else {
      console.log(`🟢 Active purchases: ${activePurchases.length}`);
    }
    
    // Check if there are any approved payments that should have purchases
    const { data: approvedPayments, error: paymentsError } = await supabase
      .from('crypto_payment_transactions')
      .select('*')
      .eq('status', 'approved');
      
    if (paymentsError) {
      console.error('❌ Error getting approved payments:', paymentsError);
    } else {
      console.log(`💰 Approved payments: ${approvedPayments.length}`);
      
      if (approvedPayments.length > 0 && allPurchases.length === 0) {
        console.log('\n⚠️  WARNING: There are approved payments but no share purchases!');
        console.log('   This suggests the payment-to-purchase conversion is not working.');
      }
    }
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

checkPurchases().then(() => {
  console.log('\n✅ Check complete');
  process.exit(0);
}).catch(console.error);
