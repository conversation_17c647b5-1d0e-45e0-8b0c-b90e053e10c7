import { supabase, getServiceRoleClient } from '../supabase';

export interface OnboardingStep {
  id: string;
  name: string;
  description: string;
  icon: string;
  required: boolean;
  order: number;
  category: 'account' | 'verification' | 'financial' | 'social' | 'advanced';
  estimatedTime: number; // minutes
  dependencies?: string[]; // step IDs that must be completed first
  unlocks?: string[]; // features unlocked after completion
}

export interface UserOnboardingProgress {
  id: string;
  user_id: number;
  step_id: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'skipped';
  started_at?: string;
  completed_at?: string;
  data?: any; // step-specific data
  created_at: string;
  updated_at: string;
}

export interface OnboardingAchievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  badge_color: string;
  requirements: {
    steps_completed?: string[];
    minimum_progress?: number;
    time_based?: boolean;
  };
}

export interface UserOnboardingStatus {
  user_id: number;
  overall_progress: number;
  completed_steps: number;
  total_steps: number;
  current_step?: OnboardingStep;
  next_steps: OnboardingStep[];
  achievements: OnboardingAchievement[];
  estimated_completion_time: number;
  started_at?: string;
  last_activity_at?: string;
}

class OnboardingService {
  private readonly ONBOARDING_STEPS: OnboardingStep[] = [
    // Step 1: Country Selection
    {
      id: 'country_selection',
      name: 'Select Your Country',
      description: 'Choose your country to determine available payment methods',
      icon: '🌍',
      required: true,
      order: 1,
      category: 'account',
      estimatedTime: 2,
      unlocks: ['payment_methods', 'local_currency']
    },

    // Step 2: First Share Purchase
    {
      id: 'first_share_purchase',
      name: 'Purchase Your First Shares',
      description: 'Buy your first Aureus Africa shares to start earning dividends',
      icon: '💰',
      required: true,
      order: 2,
      category: 'financial',
      estimatedTime: 7,
      dependencies: ['country_selection'],
      unlocks: ['portfolio_tracking', 'dividend_calculator']
    },

    // Step 3: KYC Verification
    {
      id: 'kyc_verification',
      name: 'Complete KYC Verification',
      description: 'Required for share certificate creation and account security',
      icon: '🆔',
      required: true,
      order: 3,
      category: 'verification',
      estimatedTime: 12,
      dependencies: ['first_share_purchase'],
      unlocks: ['withdrawals', 'share_certificates', 'higher_limits']
    },


  ];

  private readonly ACHIEVEMENTS: OnboardingAchievement[] = [
    {
      id: 'country_selected',
      name: 'Country Selected',
      description: 'Selected your country for payments',
      icon: '🌍',
      badge_color: 'blue',
      requirements: {
        steps_completed: ['country_selection']
      }
    },
    {
      id: 'first_investor',
      name: 'First Investor',
      description: 'Made your first share purchase',
      icon: '💎',
      badge_color: 'gold',
      requirements: {
        steps_completed: ['first_share_purchase']
      }
    },
    {
      id: 'verified_investor',
      name: 'Verified Investor',
      description: 'Completed KYC verification',
      icon: '✅',
      badge_color: 'green',
      requirements: {
        steps_completed: ['kyc_verification']
      }
    },
    {
      id: 'onboarding_complete',
      name: 'Onboarding Complete',
      description: 'Completed all onboarding steps',
      icon: '🚀',
      badge_color: 'rainbow',
      requirements: {
        minimum_progress: 100
      }
    }
  ];

  /**
   * Get all onboarding steps
   */
  getAllSteps(): OnboardingStep[] {
    return [...this.ONBOARDING_STEPS];
  }

  /**
   * Auto-complete steps that are already completed by the user
   */
  private async autoCompleteVerifiedSteps(userId: number): Promise<void> {
    const serviceClient = getServiceRoleClient();

    try {
      console.log(`🔍 Checking auto-completion for user ${userId}`);

      // Get user data and related information
      const { data: userData, error: userError } = await serviceClient
        .from('users')
        .select('country, kyc_status')
        .eq('id', userId)
        .single();

      if (userError || !userData) {
        console.warn('Could not fetch user data for auto-completion');
        return;
      }

      // Check for existing share purchases
      const { data: sharePurchases, error: purchaseError } = await serviceClient
        .from('aureus_share_purchases')
        .select('id')
        .eq('user_id', userId)
        .eq('status', 'active')
        .limit(1);

      if (purchaseError) {
        console.warn('Could not check share purchases for auto-completion');
      }

      // Check for existing transactions as backup
      const { data: transactions, error: transactionError } = await serviceClient
        .from('transactions')
        .select('id')
        .eq('user_id', userId)
        .eq('type', 'share_purchase')
        .limit(1);

      if (transactionError) {
        console.warn('Could not check transactions for auto-completion');
      }

      // Auto-complete country selection if user has country set
      if (userData.country) {
        await this.autoCompleteStep(userId, 'country_selection', 'country_already_selected');
      }

      // Auto-complete first share purchase if user has purchases
      if ((sharePurchases && sharePurchases.length > 0) || (transactions && transactions.length > 0)) {
        await this.autoCompleteStep(userId, 'first_share_purchase', 'shares_already_purchased');
      }

      // Auto-complete KYC verification if user has approved KYC
      if (userData.kyc_status === 'approved' || userData.kyc_status === 'verified') {
        await this.autoCompleteStep(userId, 'kyc_verification', 'kyc_already_approved');
      }

    } catch (error) {
      console.error('❌ Error auto-completing verified steps:', error);
    }
  }

  /**
   * Helper method to auto-complete a specific step
   */
  private async autoCompleteStep(userId: number, stepId: string, reason: string): Promise<void> {
    const serviceClient = getServiceRoleClient();

    try {
      // Check if step is already completed
      const { data: existingProgress } = await serviceClient
        .from('user_onboarding_progress')
        .select('status')
        .eq('user_id', userId)
        .eq('step_id', stepId)
        .single();

      if (!existingProgress || existingProgress.status !== 'completed') {
        await serviceClient
          .from('user_onboarding_progress')
          .upsert({
            user_id: userId,
            step_id: stepId,
            status: 'completed',
            completed_at: new Date().toISOString(),
            data: { auto_completed: true, reason },
            updated_at: new Date().toISOString()
          }, {
            onConflict: 'user_id,step_id'
          });

        console.log(`✅ Auto-completed step: ${stepId} (${reason})`);
      }
    } catch (error) {
      console.error(`❌ Error auto-completing step ${stepId}:`, error);
    }
  }

  /**
   * Get user's onboarding status
   */
  async getUserOnboardingStatus(userId: number): Promise<UserOnboardingStatus> {
    console.log(`🎯 Getting onboarding status for user ${userId}`);

    const serviceClient = getServiceRoleClient();

    // Auto-complete steps that are already verified
    await this.autoCompleteVerifiedSteps(userId);

    // Get user's progress for all steps
    const { data: progressData, error } = await serviceClient
      .from('user_onboarding_progress')
      .select('*')
      .eq('user_id', userId);

    if (error) {
      console.error('❌ Failed to get onboarding progress:', error);
    }

    const progress = progressData || [];
    const progressMap = new Map(progress.map(p => [p.step_id, p]));

    // Calculate overall progress
    const completedSteps = progress.filter(p => p.status === 'completed').length;
    const totalSteps = this.ONBOARDING_STEPS.length;
    const overallProgress = totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;

    // Find current step (first incomplete required step)
    const currentStep = this.ONBOARDING_STEPS.find(step => {
      const stepProgress = progressMap.get(step.id);
      return step.required && (!stepProgress || stepProgress.status !== 'completed');
    });

    // Get next available steps
    const nextSteps = this.getAvailableSteps(userId, progressMap);

    // Calculate achievements
    const achievements = this.calculateAchievements(progress, overallProgress);

    // Calculate estimated completion time
    const remainingSteps = this.ONBOARDING_STEPS.filter(step => {
      const stepProgress = progressMap.get(step.id);
      return !stepProgress || stepProgress.status !== 'completed';
    });
    const estimatedTime = remainingSteps.reduce((total, step) => total + step.estimatedTime, 0);

    // Get timing information
    const startedAt = progress.length > 0 
      ? progress.reduce((earliest, p) => 
          !earliest || (p.started_at && p.started_at < earliest) ? p.started_at : earliest, 
          null as string | null
        )
      : undefined;

    const lastActivityAt = progress.length > 0
      ? progress.reduce((latest, p) => 
          !latest || (p.updated_at && p.updated_at > latest) ? p.updated_at : latest,
          null as string | null
        )
      : undefined;

    console.log(`✅ Onboarding status calculated: ${overallProgress}% complete`);

    return {
      user_id: userId,
      overall_progress: overallProgress,
      completed_steps: completedSteps,
      total_steps: totalSteps,
      current_step: currentStep,
      next_steps: nextSteps.slice(0, 3), // Show next 3 available steps
      achievements,
      estimated_completion_time: estimatedTime,
      started_at: startedAt || undefined,
      last_activity_at: lastActivityAt || undefined
    };
  }

  /**
   * Get available steps for user
   */
  private getAvailableSteps(userId: number, progressMap: Map<string, UserOnboardingProgress>): OnboardingStep[] {
    return this.ONBOARDING_STEPS.filter(step => {
      const stepProgress = progressMap.get(step.id);
      
      // Skip if already completed
      if (stepProgress?.status === 'completed') return false;
      
      // Check if dependencies are met
      if (step.dependencies) {
        const dependenciesMet = step.dependencies.every(depId => {
          const depProgress = progressMap.get(depId);
          return depProgress?.status === 'completed';
        });
        if (!dependenciesMet) return false;
      }
      
      return true;
    }).sort((a, b) => a.order - b.order);
  }

  /**
   * Calculate user achievements
   */
  private calculateAchievements(progress: UserOnboardingProgress[], overallProgress: number): OnboardingAchievement[] {
    const completedSteps = progress
      .filter(p => p.status === 'completed')
      .map(p => p.step_id);

    return this.ACHIEVEMENTS.filter(achievement => {
      const { requirements } = achievement;
      
      // Check minimum progress requirement
      if (requirements.minimum_progress && overallProgress < requirements.minimum_progress) {
        return false;
      }
      
      // Check required steps
      if (requirements.steps_completed) {
        const hasAllSteps = requirements.steps_completed.every(stepId => 
          completedSteps.includes(stepId)
        );
        if (!hasAllSteps) return false;
      }
      
      return true;
    });
  }

  /**
   * Start an onboarding step
   */
  async startStep(userId: number, stepId: string, data?: any): Promise<boolean> {
    console.log(`🎯 Starting onboarding step ${stepId} for user ${userId}`);

    const serviceClient = getServiceRoleClient();

    try {
      const { error } = await serviceClient
        .from('user_onboarding_progress')
        .upsert({
          user_id: userId,
          step_id: stepId,
          status: 'in_progress',
          started_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          data: data || null
        }, {
          onConflict: 'user_id,step_id'
        });

      if (error) {
        console.error('❌ Failed to start onboarding step:', error);
        return false;
      }

      console.log('✅ Onboarding step started successfully');
      return true;
    } catch (error) {
      console.error('❌ Error starting onboarding step:', error);
      return false;
    }
  }

  /**
   * Complete an onboarding step
   */
  async completeStep(userId: number, stepId: string, data?: any): Promise<boolean> {
    console.log(`✅ Completing onboarding step ${stepId} for user ${userId}`);

    const serviceClient = getServiceRoleClient();

    try {
      const { error } = await serviceClient
        .from('user_onboarding_progress')
        .upsert({
          user_id: userId,
          step_id: stepId,
          status: 'completed',
          completed_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          data: data || null
        }, {
          onConflict: 'user_id,step_id'
        });

      if (error) {
        console.error('❌ Failed to complete onboarding step:', error);
        return false;
      }

      // Check for new achievements
      await this.checkAndAwardAchievements(userId);

      console.log('✅ Onboarding step completed successfully');
      return true;
    } catch (error) {
      console.error('❌ Error completing onboarding step:', error);
      return false;
    }
  }

  /**
   * Skip an onboarding step
   */
  async skipStep(userId: number, stepId: string): Promise<boolean> {
    console.log(`⏭️ Skipping onboarding step ${stepId} for user ${userId}`);

    const step = this.ONBOARDING_STEPS.find(s => s.id === stepId);
    if (step?.required) {
      console.warn('❌ Cannot skip required onboarding step');
      return false;
    }

    const serviceClient = getServiceRoleClient();

    try {
      const { error } = await serviceClient
        .from('user_onboarding_progress')
        .upsert({
          user_id: userId,
          step_id: stepId,
          status: 'skipped',
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id,step_id'
        });

      if (error) {
        console.error('❌ Failed to skip onboarding step:', error);
        return false;
      }

      console.log('✅ Onboarding step skipped successfully');
      return true;
    } catch (error) {
      console.error('❌ Error skipping onboarding step:', error);
      return false;
    }
  }

  /**
   * Check and award achievements
   */
  private async checkAndAwardAchievements(userId: number): Promise<void> {
    try {
      const status = await this.getUserOnboardingStatus(userId);
      const newAchievements = status.achievements;

      // Get existing achievements
      const serviceClient = getServiceRoleClient();
      const { data: existingAchievements } = await serviceClient
        .from('user_achievements')
        .select('achievement_id')
        .eq('user_id', userId);

      const existingIds = new Set(existingAchievements?.map(a => a.achievement_id) || []);

      // Award new achievements
      for (const achievement of newAchievements) {
        if (!existingIds.has(achievement.id)) {
          await serviceClient
            .from('user_achievements')
            .insert({
              user_id: userId,
              achievement_id: achievement.id,
              earned_at: new Date().toISOString()
            });

          console.log(`🏆 Awarded achievement "${achievement.name}" to user ${userId}`);
        }
      }
    } catch (error) {
      console.error('❌ Error checking achievements:', error);
    }
  }

  /**
   * Get all onboarding steps
   */
  getOnboardingSteps(): OnboardingStep[] {
    return [...this.ONBOARDING_STEPS];
  }

  /**
   * Get all achievements
   */
  getAchievements(): OnboardingAchievement[] {
    return [...this.ACHIEVEMENTS];
  }

  /**
   * Reset user onboarding progress
   */
  async resetOnboarding(userId: number): Promise<boolean> {
    console.log(`🔄 Resetting onboarding for user ${userId}`);

    const serviceClient = getServiceRoleClient();

    try {
      const { error } = await serviceClient
        .from('user_onboarding_progress')
        .delete()
        .eq('user_id', userId);

      if (error) {
        console.error('❌ Failed to reset onboarding:', error);
        return false;
      }

      console.log('✅ Onboarding reset successfully');
      return true;
    } catch (error) {
      console.error('❌ Error resetting onboarding:', error);
      return false;
    }
  }
}

// Export singleton instance
export const onboardingService = new OnboardingService();
