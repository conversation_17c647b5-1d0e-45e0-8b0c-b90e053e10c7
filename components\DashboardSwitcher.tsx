import React, { useState } from 'react';

interface DashboardSwitcherProps {
  currentDashboard: 'shareholder' | 'affiliate';
  onSwitch: (dashboard: 'shareholder' | 'affiliate') => void;
  user: any;
}

export const DashboardSwitcher: React.FC<DashboardSwitcherProps> = ({
  currentDashboard,
  onSwitch,
  user
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const dashboards = [
    {
      id: 'shareholder' as const,
      name: 'Shareholder Dashboard',
      description: 'Invest in shares, view portfolio, track dividends',
      icon: '📈',
      color: 'from-green-500 to-emerald-600',
      features: ['Buy Shares', 'Portfolio Tracking', 'Dividend Calculator', 'Share Certificates']
    },
    {
      id: 'affiliate' as const,
      name: 'Affiliate Dashboard',
      description: 'Network marketing, referrals, commissions',
      icon: '🤝',
      color: 'from-blue-500 to-indigo-600',
      features: ['Referral Links', 'Commission Tracking', 'Team Building', 'Marketing Tools']
    }
  ];

  const currentDashboardInfo = dashboards.find(d => d.id === currentDashboard);
  const otherDashboard = dashboards.find(d => d.id !== currentDashboard);

  return (
    <div className="relative">
      {/* Current Dashboard Button - Mobile Optimized */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-3 bg-gray-700 hover:bg-gray-600 rounded-lg px-4 py-2 transition-colors w-full min-h-[44px] touch-manipulation dashboard-switcher-button"
        style={{ WebkitTapHighlightColor: 'transparent' }}
      >
        <span className="text-2xl">{currentDashboardInfo?.icon}</span>
        <div className="text-left">
          <div className="text-white font-medium text-sm">
            {currentDashboardInfo?.name}
          </div>
          <div className="text-gray-400 text-xs">
            Click to switch
          </div>
        </div>
        <svg 
          className={`w-4 h-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown Content - Mobile Responsive */}
          <div className="absolute top-full left-0 mt-2 w-80 max-w-[calc(100vw-2rem)] bg-gray-800 rounded-xl border border-gray-700 shadow-2xl z-20 dashboard-switcher-dropdown">
            <div className="p-4">
              <h3 className="text-white font-semibold mb-3">Switch Dashboard</h3>
              
              {/* Current Dashboard */}
              <div className="mb-4">
                <div className={`bg-gradient-to-r ${currentDashboardInfo?.color} rounded-lg p-4 border-2 border-yellow-400`}>
                  <div className="flex items-center gap-3 mb-2">
                    <span className="text-2xl">{currentDashboardInfo?.icon}</span>
                    <div>
                      <div className="text-white font-semibold">
                        {currentDashboardInfo?.name}
                      </div>
                      <div className="text-white/80 text-sm">
                        Currently Active
                      </div>
                    </div>
                  </div>
                  <p className="text-white/90 text-sm mb-3">
                    {currentDashboardInfo?.description}
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {currentDashboardInfo?.features.map((feature, index) => (
                      <span 
                        key={index}
                        className="bg-white/20 text-white text-xs px-2 py-1 rounded"
                      >
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              {/* Other Dashboard */}
              <div className="mb-4">
                <button
                  onClick={() => {
                    onSwitch(otherDashboard!.id);
                    setIsOpen(false);
                  }}
                  className={`w-full bg-gradient-to-r ${otherDashboard?.color} hover:scale-105 rounded-lg p-4 transition-all duration-200 group min-h-[44px] touch-manipulation`}
                  style={{ WebkitTapHighlightColor: 'transparent' }}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <span className="text-2xl">{otherDashboard?.icon}</span>
                    <div className="text-left">
                      <div className="text-white font-semibold">
                        {otherDashboard?.name}
                      </div>
                      <div className="text-white/80 text-sm">
                        Click to switch
                      </div>
                    </div>
                    <svg 
                      className="w-5 h-5 text-white/60 group-hover:text-white transition-colors ml-auto"
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                  <p className="text-white/90 text-sm mb-3 text-left">
                    {otherDashboard?.description}
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {otherDashboard?.features.map((feature, index) => (
                      <span 
                        key={index}
                        className="bg-white/20 text-white text-xs px-2 py-1 rounded"
                      >
                        {feature}
                      </span>
                    ))}
                  </div>
                </button>
              </div>

              {/* User Info */}
              <div className="border-t border-gray-700 pt-3">
                <div className="flex items-center gap-2 text-gray-400 text-sm">
                  <div className="w-6 h-6 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center">
                    <span className="text-xs font-bold text-gray-900">
                      {(user?.database_user?.full_name || user?.email)?.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <span>
                    {user?.database_user?.full_name || user?.email?.split('@')[0]}
                  </span>
                </div>
                <p className="text-gray-500 text-xs mt-1">
                  Same account, different perspectives
                </p>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};
