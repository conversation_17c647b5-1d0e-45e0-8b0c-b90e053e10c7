#!/usr/bin/env node

/**
 * GMAIL DELIVERY FIX VERIFICATION
 * 
 * This script tests if the Gmail → @aureus.africa delivery issue is fixed
 * by checking MX records and SMTP connectivity.
 */

import dns from 'dns';
import { promisify } from 'util';
import net from 'net';

const resolveMx = promisify(dns.resolveMx);
const DOMAIN = 'aureus.africa';

async function testSmtpConnection(host, port = 25) {
  return new Promise((resolve) => {
    const socket = new net.Socket();
    const timeout = setTimeout(() => {
      socket.destroy();
      resolve({ success: false, error: 'Connection timeout' });
    }, 10000);

    socket.connect(port, host, () => {
      clearTimeout(timeout);
      socket.destroy();
      resolve({ success: true });
    });

    socket.on('error', (error) => {
      clearTimeout(timeout);
      resolve({ success: false, error: error.message });
    });
  });
}

async function verifyGmailDeliveryFix() {
  console.log('🔍 VERIFYING GMAIL DELIVERY FIX');
  console.log('================================\n');
  
  console.log(`📧 Testing incoming email delivery for: ${DOMAIN}\n`);
  
  // Check MX Records
  console.log('1️⃣ CHECKING UPDATED MX RECORDS');
  console.log('-------------------------------');
  try {
    const mxRecords = await resolveMx(DOMAIN);
    if (mxRecords && mxRecords.length > 0) {
      console.log('✅ MX records found:');
      mxRecords.forEach((record, index) => {
        console.log(`   ${index + 1}. ${record.exchange} (priority: ${record.priority})`);
      });
      
      // Check if MX still points to domain itself
      const selfPointingMx = mxRecords.find(mx => mx.exchange === DOMAIN);
      if (selfPointingMx) {
        console.log('\n❌ MX record still points to domain itself');
        console.log('   This will continue to cause Gmail delivery issues');
        console.log('   You need to update MX records to point to email hosting service');
      } else {
        console.log('\n✅ MX records point to external mail servers');
        console.log('   This should resolve Gmail delivery issues');
      }
      
      // Test SMTP connectivity
      console.log('\n🔌 Testing SMTP connectivity:');
      for (const mx of mxRecords) {
        console.log(`   Testing ${mx.exchange}:25...`);
        const result = await testSmtpConnection(mx.exchange);
        if (result.success) {
          console.log(`   ✅ ${mx.exchange} - SMTP server responding`);
        } else {
          console.log(`   ❌ ${mx.exchange} - SMTP connection failed: ${result.error}`);
        }
      }
    } else {
      console.log('❌ No MX records found');
    }
  } catch (error) {
    console.log('❌ Error checking MX records:', error.message);
  }
  
  console.log('\n2️⃣ DELIVERY STATUS ASSESSMENT');
  console.log('------------------------------');
  
  try {
    const mxRecords = await resolveMx(DOMAIN);
    const selfPointingMx = mxRecords?.find(mx => mx.exchange === DOMAIN);
    const workingMxServers = [];
    
    if (mxRecords) {
      for (const mx of mxRecords) {
        const result = await testSmtpConnection(mx.exchange);
        if (result.success) {
          workingMxServers.push(mx.exchange);
        }
      }
    }
    
    if (selfPointingMx && workingMxServers.length === 0) {
      console.log('❌ GMAIL DELIVERY STILL BROKEN');
      console.log('   • MX record points to domain itself');
      console.log('   • No working mail servers found');
      console.log('   • Gmail will continue to fail delivering emails');
    } else if (workingMxServers.length > 0) {
      console.log('✅ GMAIL DELIVERY SHOULD NOW WORK');
      console.log(`   • Found ${workingMxServers.length} working mail server(s)`);
      console.log('   • Gmail should be able to deliver emails');
      console.log('   • Working servers:', workingMxServers.join(', '));
    } else {
      console.log('⚠️ PARTIAL FIX - NEEDS VERIFICATION');
      console.log('   • MX records updated but servers not responding');
      console.log('   • May need time for DNS propagation');
      console.log('   • Test again in 1-2 hours');
    }
  } catch (error) {
    console.log('❌ Error assessing delivery status:', error.message);
  }
  
  console.log('\n3️⃣ TESTING RECOMMENDATIONS');
  console.log('----------------------------');
  console.log('To verify the fix is working:');
  console.log('');
  console.log('1. 📧 Send test email from Gmail:');
  console.log('   • From: your Gmail account');
  console.log('   • To: <EMAIL>');
  console.log('   • Subject: "Gmail Delivery Test"');
  console.log('');
  console.log('2. ⏰ Wait 5-10 minutes for delivery');
  console.log('');
  console.log('3. ✅ Check if email arrives:');
  console.log('   • If using email forwarding: check forwarded inbox');
  console.log('   • If using email hosting: check <EMAIL> inbox');
  console.log('');
  console.log('4. 🚨 If email bounces back to Gmail:');
  console.log('   • Check bounce message for error details');
  console.log('   • Verify MX records are correctly configured');
  console.log('   • Wait longer for DNS propagation (up to 48 hours)');
  console.log('');
  console.log('5. 🔧 Online testing tools:');
  console.log('   • https://mxtoolbox.com/domain/aureus.africa');
  console.log('   • https://mail-tester.com/');
  console.log('   • https://www.whatsmydns.net/');
}

// Run the verification
verifyGmailDeliveryFix().catch(console.error);
