#!/usr/bin/env node

/**
 * Check Share Purchases
 * 
 * This script examines all share purchases for <PERSON>
 * to understand the data structure.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ Missing SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkSharePurchases() {
  try {
    console.log('🔍 Checking Share Purchases\n');
    
    // Find <PERSON>'s user ID (the one showing 1 share in portfolio)
    const { data: users, error: userError } = await supabase
      .from('users')
      .select('*')
      .or('username.eq.TTTFOUNDER,id.eq.106')
      .order('id');
    
    if (userError) {
      console.error('❌ Error finding users:', userError);
      return;
    }
    
    console.log('👥 Found Users:');
    users.forEach(user => {
      console.log(`   ID: ${user.id}, Username: ${user.username}, Name: ${user.full_name}, Email: ${user.email}`);
    });
    
    // Check share purchases for user ID 106 (the one showing in portfolio)
    const { data: purchases106, error: purchase106Error } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .eq('user_id', 106)
      .order('created_at', { ascending: false });
    
    if (purchase106Error) {
      console.error('❌ Error fetching purchases for user 106:', purchase106Error);
    } else {
      console.log(`\n📈 Share Purchases for User 106 (${purchases106.length} total):`);
      purchases106.forEach((purchase, index) => {
        console.log(`   ${index + 1}. Purchase ID: ${purchase.id}`);
        console.log(`      Shares: ${purchase.shares_purchased}`);
        console.log(`      Amount: $${purchase.total_amount}`);
        console.log(`      Price per share: $${purchase.price_per_share || 'N/A'}`);
        console.log(`      Phase ID: ${purchase.phase_id || 'N/A'}`);
        console.log(`      Status: ${purchase.status}`);
        console.log(`      Created: ${purchase.created_at}`);
        console.log(`      Payment Method: ${purchase.payment_method || 'N/A'}`);
        console.log('');
      });
    }
    
    // Check share purchases for user ID 4 (TTTFOUNDER)
    const { data: purchases4, error: purchase4Error } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .eq('user_id', 4)
      .order('created_at', { ascending: false});
    
    if (purchase4Error) {
      console.error('❌ Error fetching purchases for user 4:', purchase4Error);
    } else {
      console.log(`📈 Share Purchases for User 4 - TTTFOUNDER (${purchases4.length} total):`);
      purchases4.forEach((purchase, index) => {
        console.log(`   ${index + 1}. Purchase ID: ${purchase.id}`);
        console.log(`      Shares: ${purchase.shares_purchased}`);
        console.log(`      Amount: $${purchase.total_amount}`);
        console.log(`      Price per share: $${purchase.price_per_share || 'N/A'}`);
        console.log(`      Phase ID: ${purchase.phase_id || 'N/A'}`);
        console.log(`      Status: ${purchase.status}`);
        console.log(`      Created: ${purchase.created_at}`);
        console.log(`      Payment Method: ${purchase.payment_method || 'N/A'}`);
        console.log('');
      });
    }
    
    // Check referral relationships for user 106
    const { data: referrals106, error: ref106Error } = await supabase
      .from('referrals')
      .select(`
        *,
        referrer:users!referrer_id(id, username, full_name)
      `)
      .eq('referred_id', 106);
    
    if (ref106Error) {
      console.error('❌ Error checking referrals for user 106:', ref106Error);
    } else {
      console.log(`🔗 Referral Relationships for User 106 (${referrals106.length} total):`);
      referrals106.forEach(ref => {
        console.log(`   Referrer: ${ref.referrer?.full_name || ref.referrer?.username} (ID: ${ref.referrer_id})`);
        console.log(`   Status: ${ref.status}, Rate: ${ref.commission_rate}%`);
        console.log(`   Created: ${ref.created_at}`);
        console.log('');
      });
    }
    
    // Check commission transactions for user 106
    const { data: commissions106, error: comm106Error } = await supabase
      .from('commission_transactions')
      .select(`
        *,
        referrer:users!referrer_id(id, username, full_name)
      `)
      .eq('referred_id', 106)
      .order('payment_date', { ascending: false });
    
    if (comm106Error) {
      console.error('❌ Error checking commissions for user 106:', comm106Error);
    } else {
      console.log(`💸 Commission Transactions for User 106 (${commissions106.length} total):`);
      commissions106.forEach(comm => {
        console.log(`   To: ${comm.referrer?.full_name || comm.referrer?.username} (ID: ${comm.referrer_id})`);
        console.log(`   USDT: $${comm.usdt_commission}, Shares: ${comm.share_commission}`);
        console.log(`   Purchase Amount: $${comm.share_purchase_amount}`);
        console.log(`   Status: ${comm.status}`);
        console.log(`   Date: ${comm.payment_date}`);
        console.log('');
      });
    }
    
  } catch (error) {
    console.error('❌ Check failed:', error);
  }
}

checkSharePurchases();
