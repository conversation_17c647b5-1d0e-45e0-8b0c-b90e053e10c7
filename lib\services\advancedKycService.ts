import { supabase, getServiceRoleClient } from '../supabase';
import { advancedFacialRecognition, BiometricTemplate, LivenessDetectionResult } from '../advancedFacialRecognition';

export interface AdvancedKYCVerification {
  id: string;
  user_id: number;
  verification_status: 'pending' | 'completed' | 'rejected' | 'expired';
  confidence_score: number;
  biometric_template_id?: string;
  
  // Biometric Analysis Results
  facial_landmarks_detected: number;
  landmark_quality_score: number;
  geometric_consistency_score: number;
  
  // Liveness Detection Results
  liveness_checks_passed: number;
  liveness_overall_score: number;
  blink_detection_passed: boolean;
  head_movement_passed: boolean;
  smile_detection_passed: boolean;
  eye_gaze_tracking_passed: boolean;
  mouth_movement_passed: boolean;
  
  // Security Analysis
  anti_spoofing_passed: boolean;
  spoofing_type_detected?: string;
  depth_analysis_passed: boolean;
  texture_analysis_passed: boolean;
  risk_score: number;
  
  // Technical Metadata
  processing_time_ms: number;
  verification_photo_url: string;
  session_id: string;
  device_info: any;
  
  // Timestamps
  created_at: string;
  updated_at: string;
  expires_at: string;
}

export interface BiometricVerificationAttempt {
  id: string;
  user_id: number;
  template_id?: string;
  verification_type: 'enrollment' | 'authentication' | 'kyc_verification' | 'login_verification';
  verification_result: 'success' | 'failed' | 'suspicious' | 'blocked';
  similarity_score: number;
  confidence_score: number;
  liveness_score: number;
  quality_score: number;
  spoofing_detected: boolean;
  spoofing_type?: string;
  risk_score: number;
  processing_time_ms: number;
  landmarks_detected_count: number;
  session_id: string;
  attempted_at: string;
}

class AdvancedKYCService {
  private readonly serviceClient = getServiceRoleClient();
  
  /**
   * Process advanced KYC verification with biometric analysis
   */
  async processAdvancedKYCVerification(
    userId: number,
    verificationData: {
      confidence_score: number;
      verification_photo_url: string;
      biometric_template_id: string;
      liveness_checks: any;
      security_analysis: any;
      landmark_analysis: any;
      metadata: any;
    }
  ): Promise<AdvancedKYCVerification> {
    try {
      console.log(`🔐 Processing advanced KYC verification for user ${userId}...`);
      
      // Calculate verification status based on scores
      const verificationStatus = this.calculateVerificationStatus(
        verificationData.confidence_score,
        verificationData.security_analysis,
        verificationData.landmark_analysis
      );
      
      // Calculate risk score
      const riskScore = this.calculateRiskScore(verificationData);
      
      // Set expiration (2 years for biometric data)
      const expiresAt = new Date();
      expiresAt.setFullYear(expiresAt.getFullYear() + 2);
      
      // Create KYC verification record
      const kycData = {
        user_id: userId,
        verification_status: verificationStatus,
        confidence_score: verificationData.confidence_score,
        biometric_template_id: verificationData.biometric_template_id,
        
        // Biometric Analysis
        facial_landmarks_detected: verificationData.landmark_analysis.landmarks_detected,
        landmark_quality_score: verificationData.landmark_analysis.landmark_quality,
        geometric_consistency_score: verificationData.landmark_analysis.geometric_consistency,
        
        // Liveness Detection
        liveness_checks_passed: this.countPassedLivenessChecks(verificationData.liveness_checks),
        liveness_overall_score: this.calculateLivenessScore(verificationData.liveness_checks),
        blink_detection_passed: verificationData.liveness_checks.blink_detection,
        head_movement_passed: verificationData.liveness_checks.head_movement,
        smile_detection_passed: verificationData.liveness_checks.smile_detection,
        eye_gaze_tracking_passed: verificationData.liveness_checks.eye_gaze_tracking,
        mouth_movement_passed: verificationData.liveness_checks.mouth_movement,
        
        // Security Analysis
        anti_spoofing_passed: !verificationData.security_analysis.spoofing_detected,
        spoofing_type_detected: verificationData.security_analysis.spoofing_type,
        depth_analysis_passed: verificationData.security_analysis.depth_analysis_passed,
        texture_analysis_passed: verificationData.security_analysis.texture_analysis_passed,
        risk_score: riskScore,
        
        // Technical Metadata
        processing_time_ms: verificationData.metadata.processing_time_ms,
        verification_photo_url: verificationData.verification_photo_url,
        session_id: verificationData.metadata.session_id,
        device_info: verificationData.metadata.device_info,
        
        // Timestamps
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        expires_at: expiresAt.toISOString()
      };
      
      // Store in advanced_kyc_verifications table (create if needed)
      const { data, error } = await this.serviceClient
        .from('advanced_kyc_verifications')
        .upsert(kycData, { onConflict: 'user_id' })
        .select()
        .single();
      
      if (error) {
        // If table doesn't exist, create it first
        if (error.code === '42P01') {
          await this.createAdvancedKYCTable();
          // Retry the insert
          const { data: retryData, error: retryError } = await this.serviceClient
            .from('advanced_kyc_verifications')
            .upsert(kycData, { onConflict: 'user_id' })
            .select()
            .single();
          
          if (retryError) throw retryError;
          return retryData;
        }
        throw error;
      }
      
      // Log verification attempt
      await this.logVerificationAttempt(userId, verificationData, 'success');
      
      // Update user's KYC status if verification passed
      if (verificationStatus === 'completed') {
        await this.updateUserKYCStatus(userId, true);
      }
      
      console.log(`✅ Advanced KYC verification processed for user ${userId}: ${verificationStatus}`);
      return data;
      
    } catch (error) {
      console.error('❌ Advanced KYC verification processing error:', error);
      
      // Log failed attempt
      await this.logVerificationAttempt(userId, verificationData, 'failed');
      
      throw new Error(`Advanced KYC verification failed: ${error.message}`);
    }
  }
  
  /**
   * Verify user against stored biometric template
   */
  async verifyUserBiometrics(
    userId: number,
    faceImage: HTMLCanvasElement,
    sessionId: string
  ): Promise<{
    isMatch: boolean;
    confidence: number;
    similarity: number;
    riskScore: number;
    verificationId: string;
  }> {
    try {
      console.log(`🔍 Verifying user ${userId} against stored biometric template...`);
      
      const startTime = Date.now();
      
      // Get stored biometric template
      const { data: templateData, error: templateError } = await this.serviceClient
        .from('biometric_templates')
        .select('*')
        .eq('user_id', userId)
        .eq('template_status', 'active')
        .order('created_at', { ascending: false })
        .limit(1)
        .single();
      
      if (templateError || !templateData) {
        throw new Error('No active biometric template found for user');
      }
      
      // Decrypt and reconstruct biometric template
      const storedTemplate = await this.reconstructBiometricTemplate(templateData);
      
      // Perform advanced face matching
      const matchResult = await advancedFacialRecognition.performAdvancedFaceMatching(
        faceImage,
        storedTemplate
      );
      
      // Calculate risk score
      const riskScore = this.calculateMatchRiskScore(matchResult);
      
      const processingTime = Date.now() - startTime;
      
      // Log verification attempt
      const verificationAttempt = await this.logBiometricVerificationAttempt({
        user_id: userId,
        template_id: templateData.id,
        verification_type: 'authentication',
        verification_result: matchResult.isMatch ? 'success' : 'failed',
        similarity_score: matchResult.similarity,
        confidence_score: matchResult.confidence,
        liveness_score: 0, // Not applicable for static verification
        quality_score: 0.9, // Assume good quality
        spoofing_detected: false,
        risk_score: riskScore,
        processing_time_ms: processingTime,
        landmarks_detected_count: matchResult.matchingPoints,
        session_id: sessionId
      });
      
      console.log(`✅ Biometric verification completed: ${matchResult.isMatch ? 'MATCH' : 'NO MATCH'} (${matchResult.confidence.toFixed(3)})`);
      
      return {
        isMatch: matchResult.isMatch,
        confidence: matchResult.confidence,
        similarity: matchResult.similarity,
        riskScore,
        verificationId: verificationAttempt.id
      };
      
    } catch (error) {
      console.error('❌ Biometric verification error:', error);
      throw new Error(`Biometric verification failed: ${error.message}`);
    }
  }
  
  /**
   * Get user's KYC verification status
   */
  async getUserKYCStatus(userId: number): Promise<AdvancedKYCVerification | null> {
    try {
      const { data, error } = await this.serviceClient
        .from('advanced_kyc_verifications')
        .select('*')
        .eq('user_id', userId)
        .single();
      
      if (error && error.code !== 'PGRST116') {
        throw error;
      }
      
      return data;
      
    } catch (error) {
      console.error('❌ Error getting KYC status:', error);
      return null;
    }
  }
  
  /**
   * Get user's verification attempts history
   */
  async getUserVerificationHistory(
    userId: number,
    limit: number = 10
  ): Promise<BiometricVerificationAttempt[]> {
    try {
      const { data, error } = await this.serviceClient
        .from('biometric_verification_attempts')
        .select('*')
        .eq('user_id', userId)
        .order('attempted_at', { ascending: false })
        .limit(limit);
      
      if (error) throw error;
      
      return data || [];
      
    } catch (error) {
      console.error('❌ Error getting verification history:', error);
      return [];
    }
  }
  
  /**
   * Calculate verification status based on scores and analysis
   */
  private calculateVerificationStatus(
    confidenceScore: number,
    securityAnalysis: any,
    landmarkAnalysis: any
  ): 'pending' | 'completed' | 'rejected' | 'expired' {
    // High-security thresholds for bank-level verification
    const minConfidence = 0.90;
    const minLandmarkQuality = 0.85;
    const minGeometricConsistency = 0.88;

    // Check if verification meets all criteria
    if (
      confidenceScore >= minConfidence &&
      landmarkAnalysis.landmark_quality >= minLandmarkQuality &&
      landmarkAnalysis.geometric_consistency >= minGeometricConsistency &&
      !securityAnalysis.spoofing_detected &&
      securityAnalysis.depth_analysis_passed &&
      securityAnalysis.texture_analysis_passed
    ) {
      return 'completed';
    }

    // Check if verification is clearly rejected
    if (
      confidenceScore < 0.70 ||
      securityAnalysis.spoofing_detected ||
      !securityAnalysis.depth_analysis_passed
    ) {
      return 'rejected';
    }

    // Otherwise, mark as pending for manual review
    return 'pending';
  }

  /**
   * Calculate risk score based on verification data
   */
  private calculateRiskScore(verificationData: any): number {
    let riskScore = 0;

    // Confidence score risk (lower confidence = higher risk)
    riskScore += (1 - verificationData.confidence_score) * 0.3;

    // Security analysis risk
    if (verificationData.security_analysis.spoofing_detected) {
      riskScore += 0.4;
    }
    if (!verificationData.security_analysis.depth_analysis_passed) {
      riskScore += 0.2;
    }
    if (!verificationData.security_analysis.texture_analysis_passed) {
      riskScore += 0.1;
    }

    // Liveness checks risk
    const livenessChecks = verificationData.liveness_checks;
    const failedChecks = Object.values(livenessChecks).filter(check => !check).length;
    riskScore += (failedChecks / Object.keys(livenessChecks).length) * 0.2;

    // Landmark quality risk
    riskScore += (1 - verificationData.landmark_analysis.landmark_quality) * 0.1;

    return Math.max(0, Math.min(1, riskScore));
  }

  /**
   * Count passed liveness checks
   */
  private countPassedLivenessChecks(livenessChecks: any): number {
    return Object.values(livenessChecks).filter(check => check === true).length;
  }

  /**
   * Calculate overall liveness score
   */
  private calculateLivenessScore(livenessChecks: any): number {
    const totalChecks = Object.keys(livenessChecks).length;
    const passedChecks = this.countPassedLivenessChecks(livenessChecks);
    return totalChecks > 0 ? passedChecks / totalChecks : 0;
  }

  /**
   * Calculate risk score for biometric matching
   */
  private calculateMatchRiskScore(matchResult: any): number {
    let riskScore = 0;

    // Similarity risk (lower similarity = higher risk)
    riskScore += (1 - matchResult.similarity) * 0.4;

    // Confidence risk
    riskScore += (1 - matchResult.confidence) * 0.3;

    // Geometric similarity risk
    riskScore += (1 - matchResult.geometricSimilarity) * 0.2;

    // Landmark matching risk
    const landmarkMatchRatio = matchResult.matchingPoints / matchResult.totalPoints;
    riskScore += (1 - landmarkMatchRatio) * 0.1;

    return Math.max(0, Math.min(1, riskScore));
  }

  /**
   * Log verification attempt
   */
  private async logVerificationAttempt(
    userId: number,
    verificationData: any,
    result: 'success' | 'failed'
  ): Promise<void> {
    try {
      await this.serviceClient
        .from('biometric_verification_attempts')
        .insert({
          user_id: userId,
          verification_type: 'kyc_verification',
          verification_result: result,
          confidence_score: verificationData.confidence_score,
          liveness_score: this.calculateLivenessScore(verificationData.liveness_checks),
          quality_score: verificationData.landmark_analysis.landmark_quality,
          spoofing_detected: verificationData.security_analysis.spoofing_detected,
          spoofing_type: verificationData.security_analysis.spoofing_type,
          risk_score: this.calculateRiskScore(verificationData),
          processing_time_ms: verificationData.metadata.processing_time_ms,
          landmarks_detected_count: verificationData.landmark_analysis.landmarks_detected,
          session_id: verificationData.metadata.session_id,
          device_info: verificationData.metadata.device_info,
          user_agent: verificationData.metadata.device_info
        });
    } catch (error) {
      console.error('❌ Failed to log verification attempt:', error);
    }
  }

  /**
   * Log biometric verification attempt
   */
  private async logBiometricVerificationAttempt(
    attemptData: Omit<BiometricVerificationAttempt, 'id' | 'attempted_at'>
  ): Promise<BiometricVerificationAttempt> {
    const { data, error } = await this.serviceClient
      .from('biometric_verification_attempts')
      .insert({
        ...attemptData,
        attempted_at: new Date().toISOString(),
        processed_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  /**
   * Update user's KYC status
   */
  private async updateUserKYCStatus(userId: number, verified: boolean): Promise<void> {
    try {
      await this.serviceClient
        .from('users')
        .update({
          is_verified: verified,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);
    } catch (error) {
      console.error('❌ Failed to update user KYC status:', error);
    }
  }

  /**
   * Reconstruct biometric template from encrypted storage
   */
  private async reconstructBiometricTemplate(templateData: any): Promise<BiometricTemplate> {
    // Decrypt the stored data (simplified for demo)
    const decryptedLandmarks = JSON.parse(atob(templateData.facial_landmarks_encrypted));
    const decryptedGeometry = JSON.parse(atob(templateData.template_data_encrypted));

    return {
      userId: templateData.user_id,
      templateId: templateData.id,
      facialLandmarks: decryptedLandmarks,
      geometryVector: decryptedGeometry,
      textureFeatures: [], // Would be stored separately
      depthFeatures: [], // Would be stored separately
      confidence: templateData.confidence,
      qualityScore: templateData.quality_score,
      createdAt: new Date(templateData.created_at),
      metadata: {
        imageSize: { width: 0, height: 0 }, // Would be stored in metadata
        lightingConditions: 'unknown',
        faceAngle: { pitch: 0, yaw: 0, roll: 0 },
        extractionMethod: templateData.extraction_method,
        version: templateData.template_version
      }
    };
  }

  /**
   * Create advanced KYC table if it doesn't exist
   */
  private async createAdvancedKYCTable(): Promise<void> {
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS advanced_kyc_verifications (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        verification_status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (verification_status IN ('pending', 'completed', 'rejected', 'expired')),
        confidence_score DECIMAL(5,4) DEFAULT 0.0000,
        biometric_template_id VARCHAR(100),

        -- Biometric Analysis
        facial_landmarks_detected INTEGER DEFAULT 0,
        landmark_quality_score DECIMAL(5,4) DEFAULT 0.0000,
        geometric_consistency_score DECIMAL(5,4) DEFAULT 0.0000,

        -- Liveness Detection
        liveness_checks_passed INTEGER DEFAULT 0,
        liveness_overall_score DECIMAL(5,4) DEFAULT 0.0000,
        blink_detection_passed BOOLEAN DEFAULT FALSE,
        head_movement_passed BOOLEAN DEFAULT FALSE,
        smile_detection_passed BOOLEAN DEFAULT FALSE,
        eye_gaze_tracking_passed BOOLEAN DEFAULT FALSE,
        mouth_movement_passed BOOLEAN DEFAULT FALSE,

        -- Security Analysis
        anti_spoofing_passed BOOLEAN DEFAULT FALSE,
        spoofing_type_detected VARCHAR(50),
        depth_analysis_passed BOOLEAN DEFAULT FALSE,
        texture_analysis_passed BOOLEAN DEFAULT FALSE,
        risk_score DECIMAL(5,4) DEFAULT 0.0000,

        -- Technical Metadata
        processing_time_ms INTEGER,
        verification_photo_url TEXT,
        session_id VARCHAR(100),
        device_info JSONB DEFAULT '{}'::jsonb,

        -- Timestamps
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        expires_at TIMESTAMP WITH TIME ZONE,

        UNIQUE(user_id)
      );

      CREATE INDEX IF NOT EXISTS idx_advanced_kyc_user_id ON advanced_kyc_verifications(user_id);
      CREATE INDEX IF NOT EXISTS idx_advanced_kyc_status ON advanced_kyc_verifications(verification_status);
      CREATE INDEX IF NOT EXISTS idx_advanced_kyc_created_at ON advanced_kyc_verifications(created_at);
    `;

    // Execute the SQL using a direct query
    const { error } = await this.serviceClient.rpc('exec_sql', { sql_query: createTableSQL });
    if (error) {
      console.error('❌ Failed to create advanced KYC table:', error);
    }
  }
}

// Create singleton instance
export const advancedKycService = new AdvancedKYCService();

export default advancedKycService;
