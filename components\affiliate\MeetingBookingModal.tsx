import React, { useState } from 'react';

interface Meeting {
  id: number;
  title: string;
  topic: string;
  description?: string;
  date: string;
  time: string;
  timezone: string;
  duration: number;
  maxAttendees: number;
  currentAttendees: number;
  availableSpots: number;
  isFullyBooked: boolean;
  status: string;
}

interface MeetingBookingModalProps {
  meeting: Meeting | null;
  isOpen: boolean;
  onClose: () => void;
  onBookingSuccess: (confirmationCode: string) => void;
}

const MeetingBookingModal: React.FC<MeetingBookingModalProps> = ({
  meeting,
  isOpen,
  onClose,
  onBookingSuccess
}) => {
  const [formData, setFormData] = useState({
    attendeeName: '',
    attendeeEmail: '',
    phoneNumber: '',
    notes: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.attendeeName.trim()) {
      errors.attendeeName = 'Name is required';
    } else if (formData.attendeeName.trim().length < 2) {
      errors.attendeeName = 'Name must be at least 2 characters';
    }

    if (!formData.attendeeEmail.trim()) {
      errors.attendeeEmail = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.attendeeEmail)) {
      errors.attendeeEmail = 'Please enter a valid email address';
    }

    if (formData.phoneNumber.trim() && !/^[\+]?[1-9][\d\s\-\(\)]{7,15}$/.test(formData.phoneNumber.replace(/[\s\-\(\)]/g, ''))) {
      errors.phoneNumber = 'Please enter a valid phone number';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!meeting || !validateForm()) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log('📝 Booking meeting:', meeting.id);

      const response = await fetch('/api/meetings/book', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          meetingId: meeting.id,
          attendeeName: formData.attendeeName.trim(),
          attendeeEmail: formData.attendeeEmail.trim().toLowerCase(),
          phoneNumber: formData.phoneNumber.trim() || null,
          notes: formData.notes.trim() || null
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to book meeting');
      }

      const data = await response.json();
      console.log('✅ Booking successful:', data.booking.confirmationCode);

      // Reset form
      setFormData({
        attendeeName: '',
        attendeeEmail: '',
        phoneNumber: '',
        notes: ''
      });

      onBookingSuccess(data.booking.confirmationCode);
    } catch (err) {
      console.error('❌ Booking error:', err);
      setError(err instanceof Error ? err.message : 'Failed to book meeting');
    } finally {
      setLoading(false);
    }
  };

  const formatDateTime = (date: string, time: string, timezone: string) => {
    try {
      const dateTime = new Date(`${date}T${time}`);
      return {
        date: dateTime.toLocaleDateString('en-US', { 
          weekday: 'long',
          year: 'numeric', 
          month: 'long', 
          day: 'numeric' 
        }),
        time: dateTime.toLocaleTimeString('en-US', { 
          hour: '2-digit', 
          minute: '2-digit',
          hour12: true
        }),
        timezone: timezone || 'SAST'
      };
    } catch (error) {
      return {
        date: date,
        time: time,
        timezone: timezone || 'SAST'
      };
    }
  };

  if (!isOpen || !meeting) {
    return null;
  }

  const dateTime = formatDateTime(meeting.date, meeting.time, meeting.timezone);

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content meeting-booking-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>Book Your Spot</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <div className="meeting-booking-content">
          {/* Meeting Details */}
          <div className="meeting-details-summary">
            <h3 style={{ color: '#FFD700', marginBottom: '10px' }}>{meeting.title}</h3>
            <p style={{ color: '#ccc', marginBottom: '15px' }}>{meeting.topic}</p>
            
            <div className="meeting-info-grid">
              <div className="info-item">
                <span className="info-label">📅 Date:</span>
                <span className="info-value">{dateTime.date}</span>
              </div>
              <div className="info-item">
                <span className="info-label">⏰ Time:</span>
                <span className="info-value">{dateTime.time} {dateTime.timezone}</span>
              </div>
              <div className="info-item">
                <span className="info-label">⏱️ Duration:</span>
                <span className="info-value">{meeting.duration} minutes</span>
              </div>
              <div className="info-item">
                <span className="info-label">👥 Available:</span>
                <span className="info-value" style={{ color: '#4CAF50' }}>
                  {meeting.availableSpots} of {meeting.maxAttendees} spots
                </span>
              </div>
            </div>
          </div>

          {error && (
            <div className="error-message" style={{
              backgroundColor: '#f44336',
              color: '#fff',
              padding: '12px',
              borderRadius: '8px',
              marginBottom: '20px'
            }}>
              {error}
            </div>
          )}

          {/* Booking Form */}
          <form onSubmit={handleSubmit} className="booking-form">
            <div className="form-group">
              <label htmlFor="attendeeName">Full Name *</label>
              <input
                type="text"
                id="attendeeName"
                value={formData.attendeeName}
                onChange={(e) => setFormData({ ...formData, attendeeName: e.target.value })}
                className={validationErrors.attendeeName ? 'error' : ''}
                placeholder="Enter your full name"
                disabled={loading}
              />
              {validationErrors.attendeeName && (
                <span className="error-text">{validationErrors.attendeeName}</span>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="attendeeEmail">Email Address *</label>
              <input
                type="email"
                id="attendeeEmail"
                value={formData.attendeeEmail}
                onChange={(e) => setFormData({ ...formData, attendeeEmail: e.target.value })}
                className={validationErrors.attendeeEmail ? 'error' : ''}
                placeholder="Enter your email address"
                disabled={loading}
              />
              {validationErrors.attendeeEmail && (
                <span className="error-text">{validationErrors.attendeeEmail}</span>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="phoneNumber">Phone Number (Optional)</label>
              <input
                type="tel"
                id="phoneNumber"
                value={formData.phoneNumber}
                onChange={(e) => setFormData({ ...formData, phoneNumber: e.target.value })}
                className={validationErrors.phoneNumber ? 'error' : ''}
                placeholder="+27 12 345 6789"
                disabled={loading}
              />
              {validationErrors.phoneNumber && (
                <span className="error-text">{validationErrors.phoneNumber}</span>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="notes">Additional Notes (Optional)</label>
              <textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                placeholder="Any questions or special requirements?"
                rows={3}
                disabled={loading}
              />
            </div>

            <div className="booking-actions">
              <button
                type="button"
                onClick={onClose}
                className="cancel-btn"
                disabled={loading}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="book-btn"
                disabled={loading || meeting.isFullyBooked}
              >
                {loading ? 'Booking...' : 'Confirm Booking'}
              </button>
            </div>
          </form>

          <div className="booking-disclaimer">
            <p style={{ fontSize: '12px', color: '#aaa', textAlign: 'center', marginTop: '20px' }}>
              By booking this meeting, you agree to attend at the scheduled time. 
              You will receive a confirmation email with meeting details.
            </p>
          </div>
        </div>
      </div>

      <style jsx>{`
        .meeting-booking-modal {
          max-width: 600px;
          width: 90%;
        }

        .meeting-details-summary {
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          padding: 20px;
          margin-bottom: 25px;
        }

        .meeting-info-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 15px;
          margin-top: 15px;
        }

        .info-item {
          display: flex;
          flex-direction: column;
          gap: 5px;
        }

        .info-label {
          font-size: 12px;
          color: #aaa;
          font-weight: 500;
        }

        .info-value {
          font-size: 14px;
          color: #fff;
          font-weight: 600;
        }

        .booking-form {
          display: flex;
          flex-direction: column;
          gap: 20px;
        }

        .form-group {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .form-group label {
          font-size: 14px;
          color: #ccc;
          font-weight: 500;
        }

        .form-group input,
        .form-group textarea {
          padding: 12px;
          background: #121212;
          border: 1px solid #333;
          border-radius: 8px;
          color: #fff;
          font-size: 14px;
        }

        .form-group input:focus,
        .form-group textarea:focus {
          outline: none;
          border-color: #FFD700;
          box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.2);
        }

        .form-group input.error,
        .form-group textarea.error {
          border-color: #f44336;
        }

        .error-text {
          font-size: 12px;
          color: #f44336;
        }

        .booking-actions {
          display: flex;
          gap: 15px;
          justify-content: flex-end;
          margin-top: 30px;
        }

        .cancel-btn {
          padding: 12px 24px;
          background: #666;
          color: #fff;
          border: none;
          border-radius: 8px;
          cursor: pointer;
          font-weight: 500;
        }

        .book-btn {
          padding: 12px 24px;
          background: #FFD700;
          color: #000;
          border: none;
          border-radius: 8px;
          cursor: pointer;
          font-weight: 600;
        }

        .book-btn:disabled {
          background: #666;
          color: #ccc;
          cursor: not-allowed;
        }

        @media (max-width: 768px) {
          .meeting-info-grid {
            grid-template-columns: 1fr;
            gap: 10px;
          }

          .booking-actions {
            flex-direction: column;
          }
        }
      `}</style>
    </div>
  );
};

export default MeetingBookingModal;
