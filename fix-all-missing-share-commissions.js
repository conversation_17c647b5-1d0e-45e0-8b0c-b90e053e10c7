import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixAllMissingShareCommissions() {
  console.log('🔧 FIXING ALL MISSING SHARE COMMISSIONS SYSTEMWIDE\n');

  // Get ALL commission transactions that are missing share commissions
  const { data: commissionsWithoutShares, error: commissionError } = await supabase
    .from('commission_transactions')
    .select(`
      *,
      referrer:users!referrer_id(id, username, full_name),
      referred:users!referred_id(id, username, full_name)
    `)
    .eq('share_commission', 0)
    .gt('share_purchase_amount', 0)
    .order('created_at', { ascending: false });

  if (commissionError) {
    console.log('❌ Error getting commissions without shares:', commissionError.message);
    return;
  }

  console.log(`📋 Found ${commissionsWithoutShares.length} commission transactions missing share commissions\n`);

  let totalUsersAffected = new Set();
  let totalSharesAdded = 0;
  let successCount = 0;
  let errorCount = 0;
  let skippedCount = 0;

  const userBalanceUpdates = new Map(); // Track balance updates per user

  console.log('🔍 Processing commission transactions...\n');

  for (let i = 0; i < commissionsWithoutShares.length; i++) {
    const commission = commissionsWithoutShares[i];
    
    console.log(`${i + 1}/${commissionsWithoutShares.length}. Processing Commission ID: ${commission.id}`);
    console.log(`   Referrer: ${commission.referrer?.full_name || commission.referrer?.username} (ID: ${commission.referrer_id})`);
    console.log(`   Referred: ${commission.referred?.full_name || commission.referred?.username} (ID: ${commission.referred_id})`);
    console.log(`   Purchase Amount: $${commission.share_purchase_amount}`);
    
    // Get the actual purchase to find shares purchased
    const { data: purchase, error: purchaseError } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .eq('user_id', commission.referred_id)
      .eq('total_amount', commission.share_purchase_amount)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (purchaseError || !purchase) {
      console.log(`   ⚠️ Could not find matching purchase - skipping`);
      skippedCount++;
      continue;
    }

    const sharesPurchased = purchase.shares_purchased || 0;
    const expectedShareCommission = sharesPurchased * 0.15;
    
    console.log(`   Shares Purchased: ${sharesPurchased}`);
    console.log(`   Expected Share Commission: ${expectedShareCommission.toFixed(4)}`);

    if (expectedShareCommission === 0) {
      console.log(`   ⚠️ No shares to commission (shares purchased = 0) - skipping`);
      skippedCount++;
      continue;
    }

    // Update the commission transaction to include share commission
    const { error: updateError } = await supabase
      .from('commission_transactions')
      .update({
        share_commission: expectedShareCommission
      })
      .eq('id', commission.id);

    if (updateError) {
      console.log(`   ❌ Failed to update commission transaction:`, updateError.message);
      errorCount++;
      continue;
    }

    console.log(`   ✅ Updated commission transaction with ${expectedShareCommission.toFixed(4)} shares`);
    
    // Track user balance updates
    const userId = commission.referrer_id;
    if (!userBalanceUpdates.has(userId)) {
      userBalanceUpdates.set(userId, {
        userId,
        username: commission.referrer?.username,
        fullName: commission.referrer?.full_name,
        totalSharesAdded: 0,
        transactionCount: 0
      });
    }
    
    const userUpdate = userBalanceUpdates.get(userId);
    userUpdate.totalSharesAdded += expectedShareCommission;
    userUpdate.transactionCount += 1;
    
    totalUsersAffected.add(userId);
    totalSharesAdded += expectedShareCommission;
    successCount++;

    // Add small delay to avoid overwhelming the database
    if (i % 10 === 0 && i > 0) {
      console.log(`   💤 Processed ${i} transactions, taking brief pause...`);
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  console.log(`\n💰 Updating user commission balances...`);

  let balanceUpdateSuccess = 0;
  let balanceUpdateErrors = 0;

  for (const [userId, userUpdate] of userBalanceUpdates) {
    console.log(`\n   Updating balance for ${userUpdate.fullName || userUpdate.username} (ID: ${userId})`);
    console.log(`   Adding ${userUpdate.totalSharesAdded.toFixed(4)} shares from ${userUpdate.transactionCount} transactions`);

    // Get current balance
    const { data: currentBalance, error: balanceError } = await supabase
      .from('commission_balances')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (balanceError) {
      console.log(`   ❌ Error getting current balance:`, balanceError.message);
      balanceUpdateErrors++;
      continue;
    }

    const newShareBalance = (currentBalance.share_balance || 0) + userUpdate.totalSharesAdded;
    const newTotalEarnedShares = (currentBalance.total_earned_shares || 0) + userUpdate.totalSharesAdded;

    const { error: updateBalanceError } = await supabase
      .from('commission_balances')
      .update({
        share_balance: newShareBalance,
        total_earned_shares: newTotalEarnedShares,
        last_updated: new Date().toISOString()
      })
      .eq('user_id', userId);

    if (updateBalanceError) {
      console.log(`   ❌ Error updating commission balance:`, updateBalanceError.message);
      balanceUpdateErrors++;
    } else {
      console.log(`   ✅ Updated balance: ${(currentBalance.share_balance || 0).toFixed(4)} → ${newShareBalance.toFixed(4)} shares`);
      balanceUpdateSuccess++;
    }
  }

  // Create comprehensive audit log
  const auditDetails = {
    action: 'SYSTEMWIDE_BACKFILL_MISSING_SHARE_COMMISSIONS',
    total_transactions_processed: commissionsWithoutShares.length,
    transactions_updated: successCount,
    transactions_skipped: skippedCount,
    transactions_errors: errorCount,
    total_users_affected: totalUsersAffected.size,
    total_shares_added: totalSharesAdded,
    balance_updates_success: balanceUpdateSuccess,
    balance_updates_errors: balanceUpdateErrors,
    user_updates: Array.from(userBalanceUpdates.values()),
    timestamp: new Date().toISOString()
  };

  const { error: auditError } = await supabase
    .from('admin_audit_logs')
    .insert({
      admin_telegram_id: 0,
      admin_username: 'SYSTEM_BACKFILL_ALL',
      action: 'SYSTEMWIDE_BACKFILL_MISSING_SHARE_COMMISSIONS',
      target_type: 'all_user_commissions',
      target_id: 'systemwide',
      details: JSON.stringify(auditDetails),
      timestamp: new Date().toISOString()
    });

  if (auditError) {
    console.log('⚠️ Could not create audit log:', auditError.message);
  } else {
    console.log('✅ Created comprehensive audit log entry');
  }

  console.log(`\n📊 SYSTEMWIDE BACKFILL SUMMARY:`);
  console.log(`=`.repeat(50));
  console.log(`• Total commission transactions processed: ${commissionsWithoutShares.length}`);
  console.log(`• Successfully updated transactions: ${successCount}`);
  console.log(`• Skipped transactions: ${skippedCount}`);
  console.log(`• Error transactions: ${errorCount}`);
  console.log(`• Total users affected: ${totalUsersAffected.size}`);
  console.log(`• Total shares added: ${totalSharesAdded.toFixed(4)}`);
  console.log(`• User balance updates successful: ${balanceUpdateSuccess}`);
  console.log(`• User balance update errors: ${balanceUpdateErrors}`);

  if (totalUsersAffected.size > 0) {
    console.log(`\n👥 TOP AFFECTED USERS:`);
    const sortedUsers = Array.from(userBalanceUpdates.values())
      .sort((a, b) => b.totalSharesAdded - a.totalSharesAdded)
      .slice(0, 10);
    
    sortedUsers.forEach((user, index) => {
      console.log(`   ${index + 1}. ${user.fullName || user.username} (ID: ${user.userId})`);
      console.log(`      Added: ${user.totalSharesAdded.toFixed(4)} shares from ${user.transactionCount} transactions`);
    });
  }

  console.log('\n✅ Systemwide share commission backfill complete!');
  console.log('\n🔍 RECOMMENDATION: Monitor new purchases to ensure share commissions are calculated correctly going forward.');
}

// Run the systemwide fix
fixAllMissingShareCommissions().catch(console.error);
