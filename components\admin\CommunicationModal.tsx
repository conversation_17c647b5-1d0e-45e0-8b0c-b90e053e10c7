import React, { useState, useEffect } from 'react'
import { supabase } from '../../lib/supabase'
import { logAdminAction } from '../../lib/adminAuth'

interface User {
  id: number
  username: string
  full_name: string | null
  email: string
  telegram_users?: Array<{
    telegram_id: number
    username: string
    first_name: string
    last_name: string
  }>
}

interface Notification {
  id: string
  user_id: number
  title: string
  message: string
  category: string
  is_read: boolean
  created_at: string
  admin_sender?: string
}

interface CommunicationModalProps {
  isOpen: boolean
  onClose: () => void
  user: User
  adminUser?: any
  onUpdate: () => void
}

export const CommunicationModal: React.FC<CommunicationModalProps> = ({
  isOpen,
  onClose,
  user,
  adminUser,
  onUpdate
}) => {
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState<'send' | 'history'>('send')
  const [notifications, setNotifications] = useState<Notification[]>([])
  
  // Send notification states
  const [notificationTitle, setNotificationTitle] = useState('')
  const [notificationMessage, setNotificationMessage] = useState('')
  const [notificationCategory, setNotificationCategory] = useState<'system' | 'payment' | 'commission'>('system')

  useEffect(() => {
    if (isOpen && activeTab === 'history') {
      loadNotificationHistory()
    }
  }, [isOpen, activeTab, user.id])

  if (!isOpen) return null

  const loadNotificationHistory = async () => {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(50)

      if (error) throw error
      setNotifications(data || [])
    } catch (err) {
      console.error('Error loading notification history:', err)
    }
  }

  const handleSendNotification = async () => {
    if (!notificationTitle.trim() || !notificationMessage.trim()) {
      alert('Please fill in both title and message')
      return
    }

    setLoading(true)
    try {
      // Insert notification into database
      const { error } = await supabase
        .from('notifications')
        .insert({
          user_id: user.id,
          title: notificationTitle,
          message: notificationMessage,
          category: notificationCategory,
          is_read: false,
          admin_sender: adminUser?.email || 'admin',
          created_at: new Date().toISOString()
        })

      if (error) throw error

      // Log admin action
      await logAdminAction(
        adminUser?.email || 'unknown',
        'SEND_NOTIFICATION',
        'notifications',
        user.id.toString(),
        {
          title: notificationTitle,
          message: notificationMessage,
          category: notificationCategory,
          recipient: user.username
        }
      )

      alert('Notification sent successfully!')
      setNotificationTitle('')
      setNotificationMessage('')
      setNotificationCategory('system')
      
      // Refresh notification history if on that tab
      if (activeTab === 'history') {
        loadNotificationHistory()
      }
      
      onUpdate()
    } catch (err: any) {
      console.error('Error sending notification:', err)
      alert('Failed to send notification: ' + err.message)
    } finally {
      setLoading(false)
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'payment':
        return 'bg-green-500/20 text-green-400'
      case 'commission':
        return 'bg-blue-500/20 text-blue-400'
      case 'system':
      default:
        return 'bg-gray-500/20 text-gray-400'
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'payment':
        return '💰'
      case 'commission':
        return '🤝'
      case 'system':
      default:
        return '📢'
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-700">
          <div>
            <h2 className="text-2xl font-bold text-white">
              📨 Communication - {user.full_name || user.username}
            </h2>
            <p className="text-gray-400 mt-1">@{user.username} (ID: {user.id})</p>
            {user.telegram_users && user.telegram_users.length > 0 && (
              <p className="text-blue-400 text-sm">
                Telegram: @{user.telegram_users[0].username}
              </p>
            )}
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white text-2xl"
          >
            ×
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-700">
          <button
            onClick={() => setActiveTab('send')}
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === 'send'
                ? 'text-yellow-400 border-b-2 border-yellow-400'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            📤 Send Notification
          </button>
          <button
            onClick={() => setActiveTab('history')}
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === 'history'
                ? 'text-yellow-400 border-b-2 border-yellow-400'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            📋 Message History
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {/* Send Notification Tab */}
          {activeTab === 'send' && (
            <div className="space-y-6">
              <div className="glass-card p-4">
                <h3 className="text-lg font-semibold text-white mb-4">Send New Notification</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Category
                    </label>
                    <select
                      value={notificationCategory}
                      onChange={(e) => setNotificationCategory(e.target.value as any)}
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
                    >
                      <option value="system">📢 System Notification</option>
                      <option value="payment">💰 Payment Related</option>
                      <option value="commission">🤝 Commission Related</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Title *
                    </label>
                    <input
                      type="text"
                      value={notificationTitle}
                      onChange={(e) => setNotificationTitle(e.target.value)}
                      placeholder="Enter notification title..."
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Message *
                    </label>
                    <textarea
                      value={notificationMessage}
                      onChange={(e) => setNotificationMessage(e.target.value)}
                      placeholder="Enter your message..."
                      rows={6}
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
                    />
                  </div>

                  <div className="bg-blue-900/20 border border-blue-500/20 rounded-lg p-4">
                    <h4 className="text-blue-400 font-medium mb-2">📋 Preview</h4>
                    <div className="bg-gray-800/50 rounded-lg p-3">
                      <div className="flex items-center mb-2">
                        <span className="text-lg mr-2">{getCategoryIcon(notificationCategory)}</span>
                        <span className="font-medium text-white">
                          {notificationTitle || 'Notification Title'}
                        </span>
                        <span className={`ml-auto px-2 py-1 text-xs rounded-full ${getCategoryColor(notificationCategory)}`}>
                          {notificationCategory}
                        </span>
                      </div>
                      <p className="text-gray-300 text-sm">
                        {notificationMessage || 'Your message will appear here...'}
                      </p>
                    </div>
                  </div>

                  <button
                    onClick={handleSendNotification}
                    disabled={loading || !notificationTitle.trim() || !notificationMessage.trim()}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg disabled:opacity-50"
                  >
                    {loading ? 'Sending...' : '📤 Send Notification'}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Message History Tab */}
          {activeTab === 'history' && (
            <div className="space-y-6">
              <div className="glass-card p-4">
                <h3 className="text-lg font-semibold text-white mb-4">
                  Message History ({notifications.length})
                </h3>
                
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {notifications.length > 0 ? (
                    notifications.map((notification) => (
                      <div key={notification.id} className="bg-gray-800/50 p-4 rounded-lg">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center">
                            <span className="text-lg mr-2">{getCategoryIcon(notification.category)}</span>
                            <div>
                              <h4 className="font-medium text-white">{notification.title}</h4>
                              <p className="text-xs text-gray-400">
                                {new Date(notification.created_at).toLocaleString()}
                                {notification.admin_sender && ` • by ${notification.admin_sender}`}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className={`px-2 py-1 text-xs rounded-full ${getCategoryColor(notification.category)}`}>
                              {notification.category}
                            </span>
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              notification.is_read 
                                ? 'bg-green-500/20 text-green-400' 
                                : 'bg-yellow-500/20 text-yellow-400'
                            }`}>
                              {notification.is_read ? 'Read' : 'Unread'}
                            </span>
                          </div>
                        </div>
                        <p className="text-gray-300 text-sm">{notification.message}</p>
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-500 text-center py-8">No message history found</p>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
