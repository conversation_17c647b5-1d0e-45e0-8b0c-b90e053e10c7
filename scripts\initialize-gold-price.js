/**
 * CRITICAL GOLD PRICE INITIALIZATION SCRIPT
 * 
 * This script initializes the database with the current market gold price
 * to prevent catastrophic pricing errors when APIs fail.
 * 
 * Usage: node scripts/initialize-gold-price.js [price_per_kg]
 * Example: node scripts/initialize-gold-price.js 120000
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL || '',
  process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || ''
);

const TROY_OUNCES_PER_KG = 32.15;

const DB_KEYS = {
  LAST_KNOWN_PRICE: 'gold_price_last_known_usd_per_kg',
  LAST_KNOWN_PRICE_OUNCE: 'gold_price_last_known_usd_per_ounce',
  LAST_UPDATE_TIMESTAMP: 'gold_price_last_updated',
  LAST_SUCCESSFUL_SOURCE: 'gold_price_last_source'
};

async function initializeGoldPrice(pricePerKg) {
  try {
    console.log('🚀 Initializing gold price in database...');
    
    // Validate price
    if (!pricePerKg || pricePerKg <= 0) {
      throw new Error('Invalid price provided');
    }

    // Calculate price per ounce
    const pricePerOunce = Math.round((pricePerKg / TROY_OUNCES_PER_KG) * 100) / 100;
    const timestamp = new Date().toISOString();

    console.log(`💰 Setting gold price: $${pricePerKg.toLocaleString()}/kg ($${pricePerOunce}/oz)`);

    // Prepare database updates
    const updates = [
      {
        setting_name: DB_KEYS.LAST_KNOWN_PRICE,
        setting_value: pricePerKg.toString(),
        description: 'Last known good gold price in USD per kilogram'
      },
      {
        setting_name: DB_KEYS.LAST_KNOWN_PRICE_OUNCE,
        setting_value: pricePerOunce.toString(),
        description: 'Last known good gold price in USD per troy ounce'
      },
      {
        setting_name: DB_KEYS.LAST_UPDATE_TIMESTAMP,
        setting_value: timestamp,
        description: 'Timestamp of last successful gold price update'
      },
      {
        setting_name: DB_KEYS.LAST_SUCCESSFUL_SOURCE,
        setting_value: 'Manual Admin Initialization',
        description: 'Source of last successful gold price fetch'
      }
    ];

    // Store in database
    for (const update of updates) {
      const { error } = await supabase
        .from('system_settings')
        .upsert(update, { onConflict: 'setting_name' });

      if (error) {
        throw error;
      }
    }

    console.log('✅ Gold price successfully initialized in database');
    console.log(`📊 Price: $${pricePerKg.toLocaleString()}/kg ($${pricePerOunce}/oz)`);
    console.log(`🕒 Timestamp: ${timestamp}`);
    console.log(`📍 Source: Manual Admin Initialization`);

    // Verify the data was stored correctly
    const { data: verification, error: verifyError } = await supabase
      .from('system_settings')
      .select('setting_name, setting_value')
      .in('setting_name', Object.values(DB_KEYS));

    if (verifyError) {
      throw verifyError;
    }

    console.log('\n🔍 Verification - Stored values:');
    verification.forEach(setting => {
      console.log(`  ${setting.setting_name}: ${setting.setting_value}`);
    });

    console.log('\n✅ Gold price initialization completed successfully!');
    console.log('🛡️ Database fallback is now protected against API failures.');

  } catch (error) {
    console.error('🚨 CRITICAL ERROR: Failed to initialize gold price:', error);
    process.exit(1);
  }
}

// Get price from command line argument or use current market estimate
const priceArg = process.argv[2];
const currentMarketPrice = priceArg ? parseFloat(priceArg) : 120000; // Current market estimate

if (isNaN(currentMarketPrice) || currentMarketPrice <= 0) {
  console.error('🚨 ERROR: Invalid price provided. Please provide a valid price in USD per kg.');
  console.log('Usage: node scripts/initialize-gold-price.js [price_per_kg]');
  console.log('Example: node scripts/initialize-gold-price.js 120000');
  process.exit(1);
}

// Confirm the price before proceeding
console.log(`⚠️  IMPORTANT: You are about to set the gold price to $${currentMarketPrice.toLocaleString()}/kg`);
console.log('This will be used as the fallback price when all APIs fail.');
console.log('Please ensure this price is accurate to prevent business losses.');

// Run the initialization
initializeGoldPrice(currentMarketPrice);
