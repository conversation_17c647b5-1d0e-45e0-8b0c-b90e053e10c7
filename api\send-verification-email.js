/**
 * API endpoint for sending verification emails
 * Uses Resend service on the server side for security
 */

import { Resend } from 'resend';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const RESEND_API_KEY = process.env.RESEND_API_KEY || process.env.VITE_RESEND_API_KEY;
const RESEND_FROM_EMAIL = process.env.RESEND_FROM_EMAIL || process.env.VITE_RESEND_FROM_EMAIL || '<EMAIL>';
const RESEND_FROM_NAME = process.env.RESEND_FROM_NAME || process.env.VITE_RESEND_FROM_NAME || 'Aureus Alliance Holdings';

// Initialize Resend client
let resend = null;

try {
  if (RESEND_API_KEY) {
    resend = new Resend(RESEND_API_KEY);
    console.log('✅ Server-side Resend email service initialized');
  } else {
    console.warn('⚠️ RESEND_API_KEY not found - email service disabled');
  }
} catch (error) {
  console.error('❌ Failed to initialize server-side Resend service:', error);
}

/**
 * Generate verification email content
 */
function generateVerificationEmailContent(code, purpose, userName, expiryMinutes = 15) {
  const greeting = userName ? `Hello ${userName}` : 'Hello';
  const purposeText = {
    registration: 'complete your account registration',
    account_update: 'confirm your account changes',
    withdrawal: 'authorize your withdrawal request',
    password_reset: 'reset your password',
    admin_authentication: 'authenticate admin access'
  }[purpose] || 'verify your email';

  // Special handling for admin authentication
  const isAdminAuth = purpose === 'admin_authentication';
  const subject = isAdminAuth
    ? `🔐 Admin Authentication Required - PIN: ${code}`
    : `Your Aureus Alliance verification code: ${code}`;

  const html = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <title>${isAdminAuth ? 'Admin Authentication' : 'Email Verification'}</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #D4AF37;">🏆 Aureus Alliance Holdings</h1>
            ${isAdminAuth ? '<div style="background: #dc2626; color: white; padding: 8px 16px; border-radius: 4px; display: inline-block; font-weight: bold;">🔐 ADMIN PORTAL</div>' : ''}
          </div>

          <h2>${isAdminAuth ? '🔐 Admin Authentication Required' : 'Email Verification Required'}</h2>

          <p>${greeting},</p>

          <p>${isAdminAuth
            ? 'An admin login attempt requires two-factor authentication. Use the PIN below to complete admin access:'
            : `You need to verify your email address to ${purposeText}.`
          }</p>
          
          <div style="background: ${isAdminAuth ? '#fef2f2' : '#f8f9fa'}; padding: 20px; border-radius: 8px; text-align: center; margin: 20px 0; ${isAdminAuth ? 'border: 2px solid #dc2626;' : ''}">
            <h3 style="margin: 0; color: ${isAdminAuth ? '#dc2626' : '#D4AF37'};">
              ${isAdminAuth ? '🔐 Admin Authentication PIN' : 'Your Verification Code'}
            </h3>
            <div style="font-size: 32px; font-weight: bold; letter-spacing: 8px; margin: 15px 0; color: ${isAdminAuth ? '#dc2626' : '#333'};">
              ${code}
            </div>
            <p style="margin: 0; color: #666; font-size: 14px;">
              This ${isAdminAuth ? 'PIN' : 'code'} expires in ${expiryMinutes} minutes
            </p>
            ${isAdminAuth ? '<p style="margin: 5px 0 0 0; color: #dc2626; font-size: 12px; font-weight: bold;">⚠️ ADMIN ACCESS ONLY</p>' : ''}
          </div>
          
          <p><strong>${isAdminAuth ? '🚨 CRITICAL SECURITY NOTICE:' : 'Security Notice:'}</strong></p>
          <ul>
            <li>Never share this ${isAdminAuth ? 'PIN' : 'code'} with anyone</li>
            <li>Aureus Alliance will never ask for this ${isAdminAuth ? 'PIN' : 'code'} via phone or email</li>
            ${isAdminAuth ? '<li><strong>This PIN grants full admin access to the system</strong></li>' : ''}
            ${isAdminAuth ? '<li>If you did not attempt to login as admin, contact security immediately</li>' : '<li>If you didn\'t request this verification, please ignore this email</li>'}
            ${isAdminAuth ? '<li>Admin access is logged and monitored for security</li>' : ''}
          </ul>
          
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          
          <p style="font-size: 12px; color: #666; text-align: center;">
            This email was sent by Aureus Alliance Holdings. If you have questions, 
            please contact our support team.
          </p>
        </div>
      </body>
    </html>
  `;

  const text = `
    Aureus Alliance Holdings - Email Verification
    
    ${greeting},
    
    You need to verify your email address to ${purposeText}.
    
    Your verification code: ${code}
    
    This code expires in ${expiryMinutes} minutes.
    
    Security Notice:
    - Never share this code with anyone
    - Aureus Alliance will never ask for this code via phone or email
    - If you didn't request this verification, please ignore this email
    
    This email was sent by Aureus Alliance Holdings.
  `;

  return { subject, html, text };
}

/**
 * API handler for sending verification emails
 */
export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Check if email service is configured
  if (!resend || !RESEND_API_KEY) {
    console.error('❌ Email service not configured');
    return res.status(500).json({ error: 'Email service not configured' });
  }

  try {
    const { email, code, purpose = 'registration', userName, expiryMinutes = 15 } = req.body;

    // Validate required fields
    if (!email || !code) {
      return res.status(400).json({ error: 'Email and code are required' });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ error: 'Invalid email format' });
    }

    console.log(`📧 Sending verification email to ${email} for ${purpose}`);

    // Generate email content
    const emailContent = generateVerificationEmailContent(code, purpose, userName, expiryMinutes);

    // Send email via Resend
    const result = await resend.emails.send({
      from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
      to: [email],
      subject: emailContent.subject,
      html: emailContent.html,
      text: emailContent.text,
      tags: [
        { name: 'category', value: 'verification' },
        { name: 'purpose', value: purpose }
      ]
    });

    if (result.error) {
      console.error('❌ Resend API error:', result.error);
      return res.status(500).json({ error: result.error.message });
    }

    console.log(`✅ Verification email sent successfully to ${email}`);
    
    return res.status(200).json({ 
      success: true, 
      messageId: result.data?.id,
      message: 'Verification email sent successfully'
    });

  } catch (error) {
    console.error('❌ Error sending verification email:', error);
    return res.status(500).json({ 
      error: error.message || 'Failed to send verification email' 
    });
  }
}
