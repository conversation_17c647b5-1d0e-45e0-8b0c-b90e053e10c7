# Vercel Deployment Guide for Aureus Africa

## Quick Fix for Current Error

The error "No Output Directory named 'public' found" is now fixed with the `vercel.json` configuration file that specifies `"outputDirectory": "dist"`.

## Environment Variables Setup

### Required Environment Variables for Vercel

Set these in your Vercel dashboard under Project Settings > Environment Variables:

#### Production Variables
```bash
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here

# Server-side Supabase (for API routes)
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Email Service (Resend)
RESEND_API_KEY=re_your-resend-api-key
RESEND_FROM_EMAIL=<EMAIL>
RESEND_FROM_NAME=Aureus Alliance Holdings

# Application Settings
NODE_ENV=production
VITE_APP_ENVIRONMENT=production
VITE_APP_VERSION=1.0.0

# Email Configuration
EMAIL_VERIFICATION_EXPIRY_MINUTES=15
EMAIL_VERIFICATION_MAX_ATTEMPTS=3
EMAIL_RATE_LIMIT_WINDOW_MINUTES=10

# Security
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters
```

## Deployment Steps

### 1. Push to GitHub
```bash
git add .
git commit -m "Add Vercel deployment configuration"
git push origin main
```

### 2. Connect to Vercel
1. Go to [vercel.com](https://vercel.com)
2. Import your GitHub repository
3. Select the `aureus_africa` folder as the root directory
4. Vercel will automatically detect it's a Vite project

### 3. Configure Build Settings
- **Framework Preset**: Vite
- **Root Directory**: `aureus_africa`
- **Build Command**: `npm run build`
- **Output Directory**: `dist`
- **Install Command**: `npm install`

### 4. Add Environment Variables
In Vercel dashboard, add all the environment variables listed above.

## API Routes Configuration

The `vercel.json` file is configured to handle your API routes properly:

- API routes in `/api/*` will be served as serverless functions
- All other routes will serve the React SPA
- CORS headers are configured for API routes

## Domain Configuration

### Custom Domain Setup
1. In Vercel dashboard, go to Project Settings > Domains
2. Add your custom domain: `aureusafrica.com`
3. Configure DNS records as instructed by Vercel

### CORS Update for Production
Update your server.js CORS configuration for production:

```javascript
app.use(cors({
  origin: NODE_ENV === 'production'
    ? ['https://aureusafrica.com', 'https://www.aureusafrica.com', 'https://your-vercel-app.vercel.app']
    : ['http://localhost:8000', 'http://localhost:8001', 'http://127.0.0.1:8000', 'http://127.0.0.1:8001'],
  credentials: true
}));
```

## Build Optimization

### Current Build Process
1. Clean previous build (`rimraf dist`)
2. Check environment variables
3. Run Vite build
4. Verify build integrity
5. Copy server files (for full-stack deployment)

### Vercel-Specific Optimizations
- Automatic code splitting
- CDN distribution
- Serverless functions for API routes
- Automatic HTTPS

## Troubleshooting

### Common Issues

1. **Environment Variables Not Loading**
   - Ensure all required variables are set in Vercel dashboard
   - Check variable names match exactly (case-sensitive)

2. **API Routes Not Working**
   - Verify `vercel.json` configuration
   - Check that API files are in the correct `/api` directory

3. **Build Failures**
   - Check build logs in Vercel dashboard
   - Ensure all dependencies are in `package.json`
   - Verify TypeScript compilation

4. **CORS Errors**
   - Update CORS configuration in `server.js`
   - Add your Vercel domain to allowed origins

## Monitoring

### Post-Deployment Checks
1. Test all major user flows
2. Check API endpoints functionality
3. Verify email sending works
4. Test database connections
5. Monitor error logs in Vercel dashboard

### Performance Monitoring
- Use Vercel Analytics
- Monitor Core Web Vitals
- Check serverless function performance

## Security Considerations

### Environment Variables
- Never commit `.env` files
- Use Vercel's encrypted environment variables
- Separate development and production keys

### API Security
- CORS properly configured
- Rate limiting implemented
- Input validation on all endpoints
- Secure headers configured

## Next Steps After Deployment

1. Test the deployed application thoroughly
2. Set up monitoring and alerts
3. Configure custom domain
4. Set up CI/CD pipeline for automatic deployments
5. Monitor performance and optimize as needed
