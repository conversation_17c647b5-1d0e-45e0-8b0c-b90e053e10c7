// API endpoint to fetch upcoming meetings for the landing page
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase configuration for meetings API');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔍 Fetching upcoming meetings...');

    // Get upcoming meetings (scheduled status, future dates)
    const { data: meetings, error } = await supabase
      .from('meetings')
      .select(`
        id,
        title,
        topic,
        description,
        meeting_date,
        meeting_time,
        timezone,
        duration_minutes,
        max_attendees,
        current_attendees,
        status
      `)
      .eq('status', 'scheduled')
      .gte('meeting_date', new Date().toISOString().split('T')[0])
      .order('meeting_date', { ascending: true })
      .order('meeting_time', { ascending: true })
      .limit(10);

    if (error) {
      console.error('❌ Error fetching meetings:', error);
      return res.status(500).json({ 
        error: 'Failed to fetch meetings',
        details: error.message 
      });
    }

    console.log(`✅ Found ${meetings?.length || 0} upcoming meetings`);

    // Format meetings for frontend consumption
    const formattedMeetings = meetings?.map(meeting => ({
      id: meeting.id,
      title: meeting.title,
      topic: meeting.topic,
      description: meeting.description,
      date: meeting.meeting_date,
      time: meeting.meeting_time,
      timezone: meeting.timezone || 'SAST',
      duration: meeting.duration_minutes || 60,
      maxAttendees: meeting.max_attendees,
      currentAttendees: meeting.current_attendees || 0,
      availableSpots: meeting.max_attendees - (meeting.current_attendees || 0),
      isFullyBooked: (meeting.current_attendees || 0) >= meeting.max_attendees,
      status: meeting.status
    })) || [];

    return res.status(200).json({
      success: true,
      meetings: formattedMeetings,
      count: formattedMeetings.length
    });

  } catch (error) {
    console.error('❌ Unexpected error in meetings API:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      details: error.message 
    });
  }
}
