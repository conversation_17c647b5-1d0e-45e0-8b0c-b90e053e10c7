import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing Supabase environment variables');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      telegram_id,
      telegram_username,
      telegram_first_name,
      telegram_last_name,
      email,
      password,
      username,
      full_name,
      phone,
      country_of_residence,
      address
    } = req.body;

    console.log('🔄 Starting simple Telegram migration for:', {
      telegram_id,
      username,
      email: email?.substring(0, 5) + '***'
    });

    // Validate input
    if (!telegram_id || !password || !email || !username) {
      console.error('❌ Missing required data:', { telegram_id: !!telegram_id, password: !!password, email: !!email, username: !!username });
      return res.status(400).json({ error: 'Missing required data' });
    }

    // Check if user is already migrated
    const { data: existingUser, error: checkError } = await supabase
      .from('users')
      .select('id, telegram_migrated, migration_status')
      .eq('telegram_id', telegram_id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('❌ Error checking existing user:', checkError);
      return res.status(500).json({ error: 'Database error during migration check' });
    }

    if (existingUser && existingUser.telegram_migrated) {
      console.log('⚠️ User already migrated:', existingUser.id);
      return res.status(400).json({ error: 'User has already been migrated' });
    }

    // Hash password
    const saltRounds = 12;
    const password_hash = await bcrypt.hash(password, saltRounds);

    // Check if username already exists (excluding current user)
    const { data: usernameCheck, error: usernameError } = await supabase
      .from('users')
      .select('id')
      .eq('username', username)
      .neq('telegram_id', telegram_id);

    if (usernameError) {
      console.error('❌ Error checking username:', usernameError);
      return res.status(500).json({ error: 'Database error during username check' });
    }

    if (usernameCheck && usernameCheck.length > 0) {
      console.log('⚠️ Username already exists:', username);
      return res.status(400).json({ error: 'Username already exists' });
    }

    // Check if email already exists (excluding current user)
    const { data: emailCheck, error: emailError } = await supabase
      .from('users')
      .select('id')
      .eq('email', email)
      .neq('telegram_id', telegram_id);

    if (emailError) {
      console.error('❌ Error checking email:', emailError);
      return res.status(500).json({ error: 'Database error during email check' });
    }

    if (emailCheck && emailCheck.length > 0) {
      console.log('⚠️ Email already exists:', email);
      return res.status(400).json({ error: 'Email already exists' });
    }

    let newUser;

    if (existingUser) {
      // Update existing user record
      console.log('🔄 Updating existing user record:', existingUser.id);
      
      const { data: updatedUser, error: updateError } = await supabase
        .from('users')
        .update({
          email,
          password_hash,
          username,
          full_name,
          phone,
          country_of_residence,
          address,
          telegram_migrated: true,
          migration_status: 'web_only',
          telegram_migrated_at: new Date().toISOString(),
          migration_completed_at: new Date().toISOString()
        })
        .eq('id', existingUser.id)
        .select()
        .single();

      if (updateError) {
        console.error('❌ Failed to update user:', updateError);
        return res.status(500).json({ error: 'Failed to update user record' });
      }

      newUser = updatedUser;
      console.log('✅ User record updated successfully:', newUser.id);

    } else {
      // Create new user record
      console.log('🔄 Creating new user record for telegram_id:', telegram_id);
      
      const { data: createdUser, error: createError } = await supabase
        .from('users')
        .insert({
          telegram_id,
          email,
          password_hash,
          username,
          full_name,
          phone,
          country_of_residence,
          address,
          first_name: telegram_first_name,
          last_name: telegram_last_name,
          telegram_migrated: true,
          migration_status: 'web_only',
          telegram_migrated_at: new Date().toISOString(),
          migration_completed_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (createError) {
        console.error('❌ Failed to create user:', createError);
        return res.status(500).json({ error: 'Failed to create user record' });
      }

      newUser = createdUser;
      console.log('✅ New user record created successfully:', newUser.id);
    }

    // Link telegram_users record to new user (this is the important part)
    const { error: linkError2 } = await supabase
      .from('telegram_users')
      .update({ user_id: newUser.id })
      .eq('telegram_id', telegram_id);

    if (linkError2) {
      console.error('Failed to link telegram user to new user:', linkError2);
      // Don't fail the migration for this, just log it
    }

    console.log('✅ Streamlined Telegram migration completed for:', username);

    return res.status(200).json({
      success: true,
      message: 'Migration completed successfully! Please log in with your email and password.'
    });

  } catch (error) {
    console.error('Simple migration error:', error);
    return res.status(500).json({ 
      error: error.message || 'Migration failed. Please try again.' 
    });
  }
}
