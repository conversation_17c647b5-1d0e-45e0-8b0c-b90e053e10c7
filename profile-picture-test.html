<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Picture Upload Test - Aureus Africa</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #fff;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 30px;
            border: 1px solid rgba(255, 215, 0, 0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            color: #FFD700;
            margin-bottom: 10px;
        }
        
        .test-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 215, 0, 0.1);
        }
        
        .test-section h3 {
            color: #FFD700;
            margin-top: 0;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: rgba(68, 255, 68, 0.2);
            border: 1px solid #44ff44;
            color: #44ff44;
        }
        
        .status.error {
            background: rgba(255, 68, 68, 0.2);
            border: 1px solid #ff4444;
            color: #ff4444;
        }
        
        .status.info {
            background: rgba(68, 136, 255, 0.2);
            border: 1px solid #4488ff;
            color: #4488ff;
        }
        
        .test-button {
            background: linear-gradient(135deg, #FFD700 0%, #E6C200 100%);
            color: #000;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: bold;
            cursor: pointer;
            margin: 5px;
            transition: all 0.2s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
        }
        
        .test-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .code-block {
            background: #000;
            border: 1px solid #333;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .instructions {
            background: rgba(255, 215, 0, 0.1);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .instructions h4 {
            color: #FFD700;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖼️ Profile Picture Upload Test</h1>
            <p>Test the profile picture upload and persistence functionality</p>
        </div>

        <div class="instructions">
            <h4>📋 Testing Instructions</h4>
            <ol>
                <li><strong>Login to Dashboard:</strong> Go to the main application and login</li>
                <li><strong>Upload Profile Picture:</strong> Try uploading a profile picture in both locations:
                    <ul>
                        <li><strong>Settings Page:</strong> Manual save with Save/Cancel buttons</li>
                        <li><strong>Dashboard Header:</strong> Auto-save functionality</li>
                    </ul>
                </li>
                <li><strong>Test Persistence:</strong> After uploading, refresh the page and verify the image persists</li>
                <li><strong>Test Multiple Locations:</strong> Verify the same image appears in all locations</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🔍 Database Profile Picture Check</h3>
            <p>Check if profile pictures are properly stored in the database:</p>
            <button class="test-button" onclick="checkDatabaseProfilePictures()">Check Database</button>
            <div id="database-status"></div>
        </div>

        <div class="test-section">
            <h3>💾 LocalStorage Profile Picture Check</h3>
            <p>Check if profile pictures are properly stored in localStorage:</p>
            <button class="test-button" onclick="checkLocalStorageProfilePictures()">Check LocalStorage</button>
            <div id="localstorage-status"></div>
        </div>

        <div class="test-section">
            <h3>🔄 Profile Picture Refresh Test</h3>
            <p>Test if profile pictures persist after page refresh:</p>
            <button class="test-button" onclick="testProfilePictureRefresh()">Test Refresh</button>
            <div id="refresh-status"></div>
        </div>

        <div class="test-section">
            <h3>📊 Test Results Summary</h3>
            <div id="test-summary">
                <div class="status info">Click the test buttons above to run diagnostics</div>
            </div>
        </div>
    </div>

    <script>
        let testResults = {
            database: null,
            localStorage: null,
            refresh: null
        };

        function updateTestSummary() {
            const summaryDiv = document.getElementById('test-summary');
            let html = '<h4>Test Results:</h4>';
            
            Object.entries(testResults).forEach(([test, result]) => {
                const status = result === null ? 'info' : (result ? 'success' : 'error');
                const icon = result === null ? '⏳' : (result ? '✅' : '❌');
                const text = result === null ? 'Not tested' : (result ? 'PASSED' : 'FAILED');
                html += `<div class="status ${status}">${icon} ${test.toUpperCase()}: ${text}</div>`;
            });
            
            summaryDiv.innerHTML = html;
        }

        function checkDatabaseProfilePictures() {
            const statusDiv = document.getElementById('database-status');
            statusDiv.innerHTML = '<div class="status info">⏳ Checking database...</div>';
            
            // Simulate database check (in real implementation, this would make an API call)
            setTimeout(() => {
                const hasProfilePictures = localStorage.getItem('aureus_user') && 
                    JSON.parse(localStorage.getItem('aureus_user')).profile_image_url;
                
                if (hasProfilePictures) {
                    statusDiv.innerHTML = '<div class="status success">✅ Profile picture found in user data</div>';
                    testResults.database = true;
                } else {
                    statusDiv.innerHTML = '<div class="status error">❌ No profile picture found in user data</div>';
                    testResults.database = false;
                }
                updateTestSummary();
            }, 1000);
        }

        function checkLocalStorageProfilePictures() {
            const statusDiv = document.getElementById('localstorage-status');
            
            const emailUser = localStorage.getItem('aureus_user');
            const telegramUser = localStorage.getItem('aureus_telegram_user');
            
            let html = '';
            let hasAnyProfilePicture = false;
            
            if (emailUser) {
                const userData = JSON.parse(emailUser);
                if (userData.profile_image_url) {
                    html += '<div class="status success">✅ Email user profile picture found</div>';
                    html += `<div class="code-block">URL: ${userData.profile_image_url}</div>`;
                    hasAnyProfilePicture = true;
                } else {
                    html += '<div class="status error">❌ Email user profile picture not found</div>';
                }
            }
            
            if (telegramUser) {
                const userData = JSON.parse(telegramUser);
                if (userData.database_user?.profile_image_url) {
                    html += '<div class="status success">✅ Telegram user profile picture found</div>';
                    html += `<div class="code-block">URL: ${userData.database_user.profile_image_url}</div>`;
                    hasAnyProfilePicture = true;
                } else {
                    html += '<div class="status error">❌ Telegram user profile picture not found</div>';
                }
            }
            
            if (!emailUser && !telegramUser) {
                html = '<div class="status error">❌ No user data found in localStorage</div>';
            }
            
            statusDiv.innerHTML = html;
            testResults.localStorage = hasAnyProfilePicture;
            updateTestSummary();
        }

        function testProfilePictureRefresh() {
            const statusDiv = document.getElementById('refresh-status');
            statusDiv.innerHTML = '<div class="status info">⏳ Testing refresh persistence...</div>';
            
            // Store current profile picture state
            const beforeRefresh = {
                emailUser: localStorage.getItem('aureus_user'),
                telegramUser: localStorage.getItem('aureus_telegram_user')
            };
            
            // Simulate page refresh test
            setTimeout(() => {
                const afterRefresh = {
                    emailUser: localStorage.getItem('aureus_user'),
                    telegramUser: localStorage.getItem('aureus_telegram_user')
                };
                
                let persistent = true;
                let html = '';
                
                if (beforeRefresh.emailUser && afterRefresh.emailUser) {
                    const beforeData = JSON.parse(beforeRefresh.emailUser);
                    const afterData = JSON.parse(afterRefresh.emailUser);
                    
                    if (beforeData.profile_image_url === afterData.profile_image_url) {
                        html += '<div class="status success">✅ Email user profile picture persisted</div>';
                    } else {
                        html += '<div class="status error">❌ Email user profile picture not persistent</div>';
                        persistent = false;
                    }
                }
                
                if (beforeRefresh.telegramUser && afterRefresh.telegramUser) {
                    const beforeData = JSON.parse(beforeRefresh.telegramUser);
                    const afterData = JSON.parse(afterRefresh.telegramUser);
                    
                    if (beforeData.database_user?.profile_image_url === afterData.database_user?.profile_image_url) {
                        html += '<div class="status success">✅ Telegram user profile picture persisted</div>';
                    } else {
                        html += '<div class="status error">❌ Telegram user profile picture not persistent</div>';
                        persistent = false;
                    }
                }
                
                html += '<div class="status info">💡 To fully test persistence, upload a profile picture and then refresh this page</div>';
                
                statusDiv.innerHTML = html;
                testResults.refresh = persistent;
                updateTestSummary();
            }, 1000);
        }

        // Initialize
        updateTestSummary();
        
        // Auto-run localStorage check on page load
        setTimeout(() => {
            checkLocalStorageProfilePictures();
        }, 500);
    </script>
</body>
</html>
