import React, { useState, useEffect } from 'react';
import { SimpleTelegramMigration } from '../auth/SimpleTelegramMigration';
import { supabase, getServiceRoleClient } from '../../lib/supabase';

interface User {
  id: number;
  email: string;
  username: string;
  full_name: string;
  password_hash: string;
  migration_status: string;
}

const MigratePage: React.FC = () => {
  // Custom navigation function for Vite React SPA
  const navigate = (path: string) => {
    window.location.href = path;
  };
  
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    checkUserSession();
  }, []);

  const checkUserSession = async () => {
    try {
      console.log('🔍 Checking user session for migration...');

      // Check if user is logged in via localStorage (temporary session)
      const storedUser = localStorage.getItem('aureus_user');
      const storedSession = localStorage.getItem('aureus_session');

      if (!storedUser || !storedSession) {
        console.log('❌ No session found, redirecting to login');
        navigate('/login?redirect=migrate');
        return;
      }

      const userData = JSON.parse(storedUser);
      const sessionData = JSON.parse(storedSession);

      // Check if session is expired
      if (Date.now() > sessionData.expires) {
        console.log('❌ Session expired, redirecting to login');
        localStorage.removeItem('aureus_user');
        localStorage.removeItem('aureus_session');
        navigate('/login?redirect=migrate');
        return;
      }

      // Verify user exists and has telegram_auth
      const serviceClient = getServiceRoleClient();
      const { data: dbUser, error: userError } = await serviceClient
        .from('users')
        .select('*')
        .eq('id', userData.id)
        .single();

      if (userError || !dbUser) {
        console.log('❌ User not found in database:', userError);
        setError('User not found. Please login again.');
        setLoading(false);
        return;
      }

      // Check if user needs migration
      if (dbUser.password_hash !== 'telegram_auth') {
        console.log('✅ User already migrated, redirecting to dashboard');
        navigate('/dashboard');
        return;
      }

      console.log('✅ User session valid, ready for migration');
      setUser(dbUser);
      setLoading(false);
    } catch (error) {
      console.error('❌ Error checking user session:', error);
      setError('Failed to verify user session. Please try again.');
      setLoading(false);
    }
  };

  const handleMigrationSuccess = async (userData: any) => {
    console.log('✅ Migration completed successfully:', userData);
    
    // Update user state
    setUser(userData);
    
    // Show success message briefly
    setError(null);
    setLoading(false);
    
    // Update localStorage with new user data
    if (userData) {
      localStorage.setItem('aureus_user', JSON.stringify(userData));
      
      // Update session to reflect migration completion
      const currentSession = localStorage.getItem('aureus_session');
      if (currentSession) {
        const sessionData = JSON.parse(currentSession);
        sessionData.migrated = true;
        localStorage.setItem('aureus_session', JSON.stringify(sessionData));
      }
    }

    // Redirect to dashboard
    setTimeout(() => {
      navigate('/dashboard');
    }, 1000);
  };

  const handleCancel = () => {
    // Redirect back to dashboard (they can still use Telegram for now)
    navigate('/dashboard');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center text-white">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto mb-4"></div>
          <p>Loading migration page...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="max-w-md mx-auto bg-red-500/20 border border-red-500 text-red-400 px-6 py-4 rounded-lg text-center">
          <h2 className="text-xl font-bold mb-2">Migration Error</h2>
          <p className="mb-4">{error}</p>
          <button
            onClick={() => navigate('/login')}
            className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md"
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center text-white">
          <h2 className="text-xl font-bold mb-2">No User Found</h2>
          <p className="mb-4">Please login to continue with migration.</p>
          <button
            onClick={() => navigate('/login')}
            className="bg-yellow-500 hover:bg-yellow-600 text-black px-4 py-2 rounded-md"
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <SimpleTelegramMigration
        user={user}
        onSuccess={handleMigrationSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
};

export default MigratePage;
