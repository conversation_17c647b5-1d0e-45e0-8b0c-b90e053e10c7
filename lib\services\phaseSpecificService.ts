import { supabase, getServiceRoleClient } from '../supabase';

export interface InvestmentPhase {
  id: number;
  phase_number: number;
  phase_name: string;
  price_per_share: number;
  total_shares_available: number;
  shares_sold: number;
  is_active: boolean;
  start_date: string;
  end_date?: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface PhaseCompetition {
  id: string;
  name: string;
  description: string;
  phase_id: number;
  start_date: string;
  end_date?: string;
  is_active: boolean;
  minimum_qualification_amount: number;
  total_prize_pool: number;
  max_participants?: number;
  status: 'upcoming' | 'active' | 'ended';
  total_participants: number;
  qualified_participants: number;
  created_at: string;
  updated_at: string;
}

export interface PhaseStatus {
  status: 'upcoming' | 'active' | 'completed';
  icon: string;
  color: string;
  label: string;
}

export interface PhaseProgress {
  percentage: number;
  sharesSold: number;
  sharesRemaining: number;
  targetAmount: number;
  raisedAmount: number;
}

export interface PhaseTimeline {
  startDate?: string;
  endDate?: string;
  duration?: string;
  timeRemaining?: string;
  nextPhase?: InvestmentPhase;
}

class PhaseSpecificService {
  private supabase: any;

  constructor() {
    this.supabase = supabase;
  }

  /**
   * Get all investment phases ordered by phase number
   */
  async getAllPhases(): Promise<InvestmentPhase[]> {
    try {
      const { data, error } = await this.supabase
        .from('investment_phases')
        .select('*')
        .order('phase_number', { ascending: true });

      if (error) {
        console.error('Error fetching investment phases:', error);
        return this.getFallbackPhases();
      }

      return data || this.getFallbackPhases();
    } catch (error) {
      console.error('Error in getAllPhases:', error);
      return this.getFallbackPhases();
    }
  }

  /**
   * Get the currently active phase
   */
  async getActivePhase(): Promise<InvestmentPhase | null> {
    try {
      const { data, error } = await this.supabase
        .from('investment_phases')
        .select('*')
        .eq('is_active', true)
        .single();

      if (error) {
        console.error('Error fetching active phase:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in getActivePhase:', error);
      return null;
    }
  }

  /**
   * Get phase by ID
   */
  async getPhaseById(phaseId: number): Promise<InvestmentPhase | null> {
    try {
      const { data, error } = await this.supabase
        .from('investment_phases')
        .select('*')
        .eq('id', phaseId)
        .single();

      if (error) {
        console.error('Error fetching phase by ID:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in getPhaseById:', error);
      return null;
    }
  }

  /**
   * Get competition for a specific phase
   */
  async getCompetitionForPhase(phaseId: number): Promise<PhaseCompetition | null> {
    try {
      const { data, error } = await this.supabase
        .from('competitions')
        .select('*')
        .eq('phase_id', phaseId)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error) {
        console.error('Error fetching competition for phase:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in getCompetitionForPhase:', error);
      return null;
    }
  }

  /**
   * Calculate phase status based on dates and activity
   */
  getPhaseStatus(phase: InvestmentPhase): PhaseStatus {
    const now = new Date();
    const startDate = phase.start_date ? new Date(phase.start_date) : null;
    const endDate = phase.end_date ? new Date(phase.end_date) : null;

    // Check if phase is completed (sold out or past end date)
    const isCompleted = (endDate && now > endDate) || 
                       (phase.shares_sold >= phase.total_shares_available);

    if (isCompleted) {
      return {
        status: 'completed',
        icon: '🔴',
        color: 'text-red-400',
        label: 'Completed'
      };
    }

    // Check if phase is active
    if (phase.is_active && (!startDate || now >= startDate)) {
      return {
        status: 'active',
        icon: '🟢',
        color: 'text-green-400',
        label: 'Active'
      };
    }

    // Phase is upcoming
    return {
      status: 'upcoming',
      icon: '🟡',
      color: 'text-yellow-400',
      label: 'Upcoming'
    };
  }

  /**
   * Calculate phase progress metrics
   */
  getPhaseProgress(phase: InvestmentPhase): PhaseProgress {
    const sharesSold = phase.shares_sold || 0;
    const totalShares = phase.total_shares_available;
    const percentage = totalShares > 0 ? (sharesSold / totalShares) * 100 : 0;
    const sharesRemaining = totalShares - sharesSold;
    const targetAmount = totalShares * phase.price_per_share;
    const raisedAmount = sharesSold * phase.price_per_share;

    return {
      percentage: Math.min(percentage, 100),
      sharesSold,
      sharesRemaining: Math.max(sharesRemaining, 0),
      targetAmount,
      raisedAmount
    };
  }

  /**
   * Calculate phase timeline information
   */
  getPhaseTimeline(phase: InvestmentPhase, nextPhase?: InvestmentPhase): PhaseTimeline {
    const timeline: PhaseTimeline = {
      startDate: phase.start_date,
      endDate: phase.end_date,
      nextPhase
    };

    if (phase.start_date && phase.end_date) {
      const start = new Date(phase.start_date);
      const end = new Date(phase.end_date);
      const duration = end.getTime() - start.getTime();
      const days = Math.ceil(duration / (1000 * 60 * 60 * 24));
      timeline.duration = `${days} days`;

      // Calculate time remaining if phase is active
      const now = new Date();
      if (now < end) {
        const remaining = end.getTime() - now.getTime();
        const remainingDays = Math.ceil(remaining / (1000 * 60 * 60 * 24));
        timeline.timeRemaining = remainingDays > 0 ? `${remainingDays} days remaining` : 'Ending soon';
      }
    }

    return timeline;
  }

  /**
   * Get fallback phases for development/testing
   */
  private getFallbackPhases(): InvestmentPhase[] {
    const basePhases = [
      { phase_number: 0, phase_name: 'Presale', price_per_share: 0.10, target: 100000 },
      { phase_number: 1, phase_name: 'Phase 1', price_per_share: 0.15, target: 150000 },
      { phase_number: 2, phase_name: 'Phase 2', price_per_share: 0.20, target: 200000 },
      { phase_number: 3, phase_name: 'Phase 3', price_per_share: 0.25, target: 250000 },
      { phase_number: 4, phase_name: 'Phase 4', price_per_share: 0.30, target: 300000 },
      { phase_number: 5, phase_name: 'Phase 5', price_per_share: 0.35, target: 350000 },
      { phase_number: 6, phase_name: 'Phase 6', price_per_share: 0.40, target: 400000 },
      { phase_number: 7, phase_name: 'Phase 7', price_per_share: 0.45, target: 450000 },
      { phase_number: 8, phase_name: 'Phase 8', price_per_share: 0.50, target: 500000 },
    ];

    // Extend to Phase 19 as requested
    for (let i = 9; i <= 19; i++) {
      basePhases.push({
        phase_number: i,
        phase_name: `Phase ${i}`,
        price_per_share: 0.50 + (i - 8) * 0.05, // Incrementing price
        target: 500000 + (i - 8) * 50000 // Incrementing target
      });
    }

    return basePhases.map((phase, index) => ({
      id: index + 1,
      phase_number: phase.phase_number,
      phase_name: phase.phase_name,
      price_per_share: phase.price_per_share,
      total_shares_available: phase.target / phase.price_per_share,
      shares_sold: 0,
      is_active: phase.phase_number === 1, // Phase 1 is active by default
      start_date: new Date(Date.now() + phase.phase_number * 30 * 24 * 60 * 60 * 1000).toISOString(),
      end_date: new Date(Date.now() + (phase.phase_number + 1) * 30 * 24 * 60 * 60 * 1000).toISOString(),
      description: `${phase.phase_name} - Share price $${phase.price_per_share.toFixed(2)}`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));
  }
}

export const phaseSpecificService = new PhaseSpecificService();
