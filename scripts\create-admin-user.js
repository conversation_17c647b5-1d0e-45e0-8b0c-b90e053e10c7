// <PERSON>ript to create an admin user in Supabase
// Run this with: node scripts/create-admin-user.js

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY in .env file');
  process.exit(1);
}

// Create Supabase client with service role key (has admin privileges)
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createAdminUser() {
  const email = '<EMAIL>';
  const password = 'AureusAdmin2024!'; // Change this to your preferred password
  
  try {
    console.log('Creating admin user...');
    
    // Create the user
    const { data, error } = await supabase.auth.admin.createUser({
      email: email,
      password: password,
      email_confirm: true,
      user_metadata: {
        role: 'admin',
        name: 'JP Rademeyer'
      }
    });

    if (error) {
      console.error('Error creating user:', error.message);
      return;
    }

    console.log('✅ Admin user created successfully!');
    console.log('📧 Email:', email);
    console.log('🔑 Password:', password);
    console.log('👤 User ID:', data.user.id);

    // Add user to admin_users table
    const { error: dbError } = await supabase
      .from('admin_users')
      .upsert({
        id: data.user.id,
        email: email,
        role: 'super_admin',
        created_at: new Date().toISOString()
      });

    if (dbError) {
      console.error('Error adding to admin_users table:', dbError.message);
    } else {
      console.log('✅ User added to admin_users table');
    }

    console.log('\n🎉 Setup complete! You can now login to /admin with:');
    console.log(`Email: ${email}`);
    console.log(`Password: ${password}`);
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

createAdminUser();
