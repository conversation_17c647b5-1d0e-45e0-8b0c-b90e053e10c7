import { useState, useEffect } from 'react'
import { getServiceRoleClient } from '../lib/supabase'

export interface AdminNotificationCounts {
  pendingPayments: number
  pendingWithdrawals: number
  openSupportTickets: number
  pendingConsultations: number
  pendingKyc: number
  totalPending: number
}

export const useAdminNotifications = () => {
  const [counts, setCounts] = useState<AdminNotificationCounts>({
    pendingPayments: 0,
    pendingWithdrawals: 0,
    openSupportTickets: 0,
    pendingConsultations: 0,
    pendingKyc: 0,
    totalPending: 0
  })
  const [loading, setLoading] = useState(true)

  const fetchNotificationCounts = async () => {
    try {
      const serviceClient = getServiceRoleClient()
      
      // Fetch pending payments
      const { data: pendingPayments } = await serviceClient
        .from('aureus_share_purchases')
        .select('id')
        .eq('status', 'pending')
      
      // Fetch pending commission withdrawals
      const { data: pendingWithdrawals } = await serviceClient
        .from('commission_withdrawals')
        .select('id')
        .eq('status', 'pending')
      
      // Fetch open support tickets
      const { data: openTickets } = await serviceClient
        .from('support_tickets')
        .select('id')
        .in('status', ['open', 'in_progress'])
      
      // Fetch pending consultations (scheduled but not confirmed)
      const { data: pendingConsultations } = await serviceClient
        .from('consultation_bookings')
        .select('id')
        .eq('status', 'scheduled')
      
      // Fetch pending KYC approvals
      const { data: pendingKyc } = await serviceClient
        .from('kyc_information')
        .select('id')
        .eq('kyc_status', 'pending')

      const newCounts = {
        pendingPayments: pendingPayments?.length || 0,
        pendingWithdrawals: pendingWithdrawals?.length || 0,
        openSupportTickets: openTickets?.length || 0,
        pendingConsultations: pendingConsultations?.length || 0,
        pendingKyc: pendingKyc?.length || 0,
        totalPending: 0
      }

      // Calculate total pending items
      newCounts.totalPending = 
        newCounts.pendingPayments + 
        newCounts.pendingWithdrawals + 
        newCounts.openSupportTickets + 
        newCounts.pendingConsultations + 
        newCounts.pendingKyc

      setCounts(newCounts)
    } catch (error) {
      console.error('Error fetching admin notification counts:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchNotificationCounts()
    
    // Refresh counts every 30 seconds
    const interval = setInterval(fetchNotificationCounts, 30000)
    
    return () => clearInterval(interval)
  }, [])

  return {
    counts,
    loading,
    refresh: fetchNotificationCounts
  }
}

// Helper function to get badge count for specific tab
export const getBadgeCountForTab = (tabId: string, counts: AdminNotificationCounts): number => {
  switch (tabId) {
    case 'payments':
      return counts.pendingPayments
    case 'commissions':
      return counts.pendingWithdrawals
    case 'support':
      return counts.openSupportTickets
    case 'meetings':
      return counts.pendingConsultations
    case 'users':
      return counts.pendingKyc
    default:
      return 0
  }
}
