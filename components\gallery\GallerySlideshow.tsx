import React, { useState, useEffect, useCallback } from 'react';
import type { GalleryImage } from '../../types/gallery';

interface GallerySlideshowProps {
  images: GalleryImage[];
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showThumbnails?: boolean;
  showIndicators?: boolean;
  showArrows?: boolean;
  height?: string;
  onImageClick?: (image: GalleryImage) => void;
}

export const GallerySlideshow: React.FC<GallerySlideshowProps> = ({
  images,
  autoPlay = true,
  autoPlayInterval = 5000,
  showThumbnails = true,
  showIndicators = true,
  showArrows = true,
  height = '500px',
  onImageClick
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());

  const currentImage = images[currentIndex];

  // Auto-play functionality
  useEffect(() => {
    if (!isPlaying || images.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex(prev => (prev + 1) % images.length);
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [isPlaying, images.length, autoPlayInterval]);

  // Navigation functions
  const goToNext = useCallback(() => {
    setCurrentIndex(prev => (prev + 1) % images.length);
  }, [images.length]);

  const goToPrevious = useCallback(() => {
    setCurrentIndex(prev => (prev - 1 + images.length) % images.length);
  }, [images.length]);

  const goToSlide = useCallback((index: number) => {
    setCurrentIndex(index);
  }, []);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowLeft':
          goToPrevious();
          break;
        case 'ArrowRight':
          goToNext();
          break;
        case ' ':
          e.preventDefault();
          setIsPlaying(prev => !prev);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [goToNext, goToPrevious]);

  const handleImageLoad = (imageId: string) => {
    setLoadedImages(prev => new Set([...prev, imageId]));
  };

  const handleImageClick = () => {
    if (onImageClick && currentImage) {
      onImageClick(currentImage);
    }
  };

  if (images.length === 0) {
    return (
      <div className="slideshow-container" style={{ height }}>
        <div className="slideshow-empty">
          <div className="slideshow-empty-icon">📸</div>
          <p>No images to display</p>
        </div>
      </div>
    );
  }

  return (
    <div className="slideshow-container" style={{ height }}>
      {/* Main Image Display */}
      <div className="slideshow-main">
        <div className="slideshow-image-container">
          {/* Loading State */}
          {!loadedImages.has(currentImage.id) && (
            <div className="slideshow-loading">
              <div className="slideshow-loading-spinner"></div>
            </div>
          )}

          {/* Main Image */}
          <img
            src={currentImage.image_url}
            alt={currentImage.alt_text || currentImage.title}
            className={`slideshow-image ${onImageClick ? 'slideshow-image-clickable' : ''}`}
            onLoad={() => handleImageLoad(currentImage.id)}
            onClick={handleImageClick}
            style={{
              opacity: loadedImages.has(currentImage.id) ? 1 : 0,
              transition: 'opacity 0.3s ease'
            }}
          />

          {/* Image Overlay */}
          <div className="slideshow-overlay">
            <div className="slideshow-info">
              <h3 className="slideshow-title">{currentImage.title}</h3>
              {currentImage.description && (
                <p className="slideshow-description">{currentImage.description}</p>
              )}
              {currentImage.category && (
                <span className="slideshow-category">{currentImage.category.name}</span>
              )}
            </div>
          </div>

          {/* Navigation Arrows */}
          {showArrows && images.length > 1 && (
            <>
              <button
                className="slideshow-arrow slideshow-arrow-left"
                onClick={goToPrevious}
                aria-label="Previous image"
              >
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
              <button
                className="slideshow-arrow slideshow-arrow-right"
                onClick={goToNext}
                aria-label="Next image"
              >
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
            </>
          )}
        </div>

        {/* Play/Pause Controls */}
        {images.length > 1 && (
          <div className="slideshow-controls">
            <button
              className="slideshow-play-pause"
              onClick={() => setIsPlaying(!isPlaying)}
              aria-label={isPlaying ? 'Pause slideshow' : 'Play slideshow'}
            >
              {isPlaying ? (
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <rect x="6" y="4" width="4" height="16" fill="currentColor"/>
                  <rect x="14" y="4" width="4" height="16" fill="currentColor"/>
                </svg>
              ) : (
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <polygon points="5,3 19,12 5,21" fill="currentColor"/>
                </svg>
              )}
            </button>
            <span className="slideshow-counter">
              {currentIndex + 1} / {images.length}
            </span>
          </div>
        )}
      </div>

      {/* Indicators */}
      {showIndicators && images.length > 1 && (
        <div className="slideshow-indicators">
          {images.map((_, index) => (
            <button
              key={index}
              className={`slideshow-indicator ${index === currentIndex ? 'active' : ''}`}
              onClick={() => goToSlide(index)}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}

      {/* Thumbnails */}
      {showThumbnails && images.length > 1 && (
        <div className="slideshow-thumbnails">
          <div className="slideshow-thumbnails-container">
            {images.map((image, index) => (
              <button
                key={image.id}
                className={`slideshow-thumbnail ${index === currentIndex ? 'active' : ''}`}
                onClick={() => goToSlide(index)}
                aria-label={`View ${image.title}`}
              >
                <img
                  src={image.image_url}
                  alt={image.title}
                  className="slideshow-thumbnail-image"
                />
                <div className="slideshow-thumbnail-overlay">
                  <span className="slideshow-thumbnail-title">{image.title}</span>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
