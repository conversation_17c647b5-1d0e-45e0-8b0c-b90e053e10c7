#!/usr/bin/env node

/**
 * Support System Database Setup Script
 * 
 * This script creates all necessary database tables and policies for the comprehensive
 * live chat support system with ticketing, training content, and consultation booking.
 * 
 * Usage: node scripts/setup-support-system.js
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config()

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:')
  console.error('   NEXT_PUBLIC_SUPABASE_URL')
  console.error('   SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function executeSqlFile(filePath) {
  try {
    console.log(`📄 Executing SQL file: ${filePath}`)
    const sql = fs.readFileSync(filePath, 'utf8')
    
    // Split by semicolons and execute each statement
    const statements = sql.split(';').filter(stmt => stmt.trim().length > 0)
    
    for (const statement of statements) {
      const trimmedStatement = statement.trim()
      if (trimmedStatement.length === 0) continue
      
      console.log(`   Executing: ${trimmedStatement.substring(0, 50)}...`)
      const { error } = await supabase.rpc('exec_sql', { sql_query: trimmedStatement })
      
      if (error) {
        console.error(`   ❌ Error executing statement: ${error.message}`)
        // Continue with other statements
      } else {
        console.log(`   ✅ Statement executed successfully`)
      }
    }
  } catch (error) {
    console.error(`❌ Error reading or executing SQL file ${filePath}:`, error.message)
    throw error
  }
}

async function createSqlExecutorFunction() {
  console.log('🔧 Creating SQL executor function...')
  
  const createFunctionSql = `
    CREATE OR REPLACE FUNCTION exec_sql(sql_query text)
    RETURNS void
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    BEGIN
      EXECUTE sql_query;
    END;
    $$;
  `
  
  const { error } = await supabase.rpc('exec', { sql: createFunctionSql })
  if (error) {
    console.error('❌ Error creating SQL executor function:', error.message)
    throw error
  }
  
  console.log('✅ SQL executor function created')
}

async function setupSupportSystem() {
  try {
    console.log('🚀 Starting Support System Database Setup...\n')
    
    // Create SQL executor function first
    await createSqlExecutorFunction()
    
    // Execute schema creation
    const schemaPath = path.join(__dirname, '..', 'sql', 'support_system_schema.sql')
    if (fs.existsSync(schemaPath)) {
      await executeSqlFile(schemaPath)
      console.log('✅ Support system schema created successfully\n')
    } else {
      console.error('❌ Schema file not found:', schemaPath)
    }
    
    // Execute RLS policies
    const rlsPath = path.join(__dirname, '..', 'sql', 'support_system_rls_policies.sql')
    if (fs.existsSync(rlsPath)) {
      await executeSqlFile(rlsPath)
      console.log('✅ RLS policies created successfully\n')
    } else {
      console.error('❌ RLS policies file not found:', rlsPath)
    }

    // Execute database functions
    const functionsPath = path.join(__dirname, '..', 'sql', 'support_system_functions.sql')
    if (fs.existsSync(functionsPath)) {
      await executeSqlFile(functionsPath)
      console.log('✅ Database functions created successfully\n')
    } else {
      console.error('❌ Functions file not found:', functionsPath)
    }

    // Create some sample data
    await createSampleData()
    
    console.log('🎉 Support System Database Setup Complete!')
    console.log('\n📋 Summary:')
    console.log('   ✅ Support agents management')
    console.log('   ✅ Agent availability tracking')
    console.log('   ✅ Support tickets system')
    console.log('   ✅ Live chat sessions')
    console.log('   ✅ Chat messages')
    console.log('   ✅ Training content management')
    console.log('   ✅ User training progress')
    console.log('   ✅ Consultation bookings')
    console.log('   ✅ Quick response templates')
    console.log('   ✅ Support analytics')
    console.log('   ✅ File attachments')
    console.log('   ✅ Row Level Security policies')
    console.log('   ✅ Database helper functions')
    console.log('\n🔗 Next Steps:')
    console.log('   1. Configure support agents in the admin dashboard')
    console.log('   2. Add training content for shareholders and affiliates')
    console.log('   3. Set up Zoom integration for consultations')
    console.log('   4. Test the live chat widget on your website')
    console.log('   5. Wrap your app with LiveChatProvider for website-wide chat')
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message)
    process.exit(1)
  }
}

async function createSampleData() {
  console.log('📝 Creating sample data...')
  
  try {
    // Create sample training categories
    const { error: categoryError } = await supabase
      .from('training_categories')
      .upsert([
        {
          name: 'Shareholder Basics',
          description: 'Essential information for new shareholders',
          target_audience: 'shareholder',
          sort_order: 1
        },
        {
          name: 'Investment Strategies',
          description: 'Advanced investment guidance and portfolio management',
          target_audience: 'shareholder',
          sort_order: 2
        },
        {
          name: 'Affiliate Marketing',
          description: 'Marketing and sales training for affiliates',
          target_audience: 'affiliate',
          sort_order: 1
        },
        {
          name: 'Commission System',
          description: 'Understanding the commission structure and payments',
          target_audience: 'affiliate',
          sort_order: 2
        },
        {
          name: 'Platform Training',
          description: 'General platform usage and features',
          target_audience: 'both',
          sort_order: 3
        }
      ])
    
    if (categoryError) {
      console.error('   ❌ Error creating training categories:', categoryError.message)
    } else {
      console.log('   ✅ Sample training categories created')
    }
    
    // Create sample quick response templates
    const { error: templateError } = await supabase
      .from('quick_response_templates')
      .upsert([
        {
          title: 'Welcome Greeting',
          content: 'Hello! Welcome to Aureus Alliance Holdings support. How can I assist you today?',
          category: 'greeting',
          target_audience: 'both',
          is_shared: true
        },
        {
          title: 'Shareholder Welcome',
          content: 'Thank you for contacting shareholder support. I\'m here to help with any questions about your investment portfolio, dividends, or account management.',
          category: 'greeting',
          target_audience: 'shareholder',
          is_shared: true
        },
        {
          title: 'Affiliate Welcome',
          content: 'Welcome to affiliate support! I can help you with commission questions, marketing materials, referral system, and business development opportunities.',
          category: 'greeting',
          target_audience: 'affiliate',
          is_shared: true
        },
        {
          title: 'Technical Issue',
          content: 'I understand you\'re experiencing a technical issue. Let me help you resolve this. Can you please describe what you were trying to do when the problem occurred?',
          category: 'technical',
          target_audience: 'both',
          is_shared: true
        },
        {
          title: 'Closing - Resolved',
          content: 'Great! I\'m glad we could resolve your issue. Is there anything else I can help you with today? If not, have a wonderful day!',
          category: 'closing',
          target_audience: 'both',
          is_shared: true
        }
      ])
    
    if (templateError) {
      console.error('   ❌ Error creating quick response templates:', templateError.message)
    } else {
      console.log('   ✅ Sample quick response templates created')
    }
    
  } catch (error) {
    console.error('   ❌ Error creating sample data:', error.message)
  }
}

// Run the setup
setupSupportSystem()
