/**
 * AFFILIATE NOTIFICATION SERVICE
 * 
 * Comprehensive notification system for affiliate-related events:
 * - New user registrations via referral links
 * - Commission earnings from referred user purchases
 * - Share transfers between users
 * - Commission withdrawals and conversions
 */

import { supabase, getServiceRoleClient } from '../supabase';
import { resendEmailService } from '../resendEmailService';
import { notificationService } from '../notificationService';

export interface NewReferralNotificationData {
  sponsorId: number;
  sponsorEmail: string;
  sponsorName: string;
  newUserName: string;
  newUserEmail: string;
  registrationDate: string;
  referralLink?: string;
}

export interface CommissionEarnedNotificationData {
  affiliateId: number;
  affiliateEmail: string;
  affiliateName: string;
  referredUserName: string;
  purchaseAmount: number;
  sharesPurchased: number;
  usdtCommission: number;
  shareCommission: number;
  totalUsdtBalance: number;
  totalShareBalance: number;
  transactionId: string;
  phaseName: string;
}

export interface ShareTransferNotificationData {
  senderId: number;
  senderEmail: string;
  senderName: string;
  recipientId: number;
  recipientEmail: string;
  recipientName: string;
  sharesTransferred: number;
  transferFee: number;
  transactionId: string;
  transferDate: string;
}

export interface WithdrawalNotificationData {
  userId: number;
  userEmail: string;
  userName: string;
  amount: number;
  currency: string;
  walletAddress: string;
  network?: string;
  requestDate: string;
}

class AffiliateNotificationService {
  /**
   * Send notification when a new user registers using an affiliate's referral link
   */
  async sendNewReferralNotification(data: NewReferralNotificationData): Promise<void> {
    try {
      console.log('📧 Sending new referral notification to sponsor:', data.sponsorEmail);

      // Send email notification
      await resendEmailService.sendNewReferralNotification({
        email: data.sponsorEmail,
        fullName: data.sponsorName,
        newUserName: data.newUserName,
        newUserEmail: data.newUserEmail,
        registrationDate: data.registrationDate,
        referralLink: data.referralLink
      });

      // Create in-app notification
      await notificationService.createNotification({
        user_id: data.sponsorId,
        type: 'referral_signup',
        title: '🎉 New Referral Signup!',
        message: `${data.newUserName} has joined using your referral link. They can now purchase shares and generate commissions for you!`,
        metadata: {
          new_user_name: data.newUserName,
          new_user_email: data.newUserEmail,
          registration_date: data.registrationDate
        },
        action_url: '/dashboard?section=referrals'
      });

      console.log('✅ New referral notification sent successfully');
    } catch (error) {
      console.error('❌ Error sending new referral notification:', error);
      throw error;
    }
  }

  /**
   * Send notification when a referred user purchases shares and generates commission
   */
  async sendCommissionEarnedNotification(data: CommissionEarnedNotificationData): Promise<void> {
    try {
      console.log('📧 Sending commission earned notification to affiliate:', data.affiliateEmail);

      // Send email notification
      await resendEmailService.sendCommissionEarnedNotification({
        email: data.affiliateEmail,
        fullName: data.affiliateName,
        usdtCommission: data.usdtCommission,
        shareCommission: data.shareCommission,
        referredUserName: data.referredUserName,
        purchaseAmount: data.purchaseAmount,
        transactionId: data.transactionId
      });

      // Create in-app notification
      const totalCommissionValue = data.usdtCommission + (data.shareCommission * 5); // Estimate share value
      await notificationService.createNotification({
        user_id: data.affiliateId,
        type: 'commission_earned',
        title: '💰 Commission Earned!',
        message: `You earned $${data.usdtCommission.toFixed(2)} USDT + ${data.shareCommission} shares from ${data.referredUserName}'s purchase of $${data.purchaseAmount.toFixed(2)}`,
        metadata: {
          usdt_commission: data.usdtCommission,
          share_commission: data.shareCommission,
          referred_user: data.referredUserName,
          purchase_amount: data.purchaseAmount,
          transaction_id: data.transactionId,
          phase_name: data.phaseName,
          total_commission_value: totalCommissionValue
        },
        action_url: '/dashboard?section=commissions'
      });

      console.log('✅ Commission earned notification sent successfully');
    } catch (error) {
      console.error('❌ Error sending commission earned notification:', error);
      throw error;
    }
  }

  /**
   * Send notifications for share transfers (to both sender and recipient)
   */
  async sendShareTransferNotifications(data: ShareTransferNotificationData): Promise<void> {
    try {
      console.log('📧 Sending share transfer notifications');

      // Send email to sender
      await resendEmailService.sendShareTransferNotification({
        email: data.senderEmail,
        fullName: data.senderName,
        type: 'sender',
        shares: data.sharesTransferred,
        transferFee: data.transferFee,
        recipientName: data.recipientName,
        transactionId: data.transactionId
      });

      // Send email to recipient
      await resendEmailService.sendShareTransferNotification({
        email: data.recipientEmail,
        fullName: data.recipientName,
        type: 'recipient',
        shares: data.sharesTransferred,
        transferFee: data.transferFee,
        recipientName: data.senderName, // For recipient, this is the sender's name
        transactionId: data.transactionId
      });

      // Create in-app notification for sender
      await notificationService.createNotification({
        user_id: data.senderId,
        type: 'share_transfer',
        title: '📤 Shares Transferred',
        message: `Successfully transferred ${data.sharesTransferred} shares to ${data.recipientName}. Transfer fee: ${data.transferFee} shares.`,
        metadata: {
          shares_transferred: data.sharesTransferred,
          transfer_fee: data.transferFee,
          recipient_name: data.recipientName,
          transaction_id: data.transactionId,
          transfer_date: data.transferDate,
          type: 'sender'
        },
        action_url: '/dashboard?section=commissions'
      });

      // Create in-app notification for recipient
      await notificationService.createNotification({
        user_id: data.recipientId,
        type: 'share_transfer',
        title: '📥 Shares Received',
        message: `You received ${data.sharesTransferred} shares from ${data.senderName}!`,
        metadata: {
          shares_received: data.sharesTransferred,
          sender_name: data.senderName,
          transaction_id: data.transactionId,
          transfer_date: data.transferDate,
          type: 'recipient'
        },
        action_url: '/dashboard?section=commissions'
      });

      console.log('✅ Share transfer notifications sent successfully');
    } catch (error) {
      console.error('❌ Error sending share transfer notifications:', error);
      throw error;
    }
  }

  /**
   * Send notification when user registration triggers sponsor assignment
   */
  async notifyNewUserRegistration(userId: number, sponsorUsername?: string): Promise<void> {
    try {
      if (!sponsorUsername) return;

      // Get sponsor information
      const { data: sponsor, error: sponsorError } = await supabase
        .from('users')
        .select('id, email, full_name, username')
        .eq('username', sponsorUsername)
        .single();

      if (sponsorError || !sponsor) {
        console.warn('Sponsor not found for notification:', sponsorUsername);
        return;
      }

      // Get new user information
      const { data: newUser, error: userError } = await supabase
        .from('users')
        .select('email, full_name, username')
        .eq('id', userId)
        .single();

      if (userError || !newUser) {
        console.warn('New user not found for notification:', userId);
        return;
      }

      await this.sendNewReferralNotification({
        sponsorId: sponsor.id,
        sponsorEmail: sponsor.email,
        sponsorName: sponsor.full_name || sponsor.username,
        newUserName: newUser.full_name || newUser.username,
        newUserEmail: newUser.email,
        registrationDate: new Date().toISOString()
      });

    } catch (error) {
      console.error('❌ Error notifying new user registration:', error);
      // Don't throw - this is a non-critical notification
    }
  }

  /**
   * Send withdrawal request notification
   */
  async sendWithdrawalRequestNotification(
    userId: number,
    amount: number,
    currency: string,
    walletAddress: string,
    network?: string
  ): Promise<void> {
    try {
      console.log('📧 Sending withdrawal request notification for user:', userId);

      const serviceClient = getServiceRoleClient();

      // Get user details
      const { data: user, error: userError } = await serviceClient
        .from('users')
        .select('email, username, full_name')
        .eq('id', userId)
        .single();

      if (userError || !user) {
        console.warn('User not found for withdrawal notification:', userId);
        return;
      }

      // Send email notification
      await resendEmailService.sendWithdrawalRequestNotification(
        user.email,
        user.full_name || user.username,
        amount,
        currency,
        walletAddress,
        network
      );

      // Create in-app notification
      await notificationService.createNotification({
        user_id: userId,
        title: 'Withdrawal Request Submitted',
        message: `Your withdrawal request for ${amount.toFixed(2)} ${currency} has been submitted and is pending admin approval.`,
        type: 'withdrawal',
        category: 'financial',
        metadata: {
          amount,
          currency,
          walletAddress,
          network,
          status: 'pending'
        }
      });

      console.log('✅ Withdrawal request notification sent successfully');

    } catch (error) {
      console.error('❌ Error sending withdrawal request notification:', error);
      // Don't throw - this is a non-critical notification
    }
  }
}

export const affiliateNotificationService = new AffiliateNotificationService();
