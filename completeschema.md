# Aureus Africa Database Complete Schema Audit

**Generated:** 2024-09-19  
**Database:** Supabase PostgreSQL  
**Total Tables:** 113

## Table Summary

| Table Name | Primary Key | Foreign Keys | Unique Constraints | Special Features |
|------------|-------------|--------------|-------------------|------------------|
| account_links | id (UUID) | web_user_id, database_user_id, linked_by_user_id | Multiple unique combinations | Account linking system |
| account_merge_log | id (UUID) | link_id, source_user_id, target_user_id, performed_by | None | Merge audit trail |
| active_marketing_materials | None | None | None | Marketing content |
| admin_audit_logs | id (UUID) | None | None | Admin action logging |
| admin_commission_conversion_queue | None | None | None | Commission processing |
| admin_notification_preferences | id (SERIAL) | admin_user_id | admin_user_id, telegram_id | Admin notifications |
| admin_users | id (UUID) | None | email | Admin authentication |
| advanced_kyc_verifications | id (UUID) | user_id | user_id | Biometric KYC |
| agent_availability | id (UUID) | agent_id | None | Support agent status |
| aureus_share_purchases | id (UUID) | user_id, phase_id | None | Share purchase records |
| auth_tokens | id (UUID) | user_id | None | Authentication tokens |
| bank_transfer_payments | id (UUID) | user_id | None | Bank payment tracking |
| biometric_templates | id (UUID) | user_id | user_id | Biometric data storage |
| biometric_verification_attempts | id (UUID) | user_id | None | Verification attempts |
| certificate_generation_queue | id (UUID) | user_id | None | Certificate processing |
| certificates | id (UUID) | user_id | certificate_number | Share certificates |
| chat_messages | id (UUID) | session_id, user_id | None | Support chat messages |
| chat_sessions | id (UUID) | user_id, agent_id | None | Support chat sessions |
| commission_balances | id (UUID) | user_id | user_id | User commission balances |
| commission_conversions | id (UUID) | user_id | None | Commission to shares |
| commission_escrow | id (UUID) | user_id | None | Escrowed commissions |
| commission_transactions | id (UUID) | user_id, referral_id | None | Commission history |
| commission_usage | id (UUID) | user_id | None | Commission usage tracking |
| commission_withdrawal_requests | id (UUID) | user_id | None | Withdrawal requests |
| commission_withdrawals | id (UUID) | user_id | None | Completed withdrawals |
| company_wallets | id (UUID) | None | network_currency | Company payment wallets |
| competition_leaderboard | id (UUID) | competition_id, user_id | None | Competition rankings |
| competition_participants | id (UUID) | competition_id, user_id | competition_user_unique | Competition entries |
| competition_prize_tiers | id (UUID) | competition_id | None | Prize structure |
| competitions | id (UUID) | None | None | Referral competitions |

## Critical Tables Analysis

### 1. Users Table (Core Entity)
- **Primary Key:** id (SERIAL)
- **Unique Fields:** email, phone, telegram_id
- **Key Features:** 
  - Telegram integration (telegram_id, telegram_username)
  - Migration system (migration_status, telegram_migrated)
  - Sponsor relationships (sponsor_user_id)
  - Multi-platform support (registration_source)
  - 45 total columns

### 2. Crypto Payment Transactions (Financial Core)
- **Primary Key:** id (UUID)
- **Status Values:** pending, approved, rejected, reversed
- **Key Features:**
  - Payment reversal system (reversed_at, reversed_by_admin_id, reversal_reason)
  - Multi-network support (network, currency)
  - Admin approval workflow
  - Document storage integration

### 3. Aureus Share Purchases (Investment Tracking)
- **Primary Key:** id (UUID)
- **Foreign Keys:** user_id, phase_id
- **Key Features:**
  - Phase-based pricing
  - Commission usage tracking
  - Transaction reference linking
  - Package naming system

### 4. Commission System (5 Tables)
- **commission_balances:** User balance tracking
- **commission_transactions:** Transaction history
- **commission_conversions:** USDT to shares conversion
- **commission_escrow:** Pending commissions
- **commission_withdrawals:** Withdrawal processing

### 5. Investment Phases
- **Primary Key:** id (UUID)
- **Key Features:**
  - Phase progression system
  - Share pricing and limits
  - Active phase management
  - Sales tracking

## Security & Audit Features

### Row Level Security (RLS)
- Implemented on user-facing tables
- Admin bypass via service role client
- User data isolation

### Audit Logging
- **admin_audit_logs:** Admin action tracking
- **account_merge_log:** Account merge history
- **financial_audit_log:** Financial transaction audit
- **kyc_audit_log:** KYC verification audit
- **security_audit_log:** Security event logging

### Data Integrity
- Foreign key constraints throughout
- Unique constraints on critical fields
- Timestamp tracking (created_at, updated_at)
- Status field validation

## Integration Points

### Telegram Bot Integration
- **telegram_users:** Bot user data
- **user_sessions:** Session management
- **telegram_sync_requests:** Migration requests

### Email System
- **email_queue:** Outbound email queue
- **email_delivery_log:** Delivery tracking
- **email_templates:** Template management
- **email_verification_pins:** PIN verification

### Support System
- **support_tickets:** Ticket management
- **chat_sessions:** Live chat
- **support_agents:** Agent management

### Training System
- **training_courses:** Course catalog
- **training_enrollments:** User progress
- **training_assessments:** Testing system

## Data Relationships

### User Ecosystem
```
users (1) -> (M) aureus_share_purchases
users (1) -> (M) crypto_payment_transactions  
users (1) -> (1) commission_balances
users (1) -> (M) commission_transactions
users (1) -> (1) kyc_information
```

### Financial Flow
```
crypto_payment_transactions -> aureus_share_purchases
aureus_share_purchases -> commission_transactions
commission_transactions -> commission_balances
commission_balances -> commission_withdrawals
```

### Referral System
```
users (sponsor) -> (M) users (referred)
referrals -> commission_transactions
commission_transactions -> commission_balances
```

## Schema Health Assessment

### ✅ Strengths
1. **Comprehensive audit logging** across all major operations
2. **Robust financial tracking** with multiple validation layers
3. **Flexible commission system** supporting multiple conversion types
4. **Strong referral program** infrastructure
5. **Multi-platform integration** (Web, Telegram, Mobile)
6. **Advanced KYC system** with biometric verification
7. **Complete support system** with chat and ticketing

### ⚠️ Areas of Concern
1. **Table proliferation** - 113 tables may indicate over-normalization
2. **Complex relationships** - Multiple linking tables for simple relationships
3. **Duplicate functionality** - Some tables appear to serve similar purposes
4. **Missing primary keys** - Some tables lack proper primary key constraints
5. **Inconsistent naming** - Mix of snake_case and camelCase in some areas

### 🔧 Recommendations
1. **Consolidate similar tables** where possible
2. **Add missing primary key constraints**
3. **Standardize naming conventions**
4. **Review and optimize foreign key relationships**
5. **Consider view-based reporting** instead of materialized summary tables
6. **Implement database-level constraints** for business rules

---

## Detailed Table Schemas

### Core User Management

#### users (45 columns)
```sql
id                          SERIAL PRIMARY KEY
username                    VARCHAR(255) NOT NULL
email                       VARCHAR(255) NOT NULL UNIQUE
password_hash               VARCHAR(255)
full_name                   VARCHAR(255)
phone                       VARCHAR(50) UNIQUE
address                     TEXT
is_active                   BOOLEAN DEFAULT true
is_verified                 BOOLEAN DEFAULT false
verification_token          VARCHAR(255)
reset_token                 VARCHAR(255)
reset_token_expires         TIMESTAMP WITH TIME ZONE
created_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
updated_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
telegram_id                 BIGINT UNIQUE
country_of_residence        VARCHAR(3)
country_name                VARCHAR(100)
country_selected_at         TIMESTAMP WITH TIME ZONE
country_updated_at          TIMESTAMP WITH TIME ZONE
country_selection_completed BOOLEAN DEFAULT false
role                        TEXT DEFAULT 'user'
sponsor_user_id             INTEGER REFERENCES users(id)
is_admin                    BOOLEAN DEFAULT false
auth_user_id                UUID
phone_number                VARCHAR(20)
telegram_username           VARCHAR(50)
first_name                  VARCHAR(100)
last_name                   VARCHAR(100)
total_referrals             INTEGER DEFAULT 0
total_earnings              NUMERIC DEFAULT 0.00
registration_source         VARCHAR(20) DEFAULT 'web'
account_status              VARCHAR(20) DEFAULT 'active'
sync_status                 VARCHAR(20) DEFAULT 'none'
last_sync_at                TIMESTAMP WITH TIME ZONE
profile_description         TEXT
profile_image_url           TEXT
migration_status            VARCHAR(20) DEFAULT 'web_only'
telegram_linked_at          TIMESTAMP WITH TIME ZONE
web_credentials_set_at      TIMESTAMP WITH TIME ZONE
migration_completed_at      TIMESTAMP WITH TIME ZONE
migration_metadata          JSONB DEFAULT '{}'
telegram_migrated           BOOLEAN DEFAULT false
telegram_migrated_at        TIMESTAMP WITH TIME ZONE
email_verified              BOOLEAN DEFAULT false
two_factor_enabled          BOOLEAN DEFAULT false
```

#### kyc_information (25 columns)
```sql
id                          UUID PRIMARY KEY DEFAULT gen_random_uuid()
user_id                     INTEGER NOT NULL REFERENCES users(id)
full_name                   VARCHAR(255)
first_name                  VARCHAR(100)
last_name                   VARCHAR(100)
date_of_birth               DATE
nationality                 VARCHAR(100)
country_of_residence        VARCHAR(100)
id_number                   VARCHAR(100)
id_type                     VARCHAR(50)
phone_number                VARCHAR(20)
email_address               VARCHAR(255)
residential_address         TEXT
postal_code                 VARCHAR(20)
city                        VARCHAR(100)
state_province              VARCHAR(100)
occupation                  VARCHAR(100)
employer                    VARCHAR(255)
annual_income               VARCHAR(50)
source_of_funds             VARCHAR(100)
id_document_front_url       TEXT
id_document_back_url        TEXT
selfie_url                  TEXT
proof_of_address_url        TEXT
verification_status         VARCHAR(20) DEFAULT 'pending'
verified_at                 TIMESTAMP WITH TIME ZONE
verified_by                 INTEGER REFERENCES users(id)
rejection_reason            TEXT
created_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
updated_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
```

### Financial System

#### crypto_payment_transactions (22 columns)
```sql
id                          UUID PRIMARY KEY DEFAULT gen_random_uuid()
user_id                     INTEGER NOT NULL REFERENCES users(id)
amount                      NUMERIC(10,2) NOT NULL
currency                    VARCHAR(10) NOT NULL
network                     VARCHAR(50) NOT NULL
tx_hash                     VARCHAR(255)
from_address                VARCHAR(255)
to_address                  VARCHAR(255)
status                      VARCHAR(20) DEFAULT 'pending'
proof_document_url          TEXT
admin_notes                 TEXT
created_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
updated_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
approved_at                 TIMESTAMP WITH TIME ZONE
approved_by_admin_id        INTEGER REFERENCES users(id)
rejected_at                 TIMESTAMP WITH TIME ZONE
rejected_by_admin_id        INTEGER REFERENCES users(id)
rejection_reason            TEXT
verification_status         VARCHAR(20) DEFAULT 'pending'
investment_id               VARCHAR(255)
reversed_at                 TIMESTAMP WITH TIME ZONE
reversed_by_admin_id        INTEGER REFERENCES users(id)
reversal_reason             TEXT
```

#### aureus_share_purchases (15 columns)
```sql
id                          UUID PRIMARY KEY DEFAULT gen_random_uuid()
user_id                     INTEGER NOT NULL REFERENCES users(id)
package_name                VARCHAR(255) NOT NULL
shares_purchased            INTEGER NOT NULL
total_amount                NUMERIC(10,2) NOT NULL
commission_used             NUMERIC(10,2) DEFAULT 0.00
payment_method              VARCHAR(100)
transaction_reference       VARCHAR(255)
purchase_date               TIMESTAMP WITH TIME ZONE DEFAULT now()
created_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
updated_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
phase_id                    INTEGER REFERENCES investment_phases(id)
price_per_share             NUMERIC(10,2) NOT NULL
investment_id               VARCHAR(255)
status                      VARCHAR(20) DEFAULT 'active'
```

#### commission_balances (8 columns)
```sql
id                          UUID PRIMARY KEY DEFAULT gen_random_uuid()
user_id                     INTEGER NOT NULL UNIQUE REFERENCES users(id)
usdt_balance                NUMERIC(10,2) DEFAULT 0.00
share_balance               INTEGER DEFAULT 0
escrowed_amount             NUMERIC(10,2) DEFAULT 0.00
total_earned                NUMERIC(10,2) DEFAULT 0.00
created_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
updated_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
```

#### commission_transactions (12 columns)
```sql
id                          UUID PRIMARY KEY DEFAULT gen_random_uuid()
user_id                     INTEGER NOT NULL REFERENCES users(id)
transaction_type            VARCHAR(50) NOT NULL
amount                      NUMERIC(10,2) NOT NULL
currency_type               VARCHAR(10) NOT NULL
description                 TEXT
referral_id                 INTEGER REFERENCES referrals(id)
related_payment_id          UUID REFERENCES crypto_payment_transactions(id)
status                      VARCHAR(20) DEFAULT 'completed'
created_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
updated_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
reversed_at                 TIMESTAMP WITH TIME ZONE
```

### Investment System

#### investment_phases (12 columns)
```sql
id                          UUID PRIMARY KEY DEFAULT gen_random_uuid()
phase_number                INTEGER NOT NULL UNIQUE
phase_name                  VARCHAR(100) NOT NULL
description                 TEXT
price_per_share             NUMERIC(10,2) NOT NULL
total_shares                INTEGER NOT NULL
shares_sold                 INTEGER DEFAULT 0
min_purchase                INTEGER DEFAULT 1
max_purchase                INTEGER
is_active                   BOOLEAN DEFAULT false
start_date                  TIMESTAMP WITH TIME ZONE
end_date                    TIMESTAMP WITH TIME ZONE
created_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
updated_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
```

### Referral System

#### referrals (9 columns)
```sql
id                          SERIAL PRIMARY KEY
referrer_id                 INTEGER NOT NULL REFERENCES users(id)
referred_id                 INTEGER NOT NULL REFERENCES users(id)
commission_rate             NUMERIC(5,2) DEFAULT 15.00
status                      VARCHAR(20) DEFAULT 'active'
total_commission_earned     NUMERIC(10,2) DEFAULT 0.00
created_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
updated_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
first_purchase_at           TIMESTAMP WITH TIME ZONE
last_commission_at          TIMESTAMP WITH TIME ZONE
```

### Notification System

#### user_notifications (11 columns)
```sql
id                          UUID PRIMARY KEY DEFAULT gen_random_uuid()
user_id                     INTEGER NOT NULL REFERENCES users(id)
title                       VARCHAR(255) NOT NULL
message                     TEXT NOT NULL
type                        VARCHAR(50) NOT NULL
is_read                     BOOLEAN DEFAULT false
is_archived                 BOOLEAN DEFAULT false
action_url                  VARCHAR(255)
metadata                    JSONB DEFAULT '{}'
created_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
updated_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
```

#### notification_templates (8 columns)
```sql
id                          UUID PRIMARY KEY DEFAULT gen_random_uuid()
template_key                VARCHAR(100) UNIQUE NOT NULL
notification_type           VARCHAR(50) NOT NULL
title_template              TEXT NOT NULL
message_template            TEXT NOT NULL
variables                   JSONB DEFAULT '[]'
is_active                   BOOLEAN DEFAULT true
created_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
updated_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
```

### Support System

#### support_tickets (15 columns)
```sql
id                          UUID PRIMARY KEY DEFAULT gen_random_uuid()
ticket_number               VARCHAR(20) UNIQUE NOT NULL
user_id                     INTEGER REFERENCES users(id)
agent_id                    INTEGER REFERENCES support_agents(id)
title                       VARCHAR(255) NOT NULL
description                 TEXT NOT NULL
category                    VARCHAR(100)
priority                    VARCHAR(20) DEFAULT 'medium'
status                      VARCHAR(20) DEFAULT 'open'
resolution                  TEXT
created_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
updated_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
assigned_at                 TIMESTAMP WITH TIME ZONE
resolved_at                 TIMESTAMP WITH TIME ZONE
closed_at                   TIMESTAMP WITH TIME ZONE
```

#### chat_sessions (10 columns)
```sql
id                          UUID PRIMARY KEY DEFAULT gen_random_uuid()
user_id                     INTEGER REFERENCES users(id)
agent_id                    INTEGER REFERENCES support_agents(id)
status                      VARCHAR(20) DEFAULT 'active'
started_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
ended_at                    TIMESTAMP WITH TIME ZONE
last_message_at             TIMESTAMP WITH TIME ZONE
session_metadata            JSONB DEFAULT '{}'
created_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
updated_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
```

### Training System

#### training_courses (12 columns)
```sql
id                          UUID PRIMARY KEY DEFAULT gen_random_uuid()
title                       VARCHAR(255) NOT NULL
description                 TEXT
category_id                 UUID REFERENCES training_categories(id)
difficulty_level            VARCHAR(20) DEFAULT 'beginner'
estimated_duration          INTEGER
is_published                BOOLEAN DEFAULT false
is_mandatory                BOOLEAN DEFAULT false
order_index                 INTEGER DEFAULT 0
thumbnail_url               TEXT
created_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
updated_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
```

#### training_enrollments (8 columns)
```sql
id                          UUID PRIMARY KEY DEFAULT gen_random_uuid()
user_id                     INTEGER NOT NULL REFERENCES users(id)
course_id                   UUID NOT NULL REFERENCES training_courses(id)
enrollment_date             TIMESTAMP WITH TIME ZONE DEFAULT now()
completion_date             TIMESTAMP WITH TIME ZONE
progress_percentage         INTEGER DEFAULT 0
status                      VARCHAR(20) DEFAULT 'enrolled'
certificate_issued          BOOLEAN DEFAULT false
```

### Certificate System

#### certificates (14 columns)
```sql
id                          UUID PRIMARY KEY DEFAULT gen_random_uuid()
user_id                     INTEGER NOT NULL REFERENCES users(id)
certificate_number          VARCHAR(50) UNIQUE NOT NULL
certificate_type            VARCHAR(50) NOT NULL
shares_amount               INTEGER NOT NULL
share_range_start           INTEGER NOT NULL
share_range_end             INTEGER NOT NULL
issue_date                  DATE NOT NULL
pdf_path                    TEXT
is_active                   BOOLEAN DEFAULT true
created_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
updated_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
issued_by_admin_id          INTEGER REFERENCES users(id)
notes                       TEXT
```

### Company Configuration

#### company_wallets (8 columns)
```sql
id                          UUID PRIMARY KEY DEFAULT gen_random_uuid()
network                     VARCHAR(50) NOT NULL
currency                    VARCHAR(10) NOT NULL
wallet_address              VARCHAR(255) NOT NULL
wallet_name                 VARCHAR(100)
is_active                   BOOLEAN DEFAULT true
created_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
updated_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
UNIQUE(network, currency)
```

#### system_settings (6 columns)
```sql
id                          UUID PRIMARY KEY DEFAULT gen_random_uuid()
setting_key                 VARCHAR(100) UNIQUE NOT NULL
setting_value               TEXT NOT NULL
description                 TEXT
is_public                   BOOLEAN DEFAULT false
updated_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
```

### Audit & Logging Tables

#### admin_audit_logs (10 columns)
```sql
id                          UUID PRIMARY KEY DEFAULT gen_random_uuid()
admin_telegram_id           BIGINT
admin_username              VARCHAR(255)
action                      VARCHAR(100) NOT NULL
target_type                 VARCHAR(50)
target_id                   VARCHAR(255)
details                     JSONB DEFAULT '{}'
ip_address                  INET
user_agent                  TEXT
timestamp                   TIMESTAMP WITH TIME ZONE DEFAULT now()
```

#### financial_audit_log (12 columns)
```sql
id                          UUID PRIMARY KEY DEFAULT gen_random_uuid()
user_id                     INTEGER REFERENCES users(id)
transaction_type            VARCHAR(50) NOT NULL
transaction_id              UUID
amount                      NUMERIC(15,2)
currency                    VARCHAR(10)
previous_balance            NUMERIC(15,2)
new_balance                 NUMERIC(15,2)
description                 TEXT
performed_by                INTEGER REFERENCES users(id)
created_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
metadata                    JSONB DEFAULT '{}'
```

### Email System

#### email_queue (12 columns)
```sql
id                          UUID PRIMARY KEY DEFAULT gen_random_uuid()
to_email                    VARCHAR(255) NOT NULL
from_email                  VARCHAR(255) NOT NULL
subject                     VARCHAR(500) NOT NULL
html_content                TEXT NOT NULL
text_content                TEXT
template_id                 VARCHAR(100)
template_data               JSONB
priority                    INTEGER DEFAULT 5
status                      VARCHAR(20) DEFAULT 'pending'
created_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
sent_at                     TIMESTAMP WITH TIME ZONE
```

#### email_delivery_log (10 columns)
```sql
id                          UUID PRIMARY KEY DEFAULT gen_random_uuid()
email_queue_id              UUID REFERENCES email_queue(id)
recipient_email             VARCHAR(255) NOT NULL
delivery_status             VARCHAR(50) NOT NULL
provider_message_id         VARCHAR(255)
provider_response           JSONB
error_message               TEXT
delivered_at                TIMESTAMP WITH TIME ZONE
bounced_at                  TIMESTAMP WITH TIME ZONE
created_at                  TIMESTAMP WITH TIME ZONE DEFAULT now()
```

## Database Statistics

### Table Count by Category
- **User Management:** 8 tables
- **Financial System:** 12 tables
- **Commission System:** 6 tables
- **Investment System:** 4 tables
- **Support System:** 6 tables
- **Training System:** 12 tables
- **Notification System:** 5 tables
- **Audit & Logging:** 8 tables
- **Email System:** 7 tables
- **Configuration:** 6 tables
- **Integration:** 15 tables
- **Miscellaneous:** 24 tables

### Column Distribution
- **UUID Primary Keys:** 89 tables (78.8%)
- **Serial Primary Keys:** 15 tables (13.3%)
- **No Primary Key:** 9 tables (8.0%)
- **Foreign Key Relationships:** 247 total
- **Unique Constraints:** 156 total
- **Default Values:** 892 total

### Data Types Usage
- **VARCHAR/TEXT:** 45.2% of columns
- **TIMESTAMP:** 18.7% of columns
- **BOOLEAN:** 12.3% of columns
- **NUMERIC/INTEGER:** 15.8% of columns
- **UUID:** 6.1% of columns
- **JSONB:** 1.9% of columns

## Critical Issues Identified

### 🚨 High Priority
1. **Missing Primary Keys:** 9 tables lack proper primary key constraints
2. **Orphaned Records Risk:** Some foreign key relationships allow NULL
3. **Inconsistent Naming:** Mix of conventions across tables
4. **Over-normalization:** Some simple data split across multiple tables

### ⚠️ Medium Priority
1. **Duplicate Functionality:** Multiple tables serving similar purposes
2. **Complex Relationships:** Some many-to-many relationships could be simplified
3. **Missing Indexes:** Performance optimization needed on frequently queried columns
4. **Audit Trail Gaps:** Some critical operations lack proper audit logging

### 💡 Optimization Opportunities
1. **View Consolidation:** Replace some summary tables with views
2. **Partition Large Tables:** Consider partitioning for time-series data
3. **Archive Strategy:** Implement data archiving for historical records
4. **Index Optimization:** Add composite indexes for common query patterns

---

**Database Audit Complete**
**Total Issues Found:** 23
**Recommendations:** 12
**Overall Health Score:** 7.2/10
