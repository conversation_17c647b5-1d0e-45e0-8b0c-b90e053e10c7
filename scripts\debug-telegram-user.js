#!/usr/bin/env node

/**
 * Debug Telegram User Login Issue
 * 
 * This script debugs the specific Telegram user login issue
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function debugTelegramUser() {
  const telegramId = 1393852532;
  
  console.log(`🔍 Debugging Telegram user: ${telegramId}\n`);

  try {
    // Step 1: Check telegram_users table
    console.log('📋 Step 1: Checking telegram_users table...');
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramId)
      .single();

    if (telegramError) {
      console.log(`   ❌ Error: ${telegramError.message}`);
      return;
    }

    console.log('   ✅ Telegram user found:');
    console.log(`   📧 Temp Email: ${telegramUser.temp_email || 'None'}`);
    console.log(`   🔗 User ID: ${telegramUser.user_id || 'None'}`);
    console.log(`   📝 Username: ${telegramUser.username}`);
    console.log(`   ✅ Registered: ${telegramUser.is_registered}`);

    // Step 2: Check linked users table record
    if (telegramUser.user_id) {
      console.log('\n📋 Step 2: Checking linked users table record...');
      const { data: linkedUser, error: linkedError } = await supabase
        .from('users')
        .select('*')
        .eq('id', telegramUser.user_id)
        .single();

      if (linkedError) {
        console.log(`   ❌ Error: ${linkedError.message}`);
      } else {
        console.log('   ✅ Linked user found:');
        console.log(`   📧 Email: ${linkedUser.email}`);
        console.log(`   👤 Username: ${linkedUser.username}`);
        console.log(`   📱 Telegram ID: ${linkedUser.telegram_id}`);
        console.log(`   🔐 Has Password Hash: ${linkedUser.password_hash ? 'Yes' : 'No'}`);
        
        // Step 3: Test auth login
        console.log('\n📋 Step 3: Testing Supabase auth login...');
        const authPassword = `telegram_${telegramId}_auth`;
        console.log(`   🔑 Trying password: ${authPassword}`);
        
        const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
          email: linkedUser.email,
          password: authPassword
        });

        if (authError) {
          console.log(`   ❌ Auth Error: ${authError.message}`);
          
          // Check if auth user exists
          console.log('\n📋 Step 4: Checking if Supabase auth user exists...');
          const { data: authUsers, error: listError } = await supabase.auth.admin.listUsers();
          
          if (!listError && authUsers) {
            const authUser = authUsers.users.find(u => u.email === linkedUser.email);
            if (authUser) {
              console.log('   ✅ Supabase auth user exists');
              console.log(`   🆔 Auth User ID: ${authUser.id}`);
              console.log(`   📧 Auth Email: ${authUser.email}`);
            } else {
              console.log('   ❌ Supabase auth user NOT found');
              console.log('   💡 This means the auth user was never created during profile completion');
            }
          }
        } else {
          console.log('   ✅ Auth login successful!');
          console.log(`   🆔 Auth User ID: ${authData.user.id}`);
        }
      }
    } else {
      console.log('\n⚠️ No linked user record - profile completion needed');
    }

    // Step 5: Recommendations
    console.log('\n🔧 Recommendations:');
    if (!telegramUser.user_id) {
      console.log('   📝 User needs to complete profile first');
    } else if (telegramUser.user_id) {
      console.log('   🔄 Try recreating the Supabase auth user');
      console.log('   🔑 Or reset the auth password');
    }

  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

// Run the debug
debugTelegramUser();
