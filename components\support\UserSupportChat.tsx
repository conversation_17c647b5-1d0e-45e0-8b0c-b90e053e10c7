import React, { useEffect, useState, useRef } from 'react'
import { getCurrentDbUserId, getDefaultAdminRecipientId, fetchConversation, sendSupportMessageAsUser, SupportMessage } from '../../lib/support'

interface Props { }

const UserSupportChat: React.FC<Props> = () => {
  const [loading, setLoading] = useState(true)
  const [userId, setUserId] = useState<number | null>(null)
  const [adminId, setAdminId] = useState<number | null>(null)
  const [messages, setMessages] = useState<SupportMessage[]>([])
  const [input, setInput] = useState('')
  const [subject] = useState('Support')
  const [error, setError] = useState<string | null>(null)
  const listRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const init = async () => {
      setLoading(true)
      setError(null)
      const uid = await getCurrentDbUserId()
      const aid = await getDefaultAdminRecipientId()
      setUserId(uid)
      setAdminId(aid)
      if (uid && aid) {
        const conv = await fetchConversation(uid, aid)
        setMessages(conv)
      }
      setLoading(false)
    }
    init()
  }, [])

  useEffect(() => {
    // Auto-scroll to bottom on new messages
    listRef.current?.scrollTo({ top: listRef.current.scrollHeight, behavior: 'smooth' })
  }, [messages])

  const send = async () => {
    if (!input.trim() || !userId || !adminId) return
    const text = input.trim()
    setInput('')
    // optimistic update
    setMessages(prev => [...prev, {
      id: `tmp_${Date.now()}`,
      sender_id: userId,
      recipient_id: adminId,
      subject,
      message: text,
      status: 'sent',
      created_at: new Date().toISOString()
    }])
    const res = await sendSupportMessageAsUser(userId, adminId, text, subject)
    if (!res.success) {
      setError(res.error || 'Failed to send message')
    } else {
      const conv = await fetchConversation(userId, adminId)
      setMessages(conv)
    }
  }

  if (loading) return <div className="text-gray-400">Loading support…</div>
  if (!userId || !adminId) return <div className="text-red-400">Support unavailable. No admin recipient found.</div>

  return (
    <div className="bg-gray-800 rounded-lg border border-gray-700 p-4">
      <h3 className="text-lg font-semibold text-white mb-2">💬 Chat with Support</h3>
      <div ref={listRef} className="h-64 overflow-y-auto bg-gray-900 rounded p-3 space-y-2">
        {messages.length === 0 && (
          <div className="text-gray-500 text-sm">No messages yet. Say hello and tell us how we can help.</div>
        )}
        {messages.map(m => (
          <div key={m.id} className={`max-w-[80%] px-3 py-2 rounded text-sm ${m.sender_id === userId ? 'bg-blue-600 text-white ml-auto' : 'bg-gray-700 text-gray-100'}`}>
            <div>{m.message}</div>
            <div className="text-[10px] opacity-70 mt-1">{new Date(m.created_at).toLocaleString()}</div>
          </div>
        ))}
      </div>
      {error && <div className="text-red-400 text-sm mt-2">{error}</div>}
      <div className="flex gap-2 mt-3">
        <input
          className="flex-1 px-3 py-2 bg-gray-900 border border-gray-700 rounded text-white"
          placeholder="Type your message…"
          value={input}
          onChange={e => setInput(e.target.value)}
          onKeyDown={e => { if (e.key === 'Enter') send() }}
        />
        <button onClick={send} className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-black font-semibold rounded">Send</button>
      </div>
      <div className="text-gray-500 text-xs mt-2">Messages go directly to an administrator. You’ll see replies here.</div>
    </div>
  )
}

export default UserSupportChat

