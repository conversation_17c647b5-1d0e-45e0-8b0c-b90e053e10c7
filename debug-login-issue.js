// Debug Login Issue Script
// This script helps diagnose issues with Telegram login and password verification

import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';

// Supabase configuration
const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Function to verify password
async function verifyPassword(password, hash) {
  try {
    const isValid = await bcrypt.compare(password, hash);
    console.log(`🔍 Password verification: ${isValid ? 'SUCCESS ✅' : 'FAILED ❌'}`);
    return isValid;
  } catch (error) {
    console.error('❌ Password verification error:', error);
    return false;
  }
}

// Function to check user by Telegram ID
async function checkUserByTelegramId(telegramId) {
  console.log(`\n🔍 Checking user with Telegram ID: ${telegramId}`);

  try {
    // First check telegram_users table
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramId)
      .single();

    if (telegramError) {
      console.error('❌ Error fetching telegram user:', telegramError);
      return;
    }

    if (!telegramUser) {
      console.log('❌ No telegram user found with ID:', telegramId);
      return;
    }

    console.log('✅ Telegram user found:');
    console.log(`   Username: ${telegramUser.username}`);
    console.log(`   First Name: ${telegramUser.first_name}`);
    console.log(`   Last Name: ${telegramUser.last_name || 'N/A'}`);
    console.log(`   User ID: ${telegramUser.user_id || 'Not linked'}`);
    console.log(`   Is Registered: ${telegramUser.is_registered ? 'Yes' : 'No'}`);
    console.log(`   Web Authenticated: ${telegramUser.web_authenticated ? 'Yes' : 'No'}`);

    // Check if user has a password hash directly in telegram_users
    if (telegramUser.password_hash) {
      console.log('✅ Password hash found directly in telegram_users table');

      // Test password verification
      const testPassword = 'Gunst0n5o0!@#'; // The correct password for this user
      const isValid = await verifyPassword(testPassword, telegramUser.password_hash);

      console.log(`   Test password '${testPassword}' verification: ${isValid ? 'SUCCESS ✅' : 'FAILED ❌'}`);
    } else {
      console.log('❌ No password hash in telegram_users table');
    }

    // Check linked user in users table if user_id exists
    if (telegramUser.user_id) {
      console.log('\n🔍 Checking linked user in users table...');

      const { data: linkedUser, error: linkedError } = await supabase
        .from('users')
        .select('*')
        .eq('id', telegramUser.user_id)
        .single();

      if (linkedError) {
        console.error('❌ Error fetching linked user:', linkedError);
        return;
      }

      if (!linkedUser) {
        console.log('❌ No linked user found with ID:', telegramUser.user_id);
        return;
      }

      console.log('✅ Linked user found:');
      console.log(`   Email: ${linkedUser.email}`);
      console.log(`   Username: ${linkedUser.username}`);
      console.log(`   Full Name: ${linkedUser.full_name}`);
      console.log(`   Has Password Hash: ${linkedUser.password_hash ? 'Yes' : 'No'}`);

      // Test password verification if hash exists
      if (linkedUser.password_hash) {
        const testPassword = 'Gunst0n5o0!@#'; // The correct password for this user
        const isValid = await verifyPassword(testPassword, linkedUser.password_hash);

        console.log(`   Test password '${testPassword}' verification: ${isValid ? 'SUCCESS ✅' : 'FAILED ❌'}`);
      }
    } else {
      console.log('❌ No linked user_id found in telegram_users record');

      // Try to find user by telegram_id in users table
      console.log('\n🔍 Checking for user by telegram_id in users table...');

      const { data: userByTelegramId, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('telegram_id', telegramId)
        .single();

      if (userError && userError.code !== 'PGRST116') {
        console.error('❌ Error fetching user by telegram_id:', userError);
      }

      if (userByTelegramId) {
        console.log('✅ User found by telegram_id in users table:');
        console.log(`   Email: ${userByTelegramId.email}`);
        console.log(`   Username: ${userByTelegramId.username}`);
        console.log(`   Full Name: ${userByTelegramId.full_name}`);
        console.log(`   Has Password Hash: ${userByTelegramId.password_hash ? 'Yes' : 'No'}`);

        // Test password verification if hash exists
        if (userByTelegramId.password_hash) {
          const testPassword = 'Gunst0n5o0!@#'; // The correct password for this user
          const isValid = await verifyPassword(testPassword, userByTelegramId.password_hash);

          console.log(`   Test password '${testPassword}' verification: ${isValid ? 'SUCCESS ✅' : 'FAILED ❌'}`);
        }
      } else {
        console.log('❌ No user found by telegram_id in users table');
      }
    }
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Main function
async function main() {
  const telegramId = process.argv[2] || '1270124602'; // Use command line arg or default

  console.log('🔧 DEBUG LOGIN ISSUE SCRIPT');
  console.log('==========================');
  console.log(`Running diagnostics for Telegram ID: ${telegramId}`);

  await checkUserByTelegramId(telegramId);

  console.log('\n✅ Diagnostics complete');
  process.exit(0);
}

// Run the script
main().catch(error => {
  console.error('❌ Fatal error:', error);
  process.exit(1);
});