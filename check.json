[
  {
    "schemaname": "public",
    "tablename": "account_links",
    "policyname": "Users can create their own account links",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "INSERT",
    "qual": null,
    "with_check": "(database_user_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))"
  },
  {
    "schemaname": "public",
    "tablename": "account_links",
    "policyname": "Users can view their own account links",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "(database_user_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "account_merge_log",
    "policyname": "Users can view their own merge history",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "((target_user_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid()))) OR (source_user_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid()))))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "admin_audit_logs",
    "policyname": "Admins can access audit logs",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "admin_audit_logs",
    "policyname": "Service role can manage admin_audit_logs",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "admin_notification_preferences",
    "policyname": "Admins can manage admin notification preferences",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "admin_notification_preferences",
    "policyname": "Service role can manage admin_notification_preferences",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "admin_users",
    "policyname": "Authenticated users can read admin_users",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "(auth.role() = 'authenticated'::text)",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "admin_users",
    "policyname": "Service role can manage admin_users",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(auth.role() = 'service_role'::text)",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "aureus_share_purchases",
    "policyname": "Users can insert own share purchases",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "INSERT",
    "qual": null,
    "with_check": "(user_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))"
  },
  {
    "schemaname": "public",
    "tablename": "aureus_share_purchases",
    "policyname": "Users can view own share purchases",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "(user_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "auth_tokens",
    "policyname": "Allow public access to auth_tokens",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "certificates",
    "policyname": "Service role can manage certificates",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "certificates",
    "policyname": "Users can access own certificates",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "commission_balances",
    "policyname": "Service role can manage commission_balances",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "commission_balances",
    "policyname": "Users can access own commission balances",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "commission_balances",
    "policyname": "Users can update own commission balance",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "UPDATE",
    "qual": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (auth.uid() = users.auth_user_id)))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "commission_balances",
    "policyname": "Users can view own commission balance",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "(user_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "commission_balances",
    "policyname": "Users can view their own commission balance",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.id = commission_balances.user_id) AND (users.auth_user_id = auth.uid()))))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "commission_conversions",
    "policyname": "Service role can manage commission_conversions",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "commission_conversions",
    "policyname": "Users can access own commission conversions",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "commission_escrow",
    "policyname": "Service role can manage commission_escrow",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "commission_escrow",
    "policyname": "Users can access own commission escrow",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "commission_transactions",
    "policyname": "Users can view own commission transactions",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "(referrer_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "commission_usage",
    "policyname": "Users can view own commission usage",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "(user_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "commission_withdrawal_requests",
    "policyname": "Users can create own withdrawal requests",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "INSERT",
    "qual": null,
    "with_check": "(user_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))"
  },
  {
    "schemaname": "public",
    "tablename": "commission_withdrawal_requests",
    "policyname": "Users can view own withdrawal requests",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "(user_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "commission_withdrawals",
    "policyname": "Service role can manage commission_withdrawals",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "commission_withdrawals",
    "policyname": "Users can access own commission withdrawals",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "company_wallets",
    "policyname": "Admins can access company wallets",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "company_wallets",
    "policyname": "Service role can manage company_wallets",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "country_change_log",
    "policyname": "Admins can access country change log",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "country_change_log",
    "policyname": "Service role can manage country_change_log",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "crypto_payment_transactions",
    "policyname": "Service role can manage crypto_payment_transactions",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "crypto_payment_transactions",
    "policyname": "Users can access own payment transactions",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "document_access_logs",
    "policyname": "Users can view their own document access logs",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "((auth.uid())::text = (user_id)::text)",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "financial_audit_log",
    "policyname": "Admins can access financial audit log",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "financial_audit_log",
    "policyname": "Service role can manage financial_audit_log",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "gallery_categories",
    "policyname": "Admin full access for gallery_categories",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "((auth.jwt() ->> 'email'::text) = ANY (ARRAY['<EMAIL>'::text, '<EMAIL>'::text]))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "gallery_categories",
    "policyname": "Public read access for gallery_categories",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "(is_active = true)",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "gallery_images",
    "policyname": "Admin full access for gallery_images",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "((auth.jwt() ->> 'email'::text) = ANY (ARRAY['<EMAIL>'::text, '<EMAIL>'::text]))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "gallery_images",
    "policyname": "Public read access for gallery_images",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "(is_active = true)",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "investment_phases",
    "policyname": "Authenticated users can read investment phases",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "(auth.role() = 'authenticated'::text)",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "investment_phases",
    "policyname": "Service role can manage investment_phases",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "kyc_audit_log",
    "policyname": "Users can view their own KYC audit logs",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "((auth.uid())::text = (user_id)::text)",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "kyc_information",
    "policyname": "Users can insert their own KYC data",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "INSERT",
    "qual": null,
    "with_check": "((auth.uid())::text = (user_id)::text)"
  },
  {
    "schemaname": "public",
    "tablename": "kyc_information",
    "policyname": "Users can update their own KYC data",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "UPDATE",
    "qual": "((auth.uid())::text = (user_id)::text)",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "kyc_information",
    "policyname": "Users can view their own KYC data",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "((auth.uid())::text = (user_id)::text)",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "marketing_content_generated",
    "policyname": "Users can create marketing content",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "INSERT",
    "qual": null,
    "with_check": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (auth.uid() = users.auth_user_id)))"
  },
  {
    "schemaname": "public",
    "tablename": "marketing_content_generated",
    "policyname": "Users can view own marketing content",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (auth.uid() = users.auth_user_id)))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "marketing_materials",
    "policyname": "Admins can manage marketing materials",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(EXISTS ( SELECT 1\n   FROM admin_users\n  WHERE (admin_users.email = (auth.jwt() ->> 'email'::text))))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "marketing_materials",
    "policyname": "Users can view active marketing materials",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "((is_active = true) AND (auth.role() = 'authenticated'::text))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "marketing_training_progress",
    "policyname": "Users can update own training progress",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (auth.uid() = users.auth_user_id)))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "marketing_training_progress",
    "policyname": "Users can view own training progress",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (auth.uid() = users.auth_user_id)))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "nda_acceptances",
    "policyname": "Users can view their own NDA acceptance",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "((auth.uid())::text = (user_id)::text)",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "notification_log",
    "policyname": "Admins can access notification log",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "notification_log",
    "policyname": "Service role can manage notification_log",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "notification_sound_types",
    "policyname": "Authenticated users can read notification sound types",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "(auth.role() = 'authenticated'::text)",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "notification_sound_types",
    "policyname": "Service role can manage notification_sound_types",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "payment_admin_notes",
    "policyname": "Admins can access payment admin notes",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "payment_admin_notes",
    "policyname": "Service role can manage payment_admin_notes",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "payment_allocations",
    "policyname": "Admins can manage payment allocations",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "payment_allocations",
    "policyname": "Service role can manage payment_allocations",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "referrals",
    "policyname": "Users can view own referrals",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "(referrer_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "section_balances",
    "policyname": "Admins can manage section balances",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "section_balances",
    "policyname": "Service role can manage section_balances",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "site_content",
    "policyname": "Admin users can manage site content",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "((auth.jwt() ->> 'email'::text) IN ( SELECT admin_users.email\n   FROM admin_users))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "site_content",
    "policyname": "Anyone can view site content",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "true",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "supported_countries",
    "policyname": "Admins can delete supported countries",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "DELETE",
    "qual": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "supported_countries",
    "policyname": "Admins can insert supported countries",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "INSERT",
    "qual": null,
    "with_check": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))"
  },
  {
    "schemaname": "public",
    "tablename": "supported_countries",
    "policyname": "Admins can update supported countries",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "UPDATE",
    "qual": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))"
  },
  {
    "schemaname": "public",
    "tablename": "supported_countries",
    "policyname": "Authenticated users can read supported countries",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "(auth.role() = 'authenticated'::text)",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "supported_countries",
    "policyname": "Service role can manage supported_countries",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "sync_notifications",
    "policyname": "Users can view their own sync notifications",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(user_id IN ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "system_settings",
    "policyname": "Admins can manage system settings",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "system_settings",
    "policyname": "Service role can manage system_settings",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "telegram_sync_requests",
    "policyname": "Users can view their own sync requests",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "((linked_web_user_id = auth.uid()) OR (telegram_id IN ( SELECT users.telegram_id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid()))))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "telegram_users",
    "policyname": "Service role can manage telegram_users",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "telegram_users",
    "policyname": "Users can access own telegram data",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "terms_acceptance",
    "policyname": "Service role can manage terms_acceptance",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "terms_acceptance",
    "policyname": "Users can manage own terms acceptance",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "test_connection",
    "policyname": "Service role can access test connection",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(auth.role() = 'service_role'::text)",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "test_user_exclusion_audit",
    "policyname": "Authenticated users can read audit logs",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "(auth.role() = 'authenticated'::text)",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "test_user_exclusion_audit",
    "policyname": "Service role can manage audit logs",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(auth.role() = 'service_role'::text)",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "user_messages",
    "policyname": "Users can send messages",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "INSERT",
    "qual": null,
    "with_check": "(sender_id = ( SELECT users.id\n   FROM users\n  WHERE (auth.uid() = users.auth_user_id)))"
  },
  {
    "schemaname": "public",
    "tablename": "user_messages",
    "policyname": "Users can view own messages",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "((sender_id = ( SELECT users.id\n   FROM users\n  WHERE (auth.uid() = users.auth_user_id))) OR (recipient_id = ( SELECT users.id\n   FROM users\n  WHERE (auth.uid() = users.auth_user_id))))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "user_notification_preferences",
    "policyname": "Service role can manage user_notification_preferences",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "user_notification_preferences",
    "policyname": "Users can manage own notification preferences",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "user_notification_summary",
    "policyname": "Users can view their own notification summary",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.id = user_notification_summary.user_id) AND (users.auth_user_id = auth.uid()))))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "user_notifications",
    "policyname": "Users can update own notifications",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "UPDATE",
    "qual": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (auth.uid() = users.auth_user_id)))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "user_notifications",
    "policyname": "Users can view own notifications",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (auth.uid() = users.auth_user_id)))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "user_payment_methods",
    "policyname": "Users can manage their own payment methods",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.id = user_payment_methods.user_id) AND (users.auth_user_id = auth.uid()))))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "user_preferences",
    "policyname": "Service role can manage user_preferences",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "user_preferences",
    "policyname": "Users can access own preferences",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(user_id = ( SELECT users.id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "user_sessions",
    "policyname": "Service role can manage user_sessions",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "user_sessions",
    "policyname": "Users can manage own sessions",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(telegram_id = ( SELECT users.telegram_id\n   FROM users\n  WHERE (users.auth_user_id = auth.uid())))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "users",
    "policyname": "Service role can manage users",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  },
  {
    "schemaname": "public",
    "tablename": "users",
    "policyname": "Users can access own data",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(auth_user_id = auth.uid())",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "withdrawal_invoices",
    "policyname": "Admins can manage withdrawal invoices",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "ALL",
    "qual": "(EXISTS ( SELECT 1\n   FROM users\n  WHERE ((users.auth_user_id = auth.uid()) AND (users.is_admin = true))))",
    "with_check": null
  },
  {
    "schemaname": "public",
    "tablename": "withdrawal_invoices",
    "policyname": "Service role can manage withdrawal_invoices",
    "permissive": "PERMISSIVE",
    "roles": "{service_role}",
    "cmd": "ALL",
    "qual": "true",
    "with_check": "true"
  }
]




[
  {
    "schema_name": "public",
    "function_name": "check_country_selection",
    "return_type": "boolean",
    "arguments": "p_user_id integer",
    "security_type": "SECURITY DEFINER"
  },
  {
    "schema_name": "public",
    "function_name": "check_kyc_completion",
    "return_type": "boolean",
    "arguments": "p_user_id integer",
    "security_type": "SECURITY DEFINER"
  },
  {
    "schema_name": "public",
    "function_name": "check_nda_acceptance",
    "return_type": "boolean",
    "arguments": "p_user_id integer",
    "security_type": "SECURITY DEFINER"
  },
  {
    "schema_name": "public",
    "function_name": "create_commission_escrow",
    "return_type": "json",
    "arguments": "p_user_id integer, p_request_amount numeric, p_request_type text",
    "security_type": "SECURITY DEFINER"
  },
  {
    "schema_name": "public",
    "function_name": "detect_account_matches",
    "return_type": "TABLE(match_type text, matched_user_id integer, confidence_score integer, match_details jsonb)",
    "arguments": "_user_id integer, _email text DEFAULT NULL::text, _telegram_id bigint DEFAULT NULL::bigint",
    "security_type": "SECURITY DEFINER"
  },
  {
    "schema_name": "public",
    "function_name": "get_all_countries",
    "return_type": "TABLE(country_code character varying, country_name character varying, flag_emoji character varying, is_primary boolean)",
    "arguments": "",
    "security_type": "SECURITY DEFINER"
  },
  {
    "schema_name": "public",
    "function_name": "get_certificate_timeline",
    "return_type": "text",
    "arguments": "",
    "security_type": "SECURITY DEFINER"
  },
  {
    "schema_name": "public",
    "function_name": "get_primary_countries",
    "return_type": "TABLE(country_code character varying, country_name character varying, flag_emoji character varying)",
    "arguments": "",
    "security_type": "SECURITY DEFINER"
  },
  {
    "schema_name": "public",
    "function_name": "get_user_audio_preferences",
    "return_type": "json",
    "arguments": "p_telegram_id bigint",
    "security_type": "SECURITY DEFINER"
  },
  {
    "schema_name": "public",
    "function_name": "handle_new_user_profile",
    "return_type": "trigger",
    "arguments": "",
    "security_type": "SECURITY DEFINER"
  },
  {
    "schema_name": "public",
    "function_name": "hash_id_number",
    "return_type": "character varying",
    "arguments": "p_id_number text",
    "security_type": "SECURITY DEFINER"
  },
  {
    "schema_name": "public",
    "function_name": "log_document_access",
    "return_type": "uuid",
    "arguments": "p_user_id integer, p_document_type character varying, p_document_url text, p_telegram_user_id bigint DEFAULT NULL::bigint, p_username character varying DEFAULT NULL::character varying",
    "security_type": "SECURITY DEFINER"
  },
  {
    "schema_name": "public",
    "function_name": "log_kyc_action",
    "return_type": "uuid",
    "arguments": "p_kyc_id uuid, p_user_id integer, p_action character varying, p_field_changed character varying DEFAULT NULL::character varying, p_telegram_id bigint DEFAULT NULL::bigint, p_username character varying DEFAULT NULL::character varying",
    "security_type": "SECURITY DEFINER"
  },
  {
    "schema_name": "public",
    "function_name": "merge_user_accounts",
    "return_type": "jsonb",
    "arguments": "_source_user_id integer, _target_user_id integer, _performed_by integer",
    "security_type": "SECURITY DEFINER"
  },
  {
    "schema_name": "public",
    "function_name": "process_commission_conversion",
    "return_type": "json",
    "arguments": "p_conversion_id uuid, p_admin_telegram_id bigint",
    "security_type": "SECURITY DEFINER"
  },
  {
    "schema_name": "public",
    "function_name": "release_commission_escrow",
    "return_type": "json",
    "arguments": "p_user_id integer, p_escrow_amount numeric",
    "security_type": "SECURITY DEFINER"
  },
  {
    "schema_name": "public",
    "function_name": "update_user_audio_preferences",
    "return_type": "boolean",
    "arguments": "p_telegram_id bigint, p_user_id integer, p_preferences json",
    "security_type": "SECURITY DEFINER"
  },
  {
    "schema_name": "public",
    "function_name": "update_user_auth_id",
    "return_type": "boolean",
    "arguments": "p_email text, p_auth_user_id uuid",
    "security_type": "SECURITY DEFINER"
  },
  {
    "schema_name": "public",
    "function_name": "update_user_country",
    "return_type": "boolean",
    "arguments": "p_user_id integer, p_country_code character varying, p_country_name character varying",
    "security_type": "SECURITY DEFINER"
  }
]