import React, { useState, useEffect } from 'react'
import LiveChatWidget from '../support/LiveChatWidget'
import { getCurrentUser } from '../../lib/supabase'

interface LiveChatProviderProps {
  children: React.ReactNode
}

/**
 * LiveChatProvider - Provides live chat functionality across the entire website
 * 
 * This component should be wrapped around your main app or layout to provide
 * the live chat widget on all pages. It automatically detects user authentication
 * status and shows/hides the widget accordingly.
 * 
 * Usage:
 * ```tsx
 * <LiveChatProvider>
 *   <YourAppContent />
 * </LiveChatProvider>
 * ```
 */
const LiveChatProvider: React.FC<LiveChatProviderProps> = ({ children }) => {
  const [chatOpen, setChatOpen] = useState(false)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    checkAuthStatus()
  }, [])

  const checkAuthStatus = async () => {
    try {
      const user = await getCurrentUser()
      setIsAuthenticated(!!user)
    } catch (error) {
      console.error('Error checking auth status:', error)
      setIsAuthenticated(false)
    } finally {
      setLoading(false)
    }
  }

  // Don't show chat widget if user is not authenticated or still loading
  if (loading || !isAuthenticated) {
    return <>{children}</>
  }

  return (
    <>
      {children}
      <LiveChatWidget 
        isOpen={chatOpen} 
        onToggle={() => setChatOpen(!chatOpen)}
        position="bottom-right" 
      />
    </>
  )
}

export default LiveChatProvider
