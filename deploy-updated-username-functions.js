/**
 * DEPLOY UPDATED USERNAME FUNCTIONS
 * 
 * This script deploys the updated username functions that include
 * referral code synchronization to the Supabase database.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';

// Load environment variables
dotenv.config();

// Initialize Supabase client with service role
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function deployUpdatedUsernameFunctions() {
  console.log('🚀 DEPLOYING UPDATED USERNAME FUNCTIONS');
  console.log('=======================================');
  
  try {
    // ===== PART 1: DEPLOY UPDATED ATOMIC FUNCTION =====
    console.log('\n📋 PART 1: Deploying updated atomic username function');
    
    const updatedAtomicFunction = `
      -- Updated atomic username function with referral code sync
      CREATE OR REPLACE FUNCTION update_username_atomic(
        p_user_id INTEGER,
        p_new_username VARCHAR(255)
      )
      RETURNS VOID AS $$
      DECLARE
        v_old_username VARCHAR(255);
        v_telegram_user_exists BOOLEAN := FALSE;
        v_referrals_updated INTEGER := 0;
      BEGIN
        -- Start transaction (implicit in function)
        
        -- Get current username for logging
        SELECT username INTO v_old_username 
        FROM users 
        WHERE id = p_user_id;
        
        IF v_old_username IS NULL THEN
          RAISE EXCEPTION 'User with ID % not found', p_user_id;
        END IF;
        
        -- Check if username is already taken by another user
        IF EXISTS (
          SELECT 1 FROM users 
          WHERE username = p_new_username 
          AND id != p_user_id
        ) THEN
          RAISE EXCEPTION 'Username % is already taken', p_new_username;
        END IF;
        
        -- Update users table
        UPDATE users 
        SET 
          username = p_new_username,
          updated_at = NOW()
        WHERE id = p_user_id;
        
        IF NOT FOUND THEN
          RAISE EXCEPTION 'Failed to update username for user ID %', p_user_id;
        END IF;
        
        -- Check if user has telegram account
        SELECT EXISTS (
          SELECT 1 FROM telegram_users 
          WHERE user_id = p_user_id
        ) INTO v_telegram_user_exists;
        
        -- Update telegram_users table if record exists
        IF v_telegram_user_exists THEN
          UPDATE telegram_users 
          SET 
            username = p_new_username,
            updated_at = NOW()
          WHERE user_id = p_user_id;
          
          -- Log telegram update
          RAISE NOTICE 'Updated telegram_users username for user %', p_user_id;
        ELSE
          -- Log that no telegram account exists
          RAISE NOTICE 'No telegram account found for user %', p_user_id;
        END IF;
        
        -- Update referral codes where this user is the referrer
        UPDATE referrals 
        SET 
          referral_code = REPLACE(referral_code, v_old_username, p_new_username),
          updated_at = NOW()
        WHERE referrer_id = p_user_id 
        AND referral_code LIKE v_old_username || '_%';
        
        -- Get count of updated referrals
        GET DIAGNOSTICS v_referrals_updated = ROW_COUNT;
        
        -- Log referral updates
        IF v_referrals_updated > 0 THEN
          RAISE NOTICE 'Updated % referral codes for user %', v_referrals_updated, p_user_id;
        ELSE
          RAISE NOTICE 'No referral codes to update for user %', p_user_id;
        END IF;
        
        -- Log successful update
        RAISE NOTICE 'Username updated successfully: % -> % for user % (% referrals updated)', 
          v_old_username, p_new_username, p_user_id, v_referrals_updated;
        
      EXCEPTION
        WHEN OTHERS THEN
          -- Log error and re-raise
          RAISE EXCEPTION 'Username update failed for user %: %', p_user_id, SQLERRM;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
      
      -- Grant permissions
      GRANT EXECUTE ON FUNCTION update_username_atomic(INTEGER, VARCHAR) TO authenticated;
      GRANT EXECUTE ON FUNCTION update_username_atomic(INTEGER, VARCHAR) TO service_role;
    `;
    
    // Try to execute the function creation
    try {
      // We'll use a direct SQL approach since rpc might not work
      console.log('🔄 Attempting to create/update atomic function...');
      
      // Create a simple test to see if we can execute SQL
      const { data: testResult, error: testError } = await supabase
        .from('users')
        .select('count')
        .limit(1);
        
      if (testError) {
        console.log('❌ Database connection test failed:', testError.message);
        return false;
      }
      
      console.log('✅ Database connection verified');
      console.log('⚠️  Cannot execute SQL functions directly via Supabase client');
      console.log('📝 Please run the following SQL in your Supabase dashboard:');
      console.log('');
      console.log('='.repeat(80));
      console.log(updatedAtomicFunction);
      console.log('='.repeat(80));
      
    } catch (error) {
      console.log('❌ Error deploying atomic function:', error.message);
    }
    
    // ===== PART 2: TEST CURRENT FUNCTION =====
    console.log('\n📋 PART 2: Testing current atomic function');
    
    try {
      // Test with a non-existent user to see if function exists
      const { error: testError } = await supabase.rpc('update_username_atomic', {
        p_user_id: 999999,
        p_new_username: 'test_user_' + Date.now()
      });
      
      if (testError) {
        if (testError.message.includes('User with ID 999999 not found')) {
          console.log('✅ Atomic function exists and working');
        } else if (testError.message.includes('Could not find the function')) {
          console.log('❌ Atomic function does not exist in database');
        } else {
          console.log('⚠️ Atomic function test result:', testError.message);
        }
      } else {
        console.log('⚠️ Unexpected success with non-existent user');
      }
    } catch (error) {
      console.log('❌ Error testing atomic function:', error.message);
    }
    
    // ===== PART 3: PROVIDE DEPLOYMENT INSTRUCTIONS =====
    console.log('\n📋 PART 3: Deployment Instructions');
    console.log('==================================');
    
    console.log('To complete the username synchronization system deployment:');
    console.log('');
    console.log('1. 🌐 Go to your Supabase dashboard');
    console.log('2. 📊 Navigate to SQL Editor');
    console.log('3. 📝 Copy and paste the SQL from the output above');
    console.log('4. ▶️  Execute the SQL to create/update the functions');
    console.log('5. 🧪 Test the system by changing a username in the app');
    console.log('');
    console.log('Alternative files to run:');
    console.log('• database/username-update-functions.sql (updated version)');
    console.log('• username-sync-system.sql (complete system with triggers)');
    console.log('');
    console.log('After deployment, username changes will automatically:');
    console.log('✅ Update the users table');
    console.log('✅ Update the telegram_users table (if exists)');
    console.log('✅ Update all referral codes in referrals table');
    console.log('✅ Maintain system consistency');
    console.log('✅ Provide audit logging');
    
    // ===== PART 4: CREATE VERIFICATION SCRIPT =====
    console.log('\n📋 PART 4: Creating verification script');
    
    const verificationScript = `
-- VERIFICATION SCRIPT FOR USERNAME FUNCTIONS
-- Run this after deploying the functions to verify they work correctly

-- 1. Check if functions exist
SELECT 
  proname as function_name,
  pg_get_function_identity_arguments(oid) as arguments,
  prosrc LIKE '%referral%' as includes_referral_logic
FROM pg_proc 
WHERE proname IN ('update_username_atomic', 'sync_username_changes', 'validate_username_change')
ORDER BY proname;

-- 2. Test validation function (if it exists)
SELECT validate_username_change(139, 'TEST_USERNAME_' || extract(epoch from now())::text);

-- 3. Check for triggers
SELECT 
  trigger_name,
  event_manipulation,
  event_object_table,
  action_statement
FROM information_schema.triggers 
WHERE trigger_name LIKE '%username%' OR action_statement LIKE '%username%';

-- 4. Sample referral codes to verify format
SELECT 
  r.id,
  r.referrer_id,
  u.username,
  r.referral_code,
  CASE 
    WHEN r.referral_code LIKE u.username || '_%' THEN 'CONSISTENT'
    ELSE 'INCONSISTENT'
  END as status
FROM referrals r
JOIN users u ON r.referrer_id = u.id
LIMIT 10;
    `;
    
    // Write verification script to file
    fs.writeFileSync('verify-username-functions.sql', verificationScript.trim());
    console.log('✅ Created verify-username-functions.sql');
    
    console.log('\n🎉 DEPLOYMENT PREPARATION COMPLETE!');
    console.log('===================================');
    console.log('✅ Updated atomic function ready for deployment');
    console.log('✅ Verification script created');
    console.log('✅ Instructions provided');
    console.log('');
    console.log('Next steps:');
    console.log('1. Run the SQL in Supabase dashboard');
    console.log('2. Run verify-username-functions.sql to test');
    console.log('3. Test username changes in the application');
    
    return true;
    
  } catch (error) {
    console.error('💥 Fatal error during deployment preparation:', error);
    return false;
  }
}

// Run the deployment preparation
deployUpdatedUsernameFunctions()
  .then((success) => {
    if (success) {
      console.log('\n✅ Deployment preparation completed successfully!');
    } else {
      console.log('\n❌ Deployment preparation encountered errors');
    }
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error('\n💥 Deployment preparation failed:', error);
    process.exit(1);
  });
