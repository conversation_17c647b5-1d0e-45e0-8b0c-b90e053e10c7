#!/usr/bin/env node

/**
 * BUSINESS SECURITY TESTING SCRIPT
 * 
 * This script tests the business security implementations to ensure
 * financial data is protected against manipulation and unauthorized access.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

class BusinessSecurityTester {
  constructor() {
    this.testResults = {
      totalTests: 0,
      passed: 0,
      failed: 0,
      criticalFailures: 0,
      errors: []
    };
  }

  async runBusinessSecurityTests() {
    console.log('🛡️ BUSINESS SECURITY TESTING');
    console.log('============================\n');
    console.log('🚨 Testing CRITICAL business security implementations');
    console.log('💰 Focus: Financial data protection and access control\n');

    try {
      await this.testRowLevelSecurity();
      await this.testFinancialDataAccess();
      await this.testAdminAccessControls();
      await this.testBusinessLogicValidation();
      await this.testAuditTrailSecurity();
      
      this.generateSecurityTestReport();
      
    } catch (error) {
      console.error('❌ Business security test suite failed:', error);
    }
  }

  async testRowLevelSecurity() {
    console.log('🔒 Testing Row Level Security (RLS) Implementation');
    console.log('==================================================');
    this.testResults.totalTests++;

    try {
      // Test if RLS is enabled on critical tables
      const criticalTables = [
        'aureus_share_purchases',
        'commission_balances', 
        'commission_transactions',
        'crypto_payment_transactions',
        'investment_phases',
        'company_wallets'
      ];

      let rlsEnabled = 0;
      let rlsDisabled = 0;

      for (const table of criticalTables) {
        try {
          // Check if RLS is enabled (this would normally require a custom function)
          // For now, we'll test by trying to access data without proper context
          const { data, error } = await supabase
            .from(table)
            .select('*')
            .limit(1);

          if (error) {
            console.log(`   ✅ ${table}: RLS appears to be working (access restricted)`);
            rlsEnabled++;
          } else {
            console.log(`   ❌ ${table}: RLS may not be properly configured (data accessible)`);
            rlsDisabled++;
          }
        } catch (err) {
          console.log(`   ✅ ${table}: RLS working (access denied)`);
          rlsEnabled++;
        }
      }

      if (rlsDisabled > 0) {
        this.testResults.criticalFailures++;
        this.testResults.errors.push(`${rlsDisabled} critical tables lack proper RLS protection`);
        console.log(`❌ CRITICAL FAILURE: ${rlsDisabled} tables lack RLS protection`);
      } else {
        console.log('✅ RLS test PASSED - All critical tables appear protected');
        this.testResults.passed++;
      }

    } catch (error) {
      console.log('❌ RLS test FAILED:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`RLS test error: ${error.message}`);
    }
  }

  async testFinancialDataAccess() {
    console.log('\n💰 Testing Financial Data Access Controls');
    console.log('=========================================');
    this.testResults.totalTests++;

    try {
      // Test unauthorized access to financial data
      let unauthorizedAccess = 0;
      let protectedAccess = 0;

      // Test share purchases access
      try {
        const { data: shares, error } = await supabase
          .from('aureus_share_purchases')
          .select('user_id, shares_purchased, total_amount')
          .limit(5);

        if (!error && shares && shares.length > 0) {
          console.log(`   ⚠️ Share purchases accessible: ${shares.length} records`);
          unauthorizedAccess++;
        } else {
          console.log('   ✅ Share purchases properly protected');
          protectedAccess++;
        }
      } catch (err) {
        console.log('   ✅ Share purchases access denied');
        protectedAccess++;
      }

      // Test commission balances access
      try {
        const { data: commissions, error } = await supabase
          .from('commission_balances')
          .select('user_id, usdt_balance, share_balance')
          .limit(5);

        if (!error && commissions && commissions.length > 0) {
          console.log(`   ❌ CRITICAL: Commission balances accessible: ${commissions.length} records`);
          console.log('   💀 BUSINESS RISK: Commission balances can be viewed/modified');
          unauthorizedAccess++;
          this.testResults.criticalFailures++;
        } else {
          console.log('   ✅ Commission balances properly protected');
          protectedAccess++;
        }
      } catch (err) {
        console.log('   ✅ Commission balances access denied');
        protectedAccess++;
      }

      // Test payment transactions access
      try {
        const { data: payments, error } = await supabase
          .from('crypto_payment_transactions')
          .select('user_id, amount, status')
          .limit(5);

        if (!error && payments && payments.length > 0) {
          console.log(`   ❌ CRITICAL: Payment transactions accessible: ${payments.length} records`);
          unauthorizedAccess++;
          this.testResults.criticalFailures++;
        } else {
          console.log('   ✅ Payment transactions properly protected');
          protectedAccess++;
        }
      } catch (err) {
        console.log('   ✅ Payment transactions access denied');
        protectedAccess++;
      }

      if (unauthorizedAccess > 0) {
        this.testResults.failed++;
        this.testResults.errors.push(`${unauthorizedAccess} financial data sources are unprotected`);
        console.log(`❌ CRITICAL FAILURE: ${unauthorizedAccess} financial data sources unprotected`);
      } else {
        console.log('✅ Financial data access test PASSED');
        this.testResults.passed++;
      }

    } catch (error) {
      console.log('❌ Financial data access test FAILED:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Financial data access test error: ${error.message}`);
    }
  }

  async testAdminAccessControls() {
    console.log('\n👑 Testing Admin Access Controls');
    console.log('================================');
    this.testResults.totalTests++;

    try {
      // Test admin user table access
      const { data: adminUsers, error } = await supabase
        .from('admin_users')
        .select('id, role, is_active')
        .limit(5);

      if (error) {
        console.log('   ✅ Admin users table properly protected');
      } else {
        console.log(`   ⚠️ Admin users accessible: ${adminUsers?.length || 0} records`);
        if (adminUsers && adminUsers.length > 0) {
          console.log('   📋 Admin users found:');
          adminUsers.forEach(admin => {
            console.log(`      - ID: ${admin.id}, Role: ${admin.role}, Active: ${admin.is_active}`);
          });
        }
      }

      // Test audit logs access
      const { data: auditLogs, error: auditError } = await supabase
        .from('admin_audit_logs')
        .select('action, target_type, created_at')
        .limit(5);

      if (auditError) {
        console.log('   ✅ Audit logs properly protected');
      } else {
        console.log(`   ⚠️ Audit logs accessible: ${auditLogs?.length || 0} records`);
      }

      console.log('✅ Admin access controls test COMPLETED');
      this.testResults.passed++;

    } catch (error) {
      console.log('❌ Admin access controls test FAILED:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Admin access test error: ${error.message}`);
    }
  }

  async testBusinessLogicValidation() {
    console.log('\n💼 Testing Business Logic Validation');
    console.log('====================================');
    this.testResults.totalTests++;

    try {
      // Test current phase data
      const { data: currentPhase, error } = await supabase
        .from('investment_phases')
        .select('*')
        .eq('is_active', true)
        .single();

      if (error) {
        console.log('   ⚠️ No active investment phase found');
      } else {
        console.log('   📊 Current phase data:');
        console.log(`      Phase: ${currentPhase.phase_name}`);
        console.log(`      Price per share: $${currentPhase.price_per_share}`);
        console.log(`      Shares sold: ${currentPhase.shares_sold || 0}/${currentPhase.total_shares}`);
        console.log(`      Status: ${currentPhase.is_active ? 'Active' : 'Inactive'}`);
      }

      // Test company wallets access
      const { data: wallets, error: walletError } = await supabase
        .from('company_wallets')
        .select('network, wallet_address')
        .limit(3);

      if (walletError) {
        console.log('   ✅ Company wallets properly protected');
      } else {
        console.log(`   ❌ CRITICAL: Company wallets accessible: ${wallets?.length || 0} records`);
        if (wallets && wallets.length > 0) {
          console.log('   💀 BUSINESS RISK: Company wallet addresses exposed');
          this.testResults.criticalFailures++;
        }
      }

      console.log('✅ Business logic validation test COMPLETED');
      this.testResults.passed++;

    } catch (error) {
      console.log('❌ Business logic validation test FAILED:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Business logic test error: ${error.message}`);
    }
  }

  async testAuditTrailSecurity() {
    console.log('\n📋 Testing Audit Trail Security');
    console.log('===============================');
    this.testResults.totalTests++;

    try {
      // Test if audit logs are being created
      const { data: recentLogs, error } = await supabase
        .from('admin_audit_logs')
        .select('id, action, target_type, created_at')
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) {
        console.log('   ⚠️ Audit logs access restricted (good for security)');
      } else {
        console.log(`   📋 Recent audit logs: ${recentLogs?.length || 0} entries`);
        if (recentLogs && recentLogs.length > 0) {
          console.log('   📋 Recent audit activities:');
          recentLogs.slice(0, 5).forEach(log => {
            console.log(`      - ${log.action} on ${log.target_type} at ${new Date(log.created_at).toLocaleString()}`);
          });
        }
      }

      console.log('✅ Audit trail security test COMPLETED');
      this.testResults.passed++;

    } catch (error) {
      console.log('❌ Audit trail security test FAILED:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Audit trail test error: ${error.message}`);
    }
  }

  generateSecurityTestReport() {
    console.log('\n🛡️ BUSINESS SECURITY TEST REPORT');
    console.log('=================================');
    
    const totalTests = this.testResults.totalTests;
    const successRate = ((this.testResults.passed / totalTests) * 100).toFixed(1);
    
    console.log(`📊 Total Tests: ${totalTests}`);
    console.log(`✅ Passed: ${this.testResults.passed}`);
    console.log(`❌ Failed: ${this.testResults.failed}`);
    console.log(`🚨 Critical Failures: ${this.testResults.criticalFailures}`);
    console.log(`📈 Success Rate: ${successRate}%`);

    // Calculate business risk score
    const riskScore = (this.testResults.criticalFailures * 50) + (this.testResults.failed * 20);
    console.log(`\n💀 BUSINESS RISK SCORE: ${riskScore}/100`);

    if (riskScore >= 75) {
      console.log('🚨 EXTREME RISK - BUSINESS COULD BE DESTROYED');
    } else if (riskScore >= 50) {
      console.log('⚠️ HIGH RISK - SIGNIFICANT BUSINESS THREAT');
    } else if (riskScore >= 25) {
      console.log('📋 MEDIUM RISK - BUSINESS VULNERABILITIES PRESENT');
    } else {
      console.log('✅ LOW RISK - BUSINESS RELATIVELY SECURE');
    }

    if (this.testResults.errors.length > 0) {
      console.log('\n❌ SECURITY ISSUES FOUND:');
      this.testResults.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }

    if (this.testResults.criticalFailures > 0) {
      console.log('\n🚨 CRITICAL SECURITY FAILURES DETECTED!');
      console.log('========================================');
      console.log('💀 IMMEDIATE ACTIONS REQUIRED:');
      console.log('1. 🚨 IMPLEMENT ROW LEVEL SECURITY on all financial tables');
      console.log('2. 🚨 RESTRICT DATABASE ACCESS to authorized users only');
      console.log('3. 🚨 SECURE ADMIN FUNCTIONS with proper authentication');
      console.log('4. 🚨 PROTECT COMPANY WALLET ADDRESSES from exposure');
      console.log('5. 🚨 IMPLEMENT COMPREHENSIVE AUDIT LOGGING');
      
      console.log('\n💀 BUSINESS IMPACT IF NOT FIXED:');
      console.log('• Financial data can be manipulated directly');
      console.log('• Commission balances can be artificially inflated');
      console.log('• Share purchases can be created without payment');
      console.log('• Company funds could be stolen');
      console.log('• Business could face bankruptcy through fraud');
      
      console.log('\n🚨 DO NOT LAUNCH TO PRODUCTION UNTIL FIXED!');
    } else {
      console.log('\n✅ BUSINESS SECURITY STATUS: ACCEPTABLE');
      console.log('Basic security measures appear to be in place.');
      console.log('Continue monitoring and improving security posture.');
    }

    console.log('\n📋 NEXT STEPS:');
    console.log('1. Review and fix any critical failures immediately');
    console.log('2. Implement the emergency security SQL script');
    console.log('3. Test all admin functions with proper authorization');
    console.log('4. Set up continuous security monitoring');
    console.log('5. Conduct regular security audits');
  }
}

// Run the business security test suite
const tester = new BusinessSecurityTester();
tester.runBusinessSecurityTests().catch(console.error);
