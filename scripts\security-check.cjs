#!/usr/bin/env node

/**
 * AUREUS ALLIANCE HOLDINGS - SECURITY CHECK SCRIPT
 * 
 * This script verifies that sensitive files are properly excluded from Git
 * and helps prevent accidental commits of sensitive data.
 * 
 * Run this before committing: npm run security-check
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔒 AUREUS SECURITY CHECK - Verifying sensitive files are protected...\n');

// List of sensitive files that should NEVER be committed
const SENSITIVE_FILES = [
  '.env',
  '.env.local',
  '.env.development',
  '.env.production',
  '.env.test',
  'secrets.json',
  'credentials.json',
  'config/production.json',
  'config/local.json',
  'currentdatabase.json',
  'aureus-bot-new.js'
];

// List of sensitive patterns that should be ignored
const SENSITIVE_PATTERNS = [
  /\.env\./,
  /\.key$/,
  /\.pem$/,
  /\.crt$/,
  /\.cert$/,
  /\.p12$/,
  /\.pfx$/,
  /\.jks$/,
  /\.keystore$/,
  /id_rsa/,
  /id_ed25519/,
  /\.gpg$/,
  /\.asc$/,
  /\.sql$/,
  /\.dump$/,
  /\.backup$/
];

let hasIssues = false;

// Check if sensitive files exist and are properly ignored
console.log('📋 Checking sensitive files...');
for (const file of SENSITIVE_FILES) {
  if (fs.existsSync(file)) {
    try {
      // Check if file is tracked by Git
      execSync(`git ls-files --error-unmatch "${file}"`, { stdio: 'pipe' });
      console.log(`❌ SECURITY RISK: ${file} is tracked by Git!`);
      console.log(`   Run: git rm --cached "${file}" to remove from tracking`);
      hasIssues = true;
    } catch (error) {
      // File is not tracked (good)
      console.log(`✅ ${file} exists but is properly ignored`);
    }
  }
}

// Check staged files for sensitive content
console.log('\n📋 Checking staged files for sensitive content...');
try {
  const stagedFiles = execSync('git diff --cached --name-only', { encoding: 'utf8' })
    .split('\n')
    .filter(file => file.trim());

  if (stagedFiles.length === 0) {
    console.log('ℹ️  No files staged for commit');
  } else {
    for (const file of stagedFiles) {
      if (!file.trim()) continue;
      
      // Check against sensitive patterns
      const isSensitive = SENSITIVE_PATTERNS.some(pattern => pattern.test(file));
      if (isSensitive) {
        console.log(`❌ SECURITY RISK: Sensitive file staged: ${file}`);
        hasIssues = true;
      } else {
        console.log(`✅ ${file} - OK`);
      }
      
      // Check file content for sensitive data
      if (fs.existsSync(file)) {
        try {
          const content = fs.readFileSync(file, 'utf8');
          const sensitiveKeywords = [
            'SUPABASE_SERVICE_ROLE_KEY',
            'SUPABASE_URL=https://',
            'password=',
            'secret=',
            'private_key',
            'api_key=',
            'token='
          ];
          
          for (const keyword of sensitiveKeywords) {
            if (content.includes(keyword) && !file.includes('.example') && !file.includes('template')) {
              console.log(`⚠️  WARNING: ${file} contains sensitive keyword: ${keyword}`);
              console.log(`   Please verify this is not real credential data`);
            }
          }
        } catch (error) {
          // Ignore read errors
        }
      }
    }
  }
} catch (error) {
  console.log('ℹ️  No staged files to check');
}

// Check .gitignore exists and contains essential patterns
console.log('\n📋 Checking .gitignore configuration...');
if (!fs.existsSync('.gitignore')) {
  console.log('❌ CRITICAL: .gitignore file is missing!');
  hasIssues = true;
} else {
  const gitignoreContent = fs.readFileSync('.gitignore', 'utf8');
  const requiredPatterns = [
    '.env',
    '.env.local',
    '*.key',
    '*.pem',
    'node_modules/',
    '*.log'
  ];
  
  for (const pattern of requiredPatterns) {
    if (gitignoreContent.includes(pattern)) {
      console.log(`✅ .gitignore includes: ${pattern}`);
    } else {
      console.log(`❌ .gitignore missing pattern: ${pattern}`);
      hasIssues = true;
    }
  }
}

// Check for .env file in repository history
console.log('\n📋 Checking repository history for .env files...');
try {
  const historyCheck = execSync('git log --all --full-history -- .env', { encoding: 'utf8' });
  if (historyCheck.trim()) {
    console.log('⚠️  WARNING: .env file found in Git history!');
    console.log('   Consider using git filter-branch to remove sensitive data from history');
    console.log('   See: https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/removing-sensitive-data-from-a-repository');
  } else {
    console.log('✅ No .env files found in repository history');
  }
} catch (error) {
  console.log('✅ No .env files found in repository history');
}

// Final result
console.log('\n' + '='.repeat(60));
if (hasIssues) {
  console.log('❌ SECURITY CHECK FAILED - Issues found!');
  console.log('Please fix the issues above before committing.');
  process.exit(1);
} else {
  console.log('✅ SECURITY CHECK PASSED - All sensitive files are protected!');
  console.log('Safe to commit.');
  process.exit(0);
}
