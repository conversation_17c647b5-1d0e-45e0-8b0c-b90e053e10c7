import React, { useState, useEffect } from 'react';

interface ErrorInfo {
  id: string;
  timestamp: number;
  message: string;
  stack?: string;
  type: 'error' | 'warning' | 'info';
  source: string;
}

export const SimpleErrorDisplay: React.FC = () => {
  const [errors, setErrors] = useState<ErrorInfo[]>([]);
  const [isVisible, setIsVisible] = useState(true);
  const [isMinimized, setIsMinimized] = useState(false);

  const addError = (error: Omit<ErrorInfo, 'id' | 'timestamp'>) => {
    const newError: ErrorInfo = {
      ...error,
      id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`,
      timestamp: Date.now()
    };
    
    setErrors(prev => [newError, ...prev].slice(0, 50)); // Keep last 50 errors
  };

  useEffect(() => {
    // Capture console errors
    const originalError = console.error;
    const originalWarn = console.warn;
    const originalLog = console.log;

    console.error = (...args) => {
      originalError(...args);
      addError({
        message: args.join(' '),
        type: 'error',
        source: 'console.error',
        stack: new Error().stack
      });
    };

    console.warn = (...args) => {
      originalWarn(...args);
      addError({
        message: args.join(' '),
        type: 'warning',
        source: 'console.warn'
      });
    };

    // Capture JavaScript errors
    const handleError = (event: ErrorEvent) => {
      addError({
        message: event.message,
        type: 'error',
        source: 'JavaScript Error',
        stack: event.error?.stack
      });
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      addError({
        message: `Unhandled Promise Rejection: ${event.reason}`,
        type: 'error',
        source: 'Promise Rejection',
        stack: event.reason?.stack
      });
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    // Add initial message
    addError({
      message: 'Error monitoring started - Console errors will appear here',
      type: 'info',
      source: 'Debug System'
    });

    return () => {
      console.error = originalError;
      console.warn = originalWarn;
      console.log = originalLog;
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 bg-red-600 text-white p-3 rounded-full shadow-lg z-50 hover:bg-red-700"
        title="Show Error Console"
      >
        🐛 {errors.length > 0 && <span className="ml-1 bg-white text-red-600 text-xs px-2 py-1 rounded-full">{errors.length}</span>}
      </button>
    );
  }

  return (
    <div className={`fixed ${isMinimized ? 'bottom-4 right-4 w-80 h-12' : 'bottom-4 right-4 w-96 h-96'} bg-black/90 border border-red-500 rounded-lg shadow-xl z-50 overflow-hidden`}>
      {/* Header */}
      <div className="bg-red-600 text-white p-2 flex items-center justify-between">
        <h3 className="font-semibold text-sm">🐛 Error Console ({errors.length})</h3>
        <div className="flex items-center gap-2">
          <button
            onClick={() => setIsMinimized(!isMinimized)}
            className="text-white hover:text-gray-200 text-sm"
            title={isMinimized ? "Expand" : "Minimize"}
          >
            {isMinimized ? '▲' : '▼'}
          </button>
          <button
            onClick={() => {
              const errorText = errors.map(e => `[${e.type.toUpperCase()}] ${e.message}\nSource: ${e.source}\nTime: ${new Date(e.timestamp).toLocaleString()}\n${e.stack ? `Stack: ${e.stack}\n` : ''}\n---\n`).join('');
              navigator.clipboard.writeText(errorText).then(() => {
                alert('Errors copied to clipboard!');
              }).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = errorText;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('Errors copied to clipboard!');
              });
            }}
            className="text-white hover:text-gray-200 text-sm"
            title="Copy All Errors"
          >
            📋
          </button>
          <button
            onClick={() => setErrors([])}
            className="text-white hover:text-gray-200 text-sm"
            title="Clear Errors"
          >
            🗑️
          </button>
          <button
            onClick={() => setIsVisible(false)}
            className="text-white hover:text-gray-200 text-sm"
            title="Hide Console"
          >
            ✕
          </button>
        </div>
      </div>

      {/* Content */}
      {!isMinimized && (
        <div className="p-2 h-full overflow-y-auto text-xs">
          {errors.length === 0 ? (
            <div className="text-gray-400 text-center py-4">
              No errors detected
            </div>
          ) : (
            <div className="space-y-2">
              {errors.map(error => (
                <div
                  key={error.id}
                  className={`p-2 rounded border-l-4 ${
                    error.type === 'error'
                      ? 'bg-red-900/20 border-red-500 text-red-200'
                      : error.type === 'warning'
                      ? 'bg-yellow-900/20 border-yellow-500 text-yellow-200'
                      : 'bg-blue-900/20 border-blue-500 text-blue-200'
                  }`}
                >
                  <div className="font-medium mb-1">{error.message}</div>
                  <div className="text-gray-400 text-xs">
                    {error.source} - {new Date(error.timestamp).toLocaleTimeString()}
                  </div>
                  {error.stack && (
                    <details className="mt-1">
                      <summary className="cursor-pointer text-gray-300 hover:text-white text-xs">
                        Stack trace
                      </summary>
                      <pre className="mt-1 text-xs text-gray-400 whitespace-pre-wrap overflow-x-auto">
                        {error.stack.slice(0, 500)}...
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Test Buttons */}
      {!isMinimized && (
        <div className="border-t border-gray-700 p-2 bg-gray-900">
          <div className="flex gap-2">
            <button
              onClick={() => {
                console.error('Test error message');
              }}
              className="px-2 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700"
            >
              Test Error
            </button>
            <button
              onClick={() => {
                console.warn('Test warning message');
              }}
              className="px-2 py-1 bg-yellow-600 text-white rounded text-xs hover:bg-yellow-700"
            >
              Test Warning
            </button>
            <button
              onClick={() => {
                throw new Error('Test JavaScript error');
              }}
              className="px-2 py-1 bg-purple-600 text-white rounded text-xs hover:bg-purple-700"
            >
              Test JS Error
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
