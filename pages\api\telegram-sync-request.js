/**
 * TELEGRAM SYNC REQUEST API
 *
 * Initiates a Telegram account sync request using the same 6-digit PIN system as the bot.
 * Generates a 6-digit PIN that gets stored in the auth_tokens table.
 * The user enters this PIN directly in the Telegram bot (no commands needed).
 * Uses the exact same authentication flow as the existing bot login system.
 */

import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client with service role
const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Configuration constants
const VERIFICATION_EXPIRY_MINUTES = 15;
const MAX_VERIFICATION_ATTEMPTS = 3;

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { userId, email } = req.body;

    // Validate required fields
    if (!userId || !email) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: userId and email are required'
      });
    }

    console.log(`🔗 Creating Telegram sync request for User ID: ${userId}, Email: ${email}`);

    // CRITICAL FIX: First find user by email, then verify the userId matches
    // This handles cases where the frontend sends mismatched userId/email
    const { data: userByEmail, error: emailError } = await supabase
      .from('users')
      .select('id, email, full_name, username')
      .eq('email', email)
      .single();

    if (emailError || !userByEmail) {
      console.error('❌ User not found by email:', emailError);
      return res.status(404).json({
        success: false,
        message: 'User not found with this email address'
      });
    }

    // Check if the provided userId matches the email's user
    if (parseInt(userId) !== userByEmail.id) {
      console.log(`⚠️ User ID mismatch: provided ${userId}, actual ${userByEmail.id}. Using correct ID.`);
    }

    // Use the correct user data from email lookup
    const user = userByEmail;

    // Update the userId to use the correct one from the database
    const actualUserId = user.id;

    // Check if user already has a connected Telegram account
    const { data: existingConnection, error: connectionError } = await supabase
      .from('telegram_users')
      .select('telegram_id, user_id')
      .eq('user_id', actualUserId)
      .single();

    if (existingConnection && !connectionError) {
      return res.status(400).json({
        success: false,
        message: 'Telegram account already connected to this user'
      });
    }

    // Generate 6-digit PIN using the same system as the bot
    const pin = generateSecurePin();

    // Calculate expiry time (15 minutes like the bot system)
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + VERIFICATION_EXPIRY_MINUTES);

    // Store PIN in auth_tokens table (same as bot system)
    const { data: authTokenData, error: authTokenError } = await supabase
      .from('auth_tokens')
      .insert({
        token: pin,
        confirmed: false,
        cancelled: false,
        expires_at: expiresAt.toISOString(),
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (authTokenError) {
      console.error('❌ Error storing auth token:', authTokenError);
      return res.status(500).json({
        success: false,
        message: 'Failed to generate connection PIN'
      });
    }

    console.log(`✅ Telegram connection PIN generated successfully: ${pin} (expires: ${expiresAt.toISOString()})`);

    // Return the 6-digit PIN for the user to enter in Telegram bot
    return res.status(200).json({
      success: true,
      message: 'Telegram connection PIN generated successfully',
      pin: pin,
      expiresAt: expiresAt.toISOString(),
      expiresInMinutes: VERIFICATION_EXPIRY_MINUTES,
      instructions: {
        step1: 'Open the Aureus Alliance Telegram bot (@AureusAllianceBot)',
        step2: `Simply send this 6-digit PIN: ${pin}`,
        step3: 'No commands needed - just type the PIN and send it',
        step4: 'Confirm the connection when prompted by the bot'
      }
    });

  } catch (error) {
    console.error('❌ Telegram sync request error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
}

/**
 * Generate cryptographically secure 6-digit PIN (same as bot system)
 */
function generateSecurePin() {
  // Use crypto.getRandomValues for secure random generation
  const array = new Uint32Array(1);
  crypto.getRandomValues(array);

  // Generate 6-digit code (000000-999999)
  const pin = (array[0] % 1000000).toString().padStart(6, '0');
  return pin;
}
