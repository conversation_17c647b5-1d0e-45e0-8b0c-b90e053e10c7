#!/usr/bin/env node

import { readFileSync } from 'fs';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function executeSQLFile() {
  try {
    console.log('🔄 Reading SQL file...');
    const sql = readFileSync('create-certificate-functions.sql', 'utf8');
    
    console.log('🔄 Executing certificate functions...');
    
    // Execute the SQL using Supabase Management API
    const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'apikey': supabaseServiceKey
      },
      body: JSON.stringify({
        sql: sql
      })
    });

    if (!response.ok) {
      const error = await response.text();
      console.error('❌ Failed to execute SQL:', error);
      return false;
    }

    console.log('✅ Certificate functions created successfully!');
    return true;

  } catch (error) {
    console.error('❌ Error:', error.message);
    return false;
  }
}

async function testFunction() {
  try {
    console.log('🧪 Testing new certificate function...');
    
    // Test the function with a sample call
    const { data, error } = await supabase.rpc('admin_create_certificate_dual', {
      p_user_id: 1,
      p_purchase_id: '00000000-0000-0000-0000-000000000000',
      p_shares_count: 100,
      p_certificate_data: { test: true }
    });

    if (error) {
      console.error('❌ Function test failed:', error.message);
      return false;
    }

    console.log('✅ Function test successful:', data);
    return true;

  } catch (error) {
    console.error('❌ Test error:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 EXECUTING CERTIFICATE FUNCTIONS');
  console.log('==================================');
  
  if (await executeSQLFile()) {
    console.log('');
    await testFunction();
  }
}

main().catch(console.error);
