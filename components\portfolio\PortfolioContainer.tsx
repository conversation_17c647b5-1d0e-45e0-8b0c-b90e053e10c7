/**
 * PORTFOLIO CONTAINER
 * 
 * Main container component that orchestrates all portfolio-related
 * functionality including holdings, calculations, and KYC integration.
 */

import React from 'react';
import { PortfolioSummary } from './PortfolioSummary';
import { ShareHoldingsTable } from './ShareHoldingsTable';
import { PerformanceChart } from './PerformanceChart';
import { DividendProjections } from './DividendProjections';
import { CertificateManager } from './CertificateManager';
import { KYCIntegration } from './KYCIntegration';
import { usePortfolioData } from '../../hooks/usePortfolioData';
import { usePortfolioCalculations } from '../../hooks/usePortfolioCalculations';

interface PortfolioContainerProps {
  userId: number;
  onRefreshData?: () => Promise<void>;
}

export const PortfolioContainer: React.FC<PortfolioContainerProps> = ({
  userId,
  onRefreshData
}) => {
  const {
    holdings,
    summary,
    kycStatus,
    currentPhase,
    loading,
    error,
    refetch
  } = usePortfolioData(userId);

  const {
    miningCalculations,
    performanceMetrics,
    phaseBreakdown,
    monthlyProjections
  } = usePortfolioCalculations(holdings, summary, currentPhase);

  const handleRefresh = async () => {
    await refetch();
    if (onRefreshData) {
      await onRefreshData();
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading your portfolio...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-6">
        <div className="flex items-center">
          <div className="text-red-400 mr-3">⚠️</div>
          <div>
            <h3 className="text-red-400 font-semibold">Error Loading Portfolio</h3>
            <p className="text-red-300 text-sm mt-1">{error}</p>
            <button
              onClick={handleRefresh}
              className="mt-2 text-red-400 hover:text-red-300 text-sm underline"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (holdings.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        </div>
        <h3 className="text-xl font-semibold text-white mb-2">No Share Holdings Found</h3>
        <p className="text-gray-400 mb-6">
          You haven't purchased any shares yet. Start building your gold-backed portfolio today!
        </p>
        <button
          onClick={() => window.location.href = '/dashboard?section=purchase-shares'}
          className="bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
        >
          Purchase Your First Shares
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Portfolio Summary */}
      <PortfolioSummary
        summary={summary}
        miningCalculations={miningCalculations}
        performanceMetrics={performanceMetrics}
        onRefresh={handleRefresh}
      />

      {/* KYC Integration */}
      {kycStatus && (
        <KYCIntegration
          kycStatus={kycStatus}
          userId={userId}
          onKYCUpdate={handleRefresh}
        />
      )}

      {/* Performance Chart */}
      <PerformanceChart
        holdings={holdings}
        phaseBreakdown={phaseBreakdown}
        monthlyProjections={monthlyProjections}
      />

      {/* Share Holdings Table */}
      <ShareHoldingsTable
        holdings={holdings}
        currentPhase={currentPhase}
        onRefresh={handleRefresh}
      />

      {/* Dividend Projections */}
      <DividendProjections
        miningCalculations={miningCalculations}
        monthlyProjections={monthlyProjections}
        summary={summary}
      />

      {/* Certificate Manager */}
      <CertificateManager
        holdings={holdings}
        userId={userId}
        kycStatus={kycStatus}
      />
    </div>
  );
};
