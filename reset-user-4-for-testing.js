import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with service role key
const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function resetUser4ForTesting() {
  console.log('🔄 RESETTING USER ID 4 FOR MIGRATION TESTING');
  console.log('==================================================');
  
  try {
    // Step 1: Check current user state
    console.log('📋 Step 1: Checking current user state...');
    const { data: currentUser, error: fetchError } = await supabase
      .from('users')
      .select('id, email, username, password_hash, migration_status, web_credentials_set_at, migration_completed_at')
      .eq('id', 4)
      .single();

    if (fetchError) {
      console.error('❌ Error fetching user:', fetchError);
      return;
    }

    if (!currentUser) {
      console.error('❌ User ID 4 not found');
      return;
    }

    console.log('✅ Current user state:');
    console.log('   ID:', currentUser.id);
    console.log('   Email:', currentUser.email);
    console.log('   Username:', currentUser.username);
    console.log('   Password Hash:', currentUser.password_hash);
    console.log('   Migration Status:', currentUser.migration_status);
    console.log('   Web Credentials Set:', currentUser.web_credentials_set_at);
    console.log('   Migration Completed:', currentUser.migration_completed_at);

    // Step 2: Reset user to clean Telegram state
    console.log('\n📋 Step 2: Resetting to clean Telegram state...');
    const { error: resetError } = await supabase
      .from('users')
      .update({
        password_hash: 'telegram_auth',
        migration_status: null,
        web_credentials_set_at: null,
        migration_completed_at: null,
        updated_at: new Date().toISOString()
      })
      .eq('id', 4);

    if (resetError) {
      console.error('❌ Error resetting user:', resetError);
      return;
    }

    console.log('✅ User reset successfully');

    // Step 3: Verify the reset
    console.log('\n📋 Step 3: Verifying reset...');
    const { data: verifyUser, error: verifyError } = await supabase
      .from('users')
      .select('id, email, username, password_hash, migration_status, web_credentials_set_at, migration_completed_at')
      .eq('id', 4)
      .single();

    if (verifyError) {
      console.error('❌ Error verifying reset:', verifyError);
      return;
    }

    console.log('✅ Reset verification:');
    console.log('   ID:', verifyUser.id);
    console.log('   Email:', verifyUser.email);
    console.log('   Username:', verifyUser.username);
    console.log('   Password Hash:', verifyUser.password_hash);
    console.log('   Migration Status:', verifyUser.migration_status);
    console.log('   Web Credentials Set:', verifyUser.web_credentials_set_at);
    console.log('   Migration Completed:', verifyUser.migration_completed_at);

    // Step 4: Clean up any email verification codes
    console.log('\n📋 Step 4: Cleaning up email verification codes...');
    const { error: cleanupError } = await supabase
      .from('email_verification_codes')
      .delete()
      .eq('user_id', 4);

    if (cleanupError) {
      console.log('⚠️ Note: Could not clean up email verification codes:', cleanupError.message);
    } else {
      console.log('✅ Email verification codes cleaned up');
    }

    console.log('\n🎉 SUCCESS! USER ID 4 RESET FOR TESTING');
    console.log('===========================================');
    console.log('📱 User ID 4 is now ready for migration testing:');
    console.log('   ✅ Password Hash: telegram_auth (clean Telegram user)');
    console.log('   ✅ Migration Status: null (not migrated)');
    console.log('   ✅ Web Credentials: null (not set)');
    console.log('   ✅ Email Verification: cleaned up');
    console.log('');
    console.log('📋 TESTING STEPS:');
    console.log('1. Login with Telegram authentication');
    console.log('2. Visit /migrate page');
    console.log('3. Set password (Step 1)');
    console.log('4. Verify email (Step 2)');
    console.log('5. Complete migration');
    console.log('6. Test web login with email/password');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the reset
resetUser4ForTesting();
