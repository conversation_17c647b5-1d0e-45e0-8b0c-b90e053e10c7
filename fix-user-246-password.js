import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';

// Supabase configuration
const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const fixUser246Password = async () => {
  try {
    console.log('🔧 FIXING USER ID 246 PASSWORD HASH');
    console.log('===================================');
    
    // Step 1: Verify the current issue
    console.log('\n📋 Step 1: Verifying current password hash...');
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, email, username, password_hash')
      .eq('id', 246)
      .single();

    if (userError || !user) {
      console.log('❌ Error fetching user:', userError?.message);
      return;
    }

    console.log('✅ Current user data:');
    console.log('   ID:', user.id);
    console.log('   Email:', user.email);
    console.log('   Username:', user.username);
    console.log('   Current password_hash:', user.password_hash);

    if (user.password_hash !== 'telegram_auth') {
      console.log('⚠️ Password hash is not "telegram_auth" - may have been fixed already');
      console.log('   Current hash:', user.password_hash);
      return;
    }

    // Step 2: Generate a temporary password and hash
    console.log('\n📋 Step 2: Generating temporary password...');
    
    // Generate a secure temporary password
    const tempPassword = 'TempPass' + Math.random().toString(36).substring(2, 8) + '!';
    console.log('🔑 Temporary password generated:', tempPassword);
    console.log('   ⚠️ IMPORTANT: User must change this password after first login');

    // Hash the temporary password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(tempPassword, saltRounds);
    console.log('✅ Password hashed successfully');
    console.log('   Hash length:', hashedPassword.length);
    console.log('   Hash starts with:', hashedPassword.substring(0, 10) + '...');

    // Step 3: Update the database
    console.log('\n📋 Step 3: Updating database...');
    
    const { error: updateError } = await supabase
      .from('users')
      .update({
        password_hash: hashedPassword,
        updated_at: new Date().toISOString(),
        web_credentials_set_at: new Date().toISOString()
      })
      .eq('id', 246);

    if (updateError) {
      console.log('❌ Error updating password:', updateError.message);
      return;
    }

    console.log('✅ Password hash updated successfully');

    // Step 4: Verify the fix
    console.log('\n📋 Step 4: Verifying the fix...');
    
    const { data: updatedUser, error: verifyError } = await supabase
      .from('users')
      .select('id, email, password_hash, web_credentials_set_at')
      .eq('id', 246)
      .single();

    if (verifyError || !updatedUser) {
      console.log('❌ Error verifying update:', verifyError?.message);
      return;
    }

    console.log('✅ Verification successful:');
    console.log('   Password hash length:', updatedUser.password_hash.length);
    console.log('   Is bcrypt format:', updatedUser.password_hash.startsWith('$2'));
    console.log('   Web credentials set at:', updatedUser.web_credentials_set_at);

    // Step 5: Test the password
    console.log('\n📋 Step 5: Testing password verification...');
    
    const isValid = await bcrypt.compare(tempPassword, updatedUser.password_hash);
    console.log('   Password verification test:', isValid ? 'PASSED ✅' : 'FAILED ❌');

    if (isValid) {
      console.log('\n🎉 SUCCESS! User ID 246 password has been fixed');
      console.log('============================================');
      console.log('📧 Email:', user.email);
      console.log('👤 Username:', user.username);
      console.log('🔑 Temporary Password:', tempPassword);
      console.log('');
      console.log('📋 NEXT STEPS:');
      console.log('1. Inform the user that their password has been reset');
      console.log('2. Provide them with the temporary password above');
      console.log('3. Ask them to login and change their password immediately');
      console.log('4. They should now be able to login successfully');
    } else {
      console.log('\n❌ FAILED: Password verification test failed');
    }

  } catch (error) {
    console.error('❌ Fix script error:', error);
  }
};

fixUser246Password();
