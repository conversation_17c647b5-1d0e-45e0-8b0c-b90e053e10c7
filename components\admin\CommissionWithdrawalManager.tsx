import React, { useState, useEffect } from 'react'
import { supabase } from '../../lib/supabase'
import { logAdminAction } from '../../lib/adminAuth'
import { validateKYCForFinancialOperations } from '../../lib/kycValidation'
import RecordExporter from './RecordExporter'

interface User {
  id: number
  username: string
  full_name: string | null
  email: string
}

interface CommissionWithdrawalManagerProps {
  currentUser?: any
}

export const CommissionWithdrawalManager: React.FC<CommissionWithdrawalManagerProps> = ({
  currentUser
}) => {
  const [activeTab, setActiveTab] = useState<'commission' | 'withdrawal'>('commission')
  const [loading, setLoading] = useState(false)
  const [users, setUsers] = useState<User[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [recentRecords, setRecentRecords] = useState<any[]>([])

  // Commission form states
  const [commissionForm, setCommissionForm] = useState({
    userId: '',
    type: 'referral',
    amount: '',
    description: '',
    date: new Date().toISOString().split('T')[0]
  })

  // Withdrawal form states
  const [withdrawalForm, setWithdrawalForm] = useState({
    userId: '',
    amount: '',
    userWalletAddress: '', // User's wallet address (where they want to receive)
    companyWalletAddress: '', // Company wallet address (where we send from)
    transactionHash: '',
    proofOfPayment: '', // URL or file reference for proof
    method: 'crypto',
    network: 'USDT',
    status: 'pending',
    requestDate: new Date().toISOString().split('T')[0],
    processedDate: '',
    adminNotes: ''
  })

  const [showConfirmDialog, setShowConfirmDialog] = useState<{
    show: boolean
    type: 'commission' | 'withdrawal'
    data?: any
  }>({ show: false, type: 'commission' })

  const [showExporter, setShowExporter] = useState<{
    show: boolean
    type: 'commission' | 'withdrawal'
  }>({ show: false, type: 'commission' })

  const [uploadingProof, setUploadingProof] = useState(false)

  useEffect(() => {
    loadUsers()
    loadRecentRecords()
  }, [activeTab])

  const loadUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, username, full_name, email')
        .order('username')

      if (error) throw error
      setUsers(data || [])
    } catch (error) {
      console.error('Error loading users:', error)
    }
  }

  const loadRecentRecords = async () => {
    try {
      if (activeTab === 'commission') {
        const { data, error } = await supabase
          .from('commission_transactions')
          .select(`
            *,
            referrer:users!referrer_id(username, full_name, email)
          `)
          .order('created_at', { ascending: false })
          .limit(10)

        if (error) throw error
        setRecentRecords(data || [])
      } else {
        const { data, error } = await supabase
          .from('commission_withdrawal_requests')
          .select(`
            *,
            user:users!user_id(username, full_name, email)
          `)
          .order('created_at', { ascending: false })
          .limit(10)

        if (error) throw error
        setRecentRecords(data || [])
      }
    } catch (error) {
      console.error('Error loading recent records:', error)
    }
  }

  const filteredUsers = users.filter(user =>
    user.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.id.toString().includes(searchTerm)
  )

  const handleCommissionSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!commissionForm.userId || !commissionForm.amount) {
      alert('Please fill in all required fields')
      return
    }

    setShowConfirmDialog({
      show: true,
      type: 'commission',
      data: commissionForm
    })
  }

  const handleProofUpload = async (file: File) => {
    if (!file) return null

    setUploadingProof(true)
    try {
      // Create a unique filename
      const fileExt = file.name.split('.').pop()
      const fileName = `withdrawal_proof_${Date.now()}.${fileExt}`

      // Upload to Supabase storage
      const { data, error } = await supabase.storage
        .from('withdrawal_proofs')
        .upload(fileName, file)

      if (error) {
        console.error('Upload error:', error)
        alert('Failed to upload proof of payment. Please try again.')
        return null
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('withdrawal_proofs')
        .getPublicUrl(fileName)

      return publicUrl
    } catch (error) {
      console.error('Upload error:', error)
      alert('Failed to upload proof of payment. Please try again.')
      return null
    } finally {
      setUploadingProof(false)
    }
  }

  const handleWithdrawalSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!withdrawalForm.userId || !withdrawalForm.amount || !withdrawalForm.userWalletAddress) {
      alert('Please fill in all required fields')
      return
    }

    setShowConfirmDialog({
      show: true,
      type: 'withdrawal',
      data: withdrawalForm
    })
  }

  const confirmAction = async () => {
    setLoading(true)
    try {
      if (showConfirmDialog.type === 'commission') {
        await processCommission()
      } else {
        await processWithdrawal()
      }
      
      setShowConfirmDialog({ show: false, type: 'commission' })
      loadRecentRecords()
    } catch (error) {
      console.error('Error processing action:', error)
      alert('Error processing request. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const processCommission = async () => {
    const amount = parseFloat(commissionForm.amount)
    const userId = parseInt(commissionForm.userId)

    // Create commission transaction record
    const { data: transaction, error: transactionError } = await supabase
      .from('commission_transactions')
      .insert({
        referrer_id: userId,
        referred_id: userId, // For manual adjustments, referrer and referred can be same
        share_purchase_id: null,
        commission_rate: 0, // Manual adjustment
        share_purchase_amount: 0,
        usdt_commission: amount,
        share_commission: 0,
        status: 'approved',
        payment_date: new Date(commissionForm.date).toISOString()
      })
      .select()
      .single()

    // Trigger email notification for commission (manual adjustment)
    if (!transactionError && transaction?.id) {
      try {
        const { emailNotificationTriggers } = await import('../../lib/services/emailNotificationTriggers')
        await emailNotificationTriggers.triggerCommissionNotification(transaction.id)
      } catch (emailError) {
        console.warn('Failed to trigger commission email notification:', emailError)
        // Don't fail the operation if email fails
      }
    }

    if (transactionError) throw transactionError

    // Update commission balance
    const { data: currentBalance, error: balanceError } = await supabase
      .from('commission_balances')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (balanceError && balanceError.code !== 'PGRST116') throw balanceError

    const newBalance = {
      user_id: userId,
      usdt_balance: (currentBalance?.usdt_balance || 0) + amount,
      share_balance: currentBalance?.share_balance || 0,
      total_earned_usdt: (currentBalance?.total_earned_usdt || 0) + Math.max(0, amount),
      total_earned_shares: currentBalance?.total_earned_shares || 0,
      total_withdrawn: currentBalance?.total_withdrawn || 0,
      escrowed_amount: currentBalance?.escrowed_amount || 0,
      last_updated: new Date().toISOString()
    }

    const { error: updateError } = await supabase
      .from('commission_balances')
      .upsert(newBalance)

    if (updateError) throw updateError

    // Log admin action
    await logAdminAction(
      currentUser?.adminUser?.id || 'unknown',
      'commission_adjustment',
      `Added ${amount} USDT commission to user ${userId}`,
      { userId, amount, type: commissionForm.type, description: commissionForm.description }
    )

    // Reset form
    setCommissionForm({
      userId: '',
      type: 'referral',
      amount: '',
      description: '',
      date: new Date().toISOString().split('T')[0]
    })

    alert('Commission added successfully!')
  }

  const processWithdrawal = async () => {
    const amount = parseFloat(withdrawalForm.amount)
    const userId = parseInt(withdrawalForm.userId)

    // Validate KYC status before processing withdrawal
    if (withdrawalForm.status === 'completed') {
      const kycValidation = await validateKYCForFinancialOperations(userId)
      if (!kycValidation.canWithdraw) {
        alert(`Cannot process withdrawal: ${kycValidation.message}`)
        return
      }
    }

    // Create withdrawal record with enhanced fields
    const withdrawalData: any = {
      user_id: userId,
      withdrawal_amount: amount,
      wallet_address: withdrawalForm.userWalletAddress,
      network: withdrawalForm.network,
      currency: 'USDT',
      status: withdrawalForm.status,
      admin_notes: withdrawalForm.adminNotes,
      transaction_hash: withdrawalForm.transactionHash || null,
      processed_at: withdrawalForm.processedDate ? new Date(withdrawalForm.processedDate).toISOString() : null,
      created_at: new Date(withdrawalForm.requestDate).toISOString(),
      withdrawal_method: withdrawalForm.method,
      approved_by_admin_id: currentUser?.adminUser?.id || null,
      approved_at: withdrawalForm.status === 'completed' ? new Date().toISOString() : null
    }

    // Add additional fields
    if (withdrawalForm.companyWalletAddress) {
      withdrawalData.company_wallet_address = withdrawalForm.companyWalletAddress
    }
    if (withdrawalForm.proofOfPayment) {
      withdrawalData.proof_of_payment = withdrawalForm.proofOfPayment
    }
    if (withdrawalForm.adminNotes) {
      withdrawalData.processing_notes = withdrawalForm.adminNotes
    }

    const { data: withdrawal, error: withdrawalError } = await supabase
      .from('commission_withdrawal_requests')
      .insert(withdrawalData)
      .select()
      .single()

    if (withdrawalError) throw withdrawalError

    // If withdrawal is completed, update commission balance
    if (withdrawalForm.status === 'completed') {
      const { data: currentBalance, error: balanceError } = await supabase
        .from('commission_balances')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (balanceError) throw balanceError

      const { error: updateError } = await supabase
        .from('commission_balances')
        .update({
          usdt_balance: Math.max(0, currentBalance.usdt_balance - amount),
          total_withdrawn: currentBalance.total_withdrawn + amount,
          last_updated: new Date().toISOString()
        })
        .eq('user_id', userId)

      if (updateError) throw updateError
    }

    // Log admin action
    await logAdminAction(
      currentUser?.adminUser?.id || 'unknown',
      'withdrawal_created',
      `Created withdrawal record for user ${userId}: ${amount} USDT`,
      {
        userId,
        amount,
        status: withdrawalForm.status,
        userWalletAddress: withdrawalForm.userWalletAddress,
        companyWalletAddress: withdrawalForm.companyWalletAddress,
        transactionHash: withdrawalForm.transactionHash
      }
    )

    // Reset form
    setWithdrawalForm({
      userId: '',
      amount: '',
      userWalletAddress: '',
      companyWalletAddress: '',
      transactionHash: '',
      proofOfPayment: '',
      method: 'crypto',
      network: 'USDT',
      status: 'pending',
      requestDate: new Date().toISOString().split('T')[0],
      processedDate: '',
      adminNotes: ''
    })

    alert('Withdrawal record created successfully!')
  }

  return (
    <div style={{
      backgroundColor: '#0F0F0F',
      minHeight: '100vh',
      padding: '24px',
      color: '#FFFFFF'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        {/* Header */}
        <div style={{ marginBottom: '32px' }}>
          <h1 style={{
            fontSize: '28px',
            fontWeight: '700',
            color: '#F59E0B',
            marginBottom: '8px'
          }}>
            💰 Commission & Withdrawal Management
          </h1>
          <p style={{ color: '#9CA3AF', fontSize: '16px' }}>
            Manage user commissions and withdrawal requests
          </p>
        </div>

        {/* Tab Navigation */}
        <div style={{
          display: 'flex',
          marginBottom: '32px',
          borderBottom: '1px solid #1F2937'
        }}>
          {[
            { id: 'commission', name: 'Add Commission', icon: '💸' },
            { id: 'withdrawal', name: 'Create Withdrawal', icon: '🏦' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              style={{
                padding: '12px 24px',
                backgroundColor: activeTab === tab.id ? '#1F2937' : 'transparent',
                color: activeTab === tab.id ? '#F59E0B' : '#9CA3AF',
                border: 'none',
                borderBottom: activeTab === tab.id ? '2px solid #F59E0B' : '2px solid transparent',
                cursor: 'pointer',
                fontSize: '16px',
                fontWeight: '600',
                transition: 'all 0.2s ease'
              }}
            >
              {tab.icon} {tab.name}
            </button>
          ))}
        </div>

        {/* Main Content */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr 400px',
          gap: '32px'
        }}>
          {/* Form Section */}
          <div style={{
            backgroundColor: '#1F2937',
            borderRadius: '12px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            {activeTab === 'commission' ? (
              <div>
                <h2 style={{
                  fontSize: '20px',
                  fontWeight: '600',
                  color: '#F59E0B',
                  marginBottom: '24px'
                }}>
                  💸 Add Commission
                </h2>

                <form onSubmit={handleCommissionSubmit}>
                  {/* User Selection */}
                  <div style={{ marginBottom: '20px' }}>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '600',
                      color: '#E5E7EB',
                      marginBottom: '8px'
                    }}>
                      Select User *
                    </label>
                    <input
                      type="text"
                      placeholder="Search by username, email, or ID..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      style={{
                        width: '100%',
                        padding: '12px',
                        backgroundColor: '#374151',
                        border: '1px solid #4B5563',
                        borderRadius: '8px',
                        color: '#FFFFFF',
                        fontSize: '14px',
                        marginBottom: '8px'
                      }}
                    />
                    <select
                      value={commissionForm.userId}
                      onChange={(e) => setCommissionForm({ ...commissionForm, userId: e.target.value })}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        backgroundColor: '#374151',
                        border: '1px solid #4B5563',
                        borderRadius: '8px',
                        color: '#FFFFFF',
                        fontSize: '14px'
                      }}
                    >
                      <option value="">Select a user...</option>
                      {filteredUsers.map((user) => (
                        <option key={user.id} value={user.id}>
                          {user.username} - {user.full_name} ({user.email})
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Commission Type */}
                  <div style={{ marginBottom: '20px' }}>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '600',
                      color: '#E5E7EB',
                      marginBottom: '8px'
                    }}>
                      Commission Type *
                    </label>
                    <select
                      value={commissionForm.type}
                      onChange={(e) => setCommissionForm({ ...commissionForm, type: e.target.value })}
                      style={{
                        width: '100%',
                        padding: '12px',
                        backgroundColor: '#374151',
                        border: '1px solid #4B5563',
                        borderRadius: '8px',
                        color: '#FFFFFF',
                        fontSize: '14px'
                      }}
                    >
                      <option value="referral">Referral Commission</option>
                      <option value="bonus">Performance Bonus</option>
                      <option value="manual_adjustment">Manual Adjustment</option>
                      <option value="correction">Balance Correction</option>
                      <option value="incentive">Special Incentive</option>
                    </select>
                  </div>

                  {/* Amount */}
                  <div style={{ marginBottom: '20px' }}>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '600',
                      color: '#E5E7EB',
                      marginBottom: '8px'
                    }}>
                      Amount (USDT) *
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      value={commissionForm.amount}
                      onChange={(e) => setCommissionForm({ ...commissionForm, amount: e.target.value })}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        backgroundColor: '#374151',
                        border: '1px solid #4B5563',
                        borderRadius: '8px',
                        color: '#FFFFFF',
                        fontSize: '14px'
                      }}
                      placeholder="0.00"
                    />
                  </div>

                  {/* Description */}
                  <div style={{ marginBottom: '20px' }}>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '600',
                      color: '#E5E7EB',
                      marginBottom: '8px'
                    }}>
                      Description/Notes *
                    </label>
                    <textarea
                      value={commissionForm.description}
                      onChange={(e) => setCommissionForm({ ...commissionForm, description: e.target.value })}
                      required
                      rows={3}
                      style={{
                        width: '100%',
                        padding: '12px',
                        backgroundColor: '#374151',
                        border: '1px solid #4B5563',
                        borderRadius: '8px',
                        color: '#FFFFFF',
                        fontSize: '14px',
                        resize: 'vertical'
                      }}
                      placeholder="Reason for commission adjustment..."
                    />
                  </div>

                  {/* Date */}
                  <div style={{ marginBottom: '24px' }}>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '600',
                      color: '#E5E7EB',
                      marginBottom: '8px'
                    }}>
                      Commission Date *
                    </label>
                    <input
                      type="date"
                      value={commissionForm.date}
                      onChange={(e) => setCommissionForm({ ...commissionForm, date: e.target.value })}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        backgroundColor: '#374151',
                        border: '1px solid #4B5563',
                        borderRadius: '8px',
                        color: '#FFFFFF',
                        fontSize: '14px'
                      }}
                    />
                  </div>

                  {/* Submit Button */}
                  <button
                    type="submit"
                    disabled={loading}
                    style={{
                      width: '100%',
                      padding: '14px',
                      backgroundColor: '#F59E0B',
                      color: '#000000',
                      border: 'none',
                      borderRadius: '8px',
                      fontSize: '16px',
                      fontWeight: '600',
                      cursor: loading ? 'not-allowed' : 'pointer',
                      opacity: loading ? 0.7 : 1,
                      transition: 'all 0.2s ease'
                    }}
                  >
                    {loading ? 'Processing...' : '💸 Add Commission'}
                  </button>
                </form>
              </div>
            ) : (
              <div>
                <h2 style={{
                  fontSize: '20px',
                  fontWeight: '600',
                  color: '#F59E0B',
                  marginBottom: '24px'
                }}>
                  🏦 Create Withdrawal Record
                </h2>

                <form onSubmit={handleWithdrawalSubmit}>
                  {/* User Selection */}
                  <div style={{ marginBottom: '20px' }}>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '600',
                      color: '#E5E7EB',
                      marginBottom: '8px'
                    }}>
                      Select User *
                    </label>
                    <input
                      type="text"
                      placeholder="Search by username, email, or ID..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      style={{
                        width: '100%',
                        padding: '12px',
                        backgroundColor: '#374151',
                        border: '1px solid #4B5563',
                        borderRadius: '8px',
                        color: '#FFFFFF',
                        fontSize: '14px',
                        marginBottom: '8px'
                      }}
                    />
                    <select
                      value={withdrawalForm.userId}
                      onChange={(e) => setWithdrawalForm({ ...withdrawalForm, userId: e.target.value })}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        backgroundColor: '#374151',
                        border: '1px solid #4B5563',
                        borderRadius: '8px',
                        color: '#FFFFFF',
                        fontSize: '14px'
                      }}
                    >
                      <option value="">Select a user...</option>
                      {filteredUsers.map((user) => (
                        <option key={user.id} value={user.id}>
                          {user.username} - {user.full_name} ({user.email})
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Amount */}
                  <div style={{ marginBottom: '20px' }}>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '600',
                      color: '#E5E7EB',
                      marginBottom: '8px'
                    }}>
                      Withdrawal Amount (USDT) *
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      value={withdrawalForm.amount}
                      onChange={(e) => setWithdrawalForm({ ...withdrawalForm, amount: e.target.value })}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        backgroundColor: '#374151',
                        border: '1px solid #4B5563',
                        borderRadius: '8px',
                        color: '#FFFFFF',
                        fontSize: '14px'
                      }}
                      placeholder="0.00"
                    />
                  </div>

                  {/* User Wallet Address (Destination) */}
                  <div style={{ marginBottom: '20px' }}>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '600',
                      color: '#E5E7EB',
                      marginBottom: '8px'
                    }}>
                      User Wallet Address (Destination) *
                    </label>
                    <input
                      type="text"
                      value={withdrawalForm.userWalletAddress}
                      onChange={(e) => setWithdrawalForm({ ...withdrawalForm, userWalletAddress: e.target.value })}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        backgroundColor: '#374151',
                        border: '1px solid #4B5563',
                        borderRadius: '8px',
                        color: '#FFFFFF',
                        fontSize: '14px'
                      }}
                      placeholder="Enter user's wallet address where funds will be sent..."
                    />
                  </div>

                  {/* Company Wallet Address (Source) */}
                  <div style={{ marginBottom: '20px' }}>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '600',
                      color: '#E5E7EB',
                      marginBottom: '8px'
                    }}>
                      Company Wallet Address (Source)
                    </label>
                    <input
                      type="text"
                      value={withdrawalForm.companyWalletAddress}
                      onChange={(e) => setWithdrawalForm({ ...withdrawalForm, companyWalletAddress: e.target.value })}
                      style={{
                        width: '100%',
                        padding: '12px',
                        backgroundColor: '#374151',
                        border: '1px solid #4B5563',
                        borderRadius: '8px',
                        color: '#FFFFFF',
                        fontSize: '14px'
                      }}
                      placeholder="Enter company wallet address used to send funds..."
                    />
                  </div>

                  {/* Network Selection */}
                  <div style={{ marginBottom: '20px' }}>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '600',
                      color: '#E5E7EB',
                      marginBottom: '8px'
                    }}>
                      Network *
                    </label>
                    <select
                      value={withdrawalForm.network}
                      onChange={(e) => setWithdrawalForm({ ...withdrawalForm, network: e.target.value })}
                      style={{
                        width: '100%',
                        padding: '12px',
                        backgroundColor: '#374151',
                        border: '1px solid #4B5563',
                        borderRadius: '8px',
                        color: '#FFFFFF',
                        fontSize: '14px'
                      }}
                    >
                      <option value="USDT">USDT</option>
                      <option value="TRC20">TRC20 (Tron)</option>
                      <option value="ERC20">ERC20 (Ethereum)</option>
                      <option value="BEP20">BEP20 (BSC)</option>
                      <option value="Polygon">Polygon</option>
                    </select>
                  </div>

                  {/* Transaction Hash */}
                  <div style={{ marginBottom: '20px' }}>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '600',
                      color: '#E5E7EB',
                      marginBottom: '8px'
                    }}>
                      Transaction Hash
                    </label>
                    <input
                      type="text"
                      value={withdrawalForm.transactionHash}
                      onChange={(e) => setWithdrawalForm({ ...withdrawalForm, transactionHash: e.target.value })}
                      style={{
                        width: '100%',
                        padding: '12px',
                        backgroundColor: '#374151',
                        border: '1px solid #4B5563',
                        borderRadius: '8px',
                        color: '#FFFFFF',
                        fontSize: '14px'
                      }}
                      placeholder="Enter blockchain transaction hash..."
                    />
                  </div>

                  {/* Proof of Payment Upload */}
                  <div style={{ marginBottom: '20px' }}>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '600',
                      color: '#E5E7EB',
                      marginBottom: '8px'
                    }}>
                      Proof of Payment
                    </label>
                    <div style={{
                      border: '2px dashed #4B5563',
                      borderRadius: '8px',
                      padding: '20px',
                      textAlign: 'center',
                      backgroundColor: '#374151'
                    }}>
                      <input
                        type="file"
                        accept="image/*,.pdf"
                        onChange={async (e) => {
                          const file = e.target.files?.[0]
                          if (file) {
                            const url = await handleProofUpload(file)
                            if (url) {
                              setWithdrawalForm({ ...withdrawalForm, proofOfPayment: url })
                            }
                          }
                        }}
                        style={{
                          width: '100%',
                          padding: '8px',
                          backgroundColor: 'transparent',
                          border: 'none',
                          color: '#FFFFFF',
                          fontSize: '14px'
                        }}
                      />
                      {uploadingProof && (
                        <p style={{ color: '#F59E0B', fontSize: '12px', marginTop: '8px' }}>
                          Uploading proof of payment...
                        </p>
                      )}
                      {withdrawalForm.proofOfPayment && (
                        <div style={{ marginTop: '8px' }}>
                          <p style={{ color: '#10B981', fontSize: '12px' }}>
                            ✅ Proof uploaded successfully
                          </p>
                          <a
                            href={withdrawalForm.proofOfPayment}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{
                              color: '#F59E0B',
                              fontSize: '12px',
                              textDecoration: 'underline'
                            }}
                          >
                            View uploaded proof
                          </a>
                        </div>
                      )}
                      <p style={{ color: '#9CA3AF', fontSize: '12px', marginTop: '8px' }}>
                        Upload screenshot or PDF of payment confirmation
                      </p>
                    </div>
                  </div>

                  {/* Status */}
                  <div style={{ marginBottom: '20px' }}>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '600',
                      color: '#E5E7EB',
                      marginBottom: '8px'
                    }}>
                      Status *
                    </label>
                    <select
                      value={withdrawalForm.status}
                      onChange={(e) => setWithdrawalForm({ ...withdrawalForm, status: e.target.value })}
                      style={{
                        width: '100%',
                        padding: '12px',
                        backgroundColor: '#374151',
                        border: '1px solid #4B5563',
                        borderRadius: '8px',
                        color: '#FFFFFF',
                        fontSize: '14px'
                      }}
                    >
                      <option value="pending">Pending</option>
                      <option value="processing">Processing</option>
                      <option value="completed">Completed</option>
                      <option value="failed">Failed</option>
                      <option value="cancelled">Cancelled</option>
                    </select>
                  </div>

                  {/* Request Date */}
                  <div style={{ marginBottom: '20px' }}>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '600',
                      color: '#E5E7EB',
                      marginBottom: '8px'
                    }}>
                      Request Date *
                    </label>
                    <input
                      type="date"
                      value={withdrawalForm.requestDate}
                      onChange={(e) => setWithdrawalForm({ ...withdrawalForm, requestDate: e.target.value })}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        backgroundColor: '#374151',
                        border: '1px solid #4B5563',
                        borderRadius: '8px',
                        color: '#FFFFFF',
                        fontSize: '14px'
                      }}
                    />
                  </div>

                  {/* Processing Date */}
                  <div style={{ marginBottom: '20px' }}>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '600',
                      color: '#E5E7EB',
                      marginBottom: '8px'
                    }}>
                      Processing Date (Optional)
                    </label>
                    <input
                      type="date"
                      value={withdrawalForm.processedDate}
                      onChange={(e) => setWithdrawalForm({ ...withdrawalForm, processedDate: e.target.value })}
                      style={{
                        width: '100%',
                        padding: '12px',
                        backgroundColor: '#374151',
                        border: '1px solid #4B5563',
                        borderRadius: '8px',
                        color: '#FFFFFF',
                        fontSize: '14px'
                      }}
                    />
                  </div>

                  {/* Admin Notes */}
                  <div style={{ marginBottom: '24px' }}>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '600',
                      color: '#E5E7EB',
                      marginBottom: '8px'
                    }}>
                      Admin Notes
                    </label>
                    <textarea
                      value={withdrawalForm.adminNotes}
                      onChange={(e) => setWithdrawalForm({ ...withdrawalForm, adminNotes: e.target.value })}
                      rows={3}
                      style={{
                        width: '100%',
                        padding: '12px',
                        backgroundColor: '#374151',
                        border: '1px solid #4B5563',
                        borderRadius: '8px',
                        color: '#FFFFFF',
                        fontSize: '14px',
                        resize: 'vertical'
                      }}
                      placeholder="Internal notes about this withdrawal..."
                    />
                  </div>

                  {/* Submit Button */}
                  <button
                    type="submit"
                    disabled={loading}
                    style={{
                      width: '100%',
                      padding: '14px',
                      backgroundColor: '#F59E0B',
                      color: '#000000',
                      border: 'none',
                      borderRadius: '8px',
                      fontSize: '16px',
                      fontWeight: '600',
                      cursor: loading ? 'not-allowed' : 'pointer',
                      opacity: loading ? 0.7 : 1,
                      transition: 'all 0.2s ease'
                    }}
                  >
                    {loading ? 'Processing...' : '🏦 Create Withdrawal'}
                  </button>
                </form>
              </div>
            )}
          </div>

          {/* Sidebar - Recent Records */}
          <div style={{
            backgroundColor: '#1F2937',
            borderRadius: '12px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '20px'
            }}>
              <h3 style={{
                fontSize: '18px',
                fontWeight: '600',
                color: '#F59E0B',
                margin: 0
              }}>
                📊 Recent {activeTab === 'commission' ? 'Commissions' : 'Withdrawals'}
              </h3>
              <button
                onClick={() => setShowExporter({ show: true, type: activeTab })}
                style={{
                  padding: '8px 12px',
                  backgroundColor: '#374151',
                  color: '#F59E0B',
                  border: '1px solid #4B5563',
                  borderRadius: '6px',
                  fontSize: '12px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.backgroundColor = '#4B5563'
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.backgroundColor = '#374151'
                }}
              >
                📊 Export
              </button>
            </div>

            <div style={{ maxHeight: '600px', overflowY: 'auto' }}>
              {recentRecords.length === 0 ? (
                <p style={{ color: '#9CA3AF', textAlign: 'center', padding: '20px' }}>
                  No recent records found
                </p>
              ) : (
                recentRecords.map((record, index) => (
                  <div
                    key={record.id}
                    style={{
                      backgroundColor: '#374151',
                      borderRadius: '8px',
                      padding: '16px',
                      marginBottom: '12px',
                      border: '1px solid #4B5563'
                    }}
                  >
                    {activeTab === 'commission' ? (
                      <div>
                        <div style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          marginBottom: '8px'
                        }}>
                          <span style={{
                            color: '#10B981',
                            fontWeight: '600',
                            fontSize: '16px'
                          }}>
                            +${record.usdt_commission?.toFixed(2) || '0.00'}
                          </span>
                          <span style={{
                            backgroundColor: record.status === 'approved' ? '#10B981' : '#F59E0B',
                            color: '#000000',
                            padding: '4px 8px',
                            borderRadius: '4px',
                            fontSize: '12px',
                            fontWeight: '600'
                          }}>
                            {record.status}
                          </span>
                        </div>
                        <p style={{ color: '#E5E7EB', fontSize: '14px', marginBottom: '4px' }}>
                          User: {record.referrer?.username || 'Unknown'}
                        </p>
                        <p style={{ color: '#9CA3AF', fontSize: '12px' }}>
                          {new Date(record.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    ) : (
                      <div>
                        <div style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          marginBottom: '8px'
                        }}>
                          <span style={{
                            color: '#EF4444',
                            fontWeight: '600',
                            fontSize: '16px'
                          }}>
                            -${record.withdrawal_amount?.toFixed(2) || '0.00'}
                          </span>
                          <span style={{
                            backgroundColor:
                              record.status === 'completed' ? '#10B981' :
                              record.status === 'processing' ? '#F59E0B' :
                              record.status === 'failed' ? '#EF4444' : '#6B7280',
                            color: '#000000',
                            padding: '4px 8px',
                            borderRadius: '4px',
                            fontSize: '12px',
                            fontWeight: '600'
                          }}>
                            {record.status}
                          </span>
                        </div>
                        <p style={{ color: '#E5E7EB', fontSize: '14px', marginBottom: '4px' }}>
                          User: {record.user?.username || 'Unknown'}
                        </p>
                        <p style={{ color: '#9CA3AF', fontSize: '12px' }}>
                          {new Date(record.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>

        {/* Confirmation Dialog */}
        {showConfirmDialog.show && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000
          }}>
            <div style={{
              backgroundColor: '#1F2937',
              borderRadius: '12px',
              padding: '32px',
              border: '1px solid #374151',
              maxWidth: '500px',
              width: '90%'
            }}>
              <h3 style={{
                fontSize: '20px',
                fontWeight: '600',
                color: '#F59E0B',
                marginBottom: '16px'
              }}>
                {showConfirmDialog.type === 'commission' ? '💸 Confirm Commission Addition' : '🏦 Confirm Withdrawal Creation'}
              </h3>

              <div style={{
                backgroundColor: '#374151',
                borderRadius: '8px',
                padding: '16px',
                marginBottom: '24px'
              }}>
                {showConfirmDialog.type === 'commission' ? (
                  <div>
                    <p style={{ color: '#E5E7EB', marginBottom: '8px' }}>
                      <strong>User:</strong> {users.find(u => u.id.toString() === commissionForm.userId)?.username}
                    </p>
                    <p style={{ color: '#E5E7EB', marginBottom: '8px' }}>
                      <strong>Type:</strong> {commissionForm.type}
                    </p>
                    <p style={{ color: '#E5E7EB', marginBottom: '8px' }}>
                      <strong>Amount:</strong> ${commissionForm.amount} USDT
                    </p>
                    <p style={{ color: '#E5E7EB', marginBottom: '8px' }}>
                      <strong>Description:</strong> {commissionForm.description}
                    </p>
                    <p style={{ color: '#E5E7EB' }}>
                      <strong>Date:</strong> {commissionForm.date}
                    </p>
                  </div>
                ) : (
                  <div>
                    <p style={{ color: '#E5E7EB', marginBottom: '8px' }}>
                      <strong>User:</strong> {users.find(u => u.id.toString() === withdrawalForm.userId)?.username}
                    </p>
                    <p style={{ color: '#E5E7EB', marginBottom: '8px' }}>
                      <strong>Amount:</strong> ${withdrawalForm.amount} USDT
                    </p>
                    <p style={{ color: '#E5E7EB', marginBottom: '8px' }}>
                      <strong>Network:</strong> {withdrawalForm.network}
                    </p>
                    <p style={{ color: '#E5E7EB', marginBottom: '8px' }}>
                      <strong>User Wallet:</strong> {withdrawalForm.userWalletAddress}
                    </p>
                    {withdrawalForm.companyWalletAddress && (
                      <p style={{ color: '#E5E7EB', marginBottom: '8px' }}>
                        <strong>Company Wallet:</strong> {withdrawalForm.companyWalletAddress}
                      </p>
                    )}
                    {withdrawalForm.transactionHash && (
                      <p style={{ color: '#E5E7EB', marginBottom: '8px' }}>
                        <strong>Transaction Hash:</strong> {withdrawalForm.transactionHash.substring(0, 20)}...
                      </p>
                    )}
                    {withdrawalForm.proofOfPayment && (
                      <p style={{ color: '#E5E7EB', marginBottom: '8px' }}>
                        <strong>Proof of Payment:</strong> <span style={{ color: '#10B981' }}>✅ Uploaded</span>
                      </p>
                    )}
                    <p style={{ color: '#E5E7EB', marginBottom: '8px' }}>
                      <strong>Status:</strong> {withdrawalForm.status}
                    </p>
                    <p style={{ color: '#E5E7EB' }}>
                      <strong>Request Date:</strong> {withdrawalForm.requestDate}
                    </p>
                  </div>
                )}
              </div>

              <div style={{
                display: 'flex',
                gap: '12px',
                justifyContent: 'flex-end'
              }}>
                <button
                  onClick={() => setShowConfirmDialog({ show: false, type: 'commission' })}
                  disabled={loading}
                  style={{
                    padding: '12px 24px',
                    backgroundColor: '#6B7280',
                    color: '#FFFFFF',
                    border: 'none',
                    borderRadius: '8px',
                    fontSize: '14px',
                    fontWeight: '600',
                    cursor: loading ? 'not-allowed' : 'pointer',
                    opacity: loading ? 0.7 : 1
                  }}
                >
                  Cancel
                </button>
                <button
                  onClick={confirmAction}
                  disabled={loading}
                  style={{
                    padding: '12px 24px',
                    backgroundColor: '#F59E0B',
                    color: '#000000',
                    border: 'none',
                    borderRadius: '8px',
                    fontSize: '14px',
                    fontWeight: '600',
                    cursor: loading ? 'not-allowed' : 'pointer',
                    opacity: loading ? 0.7 : 1
                  }}
                >
                  {loading ? 'Processing...' : 'Confirm'}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Export Dialog */}
        {showExporter.show && (
          <RecordExporter
            type={showExporter.type}
            onClose={() => setShowExporter({ show: false, type: 'commission' })}
          />
        )}
      </div>
    </div>
  )
}

export default CommissionWithdrawalManager
