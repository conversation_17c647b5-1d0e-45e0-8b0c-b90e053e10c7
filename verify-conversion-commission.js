/**
 * VERIFICATION: Commission Processing for Conversion Approvals
 * 
 * This script verifies that commission processing worked correctly
 * after a conversion request was approved in the admin dashboard.
 */

import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const verifyConversionCommission = async () => {
  console.log('🔍 VERIFYING CONVERSION COMMISSION PROCESSING');
  console.log('============================================\n');

  try {
    // Get the most recent test conversion request
    console.log('1️⃣ Finding most recent test conversion request...');
    
    const { data: conversionRequests, error: conversionError } = await supabase
      .from('commission_conversions')
      .select(`
        *,
        users!inner(id, username, full_name, email)
      `)
      .like('users.username', 'referred_%')
      .order('created_at', { ascending: false })
      .limit(5);

    if (conversionError) throw conversionError;

    if (!conversionRequests || conversionRequests.length === 0) {
      console.log('❌ No test conversion requests found');
      return;
    }

    console.log(`✅ Found ${conversionRequests.length} test conversion request(s)`);
    
    // Show all recent test conversions
    for (const request of conversionRequests) {
      console.log(`\n📋 Conversion Request: ${request.id}`);
      console.log(`   • User: ${request.users.username}`);
      console.log(`   • Amount: $${request.usdt_amount}`);
      console.log(`   • Shares: ${request.shares_requested}`);
      console.log(`   • Status: ${request.status}`);
      console.log(`   • Created: ${new Date(request.created_at).toLocaleString()}`);
      
      if (request.status === 'approved') {
        console.log(`   • Approved: ${new Date(request.approved_at).toLocaleString()}`);
        await verifyCommissionForRequest(request);
      } else {
        console.log(`   • Status: ${request.status} (not yet approved)`);
      }
    }

  } catch (error) {
    console.error('❌ Verification failed:', error);
    throw error;
  }
};

const verifyCommissionForRequest = async (request) => {
  console.log(`\n🔍 Verifying commission for conversion ${request.id}...`);

  try {
    // Step 1: Find the referrer for this user
    const { data: referral, error: referralError } = await supabase
      .from('referrals')
      .select(`
        *,
        referrer:users!referrer_id(id, username, full_name)
      `)
      .eq('referred_id', request.user_id)
      .eq('status', 'active')
      .single();

    if (referralError || !referral) {
      console.log('   ⚠️ No referrer found for this user');
      return;
    }

    console.log(`   📊 Referrer: ${referral.referrer.username} (ID: ${referral.referrer_id})`);

    // Step 2: Check commission transaction
    const { data: commissionTransactions, error: commissionError } = await supabase
      .from('commission_transactions')
      .select('*')
      .eq('referrer_id', referral.referrer_id)
      .eq('referred_id', request.user_id)
      .order('created_at', { ascending: false })
      .limit(1);

    if (commissionError) throw commissionError;

    // Step 3: Calculate expected commission
    const expectedUsdtCommission = request.usdt_amount * 0.15;
    const expectedShareCommission = request.shares_requested * 0.15;

    console.log(`\n   💰 Expected Commission:`);
    console.log(`      • USDT: $${expectedUsdtCommission.toFixed(2)}`);
    console.log(`      • Shares: ${expectedShareCommission.toFixed(4)}`);

    if (!commissionTransactions || commissionTransactions.length === 0) {
      console.log(`   ❌ NO COMMISSION TRANSACTION FOUND!`);
      console.log(`      This indicates the commission processing failed.`);
      return;
    }

    const commission = commissionTransactions[0];
    console.log(`\n   ✅ Commission Transaction Found:`);
    console.log(`      • Transaction ID: ${commission.id}`);
    console.log(`      • USDT Commission: $${commission.usdt_commission?.toFixed(2) || '0.00'}`);
    console.log(`      • Share Commission: ${commission.share_commission?.toFixed(4) || '0.0000'}`);
    console.log(`      • Status: ${commission.status}`);
    console.log(`      • Created: ${new Date(commission.created_at).toLocaleString()}`);

    // Step 4: Verify commission amounts
    const usdtMatch = Math.abs((commission.usdt_commission || 0) - expectedUsdtCommission) < 0.01;
    const shareMatch = Math.abs((commission.share_commission || 0) - expectedShareCommission) < 0.0001;

    console.log(`\n   🔍 Commission Verification:`);
    console.log(`      • USDT Amount: ${usdtMatch ? '✅ CORRECT' : '❌ INCORRECT'}`);
    console.log(`      • Share Amount: ${shareMatch ? '✅ CORRECT' : '❌ INCORRECT'}`);

    // Step 5: Check commission balance update
    const { data: currentBalance, error: balanceError } = await supabase
      .from('commission_balances')
      .select('*')
      .eq('user_id', referral.referrer_id)
      .single();

    if (balanceError) throw balanceError;

    console.log(`\n   💳 Referrer's Current Balance:`);
    console.log(`      • USDT Balance: $${currentBalance.usdt_balance?.toFixed(2) || '0.00'}`);
    console.log(`      • Share Balance: ${currentBalance.share_balance?.toFixed(4) || '0.0000'}`);
    console.log(`      • Total Earned USDT: $${currentBalance.total_earned_usdt?.toFixed(2) || '0.00'}`);
    console.log(`      • Total Earned Shares: ${currentBalance.total_earned_shares?.toFixed(4) || '0.0000'}`);

    // Step 6: Check notifications
    const { data: notifications, error: notificationError } = await supabase
      .from('user_notifications')
      .select('*')
      .eq('user_id', referral.referrer_id)
      .eq('type', 'commission')
      .order('created_at', { ascending: false })
      .limit(1);

    if (notificationError) throw notificationError;

    if (notifications && notifications.length > 0) {
      const notification = notifications[0];
      console.log(`\n   📧 Commission Notification:`);
      console.log(`      • Title: ${notification.title}`);
      console.log(`      • Message: ${notification.message}`);
      console.log(`      • Created: ${new Date(notification.created_at).toLocaleString()}`);
      console.log(`      • Read: ${notification.is_read ? 'Yes' : 'No'}`);
    } else {
      console.log(`\n   ⚠️ No commission notification found`);
    }

    // Step 7: Check admin audit logs
    const { data: auditLogs, error: auditError } = await supabase
      .from('admin_audit_log')
      .select('*')
      .like('description', `%${request.id}%`)
      .order('timestamp', { ascending: false })
      .limit(3);

    if (!auditError && auditLogs && auditLogs.length > 0) {
      console.log(`\n   📝 Admin Audit Logs:`);
      auditLogs.forEach((log, index) => {
        console.log(`      ${index + 1}. ${log.action}: ${log.description}`);
        console.log(`         Time: ${new Date(log.timestamp).toLocaleString()}`);
      });
    }

    // Final assessment
    console.log(`\n   🎯 OVERALL ASSESSMENT:`);
    if (usdtMatch && shareMatch && commission.status === 'approved') {
      console.log(`      ✅ COMMISSION PROCESSING SUCCESSFUL!`);
      console.log(`      ✅ All amounts are correct`);
      console.log(`      ✅ Commission transaction created`);
      console.log(`      ✅ Balance updated`);
    } else {
      console.log(`      ❌ COMMISSION PROCESSING ISSUES DETECTED`);
      if (!usdtMatch) console.log(`         • USDT commission amount incorrect`);
      if (!shareMatch) console.log(`         • Share commission amount incorrect`);
      if (commission.status !== 'approved') console.log(`         • Commission status not approved`);
    }

  } catch (error) {
    console.error(`   ❌ Error verifying commission for request ${request.id}:`, error);
  }
};

// Run the verification
verifyConversionCommission()
  .then(() => {
    console.log('\n🎉 Verification completed!');
  })
  .catch((error) => {
    console.error('\n💥 Verification failed:', error);
    process.exit(1);
  });
