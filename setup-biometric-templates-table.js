#!/usr/bin/env node

/**
 * Setup Biometric Templates Table for Advanced Facial Recognition
 * Creates secure storage for biometric templates with encryption and audit trails
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  console.error('Required: VITE_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function executeSQL(sql, description) {
  try {
    console.log(`🔄 ${description}...`);
    const { error } = await supabase.rpc('exec_sql', { sql_query: sql });
    
    if (error) {
      console.error(`❌ ${description} failed:`, error);
      return false;
    }
    
    console.log(`✅ ${description} completed`);
    return true;
  } catch (error) {
    console.error(`❌ ${description} error:`, error);
    return false;
  }
}

async function setupBiometricTemplatesTable() {
  console.log('🔐 Setting up Biometric Templates Table for Advanced Facial Recognition');
  console.log('==================================================================');
  
  // Step 1: Create biometric_templates table
  const createBiometricTemplatesSQL = `
    CREATE TABLE IF NOT EXISTS biometric_templates (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      
      -- Biometric Template Data (encrypted)
      template_data_encrypted TEXT NOT NULL,
      template_hash VARCHAR(64) NOT NULL,
      
      -- Facial Landmark Data (encrypted)
      facial_landmarks_encrypted TEXT,
      landmark_confidence DECIMAL(5,4) DEFAULT 0.0000,
      
      -- Template Metadata
      template_version VARCHAR(20) DEFAULT 'v2.0',
      extraction_method VARCHAR(50) DEFAULT 'advanced_facial_landmarks',
      quality_score DECIMAL(5,4) DEFAULT 0.0000,
      confidence DECIMAL(5,4) DEFAULT 0.0000,
      
      -- Security and Verification
      verification_count INTEGER DEFAULT 0,
      last_verified_at TIMESTAMP WITH TIME ZONE,
      template_status VARCHAR(20) DEFAULT 'active' CHECK (template_status IN ('active', 'expired', 'revoked', 'suspended')),
      
      -- Biometric Characteristics
      face_geometry_hash VARCHAR(64),
      eye_distance_ratio DECIMAL(8,6),
      nose_mouth_ratio DECIMAL(8,6),
      face_width_height_ratio DECIMAL(8,6),
      
      -- Anti-Spoofing Data
      liveness_score DECIMAL(5,4) DEFAULT 0.0000,
      texture_analysis_score DECIMAL(5,4) DEFAULT 0.0000,
      depth_analysis_score DECIMAL(5,4) DEFAULT 0.0000,
      
      -- Audit and Compliance
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      created_by_session VARCHAR(100),
      encryption_key_id VARCHAR(100),
      
      -- Constraints
      UNIQUE(user_id, template_version),
      UNIQUE(template_hash)
    );
  `;
  
  if (!await executeSQL(createBiometricTemplatesSQL, 'Creating biometric_templates table')) {
    return false;
  }
  
  // Step 2: Create biometric verification attempts table
  const createVerificationAttemptsSQL = `
    CREATE TABLE IF NOT EXISTS biometric_verification_attempts (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      template_id UUID REFERENCES biometric_templates(id) ON DELETE CASCADE,
      
      -- Verification Details
      verification_type VARCHAR(50) NOT NULL CHECK (verification_type IN ('enrollment', 'authentication', 'kyc_verification', 'login_verification')),
      verification_result VARCHAR(20) NOT NULL CHECK (verification_result IN ('success', 'failed', 'suspicious', 'blocked')),
      
      -- Matching Scores
      similarity_score DECIMAL(5,4) DEFAULT 0.0000,
      confidence_score DECIMAL(5,4) DEFAULT 0.0000,
      liveness_score DECIMAL(5,4) DEFAULT 0.0000,
      quality_score DECIMAL(5,4) DEFAULT 0.0000,
      
      -- Security Analysis
      spoofing_detected BOOLEAN DEFAULT FALSE,
      spoofing_type VARCHAR(50),
      risk_score DECIMAL(5,4) DEFAULT 0.0000,
      
      -- Technical Details
      processing_time_ms INTEGER,
      image_quality_metrics JSONB DEFAULT '{}'::jsonb,
      landmark_detection_success BOOLEAN DEFAULT FALSE,
      landmarks_detected_count INTEGER DEFAULT 0,
      
      -- Context Information
      device_info JSONB DEFAULT '{}'::jsonb,
      session_id VARCHAR(100),
      ip_address VARCHAR(45),
      user_agent TEXT,
      
      -- Timestamps
      attempted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `;
  
  if (!await executeSQL(createVerificationAttemptsSQL, 'Creating biometric_verification_attempts table')) {
    return false;
  }
  
  // Step 3: Create facial landmarks storage table
  const createFacialLandmarksSQL = `
    CREATE TABLE IF NOT EXISTS facial_landmarks_data (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      template_id UUID NOT NULL REFERENCES biometric_templates(id) ON DELETE CASCADE,
      
      -- Landmark Categories (encrypted coordinates)
      eye_landmarks_encrypted TEXT,
      nose_landmarks_encrypted TEXT,
      mouth_landmarks_encrypted TEXT,
      jawline_landmarks_encrypted TEXT,
      eyebrow_landmarks_encrypted TEXT,
      
      -- Geometric Ratios (for matching without decryption)
      inter_eye_distance DECIMAL(8,6),
      eye_nose_distance DECIMAL(8,6),
      nose_mouth_distance DECIMAL(8,6),
      face_width DECIMAL(8,6),
      face_height DECIMAL(8,6),
      
      -- Quality Metrics
      landmark_quality_score DECIMAL(5,4) DEFAULT 0.0000,
      detection_confidence DECIMAL(5,4) DEFAULT 0.0000,
      symmetry_score DECIMAL(5,4) DEFAULT 0.0000,
      
      -- Timestamps
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `;
  
  if (!await executeSQL(createFacialLandmarksSQL, 'Creating facial_landmarks_data table')) {
    return false;
  }
  
  // Step 4: Create indexes for performance
  const createIndexesSQL = `
    -- Biometric Templates Indexes
    CREATE INDEX IF NOT EXISTS idx_biometric_templates_user_id ON biometric_templates(user_id);
    CREATE INDEX IF NOT EXISTS idx_biometric_templates_status ON biometric_templates(template_status);
    CREATE INDEX IF NOT EXISTS idx_biometric_templates_hash ON biometric_templates(template_hash);
    CREATE INDEX IF NOT EXISTS idx_biometric_templates_created_at ON biometric_templates(created_at);
    CREATE INDEX IF NOT EXISTS idx_biometric_templates_quality ON biometric_templates(quality_score);
    
    -- Verification Attempts Indexes
    CREATE INDEX IF NOT EXISTS idx_verification_attempts_user_id ON biometric_verification_attempts(user_id);
    CREATE INDEX IF NOT EXISTS idx_verification_attempts_template_id ON biometric_verification_attempts(template_id);
    CREATE INDEX IF NOT EXISTS idx_verification_attempts_result ON biometric_verification_attempts(verification_result);
    CREATE INDEX IF NOT EXISTS idx_verification_attempts_attempted_at ON biometric_verification_attempts(attempted_at);
    CREATE INDEX IF NOT EXISTS idx_verification_attempts_session ON biometric_verification_attempts(session_id);
    
    -- Facial Landmarks Indexes
    CREATE INDEX IF NOT EXISTS idx_facial_landmarks_template_id ON facial_landmarks_data(template_id);
    CREATE INDEX IF NOT EXISTS idx_facial_landmarks_quality ON facial_landmarks_data(landmark_quality_score);
  `;
  
  if (!await executeSQL(createIndexesSQL, 'Creating performance indexes')) {
    return false;
  }
  
  // Step 5: Create security functions
  const createSecurityFunctionsSQL = `
    -- Function to generate biometric template hash
    CREATE OR REPLACE FUNCTION generate_biometric_hash(template_data TEXT)
    RETURNS VARCHAR(64) AS $$
    BEGIN
      RETURN encode(digest(template_data || extract(epoch from now())::text, 'sha256'), 'hex');
    END;
    $$ LANGUAGE plpgsql;
    
    -- Function to check template expiry
    CREATE OR REPLACE FUNCTION check_template_expiry(template_id UUID)
    RETURNS BOOLEAN AS $$
    DECLARE
      template_age INTERVAL;
    BEGIN
      SELECT (NOW() - created_at) INTO template_age
      FROM biometric_templates 
      WHERE id = template_id;
      
      -- Templates expire after 2 years for security
      RETURN template_age > INTERVAL '2 years';
    END;
    $$ LANGUAGE plpgsql;
    
    -- Function to log verification attempt
    CREATE OR REPLACE FUNCTION log_biometric_verification(
      p_user_id INTEGER,
      p_template_id UUID,
      p_verification_type VARCHAR(50),
      p_result VARCHAR(20),
      p_similarity_score DECIMAL(5,4),
      p_session_id VARCHAR(100)
    )
    RETURNS UUID AS $$
    DECLARE
      attempt_id UUID;
    BEGIN
      INSERT INTO biometric_verification_attempts (
        user_id, template_id, verification_type, verification_result,
        similarity_score, session_id
      ) VALUES (
        p_user_id, p_template_id, p_verification_type, p_result,
        p_similarity_score, p_session_id
      ) RETURNING id INTO attempt_id;
      
      -- Update template verification count
      UPDATE biometric_templates 
      SET verification_count = verification_count + 1,
          last_verified_at = NOW()
      WHERE id = p_template_id;
      
      RETURN attempt_id;
    END;
    $$ LANGUAGE plpgsql;
  `;
  
  if (!await executeSQL(createSecurityFunctionsSQL, 'Creating security functions')) {
    return false;
  }
  
  // Step 6: Create Row Level Security policies
  const createRLSPoliciesSQL = `
    -- Enable RLS on biometric tables
    ALTER TABLE biometric_templates ENABLE ROW LEVEL SECURITY;
    ALTER TABLE biometric_verification_attempts ENABLE ROW LEVEL SECURITY;
    ALTER TABLE facial_landmarks_data ENABLE ROW LEVEL SECURITY;
    
    -- Policy: Users can only access their own biometric data
    CREATE POLICY "Users can access own biometric templates" ON biometric_templates
      FOR ALL USING (auth.uid()::text = user_id::text);
    
    CREATE POLICY "Users can access own verification attempts" ON biometric_verification_attempts
      FOR ALL USING (auth.uid()::text = user_id::text);
    
    -- Policy: Service role can access all data for system operations
    CREATE POLICY "Service role full access biometric templates" ON biometric_templates
      FOR ALL USING (auth.role() = 'service_role');
    
    CREATE POLICY "Service role full access verification attempts" ON biometric_verification_attempts
      FOR ALL USING (auth.role() = 'service_role');
    
    CREATE POLICY "Service role full access facial landmarks" ON facial_landmarks_data
      FOR ALL USING (auth.role() = 'service_role');
  `;
  
  if (!await executeSQL(createRLSPoliciesSQL, 'Creating Row Level Security policies')) {
    return false;
  }
  
  console.log('\n✅ Biometric Templates Table Setup Complete!');
  console.log('================================================');
  console.log('✅ biometric_templates table created with encryption support');
  console.log('✅ biometric_verification_attempts table created for audit trails');
  console.log('✅ facial_landmarks_data table created for detailed landmark storage');
  console.log('✅ Performance indexes created');
  console.log('✅ Security functions implemented');
  console.log('✅ Row Level Security policies enabled');
  console.log('\n🔐 Ready for bank-level biometric verification implementation!');
  
  return true;
}

// Run the setup
setupBiometricTemplatesTable()
  .then(success => {
    if (success) {
      console.log('\n🎉 Biometric Templates Table setup completed successfully!');
      process.exit(0);
    } else {
      console.log('\n❌ Biometric Templates Table setup failed!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\n💥 Setup error:', error);
    process.exit(1);
  });
