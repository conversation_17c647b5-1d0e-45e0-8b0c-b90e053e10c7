import React from 'react';
import { ComprehensivePortfolio } from '../../portfolio/ComprehensivePortfolio';
import { DashboardData } from '../../../hooks/useDashboardData';

interface PortfolioSectionProps {
  user: any;
  dashboardData: DashboardData;
  onRefreshData: () => Promise<void>;
}

export const PortfolioSection: React.FC<PortfolioSectionProps> = ({
  user,
  dashboardData,
  onRefreshData
}) => {
  return (
    <div className="space-y-6">
      {/* Section Header */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h2 className="text-2xl font-bold text-white mb-2">Portfolio</h2>
        <p className="text-gray-400">
          View your share holdings, certificates, and portfolio performance.
        </p>
      </div>

      {/* Portfolio Component */}
      <ComprehensivePortfolio
        userId={user?.database_user?.id || user?.id}
        onRefreshData={onRefreshData}
      />
    </div>
  );
};
