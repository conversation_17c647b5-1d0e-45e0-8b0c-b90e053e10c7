const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Use the correct environment variables for server-side scripts
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const fixPresalePhase = async () => {
  try {
    console.log('🔍 CHECKING CURRENT PHASE CONFIGURATION...\n');

    // Step 1: Check all phases
    const { data: allPhases, error: allPhasesError } = await supabase
      .from('investment_phases')
      .select('*')
      .order('phase_number');

    if (allPhasesError) {
      console.error('❌ Error fetching phases:', allPhasesError);
      return;
    }

    console.log('📊 ALL PHASES:');
    allPhases.forEach(phase => {
      console.log(`   Phase ${phase.phase_number}: ${phase.phase_name} - $${phase.price_per_share} ${phase.is_active ? '🟢 ACTIVE' : '⚪ INACTIVE'}`);
    });

    // Step 2: Check for active phases
    const activePhases = allPhases.filter(p => p.is_active);
    console.log(`\n🎯 ACTIVE PHASES: ${activePhases.length}`);
    
    if (activePhases.length === 0) {
      console.log('⚠️ No active phases found!');
    } else if (activePhases.length > 1) {
      console.log('⚠️ Multiple active phases found:');
      activePhases.forEach(phase => {
        console.log(`   - ${phase.phase_name} ($${phase.price_per_share})`);
      });
    } else {
      const activePhase = activePhases[0];
      console.log(`   Current: ${activePhase.phase_name} - $${activePhase.price_per_share}`);
      
      if (activePhase.phase_number !== 0) {
        console.log('⚠️ WARNING: Active phase is not Presale!');
      }
    }

    // Step 3: Fix the phase configuration
    console.log('\n🔧 FIXING PHASE CONFIGURATION...');
    
    // First, deactivate all phases
    const { error: deactivateError } = await supabase
      .from('investment_phases')
      .update({ is_active: false })
      .neq('id', 0); // Update all records

    if (deactivateError) {
      console.error('❌ Error deactivating phases:', deactivateError);
      return;
    }

    console.log('✅ Deactivated all phases');

    // Then, activate only Presale (phase_number: 0)
    const { error: activateError } = await supabase
      .from('investment_phases')
      .update({ is_active: true })
      .eq('phase_number', 0);

    if (activateError) {
      console.error('❌ Error activating Presale:', activateError);
      return;
    }

    console.log('✅ Activated Presale phase');

    // Step 4: Verify the fix
    console.log('\n🔍 VERIFYING FIX...');
    
    const { data: verifyPhases, error: verifyError } = await supabase
      .from('investment_phases')
      .select('*')
      .eq('is_active', true);

    if (verifyError) {
      console.error('❌ Error verifying fix:', verifyError);
      return;
    }

    if (verifyPhases.length === 1 && verifyPhases[0].phase_number === 0) {
      console.log('✅ SUCCESS! Presale is now the only active phase:');
      console.log(`   ${verifyPhases[0].phase_name} - $${verifyPhases[0].price_per_share} per share`);
      console.log(`   ${verifyPhases[0].total_shares_available.toLocaleString()} shares available`);
    } else {
      console.log('❌ Fix failed - unexpected active phases:');
      verifyPhases.forEach(phase => {
        console.log(`   - ${phase.phase_name} ($${phase.price_per_share})`);
      });
    }

    console.log('\n🎉 PHASE CONFIGURATION FIXED!');
    console.log('   The purchase flow should now show:');
    console.log('   - Current Phase: Pre Sale');
    console.log('   - Share Price: $5.00 USD');
    console.log('   - Total Available: 200,000 shares');

  } catch (error) {
    console.error('❌ Error:', error);
  }
};

fixPresalePhase();
