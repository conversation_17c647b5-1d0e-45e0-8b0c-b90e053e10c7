#!/usr/bin/env node

/**
 * TEST FORGOT PASSWORD EMAIL FUNCTIONALITY
 * 
 * This script tests the forgot password email functionality by:
 * 1. Making a request to trigger password reset
 * 2. Checking if reset token was created
 * 3. Simulating the email sending process
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testForgotPasswordEmail() {
  console.log('🧪 Testing forgot password email functionality...\n');

  try {
    // Use a real user from the database
    const testEmail = '<EMAIL>';
    
    console.log(`1️⃣ Testing password reset for: ${testEmail}`);
    
    // Step 1: Check if user exists
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, email, username, full_name')
      .eq('email', testEmail)
      .single();
    
    if (userError || !user) {
      console.error('❌ Test user not found:', userError);
      return false;
    }
    
    console.log('👤 Test user found:', {
      id: user.id,
      email: user.email,
      username: user.username,
      full_name: user.full_name
    });
    
    // Step 2: Create reset token manually (simulating the password reset request)
    console.log('\n2️⃣ Creating password reset token...');
    
    const resetToken = require('crypto').randomBytes(32).toString('hex');
    const resetExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
    
    const { error: updateError } = await supabase
      .from('users')
      .update({
        reset_token: resetToken,
        reset_token_expires: resetExpires.toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id);
    
    if (updateError) {
      console.error('❌ Failed to create reset token:', updateError);
      return false;
    }
    
    console.log('✅ Reset token created successfully');
    console.log('🔑 Token (first 16 chars):', resetToken.substring(0, 16) + '...');
    console.log('⏰ Expires at:', resetExpires.toISOString());
    
    // Step 3: Test the reset link format
    console.log('\n3️⃣ Testing reset link format...');
    
    const resetLink = `http://localhost:8006/reset-password?token=${resetToken}`;
    console.log('🔗 Reset link:', resetLink);
    
    // Step 4: Verify token can be validated
    console.log('\n4️⃣ Testing token validation...');
    
    const { data: tokenUser, error: tokenError } = await supabase
      .from('users')
      .select('id, email, username, reset_token, reset_token_expires')
      .eq('reset_token', resetToken)
      .single();
    
    if (tokenError || !tokenUser) {
      console.error('❌ Token validation failed:', tokenError);
      return false;
    }
    
    // Check if token has expired
    const now = new Date();
    const expiresAt = new Date(tokenUser.reset_token_expires);
    const isValid = now <= expiresAt;
    
    console.log('🔍 Token validation result:', {
      tokenFound: !!tokenUser.reset_token,
      userEmail: tokenUser.email,
      expiresAt: tokenUser.reset_token_expires,
      isValid: isValid,
      timeUntilExpiry: Math.round((expiresAt - now) / (1000 * 60 * 60)) + ' hours'
    });
    
    if (!isValid) {
      console.error('❌ Token has expired');
      return false;
    }
    
    console.log('✅ Token is valid and can be used for password reset');
    
    // Step 5: Test email content generation (simulate)
    console.log('\n5️⃣ Testing email content generation...');
    
    const userName = user.full_name || user.username || 'User';
    const emailSubject = `Reset Your Aureus Alliance Password`;
    const emailContent = `
Hello ${userName},

You requested to reset your password for your Aureus Alliance account.

Click the link below to reset your password:
${resetLink}

This link will expire in 24 hours.

If you didn't request this password reset, please ignore this email.

Best regards,
The Aureus Alliance Team
    `.trim();
    
    console.log('📧 Email content preview:');
    console.log('Subject:', emailSubject);
    console.log('Content preview (first 200 chars):', emailContent.substring(0, 200) + '...');
    
    // Step 6: Clean up - remove the test token
    console.log('\n6️⃣ Cleaning up test token...');
    
    const { error: cleanupError } = await supabase
      .from('users')
      .update({
        reset_token: null,
        reset_token_expires: null,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id);
    
    if (cleanupError) {
      console.warn('⚠️ Failed to clean up test token:', cleanupError);
    } else {
      console.log('✅ Test token cleaned up successfully');
    }
    
    console.log('\n✅ Forgot password email test completed successfully!');
    console.log('📋 Test Summary:');
    console.log('   ✅ User lookup working');
    console.log('   ✅ Reset token generation working');
    console.log('   ✅ Token storage working');
    console.log('   ✅ Token validation working');
    console.log('   ✅ Reset link format correct');
    console.log('   ✅ Email content generation working');
    console.log('   ✅ Token cleanup working');
    
    console.log('\n🔗 To test the full flow:');
    console.log('1. Go to: http://localhost:8006/login');
    console.log('2. Click "Forgot your password?"');
    console.log(`3. Enter email: ${testEmail}`);
    console.log('4. Check for password reset email');
    console.log('5. Click the reset link in the email');
    console.log('6. Set a new password');
    
    return true;
    
  } catch (error) {
    console.error('❌ Forgot password email test failed:', error);
    return false;
  }
}

// Run the test
testForgotPasswordEmail().then(success => {
  console.log(`\n${success ? '🎉' : '💥'} Test ${success ? 'PASSED' : 'FAILED'}`);
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ Test execution failed:', error);
  process.exit(1);
});
