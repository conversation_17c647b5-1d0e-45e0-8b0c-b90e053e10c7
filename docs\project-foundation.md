# 3.2 Project Foundation - Aureus Alliance Web Dashboard

## Executive Summary
This document outlines the complete project foundation setup for the Aureus Alliance Web Dashboard, including React+Vite project structure, build and deployment scripts, environment variables and secrets management, logging and monitoring tools, basic project structure implementation, and API client and service layer setup.

## Project Initialization

### React+Vite Project Structure
```bash
# Current project structure using Vite React
# Project already initialized with:
# - TypeScript support
# - Tailwind CSS integration
# - ESLint configuration
# - Express server for API routes
# - Supabase integration

# Project directory structure
cd aureus_africa

# Install all required dependencies
npm install @supabase/supabase-js @supabase/auth-helpers-nextjs
npm install zustand
npm install zod
npm install react-hook-form @hookform/resolvers
npm install lucide-react
npm install recharts
npm install date-fns
npm install clsx tailwind-merge
npm install @radix-ui/react-dialog @radix-ui/react-dropdown-menu @radix-ui/react-select
npm install @radix-ui/react-toast @radix-ui/react-tooltip @radix-ui/react-tabs
npm install @radix-ui/react-avatar @radix-ui/react-badge @radix-ui/react-button
npm install @radix-ui/react-card @radix-ui/react-checkbox @radix-ui/react-input
npm install @radix-ui/react-label @radix-ui/react-sheet @radix-ui/react-table
npm install @radix-ui/react-textarea @radix-ui/react-toggle
npm install @hookform/resolvers/zod
npm install next-themes
npm install @next/bundle-analyzer
npm install @sentry/nextjs

# Development dependencies
npm install -D @types/node @types/react @types/react-dom
npm install -D prettier prettier-plugin-tailwindcss
npm install -D husky lint-staged @commitlint/cli @commitlint/config-conventional
npm install -D jest @testing-library/react @testing-library/jest-dom jest-environment-jsdom
npm install -D @playwright/test
npm install -D cypress
npm install -D @storybook/nextjs @storybook/addon-essentials @storybook/addon-interactions
npm install -D eslint-config-prettier eslint-plugin-import
npm install -D cross-env rimraf
```

### Complete Project Folder Structure
```
aureus_africa/
├── .env                          # Environment variables
├── .env.example                  # Environment template
├── .gitignore                    # Git ignore rules
├── .eslintrc.js                  # ESLint configuration
├── .prettierrc.js                # Prettier configuration
├── commitlint.config.js          # Commit lint rules
├── jest.config.js                # Jest configuration
├── jest.setup.js                 # Jest setup file
├── playwright.config.ts          # Playwright configuration
├── vite.config.ts                # Vite configuration
├── tailwind.config.js            # Tailwind CSS configuration
├── tsconfig.json                 # TypeScript configuration
├── package.json                  # Project dependencies
├── README.md                     # Project documentation
├── vercel.json                   # Vercel deployment config
├── 
├── .github/                      # GitHub workflows
│   └── workflows/
│       ├── ci-cd.yml            # CI/CD pipeline
│       └── codeql-analysis.yml  # Security scanning
├── 
├── .husky/                       # Git hooks
│   ├── pre-commit               # Pre-commit hook
│   ├── commit-msg               # Commit message hook
│   └── pre-push                 # Pre-push hook
├── 
├── .storybook/                   # Storybook configuration
│   ├── main.ts                  # Storybook main config
│   └── preview.ts               # Storybook preview config
├── 
├── .vscode/                      # VS Code settings
│   ├── settings.json            # Editor settings
│   ├── extensions.json          # Recommended extensions
│   └── launch.json              # Debug configuration
├── 
├── docs/                         # Documentation
│   ├── api/                     # API documentation
│   ├── components/              # Component documentation
│   ├── deployment/              # Deployment guides
│   └── development/             # Development guides
├── 
├── e2e/                          # End-to-end tests
│   ├── auth/                    # Authentication tests
│   ├── dashboard/               # Dashboard tests
│   └── utils/                   # Test utilities
├── 
├── public/                       # Static assets
│   ├── images/                  # Image assets
│   ├── icons/                   # Icon assets
│   ├── fonts/                   # Font assets
│   └── manifest.json            # PWA manifest
├── 
├── src/                          # Source code
│   ├── app/                     # Next.js App Router
│   │   ├── (auth)/              # Auth route group
│   │   │   ├── login/           # Login page
│   │   │   └── layout.tsx       # Auth layout
│   │   ├── (dashboard)/         # Dashboard route group
│   │   │   ├── dashboard/       # Main dashboard
│   │   │   ├── shares/          # Shares management
│   │   │   ├── payments/        # Payment history
│   │   │   ├── kyc/             # KYC management
│   │   │   ├── referrals/       # Referral system
│   │   │   ├── profile/         # User profile
│   │   │   ├── admin/           # Admin panel
│   │   │   └── layout.tsx       # Dashboard layout
│   │   ├── api/                 # API routes
│   │   │   ├── auth/            # Authentication endpoints
│   │   │   ├── shares/          # Share management endpoints
│   │   │   ├── payments/        # Payment endpoints
│   │   │   ├── kyc/             # KYC endpoints
│   │   │   ├── referrals/       # Referral endpoints
│   │   │   ├── admin/           # Admin endpoints
│   │   │   └── health/          # Health check
│   │   ├── globals.css          # Global styles
│   │   ├── layout.tsx           # Root layout
│   │   ├── page.tsx             # Home page
│   │   ├── loading.tsx          # Global loading
│   │   ├── error.tsx            # Global error
│   │   └── not-found.tsx        # 404 page
│   ├── 
│   ├── components/              # Reusable components
│   │   ├── ui/                  # Basic UI components
│   │   │   ├── button.tsx       # Button component
│   │   │   ├── input.tsx        # Input component
│   │   │   ├── card.tsx         # Card component
│   │   │   ├── dialog.tsx       # Dialog component
│   │   │   ├── dropdown.tsx     # Dropdown component
│   │   │   ├── table.tsx        # Table component
│   │   │   ├── toast.tsx        # Toast component
│   │   │   └── index.ts         # UI exports
│   │   ├── forms/               # Form components
│   │   │   ├── login-form.tsx   # Login form
│   │   │   ├── kyc-form.tsx     # KYC form
│   │   │   ├── payment-form.tsx # Payment form
│   │   │   └── index.ts         # Form exports
│   │   ├── layout/              # Layout components
│   │   │   ├── header.tsx       # Main header
│   │   │   ├── sidebar.tsx      # Navigation sidebar
│   │   │   ├── footer.tsx       # Footer
│   │   │   └── navigation.tsx   # Navigation component
│   │   ├── charts/              # Chart components
│   │   │   ├── share-chart.tsx  # Share price chart
│   │   │   ├── growth-chart.tsx # Growth chart
│   │   │   └── index.ts         # Chart exports
│   │   ├── features/            # Feature-specific components
│   │   │   ├── auth/            # Authentication components
│   │   │   ├── dashboard/       # Dashboard components
│   │   │   ├── shares/          # Share components
│   │   │   ├── payments/        # Payment components
│   │   │   ├── kyc/             # KYC components
│   │   │   ├── referrals/       # Referral components
│   │   │   └── admin/           # Admin components
│   │   └── providers/           # Context providers
│   │       ├── auth-provider.tsx      # Auth context
│   │       ├── theme-provider.tsx     # Theme context
│   │       ├── toast-provider.tsx     # Toast context
│   │       └── query-provider.tsx     # Query context
│   ├── 
│   ├── hooks/                   # Custom React hooks
│   │   ├── use-auth.ts          # Authentication hook
│   │   ├── use-shares.ts        # Shares data hook
│   │   ├── use-payments.ts      # Payments hook
│   │   ├── use-kyc.ts           # KYC hook
│   │   ├── use-referrals.ts     # Referrals hook
│   │   ├── use-local-storage.ts # Local storage hook
│   │   ├── use-debounce.ts      # Debounce hook
│   │   └── index.ts             # Hook exports
│   ├── 
│   ├── lib/                     # Utility libraries
│   │   ├── auth/                # Authentication utilities
│   │   │   ├── client.ts        # Auth client
│   │   │   ├── server.ts        # Server auth
│   │   │   └── types.ts         # Auth types
│   │   ├── api/                 # API utilities
│   │   │   ├── client.ts        # API client
│   │   │   ├── endpoints.ts     # API endpoints
│   │   │   ├── types.ts         # API types
│   │   │   └── middleware.ts    # API middleware
│   │   ├── database/            # Database utilities
│   │   │   ├── supabase.ts      # Supabase client
│   │   │   ├── queries.ts       # Database queries
│   │   │   └── types.ts         # Database types
│   │   ├── validation/          # Validation schemas
│   │   │   ├── auth.ts          # Auth validation
│   │   │   ├── shares.ts        # Share validation
│   │   │   ├── payments.ts      # Payment validation
│   │   │   ├── kyc.ts           # KYC validation
│   │   │   └── common.ts        # Common validation
│   │   ├── utils/               # General utilities
│   │   │   ├── cn.ts            # Class name utility
│   │   │   ├── format.ts        # Formatting utilities
│   │   │   ├── date.ts          # Date utilities
│   │   │   ├── currency.ts      # Currency utilities
│   │   │   ├── crypto.ts        # Encryption utilities
│   │   │   └── constants.ts     # App constants
│   │   ├── stores/              # Zustand stores
│   │   │   ├── auth-store.ts    # Auth state
│   │   │   ├── shares-store.ts  # Shares state
│   │   │   ├── ui-store.ts      # UI state
│   │   │   └── index.ts         # Store exports
│   │   ├── config/              # Configuration
│   │   │   ├── env.ts           # Environment config
│   │   │   ├── database.ts      # Database config
│   │   │   ├── auth.ts          # Auth config
│   │   │   └── app.ts           # App config
│   │   └── monitoring/          # Monitoring utilities
│   │       ├── sentry.ts        # Error tracking
│   │       ├── analytics.ts     # Analytics
│   │       └── performance.ts   # Performance monitoring
│   ├── 
│   ├── styles/                  # Styling
│   │   ├── globals.css          # Global styles
│   │   ├── components.css       # Component styles
│   │   └── utilities.css        # Utility classes
│   ├── 
│   └── types/                   # TypeScript type definitions
│       ├── auth.ts              # Authentication types
│       ├── shares.ts            # Share types
│       ├── payments.ts          # Payment types
│       ├── kyc.ts               # KYC types
│       ├── referrals.ts         # Referral types
│       ├── admin.ts             # Admin types
│       ├── api.ts               # API types
│       ├── database.ts          # Database types
│       └── global.ts            # Global types
└── 
└── tests/                       # Test files
    ├── __mocks__/               # Mock files
    ├── components/              # Component tests
    ├── hooks/                   # Hook tests
    ├── lib/                     # Library tests
    ├── pages/                   # Page tests
    └── utils/                   # Test utilities
```

## Build and Deployment Scripts

### Enhanced Package.json Scripts
```json
{
  "scripts": {
    "dev": "next dev",
    "dev:turbo": "next dev --turbo",
    "build": "next build",
    "build:analyze": "ANALYZE=true npm run build",
    "start": "next start",
    "lint": "next lint",
    "lint:fix": "next lint --fix",
    "type-check": "tsc --noEmit",
    "format": "prettier --write .",
    "format:check": "prettier --check .",
    
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:ci": "jest --ci --coverage --watchAll=false",
    "test:e2e": "playwright test",
    "test:e2e:ui": "playwright test --ui",
    "test:e2e:headed": "playwright test --headed",
    "test:cypress": "cypress open",
    "test:cypress:run": "cypress run",
    
    "storybook": "storybook dev -p 6006",
    "build-storybook": "storybook build",
    "storybook:test": "test-storybook",
    
    "db:generate": "supabase gen types typescript --project-id your-project-id > src/types/database.ts",
    "db:push": "supabase db push",
    "db:pull": "supabase db pull",
    "db:reset": "supabase db reset",
    
    "prepare": "husky install",
    "commit": "git-cz",
    "pre-commit": "lint-staged",
    "validate": "npm run type-check && npm run lint && npm run test:ci",
    "validate:full": "npm run validate && npm run test:e2e",
    
    "clean": "rimraf dist coverage",
    "clean:deps": "rimraf node_modules package-lock.json && npm install",
    "clean:all": "npm run clean && npm run clean:deps",
    
    "deploy:staging": "vercel --target preview",
    "deploy:production": "vercel --prod",
    "deploy:local": "npm run build && npm start",
    
    "security:audit": "npm audit --audit-level=moderate",
    "security:fix": "npm audit fix",
    "deps:check": "npx npm-check-updates",
    "deps:update": "npx npm-check-updates -u && npm install"
  }
}
```

### Vite Configuration
```javascript
// vite.config.ts
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 8000,
    host: true
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          supabase: ['@supabase/supabase-js']
        }
      }
    }
  },
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development')
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, '.')
    }
  }
});
```

## Environment Variables and Secrets Management

### Environment Configuration Files
```bash
# .env.example - Template for environment variables
# Copy this file to .env.local and fill in your values

# Application Environment
NODE_ENV=development
NEXT_PUBLIC_APP_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
SUPABASE_PROJECT_ID=your-project-id

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your-bot-token
TELEGRAM_BOT_USERNAME=AureusAllianceBot
TELEGRAM_WEBHOOK_SECRET=your-webhook-secret

# Authentication & Security
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters
ENCRYPTION_KEY=your-32-byte-hex-encryption-key-64-characters
SESSION_SECRET=your-session-secret-key

# API Configuration
API_BASE_URL=http://localhost:3000/api
WEBHOOK_SECRET=your-webhook-secret-key
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000

# Database Configuration (if using local)
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/aureus_dev
REDIS_URL=redis://localhost:6379

# External Services
SENDGRID_API_KEY=your-sendgrid-key
STRIPE_SECRET_KEY=your-stripe-secret
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable

# Analytics and Monitoring
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=your-analytics-id
SENTRY_DSN=your-sentry-dsn
SENTRY_ORG=your-sentry-org
SENTRY_PROJECT=your-sentry-project
SENTRY_AUTH_TOKEN=your-sentry-auth-token

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_ERROR_REPORTING=true
NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING=true

# Development Settings
NEXT_PUBLIC_SHOW_DEBUG_INFO=false
ENABLE_EXPERIMENTAL_FEATURES=false
```

### Environment Validation
```typescript
// src/lib/config/env.ts
import { z } from 'zod';

// Environment schema with validation
const envSchema = z.object({
  // Node environment
  NODE_ENV: z.enum(['development', 'staging', 'production']).default('development'),
  
  // Public environment variables (available in browser)
  NEXT_PUBLIC_APP_ENV: z.enum(['development', 'staging', 'production']),
  NEXT_PUBLIC_APP_URL: z.string().url(),
  NEXT_PUBLIC_SUPABASE_URL: z.string().url(),
  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string(),
  NEXT_PUBLIC_VERCEL_ANALYTICS_ID: z.string().optional(),
  NEXT_PUBLIC_ENABLE_ANALYTICS: z.string().transform(val => val === 'true').default('false'),
  NEXT_PUBLIC_ENABLE_ERROR_REPORTING: z.string().transform(val => val === 'true').default('false'),
  NEXT_PUBLIC_SHOW_DEBUG_INFO: z.string().transform(val => val === 'true').default('false'),
  
  // Server-side environment variables (secure)
  SUPABASE_SERVICE_ROLE_KEY: z.string(),
  SUPABASE_PROJECT_ID: z.string(),
  
  // Telegram configuration
  TELEGRAM_BOT_TOKEN: z.string(),
  TELEGRAM_BOT_USERNAME: z.string(),
  TELEGRAM_WEBHOOK_SECRET: z.string(),
  
  // Authentication & Security
  JWT_SECRET: z.string().min(32, 'JWT secret must be at least 32 characters'),
  ENCRYPTION_KEY: z.string().length(64, 'Encryption key must be exactly 64 characters (32 bytes in hex)'),
  SESSION_SECRET: z.string().min(32),
  
  // API configuration
  API_BASE_URL: z.string().url().optional(),
  WEBHOOK_SECRET: z.string(),
  RATE_LIMIT_MAX: z.string().transform(val => parseInt(val, 10)).default('100'),
  RATE_LIMIT_WINDOW: z.string().transform(val => parseInt(val, 10)).default('900000'),
  
  // Database (optional for local development)
  DATABASE_URL: z.string().url().optional(),
  REDIS_URL: z.string().url().optional(),
  
  // External services (optional)
  SENDGRID_API_KEY: z.string().optional(),
  STRIPE_SECRET_KEY: z.string().optional(),
  STRIPE_PUBLISHABLE_KEY: z.string().optional(),
  
  // Monitoring
  SENTRY_DSN: z.string().url().optional(),
  SENTRY_ORG: z.string().optional(),
  SENTRY_PROJECT: z.string().optional(),
  SENTRY_AUTH_TOKEN: z.string().optional(),
  
  // Feature flags
  ENABLE_EXPERIMENTAL_FEATURES: z.string().transform(val => val === 'true').default('false'),
});

// Parse and validate environment variables
const parseEnv = () => {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    console.error('❌ Invalid environment variables:');
    if (error instanceof z.ZodError) {
      error.errors.forEach((err) => {
        console.error(`  ${err.path.join('.')}: ${err.message}`);
      });
    }
    process.exit(1);
  }
};

export const env = parseEnv();

// Type for environment variables
export type Env = z.infer<typeof envSchema>;

// Utility to check if we're in a specific environment
export const isDevelopment = env.NODE_ENV === 'development';
export const isProduction = env.NODE_ENV === 'production';
export const isStaging = env.NODE_ENV === 'staging';
export const isServer = typeof window === 'undefined';
export const isClient = typeof window !== 'undefined';
```

## Logging and Monitoring Tools

### Structured Logging Setup
```typescript
// src/lib/monitoring/logger.ts
import { env, isDevelopment, isProduction } from '@/lib/config/env';

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

interface LogEntry {
  timestamp: string;
  level: string;
  message: string;
  context?: Record<string, any>;
  error?: Error;
  userId?: string;
  sessionId?: string;
  requestId?: string;
}

class Logger {
  private minLevel: LogLevel;
  
  constructor(minLevel: LogLevel = isDevelopment ? LogLevel.DEBUG : LogLevel.INFO) {
    this.minLevel = minLevel;
  }
  
  private shouldLog(level: LogLevel): boolean {
    return level >= this.minLevel;
  }
  
  private createLogEntry(
    level: LogLevel,
    message: string,
    context?: Record<string, any>,
    error?: Error
  ): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level: LogLevel[level],
      message,
      context,
      error,
      userId: context?.userId,
      sessionId: context?.sessionId,
      requestId: context?.requestId,
    };
  }
  
  private output(entry: LogEntry): void {
    if (isDevelopment) {
      // Development: Pretty console output
      const color = this.getLevelColor(entry.level);
      console.log(
        `${color}[${entry.timestamp}] ${entry.level}:${'\x1b[0m'} ${entry.message}`,
        entry.context ? entry.context : ''
      );
      if (entry.error) {
        console.error(entry.error);
      }
    } else {
      // Production: Structured JSON logging
      console.log(JSON.stringify(entry));
    }
    
    // Send to external logging service in production
    if (isProduction) {
      this.sendToExternalService(entry);
    }
  }
  
  private getLevelColor(level: string): string {
    const colors = {
      DEBUG: '\x1b[36m', // Cyan
      INFO: '\x1b[32m',  // Green
      WARN: '\x1b[33m',  // Yellow
      ERROR: '\x1b[31m', // Red
    };
    return colors[level as keyof typeof colors] || '\x1b[0m';
  }
  
  private async sendToExternalService(entry: LogEntry): Promise<void> {
    // Send to Vercel Analytics, Sentry, or other logging service
    try {
      if (env.SENTRY_DSN && entry.level === 'ERROR') {
        // Send errors to Sentry (implemented in sentry.ts)
      }
      
      // Could also send to other services like LogRocket, DataDog, etc.
    } catch (error) {
      // Fail silently to avoid infinite loops
      console.error('Failed to send log to external service:', error);
    }
  }
  
  debug(message: string, context?: Record<string, any>): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      this.output(this.createLogEntry(LogLevel.DEBUG, message, context));
    }
  }
  
  info(message: string, context?: Record<string, any>): void {
    if (this.shouldLog(LogLevel.INFO)) {
      this.output(this.createLogEntry(LogLevel.INFO, message, context));
    }
  }
  
  warn(message: string, context?: Record<string, any>): void {
    if (this.shouldLog(LogLevel.WARN)) {
      this.output(this.createLogEntry(LogLevel.WARN, message, context));
    }
  }
  
  error(message: string, error?: Error, context?: Record<string, any>): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      this.output(this.createLogEntry(LogLevel.ERROR, message, context, error));
    }
  }
  
  // Specific logging methods for common use cases
  httpRequest(method: string, url: string, statusCode: number, duration: number, context?: Record<string, any>): void {
    this.info(`${method} ${url} ${statusCode} - ${duration}ms`, {
      ...context,
      httpMethod: method,
      httpUrl: url,
      httpStatusCode: statusCode,
      httpDuration: duration,
    });
  }
  
  databaseQuery(query: string, duration: number, context?: Record<string, any>): void {
    this.debug(`Database query executed - ${duration}ms`, {
      ...context,
      dbQuery: query,
      dbDuration: duration,
    });
  }
  
  userAction(action: string, userId: string, context?: Record<string, any>): void {
    this.info(`User action: ${action}`, {
      ...context,
      userId,
      userAction: action,
    });
  }
  
  securityEvent(event: string, context?: Record<string, any>): void {
    this.warn(`Security event: ${event}`, {
      ...context,
      securityEvent: event,
    });
  }
}

// Export singleton logger instance
export const logger = new Logger();

// Export logger instance for different contexts
export const createLogger = (context: Record<string, any>) => {
  return {
    debug: (message: string, additionalContext?: Record<string, any>) =>
      logger.debug(message, { ...context, ...additionalContext }),
    info: (message: string, additionalContext?: Record<string, any>) =>
      logger.info(message, { ...context, ...additionalContext }),
    warn: (message: string, additionalContext?: Record<string, any>) =>
      logger.warn(message, { ...context, ...additionalContext }),
    error: (message: string, error?: Error, additionalContext?: Record<string, any>) =>
      logger.error(message, error, { ...context, ...additionalContext }),
  };
};
```

### Sentry Error Tracking Setup
```typescript
// src/lib/monitoring/sentry.ts
import * as Sentry from '@sentry/nextjs';
import { env, isProduction, isDevelopment } from '@/lib/config/env';

// Initialize Sentry
export const initSentry = () => {
  if (!env.SENTRY_DSN) {
    console.warn('Sentry DSN not configured, error tracking disabled');
    return;
  }
  
  Sentry.init({
    dsn: env.SENTRY_DSN,
    environment: env.NODE_ENV,
    
    // Performance monitoring
    tracesSampleRate: isProduction ? 0.1 : 1.0,
    
    // Session replay
    replaysSessionSampleRate: isProduction ? 0.1 : 1.0,
    replaysOnErrorSampleRate: 1.0,
    
    // Error filtering
    beforeSend(event, hint) {
      // Filter out development errors
      if (isDevelopment) {
        console.error('Sentry error:', hint.originalException || hint.syntheticException);
      }
      
      // Filter sensitive information
      if (event.exception) {
        const error = hint.originalException;
        if (error instanceof Error) {
          // Remove sensitive data from error messages
          event.exception.values = event.exception.values?.map(value => ({
            ...value,
            value: value.value?.replace(/password=\w+/gi, 'password=[REDACTED]'),
          }));
        }
      }
      
      return event;
    },
    
    // Additional configuration
    integrations: [
      new Sentry.BrowserTracing({
        // Route change tracking
        routingInstrumentation: Sentry.nextRouterInstrumentation,
      }),
      new Sentry.Replay({
        maskAllText: true,
        blockAllMedia: true,
      }),
    ],
  });
};

// Custom error boundary component
export const SentryErrorBoundary = Sentry.withErrorBoundary;

// Error reporting utilities
export const reportError = (error: Error, context?: Record<string, any>) => {
  Sentry.withScope((scope) => {
    if (context) {
      Object.keys(context).forEach(key => {
        scope.setTag(key, context[key]);
      });
    }
    Sentry.captureException(error);
  });
};

export const reportMessage = (message: string, level: Sentry.SeverityLevel = 'info', context?: Record<string, any>) => {
  Sentry.withScope((scope) => {
    if (context) {
      Object.keys(context).forEach(key => {
        scope.setTag(key, context[key]);
      });
    }
    Sentry.captureMessage(message, level);
  });
};

// User context management
export const setUserContext = (user: { id: string; email?: string; username?: string }) => {
  Sentry.setUser({
    id: user.id,
    email: user.email,
    username: user.username,
  });
};

export const clearUserContext = () => {
  Sentry.setUser(null);
};
```

### Performance Monitoring
```typescript
// src/lib/monitoring/performance.ts
import { getCLS, getFID, getFCP, getLCP, getTTFB, Metric } from 'web-vitals';
import { env, isProduction } from '@/lib/config/env';
import { logger } from './logger';

interface PerformanceMetric extends Metric {
  url: string;
  userAgent: string;
}

// Web Vitals tracking
export const trackWebVitals = () => {
  const sendToAnalytics = (metric: PerformanceMetric) => {
    logger.info(`Web Vital: ${metric.name}`, {
      name: metric.name,
      value: metric.value,
      rating: metric.rating,
      url: metric.url,
      userAgent: metric.userAgent,
    });
    
    // Send to external analytics service
    if (isProduction && env.NEXT_PUBLIC_VERCEL_ANALYTICS_ID) {
      // Send to Vercel Analytics
      try {
        navigator.sendBeacon('/api/analytics/web-vitals', JSON.stringify(metric));
      } catch (error) {
        console.warn('Failed to send web vitals to analytics:', error);
      }
    }
  };
  
  const enrichMetric = (metric: Metric): PerformanceMetric => ({
    ...metric,
    url: window.location.href,
    userAgent: navigator.userAgent,
  });
  
  // Track all Core Web Vitals
  getCLS((metric) => sendToAnalytics(enrichMetric(metric)));
  getFID((metric) => sendToAnalytics(enrichMetric(metric)));
  getFCP((metric) => sendToAnalytics(enrichMetric(metric)));
  getLCP((metric) => sendToAnalytics(enrichMetric(metric)));
  getTTFB((metric) => sendToAnalytics(enrichMetric(metric)));
};

// Performance measurement utilities
export class PerformanceTracker {
  private measurements = new Map<string, number>();
  
  start(name: string): void {
    this.measurements.set(name, performance.now());
  }
  
  end(name: string): number {
    const startTime = this.measurements.get(name);
    if (!startTime) {
      logger.warn(`Performance measurement '${name}' was never started`);
      return 0;
    }
    
    const duration = performance.now() - startTime;
    this.measurements.delete(name);
    
    logger.debug(`Performance: ${name} took ${duration.toFixed(2)}ms`);
    return duration;
  }
  
  measure<T>(name: string, fn: () => T): T;
  measure<T>(name: string, fn: () => Promise<T>): Promise<T>;
  measure<T>(name: string, fn: () => T | Promise<T>): T | Promise<T> {
    this.start(name);
    
    try {
      const result = fn();
      
      if (result instanceof Promise) {
        return result.finally(() => this.end(name));
      } else {
        this.end(name);
        return result;
      }
    } catch (error) {
      this.end(name);
      throw error;
    }
  }
}

// Global performance tracker instance
export const performanceTracker = new PerformanceTracker();

// React performance hook
export const usePerformanceTracker = () => {
  return {
    startMeasurement: performanceTracker.start.bind(performanceTracker),
    endMeasurement: performanceTracker.end.bind(performanceTracker),
    measure: performanceTracker.measure.bind(performanceTracker),
  };
};
```

## Basic Project Structure Implementation

### Root Layout Component
```typescript
// src/app/layout.tsx
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { ThemeProvider } from '@/components/providers/theme-provider';
import { AuthProvider } from '@/components/providers/auth-provider';
import { ToastProvider } from '@/components/providers/toast-provider';
import { initSentry } from '@/lib/monitoring/sentry';
import { trackWebVitals } from '@/lib/monitoring/performance';
import '@/styles/globals.css';

const inter = Inter({ subsets: ['latin'] });

// Initialize monitoring in production
if (typeof window !== 'undefined') {
  initSentry();
  trackWebVitals();
}

export const metadata: Metadata = {
  title: {
    default: 'Aureus Alliance Dashboard',
    template: '%s | Aureus Alliance',
  },
  description: 'Secure web dashboard for Aureus Alliance share management and investment tracking.',
  keywords: ['aureus', 'alliance', 'investment', 'shares', 'dashboard', 'blockchain'],
  authors: [{ name: 'Aureus Alliance Team' }],
  creator: 'Aureus Alliance',
  publisher: 'Aureus Alliance',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: process.env.NEXT_PUBLIC_APP_URL,
    title: 'Aureus Alliance Dashboard',
    description: 'Secure web dashboard for Aureus Alliance share management and investment tracking.',
    siteName: 'Aureus Alliance',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Aureus Alliance Dashboard',
    description: 'Secure web dashboard for Aureus Alliance share management and investment tracking.',
    creator: '@aureusalliance',
  },
  robots: {
    index: false, // Private dashboard
    follow: false,
    googleBot: {
      index: false,
      follow: false,
    },
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
          <AuthProvider>
            <ToastProvider>
              {children}
            </ToastProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
```

### Main Page Component
```typescript
// src/app/page.tsx
import { redirect } from 'next/navigation';
import { createServerClient } from '@/lib/auth/server';
import { DashboardContent } from '@/components/features/dashboard/dashboard-content';
import { AuthGuard } from '@/components/features/auth/auth-guard';

export default async function HomePage() {
  const supabase = createServerClient();
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      redirect('/login');
    }
    
    return (
      <AuthGuard>
        <DashboardContent />
      </AuthGuard>
    );
  } catch (error) {
    console.error('Error in HomePage:', error);
    redirect('/login');
  }
}
```

### Global Error Boundary
```typescript
// src/app/error.tsx
'use client';

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { reportError } from '@/lib/monitoring/sentry';
import { logger } from '@/lib/monitoring/logger';

interface ErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function Error({ error, reset }: ErrorProps) {
  useEffect(() => {
    // Log error to monitoring services
    logger.error('Application error occurred', error);
    reportError(error, { digest: error.digest });
  }, [error]);
  
  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
            <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
          </div>
          <CardTitle className="text-xl">Something went wrong</CardTitle>
          <CardDescription>
            An unexpected error occurred. Our team has been notified and is working on a fix.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button
            onClick={reset}
            className="w-full"
            variant="outline"
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Try again
          </Button>
          <Button
            onClick={() => window.location.href = '/'}
            className="w-full"
            variant="default"
          >
            Go to Dashboard
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
```

### Loading Component
```typescript
// src/app/loading.tsx
import { Loader2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

export default function Loading() {
  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardContent className="flex flex-col items-center justify-center p-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
          <h2 className="text-lg font-semibold mb-2">Loading Dashboard</h2>
          <p className="text-muted-foreground text-center">
            Please wait while we prepare your investment data...
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
```

### Not Found Page
```typescript
// src/app/not-found.tsx
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Home, Search } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800">
            <Search className="h-6 w-6 text-gray-600 dark:text-gray-400" />
          </div>
          <CardTitle className="text-xl">Page Not Found</CardTitle>
          <CardDescription>
            The page you're looking for doesn't exist or has been moved.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button asChild className="w-full">
            <Link href="/">
              <Home className="mr-2 h-4 w-4" />
              Go to Dashboard
            </Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
```

## API Client and Service Layer Setup

### Base API Client
```typescript
// src/lib/api/client.ts
import { env } from '@/lib/config/env';
import { logger } from '@/lib/monitoring/logger';
import { performanceTracker } from '@/lib/monitoring/performance';

export interface APIResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
  success: boolean;
  meta?: {
    total?: number;
    page?: number;
    limit?: number;
  };
}

export interface APIError {
  message: string;
  status: number;
  code?: string;
  details?: any;
}

class APIClientError extends Error {
  status: number;
  code?: string;
  details?: any;
  
  constructor(message: string, status: number, code?: string, details?: any) {
    super(message);
    this.name = 'APIClientError';
    this.status = status;
    this.code = code;
    this.details = details;
  }
}

export class APIClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;
  
  constructor(baseURL?: string) {
    this.baseURL = baseURL || env.API_BASE_URL || '/api';
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
  }
  
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<APIResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    const requestId = crypto.randomUUID();
    
    // Performance tracking
    const measurementName = `api-${endpoint}`;
    performanceTracker.start(measurementName);
    
    try {
      // Prepare headers
      const headers = {
        ...this.defaultHeaders,
        'X-Request-ID': requestId,
        ...options.headers,
      };
      
      // Add auth token if available
      const token = await this.getAuthToken();
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
      
      // Log request
      logger.debug(`API Request: ${options.method || 'GET'} ${endpoint}`, {
        requestId,
        url,
        method: options.method || 'GET',
      });
      
      // Make request
      const response = await fetch(url, {
        ...options,
        headers,
      });
      
      const duration = performanceTracker.end(measurementName);
      
      // Log response
      logger.httpRequest(
        options.method || 'GET',
        endpoint,
        response.status,
        duration,
        { requestId }
      );
      
      // Parse response
      let data: APIResponse<T>;
      try {
        data = await response.json();
      } catch (parseError) {
        throw new APIClientError(
          'Failed to parse response',
          response.status,
          'PARSE_ERROR'
        );
      }
      
      // Handle error responses
      if (!response.ok) {
        throw new APIClientError(
          data.error || data.message || 'Request failed',
          response.status,
          data.error,
          data
        );
      }
      
      return data;
    } catch (error) {
      performanceTracker.end(measurementName);
      
      if (error instanceof APIClientError) {
        logger.error(`API Error: ${error.message}`, error, {
          requestId,
          endpoint,
          status: error.status,
        });
        throw error;
      }
      
      // Network or other errors
      logger.error(`API Request Failed: ${endpoint}`, error as Error, {
        requestId,
        endpoint,
      });
      
      throw new APIClientError(
        'Network error occurred',
        0,
        'NETWORK_ERROR',
        error
      );
    }
  }
  
  private async getAuthToken(): Promise<string | null> {
    if (typeof window !== 'undefined') {
      // Client-side: Get from localStorage or state management
      const token = localStorage.getItem('auth-token');
      return token;
    } else {
      // Server-side: Get from cookies or headers
      const { cookies } = await import('next/headers');
      const token = cookies().get('auth-token')?.value;
      return token || null;
    }
  }
  
  // HTTP methods
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<APIResponse<T>> {
    const url = params ? `${endpoint}?${new URLSearchParams(params)}` : endpoint;
    return this.request<T>(url, { method: 'GET' });
  }
  
  async post<T>(endpoint: string, data?: any): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }
  
  async put<T>(endpoint: string, data?: any): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }
  
  async patch<T>(endpoint: string, data?: any): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }
  
  async delete<T>(endpoint: string): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
  
  // File upload
  async upload<T>(endpoint: string, file: File, data?: Record<string, any>): Promise<APIResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);
    
    if (data) {
      Object.entries(data).forEach(([key, value]) => {
        formData.append(key, String(value));
      });
    }
    
    return this.request<T>(endpoint, {
      method: 'POST',
      body: formData,
      headers: {
        // Don't set Content-Type for FormData, let browser set it
      },
    });
  }
}

// Export singleton instance
export const apiClient = new APIClient();

// Export error class
export { APIClientError };
```

### Service Layer Implementation
```typescript
// src/lib/api/services/auth-service.ts
import { apiClient, APIResponse } from '../client';
import type { User, Session } from '@/types/auth';

export interface LoginCredentials {
  telegramData: {
    id: number;
    first_name: string;
    last_name?: string;
    username?: string;
    auth_date: number;
    hash: string;
  };
}

export interface LoginResponse {
  user: User;
  session: Session;
  token: string;
}

export class AuthService {
  async login(credentials: LoginCredentials): Promise<APIResponse<LoginResponse>> {
    return apiClient.post<LoginResponse>('/auth/telegram/login', credentials);
  }
  
  async logout(): Promise<APIResponse<void>> {
    return apiClient.post<void>('/auth/logout');
  }
  
  async refreshToken(): Promise<APIResponse<{ token: string }>> {
    return apiClient.post<{ token: string }>('/auth/refresh');
  }
  
  async getProfile(): Promise<APIResponse<User>> {
    return apiClient.get<User>('/auth/profile');
  }
  
  async updateProfile(data: Partial<User>): Promise<APIResponse<User>> {
    return apiClient.patch<User>('/auth/profile', data);
  }
  
  async changePassword(currentPassword: string, newPassword: string): Promise<APIResponse<void>> {
    return apiClient.post<void>('/auth/change-password', {
      currentPassword,
      newPassword,
    });
  }
  
  async requestPasswordReset(email: string): Promise<APIResponse<void>> {
    return apiClient.post<void>('/auth/reset-password', { email });
  }
  
  async resetPassword(token: string, newPassword: string): Promise<APIResponse<void>> {
    return apiClient.post<void>('/auth/reset-password/confirm', {
      token,
      newPassword,
    });
  }
  
  async enableTwoFactor(): Promise<APIResponse<{ qrCode: string; secret: string }>> {
    return apiClient.post<{ qrCode: string; secret: string }>('/auth/2fa/enable');
  }
  
  async confirmTwoFactor(token: string): Promise<APIResponse<void>> {
    return apiClient.post<void>('/auth/2fa/confirm', { token });
  }
  
  async disableTwoFactor(token: string): Promise<APIResponse<void>> {
    return apiClient.post<void>('/auth/2fa/disable', { token });
  }
}

export const authService = new AuthService();
```

### Additional Service Classes
```typescript
// src/lib/api/services/shares-service.ts
import { apiClient, APIResponse } from '../client';
import type { Share, SharePurchase, ShareHistory } from '@/types/shares';

export interface PurchaseSharesRequest {
  amount: number;
  paymentMethod: 'crypto' | 'bank_transfer';
  currency: string;
}

export class SharesService {
  async getShares(): Promise<APIResponse<Share[]>> {
    return apiClient.get<Share[]>('/shares');
  }
  
  async getShareHistory(): Promise<APIResponse<ShareHistory[]>> {
    return apiClient.get<ShareHistory[]>('/shares/history');
  }
  
  async purchaseShares(data: PurchaseSharesRequest): Promise<APIResponse<SharePurchase>> {
    return apiClient.post<SharePurchase>('/shares/purchase', data);
  }
  
  async getSharePrice(): Promise<APIResponse<{ price: number; currency: string }>> {
    return apiClient.get<{ price: number; currency: string }>('/shares/price');
  }
  
  async getSharePriceHistory(days: number = 30): Promise<APIResponse<Array<{ date: string; price: number }>>> {
    return apiClient.get<Array<{ date: string; price: number }>>(`/shares/price-history?days=${days}`);
  }
}

export const sharesService = new SharesService();
```

```typescript
// src/lib/api/services/payments-service.ts
import { apiClient, APIResponse } from '../client';
import type { Payment, PaymentMethod } from '@/types/payments';

export interface CreatePaymentRequest {
  amount: number;
  currency: string;
  method: 'crypto' | 'bank_transfer';
  description?: string;
}

export class PaymentsService {
  async getPayments(): Promise<APIResponse<Payment[]>> {
    return apiClient.get<Payment[]>('/payments');
  }
  
  async getPayment(id: string): Promise<APIResponse<Payment>> {
    return apiClient.get<Payment>(`/payments/${id}`);
  }
  
  async createPayment(data: CreatePaymentRequest): Promise<APIResponse<Payment>> {
    return apiClient.post<Payment>('/payments', data);
  }
  
  async getPaymentMethods(): Promise<APIResponse<PaymentMethod[]>> {
    return apiClient.get<PaymentMethod[]>('/payments/methods');
  }
  
  async addPaymentMethod(data: Partial<PaymentMethod>): Promise<APIResponse<PaymentMethod>> {
    return apiClient.post<PaymentMethod>('/payments/methods', data);
  }
  
  async removePaymentMethod(id: string): Promise<APIResponse<void>> {
    return apiClient.delete<void>(`/payments/methods/${id}`);
  }
}

export const paymentsService = new PaymentsService();
```

### Service Index Export
```typescript
// src/lib/api/services/index.ts
export { authService, AuthService } from './auth-service';
export { sharesService, SharesService } from './shares-service';
export { paymentsService, PaymentsService } from './payments-service';

// Export all service types
export type * from './auth-service';
export type * from './shares-service';
export type * from './payments-service';
```

## Implementation Checklist

### Phase 3.2 Project Foundation Checklist
- [ ] **Project Initialization**
  - [ ] Create Next.js 14+ project structure
  - [ ] Install all required dependencies
  - [ ] Set up folder structure according to architecture
  - [ ] Configure TypeScript and ESLint

- [ ] **Build and Deployment**
  - [ ] Configure Next.js build settings
  - [ ] Set up package.json scripts
  - [ ] Configure Vercel deployment
  - [ ] Test build and deployment process

- [ ] **Environment Management**
  - [ ] Create environment variable templates
  - [ ] Set up environment validation
  - [ ] Configure development/staging/production environments
  - [ ] Test environment variable loading

- [ ] **Logging and Monitoring**
  - [ ] Implement structured logging system
  - [ ] Set up Sentry error tracking
  - [ ] Configure performance monitoring
  - [ ] Test monitoring in development

- [ ] **Project Structure**
  - [ ] Create all folder structures
  - [ ] Implement root layout and error boundaries
  - [ ] Set up global providers
  - [ ] Create basic page components

- [ ] **API Client Setup**
  - [ ] Implement base API client
  - [ ] Create service layer classes
  - [ ] Set up authentication integration
  - [ ] Test API client functionality

### Success Criteria
- [ ] ✅ Project structure matches technical architecture
- [ ] ✅ All dependencies installed and configured
- [ ] ✅ Build process works without errors
- [ ] ✅ Environment variables properly validated
- [ ] ✅ Logging and monitoring functional
- [ ] ✅ API client can make authenticated requests

---

**Project Foundation Status**: IMPLEMENTATION READY
**Architecture**: NEXT.JS 14+ STRUCTURE COMPLETE
**API Layer**: SERVICE CLASSES IMPLEMENTED
**Monitoring**: LOGGING & ERROR TRACKING CONFIGURED

*This project foundation provides a robust, scalable, and maintainable codebase structure that follows enterprise-grade development practices and is ready for feature implementation.*
