#!/usr/bin/env node

/**
 * Create Test User Data
 * 
 * This script creates the necessary database records for testing
 * the dashboard commission display functionality.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ Missing SUPABASE_SERVICE_ROLE_KEY in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createTestUserData() {
  console.log('🔧 Creating Test User Data for Dashboard Testing\n');

  try {
    // 1. Check if test user exists
    console.log('1. Checking for existing test user...');
    const { data: existingUser, error: userCheckError } = await supabase
      .from('users')
      .select('id, email, username')
      .eq('email', '<EMAIL>')
      .single();

    let userId;
    if (existingUser) {
      console.log('✅ Found existing user:', existingUser);
      userId = existingUser.id;
    } else {
      console.log('❌ No existing user found, creating new test user...');
      
      // Create test user
      const { data: newUser, error: createUserError } = await supabase
        .from('users')
        .insert({
          email: '<EMAIL>',
          username: 'testadmin',
          first_name: 'Test',
          last_name: 'Admin',
          is_active: true
        })
        .select()
        .single();

      if (createUserError) {
        console.error('❌ Error creating user:', createUserError);
        return;
      }

      console.log('✅ Created new user:', newUser);
      userId = newUser.id;
    }

    // 2. Create commission balance record
    console.log('\n2. Creating commission balance record...');
    const { data: existingBalance, error: balanceCheckError } = await supabase
      .from('commission_balances')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (existingBalance) {
      console.log('✅ Commission balance already exists:', existingBalance);
    } else {
      const { data: newBalance, error: createBalanceError } = await supabase
        .from('commission_balances')
        .insert({
          user_id: userId,
          usdt_balance: 3582.75,
          share_balance: 0,
          total_earned_usdt: 3582.75,
          total_earned_shares: 717,
          escrowed_amount: 0.00,
          total_withdrawn_usdt: 0.00
        })
        .select()
        .single();

      if (createBalanceError) {
        console.error('❌ Error creating commission balance:', createBalanceError);
      } else {
        console.log('✅ Created commission balance:', newBalance);
      }
    }

    // 3. Create some test share purchases
    console.log('\n3. Creating test share purchases...');
    const { data: existingShares, error: sharesCheckError } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .eq('user_id', userId);

    if (existingShares && existingShares.length > 0) {
      console.log('✅ Share purchases already exist:', existingShares.length, 'records');
    } else {
      const { data: newShares, error: createSharesError } = await supabase
        .from('aureus_share_purchases')
        .insert([
          {
            user_id: userId,
            shares_purchased: 1000,
            total_amount: 5000.00,
            status: 'active',
            purchase_date: new Date().toISOString()
          },
          {
            user_id: userId,
            shares_purchased: 500,
            total_amount: 2500.00,
            status: 'active',
            purchase_date: new Date().toISOString()
          }
        ])
        .select();

      if (createSharesError) {
        console.error('❌ Error creating share purchases:', createSharesError);
      } else {
        console.log('✅ Created share purchases:', newShares.length, 'records');
      }
    }

    // 4. Create some test referrals
    console.log('\n4. Creating test referrals...');
    const { data: existingReferrals, error: referralsCheckError } = await supabase
      .from('referrals')
      .select('*')
      .eq('referrer_id', userId);

    if (existingReferrals && existingReferrals.length > 0) {
      console.log('✅ Referrals already exist:', existingReferrals.length, 'records');
    } else {
      // Create a few test referred users first
      const { data: referredUsers, error: createReferredError } = await supabase
        .from('users')
        .insert([
          {
            email: '<EMAIL>',
            username: 'referred1',
            first_name: 'Referred',
            last_name: 'User1',
            is_active: true
          },
          {
            email: '<EMAIL>',
            username: 'referred2',
            first_name: 'Referred',
            last_name: 'User2',
            is_active: true
          }
        ])
        .select();

      if (!createReferredError && referredUsers) {
        // Create referral records
        const { data: newReferrals, error: createReferralsError } = await supabase
          .from('referrals')
          .insert([
            {
              referrer_id: userId,
              referred_id: referredUsers[0].id,
              created_at: new Date().toISOString()
            },
            {
              referrer_id: userId,
              referred_id: referredUsers[1].id,
              created_at: new Date().toISOString()
            }
          ])
          .select();

        if (createReferralsError) {
          console.error('❌ Error creating referrals:', createReferralsError);
        } else {
          console.log('✅ Created referrals:', newReferrals.length, 'records');
        }
      }
    }

    console.log('\n🎉 Test user data creation complete!');
    console.log('\n📊 SUMMARY:');
    console.log(`• User ID: ${userId}`);
    console.log('• Email: <EMAIL>');
    console.log('• USDT Commissions: $3,582.75');
    console.log('• Share Commissions: 717 shares');
    console.log('• Share Purchases: 1,500 shares ($7,500 value)');
    console.log('• Referrals: 2 users');
    console.log('\nNow test the dashboard to see the commission display!');

  } catch (error) {
    console.error('❌ Error creating test data:', error);
  }
}

createTestUserData();
