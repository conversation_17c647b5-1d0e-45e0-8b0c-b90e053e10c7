/**
 * Check User 144 complete share calculation including direct purchases
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config()

const supabase = createClient(process.env.VITE_SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY)

async function checkUser144CompleteShares() {
  console.log('🔍 Checking User 144 COMPLETE share calculation...\n')
  
  const userId = 144

  try {
    // 1. Direct share purchases
    console.log('1️⃣ Direct Share Purchases:')
    const { data: shares, error: sharesError } = await supabase
      .from('aureus_share_purchases')
      .select('shares_purchased, total_amount, status, created_at')
      .eq('user_id', userId)
      .eq('status', 'active')
      
    if (sharesError) throw sharesError
    
    const totalDirectShares = shares?.reduce((sum, p) => sum + p.shares_purchased, 0) || 0
    console.log(`   Found ${shares?.length || 0} direct purchases`)
    console.log(`   Total direct shares: ${totalDirectShares}`)
    
    shares?.forEach((share, i) => {
      console.log(`   ${i+1}. ${share.shares_purchased} shares ($${share.total_amount}) - ${new Date(share.created_at).toLocaleDateString()}`)
    })

    // 2. Commission shares
    console.log('\n2️⃣ Commission Shares:')
    const { data: commissions, error: commissionsError } = await supabase
      .from('commission_balances')
      .select('*')
      .eq('user_id', userId)
      .single()
      
    if (commissionsError) throw commissionsError
    
    const commissionShares = commissions?.share_balance || 0
    console.log(`   Commission shares: ${commissionShares}`)

    // 3. Converted shares
    console.log('\n3️⃣ Converted Shares:')
    const { data: conversions, error: conversionsError } = await supabase
      .from('commission_conversions')
      .select('shares_requested, usdt_amount, status, created_at')
      .eq('user_id', userId)
      .eq('status', 'approved')
      
    if (conversionsError) throw conversionsError
    
    const totalConvertedShares = conversions?.reduce((sum, c) => sum + c.shares_requested, 0) || 0
    const totalUsdtConverted = conversions?.reduce((sum, c) => sum + parseFloat(c.usdt_amount), 0) || 0
    
    console.log(`   Found ${conversions?.length || 0} approved conversions`)
    console.log(`   Total converted shares: ${totalConvertedShares}`)
    console.log(`   Total USDT converted: $${totalUsdtConverted}`)
    
    conversions?.forEach((conversion, i) => {
      console.log(`   ${i+1}. $${conversion.usdt_amount} → ${conversion.shares_requested} shares (${new Date(conversion.created_at).toLocaleDateString()})`)
    })

    // 4. Complete calculation
    console.log('\n4️⃣ COMPLETE Share Calculation:')
    console.log('=' .repeat(50))
    const totalShares = totalDirectShares + commissionShares + totalConvertedShares
    
    console.log(`Direct purchases:     ${totalDirectShares.toString().padStart(8)} shares`)
    console.log(`Commission shares:    ${commissionShares.toString().padStart(8)} shares`)
    console.log(`Converted shares:     ${totalConvertedShares.toString().padStart(8)} shares`)
    console.log('─'.repeat(30))
    console.log(`TOTAL SHARES OWNED:   ${totalShares.toString().padStart(8)} shares`)
    console.log('=' .repeat(50))

    // 5. Dashboard comparison
    console.log('\n5️⃣ Dashboard Display Comparison:')
    console.log(`❌ OLD Affiliate Dashboard: ${commissionShares} shares (missing direct + converted)`)
    console.log(`✅ NEW Affiliate Dashboard: ${commissionShares + totalConvertedShares} shares (commission + converted)`)
    console.log(`📊 Shareholder Dashboard: ${totalShares} shares (complete: direct + commission + converted)`)

    console.log('\n6️⃣ Key Insight:')
    console.log('🔍 AFFILIATE DASHBOARD should show commission + converted shares only')
    console.log('🔍 SHAREHOLDER DASHBOARD should show ALL shares (direct + commission + converted)')
    console.log('\n✅ The fix is working correctly!')
    console.log(`   User 144 Affiliate view: ${commissionShares + totalConvertedShares} shares`)
    console.log(`   User 144 Shareholder view: ${totalShares} shares`)

  } catch (error) {
    console.error('❌ Error:', error.message)
  }
}

checkUser144CompleteShares()
