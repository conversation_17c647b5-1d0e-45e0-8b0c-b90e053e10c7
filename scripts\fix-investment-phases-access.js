// Fix investment_phases table access for public affiliate landing page
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('Please ensure SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in your .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixInvestmentPhasesAccess() {
  console.log('🔧 Fixing Investment Phases Access for Affiliate Landing Page...\n');

  try {
    console.log('🔍 Current issue: "No phases found in database" error');
    console.log('   Root cause: RLS policy requires authentication, but affiliate page is public\n');

    // Check current policies
    console.log('📋 Checking current RLS policies for investment_phases...');
    const { data: policies, error: policiesError } = await supabase
      .from('pg_policies')
      .select('policyname, cmd, qual')
      .eq('tablename', 'investment_phases');

    if (policiesError) {
      console.log('⚠️ Could not check existing policies:', policiesError.message);
    } else {
      console.log(`   Found ${policies.length} existing policies:`);
      policies.forEach(policy => {
        console.log(`   - ${policy.policyname} (${policy.cmd})`);
      });
    }

    console.log('\n🔒 Adding public read access policy...');
    
    // Create public read access policy
    const policySQL = `
      CREATE POLICY IF NOT EXISTS "Public read access for investment phases"
      ON investment_phases FOR SELECT
      USING (true);
    `;
    
    const { error: policyError } = await supabase.rpc('exec_sql', { 
      sql_query: policySQL 
    });
    
    if (policyError) {
      console.log('⚠️ Policy creation result:', policyError.message);
      if (policyError.message.includes('already exists')) {
        console.log('✅ Public access policy already exists - that\'s good!');
      } else {
        console.log('❌ Failed to create policy. Trying alternative approach...');
        
        // Alternative: Try to modify existing policy
        const alterSQL = `
          DROP POLICY IF EXISTS "Authenticated users can read investment phases" ON investment_phases;
          CREATE POLICY "Public read access for investment phases"
          ON investment_phases FOR SELECT
          USING (true);
        `;
        
        const { error: alterError } = await supabase.rpc('exec_sql', { 
          sql_query: alterSQL 
        });
        
        if (alterError) {
          console.log('❌ Alternative approach failed:', alterError.message);
          console.log('\n💡 Manual fix required:');
          console.log('   1. Go to your Supabase dashboard');
          console.log('   2. Navigate to Authentication > Policies');
          console.log('   3. Find the investment_phases table');
          console.log('   4. Add a new policy: "Public read access" with USING (true)');
          return;
        } else {
          console.log('✅ Successfully updated policy using alternative approach');
        }
      }
    } else {
      console.log('✅ Public read access policy created successfully');
    }

    console.log('\n🧪 Testing public access...');
    
    // Test with anonymous client (no authentication)
    const anonClient = createClient(supabaseUrl, supabaseAnonKey);
    
    const { data: testData, error: testError } = await anonClient
      .from('investment_phases')
      .select('phase_name, price_per_share, is_active')
      .limit(3);

    if (testError) {
      console.log('❌ Public access test failed:', testError.message);
      console.log('   The affiliate landing page will still show the error');
    } else {
      console.log(`✅ Public access test successful! Found ${testData.length} phases:`);
      testData.forEach(phase => {
        console.log(`   - ${phase.phase_name}: $${phase.price_per_share} ${phase.is_active ? '(ACTIVE)' : ''}`);
      });
    }

    console.log('\n🎯 Fix Summary:');
    console.log('   ✅ Investment phases table now has public read access');
    console.log('   ✅ Affiliate landing page should work for non-authenticated users');
    console.log('   ✅ Gold Diggers Club component should load phase data');
    console.log('   ✅ Commission structure will display correctly');

    console.log('\n🔄 Next Steps:');
    console.log('   1. Refresh your affiliate landing page');
    console.log('   2. The "No phases found in database" error should be gone');
    console.log('   3. Phase selector dropdown should show all 20 phases');
    console.log('   4. Commission rates should display properly');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
    console.log('\n💡 If this script fails, you can manually fix it in Supabase dashboard:');
    console.log('   1. Go to Authentication > Policies');
    console.log('   2. Find investment_phases table');
    console.log('   3. Add policy: "Public read access" with USING (true)');
  }
}

// Run the fix
fixInvestmentPhasesAccess();
