import React, { useState, useEffect, useMemo } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';
import { printCertificate, downloadCertificateHTML, CertificateData } from '../../lib/utils/certificateGenerator';
import KYCVerificationForm from '../kyc/KYCVerificationForm';
import KYCStatusCard from '../kyc/KYCStatusCard';

// Import the correct constants from the working calculators
const TOTAL_SHARES = 1400000; // Total shares in circulation
const PLANT_CAPACITY_TPH = 200; // Tonnes per hour per plant
const EFFECTIVE_HOURS_PER_DAY = 20; // Operating hours per day
const OPERATING_DAYS_PER_YEAR = 330; // Operating days per year
const BULK_DENSITY_T_PER_M3 = 1.8; // Bulk density
const HA_PER_PLANT = 25; // Hectares per plant

// Default mining parameters (same as other calculators)
const DEFAULT_MINING_PARAMS = {
  avgGravelThickness: 0.8,
  inSituGrade: 0.9,
  recoveryFactor: 70, // percentage
  goldPriceUsdPerKg: 120000,
  opexPercent: 45 // percentage
};

// Aureus Alliance 5-year expansion plan
const EXPANSION_PLAN = {
  2026: { plants: 10, hectares: 250 },
  2027: { plants: 25, hectares: 625 },
  2028: { plants: 50, hectares: 1250 },
  2029: { plants: 100, hectares: 2500 },
  2030: { plants: 200, hectares: 5000 }
};

interface PortfolioProps {
  user: any;
  currentPhase?: any;
  calculatorData?: any; // Dynamic calculator data from dashboard
}

interface ShareHolding {
  id: string;
  shares_purchased: number;
  total_amount: number;
  price_per_share: number;
  phase_name: string;
  phase_number: number;
  purchase_date: string;
  status: string;
  transaction_reference?: string;
}

interface PortfolioSummary {
  totalShares: number;
  totalInvested: number;
  currentValue: number;
  unrealizedGains: number;
  unrealizedGainsPercent: number;
  averageCostPerShare: number;
  projectedAnnualDividend: number;
}

interface DividendRecord {
  id: string;
  amount: number;
  payment_date: string;
  status: string;
  type: string;
}

interface Certificate {
  id: string;
  certificate_number: string;
  shares: number;
  issue_date: string;
  status: 'issued' | 'pending' | 'revoked';
  download_url?: string;
  certificate_data?: any;
}

export const ComprehensivePortfolio: React.FC<PortfolioProps> = ({ user, currentPhase, calculatorData }) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'certificates' | 'kyc' | 'transactions' | 'analytics'>('overview');
  const [holdings, setHoldings] = useState<ShareHolding[]>([]);
  const [dividends, setDividends] = useState<DividendRecord[]>([]);
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [loading, setLoading] = useState(true);
  const [kycStatus, setKycStatus] = useState<string>('pending');
  const [kycData, setKycData] = useState<any>(null);
  const [showKYCForm, setShowKYCForm] = useState(false);

  // Portfolio calculator inputs (same as dashboard calculator)
  const [calculatorInputs, setCalculatorInputs] = useState({
    landHa: 250, // Default to 2026 capacity
    avgGravelThickness: 0.8,
    inSituGrade: 0.9,
    recoveryFactor: 70,
    goldPriceUsdPerKg: 120000, // Current market price
    opexPercent: 45,
    selectedYear: 2026
  });

  // Portfolio calculations using CORRECT Aureus Africa dividend formula
  const portfolioSummary = useMemo((): PortfolioSummary => {
    if (!holdings.length) {
      return {
        totalShares: 0,
        totalInvested: 0,
        currentValue: 0,
        unrealizedGains: 0,
        unrealizedGainsPercent: 0,
        averageCostPerShare: 0,
        projectedAnnualDividend: 0
      };
    }

    const totalShares = holdings.reduce((sum, holding) => sum + holding.shares_purchased, 0);
    const totalInvested = holdings.reduce((sum, holding) => sum + holding.total_amount, 0);

    // Calculate current value using latest phase price
    const currentPricePerShare = currentPhase?.price_per_share || 25;
    const currentValue = totalShares * currentPricePerShare;

    const unrealizedGains = currentValue - totalInvested;
    const unrealizedGainsPercent = totalInvested > 0 ? (unrealizedGains / totalInvested) * 100 : 0;
    const averageCostPerShare = totalInvested > 0 ? totalInvested / totalShares : 0;

    // Calculate projected annual dividend using portfolio's own calculator inputs
    // This allows users to adjust parameters directly in the portfolio
    const { landHa, inSituGrade, recoveryFactor, goldPriceUsdPerKg, opexPercent } = calculatorInputs;

    const numPlants = landHa / HA_PER_PLANT;
    const annualThroughputT = numPlants * PLANT_CAPACITY_TPH * EFFECTIVE_HOURS_PER_DAY * OPERATING_DAYS_PER_YEAR;
    const annualGoldKg = (annualThroughputT * (inSituGrade / BULK_DENSITY_T_PER_M3) * (recoveryFactor / 100)) / 1000;
    const annualRevenue = annualGoldKg * goldPriceUsdPerKg;
    const annualOperatingCost = annualRevenue * (opexPercent / 100);
    const annualEbit = annualRevenue - annualOperatingCost;

    const dividendPerShare = TOTAL_SHARES > 0 ? annualEbit / TOTAL_SHARES : 0;
    const projectedAnnualDividend = dividendPerShare * totalShares;

    return {
      totalShares,
      totalInvested,
      currentValue,
      unrealizedGains,
      unrealizedGainsPercent,
      averageCostPerShare,
      projectedAnnualDividend
    };
  }, [holdings, currentPhase, calculatorInputs]);

  // Handle calculator input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setCalculatorInputs(prev => ({
      ...prev,
      [name]: parseFloat(value) || 0
    }));
  };

  // Handle year selection change
  const handleYearChange = (year: number) => {
    const yearData = EXPANSION_PLAN[year as keyof typeof EXPANSION_PLAN];
    setCalculatorInputs(prev => ({
      ...prev,
      selectedYear: year,
      landHa: yearData.hectares
    }));
  };

  useEffect(() => {
    console.log('🔄 Portfolio component mounted with user:', user);
    console.log('📊 User database ID:', user?.database_user?.id);
    loadPortfolioData();
  }, [user]);

  const loadPortfolioData = async () => {
    console.log('🔄 Starting portfolio data load...');
    console.log('👤 User object:', user);
    console.log('🆔 Database user ID:', user?.database_user?.id);
    console.log('📧 User email:', user?.database_user?.email || user?.email);
    console.log('🔗 Account type:', user?.account_type);
    console.log('🔍 Full user structure:', JSON.stringify(user, null, 2));

    // Get user ID with multiple fallback strategies
    let userId = null;

    if (user?.database_user?.id) {
      userId = user.database_user.id;
      console.log('✅ Using database_user.id:', userId);
    } else if (user?.database_user?.telegram_id) {
      userId = user.database_user.telegram_id;
      console.log('✅ Using telegram_id as fallback:', userId);
    } else if (user?.id && typeof user.id === 'number') {
      userId = user.id;
      console.log('✅ Using user.id as fallback:', userId);
    }

    // Force user ID for debugging if we know the user
    // Removed hardcoded email check for better security

    if (!userId) {
      console.log('⚠️ No valid user ID found, loading demo data for testing');
      console.log('🔍 Available user properties:', Object.keys(user || {}));

      // Load demo data for testing when no user is logged in
      const demoHoldings = [
        {
          id: 'demo-1',
          shares_purchased: 1000,
          total_amount: 5000,
          price_per_share: 5.00,
          phase_name: 'Pre Sale',
          phase_number: 0,
          purchase_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'active',
          transaction_reference: 'DEMO-TX-001'
        },
        {
          id: 'demo-2',
          shares_purchased: 500,
          total_amount: 5000,
          price_per_share: 10.00,
          phase_name: 'Phase 1',
          phase_number: 1,
          purchase_date: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'active',
          transaction_reference: 'DEMO-TX-002'
        }
      ];

      setHoldings(demoHoldings);
      setKycStatus('approved');

      // Set demo KYC data
      setKycData({
        id: 'demo-kyc-001',
        user_id: 999,
        first_name: 'Demo',
        last_name: 'User',
        full_legal_name: 'Demo User',
        id_type: 'national_id',
        phone_number: '+27 ************',
        email_address: '<EMAIL>',
        country_name: 'South Africa',
        kyc_status: 'completed',
        kyc_completed_at: '2025-01-15T10:00:00Z',
        data_consent_given: true,
        privacy_policy_accepted: true
      });

      const demoCertificates = demoHoldings.map((holding, index) => ({
        id: holding.id,
        certificate_number: `AUR-DEMO-${String(index + 1).padStart(3, '0')}`,
        shares: holding.shares_purchased,
        issue_date: holding.purchase_date,
        status: 'issued' as const,
      }));

      setCertificates(demoCertificates);
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      console.log('🔄 Loading portfolio data for user ID:', userId);

      // Use service role client for all queries
      const serviceClient = getServiceRoleClient()

      // Try multiple query strategies for better compatibility
      let holdingsData = null;
      let holdingsError = null;

      // Strategy 1: Query with user_id and active status
      console.log('📊 Attempting Strategy 1: Active holdings query...');
      const query1 = await serviceClient
        .from('aureus_share_purchases')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'active')
        .order('created_at', { ascending: false });

      if (!query1.error && query1.data && query1.data.length > 0) {
        holdingsData = query1.data;
        console.log('✅ Strategy 1 successful - found', holdingsData.length, 'active holdings');
      } else {
        console.log('⚠️ Strategy 1 result:', query1.error?.message || 'No active holdings found');

        // Strategy 2: Query all statuses (maybe status filter is too restrictive)
        console.log('📊 Attempting Strategy 2: All statuses query...');
        const query2 = await serviceClient
          .from('aureus_share_purchases')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', { ascending: false });

        if (!query2.error && query2.data && query2.data.length > 0) {
          holdingsData = query2.data;
          console.log('✅ Strategy 2 successful - found', holdingsData.length, 'purchases (all statuses)');
        } else {
          console.log('⚠️ Strategy 2 result:', query2.error?.message || 'No purchases found');

          // Strategy 3: Check if table exists and is accessible
          console.log('📊 Attempting Strategy 3: Table accessibility check...');
          const query3 = await serviceClient
            .from('aureus_share_purchases')
            .select('count(*)')
            .limit(1);

          if (!query3.error) {
            console.log('✅ Table exists and is accessible, but no data for user', userId);
            holdingsData = [];
          } else {
            console.log('❌ Table access error:', query3.error?.message);
            holdingsError = query3.error;
          }
        }
      }

      if (holdingsError) {
        console.error('❌ All query strategies failed:', holdingsError);
        console.error('Error details:', {
          code: holdingsError.code,
          message: holdingsError.message,
          hint: holdingsError.hint,
          details: holdingsError.details
        });

        // Set empty data but don't crash
        setHoldings([]);
      } else {
        console.log('✅ Final holdings data:', holdingsData);

        const formattedHoldings = holdingsData?.map(holding => ({
          id: holding.id,
          shares_purchased: holding.shares_purchased || 0,
          total_amount: holding.total_amount || 0,
          price_per_share: holding.shares_purchased > 0 ? holding.total_amount / holding.shares_purchased : 0,
          phase_name: holding.package_name || 'Share Purchase',
          phase_number: 0, // We'll determine this from package name or price
          purchase_date: holding.created_at,
          status: holding.status,
          transaction_reference: holding.payment_method || 'N/A'
        })) || [];

        setHoldings(formattedHoldings);
        console.log('📈 Transformed holdings:', formattedHoldings);
      }

      // Load dividend history (placeholder - would need actual dividend table)
      // For now, we'll use mock data
      setDividends([]);

      // Load real certificates from certificates table
      console.log('📜 Loading certificates for user:', userId);
      const { data: certificatesData, error: certificatesError } = await serviceClient
        .from('certificates')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'issued')
        .order('created_at', { ascending: false });

      if (certificatesError) {
        console.error('❌ Error loading certificates:', certificatesError);
        // Fallback to mock certificates if table doesn't exist yet
        const mockCertificates = holdingsData?.map((holding, index) => ({
          id: holding.id,
          certificate_number: `AUR-${String(userId).padStart(4, '0')}-${String(index + 1).padStart(3, '0')}`,
          shares: holding.shares_purchased || 0,
          issue_date: holding.created_at,
          status: 'issued' as const,
        })) || [];
        setCertificates(mockCertificates);
        console.log('📜 Using mock certificates:', mockCertificates);
      } else {
        // Use real certificates data
        const realCertificates = certificatesData?.map(cert => ({
          id: cert.id,
          certificate_number: cert.certificate_number,
          shares: cert.shares_count,
          issue_date: cert.issue_date,
          status: cert.status,
          download_url: cert.certificate_data?.file_url || null,
          certificate_data: cert.certificate_data
        })) || [];
        setCertificates(realCertificates);
        console.log('📜 Using real certificates:', realCertificates);
      }

      // Load KYC information from existing kyc_information table
      console.log('🔐 Loading KYC data for user:', userId);
      let kycStatusResult = 'pending';
      let kycDataResult = null;

      try {
        // Always try service role client first for KYC data since it's sensitive
        console.log('🔧 Using service role client for KYC data...');
        const serviceClient = getServiceRoleClient();
        const { data: kycInfo, error: kycError } = await serviceClient
          .from('kyc_information')
          .select('*')
          .eq('user_id', userId)
          .single();

        console.log('🔍 KYC Query Result:', { kycInfo, kycError });

        if (kycError) {
          if (kycError.code === 'PGRST116') {
            console.log('ℹ️ No KYC data found (expected for new users)');
          } else {
            console.error('❌ KYC data error:', kycError);
          }
        } else if (kycInfo) {
          kycDataResult = kycInfo;
          // Use the actual database status without incorrect mapping
          kycStatusResult = kycInfo.kyc_status;
          console.log('✅ KYC Status set to:', kycStatusResult);
          console.log('✅ KYC Data loaded:', kycInfo);
        }
      } catch (kycLoadError) {
        console.error('❌ Error loading KYC data:', kycLoadError);
      }

      console.log('🎯 Final KYC Status:', kycStatusResult);
      console.log('🎯 Final KYC Data:', kycDataResult);

      setKycStatus(kycStatusResult);
      setKycData(kycDataResult);

    } catch (error) {
      console.error('❌ Error loading portfolio data:', error);
      // Set empty data on error to prevent crashes
      setHoldings([]);
      setDividends([]);
      setCertificates([]);
      setKycStatus('pending');
      setKycData(null);
    } finally {
      setLoading(false);
    }
  };

  const generateCertificatePDF = async (certificate: Certificate, action: 'view' | 'download' = 'view') => {
    const certificateData: CertificateData = {
      certificateNumber: certificate.certificate_number,
      holderName: `${user.first_name || ''} ${user.last_name || ''}`.trim() || 'Shareholder',
      shares: certificate.shares,
      issueDate: certificate.issue_date,
      userId: user.database_user?.id?.toString() || 'N/A'
    };

    if (action === 'download') {
      downloadCertificateHTML(certificateData);
    } else {
      printCertificate(certificateData);
    }
  };

  // Export functionality for transactions
  const exportTransactionsPDF = async () => {
    try {
      const transactions = holdings || [];
      if (transactions.length === 0) {
        alert('No transactions to export.');
        return;
      }

      // Import jsPDF dynamically
      const { jsPDF } = await import('jspdf');
      const doc = new jsPDF();

      // Load and add logo
      try {
        const logoUrl = 'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png';
        const response = await fetch(logoUrl);
        const blob = await response.blob();
        const logoBase64 = await new Promise<string>((resolve) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result as string);
          reader.readAsDataURL(blob);
        });

        // Add logo image
        doc.addImage(logoBase64, 'PNG', 20, 15, 25, 25);

        // Add company name next to logo
        doc.setFontSize(20);
        doc.setTextColor(212, 175, 55); // Gold color
        doc.text('AUREUS AFRICA', 50, 30);
      } catch (error) {
        console.warn('Could not load logo, using text fallback:', error);
        // Fallback to text-only header
        doc.setFontSize(20);
        doc.setTextColor(212, 175, 55); // Gold color
        doc.text('🏆 AUREUS AFRICA', 20, 25);
      }

      doc.setFontSize(16);
      doc.setTextColor(0, 0, 0);
      doc.text('Transaction Report', 20, 50);

      doc.setFontSize(10);
      doc.text(`Generated on ${new Date().toLocaleDateString()}`, 20, 60);

      // Add user info
      doc.setFontSize(12);
      doc.text(`Account: ${user.username || 'N/A'}`, 20, 75);
      doc.text(`Email: ${user.email || 'N/A'}`, 20, 85);
      doc.text(`Total Transactions: ${transactions.length}`, 20, 95);

      // Add table header
      let yPosition = 115;
      doc.setFontSize(10);
      doc.setFont(undefined, 'bold');
      doc.text('Date', 20, yPosition);
      doc.text('Type', 50, yPosition);
      doc.text('Description', 80, yPosition);
      doc.text('Shares', 130, yPosition);
      doc.text('Amount', 150, yPosition);
      doc.text('Status', 180, yPosition);

      // Add line under header
      doc.line(20, yPosition + 2, 200, yPosition + 2);
      yPosition += 10;

      // Add transaction data
      doc.setFont(undefined, 'normal');
      transactions.forEach((tx, index) => {
        if (yPosition > 270) { // Start new page if needed
          doc.addPage();
          yPosition = 20;
        }

        doc.text(new Date(tx.purchase_date).toLocaleDateString(), 20, yPosition);
        doc.text('Purchase', 50, yPosition);
        doc.text(`${tx.phase_name || 'Pre Sale Purchase'}`, 80, yPosition);
        doc.text(`+${tx.shares_purchased}`, 130, yPosition);
        doc.text(`$${tx.total_amount}`, 150, yPosition);
        doc.text(tx.status, 180, yPosition);
        yPosition += 8;
      });

      // Add summary
      yPosition += 10;
      doc.setFont(undefined, 'bold');
      const totalShares = transactions.reduce((sum, tx) => sum + tx.shares_purchased, 0);
      const totalInvested = transactions.reduce((sum, tx) => sum + tx.total_amount, 0);

      doc.text(`Total Shares: ${totalShares}`, 20, yPosition);
      doc.text(`Total Invested: $${totalInvested.toFixed(2)}`, 20, yPosition + 10);

      // Save the PDF
      doc.save(`aureus-transactions-${user.username || 'user'}-${new Date().toISOString().split('T')[0]}.pdf`);

      alert('PDF report exported successfully!');
    } catch (error) {
      console.error('Error exporting PDF:', error);
      alert('Failed to export PDF. Please try again.');
    }
  };

  const exportTransactionsExcel = async () => {
    try {
      const transactions = holdings || [];
      if (transactions.length === 0) {
        alert('No transactions to export.');
        return;
      }

      // Create CSV content
      const csvHeaders = 'Date,Type,Description,Shares,Amount,Status,Payment Method,Reference ID\n';
      const csvRows = transactions.map(tx => {
        const date = new Date(tx.purchase_date).toLocaleDateString();
        const type = 'Purchase';
        const description = `Share Purchase - ${tx.phase_name || 'Pre Sale Purchase'}`;
        const shares = `+${tx.shares_purchased}`;
        const amount = `$${tx.total_amount}`;
        const status = tx.status;
        const paymentMethod = 'N/A'; // Not available in holdings data
        const referenceId = tx.transaction_reference || tx.id || 'N/A';

        return `"${date}","${type}","${description}","${shares}","${amount}","${status}","${paymentMethod}","${referenceId}"`;
      }).join('\n');

      const csvContent = csvHeaders + csvRows;

      // Create and download CSV
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `aureus-transactions-${user.username || 'user'}-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      alert('Transaction data exported to CSV successfully!');
    } catch (error) {
      console.error('Error exporting Excel:', error);
      alert('Failed to export Excel. Please try again.');
    }
  };

  const exportTaxSummary = async () => {
    try {
      const transactions = holdings || [];
      if (transactions.length === 0) {
        alert('No transactions to export.');
        return;
      }

      const currentYear = new Date().getFullYear();
      const totalInvested = transactions.reduce((sum, tx) => sum + tx.total_amount, 0);
      const totalShares = transactions.reduce((sum, tx) => sum + tx.shares_purchased, 0);

      // Import jsPDF dynamically
      const { jsPDF } = await import('jspdf');
      const doc = new jsPDF();

      // Load and add logo
      try {
        const logoUrl = 'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png';
        const response = await fetch(logoUrl);
        const blob = await response.blob();
        const logoBase64 = await new Promise<string>((resolve) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result as string);
          reader.readAsDataURL(blob);
        });

        // Add logo image
        doc.addImage(logoBase64, 'PNG', 20, 15, 25, 25);

        // Add company name next to logo
        doc.setFontSize(20);
        doc.setTextColor(212, 175, 55); // Gold color
        doc.text('AUREUS AFRICA', 50, 30);
      } catch (error) {
        console.warn('Could not load logo, using text fallback:', error);
        // Fallback to text-only header
        doc.setFontSize(20);
        doc.setTextColor(212, 175, 55); // Gold color
        doc.text('🏆 AUREUS AFRICA', 20, 25);
      }

      doc.setFontSize(16);
      doc.setTextColor(0, 0, 0);
      doc.text(`Tax Summary Report - ${currentYear}`, 20, 50);

      doc.setFontSize(10);
      doc.text(`Generated on ${new Date().toLocaleDateString()}`, 20, 60);

      // Add investment summary box
      doc.setFontSize(14);
      doc.setFont(undefined, 'bold');
      doc.text('Investment Summary', 20, 80);

      doc.setFontSize(12);
      doc.setFont(undefined, 'normal');
      doc.text(`Account Holder: ${user.username || 'N/A'}`, 20, 95);
      doc.text(`Email: ${user.email || 'N/A'}`, 20, 105);
      doc.text(`Total Shares Purchased: ${totalShares}`, 20, 115);
      doc.text(`Total Amount Invested: $${totalInvested.toFixed(2)}`, 20, 125);
      doc.text(`Number of Transactions: ${transactions.length}`, 20, 135);

      // Add important tax information
      doc.setFontSize(12);
      doc.setFont(undefined, 'bold');
      doc.text('⚠️ Important Tax Information', 20, 155);

      doc.setFontSize(10);
      doc.setFont(undefined, 'normal');
      doc.text('This summary is for informational purposes only.', 20, 165);
      doc.text('Please consult with a qualified tax professional for specific tax advice.', 20, 175);
      doc.text('Investment in shares may have tax implications depending on your jurisdiction.', 20, 185);

      // Add transaction details table
      doc.setFontSize(12);
      doc.setFont(undefined, 'bold');
      doc.text('Transaction Details', 20, 205);

      // Table header
      let yPosition = 220;
      doc.setFontSize(10);
      doc.setFont(undefined, 'bold');
      doc.text('Date', 20, yPosition);
      doc.text('Description', 50, yPosition);
      doc.text('Shares', 120, yPosition);
      doc.text('Amount', 150, yPosition);
      doc.text('Status', 180, yPosition);

      // Add line under header
      doc.line(20, yPosition + 2, 200, yPosition + 2);
      yPosition += 10;

      // Add transaction data
      doc.setFont(undefined, 'normal');
      transactions.forEach((tx, index) => {
        if (yPosition > 270) { // Start new page if needed
          doc.addPage();
          yPosition = 20;
        }

        doc.text(new Date(tx.purchase_date).toLocaleDateString(), 20, yPosition);
        doc.text(`${tx.phase_name || 'Pre Sale Purchase'}`, 50, yPosition);
        doc.text(`${tx.shares_purchased}`, 120, yPosition);
        doc.text(`$${tx.total_amount.toFixed(2)}`, 150, yPosition);
        doc.text(tx.status, 180, yPosition);
        yPosition += 8;
      });

      // Add footer
      yPosition += 20;
      if (yPosition > 260) {
        doc.addPage();
        yPosition = 20;
      }

      doc.setFontSize(9);
      doc.setTextColor(100, 100, 100);
      doc.text('This report was generated by Aureus Africa Holdings (Pty) Ltd', 20, yPosition);
      doc.text('For questions, contact <EMAIL>', 20, yPosition + 10);

      // Save the PDF
      doc.save(`aureus-tax-summary-${user.username || 'user'}-${currentYear}.pdf`);

      alert('Tax summary PDF exported successfully!');
    } catch (error) {
      console.error('Error exporting tax summary:', error);
      alert('Failed to export tax summary. Please try again.');
    }
  };

  const handleKYCComplete = (status: string) => {
    // Use the actual status without incorrect mapping
    setKycStatus(status);
    setShowKYCForm(false);
    // Reload portfolio data to get updated KYC info
    loadPortfolioData();
  };

  const renderKYCTab = () => (
    <div className="space-y-6">
      <KYCStatusCard
        status={kycStatus as any}
        kycData={kycData}
        onStartKYC={() => {
          if (kycStatus === 'pending') {
            // Navigate to dashboard KYC section
            window.location.href = '/dashboard';
          }
        }}
        onEditKYC={() => {
          if (kycStatus === 'completed' || kycStatus === 'rejected' || kycStatus === 'expired') {
            // Navigate to dashboard KYC section
            window.location.href = '/dashboard';
          }
        }}
      />

      {/* Remove the legacy KYC form - users should go to dedicated KYC page */}
      {showKYCForm && kycStatus === 'pending' && (
        <div className="bg-blue-900/30 rounded-lg p-6 border border-blue-700">
          <div className="flex items-center space-x-3 mb-4">
            <span className="text-3xl">🚀</span>
            <div>
              <h3 className="text-blue-400 font-medium text-lg">Redirecting to KYC Certification</h3>
              <p className="text-blue-200 text-sm">
                You'll be redirected to our comprehensive KYC verification system with document upload and facial recognition.
              </p>
            </div>
          </div>
          <button
            onClick={() => window.location.href = '/kyc-certification'}
            className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Continue to KYC Certification →
          </button>
        </div>
      )}

      {showKYCForm && kycStatus !== 'pending' && (
        <div className="bg-yellow-900/30 rounded-lg p-4 border border-yellow-700">
          <div className="flex items-center space-x-3">
            <span className="text-2xl">⚠️</span>
            <div>
              <h3 className="text-yellow-400 font-medium">KYC Already Submitted</h3>
              <p className="text-yellow-200 text-sm mt-1">
                Your KYC information has already been submitted. Please wait for admin approval.
              </p>
            </div>
          </div>
          <button
            onClick={() => setShowKYCForm(false)}
            className="mt-3 px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 text-sm font-medium"
          >
            Close
          </button>
        </div>
      )}
    </div>
  );

  const renderCertificatesTab = () => (
    <div className="space-y-6">
      {/* KYC Status Notice */}
      {kycStatus !== 'completed' && kycStatus !== 'approved' && (
        <div className="bg-yellow-900/30 rounded-lg p-4 border border-yellow-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-yellow-400 text-xl">⚠️</span>
              <div>
                <h4 className="text-yellow-400 font-semibold">KYC Verification Required</h4>
                <p className="text-yellow-200 text-sm">
                  Complete your KYC verification to access and download your share certificates.
                </p>
              </div>
            </div>
            <button
              onClick={() => setActiveTab('kyc')}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm font-medium"
            >
              Complete KYC
            </button>
          </div>
        </div>
      )}

      {/* Certificate Status Warning */}
      {certificates.length === 0 && holdings.length > 0 && (
        <div className="bg-blue-900/30 rounded-lg p-4 border border-blue-700 mb-6">
          <div className="flex items-center space-x-2">
            <span className="text-2xl">📜</span>
            <div>
              <h4 className="text-blue-400 font-semibold">Certificates Pending</h4>
              <p className="text-blue-200 text-sm">
                Your share certificates are being prepared by our admin team.
                You will be able to download them once they are issued.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Certificates Grid */}
      {certificates.length === 0 ? (
        <div className="bg-gray-800 rounded-lg p-8 border border-gray-700 text-center">
          <div className="text-6xl mb-4">📜</div>
          <h3 className="text-xl font-semibold text-white mb-2">No Certificates Available</h3>
          <p className="text-gray-400 mb-4">
            {holdings.length === 0
              ? "You don't have any share purchases yet."
              : "Your certificates are being prepared by our admin team."
            }
          </p>
          {holdings.length > 0 && (
            <div className="bg-blue-900/30 rounded-lg p-4 border border-blue-700">
              <p className="text-blue-200 text-sm">
                💡 <strong>Note:</strong> Certificates are manually issued by admin after share purchase approval.
                Please contact support if you have approved purchases but no certificates after 24 hours.
              </p>
            </div>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {certificates.map((certificate) => (
          <div key={certificate.id} className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="text-center mb-4">
              <div className="text-3xl mb-2">📜</div>
              <h4 className="text-lg font-semibold text-white">
                Certificate #{certificate.certificate_number}
              </h4>
              <p className="text-gray-400 text-sm">
                {certificate.shares.toLocaleString()} shares
              </p>
            </div>

            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">Issue Date:</span>
                <span className="text-white">
                  {new Date(certificate.issue_date).toLocaleDateString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Status:</span>
                <span className={`font-semibold ${
                  certificate.status === 'issued' ? 'text-green-400' :
                  certificate.status === 'pending' ? 'text-yellow-400' : 'text-red-400'
                }`}>
                  {certificate.status.charAt(0).toUpperCase() + certificate.status.slice(1)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Certificate ID:</span>
                <span className="text-white font-mono text-xs">
                  {certificate.certificate_number}
                </span>
              </div>
              {certificate.download_url && (
                <div className="flex justify-between">
                  <span className="text-gray-400">Type:</span>
                  <span className="text-blue-400 font-semibold text-xs">
                    🏛️ Admin Generated
                  </span>
                </div>
              )}
              {certificate.certificate_data?.generation_metadata?.securityHash && (
                <div className="flex justify-between">
                  <span className="text-gray-400">Security Hash:</span>
                  <span className="text-green-400 font-mono text-xs">
                    {certificate.certificate_data.generation_metadata.securityHash}
                  </span>
                </div>
              )}
            </div>

            <div className="mt-4 space-y-2">
              {certificate.download_url ? (
                // Admin-generated certificate with PDF download
                <>
                  <button
                    onClick={() => window.open(certificate.download_url, '_blank')}
                    disabled={kycStatus !== 'completed' && kycStatus !== 'approved'}
                    className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-500 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                  >
                    📄 View PDF Certificate
                  </button>
                  <button
                    onClick={() => {
                      const link = document.createElement('a');
                      link.href = certificate.download_url!;
                      link.download = `Certificate_${certificate.certificate_number}.pdf`;
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                    }}
                    disabled={kycStatus !== 'approved'}
                    className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-500 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                  >
                    📥 Download PDF
                  </button>
                </>
              ) : (
                // Fallback to generated certificate
                <>
                  <button
                    onClick={() => generateCertificatePDF(certificate, 'view')}
                    disabled={kycStatus !== 'approved'}
                    className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-500 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                  >
                    📄 View Certificate
                  </button>
                  <button
                    onClick={() => generateCertificatePDF(certificate, 'download')}
                    disabled={kycStatus !== 'approved'}
                    className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-500 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                  >
                    📥 Download HTML
                  </button>
                </>
              )}
            </div>
          </div>
        ))}
        </div>
      )}

      {/* Certificate Information */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h4 className="text-lg font-semibold text-white mb-4">📋 Certificate Information</h4>
        <div className="space-y-3 text-sm">
          <div>
            <h5 className="text-blue-400 font-semibold mb-1">What are Share Certificates?</h5>
            <p className="text-gray-300">
              Digital share certificates are official documents that prove your ownership of Aureus Africa shares.
              Each certificate includes your name, share quantity, unique certificate number, and official company seal.
            </p>
          </div>
          <div>
            <h5 className="text-blue-400 font-semibold mb-1">Certificate Features:</h5>
            <ul className="text-gray-300 space-y-1 ml-4">
              <li>• Unique certificate numbers for verification</li>
              <li>• QR codes for authenticity checking</li>
              <li>• Professional PDF format for printing</li>
              <li>• Legally recognized ownership documents</li>
            </ul>
          </div>
          <div>
            <h5 className="text-blue-400 font-semibold mb-1">Requirements:</h5>
            <p className="text-gray-300">
              KYC verification must be completed before certificates can be issued or downloaded.
              This ensures compliance with regulatory requirements and protects your holdings.
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* Portfolio Calculator Controls */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-xl font-bold text-white mb-4">📊 Portfolio Calculator</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Target Year */}
          <div>
            <label className="block text-sm font-medium text-orange-400 mb-2">TARGET YEAR</label>
            <select
              name="selectedYear"
              value={calculatorInputs.selectedYear}
              onChange={(e) => handleYearChange(parseInt(e.target.value))}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            >
              {Object.entries(EXPANSION_PLAN).map(([year, data]) => (
                <option key={year} value={year}>
                  {year} - {data.plants} Plants ({data.hectares} ha)
                </option>
              ))}
            </select>
            <p className="text-xs text-gray-400 mt-1">Select our planned capacity for a specific year</p>
          </div>

          {/* Land Size */}
          <div>
            <label className="block text-sm font-medium text-orange-400 mb-2">LAND SIZE (Manual Override)</label>
            <select
              name="landHa"
              value={calculatorInputs.landHa}
              onChange={handleInputChange}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            >
              <option value={25}>25 ha</option>
              <option value={250}>250 ha</option>
              <option value={625}>625 ha</option>
              <option value={1250}>1250 ha</option>
              <option value={2500}>2500 ha</option>
              <option value={5000}>5000 ha</option>
            </select>
            <p className="text-xs text-gray-400 mt-1">Corresponds to 1,400,000 shares<br/>Adjust manually for "what-if" scenarios</p>
          </div>

          {/* Your Shares */}
          <div>
            <label className="block text-sm font-medium text-orange-400 mb-2">YOUR SHARES</label>
            <input
              type="number"
              value={portfolioSummary.totalShares}
              readOnly
              className="w-full bg-gray-600 border border-gray-500 rounded-lg px-3 py-2 text-gray-300 cursor-not-allowed"
            />
            <p className="text-xs text-gray-400 mt-1">Total project shares: 1,400,000</p>
          </div>

          {/* Gold Price */}
          <div>
            <label className="block text-sm font-medium text-orange-400 mb-2">GOLD PRICE</label>
            <input
              type="number"
              name="goldPriceUsdPerKg"
              value={calculatorInputs.goldPriceUsdPerKg}
              onChange={handleInputChange}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            />
            <p className="text-xs text-gray-400 mt-1">USD per kilogram</p>
          </div>
        </div>
      </div>

      {/* Portfolio Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="text-2xl font-bold text-blue-400">
            {portfolioSummary.totalShares.toLocaleString()}
          </div>
          <div className="text-sm text-gray-400">Total Shares</div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="text-2xl font-bold text-green-400">
            ${portfolioSummary.currentValue.toLocaleString(undefined, { maximumFractionDigits: 0 })}
          </div>
          <div className="text-sm text-gray-400">Current Value</div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className={`text-2xl font-bold ${portfolioSummary.unrealizedGains >= 0 ? 'text-green-400' : 'text-red-400'}`}>
            ${portfolioSummary.unrealizedGains.toLocaleString(undefined, { maximumFractionDigits: 0 })}
          </div>
          <div className="text-sm text-gray-400">
            Unrealized {portfolioSummary.unrealizedGains >= 0 ? 'Dividends Potential' : 'Losses'}
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="text-2xl font-bold text-yellow-400">
            ${portfolioSummary.projectedAnnualDividend.toLocaleString(undefined, { maximumFractionDigits: 0 })}
          </div>
          <div className="text-sm text-gray-400">Projected Annual Dividend</div>
        </div>
      </div>

      {/* Holdings Breakdown */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-xl font-semibold text-white mb-4">📊 Holdings Breakdown</h3>
        
        {holdings.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-400 text-lg mb-4">📈</div>
            <h4 className="text-lg font-semibold text-white mb-2">No Share Holdings Yet</h4>
            <p className="text-gray-400 mb-4">
              Start building your portfolio by purchasing shares in the current phase.
            </p>
            <div className="text-xs text-gray-500 mb-4 space-y-1">
              <div>Debug Info:</div>
              <div>• User ID: {user?.database_user?.id || 'Not found'}</div>
              <div>• User Email: {user?.database_user?.email || user?.email || 'Not found'}</div>
              <div>• Account Type: {user?.account_type || 'Unknown'}</div>
              <div>• Holdings Count: {holdings.length}</div>
              <div>• Loading: {loading ? 'Yes' : 'No'}</div>
            </div>
            <button className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-500">
              Purchase Shares
            </button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-700">
                  <th className="text-left py-2 text-gray-400">Phase</th>
                  <th className="text-left py-2 text-gray-400">Shares</th>
                  <th className="text-left py-2 text-gray-400">Purchase Price</th>
                  <th className="text-left py-2 text-gray-400">Current Value</th>
                  <th className="text-left py-2 text-gray-400">Dividend Potential</th>
                  <th className="text-left py-2 text-gray-400">Date</th>
                </tr>
              </thead>
              <tbody>
                {holdings.map((holding) => {
                  const currentValue = holding.shares_purchased * (currentPhase?.price_per_share || 25);
                  const gainLoss = currentValue - holding.total_amount;
                  const gainLossPercent = (gainLoss / holding.total_amount) * 100;
                  
                  return (
                    <tr key={holding.id} className="border-b border-gray-700/50">
                      <td className="py-3 text-white font-semibold">{holding.phase_name}</td>
                      <td className="py-3 text-gray-300">{holding.shares_purchased.toLocaleString()}</td>
                      <td className="py-3 text-gray-300">${holding.total_amount.toLocaleString()}</td>
                      <td className="py-3 text-gray-300">${currentValue.toLocaleString()}</td>
                      <td className={`py-3 font-semibold ${gainLoss >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        ${gainLoss.toLocaleString()} ({gainLossPercent.toFixed(1)}%)
                      </td>
                      <td className="py-3 text-gray-400">
                        {new Date(holding.purchase_date).toLocaleDateString()}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Performance Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <h4 className="text-lg font-semibold text-white mb-4">💰 Portfolio Metrics</h4>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Total Invested:</span>
              <span className="text-white font-semibold">
                ${portfolioSummary.totalInvested.toLocaleString()}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Average Cost/Share:</span>
              <span className="text-white font-semibold">
                ${portfolioSummary.averageCostPerShare.toFixed(2)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Current Price/Share:</span>
              <span className="text-white font-semibold">
                ${currentPhase?.price_per_share || 25}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Total Return:</span>
              <span className={`font-semibold ${portfolioSummary.unrealizedGainsPercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {portfolioSummary.unrealizedGainsPercent.toFixed(1)}%
              </span>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <h4 className="text-lg font-semibold text-white mb-4">📈 Dividend Projections</h4>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Annual Dividend:</span>
              <span className="text-green-400 font-semibold">
                ${portfolioSummary.projectedAnnualDividend.toLocaleString(undefined, { maximumFractionDigits: 0 })}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Quarterly Dividend:</span>
              <span className="text-green-400 font-semibold">
                ${(portfolioSummary.projectedAnnualDividend / 4).toLocaleString(undefined, { maximumFractionDigits: 0 })}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Dividend per Share:</span>
              <span className="text-green-400 font-semibold">
                ${portfolioSummary.totalShares > 0
                  ? (portfolioSummary.projectedAnnualDividend / portfolioSummary.totalShares).toFixed(2)
                  : '0.00'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Dividend Yield:</span>
              <span className="text-green-400 font-semibold">
                {portfolioSummary.totalInvested > 0
                  ? ((portfolioSummary.projectedAnnualDividend / portfolioSummary.totalInvested) * 100).toFixed(1)
                  : 0}%
              </span>
            </div>
          </div>
          <div className="mt-4 p-3 bg-blue-900/30 rounded-lg border border-blue-700">
            <p className="text-blue-200 text-sm mb-2">
              💡 <strong>Dividend Calculation Method:</strong>
            </p>
            <ul className="text-blue-200 text-xs space-y-1 ml-4">
              <li>• Based on baseline 25ha (1 plant) mining operation</li>
              <li>• Gold production: {((1 * PLANT_CAPACITY_TPH * EFFECTIVE_HOURS_PER_DAY * OPERATING_DAYS_PER_YEAR * (DEFAULT_MINING_PARAMS.inSituGrade / BULK_DENSITY_T_PER_M3) * (DEFAULT_MINING_PARAMS.recoveryFactor / 100)) / 1000).toFixed(0)} kg/year</li>
              <li>• Revenue: ${((((1 * PLANT_CAPACITY_TPH * EFFECTIVE_HOURS_PER_DAY * OPERATING_DAYS_PER_YEAR * (DEFAULT_MINING_PARAMS.inSituGrade / BULK_DENSITY_T_PER_M3) * (DEFAULT_MINING_PARAMS.recoveryFactor / 100)) / 1000) * DEFAULT_MINING_PARAMS.goldPriceUsdPerKg) / 1000000).toFixed(1)}M annually</li>
              <li>• 100% EBIT distributed across {TOTAL_SHARES.toLocaleString()} total shares</li>
              <li>• Actual dividends scale with operational expansion</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );

  const renderTransactionsTab = () => (
    <div className="space-y-6">
      {/* Transaction Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="text-2xl font-bold text-blue-400">
            {holdings.length}
          </div>
          <div className="text-sm text-gray-400">Share Purchases</div>
        </div>
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="text-2xl font-bold text-green-400">
            {dividends.length}
          </div>
          <div className="text-sm text-gray-400">Dividend Payments</div>
        </div>
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="text-2xl font-bold text-yellow-400">
            ${portfolioSummary.totalInvested.toLocaleString()}
          </div>
          <div className="text-sm text-gray-400">Total Invested</div>
        </div>
      </div>

      {/* Transaction History */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold text-white">📋 Transaction History</h3>
        </div>

        {holdings.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-400 text-lg mb-4">📋</div>
            <h4 className="text-lg font-semibold text-white mb-2">No Transactions Yet</h4>
            <p className="text-gray-400">
              Your share purchases and dividend payments will appear here.
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-700">
                  <th className="text-left py-2 text-gray-400">Date</th>
                  <th className="text-left py-2 text-gray-400">Type</th>
                  <th className="text-left py-2 text-gray-400">Description</th>
                  <th className="text-left py-2 text-gray-400">Shares</th>
                  <th className="text-left py-2 text-gray-400">Amount</th>
                  <th className="text-left py-2 text-gray-400">Status</th>
                </tr>
              </thead>
              <tbody>
                {holdings.map((holding) => (
                  <tr key={holding.id} className="border-b border-gray-700/50">
                    <td className="py-3 text-gray-300">
                      {new Date(holding.purchase_date).toLocaleDateString()}
                    </td>
                    <td className="py-3">
                      <span className="px-2 py-1 bg-blue-600 text-white rounded text-xs">
                        Purchase
                      </span>
                    </td>
                    <td className="py-3 text-gray-300">
                      Share Purchase - {holding.phase_name}
                    </td>
                    <td className="py-3 text-white font-semibold">
                      +{holding.shares_purchased.toLocaleString()}
                    </td>
                    <td className="py-3 text-gray-300">
                      ${holding.total_amount.toLocaleString()}
                    </td>
                    <td className="py-3">
                      <span className="px-2 py-1 bg-green-600 text-white rounded text-xs">
                        {holding.status}
                      </span>
                    </td>
                  </tr>
                ))}
                {dividends.map((dividend) => (
                  <tr key={dividend.id} className="border-b border-gray-700/50">
                    <td className="py-3 text-gray-300">
                      {new Date(dividend.payment_date).toLocaleDateString()}
                    </td>
                    <td className="py-3">
                      <span className="px-2 py-1 bg-green-600 text-white rounded text-xs">
                        Dividend
                      </span>
                    </td>
                    <td className="py-3 text-gray-300">
                      {dividend.type} Dividend Payment
                    </td>
                    <td className="py-3 text-gray-400">-</td>
                    <td className="py-3 text-green-400 font-semibold">
                      +${dividend.amount.toLocaleString()}
                    </td>
                    <td className="py-3">
                      <span className="px-2 py-1 bg-green-600 text-white rounded text-xs">
                        {dividend.status}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Export Options */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h4 className="text-lg font-semibold text-white mb-4">📊 Export Options</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={exportTransactionsPDF}
            className="p-4 bg-gray-700 rounded-lg hover:bg-gray-600 text-center transition-colors duration-200"
          >
            <div className="text-2xl mb-2">📄</div>
            <div className="text-white font-semibold">PDF Report</div>
            <div className="text-gray-400 text-sm">Complete transaction history</div>
          </button>
          <button
            onClick={exportTransactionsExcel}
            className="p-4 bg-gray-700 rounded-lg hover:bg-gray-600 text-center transition-colors duration-200"
          >
            <div className="text-2xl mb-2">📊</div>
            <div className="text-white font-semibold">Excel Export</div>
            <div className="text-gray-400 text-sm">Spreadsheet format</div>
          </button>
          <button
            onClick={exportTaxSummary}
            className="p-4 bg-gray-700 rounded-lg hover:bg-gray-600 text-center transition-colors duration-200"
          >
            <div className="text-2xl mb-2">🧾</div>
            <div className="text-white font-semibold">Tax Summary</div>
            <div className="text-gray-400 text-sm">For tax reporting</div>
          </button>
        </div>
      </div>
    </div>
  );

  const renderAnalyticsTab = () => {
    // Calculate additional metrics for enhanced analytics
    const totalDaysInvested = holdings.reduce((total, holding) => {
      const daysSincePurchase = Math.floor((Date.now() - new Date(holding.purchase_date).getTime()) / (1000 * 60 * 60 * 24));
      return total + daysSincePurchase;
    }, 0);

    const averageDaysInvested = holdings.length > 0 ? Math.floor(totalDaysInvested / holdings.length) : 0;
    const annualizedReturn = portfolioSummary.totalInvested > 0 && averageDaysInvested > 0
      ? ((portfolioSummary.unrealizedGains / portfolioSummary.totalInvested) * (365 / averageDaysInvested) * 100)
      : 0;

    const monthlyDividendProjection = portfolioSummary.projectedAnnualDividend / 12;
    const weeklyDividendProjection = portfolioSummary.projectedAnnualDividend / 52;
    const dailyDividendProjection = portfolioSummary.projectedAnnualDividend / 365;

    return (
      <div className="space-y-6">
        {/* Enhanced Performance Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <h4 className="text-lg font-semibold text-white mb-4">📈 Portfolio Performance</h4>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Total Return:</span>
                <span className={`text-xl font-bold ${portfolioSummary.unrealizedGainsPercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {portfolioSummary.unrealizedGainsPercent.toFixed(1)}%
                </span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-3">
                <div
                  className={`h-3 rounded-full ${portfolioSummary.unrealizedGainsPercent >= 0 ? 'bg-gradient-to-r from-green-600 to-green-400' : 'bg-gradient-to-r from-red-600 to-red-400'}`}
                  style={{ width: `${Math.min(Math.abs(portfolioSummary.unrealizedGainsPercent), 100)}%` }}
                />
              </div>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="text-gray-400">Absolute:</span>
                  <div className={`font-semibold ${portfolioSummary.unrealizedGains >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                    ${Math.abs(portfolioSummary.unrealizedGains).toLocaleString()}
                  </div>
                </div>
                <div>
                  <span className="text-gray-400">Annualized:</span>
                  <div className={`font-semibold ${annualizedReturn >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {annualizedReturn.toFixed(1)}%
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <h4 className="text-lg font-semibold text-white mb-4">⏱️ Time Analytics</h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-400">Avg. Days Invested:</span>
                <span className="text-white font-semibold">{averageDaysInvested}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Total Positions:</span>
                <span className="text-white font-semibold">{holdings.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Avg. Position Size:</span>
                <span className="text-white font-semibold">
                  {holdings.length > 0 ? Math.round(portfolioSummary.totalShares / holdings.length).toLocaleString() : 0} shares
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Portfolio Concentration:</span>
                <span className="text-blue-400 font-semibold">
                  {((portfolioSummary.totalShares / TOTAL_SHARES) * 100).toFixed(4)}%
                </span>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <h4 className="text-lg font-semibold text-white mb-4">💎 Value Metrics</h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-400">Book Value:</span>
                <span className="text-white font-semibold">
                  ${portfolioSummary.totalInvested.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Market Value:</span>
                <span className="text-blue-400 font-semibold">
                  ${portfolioSummary.currentValue.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">P/B Ratio:</span>
                <span className="text-yellow-400 font-semibold">
                  {portfolioSummary.totalInvested > 0 ? (portfolioSummary.currentValue / portfolioSummary.totalInvested).toFixed(2) : 'N/A'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Cost per Share:</span>
                <span className="text-gray-300 font-semibold">
                  ${portfolioSummary.averageCostPerShare.toFixed(2)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Dividend Analysis */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <h4 className="text-lg font-semibold text-white mb-4">💰 Dividend Analysis</h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-400">Dividend Yield on Cost:</span>
                <span className="text-green-400 font-semibold">
                  {portfolioSummary.totalInvested > 0
                    ? ((portfolioSummary.projectedAnnualDividend / portfolioSummary.totalInvested) * 100).toFixed(1)
                    : 0}%
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Annual Projection:</span>
                <span className="text-green-400 font-semibold">
                  ${portfolioSummary.projectedAnnualDividend.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Dividend per Share:</span>
                <span className="text-green-400 font-semibold">
                  ${portfolioSummary.totalShares > 0
                    ? (portfolioSummary.projectedAnnualDividend / portfolioSummary.totalShares).toFixed(2)
                    : '0.00'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Payback Period:</span>
                <span className="text-white font-semibold">
                  {portfolioSummary.projectedAnnualDividend > 0
                    ? (portfolioSummary.totalInvested / portfolioSummary.projectedAnnualDividend).toFixed(1)
                    : 'N/A'} years
                </span>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <h4 className="text-lg font-semibold text-white mb-4">📅 Dividend Breakdown</h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-400">Monthly:</span>
                <span className="text-blue-400 font-semibold">
                  ${monthlyDividendProjection.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Weekly:</span>
                <span className="text-blue-400 font-semibold">
                  ${weeklyDividendProjection.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Daily:</span>
                <span className="text-blue-400 font-semibold">
                  ${dailyDividendProjection.toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Per Share/Year:</span>
                <span className="text-green-400 font-semibold">
                  ${portfolioSummary.totalShares > 0
                    ? (portfolioSummary.projectedAnnualDividend / portfolioSummary.totalShares).toFixed(2)
                    : '0.00'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Dividend Calculation Details */}
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <h4 className="text-lg font-semibold text-white mb-4">🔍 Calculation Methodology</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h5 className="text-blue-400 font-semibold mb-2">Mining Parameters</h5>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">Plant Capacity:</span>
                  <span className="text-white">{PLANT_CAPACITY_TPH} TPH</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Operating Hours/Day:</span>
                  <span className="text-white">{EFFECTIVE_HOURS_PER_DAY} hours</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Operating Days/Year:</span>
                  <span className="text-white">{OPERATING_DAYS_PER_YEAR} days</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Recovery Factor:</span>
                  <span className="text-white">{DEFAULT_MINING_PARAMS.recoveryFactor}%</span>
                </div>
              </div>
            </div>
            <div>
              <h5 className="text-blue-400 font-semibold mb-2">Financial Parameters</h5>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">Gold Price:</span>
                  <span className="text-yellow-400">${DEFAULT_MINING_PARAMS.goldPriceUsdPerKg.toLocaleString()}/kg</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Total Shares:</span>
                  <span className="text-white">{TOTAL_SHARES.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Your Ownership:</span>
                  <span className="text-blue-400">
                    {((portfolioSummary.totalShares / TOTAL_SHARES) * 100).toFixed(4)}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Distribution:</span>
                  <span className="text-green-400">100% EBIT</span>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-4 p-3 bg-blue-900/30 rounded-lg border border-blue-700">
            <p className="text-blue-200 text-sm">
              💡 <strong>Note:</strong> Dividends are calculated based on 100% EBIT distribution from baseline mining operations.
              Actual dividends may vary based on operational performance, gold prices, and company decisions.
            </p>
          </div>
        </div>

        {/* Enhanced Holdings Distribution */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h4 className="text-lg font-semibold text-white mb-4">🥧 Holdings Distribution by Phase</h4>
        {holdings.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-400 text-lg mb-4">📊</div>
            <p className="text-gray-400">No holdings to analyze yet.</p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Visual Distribution Bars */}
            <div className="space-y-4">
              {holdings.map((holding, index) => {
                const percentage = (holding.shares_purchased / portfolioSummary.totalShares) * 100;
                const valuePercentage = (holding.total_amount / portfolioSummary.totalInvested) * 100;
                const colors = [
                  'bg-gradient-to-r from-blue-600 to-blue-400',
                  'bg-gradient-to-r from-green-600 to-green-400',
                  'bg-gradient-to-r from-purple-600 to-purple-400',
                  'bg-gradient-to-r from-yellow-600 to-yellow-400',
                  'bg-gradient-to-r from-red-600 to-red-400',
                  'bg-gradient-to-r from-indigo-600 to-indigo-400'
                ];
                const colorClass = colors[index % colors.length];

                return (
                  <div key={holding.id} className="space-y-3">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center space-x-3">
                        <div className={`w-4 h-4 rounded-full ${colorClass}`}></div>
                        <span className="text-white font-semibold">{holding.phase_name}</span>
                      </div>
                      <div className="text-right">
                        <div className="text-white font-semibold">
                          {holding.shares_purchased.toLocaleString()} shares
                        </div>
                        <div className="text-gray-400 text-sm">
                          ${holding.total_amount.toLocaleString()} ({percentage.toFixed(1)}%)
                        </div>
                      </div>
                    </div>

                    {/* Shares Distribution Bar */}
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs text-gray-400">
                        <span>Share Distribution</span>
                        <span>{percentage.toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-3">
                        <div
                          className={`h-3 rounded-full ${colorClass}`}
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                    </div>

                    {/* Value Distribution Bar */}
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs text-gray-400">
                        <span>Value Distribution</span>
                        <span>{valuePercentage.toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${colorClass} opacity-70`}
                          style={{ width: `${valuePercentage}%` }}
                        />
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Summary Statistics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-700">
              <div className="text-center">
                <div className="text-lg font-bold text-blue-400">
                  {holdings.length}
                </div>
                <div className="text-xs text-gray-400">Positions</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-green-400">
                  {holdings.length > 0 ? Math.round(portfolioSummary.totalShares / holdings.length).toLocaleString() : 0}
                </div>
                <div className="text-xs text-gray-400">Avg Shares/Position</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-yellow-400">
                  ${holdings.length > 0 ? Math.round(portfolioSummary.totalInvested / holdings.length).toLocaleString() : 0}
                </div>
                <div className="text-xs text-gray-400">Avg Value/Position</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-purple-400">
                  {holdings.length > 0 ? (portfolioSummary.totalInvested / portfolioSummary.totalShares).toFixed(2) : '0.00'}
                </div>
                <div className="text-xs text-gray-400">Avg Cost/Share</div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced Market Comparison */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h4 className="text-lg font-semibold text-white mb-4">🏆 Market Comparison & Benchmarking</h4>

        {/* Primary Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-gray-700 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-yellow-400 mb-1">
              ${(109026).toLocaleString()}
            </div>
            <div className="text-sm text-gray-400 mb-1">Current Gold Price/kg</div>
            <div className="text-xs text-green-400">+2.3% this month</div>
            <div className="text-xs text-gray-500">vs $106,500 last month</div>
          </div>

          <div className="bg-gray-700 rounded-lg p-4 text-center">
            <div className={`text-2xl font-bold mb-1 ${portfolioSummary.unrealizedGainsPercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {portfolioSummary.unrealizedGainsPercent.toFixed(1)}%
            </div>
            <div className="text-sm text-gray-400 mb-1">Your Portfolio Return</div>
            <div className="text-xs text-blue-400">
              {portfolioSummary.unrealizedGainsPercent >= 0 ? 'Outperforming' : 'Underperforming'} gold
            </div>
            <div className="text-xs text-gray-500">
              {annualizedReturn >= 0 ? '+' : ''}{annualizedReturn.toFixed(1)}% annualized
            </div>
          </div>

          <div className="bg-gray-700 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-400 mb-1">
              {portfolioSummary.totalInvested > 0
                ? ((portfolioSummary.projectedAnnualDividend / portfolioSummary.totalInvested) * 100).toFixed(1)
                : 0}%
            </div>
            <div className="text-sm text-gray-400 mb-1">Dividend Yield on Cost</div>
            <div className="text-xs text-green-400">vs 2.1% S&P 500 avg</div>
            <div className="text-xs text-gray-500">vs 0.5% savings account</div>
          </div>
        </div>

        {/* Benchmark Comparisons */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h5 className="text-blue-400 font-semibold mb-3">📊 Shareholding vs Benchmarks</h5>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Gold ETF (GLD):</span>
                <div className="text-right">
                  <span className="text-yellow-400 font-semibold">+1.8%</span>
                  <div className="text-xs text-gray-500">YTD</div>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Mining Stocks (GDX):</span>
                <div className="text-right">
                  <span className="text-red-400 font-semibold">-3.2%</span>
                  <div className="text-xs text-gray-500">YTD</div>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">S&P 500:</span>
                <div className="text-right">
                  <span className="text-green-400 font-semibold">+12.4%</span>
                  <div className="text-xs text-gray-500">YTD</div>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Your Shareholding:</span>
                <div className="text-right">
                  <span className={`font-semibold ${portfolioSummary.unrealizedGainsPercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {portfolioSummary.unrealizedGainsPercent >= 0 ? '+' : ''}{portfolioSummary.unrealizedGainsPercent.toFixed(1)}%
                  </span>
                  <div className="text-xs text-gray-500">Total Return</div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h5 className="text-blue-400 font-semibold mb-3">💎 Shareholding Characteristics</h5>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-400">Asset Class:</span>
                <span className="text-white">Precious Metals Mining</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Risk Level:</span>
                <span className="text-yellow-400">Moderate-High</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Liquidity:</span>
                <span className="text-orange-400">Limited (Private Equity)</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Dividend Focus:</span>
                <span className="text-green-400">High Yield Strategy</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Inflation Hedge:</span>
                <span className="text-blue-400">Strong</span>
              </div>
            </div>
          </div>
        </div>

        {/* Performance Attribution */}
        <div className="mt-6 p-4 bg-blue-900/20 rounded-lg border border-blue-700">
          <h5 className="text-blue-400 font-semibold mb-2">🎯 Performance Attribution</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-400">Gold Price Impact:</span>
              <div className="text-yellow-400 font-semibold">+2.3%</div>
            </div>
            <div>
              <span className="text-gray-400">Operational Performance:</span>
              <div className="text-green-400 font-semibold">Expected</div>
            </div>
            <div>
              <span className="text-gray-400">Market Sentiment:</span>
              <div className="text-blue-400 font-semibold">Neutral</div>
            </div>
          </div>
        </div>
      </div>

      {/* Investment Insights */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h4 className="text-lg font-semibold text-white mb-4">💡 Investment Insights</h4>
        <div className="space-y-3">
          <div className="flex items-start space-x-3">
            <div className="text-green-400 text-xl">✅</div>
            <div>
              <div className="text-white font-semibold">Diversification Opportunity</div>
              <div className="text-sm text-gray-300">
                Consider spreading purchases across multiple phases to reduce average cost.
              </div>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="text-blue-400 text-xl">📈</div>
            <div>
              <div className="text-white font-semibold">Growth Potential</div>
              <div className="text-sm text-gray-300">
                Early phase purchases historically show higher dividends as operations expand.
              </div>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="text-yellow-400 text-xl">💰</div>
            <div>
              <div className="text-white font-semibold">Dividend Reinvestment</div>
              <div className="text-sm text-gray-300">
                Reinvesting dividends can compound your dividends over time.
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Shareholder Insights and Recommendations */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h4 className="text-lg font-semibold text-white mb-4">🧠 Shareholder Insights & Recommendations</h4>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Portfolio Analysis */}
          <div>
            <h5 className="text-blue-400 font-semibold mb-3">📈 Portfolio Analysis</h5>
            <div className="space-y-3">
              {portfolioSummary.totalShares === 0 ? (
                <div className="p-3 bg-yellow-900/30 rounded-lg border border-yellow-700">
                  <p className="text-yellow-200 text-sm">
                    🚀 <strong>Getting Started:</strong> Consider making your first share purchase to begin building your gold mining shareholding.
                  </p>
                </div>
              ) : (
                <>
                  {/* Concentration Analysis */}
                  {((portfolioSummary.totalShares / TOTAL_SHARES) * 100) < 0.01 ? (
                    <div className="p-3 bg-blue-900/30 rounded-lg border border-blue-700">
                      <p className="text-blue-200 text-sm">
                        💡 <strong>Growth Opportunity:</strong> Your current position represents {((portfolioSummary.totalShares / TOTAL_SHARES) * 100).toFixed(4)}% of total shares.
                        Consider increasing your position to maximize dividend potential.
                      </p>
                    </div>
                  ) : (
                    <div className="p-3 bg-green-900/30 rounded-lg border border-green-700">
                      <p className="text-green-200 text-sm">
                        ✅ <strong>Strong Position:</strong> You own {((portfolioSummary.totalShares / TOTAL_SHARES) * 100).toFixed(4)}% of total shares,
                        positioning you well for dividend distributions.
                      </p>
                    </div>
                  )}

                  {/* Diversification Analysis */}
                  {holdings.length === 1 ? (
                    <div className="p-3 bg-orange-900/30 rounded-lg border border-orange-700">
                      <p className="text-orange-200 text-sm">
                        🎯 <strong>Diversification Tip:</strong> Consider purchasing shares across multiple phases to spread your entry points
                        and potentially benefit from different pricing tiers.
                      </p>
                    </div>
                  ) : holdings.length > 1 ? (
                    <div className="p-3 bg-green-900/30 rounded-lg border border-green-700">
                      <p className="text-green-200 text-sm">
                        🎯 <strong>Well Diversified:</strong> You have positions across {holdings.length} different phases,
                        providing good diversification across entry points.
                      </p>
                    </div>
                  ) : null}

                  {/* Performance Analysis */}
                  {portfolioSummary.unrealizedGainsPercent > 10 ? (
                    <div className="p-3 bg-green-900/30 rounded-lg border border-green-700">
                      <p className="text-green-200 text-sm">
                        🚀 <strong>Strong Performance:</strong> Your portfolio is up {portfolioSummary.unrealizedGainsPercent.toFixed(1)}%!
                        Consider your exit strategy or reinvestment plans.
                      </p>
                    </div>
                  ) : portfolioSummary.unrealizedGainsPercent < -5 ? (
                    <div className="p-3 bg-red-900/30 rounded-lg border border-red-700">
                      <p className="text-red-200 text-sm">
                        📉 <strong>Temporary Decline:</strong> Your shareholding is down {Math.abs(portfolioSummary.unrealizedGainsPercent).toFixed(1)}%.
                        Remember that mining shareholdings are long-term positions focused on dividend income.
                      </p>
                    </div>
                  ) : (
                    <div className="p-3 bg-blue-900/30 rounded-lg border border-blue-700">
                      <p className="text-blue-200 text-sm">
                        📊 <strong>Stable Performance:</strong> Your shareholding return of {portfolioSummary.unrealizedGainsPercent.toFixed(1)}%
                        shows steady performance. Focus on the dividend income potential.
                      </p>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>

          {/* Strategic Recommendations */}
          <div>
            <h5 className="text-blue-400 font-semibold mb-3">🎯 Strategic Recommendations</h5>
            <div className="space-y-3">
              {/* Dividend Focus */}
              <div className="p-3 bg-purple-900/30 rounded-lg border border-purple-700">
                <p className="text-purple-200 text-sm">
                  💰 <strong>Dividend Strategy:</strong> With a projected yield of{' '}
                  {portfolioSummary.totalInvested > 0
                    ? ((portfolioSummary.projectedAnnualDividend / portfolioSummary.totalInvested) * 100).toFixed(1)
                    : 0}%,
                  this shareholding is positioned as a high-yield dividend strategy rather than capital appreciation.
                </p>
              </div>

              {/* Risk Management */}
              <div className="p-3 bg-yellow-900/30 rounded-lg border border-yellow-700">
                <p className="text-yellow-200 text-sm">
                  ⚠️ <strong>Risk Considerations:</strong> Mining shareholdings carry operational, commodity price, and regulatory risks.
                  Ensure this fits your risk tolerance and represents an appropriate portion of your total shareholdings.
                </p>
              </div>

              {/* Long-term Perspective */}
              <div className="p-3 bg-indigo-900/30 rounded-lg border border-indigo-700">
                <p className="text-indigo-200 text-sm">
                  ⏰ <strong>Long-term View:</strong> Gold mining operations typically require 3-5 years to reach full production.
                  Plan for a long-term holding period to realize full dividend potential.
                </p>
              </div>

              {/* Market Timing */}
              <div className="p-3 bg-gray-700 rounded-lg border border-gray-600">
                <p className="text-gray-200 text-sm">
                  📅 <strong>Market Timing:</strong> With gold at ${(109026).toLocaleString()}/kg and mining costs relatively stable,
                  current market conditions appear favorable for gold mining operations.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Items */}
        <div className="mt-6 p-4 bg-gray-700 rounded-lg border border-gray-600">
          <h5 className="text-white font-semibold mb-2">📋 Recommended Actions</h5>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <ul className="space-y-1 text-gray-300">
                <li>• Monitor quarterly dividend announcements</li>
                <li>• Track gold price movements and market trends</li>
                <li>• Review operational updates from management</li>
              </ul>
            </div>
            <div>
              <ul className="space-y-1 text-gray-300">
                <li>• Consider tax implications of dividend income</li>
                <li>• Evaluate shareholding allocation vs other assets</li>
                <li>• Plan for potential share purchase opportunities</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
  };

  return (
    <div className="bg-gray-900 rounded-lg border border-gray-700">
      {/* Header */}
      <div className="p-6 border-b border-gray-700">
        <h2 className="text-2xl font-bold text-white mb-2">📊 My Portfolio</h2>
        <p className="text-gray-400">
          Comprehensive view of your Aureus Africa share holdings and performance
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="flex border-b border-gray-700">
        {[
          { key: 'overview', label: '📊 Overview', icon: '📊' },
          { key: 'certificates', label: '📜 Certificates', icon: '📜' },
          { key: 'kyc', label: '🔐 KYC Verification', icon: '🔐' },
          { key: 'transactions', label: '📋 Transactions', icon: '📋' },
          { key: 'analytics', label: '📈 Analytics', icon: '📈' }
        ].map(tab => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key as any)}
            className={`flex-1 p-4 text-center font-medium transition-colors ${
              activeTab === tab.key
                ? 'bg-blue-600 text-white border-b-2 border-blue-400'
                : 'text-gray-400 hover:text-gray-300 hover:bg-gray-800'
            }`}
          >
            <span className="mr-2">{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {loading ? (
          <div className="text-center py-8">
            <div className="text-gray-400 text-lg mb-4">⏳</div>
            <p className="text-gray-400">Loading portfolio data...</p>
          </div>
        ) : (
          <>
            {activeTab === 'overview' && renderOverviewTab()}
            {activeTab === 'certificates' && renderCertificatesTab()}
            {activeTab === 'kyc' && renderKYCTab()}
            {activeTab === 'transactions' && renderTransactionsTab()}
            {activeTab === 'analytics' && renderAnalyticsTab()}
          </>
        )}
      </div>
    </div>
  );
};
