﻿
import React, { useState, useEffect, useCallback, useRef } from 'react';
import ErrorBoundary from './components/ErrorBoundary';
import { EnhancedGallery } from './components/gallery/EnhancedGallery';
import { AdminRouter } from './AdminRouter';
import { useSiteContentContext } from './contexts/SiteContentContext';
import { useGoldPrice } from './hooks/useGoldPrice';
import { PrivacyPolicy } from './components/PrivacyPolicy';
import { TermsAndConditions } from './components/TermsAndConditions';
import { LegalDisclaimer } from './components/LegalDisclaimer';
import { EmailRegistrationForm } from './components/EmailRegistrationForm'
import { TelegramRedirectMessage } from './components/TelegramRedirectMessage';
import { UnifiedAuthPageClean as UnifiedAuthPage } from './components/UnifiedAuthPageClean';
import { AccountSyncDashboard } from './components/AccountSyncDashboard';
import { SyncPage } from './components/SyncPage';
import { ProfileCompletionForm } from './components/ProfileCompletionForm';
import { BasicProfileCompletion } from './components/BasicProfileCompletion';
import { UserDashboard } from './components/UserDashboard'
import { AffiliateDashboard } from './components/AffiliateDashboard';
import { PasswordResetForm } from './components/PasswordResetForm';
import { ComprehensivePhaseDisplay } from './components/ComprehensivePhaseDisplay';
import GoldDiggersClub from './components/competition/GoldDiggersClub';
import { initializeReferralTracking } from './lib/referralPersistence';
import { getCurrentUser, signOut } from './lib/supabase';
import { EmailProcessingService } from './lib/services/emailProcessingService';
import AffiliateLandingPage from './components/AffiliateLandingPage'
import { FlowingLandingPage as CustomAffiliateLandingPage } from './components/affiliate/FlowingLandingPage';
import CorporateHomepage from './CorporateHomepage';
import PurchaseSharesPage from './pages/PurchaseSharesPage';
// Using single dark theme design system


import { DEFAULT_VALUES, LAND_SIZE_OPTIONS, PLANT_CAPACITY_TPH, EFFECTIVE_HOURS_PER_DAY, OPERATING_DAYS_PER_YEAR, BULK_DENSITY_T_PER_M3, HA_PER_PLANT, TOTAL_SHARES, PROJECT_TOTAL_HA, EXPANSION_PLAN, EXPANSION_YEARS, getExpansionPlanForYear } from './constants';
import type { CalculatorInputs, CalculatedValues, ProjectionYear } from './types';

// --- ICONS ---
const ResetIcon: React.FC = () => (<svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}><path strokeLinecap="round" strokeLinejoin="round" d="M4 4v5h5M20 20v-5h-5M4 4l1.5 1.5A9 9 0 0120.5 10M20 20l-1.5-1.5A9 9 0 003.5 14" /></svg>);
const CheckIcon: React.FC<{className?: string}> = ({className}) => (<svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 ${className}`} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={3}><path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" /></svg>);
const ArrowRightIcon: React.FC = () => (<svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}><path strokeLinecap="round" strokeLinejoin="round" d="M17 8l4 4m0 0l-4 4m4-4H3" /></svg>);
const TrophyIcon: React.FC<{className?: string}> = ({className}) => (<svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="currentColor"><path d="M18.5 2h-13A2.5 2.5 0 003 4.5V9a2 2 0 002 2h2v2H5a2 2 0 00-2 2v2.5a2.5 2.5 0 002.5 2.5h13a2.5 2.5 0 002.5-2.5V15a2 2 0 00-2-2h-2v-2h2a2 2 0 002-2V4.5A2.5 2.5 0 0018.5 2zM12 13a1 1 0 01-1 1H7a1 1 0 01-1-1v-2h6v2zm6-2a1 1 0 01-1 1h-4v-2h5a1 1 0 011 1v1z"></path></svg>);
const CrownIcon: React.FC<{className?: string}> = ({className}) => (<svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="currentColor"><path d="M5.18 4.6l-1.5 8.23 2.26.42 1.3-7.14 2.8 2.3-1.45 8.04h8.82l-1.45-8.04 2.8-2.3 1.3 7.14 2.26-.42-1.5-8.23L12 10.3 5.18 4.6zM18.5 20h-13a1 1 0 010-2h13a1 1 0 010 2z"></path></svg>);
const SecondPlaceIcon: React.FC<{className?: string}> = ({className}) => (<svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm-1.5 13.5V14H8v-2h2.5V9.5h3V12H16v2h-2.5v1.5h-3z"></path></svg>);
const ThirdPlaceIcon: React.FC<{className?: string}> = ({className}) => (<svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm1 13.5v-1H9.5v-3h3v-1H13V9.67A1.5 1.5 0 0111.5 8.5a1.5 1.5 0 011.5-1.5v-1h-3v1a1.5 1.5 0 01-1.5 1.5 1.5 1.5 0 01-1.5-1.5v-1h-1v3h1.5v1H8v1.83A1.5 1.5 0 019.5 16a1.5 1.5 0 01-1.5 1.5v1h3v-1A1.5 1.5 0 019.5 15a1.5 1.5 0 011.5-1.5h.5v3h1.5z"></path></svg>);
const MedalIcon: React.FC<{className?: string}> = ({className}) => (<svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm0 15a5 5 0 110-10 5 5 0 010 10z"></path><path d="M12 14a2 2 0 100-4 2 2 0 000 4z"></path></svg>);
const NetworkIcon: React.FC<{className?: string}> = ({className}) => <svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="currentColor"><path d="M19.41,12a1,1,0,0,0-.41-1.41L13.41,6.41a3,3,0,0,0-4.82,0L3,12l5.59,5.59a3,3,0,0,0,4.82,0L19,13.41A1,1,0,0,0,19.41,12ZM12,15.59A1.59,1.59,0,1,1,13.59,14,1.59,1.59,0,0,1,12,15.59Zm0-7.18A1.59,1.59,0,1,1,13.59,7,1.59,1.59,0,0,1,12,8.41ZM7,12A1.59,1.59,0,1,1,8.59,10.41,1.59,1.59,0,0,1,7,12Zm10,0a1.59,1.59,0,1,1,1.59-1.59A1.59,1.59,0,0,1,17,12Z"/></svg>;
const ChartPieIcon: React.FC<{ className?: string }> = ({ className }) => <svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="currentColor"><path d="M12,2A10,10,0,1,0,22,12,10,10,0,0,0,12,2Zm1,17.93V13h8.93A8,8,0,0,1,13,19.93ZM13,11V4.07A8,8,0,0,1,20.93,11Z" /><path d="M11,17.93A8,8,0,0,1,4.07,13H11Z" /></svg>;
const HeartIcon: React.FC<{ className?: string }> = ({ className }) => <svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="currentColor"><path d="M12,21.35l-1.45-1.32C5.4,15.36,2,12.28,2,8.5,2,5.42,4.42,3,7.5,3c1.74,0,3.41.81,4.5,2.09C13.09,3.81,14.76,3,16.5,3,19.58,3,22,5.42,22,8.5c0,3.78-3.4,6.86-8.55,11.54Z" /></svg>;
const DocumentIcon: React.FC<{className?: string}> = ({className}) => <svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="currentColor"><path d="M14,2H6A2,2,0,0,0,4,4v16a2,2,0,0,0,2,2H18a2,2,0,0,0,2-2V8Zm2,16H8V4h5v5h5Z"/></svg>;
const VideoCameraIcon: React.FC<{className?: string}> = ({className}) => <svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="currentColor"><path d="M17,10.5V7a1,1,0,0,0-1-1H4A1,1,0,0,0,3,7v10a1,1,0,0,0,1,1h12a1,1,0,0,0,1-1v-3.5l4,4v-11Z"/></svg>;
const UsersIcon: React.FC<{className?: string}> = ({className}) => <svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="currentColor"><path d="M16,13a4,4,0,1,0-4-4A4,4,0,0,0,16,13Zm4-6a2,2,0,1,0-2-2A2,2,0,0,0,20,7ZM4,13a4,4,0,1,0-4-4A4,4,0,0,0,4,13Zm4,6a2,2,0,1,0-2-2A2,2,0,0,0,8,19Z"/></svg>;

// --- HELPER & UI COMPONENTS ---
const formatNumber = (num: number, options?: Intl.NumberFormatOptions) => {
  if (isNaN(num) || !isFinite(num)) return 'N/A';
  return new Intl.NumberFormat('en-US', options).format(num);
};

// --- ENHANCED ANIMATION WRAPPER ---
const AnimatedSectionWrapper: React.FC<{children: React.ReactNode, className?: string}> = ({ children, className = 'fade-in-section' }) => {
    const [isVisible, setVisible] = useState(false);
    const domRef = useRef<HTMLDivElement>(null);

    // Initialize iOS Safari fixes on component mount
    useEffect(() => {
        import('./lib/iosSafariUtils').then(({ applyIOSSafariFixes, detectBrowser }) => {
            const browser = detectBrowser();
            if (browser.isIOSSafari) {
                console.log('🍎 iOS Safari detected - applying fixes...');
                applyIOSSafariFixes();
            }
        }).catch(error => {
            console.warn('⚠️ Failed to load iOS Safari utilities:', error);
        });
    }, []);

    useEffect(() => {
        const observer = new IntersectionObserver(entries => {
            if (entries[0].isIntersecting) {
                setVisible(true);
                observer.unobserve(domRef.current!);
            }
        }, {
            threshold: 0.1,
            rootMargin: '50px 0px -50px 0px'
        });

        if (domRef.current) {
            observer.observe(domRef.current);
        }

        return () => {
            if (domRef.current) {
                observer.unobserve(domRef.current!);
            }
        };
    }, []);

    return (
        <div ref={domRef} className={`${className} ${isVisible ? 'is-visible' : ''}`}>
            {children}
        </div>
    );
};

// --- CALCULATOR COMPONENT ---
interface CalculatorProps {
    liveGoldPrice: number;
    goldPriceLoading: boolean;
    goldPriceError: string | null;
}

const Calculator: React.FC<CalculatorProps> = ({ liveGoldPrice, goldPriceLoading, goldPriceError }) => {
    let getContent: (section: string, key: string, defaultValue?: any) => any;

    try {
        const context = useSiteContentContext();
        getContent = context.getContent;
    } catch (error) {
        console.warn('Calculator: Site content context not available, using defaults');
        getContent = (section: string, key: string, defaultValue: any = '') => defaultValue;
    }
    const [inputs, setInputs] = useState<CalculatorInputs>(() => {
        const expansionData = getExpansionPlanForYear(2026);
        const initialState = {
            ...DEFAULT_VALUES,
            selectedYear: 2026,
            landHa: expansionData?.hectares || 250, // Override DEFAULT_VALUES.landHa with expansion plan
            goldPriceUsdPerKg: 109026, // Use current market price
            userShares: 1 // Start with 1 share
        };
        console.log('Calculator initial state:', initialState);
        return initialState;
    });
    const [calculated, setCalculated] = useState<CalculatedValues>({} as CalculatedValues);
    const [projection, setProjection] = useState<ProjectionYear[]>([]);
    const [allocatedShares, setAllocatedShares] = useState(0);
    const [selectedProjectionYear, setSelectedProjectionYear] = useState(1);

    // Get dynamic values from database with safe parsing
    const calculatorTitle = getContent('calculator', 'title', 'Financial Calculator') || 'Financial Calculator';
    const calculatorSubtitle = getContent('calculator', 'subtitle', 'Experience the power of data-driven investment decisions with our interactive calculator') || 'Experience the power of data-driven investment decisions with our interactive calculator';
    const defaultLandSize = parseInt(getContent('calculator', 'default_land_size', '25') || '25') || 25;
    const defaultUserShares = parseInt(getContent('calculator', 'default_user_shares', '1') || '1') || 1;
    const defaultGoldPrice = parseInt(getContent('calculator', 'gold_price_default', '100000') || '100000') || 100000;

    const resetToDefaults = useCallback(() => {
        const expansionData = getExpansionPlanForYear(2026);
        setInputs({
            ...DEFAULT_VALUES,
            landHa: expansionData?.hectares || 250, // Use expansion plan value
            userShares: 1, // Start with 1 share
            goldPriceUsdPerKg: 120000, // Use current market price
            selectedYear: 2026
        });
    }, []);

    // Update inputs when database values change
    useEffect(() => {
        const expansionData = getExpansionPlanForYear(2026);
        setInputs(prev => ({
            ...prev,
            landHa: prev.selectedYear ? getExpansionPlanForYear(prev.selectedYear)?.hectares || 250 : expansionData?.hectares || 250,
            userShares: 1, // Start with 1 share
            goldPriceUsdPerKg: 120000, // Use current market price
            selectedYear: prev.selectedYear || 2026
        }));
    }, []);

    useEffect(() => {
        const { landHa, avgGravelThickness, inSituGrade, recoveryFactor, goldPriceUsdPerKg, opexPercent, userShares, dividendPayoutPercent } = inputs;

        // Core Logic: Each wash plant processes exactly 25 hectares
        const numPlants = landHa / HA_PER_PLANT; // 25 hectares per plant
        const plantsPerProject = PROJECT_TOTAL_HA / HA_PER_PLANT;
        const sharesPerPlant = TOTAL_SHARES / plantsPerProject;
        setAllocatedShares(numPlants * sharesPerPlant);

        // Create 5-year expansion projection based on EXPANSION_PLAN
        const newProjection: any[] = [];

        EXPANSION_YEARS.forEach((year, index) => {
            const expansionData = getExpansionPlanForYear(year);
            if (!expansionData) return;

            const yearNumPlants = expansionData.plants;

            // Annual production calculations for this year
            const annualThroughputT = yearNumPlants * PLANT_CAPACITY_TPH * EFFECTIVE_HOURS_PER_DAY * OPERATING_DAYS_PER_YEAR;
            const annualGoldKg = (annualThroughputT * (inSituGrade / BULK_DENSITY_T_PER_M3) * (recoveryFactor / 100)) / 1000;
            const annualRevenue = annualGoldKg * goldPriceUsdPerKg;
            const annualOperatingCost = annualRevenue * (opexPercent / 100);
            const annualEbit = annualRevenue - annualOperatingCost;

            // Dividend calculations for this year - 100% EBIT payout
            const totalAnnualDividend = annualEbit; // Full EBIT as dividend
            const yearDividendPerShare = TOTAL_SHARES > 0 ? totalAnnualDividend / TOTAL_SHARES : 0;
            const userAnnualDividend = yearDividendPerShare * userShares;

            newProjection.push({
                year: index + 1, // Display as Year 1, 2, 3, 4, 5
                actualYear: year, // Keep track of actual year (2026, 2027, etc.)
                ebit: annualEbit,
                dividendPerShare: yearDividendPerShare,
                userDividend: userAnnualDividend,
                numPlants: yearNumPlants,
                annualGoldKg,
                annualRevenue,
            });
        });

        setProjection(newProjection);


        // Direct calculations based on user's land size and shares
        const userNumPlants = landHa / HA_PER_PLANT; // Plants based on user's land
        const userAnnualThroughputT = userNumPlants * PLANT_CAPACITY_TPH * EFFECTIVE_HOURS_PER_DAY * OPERATING_DAYS_PER_YEAR;
        const userAnnualGoldKg = (userAnnualThroughputT * (inSituGrade / BULK_DENSITY_T_PER_M3) * (recoveryFactor / 100)) / 1000;
        const userAnnualRevenue = userAnnualGoldKg * goldPriceUsdPerKg;
        const userAnnualOperatingCost = userAnnualRevenue * (opexPercent / 100);
        const userAnnualEbit = userAnnualRevenue - userAnnualOperatingCost;

        // Calculate dividends based on actual EBIT potential (dynamic calculation)
        // Use the calculated EBIT from the user's selected land size and parameters
        const landEbitPotential = userAnnualEbit; // EBIT based on user's land selection

        // Calculate dividend per share dynamically: Full EBIT ÷ Total Shares (100% payout)
        const dividendPerShare = TOTAL_SHARES > 0 ? landEbitPotential / TOTAL_SHARES : 0;
        const userActualDividend = dividendPerShare * userShares;

        console.log('💰 Dynamic dividend calculation:', {
            landEbitPotential,
            totalShares: TOTAL_SHARES,
            dividendPerShare,
            userShares,
            userActualDividend,
            expectedDividendPerShare: landEbitPotential / TOTAL_SHARES
        });

        setCalculated({
            numPlants: userNumPlants, // User's plants based on land size
            annualRevenue: userAnnualRevenue, // User's revenue based on land
            annualEbit: userAnnualEbit, // User's EBIT based on land
            annualGoldKg: userAnnualGoldKg, // User's gold production based on land
            dividendPerShare: dividendPerShare, // Dynamic dividend per share based on EBIT
            userAnnualDividend: userActualDividend, // User's dividend based on actual EBIT potential
            volumeM3: landHa * 10000 * avgGravelThickness,
            tonnesInSitu: (landHa * 10000 * avgGravelThickness * BULK_DENSITY_T_PER_M3),
            containedGoldG: (landHa * 10000 * avgGravelThickness * BULK_DENSITY_T_PER_M3 * inSituGrade),
            annualThroughputT: userAnnualThroughputT,
            annualOperatingCost: userAnnualOperatingCost,
        });

    }, [inputs]);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setInputs(prev => ({ ...prev, [name]: parseFloat(value) || 0 }));
    };

    // Handler for year selection - automatically sets corresponding hectares
    const handleYearSelection = (e: React.ChangeEvent<HTMLSelectElement>) => {
        const selectedYear = parseInt(e.target.value);
        const expansionData = getExpansionPlanForYear(selectedYear);

        console.log('Year selected:', selectedYear, 'Expansion data:', expansionData);

        if (expansionData) {
            setInputs(prev => ({
                ...prev,
                selectedYear: selectedYear,
                landHa: expansionData.hectares
            }));
        }
    };

    const Card: React.FC<{title: string; children: React.ReactNode;}> = ({ title, children }) => (
        <div className="bg-black/20 rounded-lg p-3">
            <h3 className="text-sm font-bold text-gold mb-3">{title}</h3>
            <div className="space-y-2">{children}</div>
        </div>
    );

    const CalculatorInput: React.FC<{ label: string; name: keyof CalculatorInputs; value: number | string; onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void; unit?: string; type?: 'number' | 'select'; options?: number[]; step?: number; readOnly?: boolean; }> = ({ label, name, value, onChange, unit, type = 'number', options, step = 0.1, readOnly = false }) => (
        <div className="calculator-input-container">
            <label htmlFor={name} className="calculator-input-label">{label}</label>
            <div className="calculator-input-field">
            {type === 'select' ? (
                <select id={name} name={name} value={value} onChange={onChange} disabled={readOnly}>
                    {options?.map(opt => <option key={opt} value={opt}>{opt}</option>)}
                </select>
            ) : (
                <input type="number" id={name} name={name} value={value} onChange={onChange} step={step} readOnly={readOnly} className={readOnly ? 'opacity-60 cursor-not-allowed' : ''}/>
            )}
            {unit && <span className="calculator-input-unit">{unit}</span>}
            </div>
        </div>
    );

    const CalculatedDisplay: React.FC<{label: string; value: string; unit?: string; isWarning?: boolean;}> = ({ label, value, unit, isWarning }) => (
        <div className={`flex items-center justify-between p-2 rounded ${isWarning ? 'bg-red-900/20' : 'bg-white/5'}`}>
            <span className={`text-sm ${isWarning ? 'text-red-300' : 'text-gray-200'}`}>{label}</span>
            <div className={`font-bold text-sm ${isWarning ? 'text-red-400' : 'text-white'}`}>
                <span>{value}</span>
                {unit && <span className="ml-1 text-amber-400">{unit}</span>}
            </div>
        </div>
    );

    const ProjectionTabs: React.FC<{projection: any[], selectedYear: number, onYearSelect: (year: number) => void}> = ({ projection, selectedYear, onYearSelect }) => {
        const selectedData = projection.find(p => p.year === selectedYear) || projection[0];

        return (
            <div className="projection-tabs-container">
                {/* Tab Navigation */}
                <div className="tab-navigation">
                    <div className="tab-scroll-container">
                        {projection.map(p => (
                            <button
                                key={p.year}
                                onClick={() => onYearSelect(p.year)}
                                className={`tab-button ${selectedYear === p.year ? 'tab-active' : 'tab-inactive'}`}
                            >
                                {p.actualYear ? `${p.actualYear}` : `Year ${p.year}`}
                            </button>
                        ))}
                    </div>
                </div>

                {/* Tab Content */}
                <div className="tab-content">
                    <div className="year-header">
                        <h4 className="text-xl font-bold text-gold mb-4">
                            {selectedData?.actualYear ? `${selectedData.actualYear} Projection` : `Year ${selectedYear} Projection`}
                        </h4>
                    </div>

                    <div className="projection-metrics-grid">
                        <div className="metric-card">
                            <div className="metric-label">Plants in Operation</div>
                            <div className="metric-value">{formatNumber(selectedData?.numPlants || 0, {maximumFractionDigits: 1})}</div>
                            <div className="metric-unit">plants</div>
                        </div>

                        <div className="metric-card">
                            <div className="metric-label">Annual EBIT</div>
                            <div className="metric-value text-green-400">{formatNumber(selectedData?.ebit || 0, { style: 'currency', currency: 'USD', minimumFractionDigits: 0 })}</div>
                            <div className="metric-unit">earnings</div>
                        </div>

                        <div className="metric-card">
                            <div className="metric-label">Gold Production</div>
                            <div className="metric-value text-amber-400">{formatNumber(selectedData?.annualGoldKg || 0, {maximumFractionDigits: 0})}</div>
                            <div className="metric-unit">kg/year</div>
                        </div>

                        <div className="metric-card">
                            <div className="metric-label">Annual Revenue</div>
                            <div className="metric-value text-blue-400">{formatNumber(selectedData?.annualRevenue || 0, { style: 'currency', currency: 'USD', minimumFractionDigits: 0 })}</div>
                            <div className="metric-unit">revenue</div>
                        </div>

                        <div className="metric-card highlight">
                            <div className="metric-label">Dividend per Share</div>
                            <div className="metric-value text-gold">{formatNumber(selectedData?.dividendPerShare || 0, { style: 'currency', currency: 'USD', minimumFractionDigits: 4 })}</div>
                            <div className="metric-unit">per share</div>
                        </div>

                        <div className="metric-card highlight">
                            <div className="metric-label">Your Annual Dividend</div>
                            <div className="metric-value text-gold font-bold">{formatNumber(selectedData?.userDividend || 0, { style: 'currency', currency: 'USD', minimumFractionDigits: 2 })}</div>
                            <div className="metric-unit">your return</div>
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    return (
        <div className="card calculator-container">
            <header className="calculator-header">
                <div className="calculator-header-content">
                    <div className="calculator-title-section">
                        <h3 className="text-lg font-bold text-gold mb-1">Interactive {calculatorTitle}</h3>
                        <p className="text-sm text-gray-300">Model your share purchasing scenario to explore potential returns.</p>
                    </div>
                    <div className="calculator-reset-button">
                        <button onClick={resetToDefaults} className="btn btn-secondary flex items-center gap-2 text-sm">
                            <ResetIcon />
                            <span>Reset</span>
                        </button>
                    </div>
                </div>
            </header>

            <div className="calculator-layout-modern">
                {/* Modern Input Section */}
                <div className="calculator-inputs-modern">
                    <div className="inputs-header">
                        <h3 className="text-xl font-bold text-gold mb-2">Investment Parameters</h3>
                        <p className="text-sm text-gray-400 mb-6">Configure your investment scenario and project assumptions</p>
                    </div>

                    <div className="modern-inputs-grid">
                        {/* Year Selection */}
                        <div className="input-group editable">
                            <label className="input-label">Target Year</label>
                            <select
                                name="selectedYear"
                                value={inputs.selectedYear || 2026}
                                onChange={handleYearSelection}
                                className="modern-input"
                            >
                                {EXPANSION_YEARS.map(year => {
                                    const expansionData = getExpansionPlanForYear(year);
                                    return (
                                        <option key={year} value={year}>
                                            {expansionData?.month} {year} - {expansionData?.plants} Plants ({expansionData?.hectares.toLocaleString()} ha)
                                        </option>
                                    );
                                })}
                            </select>
                            <div className="input-info">
                                <span className="info-text">Select our planned capacity for a specific year</span>
                            </div>
                        </div>

                        {/* Editable Fields */}
                        <div className="input-group editable">
                            <label className="input-label">Land Size (Manual Override)</label>
                            <select
                                name="landHa"
                                value={inputs.landHa}
                                onChange={handleInputChange}
                                className="modern-input"
                            >
                                {LAND_SIZE_OPTIONS.map(size => (
                                    <option key={size} value={size}>{size} ha</option>
                                ))}
                            </select>
                            <div className="input-info">
                                <span className="info-badge">Corresponds to {formatNumber(allocatedShares)} shares</span>
                                <span className="info-text">Adjust manually for "what-if" scenarios</span>
                            </div>
                        </div>

                        <div className="input-group editable">
                            <label className="input-label">Your Shares</label>
                            <input
                                type="number"
                                name="userShares"
                                value={inputs.userShares}
                                onChange={handleInputChange}
                                step={100}
                                className="modern-input"
                            />
                            <div className="input-info">
                                <span className="info-text">Total project shares: {formatNumber(TOTAL_SHARES)}</span>
                            </div>
                        </div>

                        <div className="input-group editable gold-price-group">
                            <label className="input-label">Gold Price</label>
                            <input
                                type="number"
                                name="goldPriceUsdPerKg"
                                value={inputs.goldPriceUsdPerKg}
                                onChange={handleInputChange}
                                step={1000}
                                className="modern-input"
                            />
                            <div className="input-info">
                                <div className="gold-price-controls">
                                    <button
                                        type="button"
                                        onClick={() => setInputs(prev => ({ ...prev, goldPriceUsdPerKg: liveGoldPrice }))}
                                        disabled={goldPriceLoading}
                                        className="live-price-btn"
                                    >
                                        {goldPriceLoading ? 'Loading...' : 'Use Live Price'}
                                    </button>
                                    <span className="live-price-display">
                                        Live: ${liveGoldPrice.toLocaleString()}/kg
                                    </span>
                                </div>
                                {goldPriceError && (
                                    <span className="error-text">({goldPriceError})</span>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Modern Results Section */}
                <div className="calculator-results-modern">
                    <div className="results-header">
                        <h3 className="text-xl font-bold text-gold mb-2">Financial Projections</h3>
                        <p className="text-sm text-gray-400 mb-6">Based on your investment parameters</p>
                    </div>

                    <div className="results-grid">
                        <div className="result-card operational">
                            <div className="result-icon">🏭</div>
                            <div className="result-content">
                                <div className="result-label">Plants for Your Land</div>
                                <div className="result-value">{formatNumber(calculated.numPlants, {maximumFractionDigits: 1})}</div>
                                <div className="result-unit">plants</div>
                            </div>
                        </div>

                        <div className="result-card revenue">
                            <div className="result-icon">💰</div>
                            <div className="result-content">
                                <div className="result-label">Land Revenue Potential</div>
                                <div className="result-value">{formatNumber(calculated.annualRevenue, { style: 'currency', currency: 'USD', minimumFractionDigits: 0 })}</div>
                                <div className="result-unit">per year</div>
                            </div>
                        </div>

                        <div className="result-card ebit">
                            <div className="result-icon">📈</div>
                            <div className="result-content">
                                <div className="result-label">Land EBIT Potential</div>
                                <div className="result-value">{formatNumber(calculated.annualEbit, { style: 'currency', currency: 'USD', minimumFractionDigits: 0 })}</div>
                                <div className="result-unit">earnings</div>
                            </div>
                        </div>

                        <div className="result-card gold">
                            <div className="result-icon">🥇</div>
                            <div className="result-content">
                                <div className="result-label">Land Gold Production</div>
                                <div className="result-value">{formatNumber(calculated.annualGoldKg, { maximumFractionDigits: 2 })}</div>
                                <div className="result-unit">kg/year</div>
                            </div>
                        </div>

                        <div className="result-card dividend highlight">
                            <div className="result-icon">💎</div>
                            <div className="result-content">
                                <div className="result-label">Your Annual Dividend</div>
                                <div className="result-value">{formatNumber(calculated.userAnnualDividend, { style: 'currency', currency: 'USD', minimumFractionDigits: 2 })}</div>
                                <div className="result-unit">your return</div>
                            </div>
                        </div>

                        <div className="result-card dividend-share highlight">
                            <div className="result-icon">📊</div>
                            <div className="result-content">
                                <div className="result-label">Dividend per Share</div>
                                <div className="result-value">{formatNumber(calculated.dividendPerShare, { style: 'currency', currency: 'USD', minimumFractionDigits: 4 })}</div>
                                <div className="result-unit">per share</div>
                            </div>
                        </div>
                    </div>

                    {/* Project Assumptions Subsection */}
                    <div className="project-assumptions-section">
                        <div className="assumptions-header">
                            <h4 className="text-lg font-semibold text-gold mb-3">Project Assumptions</h4>
                            <p className="text-xs text-gray-400 mb-4">Technical parameters used in calculations</p>
                        </div>

                        <div className="assumptions-grid">
                            <div className="assumption-item">
                                <span className="assumption-label">Gravel Thickness</span>
                                <span className="assumption-value">{inputs.avgGravelThickness} meters</span>
                            </div>

                            <div className="assumption-item">
                                <span className="assumption-label">In-situ Grade</span>
                                <span className="assumption-value">{inputs.inSituGrade} g/m³</span>
                            </div>

                            <div className="assumption-item">
                                <span className="assumption-label">Recovery Factor</span>
                                <span className="assumption-value">{inputs.recoveryFactor}% efficiency</span>
                            </div>

                            <div className="assumption-item">
                                <span className="assumption-label">Operating Cost</span>
                                <span className="assumption-value">{inputs.opexPercent}% of revenue</span>
                            </div>

                            <div className="assumption-item">
                                <span className="assumption-label">Dividend Payout</span>
                                <span className="assumption-value">{inputs.dividendPayoutPercent}% of EBIT</span>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Year-by-Year Projection */}
                <div className="projection-section-modern">
                    <div className="projection-header">
                        <h3 className="text-xl font-bold text-gold mb-2">Year-by-Year Expansion</h3>
                        <p className="text-sm text-gray-400 mb-6">Detailed projections for each year of operation</p>
                    </div>

                    <ProjectionTabs
                        projection={projection}
                        selectedYear={selectedProjectionYear}
                        onYearSelect={setSelectedProjectionYear}
                    />
                </div>
            </div>
        </div>
    );
};

// --- NEW SITE COMPONENTS ---
const NavLink: React.FC<{href: string; children: React.ReactNode}> = ({href, children}) => (
    <a href={href} className="relative text-gray-300 hover:text-white transition-all duration-300 group text-sm">
        {children}
        <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-amber-400 to-amber-600 group-hover:w-full transition-all duration-300"></span>
    </a>
);

const Header: React.FC<{setCurrentSection: (section: string) => void, user?: any, clearLocalStorage?: () => void, handleSwitchToRegister?: () => void}> = ({setCurrentSection, user, clearLocalStorage, handleSwitchToRegister}) => {
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

    // Demo mode: DISABLED - All users have full access
    const isDemoUser = () => {
        return true; // Always return true to give all users full access
    }

    // Navigate to purchase shares page - requires authentication
    const handlePurchaseClick = (e: React.MouseEvent) => {
        e.preventDefault();

        // Import iOS Safari utilities dynamically
        import('./lib/iosSafariUtils').then(({ detectBrowser, navigateToPage, logBrowserInfo }) => {
            const browser = detectBrowser();
            logBrowserInfo();

            console.log('🛒 Purchase button clicked', {
                browser: {
                    isMobile: browser.isMobile,
                    isIOSSafari: browser.isIOSSafari,
                    isIOSChrome: browser.isIOSChrome,
                    version: browser.version
                },
                user: !!user,
                currentSection
            });

            // Check if user is authenticated
            if (user) {
                // User is logged in, redirect to purchase shares page (NOT dashboard)
                console.log('✅ User authenticated, redirecting to purchase shares page');

                if (browser.isIOSSafari) {
                    // For iOS Safari, use location.href for more reliable navigation
                    console.log('🍎 iOS Safari detected - using location.href for navigation');
                    window.location.href = '/purchase-shares';
                } else {
                    // For other browsers, use state management
                    setCurrentSection('purchase-shares');
                    window.history.pushState({}, '', '/purchase-shares');
                }
            } else {
                // User is not logged in, redirect to login with purchase intent
                console.log('🔐 User not authenticated, redirecting to login');
                localStorage.setItem('aureus_post_login_redirect', '/purchase-shares');

                console.log('📱 Setting currentSection to account-access');
                setCurrentSection('account-access');
                setAuthMode('login');

                if (browser.isIOSSafari) {
                    // For iOS Safari, use location.href for more reliable navigation
                    console.log('🍎 iOS Safari detected - using location.href for login navigation');
                    window.location.href = '/login';
                } else {
                    window.history.pushState({}, '', '/login');

                    // Add a small delay to ensure state updates on mobile
                    if (browser.isMobile) {
                        setTimeout(() => {
                            console.log('📱 Mobile: currentSection after delay:', currentSection);
                        }, 100);
                    }
                }
            }
        }).catch(error => {
            console.error('❌ Failed to load iOS Safari utilities:', error);
            // Fallback to basic navigation
            if (user) {
                window.location.href = '/purchase-shares';
            } else {
                localStorage.setItem('aureus_post_login_redirect', '/purchase-shares');
                window.location.href = '/login';
            }
        });
    }

    return (
        <header className="header-container" style={{paddingTop: '12px'}}>
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>
            <div className="container">
                <nav className="header-nav">
                    {/* Desktop single-row layout */}
                    <a href="#" className="header-logo-image float-animation desktop-logo">
                        <img
                            src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png?v=2"
                            alt="Aureus Alliance Holdings - Professional Gold Mining Investment Company Logo"
                            className="header-logo-img"
                        />
                    </a>

                    <div className="desktop-nav desktop-nav-single">
                        <NavLink href="#about">About</NavLink>
                        <NavLink href="#highlights">Highlights</NavLink>
                        <NavLink href="#expansion-plan">Expansion Plan</NavLink>
                        <NavLink href="#calculator">Calculator</NavLink>
                        <NavLink href="#project">Project</NavLink>
                    </div>

                    <div className="desktop-actions desktop-actions-single">
                        <button
                            onClick={handlePurchaseClick}
                            className="btn btn-primary"
                            style={{fontSize: '13px', padding: '8px 16px'}}
                        >
                            Purchase Shares
                        </button>
                        <a href="https://t.me/AureusAllianceHoldings" target="_blank" rel="noopener noreferrer" className="btn btn-outline" style={{fontSize: '16px', padding: '8px 12px', minWidth: '44px', display: 'flex', alignItems: 'center', justifyContent: 'center'}} title="Updates Channel">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 0C5.374 0 0 5.373 0 12s5.374 12 12 12 12-5.373 12-12S18.626 0 12 0zm5.568 8.16l-1.61 7.59c-.12.54-.44.67-.89.42l-2.46-1.82-1.19 1.14c-.13.13-.24.24-.49.24l.17-2.43 4.47-4.03c.19-.17-.04-.27-.3-.1L9.28 13.47l-2.38-.75c-.52-.16-.53-.52.11-.77l9.28-3.58c.43-.16.81.11.67.73z"/>
                            </svg>
                        </a>
                    </div>

                    {/* Two-row layout for tablets */}
                    <div className="header-nav-top tablet-layout">
                        <a href="#" className="header-logo-image float-animation">
                            <img
                                src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png?v=2"
                                alt="Aureus Alliance Holdings - Professional Gold Mining Investment Company Logo"
                                className="header-logo-img"
                            />
                        </a>

                        <div className="desktop-actions">
                            <button
                                onClick={handlePurchaseClick}
                                className="btn btn-primary"
                                style={{fontSize: '13px', padding: '8px 16px'}}
                            >
                                Purchase Shares
                            </button>
                            <a href="https://t.me/AureusAllianceHoldings" target="_blank" rel="noopener noreferrer" className="btn btn-outline" style={{fontSize: '16px', padding: '8px 12px', minWidth: '44px', display: 'flex', alignItems: 'center', justifyContent: 'center'}} title="Updates Channel">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 0C5.374 0 0 5.373 0 12s5.374 12 12 12 12-5.373 12-12S18.626 0 12 0zm5.568 8.16l-1.61 7.59c-.12.54-.44.67-.89.42l-2.46-1.82-1.19 1.14c-.13.13-.24.24-.49.24l.17-2.43 4.47-4.03c.19-.17-.04-.27-.3-.1L9.28 13.47l-2.38-.75c-.52-.16-.53-.52.11-.77l9.28-3.58c.43-.16.81.11.67.73z"/>
                                </svg>
                            </a>
                        </div>
                    </div>

                    <div className="header-nav-bottom tablet-layout">
                        <div className="desktop-nav">
                            <NavLink href="#about">About</NavLink>
                            <NavLink href="#highlights">Highlights</NavLink>
                            <NavLink href="#expansion-plan">Expansion Plan</NavLink>
                            <NavLink href="#calculator">Calculator</NavLink>
                            <NavLink href="#project">Project</NavLink>
                        </div>
                    </div>

                    {/* Mobile Menu Button */}
                    <button
                        onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                        className="mobile-menu-button"
                    >
                        <div className="hamburger-icon">
                            <span className={`hamburger-line ${mobileMenuOpen ? 'open' : ''}`}></span>
                            <span className={`hamburger-line ${mobileMenuOpen ? 'open' : ''}`}></span>
                            <span className={`hamburger-line ${mobileMenuOpen ? 'open' : ''}`}></span>
                        </div>
                    </button>
                </nav>
            </div>

            {/* Mobile Menu Overlay */}
            <div className={`mobile-menu-overlay ${mobileMenuOpen ? 'open' : ''}`} onClick={() => setMobileMenuOpen(false)}></div>

            {/* Mobile Menu Panel */}
            <div className={`mobile-menu-panel ${mobileMenuOpen ? 'open' : ''}`}>
                <div className="mobile-menu-content">
                    <nav className="mobile-menu-nav">
                        <NavLink href="#about" onClick={() => setMobileMenuOpen(false)}>About</NavLink>
                        <NavLink href="#highlights" onClick={() => setMobileMenuOpen(false)}>Highlights</NavLink>
                        <NavLink href="#expansion-plan" onClick={() => setMobileMenuOpen(false)}>Expansion Plan</NavLink>
                        <NavLink href="#calculator" onClick={() => setMobileMenuOpen(false)}>Calculator</NavLink>
                        <NavLink href="#project" onClick={() => setMobileMenuOpen(false)}>Project</NavLink>
                    </nav>

                    <div className="mobile-menu-actions">
                        <button
                            onClick={(e) => {
                                setMobileMenuOpen(false);
                                handlePurchaseClick(e);
                            }}
                            className="btn btn-primary btn-block"
                            style={{marginBottom: '0.5rem', fontSize: '14px'}}
                        >
                            Purchase Shares
                        </button>
                        <a href="https://t.me/AureusAllianceHoldings" target="_blank" rel="noopener noreferrer" className="btn btn-outline btn-block" style={{fontSize: '14px', display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px'}} title="Updates Channel">
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 0C5.374 0 0 5.373 0 12s5.374 12 12 12 12-5.373 12-12S18.626 0 12 0zm5.568 8.16l-1.61 7.59c-.12.54-.44.67-.89.42l-2.46-1.82-1.19 1.14c-.13.13-.24.24-.49.24l.17-2.43 4.47-4.03c.19-.17-.04-.27-.3-.1L9.28 13.47l-2.38-.75c-.52-.16-.53-.52.11-.77l9.28-3.58c.43-.16.81.11.67.73z"/>
                            </svg>
                            Updates
                        </a>
                    </div>
                </div>
            </div>
        </header>
    );
};

interface HeroSectionProps {
    variant?: 'home' | 'calculator' | 'affiliate' | 'phases' | 'default';
}

const HeroSection: React.FC<HeroSectionProps> = ({ variant = 'home' }) => (
    <section className={`hero-section hero-${variant}`}>
        <div className="absolute inset-0 bg-black/40"></div>

        <div className="container relative z-10">
            {/* Executive Summary Hero */}
            <div className="executive-hero">
                <div className="executive-content">
                    {/* Company Header */}
                    <div className="company-header">
                        <div className="company-logo">
                            <img src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png?v=2" alt="Aureus Alliance Holdings" className="w-16 h-16" />
                        </div>
                        <div className="company-info">
                            <h1 className="company-name">Aureus Alliance Holdings</h1>
                            <p className="company-tagline">Building Gold-Backed Impact Ventures</p>
                            <div className="company-credentials">
                                <span className="credential">Empowering Communities</span>
                                <span className="credential">Driving Sustainable Growth</span>
                                <span className="credential">Real Gold • Real Profits • Real Impact</span>
                            </div>
                        </div>
                    </div>

                    {/* Value Proposition */}
                    <div className="value-proposition">
                        <h2 className="value-title">Real Gold • Real Shares • Real Ownership</h2>
                        <p className="value-description">
                            Aureus offers real gold equity through CIPC-registered shares, giving shareholders full legal ownership
                            in a structured, professionally managed mining venture. This creates a transparent, gold-backed economy
                            built on trust, security, and long-term value.
                        </p>
                    </div>

                    {/* Key Investment Metrics */}
                    <div className="investment-metrics">
                        <div className="metric-item">
                            <div className="metric-number">1.4M</div>
                            <div className="metric-label">Total Shares Available</div>
                        </div>
                        <div className="metric-item">
                            <div className="metric-number">$5.00</div>
                            <div className="metric-label">Presale Price</div>
                        </div>
                        <div className="metric-item">
                            <div className="metric-number">200K</div>
                            <div className="metric-label">Presale Shares</div>
                        </div>
                        <div className="metric-item">
                            <div className="metric-number">2026</div>
                            <div className="metric-label">Operations Start</div>
                        </div>
                        <div className="metric-item">
                            <div className="metric-number">$15-$50</div>
                            <div className="metric-label">First Dividend April 2026</div>
                        </div>
                    </div>

                    {/* Primary CTA */}
                    <div className="primary-cta">
                        <button
                            className="cta-primary"
                            onClick={() => alert('Coming Soon')}
                        >
                            Review Share Offering Memorandum
                        </button>
                        <button
                            className="cta-secondary"
                            onClick={() => alert('Coming Soon')}
                        >
                            Download Due Diligence Package
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>
);

const Section: React.FC<{id?: string, title: string, subtitle: string, children: React.ReactNode, className?: string}> = ({id, title, subtitle, children, className=""}) => (
    <section id={id} className={`section relative ${className}`}>
        <div className="absolute inset-0 bg-gradient-to-b from-gray-900/20 to-black/40"></div>

        <div className="container relative z-10">
            <div className="text-center mb-md">
                <h2 className="heading-xl text-gold mb-xs">
                    {title}
                </h2>
                <p className="text-lg max-w-2xl mx-auto">
                    {subtitle}
                </p>
            </div>
            {children}
        </div>
    </section>
);

const CtaSection: React.FC = () => (
    <div className="relative py-16 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-amber-900/10 via-transparent to-blue-900/10"></div>
        <div className="absolute inset-0 cyber-grid opacity-20"></div>

        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
            <div className="aureus-container max-w-5xl mx-auto">
                <div className="aureus-container-inner">
                    <div className="flex items-center justify-center gap-4 mb-6">
                        <div className="w-12 h-12 bg-gradient-to-br from-amber-400 to-amber-600 rounded-xl flex items-center justify-center">
                            <span className="text-black font-bold text-xl"></span>
                        </div>
                        <h3 className="text-3xl md:text-4xl font-black text-gradient-gold">Ready to Become a Shareholder?</h3>
                    </div>

                    <p className="text-lg md:text-xl text-gray-200 mb-8 max-w-3xl mx-auto leading-relaxed">
                        Purchase shares directly and securely through our dedicated Telegram bot. Get started on your journey to owning a piece of this revolutionary project.
                    </p>

                    <div className="flex flex-col lg:flex-row gap-8 justify-center items-center">
                        <button
                            onClick={handleSwitchToRegister}
                            className="btn-primary text-lg px-8 py-4 inline-flex items-center gap-3 pulse-glow transform hover:scale-105 transition-all duration-300"
                        >
                            Purchase Shares on Telegram <ArrowRightIcon />
                        </button>

                        <div className="glass-card-strong p-6 rounded-2xl">
                            <div className="flex items-center justify-center gap-6 text-sm">
                                <div className="flex items-center gap-2">
                                    <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                                    <span className="text-white font-semibold">Secure</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse" style={{animationDelay: '0.5s'}}></div>
                                    <span className="text-white font-semibold">Instant</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <div className="w-3 h-3 bg-amber-400 rounded-full animate-pulse" style={{animationDelay: '1s'}}></div>
                                    <span className="text-white font-semibold">Verified</span>
                                </div>
                            </div>
                            <div className="text-gray-400 text-sm mt-2">NFT-backed ownership certificates</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
);

const AboutAureusSection: React.FC = () => {
    const operationalData = [
        { metric: "6,000+ hectares", label: "Target Land Area", status: "By 2030" },
        { metric: "300+ plants", label: "Target Facilities", status: "Eco-Friendly" },
        { metric: "108+ tons/year", label: "Target Gold Output", status: "Phase 19" }
    ];

    const expansionMilestones = [
        { year: "2026", target: "10 Plants", area: "250 ha", status: "Phase 1" },
        { year: "2027", target: "25 Plants", area: "625 ha", status: "Phase 2-6" },
        { year: "2028", target: "50 Plants", area: "1,250 ha", status: "Phase 7-11" },
        { year: "2029", target: "100 Plants", area: "2,500 ha", status: "Phase 12-16" },
        { year: "2030", target: "200 Plants", area: "6,000 ha", status: "Phase 17-20" }
    ];

    return (
        <section id="about" className="relative py-16 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-black to-gray-900"></div>

            <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                {/* Executive Summary */}
                <div className="executive-summary">
                    <div className="summary-header">
                        <h2 className="section-title">Sustainable, Eco-Friendly Gold Mining</h2>
                        <p className="section-subtitle">
                            By January 2026, Aureus will launch two state-of-the-art gold wash plants, fully powered by renewable energy
                            to ensure clean and sustainable mining. The land will be rehabilitated and restored to its natural, viable
                            state after mining, with all production reports openly shared with shareholders.
                        </p>
                    </div>

                    {/* Current Operations */}
                    <div className="operations-grid">
                        <div className="operation-card">
                            <h3 className="card-title">Operational Performance</h3>
                            <div className="operation-metrics">
                                {operationalData.map((item, index) => (
                                    <div key={index} className="operation-metric">
                                        <div className="metric-value">{item.metric}</div>
                                        <div className="metric-label">{item.label}</div>
                                        <div className="metric-status">{item.status}</div>
                                    </div>
                                ))}
                            </div>
                            <div className="operation-details">
                                <p className="detail-text">
                                    Our Kadoma operations utilize modern processing equipment including jaw crushers,
                                    ball mills, and gravity separation systems. Projected recovery rates average 70%
                                    with ongoing optimization programs targeting 95% efficiency.
                                </p>
                            </div>
                        </div>

                        <div className="operation-card">
                            <h3 className="card-title">5-Year Expansion Plan</h3>
                            <div className="expansion-timeline">
                                {expansionMilestones.map((milestone, index) => (
                                    <div key={index} className="timeline-item">
                                        <div className="timeline-year">{milestone.year}</div>
                                        <div className="timeline-content">
                                            <div className="timeline-target">{milestone.target}</div>
                                            <div className="timeline-area">{milestone.area}</div>
                                            <div className="timeline-status">{milestone.status}</div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Investment Highlights */}
                <div className="investment-highlights">
                    <div className="highlights-header">
                        <h3 className="highlights-title">Investment Highlights</h3>
                        <p className="highlights-subtitle">Key factors that differentiate Aureus Alliance Holdings</p>
                    </div>

                    <div className="highlights-grid">
                        <div className="highlight-card">
                            <div className="highlight-icon">🌱</div>
                            <h4 className="highlight-title">Eco-Conscious Operations</h4>
                            <p className="highlight-description">
                                State-of-the-art gold wash plants fully powered by renewable energy, with land
                                rehabilitation and restoration to natural state after mining operations.
                            </p>
                        </div>

                        <div className="highlight-card">
                            <div className="highlight-icon">💎</div>
                            <h4 className="highlight-title">NFT Share Certificates</h4>
                            <p className="highlight-description">
                                After all 1.4 million shares are sold, NFT share certificates will be issued providing
                                secure, blockchain-backed proof of ownership, tradeable from $1,000 on the official marketplace.
                            </p>
                        </div>

                        <div className="highlight-card">
                            <div className="highlight-icon">💰</div>
                            <h4 className="highlight-title">Projected Dividends</h4>
                            <p className="highlight-description">
                                By June 2026, projected annual gold output of 3.5 tons at $107,000 per kilogram,
                                with shareholder dividends projected to reach approximately $145 per share annually.
                            </p>
                        </div>

                        <div className="highlight-card">
                            <div className="highlight-icon">🌍</div>
                            <h4 className="highlight-title">Community Impact</h4>
                            <p className="highlight-description">
                                Building sustainable villages with healthcare, education, clean water, and renewable energy.
                                Each NFT share certificate directly supports community development projects.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};

const DonutChart: React.FC<{ data: { name: string; value: number; color: string }[] }> = ({ data }) => {
    const [hoveredSlice, setHoveredSlice] = useState<string | null>(null);
    const size = 200;
    const strokeWidth = 25;
    const radius = (size - strokeWidth) / 2;
    const circumference = 2 * Math.PI * radius;
    let accumulatedPercentage = 0;

    return (
        <div className="flex flex-col md:flex-row items-center gap-8">
            <div className="relative" style={{ width: size, height: size }}>
                <svg width={size} height={size} viewBox={`0 0 ${size} ${size}`} className="-rotate-90">
                    {data.map((slice, index) => {
                        const percentage = Math.max(0, Math.min(100, slice.value || 0));
                        const dashLength = Math.max(0, percentage / 100 * circumference);
                        const strokeDashoffset = Math.max(0, circumference - (accumulatedPercentage / 100 * circumference));
                        const strokeDasharray = `${dashLength} ${circumference}`;
                        accumulatedPercentage += percentage;

                        // Validate SVG attributes to prevent errors
                        if (!isFinite(dashLength) || !isFinite(strokeDashoffset) || dashLength < 0 ||
                            isNaN(dashLength) || isNaN(strokeDashoffset) ||
                            strokeDasharray.includes('NaN') || strokeDasharray.includes('Infinity')) {
                            console.warn('Invalid SVG path data detected, skipping slice:', { dashLength, strokeDashoffset, strokeDasharray });
                            return null;
                        }

                        return (
                            <circle
                                key={index}
                                cx={size / 2}
                                cy={size / 2}
                                r={radius}
                                fill="transparent"
                                stroke={slice.color}
                                strokeWidth={strokeWidth}
                                strokeDasharray={strokeDasharray}
                                strokeDashoffset={strokeDashoffset}
                                className={`transition-all duration-300 ${hoveredSlice === slice.name ? 'opacity-100' : 'opacity-70'}`}
                                onMouseEnter={() => setHoveredSlice(slice.name)}
                                onMouseLeave={() => setHoveredSlice(null)}
                            />
                        );
                    }).filter(Boolean)}
                </svg>
                 <div className="absolute inset-0 flex items-center justify-center text-center">
                    <div>
                        <p className="text-3xl font-bold gold-gradient-text">{hoveredSlice ? `${data.find(d=>d.name === hoveredSlice)?.value}%` : '100%'}</p>
                        <p className="text-sm text-gray-400 uppercase tracking-wider">{hoveredSlice || 'Total'}</p>
                    </div>
                </div>
            </div>
            <ul className="space-y-2">
                {data.map((slice) => (
                    <li key={slice.name} className="flex items-center gap-3 text-sm" onMouseEnter={() => setHoveredSlice(slice.name)} onMouseLeave={() => setHoveredSlice(null)}>
                        <div className="w-3 h-3 rounded-sm" style={{ backgroundColor: slice.color }}></div>
                        <span className={`transition-colors ${hoveredSlice === slice.name ? 'text-white font-bold' : 'text-gray-400'}`}>
                            {slice.name}: <span className="font-semibold">{slice.value}%</span>
                        </span>
                    </li>
                ))}
            </ul>
        </div>
    );
};

const PresaleOpportunitySection: React.FC = () => {
    return (
        <section className="section bg-gradient-to-br from-amber-900 via-yellow-800 to-amber-900">
            <div className="container">
                <div className="text-center mb-lg">
                    <h2 className="heading-xl text-white mb-xs">Aureus.Africa Presale Opportunity</h2>
                    <div className="w-24 h-1 bg-white mx-auto mb-md"></div>
                    <p className="text-xl text-amber-100 max-w-3xl mx-auto">
                        Powered by S.U.N., Aureus.Africa offers an exclusive 200,000 presale shares at just $5 each.
                    </p>
                </div>

                <div className="grid md:grid-cols-3 gap-lg mb-lg">
                    <div className="bg-white/10 backdrop-blur-sm rounded-lg p-lg text-center">
                        <div className="text-4xl font-bold text-white mb-sm">$5</div>
                        <div className="text-amber-100 mb-sm">Presale Price</div>
                        <div className="text-sm text-amber-200">Lowest Price • Highest Volume</div>
                    </div>
                    <div className="bg-white/10 backdrop-blur-sm rounded-lg p-lg text-center">
                        <div className="text-4xl font-bold text-white mb-sm">200K</div>
                        <div className="text-amber-100 mb-sm">Presale Shares</div>
                        <div className="text-sm text-amber-200">Exclusive early-entry opportunity</div>
                    </div>
                    <div className="bg-white/10 backdrop-blur-sm rounded-lg p-lg text-center">
                        <div className="text-4xl font-bold text-white mb-sm">$10-$1K</div>
                        <div className="text-amber-100 mb-sm">Future Phases</div>
                        <div className="text-sm text-amber-200">Prices increase across 19 phases</div>
                    </div>
                </div>

                <div className="text-center">
                    <p className="text-lg text-amber-100 mb-md">
                        The $5 Presale — Your First & Best Entry Point
                    </p>
                    <button
                        onClick={() => window.location.href = '#expansion-plan'}
                        className="btn-primary bg-white text-amber-900 hover:bg-amber-50"
                    >
                        View Full Phase Plan
                    </button>
                </div>
            </div>
        </section>
    );
};

const KeyHighlightsSection: React.FC = () => {
    const competitiveAdvantages = [
        "Eco-friendly gold wash plants powered by renewable energy",
        "Land rehabilitation and restoration to natural state",
        "Blockchain-backed NFT share certificates for secure ownership",
        "Community-focused development with healthcare and education",
    ];
    const operationalTransparency = [
        { icon: <VideoCameraIcon className="w-6 h-6 text-amber-500"/>, text: "Total transparency in production reports" },
        { icon: <DocumentIcon className="w-6 h-6 text-amber-500"/>, text: "Real dividends from actual gold output" },
        { icon: <UsersIcon className="w-6 h-6 text-amber-500"/>, text: "Community empowerment programs" },
    ];
    const revenueAllocationData = [
        { name: 'Mining operations & infrastructure', value: 45, color: '#D9A44D' },
        { name: 'Equipment & technology upgrades', value: 25, color: '#A97E33' },
        { name: 'Expansion & new site development', value: 20, color: '#6F521B' },
        { name: 'Community development programs', value: 10, color: '#523C0F' },
    ];

    return (
        <section id="highlights" className="relative py-24 lg:py-32 overflow-hidden">
            {/* Enhanced Background */}
            <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-black to-gray-900"></div>
            <div className="absolute inset-0 cyber-grid opacity-30"></div>

            {/* Floating Background Elements */}
            <div className="absolute top-1/3 -left-40 w-80 h-80 bg-amber-500/5 rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute bottom-1/3 -right-40 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>

            <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                {/* Professional Section Header - Matching Gold Diggers Club Style */}
                <div className="competition-hero">
                    <div className="hero-content">
                        <h1 className="hero-title">
                            ⭐ <span className="gold-text">Key Highlights</span>
                        </h1>
                        <p className="hero-subtitle">
                            Competitive Advantages and Operational Excellence
                        </p>
                        <div className="hero-description">
                            <p>🏆 <strong>Regulatory Compliance</strong> - Full DMRE approval with verified mining rights</p>
                            <p>💎 <strong>Operational Excellence</strong> - 12 months of continuous gold production</p>
                            <p>🔗 <strong>Digital Innovation</strong> - Blockchain-verified ownership certificates</p>
                        </div>
                    </div>
                </div>

                {/* Professional Dashboard Content */}
                <div className="professional-dashboard">
                    <div className="metrics-grid">
                        {/* Competitive Advantages */}
                        <div className="metric-card prize-pool">
                            <div className="metric-icon">🏆</div>
                            <div className="metric-content">
                                <div className="metric-label">COMPETITIVE ADVANTAGES</div>
                                <div className="metric-description">
                                    <div className="space-y-2">
                                        {competitiveAdvantages.slice(0, 4).map(point => (
                                            <div key={point} className="flex items-start gap-2 text-sm">
                                                <CheckIcon className="text-green-400 flex-shrink-0 w-4 h-4 mt-0.5" />
                                                <span>{point}</span>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Operational Transparency */}
                        <div className="metric-card participants">
                            <div className="metric-icon">🔍</div>
                            <div className="metric-content">
                                <div className="metric-label">OPERATIONAL TRANSPARENCY</div>
                                <div className="metric-description">
                                    <div className="space-y-2">
                                        {operationalTransparency.map(point => (
                                            <div key={point.text} className="flex items-center gap-2 text-sm">
                                                {point.icon}
                                                <span>{point.text}</span>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Shareholder Benefits */}
                        <div className="metric-card share-price">
                            <div className="metric-icon">💎</div>
                            <div className="metric-content">
                                <div className="metric-label">SHAREHOLDER BENEFITS</div>
                                <div className="metric-description">
                                    <div className="space-y-2">
                                        <div className="flex items-start gap-2 text-sm">
                                            <CheckIcon className="text-green-400 flex-shrink-0 w-4 h-4 mt-0.5" />
                                            <span>Proportional profit distribution</span>
                                        </div>
                                        <div className="flex items-start gap-2 text-sm">
                                            <CheckIcon className="text-green-400 flex-shrink-0 w-4 h-4 mt-0.5" />
                                            <span>Digital certificate ownership</span>
                                        </div>
                                        <div className="flex items-start gap-2 text-sm">
                                            <CheckIcon className="text-green-400 flex-shrink-0 w-4 h-4 mt-0.5" />
                                            <span>Quarterly financial reporting</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
};

const HumanitarianProjectsContent: React.FC = () => {
    const humanitarianImageUrl = 'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/sustainable-villages.jpg';

    const villagePoints = [
      "A self-sustaining, solar-powered smart village",
      "Focus: housing, education, nutrition, farming, clean water",
      "Located near Kadoma gold operations in Zimbabwe",
      "Naming rights formalized via Aureus Alliance Holdings",
    ];

    const clinicPoints = [
      "A series of mobile and modular health clinics",
      "Focus: maternal health, vaccination, rural care",
      "First clinics to open in Kadoma Zimbabwe",
      "Powered by share sales, named via Aureus Alliance Holdings",
    ];

    const TickIcon: React.FC<{className?: string}> = ({className}) => (<svg className={className} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5 13l4 4L19 7" stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/></svg>);

    const ListPoint: React.FC<{children: React.ReactNode}> = ({children}) => (
        <li className="flex items-start gap-3">
            <div className="mt-1.5 w-4 h-4 flex-shrink-0 rounded-full bg-white/80 flex items-center justify-center">
                <TickIcon className="w-2.5 h-2.5 text-amber-500" />
            </div>
            <span className="text-white">{children}</span>
        </li>
    );

    return (
      <div className="mt-12">
        <div className="grid lg:grid-cols-2 gap-8 xl:gap-16">
          <div className="space-y-6 flex flex-col">
              <header>
                  <h2 className="text-4xl md:text-5xl font-black uppercase tracking-wider text-white">Sustainable Villages</h2>
                  <h3 className="text-4xl md:text-5xl font-black uppercase -mt-2 gold-text">Health Trust Clinics</h3>
              </header>
              <img src={humanitarianImageUrl} alt="Aureus Alliance Holdings Sustainable Villages and Health Trust Clinics - Community Development Projects in Zimbabwe" className="rounded-lg shadow-2xl w-full" />
          </div>
          <div className="flex flex-col justify-start pt-4 text-white">
              <div className="space-y-8">
                  <p className="text-lg font-bold uppercase tracking-wide">
                  </p>
                  <div className="space-y-4">
                      <h4 className="text-2xl font-bold gold-text">SUSTAINABLE VILLAGE:</h4>
                      <ul className="space-y-2.5">
                          {villagePoints.map(point => <ListPoint key={point}>{point}</ListPoint>)}
                      </ul>
                  </div>
                  <div className="space-y-4">
                      <h4 className="text-2xl font-bold gold-text">HEALTH TRUST CLINICS:</h4>
                      <ul className="space-y-2.5">
                          {clinicPoints.map(point => <ListPoint key={point}>{point}</ListPoint>)}
                      </ul>
                  </div>
              </div>
          </div>
        </div>
        <footer className="text-center mt-16 text-white">
          <p className="text-2xl font-black uppercase tracking-wide max-w-5xl mx-auto">
              These projects transform ROI into visible humanitarian monuments — immortalizing leadership through community revival.</p>
        </footer>
      </div>
    );
};

const CharitySection: React.FC = () => (
    <section id="charity" className="section">
        <div className="container">
            <div className="text-center mb-md">
                <h2 className="heading-xl text-gold mb-xs">More Than Gold</h2>
                <p className="text-lg max-w-2xl mx-auto">
                    Aureus goes beyond mining — we're building smart, sustainable villages that transform lives and uplift communities.
                    Each NFT share certificate directly supports healthcare, schools, and sustainable development projects across Africa.
                </p>
            </div>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-lg">
                <div className="text-center">
                    <div className="text-4xl mb-sm">🏥</div>
                    <h3 className="text-xl font-semibold text-gold mb-sm">Healthcare</h3>
                    <p className="text-gray-300">Regional hospital, mobile health units, and maternal care fleets</p>
                </div>
                <div className="text-center">
                    <div className="text-4xl mb-sm">🎓</div>
                    <h3 className="text-xl font-semibold text-gold mb-sm">Education</h3>
                    <p className="text-gray-300">Solar-powered schools and 30,000+ full scholarships</p>
                </div>
                <div className="text-center">
                    <div className="text-4xl mb-sm">💧</div>
                    <h3 className="text-xl font-semibold text-gold mb-sm">Clean Water</h3>
                    <p className="text-gray-300">200+ boreholes and 100+ water purification systems</p>
                </div>
                <div className="text-center">
                    <div className="text-4xl mb-sm">🍽️</div>
                    <h3 className="text-xl font-semibold text-gold mb-sm">Nutrition</h3>
                    <p className="text-gray-300">Feeding 1,000,000+ children through nutrition programs</p>
                </div>
            </div>
        </div>
    </section>
);

const DecadeOfGoldSection: React.FC = () => (
    <section className="section bg-gradient-to-br from-gray-900 via-black to-gray-900">
        <div className="container">
            <div className="text-center mb-lg">
                <h2 className="heading-xl text-gold mb-xs">The Decade of Gold</h2>
                <div className="w-24 h-1 bg-gold mx-auto mb-md"></div>
                <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                    By 2030, Aureus will control over 5,000 hectares of gold-rich land, driving an ambitious expansion plan
                    with 10 eco-friendly wash plants launched annually.
                </p>
            </div>

            <div className="grid md:grid-cols-3 gap-lg mb-lg">
                <div className="bg-gradient-to-br from-amber-900/20 to-yellow-900/20 backdrop-blur-sm rounded-lg p-lg text-center border border-amber-500/20">
                    <div className="text-5xl font-bold text-gold mb-sm">5,000</div>
                    <div className="text-amber-200 mb-sm">Hectares</div>
                    <div className="text-sm text-gray-400">Gold-rich land under management</div>
                </div>
                <div className="bg-gradient-to-br from-amber-900/20 to-yellow-900/20 backdrop-blur-sm rounded-lg p-lg text-center border border-amber-500/20">
                    <div className="text-5xl font-bold text-gold mb-sm">10</div>
                    <div className="text-amber-200 mb-sm">Plants/Year</div>
                    <div className="text-sm text-gray-400">New eco-friendly wash plants annually</div>
                </div>
                <div className="bg-gradient-to-br from-amber-900/20 to-yellow-900/20 backdrop-blur-sm rounded-lg p-lg text-center border border-amber-500/20">
                    <div className="text-5xl font-bold text-gold mb-sm">80</div>
                    <div className="text-amber-200 mb-sm">Tons/Year</div>
                    <div className="text-sm text-gray-400">Projected annual gold output by 2030</div>
                </div>
            </div>

            <div className="text-center">
                <p className="text-lg text-amber-200 mb-md">
                    Sustainable Growth. Unlimited Potential.
                </p>
                <p className="text-gray-400 max-w-2xl mx-auto">
                    Every plant is designed for eco-friendly operations and long-term profitability,
                    combining responsible mining with environmental stewardship.
                </p>
            </div>
        </div>
    </section>
);




const ProjectSection: React.FC = () => {
    const points = [ "A 2020 geotechnical report confirms the site's viability.", "Managed in-house with an experienced African operator.", "High-efficiency plant from a firm with 32+ years of expertise.", "Secure sales process directly to Fidelity Gold Refinery." ];
    return (
        <section id="project" className="section">
            <div className="container">
                <div className="text-center mb-md">
                    <h2 className="heading-xl text-gold mb-xs">The Land & Project Status</h2>
                    <p className="text-lg max-w-2xl mx-auto">A secured 250-hectare mining block confirmed for viability and primed for scalable, high-yield gold mining.</p>
                </div>

                <div className="grid grid-2 gap-md items-center">
                    <div className="card">
                        <h3 className="heading-md text-gold mb-sm">Strategic Viability</h3>
                        <p className="text-sm mb-sm">The licensed area is in Kadoma, a region renowned for rich gold placer deposits. Its connectivity ensures smooth logistics and uninterrupted operations.</p>
                        <ul className="space-y-xs">
                            {points.map((point, i) => (
                                <li key={i} className="flex items-start gap-xs text-sm">
                                    <CheckIcon className="text-amber-500 mt-0.5 flex-shrink-0 w-4 h-4" />
                                    <span>{point}</span>
                                </li>
                            ))}
                        </ul>
                    </div>
                    <div>
                        <img src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/land-prospect.png" alt="Aureus Alliance Holdings 250-Hectare Gold Mining Concession - Satellite View of Licensed Mining Area in Kadoma, Zimbabwe" className="rounded w-full"/>
                    </div>
                </div>
            </div>
        </section>
    );
};
const FinancialsSection: React.FC = () => {
    const [selectedGoldPrice, setSelectedGoldPrice] = useState(100);

    const tableData = [
        { site: 'Existing 250-hectare block', kg: 2400, val100: 240000000, val150: 360000000 },
        { site: 'Mutare Expansion (47 sites)', kg: 7600, val100: 760000000, val150: ********** },
    ];
    const totals = {
        kg: tableData.reduce((acc, row) => acc + row.kg, 0),
        val100: tableData.reduce((acc, row) => acc + row.val100, 0),
        val150: tableData.reduce((acc, row) => acc + row.val150, 0),
    };
    const headerClass = "p-4 text-gray-400 text-sm sm:text-base";
    const cellClass = "p-4 text-white text-sm sm:text-base";

    // Site Values Tab Component
    const SiteValuesTabs: React.FC = () => {
        const goldPrices = [
            { price: 100, label: '$100k/kg' },
            { price: 110, label: '$110k/kg' },
            { price: 120, label: '$120k/kg' },
            { price: 130, label: '$130k/kg' },
            { price: 140, label: '$140k/kg' },
            { price: 150, label: '$150k/kg' }
        ];

        const getCurrentData = () => {
            return tableData.map(row => ({
                ...row,
                currentValue: (row.kg * selectedGoldPrice * 1000) // kg * price per kg * 1000 (since price is in k)
            }));
        };

        const getCurrentTotal = () => {
            return totals.kg * selectedGoldPrice * 1000; // Total kg * price per kg * 1000
        };

        return (
            <div className="projection-tabs-container">
                {/* Tab Navigation */}
                <div className="tab-navigation">
                    <div className="tab-scroll-container">
                        {goldPrices.map(priceData => (
                            <button
                                key={priceData.price}
                                onClick={() => setSelectedGoldPrice(priceData.price)}
                                className={`tab-button ${selectedGoldPrice === priceData.price ? 'tab-active' : 'tab-inactive'}`}
                            >
                                {priceData.label}
                            </button>
                        ))}
                    </div>
                </div>

                {/* Tab Content */}
                <div className="tab-content">
                    <div className="site-values-grid">
                        {getCurrentData().map(row => (
                            <div key={row.site} className="site-value-card">
                                <div className="site-header">
                                    <h4 className="site-title">{row.site}</h4>
                                    <div className="site-badge">{formatNumber(row.kg)} KG</div>
                                </div>
                                <div className="site-value">
                                    <span className="value-label">Site Value</span>
                                    <span className="value-amount">
                                        {formatNumber(row.currentValue, {style: 'currency', currency: 'USD', minimumFractionDigits: 0})}
                                    </span>
                                </div>
                            </div>
                        ))}
                    </div>

                    {/* Portfolio Summary */}
                    <div className="portfolio-summary">
                        <div className="summary-header">
                            <h4 className="summary-title">Portfolio Summary</h4>
                            <div className="summary-subtitle">Total across all mining sites</div>
                        </div>
                        <div className="summary-metrics">
                            <div className="summary-metric">
                                <span className="metric-value">{formatNumber(totals.kg)}</span>
                                <span className="metric-label">Total KG</span>
                            </div>
                            <div className="summary-divider"></div>
                            <div className="summary-metric primary">
                                <span className="metric-value">
                                    {formatNumber(getCurrentTotal(), {style: 'currency', currency: 'USD', minimumFractionDigits: 0})}
                                </span>
                                <span className="metric-label">Total Portfolio Value</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    return (
        <section id="financials" className="section">
            <div className="container">
                <div className="text-center mb-md">
                    <h2 className="heading-xl text-gold mb-xs">Financials & Share Value</h2>
                    <p className="text-lg max-w-2xl mx-auto">Conservative estimates based on geological data, providing a baseline for forecasting and share purchasing decisions.</p>
                </div>

                <div className="resource-estimates-modern">
                    <div className="resource-header">
                        <h3 className="resource-title">Resource Estimates (In Situ)</h3>
                        <div className="resource-subtitle">Conservative geological data baseline</div>
                    </div>

                    <div className="resource-grid">
                        <div className="resource-card">
                            <div className="resource-icon">📏</div>
                            <div className="resource-content">
                                <div className="resource-label">Area Extent</div>
                                <div className="resource-value">250 ha</div>
                            </div>
                        </div>

                        <div className="resource-card">
                            <div className="resource-icon">📐</div>
                            <div className="resource-content">
                                <div className="resource-label">Gravel Thickness</div>
                                <div className="resource-value">0.8 m</div>
                            </div>
                        </div>

                        <div className="resource-card">
                            <div className="resource-icon">📦</div>
                            <div className="resource-content">
                                <div className="resource-label">Volume (m³)</div>
                                <div className="resource-value">2,400,000</div>
                            </div>
                        </div>

                        <div className="resource-card">
                            <div className="resource-icon">⚖️</div>
                            <div className="resource-content">
                                <div className="resource-label">Grade (g/m³)</div>
                                <div className="resource-value">0.9</div>
                            </div>
                        </div>

                        <div className="resource-card highlight">
                            <div className="resource-icon">🏆</div>
                            <div className="resource-content">
                                <div className="resource-label">Potential Gold Output</div>
                                <div className="resource-value gold">2,160 kg</div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Projected Site Values with Tab System */}
                <div className="projection-section-modern">
                    <div className="projection-header">
                        <h3 className="text-xl font-bold text-gold mb-2">Projected Site Values (Pre-Expense)</h3>
                        <p className="text-sm text-gray-400 mb-6">Based on figures from the prospectus - select gold price per kg</p>
                    </div>

                    <SiteValuesTabs />
                </div>
            </div>
        </section>
    );
};

// Affiliate competition content removed from public homepage

const GallerySection: React.FC = () => {
    return (
        <section id="gallery" className="section">
            <div className="container">
                <div className="text-center mb-md">
                    <h2 className="heading-xl text-gold mb-xs">Proof of Concept & Gallery</h2>
                    <p className="text-lg max-w-2xl mx-auto">A visual journey through our on-site activities, team collaborations, and project milestones.</p>
                </div>
                <div className="gallery-container">
                    <EnhancedGallery
                        showCategories={true}
                        showSearch={false}
                        itemsPerPage={9}
                        showFeaturedFirst={true}
                        fallbackMode="static"
                        showConnectionStatus={false}
                        layout="slideshow"
                        height="450px"
                        compact={true}
                    />
                </div>
            </div>
        </section>
    );
};


const Footer: React.FC<{setCurrentSection: (section: string) => void, user?: any, handleSwitchToRegister?: () => void}> = ({setCurrentSection, user, handleSwitchToRegister}) => {
    // Demo mode: DISABLED - All users have full access
    const isDemoUser = () => {
        return true; // Always return true to give all users full access
    }

    // Demo mode purchase button handler - DISABLED (all users can purchase)
    const handlePurchaseClick = (e: React.MouseEvent) => {
        // Demo mode disabled - allow all users to access purchase functionality
        return true
    }

    return (
    <footer className="footer-container">
        <div className="container">
            <div className="footer-content">
                <div className="footer-logo">AUREUS</div>

                <nav className="footer-nav">
                    <NavLink href="#about">About</NavLink>
                    <NavLink href="#highlights">Highlights</NavLink>
                    <NavLink href="#expansion-plan">Share Phases</NavLink>
                    <NavLink href="#calculator">Calculator</NavLink>
                    <NavLink href="#project">Project</NavLink>
                    <NavLink href="#gallery">Gallery</NavLink>
                    <NavLink href="#charity">Charity</NavLink>
                    <button
                        onClick={() => {
                            setCurrentSection('affiliate');
                            window.history.pushState({}, '', '/affiliate');
                        }}
                        className="text-yellow-400 hover:text-yellow-300 underline bg-transparent border-none cursor-pointer"
                    >
                        Apply to Become an Aureus Affiliate
                    </button>
                </nav>

                <div className="footer-cta">
                    <button
                        onClick={handleSwitchToRegister}
                        className="btn btn-primary"
                    >
                        Secure Your Shares
                    </button>
                </div>

                <div className="footer-legal">
                    <div className="footer-legal-nav">
                        <a href="#privacy-policy" onClick={(e) => { e.preventDefault(); setCurrentSection('privacy-policy'); }}>Privacy Policy</a>
                        <a href="#terms-conditions" onClick={(e) => { e.preventDefault(); setCurrentSection('terms-conditions'); }}>Terms & Conditions</a>
                        <a href="#disclaimer" onClick={(e) => { e.preventDefault(); setCurrentSection('disclaimer'); }}>Disclaimer</a>
                        <a href="#affiliate" onClick={(e) => { e.preventDefault(); setCurrentSection('affiliate'); }}>Become an Affiliate Member</a>
                    </div>
                    <p>&copy; {new Date().getFullYear()} Aureus Alliance Holdings. All rights reserved.</p>
                    <p>
                        Disclaimer: This website and the information contained herein are for informational purposes only and do not constitute an offer to sell or a solicitation of an offer to purchase any shares.
                    </p>
                </div>
            </div>
        </div>
    </footer>
    );
};

const App: React.FC = () => {
    const [showAdmin, setShowAdmin] = useState(false);
    const [currentSection, setCurrentSection] = useState<string>('home');
    const [user, setUser] = useState<any>(null);
    const [authMode, setAuthMode] = useState<'login' | 'register'>('login');
    const [isCheckingSession, setIsCheckingSession] = useState(true);
    const [previousSection, setPreviousSection] = useState<string>('home'); // Track where user came from
    const [activeDashboard, setActiveDashboard] = useState<'shareholder' | 'affiliate'>('shareholder');
    const [affiliateUsername, setAffiliateUsername] = useState<string>('');

    // Handle direct URL access to login/register pages - both go to unified account access page
    useEffect(() => {
        const path = window.location.pathname;
        const urlParams = new URLSearchParams(window.location.search);
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        console.log('🔄 URL routing useEffect triggered', { path, currentSection, isMobile });

        // Initialize referral tracking for all page loads
        initializeReferralTracking();

        if (path === '/login' || path === '/register') {
            console.log('🔐 Processing login/register URL', { path, currentSection });
            // Check if we're in the middle of an authentication success redirect
            const authSuccessRedirect = localStorage.getItem('aureus_auth_success_redirect');
            if (authSuccessRedirect === 'true') {
                console.log('🔄 Authentication success redirect in progress, skipping login page setup');
                return;
            }

            // Check if user is already authenticated (mobile fix) - but validate the session first
            const existingUser = localStorage.getItem('aureus_user') || localStorage.getItem('aureus_telegram_user');
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const mobileAuthInProgress = localStorage.getItem('aureus_mobile_auth_redirect') === 'true';

            // Only redirect if we have valid user data AND the user is actually authenticated
            if (existingUser && isMobile && !mobileAuthInProgress) {
                try {
                    const userData = JSON.parse(existingUser);
                    // Validate that the user data has essential fields and isn't stale
                    const hasValidData = userData && (userData.id || userData.telegram_id) && userData.email;
                    const authTimestamp = localStorage.getItem('aureus_auth_timestamp');
                    const isRecentAuth = authTimestamp && (Date.now() - parseInt(authTimestamp)) < 24 * 60 * 60 * 1000; // 24 hours

                    if (hasValidData && isRecentAuth) {
                        console.log('📱 Mobile user has valid recent authentication, redirecting to dashboard');
                        setCurrentSection('dashboard');
                        window.history.pushState({}, '', '/dashboard');
                        return;
                    } else {
                        console.log('📱 Mobile user has stale/invalid authentication data, allowing login page access');
                        // Clear stale data
                        localStorage.removeItem('aureus_user');
                        localStorage.removeItem('aureus_telegram_user');
                        localStorage.removeItem('aureus_auth_timestamp');
                        localStorage.removeItem('aureus_mobile_auth_redirect'); // Clear this flag too
                    }
                } catch (error) {
                    console.log('📱 Invalid user data in localStorage, clearing and allowing login');
                    localStorage.removeItem('aureus_user');
                    localStorage.removeItem('aureus_telegram_user');
                    localStorage.removeItem('aureus_mobile_auth_redirect'); // Clear this flag too
                }
            }

            // REMOVED: The problematic early return that was blocking login page access
            // Mobile auth redirect flag should not prevent login page access

            console.log('🔐 Setting currentSection to account-access from useEffect');
            setCurrentSection('account-access');
            // Set authMode based on the original URL for context
            setAuthMode(path === '/register' ? 'register' : 'login');
        } else if (path === '/purchase-shares') {
            // Check if user is authenticated for purchase shares page
            getCurrentUser().then(currentUser => {
                if (currentUser) {
                    console.log('✅ User authenticated, showing purchase shares page');
                    setCurrentSection('purchase-shares');
                } else {
                    console.log('🔐 User not authenticated, redirecting to login with purchase intent');
                    localStorage.setItem('aureus_post_login_redirect', '/purchase-shares');
                    setCurrentSection('account-access');
                    setAuthMode('login');
                    window.history.pushState({}, '', '/login');
                }
            }).catch(error => {
                console.error('❌ Error checking user authentication:', error);
                // Fallback to login page
                localStorage.setItem('aureus_post_login_redirect', '/purchase-shares');
                setCurrentSection('account-access');
                setAuthMode('login');
                window.history.pushState({}, '', '/login');
            });
        } else if (path === '/reset-password') {
            // Redirect to login page - password reset is now handled within UnifiedAuthPage
            setCurrentSection('account-access');
            setAuthMode('login');
            window.history.pushState({}, '', '/login');
        } else if (path === '/sync') {
            // Handle sync page with token
            const syncToken = urlParams.get('token');
            if (syncToken) {
                setCurrentSection('sync');
                // Store sync token for the sync page
                (window as any).syncToken = syncToken;
            } else {
                // No token, redirect to home
                setCurrentSection('home');
                window.history.pushState({}, '', '/');
            }
        } else if (path === '/affiliate') {
            setCurrentSection('affiliate');
            // Track registration path for user type detection
            localStorage.setItem('aureus_registration_path', '/affiliate');
        } else if (path === '/dashboard') {
            // Handle dashboard route - check if user is logged in
            console.log('🏠 Dashboard route detected');
            // This will be handled by the session check logic

        } else if (path === '/migrate-from-telegram' || path === '/migrate' || path.includes('/auth/telegram')) {
            // Handle migration pages
            console.log('🔄 Migration page detected:', path);
            setCurrentSection('migration');
        } else if (path !== '/') {
            // Check if this is a username-based affiliate landing page
            const username = path.substring(1); // Remove leading slash
            console.log('🔍 Checking path for affiliate landing:', { path, username, hasSlash: username.includes('/'), length: username.length, startsWithStatic: username.startsWith('static/') });
            // Allow usernames with dots but not slashes (to avoid matching other routes)
            // Exclude reserved paths like 'dashboard', 'admin', etc.
            const reservedPaths = [
                'dashboard', 'admin', 'api', 'static', '_next', 'favicon.ico',
                'complete-profile', 'login', 'register', 'purchase-shares', 'affiliate',
                'migrate-from-telegram', 'migrate', 'sync', 'reset-password',
                'privacy-policy', 'terms-conditions', 'aureus.js'
            ];
            if (username && !username.includes('/') && username.length > 0 && !username.startsWith('static/') && !reservedPaths.includes(username)) {
                console.log('🎯 Potential affiliate landing page for username:', username);
                setCurrentSection('affiliate-landing');
                setAffiliateUsername(username);
            } else {
                console.log('❌ Path did not match affiliate landing criteria or is reserved path');
            }
        }
    }, []);
    // Global error handler for SVG and other errors
    useEffect(() => {
        const handleGlobalError = (event: ErrorEvent) => {
            // Suppress jQuery SVG path errors that don't affect our app
            if (event.message && event.message.includes('Expected number') && event.message.includes('path')) {
                console.warn('Suppressed SVG path error (likely from browser extension):', event.message);
                event.preventDefault();
                return false;
            }
            // Log other errors normally
            console.error('Global error:', event.error);
        };

        const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
            console.error('Unhandled promise rejection:', event.reason);
        };

        window.addEventListener('error', handleGlobalError);
        window.addEventListener('unhandledrejection', handleUnhandledRejection);

        return () => {
            window.removeEventListener('error', handleGlobalError);
            window.removeEventListener('unhandledrejection', handleUnhandledRejection);
        };
    }, []);

    // Live gold price integration
    const { price: liveGoldPrice, isLoading: goldPriceLoading, error: goldPriceError, refetch: refetchGoldPrice } = useGoldPrice();

    // Demo mode: DISABLED - All users have full access
    const isDemoUser = () => {
        return true; // Always return true to give all users full access
    }

    // Demo mode purchase button handler - DISABLED (all users can purchase)
    const handlePurchaseClick = (e: React.MouseEvent) => {
        // Demo mode disabled - allow all users to access purchase functionality
        return true
    }



    // Authentication handlers
    const handleLoginSuccess = (userData: any) => {
        console.log('✅ Login successful:', userData);
        console.log('🔍 User data analysis:', {
            needsProfileCompletion: userData.needsProfileCompletion,
            profile_completion_required: userData.user_metadata?.profile_completion_required,
            database_user: userData.database_user,
            email: userData.database_user?.email,
            full_name: userData.database_user?.full_name,
            phone: userData.database_user?.phone,
            country_of_residence: userData.database_user?.country_of_residence,
            telegram_connected: userData.user_metadata?.telegram_connected,
            telegram_id: userData.user_metadata?.telegram_id
        });

        // CRITICAL FIX: Ensure Telegram connection data is preserved in user state
        if (userData.user_metadata?.telegram_connected && userData.user_metadata?.telegram_id) {
            console.log('🔧 Preserving Telegram connection in user state');

            // Ensure localStorage has the complete session data
            const sessionData = JSON.parse(localStorage.getItem('aureus_session') || '{}');
            const userData_stored = JSON.parse(localStorage.getItem('aureus_user') || '{}');

            console.log('📋 Current session data:', sessionData);
            console.log('📋 Current user data:', userData_stored);

            // If session data is incomplete, create it
            if (!sessionData.telegramConnected || !sessionData.telegramId) {
                console.log('⚠️ Session data incomplete, fixing...');
                const completeSessionData = {
                    ...sessionData,
                    userId: userData.database_user?.id,
                    telegramId: userData.user_metadata.telegram_id,
                    telegramUsername: userData.user_metadata.telegram_username,
                    telegramConnected: true,
                    telegramRegistered: userData.user_metadata.telegram_registered,
                    loginMethod: 'telegram',
                    sessionStart: new Date().toISOString(),
                    lastActivity: new Date().toISOString()
                };
                localStorage.setItem('aureus_session', JSON.stringify(completeSessionData));
                console.log('✅ Session data fixed');
            }
        }

        // Set auth timestamp for mobile validation
        localStorage.setItem('aureus_auth_timestamp', Date.now().toString());

        setUser(userData);

        // Use the default dashboard logic to handle routing properly
        handleDefaultDashboardLogic(userData);
    };

    const handleRegistrationSuccess = (userData: any) => {
        console.log('✅ Registration successful:', userData);

        // Extract the actual database user data from the registration response
        const dbUser = userData.database_user || userData;

        console.log('🔍 Registration complete - going directly to dashboard (no profile completion step)');

        // Structure user data properly for dashboard compatibility
        const structuredUser = {
            id: `db_${dbUser.id}`,
            email: dbUser.email,
            username: dbUser.username,
            account_type: 'email',
            user_type: 'shareholder', // Default to shareholder for new registrations
            database_user: {
                id: dbUser.id,
                email: dbUser.email,
                username: dbUser.username,
                full_name: dbUser.full_name || '',
                phone: dbUser.phone || '',
                country_of_residence: dbUser.country_of_residence || 'ZA',
                country_selection_completed: true, // Mark as completed to skip profile completion
                password_hash: dbUser.password_hash,
                is_active: true,
                is_verified: false,
                role: 'user',
                account_type: 'email'
            },
            needsProfileCompletion: false // No profile completion step - go directly to dashboard
        };

        console.log('📝 Structured user data:', structuredUser);

        // Store user data in localStorage for persistence
        localStorage.setItem('aureus_user', JSON.stringify(structuredUser));
        localStorage.setItem('aureus_user_type', 'shareholder');
        localStorage.setItem('aureus_auth_timestamp', Date.now().toString()); // Add auth timestamp

        setUser(structuredUser);

        // Use the default dashboard logic to handle routing properly
        handleDefaultDashboardLogic(structuredUser);
    };

    const handleSwitchToLogin = () => {
        setAuthMode('login');
        setCurrentSection('account-access');
        // Update URL without page reload
        window.history.pushState({}, '', '/login');
    };

    const handleSwitchToRegister = () => {
        setAuthMode('register');
        setCurrentSection('account-access');
        // Update URL without page reload
        window.history.pushState({}, '', '/register');
    };

    // REMOVED: handleProfileComplete - users authenticate directly now

    // Mobile Authentication State Manager - Prevents Race Conditions
    const mobileAuthManager = {
        isRedirecting: false,
        redirectAttempts: 0,
        maxAttempts: 3,

        startRedirect: function(userData: any) {
            if (this.isRedirecting) {
                console.log('📱 Mobile redirect already in progress, ignoring duplicate request');
                return;
            }

            this.isRedirecting = true;
            this.redirectAttempts = 0;

            console.log('📱 Starting mobile authentication redirect sequence');

            // Clear any existing redirect flags to prevent conflicts
            localStorage.removeItem('aureus_auth_success_redirect');
            localStorage.removeItem('aureus_post_login_redirect');

            // Set mobile-specific authentication flag
            localStorage.setItem('aureus_mobile_auth_redirect', 'true');
            localStorage.setItem('aureus_auth_timestamp', Date.now().toString());

            // Store user data
            if (userData.account_type === 'telegram') {
                localStorage.setItem('aureus_telegram_user', JSON.stringify(userData.database_user));
                localStorage.setItem('aureus_user', JSON.stringify(userData.database_user));
            } else {
                localStorage.setItem('aureus_user', JSON.stringify(userData.database_user));
            }
            localStorage.setItem('aureus_user_type', userData.user_type || 'shareholder');

            // Set user state immediately
            setUser(userData);

            // Perform redirect
            this.performRedirect();
        },

        performRedirect: function() {
            this.redirectAttempts++;

            console.log(`📱 Mobile redirect attempt ${this.redirectAttempts}/${this.maxAttempts}`);

            // Use the default dashboard logic to handle profile completion checks
            let userData = {};
            try {
                const storedUser = localStorage.getItem('aureus_user');
                console.log('📱 Raw stored user data:', storedUser);

                if (storedUser && storedUser !== 'undefined' && storedUser !== 'null') {
                    userData = JSON.parse(storedUser);
                } else {
                    console.log('📱 No valid user data found in localStorage, using empty object');
                }
            } catch (error) {
                console.error('📱 Error parsing user data from localStorage:', error);
                console.log('📱 Using empty user data object');
            }

            console.log('📱 Using handleDefaultDashboardLogic for mobile redirect');
            handleDefaultDashboardLogic({ database_user: userData });

            // Verify redirect success after a delay
            setTimeout(() => {
                const currentPath = window.location.pathname;
                const currentSectionValue = currentSection;

                console.log(`📱 Redirect verification - Path: ${currentPath}, Section: ${currentSectionValue}`);

                // Accept dashboard, complete-profile, migration pages, or any valid redirect as success
                const validPaths = ['/dashboard', '/complete-profile', '/migrate-from-telegram', '/migrate'];
                const isValidRedirect = validPaths.some(path => currentPath === path) || currentPath.includes('migrate');

                if (!isValidRedirect && this.redirectAttempts < this.maxAttempts) {
                    console.log('📱 Redirect failed, retrying...');
                    this.performRedirect();
                } else if (isValidRedirect) {
                    console.log('📱 Mobile redirect successful!');
                    this.cleanup();
                } else {
                    console.log('📱 Max redirect attempts reached, forcing final redirect');
                    window.location.href = '/dashboard';
                    this.cleanup();
                }
            }, 1200); // Increased delay for mobile browsers
        },

        cleanup: function() {
            this.isRedirecting = false;
            this.redirectAttempts = 0;
            localStorage.removeItem('aureus_mobile_auth_redirect');
            localStorage.removeItem('aureus_auth_timestamp');
            console.log('📱 Mobile auth manager cleanup complete');
        }
    };

    // Function to refresh user data from database
    const loadUserData = async () => {
        try {
            console.log('🔄 Refreshing user data from database...');
            const currentUser = await getCurrentUser();
            if (currentUser) {
                setUser(currentUser);
                console.log('✅ User data refreshed successfully');
            }
        } catch (error) {
            console.error('❌ Error refreshing user data:', error);
        }
    };

    const handleLogout = async () => {
        console.log('🚪 handleLogout called');
        try {
            console.log('🔐 Calling signOut...');

            // First, clear user state immediately to prevent any UI issues
            setUser(null);
            setCurrentSection('home');
            setActiveDashboard('shareholder'); // Reset to default dashboard

            // Call Supabase signOut
            const signOutResult = await signOut();
            console.log('🔐 SignOut result:', signOutResult);

            // Clear ALL possible localStorage data
            console.log('🧹 Clearing all localStorage data...');
            localStorage.removeItem('aureus_telegram_user');
            localStorage.removeItem('aureus_test_user');
            localStorage.removeItem('aureus-remember-me');
            localStorage.removeItem('aureus_email_user');
            localStorage.removeItem('aureus_user'); // THIS WAS MISSING - email users!
            localStorage.removeItem('aureus_session'); // Clear session data too
            localStorage.removeItem('aureus_active_dashboard'); // Clear dashboard preference
            localStorage.removeItem('supabase.auth.token');

            // Clear all Supabase auth keys that might exist
            Object.keys(localStorage).forEach(key => {
                if (key.startsWith('supabase.auth.')) {
                    localStorage.removeItem(key);
                }
            });

            // Clear any session storage as well
            sessionStorage.clear();

            // Clean up mobile auth manager
            mobileAuthManager.cleanup();

            // Set a flag to prevent auto-login on next session check
            localStorage.setItem('aureus_logout_flag', 'true');

            // Force navigation to home and clear URL
            window.history.pushState({}, '', '/');

            // Force a page reload to ensure complete state reset
            console.log('🔄 Forcing page reload to ensure complete logout...');
            setTimeout(() => {
                window.location.reload();
            }, 100);

            console.log('✅ Logged out successfully - all data cleared');
        } catch (error) {
            console.error('❌ Logout error:', error);
            // Still clear local state even if signOut fails
            console.log('🧹 Force clearing all data despite error...');

            // Clear user state
            setUser(null);
            setCurrentSection('home');

            // Clear all storage
            localStorage.removeItem('aureus_telegram_user');
            localStorage.removeItem('aureus_test_user');
            localStorage.removeItem('aureus-remember-me');
            localStorage.removeItem('aureus_email_user');
            localStorage.removeItem('aureus_user'); // THIS WAS MISSING - email users!
            localStorage.removeItem('aureus_session'); // Clear session data too
            localStorage.removeItem('supabase.auth.token');

            // Clear all Supabase auth keys
            Object.keys(localStorage).forEach(key => {
                if (key.startsWith('supabase.auth.')) {
                    localStorage.removeItem(key);
                }
            });

            sessionStorage.clear();
            localStorage.setItem('aureus_logout_flag', 'true');

            // Force navigation and reload
            window.history.pushState({}, '', '/');
            setTimeout(() => {
                window.location.reload();
            }, 100);

            console.log('✅ Force logout completed');
        }
    };

    // Dashboard switching handler
    const handleDashboardSwitch = (dashboard: 'shareholder' | 'affiliate') => {
        setActiveDashboard(dashboard);
        localStorage.setItem('aureus_active_dashboard', dashboard);
        console.log(`🔄 Switched to ${dashboard} dashboard`);
    };

    // DEBUG: Add function to clear localStorage
    const clearLocalStorage = () => {
        localStorage.removeItem('aureus_telegram_user');
        localStorage.removeItem('aureus_test_user');
        setUser(null);
        setCurrentSection('home');
        console.log('🧹 localStorage cleared');
    };

    // Get content from context with fallbacks
    let getContent: (section: string, key: string, defaultValue?: any) => any;
    try {
        const context = useSiteContentContext();
        getContent = context.getContent;
    } catch (error) {
        console.warn('App: Site content context not available, using defaults');
        getContent = (section: string, key: string, defaultValue: any = '') => defaultValue;
    }

    // Get calculator content with fallbacks
    const calculatorTitle = getContent('calculator', 'title', 'Projected Gold Output & Dividends') || 'Projected Gold Output & Dividends';
    const calculatorSubtitle = getContent('calculator', 'subtitle', 'By June 2026, Aureus is projected to achieve an annual gold output of 3.5 tons. Calculate your potential dividends based on actual gold production.') || 'By June 2026, Aureus is projected to achieve an annual gold output of 3.5 tons. Calculate your potential dividends based on actual gold production.';

    // Handle default dashboard logic
    const handleDefaultDashboardLogic = (currentUser: any) => {
        console.log('🏠 Executing default dashboard logic for user:', currentUser.email);

        // Mobile-specific debugging
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        if (isMobile) {
            console.log('📱 Mobile device - executing dashboard logic');
            console.log('📱 Current user data:', {
                email: currentUser.email,
                country_completed: currentUser.database_user?.country_selection_completed,
                full_name: currentUser.database_user?.full_name,
                phone: currentUser.database_user?.phone,
                needsProfileCompletion: currentUser.needsProfileCompletion,
                profile_completion_required: currentUser.user_metadata?.profile_completion_required
            });
        }

        // DISABLED: Old profile completion system - now handled by new onboarding system
        // Check if user needs basic profile completion
        // const needsBasicProfile = !currentUser.database_user?.country_selection_completed ||
        //                          !currentUser.database_user?.full_name ||
        //                          !currentUser.database_user?.phone;

        // if (needsBasicProfile) {
        //     console.log('🔄 User needs basic profile completion, redirecting to basic-profile-completion');
        //     if (isMobile) console.log('📱 Mobile redirect: basic-profile-completion');
        //     setCurrentSection('basic-profile-completion');
        //     // Update URL without page reload to avoid infinite loop
        //     if (window.location.pathname !== '/complete-profile') {
        //         window.history.pushState({}, '', '/complete-profile');
        //     }
        // } else
        // Always go to dashboard - profile completion is now handled in legal documents page
        console.log('✅ Going to dashboard - profile completion handled in legal documents');
        if (isMobile) console.log('📱 Mobile redirect: dashboard');
        setCurrentSection('dashboard');
        // Update URL without page reload to avoid infinite loop
        if (window.location.pathname !== '/dashboard') {
            window.history.pushState({}, '', '/dashboard');
        }
    };

    // Check for existing session on mount
    useEffect(() => {
        const checkSession = async () => {
            try {
                console.log('🔍 Checking for existing session...');
                console.log('🔍 [DEBUG] localStorage aureus_telegram_user:', localStorage.getItem('aureus_telegram_user'));
                console.log('🔍 [DEBUG] localStorage aureus_test_user:', localStorage.getItem('aureus_test_user'));

                // Check if user recently logged out
                const logoutFlag = localStorage.getItem('aureus_logout_flag');
                if (logoutFlag === 'true') {
                    console.log('🚪 User recently logged out, skipping auto-login');
                    localStorage.removeItem('aureus_logout_flag'); // Clear the flag
                    setIsCheckingSession(false);
                    return;
                }

                // TEMPORARY: Clear any corrupted localStorage data
                const telegramUser = localStorage.getItem('aureus_telegram_user');
                if (telegramUser) {
                    try {
                        const parsed = JSON.parse(telegramUser);
                        console.log('🔍 [DEBUG] Parsed telegram user from localStorage:', parsed);
                        if (!parsed.database_user || !parsed.database_user.telegram_id) {
                            console.log('⚠️ [DEBUG] Corrupted telegram user data detected, clearing localStorage');
                            localStorage.removeItem('aureus_telegram_user');
                        }
                    } catch (e) {
                        console.log('⚠️ [DEBUG] Invalid JSON in localStorage, clearing');
                        localStorage.removeItem('aureus_telegram_user');
                    }
                }

                const currentUser = await getCurrentUser();
                console.log('🔍 [DEBUG] getCurrentUser result:', currentUser);

                if (currentUser) {
                    console.log('✅ Found existing session:', currentUser);
                    console.log('🔍 [DEBUG] currentUser.needsProfileCompletion:', currentUser.needsProfileCompletion);
                    console.log('🔍 [DEBUG] currentUser.user_metadata?.profile_completion_required:', currentUser.user_metadata?.profile_completion_required);
                    console.log('🔍 [DEBUG] currentUser.database_user?.telegram_id:', currentUser.database_user?.telegram_id);

                    setUser(currentUser);

                    // Don't auto-redirect if user is trying to access affiliate page
                    const currentPath = window.location.pathname;
                    console.log('🔍 Current path during session check:', currentPath);

                    if (currentPath === '/affiliate') {
                        console.log('🏢 User accessing affiliate page, staying on affiliate page');
                        setCurrentSection('affiliate');
                    } else if (currentPath === '/login' || currentPath === '/register') {
                        console.log('🔐 User accessing auth page, staying on account-access');
                        setCurrentSection('account-access');
                        setAuthMode(currentPath === '/register' ? 'register' : 'login');
                    } else if (currentPath === '/purchase-shares') {
                        console.log('💰 User accessing purchase-shares page, showing purchase shares');
                        setCurrentSection('purchase-shares');

                    } else if (currentPath === '/dashboard') {
                        console.log('🏠 User accessing dashboard page, redirecting to dashboard');
                        handleDefaultDashboardLogic(currentUser);
                    } else if (currentPath.includes('migrate')) {
                        console.log('🔄 User accessing migration page, staying on migration page');
                        // Don't redirect migration pages - let them handle their own logic
                        return;
                    } else if (currentPath !== '/' && !currentPath.includes('/')) {
                        // Check if this is a username-based affiliate landing page
                        const username = currentPath.substring(1); // Remove leading slash
                        const reservedPaths = [
                            'dashboard', 'admin', 'api', 'static', '_next', 'favicon.ico',
                            'complete-profile', 'login', 'register', 'purchase-shares', 'affiliate',
                            'migrate-from-telegram', 'migrate', 'sync', 'reset-password',
                            'privacy-policy', 'terms-conditions', 'kyc-certification', 'aureus.js'
                        ];
                        if (username && username.length > 0 && !reservedPaths.includes(username)) {
                            console.log('🎯 Username-based affiliate landing page detected during session check:', username);
                            setCurrentSection('affiliate-landing');
                            setAffiliateUsername(username);
                        } else {
                            // Default dashboard logic
                            handleDefaultDashboardLogic(currentUser);
                        }
                    } else {
                        // Check if user needs basic profile completion
                        const needsBasicProfile = !currentUser.database_user?.country_selection_completed ||
                                                 !currentUser.database_user?.full_name ||
                                                 !currentUser.database_user?.phone;

                        // Always go to dashboard - profile completion is now handled in legal documents page
                        console.log('✅ Going to dashboard - profile completion handled in legal documents');
                        setCurrentSection('dashboard');
                    }
                } else {
                    console.log('ℹ️ No existing session found');
                }
            } catch (error) {
                console.error('❌ Session check error:', error);
            } finally {
                setIsCheckingSession(false);
            }
        };

        checkSession();

        // Restore dashboard preference
        const savedDashboard = localStorage.getItem('aureus_active_dashboard') as 'shareholder' | 'affiliate';
        if (savedDashboard && (savedDashboard === 'shareholder' || savedDashboard === 'affiliate')) {
            setActiveDashboard(savedDashboard);
        }
    }, []);

    // Check for admin parameter on mount
    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('admin') === 'true') {
            setShowAdmin(true);
        }
    }, []);

    // Initialize email processing service
    useEffect(() => {
        try {
            const emailProcessingService = EmailProcessingService.getInstance();
            emailProcessingService.startAutomaticProcessing();
            console.log('✅ Email processing service initialized');
        } catch (error) {
            console.error('❌ Failed to initialize email processing service:', error);
        }
    }, []);

    // Handle back to main site
    const handleBackToMain = () => {
        setShowAdmin(false);
        // Remove admin parameter from URL
        const url = new URL(window.location.href);
        url.searchParams.delete('admin');
        window.history.replaceState({}, '', url.toString());
    };

    // Show loading screen while checking session
    if (isCheckingSession) {
        return (
            <div className="bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white min-h-screen flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto mb-4"></div>
                    <p className="text-gray-300">Checking session...</p>
                </div>
            </div>
        );
    }

    // Show admin interface if admin=true
    if (showAdmin) {
        return <AdminRouter onBackToMain={handleBackToMain} />;
    }

    // Show legal pages if selected
    if (currentSection === 'privacy-policy') {
        return (
            <ErrorBoundary>
                <div className="legal-section">
                    <div className="container">
                        <button
                            className="back-button"
                            onClick={() => setCurrentSection('home')}
                        >
                            ← Back to Home
                        </button>
                        <PrivacyPolicy />
                    </div>
                </div>
            </ErrorBoundary>
        );
    }

    if (currentSection === 'terms-conditions') {
        return (
            <ErrorBoundary>
                <div className="legal-section">
                    <div className="container">
                        <button
                            className="back-button"
                            onClick={() => setCurrentSection('home')}
                        >
                            ← Back to Home
                        </button>
                        <TermsAndConditions />
                    </div>
                </div>
            </ErrorBoundary>
        );
    }

    if (currentSection === 'disclaimer') {
        return (
            <ErrorBoundary>
                <div className="legal-section">
                    <div className="container">
                        <button
                            className="back-button"
                            onClick={() => setCurrentSection('home')}
                        >
                            ← Back to Home
                        </button>
                        <LegalDisclaimer />
                    </div>
                </div>
            </ErrorBoundary>
        );
    }

    // CSS TEST MODE - TEMPORARY
    if (window.location.search.includes('csstest')) {
        return (
            <div className="bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white min-h-screen overflow-y-auto">
                <div className="container mx-auto px-4 py-8">
                    <div className="bg-gray-900/50 backdrop-blur-xl rounded-3xl p-8 border border-gray-700/30 shadow-2xl mb-8">
                        <div className="space-y-8">
                            <div className="text-center">
                                <h3 className="text-2xl font-bold text-white mb-2">
                                    CSS TEST - Sign In to Your Account
                                </h3>
                                <p className="text-gray-400">
                                    Access your Aureus Alliance Holdings dashboard
                                </p>
                            </div>

                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-semibold text-gray-300 mb-2">
                                        Email Address
                                    </label>
                                    <input
                                        type="email"
                                        className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:border-transparent transition-all duration-300"
                                        placeholder="<EMAIL>"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-semibold text-gray-300 mb-2">
                                        Password
                                    </label>
                                    <input
                                        type="password"
                                        className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:border-transparent transition-all duration-300"
                                        placeholder="••••••••••••••••"
                                    />
                                </div>

                                <button className="w-full bg-gradient-to-r from-gold-400 to-gold-600 text-black font-bold py-3 px-6 rounded-xl hover:from-gold-500 hover:to-gold-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-gold-400/25">
                                    Sign In - CSS TEST
                                </button>

                                <button onClick={() => window.location.search = ''} className="w-full bg-gray-600 text-white font-bold py-2 px-4 rounded-xl hover:bg-gray-700 transition-all duration-300">
                                    Back to App
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // Show unified account access page for both login and register
    if (currentSection === 'account-access') {
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        console.log('🔐 Rendering account-access page', { isMobile, authMode, currentSection });

        return (
            <ErrorBoundary>
                <UnifiedAuthPage
                    onAuthSuccess={(userData) => {
                        console.log('✅ Unified auth success:', userData);

                        // Mobile-specific debugging
                        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
                        if (isMobile) {
                            console.log('📱 Mobile device - processing auth success callback');
                            console.log('📱 User data received:', {
                                account_type: userData.account_type,
                                user_type: userData.user_type,
                                email: userData.database_user?.email,
                                id: userData.database_user?.id
                            });
                        }

                        setUser(userData);

                        // Store user data in localStorage for persistence
                        if (userData.account_type === 'telegram') {
                            console.log('💾 Storing Telegram user data in localStorage');
                            localStorage.setItem('aureus_telegram_user', JSON.stringify(userData.database_user));
                            localStorage.setItem('aureus_user', JSON.stringify(userData.database_user));
                        } else {
                            localStorage.setItem('aureus_user', JSON.stringify(userData.database_user));
                        }

                        // Store user type for proper dashboard routing - default to shareholder
                        localStorage.setItem('aureus_user_type', userData.user_type || 'shareholder');

                        // FIXED: Always redirect to dashboard after login, ignore purchase shares redirect
                        // Users can navigate to purchase shares from the dashboard if needed
                        const postLoginRedirect = localStorage.getItem('aureus_post_login_redirect');
                        if (postLoginRedirect) {
                            console.log('🔄 Post-login redirect found but ignoring:', postLoginRedirect);
                            localStorage.removeItem('aureus_post_login_redirect');
                            console.log('🏠 Redirecting to dashboard instead of purchase shares');
                        }

                        // Always redirect to dashboard after successful authentication
                        console.log('🏠 Redirecting to dashboard after successful auth');
                        console.log('👤 User type:', userData.user_type);

                        // Set a flag to prevent URL routing from interfering
                        localStorage.setItem('aureus_auth_success_redirect', 'true');

                        if (isMobile) {
                            // Check if user is on migration page - if so, skip mobile auth manager
                            const currentPath = window.location.pathname;
                            if (currentPath.includes('migrate')) {
                                console.log('📱 Mobile device on migration page - skipping mobile auth manager and redirects');
                                // Don't redirect migration pages - let them handle their own logic
                                return;
                            } else {
                                console.log('📱 Mobile device detected - using centralized mobile auth manager');
                                mobileAuthManager.startRedirect(userData);
                            }
                        } else {
                            console.log('🖥️ Desktop device - using standard redirect logic');

                            // Use the default dashboard logic for desktop
                            handleDefaultDashboardLogic(userData);

                            // Clear the flag after redirect
                            setTimeout(() => {
                                localStorage.removeItem('aureus_auth_success_redirect');
                            }, 2000);
                        }

                        // Reset previous section
                        setPreviousSection('home');
                    }}
                    onBack={() => {
                        // Go back to where user came from
                        if (previousSection === 'affiliate') {
                            setCurrentSection('affiliate');
                            window.history.pushState({}, '', '/affiliate');
                        } else {
                            setCurrentSection('home');
                            window.history.pushState({}, '', '/');
                        }
                        setPreviousSection('home'); // Reset
                    }}
                    userType={previousSection === 'affiliate' ? 'affiliate' : 'affiliate'} // Default to affiliate for backward compatibility
                />
            </ErrorBoundary>
        );
    }

    // Purchase Shares page (authenticated users)
    if (currentSection === 'purchase-shares') {
        return (
            <ErrorBoundary>
                <PurchaseSharesPage
                    onBack={() => {
                        setCurrentSection('dashboard');
                        window.history.pushState({}, '', '/dashboard');
                    }}
                    onNavigate={(page: string) => {
                        setCurrentSection(page);
                        window.history.pushState({}, '', `/${page}`);
                    }}
                    onAuthSuccess={(userData) => {
                        console.log('✅ Purchase shares auth success:', userData);
                        setUser(userData);

                        // Store user data in localStorage for persistence
                        if (userData.account_type === 'telegram') {
                            console.log('💾 Storing Telegram user data in localStorage');
                            localStorage.setItem('aureus_telegram_user', JSON.stringify(userData.database_user));
                            localStorage.setItem('aureus_user', JSON.stringify(userData.database_user));
                        } else {
                            localStorage.setItem('aureus_user', JSON.stringify(userData.database_user));
                        }

                        // Store user type as shareholder
                        localStorage.setItem('aureus_user_type', 'shareholder');

                        // Check if user needs profile completion
                        const needsProfile = !userData.database_user?.country_selection_completed ||
                                           !userData.database_user?.full_name ||
                                           !userData.database_user?.phone;

                        if (needsProfile) {
                            console.log('🔄 User needs profile completion, staying on purchase page with profile section');
                            // Store the purchase shares intent for after profile completion
                            localStorage.setItem('aureus_post_profile_redirect', '/purchase-shares');
                            // Don't redirect - let the purchase page handle profile completion
                        } else {
                            // Profile is complete, stay on purchase shares page
                            console.log('✅ Profile complete, staying on purchase shares page');
                            // Clear any redirect since we're already on the purchase page
                            localStorage.removeItem('aureus_post_login_redirect');
                        }
                    }}
                />
            </ErrorBoundary>
        );
    }



    // Shareholder registration route (Purchase Shares)
    if (currentSection === 'shareholder-registration') {
        return (
            <ErrorBoundary>
                <PurchaseSharesPage
                    onBack={() => {
                        setCurrentSection('home');
                        window.history.pushState({}, '', '/');
                    }}
                    onNavigate={(page: string) => {
                        setCurrentSection(page);
                        window.history.pushState({}, '', `/${page}`);
                    }}
                    onAuthSuccess={(userData) => {
                        console.log('✅ Purchase shares auth success:', userData);
                        setUser(userData);

                        // Store user data in localStorage for persistence
                        if (userData.account_type === 'telegram') {
                            console.log('💾 Storing Telegram user data in localStorage');
                            localStorage.setItem('aureus_telegram_user', JSON.stringify(userData.database_user));
                            localStorage.setItem('aureus_user', JSON.stringify(userData.database_user));
                        } else {
                            localStorage.setItem('aureus_user', JSON.stringify(userData.database_user));
                        }

                        // Store user type as shareholder
                        localStorage.setItem('aureus_user_type', 'shareholder');

                        // Check if user needs profile completion
                        const needsProfile = !userData.database_user?.country_selection_completed ||
                                           !userData.database_user?.full_name ||
                                           !userData.database_user?.phone;

                        if (needsProfile) {
                            console.log('🔄 User needs profile completion, staying on purchase page with profile section');
                            // Store the purchase shares intent for after profile completion
                            localStorage.setItem('aureus_post_profile_redirect', '/purchase-shares');
                            // Don't redirect - let the purchase page handle profile completion
                        } else {
                            // Profile is complete, proceed directly to purchase shares
                            console.log('✅ Profile complete, proceeding to purchase shares');
                            // Clear any redirect since we're already on the purchase page
                            localStorage.removeItem('aureus_post_login_redirect');
                            // Stay on purchase shares page - the PurchaseSharesPage component will handle showing the flow
                        }
                    }}
                />
            </ErrorBoundary>
        );
    }

    // Username-based affiliate landing page route
    if (currentSection === 'affiliate-landing') {
        console.log('🎯 Rendering username-based affiliate landing page for:', affiliateUsername);
        return (
            <ErrorBoundary>
                <CustomAffiliateLandingPage
                    username={affiliateUsername}
                    onRegistrationSuccess={handleRegistrationSuccess}
                />
            </ErrorBoundary>
        );
    }

    // Affiliate landing route
    if (currentSection === 'affiliate') {
        console.log('🏢 Rendering affiliate landing page...');
        return (
            <ErrorBoundary>
                <AffiliateLandingPage
                    onBackHome={() => { setCurrentSection('home'); window.history.pushState({}, '', '/'); }}
                    onGoToAuth={(mode) => {
                        setPreviousSection('affiliate'); // Remember we came from affiliate page
                        setAuthMode(mode);
                        setCurrentSection('account-access');
                        window.history.pushState({}, '', mode === 'register' ? '/register' : '/login');
                    }}
                    onLogoutAndRegister={async () => {
                        console.log('🔄 Logo clicked - logging out and redirecting to registration');

                        // If user is logged in, log them out first
                        if (user) {
                            console.log('👤 User is logged in, logging out...');
                            await handleLogout();

                            // Force page refresh to ensure clean state
                            console.log('🔄 Force refreshing page for clean state...');
                            window.location.href = '/register';
                            return;
                        }

                        // Set previous section so we can return to affiliate page after registration
                        setPreviousSection('affiliate');

                        // Then redirect to registration
                        setAuthMode('register');
                        setCurrentSection('account-access');
                        window.history.pushState({}, '', '/register');
                    }}
                />
            </ErrorBoundary>
        );
    }

    // Show sync page for account linking from Telegram
    if (currentSection === 'sync') {
        const syncToken = (window as any).syncToken;

        if (!user) {
            // User not logged in, redirect to auth page
            setCurrentSection('account-access');
            return null;
        }

        return (
            <ErrorBoundary>
                <SyncPage
                    syncToken={syncToken}
                    user={user}
                    onSyncComplete={() => {
                        setCurrentSection('dashboard');
                        window.history.pushState({}, '', '/dashboard');
                        // Refresh user data
                        loadUserData();
                    }}
                    onBack={() => {
                        setCurrentSection('dashboard');
                        window.history.pushState({}, '', '/dashboard');
                    }}
                />
            </ErrorBoundary>
        );
    }

    // Show migration page
    if (currentSection === 'migration') {
        const currentPath = window.location.pathname;

        if (currentPath === '/migrate-from-telegram') {
            // Import and render the migrate-from-telegram page
            const MigrateFromTelegram = React.lazy(() => import('./pages/migrate-from-telegram'));

            return (
                <ErrorBoundary>
                    <React.Suspense fallback={<div className="min-h-screen bg-gray-900 flex items-center justify-center"><div className="text-white">Loading migration page...</div></div>}>
                        <MigrateFromTelegram />
                    </React.Suspense>
                </ErrorBoundary>
            );
        } else if (currentPath === '/migrate') {
            // Import and render the migrate page from new location
            const MigratePage = React.lazy(() => import('./components/migration/MigratePage'));

            return (
                <ErrorBoundary>
                    <React.Suspense fallback={<div className="min-h-screen bg-gray-900 flex items-center justify-center"><div className="text-white">Loading migration page...</div></div>}>
                        <MigratePage />
                    </React.Suspense>
                </ErrorBoundary>
            );
        } else if (currentPath.includes('/auth/telegram')) {
            // Import and render the telegram auth page from new location
            const TelegramAuthPage = React.lazy(() => import('./components/migration/TelegramAuthPage'));

            return (
                <ErrorBoundary>
                    <React.Suspense fallback={<div className="min-h-screen bg-gray-900 flex items-center justify-center"><div className="text-white">Loading authentication page...</div></div>}>
                        <TelegramAuthPage />
                    </React.Suspense>
                </ErrorBoundary>
            );
        } else if (currentPath === '/kyc-certification') {
            // Import and render the KYC certification page
            const KYCDocumentUpload = React.lazy(() => import('./components/user/KYCDocumentUpload'));

            return (
                <ErrorBoundary>
                    <React.Suspense fallback={<div className="min-h-screen bg-gray-900 flex items-center justify-center"><div className="text-white">Loading KYC certification...</div></div>}>
                        <KYCDocumentUpload userId={user?.database_user?.id || user?.id} />
                    </React.Suspense>
                </ErrorBoundary>
            );
        }
    }

    // REMOVED: basic-profile-completion section - now handled in legal documents page

    // Show dashboard if user is logged in and dashboard is selected
    if (currentSection === 'dashboard' && user) {
        // DISABLED: Old profile completion system - now handled by new onboarding system
        // Route guard: Check if user needs basic profile completion
        // const needsBasicProfile = !user.database_user?.country_selection_completed ||
        //                          !user.database_user?.full_name ||
        //                          !user.database_user?.phone;

        // if (needsBasicProfile) {
        //     console.log('🔄 Dashboard access blocked - user needs basic profile completion');
        //     setCurrentSection('basic-profile-completion');
        //     window.history.pushState({}, '', '/complete-profile');
        //     return null;
        // }

        // Route guard: Prevent dashboard access if profile completion is needed
        console.log('🔍 Dashboard route guard check:', {
            needsProfileCompletion: user.needsProfileCompletion,
            profile_completion_required: user.user_metadata?.profile_completion_required,
            currentSection,
            userEmail: user.email,
            hasDatabase: !!user.database_user,
            databaseUserComplete: user.database_user ? {
                hasEmail: !!user.database_user.email,
                hasPassword: !!user.database_user.password_hash,
                hasFullName: !!user.database_user.full_name,
                hasPhone: !!user.database_user.phone,
                hasCountry: !!user.database_user.country_of_residence,
                countrySelectionCompleted: !!user.database_user.country_selection_completed
            } : null
        });

        console.log('✅ Dashboard access granted - user authenticated');

        return (
            <ErrorBoundary>
                {activeDashboard === 'affiliate' ? (
                    <AffiliateDashboard
                        user={user}
                        onLogout={handleLogout}
                        onSwitchDashboard={handleDashboardSwitch}
                    />
                ) : (
                    <UserDashboard
                        user={user}
                        onLogout={handleLogout}
                        onNavigate={setCurrentSection}
                        onSwitchDashboard={handleDashboardSwitch}
                    />
                )}
            </ErrorBoundary>
        );
    }

    // Show the new professional corporate homepage
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    console.log('🏠 Rendering homepage (fallback)', { currentSection, isMobile, user: !!user });

    return (
        <ErrorBoundary>
            <CorporateHomepage />
        </ErrorBoundary>
    );
};

export default App;

