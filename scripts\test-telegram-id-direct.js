#!/usr/bin/env node

/**
 * Test Telegram ID Direct Login
 * 
 * This script tests the signInWithTelegramId function directly
 * to verify it works without Supabase Auth errors.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseAnonKey) {
  console.error('❌ Missing VITE_SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Replicate the signInWithTelegramId function
async function testTelegramIdLogin(telegramId) {
  try {
    console.log('🔐 Testing Telegram ID login for:', telegramId)

    const telegramIdNum = parseInt(telegramId)
    if (isNaN(telegramIdNum)) {
      return { success: false, error: 'Invalid Telegram ID format' }
    }

    // Look for user in main users table with telegram_id
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('telegram_id', telegramIdNum)
      .single()

    if (userError || !user) {
      console.log('❌ User not found with Telegram ID:', telegramId)
      return { success: false, error: 'Telegram ID not found. Please contact support.' }
    }

    if (!user.is_active) {
      return { success: false, error: 'Account is deactivated. Please contact support.' }
    }

    console.log('✅ Found user for Telegram ID:', user.username)

    // Create authenticated user object (no Supabase Auth needed)
    const authenticatedUser = {
      id: `telegram_${telegramIdNum}`,
      email: user.email,
      database_user: user,
      account_type: 'telegram_direct',
      user_metadata: {
        telegram_id: telegramIdNum,
        full_name: user.full_name,
        username: user.username
      }
    }

    return { success: true, user: authenticatedUser }

  } catch (error) {
    console.error('❌ Telegram ID login exception:', error)
    return { success: false, error: 'Login failed. Please try again.' }
  }
}

async function runTest() {
  console.log('🧪 Testing Direct Telegram ID Login\n');

  try {
    // Test with the correct Telegram ID
    const result = await testTelegramIdLogin('**********');

    if (result.success) {
      console.log('✅ TELEGRAM ID LOGIN SUCCESSFUL!');
      console.log('');
      console.log('User Object:');
      console.log('• ID:', result.user.id);
      console.log('• Email:', result.user.email);
      console.log('• Account Type:', result.user.account_type);
      console.log('• Database User ID:', result.user.database_user.id);
      console.log('• Username:', result.user.database_user.username);
      console.log('• Full Name:', result.user.database_user.full_name);
      console.log('• Telegram ID:', result.user.database_user.telegram_id);
      console.log('• Is Active:', result.user.database_user.is_active);
      console.log('');
      console.log('🎉 The Telegram ID login function works correctly!');
      console.log('');
      console.log('This means the web form should work when you:');
      console.log('1. Select "🚀 Quick Login with Telegram ID"');
      console.log('2. Enter: **********');
      console.log('3. Click "Access Account"');
      console.log('');
      console.log('The user should be logged in and see the dashboard');
      console.log('with commission data loaded correctly.');
    } else {
      console.log('❌ TELEGRAM ID LOGIN FAILED:');
      console.log('Error:', result.error);
    }

    // Test with invalid ID
    console.log('\n🧪 Testing with invalid Telegram ID...');
    const invalidResult = await testTelegramIdLogin('*********');
    
    if (!invalidResult.success) {
      console.log('✅ Invalid ID correctly rejected:', invalidResult.error);
    } else {
      console.log('❌ Invalid ID should have been rejected');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

runTest();
