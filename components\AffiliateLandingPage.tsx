import React, { useState } from 'react'
import GoldDiggersClub from './competition/GoldDiggersClub'
import ContactForm from './ContactForm'


interface AffiliateLandingPageProps {
  onBackHome?: () => void
  onGoToAuth?: (mode: 'login' | 'register') => void
  onLogoutAndRegister?: () => void
}

export const AffiliateLandingPage: React.FC<AffiliateLandingPageProps> = ({ onBackHome, onGoToAuth, onLogoutAndRegister }) => {
  console.log('🏢 AffiliateLandingPage component rendering...');
  const [activeModal, setActiveModal] = useState<string | null>(null);

  const openModal = (modalType: string) => {
    setActiveModal(modalType);
  };

  const closeModal = () => {
    setActiveModal(null);
  };

  return (
    <div className="page modern-website">
      {/* Consistent Header */}
      <header className="header premium-header">
        <div className="container">
          <div className="header-content">
            <div className="logo">
              <img
                src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-s.png"
                alt="Aureus Alliance Holdings"
                className="logo-image"
              />
            </div>
            <nav className="nav-menu">
              <div className="nav-main">
                <button
                  className="nav-item premium-nav-item"
                  onClick={() => onBackHome?.()}
                >
                  ← Back to Website
                </button>
                <button
                  className="nav-item premium-nav-item active"
                >
                  Affiliate Program
                </button>
              </div>
              <div className="nav-actions">
                <button
                  className="btn btn-secondary premium-btn-secondary"
                  onClick={() => onGoToAuth?.('login')}
                >
                  Affiliate Login
                </button>
                <button
                  className="btn btn-primary premium-btn"
                  onClick={() => onGoToAuth?.('login')}
                >
                  Join Now
                </button>
              </div>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main>
        {/* Hero Section */}
        <section className="hero">
          <div className="container">
            <div className="hero-content">
              <div className="content">
                <div className="badge">
                  <span className="badge-dot"></span>
                  Exclusive Affiliate Opportunity
                </div>
                <h1>Aureus Affiliate Program</h1>
                <p className="hero-description">
                  Join the ultimate affiliate program for Africa's premier gold mining company.
                  Earn substantial commissions while helping others secure their financial future through real gold-backed shares.
                </p>
                <div className="hero-stats">
                  <div className="stat">
                    <div className="stat-value">15%</div>
                    <div className="stat-label">USDT Commission</div>
                  </div>
                  <div className="stat">
                    <div className="stat-value">15%</div>
                    <div className="stat-label">Bonus Shares</div>
                  </div>
                  <div className="stat">
                    <div className="stat-value">$150K+</div>
                    <div className="stat-label">Prize Pool</div>
                  </div>
                </div>
                <div className="hero-actions">
                  <button
                    className="btn btn-primary btn-large"
                    onClick={() => onGoToAuth?.('login')}
                  >
                    Become a Gold Diggers Club Member For Free
                  </button>
                  <button
                    className="btn btn-secondary btn-large"
                    onClick={() => onGoToAuth?.('login')}
                  >
                    Affiliate Login
                  </button>
                </div>
              </div>
              <div className="logo">
                <img
                  src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-m.png"
                  alt="Aureus Alliance Holdings"
                  className="hero-logo"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="benefits">
          <div className="container">
            <div className="section-header">
              <h2>Why Join Our Affiliate Program?</h2>
              <p>Unlock exclusive earning opportunities with Africa's leading gold mining company</p>
            </div>
            <div className="benefits-grid">
              <div className="benefit-card">
                <div className="card-icon">💰</div>
                <h3>High Commission Structure</h3>
                <p>Earn up to 15% USDT + 15% bonus shares on every qualified referral. Our commission rates are among the highest in the industry.</p>
                <ul>
                  <li>15% USDT commission on all purchases</li>
                  <li>15% bonus shares (Pre-Sale exclusive)</li>
                  <li>Multi-level earning potential</li>
                  <li>No commission caps or limits</li>
                </ul>
              </div>
              <div className="benefit-card">
                <div className="card-icon">🏆</div>
                <h3>Gold Diggers Club Competitions</h3>
                <p>Compete for massive USDT prizes in our exclusive affiliate competitions. Top performers win life-changing rewards.</p>
                <ul>
                  <li>$150,000+ total prize pools</li>
                  <li>Monthly competition cycles</li>
                  <li>Leaderboard recognition</li>
                  <li>Exclusive winner benefits</li>
                </ul>
              </div>
              <div className="benefit-card">
                <div className="card-icon">📊</div>
                <h3>Advanced Analytics & Tools</h3>
                <p>Access professional-grade marketing tools, detailed analytics, and comprehensive tracking systems.</p>
                <ul>
                  <li>Real-time performance dashboard</li>
                  <li>Marketing material generator</li>
                  <li>Referral link tracking</li>
                  <li>Commission history reports</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Gold Diggers Club Section */}
        <section className="gold-diggers-section">
          <div className="container">
            <GoldDiggersClub onGoToAuth={onGoToAuth} />
          </div>
        </section>

        {/* How It Works Section */}
        <section className="how-it-works">
          <div className="container">
            <div className="section-header">
              <h2>How It Works</h2>
              <p>Simple steps to start earning with our affiliate program</p>
            </div>
            <div className="steps-grid">
              <div className="step-card">
                <div className="step-number">1</div>
                <h3>Join FREE</h3>
                <p>Sign up as a Gold Diggers Club member - completely free with no hidden costs or fees.</p>
              </div>
              <div className="step-card">
                <div className="step-number">2</div>
                <h3>Get Your Links</h3>
                <p>Receive your unique referral links and access professional marketing materials.</p>
              </div>
              <div className="step-card">
                <div className="step-number">3</div>
                <h3>Share & Earn</h3>
                <p>Share with your network and earn 15% USDT + 15% bonus shares on every purchase.</p>
              </div>
              <div className="step-card">
                <div className="step-number">4</div>
                <h3>Compete & Win</h3>
                <p>Climb the leaderboard and compete for massive USDT prizes in our competitions.</p>
              </div>
            </div>
          </div>
        </section>

        {/* Important Notice */}
        <section className="notice">
          <div className="container">
            <div className="notice-card">
              <div className="notice-icon">ℹ️</div>
              <h3>Important Notice</h3>
              <p>
                The affiliate program is entirely separate from the shareholder registration.
                Shareholders can purchase shares directly without any sponsor or referral requirements.
              </p>
            </div>
          </div>
        </section>
      </main>

      {/* Consistent Footer */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-info">
              <img
                src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-m.png"
                alt="Aureus Alliance Holdings"
                className="footer-logo"
              />
              <h3>Aureus Alliance Holdings (Pty) Ltd</h3>
              <p>CIPC-registered gold mining company • Real shares backed by real gold production</p>
            </div>

            <div className="footer-contact">
              <h4>Contact Us</h4>
              <ContactForm
                className="footer-contact-form"
                onSuccess={() => {
                  console.log('✅ Contact form submitted successfully from affiliate landing page');
                }}
                onError={(error) => {
                  console.error('❌ Contact form error from affiliate landing page:', error);
                }}
              />
            </div>

            <div className="footer-links">
              <button onClick={() => openModal('privacy')} className="footer-link-btn">
                Privacy Policy
              </button>
              <button onClick={() => openModal('terms')} className="footer-link-btn">
                Terms & Conditions
              </button>
              <button onClick={() => openModal('legal')} className="footer-link-btn">
                Legal Disclaimer
              </button>
              <button
                onClick={() => onBackHome?.()}
                className="footer-link-btn text-gold"
              >
                Back to Website
              </button>
            </div>
          </div>
          <div className="footer-bottom">
            <p>&copy; 2024 Aureus Alliance Holdings (Pty) Ltd. All rights reserved.</p>
          </div>
        </div>
      </footer>

      {/* Modal Overlay */}
      {activeModal && (
        <div className="modal-overlay" onClick={closeModal}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <button className="modal-close" onClick={closeModal}>×</button>
            {activeModal === 'privacy' && (
              <div>
                <h2>Privacy Policy</h2>
                <p>Your privacy is important to us. This policy outlines how we collect, use, and protect your information.</p>
              </div>
            )}
            {activeModal === 'terms' && (
              <div>
                <h2>Terms & Conditions</h2>
                <p>By using our affiliate program, you agree to these terms and conditions.</p>
              </div>
            )}
            {activeModal === 'legal' && (
              <div>
                <h2>Legal Disclaimer</h2>
                <p>This affiliate program is subject to applicable laws and regulations.</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default AffiliateLandingPage

