import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { ComprehensiveDividendsCalculator } from '../dividends/ComprehensiveDividendsCalculator';
import { EmailRegistrationForm } from '../EmailRegistrationForm';

interface ProfessionalLandingPageProps {
  username: string;
}

interface AffiliateProfile {
  id: number;
  username: string;
  first_name?: string;
  last_name?: string;
  full_name?: string;
  email: string;
  phone?: string;
  profile_description?: string;
  profile_image_url?: string;
  total_referrals: number;
  total_earnings: number;
  created_at: string;
}

export const ProfessionalLandingPage: React.FC<ProfessionalLandingPageProps> = ({ username }) => {
  const [affiliate, setAffiliate] = useState<AffiliateProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [showRegistration, setShowRegistration] = useState(false);

  useEffect(() => {
    loadAffiliateProfile();
  }, [username]);

  // Refresh affiliate data when page becomes visible (to catch profile picture updates)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && affiliate) {
        loadAffiliateProfile();
      }
    };

    const handleFocus = () => {
      if (affiliate) {
        loadAffiliateProfile();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, [affiliate]);

  const loadAffiliateProfile = async () => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('username', username)
        .single();

      if (error) throw error;
      setAffiliate(data);
    } catch (error) {
      console.error('Error loading affiliate profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRegistrationSuccess = (user: any) => {
    alert('Registration successful! You have been automatically connected to your sponsor.');
    window.location.href = '/login';
  };

  // Pre-populate sponsor username when registration modal opens
  useEffect(() => {
    if (showRegistration && affiliate) {
      // Wait for the form to render, then populate sponsor username
      setTimeout(() => {
        const sponsorInput = document.querySelector('input[name="sponsorUsername"]') as HTMLInputElement;
        if (sponsorInput && !sponsorInput.value) {
          sponsorInput.value = affiliate.username;
          // Trigger change event to update form state
          const event = new Event('input', { bubbles: true });
          sponsorInput.dispatchEvent(event);
        }
      }, 100);
    }
  }, [showRegistration, affiliate]);



  if (loading) {
    return (
      <div className="loading-screen">
        <div className="loading-content">
          <div className="loading-spinner"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  if (!affiliate) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center p-4">
        <div className="max-w-2xl w-full text-center">
          {/* Company Logo */}
          <div className="mb-8">
            <img
              src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-m.png"
              alt="Aureus Alliance Holdings"
              className="h-20 mx-auto mb-4"
            />
            <h1 className="text-3xl font-bold text-white mb-2">Aureus Alliance Holdings</h1>
            <p className="text-gray-400">CIPC-Registered Gold Mining Company</p>
          </div>

          {/* Error Message */}
          <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-2xl p-8 mb-8">
            <div className="text-6xl mb-6">🔍</div>
            <h2 className="text-2xl font-bold text-white mb-4">Affiliate Not Found</h2>
            <p className="text-gray-300 mb-6 leading-relaxed">
              We couldn't find an affiliate profile for "<span className="text-yellow-400 font-semibold">{username}</span>".
            </p>

            {/* Instructions */}
            <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-6 mb-6">
              <div className="flex items-start space-x-3">
                <div className="text-2xl">💡</div>
                <div className="text-left">
                  <h3 className="font-semibold text-yellow-300 mb-2">What to do next:</h3>
                  <ul className="text-yellow-100 space-y-2 text-sm">
                    <li>• Double-check the username spelling with the person who introduced you</li>
                    <li>• Make sure you're using the exact username they provided</li>
                    <li>• Contact your introducer to verify their correct Aureus username</li>
                    <li>• Usernames are case-sensitive, so check capitalization</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/"
                className="bg-yellow-600 hover:bg-yellow-700 text-black font-semibold px-8 py-3 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
              >
                <span>🏠</span>
                <span>Go to Homepage</span>
              </a>
              <a
                href="/affiliate"
                className="bg-gray-700 hover:bg-gray-600 text-white font-semibold px-8 py-3 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
              >
                <span>👥</span>
                <span>Join as Affiliate</span>
              </a>
            </div>
          </div>

          {/* Contact Information */}
          <div className="text-gray-400 text-sm">
            <p className="mb-2">Need help? Contact us:</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <a href="mailto:<EMAIL>" className="hover:text-yellow-400 transition-colors">
                📧 <EMAIL>
              </a>
              <span className="hidden sm:inline">•</span>
              <a href="https://wa.me/27783699799" className="hover:text-yellow-400 transition-colors">
                📱 JP Rademeyer: +27 78 369 9799
              </a>
              <span className="hidden sm:inline">•</span>
              <a href="https://wa.me/27744493251" className="hover:text-yellow-400 transition-colors">
                📱 Donovan James: +27 74 449 3251
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="aureus-landing">
      {/* Floating Navigation */}
      <nav className="floating-nav">
        <div className="nav-content">
          <div className="nav-brand">
            <img
              src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-s.png"
              alt="Aureus Alliance Holdings"
              className="nav-logo"
            />
            <span className="brand-text">Aureus Alliance</span>
          </div>
          <div className="nav-menu">
            <a href="#hero" className="nav-link">Home</a>
            <a href="#about" className="nav-link">About</a>
            <a href="#operations" className="nav-link">Operations</a>
            <a href="#calculator" className="nav-link">Calculator</a>
            <a href="#phases" className="nav-link">Phases</a>
          </div>
          <button
            onClick={() => setShowRegistration(true)}
            className="cta-button"
          >
            <span>Join Now</span>
            <div className="button-glow"></div>
          </button>
        </div>
      </nav>

      {/* Hero Section with Affiliate Profile */}
      <section id="hero" className="hero-section">
        <div className="hero-background">
          <div className="gradient-overlay"></div>
          <div className="particle-field"></div>
        </div>

        <div className="hero-content">
          <div className="sponsor-intro">
            <div className="sponsor-badge">
              <img
                src={affiliate.profile_image_url ? `${affiliate.profile_image_url}?t=${new Date(affiliate.updated_at || Date.now()).getTime()}` : "https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/default-avatar.png"}
                alt={affiliate.full_name || affiliate.username}
                className="sponsor-avatar"
              />
              <div className="sponsor-info">
                <span className="sponsor-label">Your Personal Sponsor</span>
                <span className="sponsor-name">{affiliate.full_name || `${affiliate.first_name} ${affiliate.last_name}`.trim() || affiliate.username}</span>
                <span className="sponsor-username">@{affiliate.username}</span>
              </div>
            </div>
          </div>

          <div className="hero-main">
            <h1 className="hero-title">
              <span className="title-line">Real Gold</span>
              <span className="title-line">Real Shares</span>
              <span className="title-line">Real Ownership</span>
            </h1>

            <p className="hero-description">
              Join the future of gold mining with transparent, profitable, and sustainable share ownership.
              Backed by real gold reserves across Africa, delivering consistent 12% annual dividends.
            </p>

            <div className="hero-metrics">
              <div className="metric-item">
                <span className="metric-number">5,000</span>
                <span className="metric-label">Hectares Planned</span>
              </div>
              <div className="metric-item">
                <span className="metric-number">200</span>
                <span className="metric-label">Plants by 2030</span>
              </div>
              <div className="metric-item">
                <span className="metric-number">12%</span>
                <span className="metric-label">Annual Returns</span>
              </div>
            </div>

            <div className="hero-actions">
              <button
                onClick={() => setShowRegistration(true)}
                className="primary-cta"
              >
                <span>Join {affiliate.first_name || affiliate.username}'s Team</span>
                <div className="cta-glow"></div>
              </button>
              <button className="secondary-cta">
                <span>Learn More</span>
              </button>
            </div>
          </div>

          <div className="hero-visual">
            <div className="visual-container">
              <img
                src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-m.png"
                alt="Aureus Alliance Holdings"
                className="hero-logo"
              />
              <div className="logo-glow"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Hero Section */}
      <section id="hero" className="hero">
        <div className="hero-container">
          <div className="hero-content">
            <div className="hero-text">
              <div className="hero-badge">
                <span className="badge-icon">⭐</span>
                AUREUS ALLIANCE HOLDINGS
              </div>
              <h1 className="hero-title">
                Real Gold • Real Shares • Real Ownership
              </h1>
              <p className="hero-subtitle">
                Join the future of gold mining with transparent, profitable, and sustainable share ownership. 
                Backed by real gold reserves across Africa, delivering consistent 12% annual dividends to 
                shareholders worldwide through our revolutionary 5-year expansion plan.
              </p>
              
              <div className="hero-metrics">
                <div className="metric">
                  <div className="metric-number">5,000</div>
                  <div className="metric-label">Hectares Planned</div>
                </div>
                <div className="metric">
                  <div className="metric-number">200</div>
                  <div className="metric-label">Plants by 2030</div>
                </div>
                <div className="metric">
                  <div className="metric-number">12%</div>
                  <div className="metric-label">Annual Returns</div>
                </div>
              </div>

              <div className="hero-actions">
                <button
                  onClick={() => setShowRegistration(true)}
                  className="btn-primary"
                >
                  Become a Shareholder
                </button>
                <button className="btn-secondary">
                  <span className="play-icon">▶</span>
                  Watch Our Story
                </button>
              </div>
            </div>

            <div className="hero-visual">
              <div className="visual-container">
                <img
                  src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-m.png"
                  alt="Aureus Alliance Holdings"
                  className="hero-logo"
                />
                <div className="floating-elements">
                  <div className="floating-element gold-bar"></div>
                  <div className="floating-element mining-icon"></div>
                  <div className="floating-element chart-icon"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Business Explanation Video */}
      <section className="business-video-section">
        <div className="section-container">
          <div className="section-header">
            <h2>Understanding the Aureus Alliance Holdings Opportunity</h2>
            <p>Watch this comprehensive explanation of our business model and expansion plan</p>
          </div>
          <div className="video-content">
            <div className="video-container">
              <video
                controls
                preload="metadata"
                poster="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/business-explanation-thumbnail.jpg"
                className="business-video-player"
              >
                <source
                  src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/business-explanation.mp4"
                  type="video/mp4"
                />
                {/* User will provide the business explanation video link */}
                Your browser does not support the video tag.
              </video>
            </div>
          </div>
        </div>
      </section>

      {/* About Section - REAL DATA */}
      <section id="about" className="about-section">
        <div className="section-container">
          <div className="section-header">
            <h2>About Aureus Alliance Holdings (Pty) Ltd</h2>
            <p>Building gold-backed impact ventures across Africa</p>
          </div>

          <div className="about-content">
            <div className="about-main">
              <p>
                Aureus Alliance Holdings (Pty) Ltd is a CIPC-registered gold mining company pioneering
                sustainable gold placer deposit mining operations across Africa. Our foundation is built on
                real gold reserves, transparent operations, and a commitment to delivering consistent
                returns to shareholders through our revolutionary 5-year expansion plan.
              </p>
            </div>

            <div className="about-grid">
              <div className="about-card">
                <div className="card-icon">🏗️</div>
                <h3>Operations Timeline</h3>
                <ul>
                  <li><strong>January 2026:</strong> Operations begin with 2 wash plants</li>
                  <li><strong>April 2026:</strong> First dividend payout ($15-$50 per share)</li>
                  <li><strong>June 2026:</strong> Operations scale to 10 wash plants</li>
                  <li><strong>2030:</strong> 200+ plants across 6,000 hectares</li>
                  <li><strong>Multi-Country:</strong> Zimbabwe, Zambia, Ghana, Tanzania, South Africa</li>
                </ul>
              </div>
              <div className="about-card">
                <div className="card-icon">💰</div>
                <h3>5-Year Expansion Plan</h3>
                <ul>
                  <li><strong>2026:</strong> 10 plants (250 hectares)</li>
                  <li><strong>2027:</strong> 25 plants (625 hectares)</li>
                  <li><strong>2028:</strong> 50 plants (1,250 hectares)</li>
                  <li><strong>2029:</strong> 100 plants (2,500 hectares)</li>
                  <li><strong>2030:</strong> 200 plants (5,000 hectares)</li>
                </ul>
              </div>
              <div className="about-card">
                <div className="card-icon">🌍</div>
                <h3>Real Impact</h3>
                <ul>
                  <li><strong>Gold Operations:</strong> 108+ tons/year by Phase 20</li>
                  <li><strong>Children Fed:</strong> 1,000,000+ across Africa</li>
                  <li><strong>Scholarships:</strong> 30,000+ educational opportunities</li>
                  <li><strong>Water Projects:</strong> 200+ boreholes</li>
                  <li><strong>Healthcare:</strong> 30+ advanced mobile units</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Washplant Setup Video Section */}
      <section id="operations" className="operations-section">
        <div className="section-container">
          <div className="operations-content">
            <div className="operations-text">
              <h2>200 TPH Washplant Setup - Yagden Engineering Zimbabwe</h2>
              <p>
                Watch the setup and preparation of our 200 tonnes per hour washplant facility
                in Zimbabwe. This is the exact type of equipment we will be deploying across
                our 5-year expansion plan to process gold-bearing gravel and deliver consistent
                returns to shareholders.
              </p>
              <ul className="operations-features">
                <li>✓ 200 tonnes per hour processing capacity</li>
                <li>✓ Yagden Engineering - proven technology</li>
                <li>✓ Eco-friendly processing methods</li>
                <li>✓ Scalable across multiple sites</li>
                <li>✓ Real equipment for real operations</li>
              </ul>
            </div>
            <div className="operations-video">
              <div className="video-container">
                <video
                  controls
                  preload="metadata"
                  poster="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/yagden-thumbnail.jpg"
                  className="operations-video-player"
                >
                  <source
                    src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/yagden.mp4"
                    type="video/mp4"
                  />
                  Your browser does not support the video tag.
                </video>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Professional Dividend Calculator */}
      <section id="calculator" className="calculator-section">
        <div className="section-container">
          <div className="section-header">
            <h2>Calculate Your Potential Dividends</h2>
            <p>Professional analysis tools for calculating your potential dividends on profits from gold mining operations</p>
          </div>
          <div className="calculator-container">
            <ComprehensiveDividendsCalculator userShares={1000} />
          </div>
        </div>
      </section>

      {/* Investment Phases */}
      <section id="phases" className="phases-section">
        <div className="section-container">
          <div className="section-header">
            <h2>Investment Phases</h2>
            <p>Choose the phase that aligns with your investment goals</p>
          </div>

          <div className="phases-grid">
            <div className="phase-card featured">
              <div className="phase-badge">CURRENT PHASE</div>
              <div className="phase-number">01</div>
              <h3>Foundation Phase</h3>
              <div className="phase-price">$1.00 <span>per share</span></div>
              <ul className="phase-features">
                <li>✓ Early adopter benefits</li>
                <li>✓ Maximum growth potential</li>
                <li>✓ 12% annual dividends</li>
                <li>✓ Gold-backed security</li>
              </ul>
              <button
                onClick={() => setShowRegistration(true)}
                className="phase-btn"
              >
                Purchase Shares
              </button>
            </div>

            <div className="phase-card">
              <div className="phase-number">02</div>
              <h3>Early Growth</h3>
              <div className="phase-price">$1.25 <span>per share</span></div>
              <ul className="phase-features">
                <li>✓ Proven track record</li>
                <li>✓ Expanded operations</li>
                <li>✓ 12% annual dividends</li>
                <li>✓ Enhanced security</li>
              </ul>
              <button className="phase-btn disabled">
                Coming Soon
              </button>
            </div>

            <div className="phase-card">
              <div className="phase-number">03</div>
              <h3>Expansion Phase</h3>
              <div className="phase-price">$1.50 <span>per share</span></div>
              <ul className="phase-features">
                <li>✓ Multi-site operations</li>
                <li>✓ Increased production</li>
                <li>✓ 12% annual dividends</li>
                <li>✓ Premium benefits</li>
              </ul>
              <button className="phase-btn disabled">
                Coming Soon
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="why-choose-section">
        <div className="section-container">
          <div className="why-choose-content">
            <div className="why-choose-text">
              <h2>Why Choose Aureus Alliance Holdings?</h2>
              <div className="reasons-grid">
                <div className="reason-item">
                  <div className="reason-icon">🏆</div>
                  <div className="reason-content">
                    <h4>Proven Track Record</h4>
                    <p>Years of successful mining operations with consistent returns to shareholders.</p>
                  </div>
                </div>
                <div className="reason-item">
                  <div className="reason-icon">🔒</div>
                  <div className="reason-content">
                    <h4>Real Gold Backing</h4>
                    <p>Every share is backed by actual gold reserves, providing tangible asset security.</p>
                  </div>
                </div>
                <div className="reason-item">
                  <div className="reason-icon">📈</div>
                  <div className="reason-content">
                    <h4>Consistent Dividends</h4>
                    <p>12% annual returns paid monthly, providing steady passive income.</p>
                  </div>
                </div>
                <div className="reason-item">
                  <div className="reason-icon">🌍</div>
                  <div className="reason-content">
                    <h4>Sustainable Operations</h4>
                    <p>Environmentally responsible mining practices across multiple African countries.</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="why-choose-visual">
              <div className="stats-container">
                <div className="stat-circle">
                  <div className="stat-number">5K+</div>
                  <div className="stat-label">Hectares</div>
                </div>
                <div className="stat-circle">
                  <div className="stat-number">200</div>
                  <div className="stat-label">Plants</div>
                </div>
                <div className="stat-circle">
                  <div className="stat-number">12%</div>
                  <div className="stat-label">Returns</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Resources & Contact Section */}
      <section className="resources-section">
        <div className="section-container">
          <div className="resources-content">
            <div className="resource-card">
              <div className="resource-icon">📄</div>
              <h3>Download Company Presentation</h3>
              <p>Get the complete Aureus Alliance Holdings presentation with detailed financial projections, expansion plans, and operational data.</p>
              <a
                href="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/aureus-alliance-presentation.pdf"
                className="btn btn-primary"
                download
              >
                Download Presentation
              </a>
            </div>
            <div className="resource-card">
              <div className="resource-icon">💬</div>
              <h3>Connect with Your Sponsor</h3>
              <p>Have questions? Want to discuss your share purchase strategy? Connect directly with {affiliate.first_name || affiliate.username} for personalized guidance.</p>
              <div className="contact-actions">
                <a href={`mailto:${affiliate.email}`} className="btn btn-secondary">
                  Email {affiliate.first_name || affiliate.username}
                </a>
                {affiliate.phone && (
                  <a href={`tel:${affiliate.phone}`} className="btn btn-secondary">
                    Call {affiliate.first_name || affiliate.username}
                  </a>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Multiple Call to Actions */}
      <section className="cta-section">
        <div className="section-container">
          <div className="cta-content">
            <h2>Ready to Secure Your Financial Future?</h2>
            <p>Join thousands of shareholders who are already earning consistent returns from real gold mining operations.</p>
            <div className="cta-actions">
              <button
                onClick={() => setShowRegistration(true)}
                className="cta-primary"
              >
                Become a Shareholder Today
              </button>
              <button
                onClick={() => document.getElementById('calculator')?.scrollIntoView({ behavior: 'smooth' })}
                className="cta-secondary"
              >
                Calculate Your Returns
              </button>
              <button
                onClick={() => document.getElementById('about')?.scrollIntoView({ behavior: 'smooth' })}
                className="cta-tertiary"
              >
                Learn More About Aureus
              </button>
            </div>
            <div className="cta-info">
              <span>✓ No hidden fees</span>
              <span>✓ Monthly dividends</span>
              <span>✓ Real gold backing</span>
              <span>✓ CIPC registered company</span>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="main-footer">
        <div className="footer-container">
          <div className="footer-content">
            <div className="footer-brand">
              <img
                src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-s.png"
                alt="Aureus Alliance Holdings"
                className="footer-logo"
              />
              <p>Real Gold • Real Shares • Real Ownership</p>
            </div>
            <div className="footer-links">
              <div className="footer-column">
                <h4>Company</h4>
                <a href="#about">About Us</a>
                <a href="#operations">Operations</a>
                <a href="#phases">Investment Phases</a>
              </div>
              <div className="footer-column">
                <h4>Support</h4>
                <a href="#contact">Contact</a>
                <a href="#faq">FAQ</a>
                <a href="#help">Help Center</a>
              </div>
              <div className="footer-column">
                <h4>Legal</h4>
                <a href="#terms">Terms of Service</a>
                <a href="#privacy">Privacy Policy</a>
                <a href="#compliance">Compliance</a>
              </div>
            </div>
          </div>
          <div className="footer-bottom">
            <p>&copy; 2024 Aureus Alliance Holdings (Pty) Ltd. All rights reserved.</p>
            <p>Your Personal Advisor: {affiliate.first_name || affiliate.username}</p>
          </div>
        </div>
      </footer>

      {/* Registration Modal */}
      {showRegistration && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>Join Aureus Alliance Holdings</h3>
              <p className="sponsor-info">
                You are registering under sponsor: <strong>@{affiliate.username}</strong> ({affiliate.first_name || affiliate.username})
              </p>
              <button
                onClick={() => setShowRegistration(false)}
                className="modal-close"
              >
                ×
              </button>
            </div>
            <div className="registration-form-container">
              <div className="sponsor-display">
                <div className="sponsor-info-box">
                  <label>Your Sponsor</label>
                  <div className="sponsor-details">
                    <strong>@{affiliate.username}</strong>
                    <span>({affiliate.first_name || affiliate.username})</span>
                  </div>
                </div>
              </div>
              <EmailRegistrationForm
                onRegistrationSuccess={handleRegistrationSuccess}
                onSwitchToLogin={() => {
                  setShowRegistration(false);
                  window.location.href = '/login';
                }}
                onSwitchToTelegram={() => {}}
              />
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        .professional-landing {
          background: #0a0a0a;
          color: white;
          min-height: 100vh;
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        /* Navigation */
        .main-nav {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          background: rgba(10, 10, 10, 0.95);
          backdrop-filter: blur(20px);
          border-bottom: 1px solid rgba(212, 175, 55, 0.1);
          z-index: 1000;
        }

        .nav-container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 20px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 70px;
        }

        .nav-logo img {
          height: 40px;
        }

        .nav-links {
          display: flex;
          gap: 30px;
        }

        .nav-links a {
          color: #b0b0b0;
          text-decoration: none;
          font-weight: 500;
          transition: color 0.3s ease;
        }

        .nav-links a:hover {
          color: #d4af37;
        }

        .nav-cta {
          background: linear-gradient(135deg, #d4af37 0%, #f4d03f 100%);
          border: none;
          border-radius: 8px;
          color: #0a0a0a;
          padding: 12px 24px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .nav-cta:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
        }

        /* Affiliate Profile Section */
        .affiliate-profile-section {
          padding: 120px 0 80px;
          background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
          border-bottom: 1px solid rgba(212, 175, 55, 0.1);
        }

        .profile-content {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 20px;
        }

        .profile-card {
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(212, 175, 55, 0.2);
          border-radius: 20px;
          padding: 40px;
          display: flex;
          gap: 40px;
          align-items: center;
          backdrop-filter: blur(20px);
        }

        .profile-image {
          flex-shrink: 0;
        }

        .avatar {
          width: 120px;
          height: 120px;
          border-radius: 50%;
          border: 3px solid #d4af37;
          object-fit: cover;
        }

        .profile-info {
          flex: 1;
        }

        .profile-name {
          font-size: 2.5rem;
          font-weight: 800;
          margin-bottom: 8px;
          background: linear-gradient(135deg, #ffffff 0%, #d4af37 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .profile-title {
          font-size: 1.2rem;
          color: #d4af37;
          margin-bottom: 4px;
        }

        .profile-username {
          font-size: 1rem;
          color: #888;
          margin-bottom: 20px;
        }

        .profile-stats {
          display: flex;
          gap: 30px;
          margin-bottom: 20px;
        }

        .stat-item {
          text-align: center;
        }

        .stat-value {
          display: block;
          font-size: 1.8rem;
          font-weight: 700;
          color: #d4af37;
        }

        .stat-label {
          font-size: 0.9rem;
          color: #888;
          margin-top: 4px;
        }

        .profile-description {
          margin-bottom: 30px;
        }

        .profile-description p {
          color: #b0b0b0;
          line-height: 1.6;
        }

        .profile-actions {
          display: flex;
          gap: 15px;
        }

        .btn {
          padding: 12px 24px;
          border-radius: 8px;
          font-weight: 600;
          text-decoration: none;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
          border: none;
          cursor: pointer;
        }

        .btn-primary {
          background: linear-gradient(135deg, #d4af37 0%, #f4d03f 100%);
          color: #0a0a0a;
        }

        .btn-primary:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
        }

        .btn-secondary {
          background: rgba(255, 255, 255, 0.1);
          color: #ffffff;
          border: 1px solid rgba(212, 175, 55, 0.3);
        }

        .btn-secondary:hover {
          background: rgba(212, 175, 55, 0.1);
          border-color: #d4af37;
        }

        /* Hero Section */
        .hero {
          padding: 120px 0 80px;
          background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
          position: relative;
          overflow: hidden;
        }

        .hero::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: radial-gradient(circle at 30% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%);
        }

        .hero-container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 20px;
          position: relative;
          z-index: 2;
        }

        .hero-content {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 60px;
          align-items: center;
        }

        .hero-badge {
          display: inline-flex;
          align-items: center;
          gap: 8px;
          background: rgba(212, 175, 55, 0.1);
          border: 1px solid rgba(212, 175, 55, 0.3);
          border-radius: 50px;
          padding: 8px 16px;
          font-size: 0.85rem;
          font-weight: 600;
          color: #d4af37;
          margin-bottom: 20px;
        }

        .badge-icon {
          font-size: 1rem;
        }

        .hero-title {
          font-size: 3.5rem;
          font-weight: 800;
          line-height: 1.1;
          margin-bottom: 20px;
          background: linear-gradient(135deg, #ffffff 0%, #d4af37 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .hero-subtitle {
          font-size: 1.2rem;
          line-height: 1.6;
          color: #b0b0b0;
          margin-bottom: 40px;
          max-width: 500px;
        }

        .hero-metrics {
          display: flex;
          gap: 30px;
          margin-bottom: 40px;
        }

        .metric {
          text-align: center;
        }

        .metric-number {
          font-size: 2.5rem;
          font-weight: 800;
          color: #d4af37;
          display: block;
        }

        .metric-label {
          font-size: 0.9rem;
          color: #888;
          margin-top: 5px;
        }

        .hero-actions {
          display: flex;
          gap: 20px;
          align-items: center;
        }

        .btn-primary {
          background: linear-gradient(135deg, #d4af37 0%, #f4d03f 100%);
          border: none;
          border-radius: 12px;
          color: #0a0a0a;
          padding: 16px 32px;
          font-size: 1.1rem;
          font-weight: 700;
          cursor: pointer;
          transition: all 0.3s ease;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .btn-primary:hover {
          transform: translateY(-3px);
          box-shadow: 0 12px 30px rgba(212, 175, 55, 0.4);
        }

        .btn-secondary {
          background: transparent;
          border: 2px solid rgba(212, 175, 55, 0.3);
          border-radius: 12px;
          color: #d4af37;
          padding: 14px 28px;
          font-size: 1.1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .btn-secondary:hover {
          border-color: #d4af37;
          background: rgba(212, 175, 55, 0.1);
          transform: translateY(-2px);
        }

        .play-icon {
          font-size: 0.9rem;
        }

        /* Hero Visual */
        .hero-visual {
          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;
        }

        .visual-container {
          position: relative;
          width: 400px;
          height: 400px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .hero-logo {
          width: 300px;
          height: 300px;
          object-fit: contain;
          filter: drop-shadow(0 0 30px rgba(212, 175, 55, 0.3));
          animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-20px); }
        }

        .floating-elements {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          pointer-events: none;
        }

        .floating-element {
          position: absolute;
          width: 40px;
          height: 40px;
          border-radius: 8px;
          animation: floatAround 8s ease-in-out infinite;
        }

        .gold-bar {
          background: linear-gradient(135deg, #d4af37 0%, #f4d03f 100%);
          top: 20%;
          left: 10%;
          animation-delay: 0s;
        }

        .mining-icon {
          background: linear-gradient(135deg, #666 0%, #999 100%);
          top: 70%;
          right: 20%;
          animation-delay: 2s;
        }

        .chart-icon {
          background: linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%);
          bottom: 30%;
          left: 20%;
          animation-delay: 4s;
        }

        @keyframes floatAround {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          25% { transform: translateY(-15px) rotate(90deg); }
          50% { transform: translateY(-10px) rotate(180deg); }
          75% { transform: translateY(-20px) rotate(270deg); }
        }

        /* About Section */
        .about-section {
          padding: 100px 0;
          background: #111111;
        }

        .section-container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 20px;
        }

        .section-header {
          text-align: center;
          margin-bottom: 60px;
        }

        .section-header h2 {
          font-size: 2.5rem;
          font-weight: 700;
          color: #d4af37;
          margin-bottom: 15px;
        }

        .section-header p {
          font-size: 1.2rem;
          color: #b0b0b0;
        }

        .about-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 40px;
        }

        .about-card {
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(212, 175, 55, 0.2);
          border-radius: 16px;
          padding: 40px;
          text-align: center;
          transition: all 0.3s ease;
        }

        .about-card:hover {
          transform: translateY(-10px);
          box-shadow: 0 20px 40px rgba(212, 175, 55, 0.1);
          border-color: #d4af37;
        }

        .card-icon {
          font-size: 3rem;
          margin-bottom: 20px;
        }

        .about-card h3 {
          font-size: 1.5rem;
          font-weight: 600;
          color: #d4af37;
          margin-bottom: 15px;
        }

        .about-card p {
          color: #b0b0b0;
          line-height: 1.6;
        }

        /* Operations Section */
        .operations-section {
          padding: 100px 0;
          background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
        }

        .operations-content {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 60px;
          align-items: center;
        }

        .operations-text h2 {
          font-size: 2.5rem;
          font-weight: 700;
          color: #d4af37;
          margin-bottom: 20px;
        }

        .operations-text p {
          font-size: 1.2rem;
          color: #b0b0b0;
          line-height: 1.6;
          margin-bottom: 30px;
        }

        .operations-features {
          list-style: none;
          padding: 0;
        }

        .operations-features li {
          color: #b0b0b0;
          margin-bottom: 10px;
          font-size: 1.1rem;
        }

        .video-container {
          position: relative;
          border-radius: 16px;
          overflow: hidden;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .operations-video-player {
          width: 100%;
          height: 300px;
          object-fit: cover;
          border-radius: 16px;
        }

        .video-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.4);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 1;
          transition: opacity 0.3s ease;
          border-radius: 16px;
        }

        .video-container:hover .video-overlay {
          opacity: 1;
        }

        .video-overlay.hidden {
          display: none;
        }

        .play-button {
          width: 80px;
          height: 80px;
          background: rgba(212, 175, 55, 0.95);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #0a0a0a;
          font-size: 2rem;
          cursor: pointer;
          transition: all 0.3s ease;
          box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
        }

        .play-button:hover {
          transform: scale(1.1);
          background: #d4af37;
          box-shadow: 0 12px 35px rgba(212, 175, 55, 0.4);
        }

        .play-button span {
          margin-left: 4px; /* Optical centering for play icon */
        }

        /* Calculator Section */
        .calculator-section {
          padding: 100px 0;
          background: #111111;
        }

        /* Investment Phases */
        .phases-section {
          padding: 100px 0;
          background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
        }

        .phases-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 30px;
          margin-top: 60px;
        }

        .phase-card {
          background: rgba(255, 255, 255, 0.05);
          border: 2px solid rgba(212, 175, 55, 0.2);
          border-radius: 20px;
          padding: 40px;
          text-align: center;
          position: relative;
          transition: all 0.3s ease;
        }

        .phase-card:hover {
          transform: translateY(-10px);
          box-shadow: 0 25px 50px rgba(212, 175, 55, 0.15);
          border-color: #d4af37;
        }

        .phase-card.featured {
          border-color: #d4af37;
          background: rgba(212, 175, 55, 0.1);
          transform: scale(1.05);
        }

        .phase-badge {
          position: absolute;
          top: -10px;
          left: 50%;
          transform: translateX(-50%);
          background: linear-gradient(135deg, #d4af37 0%, #f4d03f 100%);
          color: #0a0a0a;
          padding: 8px 20px;
          border-radius: 20px;
          font-size: 0.8rem;
          font-weight: 700;
          letter-spacing: 1px;
        }

        .phase-number {
          font-size: 4rem;
          font-weight: 800;
          color: rgba(212, 175, 55, 0.3);
          margin-bottom: 10px;
        }

        .phase-card h3 {
          font-size: 1.8rem;
          font-weight: 700;
          color: #d4af37;
          margin-bottom: 20px;
        }

        .phase-price {
          font-size: 2.5rem;
          font-weight: 800;
          color: white;
          margin-bottom: 30px;
        }

        .phase-price span {
          font-size: 1rem;
          color: #888;
          font-weight: 400;
        }

        .phase-features {
          list-style: none;
          padding: 0;
          margin-bottom: 30px;
        }

        .phase-features li {
          color: #b0b0b0;
          margin-bottom: 12px;
          font-size: 1rem;
          text-align: left;
        }

        .phase-btn {
          background: linear-gradient(135deg, #d4af37 0%, #f4d03f 100%);
          border: none;
          border-radius: 12px;
          color: #0a0a0a;
          padding: 16px 32px;
          font-size: 1.1rem;
          font-weight: 700;
          cursor: pointer;
          transition: all 0.3s ease;
          width: 100%;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .phase-btn:hover:not(.disabled) {
          transform: translateY(-2px);
          box-shadow: 0 10px 25px rgba(212, 175, 55, 0.3);
        }

        .phase-btn.disabled {
          background: rgba(255, 255, 255, 0.1);
          color: #666;
          cursor: not-allowed;
        }

        /* Why Choose Us */
        .why-choose-section {
          padding: 100px 0;
          background: #111111;
        }

        .why-choose-content {
          display: grid;
          grid-template-columns: 2fr 1fr;
          gap: 60px;
          align-items: center;
        }

        .why-choose-text h2 {
          font-size: 2.5rem;
          font-weight: 700;
          color: #d4af37;
          margin-bottom: 40px;
        }

        .reasons-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 30px;
        }

        .reason-item {
          display: flex;
          gap: 20px;
          align-items: flex-start;
        }

        .reason-icon {
          font-size: 2.5rem;
          flex-shrink: 0;
        }

        .reason-content h4 {
          color: #d4af37;
          font-size: 1.2rem;
          font-weight: 600;
          margin-bottom: 8px;
        }

        .reason-content p {
          color: #b0b0b0;
          line-height: 1.5;
        }

        .stats-container {
          display: flex;
          flex-direction: column;
          gap: 30px;
          align-items: center;
        }

        .stat-circle {
          width: 120px;
          height: 120px;
          border: 3px solid #d4af37;
          border-radius: 50%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          background: rgba(212, 175, 55, 0.1);
          transition: all 0.3s ease;
        }

        .stat-circle:hover {
          transform: scale(1.1);
          box-shadow: 0 0 30px rgba(212, 175, 55, 0.3);
        }

        .stat-circle .stat-number {
          font-size: 1.8rem;
          font-weight: 800;
          color: #d4af37;
        }

        .stat-circle .stat-label {
          font-size: 0.9rem;
          color: #888;
          margin-top: 5px;
        }

        /* Call to Action */
        .cta-section {
          padding: 100px 0;
          background: linear-gradient(135deg, #d4af37 0%, #f4d03f 100%);
          text-align: center;
        }

        .cta-content h2 {
          font-size: 3rem;
          font-weight: 800;
          color: #0a0a0a;
          margin-bottom: 20px;
        }

        .cta-content p {
          font-size: 1.3rem;
          color: rgba(10, 10, 10, 0.8);
          margin-bottom: 40px;
          max-width: 600px;
          margin-left: auto;
          margin-right: auto;
        }

        .cta-actions {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 20px;
        }

        .cta-primary {
          background: #0a0a0a;
          border: none;
          border-radius: 12px;
          color: #d4af37;
          padding: 20px 40px;
          font-size: 1.3rem;
          font-weight: 700;
          cursor: pointer;
          transition: all 0.3s ease;
          text-transform: uppercase;
          letter-spacing: 1px;
        }

        .cta-primary:hover {
          transform: translateY(-3px);
          box-shadow: 0 15px 35px rgba(10, 10, 10, 0.3);
        }

        .cta-info {
          display: flex;
          gap: 30px;
          flex-wrap: wrap;
          justify-content: center;
        }

        .cta-info span {
          color: rgba(10, 10, 10, 0.7);
          font-weight: 600;
          font-size: 1rem;
        }

        /* Footer */
        .main-footer {
          background: #0a0a0a;
          border-top: 1px solid rgba(212, 175, 55, 0.2);
          padding: 60px 0 30px;
        }

        .footer-container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 20px;
        }

        .footer-content {
          display: grid;
          grid-template-columns: 2fr 1fr 1fr 1fr;
          gap: 40px;
          margin-bottom: 40px;
        }

        .footer-brand {
          display: flex;
          flex-direction: column;
          gap: 15px;
        }

        .footer-logo {
          height: 50px;
          width: auto;
        }

        .footer-brand p {
          color: #b0b0b0;
          font-size: 1.1rem;
          font-weight: 500;
        }

        .footer-column h4 {
          color: #d4af37;
          font-size: 1.1rem;
          font-weight: 600;
          margin-bottom: 15px;
        }

        .footer-column a {
          display: block;
          color: #888;
          text-decoration: none;
          margin-bottom: 8px;
          transition: color 0.3s ease;
        }

        .footer-column a:hover {
          color: #d4af37;
        }

        .footer-bottom {
          border-top: 1px solid rgba(212, 175, 55, 0.1);
          padding-top: 30px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-wrap: wrap;
          gap: 20px;
        }

        .footer-bottom p {
          color: #666;
          margin: 0;
        }

        /* Loading and Error States */
        .loading-screen,
        .error-screen {
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #0a0a0a;
          color: white;
        }

        .loading-content,
        .error-content {
          text-align: center;
        }

        .loading-spinner {
          width: 50px;
          height: 50px;
          border: 3px solid rgba(212, 175, 55, 0.3);
          border-top: 3px solid #d4af37;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 20px;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        /* Modal Styles */
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 2000;
        }

        .modal-content {
          background: #1a1a2e;
          border-radius: 16px;
          padding: 40px;
          max-width: 500px;
          width: 90%;
          max-height: 90vh;
          overflow-y: auto;
          border: 1px solid rgba(212, 175, 55, 0.2);
        }

        .modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 30px;
        }

        .modal-header h3 {
          color: #d4af37;
          font-size: 1.5rem;
          margin: 0;
        }

        .sponsor-info {
          color: #b0b0b0;
          font-size: 0.9rem;
          margin-top: 8px;
        }

        .sponsor-display {
          margin-bottom: 20px;
        }

        .sponsor-info-box {
          background: rgba(212, 175, 55, 0.1);
          border: 1px solid rgba(212, 175, 55, 0.3);
          border-radius: 8px;
          padding: 15px;
        }

        .sponsor-info-box label {
          color: #d4af37;
          font-weight: 600;
          font-size: 0.9rem;
          display: block;
          margin-bottom: 8px;
        }

        .sponsor-details {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .sponsor-details strong {
          color: #ffffff;
          font-size: 1.1rem;
        }

        .sponsor-details span {
          color: #b0b0b0;
          font-size: 0.9rem;
        }

        /* Resources Section */
        .resources-section {
          padding: 80px 0;
          background: rgba(255, 255, 255, 0.02);
        }

        .resources-content {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 40px;
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 20px;
        }

        .resource-card {
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(212, 175, 55, 0.2);
          border-radius: 16px;
          padding: 30px;
          text-align: center;
        }

        .resource-icon {
          font-size: 3rem;
          margin-bottom: 20px;
        }

        .resource-card h3 {
          color: #d4af37;
          font-size: 1.5rem;
          margin-bottom: 15px;
        }

        .resource-card p {
          color: #b0b0b0;
          line-height: 1.6;
          margin-bottom: 25px;
        }

        .contact-actions {
          display: flex;
          gap: 15px;
          justify-content: center;
        }

        /* CTA Section Updates */
        .cta-actions {
          display: flex;
          gap: 20px;
          justify-content: center;
          margin-bottom: 30px;
          flex-wrap: wrap;
        }

        .cta-primary, .cta-secondary, .cta-tertiary {
          padding: 16px 32px;
          border-radius: 12px;
          font-weight: 600;
          text-decoration: none;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
          border: none;
          cursor: pointer;
          font-size: 1.1rem;
        }

        .cta-primary {
          background: linear-gradient(135deg, #d4af37 0%, #f4d03f 100%);
          color: #0a0a0a;
        }

        .cta-secondary {
          background: rgba(255, 255, 255, 0.1);
          color: #ffffff;
          border: 2px solid rgba(212, 175, 55, 0.3);
        }

        .cta-tertiary {
          background: transparent;
          color: #d4af37;
          border: 2px solid #d4af37;
        }

        .cta-primary:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
        }

        .cta-secondary:hover {
          background: rgba(212, 175, 55, 0.1);
          border-color: #d4af37;
        }

        .cta-tertiary:hover {
          background: rgba(212, 175, 55, 0.1);
        }

        .modal-close {
          background: none;
          border: none;
          color: #888;
          font-size: 2rem;
          cursor: pointer;
          padding: 0;
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .modal-close:hover {
          color: #d4af37;
        }

        .registration-form {
          display: flex;
          flex-direction: column;
          gap: 20px;
        }

        .form-group {
          display: flex;
          flex-direction: column;
        }

        .form-group label {
          color: #d4af37;
          font-weight: 600;
          margin-bottom: 8px;
        }

        .form-group input {
          background: rgba(255, 255, 255, 0.1);
          border: 2px solid rgba(212, 175, 55, 0.3);
          border-radius: 8px;
          padding: 12px 16px;
          color: white;
          font-size: 1rem;
          transition: border-color 0.3s ease;
        }

        .form-group input:focus {
          outline: none;
          border-color: #d4af37;
        }

        .submit-btn {
          background: linear-gradient(135deg, #d4af37 0%, #f4d03f 100%);
          border: none;
          border-radius: 8px;
          color: #0a0a0a;
          padding: 16px;
          font-size: 1.1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          margin-top: 10px;
        }

        .submit-btn:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
        }

        .submit-btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
          .nav-links {
            display: none;
          }

          .nav-container {
            padding: 0 15px;
          }

          .hero {
            padding: 100px 0 60px;
          }

          .hero-content,
          .operations-content,
          .why-choose-content {
            grid-template-columns: 1fr;
            gap: 40px;
          }

          .hero-title {
            font-size: 2.5rem;
          }

          .hero-metrics {
            justify-content: center;
            gap: 20px;
          }

          .hero-actions {
            flex-direction: column;
            align-items: stretch;
            gap: 15px;
          }

          .about-grid,
          .phases-grid {
            grid-template-columns: 1fr;
          }

          .visual-container {
            width: 300px;
            height: 300px;
          }

          .hero-logo {
            width: 200px;
            height: 200px;
          }

          .section-header h2 {
            font-size: 2rem;
          }

          .operations-text h2 {
            font-size: 2rem;
          }

          .reasons-grid {
            grid-template-columns: 1fr;
            gap: 20px;
          }

          .stats-container {
            flex-direction: row;
            justify-content: center;
            gap: 20px;
          }

          .stat-circle {
            width: 100px;
            height: 100px;
          }

          .stat-circle .stat-number {
            font-size: 1.5rem;
          }

          .cta-content h2 {
            font-size: 2.2rem;
          }

          .cta-info {
            gap: 15px;
          }

          .footer-content {
            grid-template-columns: 1fr;
            gap: 30px;
            text-align: center;
          }

          .footer-bottom {
            flex-direction: column;
            text-align: center;
          }

          .modal-content {
            padding: 30px 20px;
            margin: 20px;
          }

          .operations-video-player {
            height: 250px;
          }

          .play-button {
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
          }
        }

        @media (max-width: 480px) {
          .hero-title {
            font-size: 2rem;
          }

          .hero-metrics {
            flex-direction: column;
            gap: 15px;
          }

          .metric-number {
            font-size: 2rem;
          }

          .btn-primary,
          .btn-secondary {
            padding: 14px 24px;
            font-size: 1rem;
          }

          .about-card,
          .phase-card {
            padding: 30px 20px;
          }

          .section-header h2 {
            font-size: 1.8rem;
          }

          .operations-video-player {
            height: 200px;
          }
        }
      `}</style>
    </div>
  );
};

export default ProfessionalLandingPage;
