/**
 * API endpoint for sending marketing emails
 * Uses Resend service on the server side for security (same as 2FA PIN emails)
 */

import { createClient } from '@supabase/supabase-js';
import { Resend } from 'resend';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client with service role (exact same pattern as working API)
const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Initialize Resend client (exact same pattern as working API)
const RESEND_API_KEY = process.env.RESEND_API_KEY || process.env.VITE_RESEND_API_KEY;
const RESEND_FROM_EMAIL = process.env.RESEND_FROM_EMAIL || process.env.VITE_RESEND_FROM_EMAIL || '<EMAIL>';
const RESEND_FROM_NAME = process.env.RESEND_FROM_NAME || process.env.VITE_RESEND_FROM_NAME || 'Aureus Alliance Holdings';

let resend = null;
if (RESEND_API_KEY) {
  resend = new Resend(RESEND_API_KEY);
  console.log('✅ Server-side Resend email service initialized for marketing emails');
} else {
  console.warn('⚠️ RESEND_API_KEY not found - marketing email service disabled');
}

export default async function handler(req, res) {
  // Set CORS headers (same as working API)
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  // Check if Resend is configured
  if (!resend) {
    console.error('❌ Resend service not configured');
    return res.status(500).json({
      success: false,
      message: 'Email service not configured'
    });
  }

  try {
    const { 
      to, 
      subject, 
      htmlContent, 
      textContent,
      userId,
      templateId,
      campaignId 
    } = req.body;

    // Validate required fields
    if (!to || !subject || !htmlContent) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: to, subject, htmlContent'
      });
    }

    console.log(`📧 Sending marketing email to ${to} with subject: ${subject}`);

    // Send email via Resend
    const emailData = {
      from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
      to: [to],
      subject: subject,
      html: htmlContent
    };

    // Add text content if provided
    if (textContent) {
      emailData.text = textContent;
    }

    const result = await resend.emails.send(emailData);

    if (result.error) {
      console.error('❌ Resend API error:', result.error);
      return res.status(500).json({
        success: false,
        message: result.error.message || 'Failed to send email',
        error: result.error.message
      });
    }

    console.log('✅ Marketing email sent successfully:', result.data?.id);

    // Update campaign statistics if campaignId provided
    if (campaignId) {
      try {
        const { error: updateError } = await supabase
          .from('email_campaigns')
          .update({ 
            sent_count: supabase.raw('sent_count + 1'),
            delivered_count: supabase.raw('delivered_count + 1'),
            updated_at: new Date().toISOString()
          })
          .eq('id', campaignId);

        if (updateError) {
          console.warn('⚠️ Failed to update campaign statistics:', updateError);
        }
      } catch (statsError) {
        console.warn('⚠️ Error updating campaign stats:', statsError);
      }
    }

    return res.status(200).json({ 
      success: true, 
      messageId: result.data?.id,
      message: 'Email sent successfully' 
    });

  } catch (error) {
    console.error('❌ Error sending marketing email:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to send email',
      error: error.message
    });
  }
}
