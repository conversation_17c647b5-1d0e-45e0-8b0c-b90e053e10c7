import React, { useState, useEffect, useCallback } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';
import { SendMessageModal } from './SendMessageModal';
import { MessageTemplatesModal } from './MessageTemplatesModal';

interface Message {
  id: string;
  sender_user_id: number;
  recipient_user_id: number;
  subject: string;
  message_content: string;
  is_read: boolean;
  created_at: string;
  updated_at: string;
  sender?: {
    first_name: string;
    last_name: string;
    username: string;
  };
  recipient?: {
    first_name: string;
    last_name: string;
    username: string;
  };
}

interface MessageDashboardProps {
  userId: number;
  className?: string;
}

export const MessageDashboard: React.FC<MessageDashboardProps> = ({ userId, className = '' }) => {
  const [activeTab, setActiveTab] = useState<'inbox' | 'sent'>('inbox');
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [unreadCount, setUnreadCount] = useState(0);
  const [replyModalOpen, setReplyModalOpen] = useState(false);
  const [sortOrder, setSortOrder] = useState<'desc' | 'asc'>('desc');
  const [selectedMessages, setSelectedMessages] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState<'all' | 'important' | 'system' | 'personal'>('all');
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [templatesModalOpen, setTemplatesModalOpen] = useState(false);

  const MESSAGES_PER_PAGE = 20;

  const loadMessages = useCallback(async () => {
    setLoading(true);
    try {
      const serviceClient = getServiceRoleClient();
      const offset = (currentPage - 1) * MESSAGES_PER_PAGE;

      let query = serviceClient
        .from('internal_messages')
        .select(`
          *,
          sender:sender_user_id(first_name, last_name, username),
          recipient:recipient_user_id(first_name, last_name, username)
        `)
        .order('created_at', { ascending: sortOrder === 'asc' })
        .range(offset, offset + MESSAGES_PER_PAGE - 1);

      if (activeTab === 'inbox') {
        query = query.eq('recipient_user_id', userId);
      } else {
        query = query.eq('sender_user_id', userId);
      }

      // Apply search filter
      if (searchQuery.trim()) {
        query = query.or(`subject.ilike.%${searchQuery}%,message_content.ilike.%${searchQuery}%`);
      }

      // Apply category filter
      if (filterCategory !== 'all') {
        switch (filterCategory) {
          case 'important':
            query = query.or('subject.ilike.%urgent%,subject.ilike.%important%,subject.ilike.%priority%');
            break;
          case 'system':
            query = query.or('subject.ilike.%system%,subject.ilike.%notification%,subject.ilike.%alert%');
            break;
          case 'personal':
            query = query.not('subject.ilike.%system%').not('subject.ilike.%notification%').not('subject.ilike.%alert%');
            break;
        }
      }

      const { data, error, count } = await query;

      if (error) throw error;

      setMessages(data || []);
      setTotalPages(Math.ceil((count || 0) / MESSAGES_PER_PAGE));

      // Load unread count for inbox
      if (activeTab === 'inbox') {
        const { count: unreadCountData } = await serviceClient
          .from('internal_messages')
          .select('*', { count: 'exact', head: true })
          .eq('recipient_user_id', userId)
          .eq('is_read', false);
        
        setUnreadCount(unreadCountData || 0);
      }

    } catch (error) {
      console.error('Error loading messages:', error);
    } finally {
      setLoading(false);
    }
  }, [userId, activeTab, currentPage, sortOrder, searchQuery, filterCategory]);

  useEffect(() => {
    loadMessages();
  }, [loadMessages]);

  // Auto-refresh unread count every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      if (activeTab === 'inbox') {
        loadMessages();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [activeTab, loadMessages]);

  const markAsRead = async (messageId: string) => {
    try {
      const serviceClient = getServiceRoleClient();
      await serviceClient
        .from('internal_messages')
        .update({ is_read: true })
        .eq('id', messageId);
      
      loadMessages();
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  };

  const markAsUnread = async (messageId: string) => {
    try {
      const serviceClient = getServiceRoleClient();
      await serviceClient
        .from('internal_messages')
        .update({ is_read: false })
        .eq('id', messageId);
      
      loadMessages();
    } catch (error) {
      console.error('Error marking message as unread:', error);
    }
  };

  const deleteMessage = async (messageId: string) => {
    if (!confirm('Are you sure you want to delete this message? This action cannot be undone.')) {
      return;
    }

    try {
      const serviceClient = getServiceRoleClient();
      await serviceClient
        .from('internal_messages')
        .delete()
        .eq('id', messageId);
      
      setSelectedMessage(null);
      loadMessages();
    } catch (error) {
      console.error('Error deleting message:', error);
    }
  };

  const handleBulkMarkRead = async () => {
    if (selectedMessages.size === 0) return;

    try {
      const serviceClient = getServiceRoleClient();
      await serviceClient
        .from('internal_messages')
        .update({ is_read: true })
        .in('id', Array.from(selectedMessages));
      
      setSelectedMessages(new Set());
      loadMessages();
    } catch (error) {
      console.error('Error bulk marking as read:', error);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedMessages.size === 0) return;
    
    if (!confirm(`Are you sure you want to delete ${selectedMessages.size} messages? This action cannot be undone.`)) {
      return;
    }

    try {
      const serviceClient = getServiceRoleClient();
      await serviceClient
        .from('internal_messages')
        .delete()
        .in('id', Array.from(selectedMessages));
      
      setSelectedMessages(new Set());
      loadMessages();
    } catch (error) {
      console.error('Error bulk deleting messages:', error);
    }
  };

  const toggleMessageSelection = (messageId: string) => {
    const newSelected = new Set(selectedMessages);
    if (newSelected.has(messageId)) {
      newSelected.delete(messageId);
    } else {
      newSelected.add(messageId);
    }
    setSelectedMessages(newSelected);
  };

  const formatRelativeTime = (timestamp: string) => {
    const now = new Date();
    const messageTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - messageTime.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    if (diffInMinutes < 10080) return `${Math.floor(diffInMinutes / 1440)}d ago`;
    return messageTime.toLocaleDateString();
  };

  const truncateMessage = (content: string, maxLength: number = 100) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  const openMessageThread = (message: Message) => {
    setSelectedMessage(message);
    if (activeTab === 'inbox' && !message.is_read) {
      markAsRead(message.id);
    }
  };

  const openReplyModal = () => {
    setReplyModalOpen(true);
  };

  if (loading) {
    return (
      <div className={`${className} flex items-center justify-center p-8`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-3 text-gray-400">Loading messages...</span>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-white text-2xl font-bold">Messages</h2>
          <p className="text-gray-400">Manage your internal communications</p>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-4 mb-6">
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex items-center space-x-4">
            <h3 className="text-white font-medium">Quick Actions</h3>
            <button
              onClick={() => setTemplatesModalOpen(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-sm flex items-center space-x-2 transition-colors"
            >
              <span>Message Templates</span>
            </button>
            <button
              onClick={() => setReplyModalOpen(true)}
              className="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg text-sm flex items-center space-x-2 transition-colors"
            >
              <span>✉️</span>
              <span>Compose New</span>
            </button>
          </div>

          <div className="flex items-center space-x-2 text-sm text-gray-400">
            <span>Total: {messages.length}</span>
            {activeTab === 'inbox' && unreadCount > 0 && (
              <>
                <span>•</span>
                <span className="text-red-400">Unread: {unreadCount}</span>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex bg-gray-700 rounded-lg p-1 mb-6">
        <button
          onClick={() => {
            setActiveTab('inbox');
            setCurrentPage(1);
            setSelectedMessages(new Set());
          }}
          className={`flex-1 px-4 py-2 rounded text-sm font-medium transition-colors ${
            activeTab === 'inbox' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'
          }`}
        >
          Inbox {unreadCount > 0 && <span className="ml-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full">{unreadCount}</span>}
        </button>
        <button
          onClick={() => {
            setActiveTab('sent');
            setCurrentPage(1);
            setSelectedMessages(new Set());
          }}
          className={`flex-1 px-4 py-2 rounded text-sm font-medium transition-colors ${
            activeTab === 'sent' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'
          }`}
        >
          Sent
        </button>
      </div>

      {/* Search and Filter Controls */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-4 mb-6">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search Bar */}
          <div className="flex-1">
            <div className="relative">
              <input
                type="text"
                placeholder="Search messages by subject or content..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 pl-10 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span className="text-gray-400">🔍</span>
              </div>
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery('')}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-white"
                >
                  ✕
                </button>
              )}
            </div>
          </div>

          {/* Category Filter */}
          <div className="flex items-center space-x-4">
            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value as any)}
              className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Messages</option>
              <option value="important">Important</option>
              <option value="system">System</option>
              <option value="personal">Personal</option>
            </select>

            {/* Sort Order */}
            <button
              onClick={() => setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc')}
              className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
              title={`Sort ${sortOrder === 'desc' ? 'oldest first' : 'newest first'}`}
            >
              {sortOrder === 'desc' ? '📅 Newest' : '📅 Oldest'}
            </button>

            {/* Advanced Filters Toggle */}
            <button
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              🔧 {showAdvancedFilters ? 'Hide' : 'Show'} Filters
            </button>
          </div>
        </div>

        {/* Advanced Filters */}
        {showAdvancedFilters && (
          <div className="mt-4 pt-4 border-t border-gray-700">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Read Status</label>
                <select className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm">
                  <option value="all">All Messages</option>
                  <option value="unread">Unread Only</option>
                  <option value="read">Read Only</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Date Range</label>
                <select className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm">
                  <option value="all">All Time</option>
                  <option value="today">Today</option>
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Priority</label>
                <select className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm">
                  <option value="all">All Priorities</option>
                  <option value="high">High Priority</option>
                  <option value="normal">Normal Priority</option>
                  <option value="low">Low Priority</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Message Statistics */}
      {(searchQuery || filterCategory !== 'all') && (
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-4 mb-4">
          <div className="flex items-center justify-between text-sm text-gray-300">
            <div className="flex items-center space-x-4">
              <span>Showing {messages.length} messages</span>
              {searchQuery && (
                <span className="bg-blue-600/20 text-blue-400 px-2 py-1 rounded">
                  Search: "{searchQuery}"
                </span>
              )}
              {filterCategory !== 'all' && (
                <span className="bg-green-600/20 text-green-400 px-2 py-1 rounded">
                  Category: {filterCategory}
                </span>
              )}
            </div>
            <button
              onClick={() => {
                setSearchQuery('');
                setFilterCategory('all');
              }}
              className="text-gray-400 hover:text-white text-xs"
            >
              Clear Filters
            </button>
          </div>
        </div>
      )}

      {selectedMessage ? (
        // Message Thread View
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={() => setSelectedMessage(null)}
              className="text-blue-400 hover:text-blue-300 flex items-center"
            >
              ← Back to {activeTab === 'inbox' ? 'Inbox' : 'Sent'}
            </button>
            <div className="flex space-x-2">
              {activeTab === 'inbox' && (
                <>
                  <button
                    onClick={() => selectedMessage.is_read ? markAsUnread(selectedMessage.id) : markAsRead(selectedMessage.id)}
                    className="text-yellow-400 hover:text-yellow-300 text-sm"
                  >
                    {selectedMessage.is_read ? '📖 Mark Unread' : '📗 Mark Read'}
                  </button>
                  <button
                    onClick={openReplyModal}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm"
                  >
                    💬 Reply
                  </button>
                </>
              )}
              <button
                onClick={() => deleteMessage(selectedMessage.id)}
                className="text-red-400 hover:text-red-300 text-sm"
              >
                🗑️ Delete
              </button>
            </div>
          </div>

          <div className="border-b border-gray-600 pb-4 mb-4">
            <h3 className="text-white text-xl font-semibold mb-2">{selectedMessage.subject}</h3>
            <div className="flex items-center justify-between text-sm text-gray-400">
              <div>
                {activeTab === 'inbox' ? (
                  <span>From: {selectedMessage.sender?.first_name} {selectedMessage.sender?.last_name} (@{selectedMessage.sender?.username})</span>
                ) : (
                  <span>To: {selectedMessage.recipient?.first_name} {selectedMessage.recipient?.last_name} (@{selectedMessage.recipient?.username})</span>
                )}
              </div>
              <div>{new Date(selectedMessage.created_at).toLocaleString()}</div>
            </div>
          </div>

          <div className="text-white whitespace-pre-wrap leading-relaxed">
            {selectedMessage.message_content}
          </div>
        </div>
      ) : (
        // Message List View
        <>
          {/* Controls */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4">
              {selectedMessages.size > 0 && (
                <div className="flex items-center space-x-2">
                  <span className="text-gray-400 text-sm">{selectedMessages.size} selected</span>
                  {activeTab === 'inbox' && (
                    <button
                      onClick={handleBulkMarkRead}
                      className="text-blue-400 hover:text-blue-300 text-sm"
                    >
                      📗 Mark Read
                    </button>
                  )}
                  <button
                    onClick={handleBulkDelete}
                    className="text-red-400 hover:text-red-300 text-sm"
                  >
                    🗑️ Delete
                  </button>
                </div>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <select
                value={sortOrder}
                onChange={(e) => setSortOrder(e.target.value as 'desc' | 'asc')}
                className="bg-gray-700 border border-gray-600 rounded px-3 py-1 text-white text-sm"
              >
                <option value="desc">Newest First</option>
                <option value="asc">Oldest First</option>
              </select>
            </div>
          </div>

          {/* Message List */}
          <div className="bg-gray-800 rounded-lg border border-gray-700">
            {messages.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-4xl mb-4">📭</div>
                <h3 className="text-white text-lg mb-2">No Messages</h3>
                <p className="text-gray-400">
                  {activeTab === 'inbox' ? "You haven't received any messages yet." : "You haven't sent any messages yet."}
                </p>
              </div>
            ) : (
              <div className="divide-y divide-gray-700">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`p-4 hover:bg-gray-700/50 cursor-pointer transition-colors ${
                      activeTab === 'inbox' && !message.is_read ? 'bg-blue-600/10 border-l-4 border-l-blue-500' : ''
                    }`}
                    onClick={() => openMessageThread(message)}
                  >
                    <div className="flex items-start space-x-3">
                      <input
                        type="checkbox"
                        checked={selectedMessages.has(message.id)}
                        onChange={(e) => {
                          e.stopPropagation();
                          toggleMessageSelection(message.id);
                        }}
                        className="mt-1"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <div className="flex items-center space-x-2">
                            {/* Priority Indicator */}
                            {(message.subject.toLowerCase().includes('urgent') ||
                              message.subject.toLowerCase().includes('important') ||
                              message.subject.toLowerCase().includes('priority')) && (
                              <span className="text-red-400 text-sm" title="High Priority">🔴</span>
                            )}
                            {(message.subject.toLowerCase().includes('system') ||
                              message.subject.toLowerCase().includes('notification')) && (
                              <span className="text-blue-400 text-sm" title="System Message">⚙️</span>
                            )}

                            <span className={`text-white ${activeTab === 'inbox' && !message.is_read ? 'font-bold' : ''}`}>
                              {activeTab === 'inbox'
                                ? `${message.sender?.first_name} ${message.sender?.last_name} (@${message.sender?.username})`
                                : `${message.recipient?.first_name} ${message.recipient?.last_name} (@${message.recipient?.username})`
                              }
                            </span>
                            {activeTab === 'inbox' && !message.is_read && (
                              <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">New</span>
                            )}
                          </div>
                          <span className="text-gray-400 text-sm">{formatRelativeTime(message.created_at)}</span>
                        </div>
                        <div className={`text-gray-300 mb-1 ${activeTab === 'inbox' && !message.is_read ? 'font-semibold' : ''}`}>
                          {message.subject}
                        </div>
                        <div className="text-gray-400 text-sm">
                          {truncateMessage(message.message_content)}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-gray-400 text-sm">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 bg-gray-700 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600"
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 bg-gray-700 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600"
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </>
      )}

      {/* Reply Modal */}
      {selectedMessage && (
        <SendMessageModal
          isOpen={replyModalOpen}
          onClose={() => setReplyModalOpen(false)}
          recipientId={activeTab === 'inbox' ? selectedMessage.sender_user_id : selectedMessage.recipient_user_id}
          recipientName={activeTab === 'inbox' 
            ? `${selectedMessage.sender?.first_name} ${selectedMessage.sender?.last_name} (@${selectedMessage.sender?.username})`
            : `${selectedMessage.recipient?.first_name} ${selectedMessage.recipient?.last_name} (@${selectedMessage.recipient?.username})`
          }
          senderId={userId}
          defaultSubject={selectedMessage.subject.startsWith('Re: ') ? selectedMessage.subject : `Re: ${selectedMessage.subject}`}
        />
      )}

      {/* Message Templates Modal */}
      <MessageTemplatesModal
        isOpen={templatesModalOpen}
        onClose={() => setTemplatesModalOpen(false)}
        onSelectTemplate={(template) => {
          // For now, just show an alert with the template
          // In a full implementation, this would open the compose modal with the template
          alert(`Selected template: ${template.name}\n\nSubject: ${template.subject}\n\nContent: ${template.content}`);
        }}
      />
    </div>
  );
};
