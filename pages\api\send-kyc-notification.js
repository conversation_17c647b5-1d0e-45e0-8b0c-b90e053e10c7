/**
 * API endpoint for sending KYC status notification emails
 * Uses Resend service on the server side for security
 */

import { Resend } from 'resend';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const RESEND_API_KEY = process.env.RESEND_API_KEY || process.env.VITE_RESEND_API_KEY;
const RESEND_FROM_EMAIL = process.env.RESEND_FROM_EMAIL || process.env.VITE_RESEND_FROM_EMAIL || '<EMAIL>';
const RESEND_FROM_NAME = process.env.RESEND_FROM_NAME || process.env.VITE_RESEND_FROM_NAME || 'Aureus Alliance Holdings';

// Initialize Resend client
let resend = null;

try {
  if (RESEND_API_KEY) {
    resend = new Resend(RESEND_API_KEY);
    console.log('✅ Server-side Resend email service initialized for KYC notifications');
  } else {
    console.warn('⚠️ RESEND_API_KEY not found - KYC email service disabled');
  }
} catch (error) {
  console.error('❌ Failed to initialize server-side Resend service for KYC:', error);
}

/**
 * Generate HTML email content for KYC notifications
 */
function generateKYCEmailHTML(emailData) {
  const isApproved = emailData.status === 'approved';
  const statusColor = isApproved ? '#10B981' : '#EF4444';
  const statusIcon = isApproved ? '🎉' : '❌';
  const statusText = isApproved ? 'APPROVED' : 'REQUIRES UPDATE';

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>KYC Verification ${statusText}</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8fafc;">
      <div style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #1f2937 0%, #374151 100%); padding: 40px 30px; text-align: center;">
          <img src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-s.png" alt="Aureus Alliance" style="height: 60px; margin-bottom: 20px;">
          <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">KYC Verification ${statusText}</h1>
        </div>

        <!-- Content -->
        <div style="padding: 40px 30px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <div style="display: inline-block; background-color: ${statusColor}; color: white; padding: 12px 24px; border-radius: 25px; font-weight: bold; font-size: 16px;">
              ${statusIcon} ${statusText}
            </div>
          </div>

          <p style="font-size: 16px; color: #374151; margin-bottom: 20px;">Dear ${emailData.fullName},</p>

          ${isApproved ? `
            <p style="font-size: 16px; color: #374151; line-height: 1.6; margin-bottom: 20px;">
              Congratulations! Your KYC (Know Your Customer) verification has been <strong style="color: ${statusColor};">approved</strong>. 
              You now have full access to all Aureus Alliance Holdings platform features.
            </p>
          ` : `
            <p style="font-size: 16px; color: #374151; line-height: 1.6; margin-bottom: 20px;">
              Your KYC (Know Your Customer) verification requires some updates. Please review the information below and resubmit your documents.
            </p>
          `}

          ${emailData.rejectionReason ? `
            <div style="background-color: #fef2f2; border-left: 4px solid #ef4444; padding: 16px; margin: 20px 0; border-radius: 4px;">
              <h3 style="color: #dc2626; margin: 0 0 8px 0; font-size: 16px;">Reason for Update Request:</h3>
              <p style="color: #7f1d1d; margin: 0; font-size: 14px;">${emailData.rejectionReason}</p>
            </div>
          ` : ''}

          ${emailData.comments ? `
            <div style="background-color: #f0f9ff; border-left: 4px solid #3b82f6; padding: 16px; margin: 20px 0; border-radius: 4px;">
              <h3 style="color: #1d4ed8; margin: 0 0 8px 0; font-size: 16px;">Admin Comments:</h3>
              <p style="color: #1e40af; margin: 0; font-size: 14px;">${emailData.comments}</p>
            </div>
          ` : ''}

          <div style="background-color: #f9fafb; padding: 20px; border-radius: 8px; margin: 30px 0;">
            <h3 style="color: #374151; margin: 0 0 15px 0; font-size: 18px;">Next Steps:</h3>
            <p style="color: #6b7280; margin: 0; font-size: 14px; line-height: 1.6;">${emailData.nextSteps}</p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="https://www.aureus.africa/dashboard" style="display: inline-block; background-color: #d97706; color: white; padding: 14px 28px; text-decoration: none; border-radius: 6px; font-weight: bold; font-size: 16px;">
              Access Your Dashboard
            </a>
          </div>

          <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; margin-top: 30px;">
            <p style="font-size: 12px; color: #9ca3af; margin: 0; text-align: center;">
              Submission Date: ${new Date(emailData.submissionDate).toLocaleDateString()}<br>
              Review Date: ${new Date(emailData.reviewDate).toLocaleDateString()}
            </p>
          </div>
        </div>

        <!-- Footer -->
        <div style="background-color: #f9fafb; padding: 20px 30px; text-align: center; border-top: 1px solid #e5e7eb;">
          <p style="font-size: 12px; color: #6b7280; margin: 0;">
            © 2024 Aureus Alliance Holdings (Pty) Ltd. All rights reserved.<br>
            This is an automated message. Please do not reply to this email.
          </p>
        </div>
      </div>
    </body>
    </html>
  `;
}

/**
 * Generate text email content for KYC notifications
 */
function generateKYCEmailText(emailData) {
  const isApproved = emailData.status === 'approved';
  const statusText = isApproved ? 'APPROVED' : 'REQUIRES UPDATE';

  return `
AUREUS ALLIANCE HOLDINGS
KYC Verification ${statusText}

Dear ${emailData.fullName},

${isApproved 
  ? `Congratulations! Your KYC (Know Your Customer) verification has been APPROVED. You now have full access to all Aureus Alliance Holdings platform features.`
  : `Your KYC (Know Your Customer) verification requires some updates. Please review the information below and resubmit your documents.`
}

${emailData.rejectionReason ? `
REASON FOR UPDATE REQUEST:
${emailData.rejectionReason}
` : ''}

${emailData.comments ? `
ADMIN COMMENTS:
${emailData.comments}
` : ''}

NEXT STEPS:
${emailData.nextSteps}

Access your dashboard: https://www.aureus.africa/dashboard

---
Submission Date: ${new Date(emailData.submissionDate).toLocaleDateString()}
Review Date: ${new Date(emailData.reviewDate).toLocaleDateString()}

© 2024 Aureus Alliance Holdings (Pty) Ltd. All rights reserved.
This is an automated message. Please do not reply to this email.
  `.trim();
}

/**
 * Main API handler
 */
export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Check if Resend is configured
    if (!resend) {
      console.error('❌ Resend service not configured for KYC notifications');
      return res.status(500).json({ 
        error: 'Email service not configured',
        details: 'RESEND_API_KEY not found'
      });
    }

    // Validate request body
    const {
      email,
      fullName,
      status,
      rejectionReason,
      comments,
      submissionDate,
      reviewDate,
      nextSteps
    } = req.body;

    if (!email || !fullName || !status) {
      return res.status(400).json({ 
        error: 'Missing required fields',
        required: ['email', 'fullName', 'status']
      });
    }

    if (!['approved', 'rejected'].includes(status)) {
      return res.status(400).json({ 
        error: 'Invalid status',
        allowed: ['approved', 'rejected']
      });
    }

    console.log(`📧 Sending KYC ${status} notification to:`, email);

    // Generate email content
    const emailData = {
      email,
      fullName,
      status,
      rejectionReason: rejectionReason || '',
      comments: comments || '',
      submissionDate: submissionDate || new Date().toISOString(),
      reviewDate: reviewDate || new Date().toISOString(),
      nextSteps: nextSteps || (status === 'approved' 
        ? 'You can now access all platform features including certificate downloads and commission withdrawals.'
        : 'Please review the feedback above and resubmit your KYC documents with the necessary corrections.')
    };

    const subject = status === 'approved' 
      ? '🎉 KYC Verification Approved - Welcome to Aureus Alliance!'
      : '❌ KYC Verification Update Required';

    const htmlContent = generateKYCEmailHTML(emailData);
    const textContent = generateKYCEmailText(emailData);

    // Send email via Resend
    const result = await resend.emails.send({
      from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
      to: [email],
      subject: subject,
      html: htmlContent,
      text: textContent,
      tags: [
        { name: 'category', value: 'kyc_notification' },
        { name: 'status', value: status }
      ]
    });

    if (result.error) {
      console.error('❌ Resend API error:', result.error);
      return res.status(500).json({ 
        error: 'Failed to send email',
        details: result.error.message
      });
    }

    console.log('✅ KYC notification email sent successfully:', result.data?.id);

    return res.status(200).json({
      success: true,
      messageId: result.data?.id,
      message: `KYC ${status} notification sent successfully`
    });

  } catch (error) {
    console.error('❌ KYC notification API error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error.message
    });
  }
}
