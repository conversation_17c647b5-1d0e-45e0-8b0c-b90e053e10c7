import React, { useState, useEffect } from 'react';
import { EmailPinService } from '../lib/emailPinService';

interface PinVerificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onVerified: () => void;
  userId: number;
  email: string;
  purpose: 'withdrawal' | 'security_change' | 'profile_update';
  title?: string;
  description?: string;
}

export const PinVerificationModal: React.FC<PinVerificationModalProps> = ({
  isOpen,
  onClose,
  onVerified,
  userId,
  email,
  purpose,
  title = 'Security Verification Required',
  description = 'Please enter the 6-digit PIN sent to your email address.'
}) => {
  const [pin, setPin] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [sendingPin, setSendingPin] = useState(false);
  const [pinSent, setPinSent] = useState(false);
  const [countdown, setCountdown] = useState(0);

  // Auto-send PIN when modal opens
  useEffect(() => {
    if (isOpen && !pinSent) {
      console.log('🔄 Modal opened, automatically sending PIN...');
      sendPin();
    }
  }, [isOpen]);

  // Countdown timer for resend button
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  const sendPin = async () => {
    setSendingPin(true);
    setError(null);

    try {
      const result = await EmailPinService.generateAndSendPin(userId, email, purpose);
      
      if (result.success) {
        setPinSent(true);
        setCountdown(60); // 60 second cooldown
        setError(null);
      } else {
        setError(result.error || 'Failed to send PIN');
      }
    } catch (error) {
      console.error('Error sending PIN:', error);
      setError('Failed to send PIN');
    } finally {
      setSendingPin(false);
    }
  };

  const verifyPin = async () => {
    if (pin.length !== 6) {
      setError('Please enter a 6-digit PIN');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await EmailPinService.verifyPin(userId, pin, purpose);
      
      if (result.success) {
        setSuccess(true);
        setTimeout(() => {
          onVerified();
          handleClose();
        }, 1000);
      } else {
        setError(result.error || 'Invalid PIN');
        setPin(''); // Clear PIN on error
      }
    } catch (error) {
      console.error('Error verifying PIN:', error);
      setError('Failed to verify PIN');
      setPin('');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setPin('');
    setError(null);
    setSuccess(false);
    setPinSent(false);
    setCountdown(0);
    onClose();
  };

  const handlePinChange = (value: string) => {
    // Only allow digits and limit to 6 characters
    const cleanValue = value.replace(/\D/g, '').slice(0, 6);
    setPin(cleanValue);
    setError(null);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && pin.length === 6) {
      verifyPin();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <div className="bg-slate-800 rounded-2xl border border-slate-700/50 p-6 w-full max-w-md">
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-slate-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-white mb-2">{title}</h2>
          <p className="text-slate-400 text-sm">
            {sendingPin
              ? 'Sending verification PIN to your email...'
              : pinSent
                ? 'Please enter the 6-digit PIN sent to your email.'
                : 'We will send a 6-digit PIN to your email for verification.'
            }
          </p>
        </div>

        {/* PIN Status */}
        {pinSent && !success && (
          <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-3 mb-4">
            <p className="text-blue-300 text-sm text-center">
              📧 PIN sent to {email.replace(/(.{2}).*(@.*)/, '$1***$2')}
            </p>
          </div>
        )}

        {/* Success Message */}
        {success && (
          <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-3 mb-4">
            <p className="text-green-300 text-sm text-center">
              ✅ PIN verified successfully!
            </p>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-3 mb-4">
            <p className="text-red-300 text-sm text-center">{error}</p>
          </div>
        )}

        {/* PIN Input */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-slate-300 mb-2">
            Enter 6-digit PIN
          </label>
          <input
            type="text"
            value={pin}
            onChange={(e) => handlePinChange(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="000000"
            className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600 rounded-lg text-white text-center text-2xl font-mono tracking-widest placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
            style={{ color: '#ffffff !important', backgroundColor: 'rgba(51, 65, 85, 0.5) !important' }}
            maxLength={6}
            disabled={loading || success}
          />
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={verifyPin}
            disabled={pin.length !== 6 || loading || success}
            className="w-full bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-slate-900 font-semibold py-3 px-6 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Verifying...' : success ? 'Verified!' : 'Verify PIN'}
          </button>

          <div className="flex gap-3">
            <button
              onClick={sendPin}
              disabled={sendingPin || countdown > 0 || success}
              className="flex-1 bg-slate-700 hover:bg-slate-600 text-white font-medium py-2 px-4 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {sendingPin ? 'Sending...' : countdown > 0 ? `Resend (${countdown}s)` : pinSent ? 'Resend PIN' : 'Send PIN'}
            </button>

            <button
              onClick={handleClose}
              disabled={loading}
              className="flex-1 bg-gray-600 hover:bg-gray-500 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>

        {/* Help Text */}
        <div className="mt-4 text-center">
          <p className="text-slate-500 text-xs">
            PIN expires in 10 minutes. Check your spam folder if you don't see the email.
          </p>
        </div>
      </div>
    </div>
  );
};
