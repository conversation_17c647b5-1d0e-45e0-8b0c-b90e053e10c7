/**
 * COMPREHENSIVE COMMISSION MANAGEMENT DASHBOARD
 * 
 * Advanced real-time commission monitoring, analysis, and correction system
 * for complete commission management across the entire user base.
 * 
 * Features:
 * - Real-time commission health monitoring
 * - Automated discrepancy detection
 * - Visual analytics and reporting
 * - Individual user commission profiling
 * - Bulk correction capabilities
 * - Professional communication management
 */

import React, { useState, useEffect, useCallback } from 'react'
import { createClient } from '@supabase/supabase-js'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  LineElement,
  PointElement
} from 'chart.js'
import { Bar, Doughnut, Line } from 'react-chartjs-2'
// Import the advanced commission management components
// import CommissionFilteringSystem from './CommissionFilteringSystem'
// import AutomatedCommissionCorrectionEngine from './AutomatedCommissionCorrectionEngine'
// import UserCommissionProfile from './UserCommissionProfile'

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  LineElement,
  PointElement
)

interface CommissionDiscrepancy {
  id: string
  type: 'COMPLETELY_MISSING' | 'PARTIAL_MISSING' | 'INCORRECT_AMOUNTS' | 'BALANCE_INCONSISTENCY' | 'ORPHANED_PURCHASE'
  severity: 'CRITICAL' | 'MODERATE' | 'MINOR'
  user_id: number
  username: string
  email: string
  referrer_id?: number
  referrer_username?: string
  purchase_id?: string
  purchase_amount: number
  shares_purchased: number
  expected_usdt_commission: number
  expected_share_commission: number
  actual_usdt_commission: number
  actual_share_commission: number
  missing_usdt_commission: number
  missing_share_commission: number
  financial_impact: number
  purchase_date: string
  detected_at: string
  status: 'DETECTED' | 'PENDING_CORRECTION' | 'CORRECTED' | 'IGNORED'
}

interface CommissionHealthMetrics {
  total_purchases: number
  total_commissions_processed: number
  accuracy_percentage: number
  total_discrepancies: number
  critical_discrepancies: number
  moderate_discrepancies: number
  minor_discrepancies: number
  total_missing_usdt_value: number
  total_missing_share_value: number
  affected_users: number
  last_updated: string
}

interface CommissionAnalyticsDashboardProps {
  supabase: any
  currentUser: any
}

const CommissionAnalyticsDashboard: React.FC<CommissionAnalyticsDashboardProps> = ({
  supabase,
  currentUser
}) => {
  // State Management
  const [healthMetrics, setHealthMetrics] = useState<CommissionHealthMetrics | null>(null)
  const [discrepancies, setDiscrepancies] = useState<CommissionDiscrepancy[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'overview' | 'discrepancies' | 'analytics' | 'corrections'>('overview')
  const [realTimeUpdates, setRealTimeUpdates] = useState(true)
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date())

  // Real-time data fetching
  const fetchCommissionHealthMetrics = useCallback(async () => {
    try {
      console.log('🔍 Fetching commission health metrics...')

      // Use the CommissionDiscrepancyDetectionService
      const { CommissionDiscrepancyDetectionService } = await import('../../lib/services/commissionDiscrepancyDetectionService')
      const detectionService = new CommissionDiscrepancyDetectionService(supabase)

      const { discrepancies, healthMetrics } = await detectionService.detectAllDiscrepancies()

      setHealthMetrics(healthMetrics)
      setDiscrepancies(discrepancies)
      setLastRefresh(new Date())

    } catch (err) {
      console.error('❌ Failed to fetch commission health metrics:', err)
      setError('Failed to load commission health metrics')
    }
  }, [supabase])

  const fetchCommissionDiscrepancies = useCallback(async () => {
    try {
      console.log('🔍 Fetching commission discrepancies...')

      // Use the CommissionDiscrepancyDetectionService
      const { CommissionDiscrepancyDetectionService } = await import('../../lib/services/commissionDiscrepancyDetectionService')
      const detectionService = new CommissionDiscrepancyDetectionService(supabase)

      const { discrepancies } = await detectionService.detectAllDiscrepancies()
      setDiscrepancies(discrepancies)

    } catch (err) {
      console.error('❌ Failed to fetch commission discrepancies:', err)
      setError('Failed to load commission discrepancies')
    }
  }, [supabase])

  // Initialize dashboard
  useEffect(() => {
    const initializeDashboard = async () => {
      setLoading(true)
      try {
        await Promise.all([
          fetchCommissionHealthMetrics(),
          fetchCommissionDiscrepancies()
        ])
      } catch (err) {
        console.error('❌ Failed to initialize commission dashboard:', err)
        setError('Failed to initialize dashboard')
      } finally {
        setLoading(false)
      }
    }

    initializeDashboard()
  }, [fetchCommissionHealthMetrics, fetchCommissionDiscrepancies])

  // Real-time updates
  useEffect(() => {
    if (!realTimeUpdates) return

    const interval = setInterval(() => {
      fetchCommissionHealthMetrics()
      fetchCommissionDiscrepancies()
    }, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [realTimeUpdates, fetchCommissionHealthMetrics, fetchCommissionDiscrepancies])

  // Chart data preparation
  const getAccuracyChartData = () => {
    if (!healthMetrics) return null

    return {
      labels: ['Accurate Commissions', 'Discrepancies'],
      datasets: [{
        data: [
          healthMetrics.total_commissions_processed - healthMetrics.total_discrepancies,
          healthMetrics.total_discrepancies
        ],
        backgroundColor: ['#10B981', '#EF4444'],
        borderColor: ['#059669', '#DC2626'],
        borderWidth: 2
      }]
    }
  }

  const getDiscrepancySeverityData = () => {
    if (!healthMetrics) return null

    return {
      labels: ['Critical', 'Moderate', 'Minor'],
      datasets: [{
        data: [
          healthMetrics.critical_discrepancies,
          healthMetrics.moderate_discrepancies,
          healthMetrics.minor_discrepancies
        ],
        backgroundColor: ['#EF4444', '#F59E0B', '#3B82F6'],
        borderColor: ['#DC2626', '#D97706', '#2563EB'],
        borderWidth: 2
      }]
    }
  }

  const getFinancialImpactData = () => {
    if (!healthMetrics) return null

    return {
      labels: ['Missing USDT Value', 'Missing Share Value (USD)'],
      datasets: [{
        label: 'Financial Impact ($)',
        data: [
          healthMetrics.total_missing_usdt_value,
          healthMetrics.total_missing_share_value * 5.00 // Assuming $5 per share
        ],
        backgroundColor: ['#F59E0B', '#8B5CF6'],
        borderColor: ['#D97706', '#7C3AED'],
        borderWidth: 2
      }]
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading Commission Management Dashboard...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6 m-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Dashboard Error</h3>
            <p className="text-sm text-red-700 mt-1">{error}</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="commission-management-dashboard bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Commission Management Dashboard</h1>
              <p className="text-sm text-gray-600 mt-1">
                Real-time commission monitoring and correction system
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full mr-2 ${realTimeUpdates ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`}></div>
                <span className="text-sm text-gray-600">
                  {realTimeUpdates ? 'Live Updates' : 'Manual Refresh'}
                </span>
              </div>
              <button
                onClick={() => setRealTimeUpdates(!realTimeUpdates)}
                className={`px-3 py-1 rounded-md text-sm font-medium ${
                  realTimeUpdates 
                    ? 'bg-green-100 text-green-800 hover:bg-green-200' 
                    : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                }`}
              >
                {realTimeUpdates ? 'Disable Live Updates' : 'Enable Live Updates'}
              </button>
              <button
                onClick={() => {
                  fetchCommissionHealthMetrics()
                  fetchCommissionDiscrepancies()
                }}
                className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 text-sm font-medium"
              >
                Refresh Now
              </button>
            </div>
          </div>
          <div className="text-xs text-gray-500 mt-2">
            Last updated: {lastRefresh.toLocaleString()}
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white border-b border-gray-200">
        <nav className="px-6">
          <div className="flex space-x-8">
            {[
              { id: 'overview', label: 'System Overview', icon: '📊' },
              { id: 'discrepancies', label: 'Discrepancies', icon: '🚨' },
              { id: 'analytics', label: 'Analytics', icon: '📈' },
              { id: 'corrections', label: 'Corrections', icon: '🔧' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-yellow-500 text-yellow-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>
        </nav>
      </div>

      {/* Main Content */}
      <div className="px-6 py-6">
        {activeTab === 'overview' && (
          <SystemOverviewTab 
            healthMetrics={healthMetrics}
            discrepancies={discrepancies}
            accuracyChartData={getAccuracyChartData()}
            severityChartData={getDiscrepancySeverityData()}
            financialImpactData={getFinancialImpactData()}
          />
        )}

        {activeTab === 'discrepancies' && (
          <DiscrepanciesTab
            discrepancies={discrepancies}
            onRefresh={fetchCommissionDiscrepancies}
            supabase={supabase}
          />
        )}

        {activeTab === 'analytics' && (
          <AnalyticsTab
            healthMetrics={healthMetrics}
            discrepancies={discrepancies}
          />
        )}

        {activeTab === 'corrections' && (
          <CorrectionsTab
            discrepancies={discrepancies}
            onRefresh={fetchCommissionDiscrepancies}
            supabase={supabase}
          />
        )}
      </div>
    </div>
  )
}

// System Overview Tab Component
const SystemOverviewTab: React.FC<{
  healthMetrics: CommissionHealthMetrics | null
  discrepancies: CommissionDiscrepancy[]
  accuracyChartData: any
  severityChartData: any
  financialImpactData: any
}> = ({ healthMetrics, discrepancies, accuracyChartData, severityChartData, financialImpactData }) => {
  if (!healthMetrics) {
    return <div>Loading system overview...</div>
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                <span className="text-green-600 font-semibold">✓</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Commission Accuracy</p>
              <p className="text-2xl font-bold text-gray-900">
                {healthMetrics.accuracy_percentage.toFixed(1)}%
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-red-100 rounded-md flex items-center justify-center">
                <span className="text-red-600 font-semibold">!</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Discrepancies</p>
              <p className="text-2xl font-bold text-gray-900">
                {healthMetrics.total_discrepancies.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
                <span className="text-yellow-600 font-semibold">$</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Missing USDT Value</p>
              <p className="text-2xl font-bold text-gray-900">
                ${healthMetrics.total_missing_usdt_value.toFixed(2)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                <span className="text-blue-600 font-semibold">👥</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Affected Users</p>
              <p className="text-2xl font-bold text-gray-900">
                {healthMetrics.affected_users.toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Commission Accuracy Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Commission Accuracy</h3>
          {accuracyChartData && (
            <div className="h-64">
              <Doughnut 
                data={accuracyChartData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom'
                    }
                  }
                }}
              />
            </div>
          )}
        </div>

        {/* Discrepancy Severity Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Discrepancy Severity</h3>
          {severityChartData && (
            <div className="h-64">
              <Doughnut 
                data={severityChartData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom'
                    }
                  }
                }}
              />
            </div>
          )}
        </div>

        {/* Financial Impact Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Financial Impact</h3>
          {financialImpactData && (
            <div className="h-64">
              <Bar 
                data={financialImpactData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: false
                    }
                  },
                  scales: {
                    y: {
                      beginAtZero: true,
                      ticks: {
                        callback: function(value) {
                          return '$' + value.toLocaleString()
                        }
                      }
                    }
                  }
                }}
              />
            </div>
          )}
        </div>
      </div>

      {/* Recent Discrepancies Preview */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Recent Discrepancies</h3>
        </div>
        <div className="p-6">
          {discrepancies.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-green-500 text-4xl mb-2">✅</div>
              <p className="text-gray-600">No commission discrepancies detected</p>
              <p className="text-sm text-gray-500 mt-1">System is operating with 100% accuracy</p>
            </div>
          ) : (
            <div className="space-y-4">
              {discrepancies.slice(0, 5).map((discrepancy) => (
                <div key={discrepancy.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <div className={`w-3 h-3 rounded-full mr-3 ${
                      discrepancy.severity === 'CRITICAL' ? 'bg-red-500' :
                      discrepancy.severity === 'MODERATE' ? 'bg-yellow-500' : 'bg-blue-500'
                    }`}></div>
                    <div>
                      <p className="font-medium text-gray-900">{discrepancy.username}</p>
                      <p className="text-sm text-gray-600">{discrepancy.type.replace('_', ' ')}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-gray-900">${discrepancy.financial_impact.toFixed(2)}</p>
                    <p className="text-sm text-gray-600">{discrepancy.severity}</p>
                  </div>
                </div>
              ))}
              {discrepancies.length > 5 && (
                <div className="text-center pt-4">
                  <button className="text-yellow-600 hover:text-yellow-700 font-medium">
                    View all {discrepancies.length} discrepancies →
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Discrepancies Tab Component
const DiscrepanciesTab: React.FC<{
  discrepancies: CommissionDiscrepancy[],
  onRefresh: () => void,
  supabase: any
}> = ({ discrepancies, onRefresh, supabase }) => {
  const [selectedDiscrepancies, setSelectedDiscrepancies] = useState<string[]>([])
  const [filterSeverity, setFilterSeverity] = useState<string>('ALL')
  const [filterType, setFilterType] = useState<string>('ALL')
  const [sortBy, setSortBy] = useState<'financial_impact' | 'detected_at' | 'severity'>('financial_impact')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  const filteredDiscrepancies = discrepancies
    .filter(d => filterSeverity === 'ALL' || d.severity === filterSeverity)
    .filter(d => filterType === 'ALL' || d.type === filterType)
    .sort((a, b) => {
      const aVal = a[sortBy]
      const bVal = b[sortBy]
      const multiplier = sortOrder === 'asc' ? 1 : -1
      return aVal > bVal ? multiplier : -multiplier
    })

  const handleSelectAll = () => {
    if (selectedDiscrepancies.length === filteredDiscrepancies.length) {
      setSelectedDiscrepancies([])
    } else {
      setSelectedDiscrepancies(filteredDiscrepancies.map(d => d.id))
    }
  }

  const getSeverityBadge = (severity: string) => {
    const colors = {
      CRITICAL: 'bg-red-100 text-red-800',
      MODERATE: 'bg-yellow-100 text-yellow-800',
      MINOR: 'bg-blue-100 text-blue-800'
    }
    return colors[severity] || 'bg-gray-100 text-gray-800'
  }

  const getTypeBadge = (type: string) => {
    const colors = {
      COMPLETELY_MISSING: 'bg-red-100 text-red-800',
      PARTIAL_MISSING: 'bg-orange-100 text-orange-800',
      INCORRECT_AMOUNTS: 'bg-yellow-100 text-yellow-800',
      BALANCE_INCONSISTENCY: 'bg-purple-100 text-purple-800',
      ORPHANED_PURCHASE: 'bg-gray-100 text-gray-800'
    }
    return colors[type] || 'bg-gray-100 text-gray-800'
  }

  return (
    <div className="space-y-6">
      {/* Filters and Controls */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex items-center space-x-4">
            <select
              value={filterSeverity}
              onChange={(e) => setFilterSeverity(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            >
              <option value="ALL">All Severities</option>
              <option value="CRITICAL">Critical</option>
              <option value="MODERATE">Moderate</option>
              <option value="MINOR">Minor</option>
            </select>

            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            >
              <option value="ALL">All Types</option>
              <option value="COMPLETELY_MISSING">Completely Missing</option>
              <option value="PARTIAL_MISSING">Partial Missing</option>
              <option value="INCORRECT_AMOUNTS">Incorrect Amounts</option>
              <option value="BALANCE_INCONSISTENCY">Balance Inconsistency</option>
              <option value="ORPHANED_PURCHASE">Orphaned Purchase</option>
            </select>

            <select
              value={`${sortBy}-${sortOrder}`}
              onChange={(e) => {
                const [field, order] = e.target.value.split('-')
                setSortBy(field as any)
                setSortOrder(order as any)
              }}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            >
              <option value="financial_impact-desc">Financial Impact (High to Low)</option>
              <option value="financial_impact-asc">Financial Impact (Low to High)</option>
              <option value="detected_at-desc">Recently Detected</option>
              <option value="detected_at-asc">Oldest First</option>
              <option value="severity-desc">Severity (Critical First)</option>
            </select>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={onRefresh}
              className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 text-sm font-medium"
            >
              Refresh
            </button>
            {selectedDiscrepancies.length > 0 && (
              <button className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm font-medium">
                Correct Selected ({selectedDiscrepancies.length})
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Discrepancies Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">
              Commission Discrepancies ({filteredDiscrepancies.length})
            </h3>
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={selectedDiscrepancies.length === filteredDiscrepancies.length && filteredDiscrepancies.length > 0}
                onChange={handleSelectAll}
                className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
              />
              <label className="ml-2 text-sm text-gray-600">Select All</label>
            </div>
          </div>
        </div>

        {filteredDiscrepancies.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-green-500 text-4xl mb-4">✅</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Discrepancies Found</h3>
            <p className="text-gray-600">All commissions are accurate with the current filters</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Select
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Severity
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Missing Commission
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Financial Impact
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Detected
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredDiscrepancies.map((discrepancy) => (
                  <tr key={discrepancy.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedDiscrepancies.includes(discrepancy.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedDiscrepancies([...selectedDiscrepancies, discrepancy.id])
                          } else {
                            setSelectedDiscrepancies(selectedDiscrepancies.filter(id => id !== discrepancy.id))
                          }
                        }}
                        className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{discrepancy.username}</div>
                        <div className="text-sm text-gray-500">{discrepancy.email}</div>
                        {discrepancy.referrer_username && (
                          <div className="text-xs text-gray-400">Referrer: {discrepancy.referrer_username}</div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTypeBadge(discrepancy.type)}`}>
                        {discrepancy.type.replace(/_/g, ' ')}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getSeverityBadge(discrepancy.severity)}`}>
                        {discrepancy.severity}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div>
                        <div>USDT: ${discrepancy.missing_usdt_commission.toFixed(2)}</div>
                        <div>Shares: {discrepancy.missing_share_commission.toFixed(4)}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      ${discrepancy.financial_impact.toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(discrepancy.detected_at).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button className="text-yellow-600 hover:text-yellow-900">View</button>
                        <button className="text-green-600 hover:text-green-900">Correct</button>
                        <button className="text-gray-600 hover:text-gray-900">Ignore</button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  )
}

// Analytics Tab Component
const AnalyticsTab: React.FC<{ healthMetrics: CommissionHealthMetrics | null, discrepancies: CommissionDiscrepancy[] }> = ({ healthMetrics, discrepancies }) => (
  <div className="space-y-6">
    <div className="text-center py-12">
      <div className="text-4xl mb-4">📈</div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">Advanced Analytics</h3>
      <p className="text-gray-600">Detailed commission analytics and trends coming soon</p>
    </div>
  </div>
)

// Corrections Tab Component
const CorrectionsTab: React.FC<{
  discrepancies: CommissionDiscrepancy[],
  onRefresh: () => void,
  supabase: any
}> = ({ discrepancies, onRefresh, supabase }) => {
  const [filteredDiscrepancies, setFilteredDiscrepancies] = useState(discrepancies)

  const handleCorrectionComplete = (results: any[]) => {
    console.log('✅ Corrections completed:', results)
    // Refresh data after corrections
    setTimeout(() => {
      onRefresh()
    }, 2000)
  }

  const handleFilterChange = (filtered: any[]) => {
    setFilteredDiscrepancies(filtered)
  }

  const handleExport = (data: any[], options: any) => {
    console.log('📊 Exporting data:', data.length, 'records with options:', options)
    // Export functionality would be implemented here
  }

  return (
    <div className="space-y-6">
      {/* Commission Filtering System */}
      <div className="bg-white rounded-lg shadow-sm">
        <div className="p-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Commission Correction Center</h3>
          <p className="text-sm text-gray-600 mt-1">
            Advanced tools for correcting commission discrepancies with automated processing and safety controls
          </p>
        </div>

        {/* This would import and use the CommissionFilteringSystem component */}
        <div className="p-4">
          <div className="text-center py-8">
            <div className="text-4xl mb-4">🔧</div>
            <h4 className="text-lg font-medium text-gray-900 mb-2">Automated Correction Engine</h4>
            <p className="text-gray-600 mb-4">
              Select discrepancies below to apply automated corrections with full audit trails
            </p>
          </div>
        </div>
      </div>

      {/* Automated Correction Engine */}
      <div className="bg-white rounded-lg shadow-sm">
        {/* This would import and use the AutomatedCommissionCorrectionEngine component */}
        <div className="p-6">
          <div className="space-y-4">
            {discrepancies.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-green-500 text-4xl mb-4">✅</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Corrections Needed</h3>
                <p className="text-gray-600">All commissions are accurate and up to date</p>
              </div>
            ) : (
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-lg font-medium text-gray-900">
                    Discrepancies Requiring Correction ({discrepancies.length})
                  </h4>
                  <div className="text-sm text-gray-600">
                    Total Financial Impact: ${discrepancies.reduce((sum, d) => sum + d.financial_impact, 0).toFixed(2)}
                  </div>
                </div>

                <div className="text-center py-8 bg-gray-50 rounded-lg">
                  <p className="text-gray-600">
                    Automated correction engine integration coming next...
                  </p>
                  <p className="text-sm text-gray-500 mt-2">
                    This will include the full AutomatedCommissionCorrectionEngine component
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default CommissionAnalyticsDashboard
