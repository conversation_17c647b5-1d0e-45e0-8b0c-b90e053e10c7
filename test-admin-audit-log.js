#!/usr/bin/env node

/**
 * TEST ADMIN AUDIT LOG
 * 
 * This script tests the fixed logAdminAction function to ensure
 * it works with the current database schema.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Replicate the fixed logAdminAction function
const logAdminAction = async (
  adminEmail,
  action,
  targetType,
  targetId,
  details = {},
  oldValues = {},
  newValues = {}
) => {
  try {
    // Use the current table schema (admin_telegram_id, admin_username)
    // For web admin actions, we'll use a special telegram_id and the email as username
    const logEntry = {
      admin_telegram_id: 0, // Use 0 for web admin actions (system actions)
      admin_username: adminEmail, // Store email in username field for web admins
      action,
      target_type: targetType,
      target_id: targetId,
      details: {
        ...details,
        old_values: oldValues,
        new_values: newValues,
        timestamp: new Date().toISOString(),
        user_agent: 'test_script',
        source: 'web_admin'
      },
      ip_address: null, // Would be set by server
      user_agent: 'test_script'
    }

    console.log('🔍 Admin Action Logged:', logEntry)

    // Store in database audit_logs table
    const { error } = await supabase
      .from('admin_audit_logs')
      .insert(logEntry)

    if (error) {
      console.error('Failed to store audit log:', error)
      return false
    }

    return true
  } catch (err) {
    console.error('Error logging admin action:', err)
    return false
  }
}

const testAdminAuditLog = async () => {
  try {
    console.log('🧪 Testing admin audit log function...\n');

    // Test the fixed logAdminAction function
    const result = await logAdminAction(
      '<EMAIL>',
      'TEST_ACTION',
      'user',
      '4',
      { test: true },
      { old_password: 'old_hash' },
      { new_password: 'new_hash' }
    );

    if (result) {
      console.log('✅ Admin audit log test PASSED');
      console.log('The logAdminAction function now works with the current database schema.');
      
      // Verify the log was stored
      const { data: logs, error: fetchError } = await supabase
        .from('admin_audit_logs')
        .select('*')
        .eq('action', 'TEST_ACTION')
        .order('timestamp', { ascending: false })
        .limit(1);

      if (!fetchError && logs && logs.length > 0) {
        console.log('\n📋 Stored audit log:');
        console.log(JSON.stringify(logs[0], null, 2));
        
        // Clean up test record
        await supabase
          .from('admin_audit_logs')
          .delete()
          .eq('action', 'TEST_ACTION');
          
        console.log('\n🧹 Test record cleaned up');
      }
      
    } else {
      console.log('❌ Admin audit log test FAILED');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
};

testAdminAuditLog();
