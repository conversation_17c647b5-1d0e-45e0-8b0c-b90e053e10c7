/**
 * SHARE PURCHASE EMAIL TEMPLATE
 * 
 * Template for share purchase confirmation emails
 * sent after successful share purchases.
 */

import { BaseEmailTemplate } from './BaseEmailTemplate';
import { SharePurchaseConfirmationData } from '../types/EmailTypes';

export class SharePurchaseTemplate extends BaseEmailTemplate<SharePurchaseConfirmationData> {
  protected emailType = 'share_purchase' as const;

  protected generateSubject(data: SharePurchaseConfirmationData): string {
    return `Share Purchase Confirmed - ${this.formatNumber(data.sharesPurchased)} Shares`;
  }

  protected generateBody(data: SharePurchaseConfirmationData): string {
    return `
      <div class="email-body">
        <h2 style="color: ${this.brandingConfig.colors.primary}; margin-bottom: 20px; text-align: center;">
          🎉 Share Purchase Confirmed!
        </h2>
        
        <p style="margin-bottom: 15px; color: ${this.brandingConfig.colors.text}; font-size: 18px;">
          Hello ${data.fullName},
        </p>
        
        <p style="margin-bottom: 20px; color: ${this.brandingConfig.colors.text};">
          Congratulations! Your share purchase has been successfully processed and confirmed. You are now a proud shareholder in ${this.brandingConfig.companyName}.
        </p>
        
        <div class="alert alert-success">
          <h3 style="margin-top: 0; margin-bottom: 15px; color: #34d399;">
            📋 Purchase Details
          </h3>
          <table class="table">
            <tr>
              <td class="label">Shares Purchased:</td>
              <td class="value">${this.formatNumber(data.sharesPurchased)}</td>
            </tr>
            <tr>
              <td class="label">Share Price:</td>
              <td class="value">${this.formatCurrency(data.sharePrice)}</td>
            </tr>
            <tr>
              <td class="label">Total Amount:</td>
              <td class="value">${this.formatCurrency(data.totalAmount)}</td>
            </tr>
            <tr>
              <td class="label">Investment Phase:</td>
              <td class="value">${data.phaseName}</td>
            </tr>
            <tr>
              <td class="label">Transaction ID:</td>
              <td class="value" style="font-family: monospace;">${data.transactionId}</td>
            </tr>
            <tr>
              <td class="label">Purchase Date:</td>
              <td class="value">${this.formatDate(new Date())}</td>
            </tr>
          </table>
        </div>
        
        <div class="alert alert-info">
          <h4 style="margin-top: 0; margin-bottom: 15px; color: #60a5fa;">
            📈 What Happens Next?
          </h4>
          <ul style="margin: 0; padding-left: 20px; color: #60a5fa;">
            <li style="margin-bottom: 8px;">Your shares will appear in your portfolio within 24 hours</li>
            <li style="margin-bottom: 8px;">Digital certificates will be generated and available for download</li>
            <li style="margin-bottom: 8px;">You'll start earning dividends based on mining operations</li>
            <li style="margin-bottom: 8px;">Track your investment performance in real-time</li>
          </ul>
        </div>
        
        <div style="margin: 25px 0; padding: 20px; background-color: rgba(212, 175, 55, 0.1); border-radius: 8px; border-left: 4px solid ${this.brandingConfig.colors.primary};">
          <h4 style="margin-top: 0; margin-bottom: 10px; color: ${this.brandingConfig.colors.primary};">
            💰 Investment Summary
          </h4>
          <p style="margin: 0; color: ${this.brandingConfig.colors.text}; font-size: 14px;">
            You now own <strong>${this.formatNumber(data.sharesPurchased)} shares</strong> in our gold-backed mining operations across Africa. 
            Your investment contributes to sustainable mining practices and community development.
          </p>
        </div>
        
        <div class="text-center mt-4">
          <a href="${this.brandingConfig.websiteUrl}/dashboard/portfolio" class="btn btn-primary" style="margin-right: 10px;">
            View Portfolio
          </a>
          <a href="${this.brandingConfig.websiteUrl}/dashboard/dividends" class="btn btn-secondary">
            Calculate Dividends
          </a>
        </div>
      </div>
    `;
  }

  protected generateTextContent(data: SharePurchaseConfirmationData): string {
    return `
Share Purchase Confirmed!

Hello ${data.fullName},

Congratulations! Your share purchase has been successfully processed and confirmed. You are now a proud shareholder in ${this.brandingConfig.companyName}.

Purchase Details:
- Shares Purchased: ${this.formatNumber(data.sharesPurchased)}
- Share Price: ${this.formatCurrency(data.sharePrice)}
- Total Amount: ${this.formatCurrency(data.totalAmount)}
- Investment Phase: ${data.phaseName}
- Transaction ID: ${data.transactionId}
- Purchase Date: ${this.formatDate(new Date())}

What Happens Next?
- Your shares will appear in your portfolio within 24 hours
- Digital certificates will be generated and available for download
- You'll start earning dividends based on mining operations
- Track your investment performance in real-time

Investment Summary:
You now own ${this.formatNumber(data.sharesPurchased)} shares in our gold-backed mining operations across Africa. Your investment contributes to sustainable mining practices and community development.

View Portfolio: ${this.brandingConfig.websiteUrl}/dashboard/portfolio
Calculate Dividends: ${this.brandingConfig.websiteUrl}/dashboard/dividends

${this.brandingConfig.tagline}
${this.brandingConfig.companyName}
    `.trim();
  }
}
