/**
 * DOCUMENT VERIFICATION AND OCR SYSTEM
 * 
 * Advanced document verification with OCR, validation, and fraud detection
 * for South African ID documents and international passports.
 */

import { supabase } from './supabase';

interface DocumentType {
  type: 'south_african_id' | 'passport' | 'drivers_license' | 'proof_of_address';
  name: string;
  requiredFields: string[];
  validationRules: ValidationRule[];
}

interface ValidationRule {
  field: string;
  pattern?: RegExp;
  length?: { min: number; max: number };
  required: boolean;
  validator?: (value: string) => boolean;
}

interface OCRResult {
  success: boolean;
  confidence: number;
  extractedText: string;
  fields: Record<string, string>;
  boundingBoxes: Record<string, BoundingBox>;
}

interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
  confidence: number;
}

interface DocumentValidationResult {
  isValid: boolean;
  confidence: number;
  extractedData: Record<string, string>;
  validationErrors: string[];
  fraudIndicators: string[];
  documentType: string;
  qualityScore: number;
}

interface FraudDetectionResult {
  fraudDetected: boolean;
  riskScore: number; // 0-100
  indicators: string[];
  confidence: number;
}

class DocumentVerificationSystem {
  private readonly CONFIDENCE_THRESHOLD = 0.7;
  private readonly FRAUD_RISK_THRESHOLD = 70;
  
  private readonly DOCUMENT_TYPES: Record<string, DocumentType> = {
    south_african_id: {
      type: 'south_african_id',
      name: 'South African ID Document',
      requiredFields: ['id_number', 'first_name', 'last_name', 'date_of_birth'],
      validationRules: [
        {
          field: 'id_number',
          pattern: /^\d{13}$/,
          required: true,
          validator: this.validateSouthAfricanID
        },
        {
          field: 'first_name',
          pattern: /^[A-Za-z\s]{2,50}$/,
          required: true
        },
        {
          field: 'last_name',
          pattern: /^[A-Za-z\s]{2,50}$/,
          required: true
        },
        {
          field: 'date_of_birth',
          pattern: /^\d{4}-\d{2}-\d{2}$/,
          required: true
        }
      ]
    },
    passport: {
      type: 'passport',
      name: 'International Passport',
      requiredFields: ['passport_number', 'first_name', 'last_name', 'date_of_birth', 'nationality'],
      validationRules: [
        {
          field: 'passport_number',
          pattern: /^[A-Z0-9]{6,12}$/,
          required: true
        },
        {
          field: 'first_name',
          pattern: /^[A-Za-z\s]{2,50}$/,
          required: true
        },
        {
          field: 'last_name',
          pattern: /^[A-Za-z\s]{2,50}$/,
          required: true
        }
      ]
    }
  };

  /**
   * Verify document with OCR and validation
   */
  async verifyDocument(
    documentFile: File,
    expectedType: string,
    userProvidedData?: Record<string, string>
  ): Promise<DocumentValidationResult> {
    try {
      console.log(`📄 Verifying ${expectedType} document...`);

      // 1. Perform OCR on document
      const ocrResult = await this.performOCR(documentFile);
      if (!ocrResult.success) {
        throw new Error('OCR processing failed');
      }

      // 2. Detect document type
      const detectedType = await this.detectDocumentType(ocrResult);
      
      // 3. Validate document structure and content
      const validationResult = await this.validateDocumentContent(
        detectedType,
        ocrResult.fields,
        userProvidedData
      );

      // 4. Perform fraud detection
      const fraudResult = await this.detectDocumentFraud(documentFile, ocrResult);

      // 5. Calculate quality score
      const qualityScore = this.calculateDocumentQuality(ocrResult, validationResult);

      // 6. Determine overall validity
      const isValid = validationResult.isValid && 
                     !fraudResult.fraudDetected && 
                     ocrResult.confidence >= this.CONFIDENCE_THRESHOLD &&
                     qualityScore >= 0.6;

      const result: DocumentValidationResult = {
        isValid,
        confidence: Math.min(ocrResult.confidence, validationResult.confidence),
        extractedData: ocrResult.fields,
        validationErrors: validationResult.errors,
        fraudIndicators: fraudResult.indicators,
        documentType: detectedType,
        qualityScore
      };

      // Log verification result
      await this.logDocumentVerification(result, fraudResult);

      console.log(`✅ Document verification completed: ${isValid ? 'VALID' : 'INVALID'}`);
      return result;

    } catch (error) {
      console.error('❌ Document verification error:', error);
      return {
        isValid: false,
        confidence: 0,
        extractedData: {},
        validationErrors: [error.message],
        fraudIndicators: [],
        documentType: 'unknown',
        qualityScore: 0
      };
    }
  }

  /**
   * Perform OCR on document image
   */
  private async performOCR(documentFile: File): Promise<OCRResult> {
    try {
      console.log('🔍 Performing OCR on document...');

      // Convert file to image for processing
      const imageUrl = URL.createObjectURL(documentFile);
      const img = new Image();
      
      return new Promise((resolve) => {
        img.onload = async () => {
          try {
            // Create canvas for image processing
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            if (!ctx) {
              throw new Error('Canvas context not available');
            }

            canvas.width = img.width;
            canvas.height = img.height;
            ctx.drawImage(img, 0, 0);

            // Simulate OCR processing (in production, use Tesseract.js or cloud OCR)
            const ocrResult = await this.simulateOCR(canvas);
            
            URL.revokeObjectURL(imageUrl);
            resolve(ocrResult);

          } catch (error) {
            console.error('❌ OCR processing error:', error);
            resolve({
              success: false,
              confidence: 0,
              extractedText: '',
              fields: {},
              boundingBoxes: {}
            });
          }
        };

        img.onerror = () => {
          console.error('❌ Failed to load image for OCR');
          resolve({
            success: false,
            confidence: 0,
            extractedText: '',
            fields: {},
            boundingBoxes: {}
          });
        };

        img.src = imageUrl;
      });

    } catch (error) {
      console.error('❌ OCR setup error:', error);
      return {
        success: false,
        confidence: 0,
        extractedText: '',
        fields: {},
        boundingBoxes: {}
      };
    }
  }

  /**
   * Simulate OCR processing (replace with actual OCR in production)
   */
  private async simulateOCR(canvas: HTMLCanvasElement): Promise<OCRResult> {
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Mock OCR results for South African ID
    const mockFields = {
      id_number: '9001015009087',
      first_name: 'JOHN',
      last_name: 'SMITH',
      date_of_birth: '1990-01-01',
      gender: 'M',
      nationality: 'RSA'
    };

    const mockBoundingBoxes = {
      id_number: { x: 100, y: 200, width: 200, height: 30, confidence: 0.95 },
      first_name: { x: 100, y: 150, width: 150, height: 25, confidence: 0.92 },
      last_name: { x: 260, y: 150, width: 150, height: 25, confidence: 0.91 }
    };

    return {
      success: true,
      confidence: 0.89,
      extractedText: 'REPUBLIC OF SOUTH AFRICA JOHN SMITH 9001015009087...',
      fields: mockFields,
      boundingBoxes: mockBoundingBoxes
    };
  }

  /**
   * Detect document type from OCR results
   */
  private async detectDocumentType(ocrResult: OCRResult): Promise<string> {
    const text = ocrResult.extractedText.toLowerCase();

    // Check for South African ID indicators
    if (text.includes('republic of south africa') || 
        text.includes('suid-afrika') ||
        Object.keys(ocrResult.fields).includes('id_number')) {
      return 'south_african_id';
    }

    // Check for passport indicators
    if (text.includes('passport') || 
        text.includes('passeport') ||
        Object.keys(ocrResult.fields).includes('passport_number')) {
      return 'passport';
    }

    // Check for proof of address indicators
    if (text.includes('statement') || 
        text.includes('utility') ||
        text.includes('municipal')) {
      return 'proof_of_address';
    }

    return 'unknown';
  }

  /**
   * Validate document content against rules
   */
  private async validateDocumentContent(
    documentType: string,
    extractedFields: Record<string, string>,
    userProvidedData?: Record<string, string>
  ): Promise<{ isValid: boolean; confidence: number; errors: string[] }> {
    const errors: string[] = [];
    const docType = this.DOCUMENT_TYPES[documentType];

    if (!docType) {
      return {
        isValid: false,
        confidence: 0,
        errors: ['Unknown document type']
      };
    }

    // Validate required fields
    for (const requiredField of docType.requiredFields) {
      if (!extractedFields[requiredField]) {
        errors.push(`Missing required field: ${requiredField}`);
      }
    }

    // Validate field formats
    for (const rule of docType.validationRules) {
      const value = extractedFields[rule.field];
      
      if (rule.required && !value) {
        errors.push(`Required field missing: ${rule.field}`);
        continue;
      }

      if (value) {
        // Pattern validation
        if (rule.pattern && !rule.pattern.test(value)) {
          errors.push(`Invalid format for ${rule.field}`);
        }

        // Length validation
        if (rule.length) {
          if (value.length < rule.length.min || value.length > rule.length.max) {
            errors.push(`Invalid length for ${rule.field}`);
          }
        }

        // Custom validator
        if (rule.validator && !rule.validator(value)) {
          errors.push(`Validation failed for ${rule.field}`);
        }
      }
    }

    // Cross-validate with user-provided data
    if (userProvidedData) {
      for (const [key, userValue] of Object.entries(userProvidedData)) {
        const extractedValue = extractedFields[key];
        if (extractedValue && this.normalizeString(extractedValue) !== this.normalizeString(userValue)) {
          errors.push(`Mismatch between provided and extracted ${key}`);
        }
      }
    }

    const isValid = errors.length === 0;
    const confidence = isValid ? 0.9 : Math.max(0.1, 0.9 - (errors.length * 0.2));

    return { isValid, confidence, errors };
  }

  /**
   * Detect document fraud indicators
   */
  private async detectDocumentFraud(documentFile: File, ocrResult: OCRResult): Promise<FraudDetectionResult> {
    const indicators: string[] = [];
    let riskScore = 0;

    try {
      // 1. Check image quality and tampering
      const qualityCheck = await this.checkImageQuality(documentFile);
      if (qualityCheck.lowQuality) {
        indicators.push('Low image quality detected');
        riskScore += 20;
      }
      if (qualityCheck.tamperingDetected) {
        indicators.push('Possible image tampering detected');
        riskScore += 40;
      }

      // 2. Check OCR confidence patterns
      if (ocrResult.confidence < 0.6) {
        indicators.push('Low OCR confidence');
        riskScore += 15;
      }

      // 3. Check for inconsistent fonts/formatting
      const fontCheck = this.checkFontConsistency(ocrResult);
      if (!fontCheck.consistent) {
        indicators.push('Inconsistent font formatting detected');
        riskScore += 25;
      }

      // 4. Validate ID number checksum (for South African IDs)
      if (ocrResult.fields.id_number) {
        if (!this.validateSouthAfricanID(ocrResult.fields.id_number)) {
          indicators.push('Invalid ID number checksum');
          riskScore += 30;
        }
      }

      // 5. Check for duplicate submissions
      const duplicateCheck = await this.checkForDuplicates(ocrResult.fields);
      if (duplicateCheck.found) {
        indicators.push('Document already submitted');
        riskScore += 50;
      }

      const fraudDetected = riskScore >= this.FRAUD_RISK_THRESHOLD;
      const confidence = Math.min(1.0, riskScore / 100);

      return {
        fraudDetected,
        riskScore,
        indicators,
        confidence
      };

    } catch (error) {
      console.error('❌ Fraud detection error:', error);
      return {
        fraudDetected: true,
        riskScore: 100,
        indicators: ['Fraud detection system error'],
        confidence: 1.0
      };
    }
  }

  /**
   * Validate South African ID number
   */
  private validateSouthAfricanID(idNumber: string): boolean {
    if (!/^\d{13}$/.test(idNumber)) {
      return false;
    }

    // Luhn algorithm for checksum validation
    const digits = idNumber.split('').map(Number);
    let sum = 0;

    for (let i = 0; i < 12; i++) {
      if (i % 2 === 0) {
        sum += digits[i];
      } else {
        const doubled = digits[i] * 2;
        sum += doubled > 9 ? doubled - 9 : doubled;
      }
    }

    const checkDigit = (10 - (sum % 10)) % 10;
    return checkDigit === digits[12];
  }

  /**
   * Check image quality and tampering
   */
  private async checkImageQuality(file: File): Promise<{ lowQuality: boolean; tamperingDetected: boolean }> {
    // Simulate quality checks
    await new Promise(resolve => setTimeout(resolve, 300));

    const fileSize = file.size;
    const lowQuality = fileSize < 100000; // Less than 100KB might be low quality
    const tamperingDetected = Math.random() < 0.05; // 5% chance of tampering detection

    return { lowQuality, tamperingDetected };
  }

  /**
   * Check font consistency
   */
  private checkFontConsistency(ocrResult: OCRResult): { consistent: boolean } {
    // Simulate font consistency check
    const consistent = Math.random() > 0.1; // 90% chance of consistent fonts
    return { consistent };
  }

  /**
   * Check for duplicate document submissions
   */
  private async checkForDuplicates(fields: Record<string, string>): Promise<{ found: boolean }> {
    try {
      if (!fields.id_number && !fields.passport_number) {
        return { found: false };
      }

      const identifier = fields.id_number || fields.passport_number;
      
      const { data, error } = await supabase
        .from('kyc_information')
        .select('id')
        .or(`id_number.eq.${identifier},passport_number.eq.${identifier}`)
        .limit(1);

      if (error) {
        console.error('❌ Duplicate check error:', error);
        return { found: false };
      }

      return { found: (data?.length || 0) > 0 };

    } catch (error) {
      console.error('❌ Duplicate check error:', error);
      return { found: false };
    }
  }

  /**
   * Calculate document quality score
   */
  private calculateDocumentQuality(
    ocrResult: OCRResult,
    validationResult: { isValid: boolean; confidence: number }
  ): number {
    let qualityScore = 0;

    // OCR confidence (40% weight)
    qualityScore += ocrResult.confidence * 0.4;

    // Validation confidence (30% weight)
    qualityScore += validationResult.confidence * 0.3;

    // Field completeness (20% weight)
    const totalFields = Object.keys(ocrResult.fields).length;
    const expectedFields = 5; // Expected minimum fields
    const completeness = Math.min(1, totalFields / expectedFields);
    qualityScore += completeness * 0.2;

    // Text clarity (10% weight)
    const textLength = ocrResult.extractedText.length;
    const clarity = Math.min(1, textLength / 200); // Expect at least 200 characters
    qualityScore += clarity * 0.1;

    return Math.min(1, qualityScore);
  }

  /**
   * Normalize string for comparison
   */
  private normalizeString(str: string): string {
    return str.toLowerCase().trim().replace(/\s+/g, ' ');
  }

  /**
   * Log document verification result
   */
  private async logDocumentVerification(
    result: DocumentValidationResult,
    fraudResult: FraudDetectionResult
  ): Promise<void> {
    try {
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_email: 'document_verification_system',
          action: `DOCUMENT_VERIFICATION_${result.isValid ? 'SUCCESS' : 'FAILED'}`,
          target_type: 'document_verification',
          target_id: 'document_check',
          metadata: {
            documentType: result.documentType,
            confidence: result.confidence,
            qualityScore: result.qualityScore,
            fraudRiskScore: fraudResult.riskScore,
            validationErrors: result.validationErrors,
            fraudIndicators: result.fraudIndicators,
            timestamp: new Date().toISOString()
          },
          created_at: new Date().toISOString()
        });

    } catch (error) {
      console.error('❌ Failed to log document verification:', error);
    }
  }
}

// Create singleton instance
export const documentVerification = new DocumentVerificationSystem();

export default documentVerification;
