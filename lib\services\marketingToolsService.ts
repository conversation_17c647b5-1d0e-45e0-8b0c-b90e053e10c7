/**
 * MARKETING TOOLS SERVICE
 * 
 * Comprehensive service for affiliate marketing tools including:
 * - Lead management operations
 * - Email campaign management
 * - Referral link tracking
 * - Analytics and reporting
 * - Template processing with merge fields
 */

import { supabase, getServiceRoleClient } from '../supabase';
import { resendEmailService } from '../resendEmailService';

export interface Lead {
  id: string;
  user_id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  notes?: string;
  status: 'new' | 'contacted' | 'interested' | 'converted' | 'lost';
  source: string;
  tags?: string[];
  last_contacted_at?: string;
  converted_at?: string;
  created_at: string;
  updated_at: string;
}

export interface EmailTemplate {
  id: string;
  template_name: string;
  template_type: string;
  subject_line: string;
  html_content: string;
  text_content?: string;
  description?: string;
  is_active: boolean;
  created_by?: number;
  created_at: string;
  updated_at: string;
}

export interface EmailCampaign {
  id: string;
  user_id: number;
  template_id?: string;
  campaign_name: string;
  subject: string;
  content: string;
  recipient_count: number;
  sent_count: number;
  delivered_count: number;
  opened_count: number;
  clicked_count: number;
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'failed';
  scheduled_at?: string;
  sent_at?: string;
  created_at: string;
  updated_at: string;
}

export interface ReferralLink {
  id: string;
  user_id: number;
  link_name: string;
  base_url: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_term?: string;
  utm_content?: string;
  custom_parameters: any;
  clicks_count: number;
  conversions_count: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface LeadCommunication {
  id: string;
  lead_id: string;
  user_id: number;
  type: 'email' | 'sms' | 'call' | 'meeting' | 'note';
  subject?: string;
  content: string;
  status: 'draft' | 'sent' | 'delivered' | 'opened' | 'clicked' | 'failed';
  sent_at?: string;
  delivered_at?: string;
  opened_at?: string;
  clicked_at?: string;
  created_at: string;
  updated_at: string;
}

export class MarketingToolsService {
  private static instance: MarketingToolsService;
  private serviceClient = getServiceRoleClient();

  private constructor() {}

  public static getInstance(): MarketingToolsService {
    if (!MarketingToolsService.instance) {
      MarketingToolsService.instance = new MarketingToolsService();
    }
    return MarketingToolsService.instance;
  }

  /**
   * LEAD MANAGEMENT
   */
  async createLead(userId: number, leadData: Partial<Lead>): Promise<Lead | null> {
    try {
      const { data, error } = await this.serviceClient
        .from('affiliate_leads')
        .insert({
          ...leadData,
          user_id: userId
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating lead:', error);
      return null;
    }
  }

  async updateLead(leadId: string, userId: number, updates: Partial<Lead>): Promise<Lead | null> {
    try {
      const { data, error } = await this.serviceClient
        .from('affiliate_leads')
        .update(updates)
        .eq('id', leadId)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating lead:', error);
      return null;
    }
  }

  async deleteLead(leadId: string, userId: number): Promise<boolean> {
    try {
      const { error } = await this.serviceClient
        .from('affiliate_leads')
        .delete()
        .eq('id', leadId)
        .eq('user_id', userId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error deleting lead:', error);
      return false;
    }
  }

  async getLeads(userId: number, filters?: {
    status?: string;
    source?: string;
    search?: string;
  }): Promise<Lead[]> {
    try {
      let query = this.serviceClient
        .from('affiliate_leads')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (filters?.status) {
        query = query.eq('status', filters.status);
      }

      if (filters?.source) {
        query = query.eq('source', filters.source);
      }

      if (filters?.search) {
        query = query.or(`first_name.ilike.%${filters.search}%,last_name.ilike.%${filters.search}%,email.ilike.%${filters.search}%`);
      }

      const { data, error } = await query;

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching leads:', error);
      return [];
    }
  }

  async importLeadsFromCSV(userId: number, csvData: string): Promise<{ success: number; errors: string[] }> {
    const results = { success: 0, errors: [] as string[] };
    
    try {
      const lines = csvData.split('\n');
      const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
      
      // Validate required headers
      const requiredHeaders = ['first_name', 'last_name', 'email'];
      const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));
      
      if (missingHeaders.length > 0) {
        results.errors.push(`Missing required headers: ${missingHeaders.join(', ')}`);
        return results;
      }

      for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;

        const values = line.split(',').map(v => v.trim());
        const leadData: any = { user_id: userId };

        headers.forEach((header, index) => {
          if (values[index]) {
            leadData[header] = values[index];
          }
        });

        // Validate email
        if (!leadData.email || !leadData.email.includes('@')) {
          results.errors.push(`Row ${i + 1}: Invalid email address`);
          continue;
        }

        // Set defaults
        leadData.status = leadData.status || 'new';
        leadData.source = leadData.source || 'csv_import';

        const lead = await this.createLead(userId, leadData);
        if (lead) {
          results.success++;
        } else {
          results.errors.push(`Row ${i + 1}: Failed to create lead`);
        }
      }

      return results;
    } catch (error) {
      console.error('Error importing leads:', error);
      results.errors.push('Failed to process CSV file');
      return results;
    }
  }

  /**
   * EMAIL TEMPLATE MANAGEMENT
   */
  async getEmailTemplates(): Promise<EmailTemplate[]> {
    try {
      // Only fetch marketing templates, exclude system templates
      const systemTemplateNames = [
        'share_purchase_confirmation',
        'verification_email',
        'conversion_confirmation',
        'withdrawal_confirmation',
        'welcome_email',
        'password_reset',
        'commission_notification'
      ];

      const { data, error } = await this.serviceClient
        .from('email_templates')
        .select('*')
        .eq('is_active', true)
        .not('template_name', 'in', `(${systemTemplateNames.join(',')})`)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching email templates:', error);
      return [];
    }
  }

  /**
   * MERGE FIELD PROCESSING
   */
  processMergeFields(content: string, mergeData: {
    firstName?: string;
    lastName?: string;
    affiliateLink?: string;
    affiliateName?: string;
    companyName?: string;
    [key: string]: any;
  }): string {
    let processedContent = content;

    // Standard merge fields
    const mergeFields = {
      '{firstName}': mergeData.firstName || '',
      '{lastName}': mergeData.lastName || '',
      '{affiliateLink}': mergeData.affiliateLink || '',
      '{affiliateName}': mergeData.affiliateName || '',
      '{companyName}': mergeData.companyName || 'Aureus Alliance Holdings',
      ...mergeData
    };

    Object.entries(mergeFields).forEach(([field, value]) => {
      const regex = new RegExp(field.replace(/[{}]/g, '\\$&'), 'g');
      processedContent = processedContent.replace(regex, String(value || ''));
    });

    return processedContent;
  }

  /**
   * EMAIL CAMPAIGN MANAGEMENT
   */
  async sendEmailToLead(
    userId: number,
    leadId: string,
    templateId: string,
    customSubject?: string,
    customContent?: string
  ): Promise<boolean> {
    try {
      // Get lead details
      const { data: lead, error: leadError } = await this.serviceClient
        .from('affiliate_leads')
        .select('*')
        .eq('id', leadId)
        .eq('user_id', userId)
        .single();

      if (leadError || !lead) throw new Error('Lead not found');

      // Get template
      const { data: template, error: templateError } = await this.serviceClient
        .from('email_templates')
        .select('*')
        .eq('id', templateId)
        .single();

      if (templateError || !template) throw new Error('Template not found');

      // Get user details for merge fields
      const { data: user, error: userError } = await this.serviceClient
        .from('users')
        .select('username, first_name, last_name')
        .eq('id', userId)
        .single();

      if (userError || !user) throw new Error('User not found');

      // Prepare merge data
      const mergeData = {
        firstName: lead.first_name,
        lastName: lead.last_name,
        affiliateLink: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://www.aureus.africa'}/${user.username}`,
        affiliateName: user.first_name && user.last_name ? `${user.first_name} ${user.last_name}` : user.username,
        companyName: 'Aureus Alliance Holdings'
      };

      // Process content
      const subject = this.processMergeFields(customSubject || template.subject_line, mergeData);
      const htmlContent = this.processMergeFields(customContent || template.html_content, mergeData);
      const textContent = this.processMergeFields(template.text_content || '', mergeData);

      // Send email
      const emailResult = await resendEmailService.sendEmail({
        to: lead.email,
        subject: subject,
        html: htmlContent,
        text: textContent || undefined
      });

      if (emailResult.success) {
        // Log communication
        await this.serviceClient
          .from('lead_communications')
          .insert({
            lead_id: leadId,
            user_id: userId,
            type: 'email',
            subject: subject,
            content: htmlContent,
            status: 'sent',
            sent_at: new Date().toISOString()
          });

        // Update lead last contacted
        await this.updateLead(leadId, userId, {
          last_contacted_at: new Date().toISOString()
        });

        return true;
      }

      return false;
    } catch (error) {
      console.error('Error sending email to lead:', error);
      return false;
    }
  }

  /**
   * REFERRAL LINK MANAGEMENT
   */
  async createReferralLink(userId: number, linkData: Partial<ReferralLink>): Promise<ReferralLink | null> {
    try {
      const { data, error } = await this.serviceClient
        .from('referral_links')
        .insert({
          ...linkData,
          user_id: userId
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating referral link:', error);
      return null;
    }
  }

  async getReferralLinks(userId: number): Promise<ReferralLink[]> {
    try {
      const { data, error } = await this.serviceClient
        .from('referral_links')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching referral links:', error);
      return [];
    }
  }

  async trackLinkClick(linkId: string): Promise<boolean> {
    try {
      const { error } = await this.serviceClient
        .from('referral_links')
        .update({
          clicks_count: this.serviceClient.raw('clicks_count + 1')
        })
        .eq('id', linkId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error tracking link click:', error);
      return false;
    }
  }

  async trackLinkConversion(linkId: string): Promise<boolean> {
    try {
      const { error } = await this.serviceClient
        .from('referral_links')
        .update({
          conversions_count: this.serviceClient.raw('conversions_count + 1')
        })
        .eq('id', linkId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error tracking link conversion:', error);
      return false;
    }
  }

  /**
   * ANALYTICS AND REPORTING
   */
  async getMarketingAnalytics(userId: number): Promise<{
    leads: {
      total: number;
      new: number;
      contacted: number;
      interested: number;
      converted: number;
      lost: number;
      conversionRate: number;
    };
    links: {
      totalClicks: number;
      totalConversions: number;
      clickThroughRate: number;
      topPerformingLinks: ReferralLink[];
    };
    communications: {
      totalEmails: number;
      emailsSent: number;
      emailsDelivered: number;
      emailsOpened: number;
      emailsClicked: number;
    };
    recentActivity: any[];
  }> {
    try {
      // Get lead statistics
      const { data: leads, error: leadsError } = await this.serviceClient
        .from('affiliate_leads')
        .select('status')
        .eq('user_id', userId);

      if (leadsError) throw leadsError;

      const leadStats = {
        total: leads?.length || 0,
        new: leads?.filter(l => l.status === 'new').length || 0,
        contacted: leads?.filter(l => l.status === 'contacted').length || 0,
        interested: leads?.filter(l => l.status === 'interested').length || 0,
        converted: leads?.filter(l => l.status === 'converted').length || 0,
        lost: leads?.filter(l => l.status === 'lost').length || 0,
        conversionRate: leads?.length ? ((leads.filter(l => l.status === 'converted').length / leads.length) * 100) : 0
      };

      // Get referral link statistics
      const { data: links, error: linksError } = await this.serviceClient
        .from('referral_links')
        .select('*')
        .eq('user_id', userId);

      if (linksError) throw linksError;

      const totalClicks = links?.reduce((sum, link) => sum + link.clicks_count, 0) || 0;
      const totalConversions = links?.reduce((sum, link) => sum + link.conversions_count, 0) || 0;

      const linkStats = {
        totalClicks,
        totalConversions,
        clickThroughRate: totalClicks ? ((totalConversions / totalClicks) * 100) : 0,
        topPerformingLinks: links?.sort((a, b) => b.clicks_count - a.clicks_count).slice(0, 5) || []
      };

      // Get communication statistics
      const { data: communications, error: commError } = await this.serviceClient
        .from('lead_communications')
        .select('type, status')
        .eq('user_id', userId)
        .eq('type', 'email');

      if (commError) throw commError;

      const commStats = {
        totalEmails: communications?.length || 0,
        emailsSent: communications?.filter(c => c.status === 'sent').length || 0,
        emailsDelivered: communications?.filter(c => c.status === 'delivered').length || 0,
        emailsOpened: communications?.filter(c => c.status === 'opened').length || 0,
        emailsClicked: communications?.filter(c => c.status === 'clicked').length || 0
      };

      // Get recent activity (last 10 activities)
      const { data: recentActivity, error: activityError } = await this.serviceClient
        .from('lead_communications')
        .select(`
          *,
          affiliate_leads!inner(first_name, last_name, email)
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(10);

      if (activityError) throw activityError;

      return {
        leads: leadStats,
        links: linkStats,
        communications: commStats,
        recentActivity: recentActivity || []
      };

    } catch (error) {
      console.error('Error fetching marketing analytics:', error);
      return {
        leads: { total: 0, new: 0, contacted: 0, interested: 0, converted: 0, lost: 0, conversionRate: 0 },
        links: { totalClicks: 0, totalConversions: 0, clickThroughRate: 0, topPerformingLinks: [] },
        communications: { totalEmails: 0, emailsSent: 0, emailsDelivered: 0, emailsOpened: 0, emailsClicked: 0 },
        recentActivity: []
      };
    }
  }

  /**
   * BULK OPERATIONS
   */
  async bulkDeleteLeads(userId: number, leadIds: string[]): Promise<{ success: number; errors: number }> {
    let success = 0;
    let errors = 0;

    for (const leadId of leadIds) {
      const result = await this.deleteLead(leadId, userId);
      if (result) {
        success++;
      } else {
        errors++;
      }
    }

    return { success, errors };
  }

  async exportLeadsToCSV(userId: number, filters?: any): Promise<string> {
    try {
      const leads = await this.getLeads(userId, filters);

      const headers = ['First Name', 'Last Name', 'Email', 'Phone', 'Status', 'Source', 'Notes', 'Created Date'];
      const csvRows = [headers.join(',')];

      leads.forEach(lead => {
        const row = [
          lead.first_name,
          lead.last_name,
          lead.email,
          lead.phone || '',
          lead.status,
          lead.source,
          (lead.notes || '').replace(/,/g, ';'), // Replace commas to avoid CSV issues
          new Date(lead.created_at).toLocaleDateString()
        ];
        csvRows.push(row.join(','));
      });

      return csvRows.join('\n');
    } catch (error) {
      console.error('Error exporting leads to CSV:', error);
      return '';
    }
  }

  /**
   * COMMUNICATION LOGGING
   */
  async logCommunication(
    leadId: string,
    userId: number,
    type: 'email' | 'sms' | 'call' | 'meeting' | 'note',
    content: string,
    subject?: string
  ): Promise<LeadCommunication | null> {
    try {
      const { data, error } = await this.serviceClient
        .from('lead_communications')
        .insert({
          lead_id: leadId,
          user_id: userId,
          type,
          subject,
          content,
          status: type === 'note' ? 'sent' : 'draft'
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error logging communication:', error);
      return null;
    }
  }

  async getCommunicationHistory(leadId: string, userId: number): Promise<LeadCommunication[]> {
    try {
      const { data, error } = await this.serviceClient
        .from('lead_communications')
        .select('*')
        .eq('lead_id', leadId)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching communication history:', error);
      return [];
    }
  }
}
