/**
 * COMMISSION EMAIL TEMPLATE
 * 
 * Template for commission-related emails including
 * commission earned notifications and withdrawal confirmations.
 */

import { BaseEmailTemplate } from './BaseEmailTemplate';
import { CommissionEarnedData, ConversionNotificationData, WithdrawalNotificationData } from '../types/EmailTypes';

export class CommissionTemplate extends BaseEmailTemplate<CommissionEarnedData> {
  protected emailType = 'commission_earned' as const;

  protected generateSubject(data: CommissionEarnedData): string {
    return `Commission Earned - ${this.formatCurrency(data.usdtCommission)} USDT + ${this.formatNumber(data.shareCommission)} Shares`;
  }

  protected generateBody(data: CommissionEarnedData): string {
    return `
      <div class="email-body">
        <h2 style="color: ${this.brandingConfig.colors.primary}; margin-bottom: 20px; text-align: center;">
          💰 Commission Earned!
        </h2>
        
        <p style="margin-bottom: 15px; color: ${this.brandingConfig.colors.text}; font-size: 18px;">
          Hello ${data.fullName},
        </p>
        
        <p style="margin-bottom: 20px; color: ${this.brandingConfig.colors.text};">
          Great news! You've earned a commission from a successful referral. ${data.referredUserName} has made a purchase, and you've been rewarded for your referral.
        </p>
        
        <div class="alert alert-success">
          <h3 style="margin-top: 0; margin-bottom: 15px; color: #34d399;">
            🎯 Commission Details
          </h3>
          <table class="table">
            <tr>
              <td class="label">USDT Commission:</td>
              <td class="value" style="color: #60a5fa;">${this.formatCurrency(data.usdtCommission)}</td>
            </tr>
            <tr>
              <td class="label">Share Commission:</td>
              <td class="value" style="color: #fbbf24;">${this.formatNumber(data.shareCommission)} shares</td>
            </tr>
            <tr>
              <td class="label">Referred User:</td>
              <td class="value">${data.referredUserName}</td>
            </tr>
            <tr>
              <td class="label">Purchase Amount:</td>
              <td class="value">${this.formatCurrency(data.purchaseAmount)}</td>
            </tr>
            <tr>
              <td class="label">Transaction ID:</td>
              <td class="value" style="font-family: monospace;">${data.transactionId}</td>
            </tr>
            <tr>
              <td class="label">Earned Date:</td>
              <td class="value">${this.formatDate(new Date())}</td>
            </tr>
          </table>
        </div>
        
        <div class="alert alert-info">
          <h4 style="margin-top: 0; margin-bottom: 15px; color: #60a5fa;">
            📊 Commission Breakdown
          </h4>
          <p style="margin: 0 0 10px; color: #60a5fa;">
            Your commission is calculated based on our referral program structure:
          </p>
          <ul style="margin: 0; padding-left: 20px; color: #60a5fa;">
            <li style="margin-bottom: 8px;">USDT Commission: ${((data.usdtCommission / data.purchaseAmount) * 100).toFixed(1)}% of purchase amount</li>
            <li style="margin-bottom: 8px;">Share Commission: Additional bonus shares for successful referrals</li>
            <li style="margin-bottom: 8px;">Commissions are added to your account balance immediately</li>
          </ul>
        </div>
        
        <div style="margin: 25px 0; padding: 20px; background-color: rgba(16, 185, 129, 0.1); border-radius: 8px; border-left: 4px solid ${this.brandingConfig.colors.success};">
          <h4 style="margin-top: 0; margin-bottom: 10px; color: ${this.brandingConfig.colors.success};">
            🚀 Keep Growing Your Network!
          </h4>
          <p style="margin: 0; color: ${this.brandingConfig.colors.text}; font-size: 14px;">
            Continue referring friends and family to earn more commissions. The more people you help join our platform, 
            the more you earn while building a sustainable investment community.
          </p>
        </div>
        
        <div class="text-center mt-4">
          <a href="${this.brandingConfig.websiteUrl}/dashboard/referrals" class="btn btn-primary" style="margin-right: 10px;">
            View Referrals
          </a>
          <a href="${this.brandingConfig.websiteUrl}/dashboard/commissions" class="btn btn-secondary">
            Withdraw Commissions
          </a>
        </div>
      </div>
    `;
  }

  protected generateTextContent(data: CommissionEarnedData): string {
    return `
Commission Earned!

Hello ${data.fullName},

Great news! You've earned a commission from a successful referral. ${data.referredUserName} has made a purchase, and you've been rewarded for your referral.

Commission Details:
- USDT Commission: ${this.formatCurrency(data.usdtCommission)}
- Share Commission: ${this.formatNumber(data.shareCommission)} shares
- Referred User: ${data.referredUserName}
- Purchase Amount: ${this.formatCurrency(data.purchaseAmount)}
- Transaction ID: ${data.transactionId}
- Earned Date: ${this.formatDate(new Date())}

Commission Breakdown:
Your commission is calculated based on our referral program structure:
- USDT Commission: ${((data.usdtCommission / data.purchaseAmount) * 100).toFixed(1)}% of purchase amount
- Share Commission: Additional bonus shares for successful referrals
- Commissions are added to your account balance immediately

Keep Growing Your Network!
Continue referring friends and family to earn more commissions. The more people you help join our platform, the more you earn while building a sustainable investment community.

View Referrals: ${this.brandingConfig.websiteUrl}/dashboard/referrals
Withdraw Commissions: ${this.brandingConfig.websiteUrl}/dashboard/commissions

${this.brandingConfig.tagline}
${this.brandingConfig.companyName}
    `.trim();
  }

  /**
   * Generate conversion notification template
   */
  public generateConversionTemplate(data: ConversionNotificationData) {
    return {
      type: 'conversion' as const,
      subject: `USDT to Shares Conversion Completed - ${this.formatNumber(data.sharesReceived)} Shares`,
      htmlContent: this.generateConversionHtml(data),
      textContent: this.generateConversionText(data)
    };
  }

  private generateConversionHtml(data: ConversionNotificationData): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>USDT to Shares Conversion Completed</title>
        <style>${this.getEmailStyles()}</style>
      </head>
      <body>
        <div class="email-container">
          ${this.generateHeader()}
          <div class="email-body">
            <h2 style="color: ${this.brandingConfig.colors.primary}; margin-bottom: 20px; text-align: center;">
              🔄 Conversion Completed!
            </h2>
            
            <p style="margin-bottom: 15px; color: ${this.brandingConfig.colors.text}; font-size: 18px;">
              Hello ${data.fullName},
            </p>
            
            <p style="margin-bottom: 20px; color: ${this.brandingConfig.colors.text};">
              Your USDT to shares conversion has been successfully processed. Your commission balance has been converted to shares in your portfolio.
            </p>
            
            <div class="alert alert-success">
              <h3 style="margin-top: 0; margin-bottom: 15px; color: #34d399;">
                💱 Conversion Details
              </h3>
              <table class="table">
                <tr>
                  <td class="label">USDT Converted:</td>
                  <td class="value">${this.formatCurrency(data.usdtAmount)}</td>
                </tr>
                <tr>
                  <td class="label">Shares Received:</td>
                  <td class="value">${this.formatNumber(data.sharesReceived)}</td>
                </tr>
                <tr>
                  <td class="label">Share Price:</td>
                  <td class="value">${this.formatCurrency(data.sharePrice)}</td>
                </tr>
                <tr>
                  <td class="label">Transaction ID:</td>
                  <td class="value" style="font-family: monospace;">${data.transactionId}</td>
                </tr>
                <tr>
                  <td class="label">Conversion Date:</td>
                  <td class="value">${this.formatDate(new Date())}</td>
                </tr>
              </table>
            </div>
            
            <div class="text-center mt-4">
              <a href="${this.brandingConfig.websiteUrl}/dashboard/portfolio" class="btn btn-primary">
                View Portfolio
              </a>
            </div>
          </div>
          ${this.generateFooter()}
        </div>
      </body>
      </html>
    `;
  }

  private generateConversionText(data: ConversionNotificationData): string {
    return `
Conversion Completed!

Hello ${data.fullName},

Your USDT to shares conversion has been successfully processed. Your commission balance has been converted to shares in your portfolio.

Conversion Details:
- USDT Converted: ${this.formatCurrency(data.usdtAmount)}
- Shares Received: ${this.formatNumber(data.sharesReceived)}
- Share Price: ${this.formatCurrency(data.sharePrice)}
- Transaction ID: ${data.transactionId}
- Conversion Date: ${this.formatDate(new Date())}

View Portfolio: ${this.brandingConfig.websiteUrl}/dashboard/portfolio

${this.brandingConfig.tagline}
${this.brandingConfig.companyName}
    `.trim();
  }
}
