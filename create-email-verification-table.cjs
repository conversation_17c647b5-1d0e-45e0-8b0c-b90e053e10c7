#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createEmailVerificationTable() {
  console.log('🔍 Checking email_verification_codes table...');
  
  try {
    // First, try to query the table to see if it exists
    const { data, error } = await supabase
      .from('email_verification_codes')
      .select('*')
      .limit(1);
    
    if (error && error.code === 'PGRST116') {
      console.log('❌ Table does not exist, creating it...');
      
      // Create the table using raw SQL
      const createTableSQL = `
        CREATE TABLE IF NOT EXISTS email_verification_codes (
          id SERIAL PRIMARY KEY,
          user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
          email TEXT NOT NULL,
          code_hash TEXT NOT NULL,
          purpose TEXT NOT NULL CHECK (purpose IN ('registration', 'account_update', 'withdrawal', 'password_reset')),
          expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
          attempts INTEGER DEFAULT 0,
          verified_at TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS idx_email_verification_codes_user_id ON email_verification_codes(user_id);
        CREATE INDEX IF NOT EXISTS idx_email_verification_codes_email ON email_verification_codes(email);
        CREATE INDEX IF NOT EXISTS idx_email_verification_codes_purpose ON email_verification_codes(purpose);
        CREATE INDEX IF NOT EXISTS idx_email_verification_codes_expires_at ON email_verification_codes(expires_at);
      `;
      
      // Execute the SQL directly
      const { error: createError } = await supabase.rpc('exec_sql', { sql_query: createTableSQL });
      
      if (createError) {
        console.error('❌ Failed to create table via RPC:', createError);
        
        // Try alternative method - direct SQL execution
        console.log('🔄 Trying alternative table creation method...');
        
        const { error: altError } = await supabase
          .from('information_schema.tables')
          .select('table_name')
          .eq('table_name', 'email_verification_codes');
        
        if (altError) {
          console.error('❌ Alternative method also failed:', altError);
          console.log('📝 Please create the table manually using this SQL:');
          console.log(createTableSQL);
        }
      } else {
        console.log('✅ Table created successfully via RPC');
      }
    } else if (error) {
      console.error('❌ Other table error:', error);
    } else {
      console.log('✅ email_verification_codes table already exists');
      console.log('📊 Sample data:', data);
    }
    
    // Test inserting a record to verify the table works
    console.log('🧪 Testing table functionality...');
    
    const testRecord = {
      user_id: 102, // Use a known user ID
      email: '<EMAIL>',
      code_hash: 'test_hash',
      purpose: 'password_reset',
      expires_at: new Date(Date.now() + 15 * 60 * 1000).toISOString(),
      attempts: 0
    };
    
    const { data: insertData, error: insertError } = await supabase
      .from('email_verification_codes')
      .insert(testRecord)
      .select()
      .single();
    
    if (insertError) {
      console.error('❌ Test insert failed:', insertError);
    } else {
      console.log('✅ Test insert successful:', insertData);
      
      // Clean up test record
      await supabase
        .from('email_verification_codes')
        .delete()
        .eq('id', insertData.id);
      
      console.log('🧹 Test record cleaned up');
    }
    
  } catch (e) {
    console.error('❌ Exception:', e.message);
  }
}

createEmailVerificationTable()
  .then(() => {
    console.log('✅ Email verification table setup complete');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  });
