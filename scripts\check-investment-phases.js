// Quick script to check the current status of investment phases
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('Please ensure REACT_APP_SUPABASE_URL and REACT_APP_SUPABASE_ANON_KEY are set in your .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkInvestmentPhases() {
  console.log('🔍 Checking Investment Phases Status...\n');

  try {
    // Get all phases
    const { data: phases, error } = await supabase
      .from('investment_phases')
      .select('*')
      .order('phase_number');

    if (error) {
      console.error('❌ Error fetching phases:', error.message);
      console.log('\n💡 Possible solutions:');
      console.log('   1. Check your Supabase credentials in .env file');
      console.log('   2. Ensure the investment_phases table exists');
      console.log('   3. Run: node scripts/setup-investment-phases.js');
      return;
    }

    if (!phases || phases.length === 0) {
      console.log('⚠️ No investment phases found in database');
      console.log('\n💡 To create phases, run:');
      console.log('   node scripts/setup-investment-phases.js');
      return;
    }

    console.log(`✅ Found ${phases.length} investment phases:\n`);

    // Display phase information
    phases.forEach(phase => {
      const status = phase.is_active ? '🟢 ACTIVE' : '⚪ INACTIVE';
      const progress = phase.shares_sold ? 
        `${((phase.shares_sold / phase.total_shares_available) * 100).toFixed(1)}% sold` : 
        '0% sold';
      
      console.log(`${status} ${phase.phase_name} (Phase ${phase.phase_number})`);
      console.log(`   💰 Price: $${Number(phase.price_per_share).toFixed(2)} per share`);
      console.log(`   📊 Shares: ${phase.shares_sold || 0}/${phase.total_shares_available} (${progress})`);
      if (phase.start_date) {
        console.log(`   📅 Started: ${new Date(phase.start_date).toLocaleDateString()}`);
      }
      console.log('');
    });

    // Show active phase info
    const activePhase = phases.find(p => p.is_active);
    if (activePhase) {
      console.log('🎯 Current Active Phase:');
      console.log(`   ${activePhase.phase_name} - $${Number(activePhase.price_per_share).toFixed(2)} per share`);
      
      // Commission structure based on phase
      if (activePhase.phase_number === 0) {
        console.log('   🏆 Commission: 15% USDT + 15% Bonus NFT Shares');
      } else {
        console.log('   🏆 Commission: 15% USDT Only');
      }
    } else {
      console.log('⚠️ No active phase found');
      console.log('   You may need to activate a phase in your admin dashboard');
    }

    console.log('\n📈 Total Investment Capacity:');
    const totalShares = phases.reduce((sum, p) => sum + p.total_shares_available, 0);
    const totalSold = phases.reduce((sum, p) => sum + (p.shares_sold || 0), 0);
    const totalValue = phases.reduce((sum, p) => sum + (p.total_shares_available * p.price_per_share), 0);
    
    console.log(`   Total Shares Available: ${totalShares.toLocaleString()}`);
    console.log(`   Total Shares Sold: ${totalSold.toLocaleString()}`);
    console.log(`   Total Investment Capacity: $${totalValue.toLocaleString()}`);

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the check
checkInvestmentPhases();
