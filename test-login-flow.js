#!/usr/bin/env node

/**
 * TEST THE ACTUAL LOGIN FLOW FOR TELEGRAM ID 1270124602
 * 
 * This simulates exactly what the login form does
 */

import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const verifyPassword = async (password, hash) => {
  try {
    const isValid = await bcrypt.compare(password, hash);
    return isValid;
  } catch (error) {
    console.error('Password verification error:', error);
    return false;
  }
};

const testLoginFlow = async () => {
  try {
    console.log('🧪 TESTING LOGIN FLOW FOR TELEGRAM ID 1270124602\n');

    const telegramId = '1270124602';
    const password = 'Gunst0n5o0!@#';

    console.log('📋 Step 1: Telegram ID Verification (what the form does)...');

    // Step 1: Verify Telegram ID (simulate form verification)
    const telegramIdNum = parseInt(telegramId);
    
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramIdNum)
      .maybeSingle();

    if (telegramError || !telegramUser) {
      console.log('❌ Telegram ID verification failed:', telegramError?.message);
      return;
    }

    console.log('✅ Telegram user found:', telegramUser.username);

    // Step 2: Check for complete profile (what the form does)
    let userWithCompleteProfile = null;
    
    if (telegramUser.user_id) {
      console.log('🔗 Checking linked user...');
      const { data: linkedUser, error: linkedUserError } = await supabase
        .from('users')
        .select('*')
        .eq('id', telegramUser.user_id)
        .maybeSingle();

      if (!linkedUserError && linkedUser) {
        userWithCompleteProfile = linkedUser;
        console.log('✅ Found linked user:', linkedUser.email);
      }
    } else {
      console.log('🔍 Checking users table by telegram_id...');
      const { data: userByTelegramId, error: userByTelegramIdError } = await supabase
        .from('users')
        .select('*')
        .eq('telegram_id', telegramIdNum)
        .maybeSingle();

      if (!userByTelegramIdError && userByTelegramId) {
        userWithCompleteProfile = userByTelegramId;
        console.log('✅ Found user by telegram_id:', userByTelegramId.email);
      }
    }

    if (!userWithCompleteProfile) {
      console.log('❌ No complete profile found');
      return;
    }

    // Step 3: Check if profile completion is needed
    const needsCompletion = !userWithCompleteProfile.email ||
                           !userWithCompleteProfile.password_hash ||
                           !userWithCompleteProfile.full_name ||
                           !userWithCompleteProfile.phone ||
                           !userWithCompleteProfile.country_of_residence;

    console.log(`📋 Profile completion needed: ${needsCompletion ? 'YES' : 'NO'}`);

    if (needsCompletion) {
      console.log('❌ Profile completion required');
      return;
    }

    console.log('\n📋 Step 2: Password Verification (what the login does)...');

    // Step 4: Verify password (simulate login submission)
    if (!userWithCompleteProfile.password_hash) {
      console.log('❌ No password hash found');
      return;
    }

    const passwordValid = await verifyPassword(password, userWithCompleteProfile.password_hash);
    console.log(`Password verification: ${passwordValid ? '✅ VALID' : '❌ INVALID'}`);

    if (!passwordValid) {
      console.log('❌ Login would fail - invalid password');
      return;
    }

    console.log('\n📋 Step 3: Session Creation (what happens after login)...');

    // Step 5: Create session data (what the form would do)
    const sessionData = {
      userId: userWithCompleteProfile.id,
      username: userWithCompleteProfile.username,
      email: userWithCompleteProfile.email,
      fullName: userWithCompleteProfile.full_name,
      phone: userWithCompleteProfile.phone,
      address: userWithCompleteProfile.address,
      country: userWithCompleteProfile.country_of_residence,
      isActive: userWithCompleteProfile.is_active,
      isVerified: userWithCompleteProfile.is_verified,
      isAdmin: userWithCompleteProfile.is_admin,
      telegramId: telegramUser.telegram_id,
      telegramUsername: telegramUser.username,
      telegramConnected: true,
      telegramRegistered: telegramUser.is_registered,
      loginMethod: 'telegram',
      sessionStart: new Date().toISOString(),
      lastActivity: new Date().toISOString()
    };

    console.log('✅ Session data created successfully');
    console.log('📋 User data:', {
      id: sessionData.userId,
      username: sessionData.username,
      email: sessionData.email,
      telegramId: sessionData.telegramId
    });

    console.log('\n🎉 LOGIN FLOW TEST SUCCESSFUL!');
    console.log('✅ Telegram ID verification: PASSED');
    console.log('✅ Profile completeness check: PASSED');
    console.log('✅ Password verification: PASSED');
    console.log('✅ Session creation: PASSED');
    console.log('\n🔄 The login form should work normally now!');

  } catch (error) {
    console.error('❌ Login flow test failed:', error);
  }
};

testLoginFlow();
