#!/usr/bin/env node

/**
 * Commission Backfill System
 * 
 * This script creates a system to detect and fix missing commissions
 * for share purchases that bypassed the normal payment approval flow.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ Missing SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function commissionBackfillSystem() {
  try {
    console.log('🔧 Commission Backfill System\n');
    
    // Step 1: Find share purchases without corresponding commission transactions
    console.log('🔍 Step 1: Finding Share Purchases Without Commissions');
    
    const { data: allPurchases, error: purchaseError } = await supabase
      .from('aureus_share_purchases')
      .select(`
        *,
        users(id, username, full_name, email)
      `)
      .eq('status', 'active')
      .gt('total_amount', 0) // Exclude negative/transfer purchases
      .order('created_at', { ascending: false });
    
    if (purchaseError) {
      console.error('❌ Error fetching share purchases:', purchaseError);
      return;
    }
    
    console.log(`   Total positive share purchases: ${allPurchases.length}`);
    
    // Step 2: Check each purchase for corresponding commission
    const purchasesWithoutCommission = [];
    
    for (const purchase of allPurchases) {
      const { data: existingCommission, error: commError } = await supabase
        .from('commission_transactions')
        .select('*')
        .eq('referred_id', purchase.user_id)
        .eq('share_purchase_amount', purchase.total_amount)
        .gte('payment_date', purchase.created_at)
        .lte('payment_date', new Date(new Date(purchase.created_at).getTime() + 24 * 60 * 60 * 1000).toISOString()); // Within 24 hours
      
      if (commError) {
        console.error(`❌ Error checking commission for purchase ${purchase.id}:`, commError);
        continue;
      }
      
      if (existingCommission.length === 0) {
        purchasesWithoutCommission.push(purchase);
      }
    }
    
    console.log(`   Purchases without commission: ${purchasesWithoutCommission.length}`);
    
    if (purchasesWithoutCommission.length === 0) {
      console.log('✅ All share purchases have corresponding commissions');
      return;
    }
    
    // Step 3: Analyze missing commissions
    console.log('\n📊 Step 3: Analyzing Missing Commissions');
    
    let totalMissingUSDT = 0;
    let totalMissingShares = 0;
    
    console.log('\n   Purchases missing commissions:');
    purchasesWithoutCommission.forEach((purchase, index) => {
      const missingUSDT = purchase.total_amount * 0.15;
      const missingShares = purchase.shares_purchased * 0.15;
      
      totalMissingUSDT += missingUSDT;
      totalMissingShares += missingShares;
      
      console.log(`   ${index + 1}. User: ${purchase.users?.full_name || purchase.users?.username} (ID: ${purchase.user_id})`);
      console.log(`      Purchase: ${purchase.shares_purchased} shares, $${purchase.total_amount}`);
      console.log(`      Date: ${purchase.created_at}`);
      console.log(`      Missing Commission: $${missingUSDT} USDT + ${missingShares} shares`);
      console.log('');
    });
    
    console.log(`   Total Missing Commission:`);
    console.log(`   • USDT: $${totalMissingUSDT.toFixed(2)}`);
    console.log(`   • Shares: ${totalMissingShares.toFixed(2)}`);
    
    // Step 4: Check which users have sponsors
    console.log('\n🔗 Step 4: Checking Sponsor Relationships');
    
    const purchasesWithSponsors = [];
    const purchasesWithoutSponsors = [];
    
    for (const purchase of purchasesWithoutCommission) {
      const { data: referral, error: refError } = await supabase
        .from('referrals')
        .select(`
          *,
          referrer:users!referrer_id(id, username, full_name)
        `)
        .eq('referred_id', purchase.user_id)
        .eq('status', 'active')
        .single();
      
      if (refError || !referral) {
        purchasesWithoutSponsors.push(purchase);
      } else {
        purchasesWithSponsors.push({ purchase, referral });
      }
    }
    
    console.log(`   Purchases with sponsors: ${purchasesWithSponsors.length}`);
    console.log(`   Purchases without sponsors: ${purchasesWithoutSponsors.length}`);
    
    // Step 5: Create backfill plan
    console.log('\n📋 Step 5: Commission Backfill Plan');
    
    if (purchasesWithSponsors.length > 0) {
      console.log('\n   ✅ Can backfill commissions for purchases with sponsors:');
      purchasesWithSponsors.forEach((item, index) => {
        const { purchase, referral } = item;
        const usdtCommission = purchase.total_amount * 0.15;
        const shareCommission = purchase.shares_purchased * 0.15;
        
        console.log(`   ${index + 1}. ${purchase.users?.full_name || purchase.users?.username}`);
        console.log(`      → Sponsor: ${referral.referrer?.full_name || referral.referrer?.username}`);
        console.log(`      → Commission: $${usdtCommission} USDT + ${shareCommission} shares`);
      });
    }
    
    if (purchasesWithoutSponsors.length > 0) {
      console.log('\n   ⚠️  Need to assign sponsors first:');
      purchasesWithoutSponsors.forEach((purchase, index) => {
        console.log(`   ${index + 1}. ${purchase.users?.full_name || purchase.users?.username} (ID: ${purchase.user_id})`);
        console.log(`      → Needs TTTFOUNDER as sponsor`);
      });
    }
    
    // Step 6: Implementation recommendations
    console.log('\n💡 Step 6: Implementation Recommendations');
    console.log('');
    console.log('🔧 To implement automatic commission backfill:');
    console.log('');
    console.log('1. **Immediate Actions:**');
    console.log('   • Assign TTTFOUNDER as sponsor for users without sponsors');
    console.log('   • Run commission backfill for all missing commissions');
    console.log('   • Update commission balances accordingly');
    console.log('');
    console.log('2. **Prevention Measures:**');
    console.log('   • Ensure PaymentManager.ensureShareholderSponsor() is called');
    console.log('   • Add commission calculation to direct share purchase flows');
    console.log('   • Implement database triggers for automatic commission calculation');
    console.log('');
    console.log('3. **Monitoring:**');
    console.log('   • Regular audits to detect missing commissions');
    console.log('   • Alerts when share purchases occur without commission transactions');
    console.log('   • Dashboard showing commission coverage percentage');
    console.log('');
    console.log('4. **Database Integrity:**');
    console.log('   • Add foreign key constraints where appropriate');
    console.log('   • Implement validation rules for commission calculations');
    console.log('   • Regular backup and consistency checks');
    
    // Step 7: Generate backfill script template
    console.log('\n📝 Step 7: Backfill Script Template');
    console.log('');
    console.log('// Example backfill implementation:');
    console.log('/*');
    console.log('async function backfillMissingCommissions() {');
    console.log('  for (const item of purchasesWithSponsors) {');
    console.log('    const { purchase, referral } = item;');
    console.log('    const usdtCommission = purchase.total_amount * 0.15;');
    console.log('    const shareCommission = purchase.shares_purchased * 0.15;');
    console.log('    ');
    console.log('    // Create commission transaction');
    console.log('    await supabase.from("commission_transactions").insert({');
    console.log('      referrer_id: referral.referrer_id,');
    console.log('      referred_id: purchase.user_id,');
    console.log('      commission_rate: 15.00,');
    console.log('      share_purchase_amount: purchase.total_amount,');
    console.log('      usdt_commission: usdtCommission,');
    console.log('      share_commission: shareCommission,');
    console.log('      status: "approved",');
    console.log('      payment_date: new Date().toISOString()');
    console.log('    });');
    console.log('    ');
    console.log('    // Update commission balance');
    console.log('    // ... balance update logic');
    console.log('  }');
    console.log('}');
    console.log('*/');
    
  } catch (error) {
    console.error('❌ Backfill system analysis failed:', error);
  }
}

commissionBackfillSystem();
