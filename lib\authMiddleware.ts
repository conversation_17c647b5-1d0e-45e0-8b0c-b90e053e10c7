/**
 * SECURE AUTHENTICATION MIDDLEWARE
 * 
 * This module provides secure authentication middleware with rate limiting,
 * input validation, and bot protection for the Aureus Africa platform.
 */

import { authRateLimiter, registrationRateLimiter, getClientIdentifier } from './rateLimiting';
import { supabase } from './supabase';

interface AuthRequest extends Request {
  rateLimiter?: any;
  rateLimitIdentifier?: string;
  isBotRequest?: boolean;
  user?: any;
  securityContext?: any;
}

interface AuthResponse extends Response {
  setHeader: (name: string, value: string | number) => void;
  status: (code: number) => AuthResponse;
  json: (data: any) => AuthResponse;
}

/**
 * Secure login middleware with rate limiting
 */
export const secureLoginMiddleware = async (
  req: AuthRequest, 
  res: AuthResponse, 
  next: () => void
) => {
  try {
    console.log('🔐 Processing secure login request...');

    // Get client identifier
    const identifier = getClientIdentifier(req);
    
    // Check if this is a bot request
    const isBotRequest = req.headers.get('authorization')?.includes('service_role') || 
                        req.headers.get('user-agent')?.includes('aureus-bot') ||
                        req.headers.get('x-bot-request') === 'true';

    // Apply rate limiting
    const rateLimitResult = authRateLimiter.checkRateLimit(identifier, isBotRequest);

    // Set rate limit headers
    res.setHeader('X-RateLimit-Limit', '5');
    res.setHeader('X-RateLimit-Remaining', rateLimitResult.remaining.toString());
    res.setHeader('X-RateLimit-Reset', new Date(rateLimitResult.resetTime).toISOString());

    if (!rateLimitResult.allowed) {
      console.log(`🚫 Rate limit exceeded for ${identifier}`);
      res.setHeader('Retry-After', rateLimitResult.retryAfter?.toString() || '1800');
      
      // Log security event
      await logSecurityEvent('RATE_LIMIT_EXCEEDED', identifier, {
        endpoint: 'login',
        attempts: rateLimitResult.remaining,
        retryAfter: rateLimitResult.retryAfter
      });

      return res.status(429).json({
        error: 'Too many login attempts',
        message: 'Please wait before trying again',
        retryAfter: rateLimitResult.retryAfter
      });
    }

    // Add security context to request
    req.rateLimiter = authRateLimiter;
    req.rateLimitIdentifier = identifier;
    req.isBotRequest = isBotRequest;

    console.log(`✅ Login rate limit check passed for ${identifier}`);
    next();

  } catch (error) {
    console.error('❌ Secure login middleware error:', error);
    return res.status(500).json({ error: 'Authentication system error' });
  }
};

/**
 * Secure registration middleware with rate limiting
 */
export const secureRegistrationMiddleware = async (
  req: AuthRequest, 
  res: AuthResponse, 
  next: () => void
) => {
  try {
    console.log('📝 Processing secure registration request...');

    // Get client identifier
    const identifier = getClientIdentifier(req);
    
    // Check if this is a bot request
    const isBotRequest = req.headers.get('authorization')?.includes('service_role') || 
                        req.headers.get('user-agent')?.includes('aureus-bot') ||
                        req.headers.get('x-bot-request') === 'true';

    // Apply registration rate limiting (stricter)
    const rateLimitResult = registrationRateLimiter.checkRateLimit(identifier, isBotRequest);

    // Set rate limit headers
    res.setHeader('X-RateLimit-Limit', '3');
    res.setHeader('X-RateLimit-Remaining', rateLimitResult.remaining.toString());
    res.setHeader('X-RateLimit-Reset', new Date(rateLimitResult.resetTime).toISOString());

    if (!rateLimitResult.allowed) {
      console.log(`🚫 Registration rate limit exceeded for ${identifier}`);
      res.setHeader('Retry-After', rateLimitResult.retryAfter?.toString() || '3600');
      
      // Log security event
      await logSecurityEvent('REGISTRATION_RATE_LIMIT_EXCEEDED', identifier, {
        endpoint: 'registration',
        retryAfter: rateLimitResult.retryAfter
      });

      return res.status(429).json({
        error: 'Too many registration attempts',
        message: 'Please wait before trying to register again',
        retryAfter: rateLimitResult.retryAfter
      });
    }

    // Add security context to request
    req.rateLimiter = registrationRateLimiter;
    req.rateLimitIdentifier = identifier;
    req.isBotRequest = isBotRequest;

    console.log(`✅ Registration rate limit check passed for ${identifier}`);
    next();

  } catch (error) {
    console.error('❌ Secure registration middleware error:', error);
    return res.status(500).json({ error: 'Registration system error' });
  }
};

/**
 * Record authentication success (resets rate limit)
 */
export const recordAuthSuccess = (req: AuthRequest) => {
  if (req.rateLimiter && req.rateLimitIdentifier) {
    req.rateLimiter.recordSuccess(req.rateLimitIdentifier);
    console.log(`✅ Auth success recorded for ${req.rateLimitIdentifier}`);
  }
};

/**
 * Record authentication failure (increments rate limit)
 */
export const recordAuthFailure = (req: AuthRequest, reason: string) => {
  if (req.rateLimiter && req.rateLimitIdentifier) {
    const result = req.rateLimiter.recordFailure(req.rateLimitIdentifier, req.isBotRequest);
    
    // Log security event for failed attempts
    logSecurityEvent('AUTH_FAILURE', req.rateLimitIdentifier, {
      reason,
      remaining: result.remaining,
      blocked: !result.allowed
    });

    console.log(`❌ Auth failure recorded for ${req.rateLimitIdentifier}: ${reason}`);
    return result;
  }
  return { allowed: true, remaining: 5 };
};

/**
 * Input validation middleware
 */
export const validateAuthInput = (req: AuthRequest, res: AuthResponse, next: () => void) => {
  try {
    const body = req.body;

    // Basic input validation
    if (!body) {
      return res.status(400).json({ error: 'Request body required' });
    }

    // Email validation (if present)
    if (body.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(body.email) || body.email.length > 255) {
        return res.status(400).json({ error: 'Invalid email format' });
      }

      // Check for XSS attempts
      if (body.email.includes('<') || body.email.includes('>') || body.email.includes('script')) {
        logSecurityEvent('XSS_ATTEMPT', getClientIdentifier(req), {
          field: 'email',
          value: body.email.substring(0, 50)
        });
        return res.status(400).json({ error: 'Invalid email format' });
      }
    }

    // Password validation (if present)
    if (body.password) {
      if (typeof body.password !== 'string' || body.password.length < 8 || body.password.length > 128) {
        return res.status(400).json({ error: 'Invalid password format' });
      }
    }

    // Telegram ID validation (if present)
    if (body.telegramId) {
      const telegramId = parseInt(body.telegramId);
      if (isNaN(telegramId) || telegramId <= 0) {
        return res.status(400).json({ error: 'Invalid Telegram ID format' });
      }
    }

    // Sanitize string inputs
    for (const [key, value] of Object.entries(body)) {
      if (typeof value === 'string') {
        // Remove potentially dangerous characters
        body[key] = value.replace(/[<>{}$\x00-\x1f]/g, '').trim();
      }
    }

    console.log('✅ Input validation passed');
    next();

  } catch (error) {
    console.error('❌ Input validation error:', error);
    return res.status(400).json({ error: 'Invalid input format' });
  }
};

/**
 * Security headers middleware
 */
export const addSecurityHeaders = (req: AuthRequest, res: AuthResponse, next: () => void) => {
  // Add security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Add HSTS for HTTPS
  if (req.headers.get('x-forwarded-proto') === 'https') {
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  }

  next();
};

/**
 * Log security events to database
 */
export const logSecurityEvent = async (
  eventType: string, 
  identifier: string, 
  metadata: any,
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'MEDIUM'
) => {
  try {
    await supabase
      .from('admin_audit_logs')
      .insert({
        admin_email: 'security_system',
        action: eventType,
        target_type: 'security_event',
        target_id: identifier,
        metadata: {
          ...metadata,
          severity,
          timestamp: new Date().toISOString(),
          source: 'auth_middleware'
        },
        created_at: new Date().toISOString()
      });

    console.log(`📋 Security event logged: ${eventType} for ${identifier}`);
  } catch (error) {
    console.error('❌ Failed to log security event:', error);
  }
};

/**
 * Suspicious activity detection
 */
export const detectSuspiciousActivity = (req: AuthRequest): boolean => {
  const body = req.body;
  const headers = req.headers;

  // Check for common attack patterns
  const suspiciousPatterns = [
    // SQL injection attempts
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER)\b)/i,
    /(UNION|OR|AND)\s+\d+\s*=\s*\d+/i,
    
    // XSS attempts
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/i,
    /on\w+\s*=/i,
    
    // Path traversal
    /\.\.\//,
    /\.\.\\/,
    
    // Command injection
    /[;&|`$]/
  ];

  // Check request body
  if (body) {
    const bodyString = JSON.stringify(body);
    for (const pattern of suspiciousPatterns) {
      if (pattern.test(bodyString)) {
        logSecurityEvent('SUSPICIOUS_ACTIVITY', getClientIdentifier(req), {
          type: 'malicious_input',
          pattern: pattern.toString(),
          input: bodyString.substring(0, 100)
        }, 'HIGH');
        return true;
      }
    }
  }

  // Check for unusual headers
  const userAgent = headers.get('user-agent') || '';
  if (userAgent.length > 1000 || /[<>{}$]/.test(userAgent)) {
    logSecurityEvent('SUSPICIOUS_ACTIVITY', getClientIdentifier(req), {
      type: 'malicious_headers',
      userAgent: userAgent.substring(0, 100)
    }, 'MEDIUM');
    return true;
  }

  return false;
};

/**
 * Combined secure authentication middleware
 */
export const secureAuthMiddleware = (type: 'login' | 'registration' = 'login') => {
  return async (req: AuthRequest, res: AuthResponse, next: () => void) => {
    try {
      // Add security headers
      addSecurityHeaders(req, res, () => {});

      // Validate input
      validateAuthInput(req, res, () => {});

      // Detect suspicious activity
      if (detectSuspiciousActivity(req)) {
        return res.status(400).json({ error: 'Invalid request' });
      }

      // Apply appropriate rate limiting
      if (type === 'registration') {
        await secureRegistrationMiddleware(req, res, next);
      } else {
        await secureLoginMiddleware(req, res, next);
      }

    } catch (error) {
      console.error('❌ Secure auth middleware error:', error);
      return res.status(500).json({ error: 'Authentication system error' });
    }
  };
};

export default {
  secureLoginMiddleware,
  secureRegistrationMiddleware,
  recordAuthSuccess,
  recordAuthFailure,
  validateAuthInput,
  addSecurityHeaders,
  logSecurityEvent,
  detectSuspiciousActivity,
  secureAuthMiddleware
};
