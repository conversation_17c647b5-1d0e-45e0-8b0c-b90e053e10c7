#!/usr/bin/env node

/**
 * RLS EFFECTIVENESS TESTING
 * 
 * This script tests if Row Level Security is actually working
 * by simulating different user contexts.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseAnonKey || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

// Create different client contexts
const serviceClient = createClient(supabaseUrl, supabaseServiceKey);
const anonClient = createClient(supabaseUrl, supabaseAnonKey);

class RLSEffectivenessTester {
  constructor() {
    this.testResults = {
      serviceRoleTests: 0,
      anonTests: 0,
      rlsWorking: 0,
      rlsBypassed: 0,
      securityIssues: []
    };
  }

  async testRLSEffectiveness() {
    console.log('🔒 RLS EFFECTIVENESS TESTING');
    console.log('============================\n');
    console.log('🧪 Testing Row Level Security with different user contexts');
    console.log('📊 Service Role vs Anonymous Access Comparison\n');

    try {
      await this.testServiceRoleAccess();
      await this.testAnonymousAccess();
      await this.testRLSPolicyStatus();
      
      this.generateRLSReport();
      
    } catch (error) {
      console.error('❌ RLS effectiveness test failed:', error);
    }
  }

  async testServiceRoleAccess() {
    console.log('🔑 Testing Service Role Access (Should Bypass RLS)');
    console.log('==================================================');

    const tables = [
      'aureus_share_purchases',
      'commission_balances',
      'commission_transactions',
      'crypto_payment_transactions'
    ];

    for (const table of tables) {
      try {
        const { data, error } = await serviceClient
          .from(table)
          .select('*')
          .limit(1);

        if (error) {
          console.log(`   ❌ ${table}: Service role blocked - ${error.message}`);
          this.testResults.securityIssues.push(`Service role blocked from ${table}`);
        } else {
          console.log(`   ✅ ${table}: Service role access working (${data?.length || 0} records)`);
          this.testResults.rlsBypassed++;
        }
        this.testResults.serviceRoleTests++;
      } catch (err) {
        console.log(`   ❌ ${table}: Service role error - ${err.message}`);
      }
    }
    console.log('');
  }

  async testAnonymousAccess() {
    console.log('👤 Testing Anonymous Access (Should Be Blocked by RLS)');
    console.log('======================================================');

    const tables = [
      'aureus_share_purchases',
      'commission_balances',
      'commission_transactions',
      'crypto_payment_transactions'
    ];

    for (const table of tables) {
      try {
        const { data, error } = await anonClient
          .from(table)
          .select('*')
          .limit(1);

        if (error) {
          if (error.message.includes('RLS') || error.message.includes('policy') || error.code === 'PGRST301') {
            console.log(`   ✅ ${table}: RLS working - anonymous access blocked`);
            this.testResults.rlsWorking++;
          } else {
            console.log(`   ⚠️ ${table}: Blocked but not by RLS - ${error.message}`);
          }
        } else {
          console.log(`   ❌ CRITICAL: ${table}: Anonymous access allowed (${data?.length || 0} records)`);
          this.testResults.securityIssues.push(`Anonymous access allowed to ${table}`);
        }
        this.testResults.anonTests++;
      } catch (err) {
        console.log(`   ✅ ${table}: Access properly blocked - ${err.message}`);
        this.testResults.rlsWorking++;
      }
    }
    console.log('');
  }

  async testRLSPolicyStatus() {
    console.log('📋 Testing RLS Policy Status');
    console.log('============================');

    try {
      // Check if RLS is enabled on tables
      const { data: rlsStatus, error } = await serviceClient
        .from('information_schema.tables')
        .select('table_name, row_security')
        .eq('table_schema', 'public')
        .in('table_name', [
          'aureus_share_purchases',
          'commission_balances',
          'commission_transactions',
          'crypto_payment_transactions'
        ]);

      if (error) {
        console.log('   ⚠️ Cannot check RLS status directly');
      } else if (rlsStatus) {
        console.log('   📊 RLS Status by Table:');
        rlsStatus.forEach(table => {
          const status = table.row_security === 'YES' ? '✅ ENABLED' : '❌ DISABLED';
          console.log(`      ${table.table_name}: ${status}`);
        });
      }

      // Test if policies exist
      console.log('\n   📋 Testing Policy Effectiveness:');
      console.log('   Service role should bypass RLS (for bot operations)');
      console.log('   Anonymous users should be blocked by RLS');
      console.log('   Authenticated users should see only their own data');

    } catch (error) {
      console.log('   ❌ RLS policy status check failed:', error.message);
    }
    console.log('');
  }

  generateRLSReport() {
    console.log('🔒 RLS EFFECTIVENESS REPORT');
    console.log('===========================');
    
    console.log(`📊 Service Role Tests: ${this.testResults.serviceRoleTests}`);
    console.log(`📊 Anonymous Tests: ${this.testResults.anonTests}`);
    console.log(`✅ RLS Working: ${this.testResults.rlsWorking}`);
    console.log(`🔑 RLS Bypassed (Service Role): ${this.testResults.rlsBypassed}`);
    console.log(`❌ Security Issues: ${this.testResults.securityIssues.length}`);

    // Calculate effectiveness score
    const totalTests = this.testResults.anonTests;
    const effectivenessScore = totalTests > 0 ? 
      ((this.testResults.rlsWorking / totalTests) * 100).toFixed(1) : 0;

    console.log(`\n🎯 RLS EFFECTIVENESS SCORE: ${effectivenessScore}%`);

    if (effectivenessScore >= 90) {
      console.log('✅ EXCELLENT: RLS is highly effective');
    } else if (effectivenessScore >= 70) {
      console.log('✅ GOOD: RLS is working well');
    } else if (effectivenessScore >= 50) {
      console.log('⚠️ FAIR: RLS has some gaps');
    } else {
      console.log('❌ POOR: RLS is not effective');
    }

    if (this.testResults.securityIssues.length > 0) {
      console.log('\n❌ SECURITY ISSUES FOUND:');
      this.testResults.securityIssues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
    }

    console.log('\n🔍 ANALYSIS:');
    console.log('============');
    
    if (this.testResults.rlsBypassed > 0) {
      console.log('✅ Service role can bypass RLS (EXPECTED - needed for bot operations)');
    }
    
    if (this.testResults.rlsWorking > 0) {
      console.log('✅ Anonymous access is blocked by RLS (GOOD - prevents unauthorized access)');
    }

    console.log('\n📋 SECURITY STATUS:');
    console.log('==================');
    
    if (this.testResults.rlsWorking >= this.testResults.anonTests && this.testResults.securityIssues.length === 0) {
      console.log('🎉 RLS IMPLEMENTATION SUCCESSFUL!');
      console.log('✅ Financial data is protected from unauthorized access');
      console.log('✅ Service role access preserved for legitimate operations');
      console.log('✅ Anonymous users cannot access sensitive data');
      
      console.log('\n🛡️ BUSINESS PROTECTION ACHIEVED:');
      console.log('• Commission balances protected from unauthorized viewing');
      console.log('• Share purchases restricted to authorized users');
      console.log('• Payment transactions secured with RLS');
      console.log('• Bot operations can continue normally');
      
      console.log('\n✅ YOUR BUSINESS IS NOW SIGNIFICANTLY MORE SECURE!');
    } else {
      console.log('⚠️ RLS IMPLEMENTATION NEEDS ATTENTION');
      console.log('Some security gaps may still exist.');
    }

    console.log('\n📋 NEXT STEPS:');
    console.log('1. Test user authentication flows');
    console.log('2. Verify admin panel functionality');
    console.log('3. Test Telegram bot operations');
    console.log('4. Monitor audit logs for suspicious activity');
    console.log('5. Set up regular security monitoring');
  }
}

// Run the RLS effectiveness test
const tester = new RLSEffectivenessTester();
tester.testRLSEffectiveness().catch(console.error);
