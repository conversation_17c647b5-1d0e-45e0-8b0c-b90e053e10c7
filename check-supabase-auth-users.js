import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

console.log('🔍 CHECKING SUPABASE AUTH USERS');
console.log('===============================');

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

const checkAuthUsers = async () => {
  try {
    const email = '<EMAIL>';
    
    console.log(`📧 Checking auth users for: ${email}`);
    
    // Use admin API to list auth users
    const { data: { users }, error } = await supabase.auth.admin.listUsers();
    
    if (error) {
      console.log('❌ Error listing auth users:', error);
      return;
    }
    
    console.log(`📊 Total auth users: ${users.length}`);
    
    // Find our specific user
    const targetUser = users.find(user => user.email === email);
    
    if (targetUser) {
      console.log('✅ Found auth user:');
      console.log(`   ID: ${targetUser.id}`);
      console.log(`   Email: ${targetUser.email}`);
      console.log(`   Created: ${targetUser.created_at}`);
      console.log(`   Confirmed: ${targetUser.email_confirmed_at ? 'Yes' : 'No'}`);
      console.log(`   Last sign in: ${targetUser.last_sign_in_at || 'Never'}`);
    } else {
      console.log('❌ No auth user found for this email');
      console.log('🔧 This is the problem! The user exists in our users table but not in Supabase auth.');
      console.log('💡 Solution: Create a Supabase auth user for this email.');
    }
    
    // Show first few auth users for reference
    console.log('\n📋 First 5 auth users:');
    users.slice(0, 5).forEach((user, index) => {
      console.log(`   ${index + 1}. ${user.email} (ID: ${user.id})`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
};

checkAuthUsers();
