/**
 * Real-time validation service for registration forms
 * Provides debounced validation for username and email availability
 */

import { API_BASE_URL } from '../constants';

// EMERGENCY FIX: Force relative URLs on Vercel domains
const FORCE_API_BASE_URL = typeof window !== 'undefined' &&
  (window.location.hostname.includes('vercel.app') || window.location.hostname.includes('vercel.com'))
  ? '' // Force relative URLs on Vercel
  : API_BASE_URL;

console.log('🚨 EMERGENCY API URL FIX - BUILD TIMESTAMP: 2025-01-15-14:30:', {
  hostname: typeof window !== 'undefined' ? window.location.hostname : 'server-side',
  originalAPI_BASE_URL: API_BASE_URL,
  FORCE_API_BASE_URL,
  isVercel: typeof window !== 'undefined' && window.location.hostname.includes('vercel.app'),
  buildTime: '2025-01-15-14:30'
});

export interface ValidationResult {
  isValid: boolean;
  isChecking: boolean;
  message: string;
  type: 'success' | 'error' | 'info' | 'checking';
}

export interface DuplicateCheckResponse {
  success: boolean;
  emailDuplicate?: boolean;
  usernameDuplicate?: boolean;
  sponsorExists?: boolean | null;
  error?: string;
}

class RealtimeValidationService {
  private debounceTimers: Map<string, NodeJS.Timeout> = new Map();
  private validationCache: Map<string, ValidationResult> = new Map();
  private readonly DEBOUNCE_DELAY = 800; // 800ms debounce delay

  /**
   * Debounced username validation
   */
  async validateUsername(
    username: string,
    callback: (result: ValidationResult) => void
  ): Promise<void> {
    const key = `username_${username}`;
    
    // Clear existing timer
    const existingTimer = this.debounceTimers.get(key);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Check cache first
    const cached = this.validationCache.get(key);
    if (cached && !cached.isChecking) {
      callback(cached);
      return;
    }

    // Show checking state immediately
    const checkingResult: ValidationResult = {
      isValid: false,
      isChecking: true,
      message: 'Checking username availability...',
      type: 'checking'
    };
    callback(checkingResult);

    // Set debounced validation
    const timer = setTimeout(async () => {
      try {
        const result = await this.checkUsernameAvailability(username);
        this.validationCache.set(key, result);
        callback(result);
      } catch (error) {
        const errorResult: ValidationResult = {
          isValid: false,
          isChecking: false,
          message: 'Error checking username availability',
          type: 'error'
        };
        callback(errorResult);
      }
      this.debounceTimers.delete(key);
    }, this.DEBOUNCE_DELAY);

    this.debounceTimers.set(key, timer);
  }

  /**
   * Debounced user existence validation (for transfers, etc.)
   */
  async validateUserExists(
    username: string,
    callback: (result: ValidationResult) => void
  ): Promise<void> {
    const key = `user_exists_${username}`;

    // Clear existing timer
    const existingTimer = this.debounceTimers.get(key);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Check cache first
    const cached = this.validationCache.get(key);
    if (cached && !cached.isChecking) {
      callback(cached);
      return;
    }

    // Show checking state immediately
    const checkingResult: ValidationResult = {
      isValid: false,
      isChecking: true,
      message: 'Checking username...',
      type: 'checking'
    };
    callback(checkingResult);

    // Set debounced validation
    const timer = setTimeout(async () => {
      try {
        const result = await this.checkUserExists(username);
        this.validationCache.set(key, result);
        callback(result);
      } catch (error) {
        const errorResult: ValidationResult = {
          isValid: false,
          isChecking: false,
          message: 'Error checking username',
          type: 'error'
        };
        callback(errorResult);
      }
      this.debounceTimers.delete(key);
    }, this.DEBOUNCE_DELAY);

    this.debounceTimers.set(key, timer);
  }

  /**
   * Debounced sponsor username validation
   */
  async validateSponsorUsername(
    sponsorUsername: string,
    callback: (result: ValidationResult) => void,
    currentUsername?: string
  ): Promise<void> {
    const key = `sponsor_${sponsorUsername}`;

    // Clear existing timer
    const existingTimer = this.debounceTimers.get(key);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Clear any cached results for partial matches to ensure fresh validation
    // This fixes the bug where users need to retype the last character
    for (const [cacheKey] of this.validationCache.entries()) {
      if (cacheKey.startsWith('sponsor_') && cacheKey !== key) {
        // Clear cache for other sponsor usernames to prevent stale results
        this.validationCache.delete(cacheKey);
      }
    }

    // Check cache first (only for exact match)
    const cached = this.validationCache.get(key);
    if (cached && !cached.isChecking) {
      callback(cached);
      return;
    }

    // Show checking state immediately
    const checkingResult: ValidationResult = {
      isValid: false,
      isChecking: true,
      message: 'Checking sponsor username...',
      type: 'checking'
    };
    callback(checkingResult);

    // Set debounced validation
    const timer = setTimeout(async () => {
      try {
        const result = await this.checkSponsorUsernameExists(sponsorUsername, currentUsername);
        this.validationCache.set(key, result);
        callback(result);
      } catch (error) {
        const errorResult: ValidationResult = {
          isValid: false,
          isChecking: false,
          message: 'Error checking sponsor username',
          type: 'error'
        };
        callback(errorResult);
      }
      this.debounceTimers.delete(key);
    }, this.DEBOUNCE_DELAY);

    this.debounceTimers.set(key, timer);
  }

  /**
   * Debounced email validation
   */
  async validateEmail(
    email: string,
    callback: (result: ValidationResult) => void
  ): Promise<void> {
    const key = `email_${email}`;
    
    // Clear existing timer
    const existingTimer = this.debounceTimers.get(key);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Basic email format validation first
    if (!this.isValidEmailFormat(email)) {
      const invalidResult: ValidationResult = {
        isValid: false,
        isChecking: false,
        message: 'Please enter a valid email address',
        type: 'error'
      };
      callback(invalidResult);
      return;
    }

    // Check cache first
    const cached = this.validationCache.get(key);
    if (cached && !cached.isChecking) {
      callback(cached);
      return;
    }

    // Show checking state immediately
    const checkingResult: ValidationResult = {
      isValid: false,
      isChecking: true,
      message: 'Checking email availability...',
      type: 'checking'
    };
    callback(checkingResult);

    // Set debounced validation
    const timer = setTimeout(async () => {
      try {
        const result = await this.checkEmailAvailability(email);
        this.validationCache.set(key, result);
        callback(result);
      } catch (error) {
        const errorResult: ValidationResult = {
          isValid: false,
          isChecking: false,
          message: 'Error checking email availability',
          type: 'error'
        };
        callback(errorResult);
      }
      this.debounceTimers.delete(key);
    }, this.DEBOUNCE_DELAY);

    this.debounceTimers.set(key, timer);
  }

  /**
   * Check username availability via API
   */
  private async checkUsernameAvailability(username: string): Promise<ValidationResult> {
    if (!username || username.trim().length < 2) {
      return {
        isValid: false,
        isChecking: false,
        message: 'Username must be at least 2 characters long',
        type: 'error'
      };
    }

    // Check for invalid characters
    if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
      return {
        isValid: false,
        isChecking: false,
        message: 'Username can only contain letters, numbers, underscores, and hyphens',
        type: 'error'
      };
    }

    try {
      const response = await fetch(`${FORCE_API_BASE_URL}/api/check-duplicates`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username: username.trim() }),
      });

      const data: DuplicateCheckResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Server error');
      }

      if (data.usernameDuplicate) {
        return {
          isValid: false,
          isChecking: false,
          message: 'Username is already taken',
          type: 'error'
        };
      }

      return {
        isValid: true,
        isChecking: false,
        message: 'Username is available',
        type: 'success'
      };
    } catch (error) {
      console.error('Username validation error:', error);
      throw error;
    }
  }

  /**
   * Check if user exists via API (for transfers, etc.)
   */
  private async checkUserExists(username: string): Promise<ValidationResult> {
    if (!username || username.trim().length < 2) {
      return {
        isValid: false,
        isChecking: false,
        message: 'Username must be at least 2 characters long',
        type: 'error'
      };
    }

    // Check for invalid characters
    if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
      return {
        isValid: false,
        isChecking: false,
        message: 'Username can only contain letters, numbers, underscores, and hyphens',
        type: 'error'
      };
    }

    try {
      const response = await fetch(`${FORCE_API_BASE_URL}/api/check-duplicates`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sponsorUsername: username.trim() // Reuse sponsor check logic but with different messages
        }),
      });

      const data: DuplicateCheckResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Server error');
      }

      if (data.sponsorExists === false) {
        return {
          isValid: false,
          isChecking: false,
          message: 'Username not found. Please check the spelling.',
          type: 'error'
        };
      }

      if (data.sponsorExists === true) {
        return {
          isValid: true,
          isChecking: false,
          message: 'Username found',
          type: 'success'
        };
      }

      // This shouldn't happen, but handle it gracefully
      return {
        isValid: false,
        isChecking: false,
        message: 'Unable to verify username',
        type: 'error'
      };
    } catch (error) {
      console.error('User existence validation error:', error);
      throw error;
    }
  }

  /**
   * Check sponsor username exists via API
   */
  private async checkSponsorUsernameExists(sponsorUsername: string, currentUsername?: string): Promise<ValidationResult> {
    if (!sponsorUsername || sponsorUsername.trim().length < 2) {
      return {
        isValid: false,
        isChecking: false,
        message: 'Sponsor username must be at least 2 characters long',
        type: 'error'
      };
    }

    // Check for invalid characters
    if (!/^[a-zA-Z0-9_-]+$/.test(sponsorUsername)) {
      return {
        isValid: false,
        isChecking: false,
        message: 'Sponsor username can only contain letters, numbers, underscores, and hyphens',
        type: 'error'
      };
    }

    try {
      const response = await fetch(`${FORCE_API_BASE_URL}/api/check-duplicates`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sponsorUsername: sponsorUsername.trim(),
          current_username: currentUsername?.trim()
        }),
      });

      const data: DuplicateCheckResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Server error');
      }

      if (data.sponsorExists === false) {
        return {
          isValid: false,
          isChecking: false,
          message: data.sponsorError || 'Sponsor username not found. Please check the spelling.',
          type: 'error'
        };
      }

      if (data.sponsorExists === true) {
        return {
          isValid: true,
          isChecking: false,
          message: 'Sponsor username found',
          type: 'success'
        };
      }

      // This shouldn't happen, but handle it gracefully
      return {
        isValid: false,
        isChecking: false,
        message: 'Unable to verify sponsor username',
        type: 'error'
      };
    } catch (error) {
      console.error('Sponsor username validation error:', error);
      throw error;
    }
  }

  /**
   * Check email availability via API
   */
  private async checkEmailAvailability(email: string): Promise<ValidationResult> {
    try {
      const response = await fetch(`${FORCE_API_BASE_URL}/api/check-duplicates`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: email.trim().toLowerCase() }),
      });

      const data: DuplicateCheckResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Server error');
      }

      if (data.emailDuplicate) {
        return {
          isValid: false,
          isChecking: false,
          message: 'Email is already registered',
          type: 'error'
        };
      }

      return {
        isValid: true,
        isChecking: false,
        message: 'Email is available',
        type: 'success'
      };
    } catch (error) {
      console.error('Email validation error:', error);
      throw error;
    }
  }

  /**
   * Basic email format validation
   */
  private isValidEmailFormat(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Clear validation cache
   */
  clearCache(): void {
    this.validationCache.clear();
  }

  /**
   * Clear all debounce timers
   */
  clearTimers(): void {
    this.debounceTimers.forEach(timer => clearTimeout(timer));
    this.debounceTimers.clear();
  }

  /**
   * Cleanup method to call when component unmounts
   */
  cleanup(): void {
    this.clearTimers();
    this.clearCache();
  }
}

// Export singleton instance
export const realtimeValidation = new RealtimeValidationService();
