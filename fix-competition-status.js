import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixCompetitionStatus() {
  console.log('🔧 FIXING COMPETITION STATUS TO MATCH PHASE STATUS\n');

  try {
    // 1. Get all phases with their active status
    console.log('📊 1. Getting all phases...');
    const { data: phases, error: phasesError } = await supabase
      .from('investment_phases')
      .select('*')
      .order('phase_number');

    if (phasesError) {
      console.error('❌ Error fetching phases:', phasesError);
      return;
    }

    const activePhases = phases.filter(p => p.is_active);
    const inactivePhases = phases.filter(p => !p.is_active);

    console.log(`   Found ${phases.length} total phases:`);
    console.log(`   - ${activePhases.length} active phases: ${activePhases.map(p => `Phase ${p.phase_number}`).join(', ')}`);
    console.log(`   - ${inactivePhases.length} inactive phases`);

    // 2. Get all competitions
    console.log('\n🏆 2. Getting all competitions...');
    const { data: competitions, error: compError } = await supabase
      .from('competitions')
      .select(`
        *,
        investment_phases (
          phase_number,
          phase_name,
          is_active
        )
      `);

    if (compError) {
      console.error('❌ Error fetching competitions:', compError);
      return;
    }

    console.log(`   Found ${competitions.length} competitions`);

    // 3. Update competition status based on phase status
    console.log('\n🔄 3. Updating competition status...');
    
    let updatedCount = 0;
    let alreadyCorrectCount = 0;

    for (const comp of competitions) {
      const phase = comp.investment_phases;
      const shouldBeActive = phase?.is_active || false;
      const currentlyActive = comp.status === 'active' && comp.is_active;

      const correctStatus = shouldBeActive ? 'active' : 'ended';
      const correctIsActive = shouldBeActive;

      if (comp.status !== correctStatus || comp.is_active !== correctIsActive) {
        console.log(`   🔄 Updating: ${comp.name}`);
        console.log(`      Phase ${phase?.phase_number} active: ${phase?.is_active}`);
        console.log(`      Competition: ${comp.status}/${comp.is_active} → ${correctStatus}/${correctIsActive}`);

        const { error: updateError } = await supabase
          .from('competitions')
          .update({
            status: correctStatus,
            is_active: correctIsActive,
            updated_at: new Date().toISOString()
          })
          .eq('id', comp.id);

        if (updateError) {
          console.error(`   ❌ Error updating ${comp.name}:`, updateError);
        } else {
          console.log(`   ✅ Updated ${comp.name}`);
          updatedCount++;
        }
      } else {
        console.log(`   ✅ Already correct: ${comp.name} (${correctStatus})`);
        alreadyCorrectCount++;
      }
    }

    console.log(`\n📊 4. SUMMARY:`);
    console.log(`   - ${updatedCount} competitions updated`);
    console.log(`   - ${alreadyCorrectCount} competitions already correct`);
    console.log(`   - ${activePhases.length} competitions should be active`);
    console.log(`   - ${inactivePhases.length} competitions should be ended`);

    // 5. Verify the fix
    console.log('\n🔍 5. VERIFICATION:');
    const { data: updatedCompetitions, error: verifyError } = await supabase
      .from('competitions')
      .select(`
        name,
        status,
        is_active,
        investment_phases (
          phase_number,
          phase_name,
          is_active
        )
      `)
      .order('created_at', { ascending: false });

    if (verifyError) {
      console.error('❌ Error verifying:', verifyError);
    } else {
      const activeComps = updatedCompetitions.filter(c => c.status === 'active' && c.is_active);
      const endedComps = updatedCompetitions.filter(c => c.status === 'ended' || !c.is_active);

      console.log(`   ✅ ${activeComps.length} active competitions:`);
      activeComps.forEach(comp => {
        const phase = comp.investment_phases;
        console.log(`      - ${comp.name} (Phase ${phase?.phase_number} - ${phase?.is_active ? 'ACTIVE' : 'INACTIVE'})`);
      });

      console.log(`   ✅ ${endedComps.length} ended competitions`);
    }

    console.log('\n🎉 Competition status fix completed!');

  } catch (error) {
    console.error('❌ Fix failed:', error);
  }
}

fixCompetitionStatus();
