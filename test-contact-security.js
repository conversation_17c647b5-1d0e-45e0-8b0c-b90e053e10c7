/**
 * Test contact form security
 */

import axios from 'axios';

async function testContactSecurity() {
  console.log('🧪 Testing contact form security...\n');

  // Test 1: Normal input (should pass)
  console.log('1. Testing normal input:');
  try {
    const response = await axios.post('http://localhost:8002/api/contact', {
      name: '<PERSON>',
      surname: '<PERSON><PERSON>',
      email: '<EMAIL>',
      message: 'This is a normal message'
    }, { 
      validateStatus: () => true,
      timeout: 5000
    });
    
    console.log(`Status: ${response.status}`);
    console.log(`Response: ${JSON.stringify(response.data, null, 2)}`);
    console.log(`✅ Should pass: ${response.status === 200}\n`);
  } catch (error) {
    console.log(`❌ Error: ${error.message}\n`);
  }

  // Test 2: SQL injection input (should fail)
  console.log('2. Testing SQL injection input:');
  try {
    const response = await axios.post('http://localhost:8002/api/contact', {
      name: "'; DROP TABLE users; --",
      surname: '<PERSON>',
      email: '<EMAIL>',
      message: 'This is a test message'
    }, { 
      validateStatus: () => true,
      timeout: 5000
    });
    
    console.log(`Status: ${response.status}`);
    console.log(`Response: ${JSON.stringify(response.data, null, 2)}`);
    console.log(`❌ Should fail: ${response.status === 400}\n`);
  } catch (error) {
    console.log(`❌ Error: ${error.message}\n`);
  }

  // Test 3: XSS input (should fail)
  console.log('3. Testing XSS input:');
  try {
    const response = await axios.post('http://localhost:8002/api/contact', {
      name: '<script>alert("XSS")</script>',
      surname: 'Test',
      email: '<EMAIL>',
      message: 'This is a test message'
    }, { 
      validateStatus: () => true,
      timeout: 5000
    });
    
    console.log(`Status: ${response.status}`);
    console.log(`Response: ${JSON.stringify(response.data, null, 2)}`);
    console.log(`❌ Should fail: ${response.status === 400}\n`);
  } catch (error) {
    console.log(`❌ Error: ${error.message}\n`);
  }

  console.log('🎯 Contact form security test completed!');
}

testContactSecurity().catch(console.error);
