#!/usr/bin/env node

/**
 * COMPLETE EMAIL FIX VERIFICATION
 * 
 * This script tests both email issues after DNS fixes:
 * 1. Resend → @aureus.africa delivery (SPF fix)
 * 2. Gmail → @aureus.africa delivery (MX fix)
 */

import { Resend } from 'resend';
import dns from 'dns';
import { promisify } from 'util';
import net from 'net';
import dotenv from 'dotenv';

dotenv.config();

const resolveMx = promisify(dns.resolveMx);
const resolveTxt = promisify(dns.resolveTxt);
const resolveA = promisify(dns.resolve4);

const DOMAIN = 'aureus.africa';
const YOUR_SERVER_IP = '*************';
const VERCEL_IP = '************';
const RESEND_API_KEY = process.env.RESEND_API_KEY;

const resend = new Resend(RESEND_API_KEY);

async function testSmtpConnection(host, port = 25) {
  return new Promise((resolve) => {
    const socket = new net.Socket();
    const timeout = setTimeout(() => {
      socket.destroy();
      resolve({ success: false, error: 'Connection timeout' });
    }, 10000);

    socket.connect(port, host, () => {
      clearTimeout(timeout);
      socket.destroy();
      resolve({ success: true });
    });

    socket.on('error', (error) => {
      clearTimeout(timeout);
      resolve({ success: false, error: error.message });
    });
  });
}

async function verifyCompleteEmailFix() {
  console.log('🔍 COMPLETE EMAIL FIX VERIFICATION');
  console.log('===================================\n');
  
  let spfFixed = false;
  let mxFixed = false;
  
  // Test 1: SPF Record Fix (for Resend delivery)
  console.log('1️⃣ TESTING SPF RECORD FIX');
  console.log('--------------------------');
  try {
    const txtRecords = await resolveTxt(DOMAIN);
    const spfRecords = txtRecords.filter(record => 
      record.some(txt => txt.startsWith('v=spf1'))
    );
    
    if (spfRecords.length > 0) {
      const spfText = spfRecords[0].join('');
      console.log(`Current SPF: ${spfText}`);
      
      if (spfText.includes('include:_spf.resend.com')) {
        console.log('✅ SPF record includes Resend authorization');
        spfFixed = true;
      } else {
        console.log('❌ SPF record missing Resend authorization');
        console.log('   Add: include:_spf.resend.com');
      }
    } else {
      console.log('❌ No SPF record found');
    }
  } catch (error) {
    console.log('❌ Error checking SPF:', error.message);
  }
  console.log('');
  
  // Test 2: MX Record Fix (for Gmail delivery)
  console.log('2️⃣ TESTING MX RECORD FIX');
  console.log('-------------------------');
  try {
    const mxRecords = await resolveMx(DOMAIN);
    console.log('Current MX records:');
    mxRecords.forEach((record, index) => {
      console.log(`   ${index + 1}. ${record.exchange} (priority: ${record.priority})`);
    });
    
    // Check if MX points to mail subdomain
    const mailMx = mxRecords.find(mx => mx.exchange.includes('mail.'));
    if (mailMx) {
      console.log(`\n✅ Found mail subdomain MX: ${mailMx.exchange}`);
      
      // Check if mail subdomain points to your server
      try {
        const mailARecords = await resolveA(mailMx.exchange);
        console.log(`${mailMx.exchange} points to: ${mailARecords.join(', ')}`);
        
        if (mailARecords.includes(YOUR_SERVER_IP)) {
          console.log('✅ Mail subdomain points to your server');
          
          // Test SMTP connectivity
          const smtpResult = await testSmtpConnection(YOUR_SERVER_IP);
          if (smtpResult.success) {
            console.log('✅ SMTP server responding on your server');
            mxFixed = true;
          } else {
            console.log('❌ SMTP server not responding on your server');
          }
        } else {
          console.log('❌ Mail subdomain does not point to your server');
        }
      } catch (error) {
        console.log(`❌ Error resolving ${mailMx.exchange}:`, error.message);
      }
    } else {
      // Check if MX still points to domain itself
      const selfMx = mxRecords.find(mx => mx.exchange === DOMAIN);
      if (selfMx) {
        console.log('\n❌ MX still points to domain itself (Vercel)');
        console.log('   Need to create mail.aureus.africa and update MX');
      }
    }
  } catch (error) {
    console.log('❌ Error checking MX records:', error.message);
  }
  console.log('');
  
  // Test 3: Send test emails if fixes are in place
  if (spfFixed && mxFixed) {
    console.log('3️⃣ SENDING TEST EMAILS');
    console.log('----------------------');
    
    const testEmails = [
      {
        to: '<EMAIL>',
        subject: 'Complete Fix Test - Internal Delivery',
        description: 'Test Resend → @aureus.africa delivery'
      }
    ];
    
    for (const testEmail of testEmails) {
      try {
        console.log(`📧 Sending test to: ${testEmail.to}`);
        
        const result = await resend.emails.send({
          from: 'Aureus Alliance Holdings <<EMAIL>>',
          to: [testEmail.to],
          subject: testEmail.subject,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #D4AF37;">Email Delivery Fix Verification</h2>
              <p>This email tests that both email issues have been resolved:</p>
              <ul>
                <li>✅ SPF record includes Resend authorization</li>
                <li>✅ MX record points to working mail server</li>
              </ul>
              <p>If you receive this email, both fixes are working correctly!</p>
              <p><strong>Test Details:</strong></p>
              <ul>
                <li>Sent via: Resend API</li>
                <li>From: <EMAIL></li>
                <li>To: ${testEmail.to}</li>
                <li>Time: ${new Date().toISOString()}</li>
              </ul>
            </div>
          `,
          text: `Email Delivery Fix Verification\n\nBoth email issues should now be resolved!\n\nSent: ${new Date().toISOString()}`
        });
        
        if (result.error) {
          console.log(`❌ Failed: ${result.error.message}`);
        } else {
          console.log(`✅ Sent successfully! ID: ${result.data?.id}`);
        }
      } catch (error) {
        console.log(`❌ Error: ${error.message}`);
      }
    }
    console.log('');
  }
  
  // Final Status Report
  console.log('4️⃣ FINAL STATUS REPORT');
  console.log('----------------------');
  
  if (spfFixed && mxFixed) {
    console.log('🎉 ALL EMAIL ISSUES RESOLVED!');
    console.log('✅ SPF record authorizes Resend');
    console.log('✅ MX record points to working mail server');
    console.log('✅ Both outgoing and incoming email should work');
    console.log('');
    console.log('📧 Test both directions:');
    console.log('1. Contact form → <EMAIL> (via Resend)');
    console.log('2. Gmail → <EMAIL> (direct delivery)');
  } else {
    console.log('⚠️ EMAIL ISSUES PARTIALLY RESOLVED');
    if (!spfFixed) {
      console.log('❌ SPF record still needs Resend authorization');
    }
    if (!mxFixed) {
      console.log('❌ MX record still needs to point to your server');
    }
    console.log('');
    console.log('📋 Required DNS changes:');
    if (!spfFixed) {
      console.log('• Update SPF: v=spf1 +a +mx +ip4:************* include:_spf.resend.com ~all');
    }
    if (!mxFixed) {
      console.log('• Add A record: mail.aureus.africa → *************');
      console.log('• Update MX record: 10 mail.aureus.africa');
    }
  }
}

// Run the verification
verifyCompleteEmailFix().catch(console.error);
