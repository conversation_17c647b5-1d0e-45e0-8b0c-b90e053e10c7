/**
 * FIX GRUHGO'S MISSING SHARE COMMISSION
 * 
 * Issue: <PERSON><PERSON><PERSON><PERSON> (user 88) received USDT commission but missing share commission
 * for user 90's recent $50 USDT purchase (10 shares)
 * 
 * Expected: 10 shares * 15% = 1.5 share commission
 * Actual: 0.00 share commission
 */

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co'
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseServiceKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function fixGruHgoShareCommission() {
  console.log('🔧 FIXING GRUHGO\'S MISSING SHARE COMMISSION')
  console.log('=' .repeat(50))

  try {
    // Step 1: Get the problematic commission transaction
    console.log('📋 Step 1: Finding the commission transaction with missing shares...')
    
    const { data: commissionTransaction, error: commissionError } = await supabase
      .from('commission_transactions')
      .select('*')
      .eq('id', '17bb6079-333e-4a9c-8866-162af606780f') // The recent transaction with 0 share commission
      .single()

    if (commissionError || !commissionTransaction) {
      console.error('❌ Commission transaction not found:', commissionError)
      return
    }

    console.log('✅ Found commission transaction:')
    console.log(`   ID: ${commissionTransaction.id}`)
    console.log(`   Referrer: ${commissionTransaction.referrer_id} (GruHgo)`)
    console.log(`   Referred: ${commissionTransaction.referred_id} (User 90)`)
    console.log(`   Purchase Amount: $${commissionTransaction.share_purchase_amount}`)
    console.log(`   USDT Commission: $${commissionTransaction.usdt_commission}`)
    console.log(`   Share Commission: ${commissionTransaction.share_commission} (SHOULD BE 1.5)`)

    // Step 2: Get the corresponding share purchase to verify shares purchased
    console.log('\n📋 Step 2: Getting share purchase details...')
    
    const { data: sharePurchase, error: purchaseError } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .eq('id', commissionTransaction.share_purchase_id)
      .single()

    if (purchaseError || !sharePurchase) {
      console.error('❌ Share purchase not found:', purchaseError)
      return
    }

    console.log('✅ Found share purchase:')
    console.log(`   ID: ${sharePurchase.id}`)
    console.log(`   User: ${sharePurchase.user_id}`)
    console.log(`   Shares Purchased: ${sharePurchase.shares_purchased}`)
    console.log(`   Total Amount: $${sharePurchase.total_amount}`)

    // Step 3: Calculate correct share commission
    const sharesPurchased = sharePurchase.shares_purchased
    const expectedShareCommission = sharesPurchased * 0.15
    
    console.log('\n📋 Step 3: Calculating correct commission...')
    console.log(`   Shares Purchased: ${sharesPurchased}`)
    console.log(`   Commission Rate: 15%`)
    console.log(`   Expected Share Commission: ${expectedShareCommission}`)

    // Step 4: Update the commission transaction
    console.log('\n📋 Step 4: Updating commission transaction...')
    
    const { error: updateError } = await supabase
      .from('commission_transactions')
      .update({
        share_commission: expectedShareCommission,
        updated_at: new Date().toISOString()
      })
      .eq('id', commissionTransaction.id)

    if (updateError) {
      console.error('❌ Failed to update commission transaction:', updateError)
      return
    }

    console.log('✅ Commission transaction updated successfully')

    // Step 5: Update GruHgo's commission balance
    console.log('\n📋 Step 5: Updating GruHgo\'s commission balance...')
    
    const { data: currentBalance, error: balanceError } = await supabase
      .from('commission_balances')
      .select('*')
      .eq('user_id', 88) // GruHgo's user ID
      .single()

    if (balanceError || !currentBalance) {
      console.error('❌ Failed to get current balance:', balanceError)
      return
    }

    console.log('📊 Current balance:')
    console.log(`   USDT Balance: $${currentBalance.usdt_balance}`)
    console.log(`   Share Balance: ${currentBalance.share_balance}`)
    console.log(`   Total Earned Shares: ${currentBalance.total_earned_shares}`)

    const newShareBalance = parseFloat(currentBalance.share_balance) + expectedShareCommission
    const newTotalEarnedShares = parseFloat(currentBalance.total_earned_shares) + expectedShareCommission

    const { error: balanceUpdateError } = await supabase
      .from('commission_balances')
      .update({
        share_balance: newShareBalance.toFixed(4),
        total_earned_shares: newTotalEarnedShares.toFixed(4),
        last_updated: new Date().toISOString()
      })
      .eq('user_id', 88)

    if (balanceUpdateError) {
      console.error('❌ Failed to update commission balance:', balanceUpdateError)
      return
    }

    console.log('✅ Commission balance updated successfully')
    console.log('📊 New balance:')
    console.log(`   USDT Balance: $${currentBalance.usdt_balance}`)
    console.log(`   Share Balance: ${newShareBalance.toFixed(4)}`)
    console.log(`   Total Earned Shares: ${newTotalEarnedShares.toFixed(4)}`)

    // Step 6: Verification
    console.log('\n📋 Step 6: Verification...')
    
    const { data: updatedCommission, error: verifyError } = await supabase
      .from('commission_transactions')
      .select('*')
      .eq('id', commissionTransaction.id)
      .single()

    if (verifyError || !updatedCommission) {
      console.error('❌ Verification failed:', verifyError)
      return
    }

    console.log('✅ VERIFICATION SUCCESSFUL:')
    console.log(`   Commission ID: ${updatedCommission.id}`)
    console.log(`   USDT Commission: $${updatedCommission.usdt_commission}`)
    console.log(`   Share Commission: ${updatedCommission.share_commission} shares`)
    console.log(`   Status: ${updatedCommission.status}`)

    console.log('\n🎉 SUCCESS: GruHgo\'s missing share commission has been fixed!')
    console.log(`   Added ${expectedShareCommission} shares to his commission balance`)
    console.log(`   Commission transaction updated with correct share amount`)

  } catch (error) {
    console.error('❌ Error fixing commission:', error)
  }
}

// Run the fix
fixGruHgoShareCommission()
