import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with service role key
const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function debugUser4Complete() {
  console.log('🔍 COMPLETE DEBUG OF USER ID 4 (TTTFOUNDER)');
  console.log('==============================================');
  
  try {
    // Check users table
    console.log('📋 Step 1: Checking users table...');
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', 4)
      .single();

    if (userError) {
      console.error('❌ Error fetching user:', userError);
      return;
    }

    console.log('✅ Users table data:');
    console.log('   ID:', user.id);
    console.log('   Email:', user.email);
    console.log('   Username:', user.username);
    console.log('   Password Hash:', user.password_hash);
    console.log('   Migration Status:', user.migration_status);
    console.log('   Web Credentials Set:', user.web_credentials_set_at);
    console.log('   Migration Completed:', user.migration_completed_at);
    console.log('   Account Type:', user.account_type);
    console.log('   Is Active:', user.is_active);
    console.log('   Is Verified:', user.is_verified);
    console.log('   Created At:', user.created_at);
    console.log('   Updated At:', user.updated_at);

    // Check telegram_users table
    console.log('\n📋 Step 2: Checking telegram_users table...');
    const { data: telegramUsers, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('user_id', 4);

    if (telegramError) {
      console.log('⚠️ Error checking telegram_users:', telegramError.message);
    } else if (telegramUsers && telegramUsers.length > 0) {
      console.log('✅ Telegram users data:');
      telegramUsers.forEach((tUser, index) => {
        console.log(`   Record ${index + 1}:`);
        console.log('     ID:', tUser.id);
        console.log('     User ID:', tUser.user_id);
        console.log('     Telegram ID:', tUser.telegram_id);
        console.log('     Username:', tUser.username);
        console.log('     First Name:', tUser.first_name);
        console.log('     Last Name:', tUser.last_name);
        console.log('     Is Registered:', tUser.is_registered);
        console.log('     Registration Step:', tUser.registration_step);
        console.log('     Created At:', tUser.created_at);
      });
    } else {
      console.log('❌ No telegram_users records found for user_id 4');
    }

    // Check user_sessions table
    console.log('\n📋 Step 3: Checking user_sessions table...');
    const { data: sessions, error: sessionError } = await supabase
      .from('user_sessions')
      .select('*')
      .eq('user_id', 4);

    if (sessionError) {
      console.log('⚠️ Error checking user_sessions:', sessionError.message);
    } else if (sessions && sessions.length > 0) {
      console.log('✅ Active sessions:');
      sessions.forEach((session, index) => {
        console.log(`   Session ${index + 1}:`);
        console.log('     Session ID:', session.session_id);
        console.log('     Is Active:', session.is_active);
        console.log('     Created At:', session.created_at);
        console.log('     Expires At:', session.expires_at);
      });
    } else {
      console.log('❌ No active sessions found');
    }

    // Check email_verification_codes table
    console.log('\n📋 Step 4: Checking email_verification_codes table...');
    const { data: codes, error: codeError } = await supabase
      .from('email_verification_codes')
      .select('*')
      .eq('user_id', 4);

    if (codeError) {
      console.log('⚠️ Error checking email_verification_codes:', codeError.message);
    } else if (codes && codes.length > 0) {
      console.log('✅ Email verification codes:');
      codes.forEach((code, index) => {
        console.log(`   Code ${index + 1}:`);
        console.log('     Purpose:', code.purpose);
        console.log('     Expires At:', code.expires_at);
        console.log('     Attempts:', code.attempts);
      });
    } else {
      console.log('✅ No email verification codes found');
    }

    console.log('\n🎯 ANALYSIS:');
    console.log('=============');
    
    if (user.password_hash === 'telegram_auth') {
      console.log('✅ Password hash is telegram_auth (correct for testing)');
    } else {
      console.log('❌ Password hash is NOT telegram_auth:', user.password_hash);
    }

    if (!user.migration_status) {
      console.log('✅ Migration status is null (correct for testing)');
    } else {
      console.log('❌ Migration status is NOT null:', user.migration_status);
    }

    if (!user.web_credentials_set_at) {
      console.log('✅ Web credentials set at is null (correct for testing)');
    } else {
      console.log('❌ Web credentials set at is NOT null:', user.web_credentials_set_at);
    }

    console.log('\n🔧 RECOMMENDATION:');
    if (user.password_hash === 'telegram_auth' && !user.migration_status && !user.web_credentials_set_at) {
      console.log('✅ User ID 4 appears to be correctly reset for testing');
      console.log('📱 Try logging in with Telegram and visiting /migrate');
    } else {
      console.log('❌ User ID 4 needs additional reset');
      console.log('🔄 Running complete reset now...');
      await performCompleteReset();
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

async function performCompleteReset() {
  console.log('\n🔄 PERFORMING COMPLETE RESET...');
  console.log('================================');

  try {
    // Reset users table completely
    const { error: resetError } = await supabase
      .from('users')
      .update({
        password_hash: 'telegram_auth',
        migration_status: null,
        web_credentials_set_at: null,
        migration_completed_at: null,
        account_type: 'telegram',
        is_verified: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', 4);

    if (resetError) {
      console.error('❌ Error in complete reset:', resetError);
      return;
    }

    // Clear all sessions
    const { error: sessionError } = await supabase
      .from('user_sessions')
      .delete()
      .eq('user_id', 4);

    if (sessionError) {
      console.log('⚠️ Could not clear sessions:', sessionError.message);
    } else {
      console.log('✅ All sessions cleared');
    }

    // Clear email verification codes
    const { error: codeError } = await supabase
      .from('email_verification_codes')
      .delete()
      .eq('user_id', 4);

    if (codeError) {
      console.log('⚠️ Could not clear email codes:', codeError.message);
    } else {
      console.log('✅ Email verification codes cleared');
    }

    console.log('✅ COMPLETE RESET FINISHED');
    console.log('📱 User ID 4 should now require migration');

  } catch (error) {
    console.error('❌ Error in complete reset:', error);
  }
}

// Run the debug
debugUser4Complete();
