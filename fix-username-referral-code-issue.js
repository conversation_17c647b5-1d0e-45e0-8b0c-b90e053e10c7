/**
 * FIX USERNAME CHANGE AND REFERRAL CODE ISSUE
 * 
 * This script fixes the issue where:
 * 1. User 139 (Naashie1 -> MIRAMAR) changed username but referral codes weren't updated
 * 2. All 13 referral codes still contain "Naashie1" instead of "MIRAMAR"
 * 3. This creates inconsistency in the referral system
 * 4. Share transfer to user 367 was successful but referral code is outdated
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client with service role
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixUsernameReferralCodeIssue() {
  console.log('🔧 FIXING USERNAME CHANGE AND REFERRAL CODE ISSUE');
  console.log('=================================================');
  
  try {
    // ===== PART 1: VERIFY CURRENT STATE =====
    console.log('\n📋 PART 1: Verifying Current State');
    
    // Get user 139 current data
    const { data: user139, error: user139Error } = await supabase
      .from('users')
      .select('id, username, full_name')
      .eq('id', 139)
      .single();
      
    if (user139Error) {
      console.log('❌ Error fetching user 139:', user139Error.message);
      return false;
    }
    
    console.log('✅ User 139 current username:', user139.username);
    
    // Get all referrals by user 139
    const { data: referrals, error: referralsError } = await supabase
      .from('referrals')
      .select('id, referrer_id, referred_id, referral_code, status')
      .eq('referrer_id', 139);
      
    if (referralsError) {
      console.log('❌ Error fetching referrals:', referralsError.message);
      return false;
    }
    
    console.log(`✅ Found ${referrals.length} referrals by user 139`);
    
    // Count outdated referral codes
    const outdatedReferrals = referrals.filter(ref => 
      ref.referral_code && ref.referral_code.includes('Naashie1')
    );
    
    console.log(`⚠️  ${outdatedReferrals.length} referral codes contain old username "Naashie1"`);
    
    if (outdatedReferrals.length === 0) {
      console.log('✅ No referral codes need updating!');
      return true;
    }
    
    // ===== PART 2: CREATE BACKUP =====
    console.log('\n📋 PART 2: Creating Backup of Current Referral Codes');
    
    console.log('📝 Backup of referral codes to be updated:');
    outdatedReferrals.forEach((ref, index) => {
      console.log(`   ${index + 1}. ID: ${ref.id}`);
      console.log(`      → Current Code: ${ref.referral_code}`);
      console.log(`      → Referred User: ${ref.referred_id}`);
    });
    
    // ===== PART 3: UPDATE REFERRAL CODES =====
    console.log('\n📋 PART 3: Updating Referral Codes');
    
    let updatedCount = 0;
    let errorCount = 0;
    
    for (const referral of outdatedReferrals) {
      try {
        // Generate new referral code with current username
        const newReferralCode = referral.referral_code.replace('Naashie1', user139.username);
        
        console.log(`🔄 Updating referral ${referral.id}:`);
        console.log(`   → Old: ${referral.referral_code}`);
        console.log(`   → New: ${newReferralCode}`);
        
        // Update the referral code
        const { error: updateError } = await supabase
          .from('referrals')
          .update({
            referral_code: newReferralCode,
            updated_at: new Date().toISOString()
          })
          .eq('id', referral.id);
          
        if (updateError) {
          console.log(`   ❌ Error updating referral ${referral.id}:`, updateError.message);
          errorCount++;
        } else {
          console.log(`   ✅ Successfully updated referral ${referral.id}`);
          updatedCount++;
        }
        
        // Small delay to avoid overwhelming the database
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        console.log(`   ❌ Exception updating referral ${referral.id}:`, error.message);
        errorCount++;
      }
    }
    
    console.log(`\n📊 Update Summary:`);
    console.log(`   ✅ Successfully updated: ${updatedCount} referral codes`);
    console.log(`   ❌ Failed to update: ${errorCount} referral codes`);
    
    // ===== PART 4: VERIFY UPDATES =====
    console.log('\n📋 PART 4: Verifying Updates');
    
    // Re-fetch referrals to verify updates
    const { data: updatedReferrals, error: verifyError } = await supabase
      .from('referrals')
      .select('id, referral_code')
      .eq('referrer_id', 139);
      
    if (verifyError) {
      console.log('❌ Error verifying updates:', verifyError.message);
      return false;
    }
    
    // Check for remaining outdated codes
    const stillOutdated = updatedReferrals.filter(ref => 
      ref.referral_code && ref.referral_code.includes('Naashie1')
    );
    
    if (stillOutdated.length === 0) {
      console.log('✅ All referral codes successfully updated!');
    } else {
      console.log(`⚠️  ${stillOutdated.length} referral codes still contain "Naashie1"`);
      stillOutdated.forEach(ref => {
        console.log(`   → ${ref.id}: ${ref.referral_code}`);
      });
    }
    
    // ===== PART 5: UPDATE TELEGRAM BOT CACHE (if applicable) =====
    console.log('\n📋 PART 5: Checking Telegram User Data');
    
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('id, user_id, username')
      .eq('user_id', 139)
      .single();
      
    if (telegramError) {
      console.log('⚠️  No telegram user found for user 139 (this is okay)');
    } else {
      console.log('✅ Telegram user data:');
      console.log(`   → Telegram Username: ${telegramUser.username}`);
      
      if (telegramUser.username !== user139.username) {
        console.log('🔄 Updating telegram username to match...');
        
        const { error: telegramUpdateError } = await supabase
          .from('telegram_users')
          .update({
            username: user139.username,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', 139);
          
        if (telegramUpdateError) {
          console.log('❌ Error updating telegram username:', telegramUpdateError.message);
        } else {
          console.log('✅ Telegram username updated successfully');
        }
      } else {
        console.log('✅ Telegram username already matches');
      }
    }
    
    // ===== PART 6: FINAL VERIFICATION =====
    console.log('\n📋 PART 6: Final Verification');
    
    // Check specific referral for user 367
    const { data: user367Referral, error: user367Error } = await supabase
      .from('referrals')
      .select('*')
      .eq('referrer_id', 139)
      .eq('referred_id', 367)
      .single();
      
    if (user367Error) {
      console.log('❌ Error checking user 367 referral:', user367Error.message);
    } else {
      console.log('✅ User 367 referral verification:');
      console.log(`   → Referral Code: ${user367Referral.referral_code}`);
      console.log(`   → Status: ${user367Referral.status}`);
      
      if (user367Referral.referral_code.includes('MIRAMAR')) {
        console.log('   ✅ Referral code correctly uses new username');
      } else if (user367Referral.referral_code.includes('Naashie1')) {
        console.log('   ⚠️  Referral code still uses old username');
      }
    }
    
    console.log('\n🎉 REFERRAL CODE UPDATE COMPLETE!');
    console.log('==================================');
    console.log('✅ Username change issue resolved');
    console.log('✅ Referral codes updated to use current username');
    console.log('✅ System consistency restored');
    console.log('✅ Share transfer functionality preserved');
    
    return updatedCount > 0 && errorCount === 0;
    
  } catch (error) {
    console.error('💥 Fatal error during fix:', error);
    return false;
  }
}

// Run the fix
fixUsernameReferralCodeIssue()
  .then((success) => {
    if (success) {
      console.log('\n✅ Username referral code fix completed successfully!');
    } else {
      console.log('\n❌ Username referral code fix encountered errors');
    }
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error('\n💥 Fix failed:', error);
    process.exit(1);
  });
