import React from 'react';
import { DashboardSection, useDashboardNavigation } from '../../hooks/useDashboardNavigation';
import { UserPermissions } from '../../hooks/useUserPermissions';
import { DashboardData } from '../../hooks/useDashboardData';

interface DashboardSidebarProps {
  activeSection: DashboardSection;
  onSectionChange: (section: DashboardSection) => void;
  permissions: UserPermissions;
  dashboardData: DashboardData;
}

export const DashboardSidebar: React.FC<DashboardSidebarProps> = ({
  activeSection,
  onSectionChange,
  permissions,
  dashboardData
}) => {
  const { shareholderNavigationItems } = useDashboardNavigation();

  // Filter navigation items based on permissions
  const filteredNavigationItems = shareholderNavigationItems.filter(item => {
    switch (item.id) {
      case 'purchase-shares':
        return permissions.canPurchaseShares;
      case 'portfolio':
        return permissions.canViewPortfolio;
      case 'referrals':
        return permissions.canManageReferrals;
      case 'payments':
        return permissions.canViewPayments;
      case 'settings':
        return permissions.canAccessSettings;
      default:
        return true; // Show other items by default
    }
  });

  const getBadgeCount = (sectionId: DashboardSection): number | undefined => {
    switch (sectionId) {
      case 'notifications':
        return dashboardData.notifications?.unread || 0;
      case 'referrals':
        // Could show pending referrals count
        return undefined;
      case 'payments':
        // Could show pending payments count
        return undefined;
      default:
        return undefined;
    }
  };

  return (
    <aside className="w-64 bg-gray-800 border-r border-gray-700 flex flex-col h-full">
      {/* Logo/Brand */}
      <div className="p-6 border-b border-gray-700">
        <div className="flex items-center space-x-3">
          <img 
            src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-s.png" 
            alt="Aureus Alliance Holdings" 
            className="h-8 w-8"
          />
          <div>
            <h2 className="text-white font-bold text-lg">Aureus Alliance</h2>
            <p className="text-gray-400 text-xs">Holdings Dashboard</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
        {filteredNavigationItems.map((item) => {
          const isActive = activeSection === item.id;
          const badgeCount = getBadgeCount(item.id);
          const IconComponent = item.icon;

          return (
            <button
              key={item.id}
              onClick={() => onSectionChange(item.id)}
              className={`w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left transition-colors ${
                isActive
                  ? 'bg-yellow-600 text-white'
                  : 'text-gray-300 hover:text-white hover:bg-gray-700'
              }`}
              title={item.description}
            >
              <IconComponent />
              <span className="flex-1 font-medium">{item.label}</span>
              
              {/* Badge for notifications, etc. */}
              {badgeCount !== undefined && badgeCount > 0 && (
                <span className={`inline-flex items-center justify-center px-2 py-1 text-xs font-bold rounded-full ${
                  isActive 
                    ? 'bg-yellow-800 text-yellow-100' 
                    : 'bg-red-600 text-white'
                }`}>
                  {badgeCount > 99 ? '99+' : badgeCount}
                </span>
              )}

              {/* New indicator */}
              {item.isNew && (
                <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold bg-green-600 text-white rounded-full">
                  New
                </span>
              )}
            </button>
          );
        })}
      </nav>

      {/* Quick Stats Footer */}
      <div className="p-4 border-t border-gray-700 space-y-3">
        <div className="text-xs text-gray-400 uppercase tracking-wide font-semibold">
          Quick Stats
        </div>
        
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-gray-400 text-sm">Total Shares</span>
            <span className="text-white text-sm font-medium">
              {dashboardData.totalShares.toLocaleString()}
            </span>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-gray-400 text-sm">Portfolio Value</span>
            <span className="text-green-400 text-sm font-medium">
              ${dashboardData.currentValue.toLocaleString()}
            </span>
          </div>
          
          {permissions.canManageReferrals && (
            <div className="flex justify-between items-center">
              <span className="text-gray-400 text-sm">USDT Balance</span>
              <span className="text-blue-400 text-sm font-medium">
                ${dashboardData.usdtCommissions?.available.toFixed(2) || '0.00'}
              </span>
            </div>
          )}
        </div>

        {/* KYC Status Indicator */}
        {dashboardData.kycStatus && (
          <div className="pt-2 border-t border-gray-700">
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${
                dashboardData.kycStatus === 'approved' 
                  ? 'bg-green-500' 
                  : dashboardData.kycStatus === 'pending'
                  ? 'bg-yellow-500'
                  : 'bg-gray-500'
              }`}></div>
              <span className="text-xs text-gray-400">
                KYC: {dashboardData.kycStatus === 'approved' ? 'Verified' : 
                      dashboardData.kycStatus === 'pending' ? 'Pending' : 'Not Started'}
              </span>
            </div>
          </div>
        )}
      </div>
    </aside>
  );
};
