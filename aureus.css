/*
 * AUREUS ALLIANCE HOLDINGS - UNIFIED DESIGN SYSTEM
 * Professional Corporate Design System with Premium Color Palette
 * Version: 1.0.0
 *
 * This file consolidates all styling across the entire website for:
 * - Consistent professional appearance
 * - Easy maintenance and updates
 * - Premium investor-grade visual design
 * - Seamless dark/light theme switching
 * - WCAG 2.1 AA accessibility compliance
 */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* ========================================
   CORE CSS RESET & BASE STYLES
======================================== */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  font-family: 'Inter', 'Segoe UI', 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  overflow-x: hidden;
}

/* ========================================
   PREMIUM COLOR PALETTE - CSS VARIABLES
   Based on aureus_ui_color_palette_premium.md
======================================== */

:root,
[data-theme='dark'],
[data-theme='light'] {
  /* Premium Dark Theme - Professional Corporate Design */
  --bg: #050505;
  --surface: #121212;
  --text: #FFFFFF;
  --text-muted: #B0B0B0;
  --gold: #FFD700;
  --blue: #00BFFF;
  --emerald: #2ECC71;
  --copper: #E07B39;
  --border: #1E1E1E;
  --shadow: 0px 0px 24px rgba(255, 215, 0, 0.15);
  --glow: 0 0 18px #FFD70066;

  /* Glass overlay */
  --glass-overlay: rgba(255,255,255,0.04);

  /* CTA Button Colors */
  --cta-bg: #FFD700;
  --cta-text: #000000;
  --cta-hover-bg: #E6C200;

  /* Status Colors */
  --success: #2ECC71;
  --warning: #E07B39;
  --error: #FF6B6B;
  --info: #00BFFF;
}

/* Light theme removed - using single premium dark theme */

/* ========================================
   TYPOGRAPHY SYSTEM
======================================== */

:root {
  /* Font Sizes - Mobile First */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  
  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* Font Weights */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
}

/* ========================================
   SPACING SYSTEM
======================================== */

:root {
  /* Spacing Scale - Mobile First */
  --space-xs: 0.25rem;   /* 4px */
  --space-sm: 0.5rem;    /* 8px */
  --space-md: 0.75rem;   /* 12px */
  --space-lg: 1rem;      /* 16px */
  --space-xl: 1.25rem;   /* 20px */
  --space-2xl: 1.5rem;   /* 24px */
  --space-3xl: 2rem;     /* 32px */
  --space-4xl: 2.5rem;   /* 40px */
  --space-5xl: 3rem;     /* 48px */
  --space-6xl: 4rem;     /* 64px */
  
  /* Border Radius */
  --radius-sm: 0.375rem;  /* 6px */
  --radius-md: 0.5rem;    /* 8px */
  --radius-lg: 0.75rem;   /* 12px */
  --radius-xl: 1rem;      /* 16px */
  --radius-2xl: 1.5rem;   /* 24px */
  --radius-full: 9999px;
  
  /* Container Widths */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
}

/* ========================================
   BASE ELEMENT STYLES
======================================== */

body {
  background-color: var(--bg) !important;
  color: var(--text) !important;
  transition: background-color 0.3s ease, color 0.3s ease !important;
  font-family: 'Inter', 'Segoe UI', 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif !important;
  line-height: 1.6 !important;
  letter-spacing: normal !important;
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--text);
  margin-bottom: var(--space-md);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

/* Paragraphs */
p {
  margin-bottom: var(--space-lg);
  line-height: var(--line-height-relaxed);
  color: var(--text);
}

/* Links */
a {
  color: var(--blue);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--gold);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--gold);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Lists */
ul, ol {
  margin-bottom: var(--space-lg);
  padding-left: var(--space-xl);
}

li {
  margin-bottom: var(--space-sm);
  color: var(--text);
}

/* Images */
img {
  max-width: 100%;
  height: auto;
  border-radius: var(--radius-md);
}

/* Code */
code {
  background-color: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-sm);
  padding: var(--space-xs) var(--space-sm);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: var(--font-size-sm);
  color: var(--text);
}

pre {
  background-color: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  padding: var(--space-lg);
  overflow-x: auto;
  margin-bottom: var(--space-lg);
}

pre code {
  background: none;
  border: none;
  padding: 0;
}

/* ========================================
   LAYOUT COMPONENTS
======================================== */

/* Container */
.container {
  width: 100% !important;
  max-width: var(--container-xl) !important;
  margin: 0 auto !important;
  padding: 0 var(--space-lg) !important;
}

@media (min-width: 640px) {
  .container { padding: 0 var(--space-xl); }
}

@media (min-width: 1024px) {
  .container { padding: 0 var(--space-2xl); }
}

/* Page Layout */
.page {
  min-height: 100vh !important;
  background-color: var(--bg) !important;
  color: var(--text) !important;
  transition: background-color 0.3s ease, color 0.3s ease !important;
}

/* Section Spacing */
.section {
  padding: var(--space-5xl) 0;
}

@media (max-width: 768px) {
  .section {
    padding: var(--space-4xl) 0;
  }
}

/* Grid Layouts */
.grid {
  display: grid;
  gap: var(--space-lg);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

/* Responsive Grid */
.grid-responsive {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-lg);
}

@media (max-width: 768px) {
  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: 1fr;
  }
  
  .grid-responsive {
    grid-template-columns: 1fr;
  }
}

/* Flexbox Utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.flex-wrap { flex-wrap: wrap; }
.flex-1 { flex: 1; }

/* ========================================
   COMPONENT LIBRARY - BUTTONS
======================================== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-md) var(--space-xl);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  text-decoration: none;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px; /* Touch-friendly */
  gap: var(--space-sm);
}

.btn:focus {
  outline: 2px solid var(--gold);
  outline-offset: 2px;
}

/* Primary Button */
.btn-primary {
  background-color: var(--cta-bg);
  color: var(--cta-text);
  border-color: var(--gold);
  box-shadow: var(--glow);
}

.btn-primary:hover {
  background-color: var(--cta-hover-bg);
  transform: translateY(-1px);
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.4);
}

.btn-primary:active {
  transform: translateY(0);
}

/* Secondary Button */
.btn-secondary {
  background-color: transparent;
  color: var(--text);
  border-color: var(--border);
}

.btn-secondary:hover {
  background-color: var(--surface);
  border-color: var(--gold);
  color: var(--gold);
}

/* Success Button */
.btn-success {
  background-color: var(--success);
  color: white;
  border-color: var(--success);
}

.btn-success:hover {
  background-color: var(--emerald);
  transform: translateY(-1px);
}

/* Warning Button */
.btn-warning {
  background-color: var(--warning);
  color: white;
  border-color: var(--warning);
}

.btn-warning:hover {
  background-color: var(--copper);
  transform: translateY(-1px);
}

/* Button Sizes */
.btn-sm {
  padding: var(--space-sm) var(--space-md);
  font-size: var(--font-size-sm);
  min-height: 36px;
}

.btn-lg {
  padding: var(--space-lg) var(--space-2xl);
  font-size: var(--font-size-lg);
  min-height: 52px;
}

/* Button States */
.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.btn:disabled:hover {
  background-color: initial;
  border-color: initial;
  color: initial;
}

/* ========================================
   COMPONENT LIBRARY - CARDS
======================================== */

.card {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  box-shadow: var(--shadow) !important;
  transition: all 0.2s ease !important;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

[data-theme='dark'] .card:hover {
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.1);
}

.card-header {
  margin-bottom: var(--space-lg);
}

.card-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text);
  margin-bottom: var(--space-sm);
}

.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-bottom: 0;
}

.card-content {
  margin-bottom: var(--space-lg);
}

.card-footer {
  border-top: 1px solid var(--border);
  padding-top: var(--space-lg);
  margin-top: var(--space-lg);
}

/* Card Variants */
.card-elevated {
  box-shadow: 0 10px 40px rgba(0,0,0,0.1);
}

[data-theme='dark'] .card-elevated {
  box-shadow: 0 10px 40px rgba(255, 215, 0, 0.05);
}

.card-glass {
  background-color: var(--surface);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border);
}

/* Metric Cards */
.metric-card {
  background-color: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  text-align: center;
  transition: all 0.2s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  border-color: var(--gold);
  box-shadow: var(--glow);
}

.metric-value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--gold);
  margin-bottom: var(--space-sm);
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.metric-label {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  font-weight: var(--font-weight-medium);
}

/* ========================================
   COMPONENT LIBRARY - FORMS
======================================== */

.form-group {
  margin-bottom: var(--space-xl);
}

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text);
  margin-bottom: var(--space-sm);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--space-md) var(--space-lg);
  border: 2px solid var(--border);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  background-color: var(--surface);
  color: var(--text);
  transition: all 0.2s ease;
  min-height: 44px; /* Touch-friendly */
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--gold);
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: var(--text-muted);
}

.form-textarea {
  min-height: 120px;
  resize: vertical;
}

/* Form Validation States */
.form-input.error,
.form-select.error,
.form-textarea.error {
  border-color: var(--error);
}

.form-input.success,
.form-select.success,
.form-textarea.success {
  border-color: var(--success);
}

.form-error {
  color: var(--error);
  font-size: var(--font-size-sm);
  margin-top: var(--space-sm);
}

.form-success {
  color: var(--success);
  font-size: var(--font-size-sm);
  margin-top: var(--space-sm);
}

/* Checkbox and Radio */
.form-checkbox,
.form-radio {
  appearance: none;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border);
  border-radius: var(--radius-sm);
  background-color: var(--surface);
  cursor: pointer;
  position: relative;
  margin-right: var(--space-sm);
}

.form-radio {
  border-radius: 50%;
}

.form-checkbox:checked,
.form-radio:checked {
  background-color: var(--gold);
  border-color: var(--gold);
}

.form-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.form-radio:checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background-color: white;
  border-radius: 50%;
}

/* ========================================
   COMPONENT LIBRARY - NAVIGATION
======================================== */

.header {
  background-color: var(--bg);
  border-bottom: 1px solid var(--border);
  padding: var(--space-lg) 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.header-content {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: var(--space-2xl);
}

.logo {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--gold);
  text-decoration: none;
}

.logo-image {
  height: 40px;
  width: auto;
  transition: all 0.3s ease;
}

.logo-image:hover {
  transform: scale(1.05);
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
  list-style: none;
  margin: 0;
  padding: 0;
  flex: 1;
  justify-content: space-between;
}

.nav-main {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
}

.nav-item {
  color: var(--text);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid transparent;
  transition: all 0.3s ease;
  position: relative;
}

.nav-item::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  width: 0;
  height: 3px;
  background: linear-gradient(90deg, #FFD700, #00BFFF);
  transition: all 0.3s ease;
  transform: translateX(-50%);
  border-radius: 2px;
}

.nav-item:hover {
  color: #FFD700 !important;
  transform: translateY(-2px) !important;
  background: rgba(255, 215, 0, 0.1) !important;
  border-color: #FFD700 !important;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3) !important;
}

.nav-item:hover::after {
  width: 100%;
}

.nav-item.active {
  color: var(--gold);
  background-color: var(--surface);
}

/* Tab Navigation */
.nav-tabs {
  margin-bottom: var(--space-xl);
}

.tab-container {
  display: flex;
  justify-content: center;
  gap: var(--space-sm);
  background-color: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--space-sm);
  margin-bottom: var(--space-xl);
}

.tab-button {
  background: none;
  border: none;
  color: var(--text-muted);
  font-weight: var(--font-weight-medium);
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: var(--font-size-sm);
}

.tab-button:hover {
  color: var(--text);
  background-color: var(--glass-overlay);
}

.tab-button.active {
  color: var(--bg);
  background-color: var(--gold);
  box-shadow: var(--glow);
}

/* Mobile Navigation */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: var(--space-lg);
  }

  .nav-menu {
    flex-direction: column;
    gap: var(--space-lg);
    width: 100%;
  }

  .nav-main {
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--space-sm);
  }

  .nav-actions {
    justify-content: center;
    gap: var(--space-md);
  }

  .nav-item {
    padding: var(--space-sm);
    font-size: var(--font-size-sm);
  }

  .logo-image {
    height: 32px;
  }
}

/* Breadcrumb */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  margin-bottom: var(--space-lg);
  font-size: var(--font-size-sm);
}

.breadcrumb-link {
  color: var(--blue);
  text-decoration: none;
  background: none;
  border: 1px solid transparent;
  cursor: pointer;
  font-size: inherit;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.breadcrumb-link:hover {
  color: #FFD700 !important;
  transform: translateY(-1px) !important;
  background: rgba(255, 215, 0, 0.1) !important;
  border-color: #FFD700 !important;
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.2) !important;
}

.breadcrumb-separator {
  color: var(--text-muted);
}

.breadcrumb-current {
  color: var(--text-muted);
}

/* ========================================
   AUTH PAGE COMPONENTS
======================================== */

.auth-container {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--space-xl) 0;
}

.tab-navigation {
  display: flex;
  justify-content: center;
  gap: var(--space-sm);
  margin-bottom: var(--space-4xl);
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--space-sm);
  box-shadow: var(--shadow);
}

.tab-navigation .tab-button {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  background: none;
  border: 1px solid transparent;
  color: var(--text-muted);
  font-weight: var(--font-weight-medium);
  padding: var(--space-md) var(--space-xl);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: var(--font-size-sm);
  min-width: 140px;
  justify-content: center;
  position: relative;
}

.tab-navigation .tab-button::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  width: 0;
  height: 3px;
  background: linear-gradient(90deg, #FFD700, #00BFFF);
  transition: all 0.3s ease;
  transform: translateX(-50%);
  border-radius: 2px;
}

.tab-navigation .tab-button:hover {
  color: #FFD700 !important;
  transform: translateY(-2px) !important;
  background: rgba(255, 215, 0, 0.1) !important;
  border-color: #FFD700 !important;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3) !important;
}

.tab-navigation .tab-button:hover::after {
  width: 100%;
}

.tab-navigation .tab-button.active {
  color: var(--cta-text);
  background: linear-gradient(135deg, var(--gold), #E6C200);
  box-shadow: var(--glow);
  transform: translateY(-1px);
}

.tab-icon {
  font-size: var(--font-size-lg);
}

.auth-content {
  margin-top: var(--space-4xl);
}

.auth-content .card {
  max-width: 600px;
  margin: 0 auto;
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  padding: var(--space-4xl);
  box-shadow: var(--shadow);
  position: relative;
  overflow: hidden;
}

.auth-content .card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--gold), var(--blue), var(--emerald));
}

.auth-content .card-header {
  text-align: center;
  margin-bottom: var(--space-4xl);
}

.auth-content .card-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text);
  margin-bottom: var(--space-md);
}

.auth-content .card-subtitle {
  font-size: var(--font-size-base);
  color: var(--text-muted);
  line-height: 1.6;
}

.auth-content .card-content {
  margin-top: var(--space-xl);
}

/* Enhanced Form Styling for Auth */
.auth-content .form {
  display: flex;
  flex-direction: column;
  gap: var(--space-xl);
}

.auth-content .form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.auth-content .form-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text);
}

.auth-content .form-input {
  padding: var(--space-lg);
  border: 2px solid var(--border);
  border-radius: var(--radius-md);
  background: var(--bg);
  color: var(--text);
  font-size: var(--font-size-base);
  transition: all 0.3s ease;
}

.auth-content .form-input:focus {
  outline: none;
  border-color: var(--gold);
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
}

.auth-content .form-input::placeholder {
  color: var(--text-muted);
}

/* Enhanced Button Styling for Auth */
.auth-content .btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-lg) var(--space-2xl);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  min-height: 48px;
}

.auth-content .btn-primary {
  background: linear-gradient(135deg, var(--gold), #E6C200);
  color: var(--cta-text);
  box-shadow: var(--shadow);
}

.auth-content .btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--glow), var(--shadow);
}

.auth-content .btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.auth-content .btn-lg {
  padding: var(--space-xl) var(--space-3xl);
  font-size: var(--font-size-lg);
  min-height: 56px;
}

.btn-icon {
  font-size: var(--font-size-lg);
}

/* Alert Styling for Auth */
.alert {
  padding: var(--space-lg);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-xl);
  border: 1px solid;
}

.alert-content {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.alert-icon {
  font-size: var(--font-size-lg);
}

.alert-error {
  background: rgba(255, 107, 107, 0.1);
  border-color: var(--error);
  color: var(--error);
}

.alert-success {
  background: rgba(46, 204, 113, 0.1);
  border-color: var(--success);
  color: var(--success);
}

/* Form Components for Registration */
.form-section {
  margin-bottom: var(--space-4xl);
}

.form-header {
  text-align: center;
  margin-bottom: var(--space-3xl);
}

.form-step-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text);
  margin-bottom: var(--space-sm);
}

.form-step-subtitle {
  font-size: var(--font-size-base);
  color: var(--text-muted);
}

.form-button-container {
  display: flex;
  justify-content: center;
  margin-top: var(--space-lg);
}

.form-help-text {
  margin-top: var(--space-sm);
  font-size: var(--font-size-sm);
  color: var(--warning);
  text-align: center;
}

.form-success-text {
  margin-top: var(--space-sm);
  font-size: var(--font-size-sm);
  color: var(--success);
  text-align: center;
}

.form-error-text {
  margin-top: var(--space-sm);
  font-size: var(--font-size-sm);
  color: var(--error);
}

/* Password Input Components */
.password-input-container {
  position: relative;
}

.password-input {
  padding-right: var(--space-5xl) !important;
}

.password-toggle-btn {
  position: absolute;
  right: var(--space-md);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  transition: color 0.2s ease;
  padding: var(--space-xs);
}

.password-toggle-btn:hover {
  color: var(--text);
}

.password-icon {
  width: 20px;
  height: 20px;
}

/* Button Loading State */
.btn-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Form Footer */
.form-footer {
  text-align: center;
  margin-top: var(--space-4xl);
  padding-top: var(--space-xl);
  border-top: 1px solid var(--border);
}

.link-button {
  background: none;
  border: none;
  color: var(--gold);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  font-size: var(--font-size-base);
}

.link-button:hover {
  color: var(--blue);
  text-decoration: underline;
}

/* Info Card Component */
.info-card {
  background: rgba(0, 191, 255, 0.1);
  border: 1px solid var(--blue);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  margin-bottom: var(--space-xl);
}

.info-card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--blue);
  margin-bottom: var(--space-lg);
}

.info-card-text {
  color: var(--text);
  margin-bottom: var(--space-lg);
  line-height: 1.6;
}

/* Radio Group Component */
.radio-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.radio-option {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  cursor: pointer;
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  transition: background-color 0.2s ease;
}

.radio-option:hover {
  background-color: var(--glass-overlay);
}

.radio-input {
  width: 16px;
  height: 16px;
  accent-color: var(--blue);
  cursor: pointer;
}

.radio-label {
  color: var(--text);
  font-size: var(--font-size-base);
  cursor: pointer;
}

/* Registration Form Component */
.registration-form {
  /* Remove any background styling - let parent handle it */
}

/* Progress Indicator */
.progress-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-4xl);
  gap: var(--space-sm);
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-sm);
}

.progress-step-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  border: 2px solid var(--border);
  background: var(--surface);
  color: var(--text-muted);
  transition: all 0.3s ease;
}

.progress-step.active .progress-step-circle {
  border-color: var(--gold);
  background: var(--gold);
  color: var(--cta-text);
}

.progress-step.completed .progress-step-circle {
  border-color: var(--success);
  background: var(--success);
  color: white;
}

.progress-step-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-muted);
}

.progress-step.active .progress-step-label {
  color: var(--gold);
}

.progress-step.completed .progress-step-label {
  color: var(--success);
}

.progress-connector {
  width: 32px;
  height: 2px;
  background: var(--border);
  transition: background-color 0.3s ease;
}

.progress-connector.completed {
  background: var(--success);
}

/* Mobile Progress Indicator */
@media (max-width: 768px) {
  .progress-indicator {
    gap: var(--space-xs);
  }

  .progress-step-circle {
    width: 28px;
    height: 28px;
    font-size: var(--font-size-xs);
  }

  .progress-connector {
    width: 24px;
  }

  .progress-step-label {
    font-size: var(--font-size-xs);
  }
}

/* Modal Components */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-content {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  padding: var(--space-4xl);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow);
  /* Enhanced scrollbar visibility for modals */
  scrollbar-width: thin;
  scrollbar-color: var(--gold) var(--surface);
}

/* Modal scrollbar styling */
.modal-content::-webkit-scrollbar {
  width: 10px;
}

.modal-content::-webkit-scrollbar-track {
  background: var(--surface);
  border-radius: var(--radius-md);
  border: 1px solid var(--border);
}

.modal-content::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--gold), #E6C200);
  border-radius: var(--radius-md);
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #FFE55C, var(--gold));
  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.4);
}

.modal-header {
  margin-bottom: var(--space-xl);
}

.modal-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text);
  margin-bottom: var(--space-sm);
}

.modal-body {
  margin-bottom: var(--space-xl);
}

.modal-text {
  color: var(--text);
  line-height: 1.6;
  margin-bottom: var(--space-lg);
}

.modal-close {
  position: absolute;
  top: var(--space-lg);
  right: var(--space-lg);
  background: none;
  border: none;
  font-size: var(--font-size-2xl);
  color: var(--text-muted);
  cursor: pointer;
  transition: color 0.2s ease;
}

.modal-close:hover {
  color: var(--text);
}

/* Verification Code Input */
.verification-code-input {
  text-align: center;
  font-size: var(--font-size-lg);
  letter-spacing: 0.2em;
  font-weight: var(--font-weight-bold);
}

/* Modal Buttons */
.modal-buttons {
  display: flex;
  gap: var(--space-sm);
  justify-content: flex-end;
  margin-top: var(--space-xl);
}

.modal-buttons .btn {
  flex: 1;
}

.modal-buttons .btn-outline {
  flex: 0 0 auto;
  min-width: 80px;
}

/* Validation Input Components */
.validation-input-container {
  position: relative;
}

.validation-input-wrapper {
  position: relative;
}

.validation-input {
  padding-right: var(--space-5xl) !important;
}

.validation-input.validation-success {
  border-color: var(--success);
}

.validation-input.validation-error {
  border-color: var(--error);
}

.validation-input.validation-checking {
  border-color: var(--info);
}

.validation-icon {
  position: absolute;
  right: var(--space-md);
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.validation-icon-success {
  color: var(--success);
}

.validation-icon-error {
  color: var(--error);
}

.validation-icon-checking {
  color: var(--info);
}

.validation-svg {
  width: 20px;
  height: 20px;
}

/* Validation Feedback */
.validation-feedback {
  margin-top: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  border: 1px solid var(--border);
  background: var(--surface);
}

.validation-feedback-content {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.validation-feedback-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.validation-feedback-message {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.validation-feedback-success {
  border-color: var(--success);
  background: color-mix(in srgb, var(--success) 10%, var(--surface));
  color: var(--success);
}

.validation-feedback-error {
  border-color: var(--error);
  background: color-mix(in srgb, var(--error) 10%, var(--surface));
  color: var(--error);
}

.validation-feedback-checking {
  border-color: var(--info);
  background: color-mix(in srgb, var(--info) 10%, var(--surface));
  color: var(--info);
}

.validation-feedback-default {
  border-color: var(--border);
  background: var(--surface);
  color: var(--text-muted);
}

/* Mobile Responsive for Auth */
@media (max-width: 768px) {
  .auth-container {
    padding: var(--space-lg);
  }

  .tab-navigation {
    flex-direction: column;
    gap: var(--space-xs);
  }

  .tab-navigation .tab-button {
    min-width: auto;
    width: 100%;
  }

  .auth-content .card {
    padding: var(--space-2xl);
  }

  .auth-content .card-title {
    font-size: var(--font-size-2xl);
  }
}

/* ========================================
   COMPONENT LIBRARY - THEME TOGGLE
======================================== */

.theme-toggle {
  background: var(--surface);
  border: 2px solid var(--border);
  border-radius: var(--radius-full);
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: var(--font-size-lg);
  transition: all 0.2s ease;
}

.theme-toggle:hover {
  border-color: var(--gold);
  transform: scale(1.05);
}

.theme-toggle:focus {
  outline: 2px solid var(--gold);
  outline-offset: 2px;
}

/* ========================================
   PAGE-SPECIFIC COMPONENTS
======================================== */

/* Hero Section */
.hero {
  padding: var(--space-6xl) 0;
  background: linear-gradient(135deg, var(--bg) 0%, var(--surface) 100%);
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  gap: var(--space-4xl);
  min-height: 500px;
}

.hero-content .content {
  flex: 1;
  text-align: left;
  max-width: 700px;
}

.hero-content .logo {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 300px;
}

.hero-logo {
  max-width: 100%;
  height: auto;
  max-height: 400px;
  object-fit: contain;
}

.hero-title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--text);
  margin-bottom: var(--space-xl);
  line-height: var(--line-height-tight);
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  color: var(--text-muted);
  margin-bottom: var(--space-4xl);
  line-height: var(--line-height-relaxed);
}

/* Page Header */
.page-header {
  padding: var(--space-5xl) 0 var(--space-4xl);
  text-align: center;
  background: linear-gradient(135deg, var(--bg) 0%, var(--surface) 100%);
}

.page-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--text);
  margin-bottom: var(--space-lg);
}

.page-subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-muted);
  max-width: 800px;
  margin: 0 auto;
  line-height: var(--line-height-relaxed);
}

/* Section Header */
.section-header {
  text-align: center;
  margin-bottom: var(--space-5xl);
}

.section-header h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text);
  margin-bottom: var(--space-lg);
}

.section-header p {
  font-size: var(--font-size-lg);
  color: var(--text-muted);
  max-width: 600px;
  margin: 0 auto;
}

/* CTA Buttons Container */
.cta-buttons {
  display: flex;
  gap: var(--space-lg);
  justify-content: center;
  flex-wrap: wrap;
  margin-top: var(--space-4xl);
}

@media (max-width: 768px) {
  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-buttons .btn {
    width: 100%;
    max-width: 300px;
  }
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-lg);
  margin: var(--space-4xl) 0;
}

@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-md);
  }
}

@media (max-width: 480px) {
  .metrics-grid {
    grid-template-columns: 1fr;
  }
}

/* ========================================
   EXISTING COMPONENT COMPATIBILITY
   Styles for existing components to maintain functionality
======================================== */

/* Overview Cards */
.overview-grid,
.trust-grid,
.links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-xl);
  margin-top: var(--space-4xl);
}

.overview-card,
.trust-item,
.link-card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  padding: var(--space-2xl);
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
  color: var(--text);
  position: relative;
  overflow: hidden;
}

.overview-card:hover,
.trust-item:hover,
.link-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(255, 215, 0, 0.15);
  border-color: rgba(255, 215, 0, 0.3);
}

[data-theme='dark'] .overview-card:hover,
[data-theme='dark'] .trust-item:hover,
[data-theme='dark'] .link-card:hover {
  box-shadow: 0 12px 40px rgba(255, 215, 0, 0.2);
}

/* Link Card Specific Styling */
.link-card {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
  cursor: default;
}

.link-icon {
  font-size: 2.5rem;
  margin-bottom: var(--space-lg);
  display: block;
}

.link-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.link-content h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text);
  margin-bottom: var(--space-md);
  line-height: var(--line-height-tight);
}

.link-content p {
  font-size: var(--font-size-base);
  color: var(--text-muted);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-lg);
  flex: 1;
}

/* Golden Button Link */
.btn-link-gold {
  background: linear-gradient(135deg, var(--gold), #f59e0b);
  color: var(--bg);
  border: none;
  border-radius: var(--radius-md);
  padding: var(--space-sm) var(--space-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
  align-self: flex-start;
}

.btn-link-gold:hover {
  background: linear-gradient(135deg, #f59e0b, var(--gold));
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
}

.btn-link-gold:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(255, 215, 0, 0.3);
}

/* Trust Item Styling */
.trust-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-lg);
  text-align: left;
}

.trust-icon {
  font-size: 2rem;
  flex-shrink: 0;
  margin-top: var(--space-xs);
}

.trust-content h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text);
  margin-bottom: var(--space-sm);
}

.trust-content p {
  font-size: var(--font-size-base);
  color: var(--text-muted);
  line-height: var(--line-height-relaxed);
}

/* Overview Card Styling */
.overview-card {
  text-align: left;
}

.card-icon {
  font-size: 2.5rem;
  margin-bottom: var(--space-lg);
  display: block;
}

.overview-card h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text);
  margin-bottom: var(--space-lg);
}

.overview-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.overview-card li {
  font-size: var(--font-size-base);
  color: var(--text-muted);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-md);
  padding-left: 0;
}

.overview-card li:last-child {
  margin-bottom: 0;
}

.overview-card strong {
  color: var(--gold);
  font-weight: var(--font-weight-semibold);
}

/* ========================================
   GALLERY & MEDIA SECTION
======================================== */

.gallery-section {
  padding: var(--space-6xl) 0;
  background: var(--bg);
}

/* ========================================
   MEDIA SHOWCASE SECTION
======================================== */

.media-showcase {
  padding: var(--space-4xl) 0;
  background: linear-gradient(135deg, var(--surface) 0%, rgba(255, 215, 0, 0.03) 100%);
}

.video-showcase {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4xl);
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-xl);
}

.video-description {
  padding: var(--space-2xl);
}

.video-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
  background: rgba(255, 215, 0, 0.1);
  border: 1px solid rgba(255, 215, 0, 0.3);
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-full);
  margin-bottom: var(--space-xl);
}

.badge-icon {
  font-size: var(--font-size-lg);
}

.badge-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--gold);
}

.video-description h3 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text);
  margin-bottom: var(--space-xl);
  line-height: var(--line-height-tight);
}

.intro-text {
  font-size: var(--font-size-lg);
  color: var(--text-muted);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-2xl);
}

.process-details h4 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--gold);
  margin: var(--space-xl) 0 var(--space-md) 0;
}

.process-details p {
  color: var(--text-muted);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-lg);
}

.process-list {
  list-style: none;
  padding: 0;
  margin: var(--space-lg) 0;
}

.process-list li {
  display: flex;
  align-items: flex-start;
  gap: var(--space-md);
  margin-bottom: var(--space-md);
  color: var(--text-muted);
  line-height: var(--line-height-relaxed);
}

/* Removed lightning bolt icon from process list */

.process-list strong {
  color: var(--text);
}

.efficiency-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-lg);
  margin-top: var(--space-2xl);
  padding: var(--space-xl);
  background: rgba(255, 215, 0, 0.05);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(255, 215, 0, 0.2);
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--gold);
  margin-bottom: var(--space-xs);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  font-weight: var(--font-weight-medium);
}

/* Video Player Section */
.video-player {
  position: relative;
}

.video-container {
  position: relative;
  border-radius: var(--radius-xl);
  overflow: visible; /* Changed from hidden to allow overlay below */
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  background: var(--surface-elevated);
  border: 1px solid var(--border);
}

.operations-video {
  width: 100%;
  height: auto;
  display: block;
  border-radius: var(--radius-xl);
  cursor: pointer; /* Make it clear the video is clickable */
}

.video-overlay {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.9);
  padding: var(--space-xl);
  color: white;
  border-radius: var(--radius-lg);
  margin-top: var(--space-md);
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  pointer-events: none;
  z-index: 10;
}

.video-container:hover .video-overlay {
  opacity: 1;
  transform: translateY(0);
}

.video-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-xs);
}

.video-subtitle {
  font-size: var(--font-size-sm);
  opacity: 0.9;
}

/* Mobile Responsive Styles for Video Showcase */
@media (max-width: 1024px) {
  .video-showcase {
    grid-template-columns: 1fr;
    gap: var(--space-3xl);
    text-align: center;
  }

  .video-description {
    order: 2;
    padding: var(--space-xl);
  }

  .video-player {
    order: 1;
  }

  .efficiency-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-md);
  }
}

@media (max-width: 768px) {
  .video-description h3 {
    font-size: var(--font-size-2xl);
  }

  .intro-text {
    font-size: var(--font-size-base);
  }

  .process-details h4 {
    font-size: var(--font-size-lg);
  }

  .efficiency-stats {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
    padding: var(--space-lg);
  }

  .stat-number {
    font-size: var(--font-size-xl);
  }

  .video-overlay {
    padding: var(--space-lg);
  }

  .video-title {
    font-size: var(--font-size-lg);
  }
}

@media (max-width: 480px) {
  .video-showcase {
    padding: 0 var(--space-md);
  }

  .video-description {
    padding: var(--space-md);
  }

  .process-list li {
    font-size: var(--font-size-sm);
  }
}

/* Process List Styling - Grid Layout */
.process-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--space-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.process-list li {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  color: var(--text);
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  transition: all 0.3s ease;
  box-shadow: var(--shadow);
  text-align: left;
  display: flex;
  flex-direction: column;
}

.process-list li:hover {
  background: var(--surface-elevated);
  border-color: rgba(255, 215, 0, 0.3);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.15);
}

.process-list li strong {
  color: var(--gold);
  font-weight: var(--font-weight-bold);
  display: block;
  margin-bottom: var(--space-sm);
  font-size: var(--font-size-lg);
}

/* Mobile responsive for process list */
@media (max-width: 1024px) {
  .process-list {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-md);
  }
}

@media (max-width: 768px) {
  .process-list {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-md);
  }

  .process-list li {
    padding: var(--space-md);
  }
}

@media (max-width: 480px) {
  .process-list {
    grid-template-columns: 1fr;
    gap: var(--space-sm);
  }
}

.video-content {
  display: flex;
  align-items: center;
  gap: var(--space-2xl);
  text-align: left;
}

.video-icon {
  font-size: 4rem;
  flex-shrink: 0;
  opacity: 0.8;
}

.video-info h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text);
  margin-bottom: var(--space-md);
}

.video-info p {
  font-size: var(--font-size-base);
  color: var(--text-muted);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-xl);
}

.btn-video-play {
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
  background: linear-gradient(135deg, var(--gold), #f59e0b);
  color: var(--bg);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-md) var(--space-xl);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.btn-video-play:hover {
  background: linear-gradient(135deg, #f59e0b, var(--gold));
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(255, 215, 0, 0.4);
}

.play-icon {
  font-size: var(--font-size-sm);
}

/* Operations Gallery */
.operations-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-xl);
}

.gallery-card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
  display: flex;
  align-items: flex-start;
  gap: var(--space-lg);
  text-align: left;
}

.gallery-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.15);
  border-color: rgba(255, 215, 0, 0.3);
}

.gallery-icon {
  font-size: 2rem;
  flex-shrink: 0;
  margin-top: var(--space-xs);
}

.gallery-content h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text);
  margin-bottom: var(--space-sm);
  line-height: var(--line-height-tight);
}

.gallery-content p {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  line-height: var(--line-height-relaxed);
}

/* Gallery Actions */
.gallery-actions {
  display: flex;
  gap: var(--space-lg);
  justify-content: center;
  margin-top: var(--space-4xl);
  flex-wrap: wrap;
}

/* ========================================
   FOOTER STYLES
======================================== */

.clean-footer {
  background: var(--surface);
  border-top: 1px solid var(--border);
  padding: var(--space-4xl) 0 var(--space-2xl);
  margin-top: var(--space-6xl);
}

.footer-content {
  display: grid;
  grid-template-columns: 300px 1fr 200px;
  gap: var(--space-3xl);
  margin-bottom: var(--space-3xl);
  align-items: flex-start;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.footer-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--space-md);
  grid-column: 1;
  position: relative;
}

.footer-logo {
  height: 200px;
  width: auto;
  object-fit: contain;
  margin-bottom: var(--space-sm);
}

.footer-info h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text);
  margin: 0;
}

.footer-info p {
  font-size: var(--font-size-base);
  color: var(--text-muted);
  margin: 0;
  max-width: 400px;
}

/* Footer Contact Form */
.footer-contact {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  max-width: 400px;
  grid-column: 2;
  position: relative;
}

.footer-contact h4 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  color: var(--text);
  margin: 0 0 var(--space-sm) 0;
  text-align: left;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-xs);
}

.contact-form input,
.contact-form textarea {
  background: var(--bg);
  border: 1px solid var(--border);
  border-radius: var(--radius-sm);
  padding: var(--space-xs);
  font-size: var(--font-size-xs);
  color: var(--text);
  transition: border-color 0.2s ease;
  width: 100%;
  box-sizing: border-box;
}

.contact-form input:focus,
.contact-form textarea:focus {
  outline: none;
  border-color: var(--gold);
  box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.1);
}

.contact-form textarea {
  resize: vertical;
  min-height: 50px;
  height: 50px;
}

.contact-form button {
  align-self: flex-start;
  min-width: 80px;
  padding: var(--space-xs) var(--space-sm);
  font-size: var(--font-size-xs);
  margin-top: var(--space-xs);
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  align-items: flex-start;
  grid-column: 3;
  position: relative;
}

.footer-link-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  text-decoration: none;
  position: relative;
}

.footer-link-btn:hover {
  color: var(--text);
  background: rgba(255, 215, 0, 0.1);
  transform: translateX(-2px);
}

.footer-link-btn.text-gold {
  color: var(--gold);
  font-weight: var(--font-weight-semibold);
}

.footer-link-btn.text-gold:hover {
  color: #f59e0b;
  background: rgba(255, 215, 0, 0.15);
}

.footer-bottom {
  text-align: center;
  padding-top: var(--space-xl);
  border-top: 1px solid var(--border);
}

.footer-bottom p {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin: 0;
}

/* ========================================
   MODAL STYLES
======================================== */

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--space-lg);
}

.modal-content {
  background: var(--surface);
  border-radius: var(--radius-xl);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  border: 1px solid var(--border);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xl) var(--space-2xl);
  border-bottom: 1px solid var(--border);
  background: linear-gradient(135deg, var(--surface), rgba(255, 215, 0, 0.05));
}

.modal-header h2 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-2xl);
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--space-xs);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  color: var(--text);
  background: rgba(255, 215, 0, 0.1);
}

.modal-body {
  padding: var(--space-2xl);
  overflow-y: auto;
  max-height: calc(80vh - 80px);
  /* Enhanced scrollbar visibility */
  scrollbar-width: thin;
  scrollbar-color: var(--gold) var(--surface);
}

/* Modal body scrollbar styling */
.modal-body::-webkit-scrollbar {
  width: 10px;
}

.modal-body::-webkit-scrollbar-track {
  background: var(--surface);
  border-radius: var(--radius-md);
  border: 1px solid var(--border);
}

.modal-body::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--gold), #E6C200);
  border-radius: var(--radius-md);
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #FFE55C, var(--gold));
  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.4);
}

.legal-content h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--gold);
  margin: var(--space-xl) 0 var(--space-md) 0;
}

.legal-content h3:first-child {
  margin-top: 0;
}

.legal-content p {
  font-size: var(--font-size-base);
  color: var(--text-muted);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-lg);
}

[data-theme='dark'] .overview-card:hover,
[data-theme='dark'] .trust-item:hover,
[data-theme='dark'] .link-card:hover {
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.1);
}

.card-icon,
.trust-icon,
.link-icon {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--space-sm);
  display: block;
}

/* Modern Website Compatibility */
.modern-website {
  background: var(--bg) !important;
  color: var(--text) !important;
}

.modern-website * {
  letter-spacing: normal !important;
}

/* Clean Header Compatibility */
.clean-header {
  background: var(--bg);
  border-bottom: 1px solid var(--border);
  padding: var(--space-lg) 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: var(--shadow);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

/* Gallery Components */
.gallery-content {
  padding: var(--space-5xl) 0;
}

.gallery-wrapper {
  background: var(--surface);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  box-shadow: var(--shadow);
}

.gallery-notification {
  padding: var(--space-4xl) 0;
}

/* Gallery Grid Layouts */
.gallery-grid-1 {
  display: grid !important;
  grid-template-columns: 1fr !important;
  gap: var(--space-xl) !important;
}

.gallery-grid-2 {
  display: grid !important;
  grid-template-columns: repeat(2, 1fr) !important;
  gap: var(--space-lg) !important;
}

.gallery-grid-3 {
  display: grid !important;
  grid-template-columns: repeat(3, 1fr) !important;
  gap: var(--space-lg) !important;
}

.gallery-grid-4 {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: var(--space-md) !important;
}

@media (max-width: 1024px) {
  .gallery-grid-3,
  .gallery-grid-4 {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (max-width: 640px) {
  .gallery-grid-2,
  .gallery-grid-3,
  .gallery-grid-4 {
    grid-template-columns: 1fr !important;
  }
}

/* Gallery Image Cards */
.gallery-image-card {
  aspect-ratio: 16/10 !important;
  max-height: 300px !important;
  overflow: hidden !important;
}

.gallery-image-card img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

/* Gallery Loading States */
.gallery-loading-shimmer {
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.1) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.gallery-image-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Professional Slideshow Component */
.slideshow-container {
  position: relative !important;
  width: 100% !important;
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-xl) !important;
  overflow: hidden !important;
  box-shadow: var(--shadow) !important;
}

.slideshow-main {
  position: relative !important;
  width: 100% !important;
  height: 100% !important;
}

.slideshow-image-container {
  position: relative !important;
  width: 100% !important;
  height: 100% !important;
  overflow: hidden !important;
}

.slideshow-image {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  transition: opacity 0.3s ease, transform 0.3s ease !important;
}

.slideshow-image-clickable {
  cursor: pointer !important;
}

.slideshow-image-clickable:hover {
  transform: scale(1.02) !important;
}

.slideshow-loading {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: var(--surface) !important;
}

.slideshow-loading-spinner {
  width: 40px !important;
  height: 40px !important;
  border: 3px solid var(--border) !important;
  border-top: 3px solid var(--gold) !important;
  border-radius: 50% !important;
  animation: spin 1s linear infinite !important;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.slideshow-overlay {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8)) !important;
  padding: var(--space-2xl) var(--space-xl) var(--space-xl) !important;
  color: white !important;
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
}

.slideshow-container:hover .slideshow-overlay {
  opacity: 1 !important;
}

.slideshow-title {
  font-size: var(--font-size-xl) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: var(--space-sm) !important;
  color: white !important;
}

.slideshow-description {
  font-size: var(--font-size-sm) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  margin-bottom: var(--space-sm) !important;
  line-height: var(--line-height-relaxed) !important;
}

.slideshow-category {
  display: inline-block !important;
  background: var(--gold) !important;
  color: black !important;
  padding: var(--space-xs) var(--space-sm) !important;
  border-radius: var(--radius-sm) !important;
  font-size: var(--font-size-xs) !important;
  font-weight: var(--font-weight-medium) !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

/* Slideshow Navigation Arrows */
.slideshow-arrow {
  position: absolute !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  background: rgba(0, 0, 0, 0.7) !important;
  color: white !important;
  border: none !important;
  width: 50px !important;
  height: 50px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  z-index: 10 !important;
  opacity: 0 !important;
}

.slideshow-container:hover .slideshow-arrow {
  opacity: 1 !important;
}

.slideshow-arrow:hover {
  background: rgba(0, 0, 0, 0.9) !important;
  transform: translateY(-50%) scale(1.1) !important;
}

.slideshow-arrow-left {
  left: var(--space-lg) !important;
}

.slideshow-arrow-right {
  right: var(--space-lg) !important;
}

/* Slideshow Controls */
.slideshow-controls {
  position: absolute !important;
  top: var(--space-lg) !important;
  right: var(--space-lg) !important;
  display: flex !important;
  align-items: center !important;
  gap: var(--space-md) !important;
  background: rgba(0, 0, 0, 0.7) !important;
  padding: var(--space-sm) var(--space-md) !important;
  border-radius: var(--radius-full) !important;
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
}

.slideshow-container:hover .slideshow-controls {
  opacity: 1 !important;
}

.slideshow-play-pause {
  background: none !important;
  border: none !important;
  color: white !important;
  cursor: pointer !important;
  padding: var(--space-xs) !important;
  border-radius: var(--radius-sm) !important;
  transition: background-color 0.2s ease !important;
}

.slideshow-play-pause:hover {
  background: rgba(255, 255, 255, 0.2) !important;
}

.slideshow-counter {
  color: white !important;
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-medium) !important;
}

/* Slideshow Indicators */
.slideshow-indicators {
  position: absolute !important;
  bottom: var(--space-lg) !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  display: flex !important;
  gap: var(--space-sm) !important;
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
}

.slideshow-container:hover .slideshow-indicators {
  opacity: 1 !important;
}

.slideshow-indicator {
  width: 12px !important;
  height: 12px !important;
  border-radius: 50% !important;
  background: rgba(255, 255, 255, 0.5) !important;
  border: none !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
}

.slideshow-indicator.active {
  background: var(--gold) !important;
  transform: scale(1.2) !important;
}

.slideshow-indicator:hover {
  background: rgba(255, 255, 255, 0.8) !important;
}

/* Slideshow Thumbnails */
.slideshow-thumbnails {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  background: rgba(0, 0, 0, 0.9) !important;
  padding: var(--space-md) !important;
  transform: translateY(100%) !important;
  transition: transform 0.3s ease !important;
}

.slideshow-container:hover .slideshow-thumbnails {
  transform: translateY(0) !important;
}

.slideshow-thumbnails-container {
  display: flex !important;
  gap: var(--space-sm) !important;
  overflow-x: auto !important;
  padding: var(--space-xs) 0 !important;
  scrollbar-width: thin !important;
  scrollbar-color: var(--gold) transparent !important;
}

.slideshow-thumbnails-container::-webkit-scrollbar {
  height: 4px !important;
}

.slideshow-thumbnails-container::-webkit-scrollbar-track {
  background: transparent !important;
}

.slideshow-thumbnails-container::-webkit-scrollbar-thumb {
  background: var(--gold) !important;
  border-radius: 2px !important;
}

.slideshow-thumbnail {
  position: relative !important;
  flex-shrink: 0 !important;
  width: 80px !important;
  height: 60px !important;
  border: 2px solid transparent !important;
  border-radius: var(--radius-sm) !important;
  overflow: hidden !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  background: none !important;
  padding: 0 !important;
}

.slideshow-thumbnail.active {
  border-color: var(--gold) !important;
  transform: scale(1.05) !important;
}

.slideshow-thumbnail:hover {
  border-color: rgba(255, 215, 0, 0.6) !important;
  transform: scale(1.02) !important;
}

.slideshow-thumbnail-image {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  transition: opacity 0.3s ease !important;
}

.slideshow-thumbnail:hover .slideshow-thumbnail-image {
  opacity: 0.8 !important;
}

.slideshow-thumbnail-overlay {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8)) !important;
  padding: var(--space-xs) !important;
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
}

.slideshow-thumbnail:hover .slideshow-thumbnail-overlay {
  opacity: 1 !important;
}

.slideshow-thumbnail-title {
  color: white !important;
  font-size: var(--font-size-xs) !important;
  font-weight: var(--font-weight-medium) !important;
  line-height: 1.2 !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  -webkit-box-orient: vertical !important;
  overflow: hidden !important;
}

/* Empty State */
.slideshow-empty {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  height: 100% !important;
  color: var(--text-muted) !important;
  text-align: center !important;
}

.slideshow-empty-icon {
  font-size: 4rem !important;
  margin-bottom: var(--space-lg) !important;
  opacity: 0.5 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .slideshow-arrow {
    width: 40px !important;
    height: 40px !important;
  }

  .slideshow-arrow-left {
    left: var(--space-sm) !important;
  }

  .slideshow-arrow-right {
    right: var(--space-sm) !important;
  }

  .slideshow-controls {
    top: var(--space-sm) !important;
    right: var(--space-sm) !important;
  }

  .slideshow-thumbnail {
    width: 60px !important;
    height: 45px !important;
  }

  .slideshow-thumbnails {
    padding: var(--space-sm) !important;
  }
}

/* Compact Gallery Component */
.compact-gallery {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-xl) !important;
  overflow: hidden !important;
  box-shadow: var(--shadow) !important;
}

.compact-gallery-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: flex-start !important;
  padding: var(--space-xl) !important;
  border-bottom: 1px solid var(--border) !important;
  background: var(--bg) !important;
}

.compact-gallery-info h3 {
  font-size: var(--font-size-xl) !important;
  font-weight: var(--font-weight-bold) !important;
  color: var(--text) !important;
  margin-bottom: var(--space-xs) !important;
}

.compact-gallery-subtitle {
  font-size: var(--font-size-sm) !important;
  color: var(--text-muted) !important;
  margin: 0 !important;
}

.compact-gallery-controls {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-md) !important;
}

.view-mode-toggle {
  display: flex !important;
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-md) !important;
  overflow: hidden !important;
}

.view-mode-btn {
  background: none !important;
  border: none !important;
  padding: var(--space-sm) !important;
  color: var(--text-muted) !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.view-mode-btn:hover {
  background: var(--bg) !important;
  color: var(--text) !important;
}

.view-mode-btn.active {
  background: var(--gold) !important;
  color: black !important;
}

.compact-gallery-categories {
  display: flex !important;
  gap: var(--space-sm) !important;
  padding: var(--space-lg) var(--space-xl) !important;
  border-bottom: 1px solid var(--border) !important;
  background: var(--bg) !important;
  overflow-x: auto !important;
  scrollbar-width: thin !important;
}

.compact-gallery-categories::-webkit-scrollbar {
  height: 4px !important;
}

.compact-gallery-categories::-webkit-scrollbar-track {
  background: transparent !important;
}

.compact-gallery-categories::-webkit-scrollbar-thumb {
  background: var(--border) !important;
  border-radius: 2px !important;
}

.category-btn {
  flex-shrink: 0 !important;
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-muted) !important;
  padding: var(--space-sm) var(--space-md) !important;
  border-radius: var(--radius-md) !important;
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-medium) !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.category-btn:hover {
  background: var(--bg) !important;
  border-color: var(--gold) !important;
  color: var(--text) !important;
}

.category-btn.active {
  background: var(--gold) !important;
  border-color: var(--gold) !important;
  color: black !important;
}

.compact-gallery-content {
  position: relative !important;
}

.compact-gallery-footer {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: var(--space-lg) var(--space-xl) !important;
  border-top: 1px solid var(--border) !important;
  background: var(--bg) !important;
}

.view-more-text {
  font-size: var(--font-size-sm) !important;
  color: var(--text-muted) !important;
  margin: 0 !important;
}

.view-more-btn {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-sm) !important;
  background: none !important;
  border: none !important;
  color: var(--gold) !important;
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-medium) !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.view-more-btn:hover {
  color: var(--text) !important;
  transform: translateX(2px) !important;
}

/* Empty State */
.compact-gallery-empty {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--space-6xl) var(--space-xl) !important;
  text-align: center !important;
}

.compact-gallery-empty-content {
  max-width: 400px !important;
  margin: 0 auto !important;
}

.compact-gallery-empty-icon {
  font-size: 4rem !important;
  margin-bottom: var(--space-lg) !important;
  opacity: 0.5 !important;
}

.compact-gallery-empty h3 {
  font-size: var(--font-size-xl) !important;
  font-weight: var(--font-weight-bold) !important;
  color: var(--text) !important;
  margin-bottom: var(--space-sm) !important;
}

.compact-gallery-empty p {
  font-size: var(--font-size-base) !important;
  color: var(--text-muted) !important;
  margin: 0 !important;
}

/* Professional Gallery Hero Section */
.gallery-hero {
  background: linear-gradient(135deg, var(--bg) 0%, var(--surface) 100%) !important;
  padding: var(--space-6xl) 0 var(--space-5xl) !important;
  position: relative !important;
  overflow: hidden !important;
}

.gallery-hero::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: radial-gradient(ellipse at center, rgba(255, 215, 0, 0.03) 0%, transparent 70%) !important;
  pointer-events: none !important;
}

.gallery-hero-content {
  position: relative !important;
  z-index: 2 !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

/* Professional Breadcrumb */
.gallery-breadcrumb {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-sm) !important;
  margin-bottom: var(--space-4xl) !important;
}

.breadcrumb-link {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-xs) !important;
  background: none !important;
  border: none !important;
  color: var(--text-muted) !important;
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-medium) !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  padding: var(--space-xs) var(--space-sm) !important;
  border-radius: var(--radius-md) !important;
}

.breadcrumb-link:hover {
  color: var(--gold) !important;
  background: rgba(255, 215, 0, 0.05) !important;
}

.breadcrumb-separator {
  color: var(--border) !important;
  display: flex !important;
  align-items: center !important;
}

.breadcrumb-current {
  color: var(--gold) !important;
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-medium) !important;
}

/* Hero Main Content */
.gallery-hero-main {
  text-align: center !important;
}

.gallery-hero-text {
  margin-bottom: var(--space-5xl) !important;
}

.gallery-hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem) !important;
  font-weight: var(--font-weight-bold) !important;
  color: var(--text) !important;
  line-height: var(--line-height-tight) !important;
  margin-bottom: var(--space-xl) !important;
  letter-spacing: -0.02em !important;
}

.gallery-hero-title-accent {
  color: var(--gold) !important;
  display: block !important;
  text-shadow: var(--glow) !important;
}

.gallery-hero-description {
  font-size: var(--font-size-lg) !important;
  color: var(--text-muted) !important;
  line-height: var(--line-height-relaxed) !important;
  max-width: 700px !important;
  margin: 0 auto !important;
}

/* Professional Stats Grid */
.gallery-hero-stats {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
  gap: var(--space-xl) !important;
  max-width: 900px !important;
  margin: 0 auto !important;
}

.gallery-stat-card {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--space-2xl) !important;
  display: flex !important;
  align-items: center !important;
  gap: var(--space-lg) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
  box-shadow: var(--shadow) !important;
}

.gallery-stat-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 3px !important;
  background: linear-gradient(90deg, var(--gold), var(--blue)) !important;
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
}

.gallery-stat-card:hover {
  transform: translateY(-3px) !important;
  border-color: rgba(255, 215, 0, 0.3) !important;
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.15) !important;
}

.gallery-stat-card:hover::before {
  opacity: 1 !important;
}

.gallery-stat-icon {
  font-size: 2.5rem !important;
  flex-shrink: 0 !important;
  opacity: 0.8 !important;
}

.gallery-stat-content {
  text-align: left !important;
}

.gallery-stat-value {
  font-size: var(--font-size-2xl) !important;
  font-weight: var(--font-weight-bold) !important;
  color: var(--gold) !important;
  margin-bottom: var(--space-xs) !important;
  line-height: 1 !important;
}

.gallery-stat-label {
  font-size: var(--font-size-sm) !important;
  color: var(--text-muted) !important;
  font-weight: var(--font-weight-medium) !important;
  line-height: var(--line-height-tight) !important;
}

/* Responsive Design for Gallery Hero */
@media (max-width: 768px) {
  .gallery-hero {
    padding: var(--space-5xl) 0 var(--space-4xl) !important;
  }

  .gallery-hero-title {
    font-size: 2.5rem !important;
  }

  .gallery-hero-description {
    font-size: var(--font-size-base) !important;
  }

  .gallery-hero-stats {
    grid-template-columns: 1fr !important;
    gap: var(--space-lg) !important;
  }

  .gallery-stat-card {
    padding: var(--space-xl) !important;
  }

  .gallery-stat-icon {
    font-size: 2rem !important;
  }
}

/* Responsive Design for Compact Gallery */
@media (max-width: 768px) {
  .compact-gallery-header {
    flex-direction: column !important;
    gap: var(--space-lg) !important;
    align-items: stretch !important;
  }

  .compact-gallery-controls {
    justify-content: center !important;
  }

  .compact-gallery-categories {
    padding: var(--space-md) !important;
  }

  .compact-gallery-footer {
    flex-direction: column !important;
    gap: var(--space-md) !important;
    text-align: center !important;
  }

  .view-more-btn {
    justify-content: center !important;
  }
}

.notification-card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  display: flex;
  align-items: center;
  gap: var(--space-lg);
  box-shadow: var(--shadow);
}

.notification-icon {
  font-size: var(--font-size-3xl);
  flex-shrink: 0;
}

.notification-content h3 {
  color: var(--text);
  margin-bottom: var(--space-sm);
}

.notification-content p {
  color: var(--text-muted);
  margin-bottom: var(--space-lg);
}

.notification-meta {
  display: flex;
  gap: var(--space-lg);
  font-size: var(--font-size-sm);
}

.update-frequency {
  color: var(--text-muted);
}

.verification-badge {
  color: var(--success);
  font-weight: var(--font-weight-semibold);
}

/* Calculator Components */
.calculator-intro,
.calculator-assumptions,
.calculator-tool,
.example-scenarios {
  padding: var(--space-5xl) 0;
}

.intro-grid,
.assumptions-grid,
.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-lg);
}

.intro-card,
.assumption-card,
.scenario-card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  box-shadow: var(--shadow);
  transition: all 0.2s ease;
}

.intro-card:hover,
.assumption-card:hover,
.scenario-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

[data-theme='dark'] .intro-card:hover,
[data-theme='dark'] .assumption-card:hover,
[data-theme='dark'] .scenario-card:hover {
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.1);
}

.intro-icon,
.assumption-icon,
.scenario-icon {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--space-sm);
  display: block;
}

.calculator-wrapper {
  background: var(--surface);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  box-shadow: var(--shadow);
  border: 1px solid var(--border);
}

/* ========================================
   RESPONSIVE DESIGN BREAKPOINTS
======================================== */

/* Mobile First Approach */
@media (max-width: 480px) {
  .hero-title {
    font-size: var(--font-size-3xl);
  }

  .page-title {
    font-size: var(--font-size-3xl);
  }

  .section-header h2 {
    font-size: var(--font-size-2xl);
  }

  .container {
    padding: 0 var(--space-md);
  }

  .section {
    padding: var(--space-3xl) 0;
  }

  .hero {
    padding: var(--space-4xl) 0;
  }

  .hero-content {
    flex-direction: column;
    text-align: center;
    gap: var(--space-2xl);
    min-height: auto;
  }

  .hero-content .content {
    text-align: center;
    max-width: 100%;
  }

  .hero-content .logo {
    min-width: auto;
    order: -1;
  }

  .hero-logo {
    max-height: 200px;
  }

  .phases-grid {
    grid-template-columns: 1fr !important;
    gap: var(--space-lg) !important;
  }

  .phase-card {
    padding: var(--space-lg) !important;
  }

  .phase-actions {
    justify-content: center !important;
  }

  .phase-purchase-btn {
    min-width: 70px !important;
    font-size: 10px !important;
  }

  .page-header {
    padding: var(--space-4xl) 0 var(--space-3xl);
  }

  .overview-grid,
  .trust-grid,
  .links-grid,
  .intro-grid,
  .assumptions-grid,
  .scenarios-grid {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }

  .overview-card,
  .trust-item,
  .link-card {
    padding: var(--space-xl);
  }

  .trust-item {
    flex-direction: column;
    text-align: center;
    align-items: center;
  }

  .trust-icon {
    margin-top: 0;
    margin-bottom: var(--space-md);
  }

  /* Gallery Mobile Styles */
  .video-content {
    flex-direction: column;
    text-align: center;
    gap: var(--space-xl);
  }

  .video-icon {
    font-size: 3rem;
  }

  .operations-gallery {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }

  .gallery-card {
    flex-direction: column;
    text-align: center;
    align-items: center;
    padding: var(--space-xl);
  }

  .gallery-icon {
    margin-top: 0;
    margin-bottom: var(--space-md);
    font-size: 2.5rem;
  }

  .gallery-actions {
    flex-direction: column;
    align-items: center;
  }

  .gallery-actions .btn {
    width: 100%;
    max-width: 300px;
  }

  /* Footer Mobile Styles */
  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--space-2xl);
    text-align: center;
  }

  .footer-info {
    align-items: center;
    text-align: center;
  }

  .footer-contact {
    order: 2;
  }

  .footer-links {
    align-items: center;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--space-md);
    order: 3;
  }

  .footer-link-btn {
    font-size: var(--font-size-xs);
    padding: var(--space-xs) var(--space-sm);
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  /* Modal Mobile Styles */
  .modal-overlay {
    padding: var(--space-md);
  }

  .modal-content {
    max-height: 90vh;
  }

  .modal-header {
    padding: var(--space-lg) var(--space-xl);
  }

  .modal-header h2 {
    font-size: var(--font-size-lg);
  }

  .modal-body {
    padding: var(--space-xl);
    max-height: calc(90vh - 70px);
  }

  .legal-content h3 {
    font-size: var(--font-size-base);
  }

  .legal-content p {
    font-size: var(--font-size-sm);
  }
}

@media (min-width: 768px) {
  .hero-title {
    font-size: var(--font-size-5xl);
  }
}

@media (min-width: 1024px) {
  .hero {
    padding: var(--space-6xl) 0;
  }

  .section {
    padding: var(--space-6xl) 0;
  }
}

/* ========================================
   ACCESSIBILITY ENHANCEMENTS
======================================== */

/* Focus Indicators */
*:focus {
  outline: 2px solid var(--gold);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Skip Link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--gold);
  color: var(--bg);
  padding: var(--space-sm) var(--space-md);
  text-decoration: none;
  border-radius: var(--radius-md);
  z-index: 9999;
  font-weight: var(--font-weight-semibold);
}

.skip-link:focus {
  top: 6px;
}

/* Screen Reader Only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --border: #000000;
    --shadow: 0 4px 12px rgba(0,0,0,0.3);
  }

  [data-theme='dark'] {
    --border: #FFFFFF;
    --shadow: 0 4px 12px rgba(255,255,255,0.3);
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ========================================
   PRINT STYLES
======================================== */

@media print {
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }

  .header,
  .theme-toggle,
  .btn {
    display: none !important;
  }

  .page {
    margin: 0;
    padding: 0;
  }

  .container {
    max-width: none;
    padding: 0;
  }
}

/* ========================================
   UTILITY CLASSES
======================================== */

/* Text Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.text-gold { color: var(--gold); }
.text-blue { color: var(--blue); }
.text-success { color: var(--success); }
.text-warning { color: var(--warning); }
.text-error { color: var(--error); }
.text-muted { color: var(--text-muted); }

/* Background Utilities */
.bg-surface { background-color: var(--surface); }
.bg-gold { background-color: var(--gold); }
.bg-blue { background-color: var(--blue); }
.bg-success { background-color: var(--success); }
.bg-warning { background-color: var(--warning); }
.bg-error { background-color: var(--error); }

/* Spacing Utilities */
.m-0 { margin: 0; }
.mt-0 { margin-top: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }
.mr-0 { margin-right: 0; }

.p-0 { padding: 0; }
.pt-0 { padding-top: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }
.pr-0 { padding-right: 0; }

/* Border Utilities */
.border { border: 1px solid var(--border); }
.border-gold { border-color: var(--gold); }
.border-blue { border-color: var(--blue); }
.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-full { border-radius: var(--radius-full); }

/* Shadow Utilities */
.shadow { box-shadow: var(--shadow); }
.shadow-glow { box-shadow: var(--glow); }

/* Display Utilities */
.hidden { display: none; }
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }

/* Position Utilities */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* Width Utilities */
.w-full { width: 100%; }
.w-auto { width: auto; }
.max-w-full { max-width: 100%; }

/* Height Utilities */
.h-full { height: 100%; }
.h-auto { height: auto; }
.min-h-screen { min-height: 100vh; }

/* ========================================
   ANIMATION UTILITIES
======================================== */

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* ========================================
   LOADING STATES
======================================== */

.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 215, 0, 0.1),
    transparent
  );
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* ========================================
   COMPONENT STATES
======================================== */

.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.loading-state {
  opacity: 0.7;
  cursor: wait;
}

.error-state {
  border-color: var(--error);
  background-color: rgba(220, 53, 69, 0.1);
}

.success-state {
  border-color: var(--success);
  background-color: rgba(39, 174, 96, 0.1);
}

/* ========================================
   MODERN ENHANCEMENTS
======================================== */

/* Custom Scrollbar - Global */
* {
  /* Firefox scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: var(--gold) var(--surface);
}

::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: var(--surface);
  border-radius: var(--radius-md);
  border: 1px solid var(--border);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--border), rgba(255, 255, 255, 0.1));
  border-radius: var(--radius-md);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, var(--gold), #E6C200);
  border-color: var(--gold);
  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

::-webkit-scrollbar-thumb:active {
  background: var(--gold);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Selection */
::selection {
  background-color: var(--gold);
  color: var(--bg);
}

::-moz-selection {
  background-color: var(--gold);
  color: var(--bg);
}

/* ========================================
   LEGACY COMPATIBILITY OVERRIDES
   Ensures existing components work with new system
======================================== */

/* Override any conflicting Tailwind or existing styles */
.aureus-container-inner {
  padding: var(--space-lg);
}

/* Ensure theme variables work with existing components */
.theme-bg { background-color: var(--bg); }
.theme-surface { background-color: var(--surface); }
.theme-text { color: var(--text); }
.theme-text-muted { color: var(--text-muted); }
.theme-border { border-color: var(--border); }
.theme-shadow { box-shadow: var(--shadow); }

/* ========================================
   FOOTER COMPONENTS
======================================== */

.clean-footer {
  background-color: var(--surface) !important;
  border-top: 1px solid var(--border) !important;
  padding: var(--space-5xl) 0 var(--space-3xl) !important;
  color: var(--text) !important;
}

.footer-content {
  display: grid !important;
  grid-template-columns: 300px 1fr 200px !important;
  gap: var(--space-3xl) !important;
  margin-bottom: var(--space-3xl) !important;
  align-items: flex-start !important;
  max-width: 1200px !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

/* Force desktop layout on larger screens */
@media (min-width: 769px) {
  .footer-content {
    display: grid !important;
    grid-template-columns: 300px 1fr 200px !important;
    gap: var(--space-3xl) !important;
  }
}

.footer-info {
  grid-column: 1 !important;
  position: relative !important;
}

.footer-info h3 {
  color: var(--gold) !important;
  font-size: var(--font-size-xl) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: var(--space-sm) !important;
}

.footer-info p {
  color: var(--text-muted) !important;
  font-size: var(--font-size-sm) !important;
  line-height: var(--line-height-relaxed) !important;
}

.footer-contact {
  grid-column: 2 !important;
  position: relative !important;
}

.footer-links {
  display: flex !important;
  flex-direction: column !important;
  gap: var(--space-sm) !important;
  grid-column: 3 !important;
  position: relative !important;
}

.footer-links button {
  background: none !important;
  border: none !important;
  color: var(--text-muted) !important;
  font-size: var(--font-size-sm) !important;
  cursor: pointer !important;
  text-align: left !important;
  padding: var(--space-xs) 0 !important;
  transition: color 0.2s ease !important;
}

.footer-links button:hover {
  color: var(--gold) !important;
}

.footer-bottom {
  border-top: 1px solid var(--border) !important;
  padding-top: var(--space-lg) !important;
  text-align: center !important;
}

.footer-bottom p {
  color: var(--text-muted) !important;
  font-size: var(--font-size-xs) !important;
  margin: 0 !important;
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr !important;
    gap: var(--space-xl) !important;
    text-align: center !important;
  }

  .footer-links {
    align-items: center !important;
    flex-direction: row !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    gap: var(--space-md) !important;
  }

  .footer-links button {
    text-align: center !important;
  }
}

/* ========================================
   AFFILIATE PAGE COMPONENTS - PREMIUM DESIGN
======================================== */

/* Benefits Section - Premium Styling */
.benefits {
  padding: var(--space-4xl) 0;
  background: var(--background);
  position: relative;
}

.benefits::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 215, 0, 0.02) 0%,
    transparent 50%,
    rgba(0, 191, 255, 0.02) 100%);
  pointer-events: none;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: var(--space-2xl);
  margin-top: var(--space-2xl);
  position: relative;
  z-index: 1;
}

.benefit-card {
  background: var(--card-bg);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  padding: var(--space-2xl);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.benefit-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--gold), var(--blue));
  transform: scaleX(0);
  transition: transform 0.4s ease;
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.benefit-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.03) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

.benefit-card:hover::before {
  transform: scaleX(1);
}

.benefit-card:hover::after {
  opacity: 1;
}

.benefit-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(255, 215, 0, 0.15);
  border-color: rgba(255, 215, 0, 0.3);
}

.benefit-card .card-icon {
  font-size: 3.5rem;
  margin-bottom: var(--space-lg);
  display: block;
  filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.3));
}

.benefit-card h3 {
  color: var(--text);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-md);
  background: linear-gradient(135deg, var(--text), var(--gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.benefit-card p {
  color: var(--text-muted);
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-lg);
}

.benefit-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.benefit-card ul li {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  padding: var(--space-xs) 0;
  position: relative;
  padding-left: var(--space-lg);
  transition: color 0.3s ease;
}

.benefit-card ul li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--gold);
  font-weight: var(--font-weight-bold);
  text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
}

.benefit-card:hover ul li {
  color: var(--text);
}

/* How It Works Section - Premium Styling */
.how-it-works {
  padding: var(--space-4xl) 0;
  background: var(--surface);
  position: relative;
  overflow: hidden;
}

.how-it-works::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(255, 215, 0, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(0, 191, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-2xl);
  margin-top: var(--space-2xl);
  position: relative;
  z-index: 1;
}

.step-card {
  background: var(--card-bg);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  padding: var(--space-2xl);
  text-align: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.step-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 215, 0, 0.02) 0%,
    transparent 50%,
    rgba(0, 191, 255, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

.step-card:hover::before {
  opacity: 1;
}

.step-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px rgba(255, 215, 0, 0.2);
  border-color: rgba(255, 215, 0, 0.4);
}

.step-number {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--gold), var(--blue));
  color: var(--background);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  margin: 0 auto var(--space-lg) auto;
  box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
  transition: all 0.4s ease;
  position: relative;
}

.step-number::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, var(--gold), var(--blue));
  border-radius: 50%;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.step-card:hover .step-number {
  transform: scale(1.1);
  box-shadow: 0 15px 40px rgba(255, 215, 0, 0.4);
}

.step-card:hover .step-number::before {
  opacity: 0.3;
}

.step-card h3 {
  color: var(--text);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-md);
  background: linear-gradient(135deg, var(--text), var(--gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.step-card p {
  color: var(--text-muted);
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  transition: color 0.3s ease;
}

.step-card:hover p {
  color: var(--text);
}

/* Gold Diggers Section - Premium Styling */
.gold-diggers-section {
  padding: var(--space-4xl) 0;
  background: var(--background);
  position: relative;
  overflow: hidden;
}

.gold-diggers-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(135deg, rgba(255, 215, 0, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(0, 191, 255, 0.02) 0%, transparent 70%);
  pointer-events: none;
}

.gold-diggers-section .container {
  position: relative;
  z-index: 1;
}

/* Gold Diggers Club Component Integration */
.gold-diggers-section .gold-diggers-club {
  background: transparent;
  border: none;
  padding: 0;
}

.gold-diggers-section .competition-hero {
  background: var(--card-bg);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  padding: var(--space-3xl);
  margin-bottom: var(--space-2xl);
  backdrop-filter: blur(10px);
  box-shadow: 0 20px 40px rgba(255, 215, 0, 0.1);
  transition: all 0.4s ease;
}

.gold-diggers-section .competition-hero:hover {
  transform: translateY(-4px);
  box-shadow: 0 30px 60px rgba(255, 215, 0, 0.15);
  border-color: rgba(255, 215, 0, 0.3);
}

.gold-diggers-section .hero-title {
  background: linear-gradient(135deg, var(--text), var(--gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: none;
}

.gold-diggers-section .gold-text {
  background: linear-gradient(135deg, var(--gold), var(--gold-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gold-diggers-section .competition-card,
.gold-diggers-section .leaderboard-card,
.gold-diggers-section .phase-selector {
  background: var(--card-bg);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.gold-diggers-section .competition-card:hover,
.gold-diggers-section .leaderboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 30px rgba(255, 215, 0, 0.1);
  border-color: rgba(255, 215, 0, 0.2);
}

/* ========================================
   PREMIUM LEADERBOARD STYLING
======================================== */

/* Leaderboard Container */
.leaderboard-section {
  background: var(--card-bg);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  padding: var(--space-2xl);
  margin: var(--space-2xl) 0;
  backdrop-filter: blur(10px);
  box-shadow: 0 20px 40px rgba(255, 215, 0, 0.1);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.leaderboard-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--gold), var(--blue));
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.leaderboard-section:hover {
  transform: translateY(-4px);
  box-shadow: 0 30px 60px rgba(255, 215, 0, 0.15);
  border-color: rgba(255, 215, 0, 0.3);
}

/* Leaderboard Header */
.leaderboard-header {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  margin-bottom: var(--space-xl);
  padding-bottom: var(--space-lg);
  border-bottom: 1px solid var(--border);
}

.leaderboard-title {
  color: var(--text);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(135deg, var(--text), var(--gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.leaderboard-subtitle {
  color: var(--text-muted);
  font-size: var(--font-size-base);
  margin: var(--space-sm) 0 0 0;
}

/* Leaderboard Table */
.leaderboard-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: transparent;
}

.leaderboard-table .leaderboard-header {
  display: grid;
  grid-template-columns: 80px 1fr 120px 140px 160px;
  gap: var(--space-md);
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(0, 191, 255, 0.1));
  border: 1px solid rgba(255, 215, 0, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  margin-bottom: var(--space-md);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-sm);
  color: var(--text);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.leaderboard-row {
  display: grid;
  grid-template-columns: 80px 1fr 120px 140px 160px;
  gap: var(--space-md);
  align-items: center;
  padding: var(--space-lg);
  margin-bottom: var(--space-sm);
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.leaderboard-row::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: transparent;
  transition: background 0.3s ease;
}

.leaderboard-row:hover {
  transform: translateX(4px);
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.15);
  border-color: rgba(255, 215, 0, 0.3);
}

.leaderboard-row:hover::before {
  background: linear-gradient(180deg, var(--gold), var(--blue));
}

/* Top 3 Special Styling */
.leaderboard-row.top-three {
  background: linear-gradient(135deg,
    rgba(255, 215, 0, 0.05) 0%,
    rgba(255, 215, 0, 0.02) 100%);
  border-color: rgba(255, 215, 0, 0.3);
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.1);
}

.leaderboard-row.top-three::before {
  background: linear-gradient(180deg, var(--gold), var(--gold-light));
}

.leaderboard-row.top-three:hover {
  background: linear-gradient(135deg,
    rgba(255, 215, 0, 0.08) 0%,
    rgba(255, 215, 0, 0.04) 100%);
  box-shadow: 0 12px 30px rgba(255, 215, 0, 0.2);
}

/* Placeholder Row Styling */
.leaderboard-row.placeholder-row {
  background: linear-gradient(135deg,
    rgba(0, 191, 255, 0.05) 0%,
    rgba(0, 191, 255, 0.02) 100%);
  border-color: rgba(0, 191, 255, 0.2);
  border-style: dashed;
}

.leaderboard-row.placeholder-row::before {
  background: linear-gradient(180deg, var(--blue), var(--blue-light));
}

/* Leaderboard Cell Styling */
.leaderboard-row .rank {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
}

.leaderboard-row .username {
  color: var(--text);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-base);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.leaderboard-row .referrals,
.leaderboard-row .volume {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-align: center;
  font-family: 'Courier New', monospace;
}

.leaderboard-row .prize {
  color: var(--gold);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  text-align: center;
  background: linear-gradient(135deg, var(--gold), var(--gold-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Top 3 Enhanced Styling */
.leaderboard-row.top-three .rank {
  font-size: var(--font-size-2xl);
  filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.5));
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from { filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.5)); }
  to { filter: drop-shadow(0 0 12px rgba(255, 215, 0, 0.8)); }
}

.leaderboard-row.top-three .username {
  color: var(--gold);
  font-weight: var(--font-weight-bold);
}

.leaderboard-row.top-three .referrals,
.leaderboard-row.top-three .volume {
  color: var(--text);
  font-weight: var(--font-weight-semibold);
}

.leaderboard-row.top-three .prize {
  font-size: var(--font-size-base);
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

/* Placeholder Row Special Styling */
.leaderboard-row.placeholder-row .rank {
  color: var(--blue);
  font-size: var(--font-size-2xl);
  animation: pulse 2s infinite;
}

.leaderboard-row.placeholder-row .username {
  color: var(--blue);
  font-weight: var(--font-weight-bold);
  font-style: italic;
}

.leaderboard-row.placeholder-row .prize {
  color: var(--blue);
  background: linear-gradient(135deg, var(--blue), var(--blue-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Empty State Styling */
.leaderboard-empty {
  text-align: center;
  padding: var(--space-3xl);
  background: linear-gradient(135deg,
    rgba(255, 215, 0, 0.05) 0%,
    rgba(0, 191, 255, 0.05) 100%);
  border: 2px dashed rgba(255, 215, 0, 0.3);
  border-radius: var(--radius-xl);
  margin: var(--space-xl) 0;
}

.leaderboard-empty p {
  color: var(--text-muted);
  font-size: var(--font-size-lg);
  margin: var(--space-md) 0;
}

.leaderboard-empty p:first-child {
  color: var(--gold);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-xl);
}

/* Simple Footer Styling */
.simple-footer {
  margin-top: var(--space-2xl);
  padding-top: var(--space-lg);
  border-top: 1px solid var(--border);
  text-align: center;
}

.simple-footer p {
  color: var(--text-muted);
  font-size: var(--font-size-xs);
  font-style: italic;
  margin: 0;
}

/* Responsive Leaderboard */
@media (max-width: 768px) {
  .leaderboard-table .leaderboard-header,
  .leaderboard-row {
    grid-template-columns: 60px 1fr 80px 100px 120px;
    gap: var(--space-sm);
    padding: var(--space-md);
    font-size: var(--font-size-xs);
  }

  .leaderboard-row .rank {
    font-size: var(--font-size-lg);
  }

  .leaderboard-row.top-three .rank {
    font-size: var(--font-size-xl);
  }
}

@media (max-width: 480px) {
  .leaderboard-table .leaderboard-header,
  .leaderboard-row {
    grid-template-columns: 50px 1fr 70px 90px;
    gap: var(--space-xs);
  }

  .leaderboard-table .leaderboard-header span:nth-child(3),
  .leaderboard-row .referrals {
    display: none;
  }
}

/* ========================================
   PREMIUM PHASE METRICS STYLING
======================================== */

/* Phase Explorer Container */
.phase-explorer {
  background: var(--card-bg);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  padding: var(--space-2xl);
  margin: var(--space-2xl) 0;
  backdrop-filter: blur(10px);
  box-shadow: 0 20px 40px rgba(255, 215, 0, 0.1);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.phase-explorer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--blue), var(--gold));
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.phase-explorer:hover {
  transform: translateY(-4px);
  box-shadow: 0 30px 60px rgba(0, 191, 255, 0.15);
  border-color: rgba(0, 191, 255, 0.3);
}

/* Phase Explorer Header */
.phase-explorer-header {
  margin-bottom: var(--space-2xl);
  text-align: center;
}

.phase-explorer-header h3 {
  color: var(--text);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(135deg, var(--text), var(--blue));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 var(--space-sm) 0;
}

.phase-explorer-header p {
  color: var(--text-muted);
  font-size: var(--font-size-base);
  margin: 0;
}

/* Phase Selector Container */
.phase-selector-container {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  margin-bottom: var(--space-2xl);
  padding: var(--space-lg);
  background: linear-gradient(135deg, rgba(0, 191, 255, 0.05), rgba(255, 215, 0, 0.05));
  border: 1px solid rgba(0, 191, 255, 0.2);
  border-radius: var(--radius-lg);
}

.phase-label {
  color: var(--text);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-base);
  white-space: nowrap;
}

.phase-select {
  flex: 1;
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  padding: var(--space-md) var(--space-lg);
  color: var(--text);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  transition: all 0.3s ease;
  cursor: pointer;
}

.phase-select:hover {
  border-color: var(--blue);
  box-shadow: 0 0 0 3px rgba(0, 191, 255, 0.1);
}

.phase-select:focus {
  outline: none;
  border-color: var(--blue);
  box-shadow: 0 0 0 3px rgba(0, 191, 255, 0.2);
}

.phase-select option {
  background: var(--surface);
  color: var(--text);
  padding: var(--space-sm);
}

/* Phase Performance Section */
.phase-performance {
  margin-top: var(--space-xl);
}

.phase-performance-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-xl);
  padding-bottom: var(--space-lg);
  border-bottom: 1px solid var(--border);
}

.phase-performance-title {
  color: var(--text);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin: 0;
}

.phase-status-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  background: linear-gradient(135deg, rgba(255, 69, 0, 0.1), rgba(255, 69, 0, 0.05));
  border: 1px solid rgba(255, 69, 0, 0.3);
  border-radius: var(--radius-full);
  color: #ff4500;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  animation: pulse 2s infinite;
}

.phase-status-indicator::before {
  content: '🔥';
  font-size: var(--font-size-base);
}

/* Business Metrics Grid */
.business-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-xl);
  margin: var(--space-xl) 0;
}

.metric-item {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.metric-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: transparent;
  transition: background 0.3s ease;
}

.metric-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 15px 30px rgba(255, 215, 0, 0.15);
  border-color: rgba(255, 215, 0, 0.3);
}

.metric-item:hover::before {
  background: linear-gradient(90deg, var(--gold), var(--blue));
}

.metric-item:nth-child(1):hover::before {
  background: linear-gradient(90deg, #4ade80, #22c55e); /* Green for shares */
}

.metric-item:nth-child(2):hover::before {
  background: linear-gradient(90deg, var(--gold), #fbbf24); /* Gold for commission */
}

.metric-item:nth-child(3):hover::before {
  background: linear-gradient(90deg, var(--blue), #3b82f6); /* Blue for remaining */
}

.metric-item:nth-child(4):hover::before {
  background: linear-gradient(90deg, #f59e0b, #d97706); /* Orange for your commission */
}

/* Metric Item Layout */
.metric-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-lg);
}

.metric-icon {
  font-size: var(--font-size-3xl);
  line-height: 1;
  flex-shrink: 0;
  filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.3));
  transition: all 0.3s ease;
}

.metric-item:hover .metric-icon {
  transform: scale(1.1);
  filter: drop-shadow(0 0 12px rgba(255, 215, 0, 0.5));
}

.metric-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.metric-label {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0;
}

.metric-value {
  color: var(--text);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  font-family: 'Courier New', monospace;
  line-height: 1.2;
  margin: 0;
  background: linear-gradient(135deg, var(--text), var(--gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.metric-subtitle {
  color: var(--text-muted);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  font-style: italic;
  margin: 0;
}

/* Special Styling for Different Metric Types */
.metric-item:nth-child(1) .metric-value {
  background: linear-gradient(135deg, #4ade80, #22c55e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.metric-item:nth-child(2) .metric-value {
  background: linear-gradient(135deg, var(--gold), #fbbf24);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.metric-item:nth-child(3) .metric-value {
  background: linear-gradient(135deg, var(--blue), #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.metric-item:nth-child(4) .metric-value {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Responsive Metrics Grid */
@media (max-width: 768px) {
  .business-metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-lg);
  }

  .metric-item {
    padding: var(--space-lg);
  }

  .metric-icon {
    font-size: var(--font-size-2xl);
  }

  .metric-value {
    font-size: var(--font-size-xl);
  }
}

@media (max-width: 480px) {
  .business-metrics-grid {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }

  .metric-item {
    padding: var(--space-md);
    flex-direction: column;
    text-align: center;
    gap: var(--space-md);
  }

  .metric-icon {
    align-self: center;
  }
}

/* ========================================
   PREMIUM PRIZE TIERS STYLING
======================================== */

/* Prize Tiers Container */
.prize-tiers {
  background: var(--card-bg);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  padding: var(--space-3xl);
  margin: var(--space-3xl) 0;
  backdrop-filter: blur(10px);
  box-shadow: 0 25px 50px rgba(255, 215, 0, 0.15);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.prize-tiers::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg,
    #FFD700 0%,
    #FFA500 25%,
    #C0C0C0 50%,
    #CD7F32 75%,
    #FFD700 100%);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.prize-tiers:hover {
  transform: translateY(-6px);
  box-shadow: 0 35px 70px rgba(255, 215, 0, 0.2);
  border-color: rgba(255, 215, 0, 0.4);
}

/* Prize Tiers Header */
.prize-tiers h3 {
  color: var(--text);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  text-align: center;
  margin: 0 0 var(--space-sm) 0;
  background: linear-gradient(135deg, var(--gold), #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.prize-tiers p {
  color: var(--text-muted);
  font-size: var(--font-size-lg);
  text-align: center;
  margin: 0 0 var(--space-3xl) 0;
  font-weight: var(--font-weight-medium);
}

/* Tiers Grid */
.tiers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-2xl);
  margin-top: var(--space-2xl);
}

/* Individual Tier Cards */
.tier-card {
  background: var(--surface);
  border: 2px solid var(--border);
  border-radius: var(--radius-xl);
  padding: var(--space-2xl);
  text-align: center;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.tier-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: transparent;
  transition: all 0.4s ease;
}

.tier-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(255, 215, 0, 0.2);
}

/* Champion (1st Place) */
.tier-card.champion {
  border-color: rgba(255, 215, 0, 0.5);
  background: linear-gradient(135deg,
    rgba(255, 215, 0, 0.1) 0%,
    rgba(255, 215, 0, 0.05) 100%);
}

.tier-card.champion::before {
  background: linear-gradient(90deg, #FFD700, #FFA500);
}

.tier-card.champion:hover {
  border-color: rgba(255, 215, 0, 0.8);
  box-shadow: 0 25px 50px rgba(255, 215, 0, 0.3);
}

/* Runner-up (2nd Place) */
.tier-card.runner-up {
  border-color: rgba(192, 192, 192, 0.5);
  background: linear-gradient(135deg,
    rgba(192, 192, 192, 0.1) 0%,
    rgba(192, 192, 192, 0.05) 100%);
}

.tier-card.runner-up::before {
  background: linear-gradient(90deg, #C0C0C0, #A8A8A8);
}

.tier-card.runner-up:hover {
  border-color: rgba(192, 192, 192, 0.8);
  box-shadow: 0 25px 50px rgba(192, 192, 192, 0.2);
}

/* Bronze (3rd Place) */
.tier-card.bronze {
  border-color: rgba(205, 127, 50, 0.5);
  background: linear-gradient(135deg,
    rgba(205, 127, 50, 0.1) 0%,
    rgba(205, 127, 50, 0.05) 100%);
}

.tier-card.bronze::before {
  background: linear-gradient(90deg, #CD7F32, #B8860B);
}

.tier-card.bronze:hover {
  border-color: rgba(205, 127, 50, 0.8);
  box-shadow: 0 25px 50px rgba(205, 127, 50, 0.2);
}

/* Elite (4th-10th Place) */
.tier-card.elite {
  border-color: rgba(0, 191, 255, 0.5);
  background: linear-gradient(135deg,
    rgba(0, 191, 255, 0.1) 0%,
    rgba(0, 191, 255, 0.05) 100%);
}

.tier-card.elite::before {
  background: linear-gradient(90deg, var(--blue), #0080FF);
}

.tier-card.elite:hover {
  border-color: rgba(0, 191, 255, 0.8);
  box-shadow: 0 25px 50px rgba(0, 191, 255, 0.2);
}

/* Tier Card Content Elements */
.tier-emoji {
  font-size: 4rem;
  line-height: 1;
  margin-bottom: var(--space-lg);
  filter: drop-shadow(0 0 15px rgba(255, 215, 0, 0.5));
  transition: all 0.4s ease;
}

.tier-card:hover .tier-emoji {
  transform: scale(1.2) rotate(5deg);
  filter: drop-shadow(0 0 25px rgba(255, 215, 0, 0.8));
}

.tier-name {
  color: var(--text);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: var(--space-md);
  transition: all 0.3s ease;
}

.tier-card.champion .tier-name {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tier-card.runner-up .tier-name {
  background: linear-gradient(135deg, #C0C0C0, #A8A8A8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tier-card.bronze .tier-name {
  background: linear-gradient(135deg, #CD7F32, #B8860B);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tier-card.elite .tier-name {
  background: linear-gradient(135deg, var(--blue), #0080FF);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tier-prize {
  color: var(--text);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  font-family: 'Courier New', monospace;
  margin-bottom: var(--space-sm);
  text-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
  transition: all 0.3s ease;
}

.tier-card:hover .tier-prize {
  transform: scale(1.05);
  text-shadow: 0 0 25px rgba(255, 215, 0, 0.5);
}

.tier-card.champion .tier-prize {
  color: #FFD700;
  text-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
}

.tier-card.runner-up .tier-prize {
  color: #C0C0C0;
  text-shadow: 0 0 20px rgba(192, 192, 192, 0.6);
}

.tier-card.bronze .tier-prize {
  color: #CD7F32;
  text-shadow: 0 0 20px rgba(205, 127, 50, 0.6);
}

.tier-card.elite .tier-prize {
  color: var(--blue);
  text-shadow: 0 0 20px rgba(0, 191, 255, 0.6);
}

.tier-rank {
  color: var(--text-muted);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--space-xs);
}

.tier-description {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  font-style: italic;
  opacity: 0.8;
}

/* Special Effects for Champion */
.tier-card.champion {
  animation: championGlow 4s ease-in-out infinite;
}

@keyframes championGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.2);
  }
  50% {
    box-shadow: 0 0 40px rgba(255, 215, 0, 0.4);
  }
}

/* Responsive Prize Tiers */
@media (max-width: 768px) {
  .tiers-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-xl);
  }

  .tier-card {
    padding: var(--space-xl);
  }

  .tier-emoji {
    font-size: 3rem;
  }

  .tier-prize {
    font-size: var(--font-size-2xl);
  }
}

@media (max-width: 480px) {
  .prize-tiers {
    padding: var(--space-2xl);
  }

  .tiers-grid {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }

  .tier-card {
    padding: var(--space-lg);
  }

  .tier-emoji {
    font-size: 2.5rem;
  }

  .tier-name {
    font-size: var(--font-size-lg);
  }

  .tier-prize {
    font-size: var(--font-size-xl);
  }
}

/* Professional Telegram Authentication Styles */
.professional-telegram-auth {
  max-width: 480px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: var(--space-2xl);
}

/* PIN Display Section */
.pin-display-section {
  text-align: center;
}

.pin-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  margin-bottom: var(--space-lg);
}

.pin-icon {
  font-size: 24px;
}

.pin-header h3 {
  color: var(--text);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin: 0;
}

.pin-display-card {
  background: linear-gradient(135deg, var(--surface-elevated) 0%, var(--surface) 100%);
  border: 2px solid var(--gold);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-lg);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.pin-number {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--gold);
  letter-spacing: 0.15em;
  text-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
  user-select: all;
  flex: 1;
  text-align: left;
}

.copy-btn {
  background: var(--gold);
  color: var(--bg);
  border: none;
  border-radius: var(--radius-md);
  padding: 12px 20px;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.copy-btn:hover {
  background: var(--gold-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
}

.copy-btn:active {
  transform: translateY(0);
}

/* Instructions Section */
.instructions-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.instruction-card {
  display: flex;
  align-items: flex-start;
  gap: var(--space-md);
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  transition: all 0.2s ease;
}

.instruction-card:hover {
  background: var(--surface-elevated);
  border-color: var(--gold);
  transform: translateY(-1px);
}

.instruction-icon {
  font-size: 24px;
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gold);
  border-radius: 50%;
  color: var(--bg);
}

.instruction-content h4 {
  color: var(--text);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  margin: 0 0 4px 0;
}

.instruction-content p {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  margin: 0;
  line-height: 1.5;
}

/* Status Section */
.status-section {
  text-align: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-md);
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

.status-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border);
  border-top: 2px solid var(--gold);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Action Section */
.action-section {
  text-align: center;
}

.btn-outline {
  background: transparent;
  color: var(--text-muted);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  padding: 12px 24px;
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-outline:hover:not(:disabled) {
  background: var(--surface);
  color: var(--text);
  border-color: var(--gold);
}

.btn-outline:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .professional-telegram-auth {
    max-width: 100%;
    padding: 0 var(--space-md);
  }

  .pin-display-card {
    flex-direction: column;
    text-align: center;
    gap: var(--space-md);
  }

  .pin-number {
    font-size: 2rem;
    text-align: center;
  }

  .instruction-card {
    padding: var(--space-md);
  }

  .instruction-icon {
    width: 32px;
    height: 32px;
    font-size: 18px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.polling-status p {
  margin: 0;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

/* Responsive Telegram Auth */
@media (max-width: 768px) {
  .pin-code {
    font-size: var(--font-size-2xl);
    padding: var(--space-md);
  }

  .auth-pin-display {
    padding: var(--space-lg);
  }

  .auth-instructions {
    padding: var(--space-md);
  }
}

/* Notice Section - Premium Styling */
.notice {
  padding: var(--space-4xl) 0;
  background: var(--surface);
  position: relative;
}

.notice::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(0, 191, 255, 0.05) 0%, transparent 70%);
  pointer-events: none;
}

.notice-card {
  background: var(--card-bg);
  border: 1px solid rgba(0, 191, 255, 0.2);
  border-radius: var(--radius-xl);
  padding: var(--space-3xl);
  text-align: center;
  max-width: 900px;
  margin: 0 auto;
  position: relative;
  backdrop-filter: blur(10px);
  box-shadow: 0 20px 40px rgba(0, 191, 255, 0.1);
  transition: all 0.4s ease;
}

.notice-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--blue), var(--gold));
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.notice-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 30px 60px rgba(0, 191, 255, 0.15);
  border-color: rgba(0, 191, 255, 0.3);
}

.notice-icon {
  font-size: 4rem;
  margin-bottom: var(--space-lg);
  display: block;
  filter: drop-shadow(0 0 15px rgba(0, 191, 255, 0.4));
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.notice-card h3 {
  color: var(--text);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-lg);
  background: linear-gradient(135deg, var(--text), var(--blue));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.notice-card p {
  color: var(--text-muted);
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  margin: 0;
  max-width: 700px;
  margin: 0 auto;
}

/* Premium Affiliate Page Enhancements */
.page.modern-website .hero {
  background: linear-gradient(135deg,
    var(--bg) 0%,
    rgba(255, 215, 0, 0.02) 50%,
    var(--bg) 100%);
  position: relative;
  overflow: hidden;
}

.page.modern-website .hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255, 215, 0, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(0, 191, 255, 0.06) 0%, transparent 50%);
  pointer-events: none;
}

.page.modern-website .hero-content {
  position: relative;
  z-index: 1;
}

.page.modern-website .hero-logo {
  filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.3));
  transition: all 0.4s ease;
}

/* Affiliate Landing Page Specific Styles */
.advisor-profile-image {
  width: 300px;
  height: 300px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid var(--gold);
  box-shadow: var(--glow);
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--gold) 0%, #E6C200 100%);
}

.profile-hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-hero-placeholder {
  font-size: 4rem;
  font-weight: bold;
  color: var(--bg);
  text-align: center;
}

.video-container {
  margin: var(--space-lg) 0;
  border-radius: var(--radius-lg);
  overflow: hidden;
  background: var(--surface);
}

.operations-video {
  width: 100%;
  height: auto;
  border-radius: var(--radius-lg);
}

.video-description {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  margin-top: var(--space-sm);
  text-align: center;
}

.calculator-container {
  background: var(--surface);
  border-radius: var(--radius-xl);
  padding: var(--space-3xl);
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
}

/* Professional Modal Styles */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  padding: var(--space-lg);
}

.modal-content {
  background: var(--surface);
  border-radius: var(--radius-xl);
  padding: var(--space-3xl);
  max-width: 500px;
  width: 100%;
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-2xl);
}

.modal-header h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 2rem;
  cursor: pointer;
  transition: color 0.2s ease;
}

.modal-close:hover {
  color: var(--text);
}

.advisor-connection-notice {
  display: flex;
  gap: var(--space-lg);
  padding: var(--space-lg);
  background: rgba(255, 215, 0, 0.1);
  border: 1px solid rgba(255, 215, 0, 0.2);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-2xl);
}

.notice-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.notice-content h4 {
  color: var(--gold);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-sm);
}

.notice-content p {
  color: var(--text);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
}

/* CTA Section */
.cta-section {
  padding: var(--space-6xl) 0;
  background: linear-gradient(135deg, var(--gold) 0%, #E6C200 100%);
  color: var(--bg);
  text-align: center;
}

.cta-section h2 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-lg);
  color: var(--bg);
}

.cta-section p {
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-3xl);
  color: rgba(0, 0, 0, 0.8);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-section .btn {
  background: var(--bg);
  color: var(--gold);
  border: 2px solid var(--bg);
}

.cta-section .btn:hover {
  background: transparent;
  color: var(--bg);
  border-color: var(--bg);
}

.page.modern-website .hero-logo:hover {
  transform: scale(1.05);
  filter: drop-shadow(0 0 30px rgba(255, 215, 0, 0.5));
}

.page.modern-website .btn-large {
  padding: var(--space-lg) var(--space-2xl);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  border-radius: var(--radius-xl);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.page.modern-website .btn-large::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.page.modern-website .btn-large:hover::before {
  left: 100%;
}

.page.modern-website .btn-primary.btn-large {
  background: linear-gradient(135deg, var(--gold), var(--gold-light));
  color: var(--background);
  box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
}

.page.modern-website .btn-primary.btn-large:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 40px rgba(255, 215, 0, 0.4);
}

.page.modern-website .btn-secondary.btn-large {
  background: transparent;
  border: 2px solid var(--gold);
  color: var(--gold);
}

.page.modern-website .btn-secondary.btn-large:hover {
  background: var(--gold);
  color: var(--background);
  transform: translateY(-2px);
  box-shadow: 0 15px 40px rgba(255, 215, 0, 0.3);
}

/* Responsive Design for Affiliate Page - Enhanced */
@media (max-width: 1024px) {
  .benefits-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-xl);
  }

  .steps-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-xl);
  }
}

@media (max-width: 768px) {
  .benefits-grid {
    grid-template-columns: 1fr;
    gap: var(--space-xl);
  }

  .steps-grid {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }

  .benefit-card,
  .step-card {
    padding: var(--space-xl);
  }

  .notice-card {
    padding: var(--space-xl);
  }

  .step-number {
    width: 70px;
    height: 70px;
    font-size: var(--font-size-xl);
  }

  .benefit-card .card-icon {
    font-size: 3rem;
  }

  .notice-icon {
    font-size: 3.5rem;
  }
}

@media (max-width: 480px) {
  .benefit-card,
  .step-card,
  .notice-card {
    padding: var(--space-lg);
  }

  .page.modern-website .btn-large {
    padding: var(--space-md) var(--space-lg);
    font-size: var(--font-size-base);
  }
}

/* ========================================
   COMING SOON COMPONENTS
======================================== */

.content-section {
  padding: var(--space-5xl) 0 !important;
}

.coming-soon-card {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-5xl) !important;
  text-align: center !important;
  box-shadow: var(--shadow) !important;
  max-width: 600px !important;
  margin: 0 auto !important;
}

.coming-soon-icon {
  font-size: var(--font-size-5xl) !important;
  margin-bottom: var(--space-xl) !important;
}

.coming-soon-card h2 {
  color: var(--text) !important;
  font-size: var(--font-size-3xl) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: var(--space-lg) !important;
}

.coming-soon-card p {
  color: var(--text-muted) !important;
  font-size: var(--font-size-lg) !important;
  line-height: var(--line-height-relaxed) !important;
  margin-bottom: var(--space-4xl) !important;
}

/* ========================================
   HERO SECTIONS & METRICS
======================================== */

.hero {
  background: var(--bg) !important;
  padding: var(--space-6xl) 0 var(--space-5xl) !important;
  text-align: center !important;
  position: relative !important;
}

.hero-content {
  max-width: 1200px !important;
  margin: 0 auto !important;
}

.hero-title {
  font-size: var(--font-size-4xl) !important;
  font-weight: var(--font-weight-bold) !important;
  color: var(--text) !important;
  margin-bottom: var(--space-lg) !important;
  line-height: var(--line-height-tight) !important;
}

.hero-subtitle {
  font-size: var(--font-size-lg) !important;
  color: var(--text-muted) !important;
  line-height: var(--line-height-relaxed) !important;
  margin-bottom: var(--space-4xl) !important;
  max-width: 700px !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

.metrics-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)) !important;
  gap: var(--space-lg) !important;
  margin: var(--space-4xl) 0 !important;
  max-width: 700px !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

.metric-card {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-lg) !important;
  text-align: center !important;
  box-shadow: var(--shadow) !important;
  transition: all 0.2s ease !important;
}

.metric-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.1) !important;
}

.metric-value {
  font-size: var(--font-size-2xl) !important;
  font-weight: var(--font-weight-bold) !important;
  color: var(--gold) !important;
  margin-bottom: var(--space-xs) !important;
}

.metric-label {
  font-size: var(--font-size-sm) !important;
  color: var(--text-muted) !important;
  font-weight: var(--font-weight-medium) !important;
}

/* CTA Buttons */
.cta-buttons {
  display: flex !important;
  gap: var(--space-lg) !important;
  justify-content: center !important;
  flex-wrap: wrap !important;
  margin-top: var(--space-4xl) !important;
}

/* ========================================
   SECTION LAYOUTS
======================================== */

.overview,
.trust-section,
.gallery-section,
.quick-links {
  padding: var(--space-5xl) 0 !important;
  background: var(--bg) !important;
}

.section-header {
  text-align: center !important;
  margin-bottom: var(--space-4xl) !important;
}

.section-header h2 {
  font-size: var(--font-size-3xl) !important;
  font-weight: var(--font-weight-bold) !important;
  color: var(--text) !important;
  margin-bottom: var(--space-lg) !important;
}

.section-header p {
  font-size: var(--font-size-lg) !important;
  color: var(--text-muted) !important;
  max-width: 600px !important;
  margin: 0 auto !important;
}

/* ========================================
   NAVIGATION TABS
======================================== */

.nav-tabs {
  display: flex !important;
  justify-content: center !important;
  gap: var(--space-sm) !important;
  margin-bottom: var(--space-4xl) !important;
  flex-wrap: wrap !important;
  border-bottom: 1px solid var(--border) !important;
  padding-bottom: var(--space-lg) !important;
}

.nav-tab {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-md) !important;
  padding: var(--space-md) var(--space-lg) !important;
  color: var(--text-muted) !important;
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-medium) !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  text-decoration: none !important;
}

.nav-tab:hover,
.nav-tab.active {
  background: var(--gold) !important;
  color: var(--cta-text) !important;
  border-color: var(--gold) !important;
  transform: translateY(-1px) !important;
}

/* ========================================
   DATA TABLES
======================================== */

.data-table {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  overflow: hidden !important;
  box-shadow: var(--shadow) !important;
  margin: var(--space-xl) 0 !important;
}

.data-table table {
  width: 100% !important;
  border-collapse: collapse !important;
}

.data-table th {
  background: var(--gold) !important;
  color: var(--cta-text) !important;
  padding: var(--space-lg) !important;
  font-weight: var(--font-weight-bold) !important;
  font-size: var(--font-size-sm) !important;
  text-align: left !important;
  border-bottom: 1px solid var(--border) !important;
}

.data-table td {
  padding: var(--space-lg) !important;
  color: var(--text) !important;
  font-size: var(--font-size-sm) !important;
  border-bottom: 1px solid var(--border) !important;
}

.data-table tr:last-child td {
  border-bottom: none !important;
}

.data-table tr:hover {
  background: rgba(255, 215, 0, 0.05) !important;
}

/* ========================================
   RISK FACTORS & DISCLAIMERS
======================================== */

.risk-section {
  padding: var(--space-5xl) 0 !important;
  background: var(--bg) !important;
}

.risk-factors {
  display: grid !important;
  gap: var(--space-lg) !important;
  margin-top: var(--space-4xl) !important;
}

.risk-item {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  box-shadow: var(--shadow) !important;
  display: flex !important;
  align-items: flex-start !important;
  gap: var(--space-lg) !important;
}

.risk-icon {
  font-size: var(--font-size-2xl) !important;
  flex-shrink: 0 !important;
  margin-top: var(--space-xs) !important;
}

.risk-icon.warning { color: var(--warning) !important; }
.risk-icon.danger { color: var(--error) !important; }
.risk-icon.info { color: var(--info) !important; }

.risk-content h4 {
  color: var(--text) !important;
  font-size: var(--font-size-lg) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: var(--space-sm) !important;
}

.risk-content p {
  color: var(--text-muted) !important;
  font-size: var(--font-size-sm) !important;
  line-height: var(--line-height-relaxed) !important;
  margin: 0 !important;
}

/* ========================================
   PHASE BREAKDOWN SECTIONS
======================================== */

.phase-section {
  padding: var(--space-5xl) 0 !important;
  background: var(--bg) !important;
}

.phase-groups {
  display: grid !important;
  gap: var(--space-4xl) !important;
  margin-top: var(--space-4xl) !important;
}

.phase-group {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-4xl) !important;
  box-shadow: var(--shadow) !important;
}

.phase-group h3 {
  color: var(--gold) !important;
  font-size: var(--font-size-2xl) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: var(--space-lg) !important;
  text-align: center !important;
}

.phase-list {
  display: grid !important;
  gap: var(--space-lg) !important;
}

.phase-item {
  background: rgba(255, 215, 0, 0.05) !important;
  border: 1px solid rgba(255, 215, 0, 0.2) !important;
  border-radius: var(--radius-md) !important;
  padding: var(--space-lg) !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  flex-wrap: wrap !important;
  gap: var(--space-md) !important;
}

.phase-info {
  flex: 1 !important;
}

.phase-name {
  color: var(--text) !important;
  font-size: var(--font-size-lg) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: var(--space-xs) !important;
}

.phase-details {
  color: var(--text-muted) !important;
  font-size: var(--font-size-sm) !important;
}

.phase-pricing {
  text-align: right !important;
}

.phase-price {
  color: var(--gold) !important;
  font-size: var(--font-size-xl) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: var(--space-xs) !important;
}

.phase-funding {
  color: var(--text-muted) !important;
  font-size: var(--font-size-sm) !important;
}

/* ========================================
   INVESTMENT STRATEGY SECTIONS
======================================== */

.strategy-section {
  padding: var(--space-5xl) 0 !important;
  background: var(--bg) !important;
}

.strategy-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
  gap: var(--space-xl) !important;
  margin-top: var(--space-4xl) !important;
}

.strategy-card {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  box-shadow: var(--shadow) !important;
  text-align: center !important;
  transition: all 0.2s ease !important;
}

.strategy-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.1) !important;
}

.strategy-icon {
  font-size: var(--font-size-3xl) !important;
  margin-bottom: var(--space-lg) !important;
  display: block !important;
}

.strategy-card h4 {
  color: var(--text) !important;
  font-size: var(--font-size-xl) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: var(--space-lg) !important;
}

.strategy-card p {
  color: var(--text-muted) !important;
  font-size: var(--font-size-sm) !important;
  line-height: var(--line-height-relaxed) !important;
}

/* ========================================
   MINE PRODUCTION & OPERATIONS
======================================== */

.operations-section {
  padding: var(--space-5xl) 0 !important;
  background: var(--bg) !important;
}

.operations-hero {
  text-align: center !important;
  margin-bottom: var(--space-5xl) !important;
}

.operations-hero h1 {
  color: var(--text) !important;
  font-size: var(--font-size-4xl) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: var(--space-lg) !important;
}

.operations-hero p {
  color: var(--text-muted) !important;
  font-size: var(--font-size-lg) !important;
  max-width: 700px !important;
  margin: 0 auto !important;
}

.operations-grid {
  display: grid !important;
  gap: var(--space-4xl) !important;
}

.operation-block {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-4xl) !important;
  box-shadow: var(--shadow) !important;
}

.operation-block h2 {
  color: var(--gold) !important;
  font-size: var(--font-size-2xl) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: var(--space-lg) !important;
  text-align: center !important;
}

.operation-block h3 {
  color: var(--text) !important;
  font-size: var(--font-size-xl) !important;
  font-weight: var(--font-weight-bold) !important;
  margin: var(--space-xl) 0 var(--space-lg) !important;
}

.operation-block p {
  color: var(--text-muted) !important;
  font-size: var(--font-size-base) !important;
  line-height: var(--line-height-relaxed) !important;
  margin-bottom: var(--space-lg) !important;
}

.operation-block ul {
  list-style: none !important;
  padding: 0 !important;
  margin: var(--space-lg) 0 !important;
}

.operation-block li {
  color: var(--text) !important;
  font-size: var(--font-size-sm) !important;
  padding: var(--space-sm) 0 !important;
  border-bottom: 1px solid var(--border) !important;
  display: flex !important;
  align-items: center !important;
  gap: var(--space-sm) !important;
}

.operation-block li:last-child {
  border-bottom: none !important;
}

.operation-block li::before {
  content: "✓" !important;
  color: var(--emerald) !important;
  font-weight: var(--font-weight-bold) !important;
  font-size: var(--font-size-sm) !important;
}

/* ========================================
   BREADCRUMBS & PAGE HEADERS
======================================== */

.page-header {
  padding: var(--space-4xl) 0 !important;
  background: var(--bg) !important;
  border-bottom: 1px solid var(--border) !important;
}

.breadcrumb {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-sm) !important;
  margin-bottom: var(--space-lg) !important;
  font-size: var(--font-size-sm) !important;
}

.breadcrumb-link {
  color: var(--text-muted) !important;
  text-decoration: none !important;
  background: none !important;
  border: none !important;
  cursor: pointer !important;
  font-size: var(--font-size-sm) !important;
  padding: 0 !important;
}

.breadcrumb-link:hover {
  color: var(--gold) !important;
}

.breadcrumb-separator {
  color: var(--text-muted) !important;
}

.breadcrumb-current {
  color: var(--text) !important;
  font-weight: var(--font-weight-medium) !important;
}

.page-title {
  color: var(--text) !important;
  font-size: var(--font-size-4xl) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: var(--space-lg) !important;
  text-align: center !important;
}

.page-subtitle {
  color: var(--text-muted) !important;
  font-size: var(--font-size-lg) !important;
  text-align: center !important;
  max-width: 700px !important;
  margin: 0 auto !important;
}

/* ========================================
   MOBILE DASHBOARD SWITCHER FIXES
======================================== */

/* Critical mobile responsiveness fixes for dashboard switcher */
@media (max-width: 768px) {
  /* Ensure dashboard switcher dropdown doesn't cause horizontal overflow */
  .dashboard-switcher-dropdown {
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: calc(100vw - 2rem) !important;
    max-width: 320px !important;
  }

  /* Mobile-optimized touch targets */
  .dashboard-switcher-button {
    min-height: 44px !important;
    min-width: 44px !important;
    touch-action: manipulation !important;
    -webkit-tap-highlight-color: transparent !important;
  }

  /* Prevent horizontal scrolling on mobile */
  body, html {
    overflow-x: hidden !important;
    max-width: 100vw !important;
  }

  /* Mobile container constraints */
  .mobile-container {
    max-width: 100vw !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
  }

  /* Mobile dashboard layout fixes */
  .mobile-dashboard-content {
    width: 100% !important;
    max-width: 100vw !important;
    overflow-x: hidden !important;
    padding: 1rem !important;
    box-sizing: border-box !important;
  }

  /* Mobile responsive adjustments for dashboard components */
  .mobile-dashboard-content > * {
    max-width: 100% !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
  }

  /* Fix common mobile overflow issues */
  .mobile-dashboard-content table {
    width: 100% !important;
    table-layout: fixed !important;
    overflow-x: auto !important;
    display: block !important;
    white-space: nowrap !important;
  }

  .mobile-dashboard-content .grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  .mobile-dashboard-content .flex {
    flex-wrap: wrap !important;
    gap: 0.5rem !important;
  }

  /* Mobile card adjustments */
  .mobile-dashboard-content .card,
  .mobile-dashboard-content .bg-gray-800,
  .mobile-dashboard-content .bg-gray-700 {
    margin-left: 0 !important;
    margin-right: 0 !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
  }

  /* Mobile text adjustments */
  .mobile-dashboard-content h1,
  .mobile-dashboard-content h2,
  .mobile-dashboard-content h3 {
    font-size: clamp(1.25rem, 4vw, 2rem) !important;
    line-height: 1.2 !important;
    word-wrap: break-word !important;
  }

  /* Mobile button adjustments */
  .mobile-dashboard-content button {
    min-height: 44px !important;
    padding: 0.75rem 1rem !important;
    font-size: 1rem !important;
    touch-action: manipulation !important;
  }

  /* Mobile form adjustments */
  .mobile-dashboard-content input,
  .mobile-dashboard-content select,
  .mobile-dashboard-content textarea {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    font-size: 16px !important; /* Prevents zoom on iOS */
  }

  /* Mobile modal adjustments */
  .mobile-dashboard-content .modal,
  .mobile-dashboard-content .fixed {
    max-width: calc(100vw - 2rem) !important;
    margin: 1rem !important;
    box-sizing: border-box !important;
  }

  /* Mobile image adjustments */
  .mobile-dashboard-content img {
    max-width: 100% !important;
    height: auto !important;
    object-fit: contain !important;
  }

  /* Mobile video adjustments */
  .mobile-dashboard-content video {
    max-width: 100% !important;
    height: auto !important;
  }

  /* Mobile pre/code adjustments */
  .mobile-dashboard-content pre,
  .mobile-dashboard-content code {
    overflow-x: auto !important;
    max-width: 100% !important;
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
  }

  /* Mobile purchase flow specific fixes */
  .share-purchase-modal,
  .purchase-flow-container {
    max-width: calc(100vw - 2rem) !important;
    margin: 1rem !important;
    padding: 1rem !important;
    box-sizing: border-box !important;
  }

  .purchase-amount-input {
    font-size: 16px !important; /* Prevents zoom on iOS */
    max-width: 100% !important;
    box-sizing: border-box !important;
  }

  .quick-amount-buttons {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr)) !important;
    gap: 0.5rem !important;
    width: 100% !important;
  }

  .payment-method-buttons {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
    width: 100% !important;
  }

  /* Terms Modal Scrollbar Styles */
  .terms-modal-content {
    scrollbar-width: auto !important;
    scrollbar-color: #fbbf24 #374151 !important;
  }

  .terms-modal-content::-webkit-scrollbar {
    width: 12px !important;
    height: 12px !important;
  }

  .terms-modal-content::-webkit-scrollbar-track {
    background: #374151 !important;
    border-radius: 6px !important;
  }

  .terms-modal-content::-webkit-scrollbar-thumb {
    background: #fbbf24 !important;
    border-radius: 6px !important;
    border: 2px solid #374151 !important;
  }

  .terms-modal-content::-webkit-scrollbar-thumb:hover {
    background: #f59e0b !important;
  }

  .terms-modal-content::-webkit-scrollbar-corner {
    background: #374151 !important;
  }

  .crypto-network-buttons {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 0.75rem !important;
    width: 100% !important;
  }

  /* Mobile typography improvements */
  .mobile-dashboard-content .text-4xl,
  .mobile-dashboard-content .text-5xl {
    font-size: clamp(1.5rem, 6vw, 2.5rem) !important;
    line-height: 1.1 !important;
  }

  .mobile-dashboard-content .text-3xl {
    font-size: clamp(1.25rem, 5vw, 1.875rem) !important;
    line-height: 1.2 !important;
  }

  .mobile-dashboard-content .text-2xl {
    font-size: clamp(1.125rem, 4vw, 1.5rem) !important;
    line-height: 1.3 !important;
  }

  .mobile-dashboard-content .text-xl {
    font-size: clamp(1rem, 3.5vw, 1.25rem) !important;
    line-height: 1.4 !important;
  }

  /* Mobile spacing adjustments */
  .mobile-dashboard-content .p-8,
  .mobile-dashboard-content .p-6 {
    padding: 1rem !important;
  }

  .mobile-dashboard-content .px-8,
  .mobile-dashboard-content .px-6 {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  .mobile-dashboard-content .py-8,
  .mobile-dashboard-content .py-6 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }

  .mobile-dashboard-content .m-8,
  .mobile-dashboard-content .m-6 {
    margin: 1rem !important;
  }

  .mobile-dashboard-content .mx-8,
  .mobile-dashboard-content .mx-6 {
    margin-left: 1rem !important;
    margin-right: 1rem !important;
  }

  .mobile-dashboard-content .my-8,
  .mobile-dashboard-content .my-6 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }

  /* Mobile navigation improvements */
  .mobile-dashboard-content .lg\\:hidden {
    display: block !important;
  }

  .mobile-dashboard-content .hidden.lg\\:block {
    display: none !important;
  }

  /* Mobile grid improvements */
  .mobile-dashboard-content .grid-cols-2,
  .mobile-dashboard-content .grid-cols-3,
  .mobile-dashboard-content .grid-cols-4,
  .mobile-dashboard-content .md\\:grid-cols-2,
  .mobile-dashboard-content .md\\:grid-cols-3,
  .mobile-dashboard-content .lg\\:grid-cols-2,
  .mobile-dashboard-content .lg\\:grid-cols-3,
  .mobile-dashboard-content .lg\\:grid-cols-4 {
    grid-template-columns: 1fr !important;
  }

  /* Mobile flex improvements */
  .mobile-dashboard-content .flex-row {
    flex-direction: column !important;
  }

  .mobile-dashboard-content .justify-between {
    justify-content: flex-start !important;
    gap: 1rem !important;
  }

  /* Mobile card improvements */
  .mobile-dashboard-content .rounded-lg,
  .mobile-dashboard-content .rounded-xl {
    border-radius: 0.5rem !important;
  }

  /* Mobile shadow improvements */
  .mobile-dashboard-content .shadow-lg,
  .mobile-dashboard-content .shadow-xl,
  .mobile-dashboard-content .shadow-2xl {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  }

  /* Mobile performance optimizations */
  .mobile-dashboard-content * {
    -webkit-tap-highlight-color: transparent !important;
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
  }

  .mobile-dashboard-content input,
  .mobile-dashboard-content textarea,
  .mobile-dashboard-content select {
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
  }

  /* Mobile scroll improvements */
  .mobile-dashboard-content {
    -webkit-overflow-scrolling: touch !important;
    scroll-behavior: smooth !important;
  }

  /* Mobile focus improvements */
  .mobile-dashboard-content button:focus,
  .mobile-dashboard-content input:focus,
  .mobile-dashboard-content select:focus,
  .mobile-dashboard-content textarea:focus {
    outline: 2px solid #3b82f6 !important;
    outline-offset: 2px !important;
  }

  /* Mobile accessibility improvements */
  .mobile-dashboard-content [role="button"],
  .mobile-dashboard-content button,
  .mobile-dashboard-content .clickable {
    cursor: pointer !important;
    touch-action: manipulation !important;
  }

  /* Mobile loading states */
  .mobile-dashboard-content .loading {
    pointer-events: none !important;
    opacity: 0.6 !important;
  }

  /* Mobile error states */
  .mobile-dashboard-content .error {
    border-color: #ef4444 !important;
    background-color: rgba(239, 68, 68, 0.1) !important;
  }

  /* Mobile success states */
  .mobile-dashboard-content .success {
    border-color: #10b981 !important;
    background-color: rgba(16, 185, 129, 0.1) !important;
  }

  /* Mobile purchase flow scrolling fixes */
  .premium-purchase-flow {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
    overscroll-behavior: contain !important;
    z-index: 9999 !important;
    padding: 8px !important;
  }

  .premium-purchase-modal {
    position: relative !important;
    width: 100% !important;
    max-width: calc(100% - 16px) !important;
    margin: 0 auto !important;
    max-height: calc(100vh - 16px) !important;
    display: flex !important;
    flex-direction: column !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
    border-radius: 12px !important;
  }

  /* Ensure body doesn't scroll when modal is open */
  body.purchase-modal-open {
    overflow: hidden !important;
    position: fixed !important;
    width: 100% !important;
    height: 100% !important;
  }

  /* Force visible scrollbars for purchase modal */
  .premium-purchase-modal {
    scrollbar-width: thin !important;
    scrollbar-color: var(--gold) rgba(255, 255, 255, 0.1) !important;
  }

  .premium-purchase-modal::-webkit-scrollbar {
    width: 12px !important;
  }

  .premium-purchase-modal::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 6px !important;
    border: 1px solid rgba(255, 255, 255, 0.05) !important;
  }

  .premium-purchase-modal::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, var(--gold), #E6C200) !important;
    border-radius: 6px !important;
    border: 1px solid rgba(255, 215, 0, 0.3) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
  }

  .premium-purchase-modal::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #FFE55C, var(--gold)) !important;
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.4) !important;
  }

  .premium-purchase-modal::-webkit-scrollbar-thumb:active {
    background: var(--gold) !important;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3) !important;
  }

  /* Additional scrollbar visibility for purchase flow containers */
  .purchaseFlowContainer,
  .purchaseFlowModal,
  .share-purchase-modal {
    scrollbar-width: thin !important;
    scrollbar-color: var(--gold) rgba(255, 255, 255, 0.1) !important;
  }

  .purchaseFlowContainer::-webkit-scrollbar,
  .purchaseFlowModal::-webkit-scrollbar,
  .share-purchase-modal::-webkit-scrollbar {
    width: 12px !important;
    display: block !important;
  }

  .purchaseFlowContainer::-webkit-scrollbar-track,
  .purchaseFlowModal::-webkit-scrollbar-track,
  .share-purchase-modal::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 6px !important;
    border: 1px solid rgba(255, 255, 255, 0.05) !important;
  }

  .purchaseFlowContainer::-webkit-scrollbar-thumb,
  .purchaseFlowModal::-webkit-scrollbar-thumb,
  .share-purchase-modal::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, var(--gold), #E6C200) !important;
    border-radius: 6px !important;
    border: 1px solid rgba(255, 215, 0, 0.3) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
  }

  .purchaseFlowContainer::-webkit-scrollbar-thumb:hover,
  .purchaseFlowModal::-webkit-scrollbar-thumb:hover,
  .share-purchase-modal::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #FFE55C, var(--gold)) !important;
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.4) !important;
  }
}

/* ========================================
   RESPONSIVE DESIGN ENHANCEMENTS
======================================== */

@media (max-width: 768px) {
  .hero-title {
    font-size: var(--font-size-3xl) !important;
  }

  .metrics-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: var(--space-md) !important;
  }

  .cta-buttons {
    flex-direction: column !important;
    align-items: center !important;
  }

  .nav-tabs {
    flex-direction: column !important;
    align-items: center !important;
  }

  .phase-item {
    flex-direction: column !important;
    text-align: center !important;
  }

  .phase-pricing {
    text-align: center !important;
  }

  .data-table {
    overflow-x: auto !important;
  }

  .strategy-grid {
    grid-template-columns: 1fr !important;
  }
}

@media (max-width: 480px) {
  .metrics-grid {
    grid-template-columns: 1fr !important;
  }

  .hero-title {
    font-size: var(--font-size-2xl) !important;
  }

  .page-title {
    font-size: var(--font-size-3xl) !important;
  }
}

/* ========================================
   INVESTMENT PHASES PAGE COMPONENTS
======================================== */

.phase-overview {
  padding: var(--space-5xl) 0 !important;
  background: var(--bg) !important;
}

.phase-summary-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
  gap: var(--space-xl) !important;
  margin-top: var(--space-4xl) !important;
}

.phase-summary-card {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  box-shadow: var(--shadow) !important;
  transition: all 0.2s ease !important;
}

.phase-summary-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.1) !important;
}

.phase-summary-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: var(--space-lg) !important;
  padding-bottom: var(--space-lg) !important;
  border-bottom: 1px solid var(--border) !important;
}

.phase-group {
  color: var(--text) !important;
  font-size: var(--font-size-xl) !important;
  font-weight: var(--font-weight-bold) !important;
  margin: 0 !important;
}

.price-range {
  background: var(--gold) !important;
  color: var(--cta-text) !important;
  padding: var(--space-sm) var(--space-md) !important;
  border-radius: var(--radius-md) !important;
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-bold) !important;
}

.phase-summary-details {
  margin-bottom: var(--space-lg) !important;
}

.detail-row {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: var(--space-sm) 0 !important;
  border-bottom: 1px solid rgba(255, 215, 0, 0.1) !important;
}

.detail-row:last-child {
  border-bottom: none !important;
}

.detail-label {
  color: var(--text-muted) !important;
  font-size: var(--font-size-sm) !important;
}

.detail-value {
  color: var(--text) !important;
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-bold) !important;
}

.phase-highlight {
  background: rgba(255, 215, 0, 0.05) !important;
  border: 1px solid rgba(255, 215, 0, 0.2) !important;
  border-radius: var(--radius-md) !important;
  padding: var(--space-lg) !important;
  display: flex !important;
  align-items: flex-start !important;
  gap: var(--space-md) !important;
}

.highlight-icon {
  font-size: var(--font-size-lg) !important;
  flex-shrink: 0 !important;
}

.phase-highlight p {
  color: var(--text) !important;
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-medium) !important;
  margin: 0 !important;
  line-height: var(--line-height-relaxed) !important;
}

/* Phase Actions */
.phase-actions {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: var(--space-sm) !important;
  margin-top: var(--space-lg) !important;
  padding-top: var(--space-lg) !important;
  border-top: 1px solid var(--border) !important;
}

.phase-purchase-btn {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  padding: var(--space-sm) var(--space-md) !important;
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-md) !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  min-width: 80px !important;
  font-size: var(--font-size-xs) !important;
  text-align: center !important;
}

.phase-purchase-btn.available {
  background: linear-gradient(135deg, var(--gold) 0%, #B8860B 100%) !important;
  color: var(--bg) !important;
  border-color: var(--gold) !important;
}

.phase-purchase-btn.available:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3) !important;
}

.phase-purchase-btn.unavailable {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
}

.btn-phase {
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: 2px !important;
}

.btn-price {
  font-size: var(--font-size-xs) !important;
  margin-bottom: 2px !important;
}

.btn-status {
  font-size: 10px !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.btn-status.active {
  color: #10B981 !important;
}

.btn-status.coming-soon {
  color: #6B7280 !important;
}

.btn-status.sold-out {
  color: #EF4444 !important;
}

/* ========================================
   INDIVIDUAL PHASES SECTION
======================================== */

.individual-phases {
  padding: var(--space-5xl) 0 !important;
  background: var(--surface) !important;
}

.loading-state {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  padding: var(--space-4xl) 0 !important;
  color: var(--text-secondary) !important;
}

.loading-spinner {
  width: 40px !important;
  height: 40px !important;
  border: 3px solid var(--border) !important;
  border-top: 3px solid var(--gold) !important;
  border-radius: 50% !important;
  animation: spin 1s linear infinite !important;
  margin-bottom: var(--space-md) !important;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Phases List Layout */
.phases-list {
  display: flex !important;
  flex-direction: column !important;
  gap: var(--space-xl) !important;
  margin-top: var(--space-4xl) !important;
}

/* Tabbed Interface Styles */
.phases-tabbed-interface {
  margin-top: var(--space-4xl) !important;
}

.phase-tabs {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: var(--space-xs) !important;
  margin-bottom: var(--space-xl) !important;
  padding: var(--space-sm) !important;
  background: rgba(0, 0, 0, 0.3) !important;
  border-radius: var(--radius-lg) !important;
  border: 1px solid rgba(212, 175, 55, 0.2) !important;
}

.phase-tab {
  flex: 1 !important;
  min-width: 120px !important;
  padding: var(--space-sm) var(--space-md) !important;
  background: rgba(0, 0, 0, 0.4) !important;
  border: 1px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-md) !important;
  color: var(--text-secondary) !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  text-align: center !important;
  position: relative !important;
}

.phase-tab:hover {
  background: rgba(212, 175, 55, 0.1) !important;
  border-color: rgba(212, 175, 55, 0.5) !important;
  transform: translateY(-2px) !important;
}

.phase-tab.active {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1)) !important;
  border-color: var(--gold) !important;
  color: var(--gold) !important;
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3) !important;
}

.phase-tab.available {
  border-left: 4px solid var(--success) !important;
}

.phase-tab.unavailable {
  border-left: 4px solid var(--text-muted) !important;
}

.tab-title {
  font-weight: 600 !important;
  font-size: var(--text-sm) !important;
  margin-bottom: var(--space-xs) !important;
}

.tab-price {
  font-size: var(--text-lg) !important;
  font-weight: 700 !important;
  color: var(--gold) !important;
  margin-bottom: var(--space-xs) !important;
}

.tab-status {
  font-size: var(--text-xs) !important;
  padding: 2px 6px !important;
  border-radius: var(--radius-sm) !important;
  font-weight: 500 !important;
}

.selected-phase-content {
  animation: fadeInUp 0.3s ease !important;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.phase-row {
  display: grid !important;
  grid-template-columns: 300px 1fr 1fr !important;
  gap: var(--space-lg) !important;
  background: var(--bg) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  box-shadow: var(--shadow) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.phase-row.active {
  border-color: var(--gold) !important;
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.15) !important;
}

.phase-row.active::before {
  content: 'ACTIVE' !important;
  position: absolute !important;
  top: 10px !important;
  right: -25px !important;
  background: var(--gold) !important;
  color: var(--bg) !important;
  padding: 4px 30px !important;
  font-size: 10px !important;
  font-weight: var(--font-weight-bold) !important;
  transform: rotate(45deg) !important;
  letter-spacing: 1px !important;
}

.phase-row:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1) !important;
}

/* Phase Columns */
.phase-column {
  display: flex !important;
  flex-direction: column !important;
}

.phase-details-column {
  border-right: 1px solid var(--border) !important;
  padding-right: var(--space-lg) !important;
}

.operational-column {
  border-right: 1px solid var(--border) !important;
  padding-right: var(--space-lg) !important;
  padding-left: var(--space-lg) !important;
}

.csr-column {
  padding-left: var(--space-lg) !important;
}

/* Phase Header */
.phase-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: flex-start !important;
  margin-bottom: var(--space-md) !important;
}

/* Column Titles */
.column-title {
  font-size: var(--font-size-lg) !important;
  font-weight: var(--font-weight-semibold) !important;
  color: var(--text) !important;
  margin: 0 0 var(--space-md) 0 !important;
  display: flex !important;
  align-items: center !important;
  gap: var(--space-sm) !important;
}

.column-icon {
  font-size: var(--font-size-lg) !important;
}

/* Allocation Lists */
.allocation-list {
  list-style: none !important;
  padding: 0 !important;
  margin: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  gap: var(--space-sm) !important;
}

.allocation-item {
  display: flex !important;
  align-items: flex-start !important;
  gap: var(--space-sm) !important;
  font-size: var(--font-size-sm) !important;
  color: var(--text-muted) !important;
  line-height: 1.4 !important;
}

.allocation-item::before {
  content: '✓' !important;
  color: var(--gold) !important;
  font-weight: var(--font-weight-bold) !important;
  flex-shrink: 0 !important;
  margin-top: 2px !important;
}

/* Responsive Design for Phase Rows */
@media (max-width: 1024px) {
  .phase-row {
    grid-template-columns: 1fr !important;
    gap: var(--space-lg) !important;
  }

  .phase-details-column,
  .operational-column {
    border-right: none !important;
    border-bottom: 1px solid var(--border) !important;
    padding-right: 0 !important;
    padding-bottom: var(--space-lg) !important;
    padding-left: 0 !important;
  }

  .csr-column {
    padding-left: 0 !important;
  }
}

@media (max-width: 768px) {
  .phases-list {
    gap: var(--space-lg) !important;
  }

  .phase-row {
    padding: var(--space-lg) !important;
  }

  .phase-header {
    flex-direction: column !important;
    gap: var(--space-sm) !important;
  }

  .phase-status-badge {
    align-self: flex-start !important;
  }

  /* Tabbed Interface Mobile Styles */
  .phase-tabs {
    flex-direction: column !important;
    gap: var(--space-xs) !important;
  }

  .phase-tab {
    min-width: unset !important;
    flex: none !important;
  }

  .tab-title {
    font-size: var(--text-xs) !important;
  }

  .tab-price {
    font-size: var(--text-md) !important;
  }
}

.phase-card-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: flex-start !important;
  margin-bottom: var(--space-lg) !important;
  padding-bottom: var(--space-lg) !important;
  border-bottom: 1px solid var(--border) !important;
}

.phase-info {
  display: flex !important;
  flex-direction: column !important;
  gap: var(--space-sm) !important;
}

.phase-title {
  font-size: var(--font-size-lg) !important;
  font-weight: var(--font-weight-bold) !important;
  color: var(--text) !important;
  margin: 0 !important;
}

.phase-status-badge {
  padding: 4px 8px !important;
  border-radius: var(--radius-sm) !important;
  font-size: var(--font-size-xs) !important;
  font-weight: var(--font-weight-bold) !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  align-self: flex-start !important;
}

.phase-price {
  margin-bottom: var(--space-md) !important;
}

.price-value {
  display: block !important;
  font-size: var(--font-size-2xl) !important;
  font-weight: var(--font-weight-bold) !important;
  color: var(--gold) !important;
  line-height: 1 !important;
}

.price-label {
  display: block !important;
  font-size: var(--font-size-sm) !important;
  color: var(--text-muted) !important;
  margin-top: var(--space-xs) !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.phase-card-body {
  margin-bottom: var(--space-lg) !important;
}

.shares-info {
  margin-bottom: var(--space-lg) !important;
}

.shares-row {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: var(--space-sm) 0 !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
}

.shares-row:last-child {
  border-bottom: none !important;
  font-weight: var(--font-weight-bold) !important;
}

.shares-label {
  font-size: var(--font-size-sm) !important;
  color: var(--text-secondary) !important;
}

.shares-value {
  font-size: var(--font-size-sm) !important;
  color: var(--text) !important;
  font-weight: var(--font-weight-medium) !important;
}

.shares-value.highlight {
  color: var(--gold) !important;
  font-weight: var(--font-weight-bold) !important;
}

.shares-value.gold {
  color: var(--gold) !important;
  font-weight: var(--font-weight-bold) !important;
}

/* Phase Details Sections */
.phase-details-section {
  margin-top: var(--space-lg) !important;
  padding-top: var(--space-lg) !important;
  border-top: 1px solid var(--border) !important;
}

.details-title {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-sm) !important;
  font-size: var(--font-size-md) !important;
  font-weight: var(--font-weight-bold) !important;
  color: var(--text) !important;
  margin: 0 0 var(--space-md) 0 !important;
}

.details-icon {
  font-size: var(--font-size-lg) !important;
  flex-shrink: 0 !important;
}

.details-list {
  list-style: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.details-item {
  display: flex !important;
  align-items: flex-start !important;
  gap: var(--space-sm) !important;
  padding: var(--space-sm) 0 !important;
  font-size: var(--font-size-sm) !important;
  color: var(--text-secondary) !important;
  line-height: var(--line-height-relaxed) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
}

.details-item:last-child {
  border-bottom: none !important;
}

.details-item::before {
  content: '✓' !important;
  color: var(--gold) !important;
  font-weight: var(--font-weight-bold) !important;
  flex-shrink: 0 !important;
  margin-top: 2px !important;
}

.progress-bar {
  width: 100% !important;
  height: 8px !important;
  background: var(--border) !important;
  border-radius: var(--radius-full) !important;
  overflow: hidden !important;
  margin-bottom: var(--space-sm) !important;
}

.progress-fill {
  height: 100% !important;
  background: linear-gradient(90deg, var(--gold) 0%, #B8860B 100%) !important;
  border-radius: var(--radius-full) !important;
  transition: width 0.3s ease !important;
}

.progress-text {
  font-size: var(--font-size-xs) !important;
  color: var(--text-secondary) !important;
  text-align: center !important;
}

.phase-card-footer {
  padding-top: var(--space-lg) !important;
  border-top: 1px solid var(--border) !important;
}

.purchase-btn {
  width: 100% !important;
  padding: var(--space-md) var(--space-lg) !important;
  border: none !important;
  border-radius: var(--radius-md) !important;
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-bold) !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: var(--space-sm) !important;
}

.purchase-btn.available {
  background: linear-gradient(135deg, var(--gold) 0%, #B8860B 100%) !important;
  color: var(--bg) !important;
}

.purchase-btn.available:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4) !important;
}

.purchase-btn.unavailable {
  background: var(--surface) !important;
  color: var(--text-secondary) !important;
  border: 1px solid var(--border) !important;
  cursor: not-allowed !important;
  opacity: 0.7 !important;
}

.btn-icon {
  font-size: var(--font-size-md) !important;
}

/* ========================================
   INVESTMENT POINTS SECTION
======================================== */

.investment-points {
  padding: var(--space-5xl) 0 !important;
  background: var(--bg) !important;
}

.points-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
  gap: var(--space-xl) !important;
  margin-top: var(--space-4xl) !important;
}

.point-card {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  box-shadow: var(--shadow) !important;
  text-align: center !important;
  transition: all 0.2s ease !important;
}

.point-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.1) !important;
}

.point-icon {
  font-size: var(--font-size-3xl) !important;
  margin-bottom: var(--space-lg) !important;
  display: block !important;
}

.point-card h3 {
  color: var(--text) !important;
  font-size: var(--font-size-xl) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: var(--space-lg) !important;
}

.point-card p {
  color: var(--text-muted) !important;
  font-size: var(--font-size-sm) !important;
  line-height: var(--line-height-relaxed) !important;
  margin: 0 !important;
}

/* ========================================
   ENHANCED INVESTMENT STRATEGY RECOMMENDATIONS
======================================== */

.investment-strategy {
  padding: var(--space-5xl) 0 !important;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.95)) !important;
}

.strategy-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)) !important;
  gap: var(--space-4xl) !important;
  margin-top: var(--space-4xl) !important;
}

.strategy-card {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.9)) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--space-4xl) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.strategy-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 4px !important;
  background: linear-gradient(90deg, var(--gold), rgba(212, 175, 55, 0.5)) !important;
}

.strategy-card:hover {
  transform: translateY(-8px) !important;
  box-shadow: 0 16px 48px rgba(212, 175, 55, 0.2) !important;
  border-color: var(--gold) !important;
}

.strategy-card.early-adopter::before {
  background: linear-gradient(90deg, #ff6b35, #f7931e) !important;
}

.strategy-card.growth-investor::before {
  background: linear-gradient(90deg, #4caf50, #8bc34a) !important;
}

.strategy-card.stable-returns::before {
  background: linear-gradient(90deg, #2196f3, #03a9f4) !important;
}

.strategy-header {
  text-align: center !important;
  margin-bottom: var(--space-xl) !important;
}

.strategy-badge {
  display: inline-block !important;
  padding: var(--space-sm) var(--space-lg) !important;
  border-radius: var(--radius-full) !important;
  font-weight: 700 !important;
  font-size: var(--text-sm) !important;
  margin-bottom: var(--space-sm) !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.strategy-badge.early {
  background: linear-gradient(135deg, #ff6b35, #f7931e) !important;
  color: white !important;
}

.strategy-badge.growth {
  background: linear-gradient(135deg, #4caf50, #8bc34a) !important;
  color: white !important;
}

.strategy-badge.stable {
  background: linear-gradient(135deg, #2196f3, #03a9f4) !important;
  color: white !important;
}

.strategy-subtitle {
  color: var(--text-secondary) !important;
  font-size: var(--text-sm) !important;
  font-style: italic !important;
}

.strategy-card h3 {
  color: var(--gold) !important;
  font-size: var(--text-2xl) !important;
  font-weight: 700 !important;
  margin: var(--space-lg) 0 !important;
  text-align: center !important;
}

.strategy-price-range {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: var(--space-sm) !important;
  margin-bottom: var(--space-xl) !important;
  padding: var(--space-lg) !important;
  background: rgba(212, 175, 55, 0.1) !important;
  border-radius: var(--radius-lg) !important;
  border: 1px solid rgba(212, 175, 55, 0.3) !important;
}

.price-from, .price-to {
  font-size: var(--text-xl) !important;
  font-weight: 700 !important;
  color: var(--gold) !important;
}

.price-separator {
  color: var(--text-secondary) !important;
  font-size: var(--text-lg) !important;
}

.price-label {
  color: var(--text-secondary) !important;
  font-size: var(--text-sm) !important;
}

.strategy-highlights {
  display: flex !important;
  flex-direction: column !important;
  gap: var(--space-sm) !important;
  margin-bottom: var(--space-xl) !important;
}

.highlight-item {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-sm) !important;
  padding: var(--space-sm) !important;
  background: rgba(255, 255, 255, 0.05) !important;
  border-radius: var(--radius-md) !important;
  color: var(--text) !important;
  font-size: var(--text-sm) !important;
  font-weight: 500 !important;
}

.highlight-icon {
  font-size: var(--text-lg) !important;
}

.strategy-details {
  margin-bottom: var(--space-xl) !important;
}

.strategy-details h4 {
  color: var(--gold) !important;
  font-size: var(--text-lg) !important;
  font-weight: 600 !important;
  margin-bottom: var(--space-md) !important;
}

.strategy-benefits {
  list-style: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.strategy-benefits li {
  color: var(--text) !important;
  font-size: var(--text-sm) !important;
  padding: var(--space-xs) 0 !important;
  padding-left: var(--space-lg) !important;
  position: relative !important;
}

.strategy-benefits li::before {
  content: "✓" !important;
  position: absolute !important;
  left: 0 !important;
  color: var(--success) !important;
  font-weight: 700 !important;
}

.strategy-risk {
  padding: var(--space-lg) !important;
  border-radius: var(--radius-lg) !important;
  margin-bottom: var(--space-xl) !important;
  border-left: 4px solid !important;
}

.strategy-risk.early {
  background: rgba(255, 107, 53, 0.1) !important;
  border-left-color: #ff6b35 !important;
}

.strategy-risk.growth {
  background: rgba(76, 175, 80, 0.1) !important;
  border-left-color: #4caf50 !important;
}

.strategy-risk.stable {
  background: rgba(33, 150, 243, 0.1) !important;
  border-left-color: #2196f3 !important;
}

.risk-header {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-sm) !important;
  margin-bottom: var(--space-sm) !important;
}

.risk-header strong {
  color: var(--text) !important;
  font-size: var(--text-md) !important;
}

.risk-icon {
  font-size: var(--text-lg) !important;
}

.strategy-risk p {
  color: var(--text-secondary) !important;
  font-size: var(--text-sm) !important;
  margin: 0 !important;
  line-height: var(--line-height-relaxed) !important;
}

.strategy-cta {
  text-align: center !important;
}

.strategy-btn {
  background: linear-gradient(135deg, var(--gold), rgba(212, 175, 55, 0.8)) !important;
  color: var(--bg) !important;
  border: none !important;
  padding: var(--space-md) var(--space-xl) !important;
  border-radius: var(--radius-lg) !important;
  font-weight: 700 !important;
  font-size: var(--text-md) !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  width: 100% !important;
}

.strategy-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(212, 175, 55, 0.4) !important;
}

.strategy-btn.early {
  background: linear-gradient(135deg, #ff6b35, #f7931e) !important;
}

.strategy-btn.growth {
  background: linear-gradient(135deg, #4caf50, #8bc34a) !important;
}

.strategy-btn.stable {
  background: linear-gradient(135deg, #2196f3, #03a9f4) !important;
}

/* Strategy Comparison Table */
.strategy-comparison {
  margin-top: var(--space-5xl) !important;
  padding: var(--space-4xl) !important;
  background: rgba(0, 0, 0, 0.6) !important;
  border-radius: var(--radius-xl) !important;
  border: 1px solid rgba(212, 175, 55, 0.3) !important;
}

.strategy-comparison h3 {
  color: var(--gold) !important;
  font-size: var(--text-2xl) !important;
  font-weight: 700 !important;
  text-align: center !important;
  margin-bottom: var(--space-xl) !important;
}

.comparison-table {
  display: grid !important;
  grid-template-columns: 1fr 120px 100px 150px 1fr !important;
  gap: var(--space-sm) !important;
  background: rgba(0, 0, 0, 0.4) !important;
  border-radius: var(--radius-lg) !important;
  overflow: hidden !important;
}

.comparison-header {
  display: contents !important;
}

.comparison-header .comparison-cell {
  background: rgba(212, 175, 55, 0.2) !important;
  color: var(--gold) !important;
  font-weight: 700 !important;
  font-size: var(--text-sm) !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.comparison-row {
  display: contents !important;
}

.comparison-row.early .comparison-cell:first-child {
  border-left: 4px solid #ff6b35 !important;
}

.comparison-row.growth .comparison-cell:first-child {
  border-left: 4px solid #4caf50 !important;
}

.comparison-row.stable .comparison-cell:first-child {
  border-left: 4px solid #2196f3 !important;
}

.comparison-cell {
  padding: var(--space-md) var(--space-lg) !important;
  color: var(--text) !important;
  font-size: var(--text-sm) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  display: flex !important;
  align-items: center !important;
}

.comparison-row:last-child .comparison-cell {
  border-bottom: none !important;
}

/* Responsive Design for Enhanced Strategy Section */
@media (max-width: 1024px) {
  .strategy-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)) !important;
    gap: var(--space-xl) !important;
  }

  .comparison-table {
    grid-template-columns: 1fr 100px 80px 120px 1fr !important;
    font-size: var(--text-xs) !important;
  }
}

@media (max-width: 768px) {
  .strategy-grid {
    grid-template-columns: 1fr !important;
    gap: var(--space-lg) !important;
  }

  .strategy-card {
    padding: var(--space-xl) !important;
  }

  .strategy-price-range {
    flex-direction: column !important;
    gap: var(--space-xs) !important;
  }

  .price-from, .price-to {
    font-size: var(--text-lg) !important;
  }

  .comparison-table {
    display: block !important;
    overflow-x: auto !important;
  }

  .comparison-header,
  .comparison-row {
    display: grid !important;
    grid-template-columns: 1fr 100px 80px 120px 1fr !important;
    gap: var(--space-xs) !important;
  }

  .strategy-comparison {
    padding: var(--space-xl) !important;
  }

  .strategy-comparison h3 {
    font-size: var(--text-xl) !important;
  }
}

@media (max-width: 480px) {
  .strategy-card {
    padding: var(--space-lg) !important;
  }

  .strategy-badge {
    font-size: var(--text-xs) !important;
    padding: var(--space-xs) var(--space-md) !important;
  }

  .strategy-card h3 {
    font-size: var(--text-xl) !important;
  }

  .comparison-table {
    font-size: var(--text-xs) !important;
  }

  .comparison-cell {
    padding: var(--space-sm) !important;
  }
}

/* ========================================
   FINANCIAL DATA PAGE COMPONENTS
======================================== */

/* Enhanced Key Assumptions Section */
.enhanced-assumptions-section {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: 16px !important;
  padding: 32px !important;
  margin: 32px 0 !important;
  box-shadow: var(--shadow) !important;
}

.assumptions-header {
  display: flex !important;
  align-items: center !important;
  gap: 16px !important;
  margin-bottom: 32px !important;
  padding-bottom: 24px !important;
  border-bottom: 1px solid var(--border) !important;
}

.assumptions-icon {
  font-size: 32px !important;
  background: linear-gradient(135deg, var(--gold), #FFA500) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

.assumptions-content h3 {
  color: var(--text) !important;
  font-size: 24px !important;
  font-weight: 700 !important;
  margin: 0 0 8px 0 !important;
}

.assumptions-content p {
  color: var(--text-muted) !important;
  font-size: 16px !important;
  margin: 0 !important;
}

.assumptions-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
  gap: 20px !important;
}

.assumption-card {
  background: rgba(255, 255, 255, 0.02) !important;
  border: 1px solid var(--border) !important;
  border-radius: 12px !important;
  padding: 24px !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.assumption-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 3px !important;
  background: linear-gradient(90deg, var(--gold), var(--blue)) !important;
}

.assumption-card:hover {
  transform: translateY(-4px) !important;
  box-shadow: var(--glow) !important;
  border-color: var(--gold) !important;
}

.assumption-card .assumption-icon {
  font-size: 28px !important;
  margin-bottom: 16px !important;
  display: block !important;
}

.assumption-details h4 {
  color: var(--text) !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  margin: 0 0 8px 0 !important;
}

.assumption-value {
  color: var(--gold) !important;
  font-size: 24px !important;
  font-weight: 800 !important;
  margin: 8px 0 12px 0 !important;
  text-shadow: var(--glow) !important;
}

.assumption-details p {
  color: var(--text-muted) !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  margin: 0 !important;
}

/* Enhanced Risk Factors Section */
.enhanced-risk-factors-section {
  background: var(--bg) !important;
  padding: 64px 0 !important;
  margin-top: 48px !important;
}

.enhanced-risk-factors-section .section-header {
  display: flex !important;
  align-items: center !important;
  gap: 20px !important;
  margin-bottom: 48px !important;
  text-align: left !important;
}

.enhanced-risk-factors-section .header-icon {
  font-size: 48px !important;
  background: linear-gradient(135deg, #FF6B6B, #FF8E53) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

.enhanced-risk-factors-section .header-content h2 {
  color: var(--text) !important;
  font-size: 32px !important;
  font-weight: 700 !important;
  margin: 0 0 8px 0 !important;
}

.enhanced-risk-factors-section .header-content p {
  color: var(--text-muted) !important;
  font-size: 18px !important;
  margin: 0 !important;
}

.enhanced-risk-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)) !important;
  gap: 24px !important;
  margin-bottom: 48px !important;
}

.enhanced-risk-card {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: 16px !important;
  padding: 28px !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.enhanced-risk-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 4px !important;
}

.enhanced-risk-card.market-risk::before {
  background: linear-gradient(90deg, #FF6B6B, #FF8E53) !important;
}

.enhanced-risk-card.operational-risk::before {
  background: linear-gradient(90deg, #4ECDC4, #44A08D) !important;
}

.enhanced-risk-card.political-risk::before {
  background: linear-gradient(90deg, #A8E6CF, #7FCDCD) !important;
}

.enhanced-risk-card.performance-risk::before {
  background: linear-gradient(90deg, var(--blue), #6C5CE7) !important;
}

.enhanced-risk-card:hover {
  transform: translateY(-6px) !important;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15) !important;
  border-color: var(--gold) !important;
}

.risk-card-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 20px !important;
}

.risk-icon-wrapper {
  background: rgba(255, 255, 255, 0.05) !important;
  border-radius: 12px !important;
  padding: 12px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.risk-icon-wrapper .risk-icon {
  font-size: 24px !important;
}

.risk-severity {
  padding: 6px 12px !important;
  border-radius: 20px !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.risk-severity.high {
  background: rgba(255, 107, 107, 0.2) !important;
  color: #FF6B6B !important;
  border: 1px solid rgba(255, 107, 107, 0.3) !important;
}

.risk-severity.medium {
  background: rgba(255, 193, 7, 0.2) !important;
  color: #FFC107 !important;
  border: 1px solid rgba(255, 193, 7, 0.3) !important;
}

.risk-severity.low {
  background: rgba(46, 204, 113, 0.2) !important;
  color: #2ECC71 !important;
  border: 1px solid rgba(46, 204, 113, 0.3) !important;
}

.risk-content h3 {
  color: var(--text) !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  margin: 0 0 12px 0 !important;
}

.risk-content p {
  color: var(--text-muted) !important;
  font-size: 15px !important;
  line-height: 1.6 !important;
  margin: 0 0 16px 0 !important;
}

.risk-mitigation {
  background: rgba(255, 215, 0, 0.05) !important;
  border: 1px solid rgba(255, 215, 0, 0.2) !important;
  border-radius: 8px !important;
  padding: 12px !important;
  font-size: 14px !important;
}

.risk-mitigation strong {
  color: var(--gold) !important;
  font-weight: 600 !important;
}

/* Enhanced Disclaimer Section */
.enhanced-disclaimer {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.05), rgba(0, 191, 255, 0.05)) !important;
  border: 2px solid var(--border) !important;
  border-radius: 20px !important;
  padding: 40px !important;
  margin-top: 48px !important;
  position: relative !important;
  overflow: hidden !important;
}

.enhanced-disclaimer::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 4px !important;
  background: linear-gradient(90deg, var(--gold), var(--blue)) !important;
}

.disclaimer-header {
  display: flex !important;
  align-items: center !important;
  gap: 16px !important;
  margin-bottom: 24px !important;
  padding-bottom: 20px !important;
  border-bottom: 1px solid var(--border) !important;
}

.disclaimer-icon {
  font-size: 32px !important;
  background: linear-gradient(135deg, var(--gold), var(--blue)) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

.disclaimer-header h3 {
  color: var(--text) !important;
  font-size: 24px !important;
  font-weight: 700 !important;
  margin: 0 !important;
}

.disclaimer-content {
  display: grid !important;
  gap: 20px !important;
}

.disclaimer-content p {
  color: var(--text-muted) !important;
  font-size: 16px !important;
  line-height: 1.7 !important;
  margin: 0 !important;
  padding: 20px !important;
  background: rgba(255, 255, 255, 0.02) !important;
  border: 1px solid var(--border) !important;
  border-radius: 12px !important;
  position: relative !important;
}

.disclaimer-content p strong {
  color: var(--gold) !important;
  font-weight: 700 !important;
  display: block !important;
  margin-bottom: 8px !important;
  font-size: 17px !important;
}

/* Responsive Design for Enhanced Sections */
@media (max-width: 768px) {
  .enhanced-assumptions-section,
  .enhanced-disclaimer {
    padding: 24px !important;
    margin: 24px 0 !important;
  }

  .assumptions-header,
  .disclaimer-header {
    flex-direction: column !important;
    text-align: center !important;
    gap: 12px !important;
  }

  .assumptions-grid {
    grid-template-columns: 1fr !important;
    gap: 16px !important;
  }

  .enhanced-risk-grid {
    grid-template-columns: 1fr !important;
    gap: 20px !important;
  }

  .enhanced-risk-factors-section .section-header {
    flex-direction: column !important;
    text-align: center !important;
    gap: 16px !important;
  }

  .enhanced-risk-factors-section .header-content h2 {
    font-size: 28px !important;
  }

  .enhanced-risk-factors-section .header-content p {
    font-size: 16px !important;
  }
}

/* Enhanced Phase Summary Section */
.enhanced-phase-summary {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: 20px !important;
  padding: 40px !important;
  margin: 40px 0 !important;
  box-shadow: var(--shadow) !important;
  position: relative !important;
  overflow: hidden !important;
}

.enhanced-phase-summary::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 4px !important;
  background: linear-gradient(90deg, var(--gold), var(--blue), var(--emerald)) !important;
}

.summary-header {
  display: flex !important;
  align-items: center !important;
  gap: 20px !important;
  margin-bottom: 40px !important;
  padding-bottom: 24px !important;
  border-bottom: 1px solid var(--border) !important;
}

.summary-icon {
  font-size: 40px !important;
  background: linear-gradient(135deg, var(--gold), #FFA500) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

.summary-content h3 {
  color: var(--text) !important;
  font-size: 28px !important;
  font-weight: 700 !important;
  margin: 0 0 8px 0 !important;
}

.summary-content p {
  color: var(--text-muted) !important;
  font-size: 18px !important;
  margin: 0 !important;
}

.enhanced-summary-cards {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)) !important;
  gap: 24px !important;
  margin-bottom: 40px !important;
}

.enhanced-summary-card {
  background: rgba(255, 255, 255, 0.02) !important;
  border: 1px solid var(--border) !important;
  border-radius: 16px !important;
  padding: 28px !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.enhanced-summary-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 3px !important;
}

.enhanced-summary-card.funding-capacity::before {
  background: linear-gradient(90deg, var(--gold), #FFD700) !important;
}

.enhanced-summary-card.operations-allocation::before {
  background: linear-gradient(90deg, var(--blue), #4ECDC4) !important;
}

.enhanced-summary-card.csr-allocation::before {
  background: linear-gradient(90deg, var(--emerald), #A8E6CF) !important;
}

.enhanced-summary-card:hover {
  transform: translateY(-6px) !important;
  box-shadow: var(--glow) !important;
  border-color: var(--gold) !important;
}

.card-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 20px !important;
}

.card-icon {
  font-size: 28px !important;
  background: rgba(255, 255, 255, 0.05) !important;
  border-radius: 12px !important;
  padding: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 44px !important;
  height: 44px !important;
}

.card-badge {
  background: rgba(255, 215, 0, 0.1) !important;
  color: var(--gold) !important;
  padding: 6px 12px !important;
  border-radius: 20px !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  border: 1px solid rgba(255, 215, 0, 0.2) !important;
}

.card-content h4 {
  color: var(--text) !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  margin: 0 0 12px 0 !important;
}

.enhanced-summary-value {
  color: var(--gold) !important;
  font-size: 32px !important;
  font-weight: 800 !important;
  margin: 12px 0 16px 0 !important;
  text-shadow: var(--glow) !important;
  display: block !important;
}

.card-content p {
  color: var(--text-muted) !important;
  font-size: 15px !important;
  line-height: 1.5 !important;
  margin: 0 0 20px 0 !important;
}

.progress-indicator {
  margin-top: 16px !important;
}

.progress-bar {
  background: rgba(255, 255, 255, 0.1) !important;
  border-radius: 10px !important;
  height: 8px !important;
  overflow: hidden !important;
  margin-bottom: 8px !important;
}

.progress-fill {
  background: linear-gradient(90deg, var(--gold), #FFA500) !important;
  height: 100% !important;
  border-radius: 10px !important;
  transition: width 0.3s ease !important;
}

.progress-text {
  color: var(--text-muted) !important;
  font-size: 12px !important;
  font-weight: 500 !important;
}

.allocation-breakdown {
  display: grid !important;
  gap: 8px !important;
  margin-top: 16px !important;
}

.breakdown-item {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 8px 12px !important;
  background: rgba(255, 255, 255, 0.03) !important;
  border-radius: 8px !important;
  font-size: 13px !important;
}

.breakdown-item span:first-child {
  color: var(--text-muted) !important;
}

.breakdown-item span:last-child {
  color: var(--gold) !important;
  font-weight: 600 !important;
}

/* Allocation Insights Section */
.allocation-insights {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
  gap: 20px !important;
  margin-top: 32px !important;
  padding-top: 32px !important;
  border-top: 1px solid var(--border) !important;
}

.insight-card {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.05), rgba(0, 191, 255, 0.05)) !important;
  border: 1px solid var(--border) !important;
  border-radius: 12px !important;
  padding: 24px !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.insight-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 2px !important;
  background: linear-gradient(90deg, var(--gold), var(--blue)) !important;
}

.insight-card:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 24px rgba(255, 215, 0, 0.1) !important;
  border-color: var(--gold) !important;
}

.insight-icon {
  font-size: 24px !important;
  margin-bottom: 16px !important;
  display: block !important;
}

.insight-content h4 {
  color: var(--text) !important;
  font-size: 18px !important;
  font-weight: 700 !important;
  margin: 0 0 12px 0 !important;
}

.insight-content p {
  color: var(--text-muted) !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
  margin: 0 !important;
}

/* Responsive Design for Enhanced Phase Summary */
@media (max-width: 768px) {
  .enhanced-phase-summary {
    padding: 24px !important;
    margin: 24px 0 !important;
  }

  .summary-header {
    flex-direction: column !important;
    text-align: center !important;
    gap: 16px !important;
  }

  .summary-content h3 {
    font-size: 24px !important;
  }

  .summary-content p {
    font-size: 16px !important;
  }

  .enhanced-summary-cards {
    grid-template-columns: 1fr !important;
    gap: 20px !important;
  }

  .enhanced-summary-card {
    padding: 20px !important;
  }

  .enhanced-summary-value {
    font-size: 28px !important;
  }

  .allocation-insights {
    grid-template-columns: 1fr !important;
    gap: 16px !important;
  }

  .insight-card {
    padding: 20px !important;
  }
}

/* Enhanced Metrics Grid */
.enhanced-metrics-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
  gap: 24px !important;
  margin: 40px 0 !important;
}

.enhanced-metric-card {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: 16px !important;
  padding: 24px !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
  cursor: pointer !important;
}

.enhanced-metric-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 3px !important;
  background: linear-gradient(90deg, var(--gold), var(--blue)) !important;
  transform: scaleX(0) !important;
  transform-origin: left !important;
  transition: transform 0.3s ease !important;
}

.enhanced-metric-card:hover::before {
  transform: scaleX(1) !important;
}

.enhanced-metric-card:hover {
  transform: translateY(-8px) !important;
  box-shadow: var(--glow) !important;
  border-color: var(--gold) !important;
}

.metric-card-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 20px !important;
}

.metric-icon {
  font-size: 28px !important;
  background: rgba(255, 215, 0, 0.1) !important;
  border-radius: 12px !important;
  padding: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 44px !important;
  height: 44px !important;
  border: 1px solid rgba(255, 215, 0, 0.2) !important;
}

.metric-category {
  background: rgba(0, 191, 255, 0.1) !important;
  color: var(--blue) !important;
  padding: 4px 8px !important;
  border-radius: 12px !important;
  font-size: 11px !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  border: 1px solid rgba(0, 191, 255, 0.2) !important;
}

.metric-content h3 {
  color: var(--text) !important;
  font-size: 18px !important;
  font-weight: 700 !important;
  margin: 0 0 12px 0 !important;
  line-height: 1.3 !important;
}

.enhanced-metric-value {
  color: var(--gold) !important;
  font-size: 28px !important;
  font-weight: 800 !important;
  margin: 12px 0 16px 0 !important;
  text-shadow: var(--glow) !important;
  display: block !important;
}

.metric-description {
  color: var(--text-muted) !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  margin: 0 0 20px 0 !important;
}

.metric-footer {
  display: flex !important;
  justify-content: flex-end !important;
  align-items: center !important;
  padding-top: 16px !important;
  border-top: 1px solid var(--border) !important;
}

.metric-status {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  font-size: 12px !important;
  color: var(--text-muted) !important;
  font-weight: 500 !important;
}

.status-indicator {
  width: 8px !important;
  height: 8px !important;
  border-radius: 50% !important;
  background: var(--emerald) !important;
  box-shadow: 0 0 8px rgba(46, 204, 113, 0.4) !important;
}

.status-indicator.active {
  animation: pulse 2s infinite !important;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 8px rgba(46, 204, 113, 0.4) !important;
  }
  50% {
    box-shadow: 0 0 16px rgba(46, 204, 113, 0.8) !important;
  }
  100% {
    box-shadow: 0 0 8px rgba(46, 204, 113, 0.4) !important;
  }
}

/* Enhanced Operational Notes Section */
.enhanced-operational-notes {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: 20px !important;
  padding: 40px !important;
  margin: 48px 0 !important;
  box-shadow: var(--shadow) !important;
  position: relative !important;
  overflow: hidden !important;
}

.enhanced-operational-notes::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 4px !important;
  background: linear-gradient(90deg, #4ECDC4, var(--blue), #6C5CE7) !important;
}

.notes-header {
  display: flex !important;
  align-items: center !important;
  gap: 20px !important;
  margin-bottom: 40px !important;
  padding-bottom: 24px !important;
  border-bottom: 1px solid var(--border) !important;
}

.notes-icon {
  font-size: 40px !important;
  background: linear-gradient(135deg, #4ECDC4, var(--blue)) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

.notes-content h3 {
  color: var(--text) !important;
  font-size: 28px !important;
  font-weight: 700 !important;
  margin: 0 0 8px 0 !important;
}

.notes-content p {
  color: var(--text-muted) !important;
  font-size: 18px !important;
  margin: 0 !important;
}

.enhanced-notes-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)) !important;
  gap: 24px !important;
  margin-bottom: 40px !important;
}

.enhanced-note-card {
  background: rgba(255, 255, 255, 0.02) !important;
  border: 1px solid var(--border) !important;
  border-radius: 16px !important;
  padding: 28px !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.enhanced-note-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 3px !important;
}

.enhanced-note-card.geological::before {
  background: linear-gradient(90deg, #4ECDC4, #44A08D) !important;
}

.enhanced-note-card.equipment::before {
  background: linear-gradient(90deg, var(--blue), #6C5CE7) !important;
}

.enhanced-note-card.benchmarks::before {
  background: linear-gradient(90deg, var(--gold), #FFA500) !important;
}

.enhanced-note-card.market::before {
  background: linear-gradient(90deg, var(--emerald), #A8E6CF) !important;
}

.enhanced-note-card:hover {
  transform: translateY(-6px) !important;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15) !important;
  border-color: var(--gold) !important;
}

.note-card-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 20px !important;
}

.note-icon-wrapper {
  background: rgba(255, 255, 255, 0.05) !important;
  border-radius: 12px !important;
  padding: 12px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.note-icon-wrapper .note-icon {
  font-size: 24px !important;
}

.note-badge {
  padding: 6px 12px !important;
  border-radius: 20px !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  background: rgba(255, 215, 0, 0.1) !important;
  color: var(--gold) !important;
  border: 1px solid rgba(255, 215, 0, 0.2) !important;
}

.note-content h4 {
  color: var(--text) !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  margin: 0 0 12px 0 !important;
}

.note-content p {
  color: var(--text-muted) !important;
  font-size: 15px !important;
  line-height: 1.6 !important;
  margin: 0 0 20px 0 !important;
}

.validation-details {
  display: grid !important;
  gap: 8px !important;
  margin-top: 16px !important;
}

.validation-item {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 8px 12px !important;
  background: rgba(255, 255, 255, 0.03) !important;
  border-radius: 8px !important;
  font-size: 13px !important;
}

.validation-label {
  color: var(--text-muted) !important;
  font-weight: 500 !important;
}

.validation-value {
  color: var(--gold) !important;
  font-weight: 600 !important;
}

/* Validation Summary Section */
.validation-summary {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
  gap: 20px !important;
  margin-top: 32px !important;
  padding-top: 32px !important;
  border-top: 1px solid var(--border) !important;
}

.validation-summary .summary-card {
  background: linear-gradient(135deg, rgba(46, 204, 113, 0.05), rgba(0, 191, 255, 0.05)) !important;
  border: 1px solid var(--border) !important;
  border-radius: 12px !important;
  padding: 24px !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.validation-summary .summary-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 2px !important;
  background: linear-gradient(90deg, var(--emerald), var(--blue)) !important;
}

.validation-summary .summary-card:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 24px rgba(46, 204, 113, 0.1) !important;
  border-color: var(--emerald) !important;
}

.validation-summary .summary-icon {
  font-size: 24px !important;
  margin-bottom: 16px !important;
  display: block !important;
}

.validation-summary .summary-content h4 {
  color: var(--text) !important;
  font-size: 18px !important;
  font-weight: 700 !important;
  margin: 0 0 12px 0 !important;
}

.validation-summary .summary-content p {
  color: var(--text-muted) !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
  margin: 0 !important;
}

/* Responsive Design for Enhanced Operational Metrics */
@media (max-width: 768px) {
  .enhanced-metrics-grid {
    grid-template-columns: 1fr !important;
    gap: 20px !important;
  }

  .enhanced-metric-card {
    padding: 20px !important;
  }

  .enhanced-metric-value {
    font-size: 24px !important;
  }

  .enhanced-operational-notes {
    padding: 24px !important;
    margin: 24px 0 !important;
  }

  .notes-header {
    flex-direction: column !important;
    text-align: center !important;
    gap: 16px !important;
  }

  .notes-content h3 {
    font-size: 24px !important;
  }

  .notes-content p {
    font-size: 16px !important;
  }

  .enhanced-notes-grid {
    grid-template-columns: 1fr !important;
    gap: 20px !important;
  }

  .enhanced-note-card {
    padding: 20px !important;
  }

  .validation-summary {
    grid-template-columns: 1fr !important;
    gap: 16px !important;
  }

  .validation-summary .summary-card {
    padding: 20px !important;
  }
}

.financial-data-section {
  padding: var(--space-5xl) 0 !important;
  background: var(--bg) !important;
}

.data-tabs {
  display: flex !important;
  justify-content: center !important;
  gap: var(--space-sm) !important;
  margin-bottom: var(--space-4xl) !important;
  flex-wrap: wrap !important;
}

.data-tab {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-md) !important;
  padding: var(--space-md) var(--space-lg) !important;
  color: var(--text-muted) !important;
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-medium) !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  text-decoration: none !important;
  display: flex !important;
  align-items: center !important;
  gap: var(--space-sm) !important;
}

.data-tab:hover,
.data-tab.active {
  background: var(--gold) !important;
  color: var(--cta-text) !important;
  border-color: var(--gold) !important;
  transform: translateY(-1px) !important;
}

.data-tab-icon {
  font-size: var(--font-size-base) !important;
}

/* Enhanced Data Tables */
.financial-table {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  overflow: hidden !important;
  box-shadow: var(--shadow) !important;
  margin: var(--space-xl) 0 !important;
}

.financial-table table {
  width: 100% !important;
  border-collapse: collapse !important;
}

.financial-table th {
  background: var(--gold) !important;
  color: var(--cta-text) !important;
  padding: var(--space-lg) var(--space-md) !important;
  font-weight: var(--font-weight-bold) !important;
  font-size: var(--font-size-xs) !important;
  text-align: center !important;
  border-right: 1px solid rgba(0,0,0,0.1) !important;
  white-space: nowrap !important;
}

.financial-table th:last-child {
  border-right: none !important;
}

.financial-table td {
  padding: var(--space-md) !important;
  color: var(--text) !important;
  font-size: var(--font-size-xs) !important;
  text-align: center !important;
  border-bottom: 1px solid var(--border) !important;
  border-right: 1px solid var(--border) !important;
  white-space: nowrap !important;
}

.financial-table td:last-child {
  border-right: none !important;
}

.financial-table tr:last-child td {
  border-bottom: none !important;
}

.financial-table tr:hover {
  background: rgba(255, 215, 0, 0.05) !important;
}

/* Table Value Highlighting */
.financial-table .highlight-value {
  color: var(--gold) !important;
  font-weight: var(--font-weight-bold) !important;
}

.financial-table .revenue-value {
  color: var(--emerald) !important;
  font-weight: var(--font-weight-bold) !important;
}

.financial-table .dividend-value {
  color: var(--blue) !important;
  font-weight: var(--font-weight-bold) !important;
}

/* ========================================
   KEY ASSUMPTIONS SECTION
======================================== */

.assumptions-section {
  padding: var(--space-4xl) 0 !important;
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  margin: var(--space-4xl) 0 !important;
}

.assumptions-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
  gap: var(--space-lg) !important;
  margin-top: var(--space-xl) !important;
}

.assumption-item {
  padding: var(--space-lg) !important;
  border-left: 3px solid var(--gold) !important;
  background: rgba(255, 215, 0, 0.05) !important;
}

.assumption-item h4 {
  color: var(--text) !important;
  font-size: var(--font-size-base) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: var(--space-sm) !important;
}

.assumption-item p {
  color: var(--text-muted) !important;
  font-size: var(--font-size-sm) !important;
  margin: 0 !important;
  line-height: var(--line-height-relaxed) !important;
}

/* ========================================
   RISK FACTORS ENHANCED
======================================== */

.risk-factors-section {
  padding: var(--space-5xl) 0 !important;
  background: var(--bg) !important;
}

.risk-factors-grid {
  display: grid !important;
  gap: var(--space-lg) !important;
  margin-top: var(--space-4xl) !important;
}

.risk-factor-item {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  box-shadow: var(--shadow) !important;
  display: flex !important;
  align-items: flex-start !important;
  gap: var(--space-lg) !important;
}

.risk-factor-icon {
  font-size: var(--font-size-2xl) !important;
  flex-shrink: 0 !important;
  margin-top: var(--space-xs) !important;
}

.risk-factor-icon.market { color: var(--warning) !important; }
.risk-factor-icon.operational { color: var(--error) !important; }
.risk-factor-icon.political { color: var(--info) !important; }
.risk-factor-icon.performance { color: var(--copper) !important; }

.risk-factor-content h4 {
  color: var(--text) !important;
  font-size: var(--font-size-lg) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: var(--space-sm) !important;
}

.risk-factor-content p {
  color: var(--text-muted) !important;
  font-size: var(--font-size-sm) !important;
  line-height: var(--line-height-relaxed) !important;
  margin: 0 !important;
}

/* Investment Disclaimer */
.investment-disclaimer {
  background: rgba(220, 53, 69, 0.1) !important;
  border: 1px solid rgba(220, 53, 69, 0.3) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  margin: var(--space-4xl) 0 !important;
}

.investment-disclaimer h4 {
  color: var(--error) !important;
  font-size: var(--font-size-lg) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: var(--space-lg) !important;
}

.investment-disclaimer p {
  color: var(--text) !important;
  font-size: var(--font-size-sm) !important;
  line-height: var(--line-height-relaxed) !important;
  margin: 0 !important;
}

/* ========================================
   CONTENT SECTIONS & CARDS
======================================== */

.content-section {
  padding: var(--space-5xl) 0 !important;
  background: var(--bg) !important;
}

.content-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
  gap: var(--space-xl) !important;
  margin-top: var(--space-4xl) !important;
}

.content-card {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  box-shadow: var(--shadow) !important;
  text-align: center !important;
  transition: all 0.2s ease !important;
}

.content-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.1) !important;
}

.content-card .card-icon {
  font-size: var(--font-size-3xl) !important;
  margin-bottom: var(--space-lg) !important;
  display: block !important;
}

.content-card h3 {
  color: var(--text) !important;
  font-size: var(--font-size-xl) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: var(--space-lg) !important;
}

.content-card p {
  color: var(--text-muted) !important;
  font-size: var(--font-size-sm) !important;
  line-height: var(--line-height-relaxed) !important;
  text-align: left !important;
  margin: 0 !important;
}

/* ========================================
   ADVANTAGES & BENEFITS SECTIONS
======================================== */

.advantages-section {
  padding: var(--space-5xl) 0 !important;
  background: var(--surface) !important;
  border-top: 1px solid var(--border) !important;
  border-bottom: 1px solid var(--border) !important;
}

.advantages-list {
  display: grid !important;
  gap: var(--space-lg) !important;
  margin-top: var(--space-4xl) !important;
  max-width: 800px !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

.advantage-item {
  background: var(--bg) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-md) !important;
  padding: var(--space-lg) !important;
  display: flex !important;
  align-items: center !important;
  gap: var(--space-lg) !important;
  transition: all 0.2s ease !important;
}

.advantage-item:hover {
  background: rgba(255, 215, 0, 0.05) !important;
  border-color: var(--gold) !important;
}

.advantage-icon {
  font-size: var(--font-size-xl) !important;
  flex-shrink: 0 !important;
  color: var(--emerald) !important;
}

.advantage-text {
  color: var(--text) !important;
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-medium) !important;
  margin: 0 !important;
}

/* ========================================
   TECHNICAL EXCELLENCE SECTION
======================================== */

.technical-section {
  padding: var(--space-5xl) 0 !important;
  background: var(--bg) !important;
}

.technical-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
  gap: var(--space-xl) !important;
  margin-top: var(--space-4xl) !important;
}

.technical-card {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  box-shadow: var(--shadow) !important;
  transition: all 0.2s ease !important;
}

.technical-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.1) !important;
}

.technical-card h4 {
  color: var(--gold) !important;
  font-size: var(--font-size-lg) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: var(--space-lg) !important;
}

.technical-card ul {
  list-style: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.technical-card li {
  color: var(--text-muted) !important;
  font-size: var(--font-size-sm) !important;
  padding: var(--space-xs) 0 !important;
  display: flex !important;
  align-items: center !important;
  gap: var(--space-sm) !important;
}

.technical-card li::before {
  content: "•" !important;
  color: var(--emerald) !important;
  font-weight: var(--font-weight-bold) !important;
}

/* ========================================
   ENVIRONMENTAL STEWARDSHIP
======================================== */

.environmental-section {
  padding: var(--space-5xl) 0 !important;
  background: var(--surface) !important;
  border-top: 1px solid var(--border) !important;
  border-bottom: 1px solid var(--border) !important;
}

.environmental-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
  gap: var(--space-xl) !important;
  margin-top: var(--space-4xl) !important;
}

.environmental-card {
  background: var(--bg) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  text-align: center !important;
  transition: all 0.2s ease !important;
}

.environmental-card:hover {
  background: rgba(46, 204, 113, 0.05) !important;
  border-color: var(--emerald) !important;
}

.environmental-card .card-icon {
  font-size: var(--font-size-3xl) !important;
  margin-bottom: var(--space-lg) !important;
  display: block !important;
  color: var(--emerald) !important;
}

.environmental-card h4 {
  color: var(--text) !important;
  font-size: var(--font-size-lg) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: var(--space-lg) !important;
}

.environmental-card p {
  color: var(--text-muted) !important;
  font-size: var(--font-size-sm) !important;
  line-height: var(--line-height-relaxed) !important;
  margin: 0 !important;
}

/* ========================================
   OPERATIONS IN ACTION SECTION
======================================== */

.operations-action-section {
  padding: var(--space-5xl) 0 !important;
  background: var(--bg) !important;
}

.operations-showcase {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-4xl) !important;
  box-shadow: var(--shadow) !important;
  text-align: center !important;
  margin-top: var(--space-4xl) !important;
}

.operations-showcase h3 {
  color: var(--gold) !important;
  font-size: var(--font-size-2xl) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: var(--space-xl) !important;
}

.operations-showcase p {
  color: var(--text-muted) !important;
  font-size: var(--font-size-lg) !important;
  line-height: var(--line-height-relaxed) !important;
  max-width: 600px !important;
  margin: 0 auto var(--space-4xl) !important;
}

.operations-cta {
  background: var(--gold) !important;
  color: var(--cta-text) !important;
  border: none !important;
  padding: var(--space-lg) var(--space-4xl) !important;
  border-radius: var(--radius-md) !important;
  font-size: var(--font-size-lg) !important;
  font-weight: var(--font-weight-bold) !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.operations-cta:hover {
  background: var(--cta-hover-bg) !important;
  transform: translateY(-1px) !important;
}

/* ========================================
   CALCULATOR PAGE COMPONENTS
======================================== */

.calculator-section {
  padding: var(--space-5xl) 0 !important;
  background: var(--bg) !important;
}

.calculator-container {
  max-width: 800px !important;
  margin: 0 auto !important;
}

.calculator-form {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-4xl) !important;
  box-shadow: var(--shadow) !important;
  margin-bottom: var(--space-4xl) !important;
}

.form-group {
  margin-bottom: var(--space-xl) !important;
}

.form-label {
  display: block !important;
  color: var(--text) !important;
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: var(--space-sm) !important;
}

.form-input,
.form-select {
  width: 100% !important;
  background: var(--bg) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-md) !important;
  padding: var(--space-md) !important;
  color: var(--text) !important;
  font-size: var(--font-size-base) !important;
  transition: all 0.2s ease !important;
}

.form-input:focus,
.form-select:focus {
  outline: none !important;
  border-color: var(--gold) !important;
  box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.2) !important;
}

.calculator-results {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-4xl) !important;
  box-shadow: var(--shadow) !important;
}

.results-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
  gap: var(--space-lg) !important;
  margin-top: var(--space-xl) !important;
}

.result-item {
  background: rgba(255, 215, 0, 0.05) !important;
  border: 1px solid rgba(255, 215, 0, 0.2) !important;
  border-radius: var(--radius-md) !important;
  padding: var(--space-lg) !important;
  text-align: center !important;
}

.result-value {
  color: var(--gold) !important;
  font-size: var(--font-size-2xl) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: var(--space-sm) !important;
}

.result-label {
  color: var(--text-muted) !important;
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-medium) !important;
}

/* ========================================
   ENHANCED CALCULATOR PARAMETERS SECTION
======================================== */

.calculator-parameters {
  padding: var(--space-5xl) 0 !important;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 0.9)) !important;
  position: relative !important;
}

.calculator-parameters::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: radial-gradient(circle at 50% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 70%) !important;
  pointer-events: none !important;
}

.calculator-parameters .section-header {
  text-align: center !important;
  margin-bottom: var(--space-5xl) !important;
  position: relative !important;
  z-index: 2 !important;
}

.calculator-parameters .section-header h2 {
  color: var(--gold) !important;
  font-size: var(--text-4xl) !important;
  font-weight: 800 !important;
  margin-bottom: var(--space-lg) !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
}

.calculator-parameters .section-header p {
  color: var(--text-secondary) !important;
  font-size: var(--text-lg) !important;
  margin-bottom: var(--space-xl) !important;
  max-width: 700px !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

.parameters-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr)) !important;
  gap: var(--space-4xl) !important;
  margin-bottom: var(--space-5xl) !important;
  position: relative !important;
  z-index: 2 !important;
}

.parameter-card {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.9)) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--space-4xl) !important;
  position: relative !important;
  overflow: hidden !important;
  transition: all 0.3s ease !important;
}

.parameter-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 4px !important;
}

.parameter-card.operational::before {
  background: linear-gradient(90deg, #4caf50, #8bc34a) !important;
}

.parameter-card.financial::before {
  background: linear-gradient(90deg, #2196f3, #03a9f4) !important;
}

.parameter-card.expansion::before {
  background: linear-gradient(90deg, #ff6b35, #f7931e) !important;
}

.parameter-card.conservative::before {
  background: linear-gradient(90deg, #9c27b0, #e91e63) !important;
}

.parameter-card:hover {
  transform: translateY(-8px) !important;
  box-shadow: 0 16px 48px rgba(212, 175, 55, 0.2) !important;
  border-color: var(--gold) !important;
}

.parameter-header {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-lg) !important;
  margin-bottom: var(--space-xl) !important;
  padding-bottom: var(--space-lg) !important;
  border-bottom: 2px solid rgba(212, 175, 55, 0.3) !important;
}

.parameter-icon {
  font-size: var(--text-4xl) !important;
  width: 80px !important;
  height: 80px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: rgba(212, 175, 55, 0.1) !important;
  border-radius: var(--radius-lg) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  flex-shrink: 0 !important;
}

.parameter-title h3 {
  color: var(--gold) !important;
  font-size: var(--text-2xl) !important;
  font-weight: 700 !important;
  margin: 0 0 var(--space-xs) 0 !important;
}

.parameter-subtitle {
  color: var(--text-secondary) !important;
  font-size: var(--text-sm) !important;
  font-style: italic !important;
}

.parameter-content {
  display: flex !important;
  flex-direction: column !important;
  gap: var(--space-lg) !important;
}

.parameter-item {
  padding: var(--space-lg) !important;
  background: rgba(255, 255, 255, 0.05) !important;
  border-radius: var(--radius-lg) !important;
  border-left: 4px solid rgba(212, 175, 55, 0.5) !important;
  transition: all 0.3s ease !important;
}

.parameter-item:hover {
  background: rgba(212, 175, 55, 0.1) !important;
  border-left-color: var(--gold) !important;
}

.param-label {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-sm) !important;
  margin-bottom: var(--space-sm) !important;
}

.param-icon {
  font-size: var(--text-lg) !important;
  width: 24px !important;
  text-align: center !important;
}

.param-label strong {
  color: var(--gold) !important;
  font-size: var(--text-md) !important;
  font-weight: 600 !important;
}

.param-value {
  color: var(--text) !important;
  font-size: var(--text-lg) !important;
  font-weight: 700 !important;
  margin-bottom: var(--space-xs) !important;
}

.param-description {
  color: var(--text-secondary) !important;
  font-size: var(--text-sm) !important;
  line-height: var(--line-height-relaxed) !important;
  margin: 0 !important;
}

/* Timeline Specific Styles */
.timeline-item {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-lg) !important;
  padding: var(--space-lg) !important;
  background: rgba(255, 255, 255, 0.05) !important;
  border-radius: var(--radius-lg) !important;
  border-left: 4px solid rgba(212, 175, 55, 0.5) !important;
  transition: all 0.3s ease !important;
}

.timeline-item:hover {
  background: rgba(212, 175, 55, 0.1) !important;
  border-left-color: var(--gold) !important;
}

.timeline-year {
  font-size: var(--text-2xl) !important;
  font-weight: 700 !important;
  color: var(--gold) !important;
  width: 80px !important;
  text-align: center !important;
  flex-shrink: 0 !important;
}

.timeline-details {
  flex: 1 !important;
}

.timeline-plants {
  color: var(--text) !important;
  font-size: var(--text-lg) !important;
  font-weight: 600 !important;
}

.timeline-hectares {
  color: var(--text-secondary) !important;
  font-size: var(--text-md) !important;
  margin-bottom: var(--space-xs) !important;
}

.timeline-description {
  color: var(--text-secondary) !important;
  font-size: var(--text-sm) !important;
  font-style: italic !important;
}

/* Conservative Approach Styles */
.conservative-item {
  display: flex !important;
  align-items: flex-start !important;
  gap: var(--space-md) !important;
  padding: var(--space-lg) !important;
  background: rgba(255, 255, 255, 0.05) !important;
  border-radius: var(--radius-lg) !important;
  border-left: 4px solid rgba(212, 175, 55, 0.5) !important;
  transition: all 0.3s ease !important;
}

.conservative-item:hover {
  background: rgba(212, 175, 55, 0.1) !important;
  border-left-color: var(--gold) !important;
}

.conservative-icon {
  font-size: var(--text-2xl) !important;
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: rgba(212, 175, 55, 0.1) !important;
  border-radius: var(--radius-md) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  flex-shrink: 0 !important;
}

.conservative-details {
  flex: 1 !important;
}

.conservative-details strong {
  color: var(--gold) !important;
  font-size: var(--text-md) !important;
  font-weight: 600 !important;
  display: block !important;
  margin-bottom: var(--space-xs) !important;
}

.conservative-details p {
  color: var(--text-secondary) !important;
  font-size: var(--text-sm) !important;
  line-height: var(--line-height-relaxed) !important;
  margin: 0 !important;
}

/* Formula Overview */
.formula-overview {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1)) !important;
  border: 2px solid var(--gold) !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--space-4xl) !important;
  position: relative !important;
  z-index: 2 !important;
  overflow: hidden !important;
}

.formula-overview::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: radial-gradient(circle at 30% 30%, rgba(212, 175, 55, 0.1) 0%, transparent 50%) !important;
  pointer-events: none !important;
}

.formula-overview h3 {
  color: var(--gold) !important;
  font-size: var(--text-2xl) !important;
  font-weight: 700 !important;
  text-align: center !important;
  margin-bottom: var(--space-xl) !important;
  position: relative !important;
  z-index: 2 !important;
}

.formula-steps {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: var(--space-lg) !important;
  flex-wrap: wrap !important;
  position: relative !important;
  z-index: 2 !important;
}

.formula-step {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  text-align: center !important;
  min-width: 180px !important;
}

.step-number {
  width: 40px !important;
  height: 40px !important;
  background: var(--gold) !important;
  color: var(--bg) !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-weight: 700 !important;
  font-size: var(--text-md) !important;
  margin-bottom: var(--space-md) !important;
}

.step-content strong {
  color: var(--gold) !important;
  font-size: var(--text-md) !important;
  font-weight: 600 !important;
  display: block !important;
  margin-bottom: var(--space-sm) !important;
}

.formula {
  color: var(--text) !important;
  font-size: var(--text-sm) !important;
  font-family: 'Courier New', monospace !important;
  background: rgba(0, 0, 0, 0.4) !important;
  padding: var(--space-sm) !important;
  border-radius: var(--radius-md) !important;
  border: 1px solid rgba(212, 175, 55, 0.3) !important;
}

.formula-arrow {
  color: var(--gold) !important;
  font-size: var(--text-2xl) !important;
  font-weight: 700 !important;
}

/* Responsive Design for Calculator Parameters */
@media (max-width: 1024px) {
  .parameters-grid {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)) !important;
    gap: var(--space-xl) !important;
  }

  .formula-steps {
    flex-direction: column !important;
    gap: var(--space-md) !important;
  }

  .formula-arrow {
    transform: rotate(90deg) !important;
    font-size: var(--text-xl) !important;
  }
}

@media (max-width: 768px) {
  .calculator-parameters {
    padding: var(--space-4xl) 0 !important;
  }

  .calculator-parameters .section-header h2 {
    font-size: var(--text-3xl) !important;
  }

  .parameters-grid {
    grid-template-columns: 1fr !important;
    gap: var(--space-lg) !important;
  }

  .parameter-card {
    padding: var(--space-xl) !important;
  }

  .parameter-header {
    flex-direction: column !important;
    text-align: center !important;
    gap: var(--space-md) !important;
  }

  .parameter-icon {
    width: 60px !important;
    height: 60px !important;
    font-size: var(--text-3xl) !important;
  }

  .timeline-item {
    flex-direction: column !important;
    text-align: center !important;
    gap: var(--space-md) !important;
  }

  .timeline-year {
    width: auto !important;
  }

  .formula-overview {
    padding: var(--space-xl) !important;
  }

  .formula-step {
    min-width: 150px !important;
  }
}

@media (max-width: 480px) {
  .parameter-card {
    padding: var(--space-lg) !important;
  }

  .parameter-icon {
    width: 50px !important;
    height: 50px !important;
    font-size: var(--text-2xl) !important;
  }

  .parameter-title h3 {
    font-size: var(--text-xl) !important;
  }

  .param-value {
    font-size: var(--text-md) !important;
  }

  .timeline-year {
    font-size: var(--text-xl) !important;
  }

  .conservative-icon {
    width: 32px !important;
    height: 32px !important;
    font-size: var(--text-lg) !important;
  }

  .formula-overview h3 {
    font-size: var(--text-xl) !important;
  }

  .step-number {
    width: 32px !important;
    height: 32px !important;
    font-size: var(--text-sm) !important;
  }

  .formula-step {
    min-width: 120px !important;
  }
}

/* ========================================
   ENHANCED CALCULATOR INTERFACE
======================================== */

.enhanced-calculator-wrapper {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 0.9)) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-xl) !important;
  overflow: hidden !important;
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.5) !important;
  position: relative !important;
}

.enhanced-calculator-wrapper::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: radial-gradient(circle at 50% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 70%) !important;
  pointer-events: none !important;
}

/* Calculator Header */
.calculator-header {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1)) !important;
  border-bottom: 2px solid rgba(212, 175, 55, 0.3) !important;
  padding: var(--space-4xl) !important;
  position: relative !important;
  z-index: 2 !important;
}

.header-content {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-lg) !important;
  margin-bottom: var(--space-xl) !important;
}

.header-icon {
  font-size: var(--text-4xl) !important;
  width: 80px !important;
  height: 80px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: rgba(212, 175, 55, 0.2) !important;
  border-radius: var(--radius-lg) !important;
  border: 2px solid var(--gold) !important;
  flex-shrink: 0 !important;
}

.header-text {
  flex: 1 !important;
}

.header-title {
  color: var(--gold) !important;
  font-size: var(--text-3xl) !important;
  font-weight: 800 !important;
  margin: 0 0 var(--space-sm) 0 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
}

.header-subtitle {
  color: var(--text-secondary) !important;
  font-size: var(--text-md) !important;
  line-height: var(--line-height-relaxed) !important;
  margin: 0 !important;
}

.header-stats {
  display: flex !important;
  gap: var(--space-xl) !important;
  justify-content: center !important;
}

.stat-item {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  text-align: center !important;
  padding: var(--space-lg) !important;
  background: rgba(0, 0, 0, 0.4) !important;
  border-radius: var(--radius-lg) !important;
  border: 1px solid rgba(212, 175, 55, 0.3) !important;
  min-width: 120px !important;
}

.stat-item .stat-icon {
  font-size: var(--text-2xl) !important;
  margin-bottom: var(--space-sm) !important;
}

.stat-item .stat-label {
  color: var(--text-secondary) !important;
  font-size: var(--text-xs) !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  margin-bottom: var(--space-xs) !important;
}

.stat-item .stat-value {
  color: var(--gold) !important;
  font-size: var(--text-lg) !important;
  font-weight: 700 !important;
}

/* Calculator Tabs */
.calculator-tabs {
  display: flex !important;
  background: rgba(0, 0, 0, 0.6) !important;
  border-bottom: 2px solid rgba(212, 175, 55, 0.3) !important;
  position: relative !important;
  z-index: 2 !important;
}

.calculator-tab {
  flex: 1 !important;
  display: flex !important;
  align-items: center !important;
  gap: var(--space-md) !important;
  padding: var(--space-xl) var(--space-lg) !important;
  background: transparent !important;
  border: none !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  border-bottom: 3px solid transparent !important;
}

.calculator-tab:hover {
  background: rgba(212, 175, 55, 0.1) !important;
}

.calculator-tab.active {
  background: rgba(212, 175, 55, 0.2) !important;
  border-bottom-color: var(--gold) !important;
}

.tab-icon {
  font-size: var(--text-2xl) !important;
  flex-shrink: 0 !important;
}

.tab-content {
  text-align: left !important;
}

.tab-label {
  color: var(--text) !important;
  font-size: var(--text-md) !important;
  font-weight: 600 !important;
  margin-bottom: var(--space-xs) !important;
}

.calculator-tab.active .tab-label {
  color: var(--gold) !important;
}

.tab-description {
  color: var(--text-secondary) !important;
  font-size: var(--text-xs) !important;
  line-height: var(--line-height-relaxed) !important;
}

/* Calculator Content */
.calculator-content {
  padding: var(--space-4xl) !important;
  position: relative !important;
  z-index: 2 !important;
}

.calculator-tab-content {
  display: flex !important;
  flex-direction: column !important;
  gap: var(--space-4xl) !important;
}

/* Stats Grid */
.stats-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
  gap: var(--space-xl) !important;
}

.stat-card {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.9)) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--space-xl) !important;
  position: relative !important;
  overflow: hidden !important;
  transition: all 0.3s ease !important;
}

.stat-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 4px !important;
}

.stat-card.shares::before {
  background: linear-gradient(90deg, #2196f3, #03a9f4) !important;
}

.stat-card.annual::before {
  background: linear-gradient(90deg, #4caf50, #8bc34a) !important;
}

.stat-card.monthly::before {
  background: linear-gradient(90deg, #ff6b35, #f7931e) !important;
}

.stat-card:hover {
  transform: translateY(-4px) !important;
  box-shadow: 0 12px 32px rgba(212, 175, 55, 0.2) !important;
  border-color: var(--gold) !important;
}

.stat-header {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-md) !important;
  margin-bottom: var(--space-lg) !important;
}

.stat-header .stat-icon {
  font-size: var(--text-2xl) !important;
  width: 50px !important;
  height: 50px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: rgba(212, 175, 55, 0.1) !important;
  border-radius: var(--radius-md) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  flex-shrink: 0 !important;
}

.stat-info {
  flex: 1 !important;
}

.stat-info .stat-label {
  color: var(--gold) !important;
  font-size: var(--text-lg) !important;
  font-weight: 600 !important;
  margin-bottom: var(--space-xs) !important;
}

.stat-info .stat-description {
  color: var(--text-secondary) !important;
  font-size: var(--text-sm) !important;
  line-height: var(--line-height-relaxed) !important;
}

.stat-card .stat-value {
  color: var(--text) !important;
  font-size: var(--text-3xl) !important;
  font-weight: 800 !important;
  margin-bottom: var(--space-sm) !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.stat-card .stat-percentage {
  color: var(--text-secondary) !important;
  font-size: var(--text-sm) !important;
  font-style: italic !important;
}

/* Year Selection Section */
.year-selection-section {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.9)) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--space-4xl) !important;
  position: relative !important;
  overflow: hidden !important;
}

.year-selection-section::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 4px !important;
  background: linear-gradient(90deg, #ff6b35, #f7931e) !important;
}

.year-selection-section .section-header {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-lg) !important;
  margin-bottom: var(--space-4xl) !important;
}

.year-selection-section .section-icon {
  font-size: var(--text-3xl) !important;
  width: 60px !important;
  height: 60px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: rgba(212, 175, 55, 0.1) !important;
  border-radius: var(--radius-lg) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  flex-shrink: 0 !important;
}

.year-selection-section .section-info {
  flex: 1 !important;
}

.year-selection-section .section-title {
  color: var(--gold) !important;
  font-size: var(--text-2xl) !important;
  font-weight: 700 !important;
  margin: 0 0 var(--space-sm) 0 !important;
}

.year-selection-section .section-description {
  color: var(--text-secondary) !important;
  font-size: var(--text-md) !important;
  line-height: var(--line-height-relaxed) !important;
  margin: 0 !important;
}

.year-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
  gap: var(--space-lg) !important;
}

.year-card {
  background: rgba(0, 0, 0, 0.6) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-lg) !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.year-card:hover {
  background: rgba(212, 175, 55, 0.1) !important;
  border-color: var(--gold) !important;
  transform: translateY(-2px) !important;
}

.year-card.active {
  background: rgba(212, 175, 55, 0.2) !important;
  border-color: var(--gold) !important;
  box-shadow: 0 8px 24px rgba(212, 175, 55, 0.3) !important;
}

.year-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: var(--space-md) !important;
}

.year-number {
  color: var(--gold) !important;
  font-size: var(--text-2xl) !important;
  font-weight: 800 !important;
}

.year-status {
  color: var(--gold) !important;
  font-size: var(--text-lg) !important;
  font-weight: 700 !important;
}

.year-details {
  display: flex !important;
  flex-direction: column !important;
  gap: var(--space-sm) !important;
  margin-bottom: var(--space-md) !important;
}

.year-stat {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-sm) !important;
}

.year-stat .stat-icon {
  font-size: var(--text-md) !important;
  width: 20px !important;
  text-align: center !important;
}

.year-stat .stat-text {
  color: var(--text) !important;
  font-size: var(--text-sm) !important;
  font-weight: 500 !important;
}

.year-production {
  padding-top: var(--space-sm) !important;
  border-top: 1px solid rgba(212, 175, 55, 0.3) !important;
  text-align: center !important;
}

.production-label {
  display: block !important;
  color: var(--text-secondary) !important;
  font-size: var(--text-xs) !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  margin-bottom: var(--space-xs) !important;
}

.production-value {
  color: var(--gold) !important;
  font-size: var(--text-md) !important;
  font-weight: 700 !important;
}

/* Advanced Parameters Section */
.advanced-parameters-section {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.9)) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-xl) !important;
  overflow: hidden !important;
  position: relative !important;
}

.advanced-parameters-section::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 4px !important;
  background: linear-gradient(90deg, #9c27b0, #e91e63) !important;
}

.advanced-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: var(--space-xl) var(--space-4xl) !important;
  border-bottom: 2px solid rgba(212, 175, 55, 0.3) !important;
}

.advanced-title {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-lg) !important;
}

.advanced-icon {
  font-size: var(--text-3xl) !important;
  width: 60px !important;
  height: 60px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: rgba(212, 175, 55, 0.1) !important;
  border-radius: var(--radius-lg) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  flex-shrink: 0 !important;
}

.advanced-info {
  flex: 1 !important;
}

.advanced-label {
  color: var(--gold) !important;
  font-size: var(--text-2xl) !important;
  font-weight: 700 !important;
  margin: 0 0 var(--space-sm) 0 !important;
}

.advanced-description {
  color: var(--text-secondary) !important;
  font-size: var(--text-md) !important;
  line-height: var(--line-height-relaxed) !important;
  margin: 0 !important;
}

.advanced-toggle {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-sm) !important;
  background: rgba(212, 175, 55, 0.1) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-md) var(--space-lg) !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
}

.advanced-toggle:hover {
  background: rgba(212, 175, 55, 0.2) !important;
  border-color: var(--gold) !important;
}

.advanced-toggle.active {
  background: rgba(212, 175, 55, 0.3) !important;
  border-color: var(--gold) !important;
}

.toggle-icon {
  color: var(--gold) !important;
  font-size: var(--text-md) !important;
  font-weight: 700 !important;
}

.toggle-text {
  color: var(--text) !important;
  font-size: var(--text-sm) !important;
  font-weight: 600 !important;
}

.advanced-content {
  padding: var(--space-4xl) !important;
}

.parameters-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
  gap: var(--space-xl) !important;
  margin-bottom: var(--space-4xl) !important;
}

.parameter-input-group {
  background: rgba(0, 0, 0, 0.4) !important;
  border: 1px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-lg) !important;
  transition: all 0.3s ease !important;
}

.parameter-input-group:hover {
  background: rgba(212, 175, 55, 0.1) !important;
  border-color: rgba(212, 175, 55, 0.5) !important;
}

.input-header {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-md) !important;
  margin-bottom: var(--space-lg) !important;
}

.input-icon {
  font-size: var(--text-xl) !important;
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: rgba(212, 175, 55, 0.1) !important;
  border-radius: var(--radius-md) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  flex-shrink: 0 !important;
}

.input-info {
  flex: 1 !important;
}

.input-label {
  color: var(--gold) !important;
  font-size: var(--text-md) !important;
  font-weight: 600 !important;
  display: block !important;
  margin-bottom: var(--space-xs) !important;
}

.input-description {
  color: var(--text-secondary) !important;
  font-size: var(--text-sm) !important;
  line-height: var(--line-height-relaxed) !important;
}

.input-wrapper {
  position: relative !important;
  display: flex !important;
  align-items: center !important;
}

.parameter-input {
  width: 100% !important;
  background: rgba(0, 0, 0, 0.6) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-md) !important;
  padding: var(--space-md) var(--space-lg) !important;
  color: var(--text) !important;
  font-size: var(--text-md) !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

.parameter-input:focus {
  outline: none !important;
  border-color: var(--gold) !important;
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.2) !important;
  background: rgba(0, 0, 0, 0.8) !important;
}

.input-prefix,
.input-suffix {
  position: absolute !important;
  color: var(--text-secondary) !important;
  font-size: var(--text-md) !important;
  font-weight: 600 !important;
  pointer-events: none !important;
}

.input-prefix {
  left: var(--space-md) !important;
}

.input-suffix {
  right: var(--space-md) !important;
}

.parameter-input:has(+ .input-suffix) {
  padding-right: var(--space-4xl) !important;
}

.input-wrapper:has(.input-prefix) .parameter-input {
  padding-left: var(--space-4xl) !important;
}

/* Parameter Impact Summary */
.parameter-impact {
  background: rgba(212, 175, 55, 0.1) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
}

.impact-title {
  color: var(--gold) !important;
  font-size: var(--text-lg) !important;
  font-weight: 700 !important;
  margin: 0 0 var(--space-lg) 0 !important;
  text-align: center !important;
}

.impact-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
  gap: var(--space-md) !important;
}

.impact-item {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: var(--space-md) !important;
  background: rgba(0, 0, 0, 0.4) !important;
  border-radius: var(--radius-md) !important;
  border: 1px solid rgba(212, 175, 55, 0.3) !important;
}

.impact-label {
  color: var(--text-secondary) !important;
  font-size: var(--text-sm) !important;
  font-weight: 500 !important;
}

.impact-value {
  color: var(--gold) !important;
  font-size: var(--text-md) !important;
  font-weight: 700 !important;
}

/* Responsive Design for Enhanced Calculator */
@media (max-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    gap: var(--space-lg) !important;
  }

  .year-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)) !important;
  }

  .parameters-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    gap: var(--space-lg) !important;
  }

  .header-stats {
    gap: var(--space-lg) !important;
  }

  .stat-item {
    min-width: 100px !important;
  }
}

@media (max-width: 768px) {
  .calculator-header {
    padding: var(--space-xl) !important;
  }

  .header-content {
    flex-direction: column !important;
    text-align: center !important;
    gap: var(--space-md) !important;
  }

  .header-icon {
    width: 60px !important;
    height: 60px !important;
    font-size: var(--text-3xl) !important;
  }

  .header-title {
    font-size: var(--text-2xl) !important;
  }

  .header-stats {
    flex-direction: column !important;
    gap: var(--space-md) !important;
  }

  .calculator-tabs {
    flex-direction: column !important;
  }

  .calculator-tab {
    justify-content: center !important;
    text-align: center !important;
  }

  .calculator-content {
    padding: var(--space-xl) !important;
  }

  .stats-grid {
    grid-template-columns: 1fr !important;
    gap: var(--space-md) !important;
  }

  .year-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)) !important;
    gap: var(--space-md) !important;
  }

  .year-card {
    padding: var(--space-md) !important;
  }

  .advanced-header {
    flex-direction: column !important;
    gap: var(--space-lg) !important;
    text-align: center !important;
    padding: var(--space-lg) !important;
  }

  .advanced-title {
    flex-direction: column !important;
    text-align: center !important;
    gap: var(--space-md) !important;
  }

  .parameters-grid {
    grid-template-columns: 1fr !important;
    gap: var(--space-md) !important;
  }

  .impact-grid {
    grid-template-columns: 1fr !important;
    gap: var(--space-sm) !important;
  }
}

@media (max-width: 480px) {
  .calculator-header {
    padding: var(--space-lg) !important;
  }

  .header-icon {
    width: 50px !important;
    height: 50px !important;
    font-size: var(--text-2xl) !important;
  }

  .header-title {
    font-size: var(--text-xl) !important;
  }

  .calculator-content {
    padding: var(--space-lg) !important;
  }

  .stat-card {
    padding: var(--space-lg) !important;
  }

  .stat-card .stat-value {
    font-size: var(--text-2xl) !important;
  }

  .year-card {
    padding: var(--space-sm) !important;
  }

  .year-number {
    font-size: var(--text-xl) !important;
  }

  .advanced-content {
    padding: var(--space-lg) !important;
  }

  .parameter-input-group {
    padding: var(--space-md) !important;
  }

  .input-header {
    flex-direction: column !important;
    text-align: center !important;
    gap: var(--space-sm) !important;
  }

  .input-icon {
    width: 32px !important;
    height: 32px !important;
    font-size: var(--text-lg) !important;
  }
}

/* ========================================
   ENHANCED SCENARIOS SECTION
======================================== */

.enhanced-scenarios {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 0.9)) !important;
  padding: var(--space-6xl) 0 !important;
  position: relative !important;
  overflow: hidden !important;
}

.enhanced-scenarios::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: radial-gradient(circle at 30% 70%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 30%, rgba(212, 175, 55, 0.05) 0%, transparent 50%) !important;
  pointer-events: none !important;
}

/* Scenarios Header */
.scenarios-header {
  text-align: center !important;
  margin-bottom: var(--space-6xl) !important;
  position: relative !important;
  z-index: 2 !important;
}

.scenarios-header .header-content {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: var(--space-xl) !important;
  margin-bottom: var(--space-4xl) !important;
}

.scenarios-header .header-icon {
  font-size: var(--text-5xl) !important;
  width: 100px !important;
  height: 100px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1)) !important;
  border-radius: var(--radius-xl) !important;
  border: 3px solid var(--gold) !important;
  flex-shrink: 0 !important;
}

.scenarios-header .header-text {
  text-align: left !important;
}

.scenarios-title {
  color: var(--gold) !important;
  font-size: var(--text-4xl) !important;
  font-weight: 800 !important;
  margin: 0 0 var(--space-md) 0 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
}

.scenarios-subtitle {
  color: var(--text-secondary) !important;
  font-size: var(--text-lg) !important;
  line-height: var(--line-height-relaxed) !important;
  margin: 0 !important;
  max-width: 600px !important;
}

.scenarios-stats {
  display: flex !important;
  justify-content: center !important;
  gap: var(--space-4xl) !important;
}

.stat-highlight {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  text-align: center !important;
  padding: var(--space-lg) !important;
  background: rgba(0, 0, 0, 0.6) !important;
  border-radius: var(--radius-lg) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  min-width: 140px !important;
}

.stat-highlight .stat-icon {
  font-size: var(--text-2xl) !important;
  margin-bottom: var(--space-sm) !important;
}

.stat-highlight .stat-label {
  color: var(--text-secondary) !important;
  font-size: var(--text-xs) !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  margin-bottom: var(--space-xs) !important;
}

.stat-highlight .stat-value {
  color: var(--gold) !important;
  font-size: var(--text-md) !important;
  font-weight: 700 !important;
}

/* Enhanced Scenarios Grid */
.enhanced-scenarios-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)) !important;
  gap: var(--space-4xl) !important;
  margin-bottom: var(--space-6xl) !important;
  position: relative !important;
  z-index: 2 !important;
}

.enhanced-scenario-card {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.8)) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-2xl) !important;
  padding: var(--space-4xl) !important;
  position: relative !important;
  overflow: hidden !important;
  transition: all 0.4s ease !important;
}

.enhanced-scenario-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 6px !important;
}

.enhanced-scenario-card.conservative::before {
  background: linear-gradient(90deg, #2196f3, #03a9f4) !important;
}

.enhanced-scenario-card.growth::before {
  background: linear-gradient(90deg, #4caf50, #8bc34a) !important;
}

.enhanced-scenario-card.serious::before {
  background: linear-gradient(90deg, #9c27b0, #e91e63) !important;
}

.enhanced-scenario-card:hover {
  transform: translateY(-8px) !important;
  box-shadow: 0 20px 60px rgba(212, 175, 55, 0.3) !important;
  border-color: var(--gold) !important;
}

.enhanced-scenario-card.featured {
  transform: scale(1.05) !important;
  border-color: var(--gold) !important;
  box-shadow: 0 16px 48px rgba(212, 175, 55, 0.4) !important;
}

.enhanced-scenario-card.featured:hover {
  transform: scale(1.05) translateY(-8px) !important;
}

/* Scenario Badge */
.scenario-badge {
  position: absolute !important;
  top: var(--space-lg) !important;
  right: var(--space-lg) !important;
  display: flex !important;
  align-items: center !important;
  gap: var(--space-sm) !important;
  background: rgba(212, 175, 55, 0.2) !important;
  border: 2px solid var(--gold) !important;
  border-radius: var(--radius-full) !important;
  padding: var(--space-sm) var(--space-md) !important;
}

.badge-icon {
  font-size: var(--text-md) !important;
}

.badge-text {
  color: var(--gold) !important;
  font-size: var(--text-xs) !important;
  font-weight: 700 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

/* Featured Ribbon */
.featured-ribbon {
  position: absolute !important;
  top: var(--space-xl) !important;
  left: -var(--space-lg) !important;
  background: linear-gradient(45deg, #ff6b35, #f7931e) !important;
  color: white !important;
  font-size: var(--text-xs) !important;
  font-weight: 700 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  padding: var(--space-sm) var(--space-xl) !important;
  transform: rotate(-45deg) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
}

/* Scenario Main Content */
.scenario-main {
  margin-bottom: var(--space-4xl) !important;
}

.scenario-title h3 {
  color: var(--gold) !important;
  font-size: var(--text-2xl) !important;
  font-weight: 700 !important;
  margin: 0 0 var(--space-sm) 0 !important;
}

.scenario-subtitle {
  color: var(--text-secondary) !important;
  font-size: var(--text-md) !important;
  font-style: italic !important;
  margin-bottom: var(--space-xl) !important;
}

.investment-amount {
  background: rgba(212, 175, 55, 0.1) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  text-align: center !important;
}

.amount-label {
  color: var(--text-secondary) !important;
  font-size: var(--text-sm) !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  margin-bottom: var(--space-sm) !important;
}

.amount-value {
  color: var(--gold) !important;
  font-size: var(--text-4xl) !important;
  font-weight: 800 !important;
  margin-bottom: var(--space-sm) !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.amount-details {
  color: var(--text) !important;
  font-size: var(--text-md) !important;
  font-weight: 500 !important;
}

/* Scenario Projections */
.scenario-projections {
  margin-bottom: var(--space-4xl) !important;
}

.projection-timeline {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  margin-bottom: var(--space-xl) !important;
  position: relative !important;
}

.timeline-item {
  background: rgba(0, 0, 0, 0.6) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-lg) !important;
  text-align: center !important;
  flex: 1 !important;
  max-width: 140px !important;
}

.timeline-item.start {
  border-color: #2196f3 !important;
}

.timeline-item.end {
  border-color: #4caf50 !important;
}

.timeline-year {
  color: var(--gold) !important;
  font-size: var(--text-lg) !important;
  font-weight: 700 !important;
  margin-bottom: var(--space-xs) !important;
}

.timeline-plants {
  color: var(--text-secondary) !important;
  font-size: var(--text-xs) !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  margin-bottom: var(--space-sm) !important;
}

.timeline-dividend {
  color: var(--text) !important;
  font-size: var(--text-sm) !important;
  font-weight: 600 !important;
}

.timeline-connector {
  flex: 1 !important;
  height: 2px !important;
  background: linear-gradient(90deg, #2196f3, #4caf50) !important;
  margin: 0 var(--space-md) !important;
  position: relative !important;
}

.timeline-connector::after {
  content: '→' !important;
  position: absolute !important;
  right: -8px !important;
  top: -8px !important;
  color: var(--gold) !important;
  font-size: var(--text-md) !important;
  font-weight: 700 !important;
}

.scenario-highlights {
  display: flex !important;
  gap: var(--space-lg) !important;
  justify-content: center !important;
}

.highlight-item {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-sm) !important;
  background: rgba(0, 0, 0, 0.4) !important;
  border: 1px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-md) !important;
  padding: var(--space-md) var(--space-lg) !important;
}

.highlight-icon {
  font-size: var(--text-md) !important;
}

.highlight-text {
  color: var(--text) !important;
  font-size: var(--text-sm) !important;
  font-weight: 600 !important;
}

/* Scenario Footer */
.scenario-footer {
  border-top: 2px solid rgba(212, 175, 55, 0.3) !important;
  padding-top: var(--space-xl) !important;
  text-align: center !important;
}

.roi-indicator {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: var(--space-md) !important;
}

.roi-label {
  color: var(--text-secondary) !important;
  font-size: var(--text-md) !important;
  font-weight: 500 !important;
}

.roi-value {
  color: var(--gold) !important;
  font-size: var(--text-xl) !important;
  font-weight: 800 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

/* Scenarios Comparison */
.scenarios-comparison {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.8)) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-2xl) !important;
  padding: var(--space-4xl) !important;
  margin-bottom: var(--space-6xl) !important;
  position: relative !important;
  z-index: 2 !important;
}

.scenarios-comparison::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 6px !important;
  background: linear-gradient(90deg, #2196f3, #4caf50, #9c27b0) !important;
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0 !important;
}

.comparison-title {
  color: var(--gold) !important;
  font-size: var(--text-2xl) !important;
  font-weight: 700 !important;
  text-align: center !important;
  margin: 0 0 var(--space-4xl) 0 !important;
}

.comparison-table {
  display: grid !important;
  grid-template-columns: repeat(6, 1fr) !important;
  gap: 2px !important;
  background: rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-lg) !important;
  overflow: hidden !important;
}

.comparison-header {
  display: contents !important;
}

.comparison-header .comparison-cell {
  background: rgba(212, 175, 55, 0.2) !important;
  color: var(--gold) !important;
  font-weight: 700 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  font-size: var(--text-sm) !important;
}

.comparison-row {
  display: contents !important;
}

.comparison-cell {
  background: rgba(0, 0, 0, 0.8) !important;
  color: var(--text) !important;
  padding: var(--space-lg) var(--space-md) !important;
  text-align: center !important;
  font-size: var(--text-sm) !important;
  font-weight: 500 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.comparison-row.conservative .comparison-cell:first-child {
  color: #2196f3 !important;
  font-weight: 700 !important;
}

.comparison-row.growth .comparison-cell:first-child {
  color: #4caf50 !important;
  font-weight: 700 !important;
}

.comparison-row.serious .comparison-cell:first-child {
  color: #9c27b0 !important;
  font-weight: 700 !important;
}

/* Enhanced Disclaimer */
.enhanced-disclaimer {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(212, 175, 55, 0.05)) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-2xl) !important;
  padding: var(--space-4xl) !important;
  position: relative !important;
  z-index: 2 !important;
}

.disclaimer-header {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-lg) !important;
  margin-bottom: var(--space-xl) !important;
}

.disclaimer-icon {
  font-size: var(--text-3xl) !important;
  width: 60px !important;
  height: 60px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: rgba(255, 193, 7, 0.2) !important;
  border-radius: var(--radius-lg) !important;
  border: 2px solid #ffc107 !important;
  flex-shrink: 0 !important;
}

.disclaimer-title {
  color: #ffc107 !important;
  font-size: var(--text-xl) !important;
  font-weight: 700 !important;
  margin: 0 !important;
}

.disclaimer-content {
  color: var(--text) !important;
  font-size: var(--text-md) !important;
  line-height: var(--line-height-relaxed) !important;
}

.disclaimer-content p {
  margin-bottom: var(--space-lg) !important;
}

.disclaimer-content strong {
  color: var(--gold) !important;
  font-weight: 700 !important;
}

.risk-factors {
  background: rgba(0, 0, 0, 0.4) !important;
  border: 1px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  margin: var(--space-xl) 0 !important;
}

.risk-factors h5 {
  color: var(--gold) !important;
  font-size: var(--text-md) !important;
  font-weight: 700 !important;
  margin: 0 0 var(--space-md) 0 !important;
}

.risk-factors ul {
  list-style: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.risk-factors li {
  color: var(--text-secondary) !important;
  font-size: var(--text-sm) !important;
  line-height: var(--line-height-relaxed) !important;
  margin-bottom: var(--space-lg) !important;
  padding-left: var(--space-xl) !important;
  position: relative !important;
}

.risk-factors li::before {
  content: '⚠️' !important;
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
  font-size: var(--text-sm) !important;
}

.disclaimer-footer {
  background: rgba(212, 175, 55, 0.1) !important;
  border: 1px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-lg) !important;
  margin-top: var(--space-xl) !important;
  text-align: center !important;
  font-style: italic !important;
}

/* Responsive Design for Enhanced Scenarios */
@media (max-width: 1024px) {
  .enhanced-scenarios-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
    gap: var(--space-xl) !important;
  }

  .scenarios-stats {
    gap: var(--space-xl) !important;
  }

  .stat-highlight {
    min-width: 120px !important;
  }

  .comparison-table {
    grid-template-columns: repeat(3, 1fr) !important;
  }

  .comparison-table .comparison-cell:nth-child(n+4) {
    display: none !important;
  }
}

@media (max-width: 768px) {
  .enhanced-scenarios {
    padding: var(--space-4xl) 0 !important;
  }

  .scenarios-header .header-content {
    flex-direction: column !important;
    text-align: center !important;
    gap: var(--space-lg) !important;
  }

  .scenarios-header .header-text {
    text-align: center !important;
  }

  .scenarios-title {
    font-size: var(--text-3xl) !important;
  }

  .scenarios-stats {
    flex-direction: column !important;
    gap: var(--space-lg) !important;
  }

  .enhanced-scenarios-grid {
    grid-template-columns: 1fr !important;
    gap: var(--space-xl) !important;
  }

  .enhanced-scenario-card {
    padding: var(--space-xl) !important;
  }

  .enhanced-scenario-card.featured {
    transform: none !important;
  }

  .enhanced-scenario-card.featured:hover {
    transform: translateY(-4px) !important;
  }

  .projection-timeline {
    flex-direction: column !important;
    gap: var(--space-lg) !important;
  }

  .timeline-connector {
    width: 2px !important;
    height: var(--space-lg) !important;
    margin: 0 !important;
    background: linear-gradient(180deg, #2196f3, #4caf50) !important;
  }

  .timeline-connector::after {
    content: '↓' !important;
    right: -8px !important;
    top: auto !important;
    bottom: -8px !important;
  }

  .scenario-highlights {
    flex-direction: column !important;
    gap: var(--space-md) !important;
  }

  .comparison-table {
    grid-template-columns: 1fr 1fr !important;
    font-size: var(--text-xs) !important;
  }

  .comparison-table .comparison-cell:nth-child(n+3) {
    display: none !important;
  }

  .disclaimer-header {
    flex-direction: column !important;
    text-align: center !important;
    gap: var(--space-md) !important;
  }
}

@media (max-width: 480px) {
  .enhanced-scenarios {
    padding: var(--space-xl) 0 !important;
  }

  .scenarios-header .header-icon {
    width: 80px !important;
    height: 80px !important;
    font-size: var(--text-4xl) !important;
  }

  .scenarios-title {
    font-size: var(--text-2xl) !important;
  }

  .enhanced-scenario-card {
    padding: var(--space-lg) !important;
  }

  .amount-value {
    font-size: var(--text-3xl) !important;
  }

  .timeline-item {
    padding: var(--space-md) !important;
    max-width: none !important;
  }

  .comparison-table {
    grid-template-columns: 1fr !important;
  }

  .comparison-table .comparison-cell {
    padding: var(--space-md) var(--space-sm) !important;
  }

  .disclaimer-icon {
    width: 50px !important;
    height: 50px !important;
    font-size: var(--text-2xl) !important;
  }

  .disclaimer-title {
    font-size: var(--text-lg) !important;
  }
}

/* ========================================
   ENHANCED NEXT STEPS SECTION
======================================== */

.enhanced-next-steps {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 0.9)) !important;
  padding: var(--space-6xl) 0 !important;
  position: relative !important;
  overflow: hidden !important;
}

.enhanced-next-steps::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: radial-gradient(circle at 50% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 70%) !important;
  pointer-events: none !important;
}

/* Next Steps Header */
.next-steps-header {
  text-align: center !important;
  margin-bottom: var(--space-6xl) !important;
  position: relative !important;
  z-index: 2 !important;
}

.next-steps-header .header-content {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: var(--space-xl) !important;
  margin-bottom: var(--space-4xl) !important;
}

.next-steps-header .header-icon {
  font-size: var(--text-5xl) !important;
  width: 100px !important;
  height: 100px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1)) !important;
  border-radius: var(--radius-xl) !important;
  border: 3px solid var(--gold) !important;
  flex-shrink: 0 !important;
}

.next-steps-header .header-text {
  text-align: left !important;
}

.next-steps-title {
  color: var(--gold) !important;
  font-size: var(--text-4xl) !important;
  font-weight: 800 !important;
  margin: 0 0 var(--space-md) 0 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
}

.next-steps-subtitle {
  color: var(--text-secondary) !important;
  font-size: var(--text-lg) !important;
  line-height: var(--line-height-relaxed) !important;
  margin: 0 !important;
  max-width: 600px !important;
}

.confidence-indicators {
  display: flex !important;
  justify-content: center !important;
  gap: var(--space-4xl) !important;
}

.confidence-item {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-sm) !important;
  background: rgba(0, 0, 0, 0.6) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-md) var(--space-lg) !important;
}

.confidence-icon {
  font-size: var(--text-lg) !important;
}

.confidence-text {
  color: var(--text) !important;
  font-size: var(--text-sm) !important;
  font-weight: 600 !important;
}

/* Enhanced Next Steps Grid */
.enhanced-next-steps-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)) !important;
  gap: var(--space-4xl) !important;
  margin-bottom: var(--space-6xl) !important;
  position: relative !important;
  z-index: 2 !important;
}

.next-step-card-wrapper {
  position: relative !important;
}

.next-step-card-wrapper.featured {
  transform: scale(1.05) !important;
}

.featured-badge {
  position: absolute !important;
  top: -var(--space-md) !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  background: linear-gradient(45deg, #ff6b35, #f7931e) !important;
  color: white !important;
  font-size: var(--text-xs) !important;
  font-weight: 700 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  padding: var(--space-sm) var(--space-lg) !important;
  border-radius: var(--radius-full) !important;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.4) !important;
  z-index: 3 !important;
}

.enhanced-next-step-card {
  width: 100% !important;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.8)) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-2xl) !important;
  padding: var(--space-4xl) !important;
  cursor: pointer !important;
  transition: all 0.4s ease !important;
  position: relative !important;
  overflow: hidden !important;
  text-align: left !important;
}

.enhanced-next-step-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 6px !important;
}

.enhanced-next-step-card.explore::before {
  background: linear-gradient(90deg, #2196f3, #03a9f4) !important;
}

.enhanced-next-step-card.review::before {
  background: linear-gradient(90deg, #4caf50, #8bc34a) !important;
}

.enhanced-next-step-card.invest::before {
  background: linear-gradient(90deg, #ff6b35, #f7931e) !important;
}

.enhanced-next-step-card:hover {
  transform: translateY(-8px) !important;
  box-shadow: 0 20px 60px rgba(212, 175, 55, 0.3) !important;
  border-color: var(--gold) !important;
}

.enhanced-next-step-card.featured {
  border-color: var(--gold) !important;
  box-shadow: 0 16px 48px rgba(212, 175, 55, 0.4) !important;
}

.enhanced-next-step-card.featured:hover {
  transform: translateY(-8px) !important;
  box-shadow: 0 24px 72px rgba(212, 175, 55, 0.5) !important;
}

/* Card Header */
.card-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: var(--space-xl) !important;
}

.card-header .step-icon {
  font-size: var(--text-3xl) !important;
  width: 70px !important;
  height: 70px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: rgba(212, 175, 55, 0.1) !important;
  border-radius: var(--radius-lg) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
}

.step-badge {
  background: rgba(212, 175, 55, 0.2) !important;
  border: 2px solid var(--gold) !important;
  border-radius: var(--radius-full) !important;
  padding: var(--space-sm) var(--space-md) !important;
  color: var(--gold) !important;
  font-size: var(--text-xs) !important;
  font-weight: 700 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

/* Card Content */
.card-content {
  margin-bottom: var(--space-4xl) !important;
}

.card-content h3 {
  color: var(--gold) !important;
  font-size: var(--text-xl) !important;
  font-weight: 700 !important;
  margin: 0 0 var(--space-md) 0 !important;
}

.card-content p {
  color: var(--text-secondary) !important;
  font-size: var(--text-md) !important;
  line-height: var(--line-height-relaxed) !important;
  margin: 0 0 var(--space-lg) 0 !important;
}

.card-features {
  display: flex !important;
  flex-direction: column !important;
  gap: var(--space-sm) !important;
}

.feature {
  color: var(--text) !important;
  font-size: var(--text-sm) !important;
  font-weight: 500 !important;
  padding-left: var(--space-md) !important;
}

/* Card Footer */
.card-footer {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding-top: var(--space-xl) !important;
  border-top: 2px solid rgba(212, 175, 55, 0.3) !important;
}

.action-text {
  color: var(--gold) !important;
  font-size: var(--text-md) !important;
  font-weight: 700 !important;
}

.action-arrow {
  color: var(--gold) !important;
  font-size: var(--text-lg) !important;
  font-weight: 700 !important;
  transition: transform 0.3s ease !important;
}

.enhanced-next-step-card:hover .action-arrow {
  transform: translateX(4px) !important;
}

/* Next Steps Footer */
.next-steps-footer {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(212, 175, 55, 0.05)) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-2xl) !important;
  padding: var(--space-4xl) !important;
  position: relative !important;
  z-index: 2 !important;
}

.next-steps-footer .footer-content {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  gap: var(--space-4xl) !important;
  align-items: center !important;
}

.footer-stats {
  display: flex !important;
  justify-content: space-around !important;
  gap: var(--space-xl) !important;
}

.footer-stats .stat-item {
  text-align: center !important;
}

.footer-stats .stat-value {
  color: var(--gold) !important;
  font-size: var(--text-2xl) !important;
  font-weight: 800 !important;
  display: block !important;
  margin-bottom: var(--space-xs) !important;
}

.footer-stats .stat-label {
  color: var(--text-secondary) !important;
  font-size: var(--text-xs) !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.footer-cta {
  text-align: center !important;
}

.cta-text {
  color: var(--text) !important;
  font-size: var(--text-lg) !important;
  font-weight: 600 !important;
  margin: 0 0 var(--space-lg) 0 !important;
  line-height: var(--line-height-relaxed) !important;
}

.urgency-indicator {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: var(--space-sm) !important;
  background: rgba(255, 193, 7, 0.1) !important;
  border: 2px solid #ffc107 !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-md) var(--space-lg) !important;
}

.urgency-icon {
  font-size: var(--text-md) !important;
}

.urgency-text {
  color: #ffc107 !important;
  font-size: var(--text-sm) !important;
  font-weight: 600 !important;
}

/* Responsive Design for Enhanced Next Steps */
@media (max-width: 1024px) {
  .enhanced-next-steps-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
    gap: var(--space-xl) !important;
  }

  .confidence-indicators {
    gap: var(--space-xl) !important;
  }

  .next-steps-footer .footer-content {
    gap: var(--space-xl) !important;
  }

  .footer-stats {
    gap: var(--space-md) !important;
  }
}

@media (max-width: 768px) {
  .enhanced-next-steps {
    padding: var(--space-4xl) 0 !important;
  }

  .next-steps-header .header-content {
    flex-direction: column !important;
    text-align: center !important;
    gap: var(--space-lg) !important;
  }

  .next-steps-header .header-text {
    text-align: center !important;
  }

  .next-steps-title {
    font-size: var(--text-3xl) !important;
  }

  .confidence-indicators {
    flex-direction: column !important;
    gap: var(--space-lg) !important;
  }

  .enhanced-next-steps-grid {
    grid-template-columns: 1fr !important;
    gap: var(--space-xl) !important;
  }

  .next-step-card-wrapper.featured {
    transform: none !important;
  }

  .enhanced-next-step-card {
    padding: var(--space-xl) !important;
  }

  .next-steps-footer .footer-content {
    grid-template-columns: 1fr !important;
    gap: var(--space-xl) !important;
    text-align: center !important;
  }

  .footer-stats {
    justify-content: center !important;
    flex-wrap: wrap !important;
  }
}

@media (max-width: 480px) {
  .enhanced-next-steps {
    padding: var(--space-xl) 0 !important;
  }

  .next-steps-header .header-icon {
    width: 80px !important;
    height: 80px !important;
    font-size: var(--text-4xl) !important;
  }

  .next-steps-title {
    font-size: var(--text-2xl) !important;
  }

  .enhanced-next-step-card {
    padding: var(--space-lg) !important;
  }

  .card-header .step-icon {
    width: 60px !important;
    height: 60px !important;
    font-size: var(--text-2xl) !important;
  }

  .card-content h3 {
    font-size: var(--text-lg) !important;
  }

  .footer-stats {
    flex-direction: column !important;
    gap: var(--space-lg) !important;
  }

  .footer-stats .stat-value {
    font-size: var(--text-xl) !important;
  }

  .cta-text {
    font-size: var(--text-md) !important;
  }
}

/* ========================================
   TABBED CALCULATOR PAGE
======================================== */

.calculator-page-tabbed {
  background: var(--bg-primary) !important;
  min-height: 100vh !important;
}

/* Tabbed Page Header */
.tabbed-page-header {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 0.9)) !important;
  padding: var(--space-4xl) 0 var(--space-xl) 0 !important;
  border-bottom: 2px solid rgba(212, 175, 55, 0.3) !important;
}

.tabbed-page-header .header-content {
  display: grid !important;
  grid-template-columns: 1fr auto !important;
  gap: var(--space-4xl) !important;
  align-items: center !important;
  margin-bottom: var(--space-4xl) !important;
}

.tabbed-page-header .header-text {
  text-align: left !important;
}

.tabbed-page-header .page-title {
  color: var(--gold) !important;
  font-size: var(--text-4xl) !important;
  font-weight: 800 !important;
  margin: 0 0 var(--space-md) 0 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
}

.tabbed-page-header .page-subtitle {
  color: var(--text-secondary) !important;
  font-size: var(--text-lg) !important;
  line-height: var(--line-height-relaxed) !important;
  margin: 0 !important;
  max-width: 600px !important;
}

.tabbed-page-header .header-stats {
  display: flex !important;
  gap: var(--space-xl) !important;
}

.tabbed-page-header .stat-item {
  text-align: center !important;
  background: rgba(0, 0, 0, 0.6) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-lg) !important;
  min-width: 120px !important;
}

.tabbed-page-header .stat-value {
  color: var(--gold) !important;
  font-size: var(--text-xl) !important;
  font-weight: 800 !important;
  display: block !important;
  margin-bottom: var(--space-xs) !important;
}

.tabbed-page-header .stat-label {
  color: var(--text-secondary) !important;
  font-size: var(--text-xs) !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

/* Tab Navigation */
.tab-navigation {
  display: flex !important;
  gap: var(--space-sm) !important;
  background: rgba(0, 0, 0, 0.8) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--space-md) !important;
  overflow-x: auto !important;
}

.tab-button {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-sm) !important;
  background: transparent !important;
  border: 2px solid transparent !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-md) var(--space-lg) !important;
  color: var(--text-secondary) !important;
  font-size: var(--text-sm) !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  white-space: nowrap !important;
}

.tab-button:hover {
  background: rgba(212, 175, 55, 0.1) !important;
  border-color: rgba(212, 175, 55, 0.3) !important;
  color: var(--text) !important;
}

.tab-button.active {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1)) !important;
  border-color: var(--gold) !important;
  color: var(--gold) !important;
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3) !important;
}

.tab-icon {
  font-size: var(--text-md) !important;
}

.tab-label {
  font-weight: 700 !important;
}

/* Tab Content */
.tab-content-section {
  padding: var(--space-6xl) 0 !important;
}

.tab-content {
  position: relative !important;
}

.tab-panel {
  animation: fadeIn 0.4s ease-in-out !important;
}

@keyframes fadeIn {
  from {
    opacity: 0 !important;
    transform: translateY(20px) !important;
  }
  to {
    opacity: 1 !important;
    transform: translateY(0) !important;
  }
}

.panel-header {
  text-align: center !important;
  margin-bottom: var(--space-6xl) !important;
}

.panel-header h2 {
  color: var(--gold) !important;
  font-size: var(--text-3xl) !important;
  font-weight: 700 !important;
  margin: 0 0 var(--space-md) 0 !important;
}

.panel-header p {
  color: var(--text-secondary) !important;
  font-size: var(--text-lg) !important;
  line-height: var(--line-height-relaxed) !important;
  margin: 0 !important;
  max-width: 800px !important;
  margin: 0 auto !important;
}

/* Projections Timeline */
.projections-timeline {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.8)) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-2xl) !important;
  padding: var(--space-4xl) !important;
}

.timeline-header {
  text-align: center !important;
  margin-bottom: var(--space-4xl) !important;
}

.timeline-header h3 {
  color: var(--gold) !important;
  font-size: var(--text-2xl) !important;
  font-weight: 700 !important;
  margin: 0 0 var(--space-md) 0 !important;
}

.timeline-header p {
  color: var(--text-secondary) !important;
  font-size: var(--text-md) !important;
  margin: 0 !important;
}

.timeline-grid {
  display: grid !important;
  grid-template-columns: repeat(5, 1fr) !important;
  gap: var(--space-xl) !important;
}

.timeline-year {
  background: rgba(0, 0, 0, 0.8) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  text-align: center !important;
  transition: all 0.3s ease !important;
}

.timeline-year:hover {
  border-color: var(--gold) !important;
  transform: translateY(-4px) !important;
  box-shadow: 0 8px 24px rgba(212, 175, 55, 0.3) !important;
}

.year-header {
  color: var(--gold) !important;
  font-size: var(--text-xl) !important;
  font-weight: 800 !important;
  margin-bottom: var(--space-lg) !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
}

.year-stats {
  display: flex !important;
  flex-direction: column !important;
  gap: var(--space-md) !important;
}

.year-stats .stat {
  color: var(--text) !important;
  font-size: var(--text-sm) !important;
  font-weight: 600 !important;
  padding: var(--space-sm) !important;
  background: rgba(212, 175, 55, 0.1) !important;
  border-radius: var(--radius-md) !important;
}

/* Responsive Design for Tabbed Calculator */
@media (max-width: 1024px) {
  .tabbed-page-header .header-content {
    grid-template-columns: 1fr !important;
    gap: var(--space-xl) !important;
    text-align: center !important;
  }

  .tabbed-page-header .header-stats {
    justify-content: center !important;
    gap: var(--space-md) !important;
  }

  .tab-navigation {
    justify-content: center !important;
  }

  .timeline-grid {
    grid-template-columns: repeat(3, 1fr) !important;
    gap: var(--space-lg) !important;
  }
}

@media (max-width: 768px) {
  .tabbed-page-header {
    padding: var(--space-xl) 0 !important;
  }

  .tabbed-page-header .page-title {
    font-size: var(--text-3xl) !important;
  }

  .tabbed-page-header .header-stats {
    flex-direction: column !important;
    gap: var(--space-md) !important;
  }

  .tab-navigation {
    flex-direction: column !important;
    gap: var(--space-sm) !important;
  }

  .tab-button {
    justify-content: center !important;
  }

  .timeline-grid {
    grid-template-columns: 1fr !important;
    gap: var(--space-lg) !important;
  }

  .panel-header h2 {
    font-size: var(--text-2xl) !important;
  }
}

@media (max-width: 480px) {
  .tabbed-page-header .page-title {
    font-size: var(--text-2xl) !important;
  }

  .tabbed-page-header .stat-item {
    min-width: 100px !important;
    padding: var(--space-md) !important;
  }

  .tab-content-section {
    padding: var(--space-xl) 0 !important;
  }

  .projections-timeline {
    padding: var(--space-xl) !important;
  }

  .timeline-year {
    padding: var(--space-lg) !important;
  }
}

/* ========================================
   GALLERY PAGE COMPONENTS
======================================== */

/* Gallery Hero Section */
.hero-section {
  padding: var(--space-6xl) 0 var(--space-5xl);
  background: linear-gradient(135deg, var(--bg) 0%, rgba(18, 18, 18, 0.95) 100%);
  border-bottom: 1px solid var(--border);
  position: relative;
  overflow: hidden;
}



.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--text);
  margin-bottom: var(--space-xl);
  line-height: var(--line-height-tight);
  background: linear-gradient(135deg, var(--text) 0%, var(--gold) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-muted);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-4xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: var(--space-4xl);
  margin-top: var(--space-4xl);
}

.hero-stats .stat-item {
  text-align: center;
  padding: var(--space-xl);
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
  min-width: 120px;
}

.hero-stats .stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.15);
  border-color: rgba(255, 215, 0, 0.3);
}

.hero-stats .stat-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--gold);
  margin-bottom: var(--space-xs);
}

.hero-stats .stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  font-weight: var(--font-weight-medium);
}

/* Gallery Introduction Section */
.gallery-intro {
  padding: var(--space-6xl) 0;
  background: var(--bg);
}

.intro-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-xl);
  margin-top: var(--space-4xl);
}

.intro-card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--space-2xl);
  text-align: center;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.intro-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 215, 0, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.intro-card:hover::before {
  opacity: 1;
}

.intro-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(255, 215, 0, 0.15);
  border-color: rgba(255, 215, 0, 0.3);
}

.intro-icon {
  font-size: 3rem;
  margin-bottom: var(--space-lg);
  display: block;
}

.intro-card h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text);
  margin-bottom: var(--space-lg);
  line-height: var(--line-height-tight);
}

.intro-card p {
  font-size: var(--font-size-base);
  color: var(--text-muted);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

/* Gallery Content Section */
.gallery-content {
  padding: var(--space-6xl) 0;
  background: var(--bg);
}

.gallery-wrapper {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  padding: var(--space-4xl);
  box-shadow: var(--shadow);
  position: relative;
  overflow: hidden;
}

.gallery-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.02) 0%, transparent 50%);
  pointer-events: none;
}

/* Gallery Features Section */
.gallery-features {
  padding: var(--space-6xl) 0;
  background: var(--surface);
  border-top: 1px solid var(--border);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-xl);
  margin-top: var(--space-4xl);
}

.feature-card {
  background: var(--bg);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--space-2xl);
  text-align: center;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 215, 0, 0.03) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feature-card:hover::before {
  opacity: 1;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(255, 215, 0, 0.15);
  border-color: rgba(255, 215, 0, 0.3);
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: var(--space-lg);
  display: block;
}

.feature-card h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text);
  margin-bottom: var(--space-lg);
  line-height: var(--line-height-tight);
}

.feature-card p {
  font-size: var(--font-size-base);
  color: var(--text-muted);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

/* Legitimacy Proof Section */
.legitimacy-proof {
  padding: var(--space-6xl) 0;
  background: linear-gradient(135deg, var(--surface) 0%, rgba(18, 18, 18, 0.95) 100%);
  border-top: 1px solid var(--border);
  position: relative;
  overflow: hidden;
}

.legitimacy-proof::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 70%, rgba(255, 215, 0, 0.08) 0%, transparent 50%);
  pointer-events: none;
}

.proof-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.proof-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

.proof-icon {
  font-size: 3rem;
  color: var(--emerald);
  background: rgba(46, 204, 113, 0.1);
  border-radius: var(--radius-full);
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(46, 204, 113, 0.3);
}

.proof-header h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text);
  margin: 0;
}

.proof-description {
  font-size: var(--font-size-lg);
  color: var(--text-muted);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-4xl);
}

.proof-metrics {
  display: flex;
  justify-content: center;
  gap: var(--space-4xl);
  margin-top: var(--space-4xl);
}

.proof-metric {
  text-align: center;
  padding: var(--space-xl);
  background: var(--bg);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
  min-width: 140px;
}

.proof-metric:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(46, 204, 113, 0.15);
  border-color: rgba(46, 204, 113, 0.3);
}

.proof-metric .metric-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--emerald);
  margin-bottom: var(--space-xs);
}

.proof-metric .metric-label {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  font-weight: var(--font-weight-medium);
}

/* Gallery CTA Section */
.gallery-cta {
  padding: var(--space-6xl) 0;
  background: var(--bg);
  border-top: 1px solid var(--border);
}

.cta-content {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.cta-content h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text);
  margin-bottom: var(--space-xl);
  line-height: var(--line-height-tight);
}

.cta-content p {
  font-size: var(--font-size-lg);
  color: var(--text-muted);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-4xl);
}

.cta-buttons {
  display: flex;
  gap: var(--space-lg);
  justify-content: center;
  flex-wrap: wrap;
}

/* Gallery Notification Section */
.gallery-notification {
  padding: var(--space-6xl) 0;
  background: var(--surface);
  border-top: 1px solid var(--border);
}

.notification-card {
  background: var(--bg);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  padding: var(--space-4xl);
  box-shadow: var(--shadow);
  display: flex;
  align-items: flex-start;
  gap: var(--space-2xl);
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
}

.notification-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.notification-icon {
  font-size: 3rem;
  flex-shrink: 0;
  background: rgba(255, 215, 0, 0.1);
  border-radius: var(--radius-full);
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(255, 215, 0, 0.3);
}

.notification-content {
  flex: 1;
  position: relative;
  z-index: 2;
}

.notification-content h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text);
  margin-bottom: var(--space-lg);
  line-height: var(--line-height-tight);
}

.notification-content p {
  font-size: var(--font-size-base);
  color: var(--text-muted);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-2xl);
}

.notification-meta {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-size: var(--font-size-sm);
}

.meta-label {
  color: var(--text-muted);
  font-weight: var(--font-weight-medium);
  min-width: 120px;
}

.meta-value {
  color: var(--text);
  font-weight: var(--font-weight-medium);
}

.verification-badge {
  color: var(--emerald);
  font-weight: var(--font-weight-semibold);
  background: rgba(46, 204, 113, 0.1);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  border: 1px solid rgba(46, 204, 113, 0.3);
}

/* Enhanced Card Hover Effects */
.enhanced-card {
  position: relative;
  overflow: hidden;
}

.enhanced-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
  transition: left 0.5s ease;
}

.enhanced-card:hover::after {
  left: 100%;
}

/* Responsive Design for Gallery */
@media (max-width: 768px) {
  .hero-title {
    font-size: var(--font-size-3xl);
  }

  .hero-stats {
    flex-direction: column;
    gap: var(--space-lg);
    align-items: center;
  }

  .hero-stats .stat-item {
    width: 100%;
    max-width: 200px;
  }

  .intro-grid,
  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }

  .proof-metrics {
    flex-direction: column;
    gap: var(--space-lg);
    align-items: center;
  }

  .proof-metric {
    width: 100%;
    max-width: 200px;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-buttons .btn {
    width: 100%;
    max-width: 300px;
  }

  .notification-card {
    flex-direction: column;
    text-align: center;
    align-items: center;
    padding: var(--space-2xl);
  }

  .notification-icon {
    margin-bottom: var(--space-lg);
  }

  .meta-item {
    justify-content: center;
    flex-wrap: wrap;
  }

  .meta-label {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: var(--space-4xl) 0 var(--space-3xl);
  }

  .hero-title {
    font-size: var(--font-size-2xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-base);
  }

  .gallery-intro,
  .gallery-features,
  .legitimacy-proof,
  .gallery-cta,
  .gallery-notification {
    padding: var(--space-4xl) 0;
  }

  .intro-card,
  .feature-card {
    padding: var(--space-xl);
  }

  .gallery-wrapper {
    padding: var(--space-xl);
  }

  .notification-card {
    padding: var(--space-xl);
  }
}

/* ========================================
   FINAL UTILITY CLASSES
======================================== */

/* Spacing Utilities */
.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--space-xs) !important; }
.mb-2 { margin-bottom: var(--space-sm) !important; }
.mb-3 { margin-bottom: var(--space-md) !important; }
.mb-4 { margin-bottom: var(--space-lg) !important; }
.mb-5 { margin-bottom: var(--space-xl) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--space-xs) !important; }
.mt-2 { margin-top: var(--space-sm) !important; }
.mt-3 { margin-top: var(--space-md) !important; }
.mt-4 { margin-top: var(--space-lg) !important; }
.mt-5 { margin-top: var(--space-xl) !important; }

/* Text Alignment */
.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

/* Display Utilities */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-grid { display: grid !important; }

/* Flex Utilities */
.justify-center { justify-content: center !important; }
.justify-between { justify-content: space-between !important; }
.align-center { align-items: center !important; }
.flex-wrap { flex-wrap: wrap !important; }

/* Width Utilities */
.w-full { width: 100% !important; }
.w-auto { width: auto !important; }
.w-4 { width: 3rem !important; } /* 48px - Custom large checkbox size */
.w-5 { width: 1.25rem !important; } /* 20px - Standard Tailwind size */
.w-6 { width: 1.5rem !important; } /* 24px - Standard Tailwind size */
.w-7 { width: 1.75rem !important; } /* 28px - Standard Tailwind size */
.w-8 { width: 2rem !important; } /* 32px - Standard Tailwind size */

/* Height Utilities */
.h-4 { height: 3rem !important; } /* 48px - Custom large checkbox size */
.h-5 { height: 1.25rem !important; } /* 20px - Standard Tailwind size */
.h-6 { height: 1.5rem !important; } /* 24px - Standard Tailwind size */
.h-7 { height: 1.75rem !important; } /* 28px - Standard Tailwind size */
.h-8 { height: 2rem !important; } /* 32px - Standard Tailwind size */

/* ========================================
   COMMISSION DASHBOARD STYLING
======================================== */

/* Professional commission section cards */
.commission-section-card {
  background: rgba(31, 41, 55, 0.95);
  border: 1px solid rgba(75, 85, 99, 0.5);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.commission-section-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #f59e0b);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.commission-section-card:hover::before {
  opacity: 1;
}

.commission-section-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 215, 0, 0.3);
}

/* Commission metric cards */
.commission-metric-card {
  background: rgba(55, 65, 81, 0.5);
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.commission-metric-card:hover {
  background: rgba(55, 65, 81, 0.8);
  border-color: rgba(255, 215, 0, 0.2);
  transform: scale(1.02);
}

/* Portfolio composition styling */
.portfolio-composition {
  background: rgba(55, 65, 81, 0.3);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid rgba(75, 85, 99, 0.3);
}

/* Action button enhancements */
.commission-action-btn {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.commission-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.commission-action-btn:hover::before {
  left: 100%;
}

.commission-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Presale warning styling */
.presale-warning {
  background: rgba(251, 146, 60, 0.1);
  border: 1px solid rgba(251, 146, 60, 0.3);
  border-radius: 8px;
  padding: 1rem;
  position: relative;
}

.presale-warning::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, #f59e0b, #f97316);
  border-radius: 2px 0 0 2px;
}

/* Transaction category styling */
.transaction-category {
  background: rgba(55, 65, 81, 0.3);
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.3s ease;
}

.transaction-category:hover {
  background: rgba(55, 65, 81, 0.5);
  transform: translateY(-1px);
}

.transaction-item {
  background: rgba(31, 41, 55, 0.5);
  border-radius: 6px;
  padding: 0.75rem;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.transaction-item:hover {
  background: rgba(31, 41, 55, 0.8);
  border-color: rgba(75, 85, 99, 0.3);
}

.status-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-weight: 500;
}

.status-approved {
  background: rgba(34, 197, 94, 0.2);
  color: rgb(134, 239, 172);
}

.status-pending {
  background: rgba(251, 191, 36, 0.2);
  color: rgb(253, 224, 71);
}

.status-rejected {
  background: rgba(239, 68, 68, 0.2);
  color: rgb(252, 165, 165);
}

/* Responsive grid improvements */
@media (max-width: 768px) {
  .commission-section-card {
    padding: 1rem;
  }

  .commission-metric-card {
    padding: 0.75rem;
  }

  .transaction-category {
    padding: 0.75rem;
  }

  .transaction-item {
    padding: 0.5rem;
  }
}

/* ========================================
   PREMIUM AUTH PAGE STYLING
======================================== */

.premium-auth-page {
  background: var(--bg);
  min-height: 100vh;
  position: relative;
}

.premium-header {
  border-bottom: 1px solid rgba(255, 215, 0, 0.2);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.premium-nav-item {
  color: #B0B0B0 !important;
  transition: all 0.3s ease;
  position: relative;
  padding: 8px 16px !important;
  border-radius: 8px !important;
  background: transparent !important;
  border: 1px solid transparent !important;
}

.premium-nav-item:hover {
  color: #FFD700 !important;
  transform: translateY(-2px) !important;
  background: rgba(255, 215, 0, 0.1) !important;
  border-color: #FFD700 !important;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3) !important;
}

.premium-nav-item::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  width: 0;
  height: 3px;
  background: linear-gradient(90deg, #FFD700, #00BFFF) !important;
  transition: all 0.3s ease;
  transform: translateX(-50%);
  border-radius: 2px;
}

.premium-nav-item:hover::after {
  width: 100%;
}

.premium-btn {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FFD700 100%) !important;
  color: #000000 !important;
  border: 2px solid #FFD700 !important;
  padding: 12px 24px !important;
  border-radius: 12px !important;
  font-weight: 700 !important;
  font-size: 16px !important;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
  position: relative;
  overflow: hidden;
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
}

.premium-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.premium-btn:hover {
  transform: translateY(-3px) scale(1.05) !important;
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.6), 0 0 30px rgba(255, 215, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
  background: linear-gradient(135deg, #FFED4E 0%, #FFD700 50%, #FFED4E 100%) !important;
}

.premium-btn:hover::before {
  left: 100%;
}

.premium-btn-secondary {
  background: transparent;
  color: var(--text-muted);
  border: 2px solid var(--border);
  padding: var(--space-md) var(--space-xl);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-semibold);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.premium-btn-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
  transition: left 0.5s ease;
}

.premium-btn-secondary:hover {
  color: var(--gold);
  border-color: var(--gold);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.2);
}

.premium-btn-secondary:hover::before {
  left: 100%;
}

.premium-hero {
  position: relative;
  z-index: 5;
  padding: 80px 0 60px !important;
  background: linear-gradient(135deg, rgba(5, 5, 5, 0.98) 0%, rgba(18, 18, 18, 0.95) 50%, rgba(30, 30, 30, 0.9) 100%) !important;
  border-bottom: 1px solid rgba(255, 215, 0, 0.3) !important;
  box-shadow: inset 0 0 100px rgba(255, 215, 0, 0.1) !important;
}

.premium-hero-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.premium-breadcrumb {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  margin-bottom: var(--space-xl);
}

.premium-breadcrumb-link {
  color: var(--text-muted);
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: var(--font-size-sm);
}

.premium-breadcrumb-link:hover {
  color: var(--gold);
}

.premium-breadcrumb-current {
  color: var(--gold);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.premium-hero-title {
  font-size: clamp(3rem, 6vw, 4.5rem) !important;
  font-weight: 900 !important;
  color: transparent !important;
  margin-bottom: 32px !important;
  background: linear-gradient(135deg, #FFFFFF 0%, #FFD700 30%, #FFED4E 60%, #FFD700 100%) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  text-shadow: 0 0 40px rgba(255, 215, 0, 0.5) !important;
  animation: premiumTitleGlow 3s ease-in-out infinite alternate !important;
  text-align: center !important;
  letter-spacing: 2px !important;
}

@keyframes premiumTitleGlow {
  0% {
    text-shadow: 0 0 40px rgba(255, 215, 0, 0.5);
    filter: brightness(1);
  }
  100% {
    text-shadow: 0 0 60px rgba(255, 215, 0, 0.8), 0 0 80px rgba(255, 215, 0, 0.3);
    filter: brightness(1.1);
  }
}

.premium-hero-subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-muted);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.premium-content {
  position: relative;
  z-index: 5;
  padding: var(--space-4xl) 0;
}

.premium-auth-container {
  max-width: 800px;
  margin: 0 auto;
}

.premium-tab-navigation {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 2rem;
  background: var(--card-bg);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 12px;
}

.premium-tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: 1px solid transparent;
  background: var(--button-secondary-bg);
  color: var(--text-secondary);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 14px;
}

.premium-tab-button:hover {
  color: var(--text);
  background: var(--button-secondary-hover-bg);
  border-color: var(--gold);
}

.premium-tab-button.active {
  color: var(--gold);
  background: var(--button-primary-bg);
  border-color: var(--gold);
}

.premium-auth-content {
  position: relative;
}

.premium-card {
  background: var(--card-bg);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: var(--shadow);
}

.premium-card-header {
  text-align: center;
  margin-bottom: var(--space-2xl);
}

.premium-card-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text);
  margin-bottom: var(--space-md);
  background: linear-gradient(135deg, var(--text) 0%, var(--gold) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.premium-card-subtitle {
  font-size: var(--font-size-base);
  color: var(--text-muted);
  line-height: 1.6;
}

.premium-card-content {
  position: relative;
}

/* Premium Form Styling */
.premium-auth-content .form-group {
  margin-bottom: var(--space-xl);
}

.premium-auth-content .form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text);
  margin-bottom: var(--space-sm);
}

.premium-auth-content .form-input {
  width: 100%;
  padding: 16px 20px !important;
  border: 2px solid rgba(255, 215, 0, 0.3) !important;
  border-radius: 12px !important;
  background: rgba(18, 18, 18, 0.8) !important;
  color: #FFFFFF !important;
  font-size: 16px !important;
  transition: all 0.3s ease;
  position: relative;
  backdrop-filter: blur(5px) !important;
}

.premium-auth-content .form-input:focus {
  outline: none;
  border-color: #FFD700 !important;
  box-shadow: 0 0 0 4px rgba(255, 215, 0, 0.2), 0 0 20px rgba(255, 215, 0, 0.3) !important;
  transform: translateY(-2px) !important;
  background: rgba(18, 18, 18, 0.95) !important;
}

.premium-auth-content .form-input::placeholder {
  color: var(--text-muted);
}

/* Premium Button Styling */
.premium-auth-content .btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-lg) var(--space-2xl);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  min-height: 48px;
  position: relative;
  overflow: hidden;
}

.premium-auth-content .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.premium-auth-content .btn-primary {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FFD700 100%) !important;
  color: #000000 !important;
  border: 2px solid #FFD700 !important;
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
  font-weight: 700 !important;
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
}

.premium-auth-content .btn-primary:hover:not(:disabled) {
  transform: translateY(-3px) scale(1.02) !important;
  box-shadow: 0 10px 30px rgba(255, 215, 0, 0.6), 0 0 25px rgba(255, 215, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
  background: linear-gradient(135deg, #FFED4E 0%, #FFD700 50%, #FFED4E 100%) !important;
}

.premium-auth-content .btn-primary:hover:not(:disabled)::before {
  left: 100%;
}

.premium-auth-content .btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.premium-auth-content .btn-lg {
  padding: var(--space-xl) var(--space-3xl);
  font-size: var(--font-size-lg);
  min-height: 56px;
}

/* Premium Alert Styling */
.premium-auth-content .alert {
  padding: var(--space-lg);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-xl);
  border: 1px solid;
  position: relative;
  overflow: hidden;
}

.premium-auth-content .alert::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: currentColor;
}

.premium-auth-content .alert-error {
  background: rgba(255, 107, 107, 0.1);
  border-color: var(--error);
  color: var(--error);
}

.premium-auth-content .alert-success {
  background: rgba(46, 204, 113, 0.1);
  border-color: var(--emerald);
  color: var(--emerald);
}

/* Premium Purchase Flow Styling */
.premium-purchase-flow {
  background: rgba(5, 5, 5, 0.95);
  backdrop-filter: blur(20px);
}

.premium-purchase-modal {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow), 0 0 40px rgba(255, 215, 0, 0.1) !important;
  position: relative;
  overflow: hidden;
}

.premium-purchase-modal::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--gold), var(--blue), var(--emerald));
  opacity: 0.8;
  z-index: 1;
}

.premium-purchase-modal .quickAmountButton {
  background: var(--surface) !important;
  border: 2px solid var(--border) !important;
  color: var(--text) !important;
  border-radius: var(--radius-md) !important;
  transition: all 0.3s ease !important;
  position: relative;
  overflow: hidden;
}

.premium-purchase-modal .quickAmountButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
  transition: left 0.5s ease;
}

.premium-purchase-modal .quickAmountButton:hover {
  border-color: var(--gold) !important;
  color: var(--gold) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.2) !important;
}

.premium-purchase-modal .quickAmountButton:hover::before {
  left: 100%;
}

.premium-purchase-modal .quickAmountButton.active {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(0, 191, 255, 0.05)) !important;
  border-color: var(--gold) !important;
  color: var(--gold) !important;
  box-shadow: inset 0 0 20px rgba(255, 215, 0, 0.1) !important;
}

.premium-purchase-modal .customAmountInput {
  background: var(--bg) !important;
  border: 2px solid var(--border) !important;
  color: var(--text) !important;
  border-radius: var(--radius-md) !important;
  transition: all 0.3s ease !important;
}

.premium-purchase-modal .customAmountInput:focus {
  border-color: var(--gold) !important;
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1), var(--glow) !important;
  transform: translateY(-1px) !important;
}

.premium-purchase-modal .continueButton {
  background: linear-gradient(135deg, var(--gold), #E6C200) !important;
  color: var(--bg) !important;
  border: none !important;
  border-radius: var(--radius-md) !important;
  transition: all 0.3s ease !important;
  position: relative;
  overflow: hidden;
}

.premium-purchase-modal .continueButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.premium-purchase-modal .continueButton:not(:disabled):hover {
  transform: translateY(-2px) !important;
  box-shadow: var(--glow), 0 8px 32px rgba(255, 215, 0, 0.3) !important;
}

.premium-purchase-modal .continueButton:not(:disabled):hover::before {
  left: 100%;
}

.premium-purchase-modal .priceDisplay {
  background: linear-gradient(135deg, rgba(46, 204, 113, 0.1), rgba(16, 185, 129, 0.05)) !important;
  border: 1px solid var(--emerald) !important;
  border-radius: var(--radius-md) !important;
  box-shadow: 0 4px 12px rgba(46, 204, 113, 0.1) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .premium-tab-navigation {
    flex-direction: column;
    gap: var(--space-xs);
  }

  .premium-tab-button {
    justify-content: center;
    padding: var(--space-md);
  }

  .premium-card {
    padding: var(--space-xl);
    margin: 0 var(--space-md);
  }

  .premium-hero-title {
    font-size: 2.5rem;
  }

  .premium-purchase-modal {
    margin: var(--space-md) !important;
    padding: var(--space-xl) !important;
  }
}

/* ========================================
   READY TO PURCHASE SHARES SECTION
======================================== */

.ready-to-invest {
  padding: var(--space-5xl) 0 !important;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 0.9)) !important;
  position: relative !important;
}

.ready-to-invest::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: radial-gradient(circle at 50% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 70%) !important;
  pointer-events: none !important;
}

.ready-to-invest .section-header {
  text-align: center !important;
  margin-bottom: var(--space-5xl) !important;
  position: relative !important;
  z-index: 2 !important;
}

.ready-to-invest .section-header h2 {
  color: var(--gold) !important;
  font-size: var(--text-4xl) !important;
  font-weight: 800 !important;
  margin-bottom: var(--space-lg) !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
}

.ready-to-invest .section-header p {
  color: var(--text-secondary) !important;
  font-size: var(--text-lg) !important;
  margin-bottom: var(--space-xl) !important;
  max-width: 600px !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

.header-highlight {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1)) !important;
  border: 1px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-full) !important;
  padding: var(--space-md) var(--space-xl) !important;
  display: inline-block !important;
}

.highlight-text {
  color: var(--gold) !important;
  font-weight: 600 !important;
  font-size: var(--text-md) !important;
}

.action-steps-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)) !important;
  gap: var(--space-4xl) !important;
  margin-bottom: var(--space-5xl) !important;
  position: relative !important;
  z-index: 2 !important;
}

.action-step-card {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.9)) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--space-4xl) !important;
  position: relative !important;
  overflow: hidden !important;
  transition: all 0.3s ease !important;
}

.action-step-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 4px !important;
}

.action-step-card.calculator::before {
  background: linear-gradient(90deg, #4caf50, #8bc34a) !important;
}

.action-step-card.financial::before {
  background: linear-gradient(90deg, #2196f3, #03a9f4) !important;
}

.action-step-card.registration::before {
  background: linear-gradient(90deg, #ff6b35, #f7931e) !important;
}

.action-step-card:hover {
  transform: translateY(-8px) !important;
  box-shadow: 0 16px 48px rgba(212, 175, 55, 0.2) !important;
  border-color: var(--gold) !important;
}

.step-number {
  position: absolute !important;
  top: var(--space-lg) !important;
  right: var(--space-lg) !important;
  width: 40px !important;
  height: 40px !important;
  background: rgba(212, 175, 55, 0.2) !important;
  border: 2px solid var(--gold) !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-weight: 700 !important;
  font-size: var(--text-sm) !important;
  color: var(--gold) !important;
}

.step-content {
  position: relative !important;
}

.step-icon-wrapper {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-md) !important;
  margin-bottom: var(--space-xl) !important;
}

.step-icon {
  font-size: var(--text-4xl) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 80px !important;
  height: 80px !important;
  background: rgba(212, 175, 55, 0.1) !important;
  border-radius: var(--radius-lg) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
}

.step-badge {
  background: var(--gold) !important;
  color: var(--bg) !important;
  padding: var(--space-xs) var(--space-sm) !important;
  border-radius: var(--radius-md) !important;
  font-size: var(--text-xs) !important;
  font-weight: 700 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.action-step-card h3 {
  color: var(--gold) !important;
  font-size: var(--text-2xl) !important;
  font-weight: 700 !important;
  margin-bottom: var(--space-lg) !important;
}

.step-description {
  color: var(--text) !important;
  font-size: var(--text-md) !important;
  line-height: var(--line-height-relaxed) !important;
  margin-bottom: var(--space-xl) !important;
}

.step-features {
  display: flex !important;
  flex-direction: column !important;
  gap: var(--space-sm) !important;
  margin-bottom: var(--space-xl) !important;
}

.feature-item {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-sm) !important;
  color: var(--text-secondary) !important;
  font-size: var(--text-sm) !important;
}

.feature-icon {
  font-size: var(--text-lg) !important;
  width: 24px !important;
  text-align: center !important;
}

.step-action-btn {
  width: 100% !important;
  padding: var(--space-lg) var(--space-xl) !important;
  border: none !important;
  border-radius: var(--radius-lg) !important;
  font-weight: 700 !important;
  font-size: var(--text-md) !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: var(--space-sm) !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.step-action-btn.calculator {
  background: linear-gradient(135deg, #4caf50, #8bc34a) !important;
  color: white !important;
}

.step-action-btn.financial {
  background: linear-gradient(135deg, #2196f3, #03a9f4) !important;
  color: white !important;
}

.step-action-btn.registration {
  background: linear-gradient(135deg, #ff6b35, #f7931e) !important;
  color: white !important;
}

.step-action-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3) !important;
}

/* CTA Banner */
.cta-banner {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1)) !important;
  border: 2px solid var(--gold) !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--space-4xl) !important;
  margin-bottom: var(--space-5xl) !important;
  position: relative !important;
  z-index: 2 !important;
  overflow: hidden !important;
}

.cta-banner::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: radial-gradient(circle at 30% 30%, rgba(212, 175, 55, 0.1) 0%, transparent 50%) !important;
  pointer-events: none !important;
}

.cta-content {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  gap: var(--space-4xl) !important;
  position: relative !important;
  z-index: 2 !important;
}

.cta-text h3 {
  color: var(--gold) !important;
  font-size: var(--text-2xl) !important;
  font-weight: 700 !important;
  margin-bottom: var(--space-md) !important;
}

.cta-text p {
  color: var(--text) !important;
  font-size: var(--text-md) !important;
  margin: 0 !important;
}

.cta-actions {
  display: flex !important;
  gap: var(--space-lg) !important;
  flex-shrink: 0 !important;
}

.cta-primary-btn {
  background: linear-gradient(135deg, var(--gold), rgba(212, 175, 55, 0.8)) !important;
  color: var(--bg) !important;
  border: none !important;
  padding: var(--space-lg) var(--space-xl) !important;
  border-radius: var(--radius-lg) !important;
  font-weight: 700 !important;
  font-size: var(--text-md) !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: var(--space-sm) !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  white-space: nowrap !important;
}

.cta-primary-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(212, 175, 55, 0.4) !important;
}

.cta-secondary-btn {
  background: transparent !important;
  color: var(--gold) !important;
  border: 2px solid var(--gold) !important;
  padding: var(--space-lg) var(--space-xl) !important;
  border-radius: var(--radius-lg) !important;
  font-weight: 700 !important;
  font-size: var(--text-md) !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: var(--space-sm) !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  white-space: nowrap !important;
}

.cta-secondary-btn:hover {
  background: rgba(212, 175, 55, 0.1) !important;
  transform: translateY(-2px) !important;
}

/* Trust Indicators */
.trust-indicators {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
  gap: var(--space-xl) !important;
  padding: var(--space-4xl) 0 !important;
  border-top: 1px solid rgba(212, 175, 55, 0.3) !important;
  position: relative !important;
  z-index: 2 !important;
}

.trust-item {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-lg) !important;
  padding: var(--space-lg) !important;
  background: rgba(0, 0, 0, 0.4) !important;
  border-radius: var(--radius-lg) !important;
  border: 1px solid rgba(212, 175, 55, 0.2) !important;
  transition: all 0.3s ease !important;
}

.trust-item:hover {
  background: rgba(212, 175, 55, 0.1) !important;
  border-color: rgba(212, 175, 55, 0.4) !important;
  transform: translateY(-2px) !important;
}

.trust-icon {
  font-size: var(--text-3xl) !important;
  width: 60px !important;
  height: 60px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: rgba(212, 175, 55, 0.1) !important;
  border-radius: var(--radius-lg) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  flex-shrink: 0 !important;
}

.trust-text {
  display: flex !important;
  flex-direction: column !important;
  gap: var(--space-xs) !important;
}

.trust-text strong {
  color: var(--gold) !important;
  font-size: var(--text-md) !important;
  font-weight: 700 !important;
}

.trust-text span {
  color: var(--text-secondary) !important;
  font-size: var(--text-sm) !important;
}

/* Responsive Design for Ready to Purchase Section */
@media (max-width: 1024px) {
  .action-steps-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)) !important;
    gap: var(--space-xl) !important;
  }

  .cta-content {
    flex-direction: column !important;
    text-align: center !important;
    gap: var(--space-xl) !important;
  }

  .cta-actions {
    justify-content: center !important;
  }
}

@media (max-width: 768px) {
  .ready-to-invest {
    padding: var(--space-4xl) 0 !important;
  }

  .ready-to-invest .section-header h2 {
    font-size: var(--text-3xl) !important;
  }

  .action-steps-grid {
    grid-template-columns: 1fr !important;
    gap: var(--space-lg) !important;
  }

  .action-step-card {
    padding: var(--space-xl) !important;
  }

  .step-icon-wrapper {
    flex-direction: column !important;
    text-align: center !important;
    gap: var(--space-sm) !important;
  }

  .step-icon {
    width: 60px !important;
    height: 60px !important;
    font-size: var(--text-3xl) !important;
  }

  .cta-banner {
    padding: var(--space-xl) !important;
  }

  .cta-actions {
    flex-direction: column !important;
    width: 100% !important;
  }

  .cta-primary-btn,
  .cta-secondary-btn {
    width: 100% !important;
    justify-content: center !important;
  }

  .trust-indicators {
    grid-template-columns: 1fr !important;
    gap: var(--space-md) !important;
  }

  .trust-item {
    padding: var(--space-md) !important;
  }

  .trust-icon {
    width: 50px !important;
    height: 50px !important;
    font-size: var(--text-2xl) !important;
  }
}

@media (max-width: 480px) {
  .action-step-card {
    padding: var(--space-lg) !important;
  }

  .step-number {
    width: 32px !important;
    height: 32px !important;
    font-size: var(--text-xs) !important;
  }

  .action-step-card h3 {
    font-size: var(--text-xl) !important;
  }

  .step-description {
    font-size: var(--text-sm) !important;
  }

  .cta-text h3 {
    font-size: var(--text-xl) !important;
  }

  .cta-text p {
    font-size: var(--text-sm) !important;
  }
}

/* ========================================
   END OF AUREUS UNIFIED DESIGN SYSTEM

   Total Lines: ~3,200+
   Components: 60+ styled components
   Color Palette: Premium gold-focused theme
   Theme Support: Dark (default) + Light modes
   Responsive: Mobile-first design
   Accessibility: WCAG 2.1 AA compliant
======================================== */

/* ========================================
   ENHANCED PROFESSIONAL PROJECTIONS TAB
======================================== */

/* Executive Summary */
.projections-executive-summary {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(212, 175, 55, 0.05)) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--space-4xl) !important;
  margin-bottom: var(--space-4xl) !important;
  position: relative !important;
  overflow: hidden !important;
}

.projections-executive-summary::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 4px !important;
  background: linear-gradient(90deg, var(--gold), #E6C200, var(--gold)) !important;
}

.summary-header {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-lg) !important;
  margin-bottom: var(--space-4xl) !important;
}

.summary-icon {
  width: 60px !important;
  height: 60px !important;
  background: linear-gradient(135deg, var(--gold), #E6C200) !important;
  border-radius: var(--radius-full) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: var(--text-2xl) !important;
  color: var(--background-dark) !important;
  box-shadow: 0 8px 24px rgba(212, 175, 55, 0.4) !important;
}

.summary-content h3 {
  color: var(--gold) !important;
  font-size: var(--text-2xl) !important;
  font-weight: 700 !important;
  margin-bottom: var(--space-sm) !important;
}

.summary-content p {
  color: var(--text-muted) !important;
  font-size: var(--text-lg) !important;
  line-height: 1.6 !important;
}

.summary-metrics {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
  gap: var(--space-xl) !important;
}

.metric-card {
  background: rgba(0, 0, 0, 0.6) !important;
  border: 1px solid rgba(212, 175, 55, 0.2) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  text-align: center !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.metric-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 2px !important;
  background: linear-gradient(90deg, transparent, var(--gold), transparent) !important;
  transform: translateX(-100%) !important;
  transition: transform 0.6s ease !important;
}

.metric-card:hover::before {
  transform: translateX(100%) !important;
}

.metric-card:hover {
  transform: translateY(-4px) !important;
  border-color: var(--gold) !important;
  box-shadow: 0 12px 32px rgba(212, 175, 55, 0.3) !important;
}

.metric-icon {
  font-size: var(--text-3xl) !important;
  margin-bottom: var(--space-md) !important;
  display: block !important;
}

.metric-content {
  text-align: center !important;
}

.metric-value {
  color: var(--gold) !important;
  font-size: var(--text-3xl) !important;
  font-weight: 800 !important;
  margin-bottom: var(--space-xs) !important;
  display: block !important;
}

.metric-label {
  color: var(--text) !important;
  font-size: var(--text-sm) !important;
  font-weight: 600 !important;
  margin-bottom: var(--space-xs) !important;
  display: block !important;
}

.metric-growth {
  color: var(--emerald) !important;
  font-size: var(--text-xs) !important;
  font-weight: 500 !important;
  display: block !important;
}

/* Enhanced Timeline */
.enhanced-projections-timeline {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--space-4xl) !important;
  margin-bottom: var(--space-4xl) !important;
}

.timeline-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: flex-start !important;
  margin-bottom: var(--space-4xl) !important;
  flex-wrap: wrap !important;
  gap: var(--space-xl) !important;
}

.timeline-title h3 {
  color: var(--text) !important;
  font-size: var(--text-2xl) !important;
  font-weight: 700 !important;
  margin-bottom: var(--space-sm) !important;
}

.timeline-title p {
  color: var(--text-muted) !important;
  font-size: var(--text-md) !important;
  line-height: 1.6 !important;
}

.timeline-legend {
  display: flex !important;
  gap: var(--space-lg) !important;
  flex-wrap: wrap !important;
}

.legend-item {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-sm) !important;
  font-size: var(--text-sm) !important;
  color: var(--text-muted) !important;
}

.legend-color {
  width: 12px !important;
  height: 12px !important;
  border-radius: var(--radius-sm) !important;
}

.legend-color.foundation {
  background: linear-gradient(135deg, #3B82F6, #1E40AF) !important;
}

.legend-color.growth {
  background: linear-gradient(135deg, #10B981, #059669) !important;
}

.legend-color.scale {
  background: linear-gradient(135deg, #F59E0B, #D97706) !important;
}

.legend-color.maturity {
  background: linear-gradient(135deg, var(--gold), #B8860B) !important;
}

/* Professional Timeline Grid */
.professional-timeline-grid {
  display: grid !important;
  grid-template-columns: 1fr !important;
  gap: var(--space-4xl) !important;
}

.professional-timeline-year {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6)) !important;
  border: 2px solid rgba(212, 175, 55, 0.2) !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--space-4xl) !important;
  position: relative !important;
  overflow: hidden !important;
  transition: all 0.4s ease !important;
}

.professional-timeline-year::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 4px !important;
  transition: all 0.4s ease !important;
}

.professional-timeline-year.foundation::before {
  background: linear-gradient(90deg, #3B82F6, #1E40AF) !important;
}

.professional-timeline-year.growth::before {
  background: linear-gradient(90deg, #10B981, #059669) !important;
}

.professional-timeline-year.scale::before {
  background: linear-gradient(90deg, #F59E0B, #D97706) !important;
}

.professional-timeline-year.maturity::before {
  background: linear-gradient(90deg, var(--gold), #B8860B) !important;
}

.professional-timeline-year:hover {
  transform: translateY(-4px) !important;
  border-color: var(--gold) !important;
  box-shadow: 0 16px 48px rgba(212, 175, 55, 0.2) !important;
}

.year-badge {
  display: inline-flex !important;
  flex-direction: column !important;
  align-items: center !important;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1)) !important;
  border: 1px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-lg) var(--space-xl) !important;
  margin-bottom: var(--space-xl) !important;
  position: relative !important;
}

.year-number {
  color: var(--gold) !important;
  font-size: var(--text-3xl) !important;
  font-weight: 800 !important;
  line-height: 1 !important;
  margin-bottom: var(--space-xs) !important;
}

.year-phase {
  color: var(--text-muted) !important;
  font-size: var(--text-sm) !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.year-content {
  display: grid !important;
  gap: var(--space-xl) !important;
}

.operational-metrics {
  background: rgba(0, 0, 0, 0.4) !important;
  border: 1px solid rgba(212, 175, 55, 0.1) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
}

.metric-row {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  gap: var(--space-lg) !important;
  margin-bottom: var(--space-lg) !important;
}

.metric-row:last-child {
  margin-bottom: 0 !important;
}

.metric-item {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-sm) !important;
  padding: var(--space-md) !important;
  background: rgba(212, 175, 55, 0.05) !important;
  border-radius: var(--radius-md) !important;
  border: 1px solid rgba(212, 175, 55, 0.1) !important;
}

.metric-item .metric-icon {
  font-size: var(--text-lg) !important;
  flex-shrink: 0 !important;
}

.metric-item .metric-text {
  color: var(--text) !important;
  font-size: var(--text-sm) !important;
  font-weight: 600 !important;
}

.dividend-highlight {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.15), rgba(212, 175, 55, 0.05)) !important;
  border: 2px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  text-align: center !important;
  position: relative !important;
}

.dividend-highlight::before {
  content: '💰' !important;
  position: absolute !important;
  top: -10px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  background: var(--bg) !important;
  padding: 0 var(--space-sm) !important;
  font-size: var(--text-lg) !important;
}

.dividend-label {
  color: var(--text-muted) !important;
  font-size: var(--text-sm) !important;
  font-weight: 600 !important;
  margin-bottom: var(--space-sm) !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.dividend-value {
  color: var(--gold) !important;
  font-size: var(--text-4xl) !important;
  font-weight: 800 !important;
  margin-bottom: var(--space-sm) !important;
  text-shadow: 0 2px 8px rgba(212, 175, 55, 0.3) !important;
}

.dividend-description {
  color: var(--text-muted) !important;
  font-size: var(--text-sm) !important;
  font-style: italic !important;
}

.phase-description {
  background: rgba(255, 255, 255, 0.02) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: var(--radius-md) !important;
  padding: var(--space-lg) !important;
}

.phase-description p {
  color: var(--text-muted) !important;
  font-size: var(--text-md) !important;
  line-height: 1.6 !important;
  margin: 0 !important;
}

/* Growth Analysis */
.growth-analysis {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--space-4xl) !important;
  margin-bottom: var(--space-4xl) !important;
}

.analysis-header {
  text-align: center !important;
  margin-bottom: var(--space-4xl) !important;
}

.analysis-header h3 {
  color: var(--text) !important;
  font-size: var(--text-2xl) !important;
  font-weight: 700 !important;
  margin-bottom: var(--space-sm) !important;
}

.analysis-header p {
  color: var(--text-muted) !important;
  font-size: var(--text-lg) !important;
  line-height: 1.6 !important;
}

.analysis-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
  gap: var(--space-xl) !important;
}

.analysis-card {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6)) !important;
  border: 2px solid rgba(212, 175, 55, 0.2) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.analysis-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 3px !important;
  transition: all 0.3s ease !important;
}

.analysis-card.operational::before {
  background: linear-gradient(90deg, #3B82F6, #1E40AF) !important;
}

.analysis-card.financial::before {
  background: linear-gradient(90deg, #10B981, #059669) !important;
}

.analysis-card.strategic::before {
  background: linear-gradient(90deg, var(--gold), #B8860B) !important;
}

.analysis-card:hover {
  transform: translateY(-6px) !important;
  border-color: var(--gold) !important;
  box-shadow: 0 20px 60px rgba(212, 175, 55, 0.2) !important;
}

.card-header {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-lg) !important;
  margin-bottom: var(--space-xl) !important;
  padding-bottom: var(--space-lg) !important;
  border-bottom: 1px solid rgba(212, 175, 55, 0.2) !important;
}

.card-icon {
  width: 50px !important;
  height: 50px !important;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1)) !important;
  border: 1px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-lg) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: var(--text-xl) !important;
  flex-shrink: 0 !important;
}

.card-header h4 {
  color: var(--text) !important;
  font-size: var(--text-lg) !important;
  font-weight: 700 !important;
  margin: 0 !important;
}

.card-content {
  display: flex !important;
  flex-direction: column !important;
  gap: var(--space-lg) !important;
}

.growth-stat {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: var(--space-md) !important;
  background: rgba(212, 175, 55, 0.05) !important;
  border: 1px solid rgba(212, 175, 55, 0.1) !important;
  border-radius: var(--radius-md) !important;
  transition: all 0.2s ease !important;
}

.growth-stat:hover {
  background: rgba(212, 175, 55, 0.1) !important;
  border-color: rgba(212, 175, 55, 0.2) !important;
}

.stat-label {
  color: var(--text-muted) !important;
  font-size: var(--text-sm) !important;
  font-weight: 600 !important;
}

.stat-value {
  color: var(--gold) !important;
  font-size: var(--text-sm) !important;
  font-weight: 700 !important;
}

/* Responsive Design for Enhanced Projections */
@media (max-width: 1024px) {
  .summary-metrics {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: var(--space-lg) !important;
  }

  .timeline-header {
    flex-direction: column !important;
    align-items: center !important;
    text-align: center !important;
  }

  .timeline-legend {
    justify-content: center !important;
  }

  .metric-row {
    grid-template-columns: 1fr !important;
    gap: var(--space-md) !important;
  }

  .analysis-grid {
    grid-template-columns: 1fr !important;
    gap: var(--space-lg) !important;
  }
}

@media (max-width: 768px) {
  .projections-executive-summary {
    padding: var(--space-xl) !important;
  }

  .summary-header {
    flex-direction: column !important;
    text-align: center !important;
    gap: var(--space-md) !important;
  }

  .summary-metrics {
    grid-template-columns: 1fr !important;
    gap: var(--space-md) !important;
  }

  .enhanced-projections-timeline {
    padding: var(--space-xl) !important;
  }

  .professional-timeline-year {
    padding: var(--space-xl) !important;
  }

  .dividend-value {
    font-size: var(--text-3xl) !important;
  }

  .growth-analysis {
    padding: var(--space-xl) !important;
  }
}

/* ========================================
   PROFESSIONAL INVESTMENT SCENARIOS
======================================== */

/* Investment Overview */
.investment-overview {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.08), rgba(212, 175, 55, 0.03)) !important;
  border: 2px solid rgba(212, 175, 55, 0.2) !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--space-4xl) !important;
  margin-bottom: var(--space-4xl) !important;
  position: relative !important;
}

.investment-overview::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 3px !important;
  background: linear-gradient(90deg, var(--gold), #E6C200, var(--gold)) !important;
  border-radius: var(--radius-xl) var(--radius-xl) 0 0 !important;
}

.overview-header {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-lg) !important;
  margin-bottom: var(--space-4xl) !important;
}

.overview-icon {
  width: 60px !important;
  height: 60px !important;
  background: linear-gradient(135deg, var(--gold), #E6C200) !important;
  border-radius: var(--radius-full) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: var(--text-2xl) !important;
  color: var(--bg) !important;
  box-shadow: 0 8px 24px rgba(212, 175, 55, 0.4) !important;
}

.overview-content h3 {
  color: var(--text) !important;
  font-size: var(--text-2xl) !important;
  font-weight: 700 !important;
  margin-bottom: var(--space-sm) !important;
}

.overview-content p {
  color: var(--text-muted) !important;
  font-size: var(--text-lg) !important;
  line-height: 1.6 !important;
}

.overview-metrics {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
  gap: var(--space-xl) !important;
}

.overview-metric {
  background: rgba(0, 0, 0, 0.4) !important;
  border: 1px solid rgba(212, 175, 55, 0.2) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  text-align: center !important;
  transition: all 0.3s ease !important;
}

.overview-metric:hover {
  transform: translateY(-4px) !important;
  border-color: var(--gold) !important;
  box-shadow: 0 12px 32px rgba(212, 175, 55, 0.3) !important;
}

.overview-metric .metric-icon {
  font-size: var(--text-2xl) !important;
  margin-bottom: var(--space-md) !important;
  display: block !important;
}

.overview-metric .metric-value {
  color: var(--gold) !important;
  font-size: var(--text-2xl) !important;
  font-weight: 800 !important;
  margin-bottom: var(--space-xs) !important;
  display: block !important;
}

.overview-metric .metric-label {
  color: var(--text) !important;
  font-size: var(--text-sm) !important;
  font-weight: 600 !important;
  margin-bottom: var(--space-xs) !important;
  display: block !important;
}

.overview-metric .metric-description {
  color: var(--text-muted) !important;
  font-size: var(--text-xs) !important;
  display: block !important;
}

/* Professional Scenarios */
.professional-scenarios {
  margin-bottom: var(--space-4xl) !important;
}

.scenarios-header {
  text-align: center !important;
  margin-bottom: var(--space-4xl) !important;
}

.scenarios-header h3 {
  color: var(--text) !important;
  font-size: var(--text-2xl) !important;
  font-weight: 700 !important;
  margin-bottom: var(--space-sm) !important;
}

.scenarios-header p {
  color: var(--text-muted) !important;
  font-size: var(--text-lg) !important;
  line-height: 1.6 !important;
  max-width: 600px !important;
  margin: 0 auto !important;
}

.professional-scenarios-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)) !important;
  gap: var(--space-4xl) !important;
}

/* Professional Scenario Cards */
.professional-scenario-card {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.7)) !important;
  border: 2px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--space-4xl) !important;
  position: relative !important;
  overflow: hidden !important;
  transition: all 0.4s ease !important;
}

.professional-scenario-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 4px !important;
  transition: all 0.4s ease !important;
}

.professional-scenario-card.conservative::before {
  background: linear-gradient(90deg, #3B82F6, #1E40AF) !important;
}

.professional-scenario-card.growth::before {
  background: linear-gradient(90deg, #10B981, #059669) !important;
}

.professional-scenario-card.serious::before {
  background: linear-gradient(90deg, var(--gold), #B8860B) !important;
}

.professional-scenario-card:hover {
  transform: translateY(-8px) !important;
  border-color: var(--gold) !important;
  box-shadow: 0 24px 64px rgba(212, 175, 55, 0.2) !important;
}

.professional-scenario-card.featured {
  border-color: var(--gold) !important;
  box-shadow: 0 16px 48px rgba(212, 175, 55, 0.15) !important;
}

/* Featured Badge */
.featured-badge {
  position: absolute !important;
  top: -12px !important;
  right: var(--space-xl) !important;
  background: linear-gradient(135deg, var(--gold), #E6C200) !important;
  color: var(--bg) !important;
  padding: var(--space-sm) var(--space-lg) !important;
  border-radius: var(--radius-full) !important;
  font-size: var(--text-xs) !important;
  font-weight: 700 !important;
  display: flex !important;
  align-items: center !important;
  gap: var(--space-xs) !important;
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.4) !important;
  z-index: 10 !important;
}

.featured-icon {
  font-size: var(--text-sm) !important;
}

/* Scenario Header */
.scenario-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: flex-start !important;
  margin-bottom: var(--space-4xl) !important;
  gap: var(--space-lg) !important;
}

.scenario-badge {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-md) !important;
  flex: 1 !important;
}

.badge-icon {
  width: 50px !important;
  height: 50px !important;
  border-radius: var(--radius-lg) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: var(--text-xl) !important;
  flex-shrink: 0 !important;
}

.scenario-badge.conservative .badge-icon {
  background: linear-gradient(135deg, #3B82F6, #1E40AF) !important;
  color: white !important;
}

.scenario-badge.growth .badge-icon {
  background: linear-gradient(135deg, #10B981, #059669) !important;
  color: white !important;
}

.scenario-badge.serious .badge-icon {
  background: linear-gradient(135deg, var(--gold), #B8860B) !important;
  color: var(--bg) !important;
}

.badge-content {
  flex: 1 !important;
}

.badge-title {
  color: var(--text) !important;
  font-size: var(--text-lg) !important;
  font-weight: 700 !important;
  margin-bottom: var(--space-xs) !important;
}

.badge-subtitle {
  color: var(--text-muted) !important;
  font-size: var(--text-sm) !important;
  font-weight: 500 !important;
}

/* Risk Indicator */
.risk-indicator {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  gap: var(--space-sm) !important;
}

.risk-label {
  color: var(--text-muted) !important;
  font-size: var(--text-xs) !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.risk-bars {
  display: flex !important;
  gap: var(--space-xs) !important;
}

.risk-bar {
  width: 8px !important;
  height: 20px !important;
  background: rgba(255, 255, 255, 0.1) !important;
  border-radius: var(--radius-sm) !important;
  transition: all 0.3s ease !important;
}

.risk-bar.active {
  background: var(--gold) !important;
  box-shadow: 0 2px 8px rgba(212, 175, 55, 0.4) !important;
}

.risk-indicator.low .risk-bar.active {
  background: #10B981 !important;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.4) !important;
}

.risk-indicator.medium .risk-bar.active {
  background: #F59E0B !important;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.4) !important;
}

.risk-indicator.high .risk-bar.active {
  background: #EF4444 !important;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4) !important;
}

/* Investment Section */
.scenario-investment {
  background: rgba(0, 0, 0, 0.4) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  margin-bottom: var(--space-xl) !important;
}

.investment-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: var(--space-lg) !important;
  padding-bottom: var(--space-lg) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.investment-title {
  color: var(--text-muted) !important;
  font-size: var(--text-sm) !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.investment-amount {
  color: var(--gold) !important;
  font-size: var(--text-3xl) !important;
  font-weight: 800 !important;
  text-shadow: 0 2px 8px rgba(212, 175, 55, 0.3) !important;
}

.investment-details {
  display: flex !important;
  flex-direction: column !important;
  gap: var(--space-md) !important;
}

.detail-item {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: var(--space-sm) var(--space-md) !important;
  background: rgba(255, 255, 255, 0.02) !important;
  border-radius: var(--radius-md) !important;
  border: 1px solid rgba(255, 255, 255, 0.05) !important;
}

.detail-label {
  color: var(--text-muted) !important;
  font-size: var(--text-sm) !important;
  font-weight: 500 !important;
}

.detail-value {
  color: var(--text) !important;
  font-size: var(--text-sm) !important;
  font-weight: 600 !important;
}

/* Projections Section */
.scenario-projections {
  background: rgba(0, 0, 0, 0.4) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  margin-bottom: var(--space-xl) !important;
}

.projections-header h4 {
  color: var(--text) !important;
  font-size: var(--text-lg) !important;
  font-weight: 700 !important;
  margin-bottom: var(--space-lg) !important;
  text-align: center !important;
}

.projection-timeline {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: var(--space-xl) !important;
  position: relative !important;
}

.projection-timeline::before {
  content: '' !important;
  position: absolute !important;
  top: 50% !important;
  left: 20% !important;
  right: 20% !important;
  height: 2px !important;
  background: linear-gradient(90deg, var(--gold), transparent, var(--gold)) !important;
  transform: translateY(-50%) !important;
}

.projection-year {
  background: rgba(212, 175, 55, 0.1) !important;
  border: 1px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-lg) !important;
  text-align: center !important;
  position: relative !important;
  z-index: 2 !important;
  min-width: 100px !important;
}

.year-label {
  color: var(--text-muted) !important;
  font-size: var(--text-sm) !important;
  font-weight: 600 !important;
  margin-bottom: var(--space-xs) !important;
}

.year-value {
  color: var(--gold) !important;
  font-size: var(--text-xl) !important;
  font-weight: 800 !important;
}

.projection-summary {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  gap: var(--space-lg) !important;
}

.summary-item {
  background: rgba(255, 255, 255, 0.02) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: var(--radius-md) !important;
  padding: var(--space-lg) !important;
  text-align: center !important;
}

.summary-item.total {
  border-color: rgba(16, 185, 129, 0.3) !important;
  background: rgba(16, 185, 129, 0.05) !important;
}

.summary-item.roi {
  border-color: rgba(212, 175, 55, 0.3) !important;
  background: rgba(212, 175, 55, 0.05) !important;
}

.summary-label {
  color: var(--text-muted) !important;
  font-size: var(--text-sm) !important;
  font-weight: 600 !important;
  margin-bottom: var(--space-xs) !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.summary-value {
  color: var(--gold) !important;
  font-size: var(--text-2xl) !important;
  font-weight: 800 !important;
}

/* Benefits Section */
.scenario-benefits {
  background: rgba(0, 0, 0, 0.4) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
}

.benefits-header {
  color: var(--text) !important;
  font-size: var(--text-lg) !important;
  font-weight: 700 !important;
  margin-bottom: var(--space-lg) !important;
  text-align: center !important;
}

.benefits-list {
  display: flex !important;
  flex-direction: column !important;
  gap: var(--space-md) !important;
}

.benefit-item {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-md) !important;
  padding: var(--space-md) !important;
  background: rgba(255, 255, 255, 0.02) !important;
  border: 1px solid rgba(255, 255, 255, 0.05) !important;
  border-radius: var(--radius-md) !important;
  transition: all 0.2s ease !important;
}

.benefit-item:hover {
  background: rgba(212, 175, 55, 0.05) !important;
  border-color: rgba(212, 175, 55, 0.2) !important;
}

.benefit-icon {
  width: 20px !important;
  height: 20px !important;
  background: linear-gradient(135deg, #10B981, #059669) !important;
  border-radius: var(--radius-full) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: white !important;
  font-size: var(--text-xs) !important;
  font-weight: 700 !important;
  flex-shrink: 0 !important;
}

.benefit-text {
  color: var(--text) !important;
  font-size: var(--text-sm) !important;
  font-weight: 500 !important;
  line-height: 1.4 !important;
}

/* Responsive Design for Professional Scenarios */
@media (max-width: 1200px) {
  .professional-scenarios-grid {
    grid-template-columns: 1fr !important;
    gap: var(--space-3xl) !important;
  }

  .overview-metrics {
    grid-template-columns: repeat(3, 1fr) !important;
    gap: var(--space-lg) !important;
  }
}

@media (max-width: 768px) {
  .investment-overview {
    padding: var(--space-xl) !important;
  }

  .overview-header {
    flex-direction: column !important;
    text-align: center !important;
    gap: var(--space-md) !important;
  }

  .overview-metrics {
    grid-template-columns: 1fr !important;
    gap: var(--space-md) !important;
  }

  .professional-scenario-card {
    padding: var(--space-xl) !important;
  }

  .scenario-header {
    flex-direction: column !important;
    gap: var(--space-md) !important;
  }

  .scenario-badge {
    justify-content: center !important;
    text-align: center !important;
  }

  .risk-indicator {
    align-self: center !important;
  }

  .investment-header {
    flex-direction: column !important;
    gap: var(--space-md) !important;
    text-align: center !important;
  }

  .projection-timeline {
    flex-direction: column !important;
    gap: var(--space-lg) !important;
  }

  .projection-timeline::before {
    display: none !important;
  }

  .projection-summary {
    grid-template-columns: 1fr !important;
    gap: var(--space-md) !important;
  }

  .featured-badge {
    position: static !important;
    align-self: center !important;
    margin-bottom: var(--space-lg) !important;
  }
}

@media (max-width: 480px) {
  .professional-scenarios-grid {
    gap: var(--space-xl) !important;
  }

  .professional-scenario-card {
    padding: var(--space-lg) !important;
  }

  .investment-amount {
    font-size: var(--text-2xl) !important;
  }

  .year-value {
    font-size: var(--text-lg) !important;
  }

  .summary-value {
    font-size: var(--text-xl) !important;
  }

  .badge-icon {
    width: 40px !important;
    height: 40px !important;
    font-size: var(--text-lg) !important;
  }

  .overview-icon {
    width: 50px !important;
    height: 50px !important;
    font-size: var(--text-xl) !important;
  }
}

/* Formula Explanation Styling */
.formula-explanation {
  color: var(--text-muted) !important;
  font-size: var(--text-sm) !important;
  line-height: 1.6 !important;
  text-align: center !important;
  background: rgba(212, 175, 55, 0.1) !important;
  border: 1px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: var(--radius-md) !important;
  padding: var(--space-lg) !important;
  margin-bottom: var(--space-xl) !important;
}

.dividend-breakdown {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  gap: var(--space-xl) !important;
}

.breakdown-year {
  background: rgba(0, 0, 0, 0.3) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-lg) !important;
}

.year-header {
  color: var(--gold) !important;
  font-size: var(--text-lg) !important;
  font-weight: 700 !important;
  text-align: center !important;
  margin-bottom: var(--space-md) !important;
  padding-bottom: var(--space-md) !important;
  border-bottom: 1px solid rgba(212, 175, 55, 0.3) !important;
}

.dividend-calculation {
  display: flex !important;
  flex-direction: column !important;
  gap: var(--space-md) !important;
}

.calc-step {
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: var(--radius-md) !important;
  padding: var(--space-md) !important;
  color: var(--text) !important;
  font-size: var(--text-sm) !important;
  font-weight: 600 !important;
  text-align: center !important;
}

@media (max-width: 768px) {
  .dividend-breakdown {
    grid-template-columns: 1fr !important;
    gap: var(--space-lg) !important;
  }
}

/* ========================================
   MINE PRODUCTION PAGE STYLING
======================================== */

/* Comparison Section */
.comparison-section {
  padding: var(--space-5xl) 0 !important;
  background: linear-gradient(135deg, var(--surface) 0%, rgba(255, 215, 0, 0.03) 100%) !important;
}

.comparison-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)) !important;
  gap: var(--space-4xl) !important;
  margin-top: var(--space-4xl) !important;
}

.comparison-card {
  background: var(--surface) !important;
  border: 2px solid var(--border) !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--space-2xl) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.comparison-card.wash-plants {
  border-color: var(--emerald) !important;
}

.comparison-card.wash-plants::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 4px !important;
  background: linear-gradient(90deg, var(--emerald), rgba(46, 204, 113, 0.5)) !important;
}

.comparison-card.strip-mining {
  border-color: var(--warning) !important;
}

.comparison-card.strip-mining::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 4px !important;
  background: linear-gradient(90deg, var(--warning), rgba(224, 123, 57, 0.5)) !important;
}

.comparison-card:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
}

.comparison-card h3 {
  font-size: var(--font-size-xl) !important;
  font-weight: var(--font-weight-bold) !important;
  color: var(--text) !important;
  margin-bottom: var(--space-xl) !important;
  text-align: center !important;
}

.benefit-list,
.drawback-list {
  list-style: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.benefit-list li,
.drawback-list li {
  padding: var(--space-md) 0 !important;
  font-size: var(--font-size-base) !important;
  color: var(--text) !important;
  border-bottom: 1px solid var(--border) !important;
  transition: all 0.2s ease !important;
}

.benefit-list li:last-child,
.drawback-list li:last-child {
  border-bottom: none !important;
}

.benefit-list li:hover {
  color: var(--emerald) !important;
  padding-left: var(--space-sm) !important;
}

.drawback-list li:hover {
  color: var(--warning) !important;
  padding-left: var(--space-sm) !important;
}

/* Technical Specifications Section */
.technical-section {
  padding: var(--space-5xl) 0 !important;
  background: var(--bg) !important;
}

.tech-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
  gap: var(--space-xl) !important;
  margin-top: var(--space-4xl) !important;
}

.tech-card {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  text-align: center !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.tech-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 3px !important;
  background: linear-gradient(90deg, var(--blue), rgba(0, 191, 255, 0.5)) !important;
}

.tech-card:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 15px 35px rgba(0, 191, 255, 0.2) !important;
  border-color: var(--blue) !important;
}

.tech-card h3 {
  color: var(--text) !important;
  font-size: var(--font-size-lg) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: var(--space-lg) !important;
}

.tech-stat {
  font-size: var(--font-size-3xl) !important;
  font-weight: var(--font-weight-extrabold) !important;
  color: var(--blue) !important;
  margin-bottom: var(--space-lg) !important;
  text-shadow: 0 0 10px rgba(0, 191, 255, 0.3) !important;
}

.tech-card p {
  color: var(--text-muted) !important;
  font-size: var(--font-size-sm) !important;
  line-height: var(--line-height-relaxed) !important;
}

/* Environmental Benefits Section */
.environmental-section {
  padding: var(--space-5xl) 0 !important;
  background: linear-gradient(135deg, var(--surface) 0%, rgba(46, 204, 113, 0.03) 100%) !important;
}

.environmental-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
  gap: var(--space-xl) !important;
  margin-top: var(--space-4xl) !important;
}

.env-benefit {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  text-align: center !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.env-benefit::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 3px !important;
  background: linear-gradient(90deg, var(--emerald), rgba(46, 204, 113, 0.5)) !important;
}

.env-benefit:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 15px 35px rgba(46, 204, 113, 0.2) !important;
  border-color: var(--emerald) !important;
}

.env-icon {
  font-size: 3rem !important;
  margin-bottom: var(--space-lg) !important;
  display: block !important;
}

.env-benefit h3 {
  color: var(--text) !important;
  font-size: var(--font-size-xl) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: var(--space-lg) !important;
}

.env-benefit p {
  color: var(--text-muted) !important;
  font-size: var(--font-size-sm) !important;
  line-height: var(--line-height-relaxed) !important;
}

/* Investment Appeal Section */
.investment-section {
  padding: var(--space-5xl) 0 !important;
  background: var(--bg) !important;
}

.investment-points {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
  gap: var(--space-xl) !important;
  margin-top: var(--space-4xl) !important;
}

.investment-card {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  text-align: left !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.investment-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 3px !important;
  background: linear-gradient(90deg, var(--gold), rgba(212, 175, 55, 0.5)) !important;
}

.investment-card:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 15px 35px rgba(212, 175, 55, 0.2) !important;
  border-color: var(--gold) !important;
}

.investment-card h3 {
  color: var(--text) !important;
  font-size: var(--font-size-xl) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-bottom: var(--space-lg) !important;
}

.investment-card p {
  color: var(--text-muted) !important;
  font-size: var(--font-size-sm) !important;
  line-height: var(--line-height-relaxed) !important;
}

/* CTA Section */
.cta-section {
  padding: var(--space-5xl) 0 !important;
  background: linear-gradient(135deg, var(--surface) 0%, rgba(255, 215, 0, 0.05) 100%) !important;
  text-align: center !important;
}

.cta-content {
  max-width: 800px !important;
  margin: 0 auto !important;
}

.cta-content h2 {
  font-size: var(--font-size-3xl) !important;
  font-weight: var(--font-weight-bold) !important;
  color: var(--text) !important;
  margin-bottom: var(--space-lg) !important;
}

.cta-content p {
  font-size: var(--font-size-lg) !important;
  color: var(--text-muted) !important;
  margin-bottom: var(--space-4xl) !important;
  line-height: var(--line-height-relaxed) !important;
}

/* Operational Media Section */
.operational-media {
  padding: var(--space-5xl) 0 !important;
  background: var(--bg) !important;
}

.media-section {
  margin-bottom: var(--space-5xl) !important;
}

.media-section h3 {
  font-size: var(--font-size-2xl) !important;
  font-weight: var(--font-weight-bold) !important;
  color: var(--text) !important;
  margin-bottom: var(--space-4xl) !important;
  text-align: center !important;
}

.video-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
  gap: var(--space-xl) !important;
  margin-bottom: var(--space-4xl) !important;
}

.video-placeholder {
  background: var(--surface) !important;
  border: 2px dashed var(--border) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-2xl) !important;
  text-align: center !important;
  transition: all 0.3s ease !important;
  min-height: 200px !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
}

.video-placeholder:hover {
  border-color: var(--gold) !important;
  background: rgba(255, 215, 0, 0.05) !important;
  transform: translateY(-3px) !important;
}

.video-icon {
  font-size: 3rem !important;
  margin-bottom: var(--space-lg) !important;
  opacity: 0.7 !important;
}

.video-placeholder h4 {
  font-size: var(--font-size-lg) !important;
  font-weight: var(--font-weight-bold) !important;
  color: var(--text) !important;
  margin-bottom: var(--space-md) !important;
}

.video-placeholder p {
  font-size: var(--font-size-sm) !important;
  color: var(--text-muted) !important;
  margin-bottom: var(--space-lg) !important;
  line-height: var(--line-height-relaxed) !important;
}

.placeholder-note {
  font-size: var(--font-size-xs) !important;
  color: var(--gold) !important;
  font-weight: var(--font-weight-semibold) !important;
  background: rgba(255, 215, 0, 0.1) !important;
  border: 1px solid rgba(255, 215, 0, 0.3) !important;
  border-radius: var(--radius-md) !important;
  padding: var(--space-sm) var(--space-md) !important;
}

.photo-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
  gap: var(--space-lg) !important;
}

.photo-placeholder {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  text-align: center !important;
  transition: all 0.3s ease !important;
  min-height: 150px !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
}

.photo-placeholder:hover {
  border-color: var(--blue) !important;
  background: rgba(0, 191, 255, 0.05) !important;
  transform: translateY(-3px) !important;
}

.photo-icon {
  font-size: 2rem !important;
  margin-bottom: var(--space-md) !important;
  opacity: 0.7 !important;
}

.photo-placeholder h4 {
  font-size: var(--font-size-base) !important;
  font-weight: var(--font-weight-bold) !important;
  color: var(--text) !important;
  margin-bottom: var(--space-sm) !important;
}

.photo-placeholder p {
  font-size: var(--font-size-xs) !important;
  color: var(--text-muted) !important;
  line-height: var(--line-height-relaxed) !important;
}

/* Media Note */
.media-note {
  background: rgba(0, 191, 255, 0.1) !important;
  border: 1px solid rgba(0, 191, 255, 0.3) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-xl) !important;
  display: flex !important;
  align-items: flex-start !important;
  gap: var(--space-lg) !important;
  margin-top: var(--space-4xl) !important;
}

.note-icon {
  font-size: 2rem !important;
  color: var(--blue) !important;
  flex-shrink: 0 !important;
  margin-top: var(--space-xs) !important;
}

.note-content h4 {
  font-size: var(--font-size-lg) !important;
  font-weight: var(--font-weight-bold) !important;
  color: var(--text) !important;
  margin-bottom: var(--space-md) !important;
}

.note-content p {
  font-size: var(--font-size-sm) !important;
  color: var(--text-muted) !important;
  line-height: var(--line-height-relaxed) !important;
  margin: 0 !important;
}

/* Mobile Responsiveness for Mine Production */
@media (max-width: 768px) {
  .comparison-grid {
    grid-template-columns: 1fr !important;
    gap: var(--space-2xl) !important;
  }

  .comparison-card {
    padding: var(--space-xl) !important;
  }

  .tech-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: var(--space-lg) !important;
  }

  .environmental-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: var(--space-lg) !important;
  }

  .investment-points {
    grid-template-columns: 1fr !important;
    gap: var(--space-lg) !important;
  }

  .video-grid {
    grid-template-columns: 1fr !important;
    gap: var(--space-lg) !important;
  }

  .photo-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: var(--space-md) !important;
  }

  .media-note {
    flex-direction: column !important;
    text-align: center !important;
  }

  .note-icon {
    margin-top: 0 !important;
  }
}

@media (max-width: 480px) {
  .tech-grid {
    grid-template-columns: 1fr !important;
  }

  .environmental-grid {
    grid-template-columns: 1fr !important;
  }

  .photo-grid {
    grid-template-columns: 1fr !important;
  }

  .cta-content h2 {
    font-size: var(--font-size-2xl) !important;
  }

  .cta-content p {
    font-size: var(--font-size-base) !important;
  }
}

/* ========================================
   PURCHASE SHARES PAGE - PROFESSIONAL AUTHENTICATION
======================================== */

.auth-section {
  background: var(--surface);
  padding: var(--space-4xl) 0;
  border-top: 1px solid var(--border);
}

.auth-container {
  max-width: 800px;
  margin: 0 auto;
}

.tab-navigation {
  display: flex;
  gap: var(--space-sm);
  margin-bottom: var(--space-2xl);
  background: var(--bg);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--space-sm);
}

.tab-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-lg) var(--space-xl);
  border: none;
  border-radius: var(--radius-md);
  background: transparent;
  color: var(--text-muted);
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.tab-button:hover {
  background: var(--glass-overlay);
  color: var(--text);
}

.tab-button.active {
  background: var(--gold);
  color: var(--bg);
  box-shadow: var(--glow);
}

.tab-icon {
  font-size: var(--font-size-lg);
}

.auth-content {
  margin-top: var(--space-2xl);
}

.telegram-options {
  display: grid;
  gap: var(--space-xl);
}

.option-card {
  display: flex;
  gap: var(--space-lg);
  padding: var(--space-xl);
  background: var(--bg);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
}

.option-card:hover {
  border-color: var(--gold);
  box-shadow: var(--glow);
}

.option-icon {
  font-size: var(--font-size-2xl);
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--glass-overlay);
  border-radius: var(--radius-lg);
  flex-shrink: 0;
}

.option-content {
  flex: 1;
}

.option-content h3 {
  color: var(--text);
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--space-sm);
}

.option-content p {
  color: var(--text-muted);
  margin-bottom: var(--space-lg);
}

.registration-steps {
  text-align: center;
}

.step-indicator {
  display: flex;
  justify-content: center;
  gap: var(--space-xl);
  margin-bottom: var(--space-2xl);
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-sm);
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--border);
  color: var(--text-muted);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: all 0.3s ease;
}

.step.active .step-number {
  background: var(--gold);
  color: var(--bg);
  box-shadow: var(--glow);
}

.step-label {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  font-weight: 500;
}

.step.active .step-label {
  color: var(--gold);
}

.step-content {
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
}

.step-content h3 {
  color: var(--text);
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--space-sm);
}

.step-content p {
  color: var(--text-muted);
  margin-bottom: var(--space-xl);
}

.auth-footer {
  text-align: center;
  margin-top: var(--space-xl);
  padding-top: var(--space-xl);
  border-top: 1px solid var(--border);
}

.auth-footer p {
  color: var(--text-muted);
}

.link-button {
  background: none;
  border: none;
  color: var(--gold);
  text-decoration: underline;
  cursor: pointer;
  font-weight: 500;
  margin-left: var(--space-sm);
}

.link-button:hover {
  color: var(--cta-hover-bg);
}

.trust-section {
  background: var(--bg);
  padding: var(--space-4xl) 0;
  border-top: 1px solid var(--border);
}

.trust-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-xl);
}

.trust-card {
  text-align: center;
  padding: var(--space-xl);
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
}

.trust-card:hover {
  border-color: var(--gold);
  box-shadow: var(--glow);
  transform: translateY(-2px);
}

.trust-icon {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--space-lg);
  display: block;
}

.trust-card h3 {
  color: var(--text);
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--space-sm);
}

.trust-card p {
  color: var(--text-muted);
  line-height: var(--line-height-relaxed);
}

/* Loading States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  gap: var(--space-lg);
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 3px solid var(--border);
  border-top: 3px solid var(--gold);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: var(--text-muted);
  font-size: var(--font-size-lg);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design for Purchase Shares */
@media (max-width: 768px) {
  .tab-navigation {
    flex-direction: column;
    gap: var(--space-xs);
  }

  .tab-button {
    padding: var(--space-md) var(--space-lg);
  }

  .option-card {
    flex-direction: column;
    text-align: center;
    gap: var(--space-md);
  }

  .step-indicator {
    gap: var(--space-lg);
  }

  .trust-grid {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }
}

/* ========================================
   GOLD PRICE CHART SECTION
======================================== */

.gold-price-section {
  padding: var(--space-6xl) 0;
  background: var(--bg);
  border-top: 1px solid var(--border);
}

.chart-wrapper {
  background: var(--surface);
  border-radius: var(--radius-xl);
  padding: var(--space-2xl);
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
  margin: var(--space-4xl) 0;
}

.gold-price-chart {
  width: 100%;
}

.chart-header {
  margin-bottom: var(--space-2xl);
}

.chart-container {
  position: relative;
  background: var(--surface);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.chart-controls {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-sm);
  justify-content: center;
  margin-top: var(--space-lg);
}

.chart-controls select {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-sm);
  padding: var(--space-sm) var(--space-md);
  color: var(--text);
  font-size: var(--font-size-sm);
  transition: all 0.2s ease;
}

.chart-controls select:hover {
  border-color: var(--gold);
}

.chart-controls select:focus {
  outline: none;
  border-color: var(--gold);
  box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.2);
}

/* Gold Insights Grid */
.gold-insights {
  margin-top: var(--space-4xl);
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-2xl);
  margin-top: var(--space-2xl);
}

.insight-card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  padding: var(--space-2xl);
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.insight-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--gold), var(--copper));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.insight-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow);
  border-color: var(--gold);
}

.insight-card:hover::before {
  opacity: 1;
}

.insight-icon {
  font-size: 2.5rem;
  margin-bottom: var(--space-lg);
  display: block;
}

.insight-card h3 {
  color: var(--text);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-md);
}

.insight-card p {
  color: var(--text-muted);
  line-height: var(--line-height-relaxed);
  font-size: var(--font-size-base);
}

/* Responsive Design for Gold Price Chart */
@media (max-width: 768px) {
  .gold-price-section {
    padding: var(--space-4xl) 0;
  }

  .chart-wrapper {
    padding: var(--space-lg);
    margin: var(--space-2xl) 0;
  }

  .chart-header {
    text-align: center;
  }

  .chart-header .flex {
    flex-direction: column;
    gap: var(--space-lg);
  }

  .insights-grid {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }

  .insight-card {
    padding: var(--space-xl);
  }

  .chart-controls {
    flex-direction: column;
    align-items: center;
  }

  .chart-controls select {
    width: 200px;
  }
}
