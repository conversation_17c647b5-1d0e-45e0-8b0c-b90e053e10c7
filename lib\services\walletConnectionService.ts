import { supabase, getServiceRoleClient } from '../supabase';

// SafePal Wallet Connection Service
export interface WalletConnectionResult {
  success: boolean;
  address?: string;
  error?: string;
  provider?: any;
}

export interface TransactionResult {
  success: boolean;
  hash?: string;
  error?: string;
  receipt?: any;
}

export interface PaymentTransaction {
  userId: number;
  amount: number;
  shares: number;
  network: string;
  receiverAddress: string;
  senderAddress: string;
}

class WalletConnectionService {
  private provider: any = null;
  private connectedAddress: string | null = null;
  private isConnecting: boolean = false;

  /**
   * Check if SafePal wallet is available
   */
  isSafePalAvailable(): boolean {
    return typeof window !== 'undefined' && 
           (window as any).safepal && 
           (window as any).safepal.ethereum;
  }

  /**
   * Check if MetaMask is available as fallback
   */
  isMetaMaskAvailable(): boolean {
    return typeof window !== 'undefined' && 
           (window as any).ethereum && 
           (window as any).ethereum.isMetaMask;
  }

  /**
   * Get available wallet providers
   */
  getAvailableWallets(): string[] {
    const wallets: string[] = [];
    
    if (this.isSafePalAvailable()) {
      wallets.push('SafePal');
    }
    
    if (this.isMetaMaskAvailable()) {
      wallets.push('MetaMask');
    }
    
    return wallets;
  }

  /**
   * Connect to SafePal wallet
   */
  async connectSafePal(): Promise<WalletConnectionResult> {
    if (this.isConnecting) {
      return { success: false, error: 'Connection already in progress' };
    }

    if (!this.isSafePalAvailable()) {
      return { 
        success: false, 
        error: 'SafePal wallet not detected. Please install SafePal browser extension.' 
      };
    }

    this.isConnecting = true;

    try {
      console.log('🔗 Connecting to SafePal wallet...');
      
      const safepal = (window as any).safepal;
      this.provider = safepal.ethereum;

      // Request account access
      const accounts = await this.provider.request({
        method: 'eth_requestAccounts',
      });

      if (!accounts || accounts.length === 0) {
        throw new Error('No accounts found in SafePal wallet');
      }

      this.connectedAddress = accounts[0];
      
      console.log('✅ SafePal wallet connected:', this.connectedAddress);

      // Set up event listeners
      this.setupEventListeners();

      return {
        success: true,
        address: this.connectedAddress,
        provider: this.provider
      };

    } catch (error: any) {
      console.error('❌ SafePal connection failed:', error);
      return {
        success: false,
        error: error.message || 'Failed to connect to SafePal wallet'
      };
    } finally {
      this.isConnecting = false;
    }
  }

  /**
   * Connect to MetaMask as fallback
   */
  async connectMetaMask(): Promise<WalletConnectionResult> {
    if (this.isConnecting) {
      return { success: false, error: 'Connection already in progress' };
    }

    if (!this.isMetaMaskAvailable()) {
      return { 
        success: false, 
        error: 'MetaMask not detected. Please install MetaMask browser extension.' 
      };
    }

    this.isConnecting = true;

    try {
      console.log('🔗 Connecting to MetaMask wallet...');
      
      this.provider = (window as any).ethereum;

      // Request account access
      const accounts = await this.provider.request({
        method: 'eth_requestAccounts',
      });

      if (!accounts || accounts.length === 0) {
        throw new Error('No accounts found in MetaMask wallet');
      }

      this.connectedAddress = accounts[0];
      
      console.log('✅ MetaMask wallet connected:', this.connectedAddress);

      // Set up event listeners
      this.setupEventListeners();

      return {
        success: true,
        address: this.connectedAddress,
        provider: this.provider
      };

    } catch (error: any) {
      console.error('❌ MetaMask connection failed:', error);
      return {
        success: false,
        error: error.message || 'Failed to connect to MetaMask wallet'
      };
    } finally {
      this.isConnecting = false;
    }
  }

  /**
   * Set up wallet event listeners
   */
  private setupEventListeners(): void {
    if (!this.provider) return;

    // Account changed
    this.provider.on('accountsChanged', (accounts: string[]) => {
      console.log('👤 Wallet accounts changed:', accounts);
      if (accounts.length === 0) {
        this.disconnect();
      } else {
        this.connectedAddress = accounts[0];
      }
    });

    // Chain changed
    this.provider.on('chainChanged', (chainId: string) => {
      console.log('🔗 Wallet chain changed:', chainId);
      // Reload the page to reset the dapp state
      window.location.reload();
    });

    // Disconnect
    this.provider.on('disconnect', () => {
      console.log('🔌 Wallet disconnected');
      this.disconnect();
    });
  }

  /**
   * Disconnect wallet
   */
  disconnect(): void {
    this.provider = null;
    this.connectedAddress = null;
    console.log('🔌 Wallet disconnected');
  }

  /**
   * Get connected wallet address
   */
  getConnectedAddress(): string | null {
    return this.connectedAddress;
  }

  /**
   * Check if wallet is connected
   */
  isConnected(): boolean {
    return this.provider !== null && this.connectedAddress !== null;
  }

  /**
   * Get network information
   */
  async getNetworkInfo(): Promise<{ chainId: string; networkName: string }> {
    if (!this.provider) {
      throw new Error('Wallet not connected');
    }

    const chainId = await this.provider.request({ method: 'eth_chainId' });
    
    // Map chain IDs to network names
    const networkMap: { [key: string]: string } = {
      '0x1': 'Ethereum Mainnet',
      '0x38': 'Binance Smart Chain',
      '0x89': 'Polygon',
      '0xa86a': 'Avalanche'
    };

    return {
      chainId,
      networkName: networkMap[chainId] || `Unknown Network (${chainId})`
    };
  }

  /**
   * Switch to specific network
   */
  async switchNetwork(chainId: string): Promise<boolean> {
    if (!this.provider) {
      throw new Error('Wallet not connected');
    }

    try {
      await this.provider.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId }],
      });
      return true;
    } catch (error: any) {
      console.error('❌ Failed to switch network:', error);
      return false;
    }
  }

  /**
   * Send USDT payment transaction
   */
  async sendUSDTPayment(transaction: PaymentTransaction): Promise<TransactionResult> {
    if (!this.isConnected()) {
      return { success: false, error: 'Wallet not connected' };
    }

    try {
      console.log('💸 Sending USDT payment:', transaction);

      // Get network info
      const networkInfo = await this.getNetworkInfo();
      console.log('🌐 Current network:', networkInfo);

      // USDT contract addresses for different networks
      const usdtContracts: { [key: string]: string } = {
        '0x38': '******************************************', // BSC - USDT (Binance-Peg)
        '0x89': '******************************************', // Polygon - USDT (PoS)
        '0x1': '******************************************'   // Ethereum - USDT
      };

      const contractAddress = usdtContracts[networkInfo.chainId];
      if (!contractAddress) {
        return {
          success: false,
          error: `USDT not supported on ${networkInfo.networkName}. Please switch to BSC, Polygon, or Ethereum.`
        };
      }

      // Validate network matches expected network for transaction
      const expectedChainIds: { [key: string]: string } = {
        'BSC': '0x38',
        'Polygon': '0x89',
        'Ethereum': '0x1'
      };

      const expectedChainId = expectedChainIds[transaction.network];
      if (expectedChainId && networkInfo.chainId !== expectedChainId) {
        return {
          success: false,
          error: `Wrong network. Please switch to ${transaction.network} (Chain ID: ${expectedChainId})`
        };
      }

      // Convert amount to proper format (USDT decimals vary by network)
      const decimals = networkInfo.chainId === '0x38' ? 18 : 6; // BSC USDT has 18, others have 6
      const amountBigInt = BigInt(Math.floor(transaction.amount * Math.pow(10, decimals)));
      const amountHex = amountBigInt.toString(16);

      // ERC-20 transfer function signature
      const transferFunction = '0xa9059cbb';

      // Remove 0x prefix and pad address to 32 bytes (64 hex chars)
      const cleanAddress = transaction.receiverAddress.replace('0x', '');
      const paddedAddress = cleanAddress.padStart(64, '0');

      // Pad amount to 32 bytes (64 hex chars)
      const paddedAmount = amountHex.padStart(64, '0');

      // Combine function signature + padded address + padded amount
      const data = transferFunction + paddedAddress + paddedAmount;

      console.log('🔧 Transaction data:', {
        contractAddress,
        transferFunction,
        receiverAddress: transaction.receiverAddress,
        paddedAddress,
        amount: transaction.amount,
        amountBigInt: amountBigInt.toString(),
        amountHex,
        paddedAmount,
        finalData: data
      });

      // Check USDT balance before sending
      try {
        const balance = await this.getUSDTBalance(contractAddress, decimals);
        console.log('💰 Current USDT balance:', balance);

        if (balance < transaction.amount) {
          return {
            success: false,
            error: `Insufficient USDT balance. Required: ${transaction.amount}, Available: ${balance.toFixed(6)}`
          };
        }
      } catch (balanceError) {
        console.warn('⚠️ Could not check balance:', balanceError);
        // Continue anyway - let the transaction fail naturally if insufficient funds
      }

      // Estimate gas for the transaction
      let gasLimit = '0x5208'; // Default gas limit
      try {
        const estimatedGas = await this.provider.request({
          method: 'eth_estimateGas',
          params: [{
            from: this.connectedAddress,
            to: contractAddress,
            data: data,
            value: '0x0'
          }],
        });
        // Add 20% buffer to estimated gas
        const gasBuffer = Math.floor(parseInt(estimatedGas, 16) * 1.2);
        gasLimit = '0x' + gasBuffer.toString(16);
        console.log('⛽ Estimated gas:', estimatedGas, 'with buffer:', gasLimit);
      } catch (gasError) {
        console.warn('⚠️ Gas estimation failed, using default:', gasError);
        // Use higher default for USDT transfers
        gasLimit = networkInfo.chainId === '0x1' ? '0x15f90' : '0x7530'; // Ethereum: 90k, others: 30k
      }

      // Prepare transaction parameters
      const txParams = {
        from: this.connectedAddress,
        to: contractAddress,
        data: data,
        value: '0x0', // No ETH value for ERC-20 transfer
        gas: gasLimit
      };

      console.log('📤 Sending transaction with params:', txParams);

      // Send transaction
      const txHash = await this.provider.request({
        method: 'eth_sendTransaction',
        params: [txParams],
      });

      console.log('✅ Transaction sent:', txHash);

      // Wait for transaction receipt
      const receipt = await this.waitForTransaction(txHash);

      return {
        success: true,
        hash: txHash,
        receipt: receipt
      };

    } catch (error: any) {
      console.error('❌ Payment transaction failed:', error);
      return {
        success: false,
        error: error.message || 'Transaction failed'
      };
    }
  }

  /**
   * Wait for transaction confirmation
   */
  private async waitForTransaction(txHash: string, maxWait: number = 300000): Promise<any> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWait) {
      try {
        const receipt = await this.provider.request({
          method: 'eth_getTransactionReceipt',
          params: [txHash],
        });

        if (receipt) {
          return receipt;
        }

        // Wait 2 seconds before checking again
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        console.warn('⚠️ Error checking transaction receipt:', error);
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    throw new Error('Transaction confirmation timeout');
  }

  /**
   * Get USDT balance for connected wallet
   */
  private async getUSDTBalance(contractAddress: string, decimals: number): Promise<number> {
    if (!this.provider || !this.connectedAddress) {
      throw new Error('Wallet not connected');
    }

    // ERC-20 balanceOf function signature
    const balanceOfFunction = '0x70a08231';
    const paddedAddress = this.connectedAddress.slice(2).padStart(64, '0');
    const data = balanceOfFunction + paddedAddress;

    const result = await this.provider.request({
      method: 'eth_call',
      params: [{
        to: contractAddress,
        data: data
      }, 'latest'],
    });

    // Convert hex result to decimal and adjust for decimals
    const balanceBigInt = BigInt(result);
    const balance = Number(balanceBigInt) / Math.pow(10, decimals);

    return balance;
  }
}

// Export singleton instance
export const walletConnectionService = new WalletConnectionService();
