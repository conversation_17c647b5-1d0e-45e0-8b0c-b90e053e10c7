import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function backfillCommissionPhases() {
  console.log('🔄 BACKFILLING COMMISSION PHASES\n');

  try {
    // Step 1: Check current state
    console.log('1️⃣ Checking current commission_transactions...');
    
    const { data: allCommissions, error: fetchError } = await supabase
      .from('commission_transactions')
      .select('id, payment_date, share_purchase_id, phase_id')
      .order('payment_date', { ascending: true });

    if (fetchError) {
      console.error('❌ Error fetching commissions:', fetchError);
      return;
    }

    console.log(`📊 Found ${allCommissions?.length || 0} total commission records`);
    
    const commissionsWithoutPhase = allCommissions?.filter(c => !c.phase_id) || [];
    console.log(`📊 Found ${commissionsWithoutPhase.length} commissions without phase_id`);

    if (commissionsWithoutPhase.length === 0) {
      console.log('✅ All commissions already have phase_id assigned!');
      return;
    }

    // Step 2: Get all investment phases for reference
    console.log('\n2️⃣ Getting investment phases...');
    
    const { data: phases, error: phasesError } = await supabase
      .from('investment_phases')
      .select('id, phase_number, phase_name, start_date, end_date, is_active')
      .order('phase_number', { ascending: true });

    if (phasesError) {
      console.error('❌ Error fetching phases:', phasesError);
      return;
    }

    console.log('✅ Available phases:');
    phases?.forEach(phase => {
      console.log(`   • Phase ${phase.phase_number}: ${phase.phase_name} (ID: ${phase.id}) ${phase.is_active ? '[ACTIVE]' : ''}`);
    });

    // Step 3: Backfill commissions with phase data
    console.log('\n3️⃣ Backfilling commission records...');
    
    let updated = 0;
    let errors = 0;

    for (const commission of commissionsWithoutPhase) {
      let phaseId = null;

      // Method 1: Try to get phase from linked share purchase
      if (commission.share_purchase_id) {
        const { data: purchase } = await supabase
          .from('aureus_share_purchases')
          .select('phase_id')
          .eq('id', commission.share_purchase_id)
          .single();
        
        if (purchase?.phase_id) {
          phaseId = purchase.phase_id;
          console.log(`   📎 Commission ${commission.id}: Found phase ${phaseId} from share purchase`);
        }
      }

      // Method 2: Determine phase by payment date
      if (!phaseId && commission.payment_date) {
        const paymentDate = new Date(commission.payment_date);
        
        // Find the phase that was active during the payment date
        for (const phase of phases || []) {
          const phaseStart = new Date(phase.start_date);
          const phaseEnd = phase.end_date ? new Date(phase.end_date) : new Date();
          
          if (paymentDate >= phaseStart && paymentDate <= phaseEnd) {
            phaseId = phase.id;
            console.log(`   📅 Commission ${commission.id}: Assigned to phase ${phase.phase_number} based on date ${commission.payment_date}`);
            break;
          }
        }
      }

      // Method 3: Fallback to current active phase
      if (!phaseId) {
        const activePhase = phases?.find(p => p.is_active);
        if (activePhase) {
          phaseId = activePhase.id;
          console.log(`   🔄 Commission ${commission.id}: Assigned to active phase ${activePhase.phase_number} (fallback)`);
        }
      }

      // Method 4: Final fallback to first phase
      if (!phaseId && phases && phases.length > 0) {
        phaseId = phases[0].id;
        console.log(`   ⚠️  Commission ${commission.id}: Assigned to first phase ${phases[0].phase_number} (final fallback)`);
      }

      // Update the commission record
      if (phaseId) {
        const { error: updateError } = await supabase
          .from('commission_transactions')
          .update({ phase_id: phaseId })
          .eq('id', commission.id);

        if (updateError) {
          console.error(`   ❌ Error updating commission ${commission.id}:`, updateError);
          errors++;
        } else {
          updated++;
        }
      } else {
        console.error(`   ❌ Could not determine phase for commission ${commission.id}`);
        errors++;
      }
    }

    console.log(`\n✅ Backfill completed: ${updated} updated, ${errors} errors`);

    // Step 4: Verify the results
    console.log('\n4️⃣ Verifying results...');
    
    const { data: verifyCommissions, error: verifyError } = await supabase
      .from('commission_transactions')
      .select('phase_id')
      .not('phase_id', 'is', null);

    if (verifyError) {
      console.error('❌ Error verifying results:', verifyError);
    } else {
      console.log(`✅ ${verifyCommissions?.length || 0} commissions now have phase_id assigned`);
    }

    // Step 5: Show sample phase-specific data
    console.log('\n5️⃣ Sample phase-specific commission data...');
    
    const { data: sampleData, error: sampleError } = await supabase
      .from('commission_transactions')
      .select(`
        referrer_id,
        usdt_commission,
        share_commission,
        phase_id,
        investment_phases(phase_name, phase_number)
      `)
      .not('phase_id', 'is', null)
      .limit(5);

    if (sampleError) {
      console.error('❌ Error fetching sample data:', sampleError);
    } else {
      console.log('📊 Sample commission records with phases:');
      sampleData?.forEach(comm => {
        console.log(`   • User ${comm.referrer_id}: $${comm.usdt_commission} USDT + ${comm.share_commission} shares in ${comm.investment_phases?.phase_name}`);
      });
    }

    console.log('\n🎉 Phase backfill completed! Competition leaderboards will now show real data.');

  } catch (error) {
    console.error('❌ Backfill failed:', error);
  }
}

// Run the script
backfillCommissionPhases();
