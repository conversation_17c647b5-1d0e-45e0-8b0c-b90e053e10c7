import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

const supabase = createClient(supabaseUrl, supabaseKey);

async function debugCompetitionState() {
  console.log('🔍 DEBUGGING COMPETITION STATE\n');

  try {
    // 1. Check investment phases
    console.log('📊 1. INVESTMENT PHASES:');
    const { data: phases, error: phasesError } = await supabase
      .from('investment_phases')
      .select('*')
      .order('phase_number');

    if (phasesError) {
      console.error('❌ Error fetching phases:', phasesError);
    } else {
      phases.forEach(phase => {
        console.log(`   Phase ${phase.phase_number}: ${phase.phase_name} - ${phase.is_active ? '🟢 ACTIVE' : '⚪ INACTIVE'}`);
        console.log(`     Price: $${phase.price_per_share}, Sold: ${phase.shares_sold}/${phase.total_shares_available}`);
      });
    }

    // 2. Check competitions
    console.log('\n🏆 2. COMPETITIONS:');
    const { data: competitions, error: compError } = await supabase
      .from('competitions')
      .select(`
        *,
        investment_phases (
          phase_number,
          phase_name,
          is_active,
          shares_sold,
          total_shares_available
        )
      `)
      .order('created_at', { ascending: false });

    if (compError) {
      console.error('❌ Error fetching competitions:', compError);
    } else {
      competitions.forEach(comp => {
        const phase = comp.investment_phases;
        console.log(`   ${comp.name} (${comp.status}) - Phase ${phase?.phase_number || 'N/A'}`);
        console.log(`     DB Status: ${comp.status}, Phase Active: ${phase?.is_active || false}`);
        console.log(`     Prize Pool: $${comp.total_prize_pool}, Min Qual: $${comp.minimum_qualification_amount}`);
      });
    }

    // 3. Check commission transactions (actual existing table)
    console.log('\n💰 3. COMMISSION TRANSACTIONS (Existing Data):');
    const { data: transactions, error: transError } = await supabase
      .from('commission_transactions')
      .select(`
        referrer_id,
        usdt_commission,
        share_purchase_amount,
        status,
        payment_date
      `)
      .eq('status', 'approved')
      .order('usdt_commission', { ascending: false })
      .limit(5);

    if (transError) {
      console.error('❌ Error fetching commission transactions:', transError);
    } else if (transactions.length === 0) {
      console.log('   ⚠️ NO COMMISSION TRANSACTIONS FOUND');
    } else {
      console.log('   Found commission transactions (not phase-specific):');
      transactions.forEach(trans => {
        console.log(`     User ${trans.referrer_id}: $${trans.usdt_commission} commission from $${trans.share_purchase_amount} purchase`);
      });
      console.log('   ⚠️ NOTE: These transactions are NOT linked to specific phases');
    }

    // 4. Check commission balances (cumulative data)
    console.log('\n💰 4. COMMISSION BALANCES (Cumulative):');
    const { data: balances, error: balError } = await supabase
      .from('commission_balances')
      .select(`
        user_id,
        total_earned_usdt,
        users (username, full_name)
      `)
      .gt('total_earned_usdt', 0)
      .order('total_earned_usdt', { ascending: false })
      .limit(5);

    if (balError) {
      console.error('❌ Error fetching commission balances:', balError);
    } else {
      console.log('   Top earners (cumulative):');
      balances.forEach(balance => {
        console.log(`     ${balance.users?.full_name || balance.users?.username} - $${balance.total_earned_usdt}`);
      });
    }

    // 5. Analysis
    console.log('\n📋 5. ANALYSIS:');
    console.log('   ✅ COMPETITION STATUS: Fixed - only active phase competitions are marked as active');
    console.log('   ⚠️ PHASE-SPECIFIC TRACKING: Not implemented in current database schema');
    console.log('   📝 CURRENT BEHAVIOR: Each phase competition will start fresh (no carryover data)');
    console.log('   🔧 LEADERBOARD LOGIC: Active phase uses cumulative data, inactive phases show empty');

    const activePhase = phases?.find(p => p.is_active);
    if (activePhase) {
      console.log(`   🎯 Current active phase: ${activePhase.phase_number} (${activePhase.phase_name})`);

      const activeCompetitions = competitions?.filter(c => c.status === 'active' && c.is_active);
      console.log(`   🏆 Active competitions: ${activeCompetitions?.length || 0}`);

      if (activeCompetitions && activeCompetitions.length > 0) {
        activeCompetitions.forEach(comp => {
          console.log(`      - ${comp.name}`);
        });
      }
    } else {
      console.log('   ⚠️ No active phase found');
    }

  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

debugCompetitionState();
