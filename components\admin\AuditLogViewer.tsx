import React, { useState, useEffect } from 'react'
import { supabase } from '../../lib/supabase'
import { withAdminAuth } from '../../lib/adminAuth'

interface AuditLog {
  id: string
  admin_email: string
  action: string
  target_type: string
  target_id: string
  old_values: any
  new_values: any
  details: any
  timestamp: string
  status: string
  error_message?: string
}

interface AuditLogViewerProps {
  adminUser?: any
  permissions?: any
}

const AuditLogViewerComponent: React.FC<AuditLogViewerProps> = ({ adminUser, permissions }) => {
  const [logs, setLogs] = useState<AuditLog[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [filterAction, setFilterAction] = useState<string>('all')
  const [filterTargetType, setFilterTargetType] = useState<string>('all')
  const [filterAdmin, setFilterAdmin] = useState<string>('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [logsPerPage] = useState(20)
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null)

  useEffect(() => {
    loadAuditLogs()
  }, [])

  const loadAuditLogs = async () => {
    try {
      setLoading(true)
      setError('')

      const { data, error } = await supabase
        .from('admin_audit_logs')
        .select('*')
        .order('timestamp', { ascending: false })
        .limit(500) // Limit to recent logs for performance

      if (error) {
        throw error
      }

      setLogs(data || [])
    } catch (err: any) {
      console.error('Error loading audit logs:', err)
      setError(err.message || 'Failed to load audit logs')
    } finally {
      setLoading(false)
    }
  }

  // Filter logs
  const filteredLogs = logs.filter(log => {
    const matchesAction = filterAction === 'all' || log.action === filterAction
    const matchesTargetType = filterTargetType === 'all' || log.target_type === filterTargetType
    const matchesAdmin = filterAdmin === 'all' || log.admin_email === filterAdmin
    
    return matchesAction && matchesTargetType && matchesAdmin
  })

  // Pagination
  const indexOfLastLog = currentPage * logsPerPage
  const indexOfFirstLog = indexOfLastLog - logsPerPage
  const currentLogs = filteredLogs.slice(indexOfFirstLog, indexOfLastLog)
  const totalPages = Math.ceil(filteredLogs.length / logsPerPage)

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'CREATE_USER': return '👤➕'
      case 'UPDATE_USER': return '👤✏️'
      case 'DELETE_USER': return '👤🗑️'
      case 'ACTIVATE_USER': return '👤✅'
      case 'DEACTIVATE_USER': return '👤❌'
      case 'APPROVE_PAYMENT': return '💰✅'
      case 'REJECT_PAYMENT': return '💰❌'
      case 'LOGIN': return '🔐🔓'
      case 'LOGOUT': return '🔐🔒'
      default: return '📝'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-400'
      case 'failed': return 'text-red-400'
      case 'partial': return 'text-yellow-400'
      case 'unknown': return 'text-gray-500'
      default: return 'text-gray-400'
    }
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString()
  }

  const getUniqueValues = (field: keyof AuditLog) => {
    return [...new Set(logs.map(log => log[field]))].filter(Boolean)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400"></div>
        <span className="ml-3 text-gray-300">Loading audit logs...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-white">📋 Audit Logs</h2>
          <p className="text-gray-400 mt-1">
            Track all administrative actions and system changes
          </p>
        </div>
        <div className="text-sm text-gray-400">
          Total Logs: {logs.length} | Filtered: {filteredLogs.length}
        </div>
      </div>

      {error && (
        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
          <p className="text-red-400">❌ {error}</p>
        </div>
      )}

      {/* Filters */}
      <div className="glass-card p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Action Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              🎯 Action
            </label>
            <select
              value={filterAction}
              onChange={(e) => setFilterAction(e.target.value)}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
            >
              <option value="all">All Actions</option>
              {getUniqueValues('action').map(action => (
                <option key={action} value={action}>{action}</option>
              ))}
            </select>
          </div>

          {/* Target Type Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              🎯 Target Type
            </label>
            <select
              value={filterTargetType}
              onChange={(e) => setFilterTargetType(e.target.value)}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
            >
              <option value="all">All Types</option>
              {getUniqueValues('target_type').map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>

          {/* Admin Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              👤 Admin
            </label>
            <select
              value={filterAdmin}
              onChange={(e) => setFilterAdmin(e.target.value)}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
            >
              <option value="all">All Admins</option>
              {getUniqueValues('admin_email').map(email => (
                <option key={email} value={email}>{email}</option>
              ))}
            </select>
          </div>

          {/* Refresh Button */}
          <div className="flex items-end">
            <button
              onClick={loadAuditLogs}
              className="w-full px-4 py-2 bg-yellow-500 text-black rounded-lg hover:bg-yellow-400 transition-colors"
            >
              🔄 Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Logs Table */}
      <div className="glass-card overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-800/50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Action
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Admin
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Target
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Timestamp
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Details
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {currentLogs.map((log) => (
                <tr key={log.id} className="hover:bg-gray-800/30 transition-colors">
                  <td className="px-4 py-4">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{getActionIcon(log.action)}</span>
                      <span className="text-sm font-medium text-white">
                        {(log.action || 'UNKNOWN').replace(/_/g, ' ')}
                      </span>
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="text-sm text-gray-300">{log.admin_email}</div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="text-sm text-gray-300">
                      <div>{log.target_type}</div>
                      <div className="text-xs text-gray-500">ID: {log.target_id}</div>
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="text-sm text-gray-400">
                      {formatTimestamp(log.timestamp)}
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <span className={`text-sm font-medium ${getStatusColor(log.status || 'unknown')}`}>
                      {(log.status || 'unknown').toUpperCase()}
                    </span>
                  </td>
                  <td className="px-4 py-4">
                    <button
                      onClick={() => setSelectedLog(log)}
                      className="text-blue-400 hover:text-blue-300 text-sm font-medium"
                    >
                      View Details
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-4 py-3 border-t border-gray-700 flex items-center justify-between">
            <div className="text-sm text-gray-400">
              Showing {indexOfFirstLog + 1} to {Math.min(indexOfLastLog, filteredLogs.length)} of {filteredLogs.length} logs
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600"
              >
                Previous
              </button>
              <span className="px-3 py-1 text-sm text-gray-300">
                Page {currentPage} of {totalPages}
              </span>
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Detail Modal */}
      {selectedLog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-900 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="sticky top-0 bg-gray-900 border-b border-gray-700 px-6 py-4">
              <div className="flex justify-between items-center">
                <h3 className="text-xl font-bold text-white">
                  📋 Audit Log Details
                </h3>
                <button
                  onClick={() => setSelectedLog(null)}
                  className="text-gray-400 hover:text-white text-2xl"
                >
                  ×
                </button>
              </div>
            </div>
            
            <div className="p-6 space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Action</label>
                  <p className="text-white">{getActionIcon(selectedLog.action)} {selectedLog.action}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Admin</label>
                  <p className="text-white">{selectedLog.admin_email}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Target</label>
                  <p className="text-white">{selectedLog.target_type} (ID: {selectedLog.target_id})</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Timestamp</label>
                  <p className="text-white">{formatTimestamp(selectedLog.timestamp)}</p>
                </div>
              </div>

              {/* Changes */}
              {(selectedLog.old_values && Object.keys(selectedLog.old_values).length > 0) && (
                <div>
                  <h4 className="text-lg font-semibold text-white mb-3">📝 Changes Made</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Old Values</label>
                      <pre className="bg-gray-800 p-3 rounded text-sm text-gray-300 overflow-auto">
                        {JSON.stringify(selectedLog.old_values, null, 2)}
                      </pre>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">New Values</label>
                      <pre className="bg-gray-800 p-3 rounded text-sm text-gray-300 overflow-auto">
                        {JSON.stringify(selectedLog.new_values, null, 2)}
                      </pre>
                    </div>
                  </div>
                </div>
              )}

              {/* Additional Details */}
              {selectedLog.details && Object.keys(selectedLog.details).length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-white mb-3">🔍 Additional Details</h4>
                  <pre className="bg-gray-800 p-3 rounded text-sm text-gray-300 overflow-auto">
                    {JSON.stringify(selectedLog.details, null, 2)}
                  </pre>
                </div>
              )}

              {/* Error Message */}
              {selectedLog.error_message && (
                <div>
                  <h4 className="text-lg font-semibold text-red-400 mb-3">❌ Error Message</h4>
                  <p className="text-red-300 bg-red-500/10 p-3 rounded">
                    {selectedLog.error_message}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// Export with admin authentication protection
export const AuditLogViewer = withAdminAuth(AuditLogViewerComponent, 'canViewAuditLogs')
