import React, { useState, useRef } from 'react';
import { supabase } from '../lib/supabase';

interface ProfilePictureUploadEnhancedProps {
  currentImageUrl?: string;
  userId: number;
  onImageUpdate: (newImageUrl: string) => void;
  size?: 'small' | 'medium' | 'large';
  editable?: boolean;
  userInitials?: string;
  userName?: string;
  showSaveButton?: boolean;
  autoSave?: boolean;
}

export const ProfilePictureUploadEnhanced: React.FC<ProfilePictureUploadEnhancedProps> = ({
  currentImageUrl,
  userId,
  onImageUpdate,
  size = 'medium',
  editable = true,
  userInitials = 'U',
  userName = 'User',
  showSaveButton = false,
  autoSave = true
}) => {
  const [uploading, setUploading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [pendingImageUrl, setPendingImageUrl] = useState<string | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const sizeClasses = {
    small: { container: '48px', text: '18px' },
    medium: { container: '80px', text: '28px' },
    large: { container: '120px', text: '40px' }
  };

  const currentSize = sizeClasses[size];

  // Clear messages after 3 seconds
  React.useEffect(() => {
    if (success || error) {
      const timer = setTimeout(() => {
        setSuccess(null);
        setError(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [success, error]);

  const uploadImageToStorage = async (file: File): Promise<string> => {
    // Create unique filename
    const fileExt = file.name.split('.').pop();
    const fileName = `profile-${userId}-${Date.now()}.${fileExt}`;

    // Upload to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('profile-pictures')
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (uploadError) {
      throw new Error(`Upload failed: ${uploadError.message}`);
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('profile-pictures')
      .getPublicUrl(uploadData.path);

    return publicUrl;
  };

  const saveProfilePicture = async (imageUrl: string) => {
    // Update user record in database
    const { error: updateError } = await supabase
      .from('users')
      .update({
        profile_image_url: imageUrl,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (updateError) {
      throw new Error(`Database update failed: ${updateError.message}`);
    }

    console.log('✅ Profile picture updated in database:', imageUrl);

    // Delete old image if it exists
    if (currentImageUrl && currentImageUrl.includes('profile-pictures')) {
      const oldPath = currentImageUrl.split('/profile-pictures/')[1];
      if (oldPath) {
        await supabase.storage
          .from('profile-pictures')
          .remove([oldPath]);
      }
    }

    // Update localStorage with new profile picture URL and timestamp
    const currentTimestamp = new Date().toISOString();
    const storedUser = localStorage.getItem('aureus_user');
    if (storedUser) {
      const userData = JSON.parse(storedUser);
      userData.profile_image_url = imageUrl;
      userData.updated_at = currentTimestamp;
      localStorage.setItem('aureus_user', JSON.stringify(userData));
      console.log('✅ Profile picture URL updated in localStorage with timestamp');
    }

    const storedTelegramUser = localStorage.getItem('aureus_telegram_user');
    if (storedTelegramUser) {
      const telegramUserData = JSON.parse(storedTelegramUser);
      if (telegramUserData.database_user) {
        telegramUserData.database_user.profile_image_url = imageUrl;
        telegramUserData.database_user.updated_at = currentTimestamp;
      }
      localStorage.setItem('aureus_telegram_user', JSON.stringify(telegramUserData));
      console.log('✅ Profile picture URL updated in Telegram user localStorage with timestamp');
    }

    onImageUpdate(imageUrl);
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('Please select an image file');
      return;
    }

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      setError('Image size must be less than 5MB');
      return;
    }

    setUploading(true);
    setError(null);
    setSuccess(null);

    try {
      // Upload image to storage
      const publicUrl = await uploadImageToStorage(file);

      if (autoSave) {
        // Auto-save mode: immediately save to database
        await saveProfilePicture(publicUrl);
        setSuccess('Profile picture updated successfully!');
      } else {
        // Manual save mode: store pending URL and show save button
        setPendingImageUrl(publicUrl);
        setHasUnsavedChanges(true);
        setSuccess('Image uploaded! Click Save to apply changes.');
      }
      
    } catch (err: any) {
      console.error('Profile picture upload error:', err);
      setError(err.message || 'Failed to upload image');
    } finally {
      setUploading(false);
    }
  };

  const handleSave = async () => {
    if (!pendingImageUrl) return;

    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      await saveProfilePicture(pendingImageUrl);
      setPendingImageUrl(null);
      setHasUnsavedChanges(false);
      setSuccess('Profile picture saved successfully!');
    } catch (err: any) {
      console.error('Profile picture save error:', err);
      setError(err.message || 'Failed to save profile picture');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    if (pendingImageUrl) {
      // Remove the uploaded but unsaved image from storage
      const fileName = pendingImageUrl.split('/profile-pictures/')[1];
      if (fileName) {
        supabase.storage
          .from('profile-pictures')
          .remove([fileName])
          .then(() => console.log('✅ Cancelled image removed from storage'));
      }
    }
    setPendingImageUrl(null);
    setHasUnsavedChanges(false);
    setError(null);
    setSuccess(null);
  };

  const handleClick = () => {
    if (editable && !uploading && !saving) {
      fileInputRef.current?.click();
    }
  };

  const displayImageUrl = pendingImageUrl || currentImageUrl;

  return (
    <div style={{ position: 'relative', display: 'inline-block' }}>
      {/* Profile Picture Container */}
      <div
        onClick={handleClick}
        style={{
          width: currentSize.container,
          height: currentSize.container,
          borderRadius: '50%',
          overflow: 'hidden',
          cursor: editable ? 'pointer' : 'default',
          position: 'relative',
          background: displayImageUrl 
            ? 'transparent' 
            : 'linear-gradient(135deg, #FFD700 0%, #E6C200 100%)',
          border: hasUnsavedChanges 
            ? '3px solid #FFA500' 
            : '3px solid rgba(255, 215, 0, 0.3)',
          boxShadow: hasUnsavedChanges 
            ? '0 0 18px rgba(255, 165, 0, 0.5)' 
            : '0 0 18px rgba(255, 215, 0, 0.3)',
          transition: 'all 0.2s ease'
        }}
        onMouseEnter={(e) => {
          if (editable) {
            e.currentTarget.style.transform = 'scale(1.05)';
            e.currentTarget.style.boxShadow = hasUnsavedChanges 
              ? '0 0 24px rgba(255, 165, 0, 0.7)' 
              : '0 0 24px rgba(255, 215, 0, 0.5)';
          }
        }}
        onMouseLeave={(e) => {
          if (editable) {
            e.currentTarget.style.transform = 'scale(1)';
            e.currentTarget.style.boxShadow = hasUnsavedChanges 
              ? '0 0 18px rgba(255, 165, 0, 0.5)' 
              : '0 0 18px rgba(255, 215, 0, 0.3)';
          }
        }}
      >
        {displayImageUrl ? (
          <img
            src={displayImageUrl}
            alt={`${userName} Profile Picture`}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover'
            }}
          />
        ) : (
          <div style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#000000',
            fontSize: currentSize.text,
            fontWeight: 'bold'
          }}>
            {userInitials}
          </div>
        )}

        {/* Loading Overlay */}
        {(uploading || saving) && (
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: '50%'
          }}>
            <div style={{
              width: '24px',
              height: '24px',
              border: '2px solid #FFD700',
              borderTop: '2px solid transparent',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite'
            }}></div>
          </div>
        )}

        {/* Unsaved Changes Indicator */}
        {hasUnsavedChanges && (
          <div style={{
            position: 'absolute',
            top: '-4px',
            right: '-4px',
            width: '12px',
            height: '12px',
            backgroundColor: '#FFA500',
            borderRadius: '50%',
            border: '2px solid #000',
            animation: 'pulse 2s infinite'
          }}></div>
        )}
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        style={{ display: 'none' }}
      />

      {/* Save/Cancel Buttons */}
      {showSaveButton && hasUnsavedChanges && (
        <div style={{
          position: 'absolute',
          top: '100%',
          left: '50%',
          transform: 'translateX(-50%)',
          marginTop: '8px',
          display: 'flex',
          gap: '8px',
          zIndex: 10
        }}>
          <button
            onClick={handleSave}
            disabled={saving}
            style={{
              backgroundColor: '#FFD700',
              color: '#000',
              border: 'none',
              padding: '6px 12px',
              borderRadius: '4px',
              fontSize: '12px',
              fontWeight: 'bold',
              cursor: saving ? 'not-allowed' : 'pointer',
              opacity: saving ? 0.7 : 1
            }}
          >
            {saving ? 'Saving...' : 'Save'}
          </button>
          <button
            onClick={handleCancel}
            disabled={saving}
            style={{
              backgroundColor: '#666',
              color: '#fff',
              border: 'none',
              padding: '6px 12px',
              borderRadius: '4px',
              fontSize: '12px',
              cursor: saving ? 'not-allowed' : 'pointer',
              opacity: saving ? 0.7 : 1
            }}
          >
            Cancel
          </button>
        </div>
      )}

      {/* Status Messages */}
      {(error || success) && (
        <div style={{
          position: 'absolute',
          top: '100%',
          left: '50%',
          transform: 'translateX(-50%)',
          marginTop: showSaveButton && hasUnsavedChanges ? '48px' : '8px',
          padding: '4px 8px',
          borderRadius: '4px',
          fontSize: '11px',
          fontWeight: 'bold',
          whiteSpace: 'nowrap',
          zIndex: 10,
          backgroundColor: error ? '#ff4444' : '#44ff44',
          color: '#000'
        }}>
          {error || success}
        </div>
      )}

      {/* CSS Animation */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.5; }
        }
      `}</style>
    </div>
  );
};
