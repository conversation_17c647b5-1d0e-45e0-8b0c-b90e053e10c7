#!/usr/bin/env node

/**
 * Test Database Fixes Script
 * 
 * This script tests the database schema fixes we made for the web application
 * using the Supabase client instead of direct PostgreSQL connection.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Please check VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY in your .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testDatabaseSchema() {
  console.log('🔍 Testing database schema fixes...\n');

  try {
    // Test 1: Check if commission_balances table exists
    console.log('📋 Test 1: Checking commission_balances table...');
    const { data: commissionTest, error: commissionError } = await supabase
      .from('commission_balances')
      .select('*', { count: 'exact', head: true });

    if (commissionError) {
      if (commissionError.message.includes('does not exist')) {
        console.log('   ❌ commission_balances table does not exist');
        console.log('   💡 This table needs to be created manually in Supabase dashboard');
      } else {
        console.log(`   ⚠️ commission_balances table error: ${commissionError.message}`);
      }
    } else {
      console.log('   ✅ commission_balances table exists');
    }

    // Test 2: Check if aureus_share_purchases view exists
    console.log('\n📋 Test 2: Checking aureus_share_purchases view...');
    const { data: sharesTest, error: sharesError } = await supabase
      .from('aureus_share_purchases')
      .select('*', { count: 'exact', head: true });

    if (sharesError) {
      if (sharesError.message.includes('does not exist')) {
        console.log('   ❌ aureus_share_purchases view does not exist');
        console.log('   💡 This view needs to be created manually in Supabase dashboard');
      } else {
        console.log(`   ⚠️ aureus_share_purchases view error: ${sharesError.message}`);
      }
    } else {
      console.log('   ✅ aureus_share_purchases view exists');
    }

    // Test 3: Check users table structure
    console.log('\n📋 Test 3: Checking users table structure...');
    const { data: usersTest, error: usersError } = await supabase
      .from('users')
      .select('id, email, username, full_name, phone, country_of_residence, telegram_id, is_active, is_verified')
      .limit(1);

    if (usersError) {
      console.log(`   ❌ users table error: ${usersError.message}`);
    } else {
      console.log('   ✅ users table structure looks good');
    }

    // Test 4: Check telegram_users table structure
    console.log('\n📋 Test 4: Checking telegram_users table structure...');
    const { data: telegramTest, error: telegramError } = await supabase
      .from('telegram_users')
      .select('id, user_id, telegram_id, username, first_name, last_name, is_registered, temp_email, temp_password')
      .limit(1);

    if (telegramError) {
      console.log(`   ❌ telegram_users table error: ${telegramError.message}`);
    } else {
      console.log('   ✅ telegram_users table structure looks good');
    }

    // Test 5: Check referrals table for campaign_source field
    console.log('\n📋 Test 5: Checking referrals table for campaign_source field...');
    const { data: referralsTest, error: referralsError } = await supabase
      .from('referrals')
      .select('id, referrer_id, referred_id, campaign_source')
      .limit(1);

    if (referralsError) {
      if (referralsError.message.includes('campaign_source')) {
        console.log('   ❌ campaign_source field missing from referrals table');
        console.log('   💡 This field needs to be added manually in Supabase dashboard');
      } else {
        console.log(`   ⚠️ referrals table error: ${referralsError.message}`);
      }
    } else {
      console.log('   ✅ referrals table with campaign_source field looks good');
    }

    console.log('\n🎉 Database schema test completed!');
    console.log('\n📝 Summary:');
    console.log('- Fixed database queries to use correct field names');
    console.log('- Updated registration flow to work with actual schema');
    console.log('- Added campaign tracking support');
    console.log('- Fixed gold price API with fallback mechanism');

  } catch (error) {
    console.error('❌ Database test failed:', error);
    process.exit(1);
  }
}

// Run the test
testDatabaseSchema();
