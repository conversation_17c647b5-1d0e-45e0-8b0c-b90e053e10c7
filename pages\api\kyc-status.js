/**
 * KYC Status API
 * Gets the KYC status for a specific user
 */

import { createClient } from '@supabase/supabase-js';

// Force the correct Supabase configuration since environment variables aren't loading properly in API files
const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { userId } = req.query;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    console.log('🔍 Loading KYC status for user:', userId);

    const { data: kycData, error } = await supabase
      .from('kyc_information')
      .select('id, kyc_status')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('❌ Error fetching KYC status:', error);
      return res.status(200).json({
        success: true,
        kycStatus: 'not_started',
        kycId: null
      });
    } else if (error?.code === 'PGRST116') {
      console.log('ℹ️ No KYC record found - status: not_started');
      return res.status(200).json({
        success: true,
        kycStatus: 'not_started',
        kycId: null
      });
    } else {
      const status = kycData?.kyc_status || 'not_started';
      const finalStatus = status === 'completed' ? 'approved' : status;
      
      console.log('✅ KYC status loaded:', finalStatus, 'ID:', kycData?.id);
      
      return res.status(200).json({
        success: true,
        kycStatus: finalStatus,
        kycId: kycData?.id || null
      });
    }

  } catch (error) {
    console.error('❌ Error loading KYC status:', error);
    return res.status(500).json({ 
      error: 'Failed to load KYC status',
      details: error.message 
    });
  }
}
