import React, { useState, useRef, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';
import { validatePasswordStrength, hashPassword } from '../../lib/passwordSecurity';

interface TelegramUserResetModalProps {
  isOpen: boolean;
  onClose: () => void;
  onError: (error: string) => void;
  onSuccess: (message: string) => void;
  onLoading: (loading: boolean) => void;
}

interface TelegramUserData {
  telegram_id: string;
  username: string;
  email: string;
  user_id: number;
  canChangeEmail: boolean;
}

interface FormData {
  telegramId: string;
  newPassword: string;
  confirmPassword: string;
  newEmail: string;
}

interface PasswordStrength {
  score: number;
  label: string;
  color: string;
}

export const TelegramUserResetModal: React.FC<TelegramUserResetModalProps> = ({
  isOpen,
  onClose,
  onError,
  onSuccess,
  onLoading
}) => {
  const [step, setStep] = useState<'lookup' | 'reset'>('lookup');
  const [formData, setFormData] = useState<FormData>({
    telegramId: '',
    newPassword: '',
    confirmPassword: '',
    newEmail: ''
  });
  const [telegramUserData, setTelegramUserData] = useState<TelegramUserData | null>(null);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState<PasswordStrength>({
    score: 0,
    label: '',
    color: ''
  });

  const modalRef = useRef<HTMLDivElement>(null);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setStep('lookup');
      setFormData({
        telegramId: '',
        newPassword: '',
        confirmPassword: '',
        newEmail: ''
      });
      setTelegramUserData(null);
      setErrors({});
      setPasswordStrength({ score: 0, label: '', color: '' });
    }
  }, [isOpen]);

  // Password strength validation
  useEffect(() => {
    if (formData.newPassword) {
      const strength = validatePasswordStrength(formData.newPassword);
      setPasswordStrength({
        score: strength.score,
        label: strength.label,
        color: strength.score >= 3 ? '#10b981' : strength.score >= 2 ? '#f59e0b' : '#ef4444'
      });
    } else {
      setPasswordStrength({ score: 0, label: '', color: '' });
    }
  }, [formData.newPassword]);

  // Handle click outside modal
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen, onClose]);

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear field-specific errors
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateTelegramId = (telegramId: string): boolean => {
    // Telegram IDs are numeric and typically 8-10 digits
    const telegramIdRegex = /^\d{8,12}$/;
    return telegramIdRegex.test(telegramId);
  };

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleTelegramLookup = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const newErrors: { [key: string]: string } = {};

    // Validate Telegram ID
    if (!formData.telegramId.trim()) {
      newErrors.telegramId = 'Telegram ID is required';
    } else if (!validateTelegramId(formData.telegramId.trim())) {
      newErrors.telegramId = 'Please enter a valid Telegram ID (8-12 digits)';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setLoading(true);
    onLoading(true);

    try {
      const serviceClient = getServiceRoleClient();
      
      // Look up Telegram user
      const { data: telegramUser, error: telegramError } = await serviceClient
        .from('telegram_users')
        .select('telegram_id, username, user_id')
        .eq('telegram_id', parseInt(formData.telegramId.trim()))
        .single();

      if (telegramError || !telegramUser) {
        setErrors({ telegramId: 'Telegram ID not found in our system' });
        return;
      }

      // Get corresponding user data
      const { data: userData, error: userError } = await serviceClient
        .from('users')
        .select('id, username, email')
        .eq('id', telegramUser.user_id)
        .single();

      if (userError || !userData) {
        setErrors({ telegramId: 'User account not found. Please contact support.' });
        return;
      }

      // Check if email can be changed (ends with @telegram.local)
      const canChangeEmail = userData.email.endsWith('@telegram.local');

      setTelegramUserData({
        telegram_id: telegramUser.telegram_id,
        username: userData.username,
        email: userData.email,
        user_id: userData.id,
        canChangeEmail
      });

      // Pre-populate email if it can be changed
      if (canChangeEmail) {
        setFormData(prev => ({ ...prev, newEmail: '' }));
      }

      setStep('reset');
      setErrors({});

    } catch (error) {
      console.error('Telegram lookup error:', error);
      onError('Failed to lookup Telegram user. Please try again.');
    } finally {
      setLoading(false);
      onLoading(false);
    }
  };

  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const newErrors: { [key: string]: string } = {};

    // Validate password
    if (!formData.newPassword) {
      newErrors.newPassword = 'Password is required';
    } else {
      const passwordValidation = validatePasswordStrength(formData.newPassword);
      if (passwordValidation.score < 3) {
        newErrors.newPassword = 'Password must be stronger. ' + passwordValidation.feedback.join(' ');
      }
    }

    // Validate password confirmation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    // Validate email if it can be changed
    if (telegramUserData?.canChangeEmail) {
      if (!formData.newEmail.trim()) {
        newErrors.newEmail = 'Email address is required';
      } else if (!validateEmail(formData.newEmail.trim())) {
        newErrors.newEmail = 'Please enter a valid email address';
      }
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setLoading(true);
    onLoading(true);

    try {
      const serviceClient = getServiceRoleClient();
      
      // Hash the new password
      const hashedPassword = await hashPassword(formData.newPassword);

      // Prepare update data
      const updateData: any = {
        password_hash: hashedPassword,
        updated_at: new Date().toISOString()
      };

      // Add email if it can be changed
      if (telegramUserData?.canChangeEmail && formData.newEmail.trim()) {
        // Check if new email already exists
        const { data: existingUser, error: emailCheckError } = await serviceClient
          .from('users')
          .select('id')
          .eq('email', formData.newEmail.trim().toLowerCase())
          .neq('id', telegramUserData.user_id)
          .single();

        if (existingUser) {
          setErrors({ newEmail: 'This email address is already in use' });
          return;
        }

        updateData.email = formData.newEmail.trim().toLowerCase();
      }

      // Update user record
      const { error: updateError } = await serviceClient
        .from('users')
        .update(updateData)
        .eq('id', telegramUserData!.user_id);

      if (updateError) {
        throw updateError;
      }

      // Log the successful reset for audit purposes
      console.log(`Telegram user reset completed for user ID: ${telegramUserData!.user_id}, Telegram ID: ${telegramUserData!.telegram_id}`);

      onSuccess(
        `Password reset successful! ${
          telegramUserData?.canChangeEmail && formData.newEmail 
            ? 'Your email has also been updated.' 
            : ''
        } You can now login with your new credentials.`
      );
      
      onClose();

    } catch (error) {
      console.error('Password reset error:', error);
      onError('Failed to reset password. Please try again.');
    } finally {
      setLoading(false);
      onLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div 
        ref={modalRef}
        className="bg-gray-900/95 backdrop-blur-xl rounded-2xl border border-gray-700/50 shadow-2xl w-full max-w-md max-h-[90vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="sticky top-0 bg-gray-900/95 backdrop-blur-xl border-b border-gray-700/50 px-6 py-4 rounded-t-2xl">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-bold text-amber-400 flex items-center gap-2">
                <span className="text-2xl">📱</span>
                Telegram User Reset
              </h2>
              <p className="text-gray-400 text-sm mt-1">
                {step === 'lookup' 
                  ? 'Enter your Telegram ID to reset your account' 
                  : 'Set new password and email for your account'
                }
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white text-2xl transition-colors"
              disabled={loading}
            >
              ×
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {step === 'lookup' ? (
            <form onSubmit={handleTelegramLookup} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Telegram ID
                </label>
                <input
                  type="text"
                  value={formData.telegramId}
                  onChange={(e) => handleInputChange('telegramId', e.target.value)}
                  className={`w-full px-4 py-3 bg-gray-800/50 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-colors ${
                    errors.telegramId
                      ? 'border-red-500 focus:ring-red-500'
                      : 'border-gray-600 focus:ring-amber-500'
                  }`}
                  placeholder="Enter your numeric Telegram ID"
                  disabled={loading}
                />
                {errors.telegramId && (
                  <p className="text-red-400 text-sm mt-1">{errors.telegramId}</p>
                )}
                <p className="text-gray-500 text-xs mt-1">
                  Your Telegram ID is a unique number. You can find it by messaging @userinfobot on Telegram.
                </p>
              </div>

              <button
                type="submit"
                disabled={loading || !formData.telegramId.trim()}
                className="w-full bg-amber-500 hover:bg-amber-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-black font-semibold py-3 px-4 rounded-lg transition-colors"
              >
                {loading ? 'Looking up...' : 'Find Account'}
              </button>
            </form>
          ) : (
            <>
              {/* Account Information Display */}
              <div className="bg-gray-800/50 rounded-lg p-4 mb-6">
                <h3 className="text-lg font-semibold text-white mb-3">Account Information</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Username:</span>
                    <span className="text-white font-mono">{telegramUserData?.username}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Current Email:</span>
                    <span className="text-white font-mono">{telegramUserData?.email}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Telegram ID:</span>
                    <span className="text-white font-mono">{telegramUserData?.telegram_id}</span>
                  </div>
                </div>
              </div>

              <form onSubmit={handlePasswordReset} className="space-y-4">
                {/* New Password */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    New Password
                  </label>
                  <div className="relative">
                    <input
                      type={showPassword ? "text" : "password"}
                      value={formData.newPassword}
                      onChange={(e) => handleInputChange('newPassword', e.target.value)}
                      className={`w-full px-4 py-3 pr-12 bg-gray-800/50 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-colors ${
                        errors.newPassword
                          ? 'border-red-500 focus:ring-red-500'
                          : 'border-gray-600 focus:ring-amber-500'
                      }`}
                      placeholder="Enter your new password"
                      disabled={loading}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                      disabled={loading}
                    >
                      {showPassword ? '👁️' : '👁️‍🗨️'}
                    </button>
                  </div>
                  {errors.newPassword && (
                    <p className="text-red-400 text-sm mt-1">{errors.newPassword}</p>
                  )}
                  {passwordStrength.label && (
                    <div className="mt-2">
                      <div className="flex items-center gap-2">
                        <div className="flex-1 bg-gray-700 rounded-full h-2">
                          <div
                            className="h-2 rounded-full transition-all duration-300"
                            style={{
                              width: `${(passwordStrength.score / 4) * 100}%`,
                              backgroundColor: passwordStrength.color
                            }}
                          />
                        </div>
                        <span
                          className="text-xs font-medium"
                          style={{ color: passwordStrength.color }}
                        >
                          {passwordStrength.label}
                        </span>
                      </div>
                    </div>
                  )}
                </div>

                {/* Confirm Password */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Confirm New Password
                  </label>
                  <div className="relative">
                    <input
                      type={showConfirmPassword ? "text" : "password"}
                      value={formData.confirmPassword}
                      onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                      className={`w-full px-4 py-3 pr-12 bg-gray-800/50 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-colors ${
                        errors.confirmPassword
                          ? 'border-red-500 focus:ring-red-500'
                          : 'border-gray-600 focus:ring-amber-500'
                      }`}
                      placeholder="Confirm your new password"
                      disabled={loading}
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                      disabled={loading}
                    >
                      {showConfirmPassword ? '👁️' : '👁️‍🗨️'}
                    </button>
                  </div>
                  {errors.confirmPassword && (
                    <p className="text-red-400 text-sm mt-1">{errors.confirmPassword}</p>
                  )}
                </div>

                {/* Email Update (conditional) */}
                {telegramUserData?.canChangeEmail && (
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      New Email Address
                    </label>
                    <input
                      type="email"
                      value={formData.newEmail}
                      onChange={(e) => handleInputChange('newEmail', e.target.value)}
                      className={`w-full px-4 py-3 bg-gray-800/50 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-colors ${
                        errors.newEmail
                          ? 'border-red-500 focus:ring-red-500'
                          : 'border-gray-600 focus:ring-amber-500'
                      }`}
                      placeholder="Enter your real email address"
                      disabled={loading}
                    />
                    {errors.newEmail && (
                      <p className="text-red-400 text-sm mt-1">{errors.newEmail}</p>
                    )}
                    <p className="text-gray-500 text-xs mt-1">
                      Your current email is a temporary Telegram email. Please provide a real email address.
                    </p>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex gap-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setStep('lookup')}
                    className="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors"
                    disabled={loading}
                  >
                    Back
                  </button>
                  <button
                    type="submit"
                    disabled={loading || passwordStrength.score < 3}
                    className="flex-1 bg-amber-500 hover:bg-amber-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-black font-semibold py-3 px-4 rounded-lg transition-colors"
                  >
                    {loading ? 'Updating...' : 'Reset Password'}
                  </button>
                </div>
              </form>
            </>
          )}
        </div>
      </div>
    </div>
  );
};
