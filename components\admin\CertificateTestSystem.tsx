import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { svgCertificateGenerator } from '../../lib/svgCertificateGenerator';
import { CertificateElementEditor } from './CertificateElementEditor';
import { CertificatePreviewPanel } from './CertificatePreviewPanel';

interface TestCertificateData {
  userName: string;
  userIdNumber: string;
  addressLine1: string;
  addressLine2: string;
  sharesCount: number;
  certificateNumber: string;
  refNumber: string;
  issueDate: string;
  sunId: string;
}

interface CertificateTemplateElement {
  id: string;
  element_id: string;
  element_type: 'text' | 'image' | 'line' | 'rectangle' | 'watermark';
  template_version: string;
  x_position: number;
  y_position: number;
  width?: number;
  height?: number;
  font_family?: string;
  font_size?: number;
  font_weight?: string;
  font_color?: string;
  text_anchor?: string;
  static_content?: string;
  data_field?: string;
  is_dynamic: boolean;
  opacity?: number;
  rotation?: number;
  z_index?: number;
  description?: string;
  is_active: boolean;
}

export const CertificateTestSystem: React.FC = () => {
  const [testData, setTestData] = useState<TestCertificateData>({
    userName: 'Francois Johannes Prins',
    userIdNumber: '6803265048083',
    addressLine1: '1St Thomas close, Anchorage Park, Gordonsbay, South Africa, 7140',
    addressLine2: '',
    sharesCount: 700,
    certificateNumber: '0-000-001',
    refNumber: '1-700',
    issueDate: '27/06/2025',
    sunId: '40681'
  });

  const [previewSvg, setPreviewSvg] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [templateLoaded, setTemplateLoaded] = useState(false);
  const [selectedElement, setSelectedElement] = useState<CertificateTemplateElement | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  useEffect(() => {
    loadTemplate();
  }, []);

  const loadTemplate = async () => {
    try {
      setLoading(true);
      // Force reload the template
      await svgCertificateGenerator.loadTemplate();
      setTemplateLoaded(true);
      generatePreview();
    } catch (error) {
      console.error('Error loading template:', error);
    } finally {
      setLoading(false);
    }
  };

  const generatePreview = async () => {
    try {
      setLoading(true);

      // Use the SVG certificate generator to create test certificate
      const testCertificate = await svgCertificateGenerator.generateTestCertificate({
        userName: testData.userName,
        userIdNumber: testData.userIdNumber,
        addressLine1: testData.addressLine1,
        addressLine2: testData.addressLine2,
        sharesCount: testData.sharesCount,
        certificateNumber: testData.certificateNumber,
        sunId: testData.sunId,
        refNumber: testData.refNumber,
        issueDate: testData.issueDate
      });

      if (testCertificate) {
        setPreviewSvg(testCertificate);
      } else {
        console.warn('No certificate generated - using fallback');
        // Generate a simple fallback preview
        setPreviewSvg(`
          <svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
            <rect width="800" height="600" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
            <text x="400" y="300" text-anchor="middle" font-family="Arial" font-size="24" fill="#6c757d">
              Certificate Preview Unavailable
            </text>
            <text x="400" y="340" text-anchor="middle" font-family="Arial" font-size="16" fill="#6c757d">
              Check network connection and try again
            </text>
          </svg>
        `);
      }
    } catch (error) {
      console.error('Error generating preview:', error);
      // Show error state in preview instead of alert
      setPreviewSvg(`
        <svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
          <rect width="800" height="600" fill="#fff5f5" stroke="#fed7d7" stroke-width="2"/>
          <text x="400" y="280" text-anchor="middle" font-family="Arial" font-size="20" fill="#e53e3e">
            ❌ Error Generating Certificate
          </text>
          <text x="400" y="320" text-anchor="middle" font-family="Arial" font-size="14" fill="#c53030">
            ${error instanceof Error ? error.message : 'Unknown error occurred'}
          </text>
          <text x="400" y="360" text-anchor="middle" font-family="Arial" font-size="12" fill="#9c4221">
            Check console for details
          </text>
        </svg>
      `);
    } finally {
      setLoading(false);
    }
  };

  const downloadTestPNG = async () => {
    try {
      setLoading(true);
      
      if (!previewSvg) {
        await generatePreview();
        return;
      }

      // Convert SVG to high-quality PNG
      const pngBlob = await svgCertificateGenerator.svgToHighQualityPNG(previewSvg);
      
      if (pngBlob) {
        const url = URL.createObjectURL(pngBlob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `test-certificate-${testData.certificateNumber}.png`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        alert(`✅ Test certificate PNG downloaded! Size: ${(pngBlob.size / 1024 / 1024).toFixed(2)}MB`);
      } else {
        alert('❌ Failed to generate PNG');
      }
    } catch (error) {
      console.error('Error downloading PNG:', error);
      alert('Error downloading PNG: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  const printTestCertificate = () => {
    if (!previewSvg) {
      alert('Please generate preview first');
      return;
    }

    const printWindow = window.open('', '_blank', 'width=1200,height=800');
    if (printWindow) {
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Test Certificate Print - ${testData.certificateNumber}</title>
            <style>
              body { 
                margin: 0; 
                padding: 20px; 
                background: white;
                font-family: Arial, sans-serif;
              }
              .container { 
                max-width: 1000px; 
                margin: 0 auto; 
                text-align: center;
              }
              .header {
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 2px solid #D4AF37;
              }
              .certificate-preview {
                border: 2px solid #D4AF37;
                padding: 10px;
                background: white;
              }
              @media print {
                body { padding: 0; }
                .header { display: none; }
                .certificate-preview { border: none; padding: 0; }
              }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="header">
                <h1>🧪 TEST CERTIFICATE</h1>
                <p><strong>Certificate Number:</strong> ${testData.certificateNumber}</p>
                <p><strong>User:</strong> ${testData.userName}</p>
                <p><strong>Shares:</strong> ${testData.sharesCount}</p>
              </div>
              <div class="certificate-preview">
                ${previewSvg}
              </div>
            </div>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  };

  const resetToDefaults = () => {
    setTestData({
      userName: 'Francois Johannes Prins',
      userIdNumber: '6803265048083',
      addressLine1: '1St Thomas close, Anchorage Park, Gordonsbay, South Africa, 7140',
      addressLine2: '',
      sharesCount: 700,
      certificateNumber: '0-000-001',
      refNumber: '1-700',
      issueDate: '27/06/2025',
      sunId: '40681'
    });
  };

  const handleInputChange = (field: keyof TestCertificateData, value: string | number) => {
    setTestData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleElementUpdate = (element: CertificateTemplateElement) => {
    setSelectedElement(element);
    // Trigger preview refresh
    setRefreshTrigger(prev => prev + 1);
  };

  return (
    <div className="bg-gray-900 text-white min-h-screen p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-yellow-400 mb-2">🧪 Certificate Test System</h1>
          <p className="text-gray-400">Test and perfect the certificate design before creating real certificates</p>
        </div>

        <div className="space-y-8">
          {/* Element Positioning Controls */}
          <CertificateElementEditor
            onElementUpdate={handleElementUpdate}
          />

          <div className="grid lg:grid-cols-2 gap-8">
          {/* Test Data Form */}
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <h2 className="text-xl font-semibold text-white mb-4">📝 Test Certificate Data</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">User Name</label>
                <input
                  type="text"
                  value={testData.userName}
                  onChange={(e) => handleInputChange('userName', e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">ID Number</label>
                <input
                  type="text"
                  value={testData.userIdNumber}
                  onChange={(e) => handleInputChange('userIdNumber', e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Address Line 1</label>
                <input
                  type="text"
                  value={testData.addressLine1}
                  onChange={(e) => handleInputChange('addressLine1', e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Address Line 2</label>
                <input
                  type="text"
                  value={testData.addressLine2}
                  onChange={(e) => handleInputChange('addressLine2', e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Shares Count</label>
                  <input
                    type="number"
                    value={testData.sharesCount}
                    onChange={(e) => handleInputChange('sharesCount', parseInt(e.target.value) || 0)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">SUN ID</label>
                  <input
                    type="text"
                    value={testData.sunId}
                    onChange={(e) => handleInputChange('sunId', e.target.value)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Certificate Number</label>
                  <input
                    type="text"
                    value={testData.certificateNumber}
                    onChange={(e) => handleInputChange('certificateNumber', e.target.value)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Reference Number</label>
                  <input
                    type="text"
                    value={testData.refNumber}
                    onChange={(e) => handleInputChange('refNumber', e.target.value)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Issue Date</label>
                <input
                  type="text"
                  value={testData.issueDate}
                  onChange={(e) => handleInputChange('issueDate', e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"
                  placeholder="DD/MM/YYYY"
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="mt-6 flex flex-wrap gap-2">
              <button
                onClick={generatePreview}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                disabled={loading}
              >
                {loading ? '⏳ Generating...' : '🔄 Generate Preview'}
              </button>

              <button
                onClick={downloadTestPNG}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
                disabled={loading || !previewSvg}
              >
                📥 Download PNG
              </button>

              <button
                onClick={printTestCertificate}
                className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
                disabled={!previewSvg}
              >
                🖨️ Print Test
              </button>

              <button
                onClick={resetToDefaults}
                className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
              >
                🔄 Reset Defaults
              </button>
            </div>
          </div>

          {/* Enhanced Preview Panel */}
          <CertificatePreviewPanel
            testData={testData}
            selectedElement={selectedElement}
            onElementUpdate={handleElementUpdate}
            refreshTrigger={refreshTrigger}
          />
          </div>
        </div>
      </div>
    </div>
  );
};

export default CertificateTestSystem;
