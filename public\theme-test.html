<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme Test - Aureus Alliance</title>
    <style>
        /* Import our design system */
        @import url('../aureus.css');
        
        body {
            background-color: var(--bg) !important;
            color: var(--text) !important;
            font-family: 'Inter', sans-serif !important;
            margin: 0;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .theme-status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: 8px;
            padding: 1rem;
            box-shadow: var(--shadow);
        }
        
        .toggle-btn {
            background: var(--gold);
            color: var(--cta-text);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 1rem 0;
        }
        
        .test-card {
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: 12px;
            padding: 2rem;
            margin: 1rem 0;
            box-shadow: var(--shadow);
        }
        
        .color-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .color-box {
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
        }
        
        .gold { background: var(--gold); color: var(--cta-text); }
        .blue { background: var(--blue); color: white; }
        .emerald { background: var(--emerald); color: white; }
        .copper { background: var(--copper); color: white; }
    </style>
</head>
<body>
    <div class="theme-status">
        <div>Current Theme: <span id="current-theme">dark</span></div>
        <button class="toggle-btn" onclick="toggleTheme()">Toggle Theme</button>
    </div>
    
    <div class="test-container">
        <h1 style="color: var(--gold); text-align: center; margin-bottom: 2rem;">
            🎨 Aureus Design System Theme Test
        </h1>
        
        <div class="test-card">
            <h2>Theme Variables Test</h2>
            <p>Background: <code>var(--bg)</code> = <span id="bg-value"></span></p>
            <p>Surface: <code>var(--surface)</code> = <span id="surface-value"></span></p>
            <p>Text: <code>var(--text)</code> = <span id="text-value"></span></p>
            <p>Gold: <code>var(--gold)</code> = <span id="gold-value"></span></p>
            <p>Blue: <code>var(--blue)</code> = <span id="blue-value"></span></p>
        </div>
        
        <div class="test-card">
            <h2>Color Palette Demo</h2>
            <div class="color-demo">
                <div class="color-box gold">Gold<br>#FFD700</div>
                <div class="color-box blue">Blue<br>Dynamic</div>
                <div class="color-box emerald">Emerald<br>Dynamic</div>
                <div class="color-box copper">Copper<br>Dynamic</div>
            </div>
        </div>
        
        <div class="test-card">
            <h2>Theme Toggle Instructions</h2>
            <ol>
                <li>Click the "Toggle Theme" button above</li>
                <li>Watch the background and text colors change</li>
                <li>Verify the color values update in the table above</li>
                <li>Check that the theme persists when you refresh the page</li>
            </ol>
            
            <div style="margin-top: 2rem; padding: 1rem; background: var(--surface); border-left: 4px solid var(--gold);">
                <strong>Expected Behavior:</strong>
                <ul>
                    <li><strong>Dark Theme (Default):</strong> Black background (#050505), white text</li>
                    <li><strong>Light Theme:</strong> Light background (#FAFAFA), dark text</li>
                    <li><strong>Gold Color:</strong> Always #FFD700 in both themes</li>
                    <li><strong>Blue Color:</strong> #00BFFF in dark, #007BFF in light</li>
                </ul>
            </div>
        </div>
        
        <div class="test-card">
            <h2>✅ Success Indicators</h2>
            <div id="success-indicators">
                <div class="indicator" id="dark-default">❌ Dark theme as default</div>
                <div class="indicator" id="theme-toggle">❌ Theme toggle working</div>
                <div class="indicator" id="color-variables">❌ Color variables updating</div>
                <div class="indicator" id="persistence">❌ Theme persistence</div>
            </div>
        </div>
    </div>
    
    <script>
        let currentTheme = 'dark';
        
        function updateThemeDisplay() {
            document.getElementById('current-theme').textContent = currentTheme;
            
            // Get computed CSS variables
            const root = document.documentElement;
            const computedStyle = getComputedStyle(root);
            
            document.getElementById('bg-value').textContent = computedStyle.getPropertyValue('--bg').trim();
            document.getElementById('surface-value').textContent = computedStyle.getPropertyValue('--surface').trim();
            document.getElementById('text-value').textContent = computedStyle.getPropertyValue('--text').trim();
            document.getElementById('gold-value').textContent = computedStyle.getPropertyValue('--gold').trim();
            document.getElementById('blue-value').textContent = computedStyle.getPropertyValue('--blue').trim();
            
            // Update success indicators
            updateSuccessIndicators();
        }
        
        function updateSuccessIndicators() {
            const root = document.documentElement;
            const computedStyle = getComputedStyle(root);
            const bgColor = computedStyle.getPropertyValue('--bg').trim();
            
            // Check if dark theme is default
            if (bgColor === '#050505' || bgColor === 'rgb(5, 5, 5)') {
                document.getElementById('dark-default').innerHTML = '✅ Dark theme as default';
                document.getElementById('dark-default').style.color = 'var(--emerald)';
            }
            
            // Check if theme toggle works (will be updated when toggled)
            if (localStorage.getItem('theme-toggle-tested')) {
                document.getElementById('theme-toggle').innerHTML = '✅ Theme toggle working';
                document.getElementById('theme-toggle').style.color = 'var(--emerald)';
            }
            
            // Check if color variables are working
            const goldColor = computedStyle.getPropertyValue('--gold').trim();
            if (goldColor === '#FFD700' || goldColor === 'rgb(255, 215, 0)') {
                document.getElementById('color-variables').innerHTML = '✅ Color variables updating';
                document.getElementById('color-variables').style.color = 'var(--emerald)';
            }
            
            // Check persistence
            if (localStorage.getItem('aureus-theme')) {
                document.getElementById('persistence').innerHTML = '✅ Theme persistence';
                document.getElementById('persistence').style.color = 'var(--emerald)';
            }
        }
        
        function toggleTheme() {
            currentTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.documentElement.setAttribute('data-theme', currentTheme);
            localStorage.setItem('aureus-theme', currentTheme);
            localStorage.setItem('theme-toggle-tested', 'true');
            updateThemeDisplay();
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Check for saved theme or default to dark
            const savedTheme = localStorage.getItem('aureus-theme');
            if (savedTheme) {
                currentTheme = savedTheme;
            } else {
                currentTheme = 'dark'; // Default to dark
            }
            
            document.documentElement.setAttribute('data-theme', currentTheme);
            updateThemeDisplay();
        });
    </script>
</body>
</html>
