/**
 * Create test conversion requests for commission processing testing
 */

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const createTestConversionRequests = async () => {
  try {
    console.log('🧪 CREATING TEST CONVERSION REQUESTS');
    console.log('='.repeat(50));

    // Get current phase info
    const { data: currentPhase, error: phaseError } = await supabase
      .from('investment_phases')
      .select('*')
      .eq('is_active', true)
      .single();

    if (phaseError || !currentPhase) {
      console.error('❌ Could not get current phase:', phaseError);
      return;
    }

    console.log(`✅ Current phase: ${currentPhase.phase_number} - $${currentPhase.price_per_share}/share`);

    // Test Conversion 1: Medium amount with existing test user
    console.log('\n1️⃣ Creating test conversion request #1...');

    const conversion1 = {
      user_id: 346, // referred_1758570817272 (has referrer 345)
      shares_requested: 15,
      usdt_amount: 75.00,
      share_price: parseFloat(currentPhase.price_per_share),
      phase_id: currentPhase.id,
      phase_number: currentPhase.phase_number,
      status: 'pending',
      created_at: new Date().toISOString()
    };

    const { data: testConversion1, error: error1 } = await supabase
      .from('commission_conversions')
      .insert(conversion1)
      .select()
      .single();

    if (error1) {
      console.error('❌ Failed to create test conversion 1:', error1);
    } else {
      console.log('✅ Test conversion 1 created successfully!');
      console.log(`   • ID: ${testConversion1.id}`);
      console.log(`   • User: ${testConversion1.user_id}`);
      console.log(`   • Amount: $${testConversion1.usdt_amount}`);
      console.log(`   • Shares: ${testConversion1.shares_requested}`);
    }

    // Test Conversion 2: Small amount with same test user
    console.log('\n2️⃣ Creating test conversion request #2...');
    
    const conversion2 = {
      user_id: 346, // Same user for consistency
      shares_requested: 6,
      usdt_amount: 30.00,
      share_price: parseFloat(currentPhase.price_per_share),
      phase_id: currentPhase.id,
      phase_number: currentPhase.phase_number,
      status: 'pending',
      created_at: new Date().toISOString()
    };

    const { data: testConversion2, error: error2 } = await supabase
      .from('commission_conversions')
      .insert(conversion2)
      .select()
      .single();

    if (error2) {
      console.error('❌ Failed to create test conversion 2:', error2);
    } else {
      console.log('✅ Test conversion 2 created successfully!');
      console.log(`   • ID: ${testConversion2.id}`);
      console.log(`   • User: ${testConversion2.user_id}`);
      console.log(`   • Amount: $${testConversion2.usdt_amount}`);
      console.log(`   • Shares: ${testConversion2.shares_requested}`);
    }

    // Verify referral relationship
    console.log('\n3️⃣ Verifying referral relationship...');
    
    const { data: referralData, error: referralError } = await supabase
      .from('referrals')
      .select(`
        referrer_id,
        referred_id,
        status,
        referrer:referrer_id(username, email),
        referred:referred_id(username, email)
      `)
      .eq('referred_id', 346)
      .single();

    if (referralError || !referralData) {
      console.log('⚠️ No referral relationship found for user 346');
    } else {
      console.log('✅ Referral relationship verified:');
      console.log(`   • Referrer: ${referralData.referrer.username} (ID: ${referralData.referrer_id})`);
      console.log(`   • Referred: ${referralData.referred.username} (ID: ${referralData.referred_id})`);
      console.log(`   • Status: ${referralData.status}`);
    }

    // Show expected commission calculations
    console.log('\n💰 Expected Commission Calculations:');
    console.log('\n   Test Conversion 1 ($75.00, 15 shares):');
    console.log(`   • USDT Commission: $${(75.00 * 0.15).toFixed(2)}`);
    console.log(`   • Share Commission: ${(15 * 0.15).toFixed(2)} shares`);
    
    console.log('\n   Test Conversion 2 ($30.00, 6 shares):');
    console.log(`   • USDT Commission: $${(30.00 * 0.15).toFixed(2)}`);
    console.log(`   • Share Commission: ${(6 * 0.15).toFixed(2)} shares`);

    console.log('\n🎯 Test conversions are ready!');
    console.log('\nYou can now test the commission processing by:');
    console.log('1. Going to Admin Dashboard → Commission Conversions');
    console.log('2. Approving the new test conversion requests');
    console.log('3. Verifying commission processing works correctly');
    console.log('4. Running verification script if needed');

  } catch (error) {
    console.error('❌ Error creating test conversion requests:', error);
  }
};

createTestConversionRequests();
