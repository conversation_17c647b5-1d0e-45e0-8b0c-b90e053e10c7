import React, { useState } from 'react';
import { supabase, getServiceRoleClient } from '../lib/supabase';

interface PaymentNetwork {
  id: string;
  name: string;
  technical: string;
  icon: string;
  wallet_address: string;
}

interface CurrentPhase {
  id: number;
  phase_number: number;
  phase_name: string;
  price_per_share: number;
  total_shares_available: number;
  shares_sold: number;
  is_active: boolean;
}

interface User {
  id: number;
  email: string;
  username?: string;
}

interface CryptoPaymentStepProps {
  amount: string;
  shares: number;
  network: PaymentNetwork;
  currentPhase: CurrentPhase | null;
  user: User | null;
  onBack: () => void;
  onComplete: () => void;
}

const CryptoPaymentStep: React.FC<CryptoPaymentStepProps> = ({
  amount,
  shares,
  network,
  currentPhase,
  user,
  onBack,
  onComplete
}) => {

  // Transaction state
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [error, setError] = useState('');

  // Manual payment fields
  const [senderWallet, setSenderWallet] = useState('');
  const [manualTxHash, setManualTxHash] = useState('');

  const totalCost = shares * (currentPhase?.price_per_share || 0);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const handleManualPayment = async () => {
    console.log('🚀 CRYPTO PAYMENT: Starting manual payment submission');
    console.log('🚀 CRYPTO PAYMENT: Input validation check');

    if (!senderWallet.trim()) {
      console.log('❌ CRYPTO PAYMENT: Missing sender wallet');
      setError('Please enter your wallet address');
      return;
    }

    if (!manualTxHash.trim()) {
      console.log('❌ CRYPTO PAYMENT: Missing transaction hash');
      setError('Please enter the transaction hash');
      return;
    }

    if (!user) {
      console.log('❌ CRYPTO PAYMENT: No user object');
      setError('User not found. Please log in again.');
      return;
    }

    console.log('🚀 CRYPTO PAYMENT: All validations passed, processing payment');
    console.log('🚀 CRYPTO PAYMENT: User object:', user);
    console.log('🚀 CRYPTO PAYMENT: Sender wallet:', senderWallet.trim());
    console.log('🚀 CRYPTO PAYMENT: Transaction hash:', manualTxHash.trim());
    console.log('🚀 CRYPTO PAYMENT: Network:', network);
    console.log('🚀 CRYPTO PAYMENT: Amount:', totalCost);
    console.log('🚀 CRYPTO PAYMENT: Shares:', shares);

    setIsProcessingPayment(true);
    setError('');

    try {
      console.log('🚀 CRYPTO PAYMENT: Getting user ID...');
      // Get user ID
      const userId = await getUserId();
      console.log('🚀 CRYPTO PAYMENT: User ID result:', userId);

      if (!userId) {
        throw new Error('User not found. Please ensure you are properly logged in.');
      }

      console.log('🚀 CRYPTO PAYMENT: Creating payment record...');
      // Create payment record with manual transaction details
      await createPaymentRecord(userId, manualTxHash.trim(), senderWallet.trim());

      console.log('✅ CRYPTO PAYMENT: Manual payment submitted successfully');

      // Complete the payment flow
      onComplete();

    } catch (error: any) {
      console.error('❌ CRYPTO PAYMENT: Manual payment submission failed:', error);
      console.error('❌ CRYPTO PAYMENT: Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
        toString: error.toString()
      });

      // Check if this is the "statement is not a function" error
      if (error.message && error.message.includes('statement')) {
        console.error('❌ CRYPTO PAYMENT: Detected statement function error - this is likely a JavaScript/validation issue');
        setError('System error detected. Please refresh the page and try again. If the problem persists, contact support.');
      } else {
        setError(error.message || 'Payment submission failed. Please try again.');
      }
    } finally {
      setIsProcessingPayment(false);
    }
  };



  const getUserId = async (): Promise<number | null> => {
    const serviceClient = getServiceRoleClient();
    let userData = null;
    let userError = null;

    // Handle different user ID formats
    if (user?.database_user?.id) {
      userData = { id: user.database_user.id };
      userError = null;
    } else if (user?.user_metadata?.user_id) {
      userData = { id: user.user_metadata.user_id };
      userError = null;
    } else if (user?.id?.startsWith('db_')) {
      const dbUserId = user.id.replace('db_', '');
      userData = { id: parseInt(dbUserId) };
      userError = null;
    } else if (user?.id) {
      const result = await serviceClient
        .from('users')
        .select('id')
        .eq('auth_user_id', user.id)
        .single();
      userData = result.data;
      userError = result.error;
    } else {
      userError = { message: 'No user ID available' };
    }

    if (userError || !userData) {
      return null;
    }

    return userData.id;
  };

  const createPaymentRecord = async (userId: number, txHash: string, manualSenderWallet: string) => {
    console.log('💾 CRYPTO PAYMENT: Creating payment record for manual transaction');
    console.log('💾 CRYPTO PAYMENT: Payment data details:', {
      user_id: userId,
      amount: totalCost,
      shares_to_purchase: shares,
      network: network.id,
      currency: 'USDT',
      sender_wallet: manualSenderWallet,
      receiver_wallet: network.wallet_address,
      transaction_hash: txHash
    });

    try {
      const serviceClient = getServiceRoleClient();
      console.log('💾 CRYPTO PAYMENT: Service client obtained successfully');

      const paymentData = {
        user_id: userId,
        amount: totalCost,
        shares_to_purchase: shares,
        network: network.id,
        currency: 'USDT',
        sender_wallet: manualSenderWallet,
        receiver_wallet: network.wallet_address,
        transaction_hash: txHash,
        screenshot_url: null,
        status: 'pending',
        created_at: new Date().toISOString()
      };

      console.log('💾 CRYPTO PAYMENT: About to insert payment data:', paymentData);

      const { data: paymentResult, error: paymentError } = await serviceClient
        .from('crypto_payment_transactions')
        .insert([paymentData])
        .select()
        .single();

      console.log('💾 CRYPTO PAYMENT: Database operation completed');
      console.log('💾 CRYPTO PAYMENT: Result:', paymentResult);
      console.log('💾 CRYPTO PAYMENT: Error:', paymentError);

      if (paymentError) {
        console.error('❌ CRYPTO PAYMENT: Payment creation error:', paymentError);
        console.error('❌ CRYPTO PAYMENT: Error details:', {
          message: paymentError.message,
          code: paymentError.code,
          details: paymentError.details,
          hint: paymentError.hint
        });
        throw new Error('Failed to create payment record: ' + paymentError.message);
      }

      console.log('✅ CRYPTO PAYMENT: Payment record created successfully:', paymentResult);
      return paymentResult;
    } catch (error) {
      console.error('❌ CRYPTO PAYMENT: Exception in createPaymentRecord:', error);
      console.error('❌ CRYPTO PAYMENT: Exception details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
      throw error;
    }
  };



  return (
    <div>
      <h2 style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '20px', textAlign: 'center' }}>
        {network.icon} {network.name} Payment
      </h2>



      {/* Payment Instructions */}
      <div style={{
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        border: '1px solid rgba(34, 197, 94, 0.3)',
        borderRadius: '12px',
        padding: '20px',
        marginBottom: '20px'
      }}>
        <h3 style={{ color: '#22c55e', fontWeight: 'bold', margin: '0 0 16px 0' }}>
          💰 PAYMENT INSTRUCTIONS:
        </h3>
        <div style={{ color: '#d1d5db', fontSize: '14px', lineHeight: '1.6' }}>
          <p style={{ margin: '0 0 8px 0' }}>• Amount: {totalCost.toFixed(2)} USDT</p>
          <p style={{ margin: '0 0 8px 0' }}>• Shares: {shares}</p>
          <p style={{ margin: '0 0 8px 0' }}>• Network: {network.technical}</p>
          <p style={{ margin: '0 0 8px 0' }}>• Send from your exchange/wallet to the address below</p>
          <p style={{ margin: '0' }}>• Enter your sending wallet address and transaction hash</p>
        </div>
      </div>

      {/* Company Wallet Address */}
      <div style={{ marginBottom: '20px' }}>
        <label style={{ display: 'block', color: '#9ca3af', marginBottom: '8px', fontSize: '14px' }}>
          Company Wallet Address ({network.technical})
        </label>
        <div style={{
          display: 'flex',
          backgroundColor: 'rgba(55, 65, 81, 0.5)',
          borderRadius: '8px',
          border: '2px solid #374151'
        }}>
          <input
            type="text"
            value={network.wallet_address}
            readOnly
            style={{
              flex: 1,
              padding: '12px',
              backgroundColor: 'transparent',
              border: 'none',
              color: 'white',
              fontSize: '14px',
              fontFamily: 'monospace'
            }}
          />
          <button
            onClick={() => copyToClipboard(network.wallet_address)}
            style={{
              padding: '12px 16px',
              backgroundColor: '#3b82f6',
              border: 'none',
              borderRadius: '0 6px 6px 0',
              color: 'white',
              fontSize: '14px',
              cursor: 'pointer'
            }}
          >
            Copy
          </button>
        </div>
      </div>

      {/* Manual Payment Form */}
      <div style={{ marginBottom: '20px' }}>
        <div style={{
          backgroundColor: 'rgba(34, 197, 94, 0.1)',
          border: '1px solid rgba(34, 197, 94, 0.3)',
          borderRadius: '12px',
          padding: '16px',
          marginBottom: '20px'
        }}>
          <h4 style={{ color: '#22c55e', fontWeight: 'bold', margin: '0 0 8px 0', fontSize: '14px' }}>
            💡 EXCHANGE PAYMENT INSTRUCTIONS:
          </h4>
          <div style={{ color: '#d1d5db', fontSize: '13px', lineHeight: '1.5' }}>
            <p style={{ margin: '0 0 4px 0' }}>1. Copy the company wallet address above</p>
            <p style={{ margin: '0 0 4px 0' }}>2. Send {totalCost.toFixed(2)} USDT from your exchange/wallet</p>
            <p style={{ margin: '0 0 4px 0' }}>3. Enter your sending wallet address and transaction hash below</p>
            <p style={{ margin: '0' }}>4. Submit for admin verification</p>
          </div>
        </div>

          {/* Sender Wallet Address */}
          <div style={{ marginBottom: '16px' }}>
            <label style={{ display: 'block', color: '#9ca3af', marginBottom: '8px', fontSize: '14px' }}>
              Your Wallet Address (where you sent from) *
            </label>
            <input
              type="text"
              value={senderWallet}
              onChange={(e) => setSenderWallet(e.target.value)}
              placeholder="Enter your wallet address (e.g., from Binance withdrawal)"
              style={{
                width: '100%',
                padding: '12px',
                backgroundColor: 'rgba(55, 65, 81, 0.5)',
                border: '2px solid #374151',
                borderRadius: '8px',
                color: 'white',
                fontSize: '14px'
              }}
            />
          </div>

          {/* Transaction Hash */}
          <div style={{ marginBottom: '16px' }}>
            <label style={{ display: 'block', color: '#9ca3af', marginBottom: '8px', fontSize: '14px' }}>
              Transaction Hash *
            </label>
            <input
              type="text"
              value={manualTxHash}
              onChange={(e) => setManualTxHash(e.target.value)}
              placeholder="Enter transaction hash from your exchange/wallet"
              style={{
                width: '100%',
                padding: '12px',
                backgroundColor: 'rgba(55, 65, 81, 0.5)',
                border: '2px solid #374151',
                borderRadius: '8px',
                color: 'white',
                fontSize: '14px',
                fontFamily: 'monospace'
              }}
            />
          </div>
        </div>

      {/* Error Display */}
      {error && (
        <div style={{
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          border: '1px solid rgba(239, 68, 68, 0.3)',
          borderRadius: '8px',
          padding: '12px',
          marginBottom: '20px'
        }}>
          <p style={{ color: '#f87171', fontSize: '14px', margin: 0 }}>
            ❌ {error}
          </p>
        </div>
      )}

      {/* Action Buttons */}
      <div style={{ display: 'flex', gap: '12px', marginTop: '30px' }}>
        <button
          onClick={onBack}
          disabled={isProcessingPayment}
          style={{
            flex: 1,
            padding: '12px',
            backgroundColor: '#6b7280',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontSize: '16px',
            cursor: isProcessingPayment ? 'not-allowed' : 'pointer',
            opacity: isProcessingPayment ? 0.5 : 1
          }}
        >
          Back
        </button>

        <button
          onClick={handleManualPayment}
          disabled={!senderWallet.trim() || !manualTxHash.trim() || isProcessingPayment}
          style={{
            flex: 2,
            padding: '12px',
            backgroundColor: (senderWallet.trim() && manualTxHash.trim()) ? '#10b981' : '#6b7280',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontSize: '16px',
            cursor: (!senderWallet.trim() || !manualTxHash.trim() || isProcessingPayment) ? 'not-allowed' : 'pointer',
            opacity: (!senderWallet.trim() || !manualTxHash.trim() || isProcessingPayment) ? 0.5 : 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '8px'
          }}
        >
          {isProcessingPayment ? (
            <>
              <div style={{
                width: '16px',
                height: '16px',
                border: '2px solid transparent',
                borderTop: '2px solid white',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }}></div>
              Submitting...
            </>
          ) : (
            `Submit Payment (${totalCost.toFixed(2)} USDT)`
          )}
        </button>
      </div>

      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default CryptoPaymentStep;
