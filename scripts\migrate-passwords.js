#!/usr/bin/env node

/**
 * PASSWORD MIGRATION SCRIPT
 * 
 * This script migrates users with old SHA-256 password hashes to require
 * password reset, ensuring they can create new secure bcrypt passwords.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import crypto from 'crypto';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

class PasswordMigrator {
  constructor() {
    this.migrationResults = {
      totalUsers: 0,
      oldHashUsers: 0,
      migratedUsers: 0,
      failedMigrations: 0,
      errors: [],
      migratedUsersList: []
    };
  }

  async migratePasswords() {
    console.log('🔄 STARTING PASSWORD MIGRATION');
    console.log('==============================\n');

    try {
      // First, analyze what we're working with
      await this.analyzeUsers();
      
      if (this.migrationResults.oldHashUsers === 0) {
        console.log('✅ NO MIGRATION NEEDED - All passwords are already secure!');
        return;
      }

      // Confirm migration
      console.log(`\n🚨 MIGRATION CONFIRMATION`);
      console.log(`Users to migrate: ${this.migrationResults.oldHashUsers}`);
      console.log(`Impact: These users will need to reset their passwords`);
      console.log(`Proceeding with migration in 3 seconds...\n`);
      
      // Wait 3 seconds
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Perform the migration
      await this.performMigration();
      
      // Generate migration report
      this.generateMigrationReport();

    } catch (error) {
      console.error('❌ Password migration failed:', error);
    }
  }

  async analyzeUsers() {
    console.log('🔍 Analyzing users for migration...');
    
    const { data: users, error } = await supabase
      .from('users')
      .select('id, email, username, password_hash, created_at')
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch users: ${error.message}`);
    }

    this.migrationResults.totalUsers = users.length;

    // Find users with old SHA-256 hashes
    for (const user of users) {
      if (this.isOldHashFormat(user.password_hash)) {
        this.migrationResults.oldHashUsers++;
        console.log(`   🔴 Found old hash: ${user.email}`);
      }
    }

    console.log(`   📊 Total users: ${this.migrationResults.totalUsers}`);
    console.log(`   🔴 Users needing migration: ${this.migrationResults.oldHashUsers}`);
  }

  isOldHashFormat(hash) {
    // Old SHA-256 hashes are 64 characters of hex
    return hash && hash.length === 64 && /^[a-f0-9]+$/.test(hash);
  }

  async performMigration() {
    console.log('🔄 Performing password migration...');

    // Get all users with old hash format
    const { data: users, error } = await supabase
      .from('users')
      .select('id, email, username, password_hash, created_at')
      .not('password_hash', 'is', null);

    if (error) {
      throw new Error(`Failed to fetch users for migration: ${error.message}`);
    }

    for (const user of users) {
      if (this.isOldHashFormat(user.password_hash)) {
        await this.migrateUser(user);
      }
    }
  }

  async migrateUser(user) {
    try {
      console.log(`   🔄 Migrating: ${user.email}...`);

      // Generate secure reset token
      const resetToken = crypto.randomBytes(32).toString('hex');
      const resetExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      // Update user record
      const { error: updateError } = await supabase
        .from('users')
        .update({
          reset_token: resetToken,
          reset_token_expires: resetExpires.toISOString(),
          password_hash: null, // Clear old vulnerable hash
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id);

      if (updateError) {
        throw new Error(`Failed to update user ${user.email}: ${updateError.message}`);
      }

      // Log the migration
      await this.logMigration(user, resetToken);

      this.migrationResults.migratedUsers++;
      this.migrationResults.migratedUsersList.push({
        id: user.id,
        email: user.email,
        username: user.username,
        resetToken: resetToken,
        resetExpires: resetExpires.toISOString()
      });

      console.log(`   ✅ Migrated: ${user.email}`);

    } catch (error) {
      console.log(`   ❌ Failed to migrate ${user.email}: ${error.message}`);
      this.migrationResults.failedMigrations++;
      this.migrationResults.errors.push({
        user: user.email,
        error: error.message
      });
    }
  }

  async logMigration(user, resetToken) {
    try {
      // Create migration log entry
      const { error } = await supabase
        .from('password_migration_log')
        .insert({
          user_id: user.id,
          user_email: user.email,
          migration_date: new Date().toISOString(),
          reset_token: resetToken,
          status: 'migrated'
        });

      // If table doesn't exist, that's okay - we'll log to console
      if (error && !error.message.includes('does not exist')) {
        console.log(`   ⚠️ Warning: Could not log migration for ${user.email}`);
      }
    } catch (error) {
      // Non-critical error - continue migration
      console.log(`   ⚠️ Warning: Migration logging failed for ${user.email}`);
    }
  }

  generateMigrationReport() {
    console.log('\n📋 PASSWORD MIGRATION REPORT');
    console.log('=============================');
    console.log(`Total Users Analyzed: ${this.migrationResults.totalUsers}`);
    console.log(`Users Needing Migration: ${this.migrationResults.oldHashUsers}`);
    console.log(`Successfully Migrated: ${this.migrationResults.migratedUsers} ✅`);
    console.log(`Failed Migrations: ${this.migrationResults.failedMigrations} ❌`);

    const successRate = ((this.migrationResults.migratedUsers / this.migrationResults.oldHashUsers) * 100).toFixed(1);
    console.log(`Migration Success Rate: ${successRate}%`);

    if (this.migrationResults.errors.length > 0) {
      console.log('\n❌ MIGRATION ERRORS:');
      this.migrationResults.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error.user}: ${error.error}`);
      });
    }

    if (this.migrationResults.migratedUsers > 0) {
      console.log('\n👥 MIGRATED USERS:');
      console.log('==================');
      this.migrationResults.migratedUsersList.forEach((user, index) => {
        console.log(`${index + 1}. ${user.email} (${user.username})`);
        console.log(`   Reset Token: ${user.resetToken.substring(0, 16)}...`);
        console.log(`   Expires: ${new Date(user.resetExpires).toLocaleString()}`);
        console.log('');
      });

      console.log('🔐 SECURITY IMPROVEMENTS ACHIEVED:');
      console.log('===================================');
      console.log('✅ Vulnerable SHA-256 hashes removed');
      console.log('✅ Secure reset tokens generated');
      console.log('✅ Users will create new bcrypt passwords');
      console.log('✅ Static salt vulnerability eliminated');

      console.log('\n📧 NEXT STEPS - USER NOTIFICATION:');
      console.log('===================================');
      console.log('1. Send password reset emails to migrated users');
      console.log('2. Include reset instructions and security explanation');
      console.log('3. Monitor password reset completion');
      console.log('4. Verify users can login with new passwords');

      console.log('\n📋 EMAIL TEMPLATE SUGGESTIONS:');
      console.log('===============================');
      console.log('Subject: Security Update - Password Reset Required');
      console.log('');
      console.log('Dear [User],');
      console.log('');
      console.log('We have upgraded our password security system to better');
      console.log('protect your account. As part of this security enhancement,');
      console.log('you will need to reset your password.');
      console.log('');
      console.log('Please click the link below to create a new secure password:');
      console.log('[Reset Password Link with token]');
      console.log('');
      console.log('This link will expire in 24 hours for security reasons.');
      console.log('');
      console.log('Thank you for helping us keep your account secure.');
      console.log('');
      console.log('Best regards,');
      console.log('Aureus Africa Security Team');

    }

    if (this.migrationResults.migratedUsers === this.migrationResults.oldHashUsers) {
      console.log('\n🎉 MIGRATION COMPLETED SUCCESSFULLY!');
      console.log('All users with vulnerable passwords have been migrated.');
      console.log('The system is now secure from static salt attacks.');
    } else {
      console.log('\n⚠️ MIGRATION PARTIALLY COMPLETED');
      console.log('Some users could not be migrated. Please review errors above.');
    }
  }
}

// Run the password migration
const migrator = new PasswordMigrator();
migrator.migratePasswords().catch(console.error);
