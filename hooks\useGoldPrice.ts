import { useState, useEffect } from 'react';
import { goldPriceService } from '../lib/services/goldPriceService';

interface GoldPriceData {
  price: number;
  lastUpdated: string;
  isLoading: boolean;
  error: string | null;
  source?: string;
  isLive?: boolean;
}

export const useGoldPrice = () => {
  const [goldPrice, setGoldPrice] = useState<GoldPriceData>({
    price: 120000, // CRITICAL: Use current market price as initial fallback
    lastUpdated: '',
    isLoading: true,
    error: null,
    source: 'Loading...',
    isLive: false,
  });

  const fetchGoldPrice = async () => {
    setGoldPrice(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const priceData = await goldPriceService.getCurrentPrice();

      setGoldPrice({
        price: priceData.price,
        lastUpdated: new Date(priceData.lastUpdated).toLocaleString(),
        isLoading: false,
        error: priceData.error || null,
        source: priceData.source,
        isLive: priceData.isLive,
      });

      console.log(`💰 Gold price: $${priceData.price.toLocaleString()}/kg (Source: ${priceData.source}, Live: ${priceData.isLive})`);

    } catch (error) {
      console.error('Critical error in fetchGoldPrice:', error);
      setGoldPrice(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to fetch price, using fallback',
        source: 'Error Fallback',
        isLive: false,
      }));
    }
  };

  useEffect(() => {
    // Fetch gold price on component mount
    fetchGoldPrice();
    
    // Set up interval to fetch every 5 minutes
    const interval = setInterval(fetchGoldPrice, 5 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, []);

  return {
    ...goldPrice,
    refetch: fetchGoldPrice,
  };
};
