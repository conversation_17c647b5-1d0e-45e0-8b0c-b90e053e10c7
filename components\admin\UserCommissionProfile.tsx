/**
 * ADVANCED USER COMMISSION PROFILING COMPONENT
 * 
 * Detailed commission analysis for individual users including:
 * - Complete commission transaction history
 * - Mathematical verification of all calculations
 * - Referral tree visualization
 * - Commission balance reconciliation
 * - Timeline view of commission activities
 * - Commission genealogy tracking
 */

import React, { useState, useEffect, useCallback } from 'react'
import { createClient } from '@supabase/supabase-js'

interface UserCommissionData {
  user_id: number
  username: string
  email: string
  registration_date: string
  total_purchases: number
  total_purchase_amount: number
  total_shares_purchased: number
  commission_balance: {
    usdt_balance: number
    share_balance: number
    total_earned_usdt: number
    total_earned_shares: number
    last_updated: string
  }
  commission_transactions: CommissionTransaction[]
  referral_tree: ReferralNode[]
  commission_timeline: CommissionTimelineEvent[]
  discrepancies: any[]
  accuracy_score: number
}

interface CommissionTransaction {
  id: string
  referrer_id: number
  referred_id: number
  referred_username: string
  referred_email: string
  share_purchase_id: string
  commission_rate: number
  share_purchase_amount: number
  usdt_commission: number
  share_commission: number
  expected_usdt_commission: number
  expected_share_commission: number
  accuracy_status: 'ACCURATE' | 'DISCREPANCY'
  status: string
  payment_date: string
  created_at: string
  phase_id: number
}

interface ReferralNode {
  user_id: number
  username: string
  email: string
  level: number
  direct_referrals: number
  total_referrals: number
  total_commission_earned: number
  registration_date: string
  children: ReferralNode[]
}

interface CommissionTimelineEvent {
  id: string
  type: 'PURCHASE' | 'COMMISSION_EARNED' | 'COMMISSION_PAID' | 'BALANCE_UPDATE' | 'CORRECTION'
  date: string
  description: string
  amount_usdt?: number
  amount_shares?: number
  related_user?: string
  status: 'SUCCESS' | 'PENDING' | 'FAILED'
}

interface UserCommissionProfileProps {
  userId: number
  supabase: any
  onClose: () => void
}

const UserCommissionProfile: React.FC<UserCommissionProfileProps> = ({
  userId,
  supabase,
  onClose
}) => {
  const [userData, setUserData] = useState<UserCommissionData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'overview' | 'transactions' | 'referrals' | 'timeline' | 'reconciliation'>('overview')

  // Fetch comprehensive user commission data
  const fetchUserCommissionData = useCallback(async () => {
    try {
      setLoading(true)
      console.log(`🔍 Fetching commission data for user ${userId}...`)

      // Get basic user information
      const { data: userInfo, error: userError } = await supabase
        .from('users')
        .select('id, username, email, created_at')
        .eq('id', userId)
        .single()

      if (userError) {
        throw new Error(`Failed to fetch user info: ${userError.message}`)
      }

      // Get user's purchase history
      const { data: purchases, error: purchaseError } = await supabase
        .from('aureus_share_purchases')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'active')

      if (purchaseError) {
        throw new Error(`Failed to fetch purchases: ${purchaseError.message}`)
      }

      // Get commission balance
      const { data: commissionBalance, error: balanceError } = await supabase
        .from('commission_balances')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (balanceError && balanceError.code !== 'PGRST116') {
        throw new Error(`Failed to fetch commission balance: ${balanceError.message}`)
      }

      // Get commission transactions (both as referrer and referred)
      const { data: commissionTransactions, error: commissionError } = await supabase
        .from('commission_transactions')
        .select(`
          *,
          referred:users!referred_id(username, email)
        `)
        .eq('referrer_id', userId)
        .order('created_at', { ascending: false })

      if (commissionError) {
        throw new Error(`Failed to fetch commission transactions: ${commissionError.message}`)
      }

      // Build referral tree
      const referralTree = await this.buildReferralTree(userId)

      // Generate commission timeline
      const commissionTimeline = await this.generateCommissionTimeline(userId, purchases, commissionTransactions)

      // Calculate accuracy score
      const accuracyScore = this.calculateAccuracyScore(commissionTransactions)

      const userData: UserCommissionData = {
        user_id: userId,
        username: userInfo.username,
        email: userInfo.email,
        registration_date: userInfo.created_at,
        total_purchases: purchases?.length || 0,
        total_purchase_amount: purchases?.reduce((sum, p) => sum + parseFloat(p.total_amount), 0) || 0,
        total_shares_purchased: purchases?.reduce((sum, p) => sum + parseFloat(p.shares_purchased), 0) || 0,
        commission_balance: {
          usdt_balance: parseFloat(commissionBalance?.usdt_balance || '0'),
          share_balance: parseFloat(commissionBalance?.share_balance || '0'),
          total_earned_usdt: parseFloat(commissionBalance?.total_earned_usdt || '0'),
          total_earned_shares: parseFloat(commissionBalance?.total_earned_shares || '0'),
          last_updated: commissionBalance?.last_updated || new Date().toISOString()
        },
        commission_transactions: this.processCommissionTransactions(commissionTransactions || []),
        referral_tree: referralTree,
        commission_timeline: commissionTimeline,
        discrepancies: [], // Will be populated by discrepancy detection
        accuracy_score: accuracyScore
      }

      setUserData(userData)

    } catch (err) {
      console.error('❌ Failed to fetch user commission data:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }, [userId, supabase])

  // Process commission transactions with accuracy verification
  const processCommissionTransactions = (transactions: any[]): CommissionTransaction[] => {
    return transactions.map(transaction => {
      const expectedUsdtCommission = parseFloat(transaction.share_purchase_amount) * 0.15
      const expectedShareCommission = (parseFloat(transaction.share_purchase_amount) / 5.00) * 0.15 // Assuming $5 per share

      const actualUsdtCommission = parseFloat(transaction.usdt_commission || '0')
      const actualShareCommission = parseFloat(transaction.share_commission || '0')

      const usdtAccurate = Math.abs(actualUsdtCommission - expectedUsdtCommission) <= 0.01
      const shareAccurate = Math.abs(actualShareCommission - expectedShareCommission) <= 0.001

      return {
        ...transaction,
        referred_username: transaction.referred?.username || 'Unknown',
        referred_email: transaction.referred?.email || 'Unknown',
        expected_usdt_commission: expectedUsdtCommission,
        expected_share_commission: expectedShareCommission,
        accuracy_status: (usdtAccurate && shareAccurate) ? 'ACCURATE' : 'DISCREPANCY'
      }
    })
  }

  // Build referral tree
  const buildReferralTree = async (rootUserId: number): Promise<ReferralNode[]> => {
    try {
      const { data: directReferrals, error } = await supabase
        .from('referrals')
        .select(`
          referred_id,
          users!referred_id(id, username, email, created_at)
        `)
        .eq('referrer_id', rootUserId)
        .eq('status', 'active')

      if (error) {
        console.error('Failed to fetch referrals:', error)
        return []
      }

      const referralNodes: ReferralNode[] = []

      for (const referral of directReferrals || []) {
        const user = referral.users
        if (!user) continue

        // Get commission data for this referral
        const { data: commissionData } = await supabase
          .from('commission_transactions')
          .select('usdt_commission, share_commission')
          .eq('referred_id', user.id)
          .eq('referrer_id', rootUserId)

        const totalCommissionEarned = commissionData?.reduce((sum, c) => 
          sum + parseFloat(c.usdt_commission || '0') + (parseFloat(c.share_commission || '0') * 5.00), 0
        ) || 0

        // Recursively build children (for multi-level referrals)
        const children = await this.buildReferralTree(user.id)

        referralNodes.push({
          user_id: user.id,
          username: user.username,
          email: user.email,
          level: 1, // Direct referral
          direct_referrals: children.length,
          total_referrals: children.length + children.reduce((sum, child) => sum + child.total_referrals, 0),
          total_commission_earned: totalCommissionEarned,
          registration_date: user.created_at,
          children
        })
      }

      return referralNodes
    } catch (error) {
      console.error('Failed to build referral tree:', error)
      return []
    }
  }

  // Generate commission timeline
  const generateCommissionTimeline = async (
    userId: number, 
    purchases: any[], 
    commissions: any[]
  ): Promise<CommissionTimelineEvent[]> => {
    const timeline: CommissionTimelineEvent[] = []

    // Add purchase events
    purchases?.forEach(purchase => {
      timeline.push({
        id: `purchase_${purchase.id}`,
        type: 'PURCHASE',
        date: purchase.created_at,
        description: `Purchased ${purchase.shares_purchased} shares for $${purchase.total_amount}`,
        amount_usdt: parseFloat(purchase.total_amount),
        amount_shares: parseFloat(purchase.shares_purchased),
        status: 'SUCCESS'
      })
    })

    // Add commission events
    commissions?.forEach(commission => {
      timeline.push({
        id: `commission_${commission.id}`,
        type: 'COMMISSION_EARNED',
        date: commission.created_at,
        description: `Earned commission from ${commission.referred?.username || 'referral'}`,
        amount_usdt: parseFloat(commission.usdt_commission || '0'),
        amount_shares: parseFloat(commission.share_commission || '0'),
        related_user: commission.referred?.username,
        status: commission.status === 'approved' ? 'SUCCESS' : 'PENDING'
      })
    })

    // Sort by date
    return timeline.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
  }

  // Calculate accuracy score
  const calculateAccuracyScore = (transactions: CommissionTransaction[]): number => {
    if (transactions.length === 0) return 100

    const accurateTransactions = transactions.filter(t => t.accuracy_status === 'ACCURATE').length
    return (accurateTransactions / transactions.length) * 100
  }

  useEffect(() => {
    fetchUserCommissionData()
  }, [fetchUserCommissionData])

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading user commission profile...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="text-red-500 text-4xl mb-4">❌</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Profile</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    )
  }

  if (!userData) {
    return null
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-yellow-600 to-yellow-700 text-white px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold">{userData.username} - Commission Profile</h2>
              <p className="text-yellow-100 text-sm">{userData.email}</p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <div className="text-sm text-yellow-100">Accuracy Score</div>
                <div className="text-2xl font-bold">{userData.accuracy_score.toFixed(1)}%</div>
              </div>
              <button
                onClick={onClose}
                className="text-white hover:text-yellow-200 text-2xl font-bold"
              >
                ×
              </button>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="border-b border-gray-200">
          <nav className="px-6">
            <div className="flex space-x-8">
              {[
                { id: 'overview', label: 'Overview', icon: '📊' },
                { id: 'transactions', label: 'Transactions', icon: '💰' },
                { id: 'referrals', label: 'Referral Tree', icon: '🌳' },
                { id: 'timeline', label: 'Timeline', icon: '📅' },
                { id: 'reconciliation', label: 'Reconciliation', icon: '🔍' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-yellow-500 text-yellow-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </div>
          </nav>
        </div>

        {/* Content */}
        <div className="p-6 max-h-[60vh] overflow-y-auto">
          {activeTab === 'overview' && (
            <UserOverviewTab userData={userData} />
          )}

          {activeTab === 'transactions' && (
            <UserTransactionsTab userData={userData} />
          )}

          {activeTab === 'referrals' && (
            <UserReferralTreeTab userData={userData} />
          )}

          {activeTab === 'timeline' && (
            <UserTimelineTab userData={userData} />
          )}

          {activeTab === 'reconciliation' && (
            <UserReconciliationTab userData={userData} />
          )}
        </div>
      </div>
    </div>
  )
}

// Overview Tab Component
const UserOverviewTab: React.FC<{ userData: UserCommissionData }> = ({ userData }) => (
  <div className="space-y-6">
    {/* Key Metrics */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div className="bg-blue-50 rounded-lg p-4">
        <div className="text-blue-600 text-sm font-medium">Total Purchases</div>
        <div className="text-2xl font-bold text-blue-900">{userData.total_purchases}</div>
        <div className="text-blue-600 text-sm">${userData.total_purchase_amount.toFixed(2)}</div>
      </div>

      <div className="bg-green-50 rounded-lg p-4">
        <div className="text-green-600 text-sm font-medium">USDT Balance</div>
        <div className="text-2xl font-bold text-green-900">${userData.commission_balance.usdt_balance.toFixed(2)}</div>
        <div className="text-green-600 text-sm">Total: ${userData.commission_balance.total_earned_usdt.toFixed(2)}</div>
      </div>

      <div className="bg-purple-50 rounded-lg p-4">
        <div className="text-purple-600 text-sm font-medium">Share Balance</div>
        <div className="text-2xl font-bold text-purple-900">{userData.commission_balance.share_balance.toFixed(4)}</div>
        <div className="text-purple-600 text-sm">Total: {userData.commission_balance.total_earned_shares.toFixed(4)}</div>
      </div>

      <div className="bg-yellow-50 rounded-lg p-4">
        <div className="text-yellow-600 text-sm font-medium">Referrals</div>
        <div className="text-2xl font-bold text-yellow-900">{userData.referral_tree.length}</div>
        <div className="text-yellow-600 text-sm">Direct referrals</div>
      </div>
    </div>

    {/* Recent Activity */}
    <div className="bg-white border rounded-lg p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Commission Activity</h3>
      <div className="space-y-3">
        {userData.commission_timeline.slice(0, 5).map((event) => (
          <div key={event.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center">
              <div className={`w-3 h-3 rounded-full mr-3 ${
                event.status === 'SUCCESS' ? 'bg-green-500' : 
                event.status === 'PENDING' ? 'bg-yellow-500' : 'bg-red-500'
              }`}></div>
              <div>
                <p className="font-medium text-gray-900">{event.description}</p>
                <p className="text-sm text-gray-600">{new Date(event.date).toLocaleDateString()}</p>
              </div>
            </div>
            <div className="text-right">
              {event.amount_usdt && (
                <div className="font-medium text-gray-900">${event.amount_usdt.toFixed(2)}</div>
              )}
              {event.amount_shares && (
                <div className="text-sm text-gray-600">{event.amount_shares.toFixed(4)} shares</div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
)

// Placeholder components for other tabs
const UserTransactionsTab: React.FC<{ userData: UserCommissionData }> = ({ userData }) => (
  <div>Commission Transactions Tab - Coming Next</div>
)

const UserReferralTreeTab: React.FC<{ userData: UserCommissionData }> = ({ userData }) => (
  <div>Referral Tree Visualization - Coming Next</div>
)

const UserTimelineTab: React.FC<{ userData: UserCommissionData }> = ({ userData }) => (
  <div>Commission Timeline - Coming Next</div>
)

const UserReconciliationTab: React.FC<{ userData: UserCommissionData }> = ({ userData }) => (
  <div>Commission Reconciliation - Coming Next</div>
)

export default UserCommissionProfile
