import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';

interface ReferralLinksCardProps {
  userId: number;
  className?: string;
}

interface UserInfo {
  id: number;
  username: string;
  email: string;
  first_name?: string;
  last_name?: string;
}

export const ReferralLinksCard: React.FC<ReferralLinksCardProps> = ({ userId, className = '' }) => {
  const [user, setUser] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [copiedLink, setCopiedLink] = useState<string>('');

  useEffect(() => {
    loadUserInfo();
  }, [userId]);

  const loadUserInfo = async () => {
    try {
      const { data: userData, error } = await supabase
        .from('users')
        .select('id, username, email, first_name, last_name')
        .eq('id', userId)
        .single();

      if (error) throw error;
      setUser(userData);
    } catch (error) {
      console.error('Error loading user info:', error);
    } finally {
      setLoading(false);
    }
  };

  const getReferralUsername = (user: UserInfo) => {
    // Use exact database username without any modification
    return user?.username || `user${user.id}`;
  };

  const generateReferralLinks = (user: UserInfo) => {
    const username = getReferralUsername(user);

    return {
      customLandingPage: `https://aureus.africa/${username}`
    };
  };

  const copyToClipboard = async (link: string, linkType: string) => {
    try {
      await navigator.clipboard.writeText(link);
      setCopiedLink(linkType);
      setTimeout(() => setCopiedLink(''), 2000);
    } catch (error) {
      console.error('Failed to copy link:', error);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = link;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopiedLink(linkType);
      setTimeout(() => setCopiedLink(''), 2000);
    }
  };

  if (loading) {
    return (
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 text-center">
        <div className="text-3xl mb-2">🔗</div>
        <h3 className="text-white text-lg font-semibold mb-2">Referral Links</h3>
        <p className="text-gray-400 text-sm">Loading your referral links...</p>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 text-center">
        <div className="text-3xl mb-2">❌</div>
        <h3 className="text-white text-lg font-semibold mb-2">Error</h3>
        <p className="text-gray-400 text-sm">Unable to load referral links</p>
      </div>
    );
  }

  const links = generateReferralLinks(user);
  const username = getReferralUsername(user);

  return (
    <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
      <div className="text-center mb-4">
        <div className="text-3xl mb-2">🔗</div>
        <h3 className="text-white text-lg font-semibold mb-2">Referral Links</h3>
        <p className="text-gray-400 text-sm mb-2">
          Share your links to grow your network
        </p>
        <div className="bg-gray-700 rounded-lg p-2 mb-4">
          <span className="text-yellow-400 font-mono text-sm">@{username}</span>
        </div>
      </div>

      <div className="space-y-4">
        {/* Primary Custom Landing Page */}
        <div className="bg-gradient-to-r from-yellow-600 to-yellow-500 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <span className="text-white text-lg">🎯</span>
              <span className="text-white text-sm font-semibold">Your Personal Landing Page</span>
            </div>
            <button
              onClick={() => copyToClipboard(links.customLandingPage, 'landing')}
              className={`px-4 py-2 rounded text-sm font-medium transition-colors ${
                copiedLink === 'landing'
                  ? 'bg-green-600 text-white'
                  : 'bg-white text-yellow-600 hover:bg-gray-100'
              }`}
            >
              {copiedLink === 'landing' ? '✓ Copied!' : 'Copy Link'}
            </button>
          </div>
          <div className="text-white text-xs font-mono break-all bg-black/20 p-3 rounded mb-3">
            {links.customLandingPage}
          </div>
          <div className="text-white text-xs">
            ✨ Professional landing page with your profile, company info, calculator, and auto-registration
          </div>
        </div>


      </div>

      {/* Quick Actions */}
      <div className="mt-4 pt-4 border-t border-gray-600">
        <div className="grid grid-cols-2 gap-2">
          <button
            onClick={() => window.open(links.customLandingPage, '_blank')}
            className="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-2 rounded text-xs font-medium transition-colors"
          >
            👁️ Preview Page
          </button>
          <button
            onClick={() => {
              const marketingSection = document.querySelector('[data-section="marketing"]');
              if (marketingSection) {
                marketingSection.scrollIntoView({ behavior: 'smooth' });
              }
            }}
            className="bg-purple-600 hover:bg-purple-700 text-white px-3 py-2 rounded text-xs font-medium transition-colors"
          >
            📱 Marketing Tools
          </button>
        </div>
      </div>
    </div>
  );
};
