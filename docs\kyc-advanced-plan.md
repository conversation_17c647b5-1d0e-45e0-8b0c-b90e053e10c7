Plan to restore and stabilize advanced facial recognition KYC:

1) Library strategy
- Primary: MediaPipe Face Mesh (468 landmarks) for robust detection
- Backup: face-api.js (TinyFaceDetector + Landmark + Expressions) if TFJS stable
- Avoid version conflicts: keep tfjs ^4.22 with face-api.js 0.22.2 (works) and load from /public/models

2) Model loading & initialization
- Add initializeModels() that awaits tf.ready(), attempts face-api models, then sets up FaceMesh with CDN loader
- Do not crash when face-api fails; continue with MediaPipe primary path

3) 9-step verification flow
- Steps: face_detection, landmark_mapping, blink_detection, head_left, head_right, smile_detection, eye_gaze_tracking, mouth_movement, security_checks, quality_assessment
- For each step, call matching methods in lib/advancedFacialRecognition.ts

4) Liveness & security
- performAdvancedLivenessDetection(video, ['blink','head_movement','smile','eye_gaze','mouth_movement'])
- performAntiSpoofingDetection, performDepthAnalysis, performTextureAnalysis combined in liveness result

5) Template creation & results
- createSecureBiometricTemplate(userId, canvas)
- Build AdvancedFacialRecognitionResults with liveness_checks, security_analysis, landmark_analysis, metadata

6) Error handling & UX
- Graceful fallbacks, clear errors, retry option

7) Testing
- Run dev, verify flows visually, ensure no TFJS runtime errors in console.

