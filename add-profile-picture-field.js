/**
 * ADD PROFILE PICTURE FIELD TO USERS TABLE
 * 
 * This script adds a profile_image_url field to the users table
 * to support profile pictures in the user dashboard.
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with service role key for admin operations
const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtlandlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNzI2NzQ5NCwiZXhwIjoyMDQyODQzNDk0fQ.6sP8nJlnlNqzqQs7JNcVl8Ej8NuU8KhZ8Ej8NuU8KhZ';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function addProfilePictureField() {
  console.log('🖼️ Adding profile picture field to users table...\n');

  try {
    // Check if the column already exists
    const { data: columns, error: columnError } = await supabase
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_name', 'users')
      .eq('column_name', 'profile_image_url');

    if (columnError) {
      console.error('❌ Error checking existing columns:', columnError);
      return;
    }

    if (columns && columns.length > 0) {
      console.log('✅ profile_image_url column already exists in users table');
      return;
    }

    // Add the profile_image_url column
    const { error: alterError } = await supabase.rpc('exec_sql', {
      sql: `
        ALTER TABLE public.users 
        ADD COLUMN IF NOT EXISTS profile_image_url TEXT;
        
        COMMENT ON COLUMN public.users.profile_image_url IS 'URL to user profile picture stored in Supabase Storage';
      `
    });

    if (alterError) {
      console.error('❌ Error adding profile_image_url column:', alterError);
      return;
    }

    console.log('✅ Successfully added profile_image_url column to users table');

    // Verify the column was added
    const { data: verifyColumns, error: verifyError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable')
      .eq('table_name', 'users')
      .eq('column_name', 'profile_image_url');

    if (verifyError) {
      console.error('❌ Error verifying column addition:', verifyError);
      return;
    }

    if (verifyColumns && verifyColumns.length > 0) {
      console.log('✅ Column verification successful:');
      console.log('   Column Name:', verifyColumns[0].column_name);
      console.log('   Data Type:', verifyColumns[0].data_type);
      console.log('   Nullable:', verifyColumns[0].is_nullable);
    }

    console.log('\n🎉 Profile picture field setup complete!');
    console.log('\n📝 Next steps:');
    console.log('   1. Create profile picture upload component');
    console.log('   2. Update UserDashboard to display profile pictures');
    console.log('   3. Set up Supabase Storage bucket for profile pictures');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the migration
addProfilePictureField();
