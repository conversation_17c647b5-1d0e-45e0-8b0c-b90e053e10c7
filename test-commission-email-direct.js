/**
 * Direct test of commission email functionality
 * This will send a test commission email to verify the email system works
 */

import 'dotenv/config';
import { resendEmailService } from './lib/resendEmailService.ts';

const testCommissionEmail = async () => {
  try {
    console.log('📧 TESTING COMMISSION EMAIL DIRECTLY');
    console.log('='.repeat(50));

    console.log('\n🔧 Testing email service configuration...');
    
    // Test email data
    const testEmailData = {
      email: '<EMAIL>', // Your email for testing
      fullName: 'Test Sponsor',
      usdtCommission: 11.25,
      shareCommission: 2.25,
      referredUserName: 'referred_1758570817272',
      purchaseAmount: 75.00,
      transactionId: '1df0b6bd-ab95-48cc-8c2a-a2b3cd45b184'
    };

    console.log('📋 Test Email Data:');
    console.log(`   • Recipient: ${testEmailData.email}`);
    console.log(`   • Sponsor Name: ${testEmailData.fullName}`);
    console.log(`   • USDT Commission: $${testEmailData.usdtCommission}`);
    console.log(`   • Share Commission: ${testEmailData.shareCommission} shares`);
    console.log(`   • Referred User: ${testEmailData.referredUserName}`);
    console.log(`   • Purchase Amount: $${testEmailData.purchaseAmount}`);

    console.log('\n📧 Sending commission email...');
    
    const emailResult = await resendEmailService.sendCommissionEarnedNotification(testEmailData);

    if (emailResult.success) {
      console.log('✅ Commission email sent successfully!');
      console.log(`   • Message ID: ${emailResult.messageId}`);
      console.log(`   • Sent to: ${testEmailData.email}`);
      console.log('\n📬 Check your email inbox for the commission notification!');
      
      console.log('\n🎯 EMAIL TEST RESULTS:');
      console.log('✅ Email service: WORKING');
      console.log('✅ Commission template: WORKING');
      console.log('✅ Email delivery: SUCCESS');
      
    } else {
      console.log('❌ Commission email failed:');
      console.log(`   • Error: ${emailResult.error}`);
      
      console.log('\n🔍 Troubleshooting:');
      console.log('1. Check RESEND_API_KEY in .env file');
      console.log('2. Verify RESEND_FROM_EMAIL is configured');
      console.log('3. Check Resend dashboard for any issues');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.log('\n🔍 Error Details:');
    console.log(`   • Message: ${error.message}`);
    console.log(`   • Stack: ${error.stack}`);
  }
};

testCommissionEmail();
