import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';

// Supabase configuration
const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const debugUser246 = async () => {
  try {
    console.log('🔍 DEBUGGING USER ID 246 LOGIN ISSUE');
    console.log('=====================================');

    // Step 1: Check if user exists in users table
    console.log('\n📋 Step 1: Checking users table...');
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', 246)
      .single();

    if (userError) {
      console.log('❌ Error fetching user:', userError.message);
      return;
    }

    if (!user) {
      console.log('❌ User ID 246 not found in users table');
      return;
    }

    console.log('✅ User found in users table:');
    console.log('   ID:', user.id);
    console.log('   Email:', user.email);
    console.log('   Username:', user.username);
    console.log('   Full Name:', user.full_name);
    console.log('   Phone:', user.phone);
    console.log('   Country:', user.country_of_residence);
    console.log('   Is Active:', user.is_active);
    console.log('   Account Status:', user.account_status);
    console.log('   Created At:', user.created_at);
    console.log('   Updated At:', user.updated_at);
    console.log('   Has Password Hash:', !!user.password_hash);
    console.log('   Password Hash Length:', user.password_hash?.length || 0);
    console.log('   Country Selection Completed:', user.country_selection_completed);
    console.log('   Migration Status:', user.migration_status);
    console.log('   Migration Completed At:', user.migration_completed_at);
    console.log('   Web Credentials Set At:', user.web_credentials_set_at);
    console.log('   Telegram User ID:', user.telegram_user_id);

    // Step 2: Check telegram_users table if linked
    if (user.telegram_user_id) {
      console.log('\n📋 Step 2: Checking telegram_users table...');
      const { data: telegramUser, error: telegramError } = await supabase
        .from('telegram_users')
        .select('*')
        .eq('telegram_user_id', user.telegram_user_id)
        .single();

      if (telegramError) {
        console.log('❌ Error fetching telegram user:', telegramError.message);
      } else if (telegramUser) {
        console.log('✅ Telegram user found:');
        console.log('   Telegram ID:', telegramUser.telegram_user_id);
        console.log('   Username:', telegramUser.username);
        console.log('   First Name:', telegramUser.first_name);
        console.log('   Last Name:', telegramUser.last_name);
        console.log('   User ID Link:', telegramUser.user_id);
        console.log('   Created At:', telegramUser.created_at);
      } else {
        console.log('⚠️ No telegram user found despite telegram_user_id being set');
      }
    } else {
      console.log('\n📋 Step 2: No telegram_user_id - this is a web-only account');
    }

    // Step 3: Check account status issues
    console.log('\n📋 Step 3: Account Status Analysis...');

    const issues = [];

    if (!user.is_active) {
      issues.push('❌ Account is INACTIVE');
    }

    if (!user.password_hash) {
      issues.push('❌ No password hash - user cannot login with password');
    }

    if (!user.email) {
      issues.push('❌ No email address');
    }

    if (user.account_status && user.account_status !== 'active') {
      issues.push(`❌ Account status is: ${user.account_status}`);
    }

    if (!user.country_selection_completed) {
      issues.push('⚠️ Country selection not completed (may redirect to profile completion)');
    }

    if (!user.full_name) {
      issues.push('⚠️ Full name missing (may redirect to profile completion)');
    }

    if (!user.phone) {
      issues.push('⚠️ Phone number missing (may redirect to profile completion)');
    }

    if (issues.length === 0) {
      console.log('✅ No obvious account issues found');
    } else {
      console.log('🚨 ISSUES FOUND:');
      issues.forEach(issue => console.log('   ' + issue));
    }

    // Step 4: Test password if provided
    console.log('\n📋 Step 4: Password Testing...');
    if (user.password_hash) {
      console.log('✅ Password hash exists, ready for password testing');
      console.log('   Full hash value:', user.password_hash);
      console.log('   Hash length:', user.password_hash.length);
      console.log('   Hash algorithm appears to be:', user.password_hash.startsWith('$2') ? 'bcrypt' : 'unknown/invalid');

      // Check if this is a valid bcrypt hash
      if (user.password_hash.startsWith('$2') && user.password_hash.length >= 60) {
        console.log('✅ Valid bcrypt hash format');
      } else {
        console.log('❌ INVALID HASH FORMAT - This is why login fails!');
        console.log('   Expected: bcrypt hash starting with $2a/$2b/$2y and ~60 characters');
        console.log('   Actual: ' + user.password_hash);

        if (user.password_hash.includes('telegram_')) {
          console.log('🔍 This appears to be a Telegram migration placeholder, not a real password hash');
        }
      }
    } else {
      console.log('❌ No password hash - user cannot login with email/password');
    }

    // Step 5: Check for any blocking conditions
    console.log('\n📋 Step 5: Login Flow Analysis...');

    if (user.is_active && user.password_hash && user.email) {
      console.log('✅ Basic login requirements met');

      if (user.country_selection_completed && user.full_name && user.phone) {
        console.log('✅ Profile complete - should redirect to dashboard');
      } else {
        console.log('⚠️ Incomplete profile - will redirect to profile completion page');
        console.log('   This is NORMAL behavior, not a login failure');
      }
    } else {
      console.log('❌ Basic login requirements NOT met - login will fail');
    }

    console.log('\n🎯 SUMMARY FOR USER ID 246:');
    console.log('============================');
    if (!user.is_active) {
      console.log('🚨 CRITICAL: Account is INACTIVE - this is why login fails');
      console.log('   SOLUTION: Activate the account in admin panel');
    } else if (!user.password_hash) {
      console.log('🚨 CRITICAL: No password set - user cannot login');
      console.log('   SOLUTION: User needs to set password via migration or reset');
    } else if (!user.email) {
      console.log('🚨 CRITICAL: No email address - cannot login');
      console.log('   SOLUTION: Set email address in admin panel');
    } else {
      console.log('✅ Account should be able to login');
      console.log('   If still failing, check:');
      console.log('   1. User is entering correct email/password');
      console.log('   2. No network/browser issues');
      console.log('   3. Check browser console for specific error messages');
    }

  } catch (error) {
    console.error('❌ Debug script error:', error);
  }
};

debugUser246();