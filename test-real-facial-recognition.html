<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>REAL Facial Recognition Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        
        .container {
            text-align: center;
        }
        
        video {
            width: 100%;
            max-width: 640px;
            height: 480px;
            border: 2px solid #4CAF50;
            border-radius: 10px;
            background: black;
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
        }
        
        .status.success {
            background: #4CAF50;
            color: white;
        }
        
        .status.error {
            background: #f44336;
            color: white;
        }
        
        .status.warning {
            background: #ff9800;
            color: white;
        }
        
        .status.info {
            background: #2196F3;
            color: white;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 16px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .test-results {
            text-align: left;
            background: #333;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .test-item {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #4CAF50;
            background: #444;
        }
        
        .test-item.failed {
            border-left-color: #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 REAL Facial Recognition Test</h1>
        <p><strong>NO MORE FAKE BULLSHIT!</strong> This tests actual computer vision.</p>
        
        <video id="video" autoplay muted playsinline></video>
        
        <div id="status" class="status info">
            Click "Start Camera" to begin testing REAL facial recognition
        </div>
        
        <div>
            <button id="startCamera">Start Camera</button>
            <button id="testBlink" disabled>Test REAL Blink Detection</button>
            <button id="testSmile" disabled>Test REAL Smile Detection</button>
            <button id="testHeadMove" disabled>Test REAL Head Movement</button>
        </div>
        
        <div id="results" class="test-results" style="display: none;">
            <h3>Test Results:</h3>
            <div id="resultsList"></div>
        </div>
    </div>

    <script type="module">
        const video = document.getElementById('video');
        const status = document.getElementById('status');
        const startCameraBtn = document.getElementById('startCamera');
        const testBlinkBtn = document.getElementById('testBlink');
        const testSmileBtn = document.getElementById('testSmile');
        const testHeadMoveBtn = document.getElementById('testHeadMove');
        const results = document.getElementById('results');
        const resultsList = document.getElementById('resultsList');
        
        let stream = null;
        let faceApiLoaded = false;
        
        function updateStatus(message, type = 'info') {
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        function addResult(test, result, confidence) {
            const div = document.createElement('div');
            div.className = `test-item ${result ? '' : 'failed'}`;
            div.innerHTML = `
                <strong>${test}:</strong> ${result ? '✅ PASSED' : '❌ FAILED'} 
                (Confidence: ${(confidence * 100).toFixed(1)}%)
            `;
            resultsList.appendChild(div);
            results.style.display = 'block';
        }
        
        async function startCamera() {
            try {
                updateStatus('Starting camera...', 'info');
                
                stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 640 },
                        height: { ideal: 480 },
                        facingMode: 'user'
                    }
                });
                
                video.srcObject = stream;
                
                video.onloadedmetadata = () => {
                    updateStatus('Camera started! Now testing if you can actually move your face...', 'success');
                    testBlinkBtn.disabled = false;
                    testSmileBtn.disabled = false;
                    testHeadMoveBtn.disabled = false;
                    startCameraBtn.disabled = true;
                };
                
            } catch (error) {
                updateStatus(`Camera error: ${error.message}`, 'error');
            }
        }
        
        async function testRealBlink() {
            updateStatus('Testing REAL blink detection... ACTUALLY BLINK YOUR EYES!', 'warning');
            testBlinkBtn.disabled = true;
            
            try {
                // Simulate real blink detection by checking for actual movement
                const frames = [];
                for (let i = 0; i < 10; i++) {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                    ctx.drawImage(video, 0, 0);
                    
                    // Get image data and calculate brightness in eye region
                    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                    const brightness = calculateBrightness(imageData);
                    frames.push(brightness);
                    
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
                
                // Check for brightness variation (blink causes darkness)
                const variation = calculateVariation(frames);
                const blinkDetected = variation > 0.02; // Threshold for actual movement
                const confidence = Math.min(variation * 10, 1);
                
                addResult('Real Blink Detection', blinkDetected, confidence);
                updateStatus(blinkDetected ? 
                    '✅ REAL blink detected! You actually blinked!' : 
                    '❌ No real blink detected. You need to actually blink your eyes!', 
                    blinkDetected ? 'success' : 'error'
                );
                
            } catch (error) {
                updateStatus(`Blink test error: ${error.message}`, 'error');
                addResult('Real Blink Detection', false, 0);
            }
            
            testBlinkBtn.disabled = false;
        }
        
        async function testRealSmile() {
            updateStatus('Testing REAL smile detection... ACTUALLY SMILE!', 'warning');
            testSmileBtn.disabled = true;
            
            try {
                // Take before and after images
                const beforeCanvas = document.createElement('canvas');
                const beforeCtx = beforeCanvas.getContext('2d');
                beforeCanvas.width = video.videoWidth;
                beforeCanvas.height = video.videoHeight;
                beforeCtx.drawImage(video, 0, 0);
                const beforeData = beforeCtx.getImageData(0, 0, beforeCanvas.width, beforeCanvas.height);
                
                await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
                
                const afterCanvas = document.createElement('canvas');
                const afterCtx = afterCanvas.getContext('2d');
                afterCanvas.width = video.videoWidth;
                afterCanvas.height = video.videoHeight;
                afterCtx.drawImage(video, 0, 0);
                const afterData = afterCtx.getImageData(0, 0, afterCanvas.width, afterCanvas.height);
                
                // Compare mouth region for changes
                const mouthChange = compareMouthRegion(beforeData, afterData);
                const smileDetected = mouthChange > 0.01;
                const confidence = Math.min(mouthChange * 20, 1);
                
                addResult('Real Smile Detection', smileDetected, confidence);
                updateStatus(smileDetected ? 
                    '✅ REAL smile detected! You actually smiled!' : 
                    '❌ No real smile detected. You need to actually smile!', 
                    smileDetected ? 'success' : 'error'
                );
                
            } catch (error) {
                updateStatus(`Smile test error: ${error.message}`, 'error');
                addResult('Real Smile Detection', false, 0);
            }
            
            testSmileBtn.disabled = false;
        }
        
        async function testRealHeadMovement() {
            updateStatus('Testing REAL head movement... ACTUALLY MOVE YOUR HEAD!', 'warning');
            testHeadMoveBtn.disabled = true;
            
            try {
                const positions = [];
                for (let i = 0; i < 15; i++) {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                    ctx.drawImage(video, 0, 0);
                    
                    // Find face center by looking for skin-colored regions
                    const faceCenter = findFaceCenter(canvas);
                    positions.push(faceCenter);
                    
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
                
                // Calculate total movement
                let totalMovement = 0;
                for (let i = 1; i < positions.length; i++) {
                    const dx = positions[i].x - positions[i-1].x;
                    const dy = positions[i].y - positions[i-1].y;
                    totalMovement += Math.sqrt(dx*dx + dy*dy);
                }
                
                const avgMovement = totalMovement / (positions.length - 1);
                const movementDetected = avgMovement > 5;
                const confidence = Math.min(avgMovement / 20, 1);
                
                addResult('Real Head Movement', movementDetected, confidence);
                updateStatus(movementDetected ? 
                    '✅ REAL head movement detected! You actually moved!' : 
                    '❌ No real movement detected. You need to actually move your head!', 
                    movementDetected ? 'success' : 'error'
                );
                
            } catch (error) {
                updateStatus(`Head movement test error: ${error.message}`, 'error');
                addResult('Real Head Movement', false, 0);
            }
            
            testHeadMoveBtn.disabled = false;
        }
        
        function calculateBrightness(imageData) {
            let total = 0;
            for (let i = 0; i < imageData.data.length; i += 4) {
                const r = imageData.data[i];
                const g = imageData.data[i + 1];
                const b = imageData.data[i + 2];
                total += (r + g + b) / 3;
            }
            return total / (imageData.data.length / 4);
        }
        
        function calculateVariation(values) {
            const avg = values.reduce((a, b) => a + b, 0) / values.length;
            const variance = values.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / values.length;
            return Math.sqrt(variance) / avg;
        }
        
        function compareMouthRegion(before, after) {
            // Compare bottom half of image (mouth region)
            const startY = Math.floor(before.height * 0.6);
            let diff = 0;
            let pixels = 0;
            
            for (let y = startY; y < before.height; y++) {
                for (let x = 0; x < before.width; x++) {
                    const i = (y * before.width + x) * 4;
                    const beforeBrightness = (before.data[i] + before.data[i+1] + before.data[i+2]) / 3;
                    const afterBrightness = (after.data[i] + after.data[i+1] + after.data[i+2]) / 3;
                    diff += Math.abs(beforeBrightness - afterBrightness);
                    pixels++;
                }
            }
            
            return diff / pixels / 255;
        }
        
        function findFaceCenter(canvas) {
            const ctx = canvas.getContext('2d');
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            
            let centerX = 0, centerY = 0, skinPixels = 0;
            
            for (let y = 0; y < imageData.height; y++) {
                for (let x = 0; x < imageData.width; x++) {
                    const i = (y * imageData.width + x) * 4;
                    const r = imageData.data[i];
                    const g = imageData.data[i + 1];
                    const b = imageData.data[i + 2];
                    
                    // Simple skin color detection
                    if (r > 95 && g > 40 && b > 20 && 
                        Math.max(r, g, b) - Math.min(r, g, b) > 15 &&
                        Math.abs(r - g) > 15 && r > g && r > b) {
                        centerX += x;
                        centerY += y;
                        skinPixels++;
                    }
                }
            }
            
            return skinPixels > 0 ? 
                { x: centerX / skinPixels, y: centerY / skinPixels } : 
                { x: canvas.width / 2, y: canvas.height / 2 };
        }
        
        // Event listeners
        startCameraBtn.addEventListener('click', startCamera);
        testBlinkBtn.addEventListener('click', testRealBlink);
        testSmileBtn.addEventListener('click', testRealSmile);
        testHeadMoveBtn.addEventListener('click', testRealHeadMovement);
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
            }
        });
    </script>
</body>
</html>
