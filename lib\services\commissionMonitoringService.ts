/**
 * COMMISSION MONITORING SERVICE
 * 
 * Real-time monitoring system to detect commission failures and inconsistencies.
 * This service runs continuous checks to ensure no commissions are ever missed.
 * 
 * MONITORING FEATURES:
 * 1. Real-time commission validation
 * 2. Automatic detection of missing commissions
 * 3. Commission balance consistency checks
 * 4. Alert system for critical failures
 * 5. Automated recovery mechanisms
 * 6. Comprehensive audit logging
 */

import { supabase, getServiceRoleClient } from '../supabase'

export interface CommissionAlert {
  type: 'missing_commission' | 'calculation_error' | 'balance_mismatch' | 'system_error'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  userId?: number
  transactionId?: string
  data?: any
  timestamp: string
}

export interface MonitoringReport {
  timestamp: string
  totalChecks: number
  issuesFound: number
  alerts: CommissionAlert[]
  systemHealth: 'healthy' | 'warning' | 'critical'
  recommendations: string[]
}

export class CommissionMonitoringService {
  
  /**
   * Run comprehensive commission system health check
   */
  static async runHealthCheck(): Promise<MonitoringReport> {
    const startTime = Date.now()
    console.log('🔍 COMMISSION MONITORING: Starting health check...')
    
    const alerts: CommissionAlert[] = []
    const recommendations: string[] = []
    let totalChecks = 0
    
    try {
      // Check 1: Recent payments without commissions
      console.log('🔍 Checking recent payments for missing commissions...')
      const recentPaymentAlerts = await this.checkRecentPaymentsForMissingCommissions()
      alerts.push(...recentPaymentAlerts)
      totalChecks++
      
      // Check 2: Commission calculation accuracy
      console.log('🔍 Validating commission calculations...')
      const calculationAlerts = await this.validateCommissionCalculations()
      alerts.push(...calculationAlerts)
      totalChecks++
      
      // Check 3: Commission balance consistency
      console.log('🔍 Checking commission balance consistency...')
      const balanceAlerts = await this.checkCommissionBalanceConsistency()
      alerts.push(...balanceAlerts)
      totalChecks++
      
      // Check 4: Orphaned commission transactions
      console.log('🔍 Checking for orphaned commission transactions...')
      const orphanAlerts = await this.checkOrphanedCommissions()
      alerts.push(...orphanAlerts)
      totalChecks++
      
      // Check 5: System performance metrics
      console.log('🔍 Analyzing system performance metrics...')
      const performanceAlerts = await this.checkSystemPerformance()
      alerts.push(...performanceAlerts)
      totalChecks++
      
      // Generate recommendations
      if (alerts.length > 0) {
        recommendations.push('Run bulletproof commission system to fix identified issues')
        
        const criticalAlerts = alerts.filter(a => a.severity === 'critical')
        if (criticalAlerts.length > 0) {
          recommendations.push('IMMEDIATE ACTION REQUIRED: Critical commission failures detected')
        }
        
        const missingCommissions = alerts.filter(a => a.type === 'missing_commission')
        if (missingCommissions.length > 0) {
          recommendations.push(`${missingCommissions.length} missing commissions need to be processed`)
        }
      } else {
        recommendations.push('Commission system is operating normally')
      }
      
      const processingTime = Date.now() - startTime
      const systemHealth = this.determineSystemHealth(alerts)
      
      console.log(`🔍 COMMISSION MONITORING: Health check completed in ${processingTime}ms`)
      console.log(`🔍 System Health: ${systemHealth.toUpperCase()}`)
      console.log(`🔍 Issues Found: ${alerts.length}`)
      
      return {
        timestamp: new Date().toISOString(),
        totalChecks,
        issuesFound: alerts.length,
        alerts,
        systemHealth,
        recommendations
      }
      
    } catch (error) {
      console.error('🔍 COMMISSION MONITORING: Health check failed:', error)
      
      alerts.push({
        type: 'system_error',
        severity: 'critical',
        message: `Health check system failure: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date().toISOString()
      })
      
      return {
        timestamp: new Date().toISOString(),
        totalChecks,
        issuesFound: alerts.length,
        alerts,
        systemHealth: 'critical',
        recommendations: ['System monitoring failure - manual investigation required']
      }
    }
  }
  
  /**
   * Check recent payments for missing commissions
   */
  private static async checkRecentPaymentsForMissingCommissions(): Promise<CommissionAlert[]> {
    const alerts: CommissionAlert[] = []
    
    try {
      // Get payments from last 24 hours
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
      
      const { data: recentPayments, error: paymentError } = await supabase
        .from('crypto_payment_transactions')
        .select(`
          id,
          user_id,
          amount,
          status,
          approved_at,
          users!user_id(username, full_name)
        `)
        .eq('status', 'approved')
        .gte('approved_at', twentyFourHoursAgo)
        .order('approved_at', { ascending: false })
      
      if (paymentError) {
        throw new Error(`Failed to get recent payments: ${paymentError.message}`)
      }
      
      for (const payment of recentPayments || []) {
        // Check if user has a referrer
        const { data: referral, error: referralError } = await supabase
          .from('referrals')
          .select('referrer_id')
          .eq('referred_id', payment.user_id)
          .eq('status', 'active')
          .single()
        
        if (referralError || !referral) {
          continue // No referrer, no commission expected
        }
        
        // Check if commission exists
        const { data: commission, error: commissionError } = await supabase
          .from('commission_transactions')
          .select('id')
          .eq('referred_id', payment.user_id)
          .eq('share_purchase_amount', payment.amount)
          .single()
        
        if (commissionError || !commission) {
          alerts.push({
            type: 'missing_commission',
            severity: 'high',
            message: `Missing commission for recent payment: $${payment.amount} by ${payment.users?.full_name || payment.users?.username}`,
            userId: payment.user_id,
            transactionId: payment.id,
            data: {
              amount: payment.amount,
              approvedAt: payment.approved_at,
              referrerId: referral.referrer_id
            },
            timestamp: new Date().toISOString()
          })
        }
      }
      
    } catch (error) {
      alerts.push({
        type: 'system_error',
        severity: 'medium',
        message: `Failed to check recent payments: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date().toISOString()
      })
    }
    
    return alerts
  }
  
  /**
   * Validate commission calculations for accuracy
   */
  private static async validateCommissionCalculations(): Promise<CommissionAlert[]> {
    const alerts: CommissionAlert[] = []
    
    try {
      // Get recent commission transactions
      const { data: commissions, error: commissionError } = await supabase
        .from('commission_transactions')
        .select(`
          *,
          aureus_share_purchases!share_purchase_id(shares_purchased)
        `)
        .order('created_at', { ascending: false })
        .limit(50)
      
      if (commissionError) {
        throw new Error(`Failed to get commission transactions: ${commissionError.message}`)
      }
      
      for (const commission of commissions || []) {
        const purchaseAmount = parseFloat(commission.share_purchase_amount || '0')
        const sharesPurchased = commission.aureus_share_purchases?.shares_purchased || 0
        
        // Validate USDT commission (15%)
        const expectedUsdtCommission = purchaseAmount * 0.15
        const actualUsdtCommission = parseFloat(commission.usdt_commission || '0')
        
        if (Math.abs(actualUsdtCommission - expectedUsdtCommission) > 0.01) {
          alerts.push({
            type: 'calculation_error',
            severity: 'high',
            message: `USDT commission calculation error: expected $${expectedUsdtCommission.toFixed(2)}, got $${actualUsdtCommission.toFixed(2)}`,
            transactionId: commission.id,
            data: {
              expected: expectedUsdtCommission,
              actual: actualUsdtCommission,
              purchaseAmount
            },
            timestamp: new Date().toISOString()
          })
        }
        
        // Validate share commission (15%)
        const expectedShareCommission = sharesPurchased * 0.15
        const actualShareCommission = parseFloat(commission.share_commission || '0')
        
        if (Math.abs(actualShareCommission - expectedShareCommission) > 0.001) {
          alerts.push({
            type: 'calculation_error',
            severity: 'high',
            message: `Share commission calculation error: expected ${expectedShareCommission.toFixed(4)}, got ${actualShareCommission.toFixed(4)}`,
            transactionId: commission.id,
            data: {
              expected: expectedShareCommission,
              actual: actualShareCommission,
              sharesPurchased
            },
            timestamp: new Date().toISOString()
          })
        }
      }
      
    } catch (error) {
      alerts.push({
        type: 'system_error',
        severity: 'medium',
        message: `Failed to validate commission calculations: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date().toISOString()
      })
    }
    
    return alerts
  }
  
  /**
   * Check commission balance consistency
   */
  private static async checkCommissionBalanceConsistency(): Promise<CommissionAlert[]> {
    const alerts: CommissionAlert[] = []
    
    try {
      // Get users with commission balances
      const { data: balances, error: balanceError } = await supabase
        .from('commission_balances')
        .select('*')
        .gt('total_earned_usdt', 0)
        .limit(20)
      
      if (balanceError) {
        throw new Error(`Failed to get commission balances: ${balanceError.message}`)
      }
      
      for (const balance of balances || []) {
        // Calculate expected totals from commission transactions
        const { data: transactions, error: transactionError } = await supabase
          .from('commission_transactions')
          .select('usdt_commission, share_commission')
          .eq('referrer_id', balance.user_id)
          .eq('status', 'approved')
        
        if (transactionError) {
          continue // Skip this user
        }
        
        const expectedUsdtTotal = transactions?.reduce((sum, t) => sum + parseFloat(t.usdt_commission || '0'), 0) || 0
        const expectedShareTotal = transactions?.reduce((sum, t) => sum + parseFloat(t.share_commission || '0'), 0) || 0
        
        const actualUsdtTotal = parseFloat(balance.total_earned_usdt || '0')
        const actualShareTotal = parseFloat(balance.total_earned_shares || '0')
        
        // Check USDT balance consistency
        if (Math.abs(actualUsdtTotal - expectedUsdtTotal) > 0.01) {
          alerts.push({
            type: 'balance_mismatch',
            severity: 'medium',
            message: `USDT balance mismatch for user ${balance.user_id}: expected $${expectedUsdtTotal.toFixed(2)}, got $${actualUsdtTotal.toFixed(2)}`,
            userId: balance.user_id,
            data: {
              expected: expectedUsdtTotal,
              actual: actualUsdtTotal,
              difference: actualUsdtTotal - expectedUsdtTotal
            },
            timestamp: new Date().toISOString()
          })
        }
        
        // Check share balance consistency
        if (Math.abs(actualShareTotal - expectedShareTotal) > 0.001) {
          alerts.push({
            type: 'balance_mismatch',
            severity: 'medium',
            message: `Share balance mismatch for user ${balance.user_id}: expected ${expectedShareTotal.toFixed(4)}, got ${actualShareTotal.toFixed(4)}`,
            userId: balance.user_id,
            data: {
              expected: expectedShareTotal,
              actual: actualShareTotal,
              difference: actualShareTotal - expectedShareTotal
            },
            timestamp: new Date().toISOString()
          })
        }
      }
      
    } catch (error) {
      alerts.push({
        type: 'system_error',
        severity: 'medium',
        message: `Failed to check balance consistency: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date().toISOString()
      })
    }
    
    return alerts
  }
  
  /**
   * Check for orphaned commission transactions
   */
  private static async checkOrphanedCommissions(): Promise<CommissionAlert[]> {
    const alerts: CommissionAlert[] = []
    
    try {
      // Find commission transactions without corresponding share purchases
      const { data: orphanedCommissions, error: orphanError } = await supabase
        .from('commission_transactions')
        .select(`
          id,
          referred_id,
          share_purchase_amount,
          share_purchase_id
        `)
        .not('share_purchase_id', 'is', null)
        .order('created_at', { ascending: false })
        .limit(100)
      
      if (orphanError) {
        throw new Error(`Failed to get commission transactions: ${orphanError.message}`)
      }
      
      for (const commission of orphanedCommissions || []) {
        if (commission.share_purchase_id) {
          // Check if the referenced share purchase exists
          const { data: purchase, error: purchaseError } = await supabase
            .from('aureus_share_purchases')
            .select('id')
            .eq('id', commission.share_purchase_id)
            .single()
          
          if (purchaseError || !purchase) {
            alerts.push({
              type: 'system_error',
              severity: 'low',
              message: `Orphaned commission transaction references non-existent share purchase`,
              transactionId: commission.id,
              data: {
                sharePurchaseId: commission.share_purchase_id,
                referredId: commission.referred_id
              },
              timestamp: new Date().toISOString()
            })
          }
        }
      }
      
    } catch (error) {
      alerts.push({
        type: 'system_error',
        severity: 'low',
        message: `Failed to check orphaned commissions: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date().toISOString()
      })
    }
    
    return alerts
  }
  
  /**
   * Check system performance metrics
   */
  private static async checkSystemPerformance(): Promise<CommissionAlert[]> {
    const alerts: CommissionAlert[] = []
    
    try {
      // Check commission processing speed (recent transactions)
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString()
      
      const { data: recentCommissions, error: commissionError } = await supabase
        .from('commission_transactions')
        .select('created_at, payment_date')
        .gte('created_at', oneHourAgo)
      
      if (commissionError) {
        throw new Error(`Failed to get recent commissions: ${commissionError.message}`)
      }
      
      if ((recentCommissions?.length || 0) === 0) {
        alerts.push({
          type: 'system_error',
          severity: 'low',
          message: 'No commission transactions processed in the last hour',
          timestamp: new Date().toISOString()
        })
      }
      
      // Check for commission processing delays
      for (const commission of recentCommissions || []) {
        const createdAt = new Date(commission.created_at)
        const paymentDate = new Date(commission.payment_date)
        const delayMinutes = (createdAt.getTime() - paymentDate.getTime()) / (1000 * 60)
        
        if (delayMinutes > 30) { // More than 30 minutes delay
          alerts.push({
            type: 'system_error',
            severity: 'medium',
            message: `Commission processing delay: ${Math.round(delayMinutes)} minutes`,
            data: {
              delayMinutes: Math.round(delayMinutes),
              createdAt: commission.created_at,
              paymentDate: commission.payment_date
            },
            timestamp: new Date().toISOString()
          })
        }
      }
      
    } catch (error) {
      alerts.push({
        type: 'system_error',
        severity: 'low',
        message: `Failed to check system performance: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date().toISOString()
      })
    }
    
    return alerts
  }
  
  /**
   * Determine overall system health based on alerts
   */
  private static determineSystemHealth(alerts: CommissionAlert[]): 'healthy' | 'warning' | 'critical' {
    const criticalAlerts = alerts.filter(a => a.severity === 'critical')
    const highAlerts = alerts.filter(a => a.severity === 'high')
    
    if (criticalAlerts.length > 0) {
      return 'critical'
    }
    
    if (highAlerts.length > 2) {
      return 'critical'
    }
    
    if (alerts.length > 5) {
      return 'warning'
    }
    
    if (alerts.length > 0) {
      return 'warning'
    }
    
    return 'healthy'
  }
  
  /**
   * Generate monitoring report for admin dashboard
   */
  static async generateMonitoringReport(): Promise<string> {
    const report = await this.runHealthCheck()
    
    let output = `🔍 COMMISSION SYSTEM MONITORING REPORT\n`
    output += `Generated: ${new Date(report.timestamp).toLocaleString()}\n`
    output += `System Health: ${report.systemHealth.toUpperCase()}\n`
    output += `Total Checks: ${report.totalChecks}\n`
    output += `Issues Found: ${report.issuesFound}\n\n`
    
    if (report.alerts.length > 0) {
      output += `⚠️  ALERTS:\n`
      report.alerts.forEach((alert, index) => {
        output += `${index + 1}. [${alert.severity.toUpperCase()}] ${alert.message}\n`
        if (alert.userId) output += `   User ID: ${alert.userId}\n`
        if (alert.transactionId) output += `   Transaction ID: ${alert.transactionId}\n`
      })
      output += `\n`
    }
    
    output += `📋 RECOMMENDATIONS:\n`
    report.recommendations.forEach((rec, index) => {
      output += `${index + 1}. ${rec}\n`
    })
    
    return output
  }
}
