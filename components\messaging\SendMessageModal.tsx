import React, { useState } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';
import { EmailProcessingService } from '../../lib/services/emailProcessingService';

interface SendMessageModalProps {
  isOpen: boolean;
  onClose: () => void;
  recipientId: number;
  recipientName: string;
  senderId: number;
  defaultSubject?: string;
}

export const SendMessageModal: React.FC<SendMessageModalProps> = ({
  isOpen,
  onClose,
  recipientId,
  recipientName,
  senderId,
  defaultSubject = ''
}) => {
  const [subject, setSubject] = useState(defaultSubject);
  const [messageContent, setMessageContent] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!subject.trim()) {
      setError('Subject is required');
      return;
    }
    
    if (!messageContent.trim()) {
      setError('Message content is required');
      return;
    }
    
    if (subject.length > 255) {
      setError('Subject must be 255 characters or less');
      return;
    }
    
    setIsLoading(true);
    setError('');
    
    try {
      const serviceClient = getServiceRoleClient();

      const { data: insertedMessage, error: insertError } = await serviceClient
        .from('internal_messages')
        .insert({
          sender_user_id: senderId,
          recipient_user_id: recipientId,
          subject: subject.trim(),
          message_content: messageContent.trim(),
          is_read: false
        })
        .select()
        .single();

      if (insertError) throw insertError;

      // Trigger email notification for new message
      if (insertedMessage?.id) {
        try {
          const { emailNotificationTriggers } = await import('../../lib/services/emailNotificationTriggers');
          await emailNotificationTriggers.triggerMessageNotification(insertedMessage.id);
        } catch (emailError) {
          console.error('Failed to trigger message email notification:', emailError);
          // Don't fail the message sending if email fails
        }
      }

      setSuccess(true);
      setSubject(defaultSubject);
      setMessageContent('');
      
      // Close modal after 2 seconds
      setTimeout(() => {
        setSuccess(false);
        onClose();
      }, 2000);
      
    } catch (error) {
      console.error('Error sending message:', error);
      setError('Failed to send message. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setSubject(defaultSubject);
      setMessageContent('');
      setError('');
      setSuccess(false);
      onClose();
    }
  };

  // Update subject when defaultSubject changes
  React.useEffect(() => {
    setSubject(defaultSubject);
  }, [defaultSubject]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4 border border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-white text-lg font-semibold">
            💬 Send Message to {recipientName}
          </h3>
          <button
            onClick={handleClose}
            disabled={isLoading}
            className="text-gray-400 hover:text-white disabled:opacity-50"
          >
            ✕
          </button>
        </div>

        {success ? (
          <div className="text-center py-8">
            <div className="text-green-400 text-4xl mb-4">✅</div>
            <div className="text-white text-lg mb-2">Message Sent!</div>
            <div className="text-gray-400 text-sm">
              Your message has been delivered to {recipientName}
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
              <div className="bg-red-600/20 border border-red-600/30 rounded-lg p-3">
                <div className="text-red-400 text-sm">{error}</div>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Subject *
              </label>
              <input
                type="text"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                placeholder="Enter message subject..."
                maxLength={255}
                disabled={isLoading}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"
              />
              <div className="text-xs text-gray-400 mt-1">
                {subject.length}/255 characters
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Message *
              </label>
              <textarea
                value={messageContent}
                onChange={(e) => setMessageContent(e.target.value)}
                placeholder="Type your message here..."
                rows={6}
                disabled={isLoading}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 resize-none"
              />
              <div className="text-xs text-gray-400 mt-1">
                {messageContent.length} characters
              </div>
            </div>

            <div className="flex space-x-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                disabled={isLoading}
                className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading || !subject.trim() || !messageContent.trim()}
                className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50 flex items-center justify-center"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Sending...
                  </>
                ) : (
                  '📤 Send Message'
                )}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};
