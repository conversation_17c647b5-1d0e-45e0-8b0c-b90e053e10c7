/**
 * AUDIT PDF GENERATION SERVICE
 * 
 * Professional PDF generation service for financial audit reports
 * with company branding, proper formatting, and comprehensive layouts.
 */

import jsPDF from 'jspdf'
import 'jspdf-autotable'
import { AuditResult } from './financialAuditService'

// Extend jsPDF type to include autoTable
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF
  }
}

export interface PdfGenerationOptions {
  includeCompanyLogo: boolean
  includeDetailedTransactions: boolean
  includeRecommendations: boolean
  generatedBy: string
  generatedByEmail: string
}

export class AuditPdfService {
  private doc: jsPDF
  private pageHeight: number
  private pageWidth: number
  private margin: number = 20
  private currentY: number = 20

  constructor() {
    this.doc = new jsPDF()
    this.pageHeight = this.doc.internal.pageSize.height
    this.pageWidth = this.doc.internal.pageSize.width
  }

  /**
   * Generate professional PDF audit report
   */
  async generateAuditPdf(
    auditResult: AuditResult,
    options: PdfGenerationOptions
  ): Promise<Blob> {
    console.log('🔄 Generating PDF audit report...')

    try {
      // Reset document
      this.doc = new jsPDF()
      this.currentY = 20

      // Add company header
      this.addCompanyHeader()

      // Add audit title and metadata
      this.addAuditTitle(auditResult, options)

      // Add executive summary
      this.addExecutiveSummary(auditResult)

      // Add user profile section
      this.addUserProfile(auditResult.user)

      // Add financial summary
      this.addFinancialSummary(auditResult)

      // Add detailed sections if requested
      if (options.includeDetailedTransactions) {
        this.addSharePurchasesSection(auditResult)
        this.addCommissionTransactionsSection(auditResult)
        this.addCommissionConversionsSection(auditResult)
      }

      // Add audit conclusions
      this.addAuditConclusions(auditResult)

      // Add recommendations if requested
      if (options.includeRecommendations) {
        this.addRecommendations(auditResult)
      }

      // Add footer with generation info
      this.addFooter(options)

      console.log('✅ PDF audit report generated successfully')
      return this.doc.output('blob')

    } catch (error) {
      console.error('❌ Error generating PDF audit report:', error)
      throw error
    }
  }

  /**
   * Add company header with logo and contact details
   */
  private addCompanyHeader() {
    // Company name
    this.doc.setFontSize(24)
    this.doc.setFont('helvetica', 'bold')
    this.doc.setTextColor(255, 215, 0) // Gold color
    this.doc.text('AUREUS ALLIANCE HOLDINGS (PTY) LTD', this.pageWidth / 2, this.currentY, { align: 'center' })
    
    this.currentY += 10
    
    // Subtitle
    this.doc.setFontSize(12)
    this.doc.setFont('helvetica', 'normal')
    this.doc.setTextColor(100, 100, 100)
    this.doc.text('Professional Financial Audit Report', this.pageWidth / 2, this.currentY, { align: 'center' })
    
    this.currentY += 15

    // Company contact details
    this.doc.setFontSize(10)
    this.doc.setTextColor(80, 80, 80)
    const contactInfo = [
      'Email: <EMAIL>',
      'Website: www.aureus.africa',
      'Phone: +27 74 449 3251'
    ]
    
    contactInfo.forEach(info => {
      this.doc.text(info, this.pageWidth / 2, this.currentY, { align: 'center' })
      this.currentY += 5
    })

    this.currentY += 10
    
    // Add horizontal line
    this.doc.setDrawColor(255, 215, 0)
    this.doc.setLineWidth(1)
    this.doc.line(this.margin, this.currentY, this.pageWidth - this.margin, this.currentY)
    
    this.currentY += 15
  }

  /**
   * Add audit title and metadata
   */
  private addAuditTitle(auditResult: AuditResult, options: PdfGenerationOptions) {
    // Main title
    this.doc.setFontSize(20)
    this.doc.setFont('helvetica', 'bold')
    this.doc.setTextColor(0, 0, 0)
    this.doc.text('COMPREHENSIVE FINANCIAL AUDIT REPORT', this.pageWidth / 2, this.currentY, { align: 'center' })
    
    this.currentY += 15

    // User information
    this.doc.setFontSize(14)
    this.doc.setFont('helvetica', 'normal')
    this.doc.text(`User: ${auditResult.user.full_name || auditResult.user.username}`, this.pageWidth / 2, this.currentY, { align: 'center' })
    
    this.currentY += 8
    this.doc.text(`User ID: ${auditResult.user.id}`, this.pageWidth / 2, this.currentY, { align: 'center' })
    
    this.currentY += 15

    // Audit metadata
    this.doc.setFontSize(10)
    this.doc.setTextColor(100, 100, 100)
    const auditDate = new Date().toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    })
    
    const metadata = [
      `Audit Date: ${auditDate}`,
      `Generated By: ${options.generatedBy}`,
      `Admin Email: ${options.generatedByEmail}`,
      `Report Status: ${auditResult.auditStatus}`
    ]

    metadata.forEach(info => {
      this.doc.text(info, this.pageWidth / 2, this.currentY, { align: 'center' })
      this.currentY += 5
    })

    this.currentY += 15
  }

  /**
   * Add executive summary section
   */
  private addExecutiveSummary(auditResult: AuditResult) {
    this.addSectionHeader('EXECUTIVE SUMMARY')

    // Status indicator
    const statusColor = auditResult.auditStatus === 'PASSED' ? [0, 128, 0] : 
                       auditResult.auditStatus === 'WARNING' ? [255, 165, 0] : [255, 0, 0]
    
    this.doc.setTextColor(...statusColor)
    this.doc.setFontSize(14)
    this.doc.setFont('helvetica', 'bold')
    this.doc.text(`AUDIT STATUS: ${auditResult.auditStatus}`, this.margin, this.currentY)
    
    this.currentY += 15

    // Key metrics
    this.doc.setTextColor(0, 0, 0)
    this.doc.setFontSize(11)
    this.doc.setFont('helvetica', 'normal')

    const summaryData = [
      ['Total Shares Owned', `${auditResult.calculations.totalSharesOwned.toLocaleString()} shares`],
      ['Direct Purchases', `${auditResult.calculations.totalDirectShares.toLocaleString()} shares`],
      ['Commission Shares', `${auditResult.calculations.totalCommissionShares.toLocaleString()} shares`],
      ['Converted Shares', `${auditResult.calculations.totalConvertedShares.toLocaleString()} shares`],
      ['Portfolio Value', `$${auditResult.calculations.portfolioValue.toLocaleString()}`],
      ['USDT Commission Earned', `$${auditResult.calculations.totalUsdtCommissionEarned.toLocaleString()}`],
      ['USDT Converted to Shares', `$${auditResult.calculations.totalUsdtConverted.toLocaleString()}`],
      ['Current USDT Balance', `$${auditResult.calculations.currentUsdtBalance.toLocaleString()}`]
    ]

    this.doc.autoTable({
      startY: this.currentY,
      head: [['Metric', 'Value']],
      body: summaryData,
      theme: 'grid',
      headStyles: { fillColor: [255, 215, 0], textColor: [0, 0, 0] },
      margin: { left: this.margin, right: this.margin }
    })

    this.currentY = (this.doc as any).lastAutoTable.finalY + 15
  }

  /**
   * Add user profile section
   */
  private addUserProfile(user: any) {
    this.addSectionHeader('USER PROFILE')

    const profileData = [
      ['User ID', user.id.toString()],
      ['Full Name', user.full_name || 'Not provided'],
      ['Username', user.username],
      ['Email', user.email],
      ['Registration Date', new Date(user.created_at).toLocaleDateString()],
      ['Total Referrals', user.total_referrals.toString()]
    ]

    this.doc.autoTable({
      startY: this.currentY,
      body: profileData,
      theme: 'plain',
      columnStyles: {
        0: { fontStyle: 'bold', cellWidth: 60 },
        1: { cellWidth: 100 }
      },
      margin: { left: this.margin, right: this.margin }
    })

    this.currentY = (this.doc as any).lastAutoTable.finalY + 15
  }

  /**
   * Add financial summary section
   */
  private addFinancialSummary(auditResult: AuditResult) {
    this.addSectionHeader('FINANCIAL SUMMARY')

    // Share breakdown
    this.doc.setFontSize(12)
    this.doc.setFont('helvetica', 'bold')
    this.doc.text('Share Ownership Breakdown:', this.margin, this.currentY)
    this.currentY += 10

    const shareData = [
      ['Direct Share Purchases', auditResult.calculations.totalDirectShares.toString()],
      ['Commission Shares Earned', auditResult.calculations.totalCommissionShares.toString()],
      ['Converted Shares (from USDT)', auditResult.calculations.totalConvertedShares.toString()],
      ['Total Shares Owned', auditResult.calculations.totalSharesOwned.toString()]
    ]

    this.doc.autoTable({
      startY: this.currentY,
      body: shareData,
      theme: 'striped',
      columnStyles: {
        0: { fontStyle: 'bold' }
      },
      margin: { left: this.margin, right: this.margin }
    })

    this.currentY = (this.doc as any).lastAutoTable.finalY + 15

    // Commission summary
    this.doc.setFontSize(12)
    this.doc.setFont('helvetica', 'bold')
    this.doc.text('Commission Summary:', this.margin, this.currentY)
    this.currentY += 10

    const commissionData = [
      ['Total USDT Commission Earned', `$${auditResult.calculations.totalUsdtCommissionEarned.toLocaleString()}`],
      ['Current USDT Balance', `$${auditResult.calculations.currentUsdtBalance.toLocaleString()}`],
      ['Total Share Commission Earned', `${auditResult.calculations.totalShareCommissionEarned.toLocaleString()} shares`],
      ['Current Share Balance', `${auditResult.calculations.currentShareBalance.toLocaleString()} shares`]
    ]

    this.doc.autoTable({
      startY: this.currentY,
      body: commissionData,
      theme: 'striped',
      columnStyles: {
        0: { fontStyle: 'bold' }
      },
      margin: { left: this.margin, right: this.margin }
    })

    this.currentY = (this.doc as any).lastAutoTable.finalY + 15
  }

  /**
   * Add share purchases section
   */
  private addSharePurchasesSection(auditResult: AuditResult) {
    if (auditResult.sharePurchases.length === 0) return

    this.checkPageBreak(50)
    this.addSectionHeader('SHARE PURCHASES HISTORY')

    const purchaseData = auditResult.sharePurchases.map(purchase => [
      purchase.id.substring(0, 8) + '...',
      new Date(purchase.created_at).toLocaleDateString(),
      purchase.shares_purchased.toString(),
      `$${parseFloat(purchase.total_amount).toLocaleString()}`,
      purchase.status
    ])

    this.doc.autoTable({
      startY: this.currentY,
      head: [['Purchase ID', 'Date', 'Shares', 'Amount', 'Status']],
      body: purchaseData,
      theme: 'grid',
      headStyles: { fillColor: [255, 215, 0], textColor: [0, 0, 0] },
      margin: { left: this.margin, right: this.margin },
      styles: { fontSize: 9 }
    })

    this.currentY = (this.doc as any).lastAutoTable.finalY + 15
  }

  /**
   * Add commission transactions section
   */
  private addCommissionTransactionsSection(auditResult: AuditResult) {
    if (auditResult.commissionTransactions.length === 0) return

    this.checkPageBreak(50)
    this.addSectionHeader('COMMISSION TRANSACTIONS')

    const commissionData = auditResult.commissionTransactions.map(transaction => [
      new Date(transaction.created_at).toLocaleDateString(),
      `$${parseFloat(transaction.usdt_commission).toLocaleString()}`,
      `${parseFloat(transaction.share_commission).toLocaleString()}`,
      transaction.status
    ])

    this.doc.autoTable({
      startY: this.currentY,
      head: [['Date', 'USDT Commission', 'Share Commission', 'Status']],
      body: commissionData,
      theme: 'grid',
      headStyles: { fillColor: [255, 215, 0], textColor: [0, 0, 0] },
      margin: { left: this.margin, right: this.margin },
      styles: { fontSize: 9 }
    })

    this.currentY = (this.doc as any).lastAutoTable.finalY + 15
  }

  /**
   * Add commission conversions section
   */
  private addCommissionConversionsSection(auditResult: AuditResult) {
    if (auditResult.commissionConversions.length === 0) return

    this.checkPageBreak(50)
    this.addSectionHeader('COMMISSION CONVERSIONS (USDT TO SHARES)')

    const conversionData = auditResult.commissionConversions.map(conversion => [
      conversion.id.substring(0, 8) + '...',
      new Date(conversion.created_at).toLocaleDateString(),
      `$${parseFloat(conversion.usdt_amount).toLocaleString()}`,
      conversion.shares_requested.toString(),
      `$${parseFloat(conversion.share_price).toLocaleString()}`,
      conversion.status,
      conversion.approved_at ? new Date(conversion.approved_at).toLocaleDateString() : 'N/A'
    ])

    this.doc.autoTable({
      startY: this.currentY,
      head: [['Conversion ID', 'Date', 'USDT Amount', 'Shares', 'Price/Share', 'Status', 'Approved']],
      body: conversionData,
      theme: 'grid',
      headStyles: { fillColor: [255, 215, 0], textColor: [0, 0, 0] },
      margin: { left: this.margin, right: this.margin },
      styles: { fontSize: 9 }
    })

    this.currentY = (this.doc as any).lastAutoTable.finalY + 15
  }

  /**
   * Add audit conclusions section
   */
  private addAuditConclusions(auditResult: AuditResult) {
    this.checkPageBreak(40)
    this.addSectionHeader('AUDIT CONCLUSIONS')

    this.doc.setFontSize(11)
    this.doc.setFont('helvetica', 'normal')

    auditResult.auditFindings.forEach(finding => {
      this.doc.text(`• ${finding}`, this.margin + 5, this.currentY)
      this.currentY += 8
    })

    this.currentY += 10
  }

  /**
   * Add recommendations section
   */
  private addRecommendations(auditResult: AuditResult) {
    this.checkPageBreak(30)
    this.addSectionHeader('RECOMMENDATIONS')

    this.doc.setFontSize(11)
    this.doc.setFont('helvetica', 'normal')

    auditResult.recommendations.forEach(recommendation => {
      this.doc.text(`• ${recommendation}`, this.margin + 5, this.currentY)
      this.currentY += 8
    })

    this.currentY += 10
  }

  /**
   * Add section header
   */
  private addSectionHeader(title: string) {
    this.checkPageBreak(20)
    
    this.doc.setFontSize(14)
    this.doc.setFont('helvetica', 'bold')
    this.doc.setTextColor(255, 215, 0)
    this.doc.text(title, this.margin, this.currentY)
    
    this.currentY += 5
    
    // Add underline
    this.doc.setDrawColor(255, 215, 0)
    this.doc.setLineWidth(0.5)
    this.doc.line(this.margin, this.currentY, this.margin + this.doc.getTextWidth(title), this.currentY)
    
    this.currentY += 15
    this.doc.setTextColor(0, 0, 0)
  }

  /**
   * Add footer with generation information
   */
  private addFooter(options: PdfGenerationOptions) {
    const pageCount = this.doc.getNumberOfPages()
    
    for (let i = 1; i <= pageCount; i++) {
      this.doc.setPage(i)
      
      // Footer line
      this.doc.setDrawColor(200, 200, 200)
      this.doc.setLineWidth(0.5)
      this.doc.line(this.margin, this.pageHeight - 25, this.pageWidth - this.margin, this.pageHeight - 25)
      
      // Footer text
      this.doc.setFontSize(8)
      this.doc.setTextColor(100, 100, 100)
      this.doc.text(
        `Generated by ${options.generatedBy} on ${new Date().toLocaleDateString()}`,
        this.margin,
        this.pageHeight - 15
      )
      
      // Page number
      this.doc.text(
        `Page ${i} of ${pageCount}`,
        this.pageWidth - this.margin,
        this.pageHeight - 15,
        { align: 'right' }
      )
    }
  }

  /**
   * Check if we need a page break
   */
  private checkPageBreak(requiredSpace: number) {
    if (this.currentY + requiredSpace > this.pageHeight - 40) {
      this.doc.addPage()
      this.currentY = 20
    }
  }
}

export const auditPdfService = new AuditPdfService()
