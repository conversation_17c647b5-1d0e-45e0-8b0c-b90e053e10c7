/**
 * COMPREHENSIVE SQL INJECTION PROTECTION (JavaScript version)
 * 
 * This module provides advanced SQL injection protection specifically
 * designed for the Aureus Africa application using Supabase.
 */

import { createClient } from '@supabase/supabase-js';
import { detectMaliciousInput, sanitizeInput } from './inputValidation.js';

// Initialize Supabase clients
const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseAnonKey);
const serviceClient = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Log security events to database
 */
async function logSecurityEvent(event) {
  try {
    await serviceClient
      .from('security_audit_log')
      .insert([event]);
  } catch (error) {
    console.error('Failed to log security event:', error);
    // Don't throw - logging failure shouldn't break the application
  }
}

/**
 * Validate and sanitize user input before database operations
 */
function validateInput(input, context) {
  if (input === null || input === undefined) {
    return { isValid: true, sanitized: input, issues: [] };
  }

  const inputString = typeof input === 'string' ? input : JSON.stringify(input);
  const maliciousCheck = detectMaliciousInput(inputString);
  
  if (maliciousCheck.isMalicious) {
    // Log the security event
    logSecurityEvent({
      timestamp: new Date().toISOString(),
      event_type: 'SQL_INJECTION_ATTEMPT',
      input_data: inputString.substring(0, 500), // Limit log size
      patterns_detected: maliciousCheck.patterns,
      endpoint: context,
      blocked: true
    });

    return {
      isValid: false,
      sanitized: null,
      issues: [`Malicious patterns detected: ${maliciousCheck.patterns.join(', ')}`]
    };
  }

  return {
    isValid: true,
    sanitized: typeof input === 'string' ? sanitizeInput(input) : input,
    issues: []
  };
}

/**
 * Secure database operations class
 */
export class SecureDatabase {
  /**
   * Secure user lookup by email (parameterized query)
   */
  static async getUserByEmail(email) {
    const validation = validateInput(email, 'getUserByEmail');
    if (!validation.isValid) {
      return { data: null, error: { message: 'Invalid input detected', details: validation.issues } };
    }

    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('email', validation.sanitized) // Supabase automatically parameterizes
        .single();

      return { data, error };
    } catch (error) {
      console.error('Database query error:', error);
      return { data: null, error: { message: 'Database query failed' } };
    }
  }

  /**
   * Secure user lookup by username (parameterized query)
   */
  static async getUserByUsername(username) {
    const validation = validateInput(username, 'getUserByUsername');
    if (!validation.isValid) {
      return { data: null, error: { message: 'Invalid input detected', details: validation.issues } };
    }

    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('username', validation.sanitized)
        .single();

      return { data, error };
    } catch (error) {
      console.error('Database query error:', error);
      return { data: null, error: { message: 'Database query failed' } };
    }
  }

  /**
   * Secure payment creation (parameterized query with validation)
   */
  static async createPayment(paymentData) {
    // Validate all input fields
    const validations = {
      user_id: validateInput(paymentData.user_id, 'createPayment.user_id'),
      amount: validateInput(paymentData.amount, 'createPayment.amount'),
      network: validateInput(paymentData.network, 'createPayment.network'),
      currency: validateInput(paymentData.currency, 'createPayment.currency'),
      sender_wallet: validateInput(paymentData.sender_wallet, 'createPayment.sender_wallet'),
      receiver_wallet: validateInput(paymentData.receiver_wallet, 'createPayment.receiver_wallet'),
      transaction_hash: validateInput(paymentData.transaction_hash, 'createPayment.transaction_hash'),
      screenshot_url: validateInput(paymentData.screenshot_url, 'createPayment.screenshot_url'),
      transaction_notes: validateInput(paymentData.transaction_notes, 'createPayment.transaction_notes')
    };

    // Check for validation failures
    const invalidFields = Object.entries(validations)
      .filter(([_, validation]) => !validation.isValid)
      .map(([field, validation]) => `${field}: ${validation.issues.join(', ')}`);

    if (invalidFields.length > 0) {
      return { 
        data: null, 
        error: { 
          message: 'Invalid input detected', 
          details: invalidFields 
        } 
      };
    }

    // Additional business logic validation
    if (typeof paymentData.user_id !== 'number' || paymentData.user_id <= 0) {
      return { data: null, error: { message: 'Invalid user ID' } };
    }

    if (typeof paymentData.amount !== 'number' || paymentData.amount < 25 || paymentData.amount > 10000) {
      return { data: null, error: { message: 'Invalid amount (must be between $25 and $10,000)' } };
    }

    if (!['BSC', 'POLYGON', 'TRON', 'ETH'].includes(paymentData.network)) {
      return { data: null, error: { message: 'Invalid network' } };
    }

    try {
      const sanitizedData = {
        user_id: paymentData.user_id,
        amount: paymentData.amount,
        shares_to_purchase: paymentData.shares_to_purchase,
        network: validations.network.sanitized,
        currency: validations.currency.sanitized,
        sender_wallet: validations.sender_wallet.sanitized,
        receiver_wallet: validations.receiver_wallet.sanitized,
        transaction_hash: validations.transaction_hash.sanitized,
        screenshot_url: validations.screenshot_url.sanitized,
        transaction_notes: validations.transaction_notes.sanitized,
        status: 'pending',
        created_at: new Date().toISOString()
      };

      const { data, error } = await serviceClient
        .from('crypto_payment_transactions')
        .insert([sanitizedData])
        .select()
        .single();

      return { data, error };
    } catch (error) {
      console.error('Payment creation error:', error);
      return { data: null, error: { message: 'Failed to create payment transaction' } };
    }
  }

  /**
   * Secure contact form submission
   */
  static async createContactSubmission(contactData) {
    // Validate all input fields
    const validations = {
      name: validateInput(contactData.name, 'createContact.name'),
      surname: validateInput(contactData.surname, 'createContact.surname'),
      email: validateInput(contactData.email, 'createContact.email'),
      message: validateInput(contactData.message, 'createContact.message'),
      ip_address: validateInput(contactData.ip_address, 'createContact.ip_address'),
      user_agent: validateInput(contactData.user_agent, 'createContact.user_agent')
    };

    // Check for validation failures
    const invalidFields = Object.entries(validations)
      .filter(([_, validation]) => !validation.isValid)
      .map(([field, validation]) => `${field}: ${validation.issues.join(', ')}`);

    if (invalidFields.length > 0) {
      return { 
        data: null, 
        error: { 
          message: 'Invalid contact form data', 
          details: invalidFields 
        } 
      };
    }

    try {
      const sanitizedData = {
        name: validations.name.sanitized,
        surname: validations.surname.sanitized,
        email: validations.email.sanitized,
        message: validations.message.sanitized,
        ip_address: validations.ip_address.sanitized,
        user_agent: validations.user_agent.sanitized,
        timestamp: new Date().toISOString(),
        status: 'new'
      };

      const { data, error } = await serviceClient
        .from('contact_submissions')
        .insert([sanitizedData])
        .select()
        .single();

      return { data, error };
    } catch (error) {
      console.error('Contact submission error:', error);
      return { data: null, error: { message: 'Failed to submit contact form' } };
    }
  }
}

export default SecureDatabase;
