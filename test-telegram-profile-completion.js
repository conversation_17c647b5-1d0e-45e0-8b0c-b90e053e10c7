#!/usr/bin/env node

/**
 * Test Telegram Profile Completion Status
 * 
 * This script tests if Telegram users' profile completion status
 * is being correctly read from the database.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ Missing SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testTelegramProfileCompletion() {
  try {
    console.log('🧪 Testing Telegram Profile Completion Status\n');
    
    // Get all users with telegram_id
    const { data: telegramUsers, error: telegramError } = await supabase
      .from('users')
      .select('*')
      .not('telegram_id', 'is', null)
      .order('id');
    
    if (telegramError) {
      console.error('❌ Error fetching Telegram users:', telegramError);
      return;
    }
    
    if (!telegramUsers || telegramUsers.length === 0) {
      console.log('ℹ️ No Telegram users found in database');
      return;
    }
    
    console.log(`📱 Found ${telegramUsers.length} Telegram users:\n`);
    
    telegramUsers.forEach((user, index) => {
      console.log(`${index + 1}. User ID: ${user.id}`);
      console.log(`   Telegram ID: ${user.telegram_id}`);
      console.log(`   Email: ${user.email || 'NULL'}`);
      console.log(`   Full Name: ${user.full_name || 'NULL'}`);
      console.log(`   Phone: ${user.phone || 'NULL'}`);
      console.log(`   Country: ${user.country_of_residence || 'NULL'}`);
      console.log(`   Country Selection Completed: ${user.country_selection_completed || false}`);
      console.log(`   Password Hash: ${user.password_hash ? 'SET' : 'NULL'}`);
      
      // Check profile completion status
      const needsProfileCompletion = !user.email || 
                                   !user.password_hash || 
                                   !user.full_name || 
                                   !user.phone || 
                                   !user.country_of_residence ||
                                   !user.country_selection_completed;
      
      console.log(`   Profile Complete: ${!needsProfileCompletion ? '✅ YES' : '❌ NO'}`);
      
      if (needsProfileCompletion) {
        const missing = [];
        if (!user.email) missing.push('email');
        if (!user.password_hash) missing.push('password');
        if (!user.full_name) missing.push('full_name');
        if (!user.phone) missing.push('phone');
        if (!user.country_of_residence) missing.push('country');
        if (!user.country_selection_completed) missing.push('country_selection_completed');
        console.log(`   Missing: ${missing.join(', ')}`);
      }
      
      console.log('');
    });
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testTelegramProfileCompletion();
