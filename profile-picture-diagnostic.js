/**
 * PROFILE PICTURE DIAGNOSTIC SCRIPT
 * 
 * Run this in the browser console to diagnose profile picture issues
 * Usage: Copy and paste this entire script into the browser console
 */

console.log('🔍 PROFILE PICTURE DIAGNOSTIC STARTING...\n');

// Function to check localStorage
function checkLocalStorage() {
    console.log('📱 LOCALSTORAGE ANALYSIS:');
    console.log('========================');
    
    const aureusUser = localStorage.getItem('aureus_user');
    const aureusTelegramUser = localStorage.getItem('aureus_telegram_user');
    const aureusTestUser = localStorage.getItem('aureus_test_user');
    
    if (aureusUser) {
        const userData = JSON.parse(aureusUser);
        console.log('✅ aureus_user found:', {
            id: userData.id,
            username: userData.username,
            email: userData.email,
            profile_image_url: userData.profile_image_url,
            updated_at: userData.updated_at
        });
    } else {
        console.log('❌ aureus_user not found in localStorage');
    }
    
    if (aureusTelegramUser) {
        const telegramData = JSON.parse(aureusTelegramUser);
        console.log('✅ aureus_telegram_user found:', {
            database_user: telegramData.database_user ? {
                id: telegramData.database_user.id,
                username: telegramData.database_user.username,
                profile_image_url: telegramData.database_user.profile_image_url,
                updated_at: telegramData.database_user.updated_at
            } : 'No database_user'
        });
    } else {
        console.log('❌ aureus_telegram_user not found in localStorage');
    }
    
    if (aureusTestUser) {
        console.log('🧪 aureus_test_user found (test mode active)');
    }
    
    console.log('\n');
}

// Function to check DOM elements
function checkDOMElements() {
    console.log('🖼️ DOM PROFILE PICTURE ANALYSIS:');
    console.log('=================================');
    
    // Find all profile picture images
    const profileImages = document.querySelectorAll('img[alt*="profile"], img[src*="profile-pictures"], img[src*="default-avatar"]');
    
    console.log(`Found ${profileImages.length} profile picture elements:`);
    
    profileImages.forEach((img, index) => {
        console.log(`Image ${index + 1}:`, {
            src: img.src,
            alt: img.alt,
            className: img.className,
            parentElement: img.parentElement.className
        });
    });
    
    // Check for ProfilePictureUpload components
    const uploadComponents = document.querySelectorAll('[class*="profile-picture"], [class*="ProfilePicture"]');
    console.log(`\nFound ${uploadComponents.length} profile picture upload components`);
    
    console.log('\n');
}

// Function to test image loading
function testImageLoading() {
    console.log('🔗 IMAGE LOADING TEST:');
    console.log('======================');
    
    const aureusUser = localStorage.getItem('aureus_user');
    if (aureusUser) {
        const userData = JSON.parse(aureusUser);
        const profileImageUrl = userData.profile_image_url;
        
        if (profileImageUrl) {
            console.log('Testing profile image URL:', profileImageUrl);
            
            const testImg = new Image();
            testImg.onload = () => {
                console.log('✅ Profile image loads successfully');
                console.log('Image dimensions:', testImg.naturalWidth + 'x' + testImg.naturalHeight);
            };
            testImg.onerror = () => {
                console.log('❌ Profile image failed to load');
                console.log('URL may be invalid or inaccessible');
            };
            testImg.src = profileImageUrl;
            
            // Test with cache-busting
            const cacheBustedUrl = `${profileImageUrl}?t=${new Date(userData.updated_at || Date.now()).getTime()}`;
            console.log('Testing cache-busted URL:', cacheBustedUrl);
            
            const testImg2 = new Image();
            testImg2.onload = () => {
                console.log('✅ Cache-busted profile image loads successfully');
            };
            testImg2.onerror = () => {
                console.log('❌ Cache-busted profile image failed to load');
            };
            testImg2.src = cacheBustedUrl;
        } else {
            console.log('❌ No profile_image_url found in user data');
        }
    }
    
    console.log('\n');
}

// Function to check for React components
function checkReactComponents() {
    console.log('⚛️ REACT COMPONENT ANALYSIS:');
    console.log('============================');
    
    // Check if React is available
    if (typeof React !== 'undefined') {
        console.log('✅ React is available');
    } else {
        console.log('❌ React not found in global scope');
    }
    
    // Check for common React dev tools
    if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
        console.log('✅ React DevTools detected');
    } else {
        console.log('❌ React DevTools not detected');
    }
    
    console.log('\n');
}

// Function to simulate profile picture update
function simulateProfilePictureUpdate() {
    console.log('🔄 SIMULATING PROFILE PICTURE UPDATE:');
    console.log('=====================================');

    const testImageUrl = 'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/profile-pictures/test-image.jpg';
    const currentTimestamp = new Date().toISOString();

    console.log('Simulating update with URL:', testImageUrl);
    console.log('Using timestamp:', currentTimestamp);

    // Update localStorage with timestamp
    const aureusUser = localStorage.getItem('aureus_user');
    if (aureusUser) {
        const userData = JSON.parse(aureusUser);
        userData.profile_image_url = testImageUrl;
        userData.updated_at = currentTimestamp;
        localStorage.setItem('aureus_user', JSON.stringify(userData));
        console.log('✅ Updated aureus_user localStorage with timestamp');
    }

    const aureusTelegramUser = localStorage.getItem('aureus_telegram_user');
    if (aureusTelegramUser) {
        const telegramData = JSON.parse(aureusTelegramUser);
        if (telegramData.database_user) {
            telegramData.database_user.profile_image_url = testImageUrl;
            telegramData.database_user.updated_at = currentTimestamp;
        }
        localStorage.setItem('aureus_telegram_user', JSON.stringify(telegramData));
        console.log('✅ Updated aureus_telegram_user localStorage with timestamp');
    }

    console.log('🔄 Refresh the page to see if changes persist');
    console.log('⏰ The timestamp should prevent getCurrentUser() from overriding the change');
    console.log('\n');
}

// Function to check timestamp logic
function checkTimestampLogic() {
    console.log('⏰ TIMESTAMP LOGIC CHECK:');
    console.log('=========================');

    const aureusUser = localStorage.getItem('aureus_user');
    if (aureusUser) {
        const userData = JSON.parse(aureusUser);
        const updatedAt = userData.updated_at;

        if (updatedAt) {
            const timeDiff = Date.now() - new Date(updatedAt).getTime();
            const shouldRefresh = timeDiff > 60000; // 1 minute

            console.log('Last updated:', updatedAt);
            console.log('Time difference:', Math.round(timeDiff / 1000), 'seconds');
            console.log('Should refresh from database:', shouldRefresh);

            if (shouldRefresh) {
                console.log('⚠️ Profile picture will be refreshed from database on next getCurrentUser() call');
            } else {
                console.log('✅ Profile picture will use cached version (recent update detected)');
            }
        } else {
            console.log('❌ No updated_at timestamp found - will refresh from database');
        }
    } else {
        console.log('❌ No aureus_user found in localStorage');
    }

    console.log('\n');
}

// Main diagnostic function
function runDiagnostic() {
    console.log('🚀 STARTING COMPREHENSIVE PROFILE PICTURE DIAGNOSTIC\n');
    console.log('====================================================\n');
    
    checkLocalStorage();
    checkDOMElements();
    testImageLoading();
    checkReactComponents();
    
    checkTimestampLogic();

    console.log('🎯 DIAGNOSTIC COMPLETE');
    console.log('======================');
    console.log('To simulate a profile picture update, run: simulateProfilePictureUpdate()');
    console.log('To check timestamp logic, run: checkTimestampLogic()');
    console.log('To re-run this diagnostic, run: runDiagnostic()');
}

// Make functions available globally
window.profilePictureDiagnostic = {
    runDiagnostic,
    checkLocalStorage,
    checkDOMElements,
    testImageLoading,
    checkReactComponents,
    simulateProfilePictureUpdate,
    checkTimestampLogic
};

// Auto-run diagnostic
runDiagnostic();
