/**
 * Test script to verify conversion commission processing fixes
 * Tests the fixed foreign key and audit logging issues
 */

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const testCommissionProcessingFix = async () => {
  try {
    console.log('🧪 TESTING CONVERSION COMMISSION PROCESSING FIXES');
    console.log('='.repeat(60));

    // Step 1: Check if the test conversion still exists and is approved
    console.log('\n1️⃣ Checking test conversion status...');
    const { data: conversion, error: conversionError } = await supabase
      .from('commission_conversions')
      .select('*')
      .eq('id', '35bf3e39-457d-4e1e-9658-3da3d9c6e94c')
      .single();

    if (conversionError || !conversion) {
      console.log('❌ Test conversion not found or error:', conversionError);
      return;
    }

    console.log(`✅ Found conversion: ${conversion.id}`);
    console.log(`   • Status: ${conversion.status}`);
    console.log(`   • User: ${conversion.user_id}`);
    console.log(`   • Amount: $${conversion.usdt_amount}`);
    console.log(`   • Shares: ${conversion.shares_requested}`);

    // Step 2: Test commission transaction creation with null share_purchase_id
    console.log('\n2️⃣ Testing commission transaction creation...');
    
    // Get referrer info
    const { data: referralData, error: referralError } = await supabase
      .from('referrals')
      .select('referrer_id')
      .eq('referred_id', conversion.user_id)
      .single();

    if (referralError || !referralData) {
      console.log('❌ No referrer found for user:', conversion.user_id);
      return;
    }

    console.log(`✅ Found referrer: ${referralData.referrer_id}`);

    // Test creating a commission transaction with null share_purchase_id
    const { data: testTransaction, error: transactionError } = await supabase
      .from('commission_transactions')
      .insert({
        referrer_id: referralData.referrer_id,
        referred_id: conversion.user_id,
        share_purchase_id: null, // This should work now
        commission_rate: 15,
        share_purchase_amount: conversion.usdt_amount,
        usdt_commission: conversion.usdt_amount * 0.15,
        share_commission: conversion.shares_requested * 0.15,
        status: 'approved',
        payment_date: new Date().toISOString(),
        created_at: new Date().toISOString(),
        phase_id: conversion.phase_id
      })
      .select()
      .single();

    if (transactionError) {
      console.log('❌ Commission transaction creation failed:', transactionError);
      return;
    }

    console.log('✅ Commission transaction created successfully!');
    console.log(`   • Transaction ID: ${testTransaction.id}`);
    console.log(`   • USDT Commission: $${testTransaction.usdt_commission}`);
    console.log(`   • Share Commission: ${testTransaction.share_commission}`);

    // Step 3: Test audit logging with correct table name
    console.log('\n3️⃣ Testing audit logging...');
    
    const testLogEntry = {
      admin_telegram_id: 0,
      admin_username: '<EMAIL>',
      action: 'TEST_CONVERSION_COMMISSION',
      target_type: 'conversion',
      target_id: conversion.id,
      details: {
        test: true,
        conversion_id: conversion.id,
        transaction_id: testTransaction.id,
        timestamp: new Date().toISOString(),
        source: 'test_script'
      },
      ip_address: null,
      user_agent: 'test_script'
    };

    const { data: auditLog, error: auditError } = await supabase
      .from('admin_audit_logs') // Correct table name (plural)
      .insert(testLogEntry)
      .select()
      .single();

    if (auditError) {
      console.log('❌ Audit logging failed:', auditError);
      return;
    }

    console.log('✅ Audit logging successful!');
    console.log(`   • Log ID: ${auditLog.id}`);
    console.log(`   • Action: ${auditLog.action}`);
    console.log(`   • Admin: ${auditLog.admin_username}`);

    // Step 4: Clean up test data
    console.log('\n4️⃣ Cleaning up test data...');
    
    // Delete test transaction
    await supabase
      .from('commission_transactions')
      .delete()
      .eq('id', testTransaction.id);

    // Delete test audit log
    await supabase
      .from('admin_audit_logs')
      .delete()
      .eq('id', auditLog.id);

    console.log('✅ Test data cleaned up');

    console.log('\n🎉 ALL TESTS PASSED!');
    console.log('\n📋 Summary of fixes verified:');
    console.log('   ✅ Commission transactions can use null share_purchase_id');
    console.log('   ✅ Audit logging uses correct table name (admin_audit_logs)');
    console.log('   ✅ Foreign key constraints are satisfied');
    console.log('\n🚀 The conversion approval should now work correctly!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
};

testCommissionProcessingFix();
