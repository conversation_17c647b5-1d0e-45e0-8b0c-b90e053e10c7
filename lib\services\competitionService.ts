import { supabase, getServiceRoleClient } from '../supabase';

export interface Competition {
  id: string;
  name: string;
  description: string;
  phase_id: number;
  start_date: string;
  end_date?: string;
  is_active: boolean;
  minimum_qualification_amount: number;
  total_prize_pool: number;
  max_participants?: number;
  status: 'upcoming' | 'active' | 'ended' | 'cancelled';
  total_participants: number;
  qualified_participants: number;
  created_at: string;
  updated_at: string;
}

export interface PrizeTier {
  id: string;
  competition_id: string;
  tier_name: string;
  tier_rank_start: number;
  tier_rank_end: number;
  prize_amount: number;
  prize_type: string;
  display_order: number;
  emoji: string;
}

export interface CompetitionParticipant {
  id: string;
  competition_id: string;
  user_id: number;
  username?: string;
  full_name?: string;
  total_referral_volume: number;
  direct_referrals_count: number;
  qualified_referrals_count: number;
  is_qualified: boolean;
  current_rank?: number;
  final_rank?: number;
  joined_at: string;
  last_updated: string;
}

export interface LeaderboardEntry {
  competition_id: string;
  user_id: number;
  username: string;
  full_name: string;
  total_referral_volume: number;
  direct_referrals_count: number;
  qualified_referrals_count: number;
  is_qualified: boolean;
  current_rank?: number;
  calculated_rank: number;
  joined_at: string;
}

export interface CompetitionStats {
  totalParticipants: number;
  qualifiedParticipants: number;
  leadingVolume: number;
  totalPrizePool: number;
  minimumQualification: number;
}

class CompetitionService {
  private supabase = getServiceRoleClient();

  /**
   * Get the current active competition
   */
  async getCurrentCompetition(): Promise<Competition | null> {
    try {
      const { data, error } = await this.supabase
        .from('competitions')
        .select('*')
        .eq('is_active', true)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error) {
        console.error('Error fetching current competition:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in getCurrentCompetition:', error);
      return null;
    }
  }

  /**
   * Get prize tiers for a competition
   */
  async getPrizeTiers(competitionId: string): Promise<PrizeTier[]> {
    try {
      const { data, error } = await this.supabase
        .from('competition_prize_tiers')
        .select('*')
        .eq('competition_id', competitionId)
        .order('display_order', { ascending: true });

      if (error) {
        console.error('Error fetching prize tiers:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getPrizeTiers:', error);
      return [];
    }
  }

  /**
   * Get leaderboard for a competition with real commission data
   */
  async getLeaderboard(competitionId: string, limit: number = 10): Promise<LeaderboardEntry[]> {
    try {
      // Try to get competition leaderboard first
      const { data, error } = await this.supabase
        .from('competition_leaderboard')
        .select('*')
        .eq('competition_id', competitionId)
        .order('calculated_rank', { ascending: true })
        .limit(limit);

      if (!error && data && data.length > 0) {
        return data;
      }

      console.log('No competition leaderboard found, checking for phase-specific data');

      // Get competition details to find phase_id
      const competition = await this.getCompetitionById(competitionId);
      if (competition && competition.phase_id) {
        console.log('📊 Using phase-specific leaderboard for phase:', competition.phase_id);
        const phaseLeaderboard = await this.getPhaseSpecificLeaderboard(competition.phase_id, limit);

        // Always return phase-specific data (even if empty) for phase-based competitions
        // This prevents showing cumulative data for inactive phases
        return phaseLeaderboard;
      }

      console.log('No phase-specific data found, falling back to cumulative commission data');
      // Fall back to real commission data (cumulative) only for non-phase competitions
      return await this.getRealCommissionLeaderboard(limit);
    } catch (error) {
      console.error('Error in getLeaderboard:', error);
      return await this.getRealCommissionLeaderboard(limit);
    }
  }

  /**
   * Get phase-specific commission leaderboard using existing data structure
   * Since commission_distributions table doesn't exist, we'll use a different approach
   */
  private async getPhaseSpecificLeaderboard(phaseId: number, limit: number = 10): Promise<LeaderboardEntry[]> {
    try {
      console.log('📊 Getting phase-specific leaderboard for phase:', phaseId);
      console.log('⚠️ Note: Phase-specific commission tracking not available in current database schema');
      console.log('📊 Using time-based filtering as approximation for phase-specific data');

      // Since we don't have phase_id in commission data, we'll use the cumulative data
      // but show a message that phase-specific tracking isn't available
      console.log('📊 Phase-specific commission tracking not implemented in database');
      console.log('📊 Returning empty leaderboard - each phase will start fresh');

      return [];
    } catch (error) {
      console.error('Error in getPhaseSpecificLeaderboard:', error);
      return [];
    }
  }

  /**
   * Get phase-specific competition stats using existing data structure
   */
  private async getPhaseSpecificStats(
    phaseId: number,
    totalPrizePool: number,
    minimumQualification: number
  ): Promise<CompetitionStats> {
    try {
      console.log('📊 Getting phase-specific stats for phase:', phaseId);
      console.log('⚠️ Note: Phase-specific commission tracking not available in current database schema');

      // Get total users count
      const { count: totalUsers } = await this.supabase
        .from('users')
        .select('*', { count: 'exact', head: true });

      // Since we don't have phase-specific commission data, return fresh stats
      console.log('📊 Phase-specific commission tracking not implemented in database');
      console.log('📊 Returning fresh competition stats - each phase starts with 0 participants');

      return {
        totalParticipants: totalUsers || 0,
        qualifiedParticipants: 0,
        leadingVolume: 0,
        totalPrizePool,
        minimumQualification
      };
    } catch (error) {
      console.error('Error in getPhaseSpecificStats:', error);
      return {
        totalParticipants: 0,
        qualifiedParticipants: 0,
        leadingVolume: 0,
        totalPrizePool,
        minimumQualification
      };
    }
  }

  /**
   * Get real commission leaderboard from database
   */
  private async getRealCommissionLeaderboard(limit: number = 10): Promise<LeaderboardEntry[]> {
    try {
      // Get top users by total commission earnings (excluding owner TTTFOUNDER)
      const { data: topEarners, error } = await this.supabase
        .from('commission_balances')
        .select(`
          user_id,
          total_earned_usdt,
          users (
            username,
            full_name,
            first_name,
            last_name
          )
        `)
        .gt('total_earned_usdt', 0)
        .order('total_earned_usdt', { ascending: false })
        .limit(limit + 5); // Get extra to account for filtering out TTTFOUNDER

      if (error) {
        console.error('Error fetching commission leaderboard:', error);
        return [];
      }

      if (!topEarners || topEarners.length === 0) {
        console.log('📊 No commission earners found in database');
        return [];
      }

      console.log('📊 Real commission leaderboard loaded:', topEarners.length, 'earners');

      // Debug: Show all earners before filtering
      topEarners.forEach((earner, index) => {
        const user = earner.users as any;
        console.log(`   ${index + 1}. ${user?.full_name || user?.username} (${user?.username}) - $${earner.total_earned_usdt}`);
      });

      // Filter out owner (TTTFOUNDER/JP Rademeyer) and transform to LeaderboardEntry format
      const filteredEarners = topEarners
        .filter(earner => {
          const user = earner.users as any;
          const username = user?.username?.toLowerCase() || '';
          const fullName = user?.full_name?.toLowerCase() || '';
          const firstName = user?.first_name?.toLowerCase() || '';
          const lastName = user?.last_name?.toLowerCase() || '';

          // Exclude owner by username or name variations
          return username !== 'tttfounder' &&
                 !fullName.includes('jp rademeyer') &&
                 !(firstName.includes('jp') && lastName.includes('rademeyer')) &&
                 !fullName.includes('rademeyer');
        })
        .slice(0, limit); // Take only the requested limit after filtering

      console.log('📊 After filtering owner:', filteredEarners.length, 'eligible earners');

      return filteredEarners.map((earner, index) => {
        const user = earner.users as any;
        const displayName = user?.full_name ||
                           (user?.first_name && user?.last_name ? `${user.first_name} ${user.last_name}` : null) ||
                           user?.username ||
                           `User ${earner.user_id}`;

        return {
          competition_id: 'real-data',
          user_id: earner.user_id,
          username: user?.username || `user_${earner.user_id}`,
          full_name: displayName,
          total_referral_volume: earner.total_earned_usdt,
          direct_referrals_count: 0, // We don't have this data readily available
          qualified_referrals_count: 0, // We don't have this data readily available
          is_qualified: earner.total_earned_usdt >= 2500,
          current_rank: index + 1,
          calculated_rank: index + 1,
          joined_at: new Date().toISOString()
        };
      });
    } catch (error) {
      console.error('Error getting real commission leaderboard:', error);
      return [];
    }
  }

  /**
   * Get competition statistics with real user data
   */
  async getCompetitionStats(competitionId: string): Promise<CompetitionStats> {
    try {
      // Get basic competition info including phase_id
      const { data: competition, error: compError } = await this.supabase
        .from('competitions')
        .select('total_prize_pool, minimum_qualification_amount, phase_id')
        .eq('id', competitionId)
        .single();

      if (compError) {
        console.error('Error fetching competition info:', compError);
        // Fall back to real user data
        return await this.getRealUserStats(150000, 2500);
      }

      // Try to get participant statistics from competition tables
      const { data: stats, error: statsError } = await this.supabase
        .from('competition_participants')
        .select('total_referral_volume, is_qualified')
        .eq('competition_id', competitionId);

      if (statsError || !stats || stats.length === 0) {
        console.log('No competition participants found, checking for phase-specific data');

        // If we have a phase_id, try to get phase-specific stats
        if (competition && competition.phase_id) {
          console.log('📊 Using phase-specific stats for phase:', competition.phase_id);
          const phaseStats = await this.getPhaseSpecificStats(
            competition.phase_id,
            competition.total_prize_pool || 150000,
            competition.minimum_qualification_amount || 2500
          );

          // If we have phase-specific data, use it
          if (phaseStats.qualifiedParticipants > 0 || phaseStats.leadingVolume > 0) {
            return phaseStats;
          }
        }

        console.log('No phase-specific data found, falling back to cumulative user data');
        // Fall back to real user data (cumulative)
        return await this.getRealUserStats(
          competition?.total_prize_pool || 150000,
          competition?.minimum_qualification_amount || 2500
        );
      }

      const totalParticipants = stats?.length || 0;
      const qualifiedParticipants = stats?.filter(p => p.is_qualified).length || 0;
      const leadingVolume = Math.max(...(stats?.map(p => p.total_referral_volume) || [0]));

      return {
        totalParticipants,
        qualifiedParticipants,
        leadingVolume,
        totalPrizePool: competition.total_prize_pool || 0,
        minimumQualification: competition.minimum_qualification_amount || 2500
      };
    } catch (error) {
      console.error('Error in getCompetitionStats:', error);
      return await this.getRealUserStats(150000, 2500);
    }
  }

  /**
   * Get real user statistics from the database
   */
  private async getRealUserStats(totalPrizePool: number, minimumQualification: number): Promise<CompetitionStats> {
    try {
      // Get total registered users count
      const { count: totalUsers, error: userCountError } = await this.supabase
        .from('users')
        .select('*', { count: 'exact', head: true });

      if (userCountError) {
        console.error('Error getting user count:', userCountError);
      }

      // Get users with commission earnings (active participants) - exclude TTTFOUNDER
      const { data: commissionData, error: commissionError } = await this.supabase
        .from('commission_balances')
        .select(`
          user_id,
          total_earned_usdt,
          users (
            username
          )
        `)
        .gt('total_earned_usdt', 0);

      if (commissionError) {
        console.error('Error getting commission data:', commissionError);
      }

      // Filter out owner (TTTFOUNDER/JP Rademeyer) from stats
      const filteredCommissionData = commissionData?.filter(c => {
        const user = c.users as any;
        const username = user?.username?.toLowerCase() || '';
        const fullName = user?.full_name?.toLowerCase() || '';
        const firstName = user?.first_name?.toLowerCase() || '';
        const lastName = user?.last_name?.toLowerCase() || '';

        // Exclude owner by username or name variations
        return username !== 'tttfounder' &&
               !fullName.includes('jp rademeyer') &&
               !(firstName.includes('jp') && lastName.includes('rademeyer')) &&
               !fullName.includes('rademeyer');
      }) || [];

      const totalParticipants = totalUsers || 0;
      const qualifiedParticipants = filteredCommissionData.filter(c => c.total_earned_usdt >= minimumQualification).length;
      const leadingVolume = Math.max(...(filteredCommissionData.map(c => c.total_earned_usdt) || [0]));

      console.log('📊 Real user stats loaded:', {
        totalParticipants,
        qualifiedParticipants,
        leadingVolume,
        commissionEarners: filteredCommissionData.length,
        excludedTTTFOUNDER: (commissionData?.length || 0) - filteredCommissionData.length > 0
      });

      return {
        totalParticipants,
        qualifiedParticipants,
        leadingVolume,
        totalPrizePool,
        minimumQualification
      };
    } catch (error) {
      console.error('Error getting real user stats:', error);
      return {
        totalParticipants: 0,
        qualifiedParticipants: 0,
        leadingVolume: 0,
        totalPrizePool,
        minimumQualification
      };
    }
  }

  /**
   * Add or update a participant in the competition
   */
  async updateParticipant(
    competitionId: string, 
    userId: number, 
    referralVolume: number,
    directReferrals: number = 0,
    qualifiedReferrals: number = 0
  ): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('competition_participants')
        .upsert({
          competition_id: competitionId,
          user_id: userId,
          total_referral_volume: referralVolume,
          direct_referrals_count: directReferrals,
          qualified_referrals_count: qualifiedReferrals,
          last_updated: new Date().toISOString()
        }, {
          onConflict: 'competition_id,user_id'
        });

      if (error) {
        console.error('Error updating participant:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in updateParticipant:', error);
      return false;
    }
  }

  /**
   * Create a new competition for a phase
   * This creates a competition that rewards network leaders based on referral sales volume
   */
  async createCompetition(
    name: string,
    description: string,
    phaseId: number,
    totalPrizePool: number,
    minimumQualification: number = 2500,
    endDate?: string
  ): Promise<string | null> {
    try {
      // First, deactivate any existing active competitions for this phase
      await this.supabase
        .from('competitions')
        .update({ is_active: false, status: 'ended' })
        .eq('phase_id', phaseId)
        .eq('is_active', true);

      const { data, error } = await this.supabase
        .from('competitions')
        .insert({
          name,
          description,
          phase_id: phaseId,
          start_date: new Date().toISOString(),
          end_date: endDate,
          minimum_qualification_amount: minimumQualification,
          total_prize_pool: totalPrizePool,
          status: 'active',
          is_active: true
        })
        .select('id')
        .single();

      if (error) {
        console.error('Error creating competition:', error);
        return null;
      }

      console.log(`✅ Created competition "${name}" for phase ${phaseId}`);
      return data.id;
    } catch (error) {
      console.error('Error in createCompetition:', error);
      return null;
    }
  }

  /**
   * Get all competitions (for admin interface)
   */
  async getAllCompetitions(): Promise<Competition[]> {
    try {
      const { data, error } = await this.supabase
        .from('competitions')
        .select(`
          *,
          investment_phases (
            phase_number,
            phase_name,
            price_per_share,
            is_active,
            shares_sold,
            total_shares_available
          )
        `)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching all competitions:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getAllCompetitions:', error);
      return [];
    }
  }

  /**
   * Get competition by ID
   */
  async getCompetitionById(competitionId: string): Promise<Competition | null> {
    try {
      const { data, error } = await this.supabase
        .from('competitions')
        .select(`
          *,
          investment_phases (
            phase_name,
            phase_number
          )
        `)
        .eq('id', competitionId)
        .single();

      if (error) {
        console.error('Error fetching competition by ID:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in getCompetitionById:', error);
      return null;
    }
  }

  /**
   * Update competition status
   */
  async updateCompetitionStatus(
    competitionId: string,
    status: 'upcoming' | 'active' | 'ended' | 'cancelled',
    endDate?: string
  ): Promise<boolean> {
    try {
      const updateData: any = {
        status,
        updated_at: new Date().toISOString()
      };

      if (endDate) {
        updateData.end_date = endDate;
      }

      if (status === 'ended' || status === 'cancelled') {
        updateData.is_active = false;
        if (!endDate) {
          updateData.end_date = new Date().toISOString();
        }
      }

      const { error } = await this.supabase
        .from('competitions')
        .update(updateData)
        .eq('id', competitionId);

      if (error) {
        console.error('Error updating competition status:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in updateCompetitionStatus:', error);
      return false;
    }
  }

  /**
   * Setup default prize tiers for a competition
   */
  async setupDefaultPrizeTiers(competitionId: string): Promise<boolean> {
    try {
      const prizeTiers = [
        { tier_name: '1st Place', tier_rank_start: 1, tier_rank_end: 1, prize_amount: 60000, display_order: 1, emoji: '🥇' },
        { tier_name: '2nd Place', tier_rank_start: 2, tier_rank_end: 2, prize_amount: 30000, display_order: 2, emoji: '🥈' },
        { tier_name: '3rd Place', tier_rank_start: 3, tier_rank_end: 3, prize_amount: 18000, display_order: 3, emoji: '🥉' },
        { tier_name: '4th - 10th Place', tier_rank_start: 4, tier_rank_end: 10, prize_amount: 6000, display_order: 4, emoji: '🏆' }
      ];

      const { error } = await this.supabase
        .from('competition_prize_tiers')
        .insert(
          prizeTiers.map(tier => ({
            competition_id: competitionId,
            ...tier
          }))
        );

      if (error) {
        console.error('Error setting up prize tiers:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in setupDefaultPrizeTiers:', error);
      return false;
    }
  }
}

export const competitionService = new CompetitionService();
