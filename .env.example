# SUPABASE CONFIGURATION - ACTIVE
# Credentials configured and ready for use
# NEVER commit .env files to version control

# Supabase Project Settings
SUPABASE_URL=https://fgubaqoftdeefcakejwu.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA

# Vite Environment Variables (for client-side access)
VITE_SUPABASE_URL=https://fgubaqoftdeefcakejwu.supabase.co
# REPLACE THIS WITH YOUR REAL ANON KEY FROM SUPABASE DASHBOARD
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDkyMTAsImV4cCI6MjA2Njg4NTIxMH0.ZdCtKWveoWqxufQ59OXGf2EXoCBjUhWe8spDvYASySI
# Service role key for admin operations (ONLY for development - never expose in production)
VITE_SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA

# Database Connection (for direct PostgreSQL access)
SUPABASE_DB_HOST=db.fgubaqoftdeefcakejwu.supabase.co
SUPABASE_DB_PORT=5432
SUPABASE_DB_NAME=postgres
SUPABASE_DB_USER=postgres
SUPABASE_DB_PASSWORD=D$4#wTr3VqECr6q


# Security
JWT_SECRET=AhjFe3VnLDSAeYsrjQUtk/JnL7/NyO3VmwUkphKzMnl6gWB1sZ2/VRlm14sOtVU13HH0JLlk5Q0PDBLlOmTJnQ==

# Resend
RESEND_API_KEY=re_K6WfP6k4_17kPJQr4ZRCU1GLxAVJCsM1n
RESEND_FROM_EMAIL=<EMAIL>
RESEND_FROM_NAME=Aureus Alliance Holdings