/**
 * FIX NAASHIE1 DATA INCONSISTENCY
 * 
 * This script fixes the existing data inconsistency for user ID 371 (Naashie1)
 * where the username exists in multiple tables but may be inconsistent.
 * 
 * PROBLEM: User 371 has username "naashie1" in users table and user_share_holdings table
 * but this might be causing referral link issues.
 */

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co'
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseServiceKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function fixNaashie1DataInconsistency() {
  console.log('🔧 FIXING NAASHIE1 DATA INCONSISTENCY')
  console.log('====================================')
  
  const userId = 371;
  
  // ===== PART 1: AUDIT CURRENT STATE =====
  console.log('\n📋 PART 1: Auditing current state of user 371 (Naashie1)')
  
  try {
    // Check users table
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, username, email, full_name')
      .eq('id', userId)
      .single();
    
    if (userError) {
      console.log('❌ Error fetching user data:', userError.message);
      return false;
    }
    
    console.log('👤 Users table:', userData);
    
    // Check user_share_holdings table
    const { data: shareHoldingsData, error: shareHoldingsError } = await supabase
      .from('user_share_holdings')
      .select('user_id, username, email, full_name')
      .eq('user_id', userId)
      .single();
    
    if (shareHoldingsError) {
      console.log('❌ Error fetching share holdings data:', shareHoldingsError.message);
    } else {
      console.log('📊 User Share Holdings table:', shareHoldingsData);
    }
    
    // Check telegram_users table
    const { data: telegramData, error: telegramError } = await supabase
      .from('telegram_users')
      .select('id, user_id, username')
      .eq('user_id', userId);
    
    if (telegramError) {
      console.log('❌ Error fetching telegram data:', telegramError.message);
    } else {
      console.log('📱 Telegram Users table:', telegramData);
    }
    
    // Check referrals table
    const { data: referralsData, error: referralsError } = await supabase
      .from('referrals')
      .select('id, referrer_id, referral_code')
      .eq('referrer_id', userId);
    
    if (referralsError) {
      console.log('❌ Error fetching referrals data:', referralsError.message);
    } else {
      console.log('🔗 Referrals table:', referralsData);
    }
    
    // ===== PART 2: CHECK FOR INCONSISTENCIES =====
    console.log('\n📋 PART 2: Checking for data inconsistencies');
    
    const inconsistencies = [];
    
    if (shareHoldingsData && userData.username !== shareHoldingsData.username) {
      inconsistencies.push({
        table: 'user_share_holdings',
        field: 'username',
        expected: userData.username,
        actual: shareHoldingsData.username
      });
    }
    
    if (telegramData && telegramData.length > 0) {
      telegramData.forEach((telegram, index) => {
        if (telegram.username !== userData.username) {
          inconsistencies.push({
            table: 'telegram_users',
            field: 'username',
            record_id: telegram.id,
            expected: userData.username,
            actual: telegram.username
          });
        }
      });
    }
    
    if (inconsistencies.length > 0) {
      console.log('⚠️ Found inconsistencies:');
      inconsistencies.forEach((inc, index) => {
        console.log(`   ${index + 1}. ${inc.table}.${inc.field}: expected "${inc.expected}", got "${inc.actual}"`);
      });
    } else {
      console.log('✅ No inconsistencies found - all tables have matching usernames');
    }
    
    // ===== PART 3: VERIFY REFERRAL LINK GENERATION =====
    console.log('\n📋 PART 3: Testing referral link generation');
    
    const expectedReferralLink = `https://aureus.africa/${userData.username}`;
    console.log('🔗 Expected referral link:', expectedReferralLink);
    
    // ===== PART 4: SUMMARY AND RECOMMENDATIONS =====
    console.log('\n📋 PART 4: Summary and Recommendations');
    
    if (inconsistencies.length === 0) {
      console.log('✅ User 371 (Naashie1) data is consistent across all tables');
      console.log('✅ Referral link should work correctly:', expectedReferralLink);
      console.log('');
      console.log('🔍 If referral links are still showing wrong username, the issue might be:');
      console.log('   1. Frontend caching - clear browser cache');
      console.log('   2. Application state - restart the application');
      console.log('   3. Database connection pooling - wait a few minutes');
      console.log('   4. Code still using old referral link generation logic');
    } else {
      console.log('⚠️ Data inconsistencies found - these need to be fixed');
      console.log('');
      console.log('🔧 To fix inconsistencies, run:');
      console.log('   1. Use the new comprehensive username update function');
      console.log('   2. Or manually update the inconsistent tables');
      console.log('');
      console.log('💡 Recommended action:');
      console.log(`   Run: update_username_comprehensive(${userId}, '${userData.username}')`);
    }
    
    return true;
    
  } catch (error) {
    console.log('❌ Error during audit:', error.message);
    return false;
  }
}

// Run the audit
fixNaashie1DataInconsistency()
  .then((success) => {
    if (success) {
      console.log('\n🎉 NAASHIE1 DATA AUDIT COMPLETE!');
      console.log('=================================');
      console.log('✅ Audited all username-related tables for user 371');
      console.log('✅ Identified any data inconsistencies');
      console.log('✅ Provided recommendations for fixes');
      console.log('');
      console.log('🔧 NEXT STEPS:');
      console.log('1. If inconsistencies found, use comprehensive update function');
      console.log('2. Test referral link generation after fixes');
      console.log('3. Clear any frontend caches');
      console.log('4. Verify the issue is resolved');
    } else {
      console.log('\n❌ Audit failed - please check the errors above');
    }
  })
  .catch((error) => {
    console.error('❌ Script failed:', error.message);
  });
