#!/usr/bin/env node

/**
 * Debug Commission Issue
 * 
 * This script investigates why the sponsor didn't receive commission
 * for the share purchase and checks database inconsistencies.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ Missing SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function debugCommissionIssue() {
  try {
    console.log('🔍 Debugging Commission Issue\n');
    
    // Step 1: Check database counts
    console.log('📊 Step 1: Database Consistency Check');
    
    const { count: telegramUsersCount } = await supabase
      .from('telegram_users')
      .select('*', { count: 'exact', head: true });
    
    const { count: usersCount } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true });
    
    console.log(`   Telegram Users: ${telegramUsersCount}`);
    console.log(`   Users: ${usersCount}`);
    console.log(`   Difference: ${telegramUsersCount - usersCount} (${telegramUsersCount > usersCount ? 'telegram_users has more' : 'users has more'})`);
    
    // Step 2: Find Jean Pierre's recent share purchase
    console.log('\n💰 Step 2: Recent Share Purchases');
    
    const { data: recentPurchases, error: purchaseError } = await supabase
      .from('aureus_share_purchases')
      .select(`
        *,
        users(id, username, full_name, email)
      `)
      .order('created_at', { ascending: false })
      .limit(10);
    
    if (purchaseError) {
      console.error('❌ Error fetching purchases:', purchaseError);
      return;
    }
    
    console.log('Recent purchases:');
    recentPurchases.forEach((purchase, index) => {
      console.log(`   ${index + 1}. User: ${purchase.users?.full_name || purchase.users?.username} (ID: ${purchase.user_id})`);
      console.log(`      Shares: ${purchase.shares_purchased}, Amount: $${purchase.total_amount}`);
      console.log(`      Date: ${purchase.created_at}`);
      console.log(`      Status: ${purchase.status}`);
      console.log('');
    });
    
    // Step 3: Check Jean Pierre's referral relationships
    console.log('🔗 Step 3: Jean Pierre\'s Referral Relationships');
    
    const { data: jeanPierre, error: jpError } = await supabase
      .from('users')
      .select('*')
      .eq('username', 'TTTFOUNDER')
      .single();
    
    if (jpError || !jeanPierre) {
      console.error('❌ Jean Pierre (TTTFOUNDER) not found:', jpError);
      return;
    }
    
    console.log(`   Jean Pierre ID: ${jeanPierre.id}`);
    console.log(`   Email: ${jeanPierre.email}`);
    
    // Check if Jean Pierre has a sponsor
    const { data: jpReferrals, error: jpRefError } = await supabase
      .from('referrals')
      .select(`
        *,
        referrer:users!referrer_id(id, username, full_name),
        referred:users!referred_id(id, username, full_name)
      `)
      .eq('referred_id', jeanPierre.id);
    
    if (jpRefError) {
      console.error('❌ Error fetching JP referrals:', jpRefError);
    } else {
      console.log(`   Jean Pierre's sponsors (${jpReferrals.length}):`);
      jpReferrals.forEach(ref => {
        console.log(`      Sponsor: ${ref.referrer?.full_name || ref.referrer?.username} (ID: ${ref.referrer_id})`);
        console.log(`      Status: ${ref.status}, Rate: ${ref.commission_rate}%`);
      });
    }
    
    // Step 4: Check commission transactions for Jean Pierre's purchase
    console.log('\n💸 Step 4: Commission Transactions');
    
    const { data: commissions, error: commError } = await supabase
      .from('commission_transactions')
      .select(`
        *,
        referrer:users!referrer_id(id, username, full_name),
        referred:users!referred_id(id, username, full_name)
      `)
      .eq('referred_id', jeanPierre.id)
      .order('payment_date', { ascending: false });
    
    if (commError) {
      console.error('❌ Error fetching commissions:', commError);
    } else {
      console.log(`   Commission transactions for Jean Pierre (${commissions.length}):`);
      commissions.forEach(comm => {
        console.log(`      To: ${comm.referrer?.full_name || comm.referrer?.username} (ID: ${comm.referrer_id})`);
        console.log(`      USDT: $${comm.usdt_commission}, Shares: ${comm.share_commission}`);
        console.log(`      Amount: $${comm.share_purchase_amount}, Status: ${comm.status}`);
        console.log(`      Date: ${comm.payment_date}`);
        console.log('');
      });
    }
    
    // Step 5: Check payment transactions
    console.log('💳 Step 5: Payment Transactions');
    
    const { data: payments, error: payError } = await supabase
      .from('crypto_payment_transactions')
      .select(`
        *,
        users(id, username, full_name, email)
      `)
      .eq('user_id', jeanPierre.id)
      .order('created_at', { ascending: false });
    
    if (payError) {
      console.error('❌ Error fetching payments:', payError);
    } else {
      console.log(`   Payment transactions for Jean Pierre (${payments.length}):`);
      payments.forEach(payment => {
        console.log(`      Amount: $${payment.amount}, Status: ${payment.status}`);
        console.log(`      Network: ${payment.network}, Currency: ${payment.currency}`);
        console.log(`      Created: ${payment.created_at}`);
        console.log(`      Approved: ${payment.approved_at || 'Not approved'}`);
        console.log('');
      });
    }
    
    // Step 6: Check for orphaned telegram_users
    console.log('👻 Step 6: Orphaned Telegram Users');
    
    const { data: orphanedTelegram, error: orphanError } = await supabase
      .from('telegram_users')
      .select('*')
      .is('user_id', null)
      .limit(10);
    
    if (orphanError) {
      console.error('❌ Error fetching orphaned telegram users:', orphanError);
    } else {
      console.log(`   Orphaned telegram_users (no user_id link): ${orphanedTelegram.length}`);
      orphanedTelegram.forEach(tg => {
        console.log(`      Telegram ID: ${tg.telegram_id}, Username: ${tg.username}`);
        console.log(`      Name: ${tg.first_name} ${tg.last_name}`);
        console.log(`      Registered: ${tg.is_registered}, Step: ${tg.registration_step}`);
        console.log('');
      });
    }
    
    // Step 7: Check commission balances
    console.log('💰 Step 7: Commission Balances');
    
    const { data: balances, error: balError } = await supabase
      .from('commission_balances')
      .select(`
        *,
        users(id, username, full_name)
      `)
      .order('total_earned_usdt', { ascending: false })
      .limit(10);
    
    if (balError) {
      console.error('❌ Error fetching commission balances:', balError);
    } else {
      console.log('   Top commission earners:');
      balances.forEach(balance => {
        console.log(`      ${balance.users?.full_name || balance.users?.username} (ID: ${balance.user_id})`);
        console.log(`      USDT: $${balance.total_earned_usdt || 0}, Shares: ${balance.total_earned_shares || 0}`);
        console.log(`      Current Balance - USDT: $${balance.usdt_balance || 0}, Shares: ${balance.share_balance || 0}`);
        console.log('');
      });
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

debugCommissionIssue();
