import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function createTestTelegramUser() {
  try {
    console.log('🔧 Creating test Telegram user for telegram_id: 1393852632')
    
    // First, create a user record in the users table
    const { data: newUser, error: userError } = await supabase
      .from('users')
      .insert({
        username: 'telegram_1393852632',
        email: '<EMAIL>',
        password_hash: '$2b$10$dummy.hash.for.telegram.user.testing.only',
        full_name: 'Test Telegram User',
        telegram_id: 1393852632,
        is_active: true,
        is_verified: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (userError) {
      console.error('❌ Error creating user record:', userError)
      return
    }
    
    console.log('✅ User record created:', newUser)
    
    // Now create the telegram_users record
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .insert({
        telegram_id: 1393852632,
        user_id: newUser.id,
        username: 'telegram_test_user',
        first_name: 'Test',
        last_name: 'User',
        is_registered: true,
        registration_step: 'completed',
        registration_mode: 'login',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        last_activity: new Date().toISOString()
      })
      .select()
      .single()
    
    if (telegramError) {
      console.error('❌ Error creating telegram_users record:', telegramError)
      return
    }
    
    console.log('✅ Telegram user record created:', telegramUser)
    console.log('🎉 Test user setup complete! You can now test Telegram authentication.')
    
  } catch (error) {
    console.error('❌ Script error:', error)
  }
}

createTestTelegramUser()
