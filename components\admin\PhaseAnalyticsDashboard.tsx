import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { PhaseAutomationService } from '../../lib/services/phaseAutomationService';

interface PhaseAnalytics {
  phase_id: number;
  phase_number: number;
  phase_name: string;
  price_per_share: number;
  total_shares_available: number;
  shares_sold: number;
  completion_percentage: number;
  total_revenue: number;
  unique_buyers: number;
  average_purchase_size: number;
  days_active: number;
  projected_completion_date: string;
  velocity_shares_per_day: number;
}

interface PredictiveInsights {
  nextPhaseETA: string;
  projectedRevenue: number;
  recommendedActions: string[];
  riskFactors: string[];
  opportunityScore: number;
}

interface PhaseAnalyticsDashboardProps {
  isAdmin: boolean;
}

export const PhaseAnalyticsDashboard: React.FC<PhaseAnalyticsDashboardProps> = ({
  isAdmin
}) => {
  const [phaseAnalytics, setPhaseAnalytics] = useState<PhaseAnalytics[]>([]);
  const [currentPhase, setCurrentPhase] = useState<PhaseAnalytics | null>(null);
  const [predictiveInsights, setPredictiveInsights] = useState<PredictiveInsights | null>(null);
  const [loading, setLoading] = useState(true);
  const [automationService] = useState(() => PhaseAutomationService.getInstance());
  const [automationEnabled, setAutomationEnabled] = useState(true);

  useEffect(() => {
    if (isAdmin) {
      loadPhaseAnalytics();
      checkAutomationStatus();
    }
  }, [isAdmin]);

  const loadPhaseAnalytics = async () => {
    setLoading(true);
    try {
      // Load phase analytics data
      const { data: analytics, error: analyticsError } = await supabase
        .rpc('get_phase_analytics');

      if (analyticsError) throw analyticsError;
      
      setPhaseAnalytics(analytics || []);
      
      // Find current active phase
      const active = analytics?.find((phase: PhaseAnalytics) => phase.completion_percentage < 100);
      setCurrentPhase(active || null);

      // Generate predictive insights
      if (active) {
        const insights = await generatePredictiveInsights(active, analytics || []);
        setPredictiveInsights(insights);
      }

    } catch (error) {
      console.error('Error loading phase analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const checkAutomationStatus = async () => {
    const enabled = automationService.isAutomationEnabled();
    setAutomationEnabled(enabled);
  };

  const generatePredictiveInsights = async (
    current: PhaseAnalytics, 
    allPhases: PhaseAnalytics[]
  ): Promise<PredictiveInsights> => {
    try {
      const remainingShares = current.total_shares_available - current.shares_sold;
      const daysToCompletion = current.velocity_shares_per_day > 0 
        ? Math.ceil(remainingShares / current.velocity_shares_per_day)
        : 365; // Default to 1 year if no velocity

      const nextPhaseETA = new Date();
      nextPhaseETA.setDate(nextPhaseETA.getDate() + daysToCompletion);

      const projectedRevenue = remainingShares * current.price_per_share;

      // Generate recommendations based on performance
      const recommendations: string[] = [];
      const riskFactors: string[] = [];

      if (current.velocity_shares_per_day < 100) {
        recommendations.push('Consider increasing marketing efforts to boost daily sales velocity');
        riskFactors.push('Low sales velocity may delay phase completion');
      }

      if (current.average_purchase_size < current.price_per_share * 10) {
        recommendations.push('Implement incentives for larger purchases to increase average order value');
      }

      if (current.completion_percentage > 80) {
        recommendations.push('Prepare next phase activation and notify users of upcoming price increase');
      }

      if (daysToCompletion > 90) {
        riskFactors.push('Extended phase duration may impact user engagement');
        recommendations.push('Consider limited-time promotions to accelerate phase completion');
      }

      // Calculate opportunity score (0-100)
      let opportunityScore = 50; // Base score
      
      if (current.velocity_shares_per_day > 200) opportunityScore += 20;
      if (current.average_purchase_size > current.price_per_share * 20) opportunityScore += 15;
      if (current.unique_buyers > 100) opportunityScore += 10;
      if (daysToCompletion < 30) opportunityScore += 5;

      opportunityScore = Math.min(100, Math.max(0, opportunityScore));

      return {
        nextPhaseETA: nextPhaseETA.toISOString(),
        projectedRevenue,
        recommendedActions: recommendations,
        riskFactors,
        opportunityScore
      };

    } catch (error) {
      console.error('Error generating predictive insights:', error);
      return {
        nextPhaseETA: new Date().toISOString(),
        projectedRevenue: 0,
        recommendedActions: [],
        riskFactors: [],
        opportunityScore: 0
      };
    }
  };

  const toggleAutomation = async () => {
    try {
      const newStatus = !automationEnabled;
      automationService.setAutomationEnabled(newStatus);
      setAutomationEnabled(newStatus);

      // Log the change
      await supabase.from('admin_actions').insert({
        action_type: 'phase_automation_toggle',
        description: `Phase automation ${newStatus ? 'enabled' : 'disabled'}`,
        performed_at: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error toggling automation:', error);
    }
  };

  const forcePhaseTransition = async () => {
    if (!currentPhase) return;

    try {
      setLoading(true);
      
      const result = await automationService.checkPhaseTransition();
      
      if (result) {
        await loadPhaseAnalytics();
        alert('Phase transition completed successfully!');
      } else {
        alert('No phase transition needed at this time.');
      }

    } catch (error) {
      console.error('Error forcing phase transition:', error);
      alert('Error during phase transition. Please check logs.');
    } finally {
      setLoading(false);
    }
  };

  if (!isAdmin) {
    return (
      <div style={{
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        border: '1px solid rgba(239, 68, 68, 0.3)',
        borderRadius: '12px',
        padding: '24px',
        textAlign: 'center'
      }}>
        <div style={{ fontSize: '48px', marginBottom: '16px' }}>🔒</div>
        <h3 style={{ color: '#ef4444', fontSize: '18px', marginBottom: '8px' }}>
          Access Denied
        </h3>
        <p style={{ color: '#9ca3af', fontSize: '14px' }}>
          This dashboard is only accessible to administrators.
        </p>
      </div>
    );
  }

  if (loading) {
    return (
      <div style={{
        backgroundColor: 'rgba(31, 41, 55, 0.9)',
        borderRadius: '12px',
        padding: '24px',
        border: '1px solid #374151',
        textAlign: 'center'
      }}>
        <div style={{ color: '#9ca3af' }}>Loading phase analytics...</div>
      </div>
    );
  }

  return (
    <div style={{
      backgroundColor: 'rgba(31, 41, 55, 0.9)',
      borderRadius: '12px',
      padding: '24px',
      border: '1px solid #374151'
    }}>
      {/* Header */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h3 style={{
            fontSize: '20px',
            fontWeight: 'bold',
            color: '#f59e0b',
            marginBottom: '4px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            📊 Phase Analytics Dashboard
          </h3>
          <p style={{ color: '#9ca3af', fontSize: '14px', margin: 0 }}>
            Advanced analytics and predictive insights for phase management
          </p>
        </div>

        <div style={{ display: 'flex', gap: '12px' }}>
          <button
            onClick={toggleAutomation}
            style={{
              padding: '8px 16px',
              backgroundColor: automationEnabled ? 'rgba(16, 185, 129, 0.2)' : 'rgba(239, 68, 68, 0.2)',
              border: automationEnabled ? '1px solid #10b981' : '1px solid #ef4444',
              borderRadius: '8px',
              color: automationEnabled ? '#10b981' : '#ef4444',
              fontSize: '12px',
              cursor: 'pointer'
            }}
          >
            {automationEnabled ? '🤖 Auto ON' : '⏸️ Auto OFF'}
          </button>

          <button
            onClick={forcePhaseTransition}
            disabled={loading || !currentPhase}
            style={{
              padding: '8px 16px',
              backgroundColor: loading ? 'rgba(55, 65, 81, 0.5)' : 'rgba(245, 158, 11, 0.2)',
              border: '1px solid #f59e0b',
              borderRadius: '8px',
              color: loading ? '#9ca3af' : '#f59e0b',
              fontSize: '12px',
              cursor: loading ? 'not-allowed' : 'pointer'
            }}
          >
            {loading ? '⏳ Processing...' : '🚀 Force Transition'}
          </button>
        </div>
      </div>

      {/* Current Phase Overview */}
      {currentPhase && (
        <div style={{
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          border: '1px solid rgba(59, 130, 246, 0.3)',
          borderRadius: '12px',
          padding: '20px',
          marginBottom: '24px'
        }}>
          <h4 style={{ color: '#60a5fa', fontSize: '18px', fontWeight: 'bold', marginBottom: '16px' }}>
            🎯 Current Active Phase: {currentPhase.phase_name}
          </h4>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
            gap: '16px',
            marginBottom: '16px'
          }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ color: '#60a5fa', fontSize: '24px', fontWeight: 'bold' }}>
                ${currentPhase.price_per_share}
              </div>
              <div style={{ color: '#9ca3af', fontSize: '12px' }}>Price per Share</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ color: '#10b981', fontSize: '24px', fontWeight: 'bold' }}>
                {currentPhase.completion_percentage.toFixed(1)}%
              </div>
              <div style={{ color: '#9ca3af', fontSize: '12px' }}>Completion</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ color: '#f59e0b', fontSize: '24px', fontWeight: 'bold' }}>
                {currentPhase.shares_sold.toLocaleString()}
              </div>
              <div style={{ color: '#9ca3af', fontSize: '12px' }}>Shares Sold</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ color: '#a78bfa', fontSize: '24px', fontWeight: 'bold' }}>
                ${currentPhase.total_revenue.toFixed(0)}
              </div>
              <div style={{ color: '#9ca3af', fontSize: '12px' }}>Total Revenue</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ color: '#ec4899', fontSize: '24px', fontWeight: 'bold' }}>
                {Math.round(currentPhase.velocity_shares_per_day)}
              </div>
              <div style={{ color: '#9ca3af', fontSize: '12px' }}>Shares/Day</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ color: '#22c55e', fontSize: '24px', fontWeight: 'bold' }}>
                {currentPhase.unique_buyers}
              </div>
              <div style={{ color: '#9ca3af', fontSize: '12px' }}>Unique Buyers</div>
            </div>
          </div>

          {/* Progress Bar */}
          <div style={{ marginTop: '16px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
              <span style={{ color: '#9ca3af', fontSize: '14px' }}>Phase Progress</span>
              <span style={{ color: '#60a5fa', fontSize: '14px', fontWeight: '600' }}>
                {currentPhase.shares_sold.toLocaleString()} / {currentPhase.total_shares_available.toLocaleString()} shares
              </span>
            </div>
            <div style={{
              width: '100%',
              height: '12px',
              backgroundColor: 'rgba(55, 65, 81, 0.5)',
              borderRadius: '6px',
              overflow: 'hidden'
            }}>
              <div style={{
                width: `${currentPhase.completion_percentage}%`,
                height: '100%',
                background: 'linear-gradient(90deg, #3b82f6 0%, #10b981 100%)',
                borderRadius: '6px',
                transition: 'width 0.3s ease'
              }} />
            </div>
          </div>
        </div>
      )}

      {/* Predictive Insights */}
      {predictiveInsights && (
        <div style={{
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          border: '1px solid rgba(16, 185, 129, 0.3)',
          borderRadius: '12px',
          padding: '20px',
          marginBottom: '24px'
        }}>
          <h4 style={{ color: '#10b981', fontSize: '18px', fontWeight: 'bold', marginBottom: '16px' }}>
            🔮 Predictive Insights
          </h4>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '16px',
            marginBottom: '16px'
          }}>
            <div>
              <div style={{ color: '#9ca3af', fontSize: '12px', marginBottom: '4px' }}>
                Next Phase ETA
              </div>
              <div style={{ color: '#10b981', fontSize: '16px', fontWeight: 'bold' }}>
                {new Date(predictiveInsights.nextPhaseETA).toLocaleDateString()}
              </div>
            </div>
            <div>
              <div style={{ color: '#9ca3af', fontSize: '12px', marginBottom: '4px' }}>
                Projected Revenue
              </div>
              <div style={{ color: '#10b981', fontSize: '16px', fontWeight: 'bold' }}>
                ${predictiveInsights.projectedRevenue.toFixed(0)}
              </div>
            </div>
            <div>
              <div style={{ color: '#9ca3af', fontSize: '12px', marginBottom: '4px' }}>
                Opportunity Score
              </div>
              <div style={{ 
                color: predictiveInsights.opportunityScore > 70 ? '#10b981' : 
                       predictiveInsights.opportunityScore > 40 ? '#f59e0b' : '#ef4444',
                fontSize: '16px', 
                fontWeight: 'bold' 
              }}>
                {predictiveInsights.opportunityScore}/100
              </div>
            </div>
          </div>

          {/* Recommendations */}
          {predictiveInsights.recommendedActions.length > 0 && (
            <div style={{ marginBottom: '16px' }}>
              <div style={{ color: '#10b981', fontSize: '14px', fontWeight: '600', marginBottom: '8px' }}>
                💡 Recommended Actions:
              </div>
              <ul style={{ color: '#d1d5db', fontSize: '13px', margin: 0, paddingLeft: '20px' }}>
                {predictiveInsights.recommendedActions.map((action, index) => (
                  <li key={index} style={{ marginBottom: '4px' }}>{action}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Risk Factors */}
          {predictiveInsights.riskFactors.length > 0 && (
            <div>
              <div style={{ color: '#f59e0b', fontSize: '14px', fontWeight: '600', marginBottom: '8px' }}>
                ⚠️ Risk Factors:
              </div>
              <ul style={{ color: '#d1d5db', fontSize: '13px', margin: 0, paddingLeft: '20px' }}>
                {predictiveInsights.riskFactors.map((risk, index) => (
                  <li key={index} style={{ marginBottom: '4px' }}>{risk}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Phase History */}
      <div style={{
        backgroundColor: 'rgba(55, 65, 81, 0.5)',
        borderRadius: '8px',
        padding: '16px'
      }}>
        <h4 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', marginBottom: '16px' }}>
          📈 Phase Performance History
        </h4>

        {phaseAnalytics.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '40px', color: '#9ca3af' }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>📊</div>
            <h3 style={{ fontSize: '18px', marginBottom: '8px' }}>No Analytics Data</h3>
            <p style={{ fontSize: '14px' }}>
              Phase analytics will appear here once data is available.
            </p>
          </div>
        ) : (
          <div style={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ borderBottom: '1px solid #4b5563' }}>
                  <th style={{ padding: '12px 8px', textAlign: 'left', color: '#9ca3af', fontSize: '12px' }}>Phase</th>
                  <th style={{ padding: '12px 8px', textAlign: 'center', color: '#9ca3af', fontSize: '12px' }}>Price</th>
                  <th style={{ padding: '12px 8px', textAlign: 'center', color: '#9ca3af', fontSize: '12px' }}>Completion</th>
                  <th style={{ padding: '12px 8px', textAlign: 'center', color: '#9ca3af', fontSize: '12px' }}>Revenue</th>
                  <th style={{ padding: '12px 8px', textAlign: 'center', color: '#9ca3af', fontSize: '12px' }}>Buyers</th>
                  <th style={{ padding: '12px 8px', textAlign: 'center', color: '#9ca3af', fontSize: '12px' }}>Avg. Purchase</th>
                  <th style={{ padding: '12px 8px', textAlign: 'center', color: '#9ca3af', fontSize: '12px' }}>Velocity</th>
                </tr>
              </thead>
              <tbody>
                {phaseAnalytics.slice(0, 10).map((phase, index) => (
                  <tr key={phase.phase_id} style={{ borderBottom: '1px solid #374151' }}>
                    <td style={{ padding: '12px 8px', color: '#f3f4f6', fontSize: '14px' }}>
                      <div style={{ fontWeight: '600' }}>{phase.phase_name}</div>
                      <div style={{ color: '#9ca3af', fontSize: '12px' }}>Phase {phase.phase_number}</div>
                    </td>
                    <td style={{ padding: '12px 8px', textAlign: 'center', color: '#60a5fa', fontSize: '14px', fontWeight: '600' }}>
                      ${phase.price_per_share}
                    </td>
                    <td style={{ padding: '12px 8px', textAlign: 'center' }}>
                      <div style={{ 
                        color: phase.completion_percentage >= 100 ? '#10b981' : '#f59e0b', 
                        fontSize: '14px', 
                        fontWeight: '600' 
                      }}>
                        {phase.completion_percentage.toFixed(1)}%
                      </div>
                    </td>
                    <td style={{ padding: '12px 8px', textAlign: 'center', color: '#a78bfa', fontSize: '14px' }}>
                      ${phase.total_revenue.toFixed(0)}
                    </td>
                    <td style={{ padding: '12px 8px', textAlign: 'center', color: '#22c55e', fontSize: '14px' }}>
                      {phase.unique_buyers}
                    </td>
                    <td style={{ padding: '12px 8px', textAlign: 'center', color: '#ec4899', fontSize: '14px' }}>
                      ${phase.average_purchase_size.toFixed(0)}
                    </td>
                    <td style={{ padding: '12px 8px', textAlign: 'center', color: '#9ca3af', fontSize: '14px' }}>
                      {Math.round(phase.velocity_shares_per_day)}/day
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};
