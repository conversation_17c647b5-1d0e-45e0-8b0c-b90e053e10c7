import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'https://fgubaqoftdeefcakejwu.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA'
);

async function checkAllUsers() {
  try {
    console.log('🔍 Checking all users in database...');
    
    const { data: users, error } = await supabase
      .from('users')
      .select('id, username, email, telegram_id, is_admin, full_name')
      .order('id');
      
    if (error) {
      console.error('❌ Error:', error);
      return;
    }
    
    console.log('📋 All users in database:');
    users.forEach(user => {
      console.log(`ID: ${user.id}, Username: ${user.username}, Email: ${user.email}, Full Name: ${user.full_name}, Telegram ID: ${user.telegram_id}, Admin: ${user.is_admin}`);
    });
    
    // Also check telegram_users table
    console.log('\n🔍 Checking telegram_users table...');
    const { data: telegramUsers, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .order('id');
      
    if (telegramError) {
      console.error('❌ Telegram users error:', telegramError);
    } else {
      console.log('📋 Telegram users:');
      telegramUsers.forEach(user => {
        console.log(`ID: ${user.id}, Username: ${user.username}, Telegram ID: ${user.telegram_id}, Full Name: ${user.full_name}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

checkAllUsers();
