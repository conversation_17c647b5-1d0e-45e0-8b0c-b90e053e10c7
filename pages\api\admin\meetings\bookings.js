// Admin API endpoint for meeting bookings management
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase configuration for admin meeting bookings API');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Check if user is admin
async function checkAdminAccess(req) {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { isAdmin: false, error: 'No authorization token provided' };
  }

  const token = authHeader.substring(7);
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      return { isAdmin: false, error: 'Invalid token' };
    }

    // Check if user is admin
    const { data: adminUser, error: adminError } = await supabase
      .from('admin_users')
      .select('id, role, is_active')
      .eq('user_id', user.id)
      .eq('is_active', true)
      .single();

    if (adminError || !adminUser) {
      return { isAdmin: false, error: 'Admin access required' };
    }

    return { isAdmin: true, user, adminUser };
  } catch (error) {
    return { isAdmin: false, error: 'Authentication failed' };
  }
}

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Check admin access
  const { isAdmin, user, adminUser, error } = await checkAdminAccess(req);
  if (!isAdmin) {
    return res.status(401).json({ error: error || 'Admin access required' });
  }

  try {
    switch (req.method) {
      case 'GET':
        return await handleGetBookings(req, res);
      case 'PUT':
        return await handleUpdateBooking(req, res);
      case 'DELETE':
        return await handleDeleteBooking(req, res);
      default:
        return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('❌ Admin meeting bookings API error:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      details: error.message 
    });
  }
}

async function handleGetBookings(req, res) {
  const { meetingId, status, limit = 100, offset = 0 } = req.query;

  let query = supabase
    .from('meeting_bookings')
    .select(`
      id,
      meeting_id,
      attendee_name,
      attendee_email,
      phone_number,
      booking_date,
      status,
      confirmation_code,
      notes,
      created_at,
      updated_at,
      meetings (
        id,
        title,
        topic,
        meeting_date,
        meeting_time
      )
    `)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (meetingId) {
    query = query.eq('meeting_id', meetingId);
  }

  if (status && status !== 'all') {
    query = query.eq('status', status);
  }

  const { data: bookings, error } = await query;

  if (error) {
    console.error('❌ Error fetching bookings:', error);
    return res.status(500).json({ 
      error: 'Failed to fetch bookings',
      details: error.message 
    });
  }

  return res.status(200).json({
    success: true,
    bookings: bookings || [],
    count: bookings?.length || 0
  });
}

async function handleUpdateBooking(req, res) {
  const { id, status, notes } = req.body;

  if (!id) {
    return res.status(400).json({ 
      error: 'Missing booking ID',
      details: 'Booking ID is required for updates'
    });
  }

  const validStatuses = ['confirmed', 'cancelled', 'attended', 'no_show'];
  if (status && !validStatuses.includes(status)) {
    return res.status(400).json({ 
      error: 'Invalid status',
      details: `Status must be one of: ${validStatuses.join(', ')}`
    });
  }

  const updateData = {};
  if (status) updateData.status = status;
  if (notes !== undefined) updateData.notes = notes?.trim() || null;

  const { data: booking, error } = await supabase
    .from('meeting_bookings')
    .update(updateData)
    .eq('id', id)
    .select(`
      id,
      meeting_id,
      attendee_name,
      attendee_email,
      phone_number,
      booking_date,
      status,
      confirmation_code,
      notes,
      created_at,
      updated_at,
      meetings (
        id,
        title,
        topic,
        meeting_date,
        meeting_time
      )
    `)
    .single();

  if (error) {
    console.error('❌ Error updating booking:', error);
    return res.status(500).json({ 
      error: 'Failed to update booking',
      details: error.message 
    });
  }

  console.log(`✅ Booking updated successfully: ${booking.confirmation_code}`);

  return res.status(200).json({
    success: true,
    booking
  });
}

async function handleDeleteBooking(req, res) {
  const { id } = req.query;

  if (!id) {
    return res.status(400).json({ 
      error: 'Missing booking ID',
      details: 'Booking ID is required for deletion'
    });
  }

  const { error } = await supabase
    .from('meeting_bookings')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('❌ Error deleting booking:', error);
    return res.status(500).json({ 
      error: 'Failed to delete booking',
      details: error.message 
    });
  }

  console.log(`✅ Booking deleted successfully: ${id}`);

  return res.status(200).json({
    success: true,
    message: 'Booking deleted successfully'
  });
}
