// API endpoint to link Telegram account to web user
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Function to sync existing Telegram bot data with web account
async function syncTelegramData(webUserId, telegramId) {
  console.log(`🔄 Starting data synchronization for web user ${webUserId} and Telegram ${telegramId}`);

  // Since both platforms use the same database, we need to ensure that any data
  // associated with the Telegram user is properly linked to the web user account

  // The telegram_users table is already linked via user_id, so most data should
  // already be accessible. However, we should verify data consistency.

  try {
    // 1. Check for any orphaned data that might need linking
    // Most tables already reference users.id, so they should be automatically linked

    // 2. Verify share purchases are accessible
    const { data: sharePurchases, error: sharesError } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .eq('user_id', webUserId);

    if (sharesError) {
      console.error('Error checking share purchases:', sharesError);
    } else {
      console.log(`📊 Found ${sharePurchases?.length || 0} share purchases for user`);
    }

    // 3. Verify payment transactions are accessible
    const { data: payments, error: paymentsError } = await supabase
      .from('crypto_payment_transactions')
      .select('*')
      .eq('user_id', webUserId);

    if (paymentsError) {
      console.error('Error checking payments:', paymentsError);
    } else {
      console.log(`💳 Found ${payments?.length || 0} payment transactions for user`);
    }

    // 4. Verify referral data is accessible
    const { data: referrals, error: referralsError } = await supabase
      .from('referrals')
      .select('*')
      .or(`sponsor_id.eq.${webUserId},referred_user_id.eq.${webUserId}`);

    if (referralsError) {
      console.error('Error checking referrals:', referralsError);
    } else {
      console.log(`🤝 Found ${referrals?.length || 0} referral relationships for user`);
    }

    // 5. Check commission balances
    const { data: commissions, error: commissionsError } = await supabase
      .from('commission_balances')
      .select('*')
      .eq('user_id', webUserId);

    if (commissionsError) {
      console.error('Error checking commissions:', commissionsError);
    } else {
      console.log(`💰 Found ${commissions?.length || 0} commission records for user`);
    }

    console.log(`✅ Data synchronization verification completed for user ${webUserId}`);

  } catch (error) {
    console.error('❌ Data synchronization error:', error);
    throw error;
  }
}

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { user_id, telegram_id } = req.body;

    if (!user_id || !telegram_id) {
      return res.status(400).json({ error: 'User ID and Telegram ID are required' });
    }

    // Validate telegram_id format (should be numeric)
    if (!/^\d{8,12}$/.test(telegram_id)) {
      return res.status(400).json({ error: 'Invalid Telegram ID format' });
    }

    // Check if the Telegram ID exists in the telegram_users table
    const { data: existingTelegramUser, error: findError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', parseInt(telegram_id))
      .single();

    if (findError && findError.code !== 'PGRST116') {
      console.error('Error finding Telegram user:', findError);
      return res.status(500).json({ error: 'Database error while searching for Telegram user' });
    }

    if (!existingTelegramUser) {
      return res.status(404).json({ 
        error: 'Telegram ID not found. Please make sure you have used our Telegram bot first.' 
      });
    }

    // Check if this Telegram account is already linked to another web user
    if (existingTelegramUser.user_id && existingTelegramUser.user_id !== parseInt(user_id)) {
      return res.status(409).json({ 
        error: 'This Telegram account is already linked to another web account.' 
      });
    }

    // Check if the web user already has a different Telegram account linked
    const { data: existingLink, error: linkError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('user_id', parseInt(user_id))
      .single();

    if (linkError && linkError.code !== 'PGRST116') {
      console.error('Error checking existing link:', linkError);
      return res.status(500).json({ error: 'Database error while checking existing links' });
    }

    if (existingLink && existingLink.telegram_id !== parseInt(telegram_id)) {
      return res.status(409).json({ 
        error: 'Your web account is already linked to a different Telegram account.' 
      });
    }

    // Link the accounts by updating the telegram_users record
    const { data: updatedTelegramUser, error: updateError } = await supabase
      .from('telegram_users')
      .update({
        user_id: parseInt(user_id),
        is_registered: true,
        updated_at: new Date().toISOString()
      })
      .eq('telegram_id', parseInt(telegram_id))
      .select()
      .single();

    if (updateError) {
      console.error('Error linking accounts:', updateError);
      return res.status(500).json({ error: 'Failed to link accounts' });
    }

    // Sync existing Telegram bot data with the web account
    try {
      await syncTelegramData(parseInt(user_id), parseInt(telegram_id));
      console.log(`✅ Data synchronization completed for user ${user_id}`);
    } catch (syncError) {
      console.error('⚠️ Data synchronization failed:', syncError);
      // Don't fail the linking process, just log the error
    }

    console.log(`✅ Successfully linked web user ${user_id} to Telegram ${telegram_id}`);

    res.status(200).json({
      success: true,
      message: 'Telegram account linked successfully',
      telegramUser: updatedTelegramUser
    });

  } catch (error) {
    console.error('Telegram linking error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
