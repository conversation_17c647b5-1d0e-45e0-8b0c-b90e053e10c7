import React, { useState, useRef, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';

interface KYCDocumentResubmissionProps {
  kycId: string;
  fieldName: string;
  fieldDisplayName: string;
  onComplete: () => void;
  onCancel: () => void;
}

export const KYCDocumentResubmission: React.FC<KYCDocumentResubmissionProps> = ({
  kycId,
  fieldName,
  fieldDisplayName,
  onComplete,
  onCancel
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showCamera, setShowCamera] = useState(false);
  const [cameraSupported, setCameraSupported] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  // Check camera support on mount
  useEffect(() => {
    const checkCameraSupport = async () => {
      try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        const hasCamera = devices.some(device => device.kind === 'videoinput');
        setCameraSupported(hasCamera && !!navigator.mediaDevices.getUserMedia);
      } catch (error) {
        console.log('Camera not supported:', error);
        setCameraSupported(false);
      }
    };

    checkCameraSupport();

    // Cleanup on unmount
    return () => {
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];

    if (file.size > maxSize) {
      setError('File size must be less than 10MB');
      return;
    }

    if (!allowedTypes.includes(file.type)) {
      setError('Only JPEG, PNG, and PDF files are allowed');
      return;
    }

    setSelectedFile(file);
    setError(null);
  };

  const startCamera = () => {
    console.log('🎥 Starting camera for field:', fieldName);
    setError(null);
    setShowCamera(true);
  };

  // Handle camera initialization when showCamera becomes true
  useEffect(() => {
    if (!showCamera) return;

    const initializeCamera = async () => {
      try {
        console.log('📱 Initializing camera...');

        // Wait for video element to be available with multiple attempts
        let attempts = 0;
        const maxAttempts = 20; // 2 seconds total

        while (!videoRef.current && attempts < maxAttempts) {
          console.log(`⏳ Waiting for video element... attempt ${attempts + 1}/${maxAttempts}`);
          await new Promise(resolve => setTimeout(resolve, 100));
          attempts++;
        }

        if (!videoRef.current) {
          console.error('❌ Video element never became available');
          setError('Camera interface failed to load. Please try again.');
          setShowCamera(false);
          return;
        }

        const constraints = {
          video: {
            facingMode: fieldName === 'selfie_verification' ? 'user' : 'environment',
            width: { ideal: 1280, min: 640 },
            height: { ideal: 720, min: 480 }
          }
        };

        console.log('📱 Camera constraints:', constraints);
        const stream = await navigator.mediaDevices.getUserMedia(constraints);

        console.log('✅ Setting video stream');
        videoRef.current.srcObject = stream;
        streamRef.current = stream;

        // Ensure video plays
        videoRef.current.onloadedmetadata = () => {
          console.log('📹 Video metadata loaded, playing...');
          videoRef.current?.play().catch(e => console.error('Video play error:', e));
        };

      } catch (error) {
        console.error('❌ Error accessing camera:', error);
        setError(`Unable to access camera: ${error.message}. Please check permissions and try again.`);
        setShowCamera(false);
      }
    };

    // Start immediately - no delay
    initializeCamera();
  }, [showCamera, fieldName]);

  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    setShowCamera(false);
  };

  const capturePhoto = () => {
    console.log('📸 Attempting to capture photo...');

    if (!videoRef.current || !canvasRef.current) {
      console.error('❌ Video or canvas ref not available');
      setError('Camera not ready. Please try again.');
      return;
    }

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    if (!context) {
      console.error('❌ Canvas context not available');
      setError('Unable to capture photo. Please try again.');
      return;
    }

    // Check if video is ready
    if (video.videoWidth === 0 || video.videoHeight === 0) {
      console.error('❌ Video not ready - dimensions:', video.videoWidth, 'x', video.videoHeight);
      setError('Camera not ready. Please wait a moment and try again.');
      return;
    }

    console.log('📹 Video dimensions:', video.videoWidth, 'x', video.videoHeight);

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw video frame to canvas
    context.drawImage(video, 0, 0);

    // Convert canvas to blob
    canvas.toBlob((blob) => {
      if (blob) {
        console.log('✅ Photo captured successfully, size:', blob.size);
        const timestamp = Date.now();
        const fileName = `captured_${fieldName}_${timestamp}.jpg`;
        const file = new File([blob], fileName, { type: 'image/jpeg' });

        setSelectedFile(file);
        stopCamera();
        setError(null);
      } else {
        console.error('❌ Failed to create blob from canvas');
        setError('Failed to capture photo. Please try again.');
      }
    }, 'image/jpeg', 0.9);
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      setError('Please select a file first');
      return;
    }

    setUploading(true);
    setError(null);

    try {
      const serviceClient = getServiceRoleClient();

      // Generate unique filename
      const fileExt = selectedFile.name.split('.').pop();
      const timestamp = Date.now();
      const fileName = `kyc_resubmission_${fieldName}_${kycId}_${timestamp}.${fileExt}`;

      // Upload to Supabase storage
      const { data: uploadData, error: uploadError } = await serviceClient.storage
        .from('proof')
        .upload(fileName, selectedFile, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        throw new Error(`Upload failed: ${uploadError.message}`);
      }

      // Get public URL
      const { data: { publicUrl } } = serviceClient.storage
        .from('proof')
        .getPublicUrl(fileName);

      // Map field names to document types (matching database constraint)
      const documentTypeMap: Record<string, string> = {
        'id_document': 'id_document',
        'selfie_verification': 'selfie',
        'proof_address': 'proof_of_address'
      };

      const documentType = documentTypeMap[fieldName];
      if (!documentType) {
        throw new Error(`Unknown field name: ${fieldName}`);
      }

      console.log(`📄 Mapping field '${fieldName}' to document type '${documentType}'`);

      // Check if document record already exists
      const { data: existingDoc, error: checkError } = await serviceClient
        .from('kyc_documents')
        .select('id')
        .eq('kyc_id', kycId)
        .eq('document_type', documentType)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        throw new Error(`Failed to check existing document: ${checkError.message}`);
      }

      // Get user_id from the KYC record
      const { data: kycRecord, error: kycError } = await serviceClient
        .from('kyc_information')
        .select('user_id')
        .eq('id', kycId)
        .single();

      if (kycError || !kycRecord) {
        throw new Error(`Failed to get KYC record: ${kycError?.message || 'KYC record not found'}`);
      }

      if (existingDoc) {
        // Update existing document record
        const { error: updateError } = await serviceClient
          .from('kyc_documents')
          .update({
            file_name: fileName,
            storage_path: fileName,
            url: publicUrl,
            file_size: selectedFile.size,
            file_type: selectedFile.type,
            verification_status: 'pending',
            uploaded_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', existingDoc.id);

        if (updateError) {
          throw new Error(`Failed to update document record: ${updateError.message}`);
        }
      } else {
        // Create new document record
        const { error: insertError } = await serviceClient
          .from('kyc_documents')
          .insert({
            kyc_id: kycId,
            user_id: kycRecord.user_id,
            document_type: documentType,
            file_name: fileName,
            storage_path: fileName,
            url: publicUrl,
            file_size: selectedFile.size,
            file_type: selectedFile.type,
            verification_status: 'pending',
            uploaded_at: new Date().toISOString()
          });

        if (insertError) {
          throw new Error(`Failed to create document record: ${insertError.message}`);
        }
      }

      // Update field approval status back to pending with new timestamp
      const { error: fieldError } = await serviceClient
        .from('kyc_field_approvals')
        .update({
          approval_status: 'pending',
          admin_notes: `Document resubmitted by user - ${selectedFile.name}`,
          updated_at: new Date().toISOString()
        })
        .eq('kyc_id', kycId)
        .eq('field_name', fieldName);

      if (fieldError) {
        throw new Error(`Failed to update field status: ${fieldError.message}`);
      }

      // Update overall KYC status back to pending when new documents are uploaded
      const { error: kycStatusError } = await serviceClient
        .from('kyc_information')
        .update({
          kyc_status: 'pending',
          updated_at: new Date().toISOString()
        })
        .eq('id', kycId);

      if (kycStatusError) {
        console.error('⚠️ Warning: Failed to update overall KYC status:', kycStatusError);
        // Don't throw error here - field update was successful, this is just a status update
      } else {
        console.log('✅ Overall KYC status updated to pending for resubmission');
      }

      // Log the resubmission action
      await serviceClient.rpc('log_kyc_action', {
        p_kyc_id: kycId,
        p_user_id: null, // Will be filled by the RPC function
        p_action: `resubmitted_${fieldName}`,
        p_performed_by_username: 'user_dashboard'
      });

      alert(`${fieldDisplayName} uploaded successfully! Your document has been resubmitted for review.`);
      onComplete();

    } catch (error) {
      console.error('❌ Upload error:', error);
      setError(error instanceof Error ? error.message : 'Upload failed. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  const getFieldIcon = () => {
    switch (fieldName) {
      case 'id_document': return '🆔';
      case 'selfie_verification': return '🤳';
      case 'proof_address': return '🏠';
      default: return '📄';
    }
  };

  const getFieldDescription = () => {
    switch (fieldName) {
      case 'id_document': 
        return 'Upload a clear photo of your government-issued ID (passport, driver\'s license, or national ID card). Ensure all text is readable and the document is not expired.';
      case 'selfie_verification':
        return 'Upload a clear selfie photo of yourself. Make sure your face is clearly visible, well-lit, and matches your ID document.';
      case 'proof_address':
        return 'Upload a recent utility bill, bank statement, or official document showing your current address. The document must be dated within the last 3 months.';
      default:
        return 'Upload the required document for verification.';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 max-w-md w-full mx-4">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-white flex items-center gap-3">
            <span className="text-2xl">{getFieldIcon()}</span>
            Upload {fieldDisplayName}
          </h3>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-white text-xl"
          >
            ×
          </button>
        </div>

        <div className="mb-6">
          <p className="text-gray-300 text-sm mb-4">
            {getFieldDescription()}
          </p>
        </div>

        {/* File Upload Area */}
        <div className="mb-6">
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*,application/pdf"
            onChange={handleFileSelect}
            className="hidden"
          />

          {showCamera ? (
            /* Camera Interface */
            <div className="bg-gray-900 rounded-lg p-4 border-2 border-blue-500">
              <div className="text-center mb-4">
                <h4 className="text-white font-medium">📷 Camera Active</h4>
                <p className="text-gray-400 text-sm">Position your {fieldDisplayName.toLowerCase()} in the frame and click capture</p>
              </div>

              <div className="relative bg-black rounded-lg overflow-hidden">
                <video
                  ref={videoRef}
                  autoPlay
                  playsInline
                  muted
                  className="w-full h-auto rounded-lg"
                  style={{
                    maxHeight: '400px',
                    minHeight: '200px',
                    objectFit: 'cover'
                  }}
                />
                <canvas ref={canvasRef} className="hidden" />

                {/* Camera overlay for guidance */}
                <div className="absolute inset-0 pointer-events-none">
                  <div className="absolute inset-4 border-2 border-white border-dashed rounded-lg opacity-50"></div>
                </div>
              </div>

              <div className="flex justify-center gap-3 mt-4">
                <button
                  onClick={capturePhoto}
                  className="px-6 py-3 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors flex items-center gap-2"
                >
                  📷 Capture Photo
                </button>
                <button
                  onClick={stopCamera}
                  className="px-6 py-3 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors flex items-center gap-2"
                >
                  ❌ Cancel
                </button>
              </div>
            </div>
          ) : !selectedFile ? (
            /* Upload Options */
            <div className="space-y-4">
              <div className="border-2 border-dashed border-gray-600 rounded-lg p-6 text-center">
                <div className="text-4xl mb-2">{getFieldIcon()}</div>
                <p className="text-gray-400 mb-4">Choose how to upload your document</p>

                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  {cameraSupported && (
                    <button
                      onClick={startCamera}
                      className="px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center justify-center gap-2"
                    >
                      📷 Take Photo
                    </button>
                  )}
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center justify-center gap-2"
                  >
                    📁 Choose File
                  </button>
                </div>

                <p className="text-xs text-gray-500 mt-3">JPEG, PNG, PDF • Max 10MB</p>
              </div>
            </div>
          ) : (
            /* Selected File Display */
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{getFieldIcon()}</span>
                  <div>
                    <p className="text-white font-medium">{selectedFile.name}</p>
                    <p className="text-gray-400 text-sm">
                      {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => {
                    setSelectedFile(null);
                    setError(null);
                  }}
                  className="text-red-400 hover:text-red-300"
                >
                  Remove
                </button>
              </div>
            </div>
          )}
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-600/20 border border-red-600/30 rounded-lg">
            <p className="text-red-400 text-sm">{error}</p>
          </div>
        )}

        {/* Action Buttons */}
        {!showCamera && (
          <div className="flex gap-3">
            <button
              onClick={onCancel}
              className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleUpload}
              disabled={!selectedFile || uploading}
              className="flex-1 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
            >
              {uploading ? 'Saving...' : '💾 Save Document'}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default KYCDocumentResubmission;
