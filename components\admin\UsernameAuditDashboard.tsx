import React, { useState, useEffect } from 'react';
import { getServiceRoleClient } from '../../lib/supabase';

interface UsernameInconsistency {
  user_id: number;
  current_username: string;
  table_name: string;
  column_name: string;
  incorrect_value: string;
  record_id?: string;
  severity: 'high' | 'medium' | 'low';
}

interface AuditResult {
  total_users_checked: number;
  inconsistencies_found: number;
  tables_audited: string[];
  inconsistencies: UsernameInconsistency[];
  audit_duration_ms: number;
}

interface UsernameAuditDashboardProps {
  onClose?: () => void;
}

export const UsernameAuditDashboard: React.FC<UsernameAuditDashboardProps> = ({ onClose }) => {
  const [auditResult, setAuditResult] = useState<AuditResult | null>(null);
  const [isRunningAudit, setIsRunningAudit] = useState(false);
  const [isFixingInconsistencies, setIsFixingInconsistencies] = useState(false);
  const [fixingUserIds, setFixingUserIds] = useState<Set<number>>(new Set());
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);
  const [auditLogs, setAuditLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setAuditLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  const runUsernameAudit = async () => {
    setIsRunningAudit(true);
    setMessage(null);
    setAuditLogs([]);
    addLog('🔍 Starting comprehensive username audit...');

    try {
      const serviceClient = getServiceRoleClient();
      const startTime = Date.now();

      // Get all users as the source of truth
      addLog('📊 Fetching all users from users table...');
      const { data: users, error: usersError } = await serviceClient
        .from('users')
        .select('id, username, email')
        .order('id');

      if (usersError) {
        throw new Error(`Failed to fetch users: ${usersError.message}`);
      }

      addLog(`✅ Found ${users.length} users to audit`);

      const inconsistencies: UsernameInconsistency[] = [];
      const tablesAudited: string[] = [];

      // 1. Audit user_share_holdings table
      addLog('🔍 Auditing user_share_holdings table...');
      tablesAudited.push('user_share_holdings');
      
      const { data: shareHoldings, error: shareHoldingsError } = await serviceClient
        .from('user_share_holdings')
        .select('user_id, username');

      if (!shareHoldingsError && shareHoldings) {
        for (const holding of shareHoldings) {
          const user = users.find(u => u.id === holding.user_id);
          if (user && user.username !== holding.username) {
            inconsistencies.push({
              user_id: holding.user_id,
              current_username: user.username,
              table_name: 'user_share_holdings',
              column_name: 'username',
              incorrect_value: holding.username,
              severity: 'high'
            });
          }
        }
      }

      // 2. Audit telegram_users table
      addLog('🔍 Auditing telegram_users table...');
      tablesAudited.push('telegram_users');
      
      const { data: telegramUsers, error: telegramError } = await serviceClient
        .from('telegram_users')
        .select('id, user_id, username');

      if (!telegramError && telegramUsers) {
        for (const telegramUser of telegramUsers) {
          const user = users.find(u => u.id === telegramUser.user_id);
          if (user && user.username !== telegramUser.username) {
            inconsistencies.push({
              user_id: telegramUser.user_id,
              current_username: user.username,
              table_name: 'telegram_users',
              column_name: 'username',
              incorrect_value: telegramUser.username,
              record_id: telegramUser.id.toString(),
              severity: 'medium'
            });
          }
        }
      }

      // 3. Audit competition_leaderboard table
      addLog('🔍 Auditing competition_leaderboard table...');
      tablesAudited.push('competition_leaderboard');
      
      const { data: leaderboard, error: leaderboardError } = await serviceClient
        .from('competition_leaderboard')
        .select('id, user_id, username');

      if (!leaderboardError && leaderboard) {
        for (const entry of leaderboard) {
          const user = users.find(u => u.id === entry.user_id);
          if (user && user.username !== entry.username) {
            inconsistencies.push({
              user_id: entry.user_id,
              current_username: user.username,
              table_name: 'competition_leaderboard',
              column_name: 'username',
              incorrect_value: entry.username,
              record_id: entry.id.toString(),
              severity: 'medium'
            });
          }
        }
      }

      // 4. Audit admin_commission_conversion_queue table
      addLog('🔍 Auditing admin_commission_conversion_queue table...');
      tablesAudited.push('admin_commission_conversion_queue');
      
      const { data: commissionQueue, error: commissionError } = await serviceClient
        .from('admin_commission_conversion_queue')
        .select('id, user_id, username');

      if (!commissionError && commissionQueue) {
        for (const entry of commissionQueue) {
          const user = users.find(u => u.id === entry.user_id);
          if (user && user.username !== entry.username) {
            inconsistencies.push({
              user_id: entry.user_id,
              current_username: user.username,
              table_name: 'admin_commission_conversion_queue',
              column_name: 'username',
              incorrect_value: entry.username,
              record_id: entry.id.toString(),
              severity: 'low'
            });
          }
        }
      }

      // 5. Audit email_sync_audit_log table
      addLog('🔍 Auditing email_sync_audit_log table...');
      tablesAudited.push('email_sync_audit_log');
      
      const { data: emailAuditLog, error: emailAuditError } = await serviceClient
        .from('email_sync_audit_log')
        .select('id, user_id, username');

      if (!emailAuditError && emailAuditLog) {
        for (const entry of emailAuditLog) {
          const user = users.find(u => u.id === entry.user_id);
          if (user && user.username !== entry.username) {
            inconsistencies.push({
              user_id: entry.user_id,
              current_username: user.username,
              table_name: 'email_sync_audit_log',
              column_name: 'username',
              incorrect_value: entry.username,
              record_id: entry.id.toString(),
              severity: 'low'
            });
          }
        }
      }

      // 6. Audit email_sync_backup table
      addLog('🔍 Auditing email_sync_backup table...');
      tablesAudited.push('email_sync_backup');
      
      const { data: emailBackup, error: emailBackupError } = await serviceClient
        .from('email_sync_backup')
        .select('id, user_id, username');

      if (!emailBackupError && emailBackup) {
        for (const entry of emailBackup) {
          const user = users.find(u => u.id === entry.user_id);
          if (user && user.username !== entry.username) {
            inconsistencies.push({
              user_id: entry.user_id,
              current_username: user.username,
              table_name: 'email_sync_backup',
              column_name: 'username',
              incorrect_value: entry.username,
              record_id: entry.id.toString(),
              severity: 'low'
            });
          }
        }
      }

      // 7. Audit referrals table for username-based referral codes
      addLog('🔍 Auditing referrals table for username-based codes...');
      tablesAudited.push('referrals');
      
      const { data: referrals, error: referralsError } = await serviceClient
        .from('referrals')
        .select('id, referrer_id, referral_code');

      if (!referralsError && referrals) {
        for (const referral of referrals) {
          const user = users.find(u => u.id === referral.referrer_id);
          if (user && referral.referral_code) {
            // Check if referral code contains an outdated username
            const codeContainsOldUsername = users.some(u => 
              u.id !== user.id && 
              u.username && 
              referral.referral_code.includes(u.username) &&
              !referral.referral_code.includes(user.username)
            );
            
            if (codeContainsOldUsername || (!referral.referral_code.includes(user.username) && referral.referral_code.includes('_'))) {
              inconsistencies.push({
                user_id: referral.referrer_id,
                current_username: user.username,
                table_name: 'referrals',
                column_name: 'referral_code',
                incorrect_value: referral.referral_code,
                record_id: referral.id.toString(),
                severity: 'high'
              });
            }
          }
        }
      }

      const auditDuration = Date.now() - startTime;
      
      const result: AuditResult = {
        total_users_checked: users.length,
        inconsistencies_found: inconsistencies.length,
        tables_audited: tablesAudited,
        inconsistencies: inconsistencies,
        audit_duration_ms: auditDuration
      };

      setAuditResult(result);
      
      if (inconsistencies.length === 0) {
        addLog('✅ Audit complete - No inconsistencies found!');
        setMessage({ type: 'success', text: `Audit complete! No username inconsistencies found across ${tablesAudited.length} tables.` });
      } else {
        addLog(`⚠️ Audit complete - Found ${inconsistencies.length} inconsistencies`);
        setMessage({ type: 'info', text: `Audit complete! Found ${inconsistencies.length} username inconsistencies that need fixing.` });
      }

    } catch (error) {
      console.error('Username audit failed:', error);
      addLog(`❌ Audit failed: ${error.message}`);
      setMessage({ type: 'error', text: `Audit failed: ${error.message}` });
    } finally {
      setIsRunningAudit(false);
    }
  };

  const fixSingleInconsistency = async (inconsistency: UsernameInconsistency) => {
    setFixingUserIds(prev => new Set([...prev, inconsistency.user_id]));
    
    try {
      const serviceClient = getServiceRoleClient();
      
      addLog(`🔧 Fixing inconsistency for user ${inconsistency.user_id} in ${inconsistency.table_name}...`);
      
      // Use the comprehensive update function
      const { data: result, error } = await serviceClient.rpc('update_username_comprehensive', {
        p_user_id: inconsistency.user_id,
        p_new_username: inconsistency.current_username
      });

      if (error) {
        throw new Error(error.message);
      }

      addLog(`✅ Fixed inconsistency for user ${inconsistency.user_id}`);
      
      // Remove this inconsistency from the results
      setAuditResult(prev => prev ? {
        ...prev,
        inconsistencies: prev.inconsistencies.filter(inc => 
          !(inc.user_id === inconsistency.user_id && inc.table_name === inconsistency.table_name)
        ),
        inconsistencies_found: prev.inconsistencies_found - 1
      } : null);

    } catch (error) {
      console.error('Fix failed:', error);
      addLog(`❌ Failed to fix inconsistency for user ${inconsistency.user_id}: ${error.message}`);
    } finally {
      setFixingUserIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(inconsistency.user_id);
        return newSet;
      });
    }
  };

  const fixAllInconsistencies = async () => {
    if (!auditResult || auditResult.inconsistencies.length === 0) return;

    setIsFixingInconsistencies(true);
    addLog('🔧 Starting bulk fix for all inconsistencies...');

    try {
      const serviceClient = getServiceRoleClient();
      
      // Group inconsistencies by user_id to avoid duplicate fixes
      const userIds = [...new Set(auditResult.inconsistencies.map(inc => inc.user_id))];
      
      for (const userId of userIds) {
        const userInconsistencies = auditResult.inconsistencies.filter(inc => inc.user_id === userId);
        const currentUsername = userInconsistencies[0].current_username;
        
        addLog(`🔧 Fixing all inconsistencies for user ${userId} (${currentUsername})...`);
        
        const { data: result, error } = await serviceClient.rpc('update_username_comprehensive', {
          p_user_id: userId,
          p_new_username: currentUsername
        });

        if (error) {
          addLog(`❌ Failed to fix user ${userId}: ${error.message}`);
        } else {
          addLog(`✅ Fixed all inconsistencies for user ${userId}`);
        }
      }

      // Clear all inconsistencies from results
      setAuditResult(prev => prev ? {
        ...prev,
        inconsistencies: [],
        inconsistencies_found: 0
      } : null);

      addLog('✅ Bulk fix complete!');
      setMessage({ type: 'success', text: 'All username inconsistencies have been fixed!' });

    } catch (error) {
      console.error('Bulk fix failed:', error);
      addLog(`❌ Bulk fix failed: ${error.message}`);
      setMessage({ type: 'error', text: `Bulk fix failed: ${error.message}` });
    } finally {
      setIsFixingInconsistencies(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'text-red-400 bg-red-900/20';
      case 'medium': return 'text-yellow-400 bg-yellow-900/20';
      case 'low': return 'text-blue-400 bg-blue-900/20';
      default: return 'text-gray-400 bg-gray-900/20';
    }
  };

  return (
    <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-bold text-white mb-2">Username Audit Dashboard</h2>
          <p className="text-gray-400 text-sm">
            Comprehensive username consistency check across all database tables
          </p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            ✕
          </button>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex gap-4 mb-6">
        <button
          onClick={runUsernameAudit}
          disabled={isRunningAudit}
          className={`px-6 py-3 rounded-lg font-medium transition-colors ${
            isRunningAudit
              ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {isRunningAudit ? (
            <span className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              Running Audit...
            </span>
          ) : (
            '🔍 Run Username Audit'
          )}
        </button>

        {auditResult && auditResult.inconsistencies.length > 0 && (
          <button
            onClick={fixAllInconsistencies}
            disabled={isFixingInconsistencies}
            className={`px-6 py-3 rounded-lg font-medium transition-colors ${
              isFixingInconsistencies
                ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                : 'bg-green-600 hover:bg-green-700 text-white'
            }`}
          >
            {isFixingInconsistencies ? (
              <span className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Fixing All...
              </span>
            ) : (
              `🔧 Fix All ${auditResult.inconsistencies.length} Inconsistencies`
            )}
          </button>
        )}
      </div>

      {/* Message Display */}
      {message && (
        <div className={`p-4 rounded-lg mb-6 ${
          message.type === 'success' ? 'bg-green-900/20 border border-green-700 text-green-400' :
          message.type === 'error' ? 'bg-red-900/20 border border-red-700 text-red-400' :
          'bg-blue-900/20 border border-blue-700 text-blue-400'
        }`}>
          {message.text}
        </div>
      )}

      {/* Audit Results Summary */}
      {auditResult && (
        <div className="bg-gray-700/50 rounded-lg p-4 mb-6">
          <h3 className="text-white font-semibold mb-3">Audit Results Summary</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="text-gray-400">Users Checked</div>
              <div className="text-white font-semibold">{auditResult.total_users_checked}</div>
            </div>
            <div>
              <div className="text-gray-400">Tables Audited</div>
              <div className="text-white font-semibold">{auditResult.tables_audited.length}</div>
            </div>
            <div>
              <div className="text-gray-400">Inconsistencies Found</div>
              <div className={`font-semibold ${auditResult.inconsistencies_found > 0 ? 'text-red-400' : 'text-green-400'}`}>
                {auditResult.inconsistencies_found}
              </div>
            </div>
            <div>
              <div className="text-gray-400">Audit Duration</div>
              <div className="text-white font-semibold">{(auditResult.audit_duration_ms / 1000).toFixed(2)}s</div>
            </div>
          </div>
          
          <div className="mt-3">
            <div className="text-gray-400 text-xs">Tables Audited:</div>
            <div className="text-white text-xs">{auditResult.tables_audited.join(', ')}</div>
          </div>
        </div>
      )}

      {/* Inconsistencies Table */}
      {auditResult && auditResult.inconsistencies.length > 0 && (
        <div className="bg-gray-700/50 rounded-lg p-4 mb-6">
          <h3 className="text-white font-semibold mb-4">Username Inconsistencies Found</h3>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-600">
                  <th className="text-left text-gray-400 pb-2">User ID</th>
                  <th className="text-left text-gray-400 pb-2">Current Username</th>
                  <th className="text-left text-gray-400 pb-2">Table</th>
                  <th className="text-left text-gray-400 pb-2">Incorrect Value</th>
                  <th className="text-left text-gray-400 pb-2">Severity</th>
                  <th className="text-left text-gray-400 pb-2">Action</th>
                </tr>
              </thead>
              <tbody>
                {auditResult.inconsistencies.map((inconsistency, index) => (
                  <tr key={`${inconsistency.user_id}-${inconsistency.table_name}-${index}`} className="border-b border-gray-700/50">
                    <td className="py-3 text-white font-mono">{inconsistency.user_id}</td>
                    <td className="py-3 text-green-400 font-mono">{inconsistency.current_username}</td>
                    <td className="py-3 text-blue-400">{inconsistency.table_name}</td>
                    <td className="py-3 text-red-400 font-mono">{inconsistency.incorrect_value}</td>
                    <td className="py-3">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getSeverityColor(inconsistency.severity)}`}>
                        {inconsistency.severity.toUpperCase()}
                      </span>
                    </td>
                    <td className="py-3">
                      <button
                        onClick={() => fixSingleInconsistency(inconsistency)}
                        disabled={fixingUserIds.has(inconsistency.user_id)}
                        className={`px-3 py-1 rounded text-xs font-medium transition-colors ${
                          fixingUserIds.has(inconsistency.user_id)
                            ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                            : 'bg-yellow-600 hover:bg-yellow-700 text-white'
                        }`}
                      >
                        {fixingUserIds.has(inconsistency.user_id) ? (
                          <span className="flex items-center gap-1">
                            <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin"></div>
                            Fixing...
                          </span>
                        ) : (
                          '🔧 Fix'
                        )}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Success Message for No Inconsistencies */}
      {auditResult && auditResult.inconsistencies.length === 0 && (
        <div className="bg-green-900/20 border border-green-700 rounded-lg p-6 mb-6 text-center">
          <div className="text-green-400 text-4xl mb-3">✅</div>
          <h3 className="text-green-400 font-semibold text-lg mb-2">All Clear!</h3>
          <p className="text-green-300">
            No username inconsistencies found across {auditResult.tables_audited.length} database tables.
            All {auditResult.total_users_checked} users have consistent usernames.
          </p>
        </div>
      )}

      {/* Audit Logs */}
      {auditLogs.length > 0 && (
        <div className="bg-gray-900/50 rounded-lg p-4">
          <h3 className="text-white font-semibold mb-3">Audit Logs</h3>
          <div className="bg-black/50 rounded p-3 max-h-64 overflow-y-auto">
            <div className="font-mono text-xs space-y-1">
              {auditLogs.map((log, index) => (
                <div key={index} className={`${
                  log.includes('❌') ? 'text-red-400' :
                  log.includes('✅') ? 'text-green-400' :
                  log.includes('⚠️') ? 'text-yellow-400' :
                  log.includes('🔍') ? 'text-blue-400' :
                  log.includes('🔧') ? 'text-purple-400' :
                  'text-gray-300'
                }`}>
                  {log}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Help Section */}
      <div className="mt-6 p-4 bg-blue-900/20 border border-blue-700 rounded-lg">
        <h4 className="text-blue-400 font-semibold mb-2">ℹ️ How This Works</h4>
        <div className="text-blue-300 text-sm space-y-2">
          <p><strong>Audit Process:</strong> Compares usernames in the main 'users' table against all related tables to find inconsistencies.</p>
          <p><strong>Severity Levels:</strong></p>
          <ul className="ml-4 space-y-1">
            <li><span className="text-red-400">HIGH</span> - Critical tables like user_share_holdings, referrals (affects functionality)</li>
            <li><span className="text-yellow-400">MEDIUM</span> - Important tables like telegram_users, competition_leaderboard</li>
            <li><span className="text-blue-400">LOW</span> - Audit/backup tables that don't affect core functionality</li>
          </ul>
          <p><strong>Fix Process:</strong> Uses the comprehensive username update function to synchronize all tables atomically.</p>
        </div>
      </div>
    </div>
  );
};
