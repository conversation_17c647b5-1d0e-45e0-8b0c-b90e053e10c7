# Payment Management System - Web Admin Panel

## 🎯 Overview

The Payment Management System provides a comprehensive web-based interface for admins to review, approve, and reject cryptocurrency payments, matching the functionality of the Telegram bot admin panel.

## 🚀 Features

### ✅ **Payment Review Dashboard**
- **Tabbed Interface**: Pending, Approved, Rejected payments
- **Real-time Data**: Auto-refresh and live updates
- **Detailed Information**: User details, payment amounts, transaction hashes
- **Visual Status Indicators**: Color-coded status badges
- **Time Tracking**: Shows time elapsed since payment submission

### ✅ **Payment Approval System**
- **One-Click Approval**: Instant payment processing
- **Automatic Share Allocation**: Calculates and assigns shares based on current phase price
- **Commission Processing**: Automatically processes 15% USDT + 15% share commissions
- **Phase Management**: Updates share purchase phase sold counts
- **User Notifications**: (Ready for integration with notification system)

### ✅ **Payment Rejection System**
- **Custom Rejection Reasons**: Admins must provide detailed rejection reasons
- **User Communication**: Rejection messages are stored for user notification
- **Audit Trail**: Complete tracking of who rejected what and when

### ✅ **Security & Validation**
- **Admin Authentication**: Only authenticated admin users can access
- **Transaction Integrity**: Atomic operations ensure data consistency
- **Error Handling**: Comprehensive error handling and user feedback
- **Audit Logging**: Complete audit trail of all admin actions

## 🏗️ Technical Architecture

### **Database Integration**
```sql
-- Main payment table
crypto_payment_transactions
├── Basic payment info (amount, network, wallets, etc.)
├── Admin tracking (approved_by, rejected_by, timestamps)
├── Status management (pending, approved, rejected)
└── Audit fields (verification_status, admin_notes)

-- Related tables
├── users (user information)
├── share_purchase_phases (current pricing)
├── aureus_share_purchases (share allocation)
├── commission_balances (referral commissions)
└── commission_transactions (commission tracking)
```

### **Component Structure**
```typescript
PaymentManager
├── State Management (payments, loading, messages)
├── Tab Navigation (pending, approved, rejected)
├── Payment List (formatted display with actions)
├── Approval System (one-click processing)
├── Rejection Modal (custom reason input)
└── Real-time Updates (auto-refresh)
```

## 🔄 Payment Processing Workflow

### **Approval Process**
1. **Admin clicks "Approve"** → Triggers approval workflow
2. **Get current phase** → Fetches active share purchase phase
3. **Calculate shares** → `shares = floor(amount / phase_price)`
4. **Update payment status** → Mark as approved with admin ID and timestamp
5. **Create share purchase** → Record in `aureus_share_purchases` table
6. **Update phase sold count** → Increment shares sold in current phase
7. **Process commissions** → Calculate and distribute referral commissions
8. **Success feedback** → Show confirmation to admin

### **Rejection Process**
1. **Admin clicks "Reject"** → Opens rejection modal
2. **Enter rejection reason** → Minimum 5 characters required
3. **Confirm rejection** → Updates payment status with reason
4. **Audit logging** → Records admin ID, timestamp, and reason
5. **User notification** → (Ready for notification system integration)

### **Commission Processing**
```javascript
// Automatic commission calculation (matches Telegram bot)
const usdtCommission = paymentAmount * 0.15;  // 15% USDT
const shareCommission = sharesAmount * 0.15;  // 15% shares

// Updates referrer's commission balance
commission_balances: {
  usdt_balance: +usdtCommission,
  share_balance: +shareCommission,
  total_earned_usdt: +usdtCommission,
  total_earned_shares: +shareCommission
}
```

## 📊 Admin Interface Features

### **Payment List Display**
- **Network Icons**: Visual indicators for BSC (🟡), Polygon (🟣), Tron (🔴), Bank (🏦)
- **Status Badges**: Color-coded status indicators
- **User Information**: Username, email, full name
- **Payment Details**: Amount, shares, network, transaction hash
- **Time Tracking**: "X hours ago" display
- **Action Buttons**: Approve, Reject, View Screenshot

### **Filtering & Navigation**
- **Tab System**: Easy switching between payment statuses
- **Auto-refresh**: Manual refresh button with loading states
- **Responsive Design**: Works on desktop and mobile
- **Error Handling**: Clear error messages and recovery options

## 🔧 Setup Instructions

### **1. Database Migration**
```bash
# Run the payment management migration
node scripts/run-payment-migration.js
```

### **2. Admin Access**
- Navigate to `/admin` in your web application
- Login with admin credentials
- Click on "💰 Payments" tab

### **3. Environment Variables**
```env
REACT_APP_SUPABASE_URL=your_supabase_url
REACT_APP_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_key
```

## 🎯 Usage Guide

### **For Admins**

#### **Reviewing Pending Payments**
1. Go to Admin Panel → Payments
2. Default view shows "Pending" payments
3. Review payment details and user information
4. Click "📷 View Screenshot" to see payment proof
5. Click "✅ Approve" or "❌ Reject"

#### **Approving Payments**
1. Click "✅ Approve" on any pending payment
2. System automatically:
   - Calculates shares based on current phase price
   - Creates share purchase record
   - Updates phase sold count
   - Processes referral commissions
   - Updates payment status
3. Success message confirms completion

#### **Rejecting Payments**
1. Click "❌ Reject" on any pending payment
2. Enter detailed rejection reason (minimum 5 characters)
3. Click "Confirm Rejection"
4. User will receive rejection notification with reason

#### **Viewing Payment History**
- **Approved Tab**: See all successfully processed payments
- **Rejected Tab**: Review rejected payments with reasons
- **Refresh Button**: Get latest payment updates

## 🔒 Security Features

- **Admin Authentication**: Only authenticated admins can access
- **Audit Trail**: Complete logging of all admin actions
- **Transaction Integrity**: Atomic database operations
- **Error Recovery**: Graceful error handling and rollback
- **Input Validation**: Comprehensive validation of all inputs

## 🚀 Integration with Telegram Bot

The web admin panel **perfectly matches** the Telegram bot functionality:

- ✅ **Same approval logic** - Identical share calculations and commission processing
- ✅ **Same database tables** - Uses exact same data structures
- ✅ **Same validation rules** - Consistent business logic
- ✅ **Same audit trail** - Compatible logging and tracking
- ✅ **Same commission system** - 15% USDT + 15% shares

## 📈 Future Enhancements

- **Real-time notifications** - WebSocket integration for live updates
- **Bulk operations** - Approve/reject multiple payments at once
- **Advanced filtering** - Filter by amount, network, user, date range
- **Export functionality** - Download payment reports
- **Two-factor approval** - Additional security for large payments
- **Payment analytics** - Dashboard with payment statistics

## 🐛 Troubleshooting

### **Common Issues**

1. **"No active investment phase found"**
   - Ensure at least one phase has `is_active = true` in `share_purchase_phases`

2. **"Admin user not found"**
   - Verify admin is properly authenticated and has `database_user.id`

3. **Commission processing errors**
   - Check that `commission_balances` and `commission_transactions` tables exist
   - Verify referral relationships exist in `referrals` table

4. **Database connection issues**
   - Verify Supabase credentials and permissions
   - Check that all required tables exist

### **Debug Mode**
Enable debug logging by adding to your environment:
```env
REACT_APP_DEBUG_PAYMENTS=true
```

This comprehensive payment management system provides admins with a powerful, user-friendly interface to manage all cryptocurrency payments efficiently and securely! 🎉
