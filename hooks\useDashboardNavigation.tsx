import { useState, useCallback } from 'react';

export type DashboardSection =
  | 'overview'
  | 'purchase-shares'
  | 'portfolio'
  | 'referrals'
  | 'payments'
  | 'notifications'
  | 'company-presentation'
  | 'mining-operations'
  | 'community-relations'
  | 'support-center'
  | 'settings'
  | 'kyc'
  | 'legal-documents'
  | 'dividends';

export interface NavigationItem {
  id: DashboardSection;
  label: string;
  icon: React.ComponentType;
  description?: string;
  badge?: number;
  isNew?: boolean;
}

export interface UseDashboardNavigationReturn {
  activeSection: DashboardSection;
  setActiveSection: (section: DashboardSection) => void;
  navigationItems: NavigationItem[];
  shareholderNavigationItems: NavigationItem[];
  getNavigationItem: (id: DashboardSection) => NavigationItem | undefined;
  isValidSection: (section: string) => section is DashboardSection;
}

// Icons (these would be imported from a separate icons file in a real refactor)
const DashboardIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
  </svg>
);

const SharesIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 5a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H7z" clipRule="evenodd"/>
  </svg>
);

const PortfolioIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
  </svg>
);

const ReferralIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
  </svg>
);

const PaymentIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
  </svg>
);

const NotificationIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5z" />
  </svg>
);

const SettingsIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
  </svg>
);

const DividendsIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
  </svg>
);

export const useDashboardNavigation = (
  initialSection: DashboardSection = 'overview',
  onNavigate?: (section: DashboardSection) => void
): UseDashboardNavigationReturn => {
  const [activeSection, setActiveSectionState] = useState<DashboardSection>(initialSection);

  const setActiveSection = useCallback((section: DashboardSection) => {
    setActiveSectionState(section);
    onNavigate?.(section);
  }, [onNavigate]);

  const shareholderNavigationItems: NavigationItem[] = [
    {
      id: 'overview',
      label: 'Dashboard Overview',
      icon: DashboardIcon,
      description: 'Your investment summary and key metrics'
    },
    {
      id: 'purchase-shares',
      label: 'Purchase Shares',
      icon: SharesIcon,
      description: 'Buy shares in current investment phase'
    },
    {
      id: 'portfolio',
      label: 'Portfolio',
      icon: PortfolioIcon,
      description: 'View your share holdings and certificates'
    },
    {
      id: 'dividends',
      label: 'Dividends',
      icon: DividendsIcon,
      description: 'Calculate and track dividend projections'
    },
    {
      id: 'referrals',
      label: 'Referrals',
      icon: ReferralIcon,
      description: 'Manage referrals and commission withdrawals'
    },
    {
      id: 'payments',
      label: 'Payments',
      icon: PaymentIcon,
      description: 'Payment history and transaction records'
    },
    {
      id: 'notifications',
      label: 'Notifications',
      icon: NotificationIcon,
      description: 'Important updates and announcements'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: SettingsIcon,
      description: 'Account settings and preferences'
    }
  ];

  const navigationItems = shareholderNavigationItems;

  const getNavigationItem = useCallback((id: DashboardSection): NavigationItem | undefined => {
    return navigationItems.find(item => item.id === id);
  }, [navigationItems]);

  const isValidSection = useCallback((section: string): section is DashboardSection => {
    return navigationItems.some(item => item.id === section);
  }, [navigationItems]);

  return {
    activeSection,
    setActiveSection,
    navigationItems,
    shareholderNavigationItems,
    getNavigationItem,
    isValidSection
  };
};
