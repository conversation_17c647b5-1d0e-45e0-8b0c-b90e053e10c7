import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function debugUserLookup() {
  console.log('🔍 Debugging user lookup for telegram sync request...\n');
  
  const testUserId = 4;
  const testEmail = '<EMAIL>';
  
  try {
    // Check all users in the table
    console.log('📋 ALL USERS IN DATABASE:');
    const { data: allUsers, error: allUsersError } = await supabase
      .from('users')
      .select('id, email, username, full_name, created_at')
      .order('id');
    
    if (allUsersError) {
      console.error('❌ Error fetching all users:', allUsersError);
    } else {
      console.log(`Found ${allUsers.length} users:`);
      allUsers.forEach(user => {
        console.log(`  ID: ${user.id}, Email: ${user.email}, Username: ${user.username}, Name: ${user.full_name}`);
      });
    }
    
    console.log('\n🔍 SPECIFIC USER LOOKUP (ID + EMAIL):');
    // Try the exact query from the API
    const { data: specificUser, error: specificError } = await supabase
      .from('users')
      .select('id, email, full_name, username')
      .eq('id', parseInt(testUserId))
      .eq('email', testEmail)
      .single();
    
    if (specificError) {
      console.error('❌ Specific user lookup error:', specificError);
    } else {
      console.log('✅ Found specific user:', specificUser);
    }
    
    console.log('\n🔍 USER BY ID ONLY:');
    const { data: userById, error: userByIdError } = await supabase
      .from('users')
      .select('id, email, full_name, username')
      .eq('id', parseInt(testUserId))
      .single();
    
    if (userByIdError) {
      console.error('❌ User by ID error:', userByIdError);
    } else {
      console.log('✅ Found user by ID:', userById);
    }
    
    console.log('\n🔍 USER BY EMAIL ONLY:');
    const { data: userByEmail, error: userByEmailError } = await supabase
      .from('users')
      .select('id, email, full_name, username')
      .eq('email', testEmail)
      .single();
    
    if (userByEmailError) {
      console.error('❌ User by email error:', userByEmailError);
    } else {
      console.log('✅ Found user by email:', userByEmail);
    }
    
  } catch (error) {
    console.error('❌ Debug script error:', error);
  }
}

debugUserLookup();
