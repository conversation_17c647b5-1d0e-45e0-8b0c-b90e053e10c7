import { supabase } from './supabase'

export interface ReportHistoryEntry {
  id: string
  admin_user_id: string
  report_type: string
  filename: string
  filters: any
  transaction_count: number
  file_size_bytes: number
  generated_at: string
  download_count: number
  last_downloaded_at?: string
  created_at: string
}

export interface ReportFilters {
  startDate: string
  endDate: string
  userId: string
  transactionTypes: {
    purchases: boolean
    withdrawals: boolean
    conversions: boolean
    commissions: boolean
  }
  statusFilter: string
}

/**
 * Save a report generation record to the database
 */
export const saveReportHistory = async (
  adminUserId: string,
  reportType: string,
  filename: string,
  filters: ReportFilters,
  transactionCount: number,
  fileSizeBytes: number = 0
): Promise<string | null> => {
  try {
    const { data, error } = await supabase
      .from('report_history')
      .insert({
        admin_user_id: adminUserId,
        report_type: reportType,
        filename: filename,
        filters: filters,
        transaction_count: transactionCount,
        file_size_bytes: fileSizeBytes,
        download_count: 1 // First generation counts as first download
      })
      .select('id')
      .single()

    if (error) {
      console.error('Error saving report history:', error)
      return null
    }

    return data.id
  } catch (error) {
    console.error('Exception saving report history:', error)
    return null
  }
}

/**
 * Get recent reports for an admin user
 */
export const getRecentReports = async (
  adminUserId?: string,
  limit: number = 10
): Promise<ReportHistoryEntry[]> => {
  try {
    let query = supabase
      .from('report_history')
      .select('*')
      .order('generated_at', { ascending: false })
      .limit(limit)

    if (adminUserId) {
      query = query.eq('admin_user_id', adminUserId)
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching recent reports:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Exception fetching recent reports:', error)
    return []
  }
}

/**
 * Update download count for a report
 */
export const incrementReportDownload = async (reportId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('report_history')
      .update({
        download_count: supabase.raw('download_count + 1'),
        last_downloaded_at: new Date().toISOString()
      })
      .eq('id', reportId)

    if (error) {
      console.error('Error updating report download count:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Exception updating report download count:', error)
    return false
  }
}

/**
 * Format file size in human readable format
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Generate a standardized filename for reports
 */
export const generateReportFilename = (
  reportType: string,
  filters: ReportFilters,
  timestamp?: Date
): string => {
  const date = timestamp || new Date()
  const dateStr = date.toISOString().split('T')[0]
  const timeStr = date.toISOString().split('T')[1].split('.')[0].replace(/:/g, '-')
  
  const userFilter = filters.userId === 'all' ? 'All-Users' : `User-${filters.userId}`
  const dateRange = `${filters.startDate}_to_${filters.endDate}`
  
  // Get enabled transaction types
  const enabledTypes = Object.entries(filters.transactionTypes)
    .filter(([_, enabled]) => enabled)
    .map(([type, _]) => type.charAt(0).toUpperCase() + type.slice(1))
    .join('-')
  
  const statusFilter = filters.statusFilter !== 'all' ? `_${filters.statusFilter}` : ''
  
  return `${reportType}_${userFilter}_${dateRange}_${enabledTypes}${statusFilter}_${dateStr}_${timeStr}.xlsx`
}

/**
 * Validate report filters
 */
export const validateReportFilters = (filters: ReportFilters): string[] => {
  const errors: string[] = []

  // Check date range
  if (!filters.startDate || !filters.endDate) {
    errors.push('Start date and end date are required')
  } else if (new Date(filters.startDate) > new Date(filters.endDate)) {
    errors.push('Start date must be before end date')
  }

  // Check if at least one transaction type is selected
  const hasSelectedType = Object.values(filters.transactionTypes).some(Boolean)
  if (!hasSelectedType) {
    errors.push('At least one transaction type must be selected')
  }

  // Check date range is not too large (prevent performance issues)
  if (filters.startDate && filters.endDate) {
    const daysDiff = Math.ceil(
      (new Date(filters.endDate).getTime() - new Date(filters.startDate).getTime()) / (1000 * 60 * 60 * 24)
    )
    
    if (daysDiff > 365) {
      errors.push('Date range cannot exceed 365 days')
    }
  }

  return errors
}

/**
 * Get report statistics
 */
export const getReportStatistics = async (): Promise<{
  totalReports: number
  totalTransactions: number
  totalFileSize: number
  mostActiveAdmin: string | null
  averageTransactionsPerReport: number
}> => {
  try {
    const { data, error } = await supabase
      .from('report_history')
      .select('admin_user_id, transaction_count, file_size_bytes')

    if (error) {
      console.error('Error fetching report statistics:', error)
      return {
        totalReports: 0,
        totalTransactions: 0,
        totalFileSize: 0,
        mostActiveAdmin: null,
        averageTransactionsPerReport: 0
      }
    }

    const totalReports = data.length
    const totalTransactions = data.reduce((sum, report) => sum + report.transaction_count, 0)
    const totalFileSize = data.reduce((sum, report) => sum + report.file_size_bytes, 0)
    
    // Find most active admin
    const adminCounts: { [key: string]: number } = {}
    data.forEach(report => {
      adminCounts[report.admin_user_id] = (adminCounts[report.admin_user_id] || 0) + 1
    })
    
    const mostActiveAdmin = Object.keys(adminCounts).reduce((a, b) => 
      adminCounts[a] > adminCounts[b] ? a : b, null
    )
    
    const averageTransactionsPerReport = totalReports > 0 ? totalTransactions / totalReports : 0

    return {
      totalReports,
      totalTransactions,
      totalFileSize,
      mostActiveAdmin,
      averageTransactionsPerReport
    }
  } catch (error) {
    console.error('Exception fetching report statistics:', error)
    return {
      totalReports: 0,
      totalTransactions: 0,
      totalFileSize: 0,
      mostActiveAdmin: null,
      averageTransactionsPerReport: 0
    }
  }
}
