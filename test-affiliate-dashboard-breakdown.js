import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testAffiliateDashboardBreakdown() {
  console.log('🧪 TESTING AFFILIATE DASHBOARD BREAKDOWN');
  console.log('═══════════════════════════════════════════════════════════');
  
  const testUserId = 144; // User 144 as mentioned by user
  
  try {
    console.log(`\n🔍 Testing affiliate dashboard breakdown for User ${testUserId}...`);
    
    // 1. Load commission balances
    const { data: commissions, error: commError } = await supabase
      .from('commission_balances')
      .select('*')
      .eq('user_id', testUserId)
      .single();
    
    if (commError) throw commError;
    
    // 2. Load referral count
    const { data: referrals, error: refError } = await supabase
      .from('referrals')
      .select('id')
      .eq('referrer_id', testUserId);
    
    if (refError) throw refError;
    
    // 3. Load share purchases (excluding commission conversions)
    const { data: sharePurchases, error: purchaseError } = await supabase
      .from('aureus_share_purchases')
      .select('shares_purchased, total_amount, status, payment_method')
      .eq('user_id', testUserId)
      .eq('status', 'active');
    
    if (purchaseError) throw purchaseError;
    
    // 4. Load approved commission conversions
    const { data: conversions, error: convError } = await supabase
      .from('commission_conversions')
      .select('shares_requested, usdt_amount, status')
      .eq('user_id', testUserId)
      .eq('status', 'approved');
    
    if (convError) throw convError;
    
    // 5. Calculate breakdown (same logic as AffiliateDashboard.tsx)
    console.log('\n📊 CALCULATING AFFILIATE DASHBOARD BREAKDOWN:');
    
    // Calculate purchased shares (matching Telegram bot logic exactly)
    const purchasedShares = sharePurchases?.reduce((sum, purchase) => {
      // Exclude only 'Commission Conversion' (title case) as per Telegram bot
      if (purchase.status === 'active' && purchase.payment_method !== 'Commission Conversion') {
        return sum + (purchase.shares_purchased || 0);
      }
      return sum;
    }, 0) || 0;
    
    const totalConvertedShares = conversions?.reduce((sum, conversion) => sum + conversion.shares_requested, 0) || 0;
    const totalUsdtConverted = conversions?.reduce((sum, conversion) => sum + parseFloat(conversion.usdt_amount), 0) || 0;
    
    const commissionShares = commissions?.share_balance || 0;
    const totalSharesOwned = purchasedShares + commissionShares + totalConvertedShares;
    
    const referralCount = referrals?.length || 0;
    const usdtBalance = commissions?.usdt_balance || 0;
    const totalEarnedUsdt = commissions?.total_earned_usdt || 0;
    
    // 6. Display affiliate dashboard breakdown
    console.log('\n💎 TOTAL SHARE HOLDINGS:', totalSharesOwned.toFixed(2), 'shares');
    console.log('   (All active)');
    console.log('\n📈 SHARE BREAKDOWN:');
    console.log(`   • Shares Purchased: ${purchasedShares.toFixed(2)} shares`);
    console.log(`     └ $${(purchasedShares * 5).toFixed(2)} invested`);
    console.log(`   • Commission Converted to Shares: ${totalConvertedShares.toFixed(2)} shares`);
    console.log(`     └ $${totalUsdtConverted.toFixed(2)} commission converted`);
    console.log(`   • Shares Earned (Direct): ${commissionShares.toFixed(2)} shares`);
    console.log(`     └ Direct share commissions from referrals`);
    
    console.log('\n💰 COMMISSION STATUS:');
    console.log(`   • Available USDT Commission: $${usdtBalance.toFixed(2)}`);
    console.log(`   • Total USDT Earned: $${totalEarnedUsdt.toFixed(2)}`);
    
    console.log('\n👥 REFERRAL INFORMATION:');
    console.log(`   • Total Referrals: ${referralCount}`);
    
    // 7. Verify calculations
    console.log('\n✅ VERIFICATION:');
    console.log(`   → Purchased Shares: ${purchasedShares.toFixed(2)}`);
    console.log(`   → Commission Shares: ${commissionShares.toFixed(2)}`);
    console.log(`   → Converted Shares: ${totalConvertedShares.toFixed(2)}`);
    console.log(`   → TOTAL: ${totalSharesOwned.toFixed(2)} shares`);
    console.log(`   → Should match Telegram bot: 144.95 shares`);
    console.log(`   → Difference: ${(totalSharesOwned - 144.95).toFixed(2)} shares`);

    // Analysis of the discrepancy
    console.log('\n🔍 DISCREPANCY ANALYSIS:');
    console.log('   → The 25 shares with payment_method "commission_conversion" might be');
    console.log('   → the SAME as the 25 shares in commission_conversions table');
    console.log('   → This would cause double-counting of 25 shares');
    console.log('   → Expected total: 149.95 - 25 = 124.95 shares');
    console.log('   → But bot shows 144.95, so there are still 20 missing shares');

    if (Math.abs(totalSharesOwned - 144.95) < 0.01) {
      console.log('\n🎉 SUCCESS: Affiliate dashboard breakdown matches Telegram bot!');
    } else {
      console.log('\n⚠️  WARNING: Totals do not match Telegram bot exactly');
      console.log('   → Need to investigate the relationship between share_purchases');
      console.log('   → and commission_conversions tables');
    }
    
    // 8. Show raw data for debugging
    console.log('\n🔍 RAW DATA:');
    console.log('Share Purchases:', sharePurchases);
    console.log('Commission Conversions:', conversions);
    console.log('Commission Balance:', commissions);
    
  } catch (error) {
    console.error('❌ Error testing affiliate dashboard breakdown:', error);
  }
}

// Run the test
testAffiliateDashboardBreakdown();
