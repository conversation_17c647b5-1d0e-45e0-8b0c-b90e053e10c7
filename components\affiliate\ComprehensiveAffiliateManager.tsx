/**
 * COMPREHENSIVE AFFILIATE MANAGER
 *
 * Complete affiliate management system with:
 * - Enhanced USDT commission withdrawal system with KYC validation
 * - Real-time commission to shares conversion with atomic operations
 * - Username-based share transfer system with validation
 * - Comprehensive transaction history and audit trails
 * - Email notifications for all financial operations
 * - Security measures and rate limiting
 */

import React, { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';
import { affiliateTransactionService } from '../../lib/services/affiliateTransactionService';

import { affiliateNotificationService } from '../../lib/services/affiliateNotificationService';
import { validateKYCForFinancialOperations, KYCValidationResult } from '../../lib/kycValidation';
import { realtimeValidation } from '../../lib/realtimeValidation';
import { NotificationCenter } from '../user/NotificationCenter';
import FinancialStatementReport from '../reports/FinancialStatementReport';

interface ComprehensiveAffiliateManagerProps {
  userId: number;
  onDataRefresh?: () => void;
}

interface AffiliateData {
  user: {
    id: number;
    username: string;
    email: string;
  };
  referralCount: number;
  commissionBalance: {
    usdt_balance: number;
    share_balance: number;
    total_earned_usdt: number;
    total_earned_shares: number;
    total_withdrawn: number;
    // Add conversion tracking
    total_converted_shares?: number;
    total_usdt_converted?: number;
    total_shares_owned?: number;
    // Add purchased shares tracking
    purchased_shares?: number;
    purchased_amount?: number;
    // Add transfer tracking
    shares_sent?: number;
    shares_received?: number;
    net_transferred_shares?: number;
  };
  referralStats: {
    total_referrals: number;
    active_referrals: number;
    total_commission_earned: number;
  };
  recentTransactions: any[];
  withdrawalHistory: any[];
  conversionHistory: any[];
  transferHistory: any[];
  purchaseHistory: any[];
  currentPhase: {
    id: number;
    phase_name: string;
    price_per_share: number;
  };
}

interface WithdrawalFormData {
  amount: string;
  walletAddress: string;
  network: string;
}

interface ConversionFormData {
  usdtAmount: string;
}

interface TransferFormData {
  recipientUsername: string;
  shareAmount: string;
}

export const ComprehensiveAffiliateManager: React.FC<ComprehensiveAffiliateManagerProps> = ({
  userId,
  onDataRefresh
}) => {
  const [loading, setLoading] = useState(true);
  const [affiliateData, setAffiliateData] = useState<AffiliateData | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'transactions' | 'convert' | 'transfer' | 'withdraw' | 'history' | 'notifications' | 'statement'>('overview');
  const [activeTransactionTab, setActiveTransactionTab] = useState<'usdt' | 'shares' | 'purchased' | 'conversions' | 'withdrawals'>('usdt');
  const [error, setError] = useState<string | null>(null);
  const [showFinancialStatement, setShowFinancialStatement] = useState(false);

  // KYC validation state
  const [kycValidation, setKycValidation] = useState<KYCValidationResult | null>(null);

  // Form states
  const [withdrawalForm, setWithdrawalForm] = useState<WithdrawalFormData>({
    amount: '',
    walletAddress: '',
    network: 'USDT-TRC20'
  });
  const [conversionForm, setConversionForm] = useState<ConversionFormData>({
    usdtAmount: ''
  });
  const [transferForm, setTransferForm] = useState<TransferFormData>({
    recipientUsername: '',
    shareAmount: ''
  });

  // Loading states
  const [withdrawalLoading, setWithdrawalLoading] = useState(false);
  const [conversionLoading, setConversionLoading] = useState(false);
  const [transferLoading, setTransferLoading] = useState(false);

  // Validation states
  const [recipientValidation, setRecipientValidation] = useState<any>(null);

  // Success/error states
  const [operationSuccess, setOperationSuccess] = useState<string | null>(null);
  const [operationError, setOperationError] = useState<string | null>(null);

  useEffect(() => {
    loadAffiliateData();
    loadKYCValidation();
  }, [userId]);

  const loadKYCValidation = async () => {
    try {
      const validation = await validateKYCForFinancialOperations(userId);
      setKycValidation(validation);
    } catch (error) {
      console.error('Error loading KYC validation:', error);
      setKycValidation({
        isValid: false,
        status: 'not_started',
        message: 'Error checking KYC status. Please try again.',
        canWithdraw: false,
        canReceiveDividends: false
      });
    }
  };

  const loadAffiliateData = async () => {
    try {
      setLoading(true);
      setError(null);

      const serviceClient = getServiceRoleClient();

      // Load user information
      const { data: userData, error: userError } = await serviceClient
        .from('users')
        .select('id, username, email')
        .eq('id', userId)
        .single();

      if (userError) {
        throw userError;
      }

      // Load commission balance
      const { data: commissionBalance, error: balanceError } = await serviceClient
        .from('commission_balances')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (balanceError && balanceError.code !== 'PGRST116') {
        throw balanceError;
      }

      // Load referral statistics
      const { data: referrals, error: referralError } = await serviceClient
        .from('referrals')
        .select('id, status, created_at')
        .eq('referrer_id', userId);

      if (referralError) {
        throw referralError;
      }

      // Load recent transactions with referred user information
      const { data: transactions, error: transactionError } = await serviceClient
        .from('commission_transactions')
        .select(`
          id,
          usdt_commission,
          share_commission,
          share_purchase_amount,
          status,
          payment_date,
          created_at,
          referred_id
        `)
        .eq('referrer_id', userId)
        .order('created_at', { ascending: false })
        .limit(10);

      // Load referred user information separately to avoid foreign key issues
      let transactionsWithUsers = transactions || [];
      if (transactions && transactions.length > 0) {
        const referredIds = [...new Set(transactions.map(t => t.referred_id).filter(Boolean))];

        if (referredIds.length > 0) {
          const { data: referredUsers, error: usersError } = await serviceClient
            .from('users')
            .select('id, username, first_name, last_name, full_name')
            .in('id', referredIds);

          if (!usersError && referredUsers) {
            transactionsWithUsers = transactions.map(transaction => ({
              ...transaction,
              referred_user: referredUsers.find(user => user.id === transaction.referred_id)
            }));
          } else if (usersError) {
            console.error('Error loading referred users:', usersError);
          }
        }
      }

      if (transactionError) {
        console.error('Error loading commission transactions:', transactionError);
        throw transactionError;
      }

      // Load withdrawal history
      const { data: withdrawalHistory, error: withdrawalError } = await serviceClient
        .from('commission_withdrawal_requests')
        .select(`
          id,
          withdrawal_amount,
          currency,
          wallet_address,
          network,
          status,
          created_at,
          processed_at,
          admin_notes
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(20);

      if (withdrawalError) {
        console.error('Error loading withdrawal history:', withdrawalError);
      }

      // Load conversion history (from commission_conversions table)
      const { data: conversionHistory, error: conversionError } = await serviceClient
        .from('commission_conversions')
        .select(`
          id,
          usdt_amount,
          shares_requested,
          share_price,
          status,
          created_at,
          approved_at,
          rejection_reason
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(20);

      if (conversionError) {
        console.error('Error loading conversion history:', conversionError);
      }

      // Load share purchases (direct purchases from aureus_share_purchases table)
      const { data: sharePurchases, error: purchasesError } = await serviceClient
        .from('aureus_share_purchases')
        .select('shares_purchased, total_amount, status, payment_method, created_at')
        .eq('user_id', userId)
        .eq('status', 'active')
        .order('created_at', { ascending: false });

      if (purchasesError) {
        console.error('Error loading share purchases:', purchasesError);
      }

      // Calculate purchased shares (excluding commission conversions to avoid double-counting)
      const purchasedShares = sharePurchases?.reduce((sum, purchase) => {
        // Exclude only 'Commission Conversion' (title case) as per Telegram bot
        if (purchase.status === 'active' && purchase.payment_method !== 'Commission Conversion') {
          return sum + (purchase.shares_purchased || 0);
        }
        return sum;
      }, 0) || 0;

      const purchasedAmount = sharePurchases?.reduce((sum, purchase) => {
        if (purchase.status === 'active' && purchase.payment_method !== 'Commission Conversion') {
          return sum + (purchase.total_amount || 0);
        }
        return sum;
      }, 0) || 0;

      // Calculate total converted shares from approved conversions
      const approvedConversions = conversionHistory?.filter(c => c.status === 'approved') || [];
      const totalConvertedShares = approvedConversions.reduce((sum, conversion) => sum + conversion.shares_requested, 0);
      const totalUsdtConverted = approvedConversions.reduce((sum, conversion) => sum + parseFloat(conversion.usdt_amount), 0);

      // Load transfer history (both sent and received)
      const { data: sentTransfers, error: sentTransferError } = await serviceClient
        .from('share_transfers')
        .select(`
          id,
          sender_user_id,
          recipient_user_id,
          shares_transferred,
          transfer_reason,
          status,
          created_at,
          completed_at,
          transfer_metadata
        `)
        .eq('sender_user_id', userId)
        .eq('status', 'completed')
        .order('created_at', { ascending: false })
        .limit(20);

      const { data: receivedTransfers, error: receivedTransferError } = await serviceClient
        .from('share_transfers')
        .select(`
          id,
          sender_user_id,
          recipient_user_id,
          shares_transferred,
          transfer_reason,
          status,
          created_at,
          completed_at,
          transfer_metadata
        `)
        .eq('recipient_user_id', userId)
        .eq('status', 'completed')
        .order('created_at', { ascending: false })
        .limit(20);

      if (sentTransferError) {
        console.error('Error loading sent transfer history:', sentTransferError);
      }
      if (receivedTransferError) {
        console.error('Error loading received transfer history:', receivedTransferError);
      }

      // Combine transfer history for display
      const transferHistory = [...(sentTransfers || []), ...(receivedTransfers || [])];

      // Calculate net transferred shares
      const totalSharesSent = sentTransfers?.reduce((sum, transfer) => sum + parseFloat(transfer.shares_transferred), 0) || 0;
      const totalSharesReceived = receivedTransfers?.reduce((sum, transfer) => sum + parseFloat(transfer.shares_transferred), 0) || 0;
      const netTransferredShares = totalSharesReceived - totalSharesSent;

      // Load current phase
      const { data: currentPhase, error: phaseError } = await serviceClient
        .from('investment_phases')
        .select('id, phase_name, price_per_share')
        .eq('is_active', true)
        .single();

      if (phaseError) {
        throw phaseError;
      }

      // Calculate referral stats
      const totalReferrals = referrals?.length || 0;
      const activeReferrals = referrals?.filter(r => r.status === 'active').length || 0;
      const totalCommissionEarned = (commissionBalance?.total_earned_usdt || 0) + 
                                   ((commissionBalance?.total_earned_shares || 0) * parseFloat(currentPhase.price_per_share));

      // Calculate total shares owned (purchased + commission + converted + net transferred)
      const commissionShares = commissionBalance?.share_balance || 0;
      const totalSharesOwned = purchasedShares + commissionShares + totalConvertedShares + netTransferredShares;

      setAffiliateData({
        user: userData,
        referralCount: totalReferrals,
        commissionBalance: commissionBalance ? {
          ...commissionBalance,
          total_converted_shares: totalConvertedShares,
          total_usdt_converted: totalUsdtConverted,
          total_shares_owned: totalSharesOwned,
          purchased_shares: purchasedShares,
          purchased_amount: purchasedAmount,
          shares_sent: totalSharesSent,
          shares_received: totalSharesReceived,
          net_transferred_shares: netTransferredShares
        } : {
          usdt_balance: 0,
          share_balance: 0,
          total_earned_usdt: 0,
          total_earned_shares: 0,
          total_withdrawn: 0,
          total_converted_shares: 0,
          total_usdt_converted: 0,
          total_shares_owned: 0,
          purchased_shares: 0,
          purchased_amount: 0,
          shares_sent: 0,
          shares_received: 0,
          net_transferred_shares: 0
        },
        referralStats: {
          total_referrals: totalReferrals,
          active_referrals: activeReferrals,
          total_commission_earned: totalCommissionEarned
        },
        recentTransactions: transactionsWithUsers || [],
        withdrawalHistory: withdrawalHistory || [],
        conversionHistory: conversionHistory || [],
        transferHistory: transferHistory || [],
        purchaseHistory: sharePurchases || [],
        currentPhase
      });

    } catch (error) {
      console.error('Error loading affiliate data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load affiliate data');
    } finally {
      setLoading(false);
    }
  };

  const handleDataRefresh = async () => {
    await loadAffiliateData();
    await loadKYCValidation();
    if (onDataRefresh) {
      onDataRefresh();
    }
  };

  // Clear operation messages after 5 seconds
  useEffect(() => {
    if (operationSuccess || operationError) {
      const timer = setTimeout(() => {
        setOperationSuccess(null);
        setOperationError(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [operationSuccess, operationError]);

  // Handle USDT withdrawal request
  const handleWithdrawalSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setOperationError(null);
    setOperationSuccess(null);

    if (!kycValidation?.canWithdraw) {
      setOperationError('KYC verification required for withdrawals.');
      return;
    }

    const amount = parseFloat(withdrawalForm.amount);
    if (!amount || amount <= 0) {
      setOperationError('Please enter a valid withdrawal amount.');
      return;
    }

    if (amount > (affiliateData?.commissionBalance.usdt_balance || 0)) {
      setOperationError(`Insufficient balance. Available: $${affiliateData?.commissionBalance.usdt_balance.toFixed(2)}`);
      return;
    }

    if (amount < 10) {
      setOperationError('Minimum withdrawal amount is $10.00');
      return;
    }

    if (!withdrawalForm.walletAddress.trim()) {
      setOperationError('Please enter your wallet address.');
      return;
    }

    setWithdrawalLoading(true);

    try {
      // Create withdrawal request
      const { error: insertError } = await supabase
        .from('commission_withdrawal_requests')
        .insert({
          user_id: userId,
          withdrawal_amount: amount,
          currency: 'USDT',
          wallet_address: withdrawalForm.walletAddress,
          network: withdrawalForm.network,
          status: 'pending',
          withdrawal_method: 'crypto',
          created_at: new Date().toISOString()
        });

      if (insertError) {
        throw insertError;
      }

      // Send notification email
      await affiliateNotificationService.sendWithdrawalRequestNotification(
        userId,
        amount,
        'USDT',
        withdrawalForm.walletAddress
      );

      setOperationSuccess(`Withdrawal request submitted successfully! You will receive an email confirmation shortly.`);
      setWithdrawalForm({ amount: '', walletAddress: '', network: 'USDT-TRC20' });
      await handleDataRefresh();

    } catch (error: any) {
      console.error('Error submitting withdrawal request:', error);
      setOperationError(error.message || 'Failed to submit withdrawal request. Please try again.');
    } finally {
      setWithdrawalLoading(false);
    }
  };

  // Handle commission to shares conversion
  const handleConversionSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setOperationError(null);
    setOperationSuccess(null);

    const usdtAmount = parseFloat(conversionForm.usdtAmount);
    if (!usdtAmount || usdtAmount <= 0) {
      setOperationError('Please enter a valid USDT amount.');
      return;
    }

    if (usdtAmount > (affiliateData?.commissionBalance.usdt_balance || 0)) {
      setOperationError(`Insufficient USDT balance. Available: $${affiliateData?.commissionBalance.usdt_balance.toFixed(2)}`);
      return;
    }

    if (usdtAmount < 5) {
      setOperationError('Minimum conversion amount is $5.00');
      return;
    }

    if (!affiliateData?.currentPhase) {
      setOperationError('Unable to get current share price. Please try again.');
      return;
    }

    const sharePrice = parseFloat(affiliateData.currentPhase.price_per_share);
    const sharesReceived = usdtAmount / sharePrice;

    setConversionLoading(true);

    try {
      const conversionTransaction = {
        userId,
        usdtAmount,
        sharesReceived,
        sharePrice,
        phaseId: affiliateData.currentPhase.id
      };

      const result = await affiliateTransactionService.processCommissionToSharesConversion(conversionTransaction);

      if (!result.success) {
        throw new Error(result.error || 'Conversion failed');
      }

      setOperationSuccess(`Conversion completed successfully! $${usdtAmount.toFixed(2)} → ${sharesReceived.toFixed(2)} shares added to your account.`);
      setConversionForm({ usdtAmount: '' });
      await handleDataRefresh();

    } catch (error: any) {
      console.error('Error processing conversion:', error);
      setOperationError(error.message || 'Failed to process conversion. Please try again.');
    } finally {
      setConversionLoading(false);
    }
  };

  // Handle share transfer
  const handleTransferSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setOperationError(null);
    setOperationSuccess(null);

    const shareAmount = parseFloat(transferForm.shareAmount);
    if (!shareAmount || shareAmount <= 0) {
      setOperationError('Please enter a valid share amount.');
      return;
    }

    if (shareAmount > (affiliateData?.commissionBalance.share_balance || 0)) {
      setOperationError(`Insufficient share balance. Available: ${affiliateData?.commissionBalance.share_balance.toFixed(2)}`);
      return;
    }

    if (shareAmount < 1) {
      setOperationError('Minimum transfer amount is 1 share');
      return;
    }

    if (!transferForm.recipientUsername.trim()) {
      setOperationError('Please enter recipient username.');
      return;
    }

    if (!recipientValidation?.isValid) {
      setOperationError('Please enter a valid recipient username.');
      return;
    }

    // 🔒 SECURITY: Prevent self-transfer (except for userID 4)
    if (recipientValidation.userId === userId && userId !== 4) {
      setOperationError('You cannot transfer shares to yourself');
      return;
    }

    setTransferLoading(true);

    try {
      // Get recipient user ID using service role client to bypass RLS with case-insensitive matching
      const { data: recipientUser, error: recipientError } = await getServiceRoleClient()
        .from('users')
        .select('id')
        .ilike('username', transferForm.recipientUsername.trim())
        .single();

      if (recipientError || !recipientUser) {
        throw new Error('Recipient user not found');
      }

      const transferTransaction = {
        senderId: userId,
        recipientId: recipientUser.id,
        sharesTransferred: shareAmount,
        transferFee: 0, // No transfer fee for now
        totalDeducted: shareAmount
      };

      const result = await affiliateTransactionService.processShareTransfer(transferTransaction);

      if (!result.success) {
        throw new Error(result.error || 'Transfer failed');
      }

      setOperationSuccess(`Successfully transferred ${shareAmount.toFixed(2)} shares to ${transferForm.recipientUsername}!`);
      setTransferForm({ recipientUsername: '', shareAmount: '' });
      setRecipientValidation(null);
      await handleDataRefresh();

    } catch (error: any) {
      console.error('Error processing transfer:', error);
      setOperationError(error.message || 'Failed to process transfer. Please try again.');
    } finally {
      setTransferLoading(false);
    }
  };

  // Handle recipient username validation
  const handleRecipientUsernameChange = (value: string) => {
    setTransferForm(prev => ({ ...prev, recipientUsername: value }));

    if (value.trim().length > 0) {
      // Use validateUserExists to check if username EXISTS (for transfers)
      // validateUsername checks if username is AVAILABLE (for registration)
      // validateUserExists checks if username EXISTS (for transfers to any user)
      realtimeValidation.validateUserExists(value.trim(), setRecipientValidation);
    } else {
      setRecipientValidation(null);
    }
  };

  if (loading) {
    return (
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-8">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500"></div>
          <span className="ml-3 text-gray-300">Loading affiliate data...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-6">
        <div className="flex items-center space-x-3">
          <div className="text-2xl">❌</div>
          <div>
            <h3 className="font-semibold text-white mb-1">Error Loading Data</h3>
            <p className="text-red-300">{error}</p>
            <button
              onClick={loadAffiliateData}
              className="mt-3 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!affiliateData) {
    return null;
  }

  const tabs = [
    { id: 'overview', label: '📊 Overview', icon: '📊' },
    { id: 'transactions', label: '💰 Transactions', icon: '💰' },
    { id: 'convert', label: '🔄 Convert USDT', icon: '🔄' },
    { id: 'transfer', label: '📤 Transfer Shares', icon: '📤' },
    { id: 'withdraw', label: '💸 Withdraw', icon: '💸' },
    { id: 'statement', label: '📄 Financial Statement', icon: '📄' },
    { id: 'history', label: '📋 History', icon: '📋' },
    { id: 'notifications', label: '🔔 Notifications', icon: '🔔' }
  ];

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-1">
        <div className="flex space-x-1 overflow-x-auto scrollbar-hide">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex-shrink-0 px-3 sm:px-4 py-2 sm:py-3 rounded-lg text-xs sm:text-sm font-medium transition-colors whitespace-nowrap ${
                activeTab === tab.id
                  ? 'bg-yellow-600 text-black'
                  : 'text-gray-300 hover:text-white hover:bg-gray-700'
              }`}
            >
              <span className="mr-1 sm:mr-2 text-sm sm:text-base">{tab.icon}</span>
              <span className="hidden sm:inline">{tab.label}</span>
              <span className="sm:hidden">{tab.label.split(' ')[1] || tab.label.split(' ')[0]}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div className="min-h-[400px]">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Commission Summary Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 sm:gap-6">
              {/* USDT Commissions */}
              <div className="bg-gray-800 rounded-lg border border-gray-700 p-4 sm:p-6">
                <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
                  <span className="text-xl sm:text-2xl">💰</span>
                  <div className="min-w-0 flex-1">
                    <h3 className="text-sm sm:text-lg font-bold text-white truncate">USDT Commissions</h3>
                    <p className="text-gray-400 text-xs sm:text-sm">Cash earnings available</p>
                  </div>
                </div>
                <div className="text-xl sm:text-2xl font-bold text-green-400 mb-2">
                  ${affiliateData.commissionBalance.usdt_balance.toFixed(2)}
                </div>
                <div className="text-gray-300 text-xs sm:text-sm mb-2 sm:mb-3">Current Balance</div>
                <div className="text-xs text-gray-400">
                  Total Earned: ${affiliateData.commissionBalance.total_earned_usdt.toFixed(2)}
                </div>
              </div>

              {/* Share Commissions */}
              <div className="bg-gray-800 rounded-lg border border-gray-700 p-4 sm:p-6">
                <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
                  <span className="text-xl sm:text-2xl">🎁</span>
                  <div className="min-w-0 flex-1">
                    <h3 className="text-sm sm:text-lg font-bold text-white truncate">Bonus Shares</h3>
                    <p className="text-gray-400 text-xs sm:text-sm">Free shares earned</p>
                  </div>
                </div>
                <div className="text-xl sm:text-2xl font-bold text-yellow-400 mb-2">
                  {affiliateData.commissionBalance.share_balance.toFixed(2)}
                </div>
                <div className="text-gray-300 text-xs sm:text-sm mb-2 sm:mb-3">Shares Received</div>
                <div className="text-xs text-gray-400">
                  Value: ${(affiliateData.commissionBalance.share_balance * (affiliateData.currentPhase?.price_per_share || 5)).toFixed(2)}
                </div>
              </div>

              {/* Purchased Shares */}
              <div className="bg-gray-800 rounded-lg border border-gray-700 p-4 sm:p-6">
                <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
                  <span className="text-xl sm:text-2xl">🛒</span>
                  <div className="min-w-0 flex-1">
                    <h3 className="text-sm sm:text-lg font-bold text-white truncate">Purchased Shares</h3>
                    <p className="text-gray-400 text-xs sm:text-sm">Direct share purchases</p>
                  </div>
                </div>
                <div className="text-xl sm:text-2xl font-bold text-blue-400 mb-2">
                  {(affiliateData.commissionBalance.purchased_shares || 0).toFixed(2)}
                </div>
                <div className="text-gray-300 text-xs sm:text-sm mb-2 sm:mb-3">Shares Purchased</div>
                <div className="text-xs text-gray-400">
                  Invested: ${(affiliateData.commissionBalance.purchased_amount || 0).toFixed(2)}
                </div>
              </div>

              {/* Converted USDT to Shares */}
              <div className="bg-gray-800 rounded-lg border border-gray-700 p-4 sm:p-6">
                <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
                  <span className="text-xl sm:text-2xl">🔄</span>
                  <div className="min-w-0 flex-1">
                    <h3 className="text-sm sm:text-lg font-bold text-white truncate">Converted USDT</h3>
                    <p className="text-gray-400 text-xs sm:text-sm">USDT converted to shares</p>
                  </div>
                </div>
                <div className="text-xl sm:text-2xl font-bold text-blue-400 mb-2">
                  {(affiliateData.commissionBalance.total_converted_shares || 0).toFixed(2)}
                </div>
                <div className="text-gray-300 text-xs sm:text-sm mb-2 sm:mb-3">Shares Purchased</div>
                <div className="text-xs text-gray-400">
                  USDT Used: ${(affiliateData.commissionBalance.total_usdt_converted || 0).toFixed(2)}
                </div>
              </div>

              {/* Total Shares */}
              <div className="bg-gray-800 rounded-lg border border-gray-700 p-4 sm:p-6">
                <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
                  <span className="text-xl sm:text-2xl">📊</span>
                  <div className="min-w-0 flex-1">
                    <h3 className="text-sm sm:text-lg font-bold text-white truncate">Total Shares</h3>
                    <p className="text-gray-400 text-xs sm:text-sm">All shares owned</p>
                  </div>
                </div>
                <div className="text-xl sm:text-2xl font-bold text-blue-400 mb-2">
                  {(affiliateData.commissionBalance.total_shares_owned || 0).toFixed(2)}
                </div>
                <div className="text-gray-300 text-xs sm:text-sm mb-2 sm:mb-3">Total Shares Owned</div>
                <div className="text-xs text-gray-400 space-y-1">
                  <div className="hidden sm:block">Purchased: {(affiliateData.commissionBalance.purchased_shares || 0).toFixed(2)} | Bonus: {affiliateData.commissionBalance.share_balance.toFixed(2)} | Converted: {(affiliateData.commissionBalance.total_converted_shares || 0).toFixed(2)}</div>
                  <div className="sm:hidden space-y-1">
                    <div>Purchased: {(affiliateData.commissionBalance.purchased_shares || 0).toFixed(2)}</div>
                    <div>Bonus: {affiliateData.commissionBalance.share_balance.toFixed(2)}</div>
                    <div>Converted: {(affiliateData.commissionBalance.total_converted_shares || 0).toFixed(2)}</div>
                  </div>
                  {((affiliateData.commissionBalance.shares_received || 0) > 0 || (affiliateData.commissionBalance.shares_sent || 0) > 0) && (
                    <div className={`${(affiliateData.commissionBalance.net_transferred_shares || 0) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      <div className="hidden sm:block">
                        Net Transfers: {(affiliateData.commissionBalance.net_transferred_shares || 0) >= 0 ? '+' : ''}{(affiliateData.commissionBalance.net_transferred_shares || 0).toFixed(2)}
                        (Received: {(affiliateData.commissionBalance.shares_received || 0).toFixed(2)} | Sent: {(affiliateData.commissionBalance.shares_sent || 0).toFixed(2)})
                      </div>
                      <div className="sm:hidden space-y-1">
                        <div>Net: {(affiliateData.commissionBalance.net_transferred_shares || 0) >= 0 ? '+' : ''}{(affiliateData.commissionBalance.net_transferred_shares || 0).toFixed(2)}</div>
                        <div>Received: {(affiliateData.commissionBalance.shares_received || 0).toFixed(2)}</div>
                        <div>Sent: {(affiliateData.commissionBalance.shares_sent || 0).toFixed(2)}</div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Total Withdrawals */}
              <div className="bg-gray-800 rounded-lg border border-gray-700 p-4 sm:p-6">
                <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
                  <span className="text-xl sm:text-2xl">💸</span>
                  <div className="min-w-0 flex-1">
                    <h3 className="text-sm sm:text-lg font-bold text-white truncate">Total Withdrawals</h3>
                    <p className="text-gray-400 text-xs sm:text-sm">USDT successfully withdrawn</p>
                  </div>
                </div>
                <div className="text-xl sm:text-2xl font-bold text-red-400 mb-2">
                  ${affiliateData.commissionBalance.total_withdrawn.toFixed(2)}
                </div>
                <div className="text-gray-300 text-xs sm:text-sm mb-2 sm:mb-3">Successfully Withdrawn</div>
                <div className="text-xs text-gray-400">
                  Transactions: {affiliateData.withdrawalHistory.filter(w => w.status === 'completed').length}
                </div>
              </div>
            </div>

            {/* Referral Network Summary */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
              <div className="bg-gray-800 rounded-lg border border-gray-700 p-4 sm:p-6">
                <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
                  <span className="text-xl sm:text-2xl">👥</span>
                  <div>
                    <h3 className="text-base sm:text-lg font-bold text-white">Referral Network</h3>
                    <p className="text-gray-400 text-xs sm:text-sm">People you've referred</p>
                  </div>
                </div>
                <div className="text-2xl sm:text-3xl font-bold text-blue-400 mb-2">
                  {affiliateData.referralCount}
                </div>
                <div className="text-gray-300 text-xs sm:text-sm">Total Referrals</div>
              </div>

              <div className="bg-gray-800 rounded-lg border border-gray-700 p-4 sm:p-6">
                <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
                  <span className="text-xl sm:text-2xl">💎</span>
                  <div>
                    <h3 className="text-base sm:text-lg font-bold text-white">Total Portfolio Value</h3>
                    <p className="text-gray-400 text-xs sm:text-sm">Current USDT + share value</p>
                  </div>
                </div>
                <div className="text-2xl sm:text-3xl font-bold text-purple-400 mb-2">
                  ${(
                    affiliateData.commissionBalance.usdt_balance +
                    ((affiliateData.commissionBalance.total_shares_owned || 0) * (affiliateData.currentPhase?.price_per_share || 5))
                  ).toFixed(2)}
                </div>
                <div className="text-gray-300 text-xs sm:text-sm mb-2 sm:mb-3">Available USDT + Total Shares Value</div>

                {/* Portfolio Value Calculation Explanation */}
                <div className="bg-gray-700/30 rounded-lg p-3 border-l-4 border-purple-400">
                  <h4 className="text-white font-medium text-sm mb-2">💡 How This Is Calculated:</h4>
                  <div className="space-y-1 text-xs text-gray-300">
                    <div className="flex justify-between">
                      <span>Available USDT Balance:</span>
                      <span className="text-green-400">${affiliateData.commissionBalance.usdt_balance.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Purchased Shares ({(affiliateData.commissionBalance.purchased_shares || 0).toFixed(2)}):</span>
                      <span className="text-blue-400">${((affiliateData.commissionBalance.purchased_shares || 0) * (affiliateData.currentPhase?.price_per_share || 5)).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Bonus Shares ({affiliateData.commissionBalance.share_balance.toFixed(2)}):</span>
                      <span className="text-yellow-400">${(affiliateData.commissionBalance.share_balance * (affiliateData.currentPhase?.price_per_share || 5)).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Converted Shares ({(affiliateData.commissionBalance.total_converted_shares || 0).toFixed(2)}):</span>
                      <span className="text-cyan-400">${((affiliateData.commissionBalance.total_converted_shares || 0) * (affiliateData.currentPhase?.price_per_share || 5)).toFixed(2)}</span>
                    </div>
                    <div className="border-t border-gray-600 pt-1 mt-2">
                      <div className="flex justify-between font-medium">
                        <span>Total Portfolio Value:</span>
                        <span className="text-purple-400">${(
                          affiliateData.commissionBalance.usdt_balance +
                          ((affiliateData.commissionBalance.total_shares_owned || 0) * (affiliateData.currentPhase?.price_per_share || 5))
                        ).toFixed(2)}</span>
                      </div>
                    </div>
                    <div className="text-xs text-gray-400 mt-2">
                      * Share values calculated at current phase price: ${(affiliateData.currentPhase?.price_per_share || 5).toFixed(2)} per share
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'transactions' && (
          <div className="space-y-6">
            {/* Transaction Type Tabs */}
            <div className="bg-gray-800 rounded-lg border border-gray-700 p-1">
              <div className="flex space-x-1">
                {[
                  { id: 'usdt', label: '💰 USDT Commissions', icon: '💰' },
                  { id: 'shares', label: '🎁 Bonus Shares', icon: '🎁' },
                  { id: 'purchased', label: '🛒 Purchased Shares', icon: '🛒' },
                  { id: 'conversions', label: '🔄 Conversions', icon: '🔄' },
                  { id: 'withdrawals', label: '💸 Withdrawals', icon: '💸' }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTransactionTab(tab.id as any)}
                    className={`flex-1 px-4 py-3 rounded-lg text-sm font-medium transition-colors ${
                      activeTransactionTab === tab.id
                        ? 'bg-yellow-600 text-black'
                        : 'text-gray-300 hover:text-white hover:bg-gray-700'
                    }`}
                  >
                    <span className="mr-2">{tab.icon}</span>
                    {tab.label.replace(/^.* /, '')}
                  </button>
                ))}
              </div>
            </div>

            {/* Transaction Content */}
            <div className="min-h-[400px]">
              {/* USDT Commission Earnings */}
              {activeTransactionTab === 'usdt' && (
                <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
                  <div className="flex items-center gap-3 mb-6">
                    <span className="text-3xl">💰</span>
                    <div>
                      <h2 className="text-xl font-bold text-white">USDT Commission Earnings</h2>
                      <p className="text-gray-400 text-sm">Cash earnings from referral commissions available for withdrawal or conversion to shares</p>
                    </div>
                  </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-green-400 mb-1">
                    ${affiliateData.commissionBalance.usdt_balance.toFixed(2)}
                  </div>
                  <div className="text-gray-300 text-sm">Current Balance</div>
                </div>
                <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-blue-400 mb-1">
                    ${affiliateData.commissionBalance.total_earned_usdt.toFixed(2)}
                  </div>
                  <div className="text-gray-300 text-sm">Total Earned</div>
                </div>
                <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-red-400 mb-1">
                    ${affiliateData.commissionBalance.total_withdrawn.toFixed(2)}
                  </div>
                  <div className="text-gray-300 text-sm">Total Withdrawn</div>
                </div>
                <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-purple-400 mb-1">
                    ${(affiliateData.commissionBalance.total_usdt_converted || 0).toFixed(2)}
                  </div>
                  <div className="text-gray-300 text-sm">Converted to Shares</div>
                </div>
              </div>

              {/* Recent USDT Commission Transactions */}
              <div className="border-t border-gray-700 pt-4">
                <h4 className="text-white font-medium mb-3">Recent USDT Commission Earnings</h4>
                <div className="space-y-2">
                  {affiliateData.recentTransactions && affiliateData.recentTransactions.filter(t => t.usdt_commission > 0).length > 0 ? (
                    affiliateData.recentTransactions
                      .filter(t => t.usdt_commission > 0)
                      .slice(0, 15)
                      .map((transaction) => (
                        <div key={`usdt-${transaction.id}`} className="flex justify-between items-center p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700/70 transition-colors">
                          <div className="flex items-center gap-3">
                            <div className="text-lg">💰</div>
                            <div>
                              <p className="text-white font-medium text-sm">Commission from Referral Purchase</p>
                              <p className="text-xs text-gray-400">
                                {transaction.referred_user ? (
                                  <>
                                    <span className="text-blue-400">👤 {transaction.referred_user.full_name || `${transaction.referred_user.first_name || ''} ${transaction.referred_user.last_name || ''}`.trim() || transaction.referred_user.username}</span>
                                    <span className="text-gray-500"> (@{transaction.referred_user.username})</span>
                                    <span className="text-gray-400"> • ID: {transaction.referred_id}</span>
                                  </>
                                ) : (
                                  <span>Referral ID: {transaction.referred_id || 'N/A'}</span>
                                )} • {new Date(transaction.payment_date || transaction.created_at).toLocaleDateString('en-US', {
                                  month: 'short',
                                  day: 'numeric',
                                  year: 'numeric',
                                  hour: '2-digit',
                                  minute: '2-digit'
                                })}
                              </p>
                              <p className="text-xs text-gray-500">
                                From ${(transaction.total_amount || 0).toFixed(2)} purchase
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-sm text-green-400">
                              +${(transaction.usdt_commission || 0).toFixed(2)} USDT
                            </p>
                          </div>
                        </div>
                      ))
                  ) : (
                    <div className="text-center py-8">
                      <div className="text-4xl mb-4">💰</div>
                      <p className="text-gray-400 mb-2">No USDT commission transactions yet</p>
                      <p className="text-xs text-gray-500">Commission transactions will appear here when your referrals make purchases</p>
                    </div>
                  )}
                </div>
              </div>
                </div>
              )}

              {/* Bonus Shares Tab */}
              {activeTransactionTab === 'shares' && (
                <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
                  <div className="flex items-center gap-3 mb-6">
                    <span className="text-3xl">🎁</span>
                    <div>
                      <h2 className="text-xl font-bold text-white">Bonus Shares from Referrals</h2>
                      <p className="text-gray-400 text-sm">Free shares awarded through the referral program (15% commission automatically converted to shares)</p>
                    </div>
                  </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-yellow-400 mb-1">
                    {affiliateData.commissionBalance.share_balance.toFixed(2)}
                  </div>
                  <div className="text-gray-300 text-sm">Presale Shares Received</div>
                </div>
                <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-green-400 mb-1">
                    ${(affiliateData.commissionBalance.share_balance * (affiliateData.currentPhase?.price_per_share || 5)).toFixed(2)}
                  </div>
                  <div className="text-gray-300 text-sm">Current Value</div>
                </div>
                <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-purple-400 mb-1">
                    ${(affiliateData.commissionBalance.share_balance * 0.5).toFixed(2)}
                  </div>
                  <div className="text-gray-300 text-sm">Annual Dividend Projection</div>
                </div>
              </div>

              {/* Recent Bonus Share Transactions */}
              <div className="border-t border-gray-700 pt-4">
                <h4 className="text-white font-medium mb-3">Recent Bonus Share Awards</h4>
                <div className="space-y-2">
                  {affiliateData.recentTransactions && affiliateData.recentTransactions.filter(t => t.share_commission > 0).length > 0 ? (
                    affiliateData.recentTransactions
                      .filter(t => t.share_commission > 0)
                      .slice(0, 15)
                      .map((transaction) => (
                        <div key={`shares-${transaction.id}`} className="flex justify-between items-center p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700/70 transition-colors">
                          <div className="flex items-center gap-3">
                            <div className="text-lg">🎁</div>
                            <div>
                              <p className="text-white font-medium text-sm">Bonus Shares from Referral</p>
                              <p className="text-xs text-gray-400">
                                {transaction.referred_user ? (
                                  <>
                                    <span className="text-blue-400">👤 {transaction.referred_user.full_name || `${transaction.referred_user.first_name || ''} ${transaction.referred_user.last_name || ''}`.trim() || transaction.referred_user.username}</span>
                                    <span className="text-gray-500"> (@{transaction.referred_user.username})</span>
                                    <span className="text-gray-400"> • ID: {transaction.referred_id}</span>
                                  </>
                                ) : (
                                  <span>Referral ID: {transaction.referred_id || 'N/A'}</span>
                                )} • {new Date(transaction.payment_date || transaction.created_at).toLocaleDateString('en-US', {
                                  month: 'short',
                                  day: 'numeric',
                                  year: 'numeric',
                                  hour: '2-digit',
                                  minute: '2-digit'
                                })}
                              </p>
                              <p className="text-xs text-gray-500">
                                From ${(transaction.total_amount || 0).toFixed(2)} purchase
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-sm text-yellow-400">
                              +{(transaction.share_commission || 0).toFixed(2)} Shares
                            </p>
                          </div>
                        </div>
                      ))
                  ) : (
                    <div className="text-center py-8">
                      <div className="text-4xl mb-4">🎁</div>
                      <p className="text-gray-400 mb-2">No bonus share transactions yet</p>
                      <p className="text-xs text-gray-500">Bonus share awards will appear here when your referrals make purchases</p>
                    </div>
                  )}
                </div>
              </div>
                </div>
              )}

              {/* Purchased Shares Tab */}
              {activeTransactionTab === 'purchased' && (
                <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
                  <div className="flex items-center gap-3 mb-6">
                    <span className="text-3xl">🛒</span>
                    <div>
                      <h2 className="text-xl font-bold text-white">Purchased Shares</h2>
                      <p className="text-gray-400 text-sm">Direct share purchases with your own funds</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-blue-400 mb-1">
                        {(affiliateData.commissionBalance.purchased_shares || 0).toFixed(2)}
                      </div>
                      <div className="text-gray-300 text-sm">Shares Purchased</div>
                    </div>
                    <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-green-400 mb-1">
                        ${(affiliateData.commissionBalance.purchased_amount || 0).toFixed(2)}
                      </div>
                      <div className="text-gray-300 text-sm">Total Invested</div>
                    </div>
                    <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-purple-400 mb-1">
                        ${((affiliateData.commissionBalance.purchased_shares || 0) * (affiliateData.currentPhase?.price_per_share || 5)).toFixed(2)}
                      </div>
                      <div className="text-gray-300 text-sm">Current Value</div>
                    </div>
                  </div>

                  {/* Purchase History */}
                  <div className="space-y-3">
                    <h3 className="text-lg font-semibold text-white mb-4">Purchase History</h3>
                    {affiliateData.purchaseHistory && affiliateData.purchaseHistory.length > 0 ? (
                      affiliateData.purchaseHistory
                        .filter(purchase => purchase.payment_method !== 'Commission Conversion')
                        .map((purchase, index) => (
                        <div key={index} className="bg-gray-700/30 rounded-lg p-4 border-l-4 border-blue-400">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <div className="text-white font-medium">
                                {purchase.shares_purchased} shares purchased
                              </div>
                              <div className="text-gray-400 text-sm">
                                {new Date(purchase.created_at).toLocaleDateString()} • {purchase.payment_method}
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-green-400 font-bold">
                                ${parseFloat(purchase.total_amount).toFixed(2)}
                              </div>
                              <div className="text-gray-400 text-sm">
                                ${(parseFloat(purchase.total_amount) / purchase.shares_purchased).toFixed(2)}/share
                              </div>
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-12">
                        <div className="text-4xl mb-4">🛒</div>
                        <p className="text-gray-400 mb-2">No share purchases yet</p>
                        <p className="text-xs text-gray-500">Your direct share purchases will appear here</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Conversions Tab */}
              {activeTransactionTab === 'conversions' && (
                <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
                  <div className="flex items-center gap-3 mb-6">
                    <span className="text-3xl">🔄</span>
                    <div>
                      <h2 className="text-xl font-bold text-white">Commission Conversions</h2>
                      <p className="text-gray-400 text-sm">USDT commissions converted to shares</p>
                    </div>
                  </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-blue-400 mb-1">
                    {(affiliateData.commissionBalance.total_converted_shares || 0).toFixed(2)}
                  </div>
                  <div className="text-gray-300 text-sm">Shares Purchased</div>
                </div>
                <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-purple-400 mb-1">
                    ${(affiliateData.commissionBalance.total_usdt_converted || 0).toFixed(2)}
                  </div>
                  <div className="text-gray-300 text-sm">USDT Converted</div>
                </div>
                <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-green-400 mb-1">
                    ${((affiliateData.commissionBalance.total_converted_shares || 0) * (affiliateData.currentPhase?.price_per_share || 5)).toFixed(2)}
                  </div>
                  <div className="text-gray-300 text-sm">Current Value</div>
                </div>
              </div>

              <div className="flex flex-wrap gap-3 mb-6">
                <button
                  onClick={() => setActiveTab('convert')}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors font-medium hover:transform hover:scale-105"
                >
                  Convert USDT to Shares
                </button>
                <button
                  onClick={() => setActiveTab('history')}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg transition-colors font-medium hover:transform hover:scale-105"
                >
                  View Conversion History
                </button>
              </div>

              {/* Recent Conversion Transactions */}
              <div className="border-t border-gray-700 pt-4">
                <h4 className="text-white font-medium mb-3">Recent Commission Conversions</h4>
                <div className="space-y-2">
                  {affiliateData.conversionHistory && affiliateData.conversionHistory.filter(c => c.status === 'approved').length > 0 ? (
                    affiliateData.conversionHistory
                      .filter(c => c.status === 'approved')
                      .slice(0, 15)
                      .map((conversion) => (
                        <div key={`conversion-${conversion.id}`} className="flex justify-between items-center p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700/70 transition-colors">
                          <div className="flex items-center gap-3">
                            <div className="text-lg">🔄</div>
                            <div>
                              <p className="text-white font-medium text-sm">USDT → Shares Conversion</p>
                              <p className="text-xs text-gray-400">
                                Conversion ID: {conversion.id} • {new Date(conversion.approved_at || conversion.created_at).toLocaleDateString('en-US', {
                                  month: 'short',
                                  day: 'numeric',
                                  year: 'numeric',
                                  hour: '2-digit',
                                  minute: '2-digit'
                                })}
                              </p>
                              <p className="text-xs text-gray-500">
                                ${parseFloat(conversion.usdt_amount || 0).toFixed(2)} USDT converted
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-sm text-blue-400">
                              +{(conversion.shares_requested || 0).toFixed(2)} Shares
                            </p>
                            <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-900/30 text-green-300">
                              APPROVED
                            </span>
                          </div>
                        </div>
                      ))
                  ) : (
                    <div className="text-center py-8">
                      <div className="text-4xl mb-4">🔄</div>
                      <p className="text-gray-400 mb-2">No conversion transactions yet</p>
                      <p className="text-xs text-gray-500">USDT to shares conversions will appear here when you convert your commissions</p>
                    </div>
                  )}
                </div>
              </div>
                </div>
              )}

              {/* Withdrawals Tab */}
              {activeTransactionTab === 'withdrawals' && (
                <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
                  <div className="flex items-center gap-3 mb-6">
                    <span className="text-3xl">💸</span>
                    <div>
                      <h2 className="text-xl font-bold text-white">Withdrawal History</h2>
                      <p className="text-gray-400 text-sm">Track all your USDT withdrawal requests and their status</p>
                    </div>
                  </div>

              {affiliateData.withdrawalHistory.length > 0 ? (
                <div className="space-y-3">
                  {affiliateData.withdrawalHistory.map((withdrawal) => (
                    <div key={withdrawal.id} className="bg-gray-700/50 rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div className="flex items-center gap-3">
                          <div className="text-lg">💸</div>
                          <div>
                            <p className="text-white font-medium">
                              Withdrawal Request #{withdrawal.id}
                            </p>
                            <p className="text-xs text-gray-400">
                              {new Date(withdrawal.created_at).toLocaleDateString('en-US', {
                                month: 'short',
                                day: 'numeric',
                                year: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </p>
                            <p className="text-xs text-gray-500">
                              Amount: ${parseFloat(withdrawal.withdrawal_amount).toFixed(2)} USDT
                            </p>
                          </div>
                        </div>
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                          withdrawal.status === 'completed'
                            ? 'bg-green-900/30 text-green-300'
                            : withdrawal.status === 'pending'
                            ? 'bg-yellow-900/30 text-yellow-300'
                            : withdrawal.status === 'processing'
                            ? 'bg-blue-900/30 text-blue-300'
                            : 'bg-red-900/30 text-red-300'
                        }`}>
                          {withdrawal.status.toUpperCase()}
                        </span>
                      </div>

                      {withdrawal.wallet_address && (
                        <div className="text-xs text-gray-400 mb-1">
                          <span className="font-medium">Wallet:</span> {withdrawal.wallet_address.slice(0, 10)}...{withdrawal.wallet_address.slice(-6)}
                        </div>
                      )}

                      {withdrawal.network && (
                        <div className="text-xs text-gray-400 mb-1">
                          <span className="font-medium">Network:</span> {withdrawal.network}
                        </div>
                      )}

                      {withdrawal.admin_notes && (
                        <div className="text-xs text-gray-300 mt-2 p-2 bg-gray-600/30 rounded">
                          <span className="font-medium">Admin Notes:</span> {withdrawal.admin_notes}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="text-4xl mb-4">💸</div>
                  <p className="text-gray-400 mb-4">No withdrawal history yet</p>
                  <button
                    onClick={() => setActiveTab('withdraw')}
                    className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors"
                  >
                    Make Your First Withdrawal
                  </button>
                </div>
              )}
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'convert' && (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h3 className="text-xl font-bold text-white mb-6">🔄 Convert USDT to Shares</h3>

            {/* Operation Status Messages */}
            {operationSuccess && (
              <div className="mb-6 bg-green-900/20 border border-green-500/30 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">✅</div>
                  <p className="text-green-300">{operationSuccess}</p>
                </div>
              </div>
            )}

            {operationError && (
              <div className="mb-6 bg-red-900/20 border border-red-500/30 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">❌</div>
                  <p className="text-red-300">{operationError}</p>
                </div>
              </div>
            )}

            {/* Current Balances */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div className="bg-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-300 mb-1">Available USDT</h4>
                <p className="text-2xl font-bold text-green-400">
                  ${affiliateData.commissionBalance.usdt_balance.toFixed(2)}
                </p>
              </div>
              <div className="bg-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-300 mb-1">Current Share Price</h4>
                <p className="text-2xl font-bold text-yellow-400">
                  ${parseFloat(affiliateData.currentPhase.price_per_share).toFixed(2)}
                </p>
                <p className="text-sm text-gray-400">{affiliateData.currentPhase.phase_name}</p>
              </div>
            </div>

            {/* Admin Approval Notice */}
            <div className="bg-blue-900/30 border border-blue-500/50 rounded-lg p-4 mb-4">
              <div className="flex items-start space-x-3">
                <div className="text-blue-400 text-xl">ℹ️</div>
                <div>
                  <h4 className="text-blue-400 font-semibold mb-1">Admin Approval Required</h4>
                  <p className="text-blue-200 text-sm">
                    Commission to shares conversions require admin approval. Your USDT will be deducted immediately,
                    but shares will be added to your balance only after admin approval. You will be notified once processed.
                  </p>
                </div>
              </div>
            </div>

            {/* Conversion Calculator */}
            <form onSubmit={handleConversionSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  USDT Amount to Convert
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="5"
                  max={affiliateData.commissionBalance.usdt_balance}
                  value={conversionForm.usdtAmount}
                  onChange={(e) => setConversionForm(prev => ({ ...prev, usdtAmount: e.target.value }))}
                  className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  placeholder="Enter USDT amount (minimum $5.00)"
                />
              </div>

              {/* Conversion Preview */}
              {conversionForm.usdtAmount && parseFloat(conversionForm.usdtAmount) > 0 && (
                <div className="bg-gray-700 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-gray-300 mb-2">Conversion Preview</h4>
                  <div className="flex justify-between items-center">
                    <span className="text-white">You will receive:</span>
                    <span className="text-yellow-400 font-bold">
                      {(parseFloat(conversionForm.usdtAmount) / parseFloat(affiliateData.currentPhase.price_per_share)).toFixed(2)} shares
                    </span>
                  </div>
                </div>
              )}

              <button
                type="submit"
                disabled={conversionLoading || !conversionForm.usdtAmount || parseFloat(conversionForm.usdtAmount) < 5}
                className="w-full bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-black font-bold py-3 px-4 rounded-lg transition-colors"
              >
                {conversionLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-black mr-2"></div>
                    Processing Conversion...
                  </div>
                ) : (
                  'Convert USDT to Shares'
                )}
              </button>
            </form>
          </div>
        )}

        {activeTab === 'transfer' && (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h3 className="text-xl font-bold text-white mb-6">📤 Transfer Shares</h3>

            {/* Operation Status Messages */}
            {operationSuccess && (
              <div className="mb-6 bg-green-900/20 border border-green-500/30 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">✅</div>
                  <p className="text-green-300">{operationSuccess}</p>
                </div>
              </div>
            )}

            {operationError && (
              <div className="mb-6 bg-red-900/20 border border-red-500/30 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">❌</div>
                  <p className="text-red-300">{operationError}</p>
                </div>
              </div>
            )}

            {/* Available Balance */}
            <div className="bg-gray-700 rounded-lg p-4 mb-6">
              <h4 className="text-sm font-medium text-gray-300 mb-1">Available Shares</h4>
              <p className="text-2xl font-bold text-yellow-400">
                {affiliateData.commissionBalance.share_balance.toFixed(2)}
              </p>
              <p className="text-sm text-gray-400">
                ≈ ${(affiliateData.commissionBalance.share_balance * parseFloat(affiliateData.currentPhase.price_per_share)).toFixed(2)} value
              </p>
            </div>

            {/* Transfer Form */}
            <form onSubmit={handleTransferSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Recipient Username
                </label>
                <input
                  type="text"
                  value={transferForm.recipientUsername}
                  onChange={(e) => handleRecipientUsernameChange(e.target.value)}
                  className={`w-full px-4 py-3 bg-gray-700 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 ${
                    recipientValidation?.isValid === false ? 'border-red-500 focus:ring-red-500' :
                    recipientValidation?.isValid === true ? 'border-green-500 focus:ring-green-500' :
                    'border-gray-600 focus:ring-yellow-500'
                  }`}
                  placeholder="Enter recipient's username"
                />
                {recipientValidation?.isChecking && (
                  <p className="mt-2 text-sm text-yellow-400">Checking username...</p>
                )}
                {recipientValidation?.isValid === true && (
                  <p className="mt-2 text-sm text-green-400">✓ Username found</p>
                )}
                {recipientValidation?.isValid === false && (
                  <p className="mt-2 text-sm text-red-400">✗ {recipientValidation.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Share Amount
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="1"
                  max={affiliateData.commissionBalance.share_balance}
                  value={transferForm.shareAmount}
                  onChange={(e) => setTransferForm(prev => ({ ...prev, shareAmount: e.target.value }))}
                  className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  placeholder="Enter number of shares to transfer"
                />
              </div>

              {/* Transfer Preview */}
              {transferForm.shareAmount && parseFloat(transferForm.shareAmount) > 0 && (
                <div className="bg-gray-700 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-gray-300 mb-2">Transfer Summary</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Shares to transfer:</span>
                      <span className="text-white">{parseFloat(transferForm.shareAmount).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Transfer fee:</span>
                      <span className="text-white">$0.00</span>
                    </div>
                    <div className="flex justify-between border-t border-gray-600 pt-2">
                      <span className="text-gray-400">Total value:</span>
                      <span className="text-yellow-400 font-bold">
                        ${(parseFloat(transferForm.shareAmount) * parseFloat(affiliateData.currentPhase.price_per_share)).toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              <button
                type="submit"
                disabled={transferLoading || !transferForm.recipientUsername || !transferForm.shareAmount || !recipientValidation?.isValid}
                className="w-full bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-black font-bold py-3 px-4 rounded-lg transition-colors"
              >
                {transferLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-black mr-2"></div>
                    Processing Transfer...
                  </div>
                ) : (
                  'Transfer Shares'
                )}
              </button>
            </form>
          </div>
        )}

        {activeTab === 'withdraw' && (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-4 sm:p-6">
            <h3 className="text-lg sm:text-xl font-bold text-white mb-4 sm:mb-6">💸 Withdraw USDT Commission</h3>

            {/* KYC Status Display */}
            <div className={`rounded-lg p-3 sm:p-4 mb-4 sm:mb-6 ${
              kycValidation?.canWithdraw
                ? 'bg-green-900/20 border border-green-500/30'
                : 'bg-yellow-900/20 border border-yellow-500/30'
            }`}>
              <div className="flex items-start space-x-2 sm:space-x-3">
                <div className="text-xl sm:text-2xl flex-shrink-0">
                  {kycValidation?.canWithdraw ? '✅' : '⚠️'}
                </div>
                <div className="min-w-0 flex-1">
                  <h4 className="font-semibold text-white mb-1 text-sm sm:text-base">
                    {kycValidation?.canWithdraw ? 'KYC Verified' : 'KYC Required'}
                  </h4>
                  <p className={`text-xs sm:text-sm ${
                    kycValidation?.canWithdraw ? 'text-green-300' : 'text-yellow-300'
                  }`}>
                    {kycValidation?.message}
                  </p>
                  {!kycValidation?.canWithdraw && (
                    <button
                      onClick={() => window.location.href = '#kyc'}
                      className="mt-2 bg-yellow-600 hover:bg-yellow-700 text-white text-xs sm:text-sm px-3 py-2 rounded transition-colors"
                    >
                      Complete KYC
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* Operation Status Messages */}
            {operationSuccess && (
              <div className="mb-6 bg-green-900/20 border border-green-500/30 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">✅</div>
                  <p className="text-green-300">{operationSuccess}</p>
                </div>
              </div>
            )}

            {operationError && (
              <div className="mb-6 bg-red-900/20 border border-red-500/30 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">❌</div>
                  <p className="text-red-300">{operationError}</p>
                </div>
              </div>
            )}

            {/* Available Balance */}
            <div className="bg-gray-700 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6">
              <h4 className="text-xs sm:text-sm font-medium text-gray-300 mb-1">Available USDT</h4>
              <p className="text-xl sm:text-2xl font-bold text-green-400">
                ${affiliateData.commissionBalance.usdt_balance.toFixed(2)}
              </p>
              <p className="text-xs sm:text-sm text-gray-400">Available for withdrawal</p>
            </div>

            {/* Withdrawal Form */}
            {kycValidation?.canWithdraw && (
              <form onSubmit={handleWithdrawalSubmit} className="space-y-4">
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-300 mb-2">
                    Withdrawal Amount (USD)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="10"
                    max={affiliateData.commissionBalance.usdt_balance}
                    value={withdrawalForm.amount}
                    onChange={(e) => setWithdrawalForm(prev => ({ ...prev, amount: e.target.value }))}
                    className="w-full px-3 sm:px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 text-sm sm:text-base"
                    placeholder="Enter withdrawal amount (minimum $10.00)"
                  />
                </div>

                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-300 mb-2">
                    Wallet Address
                  </label>
                  <input
                    type="text"
                    value={withdrawalForm.walletAddress}
                    onChange={(e) => setWithdrawalForm(prev => ({ ...prev, walletAddress: e.target.value }))}
                    className="w-full px-3 sm:px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 text-sm sm:text-base"
                    placeholder="Enter your USDT wallet address"
                  />
                </div>

                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-300 mb-2">
                    Network
                  </label>
                  <select
                    value={withdrawalForm.network}
                    onChange={(e) => setWithdrawalForm(prev => ({ ...prev, network: e.target.value }))}
                    className="w-full px-3 sm:px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-yellow-500 text-sm sm:text-base"
                  >
                    <option value="USDT-TRC20">USDT-TRC20 (Tron)</option>
                    <option value="USDT-ERC20">USDT-ERC20 (Ethereum)</option>
                    <option value="USDT-BEP20">USDT-BEP20 (BSC)</option>
                  </select>
                </div>

                <button
                  type="submit"
                  disabled={withdrawalLoading || !withdrawalForm.amount || !withdrawalForm.walletAddress}
                  className="w-full bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-black font-bold py-3 px-4 rounded-lg transition-colors text-sm sm:text-base"
                >
                  {withdrawalLoading ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-4 w-4 sm:h-5 sm:w-5 border-b-2 border-black mr-2"></div>
                      <span className="text-xs sm:text-sm">Submitting Request...</span>
                    </div>
                  ) : (
                    'Request Withdrawal'
                  )}
                </button>
              </form>
            )}
          </div>
        )}

        {activeTab === 'history' && (
          <div className="space-y-4 sm:space-y-6">
            {/* Withdrawal History */}
            <div className="bg-gray-800 rounded-lg border border-gray-700 p-4 sm:p-6">
              <h3 className="text-lg sm:text-xl font-bold text-white mb-4">💸 Withdrawal History</h3>
              {affiliateData.withdrawalHistory.length > 0 ? (
                <div className="space-y-3">
                  {affiliateData.withdrawalHistory.map((withdrawal) => (
                    <div key={withdrawal.id} className="flex flex-col sm:flex-row sm:justify-between sm:items-center p-3 sm:p-4 bg-gray-700 rounded-lg gap-2 sm:gap-0">
                      <div className="flex-1 min-w-0">
                        <p className="text-white font-medium text-sm sm:text-base">
                          ${withdrawal.withdrawal_amount.toFixed(2)} {withdrawal.currency}
                        </p>
                        <p className="text-xs sm:text-sm text-gray-400">
                          {new Date(withdrawal.created_at).toLocaleDateString()} • {withdrawal.network}
                        </p>
                        {withdrawal.wallet_address && (
                          <p className="text-xs text-gray-500 font-mono break-all sm:break-normal">
                            <span className="sm:hidden">{withdrawal.wallet_address.substring(0, 15)}...{withdrawal.wallet_address.substring(withdrawal.wallet_address.length - 10)}</span>
                            <span className="hidden sm:inline">{withdrawal.wallet_address.substring(0, 10)}...{withdrawal.wallet_address.substring(withdrawal.wallet_address.length - 6)}</span>
                          </p>
                        )}
                      </div>
                      <div className="text-right">
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                          withdrawal.status === 'completed' ? 'bg-green-900/30 text-green-400' :
                          withdrawal.status === 'pending' ? 'bg-yellow-900/30 text-yellow-400' :
                          withdrawal.status === 'approved' ? 'bg-blue-900/30 text-blue-400' :
                          'bg-red-900/30 text-red-400'
                        }`}>
                          {withdrawal.status.toUpperCase()}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-400 text-center py-8">No withdrawal requests yet</p>
              )}
            </div>

            {/* Conversion History */}
            <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
              <h3 className="text-xl font-bold text-white mb-4">🔄 Conversion History</h3>
              {affiliateData.conversionHistory.length > 0 ? (
                <div className="space-y-3">
                  {affiliateData.conversionHistory.map((conversion) => (
                    <div key={conversion.id} className="flex justify-between items-center p-4 bg-gray-700 rounded-lg">
                      <div>
                        <p className="text-white font-medium">USDT → Shares Conversion</p>
                        <p className="text-sm text-gray-400">
                          {new Date(conversion.created_at).toLocaleDateString()}
                        </p>
                        <p className="text-xs text-gray-500">
                          {conversion.status === 'approved' ? 'Approved' :
                           conversion.status === 'rejected' ? `Rejected: ${conversion.rejection_reason || 'No reason provided'}` :
                           'Pending Review'}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-red-400 font-medium">
                          ${Math.abs(conversion.usdt_amount).toFixed(2)} USDT
                        </p>
                        <p className="text-green-400 text-sm">
                          +{conversion.shares_requested} Shares
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-400 text-center py-8">No conversions yet</p>
              )}
            </div>

            {/* Transfer History */}
            <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
              <h3 className="text-xl font-bold text-white mb-4">📤 Transfer History</h3>
              {affiliateData.transferHistory.length > 0 ? (
                <div className="space-y-3">
                  {affiliateData.transferHistory.map((transfer) => (
                    <div key={transfer.id} className="flex justify-between items-center p-4 bg-gray-700 rounded-lg">
                      <div>
                        <p className="text-white font-medium">Share Transfer</p>
                        <p className="text-sm text-gray-400">
                          {new Date(transfer.created_at).toLocaleDateString()}
                        </p>
                        <p className="text-xs text-gray-500">
                          To: User ID {transfer.recipient_user_id}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-yellow-400 font-medium">
                          -{transfer.shares_transferred} Shares
                        </p>
                        <span className={`px-2 py-1 rounded text-xs ${
                          transfer.status === 'completed' ? 'bg-green-900/30 text-green-400' :
                          'bg-yellow-900/30 text-yellow-400'
                        }`}>
                          {transfer.status.toUpperCase()}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-400 text-center py-8">No transfers yet</p>
              )}
            </div>
          </div>
        )}

        {activeTab === 'statement' && (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-xl font-bold text-white">Financial Statement</h3>
                <p className="text-gray-400">Generate comprehensive account statements with transaction history</p>
              </div>
              <button
                onClick={() => setShowFinancialStatement(true)}
                className="bg-yellow-600 hover:bg-yellow-700 text-black px-6 py-3 rounded-lg font-medium flex items-center gap-2"
              >
                📄 Generate Statement
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-gray-700 rounded-lg p-4">
                <div className="flex items-center gap-3 mb-3">
                  <span className="text-2xl">📊</span>
                  <div>
                    <h4 className="text-white font-semibold">Comprehensive Reports</h4>
                    <p className="text-gray-400 text-sm">All transactions in one place</p>
                  </div>
                </div>
                <ul className="text-gray-300 text-sm space-y-1">
                  <li>• USDT commission earnings</li>
                  <li>• Share commission earnings</li>
                  <li>• Share purchases & transfers</li>
                  <li>• Withdrawals & conversions</li>
                </ul>
              </div>

              <div className="bg-gray-700 rounded-lg p-4">
                <div className="flex items-center gap-3 mb-3">
                  <span className="text-2xl">📄</span>
                  <div>
                    <h4 className="text-white font-semibold">Export Options</h4>
                    <p className="text-gray-400 text-sm">Professional formats</p>
                  </div>
                </div>
                <ul className="text-gray-300 text-sm space-y-1">
                  <li>• PDF statements for records</li>
                  <li>• CSV data for analysis</li>
                  <li>• Bank-style formatting</li>
                  <li>• Custom date ranges</li>
                </ul>
              </div>

              <div className="bg-gray-700 rounded-lg p-4">
                <div className="flex items-center gap-3 mb-3">
                  <span className="text-2xl">🔒</span>
                  <div>
                    <h4 className="text-white font-semibold">Secure & Accurate</h4>
                    <p className="text-gray-400 text-sm">Verified transaction data</p>
                  </div>
                </div>
                <ul className="text-gray-300 text-sm space-y-1">
                  <li>• Real-time balance calculations</li>
                  <li>• Audit trail compliance</li>
                  <li>• Transaction verification</li>
                  <li>• Professional presentation</li>
                </ul>
              </div>
            </div>

            <div className="mt-6 p-4 bg-gray-700 rounded-lg">
              <div className="flex items-start gap-3">
                <span className="text-yellow-400 text-xl">💡</span>
                <div>
                  <h4 className="text-white font-semibold mb-2">How to Use Financial Statements</h4>
                  <p className="text-gray-300 text-sm mb-2">
                    Financial statements provide a complete overview of your account activity, perfect for:
                  </p>
                  <ul className="text-gray-400 text-sm space-y-1">
                    <li>• Tax reporting and record keeping</li>
                    <li>• Performance tracking and analysis</li>
                    <li>• Compliance and audit requirements</li>
                    <li>• Personal financial planning</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'notifications' && (
          <NotificationCenter
            userId={userId}
            className="bg-gray-800 rounded-lg border border-gray-700 p-6"
          />
        )}
      </div>

      {/* Financial Statement Modal */}
      {showFinancialStatement && (
        <FinancialStatementReport
          userId={userId}
          userName={affiliateData?.user?.full_name || affiliateData?.user?.username}
          isAdmin={false}
          onClose={() => setShowFinancialStatement(false)}
        />
      )}
    </div>
  );
};
