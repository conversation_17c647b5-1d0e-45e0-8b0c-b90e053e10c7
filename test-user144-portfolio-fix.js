/**
 * Test User 144 Portfolio Fix - Verify Website Matches Telegram Bot
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config()

const supabase = createClient(process.env.VITE_SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY)

async function testUser144PortfolioFix() {
  console.log('🔍 Testing User 144 Portfolio Fix - Website vs Telegram Bot\n')
  
  const userId = 144

  try {
    // 1. Fetch share purchases (like website does)
    console.log('1️⃣ Fetching share purchases...')
    const { data: sharesPurchases, error: sharesError } = await supabase
      .from('aureus_share_purchases')
      .select('shares_purchased, total_amount, status, payment_method, created_at')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      
    if (sharesError) throw sharesError
    
    console.log(`   Found ${sharesPurchases?.length || 0} share purchase records`)
    sharesPurchases?.forEach((purchase, i) => {
      console.log(`   ${i+1}. ${purchase.shares_purchased} shares ($${purchase.total_amount}) - ${purchase.status} - ${purchase.payment_method}`)
    })

    // 2. Fetch commission conversions (MISSING FROM ORIGINAL WEBSITE)
    console.log('\n2️⃣ Fetching commission conversions...')
    const { data: commissionConversions, error: conversionsError } = await supabase
      .from('commission_conversions')
      .select('shares_requested, usdt_amount, status, created_at')
      .eq('user_id', userId)
      .eq('status', 'approved')
      
    if (conversionsError) throw conversionsError
    
    console.log(`   Found ${commissionConversions?.length || 0} commission conversion records`)
    commissionConversions?.forEach((conversion, i) => {
      console.log(`   ${i+1}. ${conversion.shares_requested} shares ($${conversion.usdt_amount} USDT converted)`)
    })

    // 3. Fetch commission balance
    console.log('\n3️⃣ Fetching commission balance...')
    const { data: commissionBalance, error: commissionError } = await supabase
      .from('commission_balances')
      .select('usdt_balance, share_balance, total_earned_usdt, total_earned_shares, escrowed_amount')
      .eq('user_id', userId)
      .single()

    if (commissionError) throw commissionError

    console.log(`   USDT Balance: $${commissionBalance.usdt_balance}`)
    console.log(`   Share Balance: ${commissionBalance.share_balance}`)
    console.log(`   Total Earned USDT: $${commissionBalance.total_earned_usdt}`)
    console.log(`   Total Earned Shares: ${commissionBalance.total_earned_shares}`)

    // 4. Fetch direct share commissions (what bot uses)
    console.log('\n4️⃣ Fetching direct share commissions (bot logic)...')
    const { data: shareCommissions, error: shareCommError } = await supabase
      .from('commission_transactions')
      .select('share_commission')
      .eq('referrer_id', userId)
      .eq('status', 'approved')

    if (shareCommError) throw shareCommError

    console.log(`   Found ${shareCommissions?.length || 0} commission transaction records`)
    const earnedShares = shareCommissions?.reduce((sum, commission) => {
      return sum + (parseFloat(commission.share_commission) || 0);
    }, 0) || 0;
    console.log(`   Total Earned Shares (from transactions): ${earnedShares}`)

    // 5. Calculate shares using BOT-MATCHED website logic
    console.log('\n5️⃣ Calculating shares using BOT-MATCHED website logic...')

    const purchasedShares = sharesPurchases?.reduce((sum, purchase) => {
      // Exclude commission conversions to avoid double-counting with convertedShares
      if (purchase.status === 'active' && purchase.payment_method !== 'Commission Conversion') {
        return sum + (purchase.shares_purchased || 0);
      }
      return sum;
    }, 0) || 0;

    const convertedShares = commissionConversions?.reduce((sum, conversion) => {
      return sum + (conversion.shares_requested || 0);
    }, 0) || 0;

    // Use earnedShares from commission transactions (MATCH BOT LOGIC)
    const totalShares = purchasedShares + convertedShares + earnedShares;

    console.log(`   ✅ BOT-MATCHED Website Calculation:`)
    console.log(`      Purchased Shares: ${purchasedShares}`)
    console.log(`      Converted Shares: ${convertedShares}`)
    console.log(`      Earned Shares (from transactions): ${earnedShares}`)
    console.log(`      TOTAL SHARES: ${totalShares}`)
    console.log(`   📊 Balance vs Transactions: ${commissionBalance.share_balance} vs ${earnedShares} (${commissionBalance.share_balance - earnedShares} difference)`)

    // 6. Compare with Telegram bot logic
    console.log('\n6️⃣ Telegram Bot Logic (for comparison):')
    console.log(`   📊 MY PORTFOLIO`)
    console.log(`   💎 TOTAL SHARE HOLDINGS: ${totalShares} shares`)
    console.log(`   📈 SHARE BREAKDOWN:`)
    console.log(`   • Shares Purchased: ${purchasedShares} shares`)
    console.log(`   • Commission Converted to Shares: ${convertedShares} shares`)
    console.log(`   • Shares Earned (Direct): ${earnedShares} shares`)

    // 7. Verification
    console.log('\n7️⃣ VERIFICATION:')
    const expectedBotTotal = 144.95; // From the bot output
    const calculatedTotal = totalShares;

    console.log(`   Expected (Bot): ${expectedBotTotal} shares`)
    console.log(`   Calculated (Bot-Matched Website): ${calculatedTotal} shares`)
    console.log(`   Match: ${calculatedTotal === expectedBotTotal ? '✅ YES' : '❌ NO'}`)

    if (calculatedTotal !== expectedBotTotal) {
      console.log(`   Difference: ${Math.abs(calculatedTotal - expectedBotTotal)} shares`)
    }

    // 8. Commission breakdown
    console.log('\n8️⃣ Commission Status:')
    console.log(`   • USDT Commission Available: $${commissionBalance.usdt_balance}`)
    console.log(`   • Total USDT Earned: $${commissionBalance.total_earned_usdt}`)
    console.log(`   • Escrowed Amount: $${commissionBalance.escrowed_amount}`)

    return {
      success: true,
      totalShares: calculatedTotal,
      expectedShares: expectedBotTotal,
      matches: calculatedTotal === expectedBotTotal,
      breakdown: {
        purchasedShares,
        convertedShares,
        earnedShares
      }
    };

  } catch (error) {
    console.error('❌ Test failed:', error);
    return { success: false, error: error.message };
  }
}

// Run the test
testUser144PortfolioFix()
  .then(result => {
    console.log('\n🎯 TEST RESULT:', result.success ? 'PASSED' : 'FAILED');
    if (result.success && result.matches) {
      console.log('✅ Website now matches Telegram bot calculation!');
    } else if (result.success && !result.matches) {
      console.log('⚠️ Website calculation fixed but still differs from bot');
    }
  })
  .catch(console.error);
