/**
 * GOLD PRICE SERVICE
 *
 * Fetches real-time gold prices from multiple reliable APIs
 * Converts prices from USD per troy ounce to USD per kilogram
 * Stores last known good price in database as fallback
 * CRITICAL: Uses database fallback to prevent catastrophic pricing errors
 */

import { createClient } from '@supabase/supabase-js';

export interface GoldPriceData {
  price: number; // USD per kg
  pricePerOunce: number; // USD per troy ounce
  lastUpdated: string;
  source: string;
  isLive: boolean;
  error?: string;
}

export interface GoldPriceAPI {
  name: string;
  url: string;
  parser: (data: any) => number; // Returns price per troy ounce
  requiresAuth?: boolean;
}

class GoldPriceService {
  private static instance: GoldPriceService;
  private cache: GoldPriceData | null = null;
  private cacheExpiry: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  private readonly TROY_OUNCES_PER_KG = 32.15;
  private supabase: any;

  // Database settings keys for gold price storage
  private readonly DB_KEYS = {
    LAST_KNOWN_PRICE: 'gold_price_last_known_usd_per_kg',
    LAST_KNOWN_PRICE_OUNCE: 'gold_price_last_known_usd_per_ounce',
    LAST_UPDATE_TIMESTAMP: 'gold_price_last_updated',
    LAST_SUCCESSFUL_SOURCE: 'gold_price_last_source'
  };

  // CRITICAL: Use working APIs with CORS support - SIMPLIFIED APPROACH
  private apis: GoldPriceAPI[] = [
    {
      name: 'CoinGecko (Free + CORS)',
      url: 'https://api.coingecko.com/api/v3/simple/price?ids=pax-gold&vs_currencies=usd',
      parser: (data: any) => {
        if (data && data['pax-gold'] && data['pax-gold'].usd) {
          return parseFloat(data['pax-gold'].usd);
        }
        throw new Error('Invalid response format');
      }
    }
  ];

  private constructor() {
    // Initialize Supabase client for database operations
    this.supabase = createClient(
      import.meta.env.VITE_SUPABASE_URL || '',
      import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY || ''
    );
  }

  public static getInstance(): GoldPriceService {
    if (!GoldPriceService.instance) {
      GoldPriceService.instance = new GoldPriceService();
    }
    return GoldPriceService.instance;
  }

  /**
   * Get current gold price with caching
   */
  public async getCurrentPrice(): Promise<GoldPriceData> {
    // Return cached data if still valid
    if (this.cache && Date.now() < this.cacheExpiry) {
      return this.cache;
    }

    try {
      const freshData = await this.fetchFreshPrice();

      // Store successful price in database for future fallback
      await this.storePriceInDatabase(freshData);

      this.cache = freshData;
      this.cacheExpiry = Date.now() + this.CACHE_DURATION;
      return freshData;
    } catch (error) {
      console.error('🚨 CRITICAL: Failed to fetch fresh gold price:', error);

      // Return stale cache if available
      if (this.cache) {
        console.warn('⚠️ Using stale cache data due to API failure');
        return {
          ...this.cache,
          error: 'Using cached data - failed to fetch fresh price'
        };
      }

      // CRITICAL: Use database fallback - last known good price
      console.error('🚨 CRITICAL: No cache available, using database fallback');
      return await this.getDatabaseFallbackPrice();
    }
  }

  /**
   * Fetch fresh price from APIs
   */
  private async fetchFreshPrice(): Promise<GoldPriceData> {
    let lastError: any = null;

    for (const api of this.apis) {
      try {
        console.log(`🔍 Fetching gold price from ${api.name}...`);
        
        const response = await fetch(api.url, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'User-Agent': 'Aureus-Africa-Platform/1.0'
          },
          // Add timeout
          signal: AbortSignal.timeout(10000) // 10 second timeout
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        const pricePerOunce = api.parser(data);
        
        if (!pricePerOunce || pricePerOunce <= 0) {
          throw new Error('Invalid price value received');
        }

        const pricePerKg = Math.round(pricePerOunce * this.TROY_OUNCES_PER_KG);

        console.log(`✅ Successfully fetched from ${api.name}: $${pricePerOunce.toFixed(2)}/oz → $${pricePerKg.toLocaleString()}/kg`);

        const priceData = {
          price: pricePerKg,
          pricePerOunce: Math.round(pricePerOunce * 100) / 100, // Round to 2 decimal places
          lastUpdated: new Date().toISOString(),
          source: api.name,
          isLive: true
        };

        return priceData;

      } catch (error) {
        console.warn(`❌ ${api.name} failed:`, error);
        lastError = error;
        continue;
      }
    }

    throw new Error(`All APIs failed. Last error: ${lastError?.message || 'Unknown error'}`);
  }

  /**
   * Store successful price data in database for fallback
   */
  private async storePriceInDatabase(priceData: GoldPriceData): Promise<void> {
    try {
      const updates = [
        {
          setting_name: this.DB_KEYS.LAST_KNOWN_PRICE,
          setting_value: priceData.price.toString(),
          description: 'Last known good gold price in USD per kilogram'
        },
        {
          setting_name: this.DB_KEYS.LAST_KNOWN_PRICE_OUNCE,
          setting_value: priceData.pricePerOunce.toString(),
          description: 'Last known good gold price in USD per troy ounce'
        },
        {
          setting_name: this.DB_KEYS.LAST_UPDATE_TIMESTAMP,
          setting_value: priceData.lastUpdated,
          description: 'Timestamp of last successful gold price update'
        },
        {
          setting_name: this.DB_KEYS.LAST_SUCCESSFUL_SOURCE,
          setting_value: priceData.source,
          description: 'Source of last successful gold price fetch'
        }
      ];

      for (const update of updates) {
        // Check if setting exists
        const { data: existing } = await this.supabase
          .from('system_settings')
          .select('id')
          .eq('setting_name', update.setting_name)
          .single();

        if (existing) {
          // Update existing record
          await this.supabase
            .from('system_settings')
            .update({
              setting_value: update.setting_value,
              description: update.description,
              updated_at: new Date().toISOString()
            })
            .eq('setting_name', update.setting_name);
        } else {
          // Insert new record
          await this.supabase
            .from('system_settings')
            .insert({
              setting_name: update.setting_name,
              setting_value: update.setting_value,
              description: update.description
            });
        }
      }

      console.log(`💾 Stored gold price in database: $${priceData.price.toLocaleString()}/kg from ${priceData.source}`);
    } catch (error) {
      console.error('⚠️ Failed to store gold price in database:', error);
      // Don't throw - this is not critical for the current operation
    }
  }

  /**
   * Get fallback price from database (last known good price)
   */
  private async getDatabaseFallbackPrice(): Promise<GoldPriceData> {
    try {
      const { data: settings, error } = await this.supabase
        .from('system_settings')
        .select('setting_name, setting_value')
        .in('setting_name', Object.values(this.DB_KEYS));

      if (error) {
        throw error;
      }

      const settingsMap = settings?.reduce((acc: any, setting: any) => {
        acc[setting.setting_name] = setting.setting_value;
        return acc;
      }, {}) || {};

      const lastKnownPrice = parseFloat(settingsMap[this.DB_KEYS.LAST_KNOWN_PRICE]);
      const lastKnownPriceOunce = parseFloat(settingsMap[this.DB_KEYS.LAST_KNOWN_PRICE_OUNCE]);
      const lastUpdated = settingsMap[this.DB_KEYS.LAST_UPDATE_TIMESTAMP];
      const lastSource = settingsMap[this.DB_KEYS.LAST_SUCCESSFUL_SOURCE];

      if (lastKnownPrice && lastKnownPrice > 0) {
        console.log(`✅ Using database fallback: $${lastKnownPrice.toLocaleString()}/kg (last updated: ${lastUpdated}, source: ${lastSource})`);

        return {
          price: lastKnownPrice,
          pricePerOunce: lastKnownPriceOunce || Math.round((lastKnownPrice / this.TROY_OUNCES_PER_KG) * 100) / 100,
          lastUpdated: lastUpdated || new Date().toISOString(),
          source: `Database Fallback (${lastSource || 'Unknown'})`,
          isLive: false,
          error: 'Using database fallback - all APIs unavailable'
        };
      }

      throw new Error('No valid price found in database');
    } catch (error) {
      console.error('🚨 CRITICAL: Database fallback failed:', error);
      return this.getEmergencyFallbackPrice();
    }
  }

  /**
   * Emergency fallback when even database fails
   */
  private getEmergencyFallbackPrice(): GoldPriceData {
    // CRITICAL: Use current market estimate - this should be updated regularly
    const emergencyPricePerOunce = 3731; // Current market estimate (matches database)
    const emergencyPricePerKg = 120000; // Current market estimate (matches database)

    console.error(`🚨 EMERGENCY FALLBACK: Using hardcoded price $${emergencyPricePerKg.toLocaleString()}/kg - UPDATE IMMEDIATELY!`);

    return {
      price: emergencyPricePerKg,
      pricePerOunce: emergencyPricePerOunce,
      lastUpdated: new Date().toISOString(),
      source: 'EMERGENCY FALLBACK',
      isLive: false,
      error: 'CRITICAL: All systems failed - using emergency fallback price'
    };
  }

  /**
   * Convert price per troy ounce to price per kilogram
   */
  public convertOunceToKg(pricePerOunce: number): number {
    return Math.round(pricePerOunce * this.TROY_OUNCES_PER_KG);
  }

  /**
   * Convert price per kilogram to price per troy ounce
   */
  public convertKgToOunce(pricePerKg: number): number {
    return Math.round((pricePerKg / this.TROY_OUNCES_PER_KG) * 100) / 100;
  }

  /**
   * Initialize database with current market price (admin function)
   */
  public async initializeDatabasePrice(pricePerKg: number, source: string = 'Manual Admin Update'): Promise<void> {
    const pricePerOunce = Math.round((pricePerKg / this.TROY_OUNCES_PER_KG) * 100) / 100;

    const priceData: GoldPriceData = {
      price: pricePerKg,
      pricePerOunce: pricePerOunce,
      lastUpdated: new Date().toISOString(),
      source: source,
      isLive: false
    };

    await this.storePriceInDatabase(priceData);
    console.log(`✅ Database initialized with gold price: $${pricePerKg.toLocaleString()}/kg`);
  }

  /**
   * Get current database stored price (for admin verification)
   */
  public async getDatabaseStoredPrice(): Promise<GoldPriceData | null> {
    try {
      return await this.getDatabaseFallbackPrice();
    } catch (error) {
      return null;
    }
  }

  /**
   * Clear cache to force fresh fetch
   */
  public clearCache(): void {
    this.cache = null;
    this.cacheExpiry = 0;
  }

  /**
   * Get cache status
   */
  public getCacheStatus(): { cached: boolean; expiresIn: number } {
    return {
      cached: this.cache !== null && Date.now() < this.cacheExpiry,
      expiresIn: Math.max(0, this.cacheExpiry - Date.now())
    };
  }
}

// Export singleton instance
export const goldPriceService = GoldPriceService.getInstance();

// Export for testing
export { GoldPriceService };
