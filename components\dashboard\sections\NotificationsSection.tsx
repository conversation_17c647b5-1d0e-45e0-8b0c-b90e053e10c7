import React from 'react';
import { NotificationCenter } from '../../user/NotificationCenter';
import { DashboardData } from '../../../hooks/useDashboardData';

interface NotificationsSectionProps {
  user: any;
  dashboardData: DashboardData;
}

export const NotificationsSection: React.FC<NotificationsSectionProps> = ({
  user,
  dashboardData
}) => {
  return (
    <div className="space-y-6">
      {/* Section Header */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-white mb-2">Notifications</h2>
            <p className="text-gray-400">
              Stay updated with important announcements and account activities.
            </p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-white">
              {dashboardData.notifications?.unread || 0}
            </div>
            <div className="text-gray-400 text-sm">Unread</div>
          </div>
        </div>
      </div>

      {/* Notification Center */}
      <NotificationCenter
        userId={user?.database_user?.id || user?.id}
      />
    </div>
  );
};
