import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

// Helper function to sanitize props for debug logging
const sanitizeProps = (props: any): Record<string, any> => {
  if (!props || typeof props !== 'object') return {};

  const sanitized: Record<string, any> = {};

  for (const [key, value] of Object.entries(props)) {
    // Skip functions, sensitive data, and large objects
    if (typeof value === 'function') {
      sanitized[key] = '[Function]';
    } else if (key.toLowerCase().includes('password') || key.toLowerCase().includes('token')) {
      sanitized[key] = '[REDACTED]';
    } else if (typeof value === 'object' && value !== null) {
      if (Array.isArray(value)) {
        sanitized[key] = `[Array(${value.length})]`;
      } else {
        sanitized[key] = '[Object]';
      }
    } else {
      sanitized[key] = value;
    }
  }

  return sanitized;
};

class ErrorBoundary extends Component<Props, State> {
  private debugContext: any = null;

  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Production Error Boundary:', error, errorInfo);

    // Report to debug system if available
    this.reportToDebugSystem(error, errorInfo);

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log to external service in production
    if (import.meta.env.PROD) {
      // Send to error tracking service
      console.error('PRODUCTION ERROR:', {
        error: error.toString(),
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        environment: import.meta.env.MODE
      });
    }

    this.setState({ error, errorInfo });
  }

  private reportToDebugSystem = (error: Error, errorInfo: ErrorInfo) => {
    // Simple debug system integration for Phase 3
    // Use a global event to communicate with the debug system
    try {
      const debugEvent = new CustomEvent('aureus-debug-error', {
        detail: {
          type: 'react',
          severity: 'critical',
          message: error.message,
          stack: error.stack,
          additionalInfo: {
            componentStack: errorInfo.componentStack,
            errorName: error.name,
            environment: import.meta.env.MODE,
            reactVersion: React.version,
            props: sanitizeProps(this.props)
          }
        }
      });
      window.dispatchEvent(debugEvent);
    } catch (debugError) {
      console.warn('Failed to report error to debug system:', debugError);
    }
  };

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div style={{
          padding: '40px',
          textAlign: 'center',
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: '#1a1a1a',
          color: '#ffffff',
          fontFamily: 'system-ui, -apple-system, sans-serif'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, #FFD700, #FFA500)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontSize: '2.5rem',
            fontWeight: 'bold',
            marginBottom: '20px'
          }}>
            🏆 Aureus Alliance Holdings
          </div>
          
          <h2 style={{ 
            color: '#ff4444', 
            marginBottom: '20px',
            fontSize: '1.5rem'
          }}>
            ⚠️ Application Error
          </h2>
          
          <p style={{ 
            marginBottom: '30px', 
            maxWidth: '600px',
            lineHeight: '1.6',
            fontSize: '1.1rem'
          }}>
            We're experiencing a temporary issue with the application. Our development team has been 
            automatically notified and is working to resolve this. Please try refreshing the page.
          </p>
          
          <div style={{ 
            display: 'flex', 
            gap: '20px', 
            flexWrap: 'wrap', 
            justifyContent: 'center',
            marginBottom: '30px'
          }}>
            <button 
              onClick={() => window.location.reload()}
              style={{
                padding: '12px 24px',
                backgroundColor: '#FFD700',
                color: '#1a1a1a',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontWeight: 'bold',
                fontSize: '1rem',
                transition: 'all 0.3s ease',
                boxShadow: '0 4px 12px rgba(255, 215, 0, 0.3)'
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.backgroundColor = '#FFA500';
                e.currentTarget.style.transform = 'translateY(-2px)';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.backgroundColor = '#FFD700';
                e.currentTarget.style.transform = 'translateY(0)';
              }}
            >
              🔄 Reload Application
            </button>
            
            <button 
              onClick={() => window.location.href = '/'}
              style={{
                padding: '12px 24px',
                backgroundColor: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontWeight: 'bold',
                fontSize: '1rem',
                transition: 'all 0.3s ease',
                boxShadow: '0 4px 12px rgba(0, 123, 255, 0.3)'
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.backgroundColor = '#0056b3';
                e.currentTarget.style.transform = 'translateY(-2px)';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.backgroundColor = '#007bff';
                e.currentTarget.style.transform = 'translateY(0)';
              }}
            >
              🏠 Go to Dashboard
            </button>
          </div>

          {/* Contact Information */}
          <div style={{
            padding: '20px',
            backgroundColor: '#2d2d2d',
            borderRadius: '8px',
            marginBottom: '20px',
            maxWidth: '500px'
          }}>
            <h3 style={{ color: '#FFD700', marginBottom: '10px' }}>Need Immediate Assistance?</h3>
            <p style={{ margin: '5px 0', fontSize: '0.9rem' }}>
              📧 Email: <EMAIL>
            </p>
            <p style={{ margin: '5px 0', fontSize: '0.9rem' }}>
              💬 Telegram: @TTTFOUNDER
            </p>
            <p style={{ margin: '5px 0', fontSize: '0.9rem' }}>
              🤖 Bot: @AureusAllianceBot
            </p>
          </div>

          {/* Development Error Details */}
          {import.meta.env.DEV && this.state.error && (
            <details style={{ 
              marginTop: '20px', 
              textAlign: 'left', 
              maxWidth: '800px',
              width: '100%'
            }}>
              <summary style={{ 
                cursor: 'pointer', 
                color: '#FFD700',
                fontSize: '1.1rem',
                fontWeight: 'bold',
                marginBottom: '10px'
              }}>
                🔍 Development Error Details (Click to expand)
              </summary>
              <div style={{
                background: '#2d2d2d',
                padding: '20px',
                borderRadius: '8px',
                marginTop: '10px',
                border: '1px solid #444'
              }}>
                <h4 style={{ color: '#ff4444', marginBottom: '10px' }}>Error Message:</h4>
                <pre style={{
                  background: '#1a1a1a',
                  padding: '10px',
                  borderRadius: '4px',
                  overflow: 'auto',
                  fontSize: '12px',
                  marginBottom: '15px',
                  color: '#ff6b6b'
                }}>
                  {this.state.error.toString()}
                </pre>
                
                <h4 style={{ color: '#ffa500', marginBottom: '10px' }}>Stack Trace:</h4>
                <pre style={{
                  background: '#1a1a1a',
                  padding: '10px',
                  borderRadius: '4px',
                  overflow: 'auto',
                  fontSize: '11px',
                  marginBottom: '15px',
                  color: '#ffeb3b',
                  maxHeight: '200px'
                }}>
                  {this.state.error.stack}
                </pre>
                
                {this.state.errorInfo && (
                  <>
                    <h4 style={{ color: '#4caf50', marginBottom: '10px' }}>Component Stack:</h4>
                    <pre style={{
                      background: '#1a1a1a',
                      padding: '10px',
                      borderRadius: '4px',
                      overflow: 'auto',
                      fontSize: '11px',
                      color: '#81c784',
                      maxHeight: '150px'
                    }}>
                      {this.state.errorInfo.componentStack}
                    </pre>
                  </>
                )}
              </div>
            </details>
          )}
          
          {/* Environment Info */}
          <div style={{
            marginTop: '20px',
            fontSize: '0.8rem',
            color: '#888',
            textAlign: 'center'
          }}>
            Environment: {import.meta.env.MODE} | 
            Version: {import.meta.env.VITE_APP_VERSION || '1.0.0'} | 
            Timestamp: {new Date().toLocaleString()}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
