/**
 * FINANCIAL AUDIT SERVICE
 * 
 * Comprehensive financial audit system for generating professional
 * audit reports for any user in the system. Performs complete
 * financial verification including shares, commissions, and balances.
 */

import { supabase, getServiceRoleClient } from '../supabase'

export interface UserProfile {
  id: number
  username: string
  email: string
  full_name: string | null
  created_at: string
  sponsor_user_id: number | null
  total_referrals: number
  total_earnings: string
}

export interface SharePurchase {
  id: string
  user_id: number
  shares_purchased: number
  total_amount: string
  phase_id: number
  status: string
  purchase_date: string
  created_at: string
}

export interface CommissionTransaction {
  id: string
  referrer_id: number
  referred_id: number
  share_purchase_id: string | null
  commission_rate: string
  share_purchase_amount: string
  usdt_commission: string
  share_commission: string
  status: string
  payment_date: string
  created_at: string
  phase_id: number | null
}

export interface CommissionBalance {
  user_id: number
  usdt_balance: string
  share_balance: string
  total_earned_usdt: string
  total_earned_shares: string
  last_updated: string
}

export interface ReferralRelationship {
  id: string
  referrer_id: number
  referred_id: number
  referral_code: string
  commission_rate: string
  total_commission: string
  status: string
  created_at: string
  campaign_source: string | null
}

export interface CommissionConversion {
  id: string
  user_id: number
  shares_requested: number
  usdt_amount: string
  share_price: string
  phase_id: number
  status: string
  created_at: string
  approved_at: string | null
  approved_by_admin_id: number | null
}

export interface CryptoPayment {
  id: string
  user_id: number
  amount: string
  currency: string
  status: string
  approved_at: string | null
  approved_by_admin_id: number | null
  created_at: string
  transaction_hash: string
}

export interface InvestmentPhase {
  id: number
  phase_name: string
  price_per_share: string
  shares_sold: string
  is_active: boolean
}

export interface AuditResult {
  user: UserProfile
  sharePurchases: SharePurchase[]
  commissionTransactions: CommissionTransaction[]
  commissionBalance: CommissionBalance | null
  referralRelationships: ReferralRelationship[]
  cryptoPayments: CryptoPayment[]
  commissionConversions: CommissionConversion[]
  investmentPhases: InvestmentPhase[]
  calculations: {
    totalDirectShares: number
    totalCommissionShares: number
    totalConvertedShares: number
    totalSharesOwned: number
    totalUsdtCommissionEarned: number
    totalShareCommissionEarned: number
    totalUsdtConverted: number
    portfolioValue: number
    currentUsdtBalance: number
    currentShareBalance: number
  }
  auditStatus: 'PASSED' | 'FAILED' | 'WARNING'
  auditFindings: string[]
  recommendations: string[]
}

export class FinancialAuditService {
  private serviceClient = getServiceRoleClient()

  /**
   * Generate comprehensive financial audit for a user
   */
  async generateAudit(userId: number): Promise<AuditResult> {
    console.log(`🔍 Starting financial audit for user ID: ${userId}`)

    try {
      // Fetch all required data in parallel
      const [
        user,
        sharePurchases,
        commissionTransactions,
        commissionBalance,
        referralRelationships,
        cryptoPayments,
        commissionConversions,
        investmentPhases
      ] = await Promise.all([
        this.getUserProfile(userId),
        this.getSharePurchases(userId),
        this.getCommissionTransactions(userId),
        this.getCommissionBalance(userId),
        this.getReferralRelationships(userId),
        this.getCryptoPayments(userId),
        this.getCommissionConversions(userId),
        this.getInvestmentPhases()
      ])

      if (!user) {
        throw new Error(`User with ID ${userId} not found`)
      }

      // Perform calculations
      const calculations = this.performCalculations(
        sharePurchases,
        commissionTransactions,
        commissionConversions,
        commissionBalance,
        investmentPhases
      )

      // Perform audit verification
      const { auditStatus, auditFindings, recommendations } = this.performAuditVerification(
        user,
        sharePurchases,
        commissionTransactions,
        commissionBalance,
        calculations
      )

      const auditResult: AuditResult = {
        user,
        sharePurchases,
        commissionTransactions,
        commissionBalance,
        referralRelationships,
        cryptoPayments,
        commissionConversions,
        investmentPhases,
        calculations,
        auditStatus,
        auditFindings,
        recommendations
      }

      console.log(`✅ Financial audit completed for user ${userId}: ${auditStatus}`)
      return auditResult

    } catch (error) {
      console.error(`❌ Financial audit failed for user ${userId}:`, error)
      throw error
    }
  }

  /**
   * Get user profile information
   */
  private async getUserProfile(userId: number): Promise<UserProfile | null> {
    const { data, error } = await this.serviceClient
      .from('users')
      .select('id, username, email, full_name, created_at, sponsor_user_id, total_referrals, total_earnings')
      .eq('id', userId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return null // User not found
      throw error
    }

    return data
  }

  /**
   * Get all share purchases for user
   */
  private async getSharePurchases(userId: number): Promise<SharePurchase[]> {
    const { data, error } = await this.serviceClient
      .from('aureus_share_purchases')
      .select('*')
      .eq('user_id', userId)
      .order('created_at')

    if (error) throw error
    return data || []
  }

  /**
   * Get all commission transactions for user
   */
  private async getCommissionTransactions(userId: number): Promise<CommissionTransaction[]> {
    const { data, error } = await this.serviceClient
      .from('commission_transactions')
      .select('*')
      .or(`referrer_id.eq.${userId},referred_id.eq.${userId}`)
      .order('created_at')

    if (error) throw error
    return data || []
  }

  /**
   * Get commission balance for user
   */
  private async getCommissionBalance(userId: number): Promise<CommissionBalance | null> {
    const { data, error } = await this.serviceClient
      .from('commission_balances')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return null // No balance record
      throw error
    }

    return data
  }

  /**
   * Get referral relationships for user
   */
  private async getReferralRelationships(userId: number): Promise<ReferralRelationship[]> {
    const { data, error } = await this.serviceClient
      .from('referrals')
      .select('*')
      .or(`referrer_id.eq.${userId},referred_id.eq.${userId}`)
      .order('created_at')

    if (error) throw error
    return data || []
  }

  /**
   * Get crypto payments for user
   */
  private async getCryptoPayments(userId: number): Promise<CryptoPayment[]> {
    const { data, error } = await this.serviceClient
      .from('crypto_payment_transactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at')

    if (error) throw error
    return data || []
  }

  /**
   * Get commission conversions for user
   */
  private async getCommissionConversions(userId: number): Promise<CommissionConversion[]> {
    const { data, error } = await this.serviceClient
      .from('commission_conversions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at')

    if (error) throw error
    return data || []
  }

  /**
   * Get investment phases information
   */
  private async getInvestmentPhases(): Promise<InvestmentPhase[]> {
    const { data, error } = await this.serviceClient
      .from('investment_phases')
      .select('*')
      .order('id')

    if (error) throw error
    return data || []
  }

  /**
   * Perform financial calculations
   */
  private performCalculations(
    sharePurchases: SharePurchase[],
    commissionTransactions: CommissionTransaction[],
    commissionConversions: CommissionConversion[],
    commissionBalance: CommissionBalance | null,
    investmentPhases: InvestmentPhase[]
  ) {
    // Calculate direct shares purchased
    const totalDirectShares = sharePurchases
      .filter(p => p.status === 'active')
      .reduce((sum, p) => sum + p.shares_purchased, 0)

    // Calculate commission shares earned (where user is referrer)
    const totalShareCommissionEarned = commissionTransactions
      .filter(t => t.status === 'approved')
      .reduce((sum, t) => sum + parseFloat(t.share_commission), 0)

    // Calculate USDT commission earned
    const totalUsdtCommissionEarned = commissionTransactions
      .filter(t => t.status === 'approved')
      .reduce((sum, t) => sum + parseFloat(t.usdt_commission), 0)

    // Calculate converted shares from USDT commissions
    const totalConvertedShares = commissionConversions
      .filter(c => c.status === 'approved')
      .reduce((sum, c) => sum + c.shares_requested, 0)

    // Calculate total USDT converted to shares
    const totalUsdtConverted = commissionConversions
      .filter(c => c.status === 'approved')
      .reduce((sum, c) => sum + parseFloat(c.usdt_amount), 0)

    // Total shares owned (direct + commission + converted)
    const totalSharesOwned = totalDirectShares + totalShareCommissionEarned + totalConvertedShares

    // Current balances
    const currentUsdtBalance = commissionBalance ? parseFloat(commissionBalance.usdt_balance) : 0
    const currentShareBalance = commissionBalance ? parseFloat(commissionBalance.share_balance) : 0

    // Portfolio value (using current phase price)
    const currentPhase = investmentPhases.find(p => p.is_active)
    const currentSharePrice = currentPhase ? parseFloat(currentPhase.price_per_share) : 5.00
    const portfolioValue = totalSharesOwned * currentSharePrice

    return {
      totalDirectShares,
      totalCommissionShares: totalShareCommissionEarned,
      totalConvertedShares,
      totalSharesOwned,
      totalUsdtCommissionEarned,
      totalShareCommissionEarned,
      totalUsdtConverted,
      portfolioValue,
      currentUsdtBalance,
      currentShareBalance
    }
  }

  /**
   * Perform audit verification and generate findings
   */
  private performAuditVerification(
    user: UserProfile,
    sharePurchases: SharePurchase[],
    commissionTransactions: CommissionTransaction[],
    commissionBalance: CommissionBalance | null,
    calculations: any
  ) {
    const auditFindings: string[] = []
    const recommendations: string[] = []
    let auditStatus: 'PASSED' | 'FAILED' | 'WARNING' = 'PASSED'

    // Verify commission balance consistency
    if (commissionBalance) {
      const expectedUsdtEarned = calculations.totalUsdtCommissionEarned
      const recordedUsdtEarned = parseFloat(commissionBalance.total_earned_usdt)
      
      if (Math.abs(expectedUsdtEarned - recordedUsdtEarned) > 0.01) {
        auditFindings.push(`USDT commission discrepancy: Expected ${expectedUsdtEarned}, Recorded ${recordedUsdtEarned}`)
        auditStatus = 'FAILED'
      }

      const expectedSharesEarned = calculations.totalShareCommissionEarned
      const recordedSharesEarned = parseFloat(commissionBalance.total_earned_shares)
      
      if (Math.abs(expectedSharesEarned - recordedSharesEarned) > 0.01) {
        auditFindings.push(`Share commission discrepancy: Expected ${expectedSharesEarned}, Recorded ${recordedSharesEarned}`)
        auditStatus = 'FAILED'
      }
    }

    // Verify all share purchases are active
    const inactiveShares = sharePurchases.filter(p => p.status !== 'active')
    if (inactiveShares.length > 0) {
      auditFindings.push(`Found ${inactiveShares.length} inactive share purchases`)
      auditStatus = 'WARNING'
    }

    // Verify commission transactions are approved
    const unapprovedCommissions = commissionTransactions.filter(t => t.status !== 'approved')
    if (unapprovedCommissions.length > 0) {
      auditFindings.push(`Found ${unapprovedCommissions.length} unapproved commission transactions`)
      auditStatus = 'WARNING'
    }

    // Verify commission conversions are approved
    const unapprovedConversions = commissionConversions.filter(c => c.status !== 'approved')
    if (unapprovedConversions.length > 0) {
      auditFindings.push(`Found ${unapprovedConversions.length} unapproved commission conversions`)
      auditStatus = 'WARNING'
    }

    // Add conversion summary to findings
    if (commissionConversions.length > 0) {
      const approvedConversions = commissionConversions.filter(c => c.status === 'approved')
      auditFindings.push(`Commission conversions: ${approvedConversions.length} approved conversions totaling ${calculations.totalConvertedShares} shares from $${calculations.totalUsdtConverted}`)
    }

    // Generate recommendations
    if (auditStatus === 'PASSED') {
      auditFindings.push('All financial records verified and accurate')
      recommendations.push('No action required - all financial data is consistent')
    } else {
      recommendations.push('Review and resolve identified discrepancies')
      recommendations.push('Consider running commission recalculation if needed')
    }

    return { auditStatus, auditFindings, recommendations }
  }
}

export const financialAuditService = new FinancialAuditService()
