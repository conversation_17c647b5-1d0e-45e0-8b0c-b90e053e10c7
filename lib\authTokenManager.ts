import { supabase } from './supabase'
import crypto from 'crypto'

/**
 * SECURE WEB AUTHENTICATION TOKEN MANAGER
 * 
 * This module manages secure token generation and validation for
 * web-to-Telegram authentication flows.
 */

export interface AuthToken {
  token: string
  telegramId?: number
  userData?: any
  userStatus?: string
  confirmed: boolean
  cancelled: boolean
  expiresAt: Date
  createdAt: Date
}

export interface TokenValidationResult {
  valid: boolean
  token?: AuthToken
  error?: string
}

export class AuthTokenManager {
  /**
   * Generate a simple 6-digit PIN for user-friendly authentication
   * @returns {string} - 6-digit PIN code
   */
  static generateWebAuthToken(): string {
    // Generate simple 6-digit PIN for better user experience
    return Math.floor(100000 + Math.random() * 900000).toString()
  }

  /**
   * Generate a secure session token
   * @returns {string} - 64-character hex session token
   */
  static generateSessionToken(): string {
    return crypto.randomBytes(32).toString('hex')
  }

  /**
   * Generate a secure API token
   * @returns {string} - 96-character hex API token
   */
  static generateApiToken(): string {
    return crypto.randomBytes(48).toString('hex')
  }

  /**
   * Create and store a new authentication token
   * @param {number} telegramId - Optional Telegram ID to associate
   * @param {number} validityMinutes - Token validity in minutes (default: 10)
   * @returns {Promise<string>} - The generated token
   */
  static async createAuthToken(telegramId?: number, validityMinutes: number = 10): Promise<string> {
    try {
      console.log('🔐 Creating secure authentication token...')

      const token = this.generateWebAuthToken()
      const expiresAt = new Date(Date.now() + validityMinutes * 60 * 1000)

      // Validate token security before storing
      const validation = this.validateTokenSecurity(token)
      if (!validation.valid) {
        throw new Error(`Generated token failed security validation: ${validation.issues.join(', ')}`)
      }

      const { error } = await supabase
        .from('auth_tokens')
        .insert({
          token,
          telegram_id: telegramId,
          expires_at: expiresAt.toISOString(),
          confirmed: false,
          cancelled: false,
          created_at: new Date().toISOString()
        })

      if (error) {
        console.error('Failed to store auth token:', error)
        throw new Error(`Failed to create auth token: ${error.message}`)
      }

      console.log(`✅ Secure auth token created: ${token.substring(0, 24)}... (expires in ${validityMinutes}m)`)
      return token

    } catch (error: any) {
      console.error('Auth token creation failed:', error)
      throw new Error(`Token creation failed: ${error.message}`)
    }
  }

  /**
   * Validate an authentication token
   * @param {string} token - Token to validate
   * @returns {Promise<TokenValidationResult>} - Validation result
   */
  static async validateAuthToken(token: string): Promise<TokenValidationResult> {
    try {
      // Basic format validation
      const securityValidation = this.validateTokenSecurity(token)
      if (!securityValidation.valid) {
        return {
          valid: false,
          error: `Token security validation failed: ${securityValidation.issues.join(', ')}`
        }
      }

      // Database validation
      const { data: tokenData, error } = await supabase
        .from('auth_tokens')
        .select('*')
        .eq('token', token)
        .single()

      if (error || !tokenData) {
        return {
          valid: false,
          error: 'Token not found or invalid'
        }
      }

      // Check expiration
      const now = new Date()
      const expiresAt = new Date(tokenData.expires_at)
      
      if (now > expiresAt) {
        return {
          valid: false,
          error: 'Token has expired'
        }
      }

      // Check if cancelled
      if (tokenData.cancelled) {
        return {
          valid: false,
          error: 'Token has been cancelled'
        }
      }

      return {
        valid: true,
        token: {
          token: tokenData.token,
          telegramId: tokenData.telegram_id,
          userData: tokenData.user_data,
          userStatus: tokenData.user_status,
          confirmed: tokenData.confirmed,
          cancelled: tokenData.cancelled,
          expiresAt: new Date(tokenData.expires_at),
          createdAt: new Date(tokenData.created_at)
        }
      }

    } catch (error: any) {
      console.error('Token validation failed:', error)
      return {
        valid: false,
        error: `Token validation error: ${error.message}`
      }
    }
  }

  /**
   * Update token status (confirm or cancel)
   * @param {string} token - Token to update
   * @param {object} updates - Updates to apply
   * @returns {Promise<boolean>} - Success status
   */
  static async updateTokenStatus(token: string, updates: {
    confirmed?: boolean
    cancelled?: boolean
    userData?: any
    userStatus?: string
  }): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('auth_tokens')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('token', token)

      if (error) {
        console.error('Failed to update token status:', error)
        return false
      }

      console.log(`✅ Token status updated: ${token.substring(0, 24)}...`)
      return true

    } catch (error: any) {
      console.error('Token status update failed:', error)
      return false
    }
  }

  /**
   * Clean up expired tokens
   * @returns {Promise<number>} - Number of tokens cleaned up
   */
  static async cleanupExpiredTokens(): Promise<number> {
    try {
      console.log('🧹 Cleaning up expired authentication tokens...')

      const { data: expiredTokens, error: selectError } = await supabase
        .from('auth_tokens')
        .select('token')
        .lt('expires_at', new Date().toISOString())

      if (selectError) {
        console.error('Failed to fetch expired tokens:', selectError)
        return 0
      }

      if (!expiredTokens || expiredTokens.length === 0) {
        console.log('✅ No expired tokens to clean up')
        return 0
      }

      const { error: deleteError } = await supabase
        .from('auth_tokens')
        .delete()
        .lt('expires_at', new Date().toISOString())

      if (deleteError) {
        console.error('Failed to delete expired tokens:', deleteError)
        return 0
      }

      console.log(`✅ Cleaned up ${expiredTokens.length} expired tokens`)
      return expiredTokens.length

    } catch (error: any) {
      console.error('Token cleanup failed:', error)
      return 0
    }
  }

  /**
   * Validate PIN format
   * @param {string} token - PIN to validate
   * @returns {object} - Validation result with security details
   */
  static validateTokenSecurity(token: string): {
    valid: boolean
    issues: string[]
    securityLevel: string
  } {
    const validation = {
      valid: true,
      issues: [] as string[],
      securityLevel: 'unknown'
    }

    // Check if token exists
    if (!token || typeof token !== 'string') {
      validation.valid = false
      validation.issues.push('PIN is missing or not a string')
      validation.securityLevel = 'invalid'
      return validation
    }

    // Check if it's exactly 6 digits
    if (!/^\d{6}$/.test(token)) {
      validation.valid = false
      validation.issues.push('PIN must be exactly 6 digits')
      validation.securityLevel = 'invalid'
      return validation
    }

    // Check for obvious patterns
    if (/(\d)\1{5}/.test(token)) {
      validation.issues.push('PIN contains repeated digits')
      validation.securityLevel = 'weak'
    }

    // Check for sequential patterns
    if (/123456|654321|012345|543210/.test(token)) {
      validation.issues.push('PIN contains sequential pattern')
      validation.securityLevel = 'weak'
    }

    // Determine security level
    if (validation.valid) {
      if (validation.issues.length === 0) {
        validation.securityLevel = 'medium'
      } else {
        validation.securityLevel = 'weak'
      }
    }

    return validation
  }

  /**
   * Get token statistics for monitoring
   * @returns {Promise<object>} - Token statistics
   */
  static async getTokenStatistics(): Promise<{
    total: number
    active: number
    expired: number
    confirmed: number
    cancelled: number
  }> {
    try {
      const { data: tokens, error } = await supabase
        .from('auth_tokens')
        .select('confirmed, cancelled, expires_at')

      if (error || !tokens) {
        return { total: 0, active: 0, expired: 0, confirmed: 0, cancelled: 0 }
      }

      const now = new Date()
      const stats = {
        total: tokens.length,
        active: 0,
        expired: 0,
        confirmed: 0,
        cancelled: 0
      }

      tokens.forEach(token => {
        const expiresAt = new Date(token.expires_at)
        
        if (now > expiresAt) {
          stats.expired++
        } else {
          stats.active++
        }

        if (token.confirmed) {
          stats.confirmed++
        }

        if (token.cancelled) {
          stats.cancelled++
        }
      })

      return stats

    } catch (error: any) {
      console.error('Failed to get token statistics:', error)
      return { total: 0, active: 0, expired: 0, confirmed: 0, cancelled: 0 }
    }
  }

  /**
   * Test the token management system
   * @returns {Promise<boolean>} - True if all tests pass
   */
  static async testTokenSystem(): Promise<boolean> {
    try {
      console.log('🧪 Testing secure token management system...')

      // Test 1: Token generation
      const token1 = this.generateWebAuthToken()
      const token2 = this.generateWebAuthToken()

      if (token1 === token2) {
        console.error('❌ Token uniqueness test failed')
        return false
      }

      console.log(`   ✓ Token generation: ${token1.substring(0, 24)}... (${token1.length} chars)`)

      // Test 2: Token security validation
      const validation = this.validateTokenSecurity(token1)
      if (!validation.valid) {
        console.error('❌ Token security validation failed:', validation.issues)
        return false
      }

      console.log(`   ✓ Token security validation passed (${validation.securityLevel} security)`)

      // Test 3: Token creation and validation (without actually storing)
      const testToken = this.generateWebAuthToken()
      const securityCheck = this.validateTokenSecurity(testToken)
      
      if (!securityCheck.valid) {
        console.error('❌ Generated token failed security check')
        return false
      }

      console.log('   ✓ Token creation and validation working')

      console.log('✅ All token management tests passed')
      return true

    } catch (error: any) {
      console.error('❌ Token system test failed:', error)
      return false
    }
  }
}
