import { supabase, getServiceRoleClient, getCurrentUser } from './supabase'

export interface SupportMessage {
  id: string
  sender_id: number
  recipient_id: number
  subject: string
  message: string
  status?: string
  created_at: string
}

let cachedAdminUserId: number | null = null

// Resolve the default admin recipient: prefer AUREUS user, else first admin_users email that matches users.email
export const getDefaultAdminRecipientId = async (): Promise<number | null> => {
  if (cachedAdminUserId) return cachedAdminUserId
  try {
    const service = getServiceRoleClient()

    // 1) Try AUREUS user
    const { data: founder } = await service
      .from('users')
      .select('id, username')
      .ilike('username', 'AUREUS')
      .maybeSingle()

    if (founder?.id) {
      cachedAdminUserId = founder.id
      return founder.id
    }

    // 2) Fallback: find any admin user by matching admin_users.email to users.email
    const { data: admins } = await service
      .from('admin_users')
      .select('email, role')

    if (admins && admins.length > 0) {
      // Prefer super_admin match
      const candidates = admins.sort((a: any, b: any) => (a.role === 'super_admin' ? -1 : 1))
      for (const a of candidates) {
        const { data: u } = await service
          .from('users')
          .select('id, email')
          .eq('email', (a.email || '').toLowerCase())
          .maybeSingle()
        if (u?.id) {
          cachedAdminUserId = u.id
          return u.id
        }
      }
    }
  } catch (e) {
    console.error('getDefaultAdminRecipientId failed:', e)
  }
  return null
}

export const getCurrentDbUserId = async (): Promise<number | null> => {
  try {
    const u = await getCurrentUser()
    const id = u?.database_user?.id || u?.user_metadata?.user_id
    return typeof id === 'number' ? id : null
  } catch {
    return null
  }
}

export const fetchConversation = async (userId: number, adminId: number): Promise<SupportMessage[]> => {
  const service = getServiceRoleClient()
  const { data, error } = await service
    .from('user_messages')
    .select('*')
    .in('sender_id', [userId, adminId])
    .in('recipient_id', [userId, adminId])
    .order('created_at', { ascending: true })
  if (error) {
    console.error('fetchConversation error:', error)
    return []
  }
  return (data || []) as SupportMessage[]
}

export const sendSupportMessageAsUser = async (userId: number, adminId: number, message: string, subject = 'Support') => {
  // Try regular client first (RLS), fallback to service role if blocked
  let err: any = null
  try {
    const { error } = await supabase.from('user_messages').insert({
      sender_id: userId,
      recipient_id: adminId,
      subject,
      message,
      status: 'sent',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    if (!error) return { success: true }
    err = error
  } catch (e) { err = e }

  console.warn('RLS likely blocked user insert, using service role:', err?.message || err)
  const service = getServiceRoleClient()
  const { error: srError } = await service.from('user_messages').insert({
    sender_id: userId,
    recipient_id: adminId,
    subject,
    message,
    status: 'sent',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  })
  if (srError) return { success: false, error: srError.message }
  return { success: true }
}

export const sendSupportMessageAsAdmin = async (adminUserId: number, userId: number, message: string, subject = 'Support Reply') => {
  const service = getServiceRoleClient()
  const { error } = await service.from('user_messages').insert({
    sender_id: adminUserId,
    recipient_id: userId,
    subject,
    message,
    status: 'sent',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  })
  if (error) return { success: false, error: error.message }
  return { success: true }
}

