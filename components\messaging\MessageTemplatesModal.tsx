import React, { useState } from 'react';

interface MessageTemplate {
  id: string;
  name: string;
  subject: string;
  content: string;
  category: 'business' | 'support' | 'personal' | 'system';
}

interface MessageTemplatesModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectTemplate: (template: MessageTemplate) => void;
}

const DEFAULT_TEMPLATES: MessageTemplate[] = [
  {
    id: '1',
    name: 'Welcome Message',
    subject: 'Welcome to Aureus Alliance Holdings',
    content: 'Dear {{name}},\n\nWelcome to Aureus Alliance Holdings! We are excited to have you as part of our community.\n\nIf you have any questions, please don\'t hesitate to reach out.\n\nBest regards,\nThe Aureus Team',
    category: 'business'
  },
  {
    id: '2',
    name: 'Payment Confirmation',
    subject: 'Payment Confirmation - {{amount}}',
    content: 'Dear {{name}},\n\nThis is to confirm that we have received your payment of {{amount}}.\n\nTransaction details:\n- Amount: {{amount}}\n- Date: {{date}}\n- Reference: {{reference}}\n\nThank you for your business.\n\nBest regards,\nThe Aureus Team',
    category: 'business'
  },
  {
    id: '3',
    name: 'Support Follow-up',
    subject: 'Following up on your inquiry',
    content: 'Dear {{name}},\n\nI wanted to follow up on your recent inquiry to ensure everything has been resolved to your satisfaction.\n\nIf you need any further assistance, please let me know.\n\nBest regards,\n{{sender_name}}',
    category: 'support'
  },
  {
    id: '4',
    name: 'Commission Update',
    subject: 'Commission Update - {{period}}',
    content: 'Dear {{name}},\n\nYour commission for {{period}} has been processed:\n\n- USDT Commission: {{usdt_amount}}\n- Share Commission: {{share_amount}}\n- Total Referrals: {{referral_count}}\n\nThank you for your continued partnership.\n\nBest regards,\nThe Aureus Team',
    category: 'business'
  },
  {
    id: '5',
    name: 'General Inquiry Response',
    subject: 'Re: Your Inquiry',
    content: 'Dear {{name}},\n\nThank you for your message. I have reviewed your inquiry and will provide you with the information you requested.\n\n{{custom_response}}\n\nIf you have any additional questions, please feel free to ask.\n\nBest regards,\n{{sender_name}}',
    category: 'support'
  },
  {
    id: '6',
    name: 'Meeting Request',
    subject: 'Meeting Request - {{topic}}',
    content: 'Dear {{name}},\n\nI would like to schedule a meeting to discuss {{topic}}.\n\nProposed times:\n- {{time_option_1}}\n- {{time_option_2}}\n- {{time_option_3}}\n\nPlease let me know which time works best for you, or suggest an alternative.\n\nBest regards,\n{{sender_name}}',
    category: 'personal'
  }
];

export const MessageTemplatesModal: React.FC<MessageTemplatesModalProps> = ({
  isOpen,
  onClose,
  onSelectTemplate
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');

  const filteredTemplates = DEFAULT_TEMPLATES.filter(template => {
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    const matchesSearch = !searchQuery || 
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.content.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesCategory && matchesSearch;
  });

  const handleSelectTemplate = (template: MessageTemplate) => {
    onSelectTemplate(template);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-lg border border-gray-700 w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <h2 className="text-xl font-bold text-white">📝 Message Templates</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white text-2xl"
          >
            ×
          </button>
        </div>

        {/* Search and Filter */}
        <div className="p-6 border-b border-gray-700">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search templates..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Categories</option>
              <option value="business">Business</option>
              <option value="support">Support</option>
              <option value="personal">Personal</option>
              <option value="system">System</option>
            </select>
          </div>
        </div>

        {/* Templates List */}
        <div className="p-6 max-h-96 overflow-y-auto">
          {filteredTemplates.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-4xl mb-4">📄</div>
              <h3 className="text-white text-lg mb-2">No Templates Found</h3>
              <p className="text-gray-400">Try adjusting your search or filter criteria.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredTemplates.map((template) => (
                <div
                  key={template.id}
                  className="bg-gray-700 rounded-lg border border-gray-600 p-4 hover:bg-gray-600 cursor-pointer transition-colors"
                  onClick={() => handleSelectTemplate(template)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-medium text-white">{template.name}</h3>
                    <span className={`text-xs px-2 py-1 rounded ${
                      template.category === 'business' ? 'bg-blue-600/20 text-blue-400' :
                      template.category === 'support' ? 'bg-green-600/20 text-green-400' :
                      template.category === 'personal' ? 'bg-purple-600/20 text-purple-400' :
                      'bg-gray-600/20 text-gray-400'
                    }`}>
                      {template.category}
                    </span>
                  </div>
                  <p className="text-sm text-gray-300 mb-2 font-medium">{template.subject}</p>
                  <p className="text-sm text-gray-400 line-clamp-3">
                    {template.content.substring(0, 120)}...
                  </p>
                  <div className="mt-3 flex justify-end">
                    <button className="text-blue-400 hover:text-blue-300 text-sm font-medium">
                      Use Template →
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-700 bg-gray-750">
          <div className="flex justify-between items-center">
            <p className="text-sm text-gray-400">
              💡 Templates support variables like {{name}}, {{amount}}, {{date}}
            </p>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
