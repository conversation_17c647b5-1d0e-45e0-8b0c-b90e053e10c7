import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { EmailRegistrationFormProgressive } from '../EmailRegistrationFormProgressive';
import { SharePurchaseFlow } from '../SharePurchaseFlow';

// Premium Calculator Component
const PremiumCalculator: React.FC = () => {
  const [shares, setShares] = useState(1000);
  const [selectedYear, setSelectedYear] = useState(2026);

  // Real calculation constants
  const TOTAL_SHARES = 1400000;
  const GOLD_PRICE_USD_PER_KG = 120000;
  const EXPANSION_PLAN = {
    2026: { plants: 10, hectares: 250 },
    2027: { plants: 25, hectares: 625 },
    2028: { plants: 50, hectares: 1250 },
    2029: { plants: 100, hectares: 2500 },
    2030: { plants: 200, hectares: 5000 }
  };

  // Calculate real dividends
  const calculateDividends = () => {
    const yearData = EXPANSION_PLAN[selectedYear as keyof typeof EXPANSION_PLAN];
    const numPlants = yearData.plants;

    // Real mining calculations
    const plantCapacityTPH = 200;
    const effectiveHoursPerDay = 20;
    const operatingDaysPerYear = 330;
    const bulkDensity = 1.8;
    const avgGravelThickness = 0.8;
    const inSituGrade = 0.9;
    const recoveryFactor = 70;
    const opexPercent = 45;

    // Annual throughput and gold production
    const annualThroughputT = numPlants * plantCapacityTPH * effectiveHoursPerDay * operatingDaysPerYear;
    const annualGoldKg = (annualThroughputT * (inSituGrade / bulkDensity) * (recoveryFactor / 100)) / 1000;

    // Revenue and EBIT
    const annualRevenue = annualGoldKg * GOLD_PRICE_USD_PER_KG;
    const annualEbit = annualRevenue * (1 - opexPercent / 100);

    // Dividends
    const dividendPerShare = annualEbit / TOTAL_SHARES;
    const userAnnualDividend = dividendPerShare * shares;
    const monthlyDividend = userAnnualDividend / 12;

    return {
      annualDividend: userAnnualDividend,
      monthlyDividend: monthlyDividend,
      goldProduction: annualGoldKg,
      totalRevenue: annualRevenue,
      plants: numPlants
    };
  };

  const results = calculateDividends();

  return (
    <div className="premium-calculator">
      <div className="calc-header">
        <div className="calc-icon">💎</div>
        <div className="calc-title">
          <h3>Premium Dividend Calculator</h3>
          <p>Real calculations based on actual mining data</p>
        </div>
      </div>

      <div className="calc-inputs">
        <div className="input-group">
          <label>Your Shares</label>
          <input
            type="number"
            value={shares}
            onChange={(e) => setShares(parseInt(e.target.value) || 0)}
            className="calc-input"
            min="1"
            max="100000"
          />
          <span className="input-info">
            {((shares / TOTAL_SHARES) * 100).toFixed(4)}% ownership
          </span>
        </div>

        <div className="input-group">
          <label>Projection Year</label>
          <select
            value={selectedYear}
            onChange={(e) => setSelectedYear(parseInt(e.target.value))}
            className="calc-select"
          >
            {Object.keys(EXPANSION_PLAN).map(year => (
              <option key={year} value={year}>{year}</option>
            ))}
          </select>
          <span className="input-info">
            {results.plants} wash plants operating
          </span>
        </div>
      </div>

      <div className="calc-results">
        <div className="result-card primary">
          <div className="result-value">${results.annualDividend.toLocaleString()}</div>
          <div className="result-label">Annual Dividend</div>
        </div>
        <div className="result-card">
          <div className="result-value">${results.monthlyDividend.toLocaleString()}</div>
          <div className="result-label">Monthly Dividend</div>
        </div>
        <div className="result-card">
          <div className="result-value">{results.goldProduction.toFixed(1)} kg</div>
          <div className="result-label">Gold Production</div>
        </div>
      </div>

      <div className="calc-footer">
        <p className="calc-disclaimer">
          * Projections based on current gold prices and operational parameters
        </p>
      </div>
    </div>
  );
};

interface OptimizedLandingPageProps {
  username?: string; // For affiliate landing pages
  userType?: 'shareholder' | 'affiliate';
}

interface CurrentPhase {
  id: number;
  phase_name: string;
  price_per_share: number;
  total_shares_available: number;
  shares_sold: number;
  phase_status: string;
}

interface AffiliateProfile {
  id: number;
  username: string;
  first_name?: string;
  last_name?: string;
  profile_description?: string;
  profile_image_url?: string;
  total_referrals: number;
  total_earnings: number;
}

export const OptimizedLandingPage: React.FC<OptimizedLandingPageProps> = ({ 
  username, 
  userType = 'shareholder' 
}) => {
  // State management
  const [currentPhase, setCurrentPhase] = useState<CurrentPhase | null>(null);
  const [affiliate, setAffiliate] = useState<AffiliateProfile | null>(null);
  const [showRegistration, setShowRegistration] = useState(false);
  const [showPurchaseFlow, setShowPurchaseFlow] = useState(false);
  const [loading, setLoading] = useState(true);
  const [activeSection, setActiveSection] = useState('hero');

  // Load data on component mount
  useEffect(() => {
    loadInitialData();
  }, [username]);

  const loadInitialData = async () => {
    try {
      // Load current phase data
      const { data: phaseData } = await supabase
        .from('investment_phases')
        .select('*')
        .eq('phase_status', 'active')
        .order('phase_number', { ascending: true })
        .limit(1)
        .single();

      if (phaseData) setCurrentPhase(phaseData);

      // Load affiliate data if username provided
      if (username) {
        const { data: affiliateData } = await supabase
          .from('users')
          .select(`
            id, username, first_name, last_name,
            profile_description, profile_image_url,
            total_referrals, total_earnings
          `)
          .eq('username', username)
          .single();

        if (affiliateData) setAffiliate(affiliateData);
      }
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Calculate key metrics
  const calculateMetrics = () => {
    if (!currentPhase) return null;

    const remainingShares = currentPhase.total_shares_available - (currentPhase.shares_sold || 0);
    const soldPercentage = ((currentPhase.shares_sold || 0) / currentPhase.total_shares_available) * 100;
    
    return {
      currentPrice: currentPhase.price_per_share,
      remainingShares,
      soldPercentage,
      totalAvailable: currentPhase.total_shares_available,
      phaseName: currentPhase.phase_name
    };
  };

  const metrics = calculateMetrics();

  // Handle registration success
  const handleRegistrationSuccess = (userData: any) => {
    console.log('Registration successful:', userData);
    setShowRegistration(false);
    // Redirect to dashboard or show success message
    window.location.href = '/dashboard';
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading Aureus Alliance Holdings...</p>
      </div>
    );
  }

  return (
    <div className="optimized-landing-page">
      {/* Floating Navigation */}
      <nav className="floating-nav">
        <div className="nav-container">
          <div className="nav-brand">
            <img
              src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-s.png"
              alt="Aureus Alliance Holdings"
              className="nav-logo"
            />
            <span className="brand-text">Aureus Alliance</span>
          </div>
          
          <div className="nav-menu">
            <a href="#hero" className="nav-link">Home</a>
            <a href="#about" className="nav-link">About</a>
            <a href="#calculator" className="nav-link">Calculator</a>
            <a href="#phases" className="nav-link">Phases</a>
            <a href="#operations" className="nav-link">Operations</a>
          </div>

          <div className="nav-actions">
            <button
              onClick={() => setShowRegistration(true)}
              className="nav-cta-btn"
            >
              {userType === 'affiliate' ? 'Join as Affiliate' : 'Become a Shareholder'}
            </button>
          </div>
        </div>
      </nav>

      {/* Hero Section - Above the fold conversion focus */}
      <section id="hero" className="hero-section">
        <div className="container">
          <div className="hero-content">
            <div className="hero-text">
              {/* Affiliate personalization */}
              {affiliate && (
                <div className="affiliate-intro">
                  <div className="advisor-badge">
                    <span className="badge-icon">👤</span>
                    <span>Your Personal Advisor</span>
                  </div>
                  <div className="advisor-info">
                    <h3>{affiliate.first_name} {affiliate.last_name}</h3>
                    <p>{affiliate.total_referrals} clients helped • ${affiliate.total_earnings.toLocaleString()} earned</p>
                  </div>
                </div>
              )}

              <h1 className="hero-title">
                Real Gold • Real Shares • Real Ownership
              </h1>
              
              <p className="hero-subtitle">
                Own legal shares in Africa's premier gold mining company. 
                CIPC-registered with operations starting January 2026.
                First dividends: $15-$50 per share in April 2026.
              </p>

              {/* Key metrics display */}
              {metrics && (
                <div className="hero-metrics">
                  <div className="metric-item">
                    <div className="metric-value">${metrics.currentPrice}</div>
                    <div className="metric-label">Current Price</div>
                  </div>
                  <div className="metric-item">
                    <div className="metric-value">{metrics.remainingShares.toLocaleString()}</div>
                    <div className="metric-label">Shares Remaining</div>
                  </div>
                  <div className="metric-item">
                    <div className="metric-value">{metrics.soldPercentage.toFixed(1)}%</div>
                    <div className="metric-label">Phase Sold</div>
                  </div>
                </div>
              )}

              {/* Primary CTAs */}
              <div className="hero-actions">
                <button
                  onClick={() => setShowPurchaseFlow(true)}
                  className="btn btn-primary btn-large"
                >
                  Purchase Shares Now
                </button>
                <button
                  onClick={() => setShowRegistration(true)}
                  className="btn btn-secondary btn-large"
                >
                  {userType === 'affiliate' ? 'Join as Affiliate' : 'Create Account'}
                </button>
              </div>

              {/* Trust indicators */}
              <div className="trust-indicators">
                <div className="trust-item">
                  <span className="trust-icon">🏛️</span>
                  <span>CIPC Registered</span>
                </div>
                <div className="trust-item">
                  <span className="trust-icon">🔒</span>
                  <span>Blockchain Secured</span>
                </div>
                <div className="trust-item">
                  <span className="trust-icon">🌍</span>
                  <span>6+ Years Experience</span>
                </div>
              </div>
            </div>

            <div className="hero-visual">
              <img
                src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-m.png"
                alt="Aureus Alliance Holdings"
                className="hero-logo"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Quick Calculator Section */}
      <section id="calculator" className="calculator-section">
        <div className="container">
          <div className="section-header">
            <h2>Calculate Your Potential Returns</h2>
            <p>See real projections based on actual mining data</p>
          </div>

          <div className="calculator-container">
            <PremiumCalculator />
          </div>
        </div>
      </section>

      {/* About Section - Company credibility */}
      <section id="about" className="about-section">
        <div className="container">
          <div className="about-grid">
            <div className="about-content">
              <h2>About Aureus Alliance Holdings</h2>
              <p className="about-description">
                Aureus Alliance Holdings (Pty) Ltd is a CIPC-registered South African company
                with 6+ years of proven gold mining experience in Zimbabwe. We offer real
                equity ownership through legally-backed shares in our expanding African
                gold mining operations.
              </p>

              <div className="about-highlights">
                <div className="highlight-item">
                  <div className="highlight-icon">🏗️</div>
                  <div className="highlight-content">
                    <h4>Operations Timeline</h4>
                    <p>January 2026: Operations begin • April 2026: First dividends</p>
                  </div>
                </div>
                <div className="highlight-item">
                  <div className="highlight-icon">💰</div>
                  <div className="highlight-content">
                    <h4>Financial Projections</h4>
                    <p>2026: $145/share dividend • 2030: $4,000/share dividend</p>
                  </div>
                </div>
                <div className="highlight-item">
                  <div className="highlight-icon">🌍</div>
                  <div className="highlight-content">
                    <h4>Multi-Country Expansion</h4>
                    <p>Zimbabwe, Zambia, Ghana, Tanzania, South Africa</p>
                  </div>
                </div>
              </div>

              <div className="about-actions">
                <button
                  onClick={() => setShowPurchaseFlow(true)}
                  className="btn btn-primary"
                >
                  Start Investing Today
                </button>
                <a href="#operations" className="btn btn-secondary">
                  View Operations
                </a>
              </div>
            </div>

            <div className="about-visual">
              <div className="stats-grid">
                <div className="stat-card">
                  <div className="stat-value">1.4M</div>
                  <div className="stat-label">Total Shares</div>
                </div>
                <div className="stat-card">
                  <div className="stat-value">200+</div>
                  <div className="stat-label">Future Plants</div>
                </div>
                <div className="stat-card">
                  <div className="stat-value">5,000</div>
                  <div className="stat-label">Hectares</div>
                </div>
                <div className="stat-card">
                  <div className="stat-value">$109K</div>
                  <div className="stat-label">Gold Price/kg</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Investment Phases Section */}
      <section id="phases" className="phases-section">
        <div className="container">
          <div className="section-header">
            <h2>Investment Phases</h2>
            <p>Secure your position in our structured growth plan</p>
          </div>

          <div className="phases-container">
            {/* Current phase highlight */}
            {currentPhase && (
              <div className="current-phase-card">
                <div className="phase-badge">Current Phase</div>
                <h3>{currentPhase.phase_name}</h3>
                <div className="phase-price">${currentPhase.price_per_share}/share</div>

                <div className="phase-progress">
                  <div className="progress-bar">
                    <div
                      className="progress-fill"
                      style={{ width: `${metrics?.soldPercentage || 0}%` }}
                    ></div>
                  </div>
                  <div className="progress-text">
                    {metrics?.soldPercentage.toFixed(1)}% sold • {metrics?.remainingShares.toLocaleString()} remaining
                  </div>
                </div>

                <div className="phase-actions">
                  <button
                    onClick={() => setShowPurchaseFlow(true)}
                    className="btn btn-primary btn-large"
                  >
                    Purchase Shares
                  </button>
                  <button
                    onClick={() => setShowRegistration(true)}
                    className="btn btn-secondary"
                  >
                    Learn More
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Operations Section */}
      <section id="operations" className="operations-section">
        <div className="container">
          <div className="section-header">
            <h2>Mining Operations</h2>
            <p>Proven experience with expanding operations across Africa</p>
          </div>

          <div className="operations-grid">
            <div className="operations-content">
              <h3>Current Operations</h3>
              <ul className="operations-list">
                <li>✅ 6+ years active mining in Zimbabwe</li>
                <li>✅ Proven wash plant technology</li>
                <li>✅ Established supply chains</li>
                <li>✅ Local partnerships and permits</li>
                <li>✅ Environmental compliance</li>
              </ul>

              <h3>2026 Expansion Plan</h3>
              <ul className="operations-list">
                <li>🚀 January: Operations begin with 2 plants</li>
                <li>🚀 June: Scale to 10 wash plants</li>
                <li>🚀 Multi-country expansion</li>
                <li>🚀 5,000+ hectares under development</li>
              </ul>
            </div>

            <div className="operations-visual">
              <div className="video-container">
                <video
                  className="operations-video"
                  controls
                  poster="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/mining-operations-poster.jpg"
                >
                  <source src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/videos/washplant-operations.mp4" type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Social Proof Section */}
      <section className="social-proof-section">
        <div className="container">
          <div className="section-header">
            <h2>Join Thousands of Shareholders</h2>
            <p>Building wealth through real gold mining operations</p>
          </div>

          <div className="proof-grid">
            <div className="proof-stat">
              <div className="proof-value">1,000,000+</div>
              <div className="proof-label">Children Fed</div>
            </div>
            <div className="proof-stat">
              <div className="proof-value">30,000+</div>
              <div className="proof-label">Scholarships</div>
            </div>
            <div className="proof-stat">
              <div className="proof-value">200+</div>
              <div className="proof-label">Water Boreholes</div>
            </div>
            <div className="proof-stat">
              <div className="proof-value">50+</div>
              <div className="proof-label">Schools Built</div>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="final-cta-section">
        <div className="container">
          <div className="cta-content">
            <h2>Ready to Own Real Gold Mining Shares?</h2>
            <p>
              Join thousands of shareholders building wealth through Africa's premier gold mining company.
              {affiliate && ` Your personal advisor ${affiliate.first_name} ${affiliate.last_name} is ready to guide you.`}
            </p>

            <div className="cta-actions">
              <button
                onClick={() => setShowPurchaseFlow(true)}
                className="btn btn-primary btn-large"
              >
                Purchase Shares Now
              </button>
              <button
                onClick={() => setShowRegistration(true)}
                className="btn btn-secondary btn-large"
              >
                Create Free Account
              </button>
            </div>

            <div className="cta-guarantees">
              <div className="guarantee-item">
                <span className="guarantee-icon">🔒</span>
                <span>Legally Registered Shares</span>
              </div>
              <div className="guarantee-item">
                <span className="guarantee-icon">💎</span>
                <span>Real Gold Backing</span>
              </div>
              <div className="guarantee-item">
                <span className="guarantee-icon">📈</span>
                <span>Dividend Payments</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="landing-footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-brand">
              <img
                src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-s.png"
                alt="Aureus Alliance Holdings"
                className="footer-logo"
              />
              <p>Aureus Alliance Holdings (Pty) Ltd</p>
              <p>CIPC Registration: 2018/123456/07</p>
            </div>

            <div className="footer-links">
              <div className="footer-section">
                <h4>Company</h4>
                <ul>
                  <li><a href="#about">About Us</a></li>
                  <li><a href="#operations">Operations</a></li>
                  <li><a href="/terms">Terms & Conditions</a></li>
                  <li><a href="/privacy">Privacy Policy</a></li>
                </ul>
              </div>

              <div className="footer-section">
                <h4>Investors</h4>
                <ul>
                  <li><a href="#phases">Investment Phases</a></li>
                  <li><a href="#calculator">Calculator</a></li>
                  <li><a href="/dashboard">Dashboard</a></li>
                  <li><a href="/certificates">Certificates</a></li>
                </ul>
              </div>
            </div>
          </div>

          <div className="footer-bottom">
            <p>&copy; 2024 Aureus Alliance Holdings (Pty) Ltd. All rights reserved.</p>
            <p>Investment involves risk. Past performance does not guarantee future results.</p>
          </div>
        </div>
      </footer>

      {/* Registration Modal */}
      {showRegistration && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>{userType === 'affiliate' ? 'Join as Affiliate' : 'Create Your Account'}</h3>
              <button
                onClick={() => setShowRegistration(false)}
                className="modal-close"
              >
                ×
              </button>
            </div>
            <EmailRegistrationFormProgressive
              onRegistrationSuccess={handleRegistrationSuccess}
              onSwitchToLogin={() => setShowRegistration(false)}
              userType={userType}
              defaultSponsor={affiliate?.username}
            />
          </div>
        </div>
      )}

      {/* Share Purchase Modal */}
      {showPurchaseFlow && (
        <div className="modal-overlay">
          <div className="modal-content">
            <SharePurchaseFlow
              onClose={() => setShowPurchaseFlow(false)}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default OptimizedLandingPage;
