/**
 * AUREUS ALLIANCE - SPONSOR CHANGE SYSTEM
 * 
 * This script provides comprehensive sponsor change functionality for admins.
 * It safely updates all related tables and maintains data integrity.
 * 
 * Usage:
 * node sponsor-change-system.js
 * 
 * Features:
 * - Change user sponsors with full audit trail
 * - Update all related commission and referral tables
 * - Maintain data integrity with transaction safety
 * - Comprehensive logging and validation
 */

import { createClient } from '@supabase/supabase-js'

// Supabase configuration
const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA'

// Create admin client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

/**
 * Change user sponsor with full database updates
 * @param {string|number} userId - User ID or username to change sponsor for
 * @param {string|number} newSponsorId - New sponsor ID or username
 * @param {string} adminUsername - Admin performing the change
 * @param {string} reason - Reason for sponsor change
 */
async function changeSponsor(userId, newSponsorId, adminUsername = 'TTTFOUNDER', reason = 'Admin sponsor change') {
  console.log('🔄 SPONSOR CHANGE SYSTEM INITIATED')
  console.log('=====================================')
  
  try {
    // Step 1: Resolve user and sponsor IDs
    const user = await resolveUser(userId)
    const newSponsor = await resolveUser(newSponsorId)
    
    if (!user || !newSponsor) {
      throw new Error('User or sponsor not found')
    }
    
    console.log(`👤 User: ${user.username} (ID: ${user.id})`)
    console.log(`🤝 New Sponsor: ${newSponsor.username} (ID: ${newSponsor.id})`)
    console.log(`👨‍💼 Admin: ${adminUsername}`)
    console.log(`📝 Reason: ${reason}`)
    console.log('')
    
    // Step 2: Get current sponsor relationship
    const currentReferral = await getCurrentSponsor(user.id)
    
    if (currentReferral) {
      console.log(`📋 Current Sponsor: ${currentReferral.referrer_username} (ID: ${currentReferral.referrer_id})`)
      
      // Check if trying to assign same sponsor
      if (currentReferral.referrer_id === newSponsor.id) {
        console.log('⚠️  User already has this sponsor!')
        return { success: false, message: 'User already has this sponsor' }
      }
    } else {
      console.log('📋 Current Sponsor: None')
    }
    
    // Step 3: Validate sponsor change
    await validateSponsorChange(user.id, newSponsor.id)
    
    // Step 4: Perform sponsor change in transaction
    const result = await performSponsorChange(user, newSponsor, currentReferral, adminUsername, reason)
    
    if (result.success) {
      console.log('')
      console.log('✅ SPONSOR CHANGE COMPLETED SUCCESSFULLY!')
      console.log('=====================================')
      console.log(`📊 Summary:`)
      console.log(`   • User: ${user.username}`)
      console.log(`   • Old Sponsor: ${currentReferral?.referrer_username || 'None'}`)
      console.log(`   • New Sponsor: ${newSponsor.username}`)
      console.log(`   • Tables Updated: ${result.tablesUpdated.join(', ')}`)
      console.log(`   • Audit Log ID: ${result.auditLogId}`)
    }
    
    return result
    
  } catch (error) {
    console.error('❌ SPONSOR CHANGE FAILED:', error.message)
    return { success: false, error: error.message }
  }
}

/**
 * Resolve user by ID or username
 */
async function resolveUser(identifier) {
  let query = supabase.from('users').select('id, username, email, full_name, is_active')
  
  if (typeof identifier === 'number' || /^\d+$/.test(identifier)) {
    query = query.eq('id', parseInt(identifier))
  } else {
    query = query.eq('username', identifier)
  }
  
  const { data, error } = await query.single()
  
  if (error || !data) {
    console.error(`❌ User not found: ${identifier}`)
    return null
  }
  
  if (!data.is_active) {
    console.error(`❌ User is inactive: ${identifier}`)
    return null
  }
  
  return data
}

/**
 * Get current sponsor relationship
 */
async function getCurrentSponsor(userId) {
  const { data, error } = await supabase
    .from('referrals')
    .select(`
      id,
      referrer_id,
      referral_code,
      commission_rate,
      total_commission,
      status,
      created_at,
      users!referrals_referrer_id_fkey (
        username,
        full_name
      )
    `)
    .eq('referred_id', userId)
    .eq('status', 'active')
    .single()
  
  if (error || !data) {
    return null
  }
  
  return {
    ...data,
    referrer_username: data.users.username,
    referrer_full_name: data.users.full_name
  }
}

/**
 * Validate sponsor change rules
 */
async function validateSponsorChange(userId, newSponsorId) {
  // Rule 1: User cannot sponsor themselves
  if (userId === newSponsorId) {
    throw new Error('User cannot sponsor themselves')
  }
  
  // Rule 2: Check for circular references
  const isCircular = await checkCircularReference(userId, newSponsorId)
  if (isCircular) {
    throw new Error('Circular reference detected - new sponsor is in user\'s downline')
  }
  
  console.log('✅ Sponsor change validation passed')
}

/**
 * Check for circular reference in sponsor chain
 */
async function checkCircularReference(userId, newSponsorId, visited = new Set()) {
  if (visited.has(newSponsorId)) {
    return true // Circular reference found
  }
  
  visited.add(newSponsorId)
  
  // Get new sponsor's sponsor
  const { data } = await supabase
    .from('referrals')
    .select('referrer_id')
    .eq('referred_id', newSponsorId)
    .eq('status', 'active')
    .single()
  
  if (data && data.referrer_id === userId) {
    return true // Direct circular reference
  }
  
  if (data && data.referrer_id) {
    return await checkCircularReference(userId, data.referrer_id, visited)
  }
  
  return false
}

/**
 * Perform sponsor change in database transaction
 */
async function performSponsorChange(user, newSponsor, currentReferral, adminUsername, reason) {
  const tablesUpdated = []
  let auditLogId = null
  
  try {
    // Start transaction-like operations
    console.log('🔄 Starting sponsor change transaction...')
    
    // Step 1: Deactivate current referral relationship
    if (currentReferral) {
      console.log('📝 Deactivating current referral relationship...')
      
      const { error: deactivateError } = await supabase
        .from('referrals')
        .update({
          status: 'inactive',
          updated_at: new Date().toISOString()
        })
        .eq('id', currentReferral.id)
      
      if (deactivateError) {
        throw new Error(`Failed to deactivate current referral: ${deactivateError.message}`)
      }
      
      tablesUpdated.push('referrals (deactivated)')
    }
    
    // Step 2: Create new referral relationship
    console.log('📝 Creating new referral relationship...')
    
    const newReferralCode = `${newSponsor.username}_${user.id}_${Date.now()}`
    
    const { data: newReferral, error: createError } = await supabase
      .from('referrals')
      .insert({
        referrer_id: newSponsor.id,
        referred_id: user.id,
        referral_code: newReferralCode,
        commission_rate: 15.00,
        total_commission: 0,
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (createError) {
      throw new Error(`Failed to create new referral: ${createError.message}`)
    }
    
    tablesUpdated.push('referrals (created)')
    
    // Step 3: Update commission balances if they exist
    console.log('📝 Checking commission balances...')
    
    const { data: commissionBalance } = await supabase
      .from('commission_balances')
      .select('id')
      .eq('user_id', user.id)
      .single()
    
    if (commissionBalance) {
      // Commission balance exists, but we don't need to change it
      // The balance belongs to the user, not the sponsor relationship
      console.log('📊 Commission balance found - no changes needed')
    }
    
    // Step 4: Create audit log entry
    console.log('📝 Creating audit log entry...')
    
    const auditDetails = {
      user_id: user.id,
      user_username: user.username,
      old_sponsor_id: currentReferral?.referrer_id || null,
      old_sponsor_username: currentReferral?.referrer_username || null,
      new_sponsor_id: newSponsor.id,
      new_sponsor_username: newSponsor.username,
      reason: reason,
      old_referral_id: currentReferral?.id || null,
      new_referral_id: newReferral.id,
      timestamp: new Date().toISOString()
    }
    
    const { data: auditLog, error: auditError } = await supabase
      .from('admin_audit_logs')
      .insert({
        admin_telegram_id: 0, // System admin
        admin_username: adminUsername,
        action: 'SPONSOR_CHANGE',
        target_type: 'user_sponsor',
        target_id: user.id.toString(),
        details: JSON.stringify(auditDetails),
        timestamp: new Date().toISOString()
      })
      .select()
      .single()
    
    if (auditError) {
      console.warn('⚠️  Failed to create audit log:', auditError.message)
    } else {
      auditLogId = auditLog.id
      tablesUpdated.push('admin_audit_logs')
    }
    
    console.log('✅ Sponsor change transaction completed successfully')
    
    return {
      success: true,
      tablesUpdated,
      auditLogId,
      newReferralId: newReferral.id,
      oldReferralId: currentReferral?.id || null
    }
    
  } catch (error) {
    console.error('❌ Transaction failed:', error.message)
    throw error
  }
}

// Export functions for use in other scripts
export {
  changeSponsor,
  resolveUser,
  getCurrentSponsor,
  validateSponsorChange
}

// CLI interface when run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('🏆 AUREUS ALLIANCE - SPONSOR CHANGE SYSTEM')
  console.log('==========================================')
  console.log('')
  console.log('This is a utility script for changing user sponsors.')
  console.log('Import and use the changeSponsor() function in your admin tools.')
  console.log('')
  console.log('Example usage:')
  console.log('  await changeSponsor("user123", "newSponsor456", "TTTFOUNDER", "Admin requested change")')
  console.log('')
}
