#!/usr/bin/env node

/**
 * Test Specific Telegram User Login
 * 
 * This script tests the login flow for a specific Telegram user
 * to debug the profile completion issue.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ Missing SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testSpecificTelegramUser(telegramId) {
  try {
    console.log(`🧪 Testing Telegram User Login: ${telegramId}\n`);
    
    const telegramIdNum = parseInt(telegramId);
    if (isNaN(telegramIdNum)) {
      console.error('❌ Invalid Telegram ID format');
      return;
    }
    
    // Step 1: Check if user exists in users table with telegram_id
    console.log('🔍 Step 1: Checking users table for telegram_id...');
    const { data: userWithTelegramId, error: userLookupError } = await supabase
      .from('users')
      .select('*')
      .eq('telegram_id', telegramIdNum)
      .maybeSingle();
    
    if (userLookupError) {
      console.error('❌ Error looking up user by telegram_id:', userLookupError);
      return;
    }
    
    if (userWithTelegramId) {
      console.log('✅ Found user in users table:');
      console.log(`   ID: ${userWithTelegramId.id}`);
      console.log(`   Email: ${userWithTelegramId.email || 'NULL'}`);
      console.log(`   Full Name: ${userWithTelegramId.full_name || 'NULL'}`);
      console.log(`   Phone: ${userWithTelegramId.phone || 'NULL'}`);
      console.log(`   Country: ${userWithTelegramId.country_of_residence || 'NULL'}`);
      console.log(`   Country Selection Completed: ${userWithTelegramId.country_selection_completed || false}`);
      console.log(`   Password Hash: ${userWithTelegramId.password_hash ? 'SET' : 'NULL'}`);
      console.log(`   Is Active: ${userWithTelegramId.is_active}`);
      
      // Check profile completion status using the exact same logic as the app
      const needsProfileCompletion = !userWithTelegramId.email || 
                                   !userWithTelegramId.password_hash || 
                                   !userWithTelegramId.full_name || 
                                   !userWithTelegramId.phone || 
                                   !userWithTelegramId.country_of_residence ||
                                   !userWithTelegramId.country_selection_completed;
      
      console.log(`\n🔍 Profile Completion Analysis:`);
      console.log(`   Needs Profile Completion: ${needsProfileCompletion ? '❌ YES' : '✅ NO'}`);
      
      if (needsProfileCompletion) {
        const missing = [];
        if (!userWithTelegramId.email) missing.push('email');
        if (!userWithTelegramId.password_hash) missing.push('password_hash');
        if (!userWithTelegramId.full_name) missing.push('full_name');
        if (!userWithTelegramId.phone) missing.push('phone');
        if (!userWithTelegramId.country_of_residence) missing.push('country_of_residence');
        if (!userWithTelegramId.country_selection_completed) missing.push('country_selection_completed');
        console.log(`   Missing Fields: ${missing.join(', ')}`);
      }
      
      // Check basic profile completion (used by App.tsx)
      const needsBasicProfile = !userWithTelegramId.country_selection_completed ||
                               !userWithTelegramId.full_name ||
                               !userWithTelegramId.phone;
      
      console.log(`\n🔍 Basic Profile Completion Analysis (App.tsx logic):`);
      console.log(`   Needs Basic Profile: ${needsBasicProfile ? '❌ YES' : '✅ NO'}`);
      
      if (needsBasicProfile) {
        const basicMissing = [];
        if (!userWithTelegramId.country_selection_completed) basicMissing.push('country_selection_completed');
        if (!userWithTelegramId.full_name) basicMissing.push('full_name');
        if (!userWithTelegramId.phone) basicMissing.push('phone');
        console.log(`   Missing Basic Fields: ${basicMissing.join(', ')}`);
      }
      
      console.log(`\n🎯 Expected App Behavior:`);
      if (needsBasicProfile) {
        console.log(`   → Should show: Basic Profile Completion Form`);
      } else if (needsProfileCompletion) {
        console.log(`   → Should show: Full Profile Completion Form`);
      } else {
        console.log(`   → Should show: Dashboard`);
      }
      
    } else {
      console.log('❌ User not found in users table');
      
      // Check telegram_users table
      console.log('\n🔍 Step 2: Checking telegram_users table...');
      const { data: telegramUser, error: telegramError } = await supabase
        .from('telegram_users')
        .select('*')
        .eq('telegram_id', telegramIdNum)
        .maybeSingle();
      
      if (telegramError) {
        console.error('❌ Error looking up telegram_users:', telegramError);
        return;
      }
      
      if (telegramUser) {
        console.log('✅ Found user in telegram_users table:');
        console.log(`   ID: ${telegramUser.id}`);
        console.log(`   User ID: ${telegramUser.user_id || 'NULL'}`);
        console.log(`   Username: ${telegramUser.username || 'NULL'}`);
        console.log(`   First Name: ${telegramUser.first_name || 'NULL'}`);
        console.log(`   Last Name: ${telegramUser.last_name || 'NULL'}`);
        console.log(`   Is Registered: ${telegramUser.is_registered || false}`);
        console.log(`   Registration Step: ${telegramUser.registration_step || 'NULL'}`);
        
        console.log(`\n🎯 Expected App Behavior:`);
        console.log(`   → Should show: Profile Completion Form (new user)`);
      } else {
        console.log('❌ User not found in telegram_users table either');
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Test with a specific Telegram ID - you can change this
const testTelegramId = process.argv[2] || '1393852532'; // Default to TTTFOUNDER
testSpecificTelegramUser(testTelegramId);
