import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

// Use service role key to check RLS policies
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_SERVICE_ROLE_KEY
);

async function checkRLSPolicies() {
  console.log('🔍 Checking RLS policies for aureus_share_purchases table...\n');
  
  try {
    // Check if RLS is enabled on the table
    const { data: tableInfo, error: tableError } = await supabase
      .rpc('check_table_rls', { table_name: 'aureus_share_purchases' });
    
    if (tableError) {
      console.log('⚠️ Could not check RLS status directly, trying alternative method...');
      
      // Try to query the table with anon key to see if it's blocked
      const anonClient = createClient(
        process.env.VITE_SUPABASE_URL,
        process.env.VITE_SUPABASE_ANON_KEY
      );
      
      const { data: anonData, error: anonError } = await anonClient
        .from('aureus_share_purchases')
        .select('id')
        .limit(1);
      
      if (anonError) {
        console.log('❌ Anon key cannot access aureus_share_purchases:', anonError.message);
        console.log('   This suggests RLS is enabled and blocking access');
        
        // Check if there are any policies
        const { data: policies, error: policyError } = await supabase
          .from('pg_policies')
          .select('*')
          .eq('tablename', 'aureus_share_purchases');
        
        if (policyError) {
          console.log('⚠️ Could not check policies:', policyError.message);
        } else {
          console.log(`📋 Found ${policies.length} RLS policies for aureus_share_purchases:`);
          policies.forEach(policy => {
            console.log(`   - ${policy.policyname}: ${policy.cmd} (${policy.permissive ? 'permissive' : 'restrictive'})`);
            console.log(`     Roles: ${policy.roles}`);
            console.log(`     Using: ${policy.using}`);
            if (policy.with_check) {
              console.log(`     With check: ${policy.with_check}`);
            }
            console.log('');
          });
        }
      } else {
        console.log('✅ Anon key can access aureus_share_purchases');
        console.log(`   Found ${anonData?.length || 0} records`);
      }
    }
    
    // Test admin access
    console.log('\n🔧 Testing admin access patterns...');
    
    // Test 1: Direct service role access
    const { data: serviceData, error: serviceError } = await supabase
      .from('aureus_share_purchases')
      .select('id')
      .limit(1);
    
    if (serviceError) {
      console.log('❌ Service role cannot access aureus_share_purchases:', serviceError.message);
    } else {
      console.log(`✅ Service role can access aureus_share_purchases: ${serviceData?.length || 0} records`);
    }
    
    // Test 2: Check if admin_users table exists and has policies
    const { data: adminUsers, error: adminError } = await supabase
      .from('admin_users')
      .select('email')
      .limit(1);
    
    if (adminError) {
      console.log('⚠️ admin_users table access issue:', adminError.message);
    } else {
      console.log(`✅ admin_users table accessible: ${adminUsers.length} records found`);
    }
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

checkRLSPolicies().then(() => {
  console.log('\n✅ RLS check complete');
  process.exit(0);
}).catch(console.error);
