import React, { useState, useEffect } from 'react';
import { svgCertificateGenerator } from '../../lib/svgCertificateGenerator';

interface SVGCertificatePreviewProps {
  purchaseId: string;
  onClose: () => void;
  onDownload?: (svgContent: string) => void;
}

export const SVGCertificatePreview: React.FC<SVGCertificatePreviewProps> = ({
  purchaseId,
  onClose,
  onDownload
}) => {
  const [svgContent, setSvgContent] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    generatePreview();
  }, [purchaseId]);

  const generatePreview = async () => {
    try {
      setLoading(true);
      setError('');
      
      const certificate = await svgCertificateGenerator.generateCertificate(purchaseId);
      
      if (!certificate) {
        throw new Error('Failed to generate certificate');
      }
      
      setSvgContent(certificate);
    } catch (err) {
      console.error('Error generating certificate preview:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadSVG = () => {
    if (!svgContent) return;
    
    const blob = new Blob([svgContent], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `certificate-${purchaseId}.svg`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    if (onDownload) {
      onDownload(svgContent);
    }
  };

  const handleDownloadPNG = async () => {
    if (!svgContent) return;
    
    try {
      const pngBlob = await svgCertificateGenerator.svgToPdf(svgContent);
      if (pngBlob) {
        const url = URL.createObjectURL(pngBlob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `certificate-${purchaseId}.png`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    } catch (err) {
      console.error('Error converting to PNG:', err);
      alert('Error converting to PNG. Please try downloading as SVG.');
    }
  };

  const handlePrint = () => {
    if (!svgContent) return;
    
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Certificate Print</title>
            <style>
              body { margin: 0; padding: 20px; }
              .certificate-container { 
                width: 100%; 
                max-width: 1000px; 
                margin: 0 auto; 
              }
              @media print {
                body { padding: 0; }
                .no-print { display: none; }
              }
            </style>
          </head>
          <body>
            <div class="certificate-container">
              ${svgContent}
            </div>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-700">
          <h2 className="text-2xl font-bold text-white">📜 Certificate Preview</h2>
          <div className="flex space-x-2">
            {!loading && !error && (
              <>
                <button
                  onClick={handleDownloadSVG}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  📥 Download SVG
                </button>
                <button
                  onClick={handleDownloadPNG}
                  className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                >
                  📥 Download PNG
                </button>
                <button
                  onClick={handlePrint}
                  className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
                >
                  🖨️ Print
                </button>
              </>
            )}
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              ✕ Close
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-auto max-h-[calc(90vh-120px)]">
          {loading && (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <p className="text-gray-400">Generating certificate preview...</p>
              </div>
            </div>
          )}

          {error && (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="text-red-500 text-6xl mb-4">⚠️</div>
                <p className="text-red-400 text-lg mb-2">Error generating certificate</p>
                <p className="text-gray-400 mb-4">{error}</p>
                <button
                  onClick={generatePreview}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  🔄 Retry
                </button>
              </div>
            </div>
          )}

          {!loading && !error && svgContent && (
            <div className="bg-white rounded-lg p-4 shadow-lg">
              <div 
                className="certificate-preview"
                dangerouslySetInnerHTML={{ __html: svgContent }}
                style={{
                  maxWidth: '100%',
                  overflow: 'auto'
                }}
              />
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-700 bg-gray-800">
          <div className="flex justify-between items-center text-sm text-gray-400">
            <div>
              Certificate dimensions: 1000px × 707px | Purchase ID: {purchaseId}
            </div>
            <div>
              Generated using official Aureus Alliance Holdings template
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SVGCertificatePreview;
