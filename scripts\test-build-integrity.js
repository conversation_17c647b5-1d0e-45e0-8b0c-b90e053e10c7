#!/usr/bin/env node

/**
 * Build Integrity Test Script
 * Tests the production build for functionality and performance
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const DIST_DIR = path.join(__dirname, '..', 'dist');

async function testStaticServer() {
  console.log('🌐 Testing static server functionality...');
  
  try {
    // Start a static server on a test port
    const testPort = 8001;
    const serverProcess = exec(`npx serve -s "${DIST_DIR}" -l ${testPort}`, { 
      cwd: path.dirname(__dirname) 
    });
    
    // Wait for server to start
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Test basic connectivity
    const testUrl = `http://localhost:${testPort}`;
    
    try {
      const response = await fetch(testUrl);
      if (response.ok) {
        console.log('✅ Static server: Responding correctly');
        
        // Test that it returns HTML
        const content = await response.text();
        if (content.includes('<div id="root">')) {
          console.log('✅ HTML content: Valid React application structure');
        } else {
          console.log('❌ HTML content: Missing React root element');
        }
        
        // Test client-side routing
        const routeResponse = await fetch(`${testUrl}/dashboard`);
        if (routeResponse.ok) {
          console.log('✅ Client-side routing: Working (returns index.html for routes)');
        } else {
          console.log('❌ Client-side routing: Not working (404 for routes)');
        }
        
      } else {
        console.log(`❌ Static server: HTTP ${response.status}`);
      }
    } catch (fetchError) {
      console.log('❌ Static server: Not accessible');
    }
    
    // Clean up
    serverProcess.kill();
    
    return true;
  } catch (error) {
    console.log('❌ Static server test failed:', error.message);
    return false;
  }
}

function testAssetIntegrity() {
  console.log('\n🔍 Testing asset integrity...');
  
  const assetsDir = path.join(DIST_DIR, 'assets');
  
  if (!fs.existsSync(assetsDir)) {
    console.log('❌ Assets directory: Not found');
    return false;
  }
  
  const assets = fs.readdirSync(assetsDir);
  const jsFiles = assets.filter(file => file.endsWith('.js'));
  const cssFiles = assets.filter(file => file.endsWith('.css'));
  
  console.log(`📊 Found ${jsFiles.length} JavaScript files, ${cssFiles.length} CSS files`);
  
  // Test JavaScript files
  let jsValid = true;
  jsFiles.forEach(file => {
    const filePath = path.join(assetsDir, file);
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Basic syntax check - look for common patterns
    if (content.includes('import') || content.includes('export') || content.includes('React')) {
      console.log(`✅ ${file}: Contains expected JavaScript patterns`);
    } else {
      console.log(`⚠️  ${file}: May not be properly bundled`);
    }
    
    // Check file size
    const stats = fs.statSync(filePath);
    const sizeKB = Math.round(stats.size / 1024);
    
    if (sizeKB > 0) {
      console.log(`  📏 Size: ${sizeKB} KB`);
    } else {
      console.log(`  ❌ Size: Empty file`);
      jsValid = false;
    }
  });
  
  // Test CSS files
  let cssValid = true;
  cssFiles.forEach(file => {
    const filePath = path.join(assetsDir, file);
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Basic CSS validation
    if (content.includes('{') && content.includes('}')) {
      console.log(`✅ ${file}: Contains valid CSS structure`);
    } else {
      console.log(`❌ ${file}: Invalid CSS structure`);
      cssValid = false;
    }
    
    // Check for Tailwind classes (should be compiled)
    if (content.includes('@tailwind') || content.includes('@apply')) {
      console.log(`⚠️  ${file}: Contains uncompiled Tailwind directives`);
    }
    
    const stats = fs.statSync(filePath);
    const sizeKB = Math.round(stats.size / 1024);
    console.log(`  📏 Size: ${sizeKB} KB`);
  });
  
  return jsValid && cssValid;
}

function testEnvironmentVariables() {
  console.log('\n🔧 Testing environment variable injection...');
  
  const indexPath = path.join(DIST_DIR, 'index.html');
  
  if (!fs.existsSync(indexPath)) {
    console.log('❌ index.html: Not found');
    return false;
  }
  
  const content = fs.readFileSync(indexPath, 'utf8');
  
  // Look for script tags that might contain environment variables
  const scriptMatches = content.match(/<script[^>]*>(.*?)<\/script>/gs);
  
  if (scriptMatches) {
    let foundEnvVars = false;
    
    scriptMatches.forEach(script => {
      if (script.includes('VITE_SUPABASE_URL') || script.includes('process.env')) {
        foundEnvVars = true;
      }
    });
    
    if (foundEnvVars) {
      console.log('✅ Environment variables: Found in build');
    } else {
      console.log('⚠️  Environment variables: Not found in inline scripts');
    }
  }
  
  // Check if assets contain environment variables
  const assetsDir = path.join(DIST_DIR, 'assets');
  const jsFiles = fs.readdirSync(assetsDir).filter(file => file.endsWith('.js'));
  
  let envVarsInAssets = false;
  jsFiles.forEach(file => {
    const filePath = path.join(assetsDir, file);
    const content = fs.readFileSync(filePath, 'utf8');
    
    if (content.includes('fgubaqoftdeefcakejwu.supabase.co')) {
      envVarsInAssets = true;
      console.log(`✅ Supabase URL: Found in ${file}`);
    }
  });
  
  if (!envVarsInAssets) {
    console.log('⚠️  Environment variables: Not found in JavaScript bundles');
  }
  
  return true;
}

function testManifestAndPWA() {
  console.log('\n📱 Testing PWA manifest...');
  
  const manifestPath = path.join(DIST_DIR, 'manifest.json');
  
  if (!fs.existsSync(manifestPath)) {
    console.log('❌ manifest.json: Not found');
    return false;
  }
  
  try {
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
    
    const requiredFields = ['name', 'short_name', 'start_url', 'display', 'theme_color'];
    let allFieldsPresent = true;
    
    requiredFields.forEach(field => {
      if (manifest[field]) {
        console.log(`✅ ${field}: ${manifest[field]}`);
      } else {
        console.log(`❌ ${field}: Missing`);
        allFieldsPresent = false;
      }
    });
    
    return allFieldsPresent;
  } catch (error) {
    console.log('❌ manifest.json: Invalid JSON format');
    return false;
  }
}

function performanceAnalysis() {
  console.log('\n⚡ Performance analysis...');
  
  const assetsDir = path.join(DIST_DIR, 'assets');
  const assets = fs.readdirSync(assetsDir);
  
  let totalSize = 0;
  let largeFiles = [];
  
  assets.forEach(file => {
    const filePath = path.join(assetsDir, file);
    const stats = fs.statSync(filePath);
    const sizeKB = Math.round(stats.size / 1024);
    
    totalSize += stats.size;
    
    if (sizeKB > 500) { // Files larger than 500KB
      largeFiles.push({ file, size: sizeKB });
    }
  });
  
  const totalSizeMB = (totalSize / (1024 * 1024)).toFixed(2);
  console.log(`📊 Total assets size: ${totalSizeMB} MB`);
  
  if (largeFiles.length > 0) {
    console.log('⚠️  Large files detected:');
    largeFiles.forEach(({ file, size }) => {
      console.log(`  • ${file}: ${size} KB`);
    });
    console.log('Consider code splitting or asset optimization.');
  } else {
    console.log('✅ No unusually large files detected');
  }
  
  // Recommendations
  if (totalSize > 10 * 1024 * 1024) { // 10MB
    console.log('⚠️  Total bundle size is quite large. Consider:');
    console.log('  • Code splitting');
    console.log('  • Tree shaking optimization');
    console.log('  • Asset compression');
  } else {
    console.log('✅ Bundle size is reasonable');
  }
  
  return true;
}

async function runBuildIntegrityTests() {
  console.log('🧪 Aureus Africa - Build Integrity Tests');
  console.log('=' .repeat(50));
  
  if (!fs.existsSync(DIST_DIR)) {
    console.log('❌ Build directory not found! Run "npm run build" first.');
    process.exit(1);
  }
  
  const tests = [
    { name: 'Asset Integrity', fn: () => Promise.resolve(testAssetIntegrity()) },
    { name: 'Environment Variables', fn: () => Promise.resolve(testEnvironmentVariables()) },
    { name: 'PWA Manifest', fn: () => Promise.resolve(testManifestAndPWA()) },
    { name: 'Performance Analysis', fn: () => Promise.resolve(performanceAnalysis()) },
    { name: 'Static Server', fn: testStaticServer }
  ];
  
  let allPassed = true;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (!result) {
        allPassed = false;
      }
    } catch (error) {
      console.log(`❌ ${test.name}: Error - ${error.message}`);
      allPassed = false;
    }
  }
  
  console.log('\n' + '='.repeat(50));
  
  if (allPassed) {
    console.log('✅ All build integrity tests passed!');
    console.log('🎉 Your build is ready for production deployment.');
  } else {
    console.log('❌ Some build integrity tests failed!');
    console.log('Please review the issues above and rebuild if necessary.');
  }
  
  return allPassed;
}

// Run the tests
if (import.meta.url === `file://${process.argv[1]}`) {
  runBuildIntegrityTests().then(success => {
    if (!success) {
      process.exit(1);
    }
  });
}

export { runBuildIntegrityTests };
