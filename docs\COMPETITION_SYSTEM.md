# Gold Diggers Club Competition System

## Overview

The Gold Diggers Club Competition System is a dynamic, database-driven leaderboard and prize distribution system that replaces the previous hardcoded implementation. It allows for phase-specific competitions with real-time participant tracking and flexible prize structures.

## ⚠️ IMPORTANT: Competition Criteria

**This system rewards NETWORK LEADERS based on referral sales performance, NOT personal investment amounts.**

- **Rankings are based on**: Total USD volume of shares sold through their referral network
- **NOT based on**: How much they personally invested
- **Goal**: Reward top network builders who bring in the most business through referrals
- **Qualification**: Minimum $2,500 in direct referral sales volume (not personal purchases)

## Features

- **Dynamic Competition Management**: Create competitions for different phases
- **Real-time Leaderboard**: Live participant rankings based on referral volume
- **Flexible Prize Tiers**: Configurable prize distribution with multiple tiers
- **Automatic Qualification**: Participants automatically qualify based on referral volume
- **Phase Integration**: Competitions tied to specific investment phases
- **Data Synchronization**: Automatic sync with bot referral data

## Database Schema

### Tables Created

1. **competitions** - Main competition records
2. **competition_prize_tiers** - Prize distribution configuration
3. **competition_participants** - Participant tracking and statistics
4. **competition_leaderboard** (view) - Real-time leaderboard calculation

### Key Functions

- `update_competition_rankings()` - Updates participant rankings
- `check_participant_qualification()` - Checks if participant meets minimum requirements
- `get_referral_statistics()` - Calculates referral statistics from bot data

## Setup Instructions

### 1. Database Setup

Run the database setup script:

```bash
cd aureus_africa
node scripts/setup-competition-system.js
```

This will:
- Create all necessary database tables
- Set up database functions and triggers
- Create a default competition for the current phase
- Configure default prize tiers

### 2. Environment Variables

Ensure these environment variables are set:

```env
REACT_APP_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### 3. Component Integration

The system is already integrated into the main App.tsx file. The hardcoded Gold Diggers section has been replaced with the dynamic `GoldDiggersClub` component.

## Usage

### Competition Service

```typescript
import { competitionService } from './lib/services/competitionService';

// Get current competition
const competition = await competitionService.getCurrentCompetition();

// Get leaderboard
const leaderboard = await competitionService.getLeaderboard(competition.id, 10);

// Get competition statistics
const stats = await competitionService.getCompetitionStats(competition.id);

// Update participant
await competitionService.updateParticipant(
  competitionId, 
  userId, 
  referralVolume, 
  directReferrals, 
  qualifiedReferrals
);
```

### Referral Sync Service

```typescript
import { referralSyncService } from './lib/services/referralSyncService';

// Manual sync
await referralSyncService.syncReferralData();

// Schedule automatic sync every 30 minutes
await referralSyncService.scheduleSync(30);

// Get leaderboard data
const leaderboard = await referralSyncService.getLeaderboardData(10);
```

## Data Flow

1. **Bot System** → Records referrals and purchases in existing tables
2. **Sync Service** → Calculates referral statistics and updates competition participants
3. **Competition Service** → Manages competition data and rankings
4. **Frontend Component** → Displays real-time leaderboard and statistics

## Default Configuration

### Prize Tiers
- 🥇 1st Place: $60,000
- 🥈 2nd Place: $30,000
- 🥉 3rd Place: $18,000
- 🏆 4th-10th Place: $6,000 each

### Competition Settings
- **Minimum Qualification**: $2,500 in direct referrals
- **Total Prize Pool**: $210,000
- **Status**: Active by default
- **Phase Integration**: Tied to current active investment phase

## API Endpoints

The system uses the existing Supabase client and doesn't require additional API endpoints. All data access is handled through the service layer.

## Monitoring and Maintenance

### Regular Tasks

1. **Data Sync**: The referral sync service should run every 30 minutes
2. **Ranking Updates**: Rankings are updated automatically via database triggers
3. **Competition Status**: Monitor competition status and create new competitions for new phases

### Database Queries for Monitoring

```sql
-- Check competition status
SELECT * FROM competitions WHERE is_active = true;

-- View current leaderboard
SELECT * FROM competition_leaderboard WHERE competition_id = 'your-competition-id';

-- Check participant counts
SELECT 
  COUNT(*) as total_participants,
  COUNT(*) FILTER (WHERE is_qualified = true) as qualified_participants
FROM competition_participants 
WHERE competition_id = 'your-competition-id';
```

## Troubleshooting

### Common Issues

1. **No Competition Data**: Run the setup script to create initial competition
2. **Missing Referral Data**: Check if the sync service is running and bot data exists
3. **Incorrect Rankings**: Verify the ranking update function is working
4. **Display Issues**: Check if the frontend component is properly imported

### Debug Queries

```sql
-- Check if tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE 'competition%';

-- Verify referral data
SELECT COUNT(*) FROM referrals WHERE status = 'active';

-- Check purchase data
SELECT COUNT(*) FROM aureus_share_purchases WHERE status = 'completed';
```

## Future Enhancements

- **Multi-Phase Competitions**: Support for competitions spanning multiple phases
- **Custom Prize Types**: Support for share-based prizes in addition to USD
- **Advanced Metrics**: Additional ranking criteria beyond referral volume
- **Automated Payouts**: Integration with payment systems for automatic prize distribution
- **Historical Data**: Archive completed competitions and maintain historical leaderboards

## Support

For issues or questions about the competition system:

1. Check the database logs for any errors
2. Verify the sync service is running properly
3. Ensure all environment variables are correctly set
4. Review the component integration in App.tsx
