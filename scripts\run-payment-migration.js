const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  console.error('Required: REACT_APP_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runPaymentMigration() {
  console.log('🚀 Starting payment management migration...');

  try {
    // Read the SQL migration file
    const sqlPath = path.join(__dirname, 'add-payment-columns.sql');
    const sqlContent = fs.readFileSync(sqlPath, 'utf8');

    // Split SQL commands by semicolon and filter out empty ones
    const sqlCommands = sqlContent
      .split(';')
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0 && !cmd.startsWith('--'));

    console.log(`📋 Found ${sqlCommands.length} SQL commands to execute`);

    // Execute each SQL command
    for (let i = 0; i < sqlCommands.length; i++) {
      const command = sqlCommands[i];
      console.log(`\n⚡ Executing command ${i + 1}/${sqlCommands.length}:`);
      console.log(command.substring(0, 100) + (command.length > 100 ? '...' : ''));

      const { error } = await supabase.rpc('exec_sql', {
        sql: command
      });

      if (error) {
        console.error(`❌ Error executing command ${i + 1}:`, error);
        // Continue with other commands instead of stopping
      } else {
        console.log(`✅ Command ${i + 1} executed successfully`);
      }
    }

    // Verify the migration by checking table structure
    console.log('\n🔍 Verifying migration...');
    
    const { data: columns, error: columnsError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable')
      .eq('table_name', 'crypto_payment_transactions')
      .eq('table_schema', 'public');

    if (columnsError) {
      console.error('❌ Error verifying table structure:', columnsError);
    } else {
      console.log('\n📊 Current crypto_payment_transactions table structure:');
      columns.forEach(col => {
        console.log(`  • ${col.column_name} (${col.data_type}) ${col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'}`);
      });
    }

    // Test a sample query
    console.log('\n🧪 Testing payment query...');
    const { data: testPayments, error: testError } = await supabase
      .from('crypto_payment_transactions')
      .select(`
        id,
        amount,
        shares_to_purchase,
        status,
        created_at,
        users!inner(username, email)
      `)
      .limit(1);

    if (testError) {
      console.error('❌ Error testing payment query:', testError);
    } else {
      console.log('✅ Payment query test successful');
      if (testPayments && testPayments.length > 0) {
        console.log('📄 Sample payment record:', testPayments[0]);
      }
    }

    console.log('\n🎉 Payment management migration completed successfully!');
    console.log('\n📋 Migration Summary:');
    console.log('✅ Added missing columns to crypto_payment_transactions');
    console.log('✅ Created indexes for better performance');
    console.log('✅ Created payment management view');
    console.log('✅ Updated existing records with calculated shares');
    console.log('\n🚀 The web admin payment management system is now ready!');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
runPaymentMigration();
