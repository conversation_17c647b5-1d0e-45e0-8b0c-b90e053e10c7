import React, { useState, useEffect } from 'react';
import SharePurchaseFlow from '../components/SharePurchaseFlow';
import { UnifiedAuthPageClean } from '../components/UnifiedAuthPageClean';
import { getCurrentUser } from '../lib/supabase';


interface PurchaseSharesPageProps {
  onBack: () => void;
  onNavigate: (page: string) => void;
  onAuthSuccess?: (user: any) => void;
}

const PurchaseSharesPage: React.FC<PurchaseSharesPageProps> = ({ onBack, onNavigate, onAuthSuccess }) => {
  const [user, setUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showPurchaseFlow, setShowPurchaseFlow] = useState(false);
  const [showAuthPage, setShowAuthPage] = useState(false);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const currentUser = await getCurrentUser();
      if (currentUser) {
        setUser(currentUser);
        setShowPurchaseFlow(true);
      } else {
        setShowAuthPage(true);
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      setShowAuthPage(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAuthSuccess = (userData: any) => {
    console.log('✅ Purchase shares auth success:', userData);
    setUser(userData);

    // If we have an onAuthSuccess callback (from App.tsx), use it to redirect to dashboard
    if (onAuthSuccess) {
      onAuthSuccess(userData);
    } else {
      // Fallback: show purchase flow
      setShowPurchaseFlow(true);
    }
  };

  const handlePurchaseFlowClose = () => {
    setShowPurchaseFlow(false);
    // Redirect to dashboard instead of calling onBack() which goes to home page
    window.location.href = '/dashboard';
  };

  const handleBackToAuth = () => {
    setShowAuthPage(true);
    setShowPurchaseFlow(false);
  };

  if (isLoading) {
    return (
      <div className="page">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p className="loading-text">Loading...</p>
        </div>
      </div>
    );
  }

  // If user is authenticated and purchase flow should be shown
  if (user && showPurchaseFlow) {
    return (
      <div className="page modern-website premium-auth-page">
        {/* Header */}
        <header className="header premium-header">
          <div className="container">
            <div className="header-content">
              <div className="logo">
                <img
                  src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-s.png"
                  alt="Aureus Alliance Holdings"
                  className="logo-image"
                />
              </div>
              <nav className="nav-menu">
                <div className="nav-main">
                  <button className="nav-item premium-nav-item" onClick={onBack}>
                    Home
                  </button>
                  <button className="nav-item premium-nav-item" onClick={() => onNavigate('investment-phases')}>
                    Share Phases
                  </button>
                  <button className="nav-item premium-nav-item" onClick={() => onNavigate('calculator')}>
                    Calculator
                  </button>
                  <button className="nav-item premium-nav-item" onClick={() => onNavigate('financial-data')}>
                    Financial Data
                  </button>
                  <button className="nav-item premium-nav-item" onClick={() => onNavigate('mine-production')}>
                    Mine Production
                  </button>
                  <button className="nav-item premium-nav-item" onClick={() => onNavigate('gallery')}>
                    Gallery
                  </button>
                </div>
                <div className="nav-actions">
                  <button className="btn btn-primary premium-btn">
                    Purchase Shares
                  </button>
                </div>
              </nav>
            </div>
          </div>
        </header>

        <SharePurchaseFlow
          user={user}
          onClose={handlePurchaseFlowClose}
        />
      </div>
    );
  }

  // If user is not authenticated, show the simple auth page
  if (showAuthPage) {
    return (
      <UnifiedAuthPageClean
        onAuthSuccess={handleAuthSuccess}
        onBack={onBack}
        userType="shareholder"
      />
    );
  }

  // Default loading state
  return (
    <div className="page">
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p className="loading-text">Initializing...</p>
      </div>
    </div>
  );
};

export default PurchaseSharesPage;
