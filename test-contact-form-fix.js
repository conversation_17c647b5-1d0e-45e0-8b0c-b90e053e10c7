#!/usr/bin/env node

/**
 * TEST CONTACT FORM EMAIL ROUTING FIX
 * 
 * This script tests the contact form API to verify that:
 * 1. Support emails <NAME_EMAIL> ONLY
 * 2. User confirmation emails go to the user's email address
 * 3. No emails are <NAME_EMAIL>
 */

import fetch from 'node-fetch';

const API_URL = 'http://localhost:8002/api/contact';

async function testContactFormFix() {
  console.log('🧪 Testing Contact Form Email Routing Fix...\n');
  
  const testData = {
    name: 'Test',
    surname: 'User',
    email: '<EMAIL>',
    message: 'This is a test message to verify email routing is fixed.'
  };
  
  try {
    console.log('📧 Submitting test contact form...');
    console.log(`   Name: ${testData.name} ${testData.surname}`);
    console.log(`   Email: ${testData.email}`);
    console.log(`   Message: ${testData.message}\n`);
    
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    });
    
    const result = await response.json();
    
    if (response.ok && result.success) {
      console.log('✅ Contact form submission successful!');
      console.log(`   Message: ${result.message}`);
      console.log(`   Support Email ID: ${result.supportEmailId || 'N/A'}`);
      console.log(`   Confirmation Email ID: ${result.confirmationEmailId || 'N/A'}\n`);
      
      console.log('🎯 Expected Email Routing:');
      console.log('   ✅ Support notification → <EMAIL>');
      console.log('   ✅ User confirmation → <EMAIL>');
      console.log('   ❌ NO emails should <NAME_EMAIL>\n');
      
      console.log('📋 Next Steps:');
      console.log('   1. Check your email <NAME_EMAIL>');
      console.log('   2. Verify NO emails were <NAME_EMAIL>');
      console.log('   3. Deploy these changes to the live website');
      
    } else {
      console.error('❌ Contact form submission failed:');
      console.error(`   Status: ${response.status}`);
      console.error(`   Error: ${result.error || 'Unknown error'}`);
      if (result.details) {
        console.error(`   Details:`, result.details);
      }
    }
    
  } catch (error) {
    console.error('❌ Network error testing contact form:', error.message);
  }
}

// Run the test
testContactFormFix();
