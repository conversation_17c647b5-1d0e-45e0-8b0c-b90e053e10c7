/**
 * KYC Field Approvals API
 * Checks field-level approval status for a specific KYC ID
 */

import { createClient } from '@supabase/supabase-js';

// Force the correct Supabase configuration since environment variables aren't loading properly in API files
const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { kycId } = req.query;

    if (!kycId) {
      return res.status(400).json({ error: 'KYC ID is required' });
    }

    console.log('📋 Checking field approvals for KYC ID:', kycId);

    // Get all field approvals for this KYC ID
    const { data: allFieldApprovals, error: allFieldError } = await supabase
      .from('kyc_field_approvals')
      .select('*')
      .eq('kyc_id', kycId)
      .order('updated_at', { ascending: false });

    if (allFieldError) {
      console.error('❌ Error loading field approvals:', allFieldError);
      return res.status(500).json({ 
        error: 'Failed to load field approvals',
        details: allFieldError.message 
      });
    }

    // Separate fields by status
    const rejectedFields = allFieldApprovals?.filter(f => f.approval_status === 'rejected') || [];
    const pendingFields = allFieldApprovals?.filter(f => f.approval_status === 'pending') || [];
    const approvedFields = allFieldApprovals?.filter(f => f.approval_status === 'approved') || [];

    console.log('📋 Field approvals summary:', {
      total: allFieldApprovals?.length || 0,
      rejected: rejectedFields.length,
      pending: pendingFields.length,
      approved: approvedFields.length
    });

    return res.status(200).json({
      success: true,
      fieldApprovals: {
        all: allFieldApprovals || [],
        rejected: rejectedFields,
        pending: pendingFields,
        approved: approvedFields,
        hasFieldApprovals: (allFieldApprovals?.length || 0) > 0
      }
    });

  } catch (error) {
    console.error('❌ Error checking field approvals:', error);
    return res.status(500).json({ 
      error: 'Failed to check field approvals',
      details: error.message 
    });
  }
}
