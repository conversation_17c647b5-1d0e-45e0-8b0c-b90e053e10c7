import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

dotenv.config();

console.log('🔍 DEBUGGING DJAMES LOGIN - DETAILED ANALYSIS');
console.log('==============================================');

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

const debugLogin = async () => {
  try {
    const email = '<EMAIL>';
    const password = 'Gunst0n5o0!@#';
    
    console.log(`📧 Email: ${email}`);
    console.log(`🔑 Password: ${password}`);
    console.log(`🔗 Supabase URL: ${supabaseUrl}`);
    
    // Step 1: Check users table
    console.log('\n📋 Step 1: Checking users table...');
    const { data: webUser, error: webError } = await supabase
      .from('users')
      .select('*')
      .eq('email', email.toLowerCase().trim())
      .single();
    
    if (webError) {
      console.log('❌ Error querying users table:', webError);
    } else if (webUser) {
      console.log('✅ Found user in users table:');
      console.log(`   ID: ${webUser.id}`);
      console.log(`   Username: ${webUser.username}`);
      console.log(`   Email: ${webUser.email}`);
      console.log(`   Active: ${webUser.is_active}`);
      console.log(`   Telegram ID: ${webUser.telegram_id}`);
      console.log(`   Password hash exists: ${!!webUser.password_hash}`);
      console.log(`   Password hash: ${webUser.password_hash?.substring(0, 20)}...`);
    } else {
      console.log('❌ No user found in users table');
    }
    
    // Step 2: Check telegram_users table
    console.log('\n📋 Step 2: Checking telegram_users table...');
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('email', email.toLowerCase().trim())
      .single();
    
    if (telegramError) {
      console.log('❌ Error querying telegram_users table:', telegramError);
    } else if (telegramUser) {
      console.log('✅ Found user in telegram_users table:');
      console.log(`   Telegram ID: ${telegramUser.telegram_id}`);
      console.log(`   Username: ${telegramUser.username}`);
      console.log(`   Email: ${telegramUser.email}`);
      console.log(`   User ID: ${telegramUser.user_id}`);
      console.log(`   Password hash exists: ${!!telegramUser.password_hash}`);
      console.log(`   Password hash: ${telegramUser.password_hash?.substring(0, 20)}...`);
    } else {
      console.log('❌ No user found in telegram_users table');
    }
    
    // Step 3: Test password verification
    console.log('\n📋 Step 3: Testing password verification...');
    
    if (webUser && webUser.password_hash) {
      console.log('🧪 Testing against users table password hash...');
      const webPasswordValid = await bcrypt.compare(password, webUser.password_hash);
      console.log(`   Result: ${webPasswordValid ? '✅ VALID' : '❌ INVALID'}`);
    }
    
    if (telegramUser && telegramUser.password_hash) {
      console.log('🧪 Testing against telegram_users table password hash...');
      const telegramPasswordValid = await bcrypt.compare(password, telegramUser.password_hash);
      console.log(`   Result: ${telegramPasswordValid ? '✅ VALID' : '❌ INVALID'}`);
    }
    
    // Step 4: Test Supabase auth
    console.log('\n📋 Step 4: Testing Supabase auth...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    
    if (authError) {
      console.log('❌ Supabase auth error:', authError.message);
    } else {
      console.log('✅ Supabase auth successful:');
      console.log(`   User ID: ${authData.user?.id}`);
      console.log(`   Email: ${authData.user?.email}`);
    }
    
    // Step 5: Simulate the exact login flow from signInWithEmailEnhanced
    console.log('\n📋 Step 5: Simulating signInWithEmailEnhanced flow...');
    
    let dbUser = null;
    let accountType = 'web';
    
    if (webUser) {
      dbUser = webUser;
      accountType = 'web';
      console.log('✅ Using web user account');
    } else if (telegramUser) {
      dbUser = telegramUser;
      accountType = 'telegram';
      console.log('✅ Using telegram user account');
    } else {
      console.log('❌ No user found in either table');
      return;
    }
    
    // Verify password
    console.log('🔐 Verifying password...');
    const passwordValid = await bcrypt.compare(password, dbUser.password_hash);
    if (!passwordValid) {
      console.log('❌ Password verification FAILED');
      console.log('   This is why login is failing!');
      return;
    }
    console.log('✅ Password verification SUCCESSFUL');
    
    // Check if user is active
    if (accountType === 'web' && !dbUser.is_active) {
      console.log('❌ Account is not active');
      return;
    }
    console.log('✅ Account is active');
    
    console.log('\n🎉 LOGIN SHOULD WORK!');
    console.log('=====================');
    console.log('All checks passed - if login is still failing, there might be an issue with:');
    console.log('1. Frontend form submission');
    console.log('2. Network connectivity');
    console.log('3. Environment variables');
    console.log('4. Supabase configuration');
    
  } catch (error) {
    console.error('❌ Debug error:', error);
  }
};

debugLogin();
