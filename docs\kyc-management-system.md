# Enhanced KYC Management System

## Overview

The Enhanced KYC (Know Your Customer) Management System provides comprehensive administrative tools for reviewing, approving, and managing user KYC submissions with advanced document verification capabilities. This system integrates seamlessly with the existing admin dashboard and provides document viewing, email notifications, audit trails, and batch processing capabilities.

## 🆕 Enhanced Features

### Document Management
- **Document Upload Tracking**: Complete metadata for all uploaded documents
- **Document Viewer**: Advanced image viewer with zoom, pan, and full-screen capabilities
- **Document Categories**: Organized by identity verification, address verification, and biometric verification
- **Individual Document Actions**: Approve or reject specific documents with detailed feedback
- **Document Quality Assessment**: Automated quality scoring and confidence metrics
- **Secure Access**: Signed URLs with expiration for secure document viewing
- **Audit Trail**: Complete logging of all document access and actions

## Features

### 🔍 **Submission Management**
- View all KYC submissions with detailed information
- Search and filter submissions by status, country, user details
- Paginated results with customizable page sizes
- Real-time statistics and metrics

### ✅ **Approval Workflow**
- Individual approve/reject actions with comments
- Batch approval for multiple submissions
- Detailed rejection reasons with user notifications
- Audit trail for all administrative actions

### 📧 **Email Notifications**
- Automatic email notifications for status changes
- Professional email templates for approvals and rejections
- Integration with existing Resend email service
- Notification logging and tracking

### 📊 **Analytics & Reporting**
- Real-time KYC statistics dashboard
- Processing time analytics
- Completion rate tracking
- Recent submission monitoring

### 🔒 **Security & Compliance**
- Row-level security (RLS) policies
- Admin action logging
- Secure data handling
- Permission-based access control

## Installation & Setup

### 1. Database Setup

Run the enhanced setup script to create necessary database tables and indexes:

```bash
node scripts/setup-enhanced-kyc-management.js
```

This script will:
- Add `rejection_reason` column to `kyc_information` table
- Create `kyc_documents` table for document metadata
- Create `kyc_document_access_log` table for audit trail
- Create `notification_logs` table for email tracking
- Set up indexes for optimal performance
- Configure RLS policies for security
- Create triggers for automatic timestamp updates

After the initial setup, migrate existing documents:

```bash
node scripts/migrate-existing-kyc-documents.js
```

This will scan the 'proof' storage bucket and create database records for existing documents.

### 2. Environment Variables

Ensure the following environment variables are configured:

```env
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Email Configuration (Resend)
VITE_RESEND_API_KEY=your_resend_api_key
VITE_RESEND_FROM_EMAIL=<EMAIL>
VITE_RESEND_FROM_NAME=Aureus Alliance Holdings
```

### 3. Admin Dashboard Integration

The KYC Management section is automatically integrated into the admin dashboard. Access it through:

1. Login to admin dashboard
2. Navigate to "KYC Management" section
3. View submissions, statistics, and perform actions

## Usage Guide

### Viewing KYC Submissions

1. **Dashboard Overview**: View key statistics including total submissions, pending reviews, approval rates, and processing times.

2. **Submission List**: Browse all KYC submissions with:
   - User information (username, email)
   - Personal details (name, country)
   - Submission status and dates
   - Quick action buttons

3. **Search & Filter**: Use the search bar and filters to find specific submissions:
   - Search by name, email, or username
   - Filter by status (pending, approved, rejected, expired)
   - Filter by country or date range

### Reviewing Submissions

1. **Individual Review**:
   - Click "View" to see detailed submission information
   - Review all personal, address, and compliance data
   - Add optional comments for the user
   - Approve or reject with detailed reasons

2. **Batch Operations**:
   - Select multiple submissions using checkboxes
   - Use "Batch Approve" for bulk approvals
   - Add comments that apply to all selected submissions

### Managing Rejections

When rejecting a KYC submission:

1. **Provide Clear Reasons**: Always specify why the submission is being rejected
2. **Add Helpful Comments**: Include guidance on how to correct issues
3. **Professional Communication**: Rejection emails are automatically sent with your feedback

## Database Schema

### KYC Information Table

```sql
kyc_information (
  id UUID PRIMARY KEY,
  user_id INTEGER REFERENCES users(id),
  first_name VARCHAR(100),
  last_name VARCHAR(100),
  full_legal_name VARCHAR(200),
  id_type VARCHAR(20),
  id_number_encrypted TEXT,
  phone_number VARCHAR(20),
  email_address VARCHAR(255),
  street_address TEXT,
  city VARCHAR(100),
  postal_code VARCHAR(20),
  country_code VARCHAR(3),
  country_name VARCHAR(100),
  kyc_status VARCHAR(20),
  rejection_reason TEXT,  -- Added for detailed rejection feedback
  created_at TIMESTAMP,
  updated_at TIMESTAMP,
  last_modified_by VARCHAR(100)
)
```

### KYC Documents Table

```sql
kyc_documents (
  id UUID PRIMARY KEY,
  kyc_id UUID REFERENCES kyc_information(id),
  user_id INTEGER REFERENCES users(id),
  document_type VARCHAR(50), -- id_document_front, id_document_back, proof_of_address, etc.
  document_category VARCHAR(30), -- identity_verification, address_verification, biometric_verification
  file_name VARCHAR(255),
  file_path VARCHAR(500),
  storage_bucket VARCHAR(50),
  file_size_bytes INTEGER,
  mime_type VARCHAR(100),
  file_hash VARCHAR(64), -- SHA-256 for integrity
  verification_status VARCHAR(20), -- pending, approved, rejected, requires_resubmission
  quality_score DECIMAL(3,2), -- 0.00 to 1.00
  confidence_score DECIMAL(3,2), -- 0.00 to 1.00
  reviewed_by VARCHAR(100),
  reviewed_at TIMESTAMP,
  rejection_reason TEXT,
  admin_notes TEXT,
  metadata JSONB, -- Additional document-specific data
  uploaded_at TIMESTAMP,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)
```

### Document Access Log Table

```sql
kyc_document_access_log (
  id UUID PRIMARY KEY,
  document_id UUID REFERENCES kyc_documents(id),
  user_id INTEGER REFERENCES users(id),
  access_type VARCHAR(20), -- view, download, approve, reject
  ip_address INET,
  user_agent TEXT,
  accessed_at TIMESTAMP,
  created_at TIMESTAMP
)
```

### Notification Logs Table

```sql
notification_logs (
  id UUID PRIMARY KEY,
  user_id INTEGER REFERENCES users(id),
  notification_type VARCHAR(50),
  status VARCHAR(20),
  email_message_id VARCHAR(255),
  subject VARCHAR(255),
  recipient_email VARCHAR(255),
  metadata JSONB,
  error_message TEXT,
  sent_at TIMESTAMP,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)
```

## API Services

### KYCAdminService

Main service for KYC management operations:

```typescript
// Get submissions with filtering
const result = await kycAdminService.getKYCSubmissions(filters, limit, offset);

// Approve submission
await kycAdminService.approveKYC(kycId, adminEmail, comments);

// Reject submission
await kycAdminService.rejectKYC(kycId, adminEmail, rejectionReason, comments);

// Get statistics
const stats = await kycAdminService.getKYCStatistics();
```

### KYCNotificationService

Email notification service:

```typescript
// Send status notification
await kycNotificationService.sendKYCStatusNotification({
  kycId,
  userId,
  status: 'approved',
  adminEmail,
  comments
});
```

## Email Templates

### Approval Email
- Congratulatory message with verification confirmation
- Next steps and available features
- Professional branding and styling

### Rejection Email
- Clear explanation of issues identified
- Specific guidance for corrections
- Support contact information
- Resubmission instructions

## Security Considerations

1. **Data Protection**: All sensitive data is encrypted and handled securely
2. **Access Control**: Only authorized admins can access KYC management
3. **Audit Trail**: All actions are logged with admin identification
4. **Email Security**: Notifications use secure email service with tracking

## Performance Optimization

1. **Database Indexes**: Optimized queries with proper indexing
2. **Pagination**: Large datasets are paginated for better performance
3. **Caching**: Statistics are cached and refreshed periodically
4. **Batch Processing**: Efficient bulk operations for large datasets

## Troubleshooting

### Common Issues

1. **Email Not Sending**:
   - Check Resend API key configuration
   - Verify email service status
   - Check notification logs for error details

2. **Database Errors**:
   - Ensure service role key has proper permissions
   - Check database connection
   - Verify table structure with setup script

3. **Permission Issues**:
   - Confirm admin user has proper role
   - Check RLS policies are correctly configured
   - Verify user authentication status

### Monitoring

- Check notification logs for email delivery status
- Monitor admin action logs for audit trail
- Review KYC statistics for processing metrics
- Use database performance monitoring for optimization

## Support

For technical support or questions about the KYC Management System:

1. Check the troubleshooting section above
2. Review database logs for error details
3. Contact the development team with specific error messages
4. Provide relevant user IDs and timestamps for investigation
