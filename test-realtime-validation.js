/**
 * Test real-time duplicate validation
 */

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'https://fgubaqoftdeefcakejwu.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA'
);

async function testEmailValidation() {
  console.log('\n🔍 Testing email duplicate validation...');
  
  // Test with existing email from your screenshot
  const existingEmail = '<EMAIL>';
  
  const { data: existingUsers, error: checkError } = await supabase
    .from('users')
    .select('email')
    .eq('email', existingEmail)
    .limit(1);

  if (checkError) {
    console.error('❌ Error checking for duplicate email:', checkError);
    return;
  }

  console.log('📊 Email check results for', existingEmail, ':', existingUsers);

  if (existingUsers && existingUsers.length > 0) {
    console.log('✅ DUPLICATE EMAIL VALIDATION SHOULD TRIGGER');
    console.log('   Expected error: "This email address is already registered..."');
  } else {
    console.log('❌ NO DUPLICATE FOUND - Email is available');
  }
}

async function testUsernameValidation() {
  console.log('\n🔍 Testing username duplicate validation...');
  
  // Test with existing username
  const existingUsername = 'jp.rademeyer84+1';
  
  const { data: existingUsers, error: checkError } = await supabase
    .from('users')
    .select('username')
    .eq('username', existingUsername)
    .limit(1);

  if (checkError) {
    console.error('❌ Error checking for duplicate username:', checkError);
    return;
  }

  console.log('📊 Username check results for', existingUsername, ':', existingUsers);

  if (existingUsers && existingUsers.length > 0) {
    console.log('✅ DUPLICATE USERNAME VALIDATION SHOULD TRIGGER');
    console.log('   Expected error: "This username is already taken..."');
  } else {
    console.log('❌ NO DUPLICATE FOUND - Username is available');
  }
}

async function runTests() {
  console.log('🧪 Testing real-time duplicate validation...');
  
  await testEmailValidation();
  await testUsernameValidation();
  
  console.log('\n✅ Validation tests completed!');
  console.log('\n📝 Instructions for manual testing:');
  console.log('1. Go to http://localhost:8001');
  console.log('2. Try typing "<EMAIL>" in the email field');
  console.log('3. You should see an error message appear after ~1 second');
  console.log('4. Try typing "jp.rademeyer84+1" in the username field');
  console.log('5. You should see an error message appear after ~1 second');
}

runTests().catch(console.error);
