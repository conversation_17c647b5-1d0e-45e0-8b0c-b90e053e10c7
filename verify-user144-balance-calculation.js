/**
 * Verify User 144 Balance Calculation - Check if $100.27 is correct
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config()

const supabase = createClient(process.env.VITE_SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY)

async function verifyUser144Balance() {
  console.log('🔍 Verifying User 144 Balance Calculation\n')
  
  const userId = 144

  try {
    console.log('1️⃣ Commission Transactions (USDT Earned):')
    console.log('═══════════════════════════════════════════')
    
    const { data: transactions, error: transError } = await supabase
      .from('commission_transactions')
      .select('usdt_commission, share_commission, status, created_at, share_purchase_amount, referred_id')
      .eq('referrer_id', userId)
      .eq('status', 'approved')
      .order('created_at')
      
    if (transError) throw transError

    let totalEarnedUSDT = 0
    transactions.forEach((trans, index) => {
      const usdt = parseFloat(trans.usdt_commission || 0)
      totalEarnedUSDT += usdt
      console.log(`   ${index + 1}. $${usdt.toFixed(2)} USDT (${trans.created_at.split('T')[0]})`)
    })
    
    console.log(`   ────────────────────────────────────────`)
    console.log(`   💰 TOTAL EARNED: $${totalEarnedUSDT.toFixed(2)} USDT`)

    console.log('\n2️⃣ Commission Conversions (USDT Spent):')
    console.log('═══════════════════════════════════════════')
    
    const { data: conversions, error: convError } = await supabase
      .from('commission_conversions')
      .select('usdt_amount, shares_requested, status, created_at, approved_at')
      .eq('user_id', userId)
      .eq('status', 'approved')
      .order('created_at')
      
    if (convError) throw convError

    let totalConvertedUSDT = 0
    conversions.forEach((conv, index) => {
      const usdt = parseFloat(conv.usdt_amount || 0)
      totalConvertedUSDT += usdt
      console.log(`   ${index + 1}. $${usdt.toFixed(2)} USDT → ${conv.shares_requested} shares (${conv.created_at.split('T')[0]})`)
    })
    
    console.log(`   ────────────────────────────────────────`)
    console.log(`   💸 TOTAL CONVERTED: $${totalConvertedUSDT.toFixed(2)} USDT`)

    console.log('\n3️⃣ Commission Withdrawals (USDT Withdrawn):')
    console.log('═══════════════════════════════════════════')
    
    const { data: withdrawals, error: withError } = await supabase
      .from('commission_withdrawals')
      .select('amount, status, created_at')
      .eq('user_id', userId)
      .in('status', ['approved', 'completed'])
      .order('created_at')
      
    if (withError) throw withError

    let totalWithdrawnUSDT = 0
    if (withdrawals && withdrawals.length > 0) {
      withdrawals.forEach((withdrawal, index) => {
        const amount = parseFloat(withdrawal.amount || 0)
        totalWithdrawnUSDT += amount
        console.log(`   ${index + 1}. $${amount.toFixed(2)} USDT (${withdrawal.status}) - ${withdrawal.created_at.split('T')[0]}`)
      })
    } else {
      console.log(`   📝 No withdrawals found`)
    }
    
    console.log(`   ────────────────────────────────────────`)
    console.log(`   💳 TOTAL WITHDRAWN: $${totalWithdrawnUSDT.toFixed(2)} USDT`)

    console.log('\n4️⃣ Current Commission Balance (Database):')
    console.log('═══════════════════════════════════════════')
    
    const { data: balance, error: balError } = await supabase
      .from('commission_balances')
      .select('*')
      .eq('user_id', userId)
      .single()
      
    if (balError) throw balError

    console.log(`   💰 USDT Balance: $${parseFloat(balance.usdt_balance || 0).toFixed(2)}`)
    console.log(`   📊 Share Balance: ${parseFloat(balance.share_balance || 0).toFixed(2)}`)
    console.log(`   💎 Total Earned USDT: $${parseFloat(balance.total_earned_usdt || 0).toFixed(2)}`)
    console.log(`   🎁 Total Earned Shares: ${parseFloat(balance.total_earned_shares || 0).toFixed(2)}`)
    console.log(`   🔒 Escrowed Amount: $${parseFloat(balance.escrowed_amount || 0).toFixed(2)}`)
    console.log(`   💸 Total Withdrawn: $${parseFloat(balance.total_withdrawn || 0).toFixed(2)}`)

    console.log('\n5️⃣ Manual Balance Calculation:')
    console.log('═══════════════════════════════════════════')
    
    const escrowedAmount = parseFloat(balance.escrowed_amount || 0)
    const calculatedBalance = totalEarnedUSDT - totalConvertedUSDT - totalWithdrawnUSDT
    const availableBalance = calculatedBalance - escrowedAmount
    
    console.log(`   📥 Total Earned: $${totalEarnedUSDT.toFixed(2)}`)
    console.log(`   📤 Total Converted: -$${totalConvertedUSDT.toFixed(2)}`)
    console.log(`   💳 Total Withdrawn: -$${totalWithdrawnUSDT.toFixed(2)}`)
    console.log(`   ────────────────────────────────────────`)
    console.log(`   💰 Calculated Balance: $${calculatedBalance.toFixed(2)}`)
    console.log(`   🔒 Escrowed Amount: -$${escrowedAmount.toFixed(2)}`)
    console.log(`   ────────────────────────────────────────`)
    console.log(`   ✅ Available Balance: $${availableBalance.toFixed(2)}`)

    console.log('\n6️⃣ Verification:')
    console.log('═══════════════════════════════════════════')
    
    const dbBalance = parseFloat(balance.usdt_balance || 0)
    const dbTotalEarned = parseFloat(balance.total_earned_usdt || 0)
    const dbTotalWithdrawn = parseFloat(balance.total_withdrawn || 0)
    
    console.log(`   🏦 Database Balance: $${dbBalance.toFixed(2)}`)
    console.log(`   🧮 Calculated Balance: $${calculatedBalance.toFixed(2)}`)
    console.log(`   📊 Match: ${Math.abs(dbBalance - calculatedBalance) < 0.01 ? '✅ YES' : '❌ NO'}`)
    
    console.log(`\n   🏦 Database Total Earned: $${dbTotalEarned.toFixed(2)}`)
    console.log(`   🧮 Calculated Total Earned: $${totalEarnedUSDT.toFixed(2)}`)
    console.log(`   📊 Match: ${Math.abs(dbTotalEarned - totalEarnedUSDT) < 0.01 ? '✅ YES' : '❌ NO'}`)
    
    console.log(`\n   🏦 Database Total Withdrawn: $${dbTotalWithdrawn.toFixed(2)}`)
    console.log(`   🧮 Calculated Total Withdrawn: $${totalWithdrawnUSDT.toFixed(2)}`)
    console.log(`   📊 Match: ${Math.abs(dbTotalWithdrawn - totalWithdrawnUSDT) < 0.01 ? '✅ YES' : '❌ NO'}`)

    console.log('\n7️⃣ Conclusion:')
    console.log('═══════════════════════════════════════════')
    
    if (Math.abs(dbBalance - calculatedBalance) < 0.01) {
      console.log(`   ✅ The $${dbBalance.toFixed(2)} balance is CORRECT`)
      console.log(`   ✅ This represents legitimate USDT commission earnings`)
      console.log(`   ✅ NOT from bonus shares - from actual referral commissions`)
      console.log(`   ✅ Available for withdrawal or conversion to shares`)
    } else {
      console.log(`   ❌ Balance discrepancy detected`)
      console.log(`   🔍 Database shows: $${dbBalance.toFixed(2)}`)
      console.log(`   🔍 Should be: $${calculatedBalance.toFixed(2)}`)
      console.log(`   🔍 Difference: $${(dbBalance - calculatedBalance).toFixed(2)}`)
    }

    return {
      success: true,
      balanceCorrect: Math.abs(dbBalance - calculatedBalance) < 0.01,
      databaseBalance: dbBalance,
      calculatedBalance: calculatedBalance,
      totalEarned: totalEarnedUSDT,
      totalConverted: totalConvertedUSDT,
      totalWithdrawn: totalWithdrawnUSDT,
      escrowedAmount: escrowedAmount
    };

  } catch (error) {
    console.error('❌ Verification failed:', error);
    return { success: false, error: error.message };
  }
}

// Run the verification
verifyUser144Balance()
  .then(result => {
    console.log('\n🎯 VERIFICATION RESULT:', result.success ? 'COMPLETED' : 'FAILED');
    if (result.success && result.balanceCorrect) {
      console.log('✅ User 144 balance is mathematically correct!');
    }
  })
  .catch(console.error);
