#!/usr/bin/env node

/**
 * SIMPLE PASSWORD RESET TEST
 * 
 * This script tests the password reset functionality by checking:
 * 1. Database schema has reset token fields
 * 2. A test user exists for testing
 * 3. Basic password reset flow components
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testPasswordResetSetup() {
  console.log('🧪 Testing password reset setup...\n');

  try {
    // Step 1: Check if users table has reset token fields
    console.log('1️⃣ Checking database schema for reset token fields...');
    
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email, reset_token, reset_token_expires')
      .limit(1);
    
    if (usersError) {
      console.error('❌ Failed to query users table:', usersError);
      return false;
    }
    
    console.log('✅ Users table accessible with reset token fields');
    
    // Step 2: Check if we have a test user
    console.log('\n2️⃣ Looking for test users...');
    
    const { data: testUsers, error: testUsersError } = await supabase
      .from('users')
      .select('id, email, username')
      .or('<EMAIL>,<EMAIL>,username.eq.test1')
      .limit(5);
    
    if (testUsersError) {
      console.error('❌ Failed to query test users:', testUsersError);
      return false;
    }
    
    console.log(`📊 Found ${testUsers.length} test users:`, testUsers.map(u => ({ email: u.email, username: u.username })));
    
    // Step 3: Test creating a reset token manually
    if (testUsers.length > 0) {
      const testUser = testUsers[0];
      console.log(`\n3️⃣ Testing reset token creation for: ${testUser.email}`);
      
      const resetToken = require('crypto').randomBytes(32).toString('hex');
      const resetExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
      
      const { error: updateError } = await supabase
        .from('users')
        .update({
          reset_token: resetToken,
          reset_token_expires: resetExpires.toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', testUser.id);
      
      if (updateError) {
        console.error('❌ Failed to create reset token:', updateError);
        return false;
      }
      
      console.log('✅ Reset token created successfully');
      console.log('🔑 Token (first 16 chars):', resetToken.substring(0, 16) + '...');
      
      // Step 4: Verify token was stored
      console.log('\n4️⃣ Verifying token storage...');
      
      const { data: updatedUser, error: verifyError } = await supabase
        .from('users')
        .select('reset_token, reset_token_expires')
        .eq('id', testUser.id)
        .single();
      
      if (verifyError) {
        console.error('❌ Failed to verify token storage:', verifyError);
        return false;
      }
      
      console.log('✅ Token stored successfully:', {
        hasToken: !!updatedUser.reset_token,
        tokenLength: updatedUser.reset_token?.length,
        expiresAt: updatedUser.reset_token_expires
      });
      
      // Step 5: Clean up - remove the test token
      console.log('\n5️⃣ Cleaning up test token...');
      
      const { error: cleanupError } = await supabase
        .from('users')
        .update({
          reset_token: null,
          reset_token_expires: null,
          updated_at: new Date().toISOString()
        })
        .eq('id', testUser.id);
      
      if (cleanupError) {
        console.warn('⚠️ Failed to clean up test token:', cleanupError);
      } else {
        console.log('✅ Test token cleaned up successfully');
      }
    }
    
    // Step 6: Check email service configuration
    console.log('\n6️⃣ Checking email service configuration...');
    
    const resendApiKey = process.env.RESEND_API_KEY;
    const resendFromEmail = process.env.RESEND_FROM_EMAIL;
    
    console.log('📧 Email configuration:', {
      hasResendApiKey: !!resendApiKey,
      resendFromEmail: resendFromEmail || 'Not configured',
      apiKeyLength: resendApiKey?.length || 0
    });
    
    if (!resendApiKey) {
      console.warn('⚠️ RESEND_API_KEY not configured - emails will not be sent');
    } else {
      console.log('✅ Email service appears to be configured');
    }
    
    console.log('\n✅ Password reset setup test completed!');
    console.log('📋 Summary:');
    console.log('   ✅ Database schema supports password reset');
    console.log('   ✅ Reset token creation/storage working');
    console.log('   ✅ Token cleanup working');
    console.log(`   ${resendApiKey ? '✅' : '⚠️'} Email service ${resendApiKey ? 'configured' : 'needs configuration'}`);
    
    return true;
    
  } catch (error) {
    console.error('❌ Password reset setup test failed:', error);
    return false;
  }
}

// Run the test
testPasswordResetSetup().then(success => {
  console.log(`\n${success ? '🎉' : '💥'} Test ${success ? 'PASSED' : 'FAILED'}`);
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ Test execution failed:', error);
  process.exit(1);
});
