/* Mobile Dashboard Optimizations */

/* Safe area insets for mobile devices */
.safe-area-inset-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-inset-top {
  padding-top: env(safe-area-inset-top);
}

/* Touch-friendly interactions */
.touch-manipulation {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* Mobile-first responsive breakpoints */
@media (max-width: 640px) {
  /* Mobile-specific styles */
  .mobile-card {
    border-radius: 12px;
    padding: 12px;
    margin-bottom: 12px;
  }
  
  .mobile-text-sm {
    font-size: 14px;
    line-height: 1.4;
  }
  
  .mobile-text-xs {
    font-size: 12px;
    line-height: 1.3;
  }
  
  /* Mobile navigation improvements */
  .mobile-nav-item {
    min-height: 44px; /* iOS recommended touch target */
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 12px;
  }
  
  /* Mobile button improvements */
  .mobile-button {
    min-height: 44px;
    min-width: 44px;
    padding: 12px 16px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.2s ease;
  }
  
  .mobile-button:active {
    transform: scale(0.98);
  }
  
  /* Mobile card grid improvements */
  .mobile-grid-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }
  
  .mobile-grid-1 {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  /* Mobile metric cards */
  .mobile-metric-card {
    background: #1f2937;
    border: 1px solid #374151;
    border-radius: 16px;
    padding: 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
  
  .mobile-metric-value {
    font-size: 18px;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 4px;
  }
  
  .mobile-metric-label {
    font-size: 12px;
    color: #9ca3af;
    line-height: 1.3;
  }
  
  /* Mobile header improvements */
  .mobile-header {
    position: sticky;
    top: 0;
    z-index: 30;
    background: #1f2937;
    border-bottom: 1px solid #374151;
    padding: 12px 16px;
  }
  
  /* Mobile content spacing */
  .mobile-content {
    padding: 16px;
    padding-bottom: 100px; /* Space for bottom navigation */
  }
  
  /* Mobile modal improvements */
  .mobile-modal {
    position: fixed;
    inset: 0;
    z-index: 50;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: flex-end;
    justify-content: center;
  }
  
  .mobile-modal-content {
    background: #1f2937;
    border-radius: 16px 16px 0 0;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    padding: 20px;
  }
  
  /* Mobile form improvements */
  .mobile-input {
    width: 100%;
    padding: 12px 16px;
    border-radius: 12px;
    border: 1px solid #374151;
    background: #111827;
    color: white;
    font-size: 16px; /* Prevents zoom on iOS */
  }
  
  .mobile-input:focus {
    outline: none;
    border-color: #fbbf24;
    box-shadow: 0 0 0 3px rgba(251, 191, 36, 0.1);
  }
}

/* Tablet styles */
@media (min-width: 641px) and (max-width: 1023px) {
  .tablet-grid-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }
  
  .tablet-grid-3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
  }
}

/* Desktop styles */
@media (min-width: 1024px) {
  .desktop-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    width: 256px;
    z-index: 40;
  }
  
  .desktop-content {
    margin-left: 256px;
  }
}

/* Animation improvements for mobile */
@media (prefers-reduced-motion: no-preference) {
  .mobile-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .mobile-fade-in {
    animation: fadeIn 0.2s ease-out;
  }
  
  @keyframes slideUp {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
}

/* Dark theme optimizations for mobile */
@media (max-width: 640px) {
  .mobile-dark-card {
    background: linear-gradient(145deg, #1f2937 0%, #111827 100%);
    border: 1px solid #374151;
    box-shadow: 
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 0 0 1px rgba(255, 255, 255, 0.05);
  }
  
  .mobile-dark-card:hover {
    box-shadow: 
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 0 0 1px rgba(255, 255, 255, 0.1);
  }
}

/* Mobile accessibility improvements */
@media (max-width: 640px) {
  .mobile-focus-visible:focus-visible {
    outline: 2px solid #fbbf24;
    outline-offset: 2px;
  }
  
  .mobile-high-contrast {
    filter: contrast(1.2);
  }
  
  /* Ensure text is readable on mobile */
  .mobile-text-readable {
    font-size: 16px;
    line-height: 1.5;
    color: #f9fafb;
  }
  
  .mobile-text-secondary {
    font-size: 14px;
    line-height: 1.4;
    color: #d1d5db;
  }
  
  .mobile-text-muted {
    font-size: 12px;
    line-height: 1.3;
    color: #9ca3af;
  }
}

/* Mobile loading states */
.mobile-skeleton {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Mobile error states */
.mobile-error-card {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 12px;
  padding: 16px;
  margin: 12px 0;
}

/* Mobile success states */
.mobile-success-card {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 12px;
  padding: 16px;
  margin: 12px 0;
}
