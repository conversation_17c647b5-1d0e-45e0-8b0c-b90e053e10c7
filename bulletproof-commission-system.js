/**
 * B<PERSON><PERSON><PERSON><PERSON>OOF COMMISSION SYSTEM
 * 
 * This system implements multiple layers of protection to ensure
 * commission calculations NEVER fail silently again.
 * 
 * CRITICAL FAILURE POINTS IDENTIFIED:
 * 1. JavaScript errors in commission processing functions
 * 2. Missing function parameters (currentPhase issue)
 * 3. Silent failures without proper error handling
 * 4. No validation of commission calculation results
 * 5. No automatic detection of missing commissions
 * 6. Multiple code paths for commission processing
 */

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co'
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseServiceKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

/**
 * COMMISSION VALIDATION SYSTEM
 * Validates that all commissions are calculated correctly
 */
class CommissionValidator {
  
  /**
   * Validate a single commission transaction
   */
  static validateCommissionTransaction(transaction, sharePurchase) {
    const errors = []
    
    // Validate USDT commission (15% of purchase amount)
    const expectedUSDTCommission = sharePurchase.total_amount * 0.15
    const actualUSDTCommission = parseFloat(transaction.usdt_commission || 0)
    
    if (Math.abs(actualUSDTCommission - expectedUSDTCommission) > 0.01) {
      errors.push(`USDT commission mismatch: expected ${expectedUSDTCommission.toFixed(2)}, got ${actualUSDTCommission.toFixed(2)}`)
    }
    
    // Validate share commission (15% of shares purchased)
    const expectedShareCommission = sharePurchase.shares_purchased * 0.15
    const actualShareCommission = parseFloat(transaction.share_commission || 0)
    
    if (Math.abs(actualShareCommission - expectedShareCommission) > 0.001) {
      errors.push(`Share commission mismatch: expected ${expectedShareCommission.toFixed(4)}, got ${actualShareCommission.toFixed(4)}`)
    }
    
    // Validate commission rate
    if (parseFloat(transaction.commission_rate) !== 15.00) {
      errors.push(`Commission rate should be 15.00%, got ${transaction.commission_rate}%`)
    }
    
    // Validate status
    if (transaction.status !== 'approved') {
      errors.push(`Commission status should be 'approved', got '${transaction.status}'`)
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      expectedUSDTCommission,
      expectedShareCommission
    }
  }
  
  /**
   * Find all missing commissions in the system
   */
  static async findMissingCommissions() {
    console.log('🔍 SCANNING FOR MISSING COMMISSIONS...')
    
    // Get all approved share purchases
    const { data: sharePurchases, error: purchaseError } = await supabase
      .from('aureus_share_purchases')
      .select(`
        *,
        users!user_id(id, username, full_name)
      `)
      .eq('status', 'active')
      .gt('total_amount', 0)
      .order('created_at', { ascending: false })
    
    if (purchaseError) {
      throw new Error(`Failed to get share purchases: ${purchaseError.message}`)
    }
    
    const missingCommissions = []
    
    for (const purchase of sharePurchases) {
      // Check if user has a referrer
      const { data: referral, error: referralError } = await supabase
        .from('referrals')
        .select('referrer_id, referred_id')
        .eq('referred_id', purchase.user_id)
        .eq('status', 'active')
        .single()
      
      if (referralError || !referral) {
        continue // No referrer, no commission needed
      }
      
      // Check if commission transaction exists
      const { data: commissionTransaction, error: commissionError } = await supabase
        .from('commission_transactions')
        .select('*')
        .eq('referrer_id', referral.referrer_id)
        .eq('referred_id', purchase.user_id)
        .eq('share_purchase_amount', purchase.total_amount)
        .single()
      
      if (commissionError || !commissionTransaction) {
        missingCommissions.push({
          purchase,
          referral,
          reason: 'No commission transaction found'
        })
      } else {
        // Validate existing commission
        const validation = this.validateCommissionTransaction(commissionTransaction, purchase)
        if (!validation.isValid) {
          missingCommissions.push({
            purchase,
            referral,
            commissionTransaction,
            reason: 'Invalid commission calculation',
            errors: validation.errors
          })
        }
      }
    }
    
    return missingCommissions
  }
  
  /**
   * Fix all missing or incorrect commissions
   */
  static async fixMissingCommissions(missingCommissions) {
    console.log(`🔧 FIXING ${missingCommissions.length} MISSING/INCORRECT COMMISSIONS...`)
    
    let fixedCount = 0
    let errorCount = 0
    
    for (const missing of missingCommissions) {
      try {
        const { purchase, referral } = missing
        
        // Calculate correct commissions
        const usdtCommission = purchase.total_amount * 0.15
        const shareCommission = purchase.shares_purchased * 0.15
        
        console.log(`\n💰 Fixing commission for purchase ${purchase.id}:`)
        console.log(`   User: ${purchase.users.full_name || purchase.users.username}`)
        console.log(`   Amount: $${purchase.total_amount}`)
        console.log(`   Shares: ${purchase.shares_purchased}`)
        console.log(`   USDT Commission: $${usdtCommission.toFixed(2)}`)
        console.log(`   Share Commission: ${shareCommission.toFixed(4)}`)
        
        if (missing.commissionTransaction) {
          // Update existing incorrect commission
          const { error: updateError } = await supabase
            .from('commission_transactions')
            .update({
              usdt_commission: usdtCommission,
              share_commission: shareCommission,
              commission_rate: 15.00,
              status: 'approved',
              updated_at: new Date().toISOString()
            })
            .eq('id', missing.commissionTransaction.id)
          
          if (updateError) {
            throw updateError
          }
          
          console.log(`   ✅ Updated existing commission transaction`)
        } else {
          // Create new commission transaction
          const { error: insertError } = await supabase
            .from('commission_transactions')
            .insert({
              referrer_id: referral.referrer_id,
              referred_id: purchase.user_id,
              share_purchase_id: purchase.id,
              commission_rate: 15.00,
              share_purchase_amount: purchase.total_amount,
              usdt_commission: usdtCommission,
              share_commission: shareCommission,
              status: 'approved',
              payment_date: purchase.created_at,
              created_at: purchase.created_at
            })
          
          if (insertError) {
            throw insertError
          }
          
          console.log(`   ✅ Created new commission transaction`)
        }
        
        // Update referrer's commission balance
        await this.updateCommissionBalance(referral.referrer_id, usdtCommission, shareCommission)
        
        fixedCount++
        
      } catch (error) {
        console.error(`   ❌ Failed to fix commission: ${error.message}`)
        errorCount++
      }
    }
    
    console.log(`\n📊 COMMISSION FIX SUMMARY:`)
    console.log(`   Fixed: ${fixedCount}`)
    console.log(`   Errors: ${errorCount}`)
    
    return { fixedCount, errorCount }
  }
  
  /**
   * Update a user's commission balance
   */
  static async updateCommissionBalance(userId, usdtAmount, shareAmount) {
    // Get current balance
    const { data: currentBalance, error: balanceError } = await supabase
      .from('commission_balances')
      .select('*')
      .eq('user_id', userId)
      .single()
    
    if (balanceError && balanceError.code !== 'PGRST116') {
      throw new Error(`Failed to get commission balance: ${balanceError.message}`)
    }
    
    const newUsdtBalance = (parseFloat(currentBalance?.usdt_balance || 0)) + usdtAmount
    const newShareBalance = (parseFloat(currentBalance?.share_balance || 0)) + shareAmount
    const newTotalUsdtEarned = (parseFloat(currentBalance?.total_earned_usdt || 0)) + usdtAmount
    const newTotalSharesEarned = (parseFloat(currentBalance?.total_earned_shares || 0)) + shareAmount
    
    // Upsert balance
    const { error: updateError } = await supabase
      .from('commission_balances')
      .upsert({
        user_id: userId,
        usdt_balance: newUsdtBalance,
        share_balance: newShareBalance,
        total_earned_usdt: newTotalUsdtEarned,
        total_earned_shares: newTotalSharesEarned,
        last_updated: new Date().toISOString()
      }, { onConflict: 'user_id' })
    
    if (updateError) {
      throw new Error(`Failed to update commission balance: ${updateError.message}`)
    }
  }
}

/**
 * MAIN EXECUTION
 */
async function runBulletproofCommissionSystem() {
  try {
    console.log('🛡️  BULLETPROOF COMMISSION SYSTEM - FULL SCAN & FIX')
    console.log('=' .repeat(60))
    
    // Step 1: Find all missing commissions
    const missingCommissions = await CommissionValidator.findMissingCommissions()
    
    if (missingCommissions.length === 0) {
      console.log('✅ NO MISSING COMMISSIONS FOUND - SYSTEM IS HEALTHY!')
      return
    }
    
    console.log(`\n⚠️  FOUND ${missingCommissions.length} MISSING/INCORRECT COMMISSIONS:`)
    missingCommissions.forEach((missing, index) => {
      console.log(`   ${index + 1}. User: ${missing.purchase.users.full_name || missing.purchase.users.username}`)
      console.log(`      Purchase: $${missing.purchase.total_amount} (${missing.purchase.shares_purchased} shares)`)
      console.log(`      Reason: ${missing.reason}`)
      if (missing.errors) {
        missing.errors.forEach(error => console.log(`      Error: ${error}`))
      }
    })
    
    // Step 2: Fix all missing commissions
    const result = await CommissionValidator.fixMissingCommissions(missingCommissions)
    
    console.log('\n🎉 BULLETPROOF COMMISSION SYSTEM COMPLETE!')
    console.log(`   Total Issues Found: ${missingCommissions.length}`)
    console.log(`   Successfully Fixed: ${result.fixedCount}`)
    console.log(`   Errors: ${result.errorCount}`)
    
    if (result.errorCount > 0) {
      console.log('\n⚠️  SOME ISSUES COULD NOT BE FIXED - MANUAL REVIEW REQUIRED')
    } else {
      console.log('\n✅ ALL COMMISSION ISSUES HAVE BEEN RESOLVED!')
    }
    
  } catch (error) {
    console.error('❌ BULLETPROOF COMMISSION SYSTEM FAILED:', error)
    throw error
  }
}

// Export for use in other modules
export { CommissionValidator, runBulletproofCommissionSystem }

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runBulletproofCommissionSystem().catch(console.error)
}
