import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkCommissionTables() {
  console.log('🔍 CHECKING COMMISSION-RELATED TABLES\n');

  const tablesToCheck = [
    'commission_distributions',
    'commission_transactions', 
    'commission_balances',
    'aureus_share_purchases'
  ];

  for (const tableName of tablesToCheck) {
    console.log(`📋 Checking table: ${tableName}`);
    
    try {
      // Try to get table structure by selecting first row
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1);

      if (error) {
        console.log(`   ❌ Error: ${error.message}`);
        if (error.code) {
          console.log(`   Code: ${error.code}`);
        }
      } else {
        console.log(`   ✅ Table exists with ${data.length} sample rows`);
        if (data.length > 0) {
          console.log(`   Columns: ${Object.keys(data[0]).join(', ')}`);
        }
      }
    } catch (err) {
      console.log(`   ❌ Exception: ${err.message}`);
    }
    console.log('');
  }

  // Check commission_transactions specifically for phase-related data
  console.log('💰 Checking commission_transactions for phase data...');
  try {
    const { data: commissions, error } = await supabase
      .from('commission_transactions')
      .select('*')
      .limit(5);

    if (error) {
      console.log(`   ❌ Error: ${error.message}`);
    } else {
      console.log(`   ✅ Found ${commissions.length} commission transactions`);
      if (commissions.length > 0) {
        console.log('   Sample transaction:');
        console.log('   ', JSON.stringify(commissions[0], null, 2));
      }
    }
  } catch (err) {
    console.log(`   ❌ Exception: ${err.message}`);
  }

  // Check aureus_share_purchases for phase linkage
  console.log('\n📊 Checking aureus_share_purchases for phase linkage...');
  try {
    const { data: purchases, error } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .limit(3);

    if (error) {
      console.log(`   ❌ Error: ${error.message}`);
    } else {
      console.log(`   ✅ Found ${purchases.length} share purchases`);
      if (purchases.length > 0) {
        console.log('   Sample purchase:');
        console.log('   ', JSON.stringify(purchases[0], null, 2));
      }
    }
  } catch (err) {
    console.log(`   ❌ Exception: ${err.message}`);
  }
}

checkCommissionTables();
