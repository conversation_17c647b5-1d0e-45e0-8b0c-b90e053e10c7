const http = require('http');

async function testEmailAPI() {
  console.log('📧 Testing email verification API...');
  
  const postData = JSON.stringify({
    email: '<EMAIL>', // Use your actual email for testing
    code: '123456',
    purpose: 'registration',
    userName: 'Test User',
    expiryMinutes: 15
  });

  const options = {
    hostname: 'localhost',
    port: 8002,
    path: '/api/send-verification-email',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          console.log('📧 Email API Response:', result);
          
          if (res.statusCode === 200) {
            console.log('✅ Email API working correctly');
            console.log('📊 Results:', {
              success: result.success,
              messageId: result.messageId,
              message: result.message
            });
          } else {
            console.error('❌ Email API error:', result);
          }
          resolve(result);
        } catch (error) {
          console.error('❌ Parse error:', error.message);
          console.log('Raw response:', data);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.error('❌ Request error:', error.message);
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

testEmailAPI().catch(console.error);
