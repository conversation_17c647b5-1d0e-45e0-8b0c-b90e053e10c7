/**
 * COMPREHENSIVE SQL INJECTION PROTECTION TESTING SCRIPT
 * 
 * This script tests all forms and API endpoints for SQL injection vulnerabilities
 * and validates that the security measures are working correctly.
 */

import axios from 'axios';
import fs from 'fs';

// Configuration
const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:8002';
const TEST_RESULTS_FILE = 'security-test-results.json';

// Common SQL injection payloads
const SQL_INJECTION_PAYLOADS = [
  // Basic SQL injection
  "'; DROP TABLE users; --",
  "' OR '1'='1",
  "' OR 1=1 --",
  "admin'--",
  "admin'/*",
  
  // Union-based injection
  "' UNION SELECT * FROM users --",
  "' UNION SELECT username, password FROM users --",
  "1' UNION SELECT null, username, password FROM users --",
  
  // Boolean-based blind injection
  "' AND 1=1 --",
  "' AND 1=2 --",
  "' AND (SELECT COUNT(*) FROM users) > 0 --",
  
  // Time-based blind injection
  "'; WAITFOR DELAY '00:00:05' --",
  "' OR SLEEP(5) --",
  "'; SELECT BENCHMARK(5000000,MD5(1)) --",
  
  // Error-based injection
  "' AND EXTRACTVALUE(1, CONCAT(0x7e, (SELECT version()), 0x7e)) --",
  "' AND (SELECT * FROM (SELECT COUNT(*),CONCAT(version(),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a) --",
  
  // Advanced payloads
  "'; INSERT INTO users (username, password) VALUES ('hacker', 'password'); --",
  "'; UPDATE users SET password='hacked' WHERE username='admin'; --",
  "' OR EXISTS(SELECT * FROM users WHERE username='admin') --",
  
  // Encoded payloads
  "%27%20OR%20%271%27%3D%271",
  "%27%3B%20DROP%20TABLE%20users%3B%20--",
  
  // NoSQL injection (for completeness)
  "'; return true; //",
  "' || '1'=='1",
  
  // XSS attempts (should also be blocked)
  "<script>alert('XSS')</script>",
  "javascript:alert('XSS')",
  "<img src=x onerror=alert('XSS')>",
  
  // Path traversal
  "../../../etc/passwd",
  "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
  
  // Command injection
  "; cat /etc/passwd",
  "| whoami",
  "&& dir",
  "`id`",
  "$(whoami)"
];

// Test results storage
const testResults = {
  timestamp: new Date().toISOString(),
  summary: {
    totalTests: 0,
    passed: 0,
    failed: 0,
    errors: 0
  },
  endpoints: {}
};

/**
 * Log test result
 */
function logResult(endpoint, payload, result, details = {}) {
  if (!testResults.endpoints[endpoint]) {
    testResults.endpoints[endpoint] = {
      totalTests: 0,
      passed: 0,
      failed: 0,
      errors: 0,
      tests: []
    };
  }
  
  const test = {
    payload: payload.substring(0, 100), // Limit payload length in logs
    result,
    timestamp: new Date().toISOString(),
    ...details
  };
  
  testResults.endpoints[endpoint].tests.push(test);
  testResults.endpoints[endpoint].totalTests++;
  testResults.endpoints[endpoint][result]++;
  
  testResults.summary.totalTests++;
  testResults.summary[result]++;
  
  const status = result === 'passed' ? '✅' : result === 'failed' ? '❌' : '⚠️';
  console.log(`${status} ${endpoint}: ${payload.substring(0, 50)}... - ${result.toUpperCase()}`);
  
  if (details.error) {
    console.log(`   Error: ${details.error}`);
  }
  if (details.response) {
    console.log(`   Response: ${JSON.stringify(details.response).substring(0, 100)}...`);
  }
}

/**
 * Test contact form endpoint
 */
async function testContactForm() {
  console.log('\n🔍 Testing Contact Form (/api/contact)...');
  
  for (const payload of SQL_INJECTION_PAYLOADS) {
    try {
      const response = await axios.post(`${BASE_URL}/api/contact`, {
        name: payload,
        surname: 'Test',
        email: '<EMAIL>',
        message: 'Test message'
      }, {
        timeout: 10000,
        validateStatus: () => true // Don't throw on 4xx/5xx
      });
      
      // Check if the request was properly blocked
      if (response.status === 400 && 
          (response.data.error?.includes('Invalid') || 
           response.data.error?.includes('Malicious') ||
           response.data.details?.some(d => d.includes('SQL_INJECTION') || d.includes('XSS')))) {
        logResult('/api/contact', payload, 'passed', {
          statusCode: response.status,
          response: response.data
        });
      } else if (response.status >= 500) {
        logResult('/api/contact', payload, 'error', {
          statusCode: response.status,
          error: response.data.error || 'Server error'
        });
      } else {
        logResult('/api/contact', payload, 'failed', {
          statusCode: response.status,
          response: response.data,
          reason: 'Malicious input was not blocked'
        });
      }
      
    } catch (error) {
      logResult('/api/contact', payload, 'error', {
        error: error.message
      });
    }
    
    // Small delay to avoid overwhelming the server
    await new Promise(resolve => setTimeout(resolve, 100));
  }
}

/**
 * Test crypto payments endpoint
 */
async function testCryptoPayments() {
  console.log('\n🔍 Testing Crypto Payments (/api/crypto-payments)...');
  
  for (const payload of SQL_INJECTION_PAYLOADS) {
    try {
      const response = await axios.post(`${BASE_URL}/api/crypto-payments`, {
        amount: 100,
        shares_to_purchase: 4,
        network: 'BSC',
        currency: 'USDT',
        sender_wallet: payload, // Inject into wallet field
        receiver_wallet: '******************************************',
        transaction_hash: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890',
        screenshot_url: 'https://example.com/screenshot.jpg',
        transaction_notes: 'Test payment'
      }, {
        headers: {
          'Authorization': 'Bearer db_123' // Test user ID
        },
        timeout: 10000,
        validateStatus: () => true
      });
      
      // Check if the request was properly blocked
      if (response.status === 400 && 
          (response.data.error?.includes('Invalid') || 
           response.data.error?.includes('Malicious') ||
           response.data.details?.some(d => typeof d === 'string' && (d.includes('SQL_INJECTION') || d.includes('XSS'))))) {
        logResult('/api/crypto-payments', payload, 'passed', {
          statusCode: response.status,
          response: response.data
        });
      } else if (response.status >= 500) {
        logResult('/api/crypto-payments', payload, 'error', {
          statusCode: response.status,
          error: response.data.error || 'Server error'
        });
      } else {
        logResult('/api/crypto-payments', payload, 'failed', {
          statusCode: response.status,
          response: response.data,
          reason: 'Malicious input was not blocked'
        });
      }
      
    } catch (error) {
      logResult('/api/crypto-payments', payload, 'error', {
        error: error.message
      });
    }
    
    await new Promise(resolve => setTimeout(resolve, 100));
  }
}

/**
 * Test user registration endpoint (if exists)
 */
async function testUserRegistration() {
  console.log('\n🔍 Testing User Registration (/api/register)...');
  
  for (const payload of SQL_INJECTION_PAYLOADS.slice(0, 10)) { // Test subset for registration
    try {
      const response = await axios.post(`${BASE_URL}/api/register`, {
        username: payload,
        email: '<EMAIL>',
        password: 'TestPassword123!',
        full_name: 'Test User',
        country: 'Test Country'
      }, {
        timeout: 10000,
        validateStatus: () => true
      });
      
      if (response.status === 400 && 
          (response.data.error?.includes('Invalid') || 
           response.data.error?.includes('Malicious'))) {
        logResult('/api/register', payload, 'passed', {
          statusCode: response.status,
          response: response.data
        });
      } else if (response.status >= 500) {
        logResult('/api/register', payload, 'error', {
          statusCode: response.status,
          error: response.data.error || 'Server error'
        });
      } else {
        logResult('/api/register', payload, 'failed', {
          statusCode: response.status,
          response: response.data,
          reason: 'Malicious input was not blocked'
        });
      }
      
    } catch (error) {
      if (error.response?.status === 404) {
        console.log('   Registration endpoint not found, skipping...');
        break;
      }
      logResult('/api/register', payload, 'error', {
        error: error.message
      });
    }
    
    await new Promise(resolve => setTimeout(resolve, 100));
  }
}

/**
 * Generate security report
 */
function generateReport() {
  console.log('\n📊 SECURITY TEST RESULTS SUMMARY');
  console.log('=====================================');
  console.log(`Total Tests: ${testResults.summary.totalTests}`);
  console.log(`✅ Passed: ${testResults.summary.passed}`);
  console.log(`❌ Failed: ${testResults.summary.failed}`);
  console.log(`⚠️  Errors: ${testResults.summary.errors}`);
  
  const successRate = testResults.summary.totalTests > 0 
    ? ((testResults.summary.passed / testResults.summary.totalTests) * 100).toFixed(1)
    : 0;
  console.log(`🎯 Success Rate: ${successRate}%`);
  
  console.log('\n📋 ENDPOINT BREAKDOWN:');
  for (const [endpoint, results] of Object.entries(testResults.endpoints)) {
    const endpointSuccessRate = results.totalTests > 0 
      ? ((results.passed / results.totalTests) * 100).toFixed(1)
      : 0;
    console.log(`  ${endpoint}: ${results.passed}/${results.totalTests} (${endpointSuccessRate}%)`);
  }
  
  // Save detailed results to file
  fs.writeFileSync(TEST_RESULTS_FILE, JSON.stringify(testResults, null, 2));
  console.log(`\n💾 Detailed results saved to: ${TEST_RESULTS_FILE}`);
  
  // Security recommendations
  console.log('\n🔒 SECURITY RECOMMENDATIONS:');
  if (testResults.summary.failed > 0) {
    console.log('❌ CRITICAL: Some SQL injection attempts were not blocked!');
    console.log('   - Review failed tests in the detailed results file');
    console.log('   - Ensure all input validation is properly implemented');
    console.log('   - Check that parameterized queries are used everywhere');
  } else {
    console.log('✅ EXCELLENT: All SQL injection attempts were properly blocked!');
  }
  
  if (testResults.summary.errors > 0) {
    console.log('⚠️  WARNING: Some tests encountered errors');
    console.log('   - Check server logs for any issues');
    console.log('   - Ensure all endpoints are accessible for testing');
  }
}

/**
 * Main test execution
 */
async function runSecurityTests() {
  console.log('🚀 STARTING COMPREHENSIVE SQL INJECTION SECURITY AUDIT');
  console.log('======================================================');
  console.log(`Target: ${BASE_URL}`);
  console.log(`Payloads: ${SQL_INJECTION_PAYLOADS.length}`);
  console.log(`Started: ${new Date().toISOString()}\n`);
  
  try {
    // Test all endpoints
    await testContactForm();
    await testCryptoPayments();
    await testUserRegistration();
    
    // Generate final report
    generateReport();
    
    console.log('\n🎉 Security audit completed successfully!');
    
    // Exit with appropriate code
    process.exit(testResults.summary.failed > 0 ? 1 : 0);
    
  } catch (error) {
    console.error('\n❌ Security audit failed:', error);
    process.exit(1);
  }
}

// Run the tests
const currentFile = new URL(import.meta.url).pathname;
const mainFile = process.argv[1];
if (currentFile === mainFile || process.argv[1].endsWith('test-sql-injection-protection.js')) {
  runSecurityTests();
}

export {
  runSecurityTests,
  SQL_INJECTION_PAYLOADS,
  testResults
};
