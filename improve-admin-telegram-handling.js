#!/usr/bin/env node

/**
 * IMPROVE ADMIN TELEGRAM HANDLING
 * 
 * This script creates helper functions and improvements for the admin
 * user management system to properly handle Telegram users.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Helper function to identify Telegram users and get correct user record
 */
const getTelegramUserInfo = async (user) => {
  try {
    // Check if user has telegram_id (direct Telegram user)
    if (user.telegram_id) {
      return { 
        isTelegramUser: true, 
        userRecord: user, 
        linkType: 'direct',
        telegramId: user.telegram_id
      }
    }
    
    // Check if user is linked from telegram_users table
    const { data: telegramUser, error } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('user_id', user.id)
      .single()
      
    if (!error && telegramUser) {
      return { 
        isTelegramUser: true, 
        userRecord: user, 
        linkType: 'linked', 
        telegramUser,
        telegramId: telegramUser.telegram_id
      }
    }
    
    return { 
      isTelegramUser: false, 
      userRecord: user, 
      linkType: 'none' 
    }
  } catch (error) {
    console.error('Error getting Telegram user info:', error)
    return { 
      isTelegramUser: false, 
      userRecord: user, 
      linkType: 'error' 
    }
  }
}

/**
 * Improved admin password change function that handles Telegram users correctly
 */
const adminChangePassword = async (userId, newPassword, adminEmail) => {
  try {
    console.log(`🔐 Admin changing password for user ID: ${userId}`)
    
    // Get the user record
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single()
    
    if (userError || !user) {
      throw new Error(`User not found: ${userError?.message}`)
    }
    
    // Get Telegram user info
    const telegramInfo = await getTelegramUserInfo(user)
    
    console.log('📋 User Analysis:')
    console.log(`   User ID: ${user.id}`)
    console.log(`   Username: ${user.username}`)
    console.log(`   Email: ${user.email}`)
    console.log(`   Is Telegram User: ${telegramInfo.isTelegramUser}`)
    console.log(`   Link Type: ${telegramInfo.linkType}`)
    if (telegramInfo.telegramId) {
      console.log(`   Telegram ID: ${telegramInfo.telegramId}`)
    }
    
    // Hash the new password
    const bcrypt = await import('bcryptjs')
    const hashedPassword = await bcrypt.default.hash(newPassword, 12)
    
    // Update the user record (this is always the correct record to update)
    const { error: updateError } = await supabase
      .from('users')
      .update({
        password_hash: hashedPassword,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)
    
    if (updateError) {
      throw new Error(`Failed to update password: ${updateError.message}`)
    }
    
    console.log('✅ Password updated successfully')
    
    // Log the admin action with proper context
    const logEntry = {
      admin_telegram_id: 0, // Web admin action
      admin_username: adminEmail,
      action: 'UPDATE_USER_PASSWORD',
      target_type: 'user',
      target_id: userId.toString(),
      details: {
        user_type: telegramInfo.isTelegramUser ? 'telegram_user' : 'web_user',
        link_type: telegramInfo.linkType,
        telegram_id: telegramInfo.telegramId || null,
        username: user.username,
        email: user.email,
        timestamp: new Date().toISOString(),
        source: 'admin_panel'
      }
    }
    
    const { error: logError } = await supabase
      .from('admin_audit_logs')
      .insert(logEntry)
    
    if (logError) {
      console.warn('⚠️ Failed to log admin action:', logError)
    } else {
      console.log('✅ Admin action logged successfully')
    }
    
    return {
      success: true,
      message: 'Password updated successfully',
      userInfo: {
        id: user.id,
        username: user.username,
        email: user.email,
        isTelegramUser: telegramInfo.isTelegramUser,
        telegramId: telegramInfo.telegramId
      }
    }
    
  } catch (error) {
    console.error('❌ Admin password change failed:', error)
    return {
      success: false,
      message: error.message,
      error: error
    }
  }
}

/**
 * Test the improved admin password change function
 */
const testImprovedAdminFunction = async () => {
  try {
    console.log('🧪 Testing improved admin password change function...\n')
    
    // Test with the user that had issues
    const result = await adminChangePassword(
      4, // User ID that the Telegram user is linked to
      'TestPassword123!', // New test password
      '<EMAIL>' // Admin email
    )
    
    if (result.success) {
      console.log('\n🎉 IMPROVED ADMIN FUNCTION TEST PASSED')
      console.log('📋 Result:', JSON.stringify(result, null, 2))
      
      // Verify the password was set correctly
      const bcrypt = await import('bcryptjs')
      const { data: verifyUser } = await supabase
        .from('users')
        .select('password_hash')
        .eq('id', 4)
        .single()
      
      if (verifyUser) {
        const isValid = await bcrypt.default.compare('TestPassword123!', verifyUser.password_hash)
        console.log(`✅ Password verification: ${isValid ? 'SUCCESS' : 'FAILED'}`)
      }
      
    } else {
      console.log('\n❌ IMPROVED ADMIN FUNCTION TEST FAILED')
      console.log('📋 Error:', result.message)
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the test
testImprovedAdminFunction()
