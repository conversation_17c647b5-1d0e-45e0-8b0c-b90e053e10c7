#!/usr/bin/env node

/**
 * CREATE DASHBOARD AND BACKUP SYSTEM TABLES
 * 
 * This script creates all necessary tables for the security dashboard
 * and automated backup system functionality.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createDashboardTables() {
  console.log('🔧 Creating dashboard and backup system tables...');
  
  try {
    console.log('\n📝 MANUAL SETUP INSTRUCTIONS:');
    console.log('=====================================');
    console.log('1. Go to Supabase Dashboard > SQL Editor');
    console.log('2. Copy and paste the following SQL scripts:');
    
    console.log('\n-- SECURITY ALERTS TABLE');
    console.log(`
-- Create security_alerts table for dashboard
CREATE TABLE IF NOT EXISTS public.security_alerts (
    id SERIAL PRIMARY KEY,
    alert_id VARCHAR(100) UNIQUE NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')),
    type VARCHAR(100) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    source VARCHAR(100) NOT NULL,
    metadata JSONB DEFAULT '{}',
    resolved BOOLEAN DEFAULT false,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by VARCHAR(255),
    response_actions TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_security_alerts_severity ON public.security_alerts(severity);
CREATE INDEX IF NOT EXISTS idx_security_alerts_resolved ON public.security_alerts(resolved);
CREATE INDEX IF NOT EXISTS idx_security_alerts_created_at ON public.security_alerts(created_at);
CREATE INDEX IF NOT EXISTS idx_security_alerts_type ON public.security_alerts(type);

-- Enable Row Level Security
ALTER TABLE public.security_alerts ENABLE ROW LEVEL SECURITY;

-- Create RLS policy (admin access only)
CREATE POLICY "security_alerts_admin_policy" ON public.security_alerts
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE user_id = (auth.jwt() ->> 'sub')::integer 
            AND is_active = true
        ) OR
        current_setting('role') = 'service_role'
    );

COMMENT ON TABLE public.security_alerts IS 'Security alerts for dashboard monitoring';
    `);

    console.log('\n-- BLOCKED IPS TABLE');
    console.log(`
-- Create blocked_ips table for IP blocking
CREATE TABLE IF NOT EXISTS public.blocked_ips (
    id SERIAL PRIMARY KEY,
    ip_address INET NOT NULL,
    reason TEXT NOT NULL,
    blocked_by VARCHAR(255),
    blocked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_blocked_ips_ip_address ON public.blocked_ips(ip_address);
CREATE INDEX IF NOT EXISTS idx_blocked_ips_active ON public.blocked_ips(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_blocked_ips_expires_at ON public.blocked_ips(expires_at);

-- Enable Row Level Security
ALTER TABLE public.blocked_ips ENABLE ROW LEVEL SECURITY;

-- Create RLS policy (admin access only)
CREATE POLICY "blocked_ips_admin_policy" ON public.blocked_ips
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE user_id = (auth.jwt() ->> 'sub')::integer 
            AND is_active = true
        ) OR
        current_setting('role') = 'service_role'
    );

COMMENT ON TABLE public.blocked_ips IS 'Blocked IP addresses for security';
    `);

    console.log('\n-- SUSPICIOUS USERS TABLE');
    console.log(`
-- Create suspicious_users table for user flagging
CREATE TABLE IF NOT EXISTS public.suspicious_users (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    reason TEXT NOT NULL,
    flagged_by VARCHAR(255),
    flagged_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraint
    CONSTRAINT fk_suspicious_users_user_id 
        FOREIGN KEY (user_id) 
        REFERENCES public.users(id) 
        ON DELETE CASCADE
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_suspicious_users_user_id ON public.suspicious_users(user_id);
CREATE INDEX IF NOT EXISTS idx_suspicious_users_active ON public.suspicious_users(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_suspicious_users_flagged_at ON public.suspicious_users(flagged_at);

-- Enable Row Level Security
ALTER TABLE public.suspicious_users ENABLE ROW LEVEL SECURITY;

-- Create RLS policy (admin access only)
CREATE POLICY "suspicious_users_admin_policy" ON public.suspicious_users
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE user_id = (auth.jwt() ->> 'sub')::integer 
            AND is_active = true
        ) OR
        current_setting('role') = 'service_role'
    );

COMMENT ON TABLE public.suspicious_users IS 'Flagged suspicious users for monitoring';
    `);

    console.log('\n-- THREAT INTELLIGENCE TABLE');
    console.log(`
-- Create threat_intelligence table for threat data
CREATE TABLE IF NOT EXISTS public.threat_intelligence (
    id SERIAL PRIMARY KEY,
    ip_address INET,
    threat_type VARCHAR(100) NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')),
    source VARCHAR(100) NOT NULL,
    description TEXT,
    first_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_threat_intelligence_ip ON public.threat_intelligence(ip_address);
CREATE INDEX IF NOT EXISTS idx_threat_intelligence_type ON public.threat_intelligence(threat_type);
CREATE INDEX IF NOT EXISTS idx_threat_intelligence_severity ON public.threat_intelligence(severity);
CREATE INDEX IF NOT EXISTS idx_threat_intelligence_active ON public.threat_intelligence(is_active) WHERE is_active = true;

-- Enable Row Level Security
ALTER TABLE public.threat_intelligence ENABLE ROW LEVEL SECURITY;

-- Create RLS policy (service role only)
CREATE POLICY "threat_intelligence_service_policy" ON public.threat_intelligence
    FOR ALL USING (current_setting('role') = 'service_role');

COMMENT ON TABLE public.threat_intelligence IS 'Threat intelligence data for security monitoring';
    `);

    console.log('\n-- BACKUP JOBS TABLE');
    console.log(`
-- Create backup_jobs table for backup tracking
CREATE TABLE IF NOT EXISTS public.backup_jobs (
    id SERIAL PRIMARY KEY,
    job_id VARCHAR(100) UNIQUE NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('database', 'files', 'configuration')),
    schedule VARCHAR(20) NOT NULL CHECK (schedule IN ('daily', 'weekly', 'monthly', 'manual')),
    status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'running', 'completed', 'failed')),
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    size_bytes BIGINT,
    checksum VARCHAR(128),
    location TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_backup_jobs_job_id ON public.backup_jobs(job_id);
CREATE INDEX IF NOT EXISTS idx_backup_jobs_type ON public.backup_jobs(type);
CREATE INDEX IF NOT EXISTS idx_backup_jobs_status ON public.backup_jobs(status);
CREATE INDEX IF NOT EXISTS idx_backup_jobs_start_time ON public.backup_jobs(start_time);

-- Enable Row Level Security
ALTER TABLE public.backup_jobs ENABLE ROW LEVEL SECURITY;

-- Create RLS policy (admin access only)
CREATE POLICY "backup_jobs_admin_policy" ON public.backup_jobs
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE user_id = (auth.jwt() ->> 'sub')::integer 
            AND is_active = true
        ) OR
        current_setting('role') = 'service_role'
    );

COMMENT ON TABLE public.backup_jobs IS 'Backup job tracking and monitoring';
    `);

    console.log('\n-- BACKUP VERIFICATIONS TABLE');
    console.log(`
-- Create backup_verifications table for backup integrity
CREATE TABLE IF NOT EXISTS public.backup_verifications (
    id SERIAL PRIMARY KEY,
    backup_job_id VARCHAR(100) NOT NULL,
    verified BOOLEAN NOT NULL,
    checksum_match BOOLEAN NOT NULL,
    restore_test BOOLEAN DEFAULT false,
    errors TEXT[],
    verified_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraint
    CONSTRAINT fk_backup_verifications_job_id 
        FOREIGN KEY (backup_job_id) 
        REFERENCES public.backup_jobs(job_id) 
        ON DELETE CASCADE
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_backup_verifications_job_id ON public.backup_verifications(backup_job_id);
CREATE INDEX IF NOT EXISTS idx_backup_verifications_verified ON public.backup_verifications(verified);
CREATE INDEX IF NOT EXISTS idx_backup_verifications_verified_at ON public.backup_verifications(verified_at);

-- Enable Row Level Security
ALTER TABLE public.backup_verifications ENABLE ROW LEVEL SECURITY;

-- Create RLS policy (admin access only)
CREATE POLICY "backup_verifications_admin_policy" ON public.backup_verifications
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE user_id = (auth.jwt() ->> 'sub')::integer 
            AND is_active = true
        ) OR
        current_setting('role') = 'service_role'
    );

COMMENT ON TABLE public.backup_verifications IS 'Backup verification results and integrity checks';
    `);

    console.log('\n-- GRANT PERMISSIONS');
    console.log(`
-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON public.security_alerts TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.security_alerts TO service_role;
GRANT USAGE ON SEQUENCE security_alerts_id_seq TO authenticated;
GRANT USAGE ON SEQUENCE security_alerts_id_seq TO service_role;

GRANT SELECT, INSERT, UPDATE ON public.blocked_ips TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.blocked_ips TO service_role;
GRANT USAGE ON SEQUENCE blocked_ips_id_seq TO authenticated;
GRANT USAGE ON SEQUENCE blocked_ips_id_seq TO service_role;

GRANT SELECT, INSERT, UPDATE ON public.suspicious_users TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.suspicious_users TO service_role;
GRANT USAGE ON SEQUENCE suspicious_users_id_seq TO authenticated;
GRANT USAGE ON SEQUENCE suspicious_users_id_seq TO service_role;

GRANT SELECT, INSERT, UPDATE ON public.threat_intelligence TO service_role;
GRANT USAGE ON SEQUENCE threat_intelligence_id_seq TO service_role;

GRANT SELECT, INSERT, UPDATE ON public.backup_jobs TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.backup_jobs TO service_role;
GRANT USAGE ON SEQUENCE backup_jobs_id_seq TO authenticated;
GRANT USAGE ON SEQUENCE backup_jobs_id_seq TO service_role;

GRANT SELECT, INSERT, UPDATE ON public.backup_verifications TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.backup_verifications TO service_role;
GRANT USAGE ON SEQUENCE backup_verifications_id_seq TO authenticated;
GRANT USAGE ON SEQUENCE backup_verifications_id_seq TO service_role;
    `);

    console.log('\n-- LOG TABLE CREATION');
    console.log(`
-- Log table creation
INSERT INTO admin_audit_logs (
    admin_email,
    action,
    target_type,
    target_id,
    metadata,
    created_at
) VALUES (
    'security_system',
    'DASHBOARD_TABLES_CREATED',
    'database_schema',
    'dashboard_backup_tables',
    jsonb_build_object(
        'tables_created', ARRAY[
            'security_alerts',
            'blocked_ips', 
            'suspicious_users',
            'threat_intelligence',
            'backup_jobs',
            'backup_verifications'
        ],
        'creation_date', NOW(),
        'purpose', 'security_dashboard_and_backup_system',
        'bot_safe', true
    ),
    NOW()
);
    `);

    console.log('\n3. Execute all the SQL scripts above');
    console.log('4. Re-run this script to verify table creation');
    console.log('\n⚠️ These tables are required for dashboard and backup functionality');

    return false;

  } catch (error) {
    console.error('❌ Dashboard tables creation failed:', error);
    return false;
  }
}

// Test table access
async function testTableAccess() {
  console.log('\n🧪 Testing table access...');
  
  const tables = [
    'security_alerts',
    'blocked_ips',
    'suspicious_users',
    'threat_intelligence',
    'backup_jobs',
    'backup_verifications'
  ];

  let allTablesExist = true;

  for (const table of tables) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('count')
        .limit(0);

      if (error) {
        console.log(`❌ Cannot access ${table} table`);
        allTablesExist = false;
      } else {
        console.log(`✅ ${table} table accessible`);
      }
    } catch (error) {
      console.log(`❌ Error testing ${table} table:`, error.message);
      allTablesExist = false;
    }
  }

  return allTablesExist;
}

// Run the setup
console.log('🚀 Starting dashboard and backup system setup...\n');

createDashboardTables()
  .then(success => {
    if (success) {
      return testTableAccess();
    } else {
      console.log('\n❌ Table creation required before testing');
      return false;
    }
  })
  .then(testSuccess => {
    if (testSuccess) {
      console.log('\n✅ Dashboard and backup system tables ready!');
      console.log('📋 Features available:');
      console.log('   ✅ Security alerts management');
      console.log('   ✅ IP blocking system');
      console.log('   ✅ Suspicious user flagging');
      console.log('   ✅ Threat intelligence storage');
      console.log('   ✅ Backup job tracking');
      console.log('   ✅ Backup verification system');
      console.log('   ✅ Bot-safe operations maintained');
      process.exit(0);
    } else {
      console.log('\n⚠️ Dashboard and backup system needs manual setup');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  });
