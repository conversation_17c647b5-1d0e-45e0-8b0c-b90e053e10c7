# Current Issues and Status

## Application Status
- ✅ Development server running on port 8000
- ✅ Build system functional
- ✅ Authentication flows working
- ✅ Database integration active

## Recent Work Completed
- Email system implementation with Resend API
- Build scripts and deployment improvements
- Admin impersonation features
- Referral link management
- Landing page optimizations
- KYC system improvements

## Outstanding Tasks
- Complete remaining security tasks from Phase 4-5
- Test all application functionality
- Address any performance issues
- Complete documentation updates

## Next Steps
1. Run comprehensive testing
2. Complete security hardening
3. Performance optimization
4. Documentation review
1180:                  `,children:e.jsx(De,{})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"font-medium text-sm truncate",children:ue.label}),ue.badge!==void 0&&ue.badge>0&&e.jsx("span",{className:"bg-red-500 text-white text-xs px-2 py-1 rounded-full ml-2",children:ue.badge})]}),e.jsx("p",{className:"text-xs text-gray-400 mt-1truncate",children:ue.description})]})]}),ue.highlight&&e.jsx("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-yellow-500 rounded-full animate-pulse"})]},ue.id)})}),e.jsx("div",{style:{padding:"16px",borderTop:"1px solid #374151"},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[e.jsx("div",{style:{width:"32px",height:"32px",background:"linear-gradient(135deg, #60a5fa 0%, #a855f7 100%)",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx("span",{style:{color:"white",fontSize:"14px",fontWeight:"500"},children:(o=(k==null?void 0:k.username)||(k==null?void 0:k.email))==null?void 0:o.charAt(0).toUpperCase()})}),e.jsxs("div",{children:[e.jsx("p",{style:{color:"white",fontSize:"14px",fontWeight:"500",margin:0},children:(k==null?void 0:k.username)||((c=k==null?void 0:k.email)==null?void 0:c.split("@")[0])}),e.jsx("p",{style:{color:"#9ca3af",fontSize:"12px",margin:0},children:"Active"})]})]}),e.jsx("button",{onClick:ge,style:{background:"none",border:"none",color:"#9ca3af",cursor:"pointer",padding:"4px"},title:"Logout",children:e.jsx("svg",{width:"20",height:"20",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})})})]})})]}),e.jsxs("div",{style:{flex:1,padding:"32px"},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"32px"},children:[e.jsxs("div",{children:[e.jsx("h1",{style:{fontSize:"32px",fontWeight:"bold",color:"white",margin:0},children:"Dashboard"}),e.jsxs("p",{style:{color:"#9ca3af",marginTop:"8px",margin:0},children:["Welcome back, ",(k==null?void 0:k.username)||((u=k==null?void 0:k.email)==null?void 0:u.split("@")[0]),"!"]})]}),e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"16px"},children:[e.jsxs("div",{style:{position:"relative"},children:[e.jsx(rN,{userId:((h=k==null?void 0:k.database_user)==null?void 0:h.id)||(k==null?void 0:k.id),onClick:()=>Y(!rt),className:"p-2 bg-gray-800 rounded-lg border border-gray-700 hover:bg-gray-700 transition-colors"}),e.jsx(aN,{userId:((f=k==null?void 0:k.database_user)==null?void 0:f.id)||(k==null?void 0:k.id),isOpen:rt,onClose:()=>Y(!1),onViewAll:()=>{R("notifications"),Y(!1)}})]}),e.jsxs("div",{style:{backgroundColor:"rgba(31, 41, 55, 0.8)",backdropFilter:"blur(10px)",padding:"16px 24px",borderRadius:"16px",border:"1px solid #374151"},children:[e.jsx("span",{style:{color:"#9ca3af",fontSize:"14px",display:"block"},children:"Account Balance"}),e.jsx("p",{style:{color:"white",fontWeight:"bold",fontSize:"18px",margin:0},children:"$"+($e?"...":ve.accountBalance.toFixed(2))})]}),e.jsxs("button",{onClick:ge,style:{background:"linear-gradient(135deg, #dc2626, #b91c1c)",border:"none",color:"white",padding:"12px 20px",borderRadius:"12px",cursor:"pointer",fontSize:"14px",fontWeight:"600",display:"flex",alignItems:"center",gap:"8px",transition:"all 0.3s ease",boxShadow:"0 4px 12px rgba(220, 38, 38, 0.3)"},onMouseEnter:ue=>{ue.currentTarget.style.transform="translateY(-2px)",ue.currentTarget.style.boxShadow="0 6px 16px rgba(220, 38, 38, 0.4)"},onMouseLeave:ue=>{ue.currentTarget.style.transform="translateY(0)",ue.currentTarget.style.boxShadow="0 4px 12px rgba(220, 38, 38, 0.3)"},title:"Sign out of your account",children:[e.jsx("svg",{width:"16",height:"16",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})}),"Logout"]})]})]}),C==="notifications"?e.jsx(tN,{userId:((x=k==null?void 0:k.database_user)==null?void 0:x.id)||(k==null?void 0:k.id),className:"bg-gray-800 rounded-lg border border-gray-700 p-6"}):C==="overview"?e.jsxs(e.Fragment,{children:[e.jsx(Jj,{userId:((b=k==null?void 0:k.database_user)==null?void 0:b.id)||(k==null?void 0:k.id),userEmail:((y=k==null?void 0:k.database_user)==null?void 0:y.email)||(k==null?void 0:k.email),onConnectionSuccess:()=>{fe()},className:"mb-6"}),e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"20px",marginBottom:"30px"},children:[e.jsxs("div",{style:{backgroundColor:"rgba(31, 41, 55, 0.9)",borderRadius:"12px",padding:"20px",textAlign:"center",border:"1px solid #374151"},children:[e.jsx("p",{style:{fontSize:"32px",fontWeight:"bold",color:"#3b82f6",margin:"0 0 5px 0"},children:$e?"...":ve.totalShares.toLocaleString()}),e.jsx("p",{style:{fontSize:"14px",color:"#9ca3af",margin:0},children:"Gold Shares Owned"})]}),e.jsxs("div",{style:{backgroundColor:"rgba(31, 41, 55, 0.9)",borderRadius:"12px",padding:"20px",textAlign:"center",border:"1px solid #374151"},children:[e.jsx("p",{style:{fontSize:"32px",fontWeight:"bold",color:"#10b981",margin:"0 0 5px 0"},children:"$"+($e?"...":ve.shareValue.toLocaleString())}),e.jsx("p",{style:{fontSize:"14px",color:"#9ca3af",margin:0},children:"Share Value"})]}),e.jsxs("div",{style:{backgroundColor:"rgba(31, 41, 55, 0.9)",borderRadius:"12px",padding:"20px",textAlign:"center",border:"1px solid #374151"},children:[e.jsx("p",{style:{fontSize:"32px",fontWeight:"bold",color:"#f59e0b",margin:"0 0 5px 0"},children:"$"+($e?"...":ve.futureDividends.toLocaleString())}),e.jsx("p",{style:{fontSize:"14px",color:"#9ca3af",margin:0},children:"Future Dividends"})]}),e.jsxs("div",{style:{backgroundColor:"rgba(31, 41, 55, 0.9)",borderRadius:"12px",padding:"20px",textAlign:"center",border:"1px solid #374151"},children:[e.jsx("p",{style:{fontSize:"32px",fontWeight:"bold",color:"#10b981",margin:"0 0 5px 0"},children:"$"+($e?"...":ve.usdtCommissions.totalEarned.toFixed(2))}),e.jsx("p",{style:{fontSize:"14px",color:"#9ca3af",margin:0},children:"USDT Commissions"})]}),e.jsxs("div",{style:{backgroundColor:"rgba(31, 41, 55, 0.9)",borderRadius:"12px",padding:"20px",textAlign:"center",border:"1px solid #374151"},children:[e.jsx("p",{style:{fontSize:"32px",fontWeight:"bold",color:"#a855f7",margin:"0 0 5px 0"},children:$e?"...":ve.shareCommissions.totalShares.toLocaleString()}),e.jsx("p",{style:{fontSize:"14px",color:"#9ca3af",margin:0},children:"Share Commissions"})]})]}),e.jsx(LN,{userShares:ve.totalShares}),e.jsxs("div",{style:{backgroundColor:"rgba(31, 41, 55, 0.9)",borderRadius:"12px",padding:"24px",marginBottom:"30px",border:"1px solid #374151"},children:[e.jsx("h3",{style:{color:"white",fontSize:"20px",fontWeight:"bold",margin:"0 0 20px 0",display:"flex",alignItems:"center",gap:"8px"},children:"💰 COMMISSION BALANCE"}),e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(300px, 1fr))",gap:"24px"},children:[e.jsxs("div",{style:{backgroundColor:"rgba(16, 185, 129, 0.1)",borderRadius:"8px",padding:"16px",border:"1px solid rgba(16, 185, 129, 0.3)"},children:[e.jsx("h4",{style:{color:"#10b981",fontSize:"16px",fontWeight:"600",margin:"0 0 12px 0"},children:"💵 USDT COMMISSIONS:"}),e.jsxs("div",{style:{color:"#d1d5db",fontSize:"14px",lineHeight:"1.6"},children:[e.jsxs("div",{style:{marginBottom:"4px"},children:["• ",e.jsx("strong",{children:"Total Earned:"})," ","$"+($e?"...":ve.usdtCommissions.totalEarned.toFixed(2))," USDT"]}),e.jsxs("div",{style:{marginBottom:"4px"},children:["• ",e.jsx("strong",{children:"Available for Withdrawal:"})," ","$"+($e?"...":ve.usdtCommissions.available.toFixed(2))," USDT"]}),e.jsxs("div",{children:["• ",e.jsx("strong",{children:"Currently Escrowed:"})," ","$"+($e?"...":ve.usdtCommissions.escrowed.toFixed(2))," USDT"]})]})]}),e.jsxs("div",{style:{backgroundColor:"rgba(168, 85, 247, 0.1)",borderRadius:"8px",padding:"16px",border:"1px solid rgba(168, 85, 247, 0.3)"},children:[e.jsx("h4",{style:{color:"#a855f7",fontSize:"16px",fontWeight:"600",margin:"0 0 12px 0"},children:"📈SHARE COMMISSIONS:"}),e.jsxs("div",{style:{color:"#d1d5db",fontSize:"14px",lineHeight:"1.6"},children:[e.jsxs("div",{style:{marginBottom:"4px"},children:["• ",e.jsx("strong",{children:"Total Shares Earned:"})," ",$e?"...":ve.shareCommissions.totalShares.toLocaleString()," shares"]}),e.jsxs("div",{style:{marginBottom:"4px"},children:["• ",e.jsx("strong",{children:"Current Value:"})," ","$"+($e?"...":ve.shareCommissions.currentValue.toFixed(2))," USD"]}),e.jsxs("div",{children:["• ",e.jsx("strong",{children:"Status:"})," Active in portfolio"]})]})]})]}),e.jsxs("div",{style:{marginTop:"20px",padding:"16px",backgroundColor:"rgba(55, 65, 81, 0.5)",borderRadius:"8px",border:"1px solid #4b5563"},children:[e.jsx("h4",{style:{color:"#f3f4f6",fontSize:"16px",fontWeight:"600",margin:"0 0 12px 0"},children:"📊 COMMISSION SUMMARY:"}),e.jsxs("div",{style:{color:"#d1d5db",fontSize:"14px",lineHeight:"1.6"},children:[e.jsxs("div",{style:{marginBottom:"4px"},children:["• ",e.jsx("strong",{children:"Total Commission Value:"})," ","$"+($e?"...":(ve.usdtCommissions.totalEarned+ve.shareCommissions.currentValue).toFixed(2))]}),e.jsxs("div",{children:["• ",e.jsx("strong",{children:"Commission Rate:"})," 15% USDT + 15% Shares"]})]})]})]}),e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(250px, 1fr))",gap:"20px",marginBottom:"30px"},children:[e.jsxs("button",{onClick:()=>T(!0),style:{background:"linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",borderRadius:"12px",padding:"30px",textAlign:"center",border:"none",cursor:"pointer",color:"white"},children:[e.jsx("h2",{style:{fontSize:"20px",fontWeight:"bold",margin:"0 0 8px 0"},children:"Buy Gold Shares"}),e.jsx("p",{style:{fontSize:"14px",opacity:.9,margin:0},children:"Purchase shares in our gold mining operation"})]}),e.jsxs("button",{onClick:()=>H(!0),style:{backgroundColor:"rgba(31, 41, 55, 0.9)",borderRadius:"12px",padding:"30px",textAlign:"center",border:"2px solid #374151",cursor:"pointer",color:"white"},children:[e.jsx("h2",{style:{fontSize:"20px",fontWeight:"bold",margin:"0 0 8px 0"},children:"View My Shares"}),e.jsx("p",{style:{fontSize:"14px",color:"#9ca3af",margin:0},children:"Check your current share holdings"})]})]}),e.jsx("div",{"data-section":"marketing",children:e.jsx(eN,{user:k,getReferralUsername:me})}),e.jsx("div",{style:{backgroundColor:"rgba(31, 41, 55, 0.9)",borderRadius:"12px",padding:"20px",border:"1px solid #374151"},children:e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"20px",alignItems:"center"},children:[e.jsxs("div",{children:[e.jsx("p",{style:{fontSize:"14px",color:"#9ca3af",margin:"0 0 4px 0"},children:"Account"}),e.jsx("p",{style:{color:"white",fontWeight:"500",margin:0},children:k==null?void 0:k.email})]}),e.jsxs("div",{children:[e.jsx("p",{style:{fontSize:"14px",color:"#9ca3af",margin:"0 0 4px 0"},children:"Status"}),e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[e.jsx("span",{style:{width:"8px",height:"8px",backgroundColor:"#22c55e",borderRadius:"50%"}}),e.jsx("p",{style:{color:"#22c55e",fontWeight:"bold",margin:0},children:"Active"})]})]}),e.jsxs("div",{children:[e.jsx("p",{style:{fontSize:"14px",color:"#9ca3af",margin:"0 0 4px 0"},children:"Dividends Start"}),e.jsx("p",{style:{color:"#f59e0b",fontWeight:"bold",margin:0},children:"March 2026"})]}),e.jsxs("div",{children:[e.jsx("p",{style:{fontSize:"14px",color:"#9ca3af",margin:"0 0 4px 0"},children:"Member Since"}),e.jsx("p",{style:{color:"white",fontWeight:"500",margin:0},children:k!=null&&k.created_at?new Date(k.created_at).toLocaleDateString():"Today"})]})]})}),e.jsxs("div",{style:{backgroundColor:"rgba(31, 41, 55, 0.9)",borderRadius:"12px",padding:"24px",marginTop:"20px",border:"1px solid #374151"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[e.jsx("div",{style:{width:"32px",height:"32px",backgroundColor:"#0088cc",borderRadius:"8px",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"16px"},children:"📱"}),e.jsx("h3",{style:{color:"white",fontSize:"18px",fontWeight:"bold",margin:0},children:"Telegram Integration"})]})}),q?e.jsxs("div",{children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"12px"},children:[e.jsx("span",{style:{width:"8px",height:"8px",backgroundColor:"#22c55e",borderRadius:"50%"}}),e.jsx("span",{style:{color:"#22c55e",fontWeight:"bold",fontSize:"14px"},children:"Connected to Telegram"})]}),e.jsxs("div",{style:{marginBottom:"16px"},children:[e.jsx("p",{style:{fontSize:"14px",color:"#9ca3af",margin:"0 0 4px 0"},children:"Connected Account"}),e.jsxs("p",{style:{color:"white",fontWeight:"500",margin:0},children:[q.first_name," ",q.last_name,q.username&&` (@${q.username})`]}),e.jsxs("p",{style:{fontSize:"12px",color:"#6b7280",margin:"2px 0 0 0"},children:["ID: ",q.telegram_id]})]}),e.jsx("button",{onClick:Ie,style:{padding:"8px 16px",backgroundColor:"transparent",border:"1px solid #ef4444",borderRadius:"6px",color:"#ef4444",fontSize:"12px",fontWeight:"500",cursor:"pointer",transition:"all 0.2s"},onMouseOver:ue=>{ue.currentTarget.style.backgroundColor="rgba(239, 68, 68, 0.1)"},onMouseOut:ue=>{ue.currentTarget.style.backgroundColor="transparent"},children:"Disconnect"})]}):e.jsxs("div",{children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"12px"},children:[e.jsx("span",{style:{width:"8px",height:"8px",backgroundColor:"#6b7280",borderRadius:"50%"}}),e.jsx("span",{style:{color:"#6b7280",fontSize:"14px"},children:"Not connected to Telegram"})]}),e.jsx("p",{style:{fontSize:"14px",color:"#9ca3af",marginBottom:"16px",lineHeight:"1.5"},children:"Connectyour Telegram account to sync your existing bot data and enable cross-platform features."}),e.jsx("button",{onClick:()=>z(!0),style:{padding:"12px 20px",backgroundColor:"#0088cc",border:"none",borderRadius:"8px",color:"white",fontSize:"14px",fontWeight:"500",cursor:"pointer",transition:"all 0.2s",display:"flex",alignItems:"center",gap:"8px"},onMouseOver:ue=>{ue.currentTarget.style.backgroundColor="#0077b3"},onMouseOut:ue=>{ue.currentTarget.style.backgroundColor="#0088cc"},children:"📱 Connect to Telegram"})]})]})]}):C==="referrals"?e.jsx(iN,{userId:((g=k==null?void 0:k.database_user)==null?void 0:g.id)||(k==null?void 0:k.id)||0,className:"bg-gray-800 rounded-lg border border-gray-700 p-6"}):C==="dividends"?e.jsx(lN,{userShares:ve.totalShares,currentPhase:null}):C==="portfolio"?e.jsx(pN,{user:k,currentPhase:{price_per_share:ve.currentSharePrice}}):C==="shares"?e.jsxs("div",{className:"bg-gray-800 rounded-lg border border-gray-700 p-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:"📊 My Shares"}),e.jsx("p",{className:"text-gray-400 mb-4",children:"Shares section temporarily unavailable."}),e.jsx("button",{onClick:()=>T(!0),className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors",children:"Purchase Shares"})]}):C==="purchase-shares"?e.jsxs("div",{className:"bg-gray-800 rounded-lg border border-gray-700 p-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-white mb-4",children:"🛒 Purchase Gold Shares"}),e.jsx("p",{className:"text-gray-400 mb-6",children:"Invest in Aureus Alliance Holdings gold mining shares. Choose your investment amount and payment method."}),e.jsx("button",{onClick:()=>T(!0),className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors",children:"Start Purchase Process"})]}):C==="company-presentation"?e.jsxs("div",{className:"bg-gray-800 rounded-lg border border-gray-700p-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-white mb-4",children:"📋 Company Presentation"}),e.jsx("p",{className:"text-gray-400 mb-6",children:"Learn about Aureus Alliance Holdings, our mining operations, and investment opportunities."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"🏆 About Us"}),e.jsx("p",{className:"text-gray-300 text-sm",children:"Premium gold mining share ownership opportunity"})]}),e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"📊 Investment Details"}),e.jsx("p",{className:"text-gray-300 text-sm",children:"Comprehensive investment information and projections"})]})]})]}):C==="mining-operations"?e.jsxs("div",{className:"bg-gray-800 rounded-lg border border-gray-700 p-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-white mb-4",children:"⛏️ Mining Operations"}),e.jsx("p",{className:"text-gray-400 mb-6",children:"View our mining operations, progress updates, and operational metrics."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4 text-center",children:[e.jsx("div",{className:"text-2xl mb-2",children:"🏗️"}),e.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"Excavation"}),e.jsx("p",{className:"text-gray-300 text-sm",children:"Current excavation progress"})]}),e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4 text-center",children:[e.jsx("div",{className:"text-2xl mb-2",children:"🔬"}),e.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"Geology"}),e.jsx("p",{className:"text-gray-300 text-sm",children:"Geological assessments"})]}),e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4 text-center",children:[e.jsx("div",{className:"text-2xl mb-2",children:"📈"}),e.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"Production"}),e.jsx("p",{className:"text-gray-300 text-sm",children:"Production metrics"})]})]})]}):C==="community-relations"?e.jsxs("div",{className:"bg-gray-800 rounded-lg border border-gray-700 p-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-white mb-4",children:"🏘️ Community Relations"}),e.jsx("p",{className:"text-gray-400 mb-6",children:"Our commitment to community development and stakeholderengagement."}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"🤝 Community Meetings"}),e.jsx("p",{className:"text-gray-300 text-sm",children:"Regular engagement with local communities"})]}),e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"🏗️ Development Plans"}),e.jsx("p",{className:"text-gray-300 text-sm",children:"Infrastructure and community development initiatives"})]})]})]}):C==="support-center"?e.jsxs("div",{className:"bg-gray-800 rounded-lg border border-gray-700 p-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-white mb-4",children:"🆘 Support Center"}),e.jsx("p",{className:"text-gray-400 mb-6",children:"Get help with your account, investments, and platform usage."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"📧 Contact Support"}),e.jsx("p",{className:"text-gray-300 text-sm mb-3",children:"Email: <EMAIL>"}),e.jsx("button",{className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm transition-colors",children:"Send Email"})]}),e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"❓FAQ"}),e.jsx("p",{className:"text-gray-300 text-sm mb-3",children:"Frequently asked questions"}),e.jsx("button",{className:"bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded text-sm transition-colors",children:"View FAQ"})]})]})]}):C==="settings"?e.jsxs("div",{className:"bg-gray-800 rounded-lg border border-gray-700 p-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-white mb-4",children:"⚙️ Settings"}),e.jsx("p",{className:"text-gray-400 mb-6",children:"Manage your account preferences and security settings."}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"👤 Profile Settings"}),e.jsx("p",{className:"text-gray-300 text-sm",children:"Update your personal information"})]}),e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"🔒 Security"}),e.jsx("p",{className:"text-gray-300 text-sm",children:"Password and security preferences"})]}),e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"🔔 Notifications"}),e.jsx("p",{className:"text-gray-300 text-sm",children:"Notification preferences"})]})]})]}):C==="kyc"?e.jsxs("div",{className:"bg-gray-800 rounded-lg border border-gray-700 p-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-white mb-4",children:"🔒 KYC Verification"}),e.jsx("p",{className:"text-gray-400 mb-6",children:"Complete your identity verification to unlock all platformfeatures."}),e.jsxs("div",{className:"bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-4 mb-4",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("span",{className:"text-yellow-500",children:"⚠️"}),e.jsx("span",{className:"text-yellow-400 font-medium",children:"Verification Required"})]}),e.jsx("p",{className:"text-yellow-300 text-sm",children:"Please complete KYC verification to access allfeatures and increase your transaction limits."})]}),e.jsx("button",{className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors",children:"Start KYC Process"})]}):C==="legal-documents"?e.jsxs("div",{className:"bg-gray-800 rounded-lg border border-gray-700 p-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-white mb-4",children:"📋 Legal Documents"}),e.jsx("p",{className:"text-gray-400 mb-6",children:"Access important legal documents and agreements."}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between bg-gray-700 rounded-lg p-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-white font-medium",children:"Terms & Conditions"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Platform usage terms"})]}),e.jsx("button",{className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm transition-colors",children:"View"})]}),e.jsxs("div",{className:"flex items-center justify-between bg-gray-700 rounded-lg p-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-white font-medium",children:"Privacy Policy"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Data protection policy"})]}),e.jsx("button",{className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm transition-colors",children:"View"})]}),e.jsxs("div",{className:"flex items-center justify-between bg-gray-700 rounded-lg p-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-white font-medium",children:"Investment Agreement"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Share purchase agreement"})]}),e.jsx("button",{className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm transition-colors",children:"View"})]})]})]}):e.jsxs("div",{className:"bg-gray-800 rounded-lg border border-gray-700 p-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-white mb-4",children:C.charAt(0).toUpperCase()+C.slice(1).replace("-"," ")}),e.jsx("p",{className:"text-gray-400",children:"This section is coming soon. Please check back later."})]})]})]}),O&&e.jsx(Vj,{user:k,onClose:()=>T(!1)}),e.jsx(Yj,{isOpen:P,onClose:()=>z(!1),onConnect:Ae,loading:_}),null]})},IN=({className:l="",onNavigateToRegister:r})=>{const[t,s]=K.useState([]),[n,o]=K.useState(null),[c,u]=K.useState(0),[h,f]=K.useState(!0),[x,b]=K.useState(null);K.useEffect(()=>{y()},[]);const y=async()=>{try{f(!0),console.log("🔄 Fetching phases from investment_phases table..."),console.log("🔍 Testing database connection...");let R;try{R=br(),console.log("✅ Using service role client for investment_phases access")}catch(P){console.error("❌ Service role client not available:",P),b("Database access configuration error. Service role client required."),s([]),o(null),u(0),f(!1);return}const{data:O,error:T}=await R.from("investment_phases").select("*").order("phase_number");if(console.log("📡 Raw Supabase response:",{data:O,error:T}),console.log("📊 Response data type:",typeof O),console.log("📊 Response data length:",O==null?void 0:O.length),T)console.error("❌ Phases fetch error:",T),console.error("❌ Error details:",{message:T.message,details:T.details,hint:T.hint,code:T.code}),b(`Database error: ${T.message} (Code: ${T.code})`),s([]),o(null),u(0);else if(!O||O.length===0){console.error("❌ No phases data returned from database"),console.error("❌ Checking if table exists and has data...");try{const{count:P,error:z}=await R.from("investment_phases").select("*",{count:"exact",head:!0});z?(console.error("❌ Table access error:",z),b(`Table access error: ${z.message}`)):(console.log("📊 Table row count:",P),b(`No phases found in database (Table has ${P} rows)`))}catch(P){console.error("❌ Count query failed:",P),b("Database table verification failed")}s([]),o(null),u(0)}else{const{data:P,error:z}=await R.from("aureus_share_purchases").select("package_name, shares_purchased, status").in("status",["active","approved","pending_approval"]);let $={};!z&&P&&P.forEach(D=>{const _=D.package_name,M=D.shares_purchased||0;let N=null;if(_==="Pre Sale Purchase"||_==="Pre Sale Package"||_==="Commission Conversion - Phase 0")N="Pre Sale";else if(_.includes("Phase1"))N="Phase 1";else if(_.includes("Phase 2"))N="Phase 2";else if(_.includes("Phase 3"))N="Phase 3";else if(_.includes("Phase 4"))N="Phase 4";else if(_.includes("Phase 5"))N="Phase 5";else if(_.includes("Phase 6"))N="Phase 6";else if(_.includes("Phase 7"))N="Phase 7";else if(_.includes("Phase 8"))N="Phase 8";else if(_.includes("Phase 9"))N="Phase 9";else if(_.includes("Phase 10"))N="Phase 10";else if(_.includes("Phase 11"))N="Phase 11";else if(_.includes("Phase 12"))N="Phase 12";else if(_.includes("Phase 13"))N="Phase 13";else if(_.includes("Phase 14"))N="Phase 14";else if(_.includes("Phase 15"))N="Phase 15";else if(_.includes("Phase 16"))N="Phase 16";else if(_.includes("Phase 17"))N="Phase 17";else if(_.includes("Phase 18"))N="Phase 18";else if(_.includes("Phase 19"))N="Phase 19";else if(_.includes("Share Transfer")||_==="Commission Conversion")return;N&&($[N]||($[N]=0),$[N]+=M)});const H=O.map(D=>{const _=$[D.phase_name]||0,M=D.shares_sold||0;return{...D,price_per_share:typeof D.price_per_share=="string"?parseFloat(D.price_per_share):D.price_per_share,total_shares_available:typeof D.total_shares_available=="string"?parseInt(D.total_shares_available):D.total_shares_available,shares_sold:M,calculated_shares_sold:_,is_active:!!D.is_active}});console.log("🔗 Package name mapping results:",$),console.log("✅ LIVE DATA from investment_phases:",H),console.log("📊 Total phases loaded:",H.length),console.log("🔍 Presale phase data:",H.find(D=>D.phase_number===0)),console.log("🔍 All shares_sold values:",H.map(D=>({phase:D.phase_number,sold:D.shares_sold,available:D.total_shares_available}))),s(H);const q=H.find(D=>D.is_active)||H[0]||null;o(q),u((q==null?void 0:q.phase_number)||0),console.log("🎯 Active phase set to:",q)}}catch(R){console.error("❌ Database connection error:",R),b(`Connection error: ${R}`),s([]),o(null),u(0)}finally{f(!1)}},g=R=>R===0?{usdt:"15%",shares:"15%",description:"USDT Commission + Bonus NFT Shares",highlight:!0,exclusiveText:"PRESALE EXCLUSIVE!"}:{usdt:"15%",shares:"0%",description:"USDT Commission Only",highlight:!1,exclusiveText:null},k=R=>{const O=R.shares_sold/R.total_shares_available*100;return R.is_active?{status:"Active",color:"#10b981",bgColor:"rgba(16, 185, 129, 0.1)"}:O>=100?{status:"Sold Out",color:"#ef4444",bgColor:"rgba(239, 68, 68, 0.1)"}:R.phase_number<((n==null?void 0:n.phase_number)||0)?{status:"Completed",color:"#6b7280",bgColor:"rgba(107, 114, 128, 0.1)"}:{status:"Upcoming",color:"#f59e0b",bgColor:"rgba(245, 158, 11, 0.1)"}},S=()=>{const R=document.getElementById("auth");R?R.scrollIntoView({behavior:"smooth"}):window.open("https://t.me/AureusAllianceBot","_blank")};if(h)return e.jsx("div",{className:`comprehensive-phase-display ${l}`,style:{minHeight:"400px"},children:e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gold mx-auto mb-4"}),e.jsx("p",{className:"text-gray-400",children:"Loading phase information..."})]})});if(x)return e.jsx("div",{className:`comprehensive-phase-display ${l}`,style:{minHeight:"400px"},children:e.jsxs("div",{className:"text-center py-8",children:[e.jsxs("p",{className:"text-red-400 mb-4",children:["⚠️ ",x]}),e.jsx("button",{onClick:y,className:"btn-secondary",children:"Retry"})]})});if(!t||t.length===0)return e.jsx("div",{className:`comprehensive-phase-display ${l}`,style:{minHeight:"400px"},children:e.jsxs("div",{className:"text-center py-8",children:[e.jsx("p",{className:"text-gray-400 mb-4",children:"No phase information available"}),e.jsx("button",{onClick:y,className:"btn-secondary",children:"Reload"})]})});const A=t.find(R=>R.phase_number===c)||t[0],w=A?g(A.phase_number):null,C=A?k(A):null;return e.jsxs("div",{className:`comprehensive-phase-display ${l}`,children:[e.jsxs("div",{className:"phase-tabs-container",children:[e.jsxs("div",{className:"phase-tabs-header",children:[e.jsx("h3",{className:"heading-md text-gold mb-sm",children:"20-Phase Share Offering"}),e.jsx("p",{className:"text-sm text-gray-400 mb-md",children:"Select a phase to view pricing and commission structure"})]}),e.jsx("div",{className:"phase-tabs-wrapper",children:e.jsx("div",{className:"phase-tabs",children:t.map(R=>{const O=R.phase_number===c,T=k(R);return e.jsxs("button",{onClick:()=>u(R.phase_number),className:`phase-tab ${O?"active":""} ${R.phase_number===0?"presale":""}`,style:{borderColor:O?T.color:"rgba(75, 85, 99, 0.3)",backgroundColor:O?T.bgColor:"rgba(31, 41, 55, 0.5)"},children:[e.jsx("div",{className:"phase-tab-number",children:R.phase_number===0?"PRE":R.phase_number}),e.jsxs("div",{className:"phase-tab-price",children:["$",R.price_per_share.toFixed(2)]}),e.jsx("div",{className:"phase-tab-status",style:{color:T.color},children:T.status})]},R.id)})})})]}),A&&w&&C&&e.jsxs("div",{className:"phase-details-card",children:[e.jsxs("div",{className:"phase-details-header",children:[e.jsxs("div",{className:"phase-title-section",children:[e.jsxs("h4",{className:"phase-title",children:[A.phase_name,A.phase_number===0&&e.jsx("span",{className:"presale-badge",children:"PRESALE"})]}),e.jsx("div",{className:"phase-status-badge",style:{backgroundColor:C.bgColor,color:C.color},children:C.status})]}),e.jsxs("div",{className:"phase-pricing",children:[e.jsxs("span",{className:"price-value",children:["$",A.price_per_share.toFixed(2)]}),e.jsx("span",{className:"price-label",children:"per share"})]})]}),e.jsxs("div",{className:"phase-details-content",children:[e.jsxs("div",{className:"phase-availability",children:[e.jsxs("div",{className:"availability-stats",children:[e.jsxs("div",{className:"stat",children:[e.jsx("span",{className:"stat-value",children:A.total_shares_available.toLocaleString()}),e.jsx("span",{className:"stat-label",children:"Total Shares"})]}),e.jsxs("div",{className:"stat",children:[e.jsx("span",{className:"stat-value",children:A.shares_sold.toLocaleString("en-US",{minimumFractionDigits:0,maximumFractionDigits:2})}),e.jsx("span",{className:"stat-label",children:"Sold"})]}),e.jsxs("div",{className:"stat",children:[e.jsx("span",{className:"stat-value",children:(A.total_shares_available-A.shares_sold).toLocaleString("en-US",{minimumFractionDigits:0,maximumFractionDigits:2})}),e.jsx("span",{className:"stat-label",children:"Available"})]})]}),e.jsx("div",{className:"progress-bar",children:e.jsx("div",{className:"progress-fill",style:{width:`${A.shares_sold/A.total_shares_available*100}%`}})})]}),e.jsxs("div",{className:"phase-capital-info",children:[e.jsx("h5",{className:"capital-title",children:"Capital Information"}),e.jsxs("div",{className:"capital-stats",children:[e.jsxs("div",{className:"capital-stat",children:[e.jsx("span",{className:"capital-label",children:"Total Potential Capital"}),e.jsxs("span",{className:"capital-value",children:["$",(A.total_shares_available*A.price_per_share).toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})]})]}),e.jsxs("div",{className:"capital-stat",children:[e.jsx("span",{className:"capital-label",children:"Capital Raised"}),e.jsxs("span",{className:"capital-value raised",children:["$",(A.shares_sold*A.price_per_share).toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})]})]}),e.jsxs("div",{className:"capital-stat",children:[e.jsx("span",{className:"capital-label",children:"Remaining Capital"}),e.jsxs("span",{className:"capital-value remaining",children:["$",((A.total_shares_available-A.shares_sold)*A.price_per_share).toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})]})]})]})]}),e.jsxs("div",{className:`commission-structure ${w.highlight?"highlighted":""}`,children:[w.exclusiveText&&e.jsxs("div",{className:"exclusive-banner",children:["⭐ ",w.exclusiveText]}),e.jsx("h5",{className:"commission-title",children:"Commission Structure"}),e.jsxs("div",{className:"commission-details",children:[e.jsxs("div",{className:"commission-item",children:[e.jsx("span",{className:"commission-icon",children:"💰"}),e.jsx("span",{className:"commission-label",children:"USDT Commission"}),e.jsx("span",{className:"commission-value",children:w.usdt})]}),e.jsxs("div",{className:"commission-item",children:[e.jsx("span",{className:"commission-icon",children:"🎁"}),e.jsx("span",{className:"commission-label",children:"NFT Share Bonus"}),e.jsx("span",{className:`commission-value ${w.shares==="0%"?"disabled":""}`,children:w.shares})]})]}),e.jsx("p",{className:"commission-description",children:w.description})]}),e.jsxs("div",{className:"purchase-section",children:[e.jsx("button",{onClick:S,className:`purchase-btn ${A.phase_number===0?"presale":"regular"}`,disabled:C.status==="Sold Out",children:C.status==="Sold Out"?"🔒 Sold Out":C.status==="Upcoming"?"⏳ Coming Soon":`🚀 Purchase ${A.phase_name} Shares`}),A.phase_number===0&&e.jsx("p",{className:"purchase-note",children:"⚡ Limited time: Get bonus NFT shares with every purchase!"})]})]})]}),e.jsxs("div",{className:"commission-overview-compact commission-v2-fixed",children:[e.jsxs("div",{className:"commission-compact-header",children:[e.jsxs("div",{className:"commission-compact-title",children:[e.jsx("span",{className:"commission-icon-small",children:"💰"}),e.jsx("h4",{children:"Commission Structure"})]}),e.jsx("p",{className:"commission-compact-subtitle",children:"Earn rewards with every referral"})]}),e.jsxs("div",{className:"commission-compact-grid",children:[e.jsxs("div",{className:"commission-compact-card current-phase",children:[e.jsx("div",{className:"commission-compact-badge presale",children:"⭐ PRESALE EXCLUSIVE"}),e.jsxs("div",{className:"commission-compact-details",children:[e.jsxs("div",{className:"commission-compact-item",children:[e.jsx("span",{className:"commission-compact-label",children:"USDT Commission"}),e.jsx("span",{className:"commission-compact-value primary",children:"15%"})]}),e.jsxs("div",{className:"commission-compact-item",children:[e.jsx("span",{className:"commission-compact-label",children:"Bonus NFT Shares"}),e.jsx("span",{className:"commission-compact-value secondary",children:"15%"})]})]}),e.jsx("div",{className:"commission-compact-highlight",children:"⚡ Limited time: Double rewards!"})]}),e.jsxs("div",{className:"commission-compact-card future-phases",children:[e.jsx("div",{className:"commission-compact-badge standard",children:"💼 PHASES 1-19"}),e.jsxs("div",{className:"commission-compact-details",children:[e.jsxs("div",{className:"commission-compact-item",children:[e.jsx("span",{className:"commission-compact-label",children:"USDT Commission"}),e.jsx("span",{className:"commission-compact-value primary",children:"15%"})]}),e.jsxs("div",{className:"commission-compact-item disabled",children:[e.jsx("span",{className:"commission-compact-label",children:"Bonus NFT Shares"}),e.jsx("span",{className:"commission-compact-value disabled",children:"0%"})]})]}),e.jsx("div",{className:"commission-compact-note",children:"USDT rewards continue"})]})]}),e.jsxs("div",{className:"commission-cta-section",children:[e.jsxs("div",{className:"commission-cta-content",children:[e.jsx("h5",{className:"commission-cta-title",children:"Ready to Start Earning?"}),e.jsx("p",{className:"commission-cta-subtitle",children:"Join our referral program and earn commissions on every successful referral"})]}),e.jsxs("div",{className:"commission-cta-buttons",children:[e.jsxs("button",{className:"commission-cta-btn primary",onClick:()=>{r?r():window.location.href="/register"},children:[e.jsx("span",{className:"cta-icon",children:"🚀"}),"Start Referring"]}),e.jsxs("button",{className:"commission-cta-btn secondary",onClick:()=>{r?r():window.location.href="/register"},children:[e.jsx("span",{className:"cta-icon",children:"📋"}),"Learn More"]})]})]})]})]})},DN=({currentPhase:l})=>{const[r,t]=K.useState(null),[s,n]=K.useState([]),[o,c]=K.useState([]),[u,h]=K.useState({totalParticipants:0,qualifiedParticipants:0,leadingVolume:0,totalPrizePool:0,minimumQualification:2500}),[f,x]=K.useState(!0),[b,y]=K.useState(null),[g,k]=K.useState([]),[S,A]=K.useState(null),w=_=>{var M;const N=((M=_==null?void 0:_.investment_phases)==null?void 0:M.phase_number)??(_==null?void 0:_.phase_id);return typeof N=="number"?N:Number(N)||0},C=_=>{var M;const N=(((M=_==null?void 0:_.investment_phases)==null?void 0:M.phase_name)||(_==null?void 0:_.name)||"").toString().toLowerCase(),J=w(_);returnN.includes("presale")||J===0},R=_=>C(_)?"Presale":`Phase ${w(_)}`,O=_=>(_||[]).slice().sort((M,N)=>{const J=C(M),U=C(N);return J&&!U?-1:!J&&U?1:w(M)-w(N)}),T=_=>{const M=_==null?void 0:_.investment_phases;return M?M.is_active?"active":M.shares_sold/M.total_shares_available*100>=100?"ended":(_==null?void 0:_.status)||"upcoming":(_==null?void 0:_.status)||"upcoming"};K.useEffect(()=>{P()},[]),K.useEffect(()=>{if(S){console.log("🔄Loading competition data for:",S),z(S);const _=setInterval(()=>{console.log("🔄 Auto-refreshing competition data for:",S),z(S)},3e4);return()=>clearInterval(_)}},[S]);const P=async()=>{try{console.log("🔍 Loading available competitions...");const _=await ln.getAllCompetitions();console.log("📋 Available competitions (raw):",_);const M=_.length>0?_:[{id:"fallback-presale",name:"Gold Diggers Club - Presale",description:"Build your network by referring new investors. Each qualified referral counts toward your ranking.",status:"active",total_prize_pool:15e4,minimum_qualification_amount:2500,phase_id:0,is_active:!0,start_date:new Date().toISOString(),total_participants:0,qualified_participants:0,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{id:"fallback-phase-1",name:"Gold Diggers Club - Phase 1",description:"First phase competition - Build your network by referring new investors.",status:"ended",total_prize_pool:12e4,minimum_qualification_amount:2e3,phase_id:1,is_active:!1,start_date:new Date(Date.now()-720*60*60*1e3).toISOString(),end_date:new Date(Date.now()-1440*60*1e3).toISOString(),total_participants:0,qualified_participants:0,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{id:"fallback-phase-2",name:"Gold Diggers Club - Phase 2",description:"Upcoming phase competition - Get ready for the next challenge!",status:"upcoming",total_prize_pool:2e5,minimum_qualification_amount:3e3,phase_id:2,is_active:!1,start_date:new Date(Date.now()+10080*60*1e3).toISOString(),total_participants:0,qualified_participants:0,created_at:new Date().toISOString(),updated_at:new Date().toISOString()}],N=O(M).map(J=>{var U;const Q=T(J);return console.log(`🔍 Competition ${R(J)}:`,{phaseActive:(U=J==null?void 0:J.investment_phases)==null?void 0:U.is_active,dbStatus:J.status,actualStatus:Q}),{...J,actualStatus:Q}});if(k(N),console.log("📋 Sorted competitions:",N.map(J=>{var U;return{id:J.id,name:R(J),dbStatus:J.status,actualStatus:J.actualStatus,phaseActive:(U=J==null?void 0:J.investment_phases)==null?void 0:U.is_active}})),N.length>0){const J=N.find(X=>X.actualStatus==="active"),U=N.find(C),Q=J||U||N[0];A(Q.id),console.log("🎯 Auto-selected competition:",R(Q),"actualStatus:",Q.actualStatus)}}catch(_){console.error("❌ Error loading available competitions:",_),y("Failed to load competitions")}},z=async _=>{try{x(!0),y(null),console.log("🏆 Loading competition data for ID:",_);let M;if(_?M=await ln.getCompetitionById(_):M=await ln.getCurrentCompetition(),!M){if(console.log("❌ No competition found for ID:",_),_&&_.startsWith("fallback-")){const Q=g.find(X=>X.id===_);if(Q){console.log("✅ Using fallback competition:",Q.name);const X=te=>{const B=te.total_prize_pool*.4;return[{id:"1",tier_name:"1st Place",tier_rank_start:1,tier_rank_end:1,prize_amount:B,display_order:1,emoji:"🥇"},{id:"2",tier_name:"2nd Place",tier_rank_start:2,tier_rank_end:2,prize_amount:B*.5,display_order:2,emoji:"🥈"},{id:"3",tier_name:"3rd Place",tier_rank_start:3,tier_rank_end:3,prize_amount:B*.3,display_order:3,emoji:"🥉"},{id:"4",tier_name:"4th Place",tier_rank_start:4,tier_rank_end:4,prize_amount:B*.1,display_order:4,emoji:"🏆"},{id:"5",tier_name:"5th Place",tier_rank_start:5,tier_rank_end:5,prize_amount:B*.1,display_order:5,emoji:"🏆"},{id:"6",tier_name:"6th Place",tier_rank_start:6,tier_rank_end:6,prize_amount:B*.1,display_order:6,emoji:"🏆"},{id:"7",tier_name:"7th Place",tier_rank_start:7,tier_rank_end:7,prize_amount:B*.1,display_order:7,emoji:"🏆"},{id:"8",tier_name:"8th Place",tier_rank_start:8,tier_rank_end:8,prize_amount:B*.1,display_order:8,emoji:"🏆"},{id:"9",tier_name:"9th Place",tier_rank_start:9,tier_rank_end:9,prize_amount:B*.1,display_order:9,emoji:"🏆"},{id:"10",tier_name:"10th Place",tier_rank_start:10,tier_rank_end:10,prize_amount:B*.1,display_order:10,emoji:"🏆"}]};t(Q),n(X(Q)),c([]),h({totalParticipants:0,qualifiedParticipants:0,leadingVolume:0,totalPrizePool:Q.total_prize_pool,minimumQualification:Q.minimum_qualification_amount}),console.log("✅ Fallback competition data loaded");return}}console.log("❌ No competition found - returning early"),y("Competition not found");return}console.log("✅ Found competition:",M),t(M);const[N,J,U]=await Promise.all([ln.getPrizeTiers(M.id),ln.getLeaderboard(M.id,10),ln.getCompetitionStats(M.id)]);console.log("🏆Prize tiers loaded:",N),console.log("📊 Leaderboard loaded:",J.length,"entries"),console.log("📊 First leaderboard entry:",J[0]),console.log("📈 Stats loaded:",U),n(N),c(J),h(U)}catch(M){console.error("❌ Error loading competition data:",M),y("Failed to load competition data")}finally{x(!1)}},$=_=>{const M=_||0;return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0,maximumFractionDigits:0}).format(M)},H=(_,M)=>{if(o.length===0)return"No participants yet";if(_===M){const N=o[_-1];return N?`${N.full_name||N.username} (${$(N.total_referral_volume)})`:"Position open"}else{const N=o.slice(_-1,M);return N.length===0?"Positions open":N.length===1?`${N[0].full_name||N[0].username} (${$(N[0].total_referral_volume)})`:`${N.length} leaders competing`}},q=()=>{if(!r)return{title:"Competition Loading...",description:"Please wait..."};switch(r.actualStatus||r.status){case"upcoming":return{title:"Competition Starting Soon!",description:"The Gold Diggers Club leaderboard will be populated as participants join the presale."};case"active":return u.totalParticipants===0?{title:"Competition Starting Soon!",description:"The Gold Diggers Club leaderboard will be populated as users register and start earning commissions."}:o.length===0?{title:`${u.totalParticipants} Users Registered!`,description:"Competition is active - be the first to earn commissions and claim your spot on the leaderboard!"}:{title:"Competition Active!",description:`${o.length} users earning commissions, competing for ${$(u.totalPrizePool)} in prizes.`};case"ended":return{title:"Competition Ended",description:"This competition has concluded. Winners will be announced soon."};default:return{title:"Competition Status Unknown",description:"Please check back later for updates."}}};if(f)return e.jsx("section",{className:"gold-diggers-section",children:e.jsx("div",{className:"container",children:e.jsxs("div",{className:"section-headertext-center mb-xl",children:[e.jsx("h2",{className:"section-title",children:"🏆 Gold Diggers Club"}),e.jsx("p",{className:"section-subtitle",children:"Loading competition data..."})]})})});if(b)return e.jsx("section",{className:"gold-diggers-section",children:e.jsx("div",{className:"container",children:e.jsxs("div",{className:"section-header text-center mb-xl",children:[e.jsx("h2",{className:"section-title",children:"🏆 Gold Diggers Club"}),e.jsx("p",{className:"section-subtitle text-red-400",children:b}),e.jsxs("div",{className:"text-sm text-gray-400 mt-4",children:[e.jsx("p",{children:"Debug info: Check browser console for details"}),e.jsx("p",{children:"If you see hardcoded amounts, please clear browser cache"})]})]})})});const D=q();return e.jsx("section",{className:"gold-diggers-section",children:e.jsxs("div",{className:"container",children:[e.jsxs("div",{className:"section-header text-center mb-xl",children:[e.jsx("h2",{className:"section-title",children:"🏆 Gold Diggers Club"}),r&&e.jsxs("div",{className:"current-competition-badge",children:[e.jsx("span",{className:"competition-name",children:r.name}),e.jsx("span",{className:`competition-status ${r.actualStatus||r.status}`,children:(r.actualStatus||r.status)==="active"?"🟢 Active":(r.actualStatus||r.status)==="ended"?"🔴 Ended":(r.actualStatus||r.status)==="upcoming"?"🟡 Upcoming":"⚪ Unknown"})]}),e.jsx("p",{className:"section-subtitle",children:(r==null?void 0:r.description)||"Build your network by referring new investors. Each qualified referral counts toward your ranking."}),(r==null?void 0:r.id)==="fallback-competition"&&e.jsx("div",{className:"text-sm text-yellow-400 mt-2",children:"⚠️ Using fallback data - Run competition setup script for live data"})]}),g.length>1&&e.jsx("div",{className:"competition-tabs mb-xl",children:e.jsx("div",{className:"tabs-container",children:g.map(_=>e.jsxs("button",{onClick:()=>A(_.id),className:`tab-button ${S===_.id?"active":""} ${f&&S===_.id?"loading":""}`,disabled:f&&S===_.id,children:[e.jsx("span",{className:"tab-icon",children:"🏆"}),e.jsx("span",{className:"tab-text",children:R(_)}),e.jsx("span",{className:"tab-status",children:(_.actualStatus||_.status)==="active"?"🟢":(_.actualStatus||_.status)==="ended"?"🔴":"🟡"})]},_.id))})}),e.jsxs("div",{className:"grid grid-cols-2 gap-xl mb-xl",children:[e.jsxs("div",{className:"gold-diggers-card",children:[e.jsxs("div",{className:"card-header",children:[e.jsx("span",{className:"card-icon",children:"💡"}),e.jsx("h3",{className:"card-title",children:"How it Works"})]}),e.jsx("div",{className:"card-content",children:e.jsxs("div",{className:"steps-list",children:[e.jsxs("div",{className:"step-item",children:[e.jsx("span",{className:"step-number",children:"1"}),e.jsxs("div",{className:"step-content",children:[e.jsx("h4",{className:"step-title",children:"Refer & Earn"}),e.jsx("p",{className:"step-description",children:"Build your network by referring new investors. Each qualified referral counts toward your ranking."})]})]}),e.jsxs("div",{className:"step-item",children:[e.jsx("span",{className:"step-number",children:"2"}),e.jsxs("div",{className:"step-content",children:[e.jsx("h4",{className:"step-title",children:"Minimum Qualification"}),e.jsxs("p",{className:"step-description",children:["Achieve minimum ",$(u.minimumQualification)," in direct referral volume toqualify for bonus pool distribution."]})]})]}),e.jsxs("div",{className:"step-item",children:[e.jsx("span",{className:"step-number",children:"3"}),e.jsxs("div",{className:"step-content",children:[e.jsx("h4",{className:"step-title",children:"Climb the Rankings"}),e.jsx("p",{className:"step-description",children:"Your position is determined by total referral volume and network growth metrics."})]})]})]})})]}),e.jsxs("div",{className:"gold-diggers-card",children:[e.jsxs("div",{className:"card-header",children:[e.jsx("span",{className:"card-icon",children:"🏆"}),e.jsx("h3",{className:"card-title",children:"Live Rankings"}),e.jsxs("span",{className:"live-indicator",children:[e.jsx("span",{className:"live-dot"}),"LIVE"]})]}),e.jsxs("div",{className:"card-content text-center",children:[e.jsxs("div",{className:"competition-status",children:[e.jsx("span",{className:"status-icon",children:"🏆"}),e.jsx("h4",{className:"status-title",children:D.title}),e.jsx("p",{className:"status-description",children:D.description}),e.jsx("p",{className:"status-subtitle",children:"Be the first to start building your network!"})]}),e.jsxs("div",{className:"qualification-badge",children:["🎯 Minimum ",$(u.minimumQualification)," in direct referrals to qualify"]}),o.length>0?e.jsxs("div",{className:"leaderboard-preview mt-4",children:[e.jsx("h5",{className:"text-sm font-semibold text-gray-300 mb-2",children:"Top Commission Earners"}),o.slice(0,3).map((_,M)=>e.jsxs("div",{className:"flex justify-between items-center py-1text-sm",children:[e.jsxs("span",{className:"text-gray-300",children:[M+1,". ",_.full_name||_.username]}),e.jsx("span",{className:"text-yellow-400 font-semibold",children:$(_.total_referral_volume)})]},_.user_id))]}):e.jsx("div",{className:"empty-leaderboard mt-4 p-4 bg-gray-800/30 rounded-lg border border-gray-700/50",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl mb-2",children:"🚀"}),e.jsx("p",{className:"text-sm text-gray-300 mb-1",children:"Competition starting soon!"}),e.jsx("p",{className:"text-xs text-gray-400",children:"Be the first to earn commissions and claim your spot on the leaderboard."})]})})]})]})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-xl mb-xl",children:[e.jsx("div",{className:"col-span-2",children:e.jsxs("div",{className:"gold-diggers-card",children:[e.jsxs("div",{className:"card-header",children:[e.jsx("span",{className:"card-icon",children:"🏆"}),e.jsx("h3",{className:"card-title",children:"Prize Distribution"})]}),e.jsxs("div",{className:"card-content",children:[e.jsx("div",{className:"prize-list",children:s.map(_=>e.jsxs("div",{className:`prize-item ${_.tier_rank_start===1?"prize-first":_.tier_rank_start===2?"prize-second":_.tier_rank_start===3?"prize-third":"prize-other"}`,children:[e.jsxs("div",{className:"prize-info",children:[e.jsxs("span",{className:"prize-rank",children:[_.emoji," ",_.tier_name]}),e.jsx("span",{className:"prize-amount",children:_.tier_rank_start===_.tier_rank_end?$(_.prize_amount):`${$(_.prize_amount)} each`})]}),e.jsxs("div",{className:"current-leader",children:[e.jsx("span",{className:"leader-label",children:"Current Leader:"}),e.jsx("span",{className:"leader-name",children:H(_.tier_rank_start,_.tier_rank_end)})]})]},_.id))}),e.jsx("div",{className:"prize-note",children:"Remaining pool distributed proportionally among top 10 qualified participants"})]})]})}),e.jsxs("div",{className:"space-y-lg",children:[e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-number",children:u.totalParticipants}),e.jsx("div",{className:"stat-label",children:u.totalParticipants===1?"User Registered":"Users Registered"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-number",children:u.leadingVolume>0?$(u.leadingVolume):"-$0"}),e.jsx("div",{className:"stat-label",children:"Leading Volume"})]}),e.jsxs("div",{className:"text-center mt-lg",children:[e.jsx("a",{href:"https://t.me/AureusAllianceBot",target:"_blank",rel:"noopener noreferrer",className:"btn-gold-diggers",children:"⚡ Join the Competition"}),e.jsx("p",{className:"text-sm text-gray-400 mt-md",children:r!=null&&r.end_date?`Competition ends ${new Date(r.end_date).toLocaleDateString()}`:"Competition ends when presale reaches $1,000,000 total volume"})]})]})]})]})})},TN=()=>e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:2,children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4 4v5h5M20 20v-5h-5M4 4l1.5 1.5A9 9 0 0120.5 10M20 20l-1.5-1.5A9 9 0 003.5 14"})}),fd=({className:l})=>e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:`h-6 w-6 ${l}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:3,children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 13l4 4L19 7"})}),RN=({className:l})=>e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:l,viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M14,2H6A2,2,0,0,0,4,4v16a2,2,0,0,0,2,2H18a2,2,0,0,0,2-2V8Zm2,16H8V4h5v5h5Z"})}),FN=({className:l})=>e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:l,viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M17,10.5V7a1,1,0,0,0-1-1H4A1,1,0,0,0,3,7v10a1,1,0,0,0,1,1h12a1,1,0,0,0,1-1v-3.5l4,4v-11Z"})}),ON=({className:l})=>e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:l,viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M16,13a4,4,0,1,0-4-4A4,4,0,0,0,16,13Zm4-6a2,2,0,1,0-2-2A2,2,0,0,0,20,7ZM4,13a4,4,0,1,0-4-4A4,4,0,0,0,4,13Zm4,6a2,2,0,1,0-2-2A2,2,0,0,0,8,19Z"})}),Oa=(l,r)=>isNaN(l)||!isFinite(l)?"N/A":new Intl.NumberFormat("en-US",r).format(l),MN=({liveGoldPrice:l,goldPriceLoading:r,goldPriceError:t})=>{let s;try{s=I1().getContent}catch{console.warn("Calculator: Site content context not available, using defaults"),s=(R,O,T="")=>T}const[n,o]=K.useState(()=>{const R=_i(2026),O={...cy,selectedYear:2026,landHa:(R==null?void 0:R.hectares)||250,goldPriceUsdPerKg:109026,userShares:1};returnconsole.log("Calculator initial state:",O),O}),[c,u]=K.useState({}),[h,f]=K.useState([]),[x,b]=K.useState(0),[y,g]=K.useState(1),k=s("calculator","title","Financial Calculator")||"Financial Calculator";s("calculator","subtitle","Experience the power of data-driven investment decisions with our interactive calculator"),parseInt(s("calculator","default_land_size","25")||"25"),parseInt(s("calculator","default_user_shares","1")||"1"),parseInt(s("calculator","gold_price_default","100000")||"100000");const S=K.useCallback(()=>{const R=_i(2026);o({...cy,landHa:(R==null?void 0:R.hectares)||250,userShares:1,goldPriceUsdPerKg:109026,selectedYear:2026})},[]);K.useEffect(()=>{const R=_i(2026);o(O=>{var T;return{...O,landHa:O.selectedYear?((T=_i(O.selectedYear))==null?void 0:T.hectares)||250:(R==null?void 0:R.hectares)||250,userShares:1,goldPriceUsdPerKg:109026,selectedYear:O.selectedYear||2026}})},[]),K.useEffect(()=>{const{landHa:R,avgGravelThickness:O,inSituGrade:T,recoveryFactor:P,goldPriceUsdPerKg:z,opexPercent:$,userShares:H,dividendPayoutPercent:q}=n,D=R/Zf,_=Nj/Zf,M=od/_;b(D*M);const N=[];I0.forEach((Z,se)=>{const me=_i(Z);if(!me)return;const ye=me.plants,we=ye*iy*oy*ly*(T/Yh)*(P/100)/1e3,ve=we*z,Be=ve*($/100),$e=ve-Be,at=$e/od,rt=at*H;N.push({year:se+1,actualYear:Z,ebit:$e,dividendPerShare:at,userDividend:rt,numPlants:ye,annualGoldKg:we,annualRevenue:ve})}),f(N);const J=R/Zf,U=J*iy*oy*ly,Q=U*(T/Yh)*(P/100)/1e3,X=Q*z,te=X*($/100),B=X-te,V=B,L=V/od,G=L*H;console.log("💰 Dynamic dividend calculation:",{landEbitPotential:V,totalShares:od,dividendPerShare:L,userShares:H,userActualDividend:G,expectedDividendPerShare:V/od}),u({numPlants:J,annualRevenue:X,annualEbit:B,annualGoldKg:Q,dividendPerShare:L,userAnnualDividend:G,volumeM3:R*1e4*O,tonnesInSitu:R*1e4*O*Yh,containedGoldG:R*1e4*O*Yh*T,annualThroughputT:U,annualOperatingCost:te})},[n]);const A=R=>{const{name:O,value:T}=R.target;o(P=>({...P,[O]:parseFloat(T)||0}))},w=R=>{const O=parseInt(R.target.value),T=_i(O);console.log("Year selected:",O,"Expansion data:",T),T&&o(P=>({...P,selectedYear:O,landHa:T.hectares}))},C=({projection:R,selectedYear:O,onYearSelect:T})=>{const P=R.find(z=>z.year===O)||R[0];return e.jsxs("div",{className:"projection-tabs-container",children:[e.jsx("div",{className:"tab-navigation",children:e.jsx("div",{className:"tab-scroll-container",children:R.map(z=>e.jsx("button",{onClick:()=>T(z.year),className:`tab-button ${O===z.year?"tab-active":"tab-inactive"}`,children:z.actualYear?`${z.actualYear}`:`Year ${z.year}`},z.year))})}),e.jsxs("div",{className:"tab-content",children:[e.jsx("div",{className:"year-header",children:e.jsx("h4",{className:"text-xl font-bold text-gold mb-4",children:P!=null&&P.actualYear?`${P.actualYear} Projection`:`Year ${O} Projection`})}),e.jsxs("div",{className:"projection-metrics-grid",children:[e.jsxs("div",{className:"metric-card",children:[e.jsx("div",{className:"metric-label",children:"Plants in Operation"}),e.jsx("div",{className:"metric-value",children:Oa((P==null?void 0:P.numPlants)||0,{maximumFractionDigits:1})}),e.jsx("div",{className:"metric-unit",children:"plants"})]}),e.jsxs("div",{className:"metric-card",children:[e.jsx("div",{className:"metric-label",children:"Annual EBIT"}),e.jsx("div",{className:"metric-value text-green-400",children:Oa((P==null?void 0:P.ebit)||0,{style:"currency",currency:"USD",minimumFractionDigits:0})}),e.jsx("div",{className:"metric-unit",children:"earnings"})]}),e.jsxs("div",{className:"metric-card",children:[e.jsx("div",{className:"metric-label",children:"Gold Production"}),e.jsx("div",{className:"metric-value text-amber-400",children:Oa((P==null?void 0:P.annualGoldKg)||0,{maximumFractionDigits:0})}),e.jsx("div",{className:"metric-unit",children:"kg/year"})]}),e.jsxs("div",{className:"metric-card",children:[e.jsx("div",{className:"metric-label",children:"Annual Revenue"}),e.jsx("div",{className:"metric-value text-blue-400",children:Oa((P==null?void 0:P.annualRevenue)||0,{style:"currency",currency:"USD",minimumFractionDigits:0})}),e.jsx("div",{className:"metric-unit",children:"revenue"})]}),e.jsxs("div",{className:"metric-card highlight",children:[e.jsx("div",{className:"metric-label",children:"Dividend per Share"}),e.jsx("div",{className:"metric-value text-gold",children:Oa((P==null?void 0:P.dividendPerShare)||0,{style:"currency",currency:"USD",minimumFractionDigits:4})}),e.jsx("div",{className:"metric-unit",children:"per share"})]}),e.jsxs("div",{className:"metric-card highlight",children:[e.jsx("div",{className:"metric-label",children:"Your Annual Dividend"}),e.jsx("div",{className:"metric-value text-gold font-bold",children:Oa((P==null?void 0:P.userDividend)||0,{style:"currency",currency:"USD",minimumFractionDigits:2})}),e.jsx("div",{className:"metric-unit",children:"your return"})]})]})]})]})};return e.jsxs("div",{className:"card calculator-container",children:[e.jsx("header",{className:"calculator-header",children:e.jsxs("div",{className:"calculator-header-content",children:[e.jsxs("div",{className:"calculator-title-section",children:[e.jsxs("h3",{className:"text-lg font-bold text-gold mb-1",children:["Interactive ",k]}),e.jsx("p",{className:"text-sm text-gray-300",children:"Model your share purchasing scenario to explore potential returns."})]}),e.jsx("div",{className:"calculator-reset-button",children:e.jsxs("button",{onClick:S,className:"btn btn-secondary flex items-center gap-2 text-sm",children:[e.jsx(TN,{}),e.jsx("span",{children:"Reset"})]})})]})}),e.jsxs("div",{className:"calculator-layout-modern",children:[e.jsxs("div",{className:"calculator-inputs-modern",children:[e.jsxs("div",{className:"inputs-header",children:[e.jsx("h3",{className:"text-xl font-bold text-gold mb-2",children:"Investment Parameters"}),e.jsx("p",{className:"text-sm text-gray-400 mb-6",children:"Configure your investment scenario and project assumptions"})]}),e.jsxs("div",{className:"modern-inputs-grid",children:[e.jsxs("div",{className:"input-group editable",children:[e.jsx("label",{className:"input-label",children:"Target Year"}),e.jsx("select",{name:"selectedYear",value:n.selectedYear||2026,onChange:w,className:"modern-input",children:I0.map(R=>{const O=_i(R);return e.jsxs("option",{value:R,children:[O==null?void 0:O.month," ",R," - ",O==null?void 0:O.plants," Plants (",O==null?void 0:O.hectares.toLocaleString()," ha)"]},R)})}),e.jsx("div",{className:"input-info",children:e.jsx("span",{className:"info-text",children:"Select our planned capacity for a specific year"})})]}),e.jsxs("div",{className:"input-group editable",children:[e.jsx("label",{className:"input-label",children:"Land Size (Manual Override)"}),e.jsx("select",{name:"landHa",value:n.landHa,onChange:A,className:"modern-input",children:_j.map(R=>e.jsxs("option",{value:R,children:[R," ha"]},R))}),e.jsxs("div",{className:"input-info",children:[e.jsxs("span",{className:"info-badge",children:["Corresponds to ",Oa(x),"shares"]}),e.jsx("span",{className:"info-text",children:'Adjust manually for "what-if" scenarios'})]})]}),e.jsxs("div",{className:"input-group editable",children:[e.jsx("label",{className:"input-label",children:"Your Shares"}),e.jsx("input",{type:"number",name:"userShares",value:n.userShares,onChange:A,step:100,className:"modern-input"}),e.jsx("div",{className:"input-info",children:e.jsxs("span",{className:"info-text",children:["Total project shares: ",Oa(od)]})})]}),e.jsxs("div",{className:"input-group editable gold-price-group",children:[e.jsx("label",{className:"input-label",children:"Gold Price"}),e.jsx("input",{type:"number",name:"goldPriceUsdPerKg",value:n.goldPriceUsdPerKg,onChange:A,step:1e3,className:"modern-input"}),e.jsxs("div",{className:"input-info",children:[e.jsxs("div",{className:"gold-price-controls",children:[e.jsx("button",{type:"button",onClick:()=>o(R=>({...R,goldPriceUsdPerKg:l})),disabled:r,className:"live-price-btn",children:r?"Loading...":"Use Live Price"}),e.jsxs("span",{className:"live-price-display",children:["Live: $",l.toLocaleString(),"/kg"]})]}),t&&e.jsxs("span",{className:"error-text",children:["(",t,")"]})]})]})]})]}),e.jsxs("div",{className:"calculator-results-modern",children:[e.jsxs("div",{className:"results-header",children:[e.jsx("h3",{className:"text-xl font-bold text-gold mb-2",children:"Financial Projections"}),e.jsx("p",{className:"text-sm text-gray-400 mb-6",children:"Based on your investment parameters"})]}),e.jsxs("div",{className:"results-grid",children:[e.jsxs("div",{className:"result-card operational",children:[e.jsx("div",{className:"result-icon",children:"🏭"}),e.jsxs("div",{className:"result-content",children:[e.jsx("div",{className:"result-label",children:"Plants for Your Land"}),e.jsx("div",{className:"result-value",children:Oa(c.numPlants,{maximumFractionDigits:1})}),e.jsx("div",{className:"result-unit",children:"plants"})]})]}),e.jsxs("div",{className:"result-card revenue",children:[e.jsx("div",{className:"result-icon",children:"💰"}),e.jsxs("div",{className:"result-content",children:[e.jsx("div",{className:"result-label",children:"Land Revenue Potential"}),e.jsx("div",{className:"result-value",children:Oa(c.annualRevenue,{style:"currency",currency:"USD",minimumFractionDigits:0})}),e.jsx("div",{className:"result-unit",children:"per year"})]})]}),e.jsxs("div",{className:"result-card ebit",children:[e.jsx("div",{className:"result-icon",children:"📈"}),e.jsxs("div",{className:"result-content",children:[e.jsx("div",{className:"result-label",children:"Land EBIT Potential"}),e.jsx("div",{className:"result-value",children:Oa(c.annualEbit,{style:"currency",currency:"USD",minimumFractionDigits:0})}),e.jsx("div",{className:"result-unit",children:"earnings"})]})]}),e.jsxs("div",{className:"result-card gold",children:[e.jsx("div",{className:"result-icon",children:"🥇"}),e.jsxs("div",{className:"result-content",children:[e.jsx("div",{className:"result-label",children:"Land Gold Production"}),e.jsx("div",{className:"result-value",children:Oa(c.annualGoldKg,{maximumFractionDigits:2})}),e.jsx("div",{className:"result-unit",children:"kg/year"})]})]}),e.jsxs("div",{className:"result-card dividend highlight",children:[e.jsx("div",{className:"result-icon",children:"💎"}),e.jsxs("div",{className:"result-content",children:[e.jsx("div",{className:"result-label",children:"Your Annual Dividend"}),e.jsx("div",{className:"result-value",children:Oa(c.userAnnualDividend,{style:"currency",currency:"USD",minimumFractionDigits:2})}),e.jsx("div",{className:"result-unit",children:"your return"})]})]}),e.jsxs("div",{className:"result-card dividend-share highlight",children:[e.jsx("div",{className:"result-icon",children:"📊"}),e.jsxs("div",{className:"result-content",children:[e.jsx("div",{className:"result-label",children:"Dividend per Share"}),e.jsx("div",{className:"result-value",children:Oa(c.dividendPerShare,{style:"currency",currency:"USD",minimumFractionDigits:4})}),e.jsx("div",{className:"result-unit",children:"per share"})]})]})]}),e.jsxs("div",{className:"project-assumptions-section",children:[e.jsxs("div",{className:"assumptions-header",children:[e.jsx("h4",{className:"text-lg font-semibold text-gold mb-3",children:"Project Assumptions"}),e.jsx("p",{className:"text-xs text-gray-400 mb-4",children:"Technical parameters used in calculations"})]}),e.jsxs("div",{className:"assumptions-grid",children:[e.jsxs("div",{className:"assumption-item",children:[e.jsx("span",{className:"assumption-label",children:"Gravel Thickness"}),e.jsxs("span",{className:"assumption-value",children:[n.avgGravelThickness," meters"]})]}),e.jsxs("div",{className:"assumption-item",children:[e.jsx("span",{className:"assumption-label",children:"In-situ Grade"}),e.jsxs("span",{className:"assumption-value",children:[n.inSituGrade," g/m³"]})]}),e.jsxs("div",{className:"assumption-item",children:[e.jsx("span",{className:"assumption-label",children:"Recovery Factor"}),e.jsxs("span",{className:"assumption-value",children:[n.recoveryFactor,"% efficiency"]})]}),e.jsxs("div",{className:"assumption-item",children:[e.jsx("span",{className:"assumption-label",children:"Operating Cost"}),e.jsxs("span",{className:"assumption-value",children:[n.opexPercent,"% of revenue"]})]}),e.jsxs("div",{className:"assumption-item",children:[e.jsx("span",{className:"assumption-label",children:"Dividend Payout"}),e.jsxs("span",{className:"assumption-value",children:[n.dividendPayoutPercent,"% of EBIT"]})]})]})]})]}),e.jsxs("div",{className:"projection-section-modern",children:[e.jsxs("div",{className:"projection-header",children:[e.jsx("h3",{className:"text-xl font-bold text-gold mb-2",children:"Year-by-Year Expansion"}),e.jsx("p",{className:"text-sm text-gray-400 mb-6",children:"Detailed projections for each year of operation"})]}),e.jsx(C,{projection:h,selectedYear:y,onYearSelect:g})]})]})]})},zr=({href:l,children:r})=>e.jsxs("a",{href:l,className:"relative text-gray-300 hover:text-white transition-all duration-300 group text-sm",children:[r,e.jsx("span",{className:"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-amber-400 to-amber-600 group-hover:w-full transition-all duration-300"})]}),zN=({setCurrentSection:l,user:r,clearLocalStorage:t,handleSwitchToRegister:s})=>{const[n,o]=K.useState(!1);return e.jsxs("header",{className:"header-container",style:{paddingTop:"12px"},children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"}),e.jsx("div",{className:"container",children:e.jsxs("nav",{className:"header-nav",children:[e.jsx("a",{href:"#",className:"header-logo-image float-animation desktop-logo",children:e.jsx("img",{src:"https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png?v=2",alt:"Aureus Alliance Holdings - Professional Gold Mining Investment Company Logo",className:"header-logo-img"})}),e.jsxs("div",{className:"desktop-nav desktop-nav-single",children:[e.jsx(zr,{href:"#about",children:"About"}),e.jsx(zr,{href:"#highlights",children:"Highlights"}),e.jsx(zr,{href:"#commission",children:"Commission"}),e.jsx(zr,{href:"#gold-diggers-club",children:"Gold Diggers Club"}),e.jsx(zr,{href:"#calculator",children:"Calculator"}),e.jsx(zr,{href:"#project",children:"Project"})]}),e.jsxs("div",{className:"desktop-actions desktop-actions-single",children:[e.jsx("button",{onClick:s,className:"btn btn-primary",style:{fontSize:"13px",padding:"8px 16px"},children:"Purchase Shares"}),e.jsx("a",{href:"https://t.me/AureusAllianceHoldings",target:"_blank",rel:"noopener noreferrer",className:"btn btn-outline",style:{fontSize:"16px",padding:"8px 12px",minWidth:"44px",display:"flex",alignItems:"center",justifyContent:"center"},title:"Updates Channel",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M12 0C5.374 0 0 5.373 0 12s5.374 12 12 12 12-5.373 12-12S18.626 0 12 0zm5.568 8.16l-1.61 7.59c-.12.54-.44.67-.89.42l-2.46-1.82-1.19 1.14c-.13.13-.24.24-.49.24l.17-2.43 4.47-4.03c.19-.17-.04-.27-.3-.1L9.28 13.47l-2.38-.75c-.52-.16-.53-.52.11-.77l9.28-3.58c.43-.16.81.11.67.73z"})})})]}),e.jsxs("div",{className:"header-nav-top tablet-layout",children:[e.jsx("a",{href:"#",className:"header-logo-image float-animation",children:e.jsx("img",{src:"https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png?v=2",alt:"Aureus Alliance Holdings - Professional Gold Mining Investment Company Logo",className:"header-logo-img"})}),e.jsxs("div",{className:"desktop-actions",children:[e.jsx("button",{onClick:s,className:"btn btn-primary",style:{fontSize:"13px",padding:"8px 16px"},children:"Purchase Shares"}),e.jsx("a",{href:"https://t.me/AureusAllianceHoldings",target:"_blank",rel:"noopener noreferrer",className:"btn btn-outline",style:{fontSize:"16px",padding:"8px 12px",minWidth:"44px",display:"flex",alignItems:"center",justifyContent:"center"},title:"Updates Channel",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M12 0C5.374 0 0 5.373 0 12s5.374 12 12 12 12-5.373 12-12S18.626 0 12 0zm5.568 8.16l-1.61 7.59c-.12.54-.44.67-.89.42l-2.46-1.82-1.19 1.14c-.13.13-.24.24-.49.24l.17-2.43 4.47-4.03c.19-.17-.04-.27-.3-.1L9.28 13.47l-2.38-.75c-.52-.16-.53-.52.11-.77l9.28-3.58c.43-.16.81.11.67.73z"})})})]})]}),e.jsx("div",{className:"header-nav-bottom tablet-layout",children:e.jsxs("div",{className:"desktop-nav",children:[e.jsx(zr,{href:"#about",children:"About"}),e.jsx(zr,{href:"#highlights",children:"Highlights"}),e.jsx(zr,{href:"#commission",children:"Commission"}),e.jsx(zr,{href:"#gold-diggers-club",children:"Gold Diggers Club"}),e.jsx(zr,{href:"#calculator",children:"Calculator"}),e.jsx(zr,{href:"#project",children:"Project"})]})}),e.jsx("button",{onClick:()=>o(!n),className:"mobile-menu-button",children:e.jsxs("div",{className:"hamburger-icon",children:[e.jsx("span",{className:`hamburger-line ${n?"open":""}`}),e.jsx("span",{className:`hamburger-line ${n?"open":""}`}),e.jsx("span",{className:`hamburger-line ${n?"open":""}`})]})})]})}),e.jsx("div",{className:`mobile-menu-overlay ${n?"open":""}`,onClick:()=>o(!1)}),e.jsx("div",{className:`mobile-menu-panel ${n?"open":""}`,children:e.jsxs("div",{className:"mobile-menu-content",children:[e.jsxs("nav",{className:"mobile-menu-nav",children:[e.jsx(zr,{href:"#about",onClick:()=>o(!1),children:"About"}),e.jsx(zr,{href:"#highlights",onClick:()=>o(!1),children:"Highlights"}),e.jsx(zr,{href:"#commission",onClick:()=>o(!1),children:"Commission"}),e.jsx(zr,{href:"#gold-diggers-club",onClick:()=>o(!1),children:"Gold Diggers Club"}),e.jsx(zr,{href:"#calculator",onClick:()=>o(!1),children:"Calculator"}),e.jsx(zr,{href:"#project",onClick:()=>o(!1),children:"Project"})]}),e.jsxs("div",{className:"mobile-menu-actions",children:[e.jsx("button",{onClick:()=>{o(!1),s()},className:"btn btn-primary btn-block",style:{marginBottom:"0.5rem",fontSize:"14px"},children:"Purchase Shares"}),e.jsxs("a",{href:"https://t.me/AureusAllianceHoldings",target:"_blank",rel:"noopener noreferrer",className:"btn btn-outline btn-block",style:{fontSize:"14px",display:"flex",alignItems:"center",justifyContent:"center",gap:"8px"},title:"Updates Channel",children:[e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M12 0C5.3740 0 5.373 0 12s5.374 12 12 12 12-5.373 12-12S18.626 0 12 0zm5.568 8.16l-1.61 7.59c-.12.54-.44.67-.89.42l-2.46-1.82-1.19 1.14c-.13.13-.24.24-.49.24l.17-2.43 4.47-4.03c.19-.17-.04-.27-.3-.1L9.28 13.47l-2.38-.75c-.52-.16-.53-.52.11-.77l9.28-3.58c.43-.16.81.11.67.73z"})}),"Updates"]})]})]})})]})},BN=()=>e.jsxs("section",{className:"hero-section",style:{backgroundImage:"url('https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/hero-background.jpg')"},children:[e.jsx("div",{className:"absolute inset-0 bg-black/70"}),e.jsx("div",{className:"container relative z-10",children:e.jsxs("div",{className:"hero-content-wrapper",children:[e.jsxs("div",{className:"hero-left-content",children:[e.jsxs("h1",{className:"heading-hero mb-sm",children:[e.jsx("span",{className:"text-gold",children:"Secure Your Wealth"}),e.jsx("span",{className:"text-white",children:" with Real Gold"})]}),e.jsx("p",{className:"text-lg mb-md max-w-xl",children:"Professional gold mining investment with blockchain-backed ownership and transparent profit sharing."}),e.jsxs("div",{className:"hero-buttons",children:[e.jsx("a",{href:"#about",className:"btn btn-primary",children:"Explore Investment"}),e.jsx("a",{href:"#calculator",className:"btn btn-secondary",children:"Calculate Returns"})]}),e.jsxs("div",{className:"hero-features",children:[e.jsxs("div",{className:"flex items-center gap-xs",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),e.jsx("span",{className:"text-sm",children:"Verified Operations"})]}),e.jsxs("div",{className:"flex items-center gap-xs",children:[e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),e.jsx("span",{className:"text-sm",children:"Blockchain Secured"})]}),e.jsxs("div",{className:"flex items-center gap-xs",children:[e.jsx("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),e.jsx("span",{className:"text-sm",children:"Sustainable Mining"})]})]}),e.jsxs("div",{className:"hero-stats-grid",children:[e.jsxs("div",{className:"card text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-gold mb-xs",children:"$5"}),e.jsx("div",{className:"text-sm mb-xs",children:"Per Share"}),e.jsx("div",{className:"text-sm",children:"Presale Price"})]}),e.jsxs("div",{className:"card text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-400 mb-xs",children:"$134.51"}),e.jsx("div",{className:"text-sm mb-xs",children:"Annual Dividend"}),e.jsx("div",{className:"text-sm",children:"Projected"})]}),e.jsxs("div",{className:"card text-center",children:[e.jsx("div",{className:"text-2xl font-bold mb-xs",children:"250 ha"}),e.jsx("div",{className:"text-sm mb-xs",children:"Concession"}),e.jsx("div",{className:"text-sm",children:"Verified"})]})]})]}),e.jsx("div",{className:"hero-right-content",children:e.jsx("img",{src:"https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png?v=2",alt:"Aureus Alliance Holdings - Sustainable Gold Mining Operations and Investment Platform",className:"hero-main-logo"})})]})})]}),UN=()=>{const l=[{date:"Now",title:"Pre-Seed Angel Round",desc:"Limited to $1,000,000 total for this round."},{date:"July",title:"NFT Equity Share Presale Launch",desc:"Public offering at higher valuation."},{date:"Q32025",title:"Gaming Platform Alpha",desc:"Early access for shareholders and NFT holders."}],r=[{title:"Gold-Backed Stability",desc:"Unlike purely speculative digital assets, Aureus is backed by physical gold mining operations with real-world value and cash flow."},{title:"Multi-Stream Revenue",desc:"Our business model combines income from gold production, NFT sales, gaming microtransactions, and marketplace fees."},{title:"Exponential Growth Potential",desc:"The integration of traditional mining with cutting-edge digital assets creates unique synergies and market positioning."}];return e.jsxs("section",{id:"about",className:"relative py-24lg:py-32 overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-black via-gray-900/50 to-black"}),e.jsx("div",{className:"absolute inset-0 cyber-grid opacity-20"}),e.jsx("div",{className:"absolute top-1/4 -left-32 w-96 h-96 bg-amber-500/10 rounded-full blur-3xl animate-pulse"}),e.jsx("div",{className:"absolute bottom-1/4 -right-32 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse",style:{animationDelay:"1s"}}),e.jsxs("div",{className:"w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsx("h2",{className:"text-4xl md:text-5xl lg:text-6xl font-black text-gradient-gold mb-4 tracking-tight",children:"About Aureus Alliance"}),e.jsx("p",{className:"text-lg md:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed",children:"A revolutionary approach to gold mining investment through blockchain technology and sustainable practices."}),e.jsx("div",{className:"flex justify-center mt-4",children:e.jsx("div",{className:"w-24 h-1 bg-gradient-to-r from-transparent via-amber-400 to-transparent rounded-full"})})]}),e.jsxs("div",{className:"grid grid-3 gap-md",children:[e.jsxs("div",{className:"card",children:[e.jsx("h3",{className:"heading-md text-gold mb-sm",children:"Our Mission"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"text-sm",children:"Aureus Alliance Holdings bridges traditional gold mining with modern blockchain technology."}),e.jsx("p",{className:"text-sm",children:"Built on verified mining concessions with proven gold reserves, providing stable returns."}),e.jsx("p",{className:"text-sm",children:"Direct participation in mining profits through NFT-backed ownership shares."})]})]}),e.jsxs("div",{className:"card",children:[e.jsx("h4",{className:"heading-md text-gold mb-sm",children:"Key Advantages"}),e.jsx("div",{className:"space-y-2",children:r.slice(0,3).map((t,s)=>e.jsxs("div",{className:"p-xs border border-white/10 rounded",children:[e.jsx("h5",{className:"text-gold font-semibold text-sm mb-xs",children:t.title}),e.jsx("p",{className:"text-sm",children:t.desc})]},s))})]}),e.jsxs("div",{className:"card",children:[e.jsx("h3",{className:"heading-md text-gold mb-sm",children:"Development Roadmap"}),e.jsx("div",{className:"space-y-2",children:l.slice(0,4).map((t,s)=>e.jsxs("div",{className:"flex gap-sm",children:[e.jsx("div",{className:"w-6 h-6 bg-gold rounded-full flex items-center justify-center text-black font-bold text-sm flex-shrink-0",children:s+1}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"text-gold text-sm font-semibold",children:t.date}),e.jsx("h4",{className:"font-semibold text-sm",children:t.title}),e.jsx("p",{className:"text-sm",children:t.desc})]})]},s))})]})]})]})]})},$N=()=>{const l=["Fully verified gold mine operations","Real gold production, not just token promises","NFT-based verified ownership","Sustainable profit model backed by physical gold"],r=[{icon:e.jsx(FN,{className:"w-6 h-6 text-amber-500"}),text:"Real mine site footage"},{icon:e.jsx(RN,{className:"w-6 h-6 text-amber-500"}),text:"Full documentation"},{icon:e.jsx(ON,{className:"w-6 h-6 text-amber-500"}),text:"Verifiable by the community"}];return e.jsxs("section",{id:"highlights",className:"relative py-24 lg:py-32 overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-gray-900 via-black to-gray-900"}),e.jsx("div",{className:"absolute inset-0 cyber-grid opacity-30"}),e.jsx("div",{className:"absolute top-1/3 -left-40 w-80 h-80 bg-amber-500/5 rounded-full blur-3xl animate-pulse"}),e.jsx("div",{className:"absolute bottom-1/3 -right-40 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl animate-pulse",style:{animationDelay:"2s"}}),e.jsxs("div",{className:"w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsx("h2",{className:"text-4xl md:text-5xl lg:text-6xl font-black text-gradient-gold mb-4 tracking-tight",children:"Key Highlights"}),e.jsx("p",{className:"text-lg md:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed",children:"Core strengths and financial incentives that make Aureus a unique opportunity."}),e.jsx("div",{className:"flex justify-center mt-4",children:e.jsx("div",{className:"w-24 h-1 bg-gradient-to-r from-transparent via-amber-400 to-transparent rounded-full"})})]}),e.jsxs("div",{className:"grid grid-3 gap-md",children:[e.jsxs("div",{className:"card",children:[e.jsx("h3",{className:"heading-md text-gold mb-sm",children:"Why Choose Aureus?"}),e.jsx("div",{className:"space-y-2",children:l.slice(0,3).map(t=>e.jsxs("div",{className:"flex items-start gap-xs",children:[e.jsx(fd,{className:"text-green-400 flex-shrink-0 w-4 h-4 mt-0.5"}),e.jsx("span",{className:"text-sm",children:t})]},t))})]}),e.jsxs("div",{className:"card",children:[e.jsx("h3",{className:"heading-md text-gold mb-sm",children:"Full Transparency"}),e.jsxs("div",{className:"space-y-2",children:[l.slice(3).map(t=>e.jsxs("div",{className:"flex items-start gap-xs",children:[e.jsx(fd,{className:"text-green-400 flex-shrink-0 w-4 h-4 mt-0.5"}),e.jsx("span",{className:"text-sm",children:t})]},t)),r.slice(0,2).map(t=>e.jsxs("div",{className:"flex items-center gap-xs text-sm",children:[t.icon,e.jsx("span",{children:t.text})]},t.text))]})]}),e.jsxs("div",{className:"card",children:[e.jsx("h3",{className:"heading-md text-gold mb-sm",children:"Investment Benefits"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-start gap-xs",children:[e.jsx(fd,{className:"text-green-400 flex-shrink-0 w-4 h-4 mt-0.5"}),e.jsx("span",{className:"text-sm",children:"Verified mining operations"})]}),e.jsxs("div",{className:"flex items-start gap-xs",children:[e.jsx(fd,{className:"text-green-400 flex-shrink-0 w-4 h-4 mt-0.5"}),e.jsx("span",{className:"text-sm",children:"Blockchain-secured ownership"})]}),e.jsxs("div",{className:"flex items-start gap-xs",children:[e.jsx(fd,{className:"text-green-400 flex-shrink-0 w-4h-4 mt-0.5"}),e.jsx("span",{className:"text-sm",children:"Transparent profit sharing"})]})]})]})]})]})]})},qN=()=>{const l="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/sustainable-villages.jpg",r=["A self-sustaining, solar-powered smart village","Focus: housing, education, nutrition, farming, clean water","Located near Kadoma gold operations in Zimbabwe","Naming rights formalized via Aureus AllianceHoldings"],t=["A series of mobile and modular health clinics","Focus: maternal health, vaccination, rural care","First clinics to open in Kadoma Zimbabwe","Powered by share sales, named via Aureus Alliance Holdings"],s=({className:o})=>e.jsx("svg",{className:o,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5 13l4 4L19 7",stroke:"currentColor",strokeWidth:"3",strokeLinecap:"round",strokeLinejoin:"round"})}),n=({children:o})=>e.jsxs("li",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"mt-1.5 w-4 h-4 flex-shrink-0 rounded-full bg-white/80 flex items-center justify-center",children:e.jsx(s,{className:"w-2.5 h-2.5 text-amber-500"})}),e.jsx("span",{className:"text-white",children:o})]});return e.jsxs("div",{className:"mt-12",children:[e.jsxs("div",{className:"grid lg:grid-cols-2 gap-8 xl:gap-16",children:[e.jsxs("div",{className:"space-y-6 flex flex-col",children:[e.jsxs("header",{children:[e.jsx("h2",{className:"text-4xl md:text-5xl font-black uppercase tracking-wider text-white",children:"Sustainable Villages"}),e.jsx("h3",{className:"text-4xl md:text-5xl font-black uppercase -mt-2 gold-text",children:"Health Trust Clinics"})]}),e.jsx("img",{src:l,alt:"Aureus Alliance Holdings Sustainable Villages and Health Trust Clinics - Community Development Projects in Zimbabwe",className:"rounded-lg shadow-2xl w-full"})]}),e.jsx("div",{className:"flex flex-col justify-start pt-4 text-white",children:e.jsxs("div",{className:"space-y-8",children:[e.jsx("p",{className:"text-lg font-bold uppercase tracking-wide"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h4",{className:"text-2xl font-bold gold-text",children:"SUSTAINABLE VILLAGE:"}),e.jsx("ul",{className:"space-y-2.5",children:r.map(o=>e.jsx(n,{children:o},o))})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h4",{className:"text-2xl font-bold gold-text",children:"HEALTH TRUST CLINICS:"}),e.jsx("ul",{className:"space-y-2.5",children:t.map(o=>e.jsx(n,{children:o},o))})]})]})})]}),e.jsx("footer",{className:"text-center mt-16 text-white",children:e.jsx("p",{className:"text-2xl font-black uppercase tracking-wide max-w-5xl mx-auto",children:"These projects transform ROI into visible humanitarian monuments — immortalizing leadership through community revival."})})]})},VN=()=>e.jsx("section",{id:"charity",className:"section",children:e.jsxs("div",{className:"container",children:[e.jsxs("div",{className:"text-center mb-md",children:[e.jsx("h2",{className:"heading-xl text-gold mb-xs",children:"Charity & Community Impact"}),e.jsx("p",{className:"text-lg max-w-2xl mx-auto",children:"Aureus Alliance is committed to donating over $28,000,000 to charities worldwide. A core tenant of our philosophy is creating legacy projects that deliver lasting, positive change far beyond financial returns."})]}),e.jsx(qN,{})]})}),GN=()=>{const l=["A 2020 geotechnical report confirms the site's viability.","Managed in-house with an experienced African operator.","High-efficiency plant from a firm with 32+ years of expertise.","Secure sales process directly to Fidelity Gold Refinery."];return e.jsx("section",{id:"project",className:"section",children:e.jsxs("div",{className:"container",children:[e.jsxs("div",{className:"text-center mb-md",children:[e.jsx("h2",{className:"heading-xl text-gold mb-xs",children:"The Land & Project Status"}),e.jsx("p",{className:"text-lg max-w-2xl mx-auto",children:"A secured250-hectare mining block confirmed for viability and primed for scalable, high-yield gold mining."})]}),e.jsxs("div",{className:"grid grid-2 gap-md items-center",children:[e.jsxs("div",{className:"card",children:[e.jsx("h3",{className:"heading-md text-gold mb-sm",children:"Strategic Viability"}),e.jsx("p",{className:"text-sm mb-sm",children:"The licensed area is in Kadoma, a region renowned for rich gold placer deposits. Its connectivity ensures smooth logistics and uninterrupted operations."}),e.jsx("ul",{className:"space-y-xs",children:l.map((r,t)=>e.jsxs("li",{className:"flex items-start gap-xs text-sm",children:[e.jsx(fd,{className:"text-amber-500 mt-0.5 flex-shrink-0 w-4 h-4"}),e.jsx("span",{children:r})]},t))})]}),e.jsx("div",{children:e.jsx("img",{src:"https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/land-prospect.png",alt:"Aureus Alliance Holdings 250-Hectare Gold Mining Concession - Satellite View of Licensed Mining Area in Kadoma, Zimbabwe",className:"rounded w-full"})})]})]})})},WN=()=>{const[l,r]=K.useState(100),t=[{site:"Existing 250-hectare block",kg:2400,val100:24e7,val150:36e7},{site:"Mutare Expansion (47 sites)",kg:7600,val100:76e7,val150:114e7}],s={kg:t.reduce((o,c)=>o+c.kg,0),val100:t.reduce((o,c)=>o+c.val100,0),val150:t.reduce((o,c)=>o+c.val150,0)},n=()=>{const o=[{price:100,label:"$100k/kg"},{price:110,label:"$110k/kg"},{price:120,label:"$120k/kg"},{price:130,label:"$130k/kg"},{price:140,label:"$140k/kg"},{price:150,label:"$150k/kg"}],c=()=>t.map(h=>({...h,currentValue:h.kg*l*1e3})),u=()=>s.kg*l*1e3;return e.jsxs("div",{className:"projection-tabs-container",children:[e.jsx("div",{className:"tab-navigation",children:e.jsx("div",{className:"tab-scroll-container",children:o.map(h=>e.jsx("button",{onClick:()=>r(h.price),className:`tab-button ${l===h.price?"tab-active":"tab-inactive"}`,children:h.label},h.price))})}),e.jsxs("div",{className:"tab-content",children:[e.jsx("div",{className:"site-values-grid",children:c().map(h=>e.jsxs("div",{className:"site-value-card",children:[e.jsxs("div",{className:"site-header",children:[e.jsx("h4",{className:"site-title",children:h.site}),e.jsxs("div",{className:"site-badge",children:[Oa(h.kg)," KG"]})]}),e.jsxs("div",{className:"site-value",children:[e.jsx("span",{className:"value-label",children:"Site Value"}),e.jsx("span",{className:"value-amount",children:Oa(h.currentValue,{style:"currency",currency:"USD",minimumFractionDigits:0})})]})]},h.site))}),e.jsxs("div",{className:"portfolio-summary",children:[e.jsxs("div",{className:"summary-header",children:[e.jsx("h4",{className:"summary-title",children:"Portfolio Summary"}),e.jsx("div",{className:"summary-subtitle",children:"Total across all mining sites"})]}),e.jsxs("div",{className:"summary-metrics",children:[e.jsxs("div",{className:"summary-metric",children:[e.jsx("span",{className:"metric-value",children:Oa(s.kg)}),e.jsx("span",{className:"metric-label",children:"Total KG"})]}),e.jsx("div",{className:"summary-divider"}),e.jsxs("div",{className:"summary-metric primary",children:[e.jsx("span",{className:"metric-value",children:Oa(u(),{style:"currency",currency:"USD",minimumFractionDigits:0})}),e.jsx("span",{className:"metric-label",children:"Total Portfolio Value"})]})]})]})]})]})};return e.jsx("section",{id:"financials",className:"section",children:e.jsxs("div",{className:"container",children:[e.jsxs("div",{className:"text-center mb-md",children:[e.jsx("h2",{className:"heading-xl text-gold mb-xs",children:"Financials & Share Value"}),e.jsx("p",{className:"text-lg max-w-2xl mx-auto",children:"Conservative estimates based on geological data, providing a baseline for forecasting and share purchasing decisions."})]}),e.jsxs("div",{className:"resource-estimates-modern",children:[e.jsxs("div",{className:"resource-header",children:[e.jsx("h3",{className:"resource-title",children:"Resource Estimates (In Situ)"}),e.jsx("div",{className:"resource-subtitle",children:"Conservative geological data baseline"})]}),e.jsxs("div",{className:"resource-grid",children:[e.jsxs("div",{className:"resource-card",children:[e.jsx("div",{className:"resource-icon",children:"📏"}),e.jsxs("div",{className:"resource-content",children:[e.jsx("div",{className:"resource-label",children:"Area Extent"}),e.jsx("div",{className:"resource-value",children:"250 ha"})]})]}),e.jsxs("div",{className:"resource-card",children:[e.jsx("div",{className:"resource-icon",children:"📐"}),e.jsxs("div",{className:"resource-content",children:[e.jsx("div",{className:"resource-label",children:"Gravel Thickness"}),e.jsx("div",{className:"resource-value",children:"0.8 m"})]})]}),e.jsxs("div",{className:"resource-card",children:[e.jsx("div",{className:"resource-icon",children:"📦"}),e.jsxs("div",{className:"resource-content",children:[e.jsx("div",{className:"resource-label",children:"Volume (m³)"}),e.jsx("div",{className:"resource-value",children:"2,400,000"})]})]}),e.jsxs("div",{className:"resource-card",children:[e.jsx("div",{className:"resource-icon",children:"⚖️"}),e.jsxs("div",{className:"resource-content",children:[e.jsx("div",{className:"resource-label",children:"Grade (g/m³)"}),e.jsx("div",{className:"resource-value",children:"0.9"})]})]}),e.jsxs("div",{className:"resource-card highlight",children:[e.jsx("div",{className:"resource-icon",children:"🏆"}),e.jsxs("div",{className:"resource-content",children:[e.jsx("div",{className:"resource-label",children:"Potential Gold Output"}),e.jsx("div",{className:"resource-value gold",children:"2,160 kg"})]})]})]})]}),e.jsxs("div",{className:"projection-section-modern",children:[e.jsxs("div",{className:"projection-header",children:[e.jsx("h3",{className:"text-xl font-bold text-gold mb-2",children:"Projected Site Values (Pre-Expense)"}),e.jsx("p",{className:"text-sm text-gray-400 mb-6",children:"Based on figures from the prospectus - select gold price per kg"})]}),e.jsx(n,{})]})]})})},HN=()=>e.jsx(DN,{}),KN=()=>e.jsx("section",{id:"gallery",className:"section",children:e.jsxs("div",{className:"container",children:[e.jsxs("div",{className:"text-center mb-md",children:[e.jsx("h2",{className:"heading-xl text-gold mb-xs",children:"Proof of Concept & Gallery"}),e.jsx("p",{className:"text-lg max-w-2xl mx-auto",children:"A visual journey through our on-site activities, team collaborations, and project milestones."})]}),e.jsx("div",{className:"gallery-container",children:e.jsx(Aw,{showCategories:!1,showSearch:!1,itemsPerPage:9,showFeaturedFirst:!0,fallbackMode:"static",showConnectionStatus:!1})})]})}),YN=({setCurrentSection:l,user:r,handleSwitchToRegister:t})=>e.jsx("footer",{className:"footer-container",children:e.jsx("div",{className:"container",children:e.jsxs("div",{className:"footer-content",children:[e.jsx("div",{className:"footer-logo",children:"AUREUS"}),e.jsxs("nav",{className:"footer-nav",children:[e.jsx(zr,{href:"#about",children:"About"}),e.jsx(zr,{href:"#highlights",children:"Highlights"}),e.jsx(zr,{href:"#commission",children:"Commission"}),e.jsx(zr,{href:"#gold-diggers-club",children:"Gold Diggers Club"}),e.jsx(zr,{href:"#calculator",children:"Calculator"}),e.jsx(zr,{href:"#project",children:"Project"}),e.jsx(zr,{href:"#gallery",children:"Gallery"}),e.jsx(zr,{href:"#charity",children:"Charity"})]}),e.jsx("div",{className:"footer-cta",children:e.jsx("button",{onClick:t,className:"btn btn-primary",children:"Purchase Shares Now"})}),e.jsxs("div",{className:"footer-legal",children:[e.jsxs("div",{className:"footer-legal-nav",children:[e.jsx("a",{href:"#privacy-policy",onClick:s=>{s.preventDefault(),l("privacy-policy")},children:"Privacy Policy"}),e.jsx("a",{href:"#terms-conditions",onClick:s=>{s.preventDefault(),l("terms-conditions")},children:"Terms & Conditions"}),e.jsx("a",{href:"#disclaimer",onClick:s=>{s.preventDefault(),l("disclaimer")},children:"Disclaimer"})]}),e.jsxs("p",{children:["© ",new Date().getFullYear()," Aureus Alliance Holdings. All rights reserved."]}),e.jsx("p",{children:"Disclaimer: This website and the information contained herein are for informational purposes only and do not constitute an offer to sell or a solicitation of an offer to purchase any shares."})]})]})})}),JN=()=>{var l;const[r,t]=K.useState(!1),[s,n]=K.useState("home"),[o,c]=K.useState(null),[u,h]=K.useState("login"),[f,x]=K.useState(!0);K.useEffect(()=>{const T=window.location.pathname,P=new URLSearchParams(window.location.search);if(T==="/login"||T==="/register")n("account-access"),h(T==="/register"?"register":"login");else if(T==="/reset-password")n("account-access"),h("login"),window.history.pushState({},"","/login");else if(T==="/sync"){const z=P.get("token");z?(n("sync"),window.syncToken=z):(n("home"),window.history.pushState({},"","/"))}},[]),K.useEffect(()=>{const T=z=>{if(z.message&&z.message.includes("Expected number")&&z.message.includes("path"))return console.warn("Suppressed SVGpath error (likely from browser extension):",z.message),z.preventDefault(),!1;console.error("Global error:",z.error)},P=z=>{console.error("Unhandled promise rejection:",z.reason)};return window.addEventListener("error",T),window.addEventListener("unhandledrejection",P),()=>{window.removeEventListener("error",T),window.removeEventListener("unhandledrejection",P)}},[]);const{price:b,isLoading:y,error:g}=yj(),k=()=>{h("register"),n("account-access"),window.history.pushState({},"","/register")},S=async()=>{try{await dm(),localStorage.removeItem("aureus_telegram_user"),localStorage.removeItem("aureus_test_user"),localStorage.removeItem("aureus-remember-me"),c(null),n("home"),console.log("✅ Logged out successfully")}catch(T){console.error("❌ Logout error:",T),localStorage.removeItem("aureus_telegram_user"),localStorage.removeItem("aureus_test_user"),localStorage.removeItem("aureus-remember-me"),c(null),n("home")}},A=()=>{localStorage.removeItem("aureus_telegram_user"),localStorage.removeItem("aureus_test_user"),c(null),n("home"),console.log("🧹 localStorage cleared")};let w;try{w=I1().getContent}catch{console.warn("App: Site content context not available, using defaults"),w=(T,P,z="")=>z}const C=w("calculator","title","Financial Calculator")||"Financial Calculator",R=w("calculator","subtitle","Experience the power of data-driven investment decisions with our interactive calculator")||"Experience the power of data-driven investment decisions with our interactive calculator";K.useEffect(()=>{(async()=>{var T,P,z;try{console.log("🔍 Checking for existing session..."),console.log("🔍 [DEBUG] localStorage aureus_telegram_user:",localStorage.getItem("aureus_telegram_user")),console.log("🔍 [DEBUG] localStorage aureus_test_user:",localStorage.getItem("aureus_test_user"));const $=localStorage.getItem("aureus_telegram_user");if($)try{const q=JSON.parse($);console.log("🔍 [DEBUG] Parsed telegram user from localStorage:",q),(!q.database_user||!q.database_user.telegram_id)&&(console.log("⚠️ [DEBUG] Corrupted telegram user data detected, clearing localStorage"),localStorage.removeItem("aureus_telegram_user"))}catch{console.log("⚠️ [DEBUG] Invalid JSON in localStorage, clearing"),localStorage.removeItem("aureus_telegram_user")}const H=await Ou();console.log("🔍 [DEBUG] getCurrentUser result:",H),H?(console.log("✅ Found existing session:",H),console.log("🔍 [DEBUG] currentUser.needsProfileCompletion:",H.needsProfileCompletion),console.log("🔍 [DEBUG] currentUser.user_metadata?.profile_completion_required:",(T=H.user_metadata)==null?void 0:T.profile_completion_required),console.log("🔍 [DEBUG] currentUser.database_user?.telegram_id:",(P=H.database_user)==null?void 0:P.telegram_id),c(H),H.needsProfileCompletion||(z=H.user_metadata)!=null&&z.profile_completion_required?(console.log("🔄 User needs profile completion, setting section to profile-completion"),n("profile-completion")):(console.log("✅ Profile complete, setting section to dashboard"),n("dashboard"))):console.log("ℹ️ No existing session found")}catch($){console.error("❌ Session check error:",$)}finally{x(!1)}})()},[]),K.useEffect(()=>{new URLSearchParams(window.location.search).get("admin")==="true"&&t(!0)},[]);const O=()=>{t(!1);const T=new URL(window.location.href);T.searchParams.delete("admin"),window.history.replaceState({},"",T.toString())};if(f)return e.jsx("div",{className:"bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white min-h-screen flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-300",children:"Checking session..."})]})});if(r)return e.jsx(gj,{onBackToMain:O});if(s==="privacy-policy")return e.jsx(nl,{children:e.jsx("div",{className:"legal-section",children:e.jsxs("div",{className:"container",children:[e.jsx("button",{className:"back-button",onClick:()=>n("home"),children:"← Back to Home"}),e.jsx(D1,{})]})})});if(s==="terms-conditions")return e.jsx(nl,{children:e.jsx("div",{className:"legal-section",children:e.jsxs("div",{className:"container",children:[e.jsx("button",{className:"back-button",onClick:()=>n("home"),children:"← Back to Home"}),e.jsx(T1,{})]})})});if(s==="disclaimer")return e.jsx(nl,{children:e.jsx("div",{className:"legal-section",children:e.jsxs("div",{className:"container",children:[e.jsx("button",{className:"back-button",onClick:()=>n("home"),children:"← Back to Home"}),e.jsx(vj,{})]})})});if(window.location.search.includes("csstest"))return e.jsx("div",{className:"bg-gradient-to-br from-gray-900 via-black to-gray-900text-white min-h-screen overflow-y-auto",children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"bg-gray-900/50 backdrop-blur-xl rounded-3xl p-8 border border-gray-700/30 shadow-2xl mb-8",children:e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"text-2xl font-bold text-white mb-2",children:"CSS TEST - Sign In to Your Account"}),e.jsx("p",{className:"text-gray-400",children:"Access your Aureus Alliance Holdings dashboard"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-semibold text-gray-300 mb-2",children:"Email Address"}),e.jsx("input",{type:"email",className:"w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-nonefocus:ring-2 focus:ring-gold-400 focus:border-transparent transition-all duration-300",placeholder:"<EMAIL>"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-semibold text-gray-300 mb-2",children:"Password"}),e.jsx("input",{type:"password",className:"w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:border-transparent transition-all duration-300",placeholder:"••••••••••••••••"})]}),e.jsx("button",{className:"w-full bg-gradient-to-r from-gold-400 to-gold-600 text-black font-bold py-3 px-6 rounded-xl hover:from-gold-500 hover:to-gold-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-gold-400/25",children:"Sign In - CSS TEST"}),e.jsx("button",{onClick:()=>window.location.search="",className:"w-full bg-gray-600 text-white font-bold py-2 px-4 rounded-xl hover:bg-gray-700 transition-all duration-300",children:"Back to App"})]})]})})})});if(s==="account-access")return e.jsx(nl,{children:e.jsx(Aj,{onAuthSuccess:T=>{console.log("✅ Unified auth success:",T),c(T),n("dashboard")},onBack:()=>{n("home"),window.history.pushState({},"","/")}})});if(s==="sync"){const T=window.syncToken;return o?e.jsx(nl,{children:e.jsx(Lj,{syncToken:T,user:o,onSyncComplete:()=>{n("dashboard"),window.history.pushState({},"","/dashboard"),loadUserData()},onBack:()=>{n("dashboard"),window.history.pushState({},"","/dashboard")}})}):(n("account-access"),null)}return s==="dashboard"&&o?(console.log("🔍 Dashboard route guard check:",{needsProfileCompletion:o.needsProfileCompletion,profile_completion_required:(l=o.user_metadata)==null?void 0:l.profile_completion_required,currentSection:s,userEmail:o.email,hasDatabase:!!o.database_user,databaseUserComplete:o.database_user?{hasEmail:!!o.database_user.email,hasPassword:!!o.database_user.password_hash,hasFullName:!!o.database_user.full_name,hasPhone:!!o.database_user.phone,hasCountry:!!o.database_user.country_of_residence}:null}),console.log("✅ Dashboard access granted - user authenticated"),e.jsx(nl,{children:e.jsx(EN,{user:o,onLogout:S,onNavigate:n})})):e.jsx(nl,{children:e.jsxs("div",{className:"bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white min-h-screen",children:[e.jsxs("div",{className:"fixed inset-0 overflow-hidden pointer-events-none",children:[e.jsx("div",{className:"absolute top-1/4 left-1/4 w-96 h-96 bg-amber-500/5 rounded-fullblur-3xl animate-pulse"}),e.jsx("div",{className:"absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-pulse",style:{animationDelay:"1s"}}),e.jsx("div",{className:"absolute top-3/4 left-3/4 w-64 h-64 bg-purple-500/5 rounded-full blur-3xl animate-pulse",style:{animationDelay:"2s"}}),e.jsx("div",{className:"absolute top-1/2 left-1/2 w-32 h-32 bg-green-500/5 rounded-full blur-2xl animate-pulse",style:{animationDelay:"3s"}})]}),e.jsx(zN,{setCurrentSection:n,user:o,clearLocalStorage:A,handleSwitchToRegister:k}),e.jsxs("main",{className:"relative z-10",children:[e.jsx(BN,{}),e.jsxs("div",{className:"relative",children:[e.jsx(UN,{}),e.jsx("section",{id:"commission",className:"section",children:e.jsx("div",{className:"container",children:e.jsx(IN,{onNavigateToRegister:k})})}),e.jsx($N,{}),e.jsx(VN,{}),e.jsx(HN,{}),e.jsx("section",{id:"calculator",className:"section",children:e.jsxs("div",{className:"container",children:[e.jsxs("div",{className:"text-center mb-md",children:[e.jsx("h2",{className:"heading-xl text-gold mb-xs",children:C}),e.jsx("p",{className:"text-lg max-w-2xl mx-auto",children:R})]}),e.jsx(MN,{liveGoldPrice:b,goldPriceLoading:y,goldPriceError:g})]})}),e.jsx(GN,{}),e.jsx(WN,{}),e.jsx(KN,{})]}),e.jsx(YN,{setCurrentSection:n,user:o,handleSwitchToRegister:k})]})]})})},O1=document.getElementById("root");if(!O1)throw new Error("Could not find root element to mount to");const XN=sw.createRoot(O1);XN.render(e.jsx(Kr.StrictMode,{children:e.jsx(nl,{children:e.jsx(bj,{children:e.jsx(JN,{})})})}));export{Ar as A,Rh as I,lm as o};
root@server /home/<USER>/public_html $