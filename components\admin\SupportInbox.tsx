import React, { useEffect, useMemo, useState } from 'react'
import { getServiceRoleClient } from '../../lib/supabase'
import { sendSupportMessageAsAdmin, fetchConversation } from '../../lib/support'

interface User { id: number; username: string; email: string; full_name: string | null }
interface Msg { id: string; sender_id: number; recipient_id: number; subject: string; message: string; created_at: string }

const SupportInbox: React.FC<{ adminUser?: any }> = ({ adminUser }) => {
  const [loading, setLoading] = useState(true)
  const [threads, setThreads] = useState<Array<{ user: User; last: Msg }>>([])
  const [activeUser, setActiveUser] = useState<User | null>(null)
  const [conversation, setConversation] = useState<Msg[]>([])
  const [reply, setReply] = useState('')
  const [adminUserId, setAdminUserId] = useState<number | null>(null)

  useEffect(() => {
    const init = async () => {
      setLoading(true)
      // Resolve adminUserId by email mapping to users table
      const service = getServiceRoleClient()
      const { data: adminMatch } = await service
        .from('users')
        .select('id, email')
        .eq('email', (adminUser?.email || '').toLowerCase())
        .maybeSingle()
      setAdminUserId(adminMatch?.id || null)

      // Build threads from latest messages grouped by user
      const { data: latest } = await service
        .from('user_messages')
        .select('id, sender_id, recipient_id, subject, message, created_at')
        .order('created_at', { ascending: false })
        .limit(200)

      const userIds = new Set<number>()
      ;(latest || []).forEach((m: any) => {
        const other = m.sender_id === adminMatch?.id ? m.recipient_id : m.sender_id
        userIds.add(other)
      })

      const { data: users } = await service
        .from('users')
        .select('id, username, email, full_name')
        .in('id', Array.from(userIds))

      const byUser: Record<number, { user: User; last: Msg }> = {}
      ;(latest || []).forEach((m: any) => {
        const other = m.sender_id === adminMatch?.id ? m.recipient_id : m.sender_id
        const u = (users || []).find((x: any) => x.id === other)
        if (u && !byUser[other]) {
          byUser[other] = { user: u, last: m }
        }
      })
      setThreads(Object.values(byUser))
      setLoading(false)
    }
    init()
  }, [adminUser?.email])

  const openThread = async (u: User) => {
    setActiveUser(u)
    if (!adminUserId) return
    const conv = await fetchConversation(u.id, adminUserId)
    setConversation(conv as any)
  }

  const send = async () => {
    if (!reply.trim() || !adminUserId || !activeUser) return
    const text = reply.trim()
    setReply('')
    await sendSupportMessageAsAdmin(adminUserId, activeUser.id, text)
    const conv = await fetchConversation(activeUser.id, adminUserId)
    setConversation(conv as any)
  }

  return (
    <div className="space-y-4">
      <h3 className="text-xl font-semibold text-gray-900">Support Inbox</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="md:col-span-1 bg-white border rounded p-3">
          <h4 className="font-medium mb-2">Recent Threads</h4>
          {loading ? (
            <div className="text-gray-500">Loading…</div>
          ) : threads.length === 0 ? (
            <div className="text-gray-500">No messages yet</div>
          ) : (
            <ul className="divide-y">
              {threads.map(t => (
                <li key={t.user.id} className="py-2 cursor-pointer hover:bg-gray-50 px-2 rounded" onClick={() => openThread(t.user)}>
                  <div className="font-medium">{t.user.full_name || t.user.username}</div>
                  <div className="text-xs text-gray-500 truncate">{t.last.message}</div>
                </li>
              ))}
            </ul>
          )}
        </div>
        <div className="md:col-span-2 bg-white border rounded p-3">
          {activeUser ? (
            <div className="flex flex-col h-96">
              <div className="border-b pb-2 mb-2">
                <div className="font-medium">Chat with {activeUser.full_name || activeUser.username} (ID {activeUser.id})</div>
              </div>
              <div className="flex-1 overflow-y-auto space-y-2">
                {conversation.map(m => (
                  <div key={m.id} className={`max-w-[80%] px-3 py-2 rounded text-sm ${m.sender_id === adminUserId ? 'bg-blue-600 text-white ml-auto' : 'bg-gray-100 text-gray-900'}`}>
                    <div>{m.message}</div>
                    <div className="text-[10px] opacity-70 mt-1">{new Date(m.created_at).toLocaleString()}</div>
                  </div>
                ))}
              </div>
              <div className="mt-2 flex gap-2">
                <input className="flex-1 border rounded px-3 py-2" placeholder="Type reply…" value={reply} onChange={e => setReply(e.target.value)} onKeyDown={e => { if (e.key === 'Enter') send() }} />
                <button className="px-4 py-2 bg-blue-600 text-white rounded" onClick={send}>Send</button>
              </div>
            </div>
          ) : (
            <div className="text-gray-500">Select a thread to view messages</div>
          )}
        </div>
      </div>
    </div>
  )
}

export default SupportInbox

