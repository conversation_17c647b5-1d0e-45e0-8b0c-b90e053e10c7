import React, { useState, useEffect } from 'react';
import { EmailVerificationModal } from '../EmailVerificationModal';
import { onboardingService } from '../../lib/services/onboardingService';

interface EmailVerificationStepProps {
  userId: number;
  userEmail: string;
  onStepComplete: () => void;
  onStepStart?: () => void;
}

export const EmailVerificationStep: React.FC<EmailVerificationStepProps> = ({
  userId,
  userEmail,
  onStepComplete,
  onStepStart
}) => {
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isCheckingVerification, setIsCheckingVerification] = useState(true);

  // Check if email is still loading
  const isEmailLoading = !userEmail || userEmail === 'Loading...';
  const isValidEmail = userEmail && userEmail !== 'Loading...' && userEmail.includes('@');

  // Check if user already verified email during registration
  useEffect(() => {
    const checkExistingVerification = async () => {
      if (!userId || isEmailLoading) {
        setIsCheckingVerification(false);
        return;
      }

      try {
        // Check if user already has verified email by checking if they registered with PIN
        // If they registered successfully, their email is already verified
        console.log('📧 Checking if email already verified during registration...');

        // Auto-complete the step since user already verified during registration
        setSuccess('Email already verified during registration! ✅');
        setIsCheckingVerification(false);

        // Auto-complete the step after a short delay
        setTimeout(() => {
          onStepComplete();
        }, 1500);

      } catch (error) {
        console.error('Error checking email verification:', error);
        setIsCheckingVerification(false);
      }
    };

    checkExistingVerification();
  }, [userId, isEmailLoading, onStepComplete]);

  const handleStartVerification = async () => {
    // Validate email before proceeding
    if (!isValidEmail) {
      setError('Please ensure you have a valid email address before proceeding.');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Start the onboarding step
      const stepStarted = await onboardingService.startStep(userId, 'email_verification');

      if (!stepStarted) {
        throw new Error('Failed to start email verification step');
      }

      // Notify parent component
      if (onStepStart) {
        onStepStart();
      }

      // Open verification modal (this will auto-send the email)
      setShowVerificationModal(true);

    } catch (error) {
      console.error('Failed to start email verification:', error);
      setError(error instanceof Error ? error.message : 'Failed to start email verification');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerificationSuccess = async () => {
    setIsLoading(true);
    
    try {
      // Complete the onboarding step
      const stepCompleted = await onboardingService.completeStep(userId, 'email_verification', {
        verified_at: new Date().toISOString(),
        email: userEmail
      });

      if (!stepCompleted) {
        throw new Error('Failed to complete email verification step');
      }

      setSuccess('Email verified successfully! 🎉');
      setShowVerificationModal(false);
      
      // Notify parent component
      setTimeout(() => {
        onStepComplete();
      }, 1500);

    } catch (error) {
      console.error('Failed to complete email verification:', error);
      setError(error instanceof Error ? error.message : 'Failed to complete email verification');
    } finally {
      setIsLoading(false);
    }
  };

  const handleModalClose = () => {
    setShowVerificationModal(false);
  };

  return (
    <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
      <div className="flex items-start gap-4">
        <div className="text-4xl">📧</div>
        <div className="flex-1">
          <h3 className="text-xl font-semibold text-white mb-2">
            Verify Email Address
          </h3>
          <p className="text-gray-400 mb-4">
            Confirm your email address to secure your account and unlock notifications.
          </p>
          
          <div className="bg-gray-700 rounded-lg p-4 mb-4">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-blue-400">📧</span>
              <span className="text-white font-medium">Email Address:</span>
            </div>
            {isEmailLoading ? (
              <div className="flex items-center gap-2 ml-6">
                <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                <span className="text-gray-400">Loading email address...</span>
              </div>
            ) : (
              <p className="text-gray-300 ml-6">{userEmail}</p>
            )}
          </div>

          {!isEmailLoading && !isValidEmail && userEmail !== 'Loading...' && (
            <div className="bg-red-900/30 border border-red-500/30 rounded-lg p-3 mb-4">
              <div className="flex items-center gap-2">
                <span className="text-red-400">❌</span>
                <span className="text-red-400 text-sm">
                  Unable to load your email address. Please refresh the page or contact support.
                </span>
              </div>
            </div>
          )}

          {error && (
            <div className="bg-red-900/30 border border-red-500/30 rounded-lg p-3 mb-4">
              <div className="flex items-center gap-2">
                <span className="text-red-400">❌</span>
                <span className="text-red-400 text-sm">{error}</span>
              </div>
            </div>
          )}

          {success && (
            <div className="bg-green-900/30 border border-green-500/30 rounded-lg p-3 mb-4">
              <div className="flex items-center gap-2">
                <span className="text-green-400">✅</span>
                <span className="text-green-400 text-sm">{success}</span>
              </div>
            </div>
          )}

          <div className="space-y-3">
            <h4 className="text-white font-medium">What happens next:</h4>
            <div className="space-y-2 text-sm text-gray-400">
              <div className="flex items-center gap-2">
                <span className="text-blue-400">1.</span>
                <span>We'll send a 6-digit verification code to your email</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-blue-400">2.</span>
                <span>Enter the code in the verification modal</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-blue-400">3.</span>
                <span>Your email will be verified and notifications unlocked</span>
              </div>
            </div>
          </div>

          <div className="mt-6">
            <button
              onClick={handleStartVerification}
              disabled={isLoading || success !== null || isEmailLoading || !isValidEmail || isCheckingVerification}
              className={`px-6 py-3 rounded-lg font-medium transition-all ${
                success
                  ? 'bg-green-600 text-white cursor-not-allowed'
                  : isLoading || isEmailLoading || !isValidEmail || isCheckingVerification
                  ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                  : 'bg-gold hover:bg-yellow-600 text-black hover:shadow-lg'
              }`}
            >
              {isCheckingVerification ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                  <span>Checking verification status...</span>
                </div>
              ) : isEmailLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                  <span>Loading Email...</span>
                </div>
              ) : !isValidEmail ? (
                <div className="flex items-center gap-2">
                  <span>❌</span>
                  <span>Invalid Email Address</span>
                </div>
              ) : isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                  <span>Starting Verification...</span>
                </div>
              ) : success ? (
                <div className="flex items-center gap-2">
                  <span>✅</span>
                  <span>Email Verified!</span>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <span>📧</span>
                  <span>Send Verification Email</span>
                </div>
              )}
            </button>
          </div>

          <div className="mt-4 p-3 bg-blue-900/20 border border-blue-500/30 rounded-lg">
            <div className="flex items-start gap-2">
              <span className="text-blue-400 text-sm">💡</span>
              <div className="text-blue-400 text-sm">
                <p className="font-medium mb-1">Why verify your email?</p>
                <ul className="space-y-1 text-xs">
                  <li>• Secure password reset functionality</li>
                  <li>• Important account notifications</li>
                  <li>• Commission and dividend updates</li>
                  <li>• Enhanced account security</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Email Verification Modal */}
      {showVerificationModal && isValidEmail && (
        <EmailVerificationModal
          isOpen={showVerificationModal}
          onClose={handleModalClose}
          onVerificationSuccess={handleVerificationSuccess}
          userId={userId}
          email={userEmail}
          purpose="account_update"
          title="Verify Your Email Address"
          description="We've sent a 6-digit verification code to your email address. Please enter it below to complete the verification process."
          autoSendCode={true}
        />
      )}
    </div>
  );
};
