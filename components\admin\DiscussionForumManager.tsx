/**
 * DISCUSSION FORUM MANAGER
 * 
 * Course discussion and Q&A management system:
 * - Thread creation and management
 * - Reply system with nested comments
 * - Voting and rating system
 * - Moderation tools and content filtering
 * - Real-time notifications
 * - Search and categorization
 */

import React, { useState, useEffect } from 'react';
import { getServiceRoleClient } from '../../lib/supabase';

interface DiscussionForumManagerProps {
  courseId: number;
  lessonId?: number;
  currentUser: any;
}

interface DiscussionThread {
  id: number;
  course_id: number;
  lesson_id?: number;
  user_id: number;
  title: string;
  content: string;
  is_pinned: boolean;
  is_locked: boolean;
  reply_count: number;
  vote_score: number;
  created_at: string;
  updated_at: string;
  user_name: string;
  user_type: string;
  replies?: DiscussionReply[];
}

interface DiscussionReply {
  id: number;
  thread_id: number;
  user_id: number;
  content: string;
  parent_reply_id?: number;
  vote_score: number;
  is_solution: boolean;
  created_at: string;
  user_name: string;
  user_type: string;
  replies?: DiscussionReply[];
}

export const DiscussionForumManager: React.FC<DiscussionForumManagerProps> = ({
  courseId,
  lessonId,
  currentUser
}) => {
  const [threads, setThreads] = useState<DiscussionThread[]>([]);
  const [loading, setLoading] = useState(true);
  const [showNewThread, setShowNewThread] = useState(false);
  const [selectedThread, setSelectedThread] = useState<DiscussionThread | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'recent' | 'popular' | 'unanswered'>('recent');

  const [newThread, setNewThread] = useState({
    title: '',
    content: '',
    is_pinned: false
  });

  useEffect(() => {
    loadThreads();
  }, [courseId, lessonId, sortBy]);

  const loadThreads = async () => {
    try {
      setLoading(true);
      const serviceClient = getServiceRoleClient();
      
      let query = serviceClient
        .from('training_discussions')
        .select(`
          *,
          users!training_discussions_user_id_fkey(username, user_type)
        `)
        .eq('course_id', courseId);

      if (lessonId) {
        query = query.eq('lesson_id', lessonId);
      }

      // Apply sorting
      switch (sortBy) {
        case 'popular':
          query = query.order('vote_score', { ascending: false });
          break;
        case 'unanswered':
          query = query.eq('reply_count', 0).order('created_at', { ascending: false });
          break;
        default:
          query = query.order('created_at', { ascending: false });
      }

      const { data, error } = await query;

      if (error) throw error;

      const formattedThreads = data?.map(thread => ({
        ...thread,
        user_name: thread.users?.username || 'Unknown User',
        user_type: thread.users?.user_type || 'user'
      })) || [];

      setThreads(formattedThreads);
    } catch (error) {
      console.error('Error loading threads:', error);
    } finally {
      setLoading(false);
    }
  };

  const createThread = async () => {
    try {
      if (!newThread.title.trim() || !newThread.content.trim()) {
        alert('Please fill in both title and content.');
        return;
      }

      const serviceClient = getServiceRoleClient();
      const { data, error } = await serviceClient
        .from('training_discussions')
        .insert({
          course_id: courseId,
          lesson_id: lessonId,
          user_id: currentUser.id,
          title: newThread.title,
          content: newThread.content,
          is_pinned: newThread.is_pinned,
          is_locked: false,
          reply_count: 0,
          vote_score: 0
        })
        .select()
        .single();

      if (error) throw error;

      // Reset form and reload threads
      setNewThread({ title: '', content: '', is_pinned: false });
      setShowNewThread(false);
      loadThreads();

    } catch (error) {
      console.error('Error creating thread:', error);
      alert('Failed to create thread. Please try again.');
    }
  };

  const toggleThreadPin = async (threadId: number, isPinned: boolean) => {
    try {
      const serviceClient = getServiceRoleClient();
      const { error } = await serviceClient
        .from('training_discussions')
        .update({ is_pinned: !isPinned })
        .eq('id', threadId);

      if (error) throw error;
      loadThreads();
    } catch (error) {
      console.error('Error toggling pin:', error);
    }
  };

  const toggleThreadLock = async (threadId: number, isLocked: boolean) => {
    try {
      const serviceClient = getServiceRoleClient();
      const { error } = await serviceClient
        .from('training_discussions')
        .update({ is_locked: !isLocked })
        .eq('id', threadId);

      if (error) throw error;
      loadThreads();
    } catch (error) {
      console.error('Error toggling lock:', error);
    }
  };

  const deleteThread = async (threadId: number) => {
    if (!confirm('Are you sure you want to delete this thread? This action cannot be undone.')) {
      return;
    }

    try {
      const serviceClient = getServiceRoleClient();
      const { error } = await serviceClient
        .from('training_discussions')
        .delete()
        .eq('id', threadId);

      if (error) throw error;
      loadThreads();
    } catch (error) {
      console.error('Error deleting thread:', error);
      alert('Failed to delete thread. Please try again.');
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)} hours ago`;
    } else if (diffInHours < 168) {
      return `${Math.floor(diffInHours / 24)} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const filteredThreads = threads.filter(thread =>
    thread.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    thread.content.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-3 text-gray-400">Loading discussions...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-white">💬 Course Discussions</h2>
          <p className="text-gray-400 mt-1">
            {lessonId ? 'Lesson-specific discussions' : 'Course-wide discussions'}
          </p>
        </div>
        <button
          onClick={() => setShowNewThread(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
        >
          ➕ New Thread
        </button>
      </div>

      {/* Search and Filters */}
      <div className="bg-gray-800 rounded-lg p-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search discussions..."
              className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div className="flex space-x-3">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="recent">Most Recent</option>
              <option value="popular">Most Popular</option>
              <option value="unanswered">Unanswered</option>
            </select>
          </div>
        </div>
      </div>

      {/* New Thread Modal */}
      {showNewThread && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
            <div className="bg-gray-700 px-6 py-4 border-b border-gray-600">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-bold text-white">Create New Discussion Thread</h3>
                <button
                  onClick={() => setShowNewThread(false)}
                  className="text-gray-400 hover:text-white text-2xl"
                >
                  ×
                </button>
              </div>
            </div>

            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Thread Title *
                </label>
                <input
                  type="text"
                  value={newThread.title}
                  onChange={(e) => setNewThread(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter thread title..."
                  maxLength={200}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Content *
                </label>
                <textarea
                  value={newThread.content}
                  onChange={(e) => setNewThread(prev => ({ ...prev, content: e.target.value }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Write your question or discussion topic..."
                  rows={6}
                  maxLength={2000}
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="pin_thread"
                  checked={newThread.is_pinned}
                  onChange={(e) => setNewThread(prev => ({ ...prev, is_pinned: e.target.checked }))}
                  className="mr-2"
                />
                <label htmlFor="pin_thread" className="text-sm text-gray-300">
                  Pin this thread to the top
                </label>
              </div>
            </div>

            <div className="bg-gray-700 px-6 py-4 border-t border-gray-600 flex justify-end space-x-3">
              <button
                onClick={() => setShowNewThread(false)}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={createThread}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Create Thread
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Threads List */}
      <div className="space-y-4">
        {filteredThreads.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-4xl mb-4">💬</div>
            <h3 className="text-xl font-semibold text-white mb-2">No discussions yet</h3>
            <p className="text-gray-400 mb-4">
              {searchQuery ? 'No threads match your search.' : 'Be the first to start a discussion!'}
            </p>
            {!searchQuery && (
              <button
                onClick={() => setShowNewThread(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
              >
                Start Discussion
              </button>
            )}
          </div>
        ) : (
          filteredThreads.map((thread) => (
            <div key={thread.id} className="bg-gray-800 rounded-lg p-6 hover:bg-gray-750 transition-colors">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    {thread.is_pinned && (
                      <span className="bg-yellow-600 text-yellow-100 text-xs px-2 py-1 rounded-full">
                        📌 Pinned
                      </span>
                    )}
                    {thread.is_locked && (
                      <span className="bg-red-600 text-red-100 text-xs px-2 py-1 rounded-full">
                        🔒 Locked
                      </span>
                    )}
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      thread.user_type === 'admin' 
                        ? 'bg-purple-600 text-purple-100' 
                        : 'bg-gray-600 text-gray-100'
                    }`}>
                      {thread.user_type === 'admin' ? '👑 Admin' : '👤 User'}
                    </span>
                  </div>

                  <h3 className="text-lg font-semibold text-white mb-2 hover:text-blue-400 cursor-pointer">
                    {thread.title}
                  </h3>

                  <p className="text-gray-400 mb-3 line-clamp-2">
                    {thread.content}
                  </p>

                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <span>By {thread.user_name}</span>
                    <span>•</span>
                    <span>{formatDate(thread.created_at)}</span>
                    <span>•</span>
                    <span>{thread.reply_count} replies</span>
                    <span>•</span>
                    <span className="flex items-center space-x-1">
                      <span>👍</span>
                      <span>{thread.vote_score}</span>
                    </span>
                  </div>
                </div>

                {/* Admin Actions */}
                <div className="flex items-center space-x-2 ml-4">
                  <button
                    onClick={() => toggleThreadPin(thread.id, thread.is_pinned)}
                    className={`text-sm px-2 py-1 rounded ${
                      thread.is_pinned 
                        ? 'text-yellow-400 hover:text-yellow-300' 
                        : 'text-gray-400 hover:text-gray-300'
                    }`}
                    title={thread.is_pinned ? 'Unpin' : 'Pin'}
                  >
                    📌
                  </button>
                  <button
                    onClick={() => toggleThreadLock(thread.id, thread.is_locked)}
                    className={`text-sm px-2 py-1 rounded ${
                      thread.is_locked 
                        ? 'text-red-400 hover:text-red-300' 
                        : 'text-gray-400 hover:text-gray-300'
                    }`}
                    title={thread.is_locked ? 'Unlock' : 'Lock'}
                  >
                    🔒
                  </button>
                  <button
                    onClick={() => deleteThread(thread.id)}
                    className="text-red-400 hover:text-red-300 text-sm px-2 py-1 rounded"
                    title="Delete"
                  >
                    🗑️
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};
