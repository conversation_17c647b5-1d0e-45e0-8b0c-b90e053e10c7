// High-Performance Certificate Generation Engine
// Generates professional PDF/PNG certificates with security elements and QR codes

import jsPDF from 'jspdf';
import QRCode from 'qrcode';
import { 
  CertificateData, 
  CertificateTemplate, 
  CertificateElement,
  SecurityElement,
  CertificateTemplateEngine,
  AUREUS_CERTIFICATE_TEMPLATE 
} from './certificateTemplate';

export interface GenerationOptions {
  format: 'pdf' | 'png' | 'both';
  quality: 'standard' | 'high' | 'print';
  includeMetadata: boolean;
  watermark: boolean;
  compression: boolean;
}

export interface GenerationResult {
  success: boolean;
  certificateNumber: string;
  pdfBuffer?: Buffer;
  pngBuffer?: Buffer;
  fileSize: number;
  generationTime: number;
  metadata: CertificateMetadata;
  error?: string;
}

export interface CertificateMetadata {
  generatedAt: string;
  generatedBy: string;
  certificateNumber: string;
  userId: number;
  template: string;
  securityHash: string;
  verificationUrl: string;
}

export class CertificateGenerator {
  private template: CertificateTemplate;
  private canvas: HTMLCanvasElement | null = null;
  private ctx: CanvasRenderingContext2D | null = null;
  private fontCache: Map<string, boolean> = new Map();
  private qrCodeCache: Map<string, string> = new Map();

  constructor(template: CertificateTemplate = AUREUS_CERTIFICATE_TEMPLATE) {
    this.template = template;
    this.initializeCanvas();
    this.preloadFonts();
  }

  private initializeCanvas(): void {
    if (typeof window !== 'undefined') {
      this.canvas = document.createElement('canvas');
      this.canvas.width = this.template.width;
      this.canvas.height = this.template.height;
      this.ctx = this.canvas.getContext('2d');

      // Enable hardware acceleration if available
      if (this.ctx) {
        this.ctx.imageSmoothingEnabled = true;
        this.ctx.imageSmoothingQuality = 'high';
      }
    }
  }

  /**
   * Preload fonts for better performance
   */
  private async preloadFonts(): Promise<void> {
    if (typeof window === 'undefined') return;

    const fonts = ['Arial', 'serif', 'sans-serif', 'monospace'];
    const promises = fonts.map(async (font) => {
      try {
        await document.fonts.load(`16px ${font}`);
        this.fontCache.set(font, true);
      } catch (error) {
        console.warn(`Failed to preload font: ${font}`);
        this.fontCache.set(font, false);
      }
    });

    await Promise.all(promises);
  }

  /**
   * Generate certificate with high performance (<2 seconds)
   */
  async generateCertificate(
    data: CertificateData,
    options: GenerationOptions = {
      format: 'pdf',
      quality: 'high',
      includeMetadata: true,
      watermark: true,
      compression: true
    }
  ): Promise<GenerationResult> {
    const startTime = performance.now();
    
    try {
      // Process template data
      const processedData = CertificateTemplateEngine.processTemplateData(data);
      
      // Generate QR code
      const qrCodeDataUrl = await this.generateQRCode(data.verificationUrl);
      
      // Create certificate metadata
      const metadata = this.createMetadata(data);
      
      let pdfBuffer: Buffer | undefined;
      let pngBuffer: Buffer | undefined;
      
      // Generate based on format
      if (options.format === 'pdf' || options.format === 'both') {
        pdfBuffer = await this.generatePDF(processedData, qrCodeDataUrl, metadata, options);
      }
      
      if (options.format === 'png' || options.format === 'both') {
        pngBuffer = await this.generatePNG(processedData, qrCodeDataUrl, options);
      }
      
      const endTime = performance.now();
      const generationTime = endTime - startTime;
      
      // Calculate file size
      const fileSize = (pdfBuffer?.length || 0) + (pngBuffer?.length || 0);
      
      return {
        success: true,
        certificateNumber: data.certificateNumber,
        pdfBuffer,
        pngBuffer,
        fileSize,
        generationTime,
        metadata
      };
      
    } catch (error) {
      const endTime = performance.now();
      return {
        success: false,
        certificateNumber: data.certificateNumber,
        fileSize: 0,
        generationTime: endTime - startTime,
        metadata: this.createMetadata(data),
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Generate PDF certificate
   */
  private async generatePDF(
    data: Record<string, string>,
    qrCodeDataUrl: string,
    metadata: CertificateMetadata,
    options: GenerationOptions
  ): Promise<Buffer> {
    const pdf = new jsPDF({
      orientation: 'landscape',
      unit: 'px',
      format: [this.template.width, this.template.height],
      compress: options.compression
    });

    // Set high quality for print
    if (options.quality === 'print') {
      pdf.setProperties({
        title: `Certificate ${metadata.certificateNumber}`,
        subject: 'Aureus Africa Share Certificate',
        author: 'Aureus Africa',
        creator: 'Certificate Generation System',
        producer: 'Aureus Africa Admin System'
      });
    }

    // Add background
    pdf.setFillColor(255, 255, 255);
    pdf.rect(0, 0, this.template.width, this.template.height, 'F');

    // Render security elements first (background layer)
    for (const element of this.template.securityElements) {
      await this.renderSecurityElement(pdf, element);
    }

    // Reset opacity to full for main content
    pdf.setGState(pdf.GState({ opacity: 1.0 }));

    // Render main elements
    for (const element of this.template.elements) {
      await this.renderPDFElement(pdf, element, data);
    }

    // Add QR code
    if (qrCodeDataUrl) {
      const qrConfig = this.template.qrCode;
      pdf.addImage(
        qrCodeDataUrl,
        'PNG',
        qrConfig.x,
        qrConfig.y,
        qrConfig.size,
        qrConfig.size
      );
    }

    // Add metadata if requested
    if (options.includeMetadata) {
      this.addPDFMetadata(pdf, metadata);
    }

    // Add watermark if requested
    if (options.watermark) {
      this.addPDFWatermark(pdf);
    }

    return Buffer.from(pdf.output('arraybuffer'));
  }

  /**
   * Generate PNG certificate
   */
  private async generatePNG(
    data: Record<string, string>,
    qrCodeDataUrl: string,
    options: GenerationOptions
  ): Promise<Buffer> {
    if (!this.canvas || !this.ctx) {
      throw new Error('Canvas not initialized');
    }

    const ctx = this.ctx;
    
    // Clear canvas
    ctx.clearRect(0, 0, this.template.width, this.template.height);
    
    // Set background
    ctx.fillStyle = this.template.backgroundColor;
    ctx.fillRect(0, 0, this.template.width, this.template.height);

    // Render security elements
    for (const element of this.template.securityElements) {
      await this.renderCanvasSecurityElement(ctx, element);
    }

    // Render main elements
    for (const element of this.template.elements) {
      await this.renderCanvasElement(ctx, element, data);
    }

    // Add QR code
    if (qrCodeDataUrl) {
      await this.addCanvasQRCode(ctx, qrCodeDataUrl);
    }

    // Add watermark if requested
    if (options.watermark) {
      this.addCanvasWatermark(ctx);
    }

    // Convert to buffer
    return new Promise((resolve, reject) => {
      this.canvas!.toBlob((blob) => {
        if (blob) {
          blob.arrayBuffer().then(buffer => resolve(Buffer.from(buffer)));
        } else {
          reject(new Error('Failed to generate PNG'));
        }
      }, 'image/png', options.quality === 'high' ? 1.0 : 0.9);
    });
  }

  /**
   * Render PDF element
   */
  private async renderPDFElement(
    pdf: jsPDF,
    element: CertificateElement,
    data: Record<string, string>
  ): Promise<void> {
    const content = element.dynamic && element.dataField 
      ? data[element.dataField] || element.content || ''
      : element.content || '';

    switch (element.type) {
      case 'text':
        // Skip empty content
        if (!content || content.trim() === '') {
          console.log(`Skipping empty text element: ${element.id}`);
          break;
        }

        console.log(`Rendering text element: ${element.id}, content: "${content}", color: ${element.color}, fontSize: ${element.fontSize}`);

        pdf.setFontSize(element.fontSize || 12);

        // Force black color for testing
        console.log(`Original color: ${element.color}`);
        pdf.setTextColor(0, 0, 0); // Force pure black
        console.log(`Set text color to pure black (0, 0, 0)`);

        // Also ensure no transparency
        pdf.setGState(pdf.GState({ opacity: 1.0 }));

        // Map font families to jsPDF compatible fonts
        let fontFamily = 'helvetica';
        if (element.fontFamily) {
          const fontMap: Record<string, string> = {
            'Arial': 'helvetica',
            'serif': 'times',
            'sans-serif': 'helvetica',
            'monospace': 'courier'
          };
          fontFamily = fontMap[element.fontFamily] || 'helvetica';
        }

        if (element.fontWeight === 'bold') {
          pdf.setFont(fontFamily, 'bold');
        } else {
          pdf.setFont(fontFamily, 'normal');
        }

        const textAlign = element.alignment || 'left';

        console.log(`Rendering text at position: x=${element.x}, y=${element.y}, align=${textAlign}`);
        console.log(`Template dimensions: ${this.template.width} x ${this.template.height}`);

        // Handle multi-line text
        if (content.includes('\n')) {
          const lines = content.split('\n');
          lines.forEach((line, index) => {
            const yPos = element.y + (index * (element.fontSize || 12) * 1.2);
            console.log(`Rendering line "${line}" at x=${element.x}, y=${yPos}`);
            pdf.text(line, element.x, element.y + (index * (element.fontSize || 12) * 1.2), { align: textAlign });
          });
        } else {
          console.log(`Rendering single line "${content}" at x=${element.x}, y=${element.y}`);
          pdf.text(content, element.x, element.y, { align: textAlign });
        }
        break;

      case 'rectangle':
        if (element.color) {
          const color = this.hexToRgb(element.color);
          pdf.setFillColor(color.r, color.g, color.b);
          pdf.rect(element.x, element.y, element.width || 100, element.height || 20, 'F');
        }
        break;

      case 'line':
        if (element.color) {
          const color = this.hexToRgb(element.color);
          pdf.setDrawColor(color.r, color.g, color.b);
          pdf.line(element.x, element.y, element.x + (element.width || 100), element.y);
        }
        break;
    }
  }

  /**
   * Render Canvas element
   */
  private async renderCanvasElement(
    ctx: CanvasRenderingContext2D,
    element: CertificateElement,
    data: Record<string, string>
  ): Promise<void> {
    const content = element.dynamic && element.dataField 
      ? data[element.dataField] || element.content || ''
      : element.content || '';

    switch (element.type) {
      case 'text':
        ctx.fillStyle = element.color || '#000000';
        ctx.font = `${element.fontWeight || 'normal'} ${element.fontSize || 12}px ${element.fontFamily || 'Arial'}`;
        
        const textAlign = element.alignment || 'left';
        ctx.textAlign = textAlign;
        ctx.fillText(content, element.x, element.y);
        break;

      case 'rectangle':
        if (element.color) {
          ctx.fillStyle = element.color;
          ctx.fillRect(element.x, element.y, element.width || 100, element.height || 20);
        }
        break;

      case 'line':
        if (element.color) {
          ctx.strokeStyle = element.color;
          ctx.lineWidth = element.height || 1;
          ctx.beginPath();
          ctx.moveTo(element.x, element.y);
          ctx.lineTo(element.x + (element.width || 100), element.y);
          ctx.stroke();
        }
        break;
    }
  }

  /**
   * Generate QR code with caching for performance
   */
  private async generateQRCode(url: string): Promise<string> {
    // Check cache first
    if (this.qrCodeCache.has(url)) {
      return this.qrCodeCache.get(url)!;
    }

    try {
      const qrCodeDataUrl = await QRCode.toDataURL(url, {
        errorCorrectionLevel: this.template.qrCode.errorCorrectionLevel,
        type: 'image/png',
        quality: 0.92,
        margin: this.template.qrCode.margin,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        width: this.template.qrCode.size
      });

      // Cache the result
      this.qrCodeCache.set(url, qrCodeDataUrl);

      // Limit cache size to prevent memory issues
      if (this.qrCodeCache.size > 100) {
        const firstKey = this.qrCodeCache.keys().next().value;
        this.qrCodeCache.delete(firstKey);
      }

      return qrCodeDataUrl;
    } catch (error) {
      console.error('QR Code generation failed:', error);
      return '';
    }
  }

  /**
   * Create certificate metadata
   */
  private createMetadata(data: CertificateData): CertificateMetadata {
    const timestamp = new Date().toISOString();
    const securityHash = this.generateSecurityHash(data, timestamp);
    
    return {
      generatedAt: timestamp,
      generatedBy: 'Aureus Africa Admin System',
      certificateNumber: data.certificateNumber,
      userId: data.userId,
      template: 'AUREUS_STANDARD_V1',
      securityHash,
      verificationUrl: data.verificationUrl
    };
  }

  /**
   * Generate security hash for anti-tampering
   */
  private generateSecurityHash(data: CertificateData, timestamp: string): string {
    const hashInput = `${data.certificateNumber}${data.userId}${data.sharesQuantity}${timestamp}`;
    // Simple hash for demo - in production, use crypto.subtle or similar
    let hash = 0;
    for (let i = 0; i < hashInput.length; i++) {
      const char = hashInput.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16).toUpperCase();
  }

  /**
   * Utility functions
   */
  private hexToRgb(hex: string): { r: number; g: number; b: number } {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : { r: 0, g: 0, b: 0 };
  }

  private async renderSecurityElement(pdf: jsPDF, element: SecurityElement): Promise<void> {
    // Implement security element rendering for PDF
    switch (element.type) {
      case 'watermark':
        pdf.setGState(pdf.GState({ opacity: element.opacity }));
        pdf.setFontSize(48);
        pdf.setTextColor(200, 200, 200);
        pdf.text('AUREUS AFRICA', element.x, element.y, { 
          align: 'center',
          angle: 45 
        });
        break;
    }
  }

  private async renderCanvasSecurityElement(ctx: CanvasRenderingContext2D, element: SecurityElement): Promise<void> {
    // Implement security element rendering for Canvas
    switch (element.type) {
      case 'watermark':
        ctx.save();
        ctx.globalAlpha = element.opacity;
        ctx.fillStyle = '#CCCCCC';
        ctx.font = '48px Arial';
        ctx.textAlign = 'center';
        ctx.translate(element.x, element.y);
        ctx.rotate(Math.PI / 4);
        ctx.fillText('AUREUS AFRICA', 0, 0);
        ctx.restore();
        break;
    }
  }

  private addPDFMetadata(pdf: jsPDF, metadata: CertificateMetadata): void {
    // Add invisible metadata for verification
    pdf.setFontSize(1);
    pdf.setTextColor(255, 255, 255);
    pdf.text(`META:${JSON.stringify(metadata)}`, 0, 0);
  }

  private addPDFWatermark(pdf: jsPDF): void {
    // Add subtle watermark
    pdf.setGState(pdf.GState({ opacity: 0.1 }));
    pdf.setFontSize(72);
    pdf.setTextColor(128, 128, 128);
    pdf.text('AUTHENTIC', this.template.width / 2, this.template.height / 2, {
      align: 'center',
      angle: 45
    });
  }

  private async addCanvasQRCode(ctx: CanvasRenderingContext2D, qrCodeDataUrl: string): Promise<void> {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        const qrConfig = this.template.qrCode;
        ctx.drawImage(img, qrConfig.x, qrConfig.y, qrConfig.size, qrConfig.size);
        resolve();
      };
      img.src = qrCodeDataUrl;
    });
  }

  private addCanvasWatermark(ctx: CanvasRenderingContext2D): void {
    ctx.save();
    ctx.globalAlpha = 0.1;
    ctx.fillStyle = '#808080';
    ctx.font = '72px Arial';
    ctx.textAlign = 'center';
    ctx.translate(this.template.width / 2, this.template.height / 2);
    ctx.rotate(Math.PI / 4);
    ctx.fillText('AUTHENTIC', 0, 0);
    ctx.restore();
  }
}

export default CertificateGenerator;
