<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iOS Safari Navigation Test - Aureus Alliance Holdings</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #ffffff;
            min-height: 100vh;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .browser-info {
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid #10b981;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
            color: #000;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin: 10px 0;
            transition: all 0.3s ease;
            min-height: 44px; /* iOS recommended touch target */
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
        }
        
        .test-button:active {
            transform: scale(0.98);
        }
        
        .log-area {
            background: #000;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #333;
        }
        
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: 600;
        }
        
        .status.success {
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid #10b981;
            color: #10b981;
        }
        
        .status.warning {
            background: rgba(245, 158, 11, 0.2);
            border: 1px solid #f59e0b;
            color: #f59e0b;
        }
        
        .status.error {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid #ef4444;
            color: #ef4444;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🍎 iOS Safari Navigation Test</h1>
            <p>Testing Purchase Shares button navigation across mobile browsers</p>
        </div>
        
        <div class="test-section">
            <h3>Browser Detection</h3>
            <div id="browserInfo" class="browser-info">
                <div>Loading browser information...</div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Navigation Tests</h3>
            <button class="test-button" onclick="testPurchaseSharesNavigation()">
                🛒 Test Purchase Shares Navigation
            </button>
            <button class="test-button" onclick="testLoginNavigation()">
                🔐 Test Login Navigation
            </button>
            <button class="test-button" onclick="testDashboardNavigation()">
                🏠 Test Dashboard Navigation
            </button>
            <button class="test-button" onclick="clearTestData()">
                🧹 Clear Test Data
            </button>
        </div>
        
        <div class="test-section">
            <h3>Test Results</h3>
            <div id="testStatus"></div>
        </div>
        
        <div class="test-section">
            <h3>Debug Log</h3>
            <div id="logArea" class="log-area"></div>
        </div>
    </div>

    <script>
        // Browser detection (matching the main app logic)
        function detectBrowser() {
            const userAgent = navigator.userAgent;
            const isIOS = /iPad|iPhone|iPod/.test(userAgent);
            const isIOSSafari = isIOS && /Safari/.test(userAgent) && !/CriOS|FxiOS|OPiOS|mercury/.test(userAgent);
            const isIOSChrome = isIOS && /CriOS/.test(userAgent);
            const isIOSFirefox = isIOS && /FxiOS/.test(userAgent);
            const isAndroid = /Android/.test(userAgent);
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
            
            let version;
            if (isIOSSafari) {
                const versionMatch = userAgent.match(/Version\/(\d+\.\d+)/);
                version = versionMatch ? versionMatch[1] : undefined;
            }
            
            return {
                isMobile,
                isIOSSafari,
                isIOSChrome,
                isIOSFirefox,
                isAndroid,
                userAgent,
                version
            };
        }
        
        // Logging function
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logArea = document.getElementById('logArea');
            logArea.innerHTML += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(message);
        }
        
        // Update status
        function updateStatus(message, type = 'success') {
            const statusDiv = document.getElementById('testStatus');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        // Test navigation functions
        function testPurchaseSharesNavigation() {
            log('🛒 Testing Purchase Shares navigation...');
            const browser = detectBrowser();
            
            if (browser.isIOSSafari) {
                log('🍎 iOS Safari detected - would use location.href');
                updateStatus('✅ iOS Safari: Would navigate using location.href = "/purchase-shares"', 'success');
            } else {
                log('📱 Other browser - would use pushState');
                updateStatus('✅ Other Browser: Would navigate using history.pushState', 'success');
            }
            
            // Simulate the actual navigation logic
            setTimeout(() => {
                log('✅ Purchase Shares navigation test completed');
            }, 500);
        }
        
        function testLoginNavigation() {
            log('🔐 Testing Login navigation...');
            const browser = detectBrowser();
            
            localStorage.setItem('aureus_post_login_redirect', '/purchase-shares');
            log('💾 Set post-login redirect to /purchase-shares');
            
            if (browser.isIOSSafari) {
                log('🍎 iOS Safari detected - would use location.href for login');
                updateStatus('✅ iOS Safari: Would navigate to login using location.href', 'success');
            } else {
                log('📱 Other browser - would use pushState for login');
                updateStatus('✅ Other Browser: Would navigate to login using pushState', 'success');
            }
            
            setTimeout(() => {
                log('✅ Login navigation test completed');
            }, 500);
        }
        
        function testDashboardNavigation() {
            log('🏠 Testing Dashboard navigation...');
            const browser = detectBrowser();
            
            if (browser.isIOSSafari) {
                log('🍎 iOS Safari detected - would use location.href for dashboard');
                updateStatus('✅ iOS Safari: Would navigate to dashboard using location.href', 'success');
            } else {
                log('📱 Other browser - would use pushState for dashboard');
                updateStatus('✅ Other Browser: Would navigate to dashboard using pushState', 'success');
            }
            
            setTimeout(() => {
                log('✅ Dashboard navigation test completed');
            }, 500);
        }
        
        function clearTestData() {
            log('🧹 Clearing test data...');
            localStorage.removeItem('aureus_post_login_redirect');
            document.getElementById('logArea').innerHTML = '';
            updateStatus('🧹 Test data cleared', 'warning');
            log('✅ Test data cleared');
        }
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            const browser = detectBrowser();
            
            const browserInfoDiv = document.getElementById('browserInfo');
            browserInfoDiv.innerHTML = `
                <div><strong>Mobile:</strong> ${browser.isMobile ? 'Yes' : 'No'}</div>
                <div><strong>iOS Safari:</strong> ${browser.isIOSSafari ? 'Yes' : 'No'}</div>
                <div><strong>iOS Chrome:</strong> ${browser.isIOSChrome ? 'Yes' : 'No'}</div>
                <div><strong>iOS Firefox:</strong> ${browser.isIOSFirefox ? 'Yes' : 'No'}</div>
                <div><strong>Android:</strong> ${browser.isAndroid ? 'Yes' : 'No'}</div>
                <div><strong>Version:</strong> ${browser.version || 'N/A'}</div>
                <div><strong>User Agent:</strong> ${browser.userAgent.substring(0, 80)}...</div>
            `;
            
            log('🔧 iOS Safari Navigation Test Page Loaded');
            log(`📱 Browser: ${browser.isIOSSafari ? 'iOS Safari' : browser.isIOSChrome ? 'iOS Chrome' : browser.isAndroid ? 'Android' : 'Desktop'}`);
            
            if (browser.isIOSSafari) {
                updateStatus('🍎 iOS Safari detected - navigation fixes will be applied', 'success');
            } else if (browser.isMobile) {
                updateStatus('📱 Mobile browser detected - standard navigation will be used', 'success');
            } else {
                updateStatus('🖥️ Desktop browser detected - standard navigation will be used', 'success');
            }
        });
    </script>
</body>
</html>
