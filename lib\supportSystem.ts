import { supabase, getServiceRoleClient, getCurrentUser } from './supabase'
import { sendNewTicketNotification, sendTicketResponseNotification } from './services/supportTicketNotificationService'

// Types
export interface SupportAgent {
  id: string
  admin_user_id: number
  user_id: number
  name: string
  email: string
  specialization: string[]
  max_concurrent_chats: number
  priority_level: number
  is_active: boolean
  availability?: AgentAvailability
}

export interface AgentAvailability {
  id: string
  agent_id: string
  status: 'available' | 'busy' | 'away' | 'offline'
  last_seen: string
  current_chat_count: number
  status_message?: string
}

export interface SupportTicket {
  id: string
  ticket_number: string
  user_id: number
  user_type: 'shareholder' | 'affiliate'
  assigned_agent_id?: string
  title: string
  description?: string
  category?: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  status: 'open' | 'in_progress' | 'waiting_user' | 'resolved' | 'closed'
  tags?: string[]
  first_response_at?: string
  resolved_at?: string
  closed_at?: string
  satisfaction_rating?: number
  satisfaction_feedback?: string
  created_at: string
  updated_at: string
  agent?: SupportAgent
  user?: any
}

export interface ChatSession {
  id: string
  ticket_id: string
  user_id: number
  agent_id?: string
  session_type: 'support' | 'consultation' | 'training'
  status: 'active' | 'transferred' | 'ended'
  started_at: string
  ended_at?: string
  transfer_reason?: string
  session_rating?: number
  agent?: SupportAgent
  user?: any
}

export interface ChatMessage {
  id: string
  session_id: string
  ticket_id: string
  sender_type: 'user' | 'agent' | 'system'
  sender_id: number
  message_type: 'text' | 'file' | 'image' | 'system_notification'
  content: string
  file_url?: string
  file_name?: string
  file_size?: number
  is_read: boolean
  read_at?: string
  is_internal_note: boolean
  created_at: string
  sender?: any
}

export interface TrainingContent {
  id: string
  category_id: string
  title: string
  description?: string
  content_type: 'video' | 'document' | 'presentation' | 'quiz' | 'webinar'
  target_audience: 'shareholder' | 'affiliate' | 'both'
  content_url?: string
  thumbnail_url?: string
  duration_minutes?: number
  file_size?: number
  download_url?: string
  prerequisites?: string[]
  learning_objectives?: string[]
  difficulty_level: 'beginner' | 'intermediate' | 'advanced'
  tags?: string[]
  is_featured: boolean
  is_mandatory: boolean
  sort_order: number
  is_active: boolean
  created_at: string
  updated_at: string
  progress?: UserTrainingProgress
}

export interface UserTrainingProgress {
  id: string
  user_id: number
  content_id: string
  status: 'not_started' | 'in_progress' | 'completed' | 'failed'
  progress_percentage: number
  time_spent_minutes: number
  started_at?: string
  completed_at?: string
  last_accessed_at: string
  quiz_score?: number
  certificate_issued: boolean
  certificate_url?: string
  notes?: string
}

export interface ConsultationBooking {
  id: string
  user_id: number
  agent_id?: string
  consultation_type: 'investment_guidance' | 'business_opportunity' | 'technical_support'
  user_type: 'shareholder' | 'affiliate'
  title: string
  description?: string
  scheduled_at: string
  duration_minutes: number
  timezone: string
  status: 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show'
  meeting_url?: string
  meeting_id?: string
  meeting_password?: string
  reminder_sent: boolean
  follow_up_required: boolean
  follow_up_notes?: string
  rating?: number
  feedback?: string
  created_at: string
  updated_at: string
  agent?: SupportAgent
  user?: any
}

// Utility Functions
export const generateTicketNumber = (): string => {
  const date = new Date()
  const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '')
  const random = Math.floor(Math.random() * 9999).toString().padStart(4, '0')
  return `TKT-${dateStr}-${random}`
}

export const getUserType = async (userId: number): Promise<'shareholder' | 'affiliate'> => {
  try {
    const serviceClient = getServiceRoleClient()
    const { data: user } = await serviceClient
      .from('users')
      .select('role, telegram_id')
      .eq('id', userId)
      .single()

    if (user?.role === 'shareholder') return 'shareholder'
    if (user?.role === 'affiliate') return 'affiliate'
    if (user?.telegram_id) return 'affiliate'
    return 'shareholder' // Default fallback
  } catch (error) {
    console.error('Error determining user type:', error)
    return 'shareholder'
  }
}

export const getCurrentDbUserId = async (): Promise<number | null> => {
  try {
    const user = await getCurrentUser()
    return user?.database_user?.id || user?.user_metadata?.user_id || null
  } catch (error) {
    console.error('Error getting current user ID:', error)
    return null
  }
}

// Agent Management
export const getAvailableAgents = async (userType?: 'shareholder' | 'affiliate'): Promise<SupportAgent[]> => {
  try {
    const serviceClient = getServiceRoleClient()

    // First get agents
    let query = serviceClient
      .from('support_agents')
      .select('*')
      .eq('is_active', true)

    if (userType) {
      query = query.like('specialization', `%${userType}_support%`)
    }

    const { data: agents, error } = await query

    if (error) throw error

    // Then get availability for each agent
    const agentsWithAvailability = await Promise.all(
      (agents || []).map(async (agent) => {
        const { data: availability } = await serviceClient
          .from('agent_availability')
          .select('*')
          .eq('agent_id', agent.id)
          .single()

        return { ...agent, availability: availability ? [availability] : [] }
      })
    )

    // Filter by availability status
    return agentsWithAvailability.filter((agent: any) =>
      agent.availability?.[0]?.status === 'available' &&
      (agent.availability?.[0]?.current_chat_count || 0) < (agent.availability?.[0]?.max_concurrent_chats || 3)
    )
  } catch (error) {
    console.error('Error getting available agents:', error)
    return []
  }
}

export const assignAgentToTicket = async (ticketId: string, agentId?: string): Promise<boolean> => {
  try {
    const serviceClient = getServiceRoleClient()

    if (!agentId) {
      // Auto-assign to available agent
      const { data: ticket } = await serviceClient
        .from('support_tickets')
        .select('user_type')
        .eq('id', ticketId)
        .single()

      if (!ticket) return false

      const availableAgents = await getAvailableAgents(ticket.user_type)
      if (availableAgents.length === 0) return false

      // Simple round-robin assignment (could be enhanced with load balancing)
      agentId = availableAgents[0].id
    }

    const { error } = await serviceClient
      .from('support_tickets')
      .update({ 
        assigned_agent_id: agentId,
        status: 'in_progress',
        updated_at: new Date().toISOString()
      })
      .eq('id', ticketId)

    if (error) throw error

    // Update agent's current chat count
    await serviceClient.rpc('increment_agent_chat_count', { agent_uuid: agentId })

    return true
  } catch (error) {
    console.error('Error assigning agent to ticket:', error)
    return false
  }
}

// Ticket Management
export const createSupportTicket = async (
  userId: number,
  title: string,
  description: string,
  category?: string,
  priority: 'low' | 'medium' | 'high' | 'urgent' = 'medium'
): Promise<SupportTicket | null> => {
  try {
    const serviceClient = getServiceRoleClient()
    const userType = await getUserType(userId)
    const ticketNumber = generateTicketNumber()

    const { data, error } = await serviceClient
      .from('support_tickets')
      .insert({
        ticket_number: ticketNumber,
        user_id: userId,
        user_type: userType,
        title,
        description,
        category,
        priority,
        status: 'open'
      })
      .select()
      .single()

    if (error) throw error

    // Try to auto-assign an agent
    await assignAgentToTicket(data.id)

    // Send email notification to support team
    try {
      console.log('📧 Sending new ticket notification email...')
      const notificationResult = await sendNewTicketNotification({
        id: data.id,
        ticket_number: ticketNumber,
        user_id: userId,
        title,
        description,
        priority,
        category: category || 'general',
        user_type: userType
      })

      if (notificationResult.success) {
        console.log('✅ New ticket notification sent successfully')
      } else {
        console.warn('⚠️ Failed to send new ticket notification:', notificationResult.error)
      }
    } catch (emailError) {
      console.error('❌ Error sending new ticket notification:', emailError)
      // Don't fail ticket creation if email fails
    }

    return data
  } catch (error) {
    console.error('Error creating support ticket:', error)
    return null
  }
}

export const getUserTickets = async (userId: number): Promise<SupportTicket[]> => {
  try {
    const serviceClient = getServiceRoleClient()

    // First get tickets
    const { data: tickets, error } = await serviceClient
      .from('support_tickets')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) throw error

    // Then get agent details for each ticket
    const ticketsWithAgents = await Promise.all(
      (tickets || []).map(async (ticket) => {
        if (ticket.assigned_agent_id) {
          const { data: agent } = await serviceClient
            .from('support_agents')
            .select('name, email')
            .eq('id', ticket.assigned_agent_id)
            .single()

          return { ...ticket, agent }
        }
        return ticket
      })
    )

    return ticketsWithAgents
  } catch (error) {
    console.error('Error getting user tickets:', error)
    return []
  }
}

export const getAgentTickets = async (agentId: string): Promise<SupportTicket[]> => {
  try {
    const serviceClient = getServiceRoleClient()

    const { data, error } = await serviceClient
      .from('support_tickets')
      .select(`
        *,
        user:users(id, username, full_name, email)
      `)
      .eq('assigned_agent_id', agentId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  } catch (error) {
    console.error('Error getting agent tickets:', error)
    return []
  }
}

export const updateTicketStatus = async (
  ticketId: string,
  status: 'open' | 'in_progress' | 'waiting_user' | 'resolved' | 'closed',
  agentId?: string,
  responseMessage?: string
): Promise<boolean> => {
  try {
    const serviceClient = getServiceRoleClient()

    // Update ticket status
    const { error } = await serviceClient
      .from('support_tickets')
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', ticketId)

    if (error) throw error

    // Send email notification to user about status change
    try {
      console.log('📧 Sending ticket status update notification...')
      const notificationResult = await sendTicketResponseNotification(ticketId, {
        statusUpdate: status,
        responseMessage,
        agentId
      })

      if (notificationResult.success) {
        console.log('✅ Ticket status update notification sent successfully')
      } else {
        console.warn('⚠️ Failed to send status update notification:', notificationResult.error)
      }
    } catch (emailError) {
      console.error('❌ Error sending status update notification:', emailError)
      // Don't fail status update if email fails
    }

    return true
  } catch (error) {
    console.error('Error updating ticket status:', error)
    return false
  }
}

export const addTicketResponse = async (
  ticketId: string,
  responseMessage: string,
  agentId: string
): Promise<boolean> => {
  try {
    const serviceClient = getServiceRoleClient()

    // Add response to ticket (you might want to create a ticket_responses table)
    // For now, we'll just send the notification

    // Send email notification to user about the response
    try {
      console.log('📧 Sending ticket response notification...')
      const notificationResult = await sendTicketResponseNotification(ticketId, {
        responseMessage,
        agentId
      })

      if (notificationResult.success) {
        console.log('✅ Ticket response notification sent successfully')
      } else {
        console.warn('⚠️ Failed to send response notification:', notificationResult.error)
      }
    } catch (emailError) {
      console.error('❌ Error sending response notification:', emailError)
      // Don't fail response addition if email fails
    }

    return true
  } catch (error) {
    console.error('Error adding ticket response:', error)
    return false
  }
}

// Chat Session Management
export const createChatSession = async (
  ticketId: string,
  userId: number,
  agentId?: string,
  sessionType: 'support' | 'consultation' | 'training' = 'support'
): Promise<ChatSession | null> => {
  try {
    const serviceClient = getServiceRoleClient()

    const { data, error } = await serviceClient
      .from('chat_sessions')
      .insert({
        ticket_id: ticketId,
        user_id: userId,
        agent_id: agentId,
        session_type: sessionType,
        status: 'active'
      })
      .select()
      .single()

    if (error) throw error
    return data
  } catch (error) {
    console.error('Error creating chat session:', error)
    return null
  }
}

export const sendChatMessage = async (
  sessionId: string,
  ticketId: string,
  senderType: 'user' | 'agent' | 'system',
  senderId: number,
  content: string,
  messageType: 'text' | 'file' | 'image' | 'system_notification' = 'text',
  fileUrl?: string,
  fileName?: string,
  fileSize?: number,
  isInternalNote: boolean = false
): Promise<ChatMessage | null> => {
  try {
    const serviceClient = getServiceRoleClient()

    const { data, error } = await serviceClient
      .from('chat_messages')
      .insert({
        session_id: sessionId,
        ticket_id: ticketId,
        sender_type: senderType,
        sender_id: senderId,
        message_type: messageType,
        content,
        file_url: fileUrl,
        file_name: fileName,
        file_size: fileSize,
        is_internal_note: isInternalNote
      })
      .select()
      .single()

    if (error) throw error

    // Update ticket's first response time if this is the first agent response
    if (senderType === 'agent') {
      await serviceClient
        .from('support_tickets')
        .update({
          first_response_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', ticketId)
        .is('first_response_at', null)
    }

    return data
  } catch (error) {
    console.error('Error sending chat message:', error)
    return null
  }
}

export const getChatMessages = async (sessionId: string): Promise<ChatMessage[]> => {
  try {
    const serviceClient = getServiceRoleClient()

    const { data, error } = await serviceClient
      .from('chat_messages')
      .select('*')
      .eq('session_id', sessionId)
      .eq('is_internal_note', false)
      .order('created_at', { ascending: true })

    if (error) throw error
    return data || []
  } catch (error) {
    console.error('Error getting chat messages:', error)
    return []
  }
}

// Training Content Management
export const getTrainingContent = async (
  userType: 'shareholder' | 'affiliate',
  categoryId?: string
): Promise<TrainingContent[]> => {
  try {
    const serviceClient = getServiceRoleClient()

    let query = serviceClient
      .from('training_courses')
      .select('*')
      .eq('status', 'published')
      .order('sort_order', { ascending: true })

    if (categoryId) {
      query = query.eq('category_id', categoryId)
    }

    const { data, error } = await query

    if (error) throw error
    return data || []
  } catch (error) {
    console.error('Error getting training content:', error)
    return []
  }
}

export const getUserTrainingProgress = async (
  userId: number,
  courseId?: string
): Promise<UserTrainingProgress[]> => {
  try {
    const serviceClient = getServiceRoleClient()

    let query = serviceClient
      .from('training_enrollments')
      .select('*')
      .eq('user_id', userId)

    if (courseId) {
      query = query.eq('course_id', courseId)
    }

    const { data, error } = await query

    if (error) throw error
    return data || []
  } catch (error) {
    console.error('Error getting user training progress:', error)
    return []
  }
}

export const updateTrainingProgress = async (
  userId: number,
  contentId: string,
  progressPercentage: number,
  timeSpentMinutes: number,
  status?: 'not_started' | 'in_progress' | 'completed' | 'failed'
): Promise<boolean> => {
  try {
    const serviceClient = getServiceRoleClient()

    const updateData: any = {
      progress_percentage: progressPercentage,
      time_spent_minutes: timeSpentMinutes,
      last_accessed_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    if (status) {
      updateData.status = status
      if (status === 'in_progress' && progressPercentage === 0) {
        updateData.started_at = new Date().toISOString()
      }
      if (status === 'completed') {
        updateData.completed_at = new Date().toISOString()
      }
    }

    const { error } = await serviceClient
      .from('training_enrollments')
      .upsert({
        user_id: userId,
        course_id: contentId,
        ...updateData
      })

    if (error) throw error
    return true
  } catch (error) {
    console.error('Error updating training progress:', error)
    return false
  }
}

// Consultation Booking Management
export const createConsultationBooking = async (
  userId: number,
  consultationType: 'investment_guidance' | 'business_opportunity' | 'technical_support',
  title: string,
  description: string,
  scheduledAt: string,
  durationMinutes: number = 30,
  timezone: string = 'UTC'
): Promise<ConsultationBooking | null> => {
  try {
    const serviceClient = getServiceRoleClient()
    const userType = await getUserType(userId)

    // Find available agent for the consultation type
    const availableAgents = await getAvailableAgents(userType)
    const agentId = availableAgents.length > 0 ? availableAgents[0].id : null

    const { data, error } = await serviceClient
      .from('consultation_bookings')
      .insert({
        user_id: userId,
        agent_id: agentId,
        consultation_type: consultationType,
        title,
        description,
        scheduled_at: scheduledAt,
        duration_minutes: durationMinutes,
        timezone,
        status: 'scheduled'
      })
      .select()
      .single()

    if (error) throw error
    return data
  } catch (error) {
    console.error('Error creating consultation booking:', error)
    return null
  }
}

export const getUserConsultations = async (userId: number): Promise<ConsultationBooking[]> => {
  try {
    const serviceClient = getServiceRoleClient()

    // First get consultations
    const { data: consultations, error } = await serviceClient
      .from('consultation_bookings')
      .select('*')
      .eq('user_id', userId)
      .order('scheduled_at', { ascending: true })

    if (error) throw error

    // Then get agent details for each consultation
    const consultationsWithAgents = await Promise.all(
      (consultations || []).map(async (consultation) => {
        if (consultation.agent_id) {
          const { data: agent } = await serviceClient
            .from('support_agents')
            .select('name, email')
            .eq('id', consultation.agent_id)
            .single()

          return { ...consultation, agent }
        }
        return consultation
      })
    )

    return consultationsWithAgents
  } catch (error) {
    console.error('Error getting user consultations:', error)
    return []
  }
}
