import { useState, useEffect } from 'react';
import { 
  onboardingService, 
  UserOnboardingStatus, 
  OnboardingStep, 
  OnboardingAchievement 
} from '../services/onboardingService';

interface UseOnboardingOptions {
  userId?: number;
  autoLoad?: boolean;
  refreshInterval?: number; // seconds
}

interface UseOnboardingReturn {
  // State
  status: UserOnboardingStatus | null;
  loading: boolean;
  error: string | null;
  
  // Actions
  startStep: (stepId: string, data?: any) => Promise<boolean>;
  completeStep: (stepId: string, data?: any) => Promise<boolean>;
  skipStep: (stepId: string) => Promise<boolean>;
  refreshStatus: () => Promise<void>;
  resetOnboarding: () => Promise<boolean>;
  
  // Computed
  isComplete: boolean;
  currentStepId: string | null;
  nextSteps: OnboardingStep[];
  achievements: OnboardingAchievement[];
  progressPercentage: number;
  estimatedTimeRemaining: number;
  
  // Helpers
  isStepCompleted: (stepId: string) => boolean;
  isStepAvailable: (stepId: string) => boolean;
  getStepStatus: (stepId: string) => 'not_started' | 'in_progress' | 'completed' | 'skipped' | 'locked';
}

export const useOnboarding = (options: UseOnboardingOptions = {}): UseOnboardingReturn => {
  const {
    userId,
    autoLoad = true,
    refreshInterval
  } = options;

  // State
  const [status, setStatus] = useState<UserOnboardingStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load status on mount and when userId changes
  useEffect(() => {
    if (autoLoad && userId) {
      refreshStatus();
    }
  }, [userId, autoLoad]);

  // Set up auto-refresh if specified
  useEffect(() => {
    if (!refreshInterval || !userId) return;

    const interval = setInterval(() => {
      refreshStatus();
    }, refreshInterval * 1000);

    return () => clearInterval(interval);
  }, [refreshInterval, userId]);

  /**
   * Refresh onboarding status
   */
  const refreshStatus = async () => {
    if (!userId) return;

    setLoading(true);
    setError(null);

    try {
      console.log(`🔄 Refreshing onboarding status for user ${userId}`);
      const newStatus = await onboardingService.getUserOnboardingStatus(userId);
      setStatus(newStatus);
      console.log('✅ Onboarding status refreshed successfully');
    } catch (err: any) {
      console.error('❌ Failed to refresh onboarding status:', err);
      setError(err.message || 'Failed to load onboarding status');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Start an onboarding step
   */
  const startStep = async (stepId: string, data?: any): Promise<boolean> => {
    if (!userId) {
      setError('User ID is required');
      return false;
    }

    setError(null);

    try {
      console.log(`🎯 Starting step: ${stepId}`);
      const success = await onboardingService.startStep(userId, stepId, data);
      
      if (success) {
        // Refresh status to get updated progress
        await refreshStatus();
        console.log('✅ Step started successfully');
      } else {
        setError('Failed to start step');
      }

      return success;
    } catch (err: any) {
      console.error('❌ Failed to start step:', err);
      setError(err.message || 'Failed to start step');
      return false;
    }
  };

  /**
   * Complete an onboarding step
   */
  const completeStep = async (stepId: string, data?: any): Promise<boolean> => {
    if (!userId) {
      setError('User ID is required');
      return false;
    }

    setError(null);

    try {
      console.log(`✅ Completing step: ${stepId}`);
      const success = await onboardingService.completeStep(userId, stepId, data);
      
      if (success) {
        // Refresh status to get updated progress and achievements
        await refreshStatus();
        console.log('✅ Step completed successfully');
      } else {
        setError('Failed to complete step');
      }

      return success;
    } catch (err: any) {
      console.error('❌ Failed to complete step:', err);
      setError(err.message || 'Failed to complete step');
      return false;
    }
  };

  /**
   * Skip an onboarding step
   */
  const skipStep = async (stepId: string): Promise<boolean> => {
    if (!userId) {
      setError('User ID is required');
      return false;
    }

    setError(null);

    try {
      console.log(`⏭️ Skipping step: ${stepId}`);
      const success = await onboardingService.skipStep(userId, stepId);
      
      if (success) {
        // Refresh status to get updated progress
        await refreshStatus();
        console.log('✅ Step skipped successfully');
      } else {
        setError('Failed to skip step');
      }

      return success;
    } catch (err: any) {
      console.error('❌ Failed to skip step:', err);
      setError(err.message || 'Failed to skip step');
      return false;
    }
  };

  /**
   * Reset onboarding progress
   */
  const resetOnboarding = async (): Promise<boolean> => {
    if (!userId) {
      setError('User ID is required');
      return false;
    }

    setError(null);

    try {
      console.log('🔄 Resetting onboarding progress');
      const success = await onboardingService.resetOnboarding(userId);
      
      if (success) {
        // Refresh status to get reset state
        await refreshStatus();
        console.log('✅ Onboarding reset successfully');
      } else {
        setError('Failed to reset onboarding');
      }

      return success;
    } catch (err: any) {
      console.error('❌ Failed to reset onboarding:', err);
      setError(err.message || 'Failed to reset onboarding');
      return false;
    }
  };

  /**
   * Check if a step is completed
   */
  const isStepCompleted = (stepId: string): boolean => {
    if (!status) return false;
    
    // This would require getting the user's progress data
    // For now, we'll use a simple check based on the current implementation
    return false; // TODO: Implement based on actual progress data
  };

  /**
   * Check if a step is available (dependencies met)
   */
  const isStepAvailable = (stepId: string): boolean => {
    if (!status) return false;
    
    return status.next_steps.some(step => step.id === stepId);
  };

  /**
   * Get step status
   */
  const getStepStatus = (stepId: string): 'not_started' | 'in_progress' | 'completed' | 'skipped' | 'locked' => {
    if (!status) return 'not_started';
    
    // Check if step is in next available steps
    if (status.next_steps.some(step => step.id === stepId)) {
      return 'not_started';
    }
    
    // Check if it's the current step
    if (status.current_step?.id === stepId) {
      return 'in_progress';
    }
    
    // For now, return not_started as default
    // TODO: Implement based on actual progress data
    return 'not_started';
  };

  // Computed values
  const isComplete = status?.overall_progress === 100;
  const currentStepId = status?.current_step?.id || null;
  const nextSteps = status?.next_steps || [];
  const achievements = status?.achievements || [];
  const progressPercentage = status?.overall_progress || 0;
  const estimatedTimeRemaining = status?.estimated_completion_time || 0;

  return {
    // State
    status,
    loading,
    error,
    
    // Actions
    startStep,
    completeStep,
    skipStep,
    refreshStatus,
    resetOnboarding,
    
    // Computed
    isComplete,
    currentStepId,
    nextSteps,
    achievements,
    progressPercentage,
    estimatedTimeRemaining,
    
    // Helpers
    isStepCompleted,
    isStepAvailable,
    getStepStatus
  };
};
