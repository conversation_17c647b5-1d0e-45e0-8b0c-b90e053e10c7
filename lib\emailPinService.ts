import { getServiceRoleClient } from './supabase';

interface EmailPinVerification {
  id?: string;
  user_id: number;
  email: string;
  pin_code: string;
  pin_type: string;
  purpose: 'withdrawal' | 'security_change' | 'profile_update';
  expires_at: string;
  verified: boolean;
  used_at?: string;
  attempts?: number;
  created_at?: string;
}

export class EmailPinService {
  private static generatePin(): string {
    // Generate 6-digit PIN
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  private static async sendPinEmail(email: string, pin: string, purpose: string): Promise<boolean> {
    try {
      console.log(`📧 Sending PIN email to ${email} for ${purpose}`);

      // Use the same API endpoint that works for password reset
      const response = await fetch('/api/send-verification-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email,
          code: pin,
          purpose: purpose === 'security_change' ? 'security_verification' : purpose,
          userName: 'User',
          expiryMinutes: 10
        }),
      });

      const result = await response.json();

      if (result.success) {
        console.log('✅ PIN email sent successfully:', result.messageId);
        return true;
      } else {
        console.error('❌ Failed to send PIN email:', result.error);
        return false;
      }
    } catch (error) {
      console.error('❌ Error sending PIN email:', error);
      return false;
    }
  }

  static async generateAndSendPin(
    userId: number, 
    email: string, 
    purpose: 'withdrawal' | 'security_change' | 'profile_update'
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const serviceClient = getServiceRoleClient();
      const pin = this.generatePin();
      const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes from now

      // Store PIN in database
      const { data, error } = await serviceClient
        .from('email_verification_pins')
        .insert({
          user_id: userId,
          email: email,
          pin_code: pin,
          pin_type: 'security', // Map purpose to pin_type for database compatibility
          purpose: purpose,
          expires_at: expiresAt.toISOString(),
          verified: false,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Error storing PIN:', error);
        return { success: false, error: 'Failed to generate verification PIN' };
      }

      // Send PIN via email
      const emailSent = await this.sendPinEmail(email, pin, purpose);
      
      if (!emailSent) {
        return { success: false, error: 'Failed to send verification email' };
      }

      console.log('✅ PIN generated and sent successfully');
      return { success: true };

    } catch (error) {
      console.error('❌ Error in generateAndSendPin:', error);
      return { success: false, error: 'Failed to generate verification PIN' };
    }
  }

  static async verifyPin(
    userId: number, 
    pin: string, 
    purpose: 'withdrawal' | 'security_change' | 'profile_update'
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const serviceClient = getServiceRoleClient();

      // Find valid PIN
      const { data: pinRecord, error: findError } = await serviceClient
        .from('email_verification_pins')
        .select('*')
        .eq('user_id', userId)
        .eq('pin_code', pin)
        .eq('purpose', purpose)
        .eq('verified', false)
        .gt('expires_at', new Date().toISOString())
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (findError || !pinRecord) {
        console.log('❌ Invalid or expired PIN');
        return { success: false, error: 'Invalid or expired PIN' };
      }

      // Mark PIN as verified
      const { error: updateError } = await serviceClient
        .from('email_verification_pins')
        .update({
          verified: true,
          used_at: new Date().toISOString()
        })
        .eq('id', pinRecord.id);

      if (updateError) {
        console.error('❌ Error updating PIN verification:', updateError);
        return { success: false, error: 'Failed to verify PIN' };
      }

      console.log('✅ PIN verified successfully');
      return { success: true };

    } catch (error) {
      console.error('❌ Error in verifyPin:', error);
      return { success: false, error: 'Failed to verify PIN' };
    }
  }

  static async cleanupExpiredPins(): Promise<void> {
    try {
      const serviceClient = getServiceRoleClient();
      
      const { error } = await serviceClient
        .from('email_verification_pins')
        .delete()
        .lt('expires_at', new Date().toISOString());

      if (error) {
        console.error('❌ Error cleaning up expired PINs:', error);
      } else {
        console.log('✅ Expired PINs cleaned up');
      }
    } catch (error) {
      console.error('❌ Error in cleanupExpiredPins:', error);
    }
  }

  static async toggle2FA(userId: number, enabled: boolean): Promise<{ success: boolean; error?: string }> {
    try {
      const serviceClient = getServiceRoleClient();

      const { error } = await serviceClient
        .from('users')
        .update({
          two_factor_enabled: enabled,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) {
        console.error('❌ Error toggling 2FA:', error);
        return { success: false, error: 'Failed to update 2FA settings' };
      }

      console.log(`✅ 2FA ${enabled ? 'enabled' : 'disabled'} for user ${userId}`);
      return { success: true };

    } catch (error) {
      console.error('❌ Error in toggle2FA:', error);
      return { success: false, error: 'Failed to update 2FA settings' };
    }
  }
}

// Auto-cleanup expired PINs every hour
if (typeof window !== 'undefined') {
  setInterval(() => {
    EmailPinService.cleanupExpiredPins();
  }, 60 * 60 * 1000); // 1 hour
}
