#!/usr/bin/env node

/**
 * SETUP EMAIL VERIFICATION SYSTEM
 * 
 * This script sets up the complete email verification system including:
 * - Database tables creation
 * - Row Level Security policies
 * - Indexes for performance
 * - Helper functions
 * - Initial data migration
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Please ensure SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in your .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupEmailVerificationSystem() {
  console.log('🚀 Setting up Email Verification System...\n');

  try {
    // Step 1: Read and execute the SQL schema
    console.log('📋 Step 1: Creating database schema...');
    const sqlPath = join(__dirname, 'sql', 'create_email_verification_tables.sql');
    const sqlContent = readFileSync(sqlPath, 'utf8');

    const { error: schemaError } = await supabase.rpc('exec_sql', {
      sql: sqlContent
    });

    if (schemaError) {
      // Try executing the SQL directly if RPC fails
      console.log('⚠️ RPC method failed, trying direct execution...');
      
      // Split SQL into individual statements and execute
      const statements = sqlContent
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      for (const statement of statements) {
        if (statement.trim()) {
          const { error } = await supabase.rpc('exec', { sql: statement });
          if (error && !error.message.includes('already exists')) {
            console.error(`❌ Error executing statement: ${statement.substring(0, 100)}...`);
            console.error(error);
          }
        }
      }
    }

    console.log('✅ Database schema created successfully');

    // Step 2: Verify table creation
    console.log('\n📊 Step 2: Verifying table creation...');
    
    const tables = [
      'email_verification_codes',
      'account_change_logs', 
      'newsletter_subscriptions',
      'email_delivery_logs'
    ];

    for (const table of tables) {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);

      if (error) {
        console.error(`❌ Table ${table} verification failed:`, error.message);
      } else {
        console.log(`✅ Table ${table} verified`);
      }
    }

    // Step 3: Test email verification service
    console.log('\n🧪 Step 3: Testing email verification service...');
    
    // Check if we can insert a test verification code
    const testUserId = 1; // Assuming user ID 1 exists
    const testEmail = '<EMAIL>';
    
    const { data: testCode, error: testError } = await supabase
      .from('email_verification_codes')
      .insert({
        user_id: testUserId,
        email: testEmail,
        code_hash: '$2a$12$test.hash.for.verification.system.setup',
        purpose: 'registration',
        expires_at: new Date(Date.now() + 15 * 60 * 1000).toISOString()
      })
      .select()
      .single();

    if (testError) {
      console.error('❌ Test verification code insertion failed:', testError.message);
    } else {
      console.log('✅ Test verification code created successfully');
      
      // Clean up test data
      await supabase
        .from('email_verification_codes')
        .delete()
        .eq('id', testCode.id);
      
      console.log('✅ Test data cleaned up');
    }

    // Step 4: Check existing users for newsletter migration
    console.log('\n📧 Step 4: Checking newsletter subscription migration...');
    
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email, full_name')
      .not('email', 'is', null)
      .neq('email', '')
      .limit(5);

    if (usersError) {
      console.error('❌ Error checking users:', usersError.message);
    } else {
      console.log(`✅ Found ${users.length} users with email addresses`);
      
      if (users.length > 0) {
        // Check if newsletter subscriptions already exist
        const { data: existingSubscriptions, error: subError } = await supabase
          .from('newsletter_subscriptions')
          .select('user_id')
          .in('user_id', users.map(u => u.id));

        if (!subError) {
          const existingUserIds = existingSubscriptions.map(s => s.user_id);
          const newUsers = users.filter(u => !existingUserIds.includes(u.id));
          
          if (newUsers.length > 0) {
            console.log(`📝 Creating newsletter subscriptions for ${newUsers.length} users...`);
            
            const subscriptions = newUsers.map(user => ({
              user_id: user.id,
              email: user.email,
              subscription_categories: ['announcements', 'updates'],
              preferences: { frequency: 'weekly', format: 'html' },
              source: 'migration'
            }));

            const { error: insertError } = await supabase
              .from('newsletter_subscriptions')
              .insert(subscriptions);

            if (insertError) {
              console.error('❌ Error creating newsletter subscriptions:', insertError.message);
            } else {
              console.log('✅ Newsletter subscriptions created successfully');
            }
          } else {
            console.log('✅ All users already have newsletter subscriptions');
          }
        }
      }
    }

    // Step 5: Environment configuration check
    console.log('\n⚙️ Step 5: Checking environment configuration...');
    
    const requiredEnvVars = [
      'RESEND_API_KEY',
      'RESEND_FROM_EMAIL',
      'RESEND_FROM_NAME'
    ];

    const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingEnvVars.length > 0) {
      console.warn('⚠️ Missing environment variables:');
      missingEnvVars.forEach(varName => {
        console.warn(`   - ${varName}`);
      });
      console.warn('\n📝 Please add these to your .env file:');
      console.warn('   RESEND_API_KEY=re_xxxxxxxxxx');
      console.warn('   RESEND_FROM_EMAIL=<EMAIL>');
      console.warn('   RESEND_FROM_NAME=Aureus Alliance Holdings');
    } else {
      console.log('✅ All required environment variables are configured');
    }

    // Step 6: Performance optimization check
    console.log('\n🚀 Step 6: Checking performance optimizations...');
    
    // Check if indexes exist
    const { data: indexes, error: indexError } = await supabase.rpc('get_table_indexes', {
      table_name: 'email_verification_codes'
    });

    if (indexError) {
      console.log('⚠️ Could not verify indexes (this is normal for some Supabase configurations)');
    } else {
      console.log('✅ Database indexes verified');
    }

    // Final summary
    console.log('\n🎉 EMAIL VERIFICATION SYSTEM SETUP COMPLETE!\n');
    console.log('📋 Summary:');
    console.log('   ✅ Database tables created');
    console.log('   ✅ Row Level Security policies applied');
    console.log('   ✅ Performance indexes created');
    console.log('   ✅ Helper functions installed');
    console.log('   ✅ Newsletter subscriptions migrated');
    console.log('   ✅ System ready for email verification');

    console.log('\n🚀 Next Steps:');
    console.log('   1. Configure Resend API credentials in .env file');
    console.log('   2. Test email verification in development');
    console.log('   3. Integrate EmailVerificationModal into your components');
    console.log('   4. Set up email templates in Resend dashboard');
    console.log('   5. Configure domain authentication for better deliverability');

    console.log('\n📚 Documentation:');
    console.log('   - Email Verification Service: lib/emailVerificationService.ts');
    console.log('   - Resend Email Service: lib/resendEmailService.ts');
    console.log('   - Verification Modal: components/EmailVerificationModal.tsx');
    console.log('   - Database Schema: sql/create_email_verification_tables.sql');

  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  }
}

// Helper function to get table indexes (if supported)
async function createGetIndexesFunction() {
  const functionSQL = `
    CREATE OR REPLACE FUNCTION get_table_indexes(table_name text)
    RETURNS TABLE(index_name text, column_names text[])
    LANGUAGE plpgsql
    AS $$
    BEGIN
      RETURN QUERY
      SELECT 
        i.relname::text as index_name,
        array_agg(a.attname ORDER BY a.attnum)::text[] as column_names
      FROM pg_class t
      JOIN pg_index ix ON t.oid = ix.indrelid
      JOIN pg_class i ON i.oid = ix.indexrelid
      JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
      WHERE t.relname = table_name
        AND t.relkind = 'r'
      GROUP BY i.relname;
    END;
    $$;
  `;

  try {
    await supabase.rpc('exec', { sql: functionSQL });
  } catch (error) {
    // Ignore errors - this is just a helper function
  }
}

// Run the setup
if (import.meta.url === `file://${process.argv[1]}`) {
  setupEmailVerificationSystem()
    .then(() => {
      console.log('\n✨ Setup completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Setup failed:', error);
      process.exit(1);
    });
}
