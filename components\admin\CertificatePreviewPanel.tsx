import React, { useState, useEffect, useRef } from 'react';
import { svgCertificateGenerator } from '../../lib/svgCertificateGenerator';

interface CertificateTemplateElement {
  id: string;
  element_id: string;
  element_type: 'text' | 'image' | 'line' | 'rectangle' | 'watermark';
  template_version: string;
  x_position: number;
  y_position: number;
  width?: number;
  height?: number;
  font_family?: string;
  font_size?: number;
  font_weight?: string;
  font_color?: string;
  text_anchor?: string;
  static_content?: string;
  data_field?: string;
  is_dynamic: boolean;
  opacity?: number;
  rotation?: number;
  z_index?: number;
  description?: string;
  is_active: boolean;
}

interface TestCertificateData {
  userName: string;
  userIdNumber: string;
  addressLine1: string;
  addressLine2: string;
  sharesCount: number;
  certificateNumber: string;
  refNumber: string;
  issueDate: string;
  sunId: string;
}

interface CertificatePreviewPanelProps {
  testData: TestCertificateData;
  selectedElement?: CertificateTemplateElement | null;
  onElementUpdate?: (element: CertificateTemplateElement) => void;
  refreshTrigger?: number;
}

export const CertificatePreviewPanel: React.FC<CertificatePreviewPanelProps> = ({
  testData,
  selectedElement,
  onElementUpdate,
  refreshTrigger = 0
}) => {
  const [previewSvg, setPreviewSvg] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [templateLoaded, setTemplateLoaded] = useState(false);
  const [error, setError] = useState<string>('');
  const previewRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    loadTemplate();
  }, []);

  useEffect(() => {
    if (templateLoaded) {
      generatePreview();
    }
  }, [testData, templateLoaded, refreshTrigger]);

  const loadTemplate = async () => {
    try {
      await svgCertificateGenerator.loadTemplate();
      setTemplateLoaded(true);
    } catch (error) {
      console.error('Error loading template:', error);
      setError('Failed to load certificate template');
    }
  };

  const generatePreview = async () => {
    try {
      setLoading(true);
      setError('');

      // Use the SVG certificate generator to create test certificate
      const testCertificate = await svgCertificateGenerator.generateTestCertificate({
        userName: testData.userName,
        userIdNumber: testData.userIdNumber,
        addressLine1: testData.addressLine1,
        addressLine2: testData.addressLine2,
        sharesCount: testData.sharesCount,
        certificateNumber: testData.certificateNumber,
        sunId: testData.sunId,
        refNumber: testData.refNumber,
        issueDate: testData.issueDate
      });

      if (testCertificate) {
        // Add visual indicators for selected element
        let enhancedSvg = testCertificate;
        if (selectedElement) {
          enhancedSvg = addElementHighlight(testCertificate, selectedElement);
        }
        setPreviewSvg(enhancedSvg);
      } else {
        throw new Error('Failed to generate test certificate');
      }
    } catch (error) {
      console.error('Error generating preview:', error);
      setError('Failed to generate certificate preview');
    } finally {
      setLoading(false);
    }
  };

  const addElementHighlight = (svgContent: string, element: CertificateTemplateElement): string => {
    // Find the closing </svg> tag and insert highlight before it
    const closingSvgIndex = svgContent.lastIndexOf('</svg>');
    if (closingSvgIndex === -1) {
      return svgContent;
    }

    // Create a highlight rectangle around the selected element
    const highlightPadding = 5;
    const highlightWidth = element.width || (element.font_size ? element.font_size * 10 : 100);
    const highlightHeight = element.height || (element.font_size ? element.font_size * 1.5 : 20);

    const highlight = `
  <!-- Element boundary box for ${element.element_id} -->
  <rect
    x="${element.x_position}"
    y="${element.y_position - highlightHeight}"
    width="${highlightWidth}"
    height="${highlightHeight}"
    fill="rgba(255, 215, 0, 0.1)"
    stroke="#FFD700"
    stroke-width="1"
    stroke-dasharray="3,3"
    opacity="0.8"
  />
  <!-- Element highlight border -->
  <rect
    x="${element.x_position - highlightPadding}"
    y="${element.y_position - highlightHeight - highlightPadding}"
    width="${highlightWidth + (highlightPadding * 2)}"
    height="${highlightHeight + (highlightPadding * 2)}"
    fill="none"
    stroke="#FFD700"
    stroke-width="2"
    stroke-dasharray="5,5"
    opacity="0.9"
  />
  <!-- Crosshair for precise positioning -->
  <line
    x1="${element.x_position - 10}"
    y1="${element.y_position}"
    x2="${element.x_position + 10}"
    y2="${element.y_position}"
    stroke="#FF4444"
    stroke-width="1"
  />
  <line
    x1="${element.x_position}"
    y1="${element.y_position - 10}"
    x2="${element.x_position}"
    y2="${element.y_position + 10}"
    stroke="#FF4444"
    stroke-width="1"
  />
  <!-- Width indicator lines -->
  <line
    x1="${element.x_position}"
    y1="${element.y_position + 15}"
    x2="${element.x_position + highlightWidth}"
    y2="${element.y_position + 15}"
    stroke="#00FF00"
    stroke-width="2"
    opacity="0.7"
  />
  <!-- Width markers -->
  <line
    x1="${element.x_position}"
    y1="${element.y_position + 12}"
    x2="${element.x_position}"
    y2="${element.y_position + 18}"
    stroke="#00FF00"
    stroke-width="2"
  />
  <line
    x1="${element.x_position + highlightWidth}"
    y1="${element.y_position + 12}"
    x2="${element.x_position + highlightWidth}"
    y2="${element.y_position + 18}"
    stroke="#00FF00"
    stroke-width="2"
  />
  <!-- Position and dimension labels -->
  <text
    x="${element.x_position + 15}"
    y="${element.y_position - 5}"
    font-family="Arial, sans-serif"
    font-size="10"
    fill="#FFD700"
    font-weight="bold"
  >
    ${element.element_id}
  </text>
  <text
    x="${element.x_position + 15}"
    y="${element.y_position + 8}"
    font-family="Arial, sans-serif"
    font-size="9"
    fill="#FFD700"
    font-weight="normal"
  >
    (${element.x_position}, ${element.y_position}) ${highlightWidth}×${highlightHeight}px
  </text>
`;

    return svgContent.slice(0, closingSvgIndex) + highlight + svgContent.slice(closingSvgIndex);
  };

  const downloadPreview = () => {
    if (!previewSvg) return;

    const blob = new Blob([previewSvg], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `certificate-preview-${testData.certificateNumber}.svg`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const refreshPreview = () => {
    generatePreview();
  };

  return (
    <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-white">🔍 Real-time Certificate Preview</h2>
        <div className="flex gap-2">
          <button
            onClick={refreshPreview}
            disabled={loading}
            className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm disabled:opacity-50"
          >
            {loading ? '🔄' : '↻'} Refresh
          </button>
          <button
            onClick={downloadPreview}
            disabled={!previewSvg || loading}
            className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-sm disabled:opacity-50"
          >
            📥 Download SVG
          </button>
        </div>
      </div>

      {/* Template Status */}
      <div className="mb-4">
        <div className="flex items-center gap-2 text-sm">
          <span className="text-gray-400">Template Status:</span>
          {templateLoaded ? (
            <span className="text-green-400 flex items-center">
              ✅ Loaded
            </span>
          ) : (
            <span className="text-yellow-400 flex items-center">
              ⏳ Loading...
            </span>
          )}
        </div>
        <div className="flex items-center gap-2 text-sm mt-1">
          <span className="text-gray-400">Dimensions:</span>
          <span className="text-white font-mono">1123px × 794px (300 DPI)</span>
        </div>
      </div>

      {/* Selected Element Info */}
      {selectedElement && (
        <div className="mb-4 p-3 bg-gray-700 rounded border border-yellow-500">
          <div className="text-sm">
            <span className="text-yellow-400 font-medium">Selected Element:</span>
            <span className="ml-2 text-white">{selectedElement.element_id}</span>
          </div>
          <div className="text-xs text-gray-300 mt-1">
            Position: ({selectedElement.x_position}, {selectedElement.y_position}) •
            Size: {selectedElement.width || 'auto'}×{selectedElement.height || 'auto'}px •
            Font: {selectedElement.font_size}px {selectedElement.font_family}
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="mb-4 p-3 bg-red-900 border border-red-600 rounded">
          <div className="text-red-300 text-sm">❌ {error}</div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400"></div>
          <span className="ml-3 text-gray-300">Generating preview...</span>
        </div>
      )}

      {/* Certificate Preview */}
      {previewSvg && !loading && (
        <div 
          ref={previewRef}
          className="border border-gray-600 rounded bg-white p-4 overflow-auto"
          style={{ maxHeight: '600px' }}
        >
          <div 
            className="certificate-preview"
            dangerouslySetInnerHTML={{ __html: previewSvg }}
            style={{ 
              transform: 'scale(0.8)', 
              transformOrigin: 'top left',
              width: '125%' // Compensate for scale
            }}
          />
        </div>
      )}

      {/* Preview Instructions */}
      <div className="mt-4 text-xs text-gray-400">
        <p>💡 <strong>Tips:</strong></p>
        <ul className="list-disc list-inside mt-1 space-y-1">
          <li>Selected elements are highlighted with a golden dashed border</li>
          <li>Red crosshairs show the exact positioning coordinates</li>
          <li>Preview updates automatically when you adjust positioning</li>
          <li>Use the refresh button if the preview doesn't update</li>
        </ul>
      </div>
    </div>
  );
};
