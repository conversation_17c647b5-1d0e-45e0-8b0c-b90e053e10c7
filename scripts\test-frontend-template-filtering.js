import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.VITE_SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);

async function testFrontendTemplateFiltering() {
  console.log('🧪 Testing Frontend Template Filtering...\n');

  try {
    // Test the same filtering logic that the service now uses
    console.log('📧 Testing filtered email templates query...');
    const systemTemplateNames = [
      'share_purchase_confirmation',
      'verification_email',
      'conversion_confirmation',
      'withdrawal_confirmation',
      'welcome_email',
      'password_reset',
      'commission_notification'
    ];

    const { data: templates, error } = await supabase
      .from('email_templates')
      .select('*')
      .eq('is_active', true)
      .not('template_name', 'in', `(${systemTemplateNames.join(',')})`)
      .order('created_at', { ascending: false });

    if (error) throw error;

    console.log(`✅ Found ${templates.length} filtered templates:`);
    templates.forEach(template => {
      console.log(`   📧 ${template.template_name} (${template.template_type})`);
    });

    // Check if any system templates leaked through
    const systemTemplatesFound = templates.filter(template =>
      systemTemplateNames.includes(template.template_name)
    );

    if (systemTemplatesFound.length === 0) {
      console.log('\n✅ SUCCESS: Frontend will now show only marketing templates!');
      console.log('🎉 The fix is working - refresh your browser to see the change.');
    } else {
      console.log('\n❌ FAILURE: System templates still present:');
      systemTemplatesFound.forEach(template => {
        console.log(`   🔧 ${template.template_name}`);
      });
    }

    console.log('\n📊 SUMMARY:');
    console.log(`   Marketing templates: ${templates.length}`);
    console.log(`   System templates leaked: ${systemTemplatesFound.length}`);
    console.log(`   Fix working: ${systemTemplatesFound.length === 0 ? '✅ YES' : '❌ NO'}`);

  } catch (error) {
    console.error('❌ Error testing frontend template filtering:', error.message);
  }
}

testFrontendTemplateFiltering();
