import React, { useState, useEffect } from 'react';
import { ComprehensivePhaseDisplay } from '../components/ComprehensivePhaseDisplay';
import { getServiceRoleClient, getCurrentUser } from '../lib/supabase';

interface SharePhasesPageProps {
  onNavigate: (page: string) => void;
}

interface PhaseData {
  id: number;
  phase_number: number;
  phase_name: string;
  price_per_share: number;
  total_shares_available: number;
  shares_sold: number;
  is_active: boolean;
  start_date?: string;
  end_date?: string;
}

const SharePhasesPage: React.FC<SharePhasesPageProps> = ({ onNavigate }) => {
  const [phases, setPhases] = useState<PhaseData[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPhaseIndex, setSelectedPhaseIndex] = useState(0); // Track which phase tab is active
  const [user, setUser] = useState<any>(null);

  // Check authentication status on component mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const currentUser = await getCurrentUser();
      setUser(currentUser);
    } catch (error) {
      console.error('Error checking auth status:', error);
      setUser(null);
    }
  };

  useEffect(() => {
    fetchPhases();
  }, []);

  const fetchPhases = async () => {
    try {
      const supabase = getServiceRoleClient();
      const { data, error } = await supabase
        .from('investment_phases')
        .select('*')
        .order('phase_number', { ascending: true });

      if (error) {
        console.error('Error fetching phases:', error);
        return;
      }

      setPhases(data || []);
    } catch (error) {
      console.error('Error fetching phases:', error);
    } finally {
      setLoading(false);
    }
  };



  const handlePurchaseShares = (phaseNumber: number) => {
    console.log('🛒 Purchase Shares clicked for phase:', phaseNumber);

    // Store the selected phase for the purchase flow
    localStorage.setItem('aureus_selected_phase', phaseNumber.toString());
    console.log('✅ Phase stored in localStorage:', phaseNumber);

    // Check authentication before proceeding
    if (user) {
      // User is authenticated, proceed to purchase shares
      console.log('✅ User authenticated, proceeding to purchase shares');
      window.location.href = '/purchase-shares';
    } else {
      // User is not authenticated, redirect to login with purchase intent
      console.log('❌ User not authenticated, redirecting to login');
      localStorage.setItem('aureus_post_login_redirect', '/purchase-shares');
      window.location.href = '/login';
    }
  };

  const getPhaseStatus = (phase: PhaseData) => {
    const sharesRemaining = phase.total_shares_available - phase.shares_sold;
    const percentageSold = (phase.shares_sold / phase.total_shares_available) * 100;

    if (phase.is_active) {
      return { status: 'Active', color: '#10B981', bgColor: 'rgba(16, 185, 129, 0.1)' };
    } else if (percentageSold >= 100) {
      return { status: 'Sold Out', color: '#EF4444', bgColor: 'rgba(239, 68, 68, 0.1)' };
    } else if (percentageSold > 0) {
      return { status: 'In Progress', color: '#F59E0B', bgColor: 'rgba(245, 158, 11, 0.1)' };
    } else {
      return { status: 'Coming Soon', color: '#6B7280', bgColor: 'rgba(107, 114, 128, 0.1)' };
    }
  };

  const getPhaseDetails = (phaseNumber: number) => {
    const phaseDetailsMap: { [key: number]: { operational: string[], csr: string[], totalFunding: string } } = {
      0: { // Presale
        operational: [
          "Launch First Wash Plant at Kadoma",
          "Infrastructure Development: Cut access roads and secure 25 hectares with fencing",
          "Acquire Machinery: Purchase state-of-the-art gold wash plant equipment",
          "Hire Core Team & Management: Establish the first operational workforce"
        ],
        csr: [
          "Feed Over 2,000 Children through food security programs",
          "Install 4 Boreholes for clean and safe drinking water",
          "Support Healthcare via 2 clinic supply drops",
          "Empower Education with 100+ funded school bursaries"
        ],
        totalFunding: "$1,000,000"
      },
      1: {
        operational: [
          "Complete First Wash Plant on 25 hectares",
          "Infrastructure Upgrades: Finalize roads, fencing, and machinery installation",
          "Hire Full Operational Team to manage production",
          "Begin Gold Production on the first 25 hectares"
        ],
        csr: [
          "Build 2 New Classrooms to support local education",
          "Install 3 Boreholes to provide clean, accessible water",
          "Feed Over 5,000 Children through expanded food programs",
          "Supply Medical Kits to 3 rural clinics"
        ],
        totalFunding: "$1,500,000"
      },
      2: {
        operational: [
          "Launch Second Wash Plant at Kadoma",
          "Expand Gold Production capacity for higher output",
          "Accelerate Site Development across additional hectares",
          "Cover Ongoing Operational Costs to ensure smooth scaling"
        ],
        csr: [
          "Build 2 Rural School Blocks to improve education access",
          "Drill 4 Clean Boreholes to provide safe drinking water",
          "Feed Over 7,000 Children through expanded programs",
          "Support 5 Clinics with essential medical supplies"
        ],
        totalFunding: "$1,875,000"
      },
      3: {
        operational: [
          "Launch Third Wash Plant at Kadoma",
          "Increase Gold Production capacity significantly",
          "Extend Site Development across additional hectares",
          "Cover Key Operational Costs to sustain rapid growth"
        ],
        csr: [
          "Build a Rural Solar-Powered School to provide sustainable education facilities",
          "Drill 5 Boreholes to deliver clean drinking water",
          "Feed Over 10,000 Children through community nutrition programs",
          "Supply 7 Rural Clinics with vital medical provisions"
        ],
        totalFunding: "$2,000,000"
      },
      4: {
        operational: [
          "Launch Fourth Wash Plant at Kadoma",
          "Boost Gold Production to increase annual output",
          "Advance Site Development across new hectares",
          "Cover Essential Operational Costs for sustained growth"
        ],
        csr: [
          "Build a Rural Solar-Powered School to support quality education",
          "Drill 5 Boreholes to ensure access to clean water",
          "Feed Over 10,000 Children through extended nutrition programs",
          "Supply 7 Clinics with essential medical equipment and medicine"
        ],
        totalFunding: "$1,875,000"
      },
      5: {
        operational: [
          "Launch Fifth Wash Plant at Kadoma",
          "Expand Gold Production to boost overall output",
          "Advance Site Development across additional hectares",
          "Cover Ongoing Operational Costs to sustain efficiency and growth"
        ],
        csr: [
          "Build a Rural Solar-Powered School to support sustainable education",
          "Drill 5 Boreholes to provide clean and safe water",
          "Feed Over 12,000 Children through expanded food programs",
          "Sponsor 200+ School Uniforms and Books to assist local students"
        ],
        totalFunding: "$1,500,000"
      },
      6: {
        operational: [
          "Launch Sixth Wash Plant at Kadoma",
          "Increase Gold Production to maximize output",
          "Expand Site Development across additional hectares",
          "Cover Operational Costs to maintain efficiency and growth"
        ],
        csr: [
          "Establish a Mobile Health Unit to reach underserved rural areas",
          "Build a Rural Solar-Powered School to enhance sustainable education",
          "Install 6 Water Purification Tanks for safe, clean drinking water",
          "Feed Over 15,000 Children through nutrition and outreach programs",
          "Sponsor 200+ Full School Scholarships to empower future generations"
        ],
        totalFunding: "$1,750,000"
      },
      7: {
        operational: [
          "Launch Seventh Wash Plant at Kadoma",
          "Boost Gold Production for higher annual output",
          "Begin Site Preparation at Mutare for future wash plant deployment",
          "Cover Operational Costs to ensure smooth scaling"
        ],
        csr: [
          "Establish 2 Mobile Health Units to serve remote rural areas",
          "Build a Rural Solar-Powered School for sustainable education",
          "Install 8 Water Purification Tanks to provide clean, safe drinking water",
          "Feed Over 18,000 Children through community nutrition initiatives",
          "Sponsor 200+ Full School Scholarships to empower underprivileged students"
        ],
        totalFunding: "$2,000,000"
      },
      8: {
        operational: [
          "Launch Eighth Wash Plant at Kadoma",
          "Expand Gold Production to increase total annual output",
          "Advance Site Preparation at Mutare for upcoming operations",
          "Cover Operational Costs to ensure sustained efficiency"
        ],
        csr: [
          "Establish 2 Mobile Health Units to support rural medical access",
          "Begin Development of a Hospital to serve surrounding communities",
          "Install 10 Water Purification Tanks for clean, safe drinking water",
          "Feed Over 20,000 Children through community nutrition programs",
          "Sponsor 200+ Full School Scholarships to uplift local education"
        ],
        totalFunding: "$2,250,000"
      },
      9: {
        operational: [
          "Launch Ninth Wash Plant at Kadoma",
          "Expand Gold Production to maximize annual output",
          "Finalize Site Preparation at Mutare for upcoming operations",
          "Cover Operational Costs to ensure efficient scaling"
        ],
        csr: [
          "Establish 2 Mobile Health Units to reach underserved communities",
          "Continue Development of the Hospital to improve healthcare infrastructure",
          "Install 12 Water Purification Tanks to provide safe, clean drinking water",
          "Feed Over 22,000 Children through expanded nutrition programs",
          "Sponsor 500+ Full School Scholarships to empower future generations"
        ],
        totalFunding: "$2,500,000"
      },
      10: {
        operational: [
          "Launch Tenth Wash Plant at Kadoma",
          "Scale Gold Production across 550 hectares",
          "Begin Operations on First Two Wash Plants at Mutare",
          "Cover Operational Costs to sustain efficient, high-volume production"
        ],
        csr: [
          "Establish 2 Mobile Health Units to serve remote rural regions",
          "Continue Development of the Regional Hospital for advanced medical care",
          "Install 15 Water Purification Tanks to ensure clean, safe drinking water",
          "Feed Over 30,000 Children through expanded nutrition initiatives",
          "Sponsor 1,000+ Full School Scholarships to transform educational access"
        ],
        totalFunding: "$5,000,000"
      },
      11: {
        operational: [
          "Purchase an Additional 250 Hectares of gold-rich land",
          "Scale Gold Production across 425 hectares in total",
          "Launch 5 New Wash Plants at Mutare to maximize output",
          "Cover Operational Costs to support large-scale, efficient production"
        ],
        csr: [
          "Establish 2 Mobile Health Units to improve rural healthcare access",
          "Continue Development of the Regional Hospital for advanced medical services",
          "Install 20 Water Purification Tanks to ensure clean, safe drinking water",
          "Feed Over 50,000 Children through extended nutrition programs",
          "Sponsor 2,000+ Full School Scholarships to empower the next generation"
        ],
        totalFunding: "$410,000,000"
      },
      12: {
        operational: [
          "Purchase an Additional 250 Hectares of gold-rich land",
          "Expand Gold Production to cover 550 hectares in total",
          "Launch 10 New Wash Plants at Mutare, accelerating production capacity",
          "Cover Operational Costs to support sustainable large-scale operations"
        ],
        csr: [
          "Deploy 2 Additional Mobile Health Units to broaden healthcare access",
          "Continue Regional Hospital Development for advanced community care",
          "Install 25 Water Purification Tanks for clean, safe drinking water",
          "Feed Over 75,000 Children through enhanced food security programs",
          "Sponsor 3,000+ Full School Scholarships to empower education and opportunity"
        ],
        totalFunding: "TBD"
      },
      13: {
        operational: [
          "Launch 20 New Wash Plants at Mutare",
          "Expand Operations to 42 Active Wash Plants across 1,050 hectares",
          "Cover Operational Costs to ensure efficient, sustainable output"
        ],
        csr: [
          "Deploy 2 Additional Mobile Health Units to strengthen rural healthcare",
          "Continue Development of the Regional Hospital to provide advanced medical facilities",
          "Install 30 Water Purification Tanks to deliver clean, safe drinking water",
          "Feed Over 100,000 Children through expanded nutrition programs",
          "Sponsor 4,000+ Full School Scholarships to enable long-term educational growth"
        ],
        totalFunding: "$20,000,000"
      },
      14: {
        operational: [
          "Launch 25 New Wash Plants at Mutare",
          "Expand Operations to 57 Active Wash Plants across 1,425 hectares",
          "Cover Operational Costs to ensure seamless scalability and efficiency"
        ],
        csr: [
          "Build 2 Remote Mobile Surgical Units to deliver life-saving care in underserved regions",
          "Continue Development of the Regional Hospital for advanced medical support",
          "Install 40 Water Purification Tanks to secure clean drinking water access",
          "Feed Over 125,000 Children through nutrition and hunger-relief programs",
          "Sponsor 5,000+ Full School Scholarships to empower future generations"
        ],
        totalFunding: "$25,000,000"
      },
      15: {
        operational: [
          "Launch 30 New Wash Plants across key South African sites",
          "Expand Operations to 87 Active Wash Plants spanning 2,175 hectares",
          "Cover Operational Costs to ensure maximum efficiency and seamless scaling"
        ],
        csr: [
          "Build 5 Remote Mobile Surgical Units to provide critical healthcare in rural regions",
          "Continue Development of the Regional Hospital for advanced medical infrastructure",
          "Install 50 Water Purification Tanks to guarantee clean, safe water access",
          "Feed Over 150,000 Children through expanded nutrition programs",
          "Sponsor 6,000+ Full School Scholarships to empower future leaders"
        ],
        totalFunding: "$30,000,000"
      },
      16: {
        operational: [
          "Launch 35 New Wash Plants at strategic South African sites",
          "Expand Operations to 122 Active Wash Plants covering 3,050 hectares",
          "Cover Operational Costs to maintain scalability, efficiency, and sustainability"
        ],
        csr: [
          "Deploy 5 Mobile Maternal & Child Care Fleets to deliver critical healthcare services",
          "Continue Development of the Regional Hospital to enhance medical infrastructure",
          "Install 60 Water Purification Tanks ensuring access to clean, safe water",
          "Feed Over 175,000 Children through nutrition and hunger-relief initiatives",
          "Sponsor 7,500+ Full School Scholarships to uplift future generations"
        ],
        totalFunding: "$35,000,000"
      },
      17: {
        operational: [
          "Launch 40 New Wash Plants across high-yield Zambian sites",
          "Scale Operations to 162 Active Wash Plants spanning 4,050 hectares",
          "Cover Operational Costs to ensure operational efficiency and sustainable growth"
        ],
        csr: [
          "Deploy 5 Mobile Maternal & Child Care Fleets to deliver essential medical services",
          "Advance Regional Hospital Development for long-term healthcare infrastructure",
          "Install 75 Water Purification Tanks providing clean, safe drinking water",
          "Feed Over 200,000 Children through enhanced nutrition initiatives",
          "Sponsor 10,000+ Full School Scholarships to empower future leaders"
        ],
        totalFunding: "$40,000,000"
      },
      18: {
        operational: [
          "Launch 45 New Wash Plants across high-potential Zambian gold zones",
          "Scale Operations to 207 Active Wash Plants spanning 5,175 hectares",
          "Cover Operational Costs to sustain efficient mining operations and infrastructure growth"
        ],
        csr: [
          "Deploy 10 Advanced Maternal & Child Care Fleets to serve remote rural communities",
          "Continue Strategic Development of Regional Hospital Infrastructure",
          "Install 80 Water Purification Tanks ensuring clean drinking water access",
          "Feed Over 225,000 Children through expanded nutrition programs",
          "Sponsor 12,000+ Full School Scholarships to empower future generations"
        ],
        totalFunding: "$45,000,000"
      },
      19: {
        operational: [
          "Launch 45 New Wash Plants across Ghana and Tanzania",
          "Scale Operations to 257 Active Wash Plants managing 6,000+ hectares of gold-rich land",
          "Achieve 108 Tons of Annual Gold Output — estimated at $4,000 per share annually",
          "Cover Ongoing Operational Costs to sustain scalable and sustainable production"
        ],
        csr: [
          "Deploy 10 Advanced Maternal & Child Healthcare Fleets across rural regions",
          "Complete the Development of the Regional Aureus Hospital",
          "Install 100 Water Purification Tanks for clean drinking water access",
          "Feed Over 250,000 Children through nutrition and food security programs",
          "Sponsor 15,000+ Full School Scholarships to empower the next generation"
        ],
        totalFunding: "$50,000,000"
      }
    };

    return phaseDetailsMap[phaseNumber] || { operational: [], csr: [], totalFunding: "TBD" };
  };

  return (
    <div className="page">
      {/* Page Header */}
      <section className="page-header">
        <div className="container">
          <div className="breadcrumb">
            <button onClick={() => onNavigate('home')} className="breadcrumb-link">
              Home
            </button>
            <span className="breadcrumb-separator">→</span>
            <span className="breadcrumb-current">Share Phases</span>
          </div>

          <h1 className="page-title">20-Phase Share Structure</h1>
          <p className="page-subtitle">
            Progressive pricing from $5 presale to $1,000 per share with detailed fund allocation
            for operations and community impact initiatives
          </p>
        </div>
      </section>

      {/* Individual Phase Details */}
      <section className="individual-phases">
        <div className="container">
          <div className="section-header">
            <h2>Individual Phase Details</h2>
            <p>Complete breakdown of all 20 phases with live pricing and availability</p>
          </div>

          {loading ? (
            <div className="loading-state">
              <div className="loading-spinner"></div>
              <p>Loading phase data...</p>
            </div>
          ) : (
            <div className="phases-tabbed-interface">
              {/* Phase Tabs */}
              <div className="phase-tabs">
                {phases.map((phase, index) => {
                  const status = getPhaseStatus(phase);
                  const isActive = selectedPhaseIndex === index;
                  const isAvailable = status.status === 'Active' || status.status === 'In Progress';

                  return (
                    <button
                      key={phase.id}
                      onClick={() => setSelectedPhaseIndex(index)}
                      className={`phase-tab ${isActive ? 'active' : ''} ${isAvailable ? 'available' : 'unavailable'}`}
                    >
                      <div className="tab-title">
                        {phase.phase_number === 0 ? 'Presale' : `Phase ${phase.phase_number}`}
                      </div>
                      <div className="tab-price">${phase.price_per_share}</div>
                      <div className="tab-status" style={{
                        backgroundColor: status.bgColor,
                        color: status.color
                      }}>
                        {status.status}
                      </div>
                    </button>
                  );
                })}
              </div>

              {/* Selected Phase Content */}
              {phases[selectedPhaseIndex] && (() => {
                const phase = phases[selectedPhaseIndex];
                const status = getPhaseStatus(phase);
                const sharesRemaining = phase.total_shares_available - phase.shares_sold;
                const percentageSold = (phase.shares_sold / phase.total_shares_available) * 100;
                const isAvailable = status.status === 'Active' || status.status === 'In Progress';
                const phaseDetails = getPhaseDetails(phase.phase_number);

                return (
                  <div className="selected-phase-content">
                    <div className={`phase-row ${phase.is_active ? 'active' : ''}`}>
                      {/* Phase Details Column */}
                      <div className="phase-column phase-details-column">
                        <div className="phase-header">
                          <h3 className="phase-title">
                            {phase.phase_number === 0 ? 'Presale' : `Phase ${phase.phase_number}`}
                          </h3>
                          <div className="phase-status-badge" style={{
                            backgroundColor: status.bgColor,
                            color: status.color
                          }}>
                            {status.status}
                          </div>
                        </div>

                        <div className="phase-price">
                          <span className="price-value">${phase.price_per_share.toFixed(2)}</span>
                          <span className="price-label">per share</span>
                        </div>

                        <div className="shares-info">
                          <div className="shares-row">
                            <span className="shares-label">Total Available:</span>
                            <span className="shares-value">{phase.total_shares_available.toLocaleString()}</span>
                          </div>
                          <div className="shares-row">
                            <span className="shares-label">Shares Sold:</span>
                            <span className="shares-value">{phase.shares_sold.toLocaleString()}</span>
                          </div>
                          <div className="shares-row">
                            <span className="shares-label">Remaining:</span>
                            <span className="shares-value highlight">{sharesRemaining.toLocaleString()}</span>
                          </div>
                          <div className="shares-row">
                            <span className="shares-label">Total Funding:</span>
                            <span className="shares-value gold">{phaseDetails.totalFunding}</span>
                          </div>
                        </div>

                        <div className="progress-bar">
                          <div
                            className="progress-fill"
                            style={{ width: `${Math.min(percentageSold, 100)}%` }}
                          ></div>
                        </div>
                        <div className="progress-text">
                          {percentageSold.toFixed(1)}% sold
                        </div>

                        <button
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            console.log('🔥 Button clicked! Available:', isAvailable, 'Phase:', phase.phase_number);
                            if (isAvailable) {
                              handlePurchaseShares(phase.phase_number);
                            } else {
                              alert(`Phase ${phase.phase_number} is ${status.status}. You can still register interest!`);
                              handlePurchaseShares(phase.phase_number);
                            }
                          }}
                          className={`purchase-btn ${isAvailable ? 'available' : 'unavailable'}`}
                          style={{
                            cursor: 'pointer',
                            zIndex: 10,
                            position: 'relative'
                          }}
                        >
                          <span className="btn-icon">{isAvailable ? '🛒' : '📝'}</span>
                          {isAvailable ? 'Purchase Shares' : `Register Interest`}
                        </button>
                      </div>

                      {/* Operational Allocation Column */}
                      <div className="phase-column operational-column">
                        <h4 className="column-title">
                          <span className="column-icon">🏭</span>
                          Operational Allocation
                        </h4>
                        <ul className="allocation-list">
                          {phaseDetails.operational.map((item, index) => (
                            <li key={index} className="allocation-item">{item}</li>
                          ))}
                        </ul>
                      </div>

                      {/* CSR Allocation Column */}
                      <div className="phase-column csr-column">
                        <h4 className="column-title">
                          <span className="column-icon">🤝</span>
                          Corporate Social Responsibility (CSR)
                        </h4>
                        <ul className="allocation-list">
                          {phaseDetails.csr.map((item, index) => (
                            <li key={index} className="allocation-item">{item}</li>
                          ))}
                        </ul>

                        {/* Purchase Button for every phase */}
                        <button
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            console.log('🔥 CSR Purchase button clicked! Phase:', phase.phase_number);
                            if (isAvailable) {
                              handlePurchaseShares(phase.phase_number);
                            } else {
                              alert(`Phase ${phase.phase_number} is ${status.status}. You can still register interest!`);
                              handlePurchaseShares(phase.phase_number);
                            }
                          }}
                          className={`purchase-btn ${isAvailable ? 'available' : 'unavailable'}`}
                          style={{
                            marginTop: '1rem',
                            width: '100%',
                            cursor: 'pointer',
                            zIndex: 10,
                            position: 'relative'
                          }}
                        >
                          <span className="btn-icon">{isAvailable ? '🛒' : '📝'}</span>
                          {isAvailable ? `Purchase Shares - $${phase.price_per_share}` : `Register Interest - $${phase.price_per_share}`}
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })()}
            </div>
          )}
        </div>
      </section>

      {/* Key Share Points */}
      <section className="investment-points">
        <div className="container">
          <div className="section-header">
            <h2>Why Progressive Pricing?</h2>
            <p>Understanding our phase-based share structure</p>
          </div>

          <div className="points-grid">
            <div className="point-card">
              <div className="point-icon">📈</div>
              <h3>Increasing Value</h3>
              <p>
                Share prices increase as operations scale and risk decreases. Early shareholders
                benefit from the lowest prices with highest potential dividends.
              </p>
            </div>

            <div className="point-card">
              <div className="point-icon">🎯</div>
              <h3>Milestone-Based</h3>
              <p>
                Each phase funds specific operational milestones and community projects,
                ensuring transparent use of shareholder capital.
              </p>
            </div>

            <div className="point-card">
              <div className="point-icon">🔒</div>
              <h3>Risk Mitigation</h3>
              <p>
                Progressive funding reduces overall project risk while allowing shareholders
                to participate at their preferred dividend level.
              </p>
            </div>

            <div className="point-card">
              <div className="point-icon">🌍</div>
              <h3>Impact Scaling</h3>
              <p>
                Community impact grows with each phase, from feeding 2,000 children in 
                presale to 250,000+ children by Phase 19.
              </p>
            </div>
          </div>
        </div>
      </section>



      {/* Share Strategy */}
      <section className="investment-strategy">
        <div className="container">
          <div className="section-header">
            <h2>Share Purchase Strategy Recommendations</h2>
            <p>Strategic guidance tailored to different shareholder profiles and dividend preferences</p>
          </div>

          <div className="strategy-grid">
            <div className="strategy-card early-adopter">
              <div className="strategy-header">
                <div className="strategy-badge early">🚀 Early Adopter</div>
                <div className="strategy-subtitle">Ground Floor Opportunity</div>
              </div>
              <h3>Presale & Phase 1-3</h3>
              <div className="strategy-price-range">
                <span className="price-from">$5</span>
                <span className="price-separator">→</span>
                <span className="price-to">$20</span>
                <span className="price-label">per share</span>
              </div>

              <div className="strategy-highlights">
                <div className="highlight-item">
                  <span className="highlight-icon">💰</span>
                  <span>Maximum Dividend Potential</span>
                </div>
                <div className="highlight-item">
                  <span className="highlight-icon">🏗️</span>
                  <span>Foundation Phase Access</span>
                </div>
                <div className="highlight-item">
                  <span className="highlight-icon">🎯</span>
                  <span>Lowest Entry Prices</span>
                </div>
              </div>

              <div className="strategy-details">
                <h4>What You're Funding:</h4>
                <ul className="strategy-benefits">
                  <li>Launch first wash plant at Kadoma site</li>
                  <li>Infrastructure development & site preparation</li>
                  <li>Core team establishment & machinery acquisition</li>
                  <li>Community programs: Feed 2,000+ children</li>
                  <li>Install 4-12 boreholes for clean water</li>
                </ul>
              </div>

              <div className="strategy-risk early">
                <div className="risk-header">
                  <span className="risk-icon">⚠️</span>
                  <strong>Risk Profile: Higher</strong>
                </div>
                <p>Pre-operational phase with development risks but maximum upside potential</p>
              </div>

              <div className="strategy-cta">
                <button
                  className="strategy-btn early"
                  onClick={() => handlePurchaseShares(0)}
                >
                  Start with Presale - $5/share
                </button>
              </div>
            </div>

            <div className="strategy-card growth-investor">
              <div className="strategy-header">
                <div className="strategy-badge growth">📈 Growth Shareholder</div>
                <div className="strategy-subtitle">Balanced Growth Strategy</div>
              </div>
              <h3>Phase 4-10</h3>
              <div className="strategy-price-range">
                <span className="price-from">$25</span>
                <span className="price-separator">→</span>
                <span className="price-to">$100</span>
                <span className="price-label">per share</span>
              </div>

              <div className="strategy-highlights">
                <div className="highlight-item">
                  <span className="highlight-icon">⚖️</span>
                  <span>Risk-Reward Balance</span>
                </div>
                <div className="highlight-item">
                  <span className="highlight-icon">🏭</span>
                  <span>Active Operations</span>
                </div>
                <div className="highlight-item">
                  <span className="highlight-icon">📊</span>
                  <span>Proven Production Model</span>
                </div>
              </div>

              <div className="strategy-details">
                <h4>What You're Funding:</h4>
                <ul className="strategy-benefits">
                  <li>Multiple wash plants (4-10 operational units)</li>
                  <li>Scaled gold production & processing capacity</li>
                  <li>Expanded site development across hectares</li>
                  <li>Community impact: Feed 10,000+ children</li>
                  <li>Healthcare & education infrastructure</li>
                </ul>
              </div>

              <div className="strategy-risk growth">
                <div className="risk-header">
                  <span className="risk-icon">📊</span>
                  <strong>Risk Profile: Moderate</strong>
                </div>
                <p>Operational phase with proven production model and steady growth trajectory</p>
              </div>

              <div className="strategy-cta">
                <button
                  className="strategy-btn growth"
                  onClick={() => handlePurchaseShares(4)}
                >
                  Join Phase 4 - $25/share
                </button>
              </div>
            </div>

            <div className="strategy-card stable-returns">
              <div className="strategy-header">
                <div className="strategy-badge stable">🏛️ Stable Dividends</div>
                <div className="strategy-subtitle">Mature Operations Focus</div>
              </div>
              <h3>Phase 11-19</h3>
              <div className="strategy-price-range">
                <span className="price-from">$200</span>
                <span className="price-separator">→</span>
                <span className="price-to">$1,000</span>
                <span className="price-label">per share</span>
              </div>

              <div className="strategy-highlights">
                <div className="highlight-item">
                  <span className="highlight-icon">🛡️</span>
                  <span>Lower Volatility</span>
                </div>
                <div className="highlight-item">
                  <span className="highlight-icon">🌍</span>
                  <span>Multi-Country Presence</span>
                </div>
                <div className="highlight-item">
                  <span className="highlight-icon">💎</span>
                  <span>Established Operations</span>
                </div>
              </div>

              <div className="strategy-details">
                <h4>What You're Funding:</h4>
                <ul className="strategy-benefits">
                  <li>Mature operations across multiple countries</li>
                  <li>Advanced processing & refining facilities</li>
                  <li>Established supply chains & partnerships</li>
                  <li>Massive community impact: 250,000+ children</li>
                  <li>Sustainable long-term dividend streams</li>
                </ul>
              </div>

              <div className="strategy-risk stable">
                <div className="risk-header">
                  <span className="risk-icon">✅</span>
                  <strong>Risk Profile: Lower</strong>
                </div>
                <p>Mature operations with established revenue streams and predictable dividends</p>
              </div>

              <div className="strategy-cta">
                <button
                  className="strategy-btn stable"
                  onClick={() => handlePurchaseShares(11)}
                >
                  Explore Phase 11 - $200/share
                </button>
              </div>
            </div>
          </div>

          {/* Strategy Comparison Table */}
          <div className="strategy-comparison">
            <h3>Quick Comparison Guide</h3>
            <div className="comparison-table">
              <div className="comparison-header">
                <div className="comparison-cell">Strategy</div>
                <div className="comparison-cell">Entry Price</div>
                <div className="comparison-cell">Risk Level</div>
                <div className="comparison-cell">Expected Timeline</div>
                <div className="comparison-cell">Best For</div>
              </div>
              <div className="comparison-row early">
                <div className="comparison-cell"><strong>Early Adopter</strong></div>
                <div className="comparison-cell">$5 - $20</div>
                <div className="comparison-cell">Higher</div>
                <div className="comparison-cell">2-3 years to production</div>
                <div className="comparison-cell">Risk-tolerant, maximum dividends</div>
              </div>
              <div className="comparison-row growth">
                <div className="comparison-cell"><strong>Growth Shareholder</strong></div>
                <div className="comparison-cell">$25 - $100</div>
                <div className="comparison-cell">Moderate</div>
                <div className="comparison-cell">1-2 years to full scale</div>
                <div className="comparison-cell">Balanced approach, proven model</div>
              </div>
              <div className="comparison-row stable">
                <div className="comparison-cell"><strong>Stable Dividends</strong></div>
                <div className="comparison-cell">$200 - $1,000</div>
                <div className="comparison-cell">Lower</div>
                <div className="comparison-cell">Immediate operations</div>
                <div className="comparison-cell">Conservative, steady income</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Ready to Purchase Shares */}
      <section className="ready-to-invest">
        <div className="container">
          <div className="section-header">
            <h2>Ready to Purchase Shares?</h2>
            <p>Take the next step in your gold mining journey with our comprehensive tools and resources</p>
            <div className="header-highlight">
              <span className="highlight-text">Join thousands of shareholders already earning from real gold production</span>
            </div>
          </div>

          <div className="action-steps-grid">
            {/* Step 1: Calculate Dividends */}
            <div className="action-step-card calculator">
              <div className="step-number">01</div>
              <div className="step-content">
                <div className="step-icon-wrapper">
                  <div className="step-icon">🧮</div>
                  <div className="step-badge">Essential Tool</div>
                </div>
                <h3>Calculate Your Dividends</h3>
                <p className="step-description">
                  Use our advanced dividend calculator to project your potential earnings across all phases.
                  See exactly how much you could earn monthly and annually based on your share purchase amount.
                </p>
                <div className="step-features">
                  <div className="feature-item">
                    <span className="feature-icon">💰</span>
                    <span>Monthly dividend projections</span>
                  </div>
                  <div className="feature-item">
                    <span className="feature-icon">📈</span>
                    <span>Dividend calculations by phase</span>
                  </div>
                  <div className="feature-item">
                    <span className="feature-icon">⏱️</span>
                    <span>Break-even timeline analysis</span>
                  </div>
                </div>
                <button
                  className="step-action-btn calculator"
                  onClick={() => onNavigate('calculator')}
                >
                  <span className="btn-icon">🚀</span>
                  Calculate My Dividends
                </button>
              </div>
            </div>

            {/* Step 2: Review Financial Data */}
            <div className="action-step-card financial">
              <div className="step-number">02</div>
              <div className="step-content">
                <div className="step-icon-wrapper">
                  <div className="step-icon">📊</div>
                  <div className="step-badge">Due Diligence</div>
                </div>
                <h3>Review Financial Data</h3>
                <p className="step-description">
                  Examine detailed financial projections, yield tables, and operational metrics.
                  Access comprehensive data on gold production, revenue forecasts, and dividend distributions.
                </p>
                <div className="step-features">
                  <div className="feature-item">
                    <span className="feature-icon">📋</span>
                    <span>Detailed yield tables</span>
                  </div>
                  <div className="feature-item">
                    <span className="feature-icon">🏭</span>
                    <span>Production forecasts</span>
                  </div>
                  <div className="feature-item">
                    <span className="feature-icon">💎</span>
                    <span>Gold price analysis</span>
                  </div>
                </div>
                <button
                  className="step-action-btn financial"
                  onClick={() => onNavigate('financial-data')}
                >
                  <span className="btn-icon">📈</span>
                  View Financial Data
                </button>
              </div>
            </div>

            {/* Step 3: Start Registration */}
            <div className="action-step-card registration">
              <div className="step-number">03</div>
              <div className="step-content">
                <div className="step-icon-wrapper">
                  <div className="step-icon">🎯</div>
                  <div className="step-badge">Get Started</div>
                </div>
                <h3>Purchase Your Shares</h3>
                <p className="step-description">
                  Begin your shareholder registration process and secure your position in Africa's most
                  promising gold mining operation. Start with as little as $50 in the presale phase.
                </p>
                <div className="step-features">
                  <div className="feature-item">
                    <span className="feature-icon">⚡</span>
                    <span>Quick 5-minute registration</span>
                  </div>
                  <div className="feature-item">
                    <span className="feature-icon">🔒</span>
                    <span>Secure payment processing</span>
                  </div>
                  <div className="feature-item">
                    <span className="feature-icon">📜</span>
                    <span>Official share certificates</span>
                  </div>
                </div>
                <button
                  className="step-action-btn registration"
                  onClick={() => handlePurchaseShares(0)}
                >
                  <span className="btn-icon">💎</span>
                  Start Registration
                </button>
              </div>
            </div>
          </div>

          {/* Call to Action Banner */}
          <div className="cta-banner">
            <div className="cta-content">
              <div className="cta-text">
                <h3>Don't Miss Out on Ground Floor Pricing</h3>
                <p>Presale shares at $5 each - Limited time opportunity before Phase 1 launches at $10 per share</p>
              </div>
              <div className="cta-actions">
                <button
                  className="cta-primary-btn"
                  onClick={() => handlePurchaseShares(0)}
                >
                  <span className="btn-icon">🚀</span>
                  Buy Presale Shares - $5
                </button>
                <button
                  className="cta-secondary-btn"
                  onClick={() => onNavigate('calculator')}
                >
                  <span className="btn-icon">🧮</span>
                  Calculate First
                </button>
              </div>
            </div>
          </div>

          {/* Trust Indicators */}
          <div className="trust-indicators">
            <div className="trust-item">
              <div className="trust-icon">🏆</div>
              <div className="trust-text">
                <strong>Proven Track Record</strong>
                <span>Operating gold mines across Africa</span>
              </div>
            </div>
            <div className="trust-item">
              <div className="trust-icon">🛡️</div>
              <div className="trust-text">
                <strong>Secure Platform</strong>
                <span>Bank-grade security & encryption</span>
              </div>
            </div>
            <div className="trust-item">
              <div className="trust-icon">📞</div>
              <div className="trust-text">
                <strong>24/7 Support</strong>
                <span>Dedicated shareholder assistance</span>
              </div>
            </div>
            <div className="trust-item">
              <div className="trust-icon">📈</div>
              <div className="trust-text">
                <strong>Real Dividends</strong>
                <span>Monthly dividend distributions</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default SharePhasesPage;
