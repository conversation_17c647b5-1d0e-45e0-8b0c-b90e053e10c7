/**
 * EMAIL SERVICE TYPE DEFINITIONS
 * 
 * Centralized type definitions for all email-related interfaces
 * and data structures used across the email service.
 */

export interface EmailDeliveryResult {
  success: boolean;
  messageId: string | null;
  error?: string;
}

export interface EmailConfig {
  apiKey: string;
  fromEmail: string;
  fromName: string;
}

export interface BaseEmailData {
  email: string;
  fullName: string;
}

// Verification Email Types
export interface EmailVerificationData extends BaseEmailData {
  code: string;
  purpose: 'registration' | 'account_update' | 'withdrawal' | 'password_reset' | 'telegram_connection';
  userName?: string;
  expiryMinutes?: number;
}

// Welcome Email Types
export interface WelcomeEmailData extends BaseEmailData {
  username: string;
  sponsorUsername?: string;
}

// Share Purchase Types
export interface SharePurchaseConfirmationData extends BaseEmailData {
  sharesPurchased: number;
  totalAmount: number;
  sharePrice: number;
  phaseName: string;
  transactionId: string;
}

// Commission Types
export interface CommissionEarnedData extends BaseEmailData {
  usdtCommission: number;
  shareCommission: number;
  referredUserName: string;
  purchaseAmount: number;
  transactionId: string;
}

export interface ConversionNotificationData extends BaseEmailData {
  usdtAmount: number;
  sharesReceived: number;
  sharePrice: number;
  transactionId: string;
}

// Withdrawal Types
export interface WithdrawalNotificationData extends BaseEmailData {
  withdrawalAmount: number;
  currency: string;
  status: 'requested' | 'approved' | 'completed' | 'rejected';
  walletAddress?: string;
  transactionId: string;
}

// Transfer Types
export interface ShareTransferNotificationData extends BaseEmailData {
  type: 'sender' | 'recipient';
  shares: number;
  transferFee: number;
  senderName?: string;
  recipientName?: string;
  transactionId: string;
}

// Migration Types
export interface MigrationConfirmationData extends BaseEmailData {
  username: string;
  telegramUsername: string;
}

// Bulk Email Types
export interface BulkEmailData {
  recipients: string[];
  subject: string;
  htmlContent: string;
  textContent?: string;
  templateData?: Record<string, any>;
}

// Newsletter Types
export interface NewsletterData {
  subject: string;
  htmlContent: string;
  textContent?: string;
  templateData?: Record<string, any>;
  segmentFilters?: {
    userType?: string[];
    hasShares?: boolean;
    kycStatus?: string[];
    registrationDateAfter?: string;
    registrationDateBefore?: string;
  };
}

// Email Template Types
export type EmailType = 
  | 'verification'
  | 'welcome'
  | 'share_purchase'
  | 'commission_earned'
  | 'conversion'
  | 'withdrawal'
  | 'share_transfer'
  | 'migration_confirmation'
  | 'newsletter'
  | 'bulk';

export interface EmailTemplate {
  type: EmailType;
  subject: string;
  htmlContent: string;
  textContent: string;
}

export interface EmailSendRequest {
  to: string;
  subject: string;
  htmlContent: string;
  textContent: string;
  emailType: EmailType;
  attachments?: EmailAttachment[];
}

export interface EmailAttachment {
  filename: string;
  content: string | Buffer;
  contentType: string;
}

// Email Preferences Types
export interface EmailPreferences {
  userId: number;
  marketingEmails: boolean;
  transactionalEmails: boolean;
  newsletterEmails: boolean;
  securityEmails: boolean;
  commissionNotifications: boolean;
  shareUpdates: boolean;
  unsubscribeToken?: string;
}

// Email Analytics Types
export interface EmailAnalytics {
  emailType: EmailType;
  sent: number;
  delivered: number;
  opened: number;
  clicked: number;
  bounced: number;
  complained: number;
  unsubscribed: number;
  date: string;
}

// Email Queue Types
export interface EmailQueueItem {
  id: string;
  to: string;
  subject: string;
  htmlContent: string;
  textContent: string;
  emailType: EmailType;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  scheduledFor?: Date;
  attempts: number;
  maxAttempts: number;
  status: 'pending' | 'sending' | 'sent' | 'failed' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
  error?: string;
}

// Email Validation Types
export interface EmailValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Email Service Configuration
export interface EmailServiceConfig {
  apiKey: string;
  fromEmail: string;
  fromName: string;
  maxRetries: number;
  retryDelay: number;
  batchSize: number;
  rateLimitPerMinute: number;
  enableAnalytics: boolean;
  enableQueue: boolean;
  defaultTemplateData: Record<string, any>;
}
