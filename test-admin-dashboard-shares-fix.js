/**
 * TEST ADMIN DASHBOARD SHARES FIX
 * 
 * This script tests the UserManager fix to ensure it now correctly
 * displays shares from both aureus_share_purchases AND commission_balances
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client with service role
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testAdminDashboardSharesFix() {
  console.log('🧪 TESTING ADMIN DASHBOARD SHARES FIX');
  console.log('====================================');
  
  try {
    // ===== SIMULATE USERMANAGER LOGIC =====
    console.log('\n📋 Simulating UserManager share calculation logic');
    
    // Test with user 367 (has transferred shares)
    const testUserId = 367;
    console.log(`\n🔍 Testing with User ID: ${testUserId}`);
    
    // Get share purchases (old logic)
    const { data: sharePurchases, error: purchaseError } = await supabase
      .from('aureus_share_purchases')
      .select('shares_purchased, total_amount, status')
      .eq('user_id', testUserId)
      .eq('status', 'active');
      
    if (purchaseError) {
      console.log('❌ Error fetching share purchases:', purchaseError.message);
      return false;
    }
    
    // Get commission balances (new logic)
    const { data: commissionBalance, error: commissionError } = await supabase
      .from('commission_balances')
      .select('usdt_balance, share_balance, total_earned_usdt, total_earned_shares')
      .eq('user_id', testUserId)
      .single();
      
    if (commissionError) {
      console.log('❌ Error fetching commission balance:', commissionError.message);
      return false;
    }
    
    // ===== OLD CALCULATION (BROKEN) =====
    console.log('\n📊 OLD CALCULATION (what was showing before):');
    const oldTotalShares = sharePurchases?.reduce((sum, purchase) =>
      sum + (purchase.shares_purchased || 0), 0) || 0;
    console.log(`   → Total shares (old logic): ${oldTotalShares}`);
    console.log(`   → This is why admin dashboard showed 0 shares`);
    
    // ===== NEW CALCULATION (FIXED) =====
    console.log('\n✅ NEW CALCULATION (fixed logic):');
    const purchasedShares = sharePurchases?.reduce((sum, purchase) =>
      sum + (purchase.shares_purchased || 0), 0) || 0;
    const commissionShares = commissionBalance?.share_balance || 0;
    const newTotalShares = purchasedShares + commissionShares;
    
    console.log(`   → Purchased shares: ${purchasedShares}`);
    console.log(`   → Commission shares: ${commissionShares}`);
    console.log(`   → Total shares (new logic): ${newTotalShares}`);
    
    // ===== COMMISSION CALCULATION =====
    console.log('\n💰 COMMISSION CALCULATION:');
    const usdtCommissions = commissionBalance?.total_earned_usdt || 0;
    const shareCommissionsValue = (commissionBalance?.total_earned_shares || 0) * 25;
    const totalCommissions = usdtCommissions + shareCommissionsValue;
    
    console.log(`   → USDT commissions: $${usdtCommissions}`);
    console.log(`   → Share commissions: ${commissionBalance?.total_earned_shares || 0} shares`);
    console.log(`   → Share commissions value: $${shareCommissionsValue} (at $25/share)`);
    console.log(`   → Total commission value: $${totalCommissions}`);
    
    // ===== TEST WITH MULTIPLE USERS =====
    console.log('\n🔍 Testing with multiple users to verify logic');
    
    const testUserIds = [139, 367, 135]; // Users with different share scenarios
    
    for (const userId of testUserIds) {
      console.log(`\n👤 User ${userId}:`);
      
      // Get data
      const { data: purchases } = await supabase
        .from('aureus_share_purchases')
        .select('shares_purchased')
        .eq('user_id', userId)
        .eq('status', 'active');
        
      const { data: commission } = await supabase
        .from('commission_balances')
        .select('share_balance, total_earned_shares')
        .eq('user_id', userId)
        .single();
      
      // Calculate
      const purchased = purchases?.reduce((sum, p) => sum + (p.shares_purchased || 0), 0) || 0;
      const commissionShares = commission?.share_balance || 0;
      const total = purchased + commissionShares;
      
      console.log(`   → Purchased: ${purchased}, Commission: ${commissionShares}, Total: ${total}`);
      
      // Display logic test
      let displayText = '';
      if (purchased > 0 && commissionShares > 0) {
        displayText = `${purchased} bought + ${commissionShares} earned`;
      } else if (purchased > 0) {
        displayText = `${purchases?.length || 0} purchases`;
      } else if (commissionShares > 0) {
        displayText = `${commissionShares} earned/transferred`;
      } else {
        displayText = 'No shares';
      }
      
      console.log(`   → Display text: "${displayText}"`);
    }
    
    // ===== VERIFICATION =====
    console.log('\n📋 VERIFICATION RESULTS');
    console.log('=======================');
    
    if (newTotalShares > oldTotalShares) {
      console.log('✅ SUCCESS: New calculation shows more shares than old calculation');
      console.log(`   → User 367 will now show ${newTotalShares} shares instead of ${oldTotalShares}`);
      console.log('✅ Admin dashboard will now correctly display transferred shares');
    } else {
      console.log('⚠️ WARNING: New calculation same as old - may need further investigation');
    }
    
    if (commissionShares > 0) {
      console.log('✅ SUCCESS: Commission shares detected and included in calculation');
    } else {
      console.log('⚠️ INFO: No commission shares for this user');
    }
    
    console.log('\n🎯 EXPECTED BEHAVIOR AFTER FIX:');
    console.log('• User 367 should show 1 share in admin dashboard');
    console.log('• Share breakdown should show "1 earned/transferred"');
    console.log('• All users with transferred shares will be visible');
    console.log('• Total shares calculation includes both purchased + commission shares');
    
    return true;
    
  } catch (error) {
    console.error('💥 Fatal error during testing:', error);
    return false;
  }
}

// Run the test
testAdminDashboardSharesFix()
  .then((success) => {
    if (success) {
      console.log('\n🎉 ADMIN DASHBOARD SHARES FIX TEST COMPLETED!');
      console.log('✅ UserManager now correctly calculates total shares');
      console.log('✅ Transferred shares will be visible in admin dashboard');
    } else {
      console.log('\n❌ Admin dashboard shares fix test failed');
    }
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error('\n💥 Test failed:', error);
    process.exit(1);
  });
