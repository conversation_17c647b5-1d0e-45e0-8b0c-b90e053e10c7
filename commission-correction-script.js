/**
 * CRITICAL COMMISSION CORRECTION SCRIPT
 * 
 * This script implements corrective actions for missing share commissions
 * identified in the audit. It creates proper commission_transactions entries
 * and updates commission_balances with full audit trail.
 * 
 * CORRECTION OBJECTIVES:
 * 1. Create corrective commission_transactions for missing share commissions
 * 2. Update commission_balances.share_balance and total_earned_shares
 * 3. Maintain complete audit trail with "RETROACTIVE COMMISSION CORRECTION"
 * 4. Verify mathematical consistency after corrections
 * 5. Generate notification emails for affected referrers
 */

import { createClient } from '@supabase/supabase-js'
import { runCommissionAudit } from './commission-audit-script.js'

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co'
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseServiceKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required')
  console.error('Please set the environment variable and run again')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

class CommissionCorrector {
  constructor() {
    this.correctionResults = {
      totalCorrections: 0,
      totalShareCommissionsAdded: 0,
      totalUsdtCommissionsAdded: 0,
      affectedReferrers: new Set(),
      correctionTransactions: [],
      balanceUpdates: [],
      errors: [],
      warnings: [],
      notificationEmails: []
    }
  }

  /**
   * Run comprehensive commission corrections
   */
  async runCommissionCorrections() {
    console.log('🔧 CRITICAL COMMISSION CORRECTION - STARTING')
    console.log('=' .repeat(60))
    console.log('Objective: Correct ALL missing share commissions in the system')
    console.log('Action: Create corrective transactions with full audit trail')
    console.log('=' .repeat(60))

    try {
      // Step 1: Run audit to get missing commissions
      console.log('\n🔍 Running comprehensive audit to identify corrections needed...')
      const auditResults = await runCommissionAudit()
      
      if (auditResults.missingCommissions.length === 0) {
        console.log('✅ NO CORRECTIONS NEEDED - All commissions are correct')
        return
      }

      console.log(`\n🚨 CORRECTIONS NEEDED: ${auditResults.missingCommissions.length} commission discrepancies found`)
      
      // Step 2: Create corrective commission transactions
      await this.createCorrectiveTransactions(auditResults.missingCommissions)
      
      // Step 3: Update commission balances
      await this.updateCommissionBalances(auditResults.missingCommissions)
      
      // Step 4: Verify corrections
      await this.verifyCorrections()
      
      // Step 5: Generate notification emails
      await this.generateNotificationEmails(auditResults.missingCommissions)
      
      // Step 6: Generate correction report
      this.generateCorrectionReport()
      
    } catch (error) {
      console.error('❌ COMMISSION CORRECTION FAILED:', error)
      this.correctionResults.errors.push(`Critical correction failure: ${error.message}`)
      throw error
    }
  }

  /**
   * Create corrective commission transactions
   */
  async createCorrectiveTransactions(missingCommissions) {
    console.log('\n🔧 STEP 2: Creating Corrective Commission Transactions')
    
    try {
      for (const missing of missingCommissions) {
        console.log(`\n🔧 Creating correction for Purchase ${missing.purchase_id}`)
        console.log(`   Referrer: ${missing.referrer_username} (ID: ${missing.referrer_id})`)
        console.log(`   Missing USDT: $${(missing.missing_usdt_commission || 0).toFixed(2)}`)
        console.log(`   Missing Shares: ${(missing.missing_share_commission || 0).toFixed(4)}`)

        // Only create correction if there are missing commissions
        if ((missing.missing_usdt_commission || 0) > 0.01 || (missing.missing_share_commission || 0) > 0.001) {
          
          const correctionData = {
            referrer_id: missing.referrer_id,
            referred_id: missing.user_id,
            share_purchase_id: missing.purchase_id,
            commission_rate: 15.00, // 15% commission rate
            share_purchase_amount: missing.purchase_amount,
            usdt_commission: missing.missing_usdt_commission || 0,
            share_commission: missing.missing_share_commission || 0,
            status: 'approved',
            payment_date: new Date().toISOString(),
            phase_id: 1, // Assuming Pre Sale phase
            created_at: new Date().toISOString(),
            // Audit trail fields
            correction_type: 'RETROACTIVE_COMMISSION_CORRECTION',
            original_purchase_date: missing.purchase_date,
            correction_reason: `Missing ${missing.type === 'COMPLETELY_MISSING' ? 'complete' : 'partial'} commission correction`,
            correction_date: new Date().toISOString()
          }

          console.log(`   📝 Creating commission transaction:`, correctionData)

          const { data: correctionTransaction, error } = await supabase
            .from('commission_transactions')
            .insert(correctionData)
            .select()
            .single()

          if (error) {
            console.error(`   ❌ Failed to create correction transaction: ${error.message}`)
            this.correctionResults.errors.push(`Failed to create correction for purchase ${missing.purchase_id}: ${error.message}`)
            continue
          }

          console.log(`   ✅ Created correction transaction ID: ${correctionTransaction.id}`)
          
          this.correctionResults.correctionTransactions.push({
            ...correctionTransaction,
            original_missing: missing
          })
          
          this.correctionResults.totalCorrections++
          this.correctionResults.totalUsdtCommissionsAdded += (missing.missing_usdt_commission || 0)
          this.correctionResults.totalShareCommissionsAdded += (missing.missing_share_commission || 0)
          this.correctionResults.affectedReferrers.add(missing.referrer_id)
        }
      }
      
      console.log(`\n✅ Created ${this.correctionResults.totalCorrections} corrective commission transactions`)
      
    } catch (error) {
      this.correctionResults.errors.push(`Failed to create corrective transactions: ${error.message}`)
      throw error
    }
  }

  /**
   * Update commission balances for affected referrers
   */
  async updateCommissionBalances(missingCommissions) {
    console.log('\n💰 STEP 3: Updating Commission Balances')
    
    try {
      // Group corrections by referrer
      const referrerCorrections = {}
      
      for (const missing of missingCommissions) {
        if (!referrerCorrections[missing.referrer_id]) {
          referrerCorrections[missing.referrer_id] = {
            referrer_id: missing.referrer_id,
            referrer_username: missing.referrer_username,
            referrer_email: missing.referrer_email,
            total_usdt_correction: 0,
            total_share_correction: 0,
            corrections: []
          }
        }
        
        referrerCorrections[missing.referrer_id].total_usdt_correction += (missing.missing_usdt_commission || 0)
        referrerCorrections[missing.referrer_id].total_share_correction += (missing.missing_share_commission || 0)
        referrerCorrections[missing.referrer_id].corrections.push(missing)
      }

      // Update each referrer's commission balance
      for (const [referrerId, correction] of Object.entries(referrerCorrections)) {
        console.log(`\n💰 Updating balance for ${correction.referrer_username} (ID: ${referrerId})`)
        console.log(`   USDT Correction: +$${correction.total_usdt_correction.toFixed(2)}`)
        console.log(`   Share Correction: +${correction.total_share_correction.toFixed(4)} shares`)

        // Get current balance
        const { data: currentBalance, error: balanceError } = await supabase
          .from('commission_balances')
          .select('*')
          .eq('user_id', referrerId)
          .single()

        if (balanceError) {
          console.error(`   ❌ Failed to get current balance: ${balanceError.message}`)
          this.correctionResults.errors.push(`Failed to get balance for referrer ${referrerId}: ${balanceError.message}`)
          continue
        }

        // Calculate new balances
        const newUsdtBalance = parseFloat(currentBalance.usdt_balance || '0') + correction.total_usdt_correction
        const newShareBalance = parseFloat(currentBalance.share_balance || '0') + correction.total_share_correction
        const newTotalEarnedUsdt = parseFloat(currentBalance.total_earned_usdt || '0') + correction.total_usdt_correction
        const newTotalEarnedShares = parseFloat(currentBalance.total_earned_shares || '0') + correction.total_share_correction

        console.log(`   Current USDT Balance: $${parseFloat(currentBalance.usdt_balance || '0').toFixed(2)} → $${newUsdtBalance.toFixed(2)}`)
        console.log(`   Current Share Balance: ${parseFloat(currentBalance.share_balance || '0').toFixed(4)} → ${newShareBalance.toFixed(4)}`)

        // Update balance
        const { data: updatedBalance, error: updateError } = await supabase
          .from('commission_balances')
          .update({
            usdt_balance: newUsdtBalance,
            share_balance: newShareBalance,
            total_earned_usdt: newTotalEarnedUsdt,
            total_earned_shares: newTotalEarnedShares,
            last_updated: new Date().toISOString(),
            // Audit trail
            last_correction_date: new Date().toISOString(),
            last_correction_type: 'RETROACTIVE_COMMISSION_CORRECTION',
            last_correction_amount_usdt: correction.total_usdt_correction,
            last_correction_amount_shares: correction.total_share_correction
          })
          .eq('user_id', referrerId)
          .select()
          .single()

        if (updateError) {
          console.error(`   ❌ Failed to update balance: ${updateError.message}`)
          this.correctionResults.errors.push(`Failed to update balance for referrer ${referrerId}: ${updateError.message}`)
          continue
        }

        console.log(`   ✅ Balance updated successfully`)
        
        this.correctionResults.balanceUpdates.push({
          referrer_id: referrerId,
          referrer_username: correction.referrer_username,
          old_usdt_balance: parseFloat(currentBalance.usdt_balance || '0'),
          new_usdt_balance: newUsdtBalance,
          old_share_balance: parseFloat(currentBalance.share_balance || '0'),
          new_share_balance: newShareBalance,
          usdt_correction: correction.total_usdt_correction,
          share_correction: correction.total_share_correction
        })
      }
      
      console.log(`\n✅ Updated commission balances for ${Object.keys(referrerCorrections).length} referrers`)
      
    } catch (error) {
      this.correctionResults.errors.push(`Failed to update commission balances: ${error.message}`)
      throw error
    }
  }

  /**
   * Verify corrections were applied correctly
   */
  async verifyCorrections() {
    console.log('\n🔍 STEP 4: Verifying Corrections')
    
    try {
      console.log('🔍 Running post-correction audit to verify fixes...')
      
      // Run audit again to check if corrections were successful
      const postCorrectionAudit = await runCommissionAudit()
      
      if (postCorrectionAudit.missingCommissions.length === 0) {
        console.log('✅ VERIFICATION SUCCESSFUL: All commission discrepancies have been corrected')
      } else {
        console.log(`⚠️  VERIFICATION WARNING: ${postCorrectionAudit.missingCommissions.length} discrepancies still remain`)
        this.correctionResults.warnings.push(`${postCorrectionAudit.missingCommissions.length} commission discrepancies still exist after correction`)
      }
      
      // Verify balance consistency
      for (const balanceUpdate of this.correctionResults.balanceUpdates) {
        const { data: verifyBalance, error } = await supabase
          .from('commission_balances')
          .select('usdt_balance, share_balance, total_earned_usdt, total_earned_shares')
          .eq('user_id', balanceUpdate.referrer_id)
          .single()

        if (error) {
          this.correctionResults.warnings.push(`Could not verify balance for referrer ${balanceUpdate.referrer_id}: ${error.message}`)
          continue
        }

        const expectedUsdtBalance = balanceUpdate.new_usdt_balance
        const expectedShareBalance = balanceUpdate.new_share_balance
        const actualUsdtBalance = parseFloat(verifyBalance.usdt_balance || '0')
        const actualShareBalance = parseFloat(verifyBalance.share_balance || '0')

        if (Math.abs(actualUsdtBalance - expectedUsdtBalance) > 0.01 || 
            Math.abs(actualShareBalance - expectedShareBalance) > 0.001) {
          this.correctionResults.warnings.push(`Balance mismatch for ${balanceUpdate.referrer_username}: expected USDT=${expectedUsdtBalance.toFixed(2)}, actual=${actualUsdtBalance.toFixed(2)}, expected shares=${expectedShareBalance.toFixed(4)}, actual=${actualShareBalance.toFixed(4)}`)
        } else {
          console.log(`✅ Balance verified for ${balanceUpdate.referrer_username}`)
        }
      }
      
    } catch (error) {
      this.correctionResults.errors.push(`Failed to verify corrections: ${error.message}`)
      throw error
    }
  }

  /**
   * Generate notification emails for affected referrers
   */
  async generateNotificationEmails(missingCommissions) {
    console.log('\n📧 STEP 5: Generating Notification Emails')
    
    try {
      // Group by referrer for email notifications
      const referrerNotifications = {}
      
      for (const missing of missingCommissions) {
        if (!referrerNotifications[missing.referrer_id]) {
          referrerNotifications[missing.referrer_id] = {
            referrer_username: missing.referrer_username,
            referrer_email: missing.referrer_email,
            total_usdt_correction: 0,
            total_share_correction: 0,
            affected_purchases: [],
            correction_date: new Date().toISOString()
          }
        }
        
        referrerNotifications[missing.referrer_id].total_usdt_correction += (missing.missing_usdt_commission || 0)
        referrerNotifications[missing.referrer_id].total_share_correction += (missing.missing_share_commission || 0)
        referrerNotifications[missing.referrer_id].affected_purchases.push({
          buyer_username: missing.buyer_username,
          purchase_amount: missing.purchase_amount,
          shares_purchased: missing.shares_purchased,
          missing_usdt: missing.missing_usdt_commission || 0,
          missing_shares: missing.missing_share_commission || 0,
          purchase_date: missing.purchase_date
        })
      }

      // Generate email content for each referrer
      for (const [referrerId, notification] of Object.entries(referrerNotifications)) {
        const emailContent = this.generateCorrectionEmailContent(notification)
        
        this.correctionResults.notificationEmails.push({
          referrer_id: referrerId,
          referrer_username: notification.referrer_username,
          referrer_email: notification.referrer_email,
          subject: `Commission Correction Applied - $${notification.total_usdt_correction.toFixed(2)} USDT + ${notification.total_share_correction.toFixed(4)} Shares Added`,
          content: emailContent,
          total_usdt_correction: notification.total_usdt_correction,
          total_share_correction: notification.total_share_correction,
          affected_purchases_count: notification.affected_purchases.length
        })
        
        console.log(`📧 Email prepared for ${notification.referrer_username}:`)
        console.log(`   Subject: Commission Correction Applied`)
        console.log(`   USDT Correction: $${notification.total_usdt_correction.toFixed(2)}`)
        console.log(`   Share Correction: ${notification.total_share_correction.toFixed(4)} shares`)
        console.log(`   Affected Purchases: ${notification.affected_purchases.length}`)
      }
      
      console.log(`\n✅ Generated ${this.correctionResults.notificationEmails.length} notification emails`)
      
    } catch (error) {
      this.correctionResults.errors.push(`Failed to generate notification emails: ${error.message}`)
      throw error
    }
  }

  /**
   * Generate email content for commission correction
   */
  generateCorrectionEmailContent(notification) {
    return `
Dear ${notification.referrer_username},

We have identified and corrected missing commissions in your account. Our audit system detected that some of your referral commissions were not properly credited to your account.

COMMISSION CORRECTION SUMMARY:
• USDT Commission Added: $${notification.total_usdt_correction.toFixed(2)}
• Share Commission Added: ${notification.total_share_correction.toFixed(4)} shares
• Affected Purchases: ${notification.affected_purchases.length}
• Correction Date: ${new Date(notification.correction_date).toLocaleDateString()}

AFFECTED PURCHASES:
${notification.affected_purchases.map((purchase, index) => `
${index + 1}. Buyer: ${purchase.buyer_username}
   Purchase: $${purchase.purchase_amount} for ${purchase.shares_purchased} shares
   Date: ${new Date(purchase.purchase_date).toLocaleDateString()}
   Missing USDT Commission: $${purchase.missing_usdt.toFixed(2)}
   Missing Share Commission: ${purchase.missing_shares.toFixed(4)} shares
`).join('')}

These corrections have been automatically applied to your commission balance. You can view your updated balance in your dashboard.

We apologize for any inconvenience caused by this oversight. Our system has been enhanced to prevent similar issues in the future.

Best regards,
Aureus Alliance Holdings Team

---
This is an automated correction notification. The commissions have been added to your account and are immediately available.
    `.trim()
  }

  /**
   * Generate comprehensive correction report
   */
  generateCorrectionReport() {
    console.log('\n📋 COMPREHENSIVE COMMISSION CORRECTION REPORT')
    console.log('=' .repeat(70))
    
    console.log(`\n📊 CORRECTION SUMMARY:`)
    console.log(`Total Corrections Applied: ${this.correctionResults.totalCorrections}`)
    console.log(`Total USDT Commissions Added: $${this.correctionResults.totalUsdtCommissionsAdded.toFixed(2)}`)
    console.log(`Total Share Commissions Added: ${this.correctionResults.totalShareCommissionsAdded.toFixed(4)} shares`)
    console.log(`Affected Referrers: ${this.correctionResults.affectedReferrers.size}`)
    console.log(`Balance Updates: ${this.correctionResults.balanceUpdates.length}`)
    console.log(`Notification Emails Generated: ${this.correctionResults.notificationEmails.length}`)

    if (this.correctionResults.correctionTransactions.length > 0) {
      console.log(`\n🔧 CORRECTION TRANSACTIONS CREATED:`)
      this.correctionResults.correctionTransactions.forEach((transaction, index) => {
        console.log(`\n${index + 1}. Transaction ID: ${transaction.id}`)
        console.log(`   Referrer: ${transaction.original_missing.referrer_username} (ID: ${transaction.referrer_id})`)
        console.log(`   Purchase: ${transaction.original_missing.purchase_id}`)
        console.log(`   USDT Commission: $${transaction.usdt_commission.toFixed(2)}`)
        console.log(`   Share Commission: ${transaction.share_commission.toFixed(4)} shares`)
        console.log(`   Correction Type: ${transaction.correction_type}`)
      })
    }

    if (this.correctionResults.balanceUpdates.length > 0) {
      console.log(`\n💰 BALANCE UPDATES:`)
      this.correctionResults.balanceUpdates.forEach((update, index) => {
        console.log(`\n${index + 1}. ${update.referrer_username} (ID: ${update.referrer_id})`)
        console.log(`   USDT Balance: $${update.old_usdt_balance.toFixed(2)} → $${update.new_usdt_balance.toFixed(2)} (+$${update.usdt_correction.toFixed(2)})`)
        console.log(`   Share Balance: ${update.old_share_balance.toFixed(4)} → ${update.new_share_balance.toFixed(4)} (+${update.share_correction.toFixed(4)})`)
      })
    }

    if (this.correctionResults.notificationEmails.length > 0) {
      console.log(`\n📧 NOTIFICATION EMAILS:`)
      this.correctionResults.notificationEmails.forEach((email, index) => {
        console.log(`\n${index + 1}. ${email.referrer_username} (${email.referrer_email})`)
        console.log(`   Subject: ${email.subject}`)
        console.log(`   USDT Correction: $${email.total_usdt_correction.toFixed(2)}`)
        console.log(`   Share Correction: ${email.total_share_correction.toFixed(4)} shares`)
        console.log(`   Affected Purchases: ${email.affected_purchases_count}`)
      })
    }

    if (this.correctionResults.errors.length > 0) {
      console.log(`\n❌ ERRORS:`)
      this.correctionResults.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`)
      })
    }

    if (this.correctionResults.warnings.length > 0) {
      console.log(`\n⚠️  WARNINGS:`)
      this.correctionResults.warnings.forEach((warning, index) => {
        console.log(`   ${index + 1}. ${warning}`)
      })
    }

    console.log(`\n🎯 CORRECTION CONCLUSION:`)
    if (this.correctionResults.totalCorrections === 0) {
      console.log(`✅ NO CORRECTIONS WERE NEEDED - System was already consistent`)
    } else {
      console.log(`✅ ${this.correctionResults.totalCorrections} COMMISSION CORRECTIONS APPLIED SUCCESSFULLY`)
      console.log(`✅ Total value added: $${this.correctionResults.totalUsdtCommissionsAdded.toFixed(2)} USDT + ${this.correctionResults.totalShareCommissionsAdded.toFixed(4)} shares`)
      console.log(`✅ ${this.correctionResults.affectedReferrers.size} referrers received missing commissions`)
      console.log(`✅ All corrections include complete audit trail`)
    }

    console.log(`\n📋 NEXT STEPS:`)
    console.log(`1. 📧 Send notification emails to affected referrers`)
    console.log(`2. 📊 Monitor system for mathematical consistency`)
    console.log(`3. 🔍 Implement preventive measures for future commission processing`)
    console.log(`4. 📝 Document correction process for compliance records`)
  }
}

/**
 * Main execution
 */
async function runCommissionCorrections() {
  const corrector = new CommissionCorrector()
  await corrector.runCommissionCorrections()
  return corrector.correctionResults
}

// Export for use in other modules
export { CommissionCorrector, runCommissionCorrections }

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runCommissionCorrections().catch(console.error)
}
