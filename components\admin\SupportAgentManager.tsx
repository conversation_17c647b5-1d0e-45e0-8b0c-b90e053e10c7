import React, { useState, useEffect } from 'react'
import { getServiceRoleClient } from '../../lib/supabase'
import { logAdminAction } from '../../lib/adminAuth'
import type { SupportAgent, AgentAvailability } from '../../lib/supportSystem'

interface SupportAgentManagerProps {
  currentUser?: any
}

const SupportAgentManager: React.FC<SupportAgentManagerProps> = ({ currentUser }) => {
  const [agents, setAgents] = useState<(SupportAgent & { availability?: AgentAvailability })[]>([])
  const [adminUsers, setAdminUsers] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [selectedAgent, setSelectedAgent] = useState<SupportAgent | null>(null)
  
  const [newAgent, setNewAgent] = useState({
    admin_user_id: '',
    user_id: '',
    agent_name: '',
    agent_email: '',
    specialization: [] as string[],
    max_concurrent_chats: 3,
    priority_level: 1
  })

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    setLoading(true)
    try {
      await Promise.all([loadAgents(), loadAdminUsers()])
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadAgents = async () => {
    try {
      const serviceClient = getServiceRoleClient()
      const { data, error } = await serviceClient
        .from('support_agents')
        .select(`
          *,
          availability:agent_availability(*)
        `)
        .order('created_at', { ascending: false })

      if (error) throw error
      setAgents(data || [])
    } catch (error) {
      console.error('Error loading agents:', error)
    }
  }

  const loadAdminUsers = async () => {
    try {
      const serviceClient = getServiceRoleClient()
      const { data: adminData, error: adminError } = await serviceClient
        .from('admin_users')
        .select('*')
        .order('email')

      if (adminError) throw adminError

      // Get corresponding user records
      const { data: userData, error: userError } = await serviceClient
        .from('users')
        .select('id, email, full_name, username')
        .in('email', adminData?.map(a => a.email) || [])

      if (userError) throw userError

      // Combine admin and user data
      const combined = adminData?.map(admin => {
        const user = userData?.find(u => u.email === admin.email)
        return { ...admin, user }
      }) || []

      setAdminUsers(combined)
    } catch (error) {
      console.error('Error loading admin users:', error)
    }
  }

  const handleCreateAgent = async () => {
    if (!newAgent.admin_user_id || !newAgent.agent_name.trim()) return

    setLoading(true)
    try {
      const serviceClient = getServiceRoleClient()
      
      // Find the corresponding user record
      const selectedAdminUser = adminUsers.find(a => a.id === newAgent.admin_user_id)
      if (!selectedAdminUser?.user) {
        alert('Selected admin user not found in users table')
        return
      }

      const { data, error } = await serviceClient
        .from('support_agents')
        .insert({
          admin_user_id: parseInt(newAgent.admin_user_id),
          user_id: selectedAdminUser.user.id,
          agent_name: newAgent.agent_name,
          agent_email: newAgent.agent_email || selectedAdminUser.email,
          specialization: newAgent.specialization,
          max_concurrent_chats: newAgent.max_concurrent_chats,
          priority_level: newAgent.priority_level,
          is_active: true
        })
        .select()
        .single()

      if (error) throw error

      // Create initial availability record
      await serviceClient
        .from('agent_availability')
        .insert({
          agent_id: data.id,
          status: 'offline',
          current_chat_count: 0
        })

      // Log admin action
      await logAdminAction(
        currentUser?.email || 'unknown',
        'CREATE_SUPPORT_AGENT',
        'support_agents',
        data.id,
        {
          agent_name: newAgent.agent_name,
          agent_email: newAgent.agent_email,
          specialization: newAgent.specialization
        }
      )

      setNewAgent({
        admin_user_id: '',
        user_id: '',
        agent_name: '',
        agent_email: '',
        specialization: [],
        max_concurrent_chats: 3,
        priority_level: 1
      })
      setShowCreateForm(false)
      await loadAgents()
    } catch (error) {
      console.error('Error creating agent:', error)
      alert('Failed to create support agent')
    } finally {
      setLoading(false)
    }
  }

  const handleToggleAgentStatus = async (agent: SupportAgent) => {
    setLoading(true)
    try {
      const serviceClient = getServiceRoleClient()
      const newStatus = !agent.is_active

      const { error } = await serviceClient
        .from('support_agents')
        .update({ 
          is_active: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', agent.id)

      if (error) throw error

      // Update availability status
      await serviceClient
        .from('agent_availability')
        .update({ 
          status: newStatus ? 'offline' : 'offline',
          updated_at: new Date().toISOString()
        })
        .eq('agent_id', agent.id)

      // Log admin action
      await logAdminAction(
        currentUser?.email || 'unknown',
        newStatus ? 'ACTIVATE_SUPPORT_AGENT' : 'DEACTIVATE_SUPPORT_AGENT',
        'support_agents',
        agent.id,
        { agent_name: agent.agent_name, new_status: newStatus }
      )

      await loadAgents()
    } catch (error) {
      console.error('Error toggling agent status:', error)
      alert('Failed to update agent status')
    } finally {
      setLoading(false)
    }
  }

  const handleSpecializationChange = (specialization: string, checked: boolean) => {
    setNewAgent(prev => ({
      ...prev,
      specialization: checked
        ? [...prev.specialization, specialization]
        : prev.specialization.filter(s => s !== specialization)
    }))
  }

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'available': return 'bg-green-100 text-green-800'
      case 'busy': return 'bg-yellow-100 text-yellow-800'
      case 'away': return 'bg-orange-100 text-orange-800'
      case 'offline': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading && agents.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Support Agent Management</h2>
          <p className="text-gray-600">
            Manage support agents, their availability, and specializations
          </p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
        >
          Add New Agent
        </button>
      </div>

      {/* Create Agent Modal */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Add New Support Agent</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Admin User *
                </label>
                <select
                  value={newAgent.admin_user_id}
                  onChange={(e) => {
                    const selectedAdmin = adminUsers.find(a => a.id === e.target.value)
                    setNewAgent(prev => ({
                      ...prev,
                      admin_user_id: e.target.value,
                      user_id: selectedAdmin?.user?.id || '',
                      agent_name: selectedAdmin?.user?.full_name || selectedAdmin?.user?.username || '',
                      agent_email: selectedAdmin?.email || ''
                    }))
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500"
                >
                  <option value="">Select admin user</option>
                  {adminUsers.map((admin) => (
                    <option key={admin.id} value={admin.id}>
                      {admin.user?.full_name || admin.user?.username || admin.email} ({admin.role})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Agent Name *
                </label>
                <input
                  type="text"
                  value={newAgent.agent_name}
                  onChange={(e) => setNewAgent(prev => ({ ...prev, agent_name: e.target.value }))}
                  placeholder="Display name for the agent"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Agent Email
                </label>
                <input
                  type="email"
                  value={newAgent.agent_email}
                  onChange={(e) => setNewAgent(prev => ({ ...prev, agent_email: e.target.value }))}
                  placeholder="Contact email for the agent"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Specializations
                </label>
                <div className="space-y-2">
                  {[
                    { value: 'shareholder_support', label: 'Shareholder Support' },
                    { value: 'affiliate_support', label: 'Affiliate Support' },
                    { value: 'technical', label: 'Technical Support' },
                    { value: 'billing', label: 'Billing Support' },
                    { value: 'general', label: 'General Support' }
                  ].map((spec) => (
                    <label key={spec.value} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={newAgent.specialization.includes(spec.value)}
                        onChange={(e) => handleSpecializationChange(spec.value, e.target.checked)}
                        className="rounded border-gray-300 text-yellow-600 focus:ring-yellow-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">{spec.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Max Concurrent Chats
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="10"
                    value={newAgent.max_concurrent_chats}
                    onChange={(e) => setNewAgent(prev => ({ ...prev, max_concurrent_chats: parseInt(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Priority Level
                  </label>
                  <select
                    value={newAgent.priority_level}
                    onChange={(e) => setNewAgent(prev => ({ ...prev, priority_level: parseInt(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  >
                    <option value={1}>1 (Highest)</option>
                    <option value={2}>2</option>
                    <option value={3}>3</option>
                    <option value={4}>4</option>
                    <option value={5}>5 (Lowest)</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setShowCreateForm(false)}
                className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateAgent}
                disabled={!newAgent.admin_user_id || !newAgent.agent_name.trim() || loading}
                className="flex-1 bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-lg transition-colors disabled:opacity-50"
              >
                {loading ? 'Creating...' : 'Create Agent'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Agents List */}
      {agents.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">👥</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Support Agents</h3>
          <p className="text-gray-600 mb-4">No support agents have been configured yet.</p>
          <button
            onClick={() => setShowCreateForm(true)}
            className="bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
          >
            Add First Agent
          </button>
        </div>
      ) : (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Agent
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Specializations
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Capacity
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Priority
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {agents.map((agent) => (
                  <tr key={agent.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-yellow-100 flex items-center justify-center">
                            <span className="text-sm font-medium text-yellow-800">
                              {agent.agent_name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {agent.agent_name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {agent.agent_email}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col space-y-1">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(agent.availability?.[0]?.status)}`}>
                          {agent.availability?.[0]?.status || 'offline'}
                        </span>
                        {agent.availability?.[0]?.last_seen && (
                          <span className="text-xs text-gray-500">
                            Last seen: {formatDate(agent.availability[0].last_seen)}
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex flex-wrap gap-1">
                        {agent.specialization.map((spec) => (
                          <span
                            key={spec}
                            className="inline-flex px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full"
                          >
                            {spec.replace('_', ' ')}
                          </span>
                        ))}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {agent.availability?.[0]?.current_chat_count || 0} / {agent.max_concurrent_chats}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      Level {agent.priority_level}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => handleToggleAgentStatus(agent)}
                        className={`${
                          agent.is_active
                            ? 'text-red-600 hover:text-red-900'
                            : 'text-green-600 hover:text-green-900'
                        } mr-4`}
                      >
                        {agent.is_active ? 'Deactivate' : 'Activate'}
                      </button>
                      <button
                        onClick={() => setSelectedAgent(agent)}
                        className="text-yellow-600 hover:text-yellow-900"
                      >
                        View Details
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  )
}

export default SupportAgentManager
