import React, { useState, useEffect } from 'react';
import { GalleryGrid } from './GalleryGrid';
import { GalleryLightbox, useLightboxNavigation } from './GalleryLightbox';
import { useGallerySearch, useGalleryCategories } from '../../hooks/useGallery';
import type { GalleryDisplayProps, GalleryImage } from '../../types/gallery';

export const DynamicGallery: React.FC<GalleryDisplayProps> = ({
  categoryFilter,
  showCategories = true,
  showSearch = true,
  itemsPerPage = 12,
  layout = 'grid',
  showFeaturedFirst = true
}) => {
  const [lightboxIndex, setLightboxIndex] = useState<number>(-1);
  const [currentPage, setCurrentPage] = useState(1);

  const {
    images,
    loading,
    error,
    searchTerm,
    setSearchTerm,
    selectedCategory,
    setSelectedCategory,
    showFeaturedOnly,
    setShowFeaturedOnly,
    clearFilters
  } = useGallerySearch();

  // Apply showFeaturedFirst prop, but show all images if there are only a few
  React.useEffect(() => {
    if (showFeaturedFirst && !showFeaturedOnly && !searchTerm && !selectedCategory) {
      // If there are 3 or fewer total images, show all images instead of just featured
      if (images.length <= 3) {
        setShowFeaturedOnly(false);
      } else {
        setShowFeaturedOnly(true);
      }
    }
  }, [showFeaturedFirst, showFeaturedOnly, searchTerm, selectedCategory, setShowFeaturedOnly, images.length]);

  const { categories, loading: categoriesLoading } = useGalleryCategories();

  // Apply initial category filter if provided
  React.useEffect(() => {
    if (categoryFilter && !selectedCategory) {
      setSelectedCategory(categoryFilter);
    }
  }, [categoryFilter, selectedCategory, setSelectedCategory]);

  const handleImageClick = (image: GalleryImage) => {
    const index = images.findIndex(img => img.id === image.id);
    setLightboxIndex(index);
  };

  const handleLightboxClose = () => {
    setLightboxIndex(-1);
  };

  const handleLightboxNext = () => {
    setLightboxIndex(prev => (prev + 1) % images.length);
  };

  const handleLightboxPrevious = () => {
    setLightboxIndex(prev => (prev - 1 + images.length) % images.length);
  };

  const handleLightboxNavigate = (index: number) => {
    setLightboxIndex(index);
  };

  // Use the lightbox navigation hook
  useLightboxNavigation(images, handleLightboxNavigate);

  // Pagination logic
  const totalPages = Math.ceil(images.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedImages = images.slice(startIndex, endIndex);

  // Smooth scroll to gallery top when page changes
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    // Scroll to gallery section smoothly
    const galleryElement = document.getElementById('gallery');
    if (galleryElement) {
      galleryElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  const hasActiveFilters = searchTerm || selectedCategory || showFeaturedOnly;

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col items-center justify-center py-16">
          <div className="relative">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-gray-600"></div>
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-amber-500 border-t-transparent absolute top-0 left-0"></div>
          </div>
          <p className="text-gray-400 mt-4 text-sm">Loading gallery images...</p>
        </div>
      </div>
    );
  }

  if (error) {
    console.error('DynamicGallery Error:', error);
    // Throw error to be caught by parent component
    throw new Error(`Gallery database error: ${error}`);
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      {(showSearch || showCategories) && (
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Search */}
            {showSearch && (
              <div>
                <label className="block text-sm font-medium text-amber-400 mb-3">
                  Search Images
                </label>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search by title or description..."
                  className="w-full px-4 py-3 border border-gray-600 bg-gray-800 text-white rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 placeholder-gray-400 transition-all duration-200"
                />
              </div>
            )}

            {/* Category Filter */}
            {showCategories && (
              <div>
                <label className="block text-sm font-medium text-amber-400 mb-3">
                  Category
                </label>
                <div className="space-y-2">
                  <button
                    onClick={() => setSelectedCategory('')}
                    disabled={categoriesLoading}
                    className={`w-full text-left px-4 py-3 rounded-lg text-sm transition-all duration-200 border ${
                      !selectedCategory
                        ? 'bg-gradient-to-r from-amber-500 to-amber-600 text-black border-amber-400 shadow-lg'
                        : 'bg-gray-800 text-gray-300 border-gray-600 hover:bg-gray-700 hover:text-amber-400 hover:border-amber-500'
                    } ${categoriesLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    All Categories
                  </button>
                  {categories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      disabled={categoriesLoading}
                      className={`w-full text-left px-4 py-3 rounded-lg text-sm transition-all duration-200 border ${
                        selectedCategory === category.id
                          ? 'bg-gradient-to-r from-amber-500 to-amber-600 text-black border-amber-400 shadow-lg'
                          : 'bg-gray-800 text-gray-300 border-gray-600 hover:bg-gray-700 hover:text-amber-400 hover:border-amber-500'
                      } ${categoriesLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                    >
                      {category.name}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Featured Filter */}
            <div>
              <label className="block text-sm font-medium text-amber-400 mb-3">
                Filter Options
              </label>
              <div className="flex items-center space-x-4">
                <label className="flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={showFeaturedOnly}
                    onChange={(e) => setShowFeaturedOnly(e.target.checked)}
                    className="rounded border-gray-600 bg-gray-800 text-amber-500 shadow-sm focus:border-amber-500 focus:ring-amber-500 focus:ring-offset-gray-900"
                  />
                  <span className="ml-3 text-sm text-gray-300 hover:text-amber-400 transition-colors">
                    Featured only
                  </span>
                </label>
                {hasActiveFilters && (
                  <button
                    onClick={clearFilters}
                    className="px-3 py-1 text-sm bg-gray-800 text-amber-400 border border-gray-600 rounded-md hover:bg-gray-700 hover:border-amber-500 transition-all duration-200"
                  >
                    Clear filters
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-400">
          {images.length === 0 ? (
            'No images found'
          ) : (
            <div className="flex items-center space-x-2">
              <span>
                Showing {startIndex + 1}-{Math.min(endIndex, images.length)} of {images.length} images
                {hasActiveFilters && ' (filtered)'}
              </span>
              {images.length > 0 && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200">
                  📸 {images.length} total
                </span>
              )}
            </div>
          )}
        </div>

        {/* View Options */}
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500 dark:text-gray-400">View:</span>
          <button
            onClick={() => {/* TODO: Implement grid view */}}
            className={`p-2 rounded ${layout === 'grid' ? 'bg-amber-100 text-amber-600 dark:bg-amber-900 dark:text-amber-400' : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'}`}
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
            </svg>
          </button>
        </div>
      </div>

      {/* Gallery Grid */}
      <GalleryGrid
        images={paginatedImages}
        onImageClick={handleImageClick}
        columns={3}
        showOverlay={true}
        showCategories={showCategories}
      />

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2 mt-8">
          <button
            onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="px-4 py-2 text-sm font-medium text-gray-300 bg-gray-800 border border-gray-600 rounded-lg hover:bg-gray-700 hover:text-amber-400 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            Previous
          </button>

          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                page === currentPage
                  ? 'text-black bg-gradient-to-r from-amber-400 to-amber-600 border border-amber-500 shadow-lg'
                  : 'text-gray-300 bg-gray-800 border border-gray-600 hover:bg-gray-700 hover:text-amber-400 hover:border-amber-500'
              }`}
            >
              {page}
            </button>
          ))}

          <button
            onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className="px-4 py-2 text-sm font-medium text-gray-300 bg-gray-800 border border-gray-600 rounded-lg hover:bg-gray-700 hover:text-amber-400 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            Next
          </button>
        </div>
      )}

      {/* Lightbox */}
      {lightboxIndex >= 0 && (
        <GalleryLightbox
          images={images}
          currentIndex={lightboxIndex}
          isOpen={lightboxIndex >= 0}
          onClose={handleLightboxClose}
          onNext={handleLightboxNext}
          onPrevious={handleLightboxPrevious}
        />
      )}
    </div>
  );
};
