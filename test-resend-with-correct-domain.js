/**
 * Test Resend with the correct aureus.africa domain
 */

import { Resend } from 'resend';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testResendWithCorrectDomain() {
  console.log('🧪 Testing Resend with aureus.africa domain...');
  
  const apiKey = process.env.RESEND_API_KEY;
  if (!apiKey) {
    console.error('❌ No RESEND_API_KEY found');
    return;
  }
  
  const resend = new Resend(apiKey);
  
  // Use the correct domain
  const fromEmail = '<EMAIL>';
  const fromName = 'Aureus Alliance Holdings';
  const testEmail = '<EMAIL>'; // Different from your main email
  
  console.log('📧 Test Configuration:');
  console.log('FROM:', `${fromName} <${fromEmail}>`);
  console.log('TO:', testEmail);
  console.log('');
  
  try {
    const result = await resend.emails.send({
      from: `${fromName} <${fromEmail}>`,
      to: [testEmail],
      subject: 'Test Email - Aureus Africa Domain Verification',
      html: `
        <h2>Domain Test Successful!</h2>
        <p>This email was sent using the verified aureus.africa domain.</p>
        <p>If you receive this email, the domain configuration is working correctly.</p>
        <p>Test code: 123456</p>
      `,
      text: 'Domain Test Successful! This email was sent using the verified aureus.africa domain. Test code: 123456',
      tags: [
        { name: 'category', value: 'test' },
        { name: 'purpose', value: 'domain_verification' }
      ]
    });
    
    if (result.error) {
      console.error('❌ Resend API error:', result.error);
      
      if (result.error.message && result.error.message.includes('verify a domain')) {
        console.log('');
        console.log('💡 SOLUTION:');
        console.log('The aureus.africa domain needs to be properly configured in Resend.');
        console.log('Please check:');
        console.log('1. Domain is verified in Resend dashboard');
        console.log('2. DNS records are properly set up');
        console.log('3. Domain status shows as "Verified"');
      }
    } else {
      console.log('✅ Email sent successfully!');
      console.log('📧 Message ID:', result.data?.id);
      console.log('');
      console.log('🎉 The aureus.africa domain is working correctly!');
      console.log('You can now update your .env file to use:');
      console.log('RESEND_FROM_EMAIL=<EMAIL>');
    }
    
  } catch (error) {
    console.error('❌ Error testing email:', error.message);
  }
}

testResendWithCorrectDomain();
