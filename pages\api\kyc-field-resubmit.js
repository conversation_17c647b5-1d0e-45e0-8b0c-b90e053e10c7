/**
 * KYC Field Resubmit API
 * Handles resubmission of specific KYC fields
 */

import { createClient } from '@supabase/supabase-js';

// Force the correct Supabase configuration since environment variables aren't loading properly in API files
const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { kycId, fieldName } = req.body;

    if (!kycId || !fieldName) {
      return res.status(400).json({ error: 'KYC ID and field name are required' });
    }

    console.log('🔄 Resubmitting field:', fieldName, 'for KYC ID:', kycId);

    // Reset field status to pending for resubmission
    const { error } = await supabase
      .from('kyc_field_approvals')
      .update({
        approval_status: 'pending',
        admin_notes: 'Resubmitted by user',
        updated_at: new Date().toISOString()
      })
      .eq('kyc_id', kycId)
      .eq('field_name', fieldName);

    if (error) {
      console.error('❌ Error updating field status:', error);
      return res.status(500).json({
        error: 'Failed to resubmit field',
        details: error.message
      });
    }

    // Update overall KYC status back to pending when fields are resubmitted
    const { error: kycStatusError } = await supabase
      .from('kyc_information')
      .update({
        kyc_status: 'pending',
        updated_at: new Date().toISOString()
      })
      .eq('id', kycId);

    if (kycStatusError) {
      console.error('⚠️ Warning: Failed to update overall KYC status:', kycStatusError);
      // Don't fail the request - field update was successful
    } else {
      console.log('✅ Overall KYC status updated to pending for resubmission');
    }

    console.log('✅ Field resubmitted successfully:', fieldName);

    return res.status(200).json({
      success: true,
      message: 'Field resubmitted successfully'
    });

  } catch (error) {
    console.error('❌ Error in field resubmission:', error);
    return res.status(500).json({ 
      error: 'Failed to resubmit field',
      details: error.message 
    });
  }
}
