/**
 * ADMIN PIN VERIFICATION MODAL
 * 
 * Specialized modal for admin two-factor authentication using PIN codes
 * <NAME_EMAIL> with professional styling and security features.
 * 
 * Features:
 * - Professional admin-focused design
 * - Real-time PIN validation
 * - Loading states and error handling
 * - Rate limiting feedback
 * - Security-focused messaging
 * - Consistent with existing dashboard theme
 */

import React, { useState, useEffect, useRef } from 'react'
import { AdminPinService } from '../lib/adminPinService'

interface AdminPinVerificationModalProps {
  isOpen: boolean
  onClose: () => void
  onVerified: () => void
  adminEmail: string
  title?: string
  description?: string
}

export const AdminPinVerificationModal: React.FC<AdminPinVerificationModalProps> = ({
  isOpen,
  onClose,
  onVerified,
  adminEmail,
  title = '🔐 Admin Authentication Required',
  description = 'Two-factor authentication is required for admin access. A PIN has been sent to the security email address.'
}) => {
  const [pin, setPin] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [sendingPin, setSendingPin] = useState(false)
  const [pinSent, setPinSent] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const [rateLimited, setRateLimited] = useState(false)

  const pinInputRef = useRef<HTMLInputElement>(null)

  // Auto-send PIN when modal opens
  useEffect(() => {
    if (isOpen && !pinSent && !rateLimited) {
      console.log('🔐 Admin PIN modal opened, automatically sending PIN...')
      sendPin()
    }
  }, [isOpen])

  // Focus PIN input when modal opens
  useEffect(() => {
    if (isOpen && pinSent && pinInputRef.current) {
      setTimeout(() => {
        pinInputRef.current?.focus()
      }, 100)
    }
  }, [isOpen, pinSent])

  // Countdown timer for rate limiting
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000)
      return () => clearTimeout(timer)
    } else if (countdown === 0 && rateLimited) {
      setRateLimited(false)
    }
  }, [countdown, rateLimited])

  const sendPin = async () => {
    setSendingPin(true)
    setError(null)

    try {
      console.log('📧 Sending admin PIN to security email...')
      const result = await AdminPinService.generateAndSendPin(adminEmail)
      
      if (result.success) {
        setPinSent(true)
        console.log('✅ Admin PIN sent successfully')
      } else {
        console.error('❌ Failed to send admin PIN:', result.error)
        setError(result.error || 'Failed to send PIN')
        
        // Handle rate limiting
        if (result.error?.includes('wait')) {
          setRateLimited(true)
          const waitMinutes = parseInt(result.error.match(/\d+/)?.[0] || '5')
          setCountdown(waitMinutes * 60)
        }
      }
    } catch (error) {
      console.error('❌ Error sending admin PIN:', error)
      setError('Failed to send PIN')
    } finally {
      setSendingPin(false)
    }
  }

  const verifyPin = async () => {
    if (pin.length !== 6) {
      setError('Please enter a 6-digit PIN')
      return
    }

    setLoading(true)
    setError(null)

    try {
      console.log('🔍 Verifying admin PIN...')
      const result = await AdminPinService.verifyPin(adminEmail, pin)
      
      if (result.success) {
        setSuccess(true)
        console.log('✅ Admin PIN verified successfully')
        setTimeout(() => {
          onVerified()
          handleClose()
        }, 1000)
      } else {
        console.error('❌ Admin PIN verification failed:', result.error)
        setError(result.error || 'Invalid PIN')
        setPin('') // Clear PIN on error
      }
    } catch (error) {
      console.error('❌ Error verifying admin PIN:', error)
      setError('Failed to verify PIN')
      setPin('')
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setPin('')
    setError(null)
    setSuccess(false)
    setPinSent(false)
    setCountdown(0)
    setRateLimited(false)
    onClose()
  }

  const handlePinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 6)
    setPin(value)
    setError(null)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && pin.length === 6 && !loading) {
      verifyPin()
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-lg border border-gray-600 max-w-md w-full p-6 relative">
        {/* Close button */}
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
          disabled={loading || sendingPin}
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* Header */}
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-white mb-2">{title}</h2>
          <p className="text-gray-300 text-sm">{description}</p>
        </div>

        {/* PIN Status */}
        {sendingPin && (
          <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 mb-4">
            <div className="flex items-center gap-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-400"></div>
              <p className="text-blue-300 text-sm">
                📧 Sending security PIN to admin email...
              </p>
            </div>
          </div>
        )}

        {pinSent && !success && (
          <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4 mb-4">
            <p className="text-green-300 text-sm text-center">
              ✅ Security PIN <NAME_EMAIL>
            </p>
          </div>
        )}

        {/* Success Message */}
        {success && (
          <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4 mb-4">
            <p className="text-green-300 text-sm text-center">
              🎉 Admin authentication successful! Redirecting...
            </p>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4 mb-4">
            <p className="text-red-300 text-sm text-center">{error}</p>
          </div>
        )}

        {/* Rate Limiting Message */}
        {rateLimited && countdown > 0 && (
          <div className="bg-orange-900/20 border border-orange-500/30 rounded-lg p-4 mb-4">
            <p className="text-orange-300 text-sm text-center">
              ⏳ Rate limited. Next PIN available in {Math.floor(countdown / 60)}:{(countdown % 60).toString().padStart(2, '0')}
            </p>
          </div>
        )}

        {/* PIN Input */}
        {pinSent && !success && (
          <div className="mb-6">
            <label htmlFor="admin-pin" className="block text-sm font-semibold text-gray-300 mb-3">
              Enter 6-Digit Security PIN
            </label>
            <input
              ref={pinInputRef}
              id="admin-pin"
              type="text"
              value={pin}
              onChange={handlePinChange}
              onKeyPress={handleKeyPress}
              placeholder="000000"
              maxLength={6}
              className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 text-white text-center text-2xl font-mono tracking-widest"
              disabled={loading}
              autoComplete="off"
            />
            <p className="text-gray-400 text-xs mt-2 text-center">
              PIN expires in 15 minutes • Maximum 3 attempts
            </p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3">
          {!pinSent ? (
            <button
              onClick={sendPin}
              disabled={sendingPin || rateLimited}
              className="flex-1 py-3 px-4 bg-red-600 hover:bg-red-700 disabled:bg-red-800 text-white rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
            >
              {sendingPin ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Sending PIN...
                </>
              ) : rateLimited ? (
                `Wait ${Math.floor(countdown / 60)}:${(countdown % 60).toString().padStart(2, '0')}`
              ) : (
                '📧 Send Security PIN'
              )}
            </button>
          ) : (
            <>
              <button
                onClick={handleClose}
                className="flex-1 py-3 px-4 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors"
                disabled={loading}
              >
                Cancel
              </button>
              <button
                onClick={verifyPin}
                disabled={loading || pin.length !== 6}
                className="flex-1 py-3 px-4 bg-red-600 hover:bg-red-700 disabled:bg-red-800 text-white rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Verifying...
                  </>
                ) : (
                  '🔐 Verify & Login'
                )}
              </button>
            </>
          )}
        </div>

        {/* Security Notice */}
        <div className="mt-4 p-3 bg-gray-900/50 rounded-lg border border-gray-700">
          <p className="text-gray-400 text-xs text-center">
            🔒 This is a secure admin authentication process. All attempts are logged and monitored.
          </p>
        </div>
      </div>
    </div>
  )
}
