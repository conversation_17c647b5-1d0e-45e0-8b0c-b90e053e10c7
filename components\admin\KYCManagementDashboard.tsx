/**
 * KY<PERSON> MANAGEMENT DASHBOARD
 * 
 * Comprehensive admin interface for managing KYC submissions with:
 * - Statistics overview
 * - Submission list with search/filter
 * - Detailed submission viewer
 * - Approve/reject functionality
 * - Batch operations
 * - Audit trail
 */

import React, { useState, useEffect, useCallback } from 'react';
import { kycAdminService, KYCSubmission, KYCStatistics, KYCFilters } from '../../lib/services/kycAdminService';
import { notificationService } from '../../lib/notificationService';
import KYCDocumentViewer from './KYCDocumentViewer';
import RejectionReasonModal from './RejectionReasonModal';

interface KYCManagementDashboardProps {
  currentUser: any;
}

const KYCManagementDashboard: React.FC<KYCManagementDashboardProps> = ({ currentUser }) => {
  // State management
  const [submissions, setSubmissions] = useState<KYCSubmission[]>([]);
  const [statistics, setStatistics] = useState<KYCStatistics>({
    total: 0,
    pending: 0,
    completed: 0,
    rejected: 0,
    expired: 0,
    averageProcessingTime: 0,
    completionRate: 0,
    recentSubmissions: 0
  });
  const [selectedSubmission, setSelectedSubmission] = useState<KYCSubmission | null>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');
  const [adminComments, setAdminComments] = useState('');

  // Field rejection modal state
  const [showFieldRejectModal, setShowFieldRejectModal] = useState(false);
  const [fieldRejectData, setFieldRejectData] = useState<{
    submissionId: string;
    fieldName: string;
    fieldDisplayName: string;
  } | null>(null);
  const [fieldRejectionReason, setFieldRejectionReason] = useState('');
  const [processingFields, setProcessingFields] = useState<Set<string>>(new Set());
  const [quickActionSubmission, setQuickActionSubmission] = useState<KYCSubmission | null>(null);
  const [showQuickRejectModal, setShowQuickRejectModal] = useState(false);
  const [processingActions, setProcessingActions] = useState<Set<string>>(new Set());

  // Individual field approval states
  const [fieldApprovals, setFieldApprovals] = useState<{
    [submissionId: string]: {
      personal_info: 'pending' | 'approved' | 'rejected';
      address_info: 'pending' | 'approved' | 'rejected';
      id_document: 'pending' | 'approved' | 'rejected';
      proof_address: 'pending' | 'approved' | 'rejected';
      selfie_verification: 'pending' | 'approved' | 'rejected';
      compliance_info: 'pending' | 'approved' | 'rejected';
    }
  }>({});

  // Filter and search state
  const [filters, setFilters] = useState<KYCFilters>({
    status: 'all',
    searchTerm: ''
  });
  const [searchInput, setSearchInput] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalSubmissions, setTotalSubmissions] = useState(0);
  const itemsPerPage = 20;

  // Modal and action state
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [comments, setComments] = useState('');
  const [selectedSubmissions, setSelectedSubmissions] = useState<Set<string>>(new Set());

  // Load data
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Load statistics
      const stats = await kycAdminService.getKYCStatistics();
      setStatistics(stats);

      // Load submissions with current filters and documents
      const offset = (currentPage - 1) * itemsPerPage;
      const result = await kycAdminService.getKYCSubmissionsWithDocuments(filters, itemsPerPage, offset);
      setSubmissions(result.submissions);
      setTotalSubmissions(result.total);

    } catch (err) {
      console.error('❌ Error loading KYC data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load KYC data');
    } finally {
      setLoading(false);
    }
  }, [filters, currentPage]);

  // Initial load and refresh
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(loadData, 30000);
    return () => clearInterval(interval);
  }, [loadData]);

  // Initialize field approvals when submissions are loaded
  useEffect(() => {
    if (submissions.length > 0) {
      const initialApprovals: typeof fieldApprovals = {};
      submissions.forEach(submission => {
        initialApprovals[submission.id] = {
          personal_info: 'pending',
          address_info: 'pending',
          id_document: 'pending',
          proof_address: 'pending',
          selfie_verification: 'pending',
          compliance_info: 'pending'
        };
      });
      setFieldApprovals(initialApprovals);
    }
  }, [submissions]);

  // Handle search
  const handleSearch = useCallback(() => {
    setFilters(prev => ({ ...prev, searchTerm: searchInput.trim() }));
    setCurrentPage(1);
  }, [searchInput]);

  // Handle filter change
  const handleFilterChange = useCallback((newFilters: Partial<KYCFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1);
  }, []);

  // Handle individual field approval
  const handleFieldApproval = useCallback(async (submissionId: string, field: string, status: 'approved' | 'rejected', rejectionReason?: string) => {
    console.log(`🚀 handleFieldApproval called: ${field} -> ${status}`, { submissionId, rejectionReason });

    if (!currentUser?.email) {
      alert('Admin email not found');
      return;
    }

    const fieldKey = `${submissionId}-${field}`;

    try {
      console.log(`🔄 Processing field approval: ${field} -> ${status}`);

      // Set processing state
      setProcessingFields(prev => new Set(prev).add(fieldKey));

      // Update local state immediately for UI feedback
      setFieldApprovals(prev => {
        const newState = {
          ...prev,
          [submissionId]: {
            ...prev[submissionId],
            [field]: status
          }
        };
        console.log('🔄 Updated fieldApprovals state:', newState);
        return newState;
      });

      // Save to database
      const result = await kycAdminService.updateFieldApproval(
        submissionId,
        field,
        status,
        currentUser.email,
        rejectionReason
      );

      if (!result.success) {
        console.error('❌ Database update failed:', result.error);
        // Revert local state on error
        setFieldApprovals(prev => ({
          ...prev,
          [submissionId]: {
            ...prev[submissionId],
            [field]: prev[submissionId]?.[field] || 'pending'
          }
        }));
        alert(`Failed to update field approval: ${result.error}`);
      } else {
        console.log(`✅ Field approval updated successfully: ${field} -> ${status}`);

        // If rejected, send email notification
        if (status === 'rejected' && rejectionReason) {
          console.log('📧 Sending rejection notification...');
          await sendFieldRejectionNotification(submissionId, field, rejectionReason);
        }
      }
    } catch (error) {
      console.error('❌ Error updating field approval:', error);
      alert('Failed to update field approval');
    } finally {
      // Clear processing state
      setProcessingFields(prev => {
        const newSet = new Set(prev);
        newSet.delete(fieldKey);
        return newSet;
      });
    }
  }, [currentUser?.email]);

  // Handle field reset to pending
  const handleFieldReset = useCallback(async (submissionId: string, field: string) => {
    console.log(`🔄 Resetting field: ${field} to pending`);
    await handleFieldApproval(submissionId, field, 'pending');
  }, [handleFieldApproval]);

  // Handle field rejection with reason
  const handleFieldReject = useCallback((submissionId: string, field: string, fieldDisplayName: string) => {
    setFieldRejectData({ submissionId, fieldName: field, fieldDisplayName });
    setFieldRejectionReason('');
    setShowFieldRejectModal(true);
  }, []);

  // Confirm field rejection
  const confirmFieldRejection = useCallback(async () => {
    if (!fieldRejectData || !fieldRejectionReason.trim()) {
      alert('Please provide a rejection reason');
      return;
    }

    await handleFieldApproval(
      fieldRejectData.submissionId,
      fieldRejectData.fieldName,
      'rejected',
      fieldRejectionReason.trim()
    );

    setShowFieldRejectModal(false);
    setFieldRejectData(null);
    setFieldRejectionReason('');
  }, [fieldRejectData, fieldRejectionReason, handleFieldApproval]);

  // Send field rejection notification
  const sendFieldRejectionNotification = useCallback(async (submissionId: string, field: string, rejectionReason: string) => {
    try {
      console.log('📧 Sending field rejection notification for:', field);

      // Use selectedSubmission directly since we're in the context of viewing it
      if (!selectedSubmission || selectedSubmission.id !== submissionId) {
        console.error('❌ Selected submission not available or ID mismatch');
        return;
      }

      console.log('✅ Using selectedSubmission for notification:', selectedSubmission.user_id);

      // Create in-app notification
      const inAppNotification = await notificationService.createNotification({
        user_id: selectedSubmission.user_id,
        type: 'account_update',
        title: '⚠️ KYC Field Requires Attention',
        message: `Your ${getFieldDisplayName(field)} needs to be updated. Reason: ${rejectionReason}. Please resubmit this information.`,
        action_url: '/kyc',
        metadata: {
          kyc_id: submissionId,
          field_name: field,
          rejection_reason: rejectionReason,
          rejection_date: new Date().toISOString()
        }
      });

      if (!inAppNotification) {
        console.warn('⚠️ Failed to create in-app notification for field rejection');
        return;
      }

      console.log(`✅ Field rejection notification sent for: ${field}`);
    } catch (error) {
      console.error('❌ Error sending field rejection notification:', error);
    }
  }, [selectedSubmission]);

  // Get field display name
  const getFieldDisplayName = (field: string): string => {
    const fieldNames: Record<string, string> = {
      personal_info: 'Personal Information',
      address_info: 'Address Information',
      compliance_info: 'Compliance Information',
      id_document: 'ID Document',
      proof_address: 'Proof of Address',
      selfie_verification: 'Selfie Verification'
    };
    return fieldNames[field] || field;
  };

  // Handle KYC approval
  const handleApproveKYC = async (submission: KYCSubmission) => {
    if (!currentUser?.email) {
      alert('User email not found');
      return;
    }

    try {
      setProcessing(true);
      const result = await kycAdminService.approveKYC(
        submission.id,
        currentUser.email,
        adminComments.trim() || undefined
      );

      if (result.success) {
        setAdminComments('');
        setSelectedSubmission(null);
        loadData();
        alert('KYC approved successfully!');
      } else {
        alert(`Failed to approve KYC: ${result.error}`);
      }
    } catch (error) {
      console.error('Error approving KYC:', error);
      alert('Failed to approve KYC');
    } finally {
      setProcessing(false);
    }
  };

  // Handle KYC rejection
  const handleRejectKYC = async () => {
    if (!selectedSubmission || !currentUser?.email || !rejectionReason.trim()) {
      alert('Please provide a rejection reason');
      return;
    }

    try {
      setProcessing(true);
      const result = await kycAdminService.rejectKYC(
        selectedSubmission.id,
        currentUser.email,
        rejectionReason.trim(),
        adminComments.trim() || undefined
      );

      if (result.success) {
        setRejectionReason('');
        setAdminComments('');
        setShowRejectModal(false);
        setSelectedSubmission(null);
        loadData();
        alert('KYC rejected successfully!');
      } else {
        alert(`Failed to reject KYC: ${result.error}`);
      }
    } catch (error) {
      console.error('Error rejecting KYC:', error);
      alert('Failed to reject KYC');
    } finally {
      setProcessing(false);
    }
  };

  // Handle quick approve action
  const handleQuickApprove = async (submission: KYCSubmission) => {
    if (!currentUser?.email) {
      alert('User email not found');
      return;
    }

    const confirmed = window.confirm(
      `Are you sure you want to approve the KYC submission for ${submission.full_legal_name || `${submission.first_name} ${submission.last_name}`}?`
    );

    if (!confirmed) return;

    try {
      setProcessingActions(prev => new Set(prev).add(submission.id));

      const result = await kycAdminService.quickApproveKYC(
        submission.id,
        currentUser.email,
        'Quick approved from admin dashboard'
      );

      if (result.success) {
        // Show success notification
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg z-50';
        notification.textContent = '✅ KYC approved successfully!';
        document.body.appendChild(notification);
        setTimeout(() => document.body.removeChild(notification), 3000);

        // Refresh data
        loadData();
      } else {
        alert(`Failed to approve KYC: ${result.error}`);
      }
    } catch (error) {
      console.error('Error approving KYC:', error);
      alert('Failed to approve KYC');
    } finally {
      setProcessingActions(prev => {
        const newSet = new Set(prev);
        newSet.delete(submission.id);
        return newSet;
      });
    }
  };

  // Handle quick reject action
  const handleQuickReject = (submission: KYCSubmission) => {
    setQuickActionSubmission(submission);
    setShowQuickRejectModal(true);
  };

  // Handle quick reject submission
  const handleQuickRejectSubmit = async (rejectionReason: string, adminNotes?: string) => {
    if (!quickActionSubmission || !currentUser?.email) {
      throw new Error('Missing submission or user information');
    }

    try {
      setProcessingActions(prev => new Set(prev).add(quickActionSubmission.id));

      const result = await kycAdminService.quickRejectKYC(
        quickActionSubmission.id,
        currentUser.email,
        rejectionReason,
        adminNotes
      );

      if (result.success) {
        // Show success notification
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-red-600 text-white px-6 py-3 rounded-lg shadow-lg z-50';
        notification.textContent = '❌ KYC rejected successfully!';
        document.body.appendChild(notification);
        setTimeout(() => document.body.removeChild(notification), 3000);

        // Refresh data
        loadData();
        setQuickActionSubmission(null);
      } else {
        throw new Error(result.error || 'Failed to reject KYC');
      }
    } finally {
      setProcessingActions(prev => {
        const newSet = new Set(prev);
        newSet.delete(quickActionSubmission.id);
        return newSet;
      });
    }
  };

  // Handle submission selection
  const handleSubmissionSelect = useCallback((submissionId: string, selected: boolean) => {
    setSelectedSubmissions(prev => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(submissionId);
      } else {
        newSet.delete(submissionId);
      }
      return newSet;
    });
  }, []);

  // Handle select all
  const handleSelectAll = useCallback((selected: boolean) => {
    if (selected) {
      setSelectedSubmissions(new Set(submissions.map(s => s.id)));
    } else {
      setSelectedSubmissions(new Set());
    }
  }, [submissions]);

  // Handle approve submission
  const handleApprove = async (submission: KYCSubmission) => {
    if (!currentUser?.email) return;

    try {
      setProcessing(true);
      const result = await kycAdminService.approveKYC(
        submission.id,
        currentUser.email,
        comments.trim() || undefined
      );

      if (result.success) {
        await loadData();
        setComments('');
        setShowDetailModal(false);
        alert('KYC submission approved successfully!');
      } else {
        alert(`Failed to approve KYC: ${result.error}`);
      }
    } catch (err) {
      console.error('❌ Error approving KYC:', err);
      alert('Failed to approve KYC submission');
    } finally {
      setProcessing(false);
    }
  };

  // Handle reject submission
  const handleReject = async (submission: KYCSubmission) => {
    if (!currentUser?.email || !rejectionReason.trim()) return;

    try {
      setProcessing(true);
      const result = await kycAdminService.rejectKYC(
        submission.id,
        currentUser.email,
        rejectionReason.trim(),
        comments.trim() || undefined
      );

      if (result.success) {
        await loadData();
        setRejectionReason('');
        setComments('');
        setShowRejectModal(false);
        setShowDetailModal(false);
        alert('KYC submission rejected successfully!');
      } else {
        alert(`Failed to reject KYC: ${result.error}`);
      }
    } catch (err) {
      console.error('❌ Error rejecting KYC:', err);
      alert('Failed to reject KYC submission');
    } finally {
      setProcessing(false);
    }
  };

  // Handle batch approve
  const handleBatchApprove = async () => {
    if (selectedSubmissions.size === 0 || !currentUser?.email) return;

    const confirmed = confirm(`Are you sure you want to approve ${selectedSubmissions.size} KYC submissions?`);
    if (!confirmed) return;

    try {
      setProcessing(true);
      const result = await kycAdminService.batchApproveKYC(
        Array.from(selectedSubmissions),
        currentUser.email,
        comments.trim() || undefined
      );

      alert(`Batch approval completed: ${result.successful} successful, ${result.failed} failed`);
      
      if (result.errors.length > 0) {
        console.error('Batch approval errors:', result.errors);
      }

      await loadData();
      setSelectedSubmissions(new Set());
      setComments('');
    } catch (err) {
      console.error('❌ Error in batch approval:', err);
      alert('Failed to perform batch approval');
    } finally {
      setProcessing(false);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'completed': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'rejected': return 'bg-red-500/20 text-red-400 border-red-500/30';
      case 'expired': return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  // Calculate pagination
  const totalPages = Math.ceil(totalSubmissions / itemsPerPage);
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalSubmissions);

  if (loading && submissions.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading KYC submissions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-yellow-400">🔍 KYC Management</h2>
          <p className="text-gray-400 mt-1">
            Review and manage Know Your Customer submissions
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={loadData}
            disabled={loading}
            className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors disabled:opacity-50"
          >
            {loading ? '🔄' : '↻'} Refresh
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4">
          <p className="text-red-400">❌ {error}</p>
        </div>
      )}

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="glass-card p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Total Submissions</p>
              <p className="text-2xl font-bold text-white">{statistics.total.toLocaleString()}</p>
            </div>
            <div className="text-3xl">📋</div>
          </div>
        </div>

        <div className="glass-card p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Pending Review</p>
              <p className="text-2xl font-bold text-yellow-400">{statistics.pending.toLocaleString()}</p>
            </div>
            <div className="text-3xl">⏳</div>
          </div>
        </div>

        <div className="glass-card p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Approved</p>
              <p className="text-2xl font-bold text-green-400">{statistics.completed.toLocaleString()}</p>
            </div>
            <div className="text-3xl">✅</div>
          </div>
        </div>

        <div className="glass-card p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Avg. Processing</p>
              <p className="text-2xl font-bold text-blue-400">{statistics.averageProcessingTime.toFixed(1)}h</p>
            </div>
            <div className="text-3xl">⚡</div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="glass-card p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="flex gap-2">
              <input
                type="text"
                placeholder="Search by name, email, username..."
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="flex-1 px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-yellow-400"
              />
              <button
                onClick={handleSearch}
                className="px-6 py-2 bg-yellow-500 hover:bg-yellow-400 text-black font-medium rounded-lg transition-colors"
              >
                🔍 Search
              </button>
            </div>
          </div>

          {/* Status Filter */}
          <div className="flex gap-2">
            <select
              value={filters.status || 'all'}
              onChange={(e) => handleFilterChange({ status: e.target.value as any })}
              className="px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-400"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="completed">Approved</option>
              <option value="rejected">Rejected</option>
              <option value="expired">Expired</option>
            </select>

            {filters.searchTerm && (
              <button
                onClick={() => {
                  setSearchInput('');
                  handleFilterChange({ searchTerm: '' });
                }}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-lg transition-colors"
              >
                ✕ Clear
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Batch Actions */}
      {selectedSubmissions.size > 0 && (
        <div className="glass-card p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-yellow-400 font-medium">
                {selectedSubmissions.size} submission{selectedSubmissions.size !== 1 ? 's' : ''} selected
              </span>
              <input
                type="text"
                placeholder="Optional comments for batch action..."
                value={comments}
                onChange={(e) => setComments(e.target.value)}
                className="px-3 py-1 bg-gray-800 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:border-yellow-400"
              />
            </div>
            <div className="flex space-x-2">
              <button
                onClick={handleBatchApprove}
                disabled={processing}
                className="px-4 py-2 bg-green-600 hover:bg-green-500 text-white rounded-lg transition-colors disabled:opacity-50"
              >
                ✅ Batch Approve
              </button>
              <button
                onClick={() => setSelectedSubmissions(new Set())}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded-lg transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Submissions List */}
      <div className="glass-card">
        {/* Table Header */}
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-white">
              KYC Submissions ({totalSubmissions.toLocaleString()})
            </h3>
            <div className="flex items-center space-x-2">
              <label className="flex items-center space-x-2 text-sm text-gray-400">
                <input
                  type="checkbox"
                  checked={selectedSubmissions.size === submissions.length && submissions.length > 0}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="rounded border-gray-600 bg-gray-800 text-yellow-400 focus:ring-yellow-400"
                />
                <span>Select All</span>
              </label>
            </div>
          </div>
        </div>

        {/* Table Content */}
        <div className="overflow-x-auto">
          {submissions.length === 0 ? (
            <div className="p-8 text-center">
              <div className="text-6xl mb-4">📋</div>
              <p className="text-gray-400 text-lg">No KYC submissions found</p>
              <p className="text-gray-500 text-sm mt-2">
                {filters.searchTerm || filters.status !== 'all'
                  ? 'Try adjusting your search or filters'
                  : 'KYC submissions will appear here when users submit their verification'
                }
              </p>
            </div>
          ) : (
            <table className="w-full">
              <thead className="bg-gray-800/50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Select
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Country
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Submitted
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-700">
                {submissions.map((submission) => (
                  <tr key={submission.id} className="hover:bg-gray-800/30 transition-colors">
                    <td className="px-4 py-4">
                      <input
                        type="checkbox"
                        checked={selectedSubmissions.has(submission.id)}
                        onChange={(e) => handleSubmissionSelect(submission.id, e.target.checked)}
                        className="rounded border-gray-600 bg-gray-800 text-yellow-400 focus:ring-yellow-400"
                      />
                    </td>
                    <td className="px-4 py-4">
                      <div>
                        <div className="text-sm font-medium text-white">
                          @{submission.users?.username}
                        </div>
                        <div className="text-sm text-gray-400">
                          {submission.users?.email}
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <div className="text-sm font-medium text-white">
                        {submission.full_legal_name || `${submission.first_name} ${submission.last_name}`}
                      </div>
                      <div className="text-sm text-gray-400 flex items-center space-x-2">
                        <span>ID: {submission.id_type.replace('_', ' ').toUpperCase()}</span>
                        {submission.documents && submission.documents.length > 0 && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-500/20 text-blue-400 border border-blue-500/30">
                            📄 {submission.documents.length} doc{submission.documents.length !== 1 ? 's' : ''}
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <div className="text-sm text-white">
                        {submission.country_name}
                      </div>
                      <div className="text-sm text-gray-400">
                        {submission.country_code}
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${getStatusBadgeColor(submission.kyc_status)}`}>
                        {submission.kyc_status.charAt(0).toUpperCase() + submission.kyc_status.slice(1)}
                      </span>
                    </td>
                    <td className="px-4 py-4 text-sm text-gray-400">
                      {formatDate(submission.created_at)}
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex space-x-2">
                        <button
                          onClick={async () => {
                            console.log('🔥 VIEW BUTTON CLICKED, setting selectedSubmission:', submission.id);
                            setSelectedSubmission(submission);
                            setShowDetailModal(true);

                            // Load existing field approvals only if not already loaded
                            try {
                              const existingApprovals = fieldApprovals[submission.id];
                              if (!existingApprovals) {
                                console.log('📋 Loading field approvals for first time:', submission.id);
                                const approvals = await kycAdminService.getFieldApprovals(submission.id);
                                setFieldApprovals(prev => ({
                                  ...prev,
                                  [submission.id]: approvals
                                }));
                              } else {
                                console.log('📋 Using existing field approvals:', existingApprovals);
                              }
                            } catch (error) {
                              console.error('❌ Error loading field approvals:', error);
                            }
                          }}
                          className="px-3 py-1 bg-blue-600 hover:bg-blue-500 text-white text-xs rounded transition-colors"
                        >
                          👁️ View
                        </button>
                        {submission.kyc_status === 'pending' && (
                          <>
                            <button
                              onClick={() => handleQuickApprove(submission)}
                              disabled={processingActions.has(submission.id)}
                              className="px-3 py-1 bg-green-600 hover:bg-green-500 text-white text-xs rounded transition-colors disabled:opacity-50 flex items-center space-x-1"
                            >
                              {processingActions.has(submission.id) ? (
                                <>
                                  <svg className="animate-spin h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                  </svg>
                                  <span>...</span>
                                </>
                              ) : (
                                <>
                                  <span>✅</span>
                                  <span>Approve</span>
                                </>
                              )}
                            </button>
                            <button
                              onClick={() => handleQuickReject(submission)}
                              disabled={processingActions.has(submission.id)}
                              className="px-3 py-1 bg-red-600 hover:bg-red-500 text-white text-xs rounded transition-colors disabled:opacity-50 flex items-center space-x-1"
                            >
                              {processingActions.has(submission.id) ? (
                                <>
                                  <svg className="animate-spin h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                  </svg>
                                  <span>...</span>
                                </>
                              ) : (
                                <>
                                  <span>❌</span>
                                  <span>Reject</span>
                                </>
                              )}
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-4 py-3 border-t border-gray-700 flex items-center justify-between">
            <div className="text-sm text-gray-400">
              Showing {startItem} to {endItem} of {totalSubmissions.toLocaleString()} results
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-white rounded transition-colors disabled:opacity-50"
              >
                Previous
              </button>
              <span className="px-3 py-1 bg-gray-800 text-white rounded">
                Page {currentPage} of {totalPages}
              </span>
              <button
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-white rounded transition-colors disabled:opacity-50"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Detail Modal */}
      {showDetailModal && selectedSubmission && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="sticky top-0 bg-gray-900 border-b border-gray-700 px-6 py-4">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-xl font-bold text-white">
                    🔍 KYC Submission Details
                  </h2>
                  <p className="text-gray-400 text-sm">
                    {selectedSubmission.full_legal_name || `${selectedSubmission.first_name} ${selectedSubmission.last_name}`}
                  </p>
                </div>
                <button
                  onClick={() => {
                    console.log('🔥 MODAL CLOSE BUTTON CLICKED');
                    setShowDetailModal(false);
                  }}
                  className="text-gray-400 hover:text-white text-2xl"
                >
                  ×
                </button>
              </div>
            </div>

            {/* Modal Content */}
            <div className="p-6 space-y-6">
              {/* User Information */}
              <div className="glass-card p-4">
                <h3 className="text-lg font-semibold text-yellow-400 mb-4">👤 User Information</h3>

                {/* Debug info for missing user data */}
                {!selectedSubmission.users && (
                  <div className="bg-red-900/20 border border-red-600/30 rounded-lg p-3 mb-4">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-red-400">⚠️</span>
                      <span className="text-red-400 font-medium">User Data Missing</span>
                    </div>
                    <p className="text-red-200 text-sm">
                      User ID: {selectedSubmission.user_id} - User record not found or join failed.
                    </p>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm text-gray-400">Username</label>
                    <p className="text-white font-medium">
                      @{selectedSubmission.users?.username || 'Unknown'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">Email</label>
                    <p className="text-white font-medium">
                      {selectedSubmission.users?.email || selectedSubmission.email_address || 'Not available'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">Account Status</label>
                    <p className="text-white font-medium">
                      {selectedSubmission.users?.is_active === true ? '✅ Active' :
                       selectedSubmission.users?.is_active === false ? '❌ Inactive' :
                       '❓ Unknown'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">Member Since</label>
                    <p className="text-white font-medium">
                      {selectedSubmission.users?.created_at ? formatDate(selectedSubmission.users.created_at) : 'N/A'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">User ID</label>
                    <p className="text-white font-medium">#{selectedSubmission.user_id}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">KYC ID</label>
                    <p className="text-white font-medium text-xs">{selectedSubmission.id}</p>
                  </div>
                </div>
              </div>

              {/* Personal Information */}
              <div className="glass-card p-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold text-yellow-400">📋 Personal Information</h3>
                  <div className="flex gap-2">
                    {/* Reset button - only show if field has been approved or rejected */}
                    {fieldApprovals[selectedSubmission.id]?.personal_info && fieldApprovals[selectedSubmission.id]?.personal_info !== 'pending' && (
                      <button
                        onClick={() => handleFieldApproval(selectedSubmission.id, 'personal_info', 'pending')}
                        className="px-2 py-1 rounded text-xs font-medium bg-gray-600 text-gray-300 hover:bg-gray-500 transition-colors"
                        title="Reset to pending"
                      >
                        🔄 Reset
                      </button>
                    )}
                    <button
                      onClick={() => handleFieldApproval(selectedSubmission.id, 'personal_info', 'approved')}
                      disabled={processingFields.has(`${selectedSubmission.id}-personal_info`) || fieldApprovals[selectedSubmission.id]?.personal_info === 'approved'}
                      className={`px-3 py-1 rounded text-sm font-medium transition-all duration-300 ${
                        (() => {
                          const status = fieldApprovals[selectedSubmission.id]?.personal_info;
                          const isProcessing = processingFields.has(`${selectedSubmission.id}-personal_info`);

                          if (isProcessing) {
                            return 'bg-yellow-600 text-white animate-pulse';
                          } else if (status === 'approved') {
                            return 'bg-green-600 text-white border-2 border-green-400 shadow-lg shadow-green-500/50 cursor-not-allowed';
                          } else if (status === 'rejected') {
                            return 'bg-green-600/20 text-green-400 hover:bg-green-600/30';
                          } else {
                            return 'bg-green-600/20 text-green-400 hover:bg-green-600/30';
                          }
                        })()
                      }`}
                    >
                      {(() => {
                        const status = fieldApprovals[selectedSubmission.id]?.personal_info;
                        const isProcessing = processingFields.has(`${selectedSubmission.id}-personal_info`);

                        if (isProcessing) {
                          return '⏳ PROCESSING...';
                        } else if (status === 'approved') {
                          return '✅ APPROVED';
                        } else {
                          return '✅ Approve';
                        }
                      })()}
                    </button>
                    <button
                      onClick={() => handleFieldReject(selectedSubmission.id, 'personal_info', 'Personal Information')}
                      disabled={processingFields.has(`${selectedSubmission.id}-personal_info`) || fieldApprovals[selectedSubmission.id]?.personal_info === 'rejected'}
                      className={`px-3 py-1 rounded text-sm font-medium transition-all duration-300 ${
                        (() => {
                          const status = fieldApprovals[selectedSubmission.id]?.personal_info;
                          const isProcessing = processingFields.has(`${selectedSubmission.id}-personal_info`);

                          if (isProcessing) {
                            return 'bg-yellow-600 text-white animate-pulse';
                          } else if (status === 'rejected') {
                            return 'bg-red-600 text-white border-2 border-red-400 shadow-lg shadow-red-500/50 cursor-not-allowed';
                          } else if (status === 'approved') {
                            return 'bg-red-600/10 text-red-300 cursor-not-allowed opacity-50';
                          } else {
                            return 'bg-red-600/20 text-red-400 hover:bg-red-600/30';
                          }
                        })()
                      }`}
                    >
                      {(() => {
                        const status = fieldApprovals[selectedSubmission.id]?.personal_info;
                        const isProcessing = processingFields.has(`${selectedSubmission.id}-personal_info`);

                        if (isProcessing) {
                          return '⏳ PROCESSING...';
                        } else if (status === 'rejected') {
                          return '❌ REJECTED';
                        } else if (status === 'approved') {
                          return '❌ Reject (Approved)';
                        } else {
                          return '❌ Reject';
                        }
                      })()}
                    </button>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm text-gray-400">Full Legal Name</label>
                    <p className="text-white font-medium">
                      {selectedSubmission.full_legal_name || `${selectedSubmission.first_name} ${selectedSubmission.last_name}`}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">ID Type</label>
                    <p className="text-white font-medium">
                      {selectedSubmission.id_type.replace('_', ' ').toUpperCase()}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">Phone Number</label>
                    <p className="text-white font-medium">{selectedSubmission.phone_number}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">Email Address</label>
                    <p className="text-white font-medium">{selectedSubmission.email_address}</p>
                  </div>
                </div>
              </div>

              {/* Address Information */}
              <div className="glass-card p-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold text-yellow-400">🏠 Address Information</h3>
                  <div className="flex gap-2">
                    {fieldApprovals[selectedSubmission.id]?.address_info === 'approved' ? (
                      <>
                        <button
                          onClick={() => handleFieldReset(selectedSubmission.id, 'address_info')}
                          className="px-3 py-1 rounded text-sm font-medium bg-gray-600/20 text-gray-400 hover:bg-gray-600/30 transition-colors"
                        >
                          🔄 Reset
                        </button>
                        <button
                          className="px-3 py-1 rounded text-sm font-medium bg-green-600 text-white border-2 border-green-400"
                        >
                          ✅ APPROVED
                        </button>
                        <button
                          onClick={() => handleFieldReject(selectedSubmission.id, 'address_info', 'Address Information')}
                          className="px-3 py-1 rounded text-sm font-medium bg-red-600/10 text-red-400/50 cursor-not-allowed"
                          disabled
                        >
                          ❌ Reject (Approved)
                        </button>
                      </>
                    ) : fieldApprovals[selectedSubmission.id]?.address_info === 'rejected' ? (
                      <>
                        <button
                          onClick={() => handleFieldReset(selectedSubmission.id, 'address_info')}
                          className="px-3 py-1 rounded text-sm font-medium bg-gray-600/20 text-gray-400 hover:bg-gray-600/30 transition-colors"
                        >
                          🔄 Reset
                        </button>
                        <button
                          onClick={() => handleFieldApproval(selectedSubmission.id, 'address_info', 'approved')}
                          className="px-3 py-1 rounded text-sm font-medium bg-green-600/10 text-green-400/50 cursor-not-allowed"
                          disabled
                        >
                          ✅ Approve (Rejected)
                        </button>
                        <button
                          className="px-3 py-1 rounded text-sm font-medium bg-red-600 text-white border-2 border-red-400"
                        >
                          ❌ REJECTED
                        </button>
                      </>
                    ) : (
                      <>
                        <button
                          onClick={() => handleFieldApproval(selectedSubmission.id, 'address_info', 'approved')}
                          className="px-3 py-1 rounded text-sm font-medium bg-green-600/20 text-green-400 hover:bg-green-600/30 transition-colors"
                        >
                          ✅ Approve
                        </button>
                        <button
                          onClick={() => handleFieldReject(selectedSubmission.id, 'address_info', 'Address Information')}
                          className="px-3 py-1 rounded text-sm font-medium bg-red-600/20 text-red-400 hover:bg-red-600/30 transition-colors"
                        >
                          ❌ Reject
                        </button>
                      </>
                    )}
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="md:col-span-2">
                    <label className="text-sm text-gray-400">Street Address</label>
                    <p className="text-white font-medium">{selectedSubmission.street_address}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">City</label>
                    <p className="text-white font-medium">{selectedSubmission.city}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">Postal Code</label>
                    <p className="text-white font-medium">{selectedSubmission.postal_code}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">Country</label>
                    <p className="text-white font-medium">
                      {selectedSubmission.country_name} ({selectedSubmission.country_code})
                    </p>
                  </div>
                </div>
              </div>

              {/* Compliance Information */}
              <div className="glass-card p-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold text-yellow-400">✅ Compliance Information</h3>
                  <div className="flex gap-2">
                    {fieldApprovals[selectedSubmission.id]?.compliance_info === 'approved' ? (
                      <>
                        <button
                          onClick={() => handleFieldReset(selectedSubmission.id, 'compliance_info')}
                          className="px-3 py-1 rounded text-sm font-medium bg-gray-600/20 text-gray-400 hover:bg-gray-600/30 transition-colors"
                        >
                          🔄 Reset
                        </button>
                        <button
                          className="px-3 py-1 rounded text-sm font-medium bg-green-600 text-white border-2 border-green-400"
                        >
                          ✅ APPROVED
                        </button>
                        <button
                          onClick={() => handleFieldReject(selectedSubmission.id, 'compliance_info', 'Compliance Information')}
                          className="px-3 py-1 rounded text-sm font-medium bg-red-600/10 text-red-400/50 cursor-not-allowed"
                          disabled
                        >
                          ❌ Reject (Approved)
                        </button>
                      </>
                    ) : fieldApprovals[selectedSubmission.id]?.compliance_info === 'rejected' ? (
                      <>
                        <button
                          onClick={() => handleFieldReset(selectedSubmission.id, 'compliance_info')}
                          className="px-3 py-1 rounded text-sm font-medium bg-gray-600/20 text-gray-400 hover:bg-gray-600/30 transition-colors"
                        >
                          🔄 Reset
                        </button>
                        <button
                          onClick={() => handleFieldApproval(selectedSubmission.id, 'compliance_info', 'approved')}
                          className="px-3 py-1 rounded text-sm font-medium bg-green-600/10 text-green-400/50 cursor-not-allowed"
                          disabled
                        >
                          ✅ Approve (Rejected)
                        </button>
                        <button
                          className="px-3 py-1 rounded text-sm font-medium bg-red-600 text-white border-2 border-red-400"
                        >
                          ❌ REJECTED
                        </button>
                      </>
                    ) : (
                      <>
                        <button
                          onClick={() => handleFieldApproval(selectedSubmission.id, 'compliance_info', 'approved')}
                          className="px-3 py-1 rounded text-sm font-medium bg-green-600/20 text-green-400 hover:bg-green-600/30 transition-colors"
                        >
                          ✅ Approve
                        </button>
                        <button
                          onClick={() => handleFieldReject(selectedSubmission.id, 'compliance_info', 'Compliance Information')}
                          className="px-3 py-1 rounded text-sm font-medium bg-red-600/20 text-red-400 hover:bg-red-600/30 transition-colors"
                        >
                          ❌ Reject
                        </button>
                      </>
                    )}
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm text-gray-400">Data Consent</label>
                    <p className="text-white font-medium">
                      {selectedSubmission.data_consent_given ? '✅ Given' : '❌ Not Given'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">Privacy Policy</label>
                    <p className="text-white font-medium">
                      {selectedSubmission.privacy_policy_accepted ? '✅ Accepted' : '❌ Not Accepted'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">KYC Status</label>
                    <p className="text-white font-medium">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${getStatusBadgeColor(selectedSubmission.kyc_status)}`}>
                        {selectedSubmission.kyc_status.charAt(0).toUpperCase() + selectedSubmission.kyc_status.slice(1)}
                      </span>
                    </p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">Submitted</label>
                    <p className="text-white font-medium">{formatDate(selectedSubmission.created_at)}</p>
                  </div>
                </div>
              </div>

              {/* Document Verification */}
              <div className="glass-card p-4">
                <div className="space-y-4">
                  {/* ID Document */}
                  <div className="border border-gray-600 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-3">
                      <h4 className="text-md font-semibold text-yellow-400">🆔 ID Document</h4>
                      <div className="flex gap-2">
                        {fieldApprovals[selectedSubmission.id]?.id_document === 'approved' ? (
                          <>
                            <button
                              onClick={() => handleFieldReset(selectedSubmission.id, 'id_document')}
                              className="px-3 py-1 rounded text-sm font-medium bg-gray-600/20 text-gray-400 hover:bg-gray-600/30 transition-colors"
                            >
                              🔄 Reset
                            </button>
                            <button
                              className="px-3 py-1 rounded text-sm font-medium bg-green-600 text-white border-2 border-green-400"
                            >
                              ✅ APPROVED
                            </button>
                            <button
                              onClick={() => handleFieldReject(selectedSubmission.id, 'id_document', 'ID Document')}
                              className="px-3 py-1 rounded text-sm font-medium bg-red-600/10 text-red-400/50 cursor-not-allowed"
                              disabled
                            >
                              ❌ Reject (Approved)
                            </button>
                          </>
                        ) : fieldApprovals[selectedSubmission.id]?.id_document === 'rejected' ? (
                          <>
                            <button
                              onClick={() => handleFieldReset(selectedSubmission.id, 'id_document')}
                              className="px-3 py-1 rounded text-sm font-medium bg-gray-600/20 text-gray-400 hover:bg-gray-600/30 transition-colors"
                            >
                              🔄 Reset
                            </button>
                            <button
                              onClick={() => handleFieldApproval(selectedSubmission.id, 'id_document', 'approved')}
                              className="px-3 py-1 rounded text-sm font-medium bg-green-600/10 text-green-400/50 cursor-not-allowed"
                              disabled
                            >
                              ✅ Approve (Rejected)
                            </button>
                            <button
                              className="px-3 py-1 rounded text-sm font-medium bg-red-600 text-white border-2 border-red-400"
                            >
                              ❌ REJECTED
                            </button>
                          </>
                        ) : (
                          <>
                            <button
                              onClick={() => handleFieldApproval(selectedSubmission.id, 'id_document', 'approved')}
                              className="px-3 py-1 rounded text-sm font-medium bg-green-600/20 text-green-400 hover:bg-green-600/30 transition-colors"
                            >
                              ✅ Approve
                            </button>
                            <button
                              onClick={() => handleFieldReject(selectedSubmission.id, 'id_document', 'ID Document')}
                              className="px-3 py-1 rounded text-sm font-medium bg-red-600/20 text-red-400 hover:bg-red-600/30 transition-colors"
                            >
                              ❌ Reject
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                    <KYCDocumentViewer
                      kycData={selectedSubmission}
                      onDocumentStatusChange={loadData}
                      currentUser={currentUser}
                      documentType="id_document"
                    />
                  </div>

                  {/* Proof of Address */}
                  <div className="border border-gray-600 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-3">
                      <h4 className="text-md font-semibold text-yellow-400">🏠 Proof of Address</h4>
                      <div className="flex gap-2">
                        {fieldApprovals[selectedSubmission.id]?.proof_address === 'approved' ? (
                          <>
                            <button
                              onClick={() => handleFieldReset(selectedSubmission.id, 'proof_address')}
                              className="px-3 py-1 rounded text-sm font-medium bg-gray-600/20 text-gray-400 hover:bg-gray-600/30 transition-colors"
                            >
                              🔄 Reset
                            </button>
                            <button
                              className="px-3 py-1 rounded text-sm font-medium bg-green-600 text-white border-2 border-green-400"
                            >
                              ✅ APPROVED
                            </button>
                            <button
                              onClick={() => handleFieldReject(selectedSubmission.id, 'proof_address', 'Proof of Address')}
                              className="px-3 py-1 rounded text-sm font-medium bg-red-600/10 text-red-400/50 cursor-not-allowed"
                              disabled
                            >
                              ❌ Reject (Approved)
                            </button>
                          </>
                        ) : fieldApprovals[selectedSubmission.id]?.proof_address === 'rejected' ? (
                          <>
                            <button
                              onClick={() => handleFieldReset(selectedSubmission.id, 'proof_address')}
                              className="px-3 py-1 rounded text-sm font-medium bg-gray-600/20 text-gray-400 hover:bg-gray-600/30 transition-colors"
                            >
                              🔄 Reset
                            </button>
                            <button
                              onClick={() => handleFieldApproval(selectedSubmission.id, 'proof_address', 'approved')}
                              className="px-3 py-1 rounded text-sm font-medium bg-green-600/10 text-green-400/50 cursor-not-allowed"
                              disabled
                            >
                              ✅ Approve (Rejected)
                            </button>
                            <button
                              className="px-3 py-1 rounded text-sm font-medium bg-red-600 text-white border-2 border-red-400"
                            >
                              ❌ REJECTED
                            </button>
                          </>
                        ) : (
                          <>
                            <button
                              onClick={() => handleFieldApproval(selectedSubmission.id, 'proof_address', 'approved')}
                              className="px-3 py-1 rounded text-sm font-medium bg-green-600/20 text-green-400 hover:bg-green-600/30 transition-colors"
                            >
                              ✅ Approve
                            </button>
                            <button
                              onClick={() => handleFieldReject(selectedSubmission.id, 'proof_address', 'Proof of Address')}
                              className="px-3 py-1 rounded text-sm font-medium bg-red-600/20 text-red-400 hover:bg-red-600/30 transition-colors"
                            >
                              ❌ Reject
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                    <KYCDocumentViewer
                      kycData={selectedSubmission}
                      onDocumentStatusChange={loadData}
                      currentUser={currentUser}
                      documentType="proof_of_address"
                    />
                  </div>

                  {/* Selfie Verification */}
                  <div className="border border-gray-600 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-3">
                      <h4 className="text-md font-semibold text-yellow-400">🤳 Selfie Verification</h4>
                      <div className="flex gap-2">
                        {fieldApprovals[selectedSubmission.id]?.selfie_verification === 'approved' ? (
                          <>
                            <button
                              onClick={() => handleFieldReset(selectedSubmission.id, 'selfie_verification')}
                              className="px-3 py-1 rounded text-sm font-medium bg-gray-600/20 text-gray-400 hover:bg-gray-600/30 transition-colors"
                            >
                              🔄 Reset
                            </button>
                            <button
                              className="px-3 py-1 rounded text-sm font-medium bg-green-600 text-white border-2 border-green-400"
                            >
                              ✅ APPROVED
                            </button>
                            <button
                              onClick={() => handleFieldReject(selectedSubmission.id, 'selfie_verification', 'Selfie Verification')}
                              className="px-3 py-1 rounded text-sm font-medium bg-red-600/10 text-red-400/50 cursor-not-allowed"
                              disabled
                            >
                              ❌ Reject (Approved)
                            </button>
                          </>
                        ) : fieldApprovals[selectedSubmission.id]?.selfie_verification === 'rejected' ? (
                          <>
                            <button
                              onClick={() => handleFieldReset(selectedSubmission.id, 'selfie_verification')}
                              className="px-3 py-1 rounded text-sm font-medium bg-gray-600/20 text-gray-400 hover:bg-gray-600/30 transition-colors"
                            >
                              🔄 Reset
                            </button>
                            <button
                              onClick={() => handleFieldApproval(selectedSubmission.id, 'selfie_verification', 'approved')}
                              className="px-3 py-1 rounded text-sm font-medium bg-green-600/10 text-green-400/50 cursor-not-allowed"
                              disabled
                            >
                              ✅ Approve (Rejected)
                            </button>
                            <button
                              className="px-3 py-1 rounded text-sm font-medium bg-red-600 text-white border-2 border-red-400"
                            >
                              ❌ REJECTED
                            </button>
                          </>
                        ) : (
                          <>
                            <button
                              onClick={() => handleFieldApproval(selectedSubmission.id, 'selfie_verification', 'approved')}
                              className="px-3 py-1 rounded text-sm font-medium bg-green-600/20 text-green-400 hover:bg-green-600/30 transition-colors"
                            >
                              ✅ Approve
                            </button>
                            <button
                              onClick={() => handleFieldReject(selectedSubmission.id, 'selfie_verification', 'Selfie Verification')}
                              className="px-3 py-1 rounded text-sm font-medium bg-red-600/20 text-red-400 hover:bg-red-600/30 transition-colors"
                            >
                              ❌ Reject
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                    <KYCDocumentViewer
                      kycData={selectedSubmission}
                      onDocumentStatusChange={loadData}
                      currentUser={currentUser}
                      documentType="selfie"
                    />
                  </div>
                </div>
              </div>

              {/* KYC Actions */}
              <div className="glass-card p-4">
                <h3 className="text-lg font-semibold text-yellow-400 mb-4">⚡ KYC Actions</h3>

                {selectedSubmission.kyc_status === 'pending' && (
                  <div className="space-y-4">
                    <div className="flex space-x-4">
                      <button
                        onClick={() => handleApproveKYC(selectedSubmission)}
                        disabled={processing}
                        className="flex-1 px-6 py-3 bg-green-600 hover:bg-green-500 text-white font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {processing ? '⏳ Processing...' : '✅ Approve KYC'}
                      </button>
                      <button
                        onClick={() => setShowRejectModal(true)}
                        disabled={processing}
                        className="flex-1 px-6 py-3 bg-red-600 hover:bg-red-500 text-white font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        ❌ Reject KYC
                      </button>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Admin Comments (Optional)
                      </label>
                      <textarea
                        value={adminComments}
                        onChange={(e) => setAdminComments(e.target.value)}
                        placeholder="Add any comments about this KYC submission..."
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                        rows={3}
                      />
                    </div>
                  </div>
                )}

                {selectedSubmission.kyc_status !== 'pending' && (
                  <div className="text-center py-4">
                    <p className="text-gray-400">
                      This KYC submission has already been {selectedSubmission.kyc_status}.
                    </p>
                    {selectedSubmission.rejection_reason && (
                      <div className="mt-4 p-4 bg-red-500/20 border border-red-500/30 rounded-lg">
                        <h4 className="text-red-400 font-medium mb-2">Rejection Reason:</h4>
                        <p className="text-gray-300 text-sm">{selectedSubmission.rejection_reason}</p>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Approval Summary */}
              <div className="glass-card p-4">
                <h3 className="text-lg font-semibold text-yellow-400 mb-4">📊 Approval Summary</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {[
                    { key: 'personal_info', label: '👤 Personal Info', icon: '👤' },
                    { key: 'address_info', label: '🏠 Address Info', icon: '🏠' },
                    { key: 'compliance_info', label: '✅ Compliance', icon: '✅' },
                    { key: 'id_document', label: '🆔 ID Document', icon: '🆔' },
                    { key: 'proof_address', label: '🏠 Proof Address', icon: '🏠' },
                    { key: 'selfie_verification', label: '🤳 Selfie', icon: '🤳' }
                  ].map(({ key, label, icon }) => {
                    const status = fieldApprovals[selectedSubmission.id]?.[key as keyof typeof fieldApprovals[string]] || 'pending';
                    return (
                      <div key={key} className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg border border-gray-700">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{icon}</span>
                          <span className="text-sm text-gray-300">{label.split(' ').slice(1).join(' ')}</span>
                        </div>
                        <div className="flex items-center">
                          {status === 'approved' && <span className="text-green-400 text-lg">✅</span>}
                          {status === 'rejected' && <span className="text-red-400 text-lg">❌</span>}
                          {status === 'pending' && <span className="text-yellow-400 text-lg">⏳</span>}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Action Buttons */}
              {selectedSubmission.kyc_status === 'pending' && (
                <div className="glass-card p-4">
                  <h3 className="text-lg font-semibold text-yellow-400 mb-4">⚡ Actions</h3>

                  {/* Comments */}
                  <div className="mb-4">
                    <label className="block text-sm text-gray-400 mb-2">Comments (Optional)</label>
                    <textarea
                      value={comments}
                      onChange={(e) => setComments(e.target.value)}
                      placeholder="Add any comments for the user..."
                      rows={3}
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-yellow-400"
                    />
                  </div>

                  <div className="flex space-x-4">
                    <button
                      onClick={() => handleApprove(selectedSubmission)}
                      disabled={processing}
                      className="flex-1 px-4 py-2 bg-green-600 hover:bg-green-500 text-white font-medium rounded-lg transition-colors disabled:opacity-50"
                    >
                      {processing ? '⏳ Processing...' : '✅ Approve KYC'}
                    </button>
                    <button
                      onClick={() => setShowRejectModal(true)}
                      disabled={processing}
                      className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-500 text-white font-medium rounded-lg transition-colors disabled:opacity-50"
                    >
                      ❌ Reject KYC
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Reject Modal */}
      {showRejectModal && selectedSubmission && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-900 rounded-lg max-w-2xl w-full">
            {/* Modal Header */}
            <div className="border-b border-gray-700 px-6 py-4">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-bold text-red-400">❌ Reject KYC Submission</h2>
                <button
                  onClick={() => setShowRejectModal(false)}
                  className="text-gray-400 hover:text-white text-2xl"
                >
                  ×
                </button>
              </div>
            </div>

            {/* Modal Content */}
            <div className="p-6 space-y-4">
              <p className="text-gray-300">
                You are about to reject the KYC submission for{' '}
                <span className="font-semibold text-white">
                  {selectedSubmission.full_legal_name || `${selectedSubmission.first_name} ${selectedSubmission.last_name}`}
                </span>
                . Please provide a reason for rejection.
              </p>

              <div>
                <label className="block text-sm text-gray-400 mb-2">
                  Rejection Reason <span className="text-red-400">*</span>
                </label>
                <textarea
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  placeholder="Please specify why this KYC submission is being rejected..."
                  rows={4}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-400"
                  required
                />
              </div>

              <div>
                <label className="block text-sm text-gray-400 mb-2">Additional Comments (Optional)</label>
                <textarea
                  value={comments}
                  onChange={(e) => setComments(e.target.value)}
                  placeholder="Any additional comments or instructions for the user..."
                  rows={3}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-yellow-400"
                />
              </div>

              <div className="flex space-x-4 pt-4">
                <button
                  onClick={() => handleReject(selectedSubmission)}
                  disabled={processing || !rejectionReason.trim()}
                  className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-500 text-white font-medium rounded-lg transition-colors disabled:opacity-50"
                >
                  {processing ? '⏳ Processing...' : '❌ Confirm Rejection'}
                </button>
                <button
                  onClick={() => {
                    setShowRejectModal(false);
                    setRejectionReason('');
                    setComments('');
                  }}
                  disabled={processing}
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white font-medium rounded-lg transition-colors disabled:opacity-50"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* KYC Rejection Modal */}
      {showRejectModal && selectedSubmission && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-white mb-4">
              Reject KYC Submission
            </h3>
            <p className="text-gray-300 mb-4">
              User: {selectedSubmission.full_legal_name || `${selectedSubmission.first_name} ${selectedSubmission.last_name}`}
            </p>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Rejection Reason *
                </label>
                <textarea
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  placeholder="Please provide a detailed reason for rejection..."
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                  rows={4}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Admin Comments (Optional)
                </label>
                <textarea
                  value={adminComments}
                  onChange={(e) => setAdminComments(e.target.value)}
                  placeholder="Additional internal notes..."
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                  rows={2}
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowRejectModal(false);
                  setRejectionReason('');
                  setAdminComments('');
                }}
                className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleRejectKYC}
                disabled={processing || !rejectionReason.trim()}
                className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-500 text-white rounded transition-colors disabled:opacity-50"
              >
                {processing ? 'Rejecting...' : 'Reject KYC'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Quick Rejection Modal */}
      <RejectionReasonModal
        isOpen={showQuickRejectModal}
        onClose={() => {
          setShowQuickRejectModal(false);
          setQuickActionSubmission(null);
        }}
        onReject={handleQuickRejectSubmit}
        submissionName={quickActionSubmission ?
          (quickActionSubmission.full_legal_name || `${quickActionSubmission.first_name} ${quickActionSubmission.last_name}`) :
          ''
        }
        isProcessing={quickActionSubmission ? processingActions.has(quickActionSubmission.id) : false}
      />

      {/* Field Rejection Modal */}
      {showFieldRejectModal && fieldRejectData && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-900 border border-gray-700 rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-yellow-400">
                ❌ Reject {fieldRejectData.fieldDisplayName}
              </h3>
              <button
                onClick={() => setShowFieldRejectModal(false)}
                className="text-gray-400 hover:text-white"
              >
                ✕
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Rejection Reason *
                </label>
                <textarea
                  value={fieldRejectionReason}
                  onChange={(e) => setFieldRejectionReason(e.target.value)}
                  placeholder="Please provide a clear reason for rejection..."
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                  rows={4}
                  required
                />
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={confirmFieldRejection}
                  disabled={!fieldRejectionReason.trim()}
                  className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-500 disabled:bg-red-600/50 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors"
                >
                  ❌ Confirm Rejection
                </button>
                <button
                  onClick={() => setShowFieldRejectModal(false)}
                  className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white font-medium rounded-lg transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default KYCManagementDashboard;
