/**
 * EMAIL SERVICE
 * 
 * Main email service class that orchestrates email sending using
 * templates, client, and validation. Replaces the monolithic
 * resendEmailService.ts with a modular architecture.
 */

import { EmailClient } from './EmailClient';
import { VerificationEmailTemplate } from './templates/VerificationEmailTemplate';
import { WelcomeEmailTemplate } from './templates/WelcomeEmailTemplate';
import { SharePurchaseTemplate } from './templates/SharePurchaseTemplate';
import { CommissionTemplate } from './templates/CommissionTemplate';
import { MigrationTemplate } from './templates/MigrationTemplate';
import { EmailValidator } from './utils/EmailValidator';
import { EmailLogger } from './utils/EmailLogger';
import {
  EmailServiceConfig,
  EmailDeliveryResult,
  EmailVerificationData,
  WelcomeEmailData,
  SharePurchaseConfirmationData,
  CommissionEarnedData,
  ConversionNotificationData,
  WithdrawalNotificationData,
  ShareTransferNotificationData,
  MigrationConfirmationData,
  BulkEmailData,
  NewsletterData
} from './types/EmailTypes';

export class EmailService {
  private client: EmailClient;
  private validator: EmailValidator;
  private logger: EmailLogger;
  
  // Template instances
  private verificationTemplate: VerificationEmailTemplate;
  private welcomeTemplate: WelcomeEmailTemplate;
  private sharePurchaseTemplate: SharePurchaseTemplate;
  private commissionTemplate: CommissionTemplate;
  private migrationTemplate: MigrationTemplate;

  constructor(config: EmailServiceConfig) {
    this.client = new EmailClient(config);
    this.validator = new EmailValidator();
    this.logger = new EmailLogger();
    
    // Initialize templates
    this.verificationTemplate = new VerificationEmailTemplate();
    this.welcomeTemplate = new WelcomeEmailTemplate();
    this.sharePurchaseTemplate = new SharePurchaseTemplate();
    this.commissionTemplate = new CommissionTemplate();
    this.migrationTemplate = new MigrationTemplate();
  }

  /**
   * Send email verification code
   */
  public async sendVerificationEmail(data: EmailVerificationData): Promise<EmailDeliveryResult> {
    try {
      // Validate email data
      const validation = this.validator.validateVerificationData(data);
      if (!validation.isValid) {
        return {
          success: false,
          messageId: null,
          error: `Validation failed: ${validation.errors.join(', ')}`
        };
      }

      // Generate template
      const template = this.verificationTemplate.generateTemplate(data);
      
      // Send email
      const result = await this.client.sendEmail({
        to: data.email,
        subject: template.subject,
        htmlContent: template.htmlContent,
        textContent: template.textContent,
        emailType: 'verification'
      });

      // Log result
      await this.logger.logEmailSent('verification', data.email, result);

      return result;
    } catch (error: any) {
      console.error('Error sending verification email:', error);
      return {
        success: false,
        messageId: null,
        error: error.message
      };
    }
  }

  /**
   * Send welcome email to new users
   */
  public async sendWelcomeEmail(data: WelcomeEmailData): Promise<EmailDeliveryResult> {
    try {
      const validation = this.validator.validateWelcomeData(data);
      if (!validation.isValid) {
        return {
          success: false,
          messageId: null,
          error: `Validation failed: ${validation.errors.join(', ')}`
        };
      }

      const template = this.welcomeTemplate.generateTemplate(data);
      
      const result = await this.client.sendEmail({
        to: data.email,
        subject: template.subject,
        htmlContent: template.htmlContent,
        textContent: template.textContent,
        emailType: 'welcome'
      });

      await this.logger.logEmailSent('welcome', data.email, result);
      return result;
    } catch (error: any) {
      console.error('Error sending welcome email:', error);
      return {
        success: false,
        messageId: null,
        error: error.message
      };
    }
  }

  /**
   * Send share purchase confirmation
   */
  public async sendSharePurchaseConfirmation(data: SharePurchaseConfirmationData): Promise<EmailDeliveryResult> {
    try {
      const validation = this.validator.validateSharePurchaseData(data);
      if (!validation.isValid) {
        return {
          success: false,
          messageId: null,
          error: `Validation failed: ${validation.errors.join(', ')}`
        };
      }

      const template = this.sharePurchaseTemplate.generateTemplate(data);
      
      const result = await this.client.sendEmail({
        to: data.email,
        subject: template.subject,
        htmlContent: template.htmlContent,
        textContent: template.textContent,
        emailType: 'share_purchase'
      });

      await this.logger.logEmailSent('share_purchase', data.email, result);
      return result;
    } catch (error: any) {
      console.error('Error sending share purchase confirmation:', error);
      return {
        success: false,
        messageId: null,
        error: error.message
      };
    }
  }

  /**
   * Send commission earned notification
   */
  public async sendCommissionEarnedNotification(data: CommissionEarnedData): Promise<EmailDeliveryResult> {
    try {
      const validation = this.validator.validateCommissionData(data);
      if (!validation.isValid) {
        return {
          success: false,
          messageId: null,
          error: `Validation failed: ${validation.errors.join(', ')}`
        };
      }

      const template = this.commissionTemplate.generateCommissionEarnedTemplate(data);
      
      const result = await this.client.sendEmail({
        to: data.email,
        subject: template.subject,
        htmlContent: template.htmlContent,
        textContent: template.textContent,
        emailType: 'commission_earned'
      });

      await this.logger.logEmailSent('commission_earned', data.email, result);
      return result;
    } catch (error: any) {
      console.error('Error sending commission notification:', error);
      return {
        success: false,
        messageId: null,
        error: error.message
      };
    }
  }

  /**
   * Send migration confirmation email
   */
  public async sendMigrationConfirmation(data: MigrationConfirmationData): Promise<EmailDeliveryResult> {
    try {
      const validation = this.validator.validateMigrationData(data);
      if (!validation.isValid) {
        return {
          success: false,
          messageId: null,
          error: `Validation failed: ${validation.errors.join(', ')}`
        };
      }

      const template = this.migrationTemplate.generateTemplate(data);
      
      const result = await this.client.sendEmail({
        to: data.email,
        subject: template.subject,
        htmlContent: template.htmlContent,
        textContent: template.textContent,
        emailType: 'migration_confirmation'
      });

      await this.logger.logEmailSent('migration_confirmation', data.email, result);
      return result;
    } catch (error: any) {
      console.error('Error sending migration confirmation:', error);
      return {
        success: false,
        messageId: null,
        error: error.message
      };
    }
  }

  /**
   * Send bulk emails
   */
  public async sendBulkEmails(data: BulkEmailData): Promise<EmailDeliveryResult[]> {
    try {
      // Validate recipients
      const validRecipients = data.recipients.filter(email => 
        this.client.validateEmailAddress(email)
      );

      if (validRecipients.length === 0) {
        return [{
          success: false,
          messageId: null,
          error: 'No valid recipients found'
        }];
      }

      // Create email requests
      const requests = validRecipients.map(email => ({
        to: email,
        subject: data.subject,
        htmlContent: data.htmlContent,
        textContent: data.textContent || this.stripHtml(data.htmlContent),
        emailType: 'bulk' as const
      }));

      // Send in batches
      const results = await this.client.sendBulkEmails(requests);

      // Log bulk send
      await this.logger.logBulkEmailSent(validRecipients.length, results);

      return results;
    } catch (error: any) {
      console.error('Error sending bulk emails:', error);
      return [{
        success: false,
        messageId: null,
        error: error.message
      }];
    }
  }

  /**
   * Get service status
   */
  public getServiceStatus(): {
    isConfigured: boolean;
    rateLimitStatus: Record<string, number>;
    error?: string;
  } {
    const clientStatus = this.client.getConfigurationStatus();
    return {
      isConfigured: clientStatus.isConfigured,
      rateLimitStatus: this.client.getRateLimitStatus(),
      error: clientStatus.error
    };
  }

  /**
   * Utility method to strip HTML tags
   */
  private stripHtml(html: string): string {
    return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
  }
}
