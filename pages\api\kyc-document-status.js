/**
 * KYC Document Status API
 * Checks the status of uploaded documents for a specific KYC ID
 */

import { createClient } from '@supabase/supabase-js';

// Force the correct Supabase configuration since environment variables aren't loading properly in API files
const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { kycId } = req.query;

    if (!kycId) {
      return res.status(400).json({ error: 'KYC ID is required' });
    }

    console.log('📄 Checking document status for KYC ID:', kycId);
    
    // Map field names to document types
    const documentTypeMap = {
      'id_document': 'id_document',
      'selfie_verification': 'selfie',
      'proof_address': 'proof_of_address'
    };

    const statuses = {};

    // Check each document type
    for (const [fieldName, documentType] of Object.entries(documentTypeMap)) {
      try {
        const { data, error } = await supabase
          .from('kyc_documents')
          .select('file_name, uploaded_at, verification_status')
          .eq('kyc_id', kycId)
          .eq('document_type', documentType)
          .order('uploaded_at', { ascending: false })
          .limit(1);

        if (error) {
          console.log(`ℹ️ No document found for ${fieldName}:`, error.message);
          statuses[fieldName] = { exists: false };
        } else if (data && data.length > 0) {
          const doc = data[0];
          statuses[fieldName] = {
            exists: true,
            fileName: doc.file_name,
            uploadedAt: doc.uploaded_at,
            verificationStatus: doc.verification_status
          };
          console.log(`✅ Document found for ${fieldName}:`, doc.file_name);
        } else {
          statuses[fieldName] = { exists: false };
        }
      } catch (err) {
        console.log(`ℹ️ Error checking ${fieldName}:`, err);
        statuses[fieldName] = { exists: false };
      }
    }

    console.log('📄 Document statuses loaded:', statuses);

    return res.status(200).json({
      success: true,
      documentStatuses: statuses
    });

  } catch (error) {
    console.error('❌ Error checking document status:', error);
    return res.status(500).json({ 
      error: 'Failed to check document status',
      details: error.message 
    });
  }
}
