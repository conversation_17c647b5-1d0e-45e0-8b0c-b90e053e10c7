import React, { useState, useEffect, useRef } from 'react';

export interface TourStep {
  id: string;
  target: string; // CSS selector for the element to highlight
  title: string;
  content: string;
  position: 'top' | 'bottom' | 'left' | 'right' | 'center';
  action?: {
    type: 'click' | 'input' | 'wait';
    text: string;
    delay?: number;
  };
  skippable?: boolean;
}

export interface TourConfig {
  id: string;
  name: string;
  description: string;
  steps: TourStep[];
  autoStart?: boolean;
  showProgress?: boolean;
  allowSkip?: boolean;
}

interface GuidedTourProps {
  tour: TourConfig;
  isActive: boolean;
  onComplete: () => void;
  onSkip: () => void;
  onStepChange?: (stepIndex: number) => void;
}

export const GuidedTour: React.FC<GuidedTourProps> = ({
  tour,
  isActive,
  onComplete,
  onSkip,
  onStepChange
}) => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const [targetElement, setTargetElement] = useState<HTMLElement | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });
  const overlayRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  const currentStep = tour.steps[currentStepIndex];
  const isLastStep = currentStepIndex === tour.steps.length - 1;

  // Initialize tour
  useEffect(() => {
    if (isActive && tour.steps.length > 0) {
      setCurrentStepIndex(0);
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  }, [isActive, tour.steps.length]);

  // Update target element and position when step changes
  useEffect(() => {
    if (!isVisible || !currentStep) return;

    const element = document.querySelector(currentStep.target) as HTMLElement;
    if (element) {
      setTargetElement(element);
      updateTooltipPosition(element);
      
      // Scroll element into view
      element.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'center',
        inline: 'center'
      });

      // Add highlight class
      element.classList.add('tour-highlight');
      
      // Cleanup previous highlights
      document.querySelectorAll('.tour-highlight').forEach(el => {
        if (el !== element) {
          el.classList.remove('tour-highlight');
        }
      });
    }

    onStepChange?.(currentStepIndex);

    return () => {
      // Cleanup highlights when component unmounts
      document.querySelectorAll('.tour-highlight').forEach(el => {
        el.classList.remove('tour-highlight');
      });
    };
  }, [currentStepIndex, isVisible, currentStep, onStepChange]);

  // Update tooltip position
  const updateTooltipPosition = (element: HTMLElement) => {
    if (!tooltipRef.current) return;

    const elementRect = element.getBoundingClientRect();
    const tooltipRect = tooltipRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let top = 0;
    let left = 0;

    switch (currentStep.position) {
      case 'top':
        top = elementRect.top - tooltipRect.height - 10;
        left = elementRect.left + (elementRect.width - tooltipRect.width) / 2;
        break;
      case 'bottom':
        top = elementRect.bottom + 10;
        left = elementRect.left + (elementRect.width - tooltipRect.width) / 2;
        break;
      case 'left':
        top = elementRect.top + (elementRect.height - tooltipRect.height) / 2;
        left = elementRect.left - tooltipRect.width - 10;
        break;
      case 'right':
        top = elementRect.top + (elementRect.height - tooltipRect.height) / 2;
        left = elementRect.right + 10;
        break;
      case 'center':
        top = (viewportHeight - tooltipRect.height) / 2;
        left = (viewportWidth - tooltipRect.width) / 2;
        break;
    }

    // Ensure tooltip stays within viewport
    top = Math.max(10, Math.min(top, viewportHeight - tooltipRect.height - 10));
    left = Math.max(10, Math.min(left, viewportWidth - tooltipRect.width - 10));

    setTooltipPosition({ top, left });
  };

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (targetElement) {
        updateTooltipPosition(targetElement);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [targetElement, currentStep]);

  const nextStep = () => {
    if (isLastStep) {
      completeTour();
    } else {
      setCurrentStepIndex(prev => prev + 1);
    }
  };

  const prevStep = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(prev => prev - 1);
    }
  };

  const skipTour = () => {
    setIsVisible(false);
    onSkip();
  };

  const completeTour = () => {
    setIsVisible(false);
    onComplete();
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    // Allow clicking on the highlighted element
    if (targetElement && targetElement.contains(e.target as Node)) {
      return;
    }
    
    // Prevent other interactions
    e.preventDefault();
    e.stopPropagation();
  };

  if (!isVisible || !currentStep) return null;

  return (
    <>
      {/* Overlay */}
      <div
        ref={overlayRef}
        className="fixed inset-0 bg-black bg-opacity-75 z-50"
        onClick={handleOverlayClick}
        style={{ pointerEvents: 'auto' }}
      >
        {/* Spotlight effect for target element */}
        {targetElement && (
          <div
            className="absolute border-4 border-gold rounded-lg shadow-lg"
            style={{
              top: targetElement.getBoundingClientRect().top - 4,
              left: targetElement.getBoundingClientRect().left - 4,
              width: targetElement.getBoundingClientRect().width + 8,
              height: targetElement.getBoundingClientRect().height + 8,
              boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.75)',
              pointerEvents: 'none'
            }}
          />
        )}

        {/* Tooltip */}
        <div
          ref={tooltipRef}
          className="absolute bg-gray-800 border border-gray-700 rounded-lg shadow-xl max-w-sm z-60"
          style={{
            top: tooltipPosition.top,
            left: tooltipPosition.left,
            pointerEvents: 'auto'
          }}
        >
          {/* Arrow */}
          <div
            className={`absolute w-3 h-3 bg-gray-800 border-gray-700 transform rotate-45 ${
              currentStep.position === 'top' ? 'bottom-[-6px] border-b border-r' :
              currentStep.position === 'bottom' ? 'top-[-6px] border-t border-l' :
              currentStep.position === 'left' ? 'right-[-6px] border-r border-b' :
              currentStep.position === 'right' ? 'left-[-6px] border-l border-t' :
              'hidden'
            }`}
            style={{
              left: currentStep.position === 'top' || currentStep.position === 'bottom' ? '50%' : undefined,
              top: currentStep.position === 'left' || currentStep.position === 'right' ? '50%' : undefined,
              transform: `translate(-50%, -50%) rotate(45deg)`
            }}
          />

          <div className="p-6">
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">{currentStep.title}</h3>
              {tour.allowSkip && (
                <button
                  onClick={skipTour}
                  className="text-gray-400 hover:text-white transition-colors"
                  title="Skip tour"
                >
                  ✕
                </button>
              )}
            </div>

            {/* Content */}
            <div className="mb-6">
              <p className="text-gray-300 text-sm leading-relaxed">
                {currentStep.content}
              </p>
              
              {currentStep.action && (
                <div className="mt-4 p-3 bg-blue-900/30 border border-blue-500/30 rounded-lg">
                  <p className="text-blue-300 text-sm">
                    <span className="font-medium">Action:</span> {currentStep.action.text}
                  </p>
                </div>
              )}
            </div>

            {/* Progress */}
            {tour.showProgress && (
              <div className="mb-4">
                <div className="flex justify-between text-xs text-gray-400 mb-2">
                  <span>Step {currentStepIndex + 1} of {tour.steps.length}</span>
                  <span>{Math.round(((currentStepIndex + 1) / tour.steps.length) * 100)}%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-1">
                  <div 
                    className="bg-gold h-1 rounded-full transition-all duration-300"
                    style={{ width: `${((currentStepIndex + 1) / tour.steps.length) * 100}%` }}
                  />
                </div>
              </div>
            )}

            {/* Navigation */}
            <div className="flex justify-between items-center">
              <button
                onClick={prevStep}
                disabled={currentStepIndex === 0}
                className="px-4 py-2 text-sm font-medium text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Previous
              </button>

              <div className="flex gap-2">
                {currentStep.skippable && (
                  <button
                    onClick={nextStep}
                    className="px-4 py-2 text-sm font-medium text-gray-400 hover:text-white transition-colors"
                  >
                    Skip
                  </button>
                )}
                
                <button
                  onClick={nextStep}
                  className="px-4 py-2 bg-gold text-black text-sm font-medium rounded-lg hover:bg-yellow-500 transition-colors"
                >
                  {isLastStep ? 'Finish' : 'Next'}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* CSS for highlighting */}
      <style jsx global>{`
        .tour-highlight {
          position: relative;
          z-index: 51 !important;
          box-shadow: 0 0 0 4px rgba(255, 215, 0, 0.5) !important;
          border-radius: 8px !important;
        }
      `}</style>
    </>
  );
};

// Predefined tours for common features
export const PREDEFINED_TOURS: { [key: string]: TourConfig } = {
  dashboard_overview: {
    id: 'dashboard_overview',
    name: 'Dashboard Overview',
    description: 'Learn about your main dashboard features',
    showProgress: true,
    allowSkip: true,
    steps: [
      {
        id: 'welcome',
        target: '.dashboard-header',
        title: 'Welcome to Your Dashboard',
        content: 'This is your main dashboard where you can view all your account information, portfolio, and activities.',
        position: 'bottom'
      },
      {
        id: 'portfolio',
        target: '.portfolio-section',
        title: 'Your Portfolio',
        content: 'Here you can see your current share holdings, their value, and performance over time.',
        position: 'top'
      },
      {
        id: 'notifications',
        target: '.notification-center',
        title: 'Notifications',
        content: 'Stay updated with important account notifications, payment confirmations, and system announcements.',
        position: 'left'
      }
    ]
  },
  
  share_purchase: {
    id: 'share_purchase',
    name: 'Share Purchase Guide',
    description: 'Learn how to purchase your first shares',
    showProgress: true,
    allowSkip: false,
    steps: [
      {
        id: 'calculator',
        target: '.share-calculator',
        title: 'Share Calculator',
        content: 'Use this calculator to determine how many shares you want to purchase and see the total cost.',
        position: 'top',
        action: {
          type: 'input',
          text: 'Try entering an amount in the calculator'
        }
      },
      {
        id: 'payment_method',
        target: '.payment-methods',
        title: 'Choose Payment Method',
        content: 'Select your preferred payment method. Available options depend on your country.',
        position: 'bottom'
      },
      {
        id: 'submit',
        target: '.purchase-button',
        title: 'Complete Purchase',
        content: 'Click here to proceed with your share purchase. You\'ll receive a confirmation once payment is processed.',
        position: 'top'
      }
    ]
  },

  affiliate_dashboard: {
    id: 'affiliate_dashboard',
    name: 'Affiliate Dashboard Tour',
    description: 'Discover your affiliate marketing tools',
    showProgress: true,
    allowSkip: true,
    steps: [
      {
        id: 'referral_link',
        target: '.referral-link-section',
        title: 'Your Referral Link',
        content: 'This is your unique referral link. Share it with friends and family to earn commissions.',
        position: 'bottom'
      },
      {
        id: 'commission_tracker',
        target: '.commission-tracker',
        title: 'Commission Tracking',
        content: 'Monitor your earnings from referrals and see detailed breakdowns of your commissions.',
        position: 'top'
      },
      {
        id: 'marketing_tools',
        target: '.marketing-tools',
        title: 'Marketing Tools',
        content: 'Access promotional materials, social media content, and other resources to help you succeed.',
        position: 'left'
      }
    ]
  }
};
