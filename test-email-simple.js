/**
 * Simple test to verify email configuration and send a test email
 */

import 'dotenv/config';

const testEmailConfiguration = async () => {
  try {
    console.log('📧 TESTING EMAIL CONFIGURATION');
    console.log('='.repeat(40));

    // Check environment variables
    const resendApiKey = process.env.RESEND_API_KEY;
    const fromEmail = process.env.RESEND_FROM_EMAIL;
    const fromName = process.env.RESEND_FROM_NAME;

    console.log('\n🔧 Environment Variables:');
    console.log(`   • RESEND_API_KEY: ${resendApiKey ? '✅ Set' : '❌ Missing'}`);
    console.log(`   • FROM_EMAIL: ${fromEmail || '❌ Missing'}`);
    console.log(`   • FROM_NAME: ${fromName || '❌ Missing'}`);

    if (!resendApiKey || !fromEmail || !fromName) {
      console.log('\n❌ Email configuration incomplete!');
      return;
    }

    // Test with Resend API directly
    console.log('\n📧 Testing direct Resend API call...');
    
    const emailData = {
      from: `${fromName} <${fromEmail}>`,
      to: ['jp.rade<PERSON><EMAIL>'],
      subject: 'Test Commission Email - Aureus Africa',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #D4AF37;">💰 Commission Earned!</h2>
          <p>Hello Test Sponsor,</p>
          <p>Great news! You've earned a commission from a successful referral.</p>
          
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Commission Details:</h3>
            <ul>
              <li><strong>USDT Commission:</strong> $11.25</li>
              <li><strong>Share Commission:</strong> 2.25 shares</li>
              <li><strong>Referred User:</strong> referred_1758570817272</li>
              <li><strong>Purchase Amount:</strong> $75.00</li>
              <li><strong>Transaction ID:</strong> test-1df0b6bd-ab95-48cc-8c2a-a2b3cd45b184</li>
            </ul>
          </div>
          
          <p>This commission has been added to your account balance immediately.</p>
          
          <p>Best regards,<br>
          Aureus Alliance Holdings Team</p>
        </div>
      `
    };

    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${resendApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(emailData),
    });

    const result = await response.json();

    if (response.ok) {
      console.log('✅ Test email sent successfully!');
      console.log(`   • Message ID: ${result.id}`);
      console.log(`   • Sent to: <EMAIL>`);
      console.log('\n📬 Check your email inbox for the test commission notification!');
      
      console.log('\n🎯 EMAIL SYSTEM STATUS:');
      console.log('✅ Resend API: WORKING');
      console.log('✅ Email configuration: CORRECT');
      console.log('✅ Email delivery: SUCCESS');
      
      console.log('\n🔧 NEXT STEPS:');
      console.log('1. Check your email inbox');
      console.log('2. Force refresh the browser (Ctrl+F5)');
      console.log('3. Try approving another conversion');
      console.log('4. The email should now work in the admin dashboard');
      
    } else {
      console.log('❌ Test email failed:');
      console.log(`   • Status: ${response.status}`);
      console.log(`   • Error: ${result.message || 'Unknown error'}`);
      console.log(`   • Details:`, result);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
};

testEmailConfiguration();
