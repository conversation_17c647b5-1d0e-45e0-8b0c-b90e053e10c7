import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'https://fgubaqoftdeefcakejwu.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA'
);

async function testDatabase() {
  console.log('🔍 Testing database connection...');
  
  try {
    // Test users table
    console.log('📊 Testing users table...');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, username, email, is_active')
      .limit(5);
    
    if (usersError) {
      console.error('❌ Users query failed:', usersError);
    } else {
      console.log('✅ Users query successful. Found', users?.length || 0, 'users');
      if (users && users.length > 0) {
        console.log('📋 Sample users:');
        users.forEach(user => {
          console.log(`  - ${user.username} (${user.email}) - Active: ${user.is_active}`);
        });
      }
    }
    
    // Test admin_users table
    console.log('\n📊 Testing admin_users table...');
    const { data: admins, error: adminsError } = await supabase
      .from('admin_users')
      .select('*')
      .limit(5);
    
    if (adminsError) {
      console.error('❌ Admin users query failed:', adminsError);
    } else {
      console.log('✅ Admin users query successful. Found', admins?.length || 0, 'admins');
      if (admins && admins.length > 0) {
        console.log('📋 Admin users:');
        admins.forEach(admin => {
          console.log(`  - ${admin.email} (${admin.role}) - Active: ${admin.is_active}`);
        });
      }
    }
    
  } catch (error) {
    console.error('❌ Database test failed:', error);
  }
}

testDatabase();
