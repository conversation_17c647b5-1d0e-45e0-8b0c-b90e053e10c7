/**
 * EMAIL LOGGER
 * 
 * Logging utilities for email operations including
 * delivery tracking, error logging, and analytics.
 */

import { supabase } from '../../supabase';
import { EmailDeliveryResult, EmailType } from '../types/EmailTypes';

export class EmailLogger {
  
  /**
   * Log successful or failed email send
   */
  public async logEmailSent(
    emailType: EmailType,
    recipient: string,
    result: EmailDeliveryResult,
    additionalData?: Record<string, any>
  ): Promise<void> {
    try {
      const logEntry = {
        email_type: emailType,
        recipient: recipient,
        success: result.success,
        message_id: result.messageId,
        error_message: result.error || null,
        sent_at: new Date().toISOString(),
        additional_data: additionalData || null
      };

      // Log to database if available
      if (supabase) {
        await supabase
          .from('email_logs')
          .insert([logEntry]);
      }

      // Console logging
      if (result.success) {
        console.log(`✅ Email sent successfully: ${emailType} to ${recipient} (${result.messageId})`);
      } else {
        console.error(`❌ Email failed: ${emailType} to ${recipient} - ${result.error}`);
      }

    } catch (error) {
      console.error('Failed to log email send:', error);
    }
  }

  /**
   * Log bulk email operation
   */
  public async logBulkEmailSent(
    recipientCount: number,
    results: EmailDeliveryResult[]
  ): Promise<void> {
    try {
      const successCount = results.filter(r => r.success).length;
      const failureCount = results.filter(r => !r.success).length;

      const logEntry = {
        email_type: 'bulk',
        recipient: `bulk_${recipientCount}_recipients`,
        success: successCount > 0,
        message_id: `bulk_${Date.now()}`,
        error_message: failureCount > 0 ? `${failureCount} failures` : null,
        sent_at: new Date().toISOString(),
        additional_data: {
          total_recipients: recipientCount,
          successful_sends: successCount,
          failed_sends: failureCount,
          success_rate: ((successCount / recipientCount) * 100).toFixed(2) + '%'
        }
      };

      if (supabase) {
        await supabase
          .from('email_logs')
          .insert([logEntry]);
      }

      console.log(`📧 Bulk email completed: ${successCount}/${recipientCount} successful`);

    } catch (error) {
      console.error('Failed to log bulk email send:', error);
    }
  }

  /**
   * Log email validation errors
   */
  public async logValidationError(
    emailType: EmailType,
    recipient: string,
    errors: string[],
    warnings: string[]
  ): Promise<void> {
    try {
      const logEntry = {
        email_type: emailType,
        recipient: recipient,
        success: false,
        message_id: null,
        error_message: `Validation failed: ${errors.join(', ')}`,
        sent_at: new Date().toISOString(),
        additional_data: {
          validation_errors: errors,
          validation_warnings: warnings
        }
      };

      if (supabase) {
        await supabase
          .from('email_logs')
          .insert([logEntry]);
      }

      console.warn(`⚠️ Email validation failed: ${emailType} to ${recipient} - ${errors.join(', ')}`);

    } catch (error) {
      console.error('Failed to log validation error:', error);
    }
  }

  /**
   * Get email statistics for a date range
   */
  public async getEmailStatistics(
    startDate: string,
    endDate: string,
    emailType?: EmailType
  ): Promise<{
    totalSent: number;
    successful: number;
    failed: number;
    successRate: number;
    byType: Record<string, { sent: number; successful: number; failed: number }>;
  }> {
    try {
      if (!supabase) {
        return {
          totalSent: 0,
          successful: 0,
          failed: 0,
          successRate: 0,
          byType: {}
        };
      }

      let query = supabase
        .from('email_logs')
        .select('email_type, success')
        .gte('sent_at', startDate)
        .lte('sent_at', endDate);

      if (emailType) {
        query = query.eq('email_type', emailType);
      }

      const { data: logs, error } = await query;

      if (error) {
        console.error('Error fetching email statistics:', error);
        return {
          totalSent: 0,
          successful: 0,
          failed: 0,
          successRate: 0,
          byType: {}
        };
      }

      const totalSent = logs?.length || 0;
      const successful = logs?.filter(log => log.success).length || 0;
      const failed = totalSent - successful;
      const successRate = totalSent > 0 ? (successful / totalSent) * 100 : 0;

      // Group by email type
      const byType: Record<string, { sent: number; successful: number; failed: number }> = {};
      
      logs?.forEach(log => {
        if (!byType[log.email_type]) {
          byType[log.email_type] = { sent: 0, successful: 0, failed: 0 };
        }
        byType[log.email_type].sent++;
        if (log.success) {
          byType[log.email_type].successful++;
        } else {
          byType[log.email_type].failed++;
        }
      });

      return {
        totalSent,
        successful,
        failed,
        successRate: Math.round(successRate * 100) / 100,
        byType
      };

    } catch (error) {
      console.error('Error calculating email statistics:', error);
      return {
        totalSent: 0,
        successful: 0,
        failed: 0,
        successRate: 0,
        byType: {}
      };
    }
  }

  /**
   * Get recent email failures for debugging
   */
  public async getRecentFailures(limit: number = 50): Promise<Array<{
    emailType: string;
    recipient: string;
    errorMessage: string;
    sentAt: string;
    additionalData?: any;
  }>> {
    try {
      if (!supabase) {
        return [];
      }

      const { data: failures, error } = await supabase
        .from('email_logs')
        .select('email_type, recipient, error_message, sent_at, additional_data')
        .eq('success', false)
        .order('sent_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching recent failures:', error);
        return [];
      }

      return failures?.map(failure => ({
        emailType: failure.email_type,
        recipient: failure.recipient,
        errorMessage: failure.error_message || 'Unknown error',
        sentAt: failure.sent_at,
        additionalData: failure.additional_data
      })) || [];

    } catch (error) {
      console.error('Error fetching recent failures:', error);
      return [];
    }
  }

  /**
   * Clean up old email logs
   */
  public async cleanupOldLogs(daysToKeep: number = 90): Promise<number> {
    try {
      if (!supabase) {
        return 0;
      }

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const { data, error } = await supabase
        .from('email_logs')
        .delete()
        .lt('sent_at', cutoffDate.toISOString());

      if (error) {
        console.error('Error cleaning up old logs:', error);
        return 0;
      }

      const deletedCount = Array.isArray(data) ? data.length : 0;
      console.log(`🧹 Cleaned up ${deletedCount} old email logs (older than ${daysToKeep} days)`);
      
      return deletedCount;

    } catch (error) {
      console.error('Error during log cleanup:', error);
      return 0;
    }
  }

  /**
   * Log rate limit hit
   */
  public async logRateLimitHit(recipient: string, emailType: EmailType): Promise<void> {
    try {
      const logEntry = {
        email_type: emailType,
        recipient: recipient,
        success: false,
        message_id: null,
        error_message: 'Rate limit exceeded',
        sent_at: new Date().toISOString(),
        additional_data: {
          reason: 'rate_limit_exceeded'
        }
      };

      if (supabase) {
        await supabase
          .from('email_logs')
          .insert([logEntry]);
      }

      console.warn(`⚠️ Rate limit hit for ${emailType} email to ${recipient}`);

    } catch (error) {
      console.error('Failed to log rate limit hit:', error);
    }
  }
}
