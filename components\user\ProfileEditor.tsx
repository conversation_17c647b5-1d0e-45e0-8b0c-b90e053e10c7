import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';

interface ProfileData {
  full_name: string;
  country: string;
  whatsapp_number: string;
  telegram_username: string;
  linkedin_url: string;
  twitter_url: string;
  facebook_url: string;
  portfolio_description: string;
  tags: string[];
  profile_image_url: string;
}

interface ProfileEditorProps {
  user: any;
  onClose: () => void;
  onSave: () => void;
}

const ProfileEditor: React.FC<ProfileEditorProps> = ({ user, onClose, onSave }) => {
  const [profile, setProfile] = useState<ProfileData>({
    full_name: '',
    country: '',
    whatsapp_number: '',
    telegram_username: '',
    linkedin_url: '',
    twitter_url: '',
    facebook_url: '',
    portfolio_description: '',
    tags: [],
    profile_image_url: ''
  });
  const [loading, setLoading] = useState(false);
  const [tagInput, setTagInput] = useState('');

  useEffect(() => {
    loadProfile();
  }, [user]);

  const loadProfile = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) throw error;

      if (data) {
        setProfile({
          full_name: data.full_name || '',
          country: data.country || '',
          whatsapp_number: data.whatsapp_number || '',
          telegram_username: data.telegram_username || '',
          linkedin_url: data.linkedin_url || '',
          twitter_url: data.twitter_url || '',
          facebook_url: data.facebook_url || '',
          portfolio_description: data.portfolio_description || '',
          tags: data.tags || [],
          profile_image_url: data.profile_image_url || ''
        });
      }
    } catch (error) {
      console.error('Error loading profile:', error);
    }
  };

  const handleSave = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const { error } = await supabase
        .from('users')
        .update({
          full_name: profile.full_name,
          country: profile.country,
          whatsapp_number: profile.whatsapp_number,
          telegram_username: profile.telegram_username,
          linkedin_url: profile.linkedin_url,
          twitter_url: profile.twitter_url,
          facebook_url: profile.facebook_url,
          portfolio_description: profile.portfolio_description,
          tags: profile.tags,
          profile_image_url: profile.profile_image_url,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id);

      if (error) throw error;

      onSave();
      onClose();
    } catch (error) {
      console.error('Error saving profile:', error);
      alert('Error saving profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const addTag = () => {
    if (tagInput.trim() && !profile.tags.includes(tagInput.trim())) {
      setProfile(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setProfile(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  return (
    <div className="profile-editor-overlay">
      <div className="profile-editor">
        <div className="editor-header">
          <h2>Edit Your Profile</h2>
          <button onClick={onClose} className="close-btn">×</button>
        </div>

        <div className="editor-content">
          <div className="form-group">
            <label>Full Name</label>
            <input
              type="text"
              value={profile.full_name}
              onChange={(e) => setProfile(prev => ({ ...prev, full_name: e.target.value }))}
              placeholder="Your full name"
            />
          </div>

          <div className="form-group">
            <label>Country</label>
            <input
              type="text"
              value={profile.country}
              onChange={(e) => setProfile(prev => ({ ...prev, country: e.target.value }))}
              placeholder="Your country"
            />
          </div>

          <div className="form-group">
            <label>WhatsApp Number</label>
            <input
              type="text"
              value={profile.whatsapp_number}
              onChange={(e) => setProfile(prev => ({ ...prev, whatsapp_number: e.target.value }))}
              placeholder="+27 12 345 6789"
            />
          </div>

          <div className="form-group">
            <label>Telegram Username</label>
            <input
              type="text"
              value={profile.telegram_username}
              onChange={(e) => setProfile(prev => ({ ...prev, telegram_username: e.target.value }))}
              placeholder="username (without @)"
            />
          </div>

          <div className="form-group">
            <label>LinkedIn URL</label>
            <input
              type="url"
              value={profile.linkedin_url}
              onChange={(e) => setProfile(prev => ({ ...prev, linkedin_url: e.target.value }))}
              placeholder="https://linkedin.com/in/yourprofile"
            />
          </div>

          <div className="form-group">
            <label>Twitter URL</label>
            <input
              type="url"
              value={profile.twitter_url}
              onChange={(e) => setProfile(prev => ({ ...prev, twitter_url: e.target.value }))}
              placeholder="https://twitter.com/yourusername"
            />
          </div>

          <div className="form-group">
            <label>Facebook URL</label>
            <input
              type="url"
              value={profile.facebook_url}
              onChange={(e) => setProfile(prev => ({ ...prev, facebook_url: e.target.value }))}
              placeholder="https://facebook.com/yourprofile"
            />
          </div>

          <div className="form-group">
            <label>Profile Image URL</label>
            <input
              type="url"
              value={profile.profile_image_url}
              onChange={(e) => setProfile(prev => ({ ...prev, profile_image_url: e.target.value }))}
              placeholder="https://example.com/your-photo.jpg"
            />
          </div>

          <div className="form-group">
            <label>Portfolio Description</label>
            <textarea
              value={profile.portfolio_description}
              onChange={(e) => setProfile(prev => ({ ...prev, portfolio_description: e.target.value }))}
              placeholder="Tell people about yourself and your experience..."
              rows={4}
            />
          </div>

          <div className="form-group">
            <label>Tags</label>
            <div className="tag-input-container">
              <input
                type="text"
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                placeholder="Add a tag and press Enter"
              />
              <button type="button" onClick={addTag} className="add-tag-btn">Add</button>
            </div>
            <div className="tags-display">
              {profile.tags.map((tag, index) => (
                <span key={index} className="tag">
                  {tag}
                  <button onClick={() => removeTag(tag)} className="remove-tag">×</button>
                </span>
              ))}
            </div>
          </div>
        </div>

        <div className="editor-footer">
          <button onClick={onClose} className="cancel-btn">Cancel</button>
          <button onClick={handleSave} disabled={loading} className="save-btn">
            {loading ? 'Saving...' : 'Save Profile'}
          </button>
        </div>
      </div>

      <style jsx>{`
        .profile-editor-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
          padding: 20px;
        }

        .profile-editor {
          background: #1a1f2e;
          border-radius: 16px;
          width: 100%;
          max-width: 600px;
          max-height: 90vh;
          overflow-y: auto;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .editor-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 24px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .editor-header h2 {
          color: #ffc107;
          margin: 0;
          font-size: 1.5rem;
          font-weight: 700;
        }

        .close-btn {
          background: none;
          border: none;
          color: rgba(255, 255, 255, 0.7);
          font-size: 24px;
          cursor: pointer;
          padding: 4px;
          border-radius: 4px;
        }

        .close-btn:hover {
          background: rgba(255, 255, 255, 0.1);
          color: white;
        }

        .editor-content {
          padding: 24px;
        }

        .form-group {
          margin-bottom: 20px;
        }

        .form-group label {
          display: block;
          color: rgba(255, 255, 255, 0.9);
          margin-bottom: 8px;
          font-weight: 500;
        }

        .form-group input,
        .form-group textarea {
          width: 100%;
          padding: 12px;
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 8px;
          color: white;
          font-size: 14px;
        }

        .form-group input:focus,
        .form-group textarea:focus {
          outline: none;
          border-color: #ffc107;
          box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.2);
        }

        .tag-input-container {
          display: flex;
          gap: 8px;
        }

        .add-tag-btn {
          padding: 12px 16px;
          background: #ffc107;
          border: none;
          border-radius: 8px;
          color: #000;
          font-weight: 600;
          cursor: pointer;
          white-space: nowrap;
        }

        .tags-display {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          margin-top: 12px;
        }

        .tag {
          display: flex;
          align-items: center;
          gap: 6px;
          padding: 6px 12px;
          background: rgba(59, 130, 246, 0.2);
          border: 1px solid rgba(59, 130, 246, 0.4);
          border-radius: 16px;
          font-size: 12px;
          color: #60a5fa;
        }

        .remove-tag {
          background: none;
          border: none;
          color: #60a5fa;
          cursor: pointer;
          font-size: 14px;
          padding: 0;
          margin-left: 4px;
        }

        .editor-footer {
          display: flex;
          justify-content: flex-end;
          gap: 12px;
          padding: 24px;
          border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .cancel-btn,
        .save-btn {
          padding: 12px 24px;
          border: none;
          border-radius: 8px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .cancel-btn {
          background: rgba(255, 255, 255, 0.1);
          color: rgba(255, 255, 255, 0.8);
        }

        .cancel-btn:hover {
          background: rgba(255, 255, 255, 0.2);
        }

        .save-btn {
          background: #ffc107;
          color: #000;
        }

        .save-btn:hover:not(:disabled) {
          background: #ffcd39;
          transform: translateY(-1px);
        }

        .save-btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      `}</style>
    </div>
  );
};

export default ProfileEditor;
