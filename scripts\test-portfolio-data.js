// Test script to check portfolio data availability
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testPortfolioData() {
  console.log('🔍 Testing Portfolio Data Availability\n');

  try {
    // 1. Check if aureus_share_purchases table exists and has data
    console.log('1. Checking aureus_share_purchases table...');
    const { data: sharePurchases, error: shareError } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .limit(5);

    if (shareError) {
      console.error('❌ Error accessing aureus_share_purchases:', shareError.message);
    } else {
      console.log(`✅ Found ${sharePurchases?.length || 0} share purchase records`);
      if (sharePurchases && sharePurchases.length > 0) {
        console.log('📊 Sample record:', {
          id: sharePurchases[0].id,
          user_id: sharePurchases[0].user_id,
          shares_purchased: sharePurchases[0].shares_purchased,
          total_amount: sharePurchases[0].total_amount,
          status: sharePurchases[0].status,
          created_at: sharePurchases[0].created_at
        });
      }
    }

    // 2. Check users table
    console.log('\n2. Checking users table...');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, username, full_name, first_name, last_name')
      .limit(5);

    if (usersError) {
      console.error('❌ Error accessing users:', usersError.message);
    } else {
      console.log(`✅ Found ${users?.length || 0} user records`);
      if (users && users.length > 0) {
        console.log('👤 Sample user:', {
          id: users[0].id,
          username: users[0].username,
          full_name: users[0].full_name,
          first_name: users[0].first_name,
          last_name: users[0].last_name
        });
      }
    }

    // 3. Check for users with share purchases
    console.log('\n3. Checking users with share purchases...');
    const { data: usersWithShares, error: joinError } = await supabase
      .from('aureus_share_purchases')
      .select(`
        user_id,
        shares_purchased,
        total_amount,
        status,
        users!inner(id, username, full_name)
      `)
      .eq('status', 'active')
      .limit(5);

    if (joinError) {
      console.error('❌ Error joining tables:', joinError.message);
    } else {
      console.log(`✅ Found ${usersWithShares?.length || 0} active share purchases`);
      if (usersWithShares && usersWithShares.length > 0) {
        usersWithShares.forEach((record, index) => {
          console.log(`📈 Purchase ${index + 1}:`, {
            user_id: record.user_id,
            username: record.users?.username,
            shares: record.shares_purchased,
            amount: record.total_amount,
            status: record.status
          });
        });
      }
    }

    // 4. Check investment_phases table
    console.log('\n4. Checking investment_phases table...');
    const { data: phases, error: phasesError } = await supabase
      .from('investment_phases')
      .select('*')
      .order('phase_number');

    if (phasesError) {
      console.error('❌ Error accessing investment_phases:', phasesError.message);
    } else {
      console.log(`✅ Found ${phases?.length || 0} investment phases`);
      const activePhase = phases?.find(p => p.is_active);
      if (activePhase) {
        console.log('🎯 Active phase:', {
          phase_number: activePhase.phase_number,
          phase_name: activePhase.phase_name,
          price_per_share: activePhase.price_per_share,
          shares_sold: activePhase.shares_sold,
          total_shares_available: activePhase.total_shares_available
        });
      } else {
        console.log('⚠️ No active phase found');
      }
    }

    // 5. Check KYC tables
    console.log('\n5. Checking KYC tables...');
    
    // Try kyc_information first
    const { data: kycInfo, error: kycInfoError } = await supabase
      .from('kyc_information')
      .select('user_id, kyc_status')
      .limit(3);

    if (kycInfoError) {
      console.log('⚠️ kyc_information table not accessible:', kycInfoError.message);
    } else {
      console.log(`✅ Found ${kycInfo?.length || 0} KYC information records`);
    }

    // Try kyc_documents
    const { data: kycDocs, error: kycDocsError } = await supabase
      .from('kyc_documents')
      .select('user_id, status')
      .limit(3);

    if (kycDocsError) {
      console.log('⚠️ kyc_documents table not accessible:', kycDocsError.message);
    } else {
      console.log(`✅ Found ${kycDocs?.length || 0} KYC document records`);
    }

    // 6. Summary
    console.log('\n📋 SUMMARY:');
    console.log('='.repeat(50));
    
    const hasSharePurchases = sharePurchases && sharePurchases.length > 0;
    const hasUsers = users && users.length > 0;
    const hasActiveShares = usersWithShares && usersWithShares.length > 0;
    
    console.log(`Share Purchases Available: ${hasSharePurchases ? '✅ YES' : '❌ NO'}`);
    console.log(`Users Available: ${hasUsers ? '✅ YES' : '❌ NO'}`);
    console.log(`Active Share Holdings: ${hasActiveShares ? '✅ YES' : '❌ NO'}`);
    console.log(`Investment Phases: ${phases && phases.length > 0 ? '✅ YES' : '❌ NO'}`);
    
    if (hasActiveShares) {
      console.log('\n🎯 PORTFOLIO COMPONENT SHOULD WORK');
      console.log('The portfolio component should be able to load data for users with active share purchases.');
    } else {
      console.log('\n⚠️ NO PORTFOLIO DATA AVAILABLE');
      console.log('The portfolio component will show empty state until users make share purchases.');
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the test
testPortfolioData();
