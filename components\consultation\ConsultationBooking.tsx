import React, { useState, useEffect } from 'react'
import { 
  createConsultationBooking, 
  getUserConsultations,
  getCurrentDbUserId,
  getUserType,
  type ConsultationBooking 
} from '../../lib/supportSystem'

interface ConsultationBookingProps {
  userId?: number
  userType?: 'shareholder' | 'affiliate'
}

const ConsultationBookingComponent: React.FC<ConsultationBookingProps> = ({ 
  userId: propUserId, 
  userType: propUserType 
}) => {
  const [consultations, setConsultations] = useState<ConsultationBooking[]>([])
  const [loading, setLoading] = useState(true)
  const [showBookingForm, setShowBookingForm] = useState(false)
  const [userType, setUserType] = useState<'shareholder' | 'affiliate'>(propUserType || 'shareholder')
  const [userId, setUserId] = useState<number | null>(propUserId || null)
  
  const [newBooking, setNewBooking] = useState({
    consultation_type: '' as 'investment_guidance' | 'business_opportunity' | 'technical_support' | '',
    title: '',
    description: '',
    scheduled_at: '',
    duration_minutes: 30,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
  })

  useEffect(() => {
    initializeConsultations()
  }, [propUserId, propUserType])

  const initializeConsultations = async () => {
    setLoading(true)
    try {
      let currentUserId = propUserId
      let currentUserType = propUserType

      if (!currentUserId) {
        currentUserId = await getCurrentDbUserId()
      }
      
      if (!currentUserType && currentUserId) {
        currentUserType = await getUserType(currentUserId)
      }

      if (!currentUserId || !currentUserType) {
        console.error('User not logged in or type not determined')
        return
      }

      setUserId(currentUserId)
      setUserType(currentUserType)

      await loadConsultations(currentUserId)
    } catch (error) {
      console.error('Error initializing consultations:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadConsultations = async (currentUserId: number) => {
    try {
      const userConsultations = await getUserConsultations(currentUserId)
      setConsultations(userConsultations)
    } catch (error) {
      console.error('Error loading consultations:', error)
    }
  }

  const handleCreateBooking = async () => {
    if (!userId || !newBooking.consultation_type || !newBooking.title.trim() || !newBooking.scheduled_at) return

    setLoading(true)
    try {
      const booking = await createConsultationBooking(
        userId,
        newBooking.consultation_type,
        newBooking.title,
        newBooking.description,
        newBooking.scheduled_at,
        newBooking.duration_minutes,
        newBooking.timezone
      )

      if (booking) {
        setConsultations(prev => [booking, ...prev])
        setNewBooking({
          consultation_type: '',
          title: '',
          description: '',
          scheduled_at: '',
          duration_minutes: 30,
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
        })
        setShowBookingForm(false)
      }
    } catch (error) {
      console.error('Error creating consultation booking:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 text-blue-800'
      case 'confirmed': return 'bg-green-100 text-green-800'
      case 'in_progress': return 'bg-yellow-100 text-yellow-800'
      case 'completed': return 'bg-gray-100 text-gray-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      case 'no_show': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getConsultationTypeOptions = () => {
    if (userType === 'shareholder') {
      return [
        { value: 'investment_guidance', label: 'Investment Guidance', description: 'Get expert advice on your investment portfolio and strategy' },
        { value: 'technical_support', label: 'Technical Support', description: 'Resolve technical issues with your account or platform' }
      ]
    } else {
      return [
        { value: 'business_opportunity', label: 'Business Opportunity Discussion', description: 'Explore affiliate opportunities and business development' },
        { value: 'technical_support', label: 'Technical Support', description: 'Resolve technical issues with your account or platform' }
      ]
    }
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    })
  }

  const getMinDateTime = () => {
    const now = new Date()
    now.setHours(now.getHours() + 1) // Minimum 1 hour from now
    return now.toISOString().slice(0, 16)
  }

  if (loading && consultations.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Private Consultations</h2>
          <p className="text-gray-600">
            Book one-on-one sessions with our experts for personalized guidance
          </p>
        </div>
        <button
          onClick={() => setShowBookingForm(true)}
          className="bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
        >
          Book Consultation
        </button>
      </div>

      {/* Consultation Types Info */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        {getConsultationTypeOptions().map((option) => (
          <div key={option.value} className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="font-semibold text-gray-900 mb-2">{option.label}</h3>
            <p className="text-gray-600 text-sm">{option.description}</p>
            <div className="mt-2 text-sm text-gray-500">
              Duration: 30-60 minutes • Available via Zoom
            </div>
          </div>
        ))}
      </div>

      {/* Booking Form Modal */}
      {showBookingForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Book Private Consultation</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Consultation Type *
                </label>
                <select
                  value={newBooking.consultation_type}
                  onChange={(e) => setNewBooking(prev => ({ ...prev, consultation_type: e.target.value as any }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 text-gray-900 bg-white"
                >
                  <option value="">Select consultation type</option>
                  {getConsultationTypeOptions().map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Subject *
                </label>
                <input
                  type="text"
                  value={newBooking.title}
                  onChange={(e) => setNewBooking(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Brief description of what you'd like to discuss"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 text-gray-900 bg-white placeholder-gray-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Preferred Date & Time *
                </label>
                <input
                  type="datetime-local"
                  value={newBooking.scheduled_at}
                  onChange={(e) => setNewBooking(prev => ({ ...prev, scheduled_at: e.target.value }))}
                  min={getMinDateTime()}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 text-gray-900 bg-white"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Times are in your local timezone ({newBooking.timezone})
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Duration
                </label>
                <select
                  value={newBooking.duration_minutes}
                  onChange={(e) => setNewBooking(prev => ({ ...prev, duration_minutes: parseInt(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 text-gray-900 bg-white"
                >
                  <option value={30}>30 minutes</option>
                  <option value={45}>45 minutes</option>
                  <option value={60}>60 minutes</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Additional Details
                </label>
                <textarea
                  value={newBooking.description}
                  onChange={(e) => setNewBooking(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Please provide any additional context or specific topics you'd like to cover"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 text-gray-900 bg-white placeholder-gray-500"
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setShowBookingForm(false)}
                className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateBooking}
                disabled={!newBooking.consultation_type || !newBooking.title.trim() || !newBooking.scheduled_at || loading}
                className="flex-1 bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-lg transition-colors disabled:opacity-50"
              >
                {loading ? 'Booking...' : 'Book Consultation'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Consultations List */}
      {consultations.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">📅</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Consultations Booked</h3>
          <p className="text-gray-600 mb-4">You haven't booked any private consultations yet.</p>
          <button
            onClick={() => setShowBookingForm(true)}
            className="bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
          >
            Book Your First Consultation
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          {consultations.map((consultation) => (
            <div
              key={consultation.id}
              className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    {consultation.title}
                  </h3>
                  <p className="text-gray-600 text-sm capitalize">
                    {consultation.consultation_type.replace('_', ' ')}
                  </p>
                </div>
                <span className={`px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(consultation.status)}`}>
                  {consultation.status.replace('_', ' ')}
                </span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Scheduled Time</label>
                  <p className="text-sm text-gray-900">{formatDateTime(consultation.scheduled_at)}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Duration</label>
                  <p className="text-sm text-gray-900">{consultation.duration_minutes} minutes</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Assigned Expert</label>
                  <p className="text-sm text-gray-900">
                    {consultation.agent?.agent_name || 'To be assigned'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Booking Date</label>
                  <p className="text-sm text-gray-900">
                    {new Date(consultation.created_at).toLocaleDateString()}
                  </p>
                </div>
              </div>

              {consultation.description && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Details</label>
                  <p className="text-sm text-gray-900 bg-gray-50 rounded p-3">
                    {consultation.description}
                  </p>
                </div>
              )}

              {consultation.meeting_url && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Meeting Link</label>
                  <a
                    href={consultation.meeting_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-blue-600 hover:text-blue-800 text-sm"
                  >
                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                    Join Meeting
                  </a>
                  {consultation.meeting_password && (
                    <p className="text-xs text-gray-500 mt-1">
                      Meeting Password: {consultation.meeting_password}
                    </p>
                  )}
                </div>
              )}

              {consultation.follow_up_notes && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Follow-up Notes</label>
                  <p className="text-sm text-gray-900 bg-blue-50 rounded p-3">
                    {consultation.follow_up_notes}
                  </p>
                </div>
              )}

              {consultation.status === 'completed' && consultation.rating && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Your Rating</label>
                  <div className="flex items-center space-x-1">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <svg
                        key={star}
                        className={`w-4 h-4 ${star <= consultation.rating! ? 'text-yellow-400' : 'text-gray-300'}`}
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    ))}
                    <span className="ml-2 text-sm text-gray-600">({consultation.rating}/5)</span>
                  </div>
                  {consultation.feedback && (
                    <p className="text-sm text-gray-600 mt-2">{consultation.feedback}</p>
                  )}
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-2">
                {consultation.status === 'scheduled' && (
                  <button className="bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-1 px-3 rounded transition-colors">
                    Cancel
                  </button>
                )}
                {consultation.status === 'confirmed' && consultation.meeting_url && (
                  <a
                    href={consultation.meeting_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-1 px-3 rounded transition-colors"
                  >
                    Join Meeting
                  </a>
                )}
                {consultation.status === 'completed' && !consultation.rating && (
                  <button className="bg-yellow-600 hover:bg-yellow-700 text-white text-sm font-medium py-1 px-3 rounded transition-colors">
                    Rate Session
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default ConsultationBookingComponent
