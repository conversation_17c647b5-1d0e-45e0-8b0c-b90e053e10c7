import { createClient } from '@supabase/supabase-js';
import { Resend } from 'resend';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client with service role
const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Initialize Resend client
const RESEND_API_KEY = process.env.RESEND_API_KEY || process.env.VITE_RESEND_API_KEY;
const RESEND_FROM_EMAIL = process.env.RESEND_FROM_EMAIL || process.env.VITE_RESEND_FROM_EMAIL || '<EMAIL>';
const RESEND_FROM_NAME = process.env.RESEND_FROM_NAME || process.env.VITE_RESEND_FROM_NAME || 'Aureus Alliance Holdings';

let resend = null;
if (RESEND_API_KEY) {
  resend = new Resend(RESEND_API_KEY);
  console.log('✅ Resend email service initialized for password reset');
} else {
  console.warn('⚠️ RESEND_API_KEY not found - email service disabled');
}

/**
 * Generate password reset email content
 */
function generatePasswordResetEmailContent(pin, userName, expiryMinutes = 15) {
  const greeting = userName ? `Hello ${userName}` : 'Hello';
  
  const subject = 'Password Reset PIN - Aureus Alliance Holdings';
  
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #1a1a1a; color: #ffffff;">
      <div style="text-align: center; margin-bottom: 30px;">
        <img src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-m.png" alt="Aureus Alliance Holdings" style="height: 60px;">
      </div>
      
      <h2 style="color: #ffc107; text-align: center; margin-bottom: 30px;">Password Reset Request</h2>
      
      <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">${greeting},</p>
      
      <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
        We received a request to reset your password for your Aureus Alliance Holdings account.
      </p>
      
      <div style="background-color: #2d2d2d; padding: 20px; border-radius: 8px; text-align: center; margin: 30px 0;">
        <p style="font-size: 14px; color: #cccccc; margin-bottom: 10px;">Your 6-digit reset PIN is:</p>
        <div style="font-size: 32px; font-weight: bold; color: #ffc107; letter-spacing: 8px; font-family: monospace;">
          ${pin}
        </div>
        <p style="font-size: 12px; color: #999999; margin-top: 15px;">
          This PIN will expire in ${expiryMinutes} minutes
        </p>
      </div>
      
      <p style="font-size: 14px; line-height: 1.6; margin-bottom: 20px;">
        Enter this PIN on the password reset page to continue with resetting your password.
      </p>
      
      <div style="background-color: #2d2d2d; padding: 15px; border-radius: 8px; margin: 20px 0;">
        <p style="font-size: 12px; color: #cccccc; margin: 0;">
          <strong>Security Note:</strong> If you didn't request this password reset, please ignore this email. 
          Your account remains secure and no changes have been made.
        </p>
      </div>
      
      <p style="font-size: 14px; color: #999999; text-align: center; margin-top: 30px;">
        Best regards,<br>
        The Aureus Alliance Holdings Team
      </p>
    </div>
  `;
  
  const text = `
${greeting},

We received a request to reset your password for your Aureus Alliance Holdings account.

Your 6-digit reset PIN is: ${pin}

This PIN will expire in ${expiryMinutes} minutes.

Enter this PIN on the password reset page to continue with resetting your password.

If you didn't request this password reset, please ignore this email. Your account remains secure and no changes have been made.

Best regards,
The Aureus Alliance Holdings Team
  `;
  
  return { subject, html, text };
}

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ error: 'Email is required' });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ error: 'Invalid email format' });
    }

    console.log('🔐 Processing password reset request for:', email);

    // Check if user exists in database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, email, full_name, username')
      .eq('email', email.toLowerCase().trim())
      .single();

    if (userError || !user) {
      // Don't reveal if user exists or not for security
      return res.status(200).json({ 
        success: true, 
        message: 'If an account with this email exists, a reset PIN has been sent.' 
      });
    }

    // Generate 6-digit PIN
    const resetPin = Math.floor(100000 + Math.random() * 900000).toString();
    const expiresAt = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes from now

    console.log('🔢 Generated reset PIN:', resetPin, 'expires at:', expiresAt);

    // Store reset PIN in database
    const { error: pinError } = await supabase
      .from('password_reset_pins')
      .insert({
        user_id: user.id,
        email: email.toLowerCase().trim(),
        pin: resetPin,
        expires_at: expiresAt.toISOString(),
        used: false,
        created_at: new Date().toISOString()
      });

    if (pinError) {
      console.error('❌ Error storing reset PIN:', pinError);
      return res.status(500).json({ error: 'Failed to generate reset PIN' });
    }

    // Send email with PIN using Resend
    try {
      if (!resend) {
        console.error('❌ Resend service not configured');
        return res.status(500).json({ error: 'Email service not configured' });
      }

      console.log('📧 Sending reset PIN email to:', email);

      const userName = user.full_name || user.username || 'User';
      const emailContent = generatePasswordResetEmailContent(resetPin, userName, 15);

      const emailResult = await resend.emails.send({
        from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
        to: [email],
        subject: emailContent.subject,
        html: emailContent.html,
        text: emailContent.text,
        tags: [
          { name: 'category', value: 'password_reset' },
          { name: 'purpose', value: 'password_reset' }
        ]
      });

      if (emailResult.error) {
        console.error('❌ Resend API error:', emailResult.error);
        return res.status(500).json({ error: 'Failed to send reset PIN email' });
      }

      console.log('✅ Reset PIN email sent successfully:', emailResult.data?.id);

      return res.status(200).json({
        success: true,
        message: 'Reset PIN sent to your email address. Please check your inbox.'
      });

    } catch (emailError) {
      console.error('❌ Error sending email:', emailError);
      return res.status(500).json({ error: 'Failed to send reset PIN email' });
    }

  } catch (error) {
    console.error('❌ Password reset error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
