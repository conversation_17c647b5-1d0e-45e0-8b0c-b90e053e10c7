import React, { useState } from 'react';

interface ContactFormProps {
  className?: string;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

interface FormData {
  name: string;
  surname: string;
  email: string;
  message: string;
}

interface ValidationErrors {
  name?: string;
  surname?: string;
  email?: string;
  message?: string;
}

const ContactForm: React.FC<ContactFormProps> = ({ 
  className = '', 
  onSuccess, 
  onError 
}) => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    surname: '',
    email: '',
    message: ''
  });

  const [errors, setErrors] = useState<ValidationErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [submitMessage, setSubmitMessage] = useState('');

  // Client-side validation
  const validateForm = (): boolean => {
    const newErrors: ValidationErrors = {};

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    } else if (formData.name.trim().length > 50) {
      newErrors.name = 'Name must be less than 50 characters';
    }

    // Surname validation
    if (!formData.surname.trim()) {
      newErrors.surname = 'Surname is required';
    } else if (formData.surname.trim().length < 2) {
      newErrors.surname = 'Surname must be at least 2 characters';
    } else if (formData.surname.trim().length > 50) {
      newErrors.surname = 'Surname must be less than 50 characters';
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!emailRegex.test(formData.email.trim())) {
      newErrors.email = 'Please enter a valid email address';
    } else if (formData.email.trim().length > 100) {
      newErrors.email = 'Email address is too long';
    }

    // Message validation
    if (!formData.message.trim()) {
      newErrors.message = 'Message is required';
    } else if (formData.message.trim().length < 10) {
      newErrors.message = 'Message must be at least 10 characters';
    } else if (formData.message.trim().length > 2000) {
      newErrors.message = 'Message must be less than 2000 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle input changes
  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
    
    // Clear submit status when user makes changes
    if (submitStatus !== 'idle') {
      setSubmitStatus('idle');
      setSubmitMessage('');
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus('idle');
    setSubmitMessage('');

    try {
      console.log('📧 Submitting contact form...');
      
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        console.log('✅ Contact form submitted successfully');
        
        setSubmitStatus('success');
        setSubmitMessage(result.message || 'Your message has been sent successfully!');
        
        // Reset form
        setFormData({
          name: '',
          surname: '',
          email: '',
          message: ''
        });
        
        // Call success callback
        if (onSuccess) {
          onSuccess();
        }
      } else {
        console.error('❌ Contact form submission failed:', result);
        
        setSubmitStatus('error');
        
        if (result.details && typeof result.details === 'object') {
          // Handle validation errors from server
          setErrors(result.details);
          setSubmitMessage('Please correct the errors below and try again.');
        } else {
          setSubmitMessage(result.error || 'Failed to send message. Please try again.');
        }
        
        // Call error callback
        if (onError) {
          onError(result.error || 'Failed to send message');
        }
      }
    } catch (error) {
      console.error('❌ Contact form network error:', error);
      
      setSubmitStatus('error');
      setSubmitMessage('Network error. Please check your connection and try again.');
      
      if (onError) {
        onError('Network error');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={`contact-form-container ${className}`}>
      <form className="contact-form" onSubmit={handleSubmit}>
        <div className="form-row">
          <div className="form-field">
            <input
              type="text"
              name="name"
              placeholder="Name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className={errors.name ? 'error' : ''}
              disabled={isSubmitting}
              required
            />
            {errors.name && <span className="error-message">{errors.name}</span>}
          </div>
          
          <div className="form-field">
            <input
              type="text"
              name="surname"
              placeholder="Surname"
              value={formData.surname}
              onChange={(e) => handleInputChange('surname', e.target.value)}
              className={errors.surname ? 'error' : ''}
              disabled={isSubmitting}
              required
            />
            {errors.surname && <span className="error-message">{errors.surname}</span>}
          </div>
        </div>
        
        <div className="form-field">
          <input
            type="email"
            name="email"
            placeholder="Email Address"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            className={errors.email ? 'error' : ''}
            disabled={isSubmitting}
            required
          />
          {errors.email && <span className="error-message">{errors.email}</span>}
        </div>
        
        <div className="form-field">
          <textarea
            name="message"
            placeholder="Message"
            rows={3}
            value={formData.message}
            onChange={(e) => handleInputChange('message', e.target.value)}
            className={errors.message ? 'error' : ''}
            disabled={isSubmitting}
            required
          />
          {errors.message && <span className="error-message">{errors.message}</span>}
          <div className="character-count">
            {formData.message.length}/2000
          </div>
        </div>
        
        <button 
          type="submit" 
          className={`btn btn-primary ${isSubmitting ? 'loading' : ''}`}
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <span className="loading-spinner"></span>
              Sending...
            </>
          ) : (
            'Submit'
          )}
        </button>
        
        {/* Status Messages */}
        {submitStatus === 'success' && (
          <div className="status-message success">
            <div className="status-icon">✅</div>
            <div className="status-text">{submitMessage}</div>
          </div>
        )}
        
        {submitStatus === 'error' && (
          <div className="status-message error">
            <div className="status-icon">❌</div>
            <div className="status-text">{submitMessage}</div>
          </div>
        )}
      </form>

      <style jsx>{`
        .contact-form-container {
          width: 100%;
        }

        .contact-form {
          display: flex;
          flex-direction: column;
          gap: var(--space-xs, 12px);
        }

        .form-row {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: var(--space-xs, 12px);
        }

        .form-field {
          position: relative;
          display: flex;
          flex-direction: column;
          gap: 4px;
        }

        .contact-form input,
        .contact-form textarea {
          background: var(--bg, #1a1a1a);
          border: 1px solid var(--border, #333);
          border-radius: var(--radius-sm, 6px);
          padding: var(--space-xs, 12px);
          font-size: var(--font-size-xs, 14px);
          color: var(--text, #fff);
          transition: all 0.2s ease;
          width: 100%;
          box-sizing: border-box;
        }

        .contact-form input:focus,
        .contact-form textarea:focus {
          outline: none;
          border-color: var(--gold, #FFD700);
          box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.1);
        }

        .contact-form input.error,
        .contact-form textarea.error {
          border-color: #f44336;
          box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.1);
        }

        .contact-form input:disabled,
        .contact-form textarea:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .contact-form textarea {
          resize: vertical;
          min-height: 60px;
          font-family: inherit;
        }

        .error-message {
          color: #f44336;
          font-size: 12px;
          margin-top: 2px;
        }

        .character-count {
          font-size: 11px;
          color: var(--text-muted, #666);
          text-align: right;
          margin-top: 2px;
        }

        .btn {
          align-self: flex-start;
          min-width: 100px;
          padding: var(--space-xs, 12px) var(--space-sm, 16px);
          font-size: var(--font-size-xs, 14px);
          margin-top: var(--space-xs, 12px);
          background: var(--gold, #FFD700);
          color: #000;
          border: none;
          border-radius: var(--radius-sm, 6px);
          cursor: pointer;
          font-weight: 600;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .btn:hover:not(:disabled) {
          background: #e6c200;
          transform: translateY(-1px);
        }

        .btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }

        .loading-spinner {
          width: 14px;
          height: 14px;
          border: 2px solid transparent;
          border-top: 2px solid currentColor;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          to { transform: rotate(360deg); }
        }

        .status-message {
          display: flex;
          align-items: flex-start;
          gap: 8px;
          padding: 12px;
          border-radius: 6px;
          margin-top: 12px;
          font-size: 14px;
        }

        .status-message.success {
          background: rgba(76, 175, 80, 0.1);
          border: 1px solid rgba(76, 175, 80, 0.3);
          color: #4CAF50;
        }

        .status-message.error {
          background: rgba(244, 67, 54, 0.1);
          border: 1px solid rgba(244, 67, 54, 0.3);
          color: #f44336;
        }

        .status-icon {
          flex-shrink: 0;
          font-size: 16px;
        }

        .status-text {
          flex: 1;
          line-height: 1.4;
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
          .form-row {
            grid-template-columns: 1fr;
          }
          
          .btn {
            width: 100%;
            justify-content: center;
          }
        }
      `}</style>
    </div>
  );
};

export default ContactForm;
