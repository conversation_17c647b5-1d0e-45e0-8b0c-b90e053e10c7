/**
 * RICH TEXT EDITOR
 * 
 * Professional rich text editor for course content creation:
 * - WYSIWYG editing with toolbar
 * - Text formatting (bold, italic, underline, etc.)
 * - Lists, links, and media embedding
 * - Code blocks and syntax highlighting
 * - Image upload and embedding
 * - HTML output for storage
 */

import React, { useState, useRef, useEffect } from 'react';
import { supabase } from '../../lib/supabase';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  maxLength?: number;
  className?: string;
}

interface ToolbarButton {
  command: string;
  icon: string;
  title: string;
  requiresValue?: boolean;
}

export const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  placeholder = 'Start typing...',
  maxLength = 10000,
  className = ''
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [wordCount, setWordCount] = useState(0);

  const toolbarButtons: ToolbarButton[] = [
    { command: 'bold', icon: '𝐁', title: 'Bold' },
    { command: 'italic', icon: '𝐼', title: 'Italic' },
    { command: 'underline', icon: '𝐔', title: 'Underline' },
    { command: 'strikeThrough', icon: '𝐒', title: 'Strikethrough' },
    { command: 'separator', icon: '|', title: '' },
    { command: 'formatBlock', icon: 'H1', title: 'Heading 1' },
    { command: 'formatBlock', icon: 'H2', title: 'Heading 2' },
    { command: 'formatBlock', icon: 'H3', title: 'Heading 3' },
    { command: 'separator', icon: '|', title: '' },
    { command: 'insertUnorderedList', icon: '•', title: 'Bullet List' },
    { command: 'insertOrderedList', icon: '1.', title: 'Numbered List' },
    { command: 'separator', icon: '|', title: '' },
    { command: 'createLink', icon: '🔗', title: 'Insert Link', requiresValue: true },
    { command: 'insertImage', icon: '🖼️', title: 'Insert Image' },
    { command: 'separator', icon: '|', title: '' },
    { command: 'justifyLeft', icon: '⬅️', title: 'Align Left' },
    { command: 'justifyCenter', icon: '↔️', title: 'Align Center' },
    { command: 'justifyRight', icon: '➡️', title: 'Align Right' },
    { command: 'separator', icon: '|', title: '' },
    { command: 'insertHorizontalRule', icon: '—', title: 'Horizontal Line' },
    { command: 'removeFormat', icon: '🧹', title: 'Clear Formatting' }
  ];

  useEffect(() => {
    if (editorRef.current && value !== editorRef.current.innerHTML) {
      editorRef.current.innerHTML = value;
    }
    updateWordCount(value);
  }, [value]);

  const updateWordCount = (content: string) => {
    const text = content.replace(/<[^>]*>/g, '').trim();
    const words = text ? text.split(/\s+/).length : 0;
    setWordCount(words);
  };

  const handleCommand = (command: string, value?: string) => {
    if (command === 'separator') return;

    if (command === 'insertImage') {
      fileInputRef.current?.click();
      return;
    }

    if (command === 'formatBlock') {
      const tag = value || 'h1';
      document.execCommand(command, false, `<${tag}>`);
    } else if (command === 'createLink') {
      const url = prompt('Enter the URL:');
      if (url) {
        document.execCommand(command, false, url);
      }
    } else {
      document.execCommand(command, false, value);
    }

    editorRef.current?.focus();
    handleContentChange();
  };

  const handleImageUpload = async (file: File) => {
    try {
      setIsUploading(true);

      // Validate file
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file.');
        return;
      }

      if (file.size > 5 * 1024 * 1024) {
        alert('Image size must be less than 5MB.');
        return;
      }

      // Create unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `content-image-${Date.now()}.${fileExt}`;

      // Upload to Supabase storage
      const { data, error } = await supabase.storage
        .from('training-assets')
        .upload(`content-images/${fileName}`, file);

      if (error) throw error;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('training-assets')
        .getPublicUrl(`content-images/${fileName}`);

      // Insert image into editor
      const img = `<img src="${publicUrl}" alt="Content image" style="max-width: 100%; height: auto; border-radius: 8px; margin: 10px 0;" />`;
      document.execCommand('insertHTML', false, img);
      
      handleContentChange();

    } catch (error) {
      console.error('Error uploading image:', error);
      alert('Failed to upload image. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleContentChange = () => {
    if (editorRef.current) {
      const content = editorRef.current.innerHTML;
      
      // Check length limit
      const textContent = content.replace(/<[^>]*>/g, '');
      if (maxLength && textContent.length > maxLength) {
        alert(`Content exceeds maximum length of ${maxLength} characters.`);
        return;
      }

      onChange(content);
      updateWordCount(content);
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const text = e.clipboardData.getData('text/plain');
    document.execCommand('insertText', false, text);
    handleContentChange();
  };

  const insertCodeBlock = () => {
    const code = prompt('Enter your code:');
    if (code) {
      const codeBlock = `<pre style="background-color: #1f2937; color: #f3f4f6; padding: 16px; border-radius: 8px; overflow-x: auto; font-family: 'Courier New', monospace; margin: 10px 0;"><code>${code.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code></pre>`;
      document.execCommand('insertHTML', false, codeBlock);
      handleContentChange();
    }
  };

  const insertCallout = (type: 'info' | 'warning' | 'success' | 'error') => {
    const text = prompt('Enter callout text:');
    if (text) {
      const colors = {
        info: { bg: '#dbeafe', border: '#3b82f6', icon: 'ℹ️' },
        warning: { bg: '#fef3c7', border: '#f59e0b', icon: '⚠️' },
        success: { bg: '#d1fae5', border: '#10b981', icon: '✅' },
        error: { bg: '#fee2e2', border: '#ef4444', icon: '❌' }
      };
      
      const color = colors[type];
      const callout = `<div style="background-color: ${color.bg}; border-left: 4px solid ${color.border}; padding: 16px; margin: 16px 0; border-radius: 8px;"><strong>${color.icon} ${type.toUpperCase()}:</strong> ${text}</div>`;
      document.execCommand('insertHTML', false, callout);
      handleContentChange();
    }
  };

  return (
    <div className={`border border-gray-600 rounded-lg overflow-hidden ${className}`}>
      {/* Toolbar */}
      <div className="bg-gray-700 border-b border-gray-600 p-2 flex flex-wrap items-center gap-1">
        {toolbarButtons.map((button, index) => (
          button.command === 'separator' ? (
            <div key={index} className="w-px h-6 bg-gray-500 mx-1" />
          ) : (
            <button
              key={index}
              type="button"
              onClick={() => {
                if (button.command === 'formatBlock') {
                  if (button.icon === 'H1') handleCommand(button.command, 'h1');
                  else if (button.icon === 'H2') handleCommand(button.command, 'h2');
                  else if (button.icon === 'H3') handleCommand(button.command, 'h3');
                } else {
                  handleCommand(button.command);
                }
              }}
              className="px-2 py-1 text-sm bg-gray-600 hover:bg-gray-500 text-white rounded transition-colors"
              title={button.title}
              disabled={isUploading}
            >
              {button.icon}
            </button>
          )
        ))}
        
        {/* Additional Tools */}
        <div className="w-px h-6 bg-gray-500 mx-1" />
        <button
          type="button"
          onClick={insertCodeBlock}
          className="px-2 py-1 text-sm bg-gray-600 hover:bg-gray-500 text-white rounded transition-colors"
          title="Insert Code Block"
        >
          &lt;/&gt;
        </button>
        
        <div className="relative">
          <button
            type="button"
            className="px-2 py-1 text-sm bg-gray-600 hover:bg-gray-500 text-white rounded transition-colors"
            title="Insert Callout"
          >
            💬
          </button>
          <div className="absolute top-full left-0 mt-1 bg-gray-700 border border-gray-600 rounded shadow-lg hidden group-hover:block z-10">
            <button onClick={() => insertCallout('info')} className="block w-full px-3 py-1 text-left text-white hover:bg-gray-600">ℹ️ Info</button>
            <button onClick={() => insertCallout('warning')} className="block w-full px-3 py-1 text-left text-white hover:bg-gray-600">⚠️ Warning</button>
            <button onClick={() => insertCallout('success')} className="block w-full px-3 py-1 text-left text-white hover:bg-gray-600">✅ Success</button>
            <button onClick={() => insertCallout('error')} className="block w-full px-3 py-1 text-left text-white hover:bg-gray-600">❌ Error</button>
          </div>
        </div>
      </div>

      {/* Editor */}
      <div
        ref={editorRef}
        contentEditable
        onInput={handleContentChange}
        onPaste={handlePaste}
        className="min-h-[300px] p-4 bg-gray-800 text-white focus:outline-none"
        style={{
          lineHeight: '1.6',
          fontSize: '14px'
        }}
        data-placeholder={placeholder}
        suppressContentEditableWarning={true}
      />

      {/* Hidden file input for image uploads */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file) {
            handleImageUpload(file);
          }
        }}
        className="hidden"
      />

      {/* Footer */}
      <div className="bg-gray-700 border-t border-gray-600 px-4 py-2 flex justify-between items-center text-sm text-gray-300">
        <div className="flex items-center space-x-4">
          <span>{wordCount} words</span>
          {maxLength && (
            <span className={value.replace(/<[^>]*>/g, '').length > maxLength * 0.9 ? 'text-yellow-400' : ''}>
              {value.replace(/<[^>]*>/g, '').length}/{maxLength} characters
            </span>
          )}
        </div>
        
        {isUploading && (
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            <span>Uploading image...</span>
          </div>
        )}
      </div>

      {/* CSS for placeholder */}
      <style jsx>{`
        [contenteditable]:empty:before {
          content: attr(data-placeholder);
          color: #9ca3af;
          font-style: italic;
        }
        
        [contenteditable] h1 {
          font-size: 1.5em;
          font-weight: bold;
          margin: 0.5em 0;
          color: #d4af37;
        }
        
        [contenteditable] h2 {
          font-size: 1.3em;
          font-weight: bold;
          margin: 0.5em 0;
          color: #d4af37;
        }
        
        [contenteditable] h3 {
          font-size: 1.1em;
          font-weight: bold;
          margin: 0.5em 0;
          color: #d4af37;
        }
        
        [contenteditable] ul, [contenteditable] ol {
          margin: 0.5em 0;
          padding-left: 2em;
        }
        
        [contenteditable] li {
          margin: 0.25em 0;
        }
        
        [contenteditable] a {
          color: #3b82f6;
          text-decoration: underline;
        }
        
        [contenteditable] blockquote {
          border-left: 4px solid #6b7280;
          padding-left: 1em;
          margin: 1em 0;
          font-style: italic;
          color: #d1d5db;
        }
        
        [contenteditable] hr {
          border: none;
          border-top: 2px solid #4b5563;
          margin: 1em 0;
        }
      `}</style>
    </div>
  );
};
