/**
 * Test script to verify our validation functions are working
 */

import { validateRequest, ContactSchema, detectMaliciousInput } from './lib/inputValidation.js';

console.log('🧪 Testing validation functions...\n');

// Test 1: Normal input
console.log('1. Testing normal input:');
const normalInput = {
  name: '<PERSON>',
  surname: '<PERSON><PERSON>', 
  email: '<EMAIL>',
  message: 'This is a normal message'
};

const normalResult = validateRequest(ContactSchema)(normalInput);
console.log('Result:', normalResult);
console.log('✅ Should pass:', normalResult.success);

// Test 2: SQL injection input
console.log('\n2. Testing SQL injection input:');
const sqlInjectionInput = {
  name: "'; DROP TABLE users; --",
  surname: '<PERSON><PERSON>',
  email: '<EMAIL>', 
  message: 'This is a test message'
};

const sqlResult = validateRequest(ContactSchema)(sqlInjectionInput);
console.log('Result:', sqlResult);
console.log('❌ Should fail:', !sqlResult.success);

// Test 3: XSS input
console.log('\n3. Testing XSS input:');
const xssInput = {
  name: '<script>alert("XSS")</script>',
  surname: 'Test',
  email: '<EMAIL>',
  message: 'This is a test message'
};

const xssResult = validateRequest(ContactSchema)(xssInput);
console.log('Result:', xssResult);
console.log('❌ Should fail:', !xssResult.success);

// Test 4: Direct malicious detection
console.log('\n4. Testing malicious pattern detection:');
const maliciousPatterns = [
  "'; DROP TABLE users; --",
  '<script>alert("XSS")</script>',
  "' OR '1'='1",
  '../../../etc/passwd'
];

maliciousPatterns.forEach((pattern, index) => {
  const detection = detectMaliciousInput(pattern);
  console.log(`Pattern ${index + 1}: "${pattern.substring(0, 30)}..."`);
  console.log(`  Malicious: ${detection.isMalicious}`);
  console.log(`  Patterns: ${detection.patterns.join(', ')}`);
});

console.log('\n🎯 Validation test completed!');
