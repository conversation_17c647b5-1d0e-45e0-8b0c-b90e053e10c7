/**
 * TRANSACTION MANAGEMENT SYSTEM TEST SUITE
 * 
 * Comprehensive testing script for the new transaction management system
 * including email notifications, filtering, and data integrity.
 */

import { createClient } from '@supabase/supabase-js'
import { 
  sendTransactionNotification,
  notifyUSDTTransaction,
  notifyBankTransferTransaction 
} from './lib/services/transactionNotificationService.js'

// Test configuration
const SUPABASE_URL = process.env.VITE_SUPABASE_URL
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY
const TEST_EMAIL = '<EMAIL>' // For testing email delivery

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Missing Supabase configuration')
  process.exit(1)
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY)

/**
 * Test 1: Database Schema Verification
 */
async function testDatabaseSchema() {
  console.log('\n🔍 Testing Database Schema...')
  
  try {
    // Test crypto_payment_transactions table
    const { data: cryptoData, error: cryptoError } = await supabase
      .from('crypto_payment_transactions')
      .select('*')
      .limit(1)
    
    if (cryptoError) {
      console.error('❌ crypto_payment_transactions table error:', cryptoError.message)
      return false
    }
    
    // Test bank_transfer_payments table
    const { data: bankData, error: bankError } = await supabase
      .from('bank_transfer_payments')
      .select('*')
      .limit(1)
    
    if (bankError) {
      console.error('❌ bank_transfer_payments table error:', bankError.message)
      return false
    }
    
    // Test aureus_share_purchases table
    const { data: shareData, error: shareError } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .limit(1)
    
    if (shareError) {
      console.error('❌ aureus_share_purchases table error:', shareError.message)
      return false
    }
    
    // Test users table with KYC information
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select(`
        id,
        username,
        full_name,
        email,
        kyc_information (
          kyc_status
        )
      `)
      .limit(1)
    
    if (userError) {
      console.error('❌ users table with KYC join error:', userError.message)
      return false
    }
    
    console.log('✅ Database schema verification passed')
    console.log(`   - crypto_payment_transactions: ${cryptoData?.length || 0} records found`)
    console.log(`   - bank_transfer_payments: ${bankData?.length || 0} records found`)
    console.log(`   - aureus_share_purchases: ${shareData?.length || 0} records found`)
    console.log(`   - users with KYC: ${userData?.length || 0} records found`)
    
    return true
  } catch (error) {
    console.error('❌ Database schema test failed:', error)
    return false
  }
}

/**
 * Test 2: Transaction Data Retrieval
 */
async function testTransactionRetrieval() {
  console.log('\n📊 Testing Transaction Data Retrieval...')
  
  try {
    const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
    const endDate = new Date().toISOString()
    
    // Test comprehensive transaction query
    const { data: transactions, error } = await supabase
      .from('crypto_payment_transactions')
      .select(`
        id,
        user_id,
        amount,
        currency,
        network,
        transaction_hash,
        sender_wallet,
        receiver_wallet,
        screenshot_url,
        status,
        shares_to_purchase,
        created_at,
        updated_at,
        approved_at,
        approved_by_admin_id,
        users!inner(
          id,
          username,
          full_name,
          email,
          kyc_information(kyc_status)
        )
      `)
      .gte('created_at', startDate)
      .lte('created_at', endDate)
      .limit(10)
    
    if (error) {
      console.error('❌ Transaction retrieval failed:', error.message)
      return false
    }
    
    console.log('✅ Transaction data retrieval passed')
    console.log(`   - Retrieved ${transactions?.length || 0} transactions`)
    
    if (transactions && transactions.length > 0) {
      const sample = transactions[0]
      console.log('   - Sample transaction structure verified:')
      console.log(`     * ID: ${sample.id}`)
      console.log(`     * User: ${sample.users.full_name || sample.users.username}`)
      console.log(`     * Amount: $${sample.amount} ${sample.currency}`)
      console.log(`     * Status: ${sample.status}`)
      console.log(`     * KYC Status: ${sample.users.kyc_information?.[0]?.kyc_status || 'N/A'}`)
    }
    
    return true
  } catch (error) {
    console.error('❌ Transaction retrieval test failed:', error)
    return false
  }
}

/**
 * Test 3: Email Notification System
 */
async function testEmailNotifications() {
  console.log('\n📧 Testing Email Notification System...')
  
  try {
    // Create test transaction data
    const testTransactionData = {
      transactionId: 'test-' + Date.now(),
      userId: 1,
      userFullName: 'Test User',
      userEmail: TEST_EMAIL,
      aureusId: 'TEST001',
      paymentMethod: 'USDT',
      amount: 100,
      currency: 'USDT',
      shareQuantity: 4,
      transactionHash: '0x1234567890abcdef',
      walletAddress: '0xabcdef1234567890',
      network: 'BSC',
      status: 'pending',
      createdAt: new Date().toISOString(),
      kycStatus: 'approved'
    }
    
    // Test direct notification function
    const result = await sendTransactionNotification(testTransactionData)
    
    if (result.success) {
      console.log('✅ Email notification test passed')
      console.log(`   - Message ID: ${result.messageId}`)
      console.log(`   - Test email sent to: ${TEST_EMAIL}`)
    } else {
      console.error('❌ Email notification test failed:', result.error)
      return false
    }
    
    return true
  } catch (error) {
    console.error('❌ Email notification test failed:', error)
    return false
  }
}

/**
 * Test 4: Filtering System
 */
async function testFilteringSystem() {
  console.log('\n🔍 Testing Filtering System...')
  
  try {
    const filters = {
      paymentMethod: 'USDT',
      dateRange: {
        from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        to: new Date().toISOString().split('T')[0]
      },
      status: 'pending',
      amountRange: { min: 0, max: 1000 }
    }
    
    // Test filtered query
    let query = supabase
      .from('crypto_payment_transactions')
      .select(`
        id,
        amount,
        status,
        created_at,
        users!inner(username, email)
      `)
      .gte('created_at', `${filters.dateRange.from}T00:00:00.000Z`)
      .lte('created_at', `${filters.dateRange.to}T23:59:59.999Z`)
    
    if (filters.status !== 'all') {
      query = query.eq('status', filters.status)
    }
    
    const { data: filteredData, error } = await query.limit(5)
    
    if (error) {
      console.error('❌ Filtering system test failed:', error.message)
      return false
    }
    
    console.log('✅ Filtering system test passed')
    console.log(`   - Filtered results: ${filteredData?.length || 0} transactions`)
    console.log(`   - Date range: ${filters.dateRange.from} to ${filters.dateRange.to}`)
    console.log(`   - Status filter: ${filters.status}`)
    
    return true
  } catch (error) {
    console.error('❌ Filtering system test failed:', error)
    return false
  }
}

/**
 * Test 5: Integration Test
 */
async function testIntegration() {
  console.log('\n🔗 Testing System Integration...')
  
  try {
    // Test that all components work together
    const testUserId = 1 // Assuming user ID 1 exists
    
    // Test USDT notification helper
    const usdtResult = await notifyUSDTTransaction(
      'test-usdt-' + Date.now(),
      testUserId,
      50,
      2,
      '0xtest123',
      'BSC',
      '0xwallet123',
      'https://example.com/proof.jpg'
    )
    
    if (!usdtResult.success) {
      console.error('❌ USDT notification integration failed:', usdtResult.error)
      return false
    }
    
    // Test Bank Transfer notification helper
    const bankResult = await notifyBankTransferTransaction(
      'test-bank-' + Date.now(),
      testUserId,
      75,
      'USD',
      3,
      'REF123456',
      'https://example.com/bank-proof.pdf'
    )
    
    if (!bankResult.success) {
      console.error('❌ Bank transfer notification integration failed:', bankResult.error)
      return false
    }
    
    console.log('✅ System integration test passed')
    console.log(`   - USDT notification: ${usdtResult.messageId}`)
    console.log(`   - Bank transfer notification: ${bankResult.messageId}`)
    
    return true
  } catch (error) {
    console.error('❌ Integration test failed:', error)
    return false
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting Transaction Management System Tests...')
  console.log('=' .repeat(60))
  
  const tests = [
    { name: 'Database Schema', fn: testDatabaseSchema },
    { name: 'Transaction Retrieval', fn: testTransactionRetrieval },
    { name: 'Email Notifications', fn: testEmailNotifications },
    { name: 'Filtering System', fn: testFilteringSystem },
    { name: 'System Integration', fn: testIntegration }
  ]
  
  let passed = 0
  let failed = 0
  
  for (const test of tests) {
    try {
      const result = await test.fn()
      if (result) {
        passed++
      } else {
        failed++
      }
    } catch (error) {
      console.error(`❌ Test "${test.name}" threw an error:`, error)
      failed++
    }
  }
  
  console.log('\n' + '=' .repeat(60))
  console.log('📊 TEST RESULTS SUMMARY')
  console.log('=' .repeat(60))
  console.log(`✅ Passed: ${passed}`)
  console.log(`❌ Failed: ${failed}`)
  console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`)
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! Transaction Management System is ready for production.')
  } else {
    console.log('\n⚠️  Some tests failed. Please review the errors above before deploying.')
  }
  
  process.exit(failed === 0 ? 0 : 1)
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(error => {
    console.error('❌ Test suite failed:', error)
    process.exit(1)
  })
}

export { runAllTests }
