import React from 'react';

export const PrivacyPolicy: React.FC = () => {
    return (
        <div className="max-w-4xl mx-auto">
            <h1 className="text-3xl font-bold text-gold mb-8">Privacy Policy</h1>
            
            <div className="space-y-8 text-gray-300 leading-relaxed">
                <section>
                    <h2 className="text-2xl font-semibold text-gold mb-4">Introduction</h2>
                    <p className="mb-4">
                        Aureus Alliance Holdings (Pty) Ltd ("we", "us", "our", or "the Company") is committed to protecting
                        your privacy and personal information in accordance with the Protection of Personal Information Act 4 of 2013
                        (POPIA) and other applicable South African privacy laws.
                    </p>
                    <p className="mb-4">
                        This Privacy Policy explains how we collect, use, disclose, and protect your personal information when you
                        visit our website (aureus.africa), purchase shares in our company, or interact with our services.
                    </p>
                    <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
                        <h3 className="text-lg font-semibold text-blue-400 mb-2">Company Details</h3>
                        <p className="text-sm">
                            <strong>Company Name:</strong> Aureus Alliance Holdings (Pty) Ltd<br/>
                            <strong>Registration Number:</strong> 2025/368711/07<br/>
                            <strong>Address:</strong> 1848 Mees Avenue, Randpark Ridge, Randburg, Gauteng, 2169<br/>
                            <strong>Contact:</strong> <EMAIL>
                        </p>
                    </div>
                </section>

                <section>
                    <h2 className="text-2xl font-semibold text-gold mb-4">Information We Collect</h2>
                    <h3 className="text-xl font-semibold text-amber-400 mb-3">Personal Information</h3>
                    <p className="mb-4">We may collect the following types of personal information:</p>
                    <ul className="list-disc list-inside space-y-2 mb-6">
                        <li>Contact information (name, email address, phone number, postal address)</li>
                        <li>Identity information (ID number, passport details, proof of address)</li>
                        <li>Financial information (bank account details, payment information)</li>
                        <li>Share ownership details and transaction history</li>
                        <li>Communication records and correspondence</li>
                        <li>Website usage data and analytics</li>
                        <li>Device information and IP addresses</li>
                    </ul>
                    
                    <h3 className="text-xl font-semibold text-amber-400 mb-3">Special Personal Information</h3>
                    <p className="mb-4">Under certain circumstances, we may collect special personal information including:</p>
                    <ul className="list-disc list-inside space-y-2 mb-4">
                        <li>Financial information for compliance and regulatory purposes</li>
                        <li>Biometric data for identity verification (if required by law)</li>
                        <li>Information about criminal convictions for compliance screening</li>
                    </ul>
                </section>

                <section>
                    <h2 className="text-2xl font-semibold text-gold mb-4">Contact Information</h2>
                    <div className="grid md:grid-cols-2 gap-6">
                        <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
                            <h3 className="text-lg font-semibold text-blue-400 mb-2">Data Protection Officer</h3>
                            <p className="text-sm">
                                <strong>Email:</strong> <EMAIL><br/>
                                <strong>Phone:</strong> +27 (0)74 449 3251<br/>
                                <strong>Address:</strong> 1848 Mees Avenue, Randpark Ridge, Randburg, Gauteng, 2169<br/>
                                <strong>Response Time:</strong> Within 30 days
                            </p>
                        </div>
                        <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4">
                            <h3 className="text-lg font-semibold text-green-400 mb-2">Information Regulator</h3>
                            <p className="text-sm">
                                If unsatisfied with our response:<br/>
                                <strong>Website:</strong> inforeg.org.za<br/>
                                <strong>Email:</strong> <EMAIL><br/>
                                <strong>Phone:</strong> +27 (0)12 406 4818
                            </p>
                        </div>
                    </div>
                </section>

                <div className="border-t border-gray-600 pt-6 mt-8">
                    <p className="text-sm text-gray-400">
                        Last Updated: {new Date().toLocaleDateString()}<br/>
                        This Privacy Policy is effective as of the date last updated.
                    </p>
                </div>
            </div>
        </div>
    );
};
