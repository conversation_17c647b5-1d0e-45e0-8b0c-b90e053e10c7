import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkUserData() {
  try {
    console.log('🔍 Checking user data...');
    
    // Check users table
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, username, email, telegram_id')
      .order('id');
    
    if (usersError) {
      console.error('❌ Error fetching users:', usersError);
      return;
    }
    
    console.log('👥 Users table:');
    users.forEach(user => {
      console.log(`  ID: ${user.id}, Username: ${user.username}, Email: ${user.email}, Telegram ID: ${user.telegram_id || 'NULL'}`);
    });
    
    // Check telegram_users table
    const { data: telegramUsers, error: telegramError } = await supabase
      .from('telegram_users')
      .select('id, user_id, telegram_id, username, first_name')
      .order('telegram_id');
    
    if (telegramError) {
      console.error('❌ Error fetching telegram users:', telegramError);
      return;
    }
    
    console.log('\n📱 Telegram users table:');
    telegramUsers.forEach(tUser => {
      console.log(`  Telegram ID: ${tUser.telegram_id}, User ID: ${tUser.user_id || 'NULL'}, Username: ${tUser.username}, Name: ${tUser.first_name}`);
    });
    
    // Find the specific user we're working with
    const targetUser = users.find(u => u.username === '<EMAIL>' || u.email === '<EMAIL>');
    if (targetUser) {
      console.log('\n🎯 Target user found:');
      console.log(`  ID: ${targetUser.id}, Username: ${targetUser.username}, Telegram ID: ${targetUser.telegram_id || 'NULL'}`);
      
      // Check if there's a corresponding telegram user
      const telegramUser = telegramUsers.find(tu => tu.telegram_id === 1393852532);
      if (telegramUser) {
        console.log('📱 Corresponding Telegram user:');
        console.log(`  Telegram ID: ${telegramUser.telegram_id}, Linked User ID: ${telegramUser.user_id || 'NULL'}, Username: ${telegramUser.username}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

checkUserData();
