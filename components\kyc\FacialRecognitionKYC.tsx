import React, { useState, useRef, useEffect, useCallback } from 'react';
import { supabase } from '../../lib/supabase';

interface FacialRecognitionKYCProps {
  userId: number;
  onComplete: (results: FacialRecognitionResults) => void;
  onCancel: () => void;
}

interface FacialRecognitionResults {
  confidence_score: number;
  verification_photo_url: string;
  liveness_checks: {
    blink_detection: boolean;
    head_movement: boolean;
    smile_detection: boolean;
    face_centering: boolean;
  };
  metadata: {
    session_id: string;
    timestamp: string;
    device_info: string;
    verification_steps: VerificationStep[];
  };
}

interface VerificationStep {
  step: string;
  instruction: string;
  completed: boolean;
  confidence: number;
  timestamp: string;
}

const VERIFICATION_STEPS = [
  {
    id: 'face_detection',
    instruction: 'Position your face in the center of the frame',
    icon: '👤',
    duration: 3000
  },
  {
    id: 'blink_detection',
    instruction: 'Blink twice slowly',
    icon: '👁️',
    duration: 4000
  },
  {
    id: 'head_left',
    instruction: 'Turn your head slowly to the left',
    icon: '⬅️',
    duration: 3000
  },
  {
    id: 'head_right',
    instruction: 'Turn your head slowly to the right',
    icon: '➡️',
    duration: 3000
  },
  {
    id: 'smile_detection',
    instruction: 'Smile naturally at the camera',
    icon: '😊',
    duration: 3000
  },
  {
    id: 'final_capture',
    instruction: 'Hold still for final verification photo',
    icon: '📸',
    duration: 2000
  }
];

export const FacialRecognitionKYC: React.FC<FacialRecognitionKYCProps> = ({
  userId,
  onComplete,
  onCancel
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  
  const [currentStep, setCurrentStep] = useState(0);
  const [isRecording, setIsRecording] = useState(false);
  const [stepProgress, setStepProgress] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<VerificationStep[]>([]);
  const [faceDetected, setFaceDetected] = useState(false);
  const [sessionId] = useState(() => `facial_kyc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // Initialize camera
  const initializeCamera = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          facingMode: 'user'
        },
        audio: false
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        streamRef.current = stream;
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      setError('Unable to access camera. Please ensure camera permissions are granted.');
    }
  }, []);

  // Cleanup camera
  const cleanupCamera = useCallback(() => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
  }, []);

  // Face detection simulation (in production, use actual face detection library)
  const detectFace = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return false;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    if (!ctx) return false;

    // Set canvas size to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw current frame
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Simulate face detection (in production, use actual face detection)
    // For demo purposes, we'll assume face is detected if video is playing
    const faceDetected = video.readyState === 4 && video.videoWidth > 0;
    setFaceDetected(faceDetected);

    return faceDetected;
  }, []);

  // Process current verification step
  const processCurrentStep = useCallback(async () => {
    const step = VERIFICATION_STEPS[currentStep];
    if (!step) return;

    setIsRecording(true);
    setStepProgress(0);

    // Simulate step processing with progress
    const progressInterval = setInterval(() => {
      setStepProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          return 100;
        }
        return prev + (100 / (step.duration / 100));
      });
    }, 100);

    // Wait for step duration
    await new Promise(resolve => setTimeout(resolve, step.duration));

    // Simulate verification logic for each step
    let stepCompleted = false;
    let confidence = 0;

    switch (step.id) {
      case 'face_detection':
        stepCompleted = detectFace();
        confidence = stepCompleted ? 0.95 : 0.3;
        break;
      case 'blink_detection':
        // Simulate blink detection
        stepCompleted = Math.random() > 0.2; // 80% success rate
        confidence = stepCompleted ? 0.88 : 0.4;
        break;
      case 'head_left':
      case 'head_right':
        // Simulate head movement detection
        stepCompleted = Math.random() > 0.15; // 85% success rate
        confidence = stepCompleted ? 0.82 : 0.35;
        break;
      case 'smile_detection':
        // Simulate smile detection
        stepCompleted = Math.random() > 0.25; // 75% success rate
        confidence = stepCompleted ? 0.78 : 0.4;
        break;
      case 'final_capture':
        stepCompleted = detectFace();
        confidence = stepCompleted ? 0.92 : 0.3;
        break;
    }

    const completedStep: VerificationStep = {
      step: step.id,
      instruction: step.instruction,
      completed: stepCompleted,
      confidence,
      timestamp: new Date().toISOString()
    };

    setCompletedSteps(prev => [...prev, completedStep]);
    setIsRecording(false);

    if (stepCompleted) {
      // Move to next step after a brief pause
      setTimeout(() => {
        if (currentStep < VERIFICATION_STEPS.length - 1) {
          setCurrentStep(prev => prev + 1);
        } else {
          // All steps completed, process final results
          processFinalResults();
        }
      }, 1000);
    } else {
      // Step failed, show retry option
      setError(`${step.instruction} - Please try again`);
      setTimeout(() => {
        setError(null);
        setStepProgress(0);
      }, 2000);
    }
  }, [currentStep, detectFace]);

  // Process final results and capture verification photo
  const processFinalResults = useCallback(async () => {
    setIsProcessing(true);

    try {
      // Capture final verification photo
      const canvas = canvasRef.current;
      const video = videoRef.current;

      if (!canvas || !video) {
        throw new Error('Unable to capture verification photo');
      }

      const ctx = canvas.getContext('2d');
      if (!ctx) {
        throw new Error('Unable to get canvas context');
      }

      // Set canvas size and capture frame
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Calculate overall confidence score
      const overallConfidence = completedSteps.reduce((acc, step) => acc + step.confidence, 0) / completedSteps.length;

      // Add confidence score overlay to the image
      ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
      ctx.fillRect(10, 10, 300, 80);
      ctx.fillStyle = '#00ff00';
      ctx.font = '16px Arial';
      ctx.fillText(`Verification Score: ${(overallConfidence * 100).toFixed(1)}%`, 20, 35);
      ctx.fillText(`Session: ${sessionId}`, 20, 55);
      ctx.fillText(`Date: ${new Date().toLocaleString()}`, 20, 75);

      // Convert canvas to blob
      const blob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => {
          if (blob) resolve(blob);
        }, 'image/jpeg', 0.9);
      });

      // Upload to Supabase storage
      const fileName = `facial_verification_${userId}_${sessionId}.jpg`;
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('proof')
        .upload(fileName, blob, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        throw new Error(`Upload failed: ${uploadError.message}`);
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('proof')
        .getPublicUrl(fileName);

      // Prepare results
      const results: FacialRecognitionResults = {
        confidence_score: overallConfidence,
        verification_photo_url: publicUrl,
        liveness_checks: {
          blink_detection: completedSteps.some(s => s.step === 'blink_detection' && s.completed),
          head_movement: completedSteps.some(s => (s.step === 'head_left' || s.step === 'head_right') && s.completed),
          smile_detection: completedSteps.some(s => s.step === 'smile_detection' && s.completed),
          face_centering: completedSteps.some(s => s.step === 'face_detection' && s.completed)
        },
        metadata: {
          session_id: sessionId,
          timestamp: new Date().toISOString(),
          device_info: navigator.userAgent,
          verification_steps: completedSteps
        }
      };

      // Cleanup camera before completing
      cleanupCamera();

      // Return results
      onComplete(results);

    } catch (error) {
      console.error('Error processing final results:', error);
      setError(`Processing failed: ${error.message}`);
    } finally {
      setIsProcessing(false);
    }
  }, [completedSteps, sessionId, userId, cleanupCamera, onComplete]);

  // Initialize camera on mount
  useEffect(() => {
    initializeCamera();
    return cleanupCamera;
  }, [initializeCamera, cleanupCamera]);

  // Auto-start first step when camera is ready
  useEffect(() => {
    if (videoRef.current && videoRef.current.readyState >= 2 && currentStep === 0 && !isRecording) {
      setTimeout(() => processCurrentStep(), 1000);
    }
  }, [videoRef.current?.readyState, currentStep, isRecording, processCurrentStep]);

  const currentStepData = VERIFICATION_STEPS[currentStep];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-white">🔐 Facial Recognition Verification</h2>
          <button
            onClick={() => {
              cleanupCamera();
              onCancel();
            }}
            className="text-gray-400 hover:text-white text-2xl"
          >
            ✕
          </button>
        </div>

        {/* Progress Indicator */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-gray-400">
              Step {currentStep + 1} of {VERIFICATION_STEPS.length}
            </span>
            <span className="text-sm text-gray-400">
              {completedSteps.filter(s => s.completed).length} / {VERIFICATION_STEPS.length} completed
            </span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentStep + (stepProgress / 100)) / VERIFICATION_STEPS.length) * 100}%` }}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Camera Feed */}
          <div className="space-y-4">
            <div className="relative bg-black rounded-lg overflow-hidden aspect-video">
              <video
                ref={videoRef}
                autoPlay
                playsInline
                muted
                className="w-full h-full object-cover"
              />
              
              {/* Face detection overlay */}
              {faceDetected && (
                <div className="absolute inset-0 border-4 border-green-400 rounded-lg animate-pulse" />
              )}
              
              {/* Recording indicator */}
              {isRecording && (
                <div className="absolute top-4 right-4 flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse" />
                  <span className="text-white text-sm font-medium">Recording</span>
                </div>
              )}

              {/* Step progress overlay */}
              {isRecording && (
                <div className="absolute bottom-4 left-4 right-4">
                  <div className="bg-black bg-opacity-70 rounded-lg p-3">
                    <div className="w-full bg-gray-600 rounded-full h-2 mb-2">
                      <div 
                        className="bg-green-500 h-2 rounded-full transition-all duration-100"
                        style={{ width: `${stepProgress}%` }}
                      />
                    </div>
                    <p className="text-white text-sm text-center">
                      {currentStepData?.instruction}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Hidden canvas for image capture */}
            <canvas ref={canvasRef} className="hidden" />
          </div>

          {/* Instructions and Status */}
          <div className="space-y-6">
            {/* Current Step */}
            {currentStepData && (
              <div className="bg-gray-700 rounded-lg p-4">
                <div className="flex items-center space-x-3 mb-3">
                  <span className="text-3xl">{currentStepData.icon}</span>
                  <div>
                    <h3 className="text-white font-semibold">
                      Step {currentStep + 1}: {currentStepData.id.replace('_', ' ').toUpperCase()}
                    </h3>
                    <p className="text-gray-300 text-sm">{currentStepData.instruction}</p>
                  </div>
                </div>
                
                {!isRecording && !isProcessing && (
                  <button
                    onClick={processCurrentStep}
                    className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    Start Step
                  </button>
                )}
              </div>
            )}

            {/* Error Display */}
            {error && (
              <div className="bg-red-900/30 border border-red-700 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <span className="text-red-400">⚠️</span>
                  <p className="text-red-200">{error}</p>
                </div>
              </div>
            )}

            {/* Processing Status */}
            {isProcessing && (
              <div className="bg-blue-900/30 border border-blue-700 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-400"></div>
                  <div>
                    <p className="text-blue-200 font-medium">Processing verification...</p>
                    <p className="text-blue-300 text-sm">Please wait while we analyze your verification</p>
                  </div>
                </div>
              </div>
            )}

            {/* Completed Steps */}
            <div className="bg-gray-700 rounded-lg p-4">
              <h4 className="text-white font-medium mb-3">Verification Progress</h4>
              <div className="space-y-2">
                {VERIFICATION_STEPS.map((step, index) => {
                  const completed = completedSteps.find(s => s.step === step.id);
                  const isCurrent = index === currentStep;
                  
                  return (
                    <div key={step.id} className={`flex items-center space-x-3 p-2 rounded ${
                      isCurrent ? 'bg-blue-900/30 border border-blue-700' : ''
                    }`}>
                      <span className="text-xl">{step.icon}</span>
                      <div className="flex-1">
                        <p className={`text-sm ${completed?.completed ? 'text-green-400' : 'text-gray-400'}`}>
                          {step.instruction}
                        </p>
                        {completed && (
                          <p className="text-xs text-gray-500">
                            Confidence: {(completed.confidence * 100).toFixed(1)}%
                          </p>
                        )}
                      </div>
                      <div className="text-lg">
                        {completed?.completed ? '✅' : isCurrent ? '🔄' : '⏳'}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Tips */}
            <div className="bg-yellow-900/30 border border-yellow-700 rounded-lg p-4">
              <h4 className="text-yellow-400 font-medium mb-2">💡 Verification Tips</h4>
              <ul className="text-yellow-200 text-sm space-y-1">
                <li>• Ensure good lighting on your face</li>
                <li>• Keep your face centered in the frame</li>
                <li>• Follow instructions carefully and naturally</li>
                <li>• Avoid wearing sunglasses or hats</li>
                <li>• Stay still during final capture</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FacialRecognitionKYC;
