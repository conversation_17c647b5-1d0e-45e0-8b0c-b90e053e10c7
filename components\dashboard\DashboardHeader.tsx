import React, { useState } from 'react';
import { NotificationBadge, NotificationDropdown } from '../user/NotificationBadge';
import { DashboardSwitcher } from '../DashboardSwitcher';
import { ProfilePictureUpload } from '../ProfilePictureUpload';
import { DashboardData } from '../../hooks/useDashboardData';

interface DashboardHeaderProps {
  user: any;
  onLogout: () => void;
  onSwitchDashboard?: (dashboard: 'shareholder' | 'affiliate') => void;
  dashboardData: DashboardData;
  onRefreshData: () => Promise<void>;
}

export const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  user,
  onLogout,
  onSwitchDashboard,
  dashboardData,
  onRefreshData
}) => {
  const [showNotifications, setShowNotifications] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);

  const handleNotificationClick = () => {
    setShowNotifications(!showNotifications);
    setShowUserMenu(false);
  };

  const handleUserMenuClick = () => {
    setShowUserMenu(!showUserMenu);
    setShowNotifications(false);
  };

  return (
    <header className="bg-gray-800 border-b border-gray-700 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Left side - Dashboard title and refresh */}
        <div className="flex items-center space-x-4">
          <div>
            <h1 className="text-2xl font-bold text-white">
              Shareholder Dashboard
            </h1>
            <p className="text-gray-400 text-sm">
              Welcome back, {user?.database_user?.full_name || user?.full_name || user?.username}
            </p>
          </div>
          
          <button
            onClick={onRefreshData}
            className="text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-700"
            title="Refresh dashboard data"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>

        {/* Right side - Actions and user menu */}
        <div className="flex items-center space-x-4">
          {/* Quick Stats */}
          <div className="hidden md:flex items-center space-x-6 text-sm">
            <div className="text-center">
              <div className="text-gray-400">Total Shares</div>
              <div className="text-white font-semibold">
                {dashboardData.totalShares.toLocaleString()}
              </div>
            </div>
            <div className="text-center">
              <div className="text-gray-400">Portfolio Value</div>
              <div className="text-white font-semibold">
                ${dashboardData.currentValue.toLocaleString()}
              </div>
            </div>
            <div className="text-center">
              <div className="text-gray-400">USDT Balance</div>
              <div className="text-blue-400 font-semibold">
                ${dashboardData.usdtCommissions?.available.toFixed(2) || '0.00'}
              </div>
            </div>
          </div>

          {/* Dashboard Switcher */}
          {onSwitchDashboard && (
            <DashboardSwitcher
              currentDashboard="shareholder"
              onSwitch={onSwitchDashboard}
            />
          )}

          {/* Notifications */}
          <div className="relative">
            <button
              onClick={handleNotificationClick}
              className="relative p-2 text-gray-400 hover:text-white transition-colors rounded-lg hover:bg-gray-700"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM10.97 4.97a.235.235 0 0 0-.02 0 .327.327 0 0 0-.23.06.19.19 0 0 0-.06.1c0 .06.02.13.06.18l.02.02c.05.06.12.09.2.09s.15-.03.2-.09l.02-.02c.04-.05.06-.12.06-.18a.19.19 0 0 0-.06-.1.327.327 0 0 0-.23-.06.235.235 0 0 0-.02 0z" />
              </svg>
              {dashboardData.notifications && dashboardData.notifications.unread > 0 && (
                <NotificationBadge count={dashboardData.notifications.unread} />
              )}
            </button>

            {showNotifications && (
              <div className="absolute right-0 mt-2 w-80 z-50">
                <NotificationDropdown
                  userId={user?.database_user?.id || user?.id}
                  onClose={() => setShowNotifications(false)}
                />
              </div>
            )}
          </div>

          {/* User Menu */}
          <div className="relative">
            <button
              onClick={handleUserMenuClick}
              className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-700 transition-colors"
            >
              <ProfilePictureUpload
                userId={user?.database_user?.id || user?.id}
                currentImageUrl={user?.database_user?.profile_picture_url || user?.profile_picture_url}
                size="sm"
                showUploadOnHover={false}
              />
              <div className="hidden md:block text-left">
                <div className="text-white text-sm font-medium">
                  {user?.database_user?.full_name || user?.full_name || user?.username}
                </div>
                <div className="text-gray-400 text-xs">
                  {user?.database_user?.email || user?.email}
                </div>
              </div>
              <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>

            {showUserMenu && (
              <div className="absolute right-0 mt-2 w-48 bg-gray-800 border border-gray-700 rounded-lg shadow-lg z-50">
                <div className="py-2">
                  <button
                    onClick={() => {
                      setShowUserMenu(false);
                      // Navigate to settings
                    }}
                    className="w-full text-left px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 transition-colors"
                  >
                    <div className="flex items-center space-x-2">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      <span>Settings</span>
                    </div>
                  </button>
                  
                  <div className="border-t border-gray-700 my-2"></div>
                  
                  <button
                    onClick={() => {
                      setShowUserMenu(false);
                      onLogout();
                    }}
                    className="w-full text-left px-4 py-2 text-red-400 hover:text-red-300 hover:bg-gray-700 transition-colors"
                  >
                    <div className="flex items-center space-x-2">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                      </svg>
                      <span>Sign Out</span>
                    </div>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};
