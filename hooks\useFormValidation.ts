/**
 * FORM VALIDATION HOOK
 * 
 * Provides form validation utilities for authentication forms
 * including email, password, and general field validation.
 */

import { useState, useCallback, useMemo } from 'react';

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: string) => string | null;
}

export interface ValidationRules {
  [fieldName: string]: ValidationRule;
}

export interface ValidationErrors {
  [fieldName: string]: string;
}

export interface FormData {
  [fieldName: string]: string;
}

export interface UseFormValidationReturn {
  formData: FormData;
  errors: ValidationErrors;
  isValid: boolean;
  setFieldValue: (fieldName: string, value: string) => void;
  setFormData: (data: FormData) => void;
  validateField: (fieldName: string) => boolean;
  validateForm: () => boolean;
  clearErrors: () => void;
  resetForm: (initialData?: FormData) => void;
  getFieldError: (fieldName: string) => string | undefined;
  hasFieldError: (fieldName: string) => boolean;
}

export const useFormValidation = (
  initialData: FormData = {},
  validationRules: ValidationRules = {}
): UseFormValidationReturn => {
  const [formData, setFormDataState] = useState<FormData>(initialData);
  const [errors, setErrors] = useState<ValidationErrors>({});

  // Validation functions
  const validateEmail = useCallback((email: string): string | null => {
    if (!email) return 'Email is required';
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) return 'Please enter a valid email address';
    return null;
  }, []);

  const validatePassword = useCallback((password: string): string | null => {
    if (!password) return 'Password is required';
    if (password.length < 8) return 'Password must be at least 8 characters long';
    if (!/(?=.*[a-z])/.test(password)) return 'Password must contain at least one lowercase letter';
    if (!/(?=.*[A-Z])/.test(password)) return 'Password must contain at least one uppercase letter';
    if (!/(?=.*\d)/.test(password)) return 'Password must contain at least one number';
    if (!/(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/.test(password)) {
      return 'Password must contain at least one special character';
    }
    return null;
  }, []);

  const validateConfirmPassword = useCallback((confirmPassword: string, originalPassword: string): string | null => {
    if (!confirmPassword) return 'Please confirm your password';
    if (confirmPassword !== originalPassword) return 'Passwords do not match';
    return null;
  }, []);

  const validateField = useCallback((fieldName: string): boolean => {
    const value = formData[fieldName] || '';
    const rules = validationRules[fieldName];
    let error: string | null = null;

    if (rules) {
      // Required validation
      if (rules.required && !value.trim()) {
        error = `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;
      }
      // Min length validation
      else if (rules.minLength && value.length < rules.minLength) {
        error = `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} must be at least ${rules.minLength} characters`;
      }
      // Max length validation
      else if (rules.maxLength && value.length > rules.maxLength) {
        error = `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} must be no more than ${rules.maxLength} characters`;
      }
      // Pattern validation
      else if (rules.pattern && !rules.pattern.test(value)) {
        error = `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} format is invalid`;
      }
      // Custom validation
      else if (rules.custom) {
        error = rules.custom(value);
      }
    }

    // Built-in validations for common fields
    if (!error) {
      switch (fieldName) {
        case 'email':
          error = validateEmail(value);
          break;
        case 'password':
          error = validatePassword(value);
          break;
        case 'confirmPassword':
          error = validateConfirmPassword(value, formData.password || '');
          break;
        case 'newPassword':
          error = validatePassword(value);
          break;
        case 'confirmNewPassword':
          error = validateConfirmPassword(value, formData.newPassword || '');
          break;
      }
    }

    // Update errors state
    setErrors(prev => ({
      ...prev,
      [fieldName]: error || ''
    }));

    return !error;
  }, [formData, validationRules, validateEmail, validatePassword, validateConfirmPassword]);

  const validateForm = useCallback((): boolean => {
    const fieldNames = Object.keys(formData);
    const ruleFieldNames = Object.keys(validationRules);
    const allFieldNames = [...new Set([...fieldNames, ...ruleFieldNames])];

    let isFormValid = true;
    const newErrors: ValidationErrors = {};

    allFieldNames.forEach(fieldName => {
      const isFieldValid = validateField(fieldName);
      if (!isFieldValid) {
        isFormValid = false;
        newErrors[fieldName] = errors[fieldName];
      }
    });

    setErrors(newErrors);
    return isFormValid;
  }, [formData, validationRules, validateField, errors]);

  const setFieldValue = useCallback((fieldName: string, value: string) => {
    setFormDataState(prev => ({
      ...prev,
      [fieldName]: value
    }));

    // Clear error for this field when user starts typing
    if (errors[fieldName]) {
      setErrors(prev => ({
        ...prev,
        [fieldName]: ''
      }));
    }
  }, [errors]);

  const setFormData = useCallback((data: FormData) => {
    setFormDataState(data);
    setErrors({});
  }, []);

  const clearErrors = useCallback(() => {
    setErrors({});
  }, []);

  const resetForm = useCallback((newInitialData?: FormData) => {
    setFormDataState(newInitialData || initialData);
    setErrors({});
  }, [initialData]);

  const getFieldError = useCallback((fieldName: string): string | undefined => {
    return errors[fieldName] || undefined;
  }, [errors]);

  const hasFieldError = useCallback((fieldName: string): boolean => {
    return Boolean(errors[fieldName]);
  }, [errors]);

  // Computed property for overall form validity
  const isValid = useMemo(() => {
    return Object.values(errors).every(error => !error);
  }, [errors]);

  return {
    formData,
    errors,
    isValid,
    setFieldValue,
    setFormData,
    validateField,
    validateForm,
    clearErrors,
    resetForm,
    getFieldError,
    hasFieldError
  };
};
