import React, { useState, useEffect, useMemo } from 'react'
import { supabase } from '../../lib/supabase'
import { logAdminAction } from '../../lib/adminAuth'
import * as XLSX from 'xlsx'

interface TransactionData {
  id: string
  userId: number
  userFullName: string
  userEmail: string
  aureusId: string
  paymentMethod: 'USDT' | 'BANK_TRANSFER'
  amount: number
  currency: string
  shareQuantity: number
  transactionHash?: string
  proofOfPaymentUrl?: string
  walletAddress?: string
  bankReference?: string
  network?: string
  status: string
  createdAt: string
  updatedAt: string
  approvedAt?: string
  approvedBy?: string
  kycStatus?: string
  phaseId?: number
  phaseName?: string
  pricePerShare?: number
}

interface FilterState {
  paymentMethod: 'all' | 'USDT' | 'BANK_TRANSFER'
  dateRange: {
    from: string
    to: string
  }
  status: 'all' | 'pending' | 'approved' | 'rejected'
  userSearch: string
  amountRange: {
    min: number
    max: number
  }
  transactionSearch: string
  kycStatus: 'all' | 'approved' | 'pending' | 'rejected'
}

interface TransactionManagementSystemProps {
  currentUser?: any
}

export const TransactionManagementSystem: React.FC<TransactionManagementSystemProps> = ({ currentUser }) => {
  const [transactions, setTransactions] = useState<TransactionData[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedTransaction, setSelectedTransaction] = useState<TransactionData | null>(null)
  const [showModal, setShowModal] = useState(false)
  const [exporting, setExporting] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(25)
  const [sortField, setSortField] = useState<keyof TransactionData>('createdAt')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  const [filters, setFilters] = useState<FilterState>({
    paymentMethod: 'all',
    dateRange: {
      from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
      to: new Date().toISOString().split('T')[0] // today
    },
    status: 'all',
    userSearch: '',
    amountRange: {
      min: 0,
      max: 100000
    },
    transactionSearch: '',
    kycStatus: 'all'
  })

  // Load transactions with comprehensive filtering
  const loadTransactions = async () => {
    setLoading(true)
    try {
      console.log('🔍 Loading transactions with filters:', filters)

      // Build comprehensive query for all transaction types
      const startDateTime = `${filters.dateRange.from}T00:00:00.000Z`
      const endDateTime = `${filters.dateRange.to}T23:59:59.999Z`

      let allTransactions: TransactionData[] = []

      // 1. Load USDT transactions
      if (filters.paymentMethod === 'all' || filters.paymentMethod === 'USDT') {
        let cryptoQuery = supabase
          .from('crypto_payment_transactions')
          .select(`
            id,
            user_id,
            amount,
            currency,
            network,
            transaction_hash,
            sender_wallet,
            receiver_wallet,
            screenshot_url,
            status,
            shares_to_purchase,
            created_at,
            updated_at,
            approved_at,
            approved_by_admin_id,
            users!inner(
              id,
              username,
              full_name,
              email,
              kyc_information(kyc_status)
            )
          `)
          .gte('created_at', startDateTime)
          .lte('created_at', endDateTime)

        if (filters.status !== 'all') {
          cryptoQuery = cryptoQuery.eq('status', filters.status)
        }

        const { data: cryptoData, error: cryptoError } = await cryptoQuery

        if (cryptoError) {
          console.error('Error loading crypto transactions:', cryptoError)
        } else if (cryptoData) {
          const cryptoTransactions: TransactionData[] = cryptoData.map(tx => ({
            id: tx.id,
            userId: tx.user_id,
            userFullName: tx.users.full_name || tx.users.username,
            userEmail: tx.users.email,
            aureusId: tx.users.username,
            paymentMethod: 'USDT' as const,
            amount: parseFloat(tx.amount.toString()),
            currency: tx.currency || 'USDT',
            shareQuantity: tx.shares_to_purchase || 0,
            transactionHash: tx.transaction_hash,
            proofOfPaymentUrl: tx.screenshot_url,
            walletAddress: tx.sender_wallet,
            network: tx.network,
            status: tx.status,
            createdAt: tx.created_at,
            updatedAt: tx.updated_at,
            approvedAt: tx.approved_at,
            approvedBy: tx.approved_by_admin_id?.toString(),
            kycStatus: tx.users.kyc_information?.[0]?.kyc_status || 'pending'
          }))

          allTransactions.push(...cryptoTransactions)
        }
      }

      // 2. Load Bank Transfer transactions
      if (filters.paymentMethod === 'all' || filters.paymentMethod === 'BANK_TRANSFER') {
        let bankQuery = supabase
          .from('bank_transfer_payments')
          .select(`
            id,
            user_id,
            usd_amount,
            zar_amount,
            proof_file_id,
            bank_account,
            status,
            created_at,
            updated_at,
            approved_at,
            approved_by_admin_id,
            users!inner(
              id,
              username,
              full_name,
              email,
              kyc_information(kyc_status)
            )
          `)
          .gte('created_at', startDateTime)
          .lte('created_at', endDateTime)

        if (filters.status !== 'all') {
          bankQuery = bankQuery.eq('status', filters.status)
        }

        const { data: bankData, error: bankError } = await bankQuery

        if (bankError) {
          console.error('Error loading bank transfer transactions:', bankError)
        } else if (bankData) {
          const bankTransactions: TransactionData[] = bankData.map(tx => ({
            id: tx.id,
            userId: tx.user_id,
            userFullName: tx.users.full_name || tx.users.username,
            userEmail: tx.users.email,
            aureusId: tx.users.username,
            paymentMethod: 'BANK_TRANSFER' as const,
            amount: parseFloat(tx.usd_amount?.toString() || '0'),
            currency: 'USD',
            shareQuantity: 0, // Calculate from amount and current phase price
            proofOfPaymentUrl: tx.proof_file_id,
            bankReference: tx.bank_account,
            status: tx.status,
            createdAt: tx.created_at,
            updatedAt: tx.updated_at,
            approvedAt: tx.approved_at,
            approvedBy: tx.approved_by_admin_id?.toString(),
            kycStatus: tx.users.kyc_information?.[0]?.kyc_status || 'pending'
          }))

          allTransactions.push(...bankTransactions)
        }
      }

      // Apply additional filters
      let filteredTransactions = allTransactions

      // User search filter
      if (filters.userSearch.trim()) {
        const searchTerm = filters.userSearch.toLowerCase()
        filteredTransactions = filteredTransactions.filter(tx =>
          tx.userFullName.toLowerCase().includes(searchTerm) ||
          tx.userEmail.toLowerCase().includes(searchTerm) ||
          tx.aureusId.toLowerCase().includes(searchTerm)
        )
      }

      // Amount range filter
      filteredTransactions = filteredTransactions.filter(tx =>
        tx.amount >= filters.amountRange.min && tx.amount <= filters.amountRange.max
      )

      // Transaction search filter (hash/reference)
      if (filters.transactionSearch.trim()) {
        const searchTerm = filters.transactionSearch.toLowerCase()
        filteredTransactions = filteredTransactions.filter(tx =>
          tx.transactionHash?.toLowerCase().includes(searchTerm) ||
          tx.bankReference?.toLowerCase().includes(searchTerm) ||
          tx.id.toLowerCase().includes(searchTerm)
        )
      }

      // KYC status filter
      if (filters.kycStatus !== 'all') {
        filteredTransactions = filteredTransactions.filter(tx =>
          tx.kycStatus === filters.kycStatus
        )
      }

      setTransactions(filteredTransactions)
      console.log(`✅ Loaded ${filteredTransactions.length} transactions`)

    } catch (error) {
      console.error('❌ Error loading transactions:', error)
    } finally {
      setLoading(false)
    }
  }

  // Sort and paginate transactions
  const sortedAndPaginatedTransactions = useMemo(() => {
    let sorted = [...transactions]

    // Apply sorting
    sorted.sort((a, b) => {
      const aValue = a[sortField]
      const bValue = b[sortField]

      if (aValue === undefined || aValue === null) return 1
      if (bValue === undefined || bValue === null) return -1

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue)
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue
      }

      return 0
    })

    // Apply pagination
    const startIndex = (currentPage - 1) * pageSize
    const endIndex = pageSize === -1 ? sorted.length : startIndex + pageSize

    return {
      transactions: sorted.slice(startIndex, endIndex),
      totalCount: sorted.length,
      totalPages: pageSize === -1 ? 1 : Math.ceil(sorted.length / pageSize)
    }
  }, [transactions, sortField, sortDirection, currentPage, pageSize])

  // Load transactions on component mount and filter changes
  useEffect(() => {
    loadTransactions()
  }, [filters])

  // Handle sorting
  const handleSort = (field: keyof TransactionData) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('desc')
    }
  }

  // Export transactions to Excel
  const exportTransactions = async () => {
    setExporting(true)
    try {
      console.log('📊 Exporting transactions to Excel...')

      const exportData = transactions.map(tx => ({
        'Transaction ID': tx.id,
        'Date': new Date(tx.createdAt).toLocaleDateString(),
        'Time': new Date(tx.createdAt).toLocaleTimeString(),
        'User Full Name': tx.userFullName,
        'User Email': tx.userEmail,
        'Aureus ID': tx.aureusId,
        'Payment Method': tx.paymentMethod,
        'Amount': tx.amount,
        'Currency': tx.currency,
        'Share Quantity': tx.shareQuantity,
        'Status': tx.status,
        'KYC Status': tx.kycStatus,
        'Transaction Hash': tx.transactionHash || '',
        'Wallet Address': tx.walletAddress || '',
        'Bank Reference': tx.bankReference || '',
        'Network': tx.network || '',
        'Proof of Payment': tx.proofOfPaymentUrl || '',
        'Approved At': tx.approvedAt ? new Date(tx.approvedAt).toLocaleString() : '',
        'Approved By': tx.approvedBy || '',
        'Created At': new Date(tx.createdAt).toLocaleString(),
        'Updated At': new Date(tx.updatedAt).toLocaleString()
      }))

      const workbook = XLSX.utils.book_new()
      const worksheet = XLSX.utils.json_to_sheet(exportData)
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Transactions')

      const filename = `transaction-report-${new Date().toISOString().split('T')[0]}.xlsx`
      XLSX.writeFile(workbook, filename)

      // Log admin action
      await logAdminAction(
        currentUser?.adminUser?.id || 'unknown',
        'transaction_export',
        `Exported ${transactions.length} transactions`,
        { filters, filename, transactionCount: transactions.length }
      )

      console.log('✅ Transaction export completed:', filename)
      alert(`Export completed! File: ${filename}`)

    } catch (error) {
      console.error('❌ Error exporting transactions:', error)
      alert('Failed to export transactions. Please try again.')
    } finally {
      setExporting(false)
    }
  }

  return (
    <div className="transaction-management-system">
      <div className="header-section">
        <div className="title-section">
          <h2>🔄 Transaction Management System</h2>
          <p>Comprehensive transaction monitoring with automated email notifications</p>
        </div>

        <div className="action-buttons">
          <button
            onClick={exportTransactions}
            disabled={exporting || transactions.length === 0}
            className="btn btn-primary"
          >
            {exporting ? '📊 Exporting...' : '📊 Export to Excel'}
          </button>
          <button
            onClick={loadTransactions}
            disabled={loading}
            className="btn btn-secondary"
          >
            {loading ? '🔄 Loading...' : '🔄 Refresh'}
          </button>
        </div>
      </div>

      {/* Advanced Filter Panel */}
      <div className="filter-panel">
        <div className="filter-section">
          <h3>🔍 Advanced Filters</h3>

          <div className="filter-grid">
            {/* Payment Method Filter */}
            <div className="filter-item">
              <label>Payment Method</label>
              <select
                value={filters.paymentMethod}
                onChange={(e) => setFilters(prev => ({
                  ...prev,
                  paymentMethod: e.target.value as FilterState['paymentMethod']
                }))}
                className="filter-select"
              >
                <option value="all">All Methods</option>
                <option value="USDT">USDT Payments</option>
                <option value="BANK_TRANSFER">Bank Transfers</option>
              </select>
            </div>

            {/* Date Range Filter */}
            <div className="filter-item">
              <label>From Date</label>
              <input
                type="date"
                value={filters.dateRange.from}
                onChange={(e) => setFilters(prev => ({
                  ...prev,
                  dateRange: { ...prev.dateRange, from: e.target.value }
                }))}
                className="filter-input"
              />
            </div>

            <div className="filter-item">
              <label>To Date</label>
              <input
                type="date"
                value={filters.dateRange.to}
                onChange={(e) => setFilters(prev => ({
                  ...prev,
                  dateRange: { ...prev.dateRange, to: e.target.value }
                }))}
                className="filter-input"
              />
            </div>

            {/* Status Filter */}
            <div className="filter-item">
              <label>Transaction Status</label>
              <select
                value={filters.status}
                onChange={(e) => setFilters(prev => ({
                  ...prev,
                  status: e.target.value as FilterState['status']
                }))}
                className="filter-select"
              >
                <option value="all">All Statuses</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>

            {/* User Search */}
            <div className="filter-item">
              <label>User Search</label>
              <input
                type="text"
                placeholder="Name, email, or Aureus ID..."
                value={filters.userSearch}
                onChange={(e) => setFilters(prev => ({
                  ...prev,
                  userSearch: e.target.value
                }))}
                className="filter-input"
              />
            </div>

            {/* Transaction Search */}
            <div className="filter-item">
              <label>Transaction Search</label>
              <input
                type="text"
                placeholder="Hash, reference, or ID..."
                value={filters.transactionSearch}
                onChange={(e) => setFilters(prev => ({
                  ...prev,
                  transactionSearch: e.target.value
                }))}
                className="filter-input"
              />
            </div>

            {/* Amount Range */}
            <div className="filter-item">
              <label>Min Amount ($)</label>
              <input
                type="number"
                min="0"
                value={filters.amountRange.min}
                onChange={(e) => setFilters(prev => ({
                  ...prev,
                  amountRange: { ...prev.amountRange, min: parseFloat(e.target.value) || 0 }
                }))}
                className="filter-input"
              />
            </div>

            <div className="filter-item">
              <label>Max Amount ($)</label>
              <input
                type="number"
                min="0"
                value={filters.amountRange.max}
                onChange={(e) => setFilters(prev => ({
                  ...prev,
                  amountRange: { ...prev.amountRange, max: parseFloat(e.target.value) || 100000 }
                }))}
                className="filter-input"
              />
            </div>

            {/* KYC Status Filter */}
            <div className="filter-item">
              <label>KYC Status</label>
              <select
                value={filters.kycStatus}
                onChange={(e) => setFilters(prev => ({
                  ...prev,
                  kycStatus: e.target.value as FilterState['kycStatus']
                }))}
                className="filter-select"
              >
                <option value="all">All KYC Statuses</option>
                <option value="approved">KYC Approved</option>
                <option value="pending">KYC Pending</option>
                <option value="rejected">KYC Rejected</option>
              </select>
            </div>
          </div>

          <div className="filter-summary">
            <span>Showing {sortedAndPaginatedTransactions.totalCount} transactions</span>
            {filters.paymentMethod !== 'all' && (
              <span className="filter-tag">{filters.paymentMethod}</span>
            )}
            {filters.status !== 'all' && (
              <span className="filter-tag">{filters.status}</span>
            )}
            {filters.userSearch && (
              <span className="filter-tag">User: {filters.userSearch}</span>
            )}
            {filters.transactionSearch && (
              <span className="filter-tag">TX: {filters.transactionSearch}</span>
            )}
          </div>
        </div>
      </div>

      {/* Pagination Controls */}
      <div className="pagination-controls">
        <div className="page-size-selector">
          <label>Show:</label>
          <select
            value={pageSize}
            onChange={(e) => {
              setPageSize(parseInt(e.target.value))
              setCurrentPage(1)
            }}
            className="page-size-select"
          >
            <option value={25}>25</option>
            <option value={50}>50</option>
            <option value={100}>100</option>
            <option value={-1}>All</option>
          </select>
          <span>per page</span>
        </div>

        {pageSize !== -1 && (
          <div className="pagination-buttons">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="btn btn-sm"
            >
              ← Previous
            </button>

            <span className="page-info">
              Page {currentPage} of {sortedAndPaginatedTransactions.totalPages}
            </span>

            <button
              onClick={() => setCurrentPage(Math.min(sortedAndPaginatedTransactions.totalPages, currentPage + 1))}
              disabled={currentPage === sortedAndPaginatedTransactions.totalPages}
              className="btn btn-sm"
            >
              Next →
            </button>
          </div>
        )}
      </div>

      {/* Transaction Table */}
      <div className="transaction-table-container">
        {loading ? (
          <div className="loading-state">
            <div className="loading-spinner"></div>
            <p>Loading transactions...</p>
          </div>
        ) : sortedAndPaginatedTransactions.transactions.length === 0 ? (
          <div className="empty-state">
            <h3>No transactions found</h3>
            <p>Try adjusting your filters or date range</p>
          </div>
        ) : (
          <div className="table-wrapper">
            <table className="transaction-table">
              <thead>
                <tr>
                  <th onClick={() => handleSort('createdAt')} className="sortable">
                    Date {sortField === 'createdAt' && (sortDirection === 'asc' ? '↑' : '↓')}
                  </th>
                  <th onClick={() => handleSort('userFullName')} className="sortable">
                    User {sortField === 'userFullName' && (sortDirection === 'asc' ? '↑' : '↓')}
                  </th>
                  <th onClick={() => handleSort('paymentMethod')} className="sortable">
                    Method {sortField === 'paymentMethod' && (sortDirection === 'asc' ? '↑' : '↓')}
                  </th>
                  <th onClick={() => handleSort('amount')} className="sortable">
                    Amount {sortField === 'amount' && (sortDirection === 'asc' ? '↑' : '↓')}
                  </th>
                  <th onClick={() => handleSort('shareQuantity')} className="sortable">
                    Shares {sortField === 'shareQuantity' && (sortDirection === 'asc' ? '↑' : '↓')}
                  </th>
                  <th onClick={() => handleSort('status')} className="sortable">
                    Status {sortField === 'status' && (sortDirection === 'asc' ? '↑' : '↓')}
                  </th>
                  <th onClick={() => handleSort('kycStatus')} className="sortable">
                    KYC {sortField === 'kycStatus' && (sortDirection === 'asc' ? '↑' : '↓')}
                  </th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {sortedAndPaginatedTransactions.transactions.map((transaction) => (
                  <tr key={transaction.id} className="transaction-row">
                    <td className="date-cell">
                      <div className="date-info">
                        <span className="date">{new Date(transaction.createdAt).toLocaleDateString()}</span>
                        <span className="time">{new Date(transaction.createdAt).toLocaleTimeString()}</span>
                      </div>
                    </td>

                    <td className="user-cell">
                      <div className="user-info">
                        <span className="name">{transaction.userFullName}</span>
                        <span className="email">{transaction.userEmail}</span>
                        <span className="aureus-id">ID: {transaction.aureusId}</span>
                      </div>
                    </td>

                    <td className="method-cell">
                      <span className={`payment-method-badge ${transaction.paymentMethod.toLowerCase()}`}>
                        {transaction.paymentMethod === 'USDT' ? '₮ USDT' : '🏦 Bank'}
                      </span>
                      {transaction.network && (
                        <span className="network-info">{transaction.network}</span>
                      )}
                    </td>

                    <td className="amount-cell">
                      <div className="amount-info">
                        <span className="amount">${transaction.amount.toLocaleString()}</span>
                        <span className="currency">{transaction.currency}</span>
                      </div>
                    </td>

                    <td className="shares-cell">
                      <span className="shares">{transaction.shareQuantity.toLocaleString()}</span>
                    </td>

                    <td className="status-cell">
                      <span className={`status-badge ${transaction.status.toLowerCase()}`}>
                        {transaction.status}
                      </span>
                    </td>

                    <td className="kyc-cell">
                      <span className={`kyc-badge ${transaction.kycStatus?.toLowerCase() || 'unknown'}`}>
                        {transaction.kycStatus || 'Unknown'}
                      </span>
                    </td>

                    <td className="actions-cell">
                      <button
                        onClick={() => {
                          setSelectedTransaction(transaction)
                          setShowModal(true)
                        }}
                        className="btn btn-sm btn-primary"
                      >
                        View Details
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Transaction Detail Modal */}
      {showModal && selectedTransaction && (
        <div className="modal-overlay" onClick={() => setShowModal(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>Transaction Details</h3>
              <button
                onClick={() => setShowModal(false)}
                className="modal-close"
              >
                ×
              </button>
            </div>

            <div className="modal-body">
              <div className="transaction-details">
                <div className="detail-section">
                  <h4>Transaction Information</h4>
                  <div className="detail-grid">
                    <div className="detail-item">
                      <label>Transaction ID</label>
                      <span>{selectedTransaction.id}</span>
                    </div>
                    <div className="detail-item">
                      <label>Payment Method</label>
                      <span className={`payment-method-badge ${selectedTransaction.paymentMethod.toLowerCase()}`}>
                        {selectedTransaction.paymentMethod}
                      </span>
                    </div>
                    <div className="detail-item">
                      <label>Amount</label>
                      <span>${selectedTransaction.amount.toLocaleString()} {selectedTransaction.currency}</span>
                    </div>
                    <div className="detail-item">
                      <label>Share Quantity</label>
                      <span>{selectedTransaction.shareQuantity.toLocaleString()}</span>
                    </div>
                    <div className="detail-item">
                      <label>Status</label>
                      <span className={`status-badge ${selectedTransaction.status.toLowerCase()}`}>
                        {selectedTransaction.status}
                      </span>
                    </div>
                    <div className="detail-item">
                      <label>Created At</label>
                      <span>{new Date(selectedTransaction.createdAt).toLocaleString()}</span>
                    </div>
                  </div>
                </div>

                <div className="detail-section">
                  <h4>User Information</h4>
                  <div className="detail-grid">
                    <div className="detail-item">
                      <label>Full Name</label>
                      <span>{selectedTransaction.userFullName}</span>
                    </div>
                    <div className="detail-item">
                      <label>Email Address</label>
                      <span>{selectedTransaction.userEmail}</span>
                    </div>
                    <div className="detail-item">
                      <label>Aureus ID</label>
                      <span>{selectedTransaction.aureusId}</span>
                    </div>
                    <div className="detail-item">
                      <label>KYC Status</label>
                      <span className={`kyc-badge ${selectedTransaction.kycStatus?.toLowerCase() || 'unknown'}`}>
                        {selectedTransaction.kycStatus || 'Unknown'}
                      </span>
                    </div>
                  </div>
                </div>

                {selectedTransaction.paymentMethod === 'USDT' && (
                  <div className="detail-section">
                    <h4>USDT Payment Details</h4>
                    <div className="detail-grid">
                      <div className="detail-item">
                        <label>Network</label>
                        <span>{selectedTransaction.network || 'Not specified'}</span>
                      </div>
                      <div className="detail-item">
                        <label>Transaction Hash</label>
                        <span className="hash-text">{selectedTransaction.transactionHash || 'Pending'}</span>
                      </div>
                      <div className="detail-item">
                        <label>Wallet Address</label>
                        <span className="address-text">{selectedTransaction.walletAddress || 'Not provided'}</span>
                      </div>
                      <div className="detail-item">
                        <label>Proof of Payment</label>
                        {selectedTransaction.proofOfPaymentUrl ? (
                          <a
                            href={selectedTransaction.proofOfPaymentUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="proof-link"
                          >
                            View Screenshot
                          </a>
                        ) : (
                          <span>Not provided</span>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {selectedTransaction.paymentMethod === 'BANK_TRANSFER' && (
                  <div className="detail-section">
                    <h4>Bank Transfer Details</h4>
                    <div className="detail-grid">
                      <div className="detail-item">
                        <label>Bank Reference</label>
                        <span>{selectedTransaction.bankReference || 'Not provided'}</span>
                      </div>
                      <div className="detail-item">
                        <label>Currency</label>
                        <span>{selectedTransaction.currency}</span>
                      </div>
                      <div className="detail-item">
                        <label>Proof of Payment</label>
                        {selectedTransaction.proofOfPaymentUrl ? (
                          <a
                            href={selectedTransaction.proofOfPaymentUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="proof-link"
                          >
                            View Bank Transfer Proof
                          </a>
                        ) : (
                          <span>Not provided</span>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {selectedTransaction.approvedAt && (
                  <div className="detail-section">
                    <h4>Approval Information</h4>
                    <div className="detail-grid">
                      <div className="detail-item">
                        <label>Approved At</label>
                        <span>{new Date(selectedTransaction.approvedAt).toLocaleString()}</span>
                      </div>
                      <div className="detail-item">
                        <label>Approved By</label>
                        <span>{selectedTransaction.approvedBy || 'System'}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="modal-footer">
              <button
                onClick={() => setShowModal(false)}
                className="btn btn-secondary"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
