import React, { useState } from 'react'
import { signInWithEmail, signUpWithEmail } from '../lib/supabase'
import { checkAdminStatus, logAdminAction } from '../lib/adminAuth'
import { AdminPinVerificationModal } from './AdminPinVerificationModal'

interface AdminLoginProps {
  onLoginSuccess: (user: any) => void
}

export const AdminLogin: React.FC<AdminLoginProps> = ({ onLoginSuccess }) => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [showSignup, setShowSignup] = useState(false)
  const [showPassword, setShowPassword] = useState(true)

  // Two-factor authentication states
  const [showPinModal, setShowPinModal] = useState(false)
  const [pendingUser, setPendingUser] = useState<any>(null)
  const [pendingAdminUser, setPendingAdminUser] = useState<any>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    // Log login attempt
    const loginAttemptTime = new Date().toISOString()
    const clientInfo = {
      userAgent: navigator.userAgent,
      timestamp: loginAttemptTime,
      ipAddress: 'client-side', // Will be logged server-side with actual IP
      email: email
    }

    try {
      console.log('🔐 Admin login attempt for:', email)

      const { user, error } = showSignup
        ? await signUpWithEmail(email, password)
        : await signInWithEmail(email, password)

      if (error) {
        console.log('❌ Admin login failed - authentication error:', error.message)

        // Log failed login attempt
        await logAdminAction(
          email,
          'admin_login_failed',
          'authentication',
          'login_attempt',
          {
            ...clientInfo,
            error: error.message,
            reason: 'invalid_credentials'
          }
        )

        setError(error.message)
      } else if (user) {
        if (showSignup) {
          console.log('✅ Admin account created:', user.email)

          // Log account creation
          await logAdminAction(
            user.email,
            'admin_account_created',
            'user',
            user.id,
            clientInfo
          )

          setError('')
          alert('Account created! You can now login.')
          setShowSignup(false)
        } else {
          // 🔒 CRITICAL SECURITY CHECK: Verify user is actually an admin
          console.log('🔍 Checking admin status for:', user.email)
          const adminUser = await checkAdminStatus(user.email)

          if (!adminUser) {
            console.log('❌ Access denied: User is not an admin:', user.email)

            // Log unauthorized access attempt
            await logAdminAction(
              user.email,
              'admin_access_denied',
              'security',
              user.id,
              {
                ...clientInfo,
                reason: 'insufficient_privileges',
                attempted_role: 'admin'
              }
            )

            setError('Access denied. You do not have admin privileges.')
            return
          }

          console.log('✅ Admin credentials verified, initiating two-factor authentication...')

          // Log successful credential verification
          await logAdminAction(
            user.email,
            'admin_credentials_verified',
            'authentication',
            user.id,
            {
              ...clientInfo,
              admin_role: adminUser.role,
              next_step: 'two_factor_authentication'
            }
          )

          // Store pending user data and show PIN modal
          setPendingUser(user)
          setPendingAdminUser(adminUser)
          setShowPinModal(true)

          // Clear form but keep loading state until PIN is verified
          setPassword('')
        }
      }
    } catch (err) {
      console.error('❌ Admin login error:', err)

      // Log unexpected error
      await logAdminAction(
        email,
        'admin_login_error',
        'system',
        'error',
        {
          ...clientInfo,
          error: err instanceof Error ? err.message : 'Unknown error',
          stack: err instanceof Error ? err.stack : undefined
        }
      )

      setError('An unexpected error occurred')
      setLoading(false)
    }
  }

  // Handle successful PIN verification
  const handlePinVerified = async () => {
    console.log('🎉 Two-factor authentication completed successfully')

    if (pendingUser && pendingAdminUser) {
      // Log successful admin login
      await logAdminAction(
        pendingAdminUser.email,
        'admin_login_success',
        'authentication',
        pendingUser.id,
        {
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString(),
          admin_role: pendingAdminUser.role,
          two_factor_completed: true,
          login_method: 'email_password_pin'
        }
      )

      console.log('✅ Admin access granted:', pendingAdminUser.email, pendingAdminUser.role)
      onLoginSuccess({ ...pendingUser, adminUser: pendingAdminUser })
    }

    setShowPinModal(false)
    setLoading(false)

    // Clear pending data
    setPendingUser(null)
    setPendingAdminUser(null)
  }

  // Handle PIN modal close (user cancelled)
  const handlePinModalClose = async () => {
    console.log('🚫 Two-factor authentication cancelled')

    if (pendingUser && pendingAdminUser) {
      // Log cancelled two-factor authentication
      await logAdminAction(
        pendingAdminUser.email,
        'admin_2fa_cancelled',
        'authentication',
        pendingUser.id,
        {
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString(),
          admin_role: pendingAdminUser.role,
          reason: 'user_cancelled'
        }
      )
    }

    setShowPinModal(false)
    setLoading(false)
    setError('Two-factor authentication is required for admin access')

    // Clear pending data
    setPendingUser(null)
    setPendingAdminUser(null)
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-black to-gray-900 p-4">
      <div className="glass-card w-full max-w-lg">
        {/* Header */}
        <div className="text-center mb-10">
          <div className="mb-4">
            <h1 className="text-4xl font-bold text-gradient-gold mb-2">
              🏆 AUREUS
            </h1>
            <p className="text-xl text-gray-300 font-medium">Alliance Holdings</p>
          </div>
          <div className="h-px bg-gradient-to-r from-transparent via-yellow-500/50 to-transparent mb-4"></div>
          <p className="text-gray-400">Admin Portal</p>
        </div>



        {/* Login Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-5">
            <div>
              <label htmlFor="email" className="block text-sm font-semibold text-gray-300 mb-3">
                Email Address
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="w-full px-4 py-4 bg-gray-800/60 border border-gray-600/50 rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200"
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-semibold text-gray-300 mb-3">
                Password
              </label>
              <div className="relative">
                <input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="w-full px-4 py-4 pr-12 bg-gray-800/60 border border-gray-600/50 rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200"
                  placeholder="••••••••"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200"
                >
                  {showPassword ? (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                    </svg>
                  ) : (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
                      <circle cx="12" cy="12" r="3" />
                    </svg>
                  )}
                </button>
              </div>
            </div>
          </div>

          {error && (
            <div className="p-4 bg-red-900/30 border border-red-500/50 rounded-xl text-red-300 text-sm backdrop-blur-sm">
              <div className="flex items-center gap-2">
                <span className="text-red-400">⚠️</span>
                {error}
              </div>
            </div>
          )}

          <button
            type="submit"
            disabled={loading}
            className="w-full py-4 px-6 bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-black font-bold rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-lg shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]"
          >
            {loading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="w-5 h-5 border-2 border-black/30 border-t-black rounded-full animate-spin"></div>
                {showSignup ? 'Creating Account...' : showPinModal ? 'Awaiting 2FA...' : 'Signing In...'}
              </div>
            ) : (
              showSignup ? 'Create Account' : 'Sign In'
            )}
          </button>
        </form>

        {/* Footer */}
        <div className="mt-8 text-center space-y-4">
          <div className="h-px bg-gradient-to-r from-transparent via-gray-600/50 to-transparent"></div>

          <button
            type="button"
            onClick={() => setShowSignup(!showSignup)}
            className="text-yellow-400 hover:text-yellow-300 text-sm font-medium transition-colors duration-200"
          >
            {showSignup ? 'Already have an account? Sign In' : 'Need to create an account? Sign Up'}
          </button>

          <div className="flex items-center justify-center gap-2 text-xs text-gray-500">
            <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
            <span>Authorized personnel only</span>
          </div>
        </div>
      </div>

      {/* Two-Factor Authentication Modal */}
      <AdminPinVerificationModal
        isOpen={showPinModal}
        onClose={handlePinModalClose}
        onVerified={handlePinVerified}
        adminEmail={pendingAdminUser?.email || email}
        title="🔐 Admin Two-Factor Authentication"
        description="Admin access requires two-factor authentication. A security PIN has been sent to the admin email address."
      />
    </div>
  )
}
