/**
 * KYC STATUS EMAIL TEMPLATES
 * 
 * Email templates for KYC approval and rejection notifications
 * using the existing email template system.
 */

import { BaseEmailTemplate } from './BaseEmailTemplate';

export interface KYCStatusEmailData {
  fullName: string;
  email: string;
  username: string;
  status: 'approved' | 'rejected';
  rejectionReason?: string;
  comments?: string;
  submissionDate: string;
  reviewDate: string;
  nextSteps?: string;
}

export class KYCStatusEmailTemplate extends BaseEmailTemplate<KYCStatusEmailData> {
  protected emailType = 'kyc_status' as const;

  protected generateSubject(data: KYCStatusEmailData): string {
    const statusText = data.status === 'approved' ? 'Approved' : 'Requires Attention';
    return `KYC Verification ${statusText} - ${this.brandingConfig.companyName}`;
  }

  protected generateBody(data: KYCStatusEmailData): string {
    if (data.status === 'approved') {
      return this.generateApprovedBody(data);
    } else {
      return this.generateRejectedBody(data);
    }
  }

  private generateApprovedBody(data: KYCStatusEmailData): string {
    return `
      <div class="email-body">
        <div style="text-align: center; margin-bottom: 30px;">
          <div style="background: linear-gradient(135deg, #10B981, #059669); color: white; padding: 20px; border-radius: 12px; display: inline-block;">
            <h2 style="margin: 0; font-size: 24px; font-weight: bold;">
              ✅ KYC Verification Approved!
            </h2>
          </div>
        </div>
        
        <p style="margin-bottom: 20px; color: ${this.brandingConfig.colors.text}; font-size: 18px;">
          Hello ${data.fullName},
        </p>
        
        <p style="margin-bottom: 20px; color: ${this.brandingConfig.colors.text};">
          Congratulations! Your KYC (Know Your Customer) verification has been successfully approved. 
          Your account is now fully verified and you have access to all platform features.
        </p>

        <div style="background: #F0FDF4; border: 1px solid #BBF7D0; border-radius: 8px; padding: 20px; margin: 25px 0;">
          <h3 style="color: #059669; margin: 0 0 15px 0; font-size: 18px;">
            📋 Verification Details
          </h3>
          <div style="color: #374151;">
            <p style="margin: 8px 0;"><strong>Account:</strong> ${data.username}</p>
            <p style="margin: 8px 0;"><strong>Submitted:</strong> ${this.formatDate(new Date(data.submissionDate))}</p>
            <p style="margin: 8px 0;"><strong>Approved:</strong> ${this.formatDate(new Date(data.reviewDate))}</p>
            <p style="margin: 8px 0;"><strong>Status:</strong> <span style="color: #059669; font-weight: bold;">✅ Verified</span></p>
          </div>
        </div>

        <div style="background: #FEF3C7; border: 1px solid #FCD34D; border-radius: 8px; padding: 20px; margin: 25px 0;">
          <h3 style="color: #D97706; margin: 0 0 15px 0; font-size: 18px;">
            🎉 What's Next?
          </h3>
          <ul style="color: #374151; margin: 0; padding-left: 20px;">
            <li style="margin-bottom: 10px;">Access all investment phases and purchase shares</li>
            <li style="margin-bottom: 10px;">Withdraw commission earnings to your wallet</li>
            <li style="margin-bottom: 10px;">Receive dividend payments when eligible</li>
            <li style="margin-bottom: 10px;">Request and receive your official share certificates</li>
            <li style="margin-bottom: 10px;">Participate in the Gold Diggers Club competitions</li>
          </ul>
        </div>

        ${data.comments ? `
        <div style="background: #F3F4F6; border-radius: 8px; padding: 20px; margin: 25px 0;">
          <h3 style="color: ${this.brandingConfig.colors.primary}; margin: 0 0 15px 0; font-size: 18px;">
            💬 Additional Notes
          </h3>
          <p style="color: #374151; margin: 0; font-style: italic;">
            "${data.comments}"
          </p>
        </div>
        ` : ''}

        <div style="text-align: center; margin: 30px 0;">
          <a href="${this.brandingConfig.websiteUrl}/dashboard" 
             style="background: ${this.brandingConfig.colors.primary}; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">
            Access Your Dashboard
          </a>
        </div>

        <div style="background: #F9FAFB; border-radius: 8px; padding: 20px; margin: 25px 0; text-align: center;">
          <p style="color: #6B7280; margin: 0; font-size: 14px;">
            Thank you for choosing ${this.brandingConfig.companyName}. 
            We're excited to have you as a verified member of our community!
          </p>
        </div>
      </div>
    `;
  }

  private generateRejectedBody(data: KYCStatusEmailData): string {
    return `
      <div class="email-body">
        <div style="text-align: center; margin-bottom: 30px;">
          <div style="background: linear-gradient(135deg, #EF4444, #DC2626); color: white; padding: 20px; border-radius: 12px; display: inline-block;">
            <h2 style="margin: 0; font-size: 24px; font-weight: bold;">
              ⚠️ KYC Verification Requires Attention
            </h2>
          </div>
        </div>
        
        <p style="margin-bottom: 20px; color: ${this.brandingConfig.colors.text}; font-size: 18px;">
          Hello ${data.fullName},
        </p>
        
        <p style="margin-bottom: 20px; color: ${this.brandingConfig.colors.text};">
          Thank you for submitting your KYC (Know Your Customer) verification. 
          Unfortunately, we need additional information or corrections before we can approve your verification.
        </p>

        <div style="background: #FEF2F2; border: 1px solid #FECACA; border-radius: 8px; padding: 20px; margin: 25px 0;">
          <h3 style="color: #DC2626; margin: 0 0 15px 0; font-size: 18px;">
            📋 Verification Details
          </h3>
          <div style="color: #374151;">
            <p style="margin: 8px 0;"><strong>Account:</strong> ${data.username}</p>
            <p style="margin: 8px 0;"><strong>Submitted:</strong> ${this.formatDate(new Date(data.submissionDate))}</p>
            <p style="margin: 8px 0;"><strong>Reviewed:</strong> ${this.formatDate(new Date(data.reviewDate))}</p>
            <p style="margin: 8px 0;"><strong>Status:</strong> <span style="color: #DC2626; font-weight: bold;">⚠️ Requires Attention</span></p>
          </div>
        </div>

        ${data.rejectionReason ? `
        <div style="background: #FEF2F2; border: 1px solid #FECACA; border-radius: 8px; padding: 20px; margin: 25px 0;">
          <h3 style="color: #DC2626; margin: 0 0 15px 0; font-size: 18px;">
            🔍 Issues Identified
          </h3>
          <p style="color: #374151; margin: 0;">
            ${data.rejectionReason}
          </p>
        </div>
        ` : ''}

        ${data.comments ? `
        <div style="background: #F3F4F6; border-radius: 8px; padding: 20px; margin: 25px 0;">
          <h3 style="color: ${this.brandingConfig.colors.primary}; margin: 0 0 15px 0; font-size: 18px;">
            💬 Additional Notes
          </h3>
          <p style="color: #374151; margin: 0; font-style: italic;">
            "${data.comments}"
          </p>
        </div>
        ` : ''}

        <div style="background: #FEF3C7; border: 1px solid #FCD34D; border-radius: 8px; padding: 20px; margin: 25px 0;">
          <h3 style="color: #D97706; margin: 0 0 15px 0; font-size: 18px;">
            🔄 Next Steps
          </h3>
          <ol style="color: #374151; margin: 0; padding-left: 20px;">
            <li style="margin-bottom: 10px;">Review the issues identified above</li>
            <li style="margin-bottom: 10px;">Prepare corrected or additional documentation</li>
            <li style="margin-bottom: 10px;">Submit a new KYC verification through your dashboard</li>
            <li style="margin-bottom: 10px;">Contact support if you need assistance</li>
          </ol>
        </div>

        <div style="text-align: center; margin: 30px 0;">
          <a href="${this.brandingConfig.websiteUrl}/dashboard?tab=kyc" 
             style="background: ${this.brandingConfig.colors.primary}; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block; margin-right: 15px;">
            Resubmit KYC
          </a>
          <a href="${this.brandingConfig.websiteUrl}/support" 
             style="background: #6B7280; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">
            Contact Support
          </a>
        </div>

        <div style="background: #F9FAFB; border-radius: 8px; padding: 20px; margin: 25px 0; text-align: center;">
          <p style="color: #6B7280; margin: 0; font-size: 14px;">
            We're here to help you complete your verification. 
            Please don't hesitate to reach out if you have any questions.
          </p>
        </div>
      </div>
    `;
  }

  protected generateTextContent(data: KYCStatusEmailData): string {
    if (data.status === 'approved') {
      return this.generateApprovedTextContent(data);
    } else {
      return this.generateRejectedTextContent(data);
    }
  }

  private generateApprovedTextContent(data: KYCStatusEmailData): string {
    return `
KYC Verification Approved - ${this.brandingConfig.companyName}

Hello ${data.fullName},

Congratulations! Your KYC (Know Your Customer) verification has been successfully approved. Your account is now fully verified and you have access to all platform features.

Verification Details:
- Account: ${data.username}
- Submitted: ${this.formatDate(new Date(data.submissionDate))}
- Approved: ${this.formatDate(new Date(data.reviewDate))}
- Status: ✅ Verified

What's Next?
• Access all investment phases and purchase shares
• Withdraw commission earnings to your wallet
• Receive dividend payments when eligible
• Request and receive your official share certificates
• Participate in the Gold Diggers Club competitions

${data.comments ? `Additional Notes: "${data.comments}"` : ''}

Access your dashboard: ${this.brandingConfig.websiteUrl}/dashboard

Thank you for choosing ${this.brandingConfig.companyName}. We're excited to have you as a verified member of our community!

${this.brandingConfig.tagline}
${this.brandingConfig.companyName}
${this.brandingConfig.subtitle}
    `.trim();
  }

  private generateRejectedTextContent(data: KYCStatusEmailData): string {
    return `
KYC Verification Requires Attention - ${this.brandingConfig.companyName}

Hello ${data.fullName},

Thank you for submitting your KYC (Know Your Customer) verification. Unfortunately, we need additional information or corrections before we can approve your verification.

Verification Details:
- Account: ${data.username}
- Submitted: ${this.formatDate(new Date(data.submissionDate))}
- Reviewed: ${this.formatDate(new Date(data.reviewDate))}
- Status: ⚠️ Requires Attention

${data.rejectionReason ? `Issues Identified: ${data.rejectionReason}` : ''}

${data.comments ? `Additional Notes: "${data.comments}"` : ''}

Next Steps:
1. Review the issues identified above
2. Prepare corrected or additional documentation
3. Submit a new KYC verification through your dashboard
4. Contact support if you need assistance

Resubmit KYC: ${this.brandingConfig.websiteUrl}/dashboard?tab=kyc
Contact Support: ${this.brandingConfig.websiteUrl}/support

We're here to help you complete your verification. Please don't hesitate to reach out if you have any questions.

${this.brandingConfig.tagline}
${this.brandingConfig.companyName}
${this.brandingConfig.subtitle}
    `.trim();
  }
}
