import React, { useState } from 'react';
import { DashboardHeader } from './DashboardHeader';
import { DashboardSidebar } from './DashboardSidebar';
import { MobileDashboardNavigation } from './MobileDashboardNavigation';
import { MobileBottomNavigation } from './MobileBottomNavigation';
import { DashboardContent } from './DashboardContent';
import { ImpersonationIndicator } from '../admin/ImpersonationIndicator';

import { DashboardSection } from '../../hooks/useDashboardNavigation.tsx';
import { UserPermissions, ImpersonationSession } from '../../hooks/useUserPermissions';
import { DashboardData } from '../../hooks/useDashboardData';

interface DashboardLayoutProps {
  user: any;
  activeSection: DashboardSection;
  onSectionChange: (section: DashboardSection) => void;
  onLogout: () => void;
  onSwitchDashboard?: (dashboard: 'shareholder' | 'affiliate') => void;
  permissions: UserPermissions;
  impersonationSession: ImpersonationSession;
  dashboardData: DashboardData;
  loading: boolean;
  error: string | null;
  onRefreshData: () => Promise<void>;
}

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  user,
  activeSection,
  onSectionChange,
  onLogout,
  onSwitchDashboard,
  permissions,
  impersonationSession,
  dashboardData,
  loading,
  error,
  onRefreshData
}) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Debug mobile menu state
  React.useEffect(() => {
    console.log('Mobile menu state:', isMobileMenuOpen);
  }, [isMobileMenuOpen]);

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Desktop Sidebar - Hidden on mobile */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <DashboardSidebar
          activeSection={activeSection}
          onSectionChange={onSectionChange}
          permissions={permissions}
          dashboardData={dashboardData}
        />
      </div>

      {/* Mobile Navigation */}
      <MobileDashboardNavigation
        activeSection={activeSection}
        onSectionChange={onSectionChange}
        permissions={permissions}
        dashboardData={dashboardData}
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
      />

      {/* Main Content Area */}
      <div className="lg:pl-64 flex flex-col min-h-screen">
        {/* Impersonation Indicator */}
        {impersonationSession.isActive && (
          <ImpersonationIndicator
            adminUser={impersonationSession.adminUser}
            targetUser={impersonationSession.targetUser}
          />
        )}

        {/* Migration Status Banner removed - migration system simplified */}

        {/* Mobile Header with Menu Toggle */}
        <div className="lg:hidden bg-gray-800 border-b border-gray-700 px-4 py-3 flex items-center justify-between sticky top-0 z-30">
          <button
            onClick={() => {
              console.log('Hamburger button clicked!');
              setIsMobileMenuOpen(true);
            }}
            className="text-white p-3 rounded-md hover:bg-gray-700 active:bg-gray-600 transition-colors touch-manipulation"
            aria-label="Open navigation menu"
            style={{ minHeight: '44px', minWidth: '44px' }}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>

          <div className="flex items-center space-x-3">
            <img
              src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-s.png"
              alt="Aureus Alliance Holdings"
              className="h-8 w-8"
            />
            <div>
              <h1 className="text-white font-bold text-lg">Dashboard</h1>
            </div>
          </div>

          <div className="w-10"></div> {/* Spacer for centering */}
        </div>

        {/* Desktop Header */}
        <div className="hidden lg:block">
          <DashboardHeader
            user={user}
            onLogout={onLogout}
            onSwitchDashboard={onSwitchDashboard}
            dashboardData={dashboardData}
            onRefreshData={onRefreshData}
          />
        </div>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto pb-20 lg:pb-0">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-8">
            {error && (
              <div className="mb-6 bg-red-900/20 border border-red-500/30 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="text-red-400 mr-3">⚠️</div>
                  <div>
                    <h3 className="text-red-400 font-semibold">Error Loading Dashboard</h3>
                    <p className="text-red-300 text-sm mt-1">{error}</p>
                    <button
                      onClick={onRefreshData}
                      className="mt-2 text-red-400 hover:text-red-300 text-sm underline"
                    >
                      Try Again
                    </button>
                  </div>
                </div>
              </div>
            )}

            <DashboardContent
              activeSection={activeSection}
              user={user}
              permissions={permissions}
              dashboardData={dashboardData}
              loading={loading}
              onRefreshData={onRefreshData}
            />
          </div>
        </main>
      </div>

      {/* Mobile Bottom Navigation */}
      <MobileBottomNavigation
        activeSection={activeSection}
        onSectionChange={onSectionChange}
        permissions={permissions}
        dashboardData={dashboardData}
      />
    </div>
  );
};
