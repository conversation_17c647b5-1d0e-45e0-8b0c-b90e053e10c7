import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { validatePasswordStrength } from '../../lib/passwordSecurity';

interface TelegramUser {
  id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  photo_url?: string;
  auth_date: number;
  hash: string;
}

interface SimpleTelegramMigrationProps {
  onMigrationComplete: (userData: any) => void;
  onError: (error: string) => void;
}

interface SimpleTelegramMigrationPropsWithUser extends SimpleTelegramMigrationProps {
  user?: any; // Pre-authenticated user data for mobile flow
}

export const SimpleTelegramMigration: React.FC<SimpleTelegramMigrationPropsWithUser> = ({
  onMigrationComplete,
  onError,
  user
}) => {
  const [step, setStep] = useState<'telegram_auth' | 'password_setup' | 'migrating'>('telegram_auth');
  const [telegramUser, setTelegramUser] = useState<TelegramUser | null>(null);
  const [existingTelegramUser, setExistingTelegramUser] = useState<any>(null);
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const passwordValidation = validatePasswordStrength(password);
  const passwordsMatch = password === confirmPassword;

  // If user data is provided (mobile flow), skip to password setup
  useEffect(() => {
    if (user && user.telegram_id) {
      console.log('📱 Mobile migration flow - user data provided:', user);
      // Convert user data to expected format
      const telegramUserData = {
        id: user.telegram_id,
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        username: user.username || '',
        photo_url: '',
        auth_date: Math.floor(Date.now() / 1000),
        hash: ''
      };

      const existingUserData = {
        user_id: user.id,
        telegram_id: user.telegram_id,
        username: user.username,
        first_name: user.first_name,
        last_name: user.last_name
      };

      setTelegramUser(telegramUserData);
      setExistingTelegramUser(existingUserData);
      setStep('password_setup');
    }
  }, [user]);

  // Initialize Telegram Login Widget
  useEffect(() => {
    if (step === 'telegram_auth' && !user) {
      // Check if device is mobile
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

      if (isMobile) {
        console.log('📱 Mobile device detected - showing mobile instructions');
        setError('Mobile Migration: Please use the "Login with Telegram Bot" option first to authenticate, then return to this page to complete your migration.');
        return;
      }

      // Load Telegram Login Widget script (desktop only)
      const script = document.createElement('script');
      script.src = 'https://telegram.org/js/telegram-widget.js?22';
      script.setAttribute('data-telegram-login', process.env.NEXT_PUBLIC_TELEGRAM_BOT_USERNAME || 'aureus_bot');
      script.setAttribute('data-size', 'large');
      script.setAttribute('data-radius', '8');
      script.setAttribute('data-request-access', 'write');
      script.setAttribute('data-userpic', 'true');
      script.setAttribute('data-onauth', 'onTelegramAuth(user)');

      // Add global callback function
      (window as any).onTelegramAuth = handleTelegramAuth;

      const container = document.getElementById('telegram-login-container');
      if (container) {
        container.innerHTML = '';
        container.appendChild(script);
      }

      return () => {
        // Cleanup
        delete (window as any).onTelegramAuth;
      };
    }
  }, [step, user]);

  const handleTelegramAuth = async (user: TelegramUser) => {
    console.log('🔐 Telegram auth received:', user);
    setIsLoading(true);
    setError(null);

    try {
      // Verify Telegram authentication
      const isValid = await verifyTelegramAuth(user);
      if (!isValid) {
        throw new Error('Telegram authentication verification failed');
      }

      // Check if user exists in telegram_users table
      const { data: existingUser, error: fetchError } = await supabase
        .from('telegram_users')
        .select('*')
        .eq('telegram_id', user.id)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') {
        throw new Error(`Database error: ${fetchError.message}`);
      }

      if (!existingUser) {
        throw new Error('Telegram account not found in our system. Please contact support.');
      }

      // Check if user is already migrated
      if (existingUser.user_id) {
        const { data: webUser, error: webUserError } = await supabase
          .from('users')
          .select('telegram_migrated')
          .eq('id', existingUser.user_id)
          .single();

        if (!webUserError && webUser && webUser.telegram_migrated) {
          throw new Error('This Telegram account has already been migrated. Please use the regular website login with your username and password.');
        }
      }

      setTelegramUser(user);
      setExistingTelegramUser(existingUser);
      setStep('password_setup');

    } catch (error: any) {
      console.error('Telegram auth error:', error);
      setError(error.message || 'Failed to authenticate with Telegram');
      onError(error.message || 'Failed to authenticate with Telegram');
    } finally {
      setIsLoading(false);
    }
  };

  const verifyTelegramAuth = async (user: TelegramUser): Promise<boolean> => {
    try {
      const response = await fetch('/api/auth/verify-telegram', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(user),
      });

      if (!response.ok) {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to verify Telegram authentication');
        return false;
      }

      const result = await response.json();
      return result.valid;
    } catch (error) {
      console.error('Telegram verification error:', error);
      return false;
    }
  };

  const handlePasswordSetup = async () => {
    if (!passwordValidation.valid || !passwordsMatch || !telegramUser || !existingTelegramUser) {
      return;
    }

    setIsLoading(true);
    setError(null);
    setStep('migrating');

    try {
      // Call simplified migration API
      const response = await fetch('/api/auth/simple-migrate-telegram', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          telegramUser,
          existingTelegramUser,
          password
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Migration failed');
      }

      const result = await response.json();
      
      // Migration successful
      onMigrationComplete(result.user);

    } catch (error: any) {
      console.error('Migration error:', error);
      setError(error.message || 'Migration failed');
      setStep('password_setup');
    } finally {
      setIsLoading(false);
    }
  };

  if (step === 'telegram_auth') {
    return (
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold text-center mb-6 text-gray-800">
          Migrate Your Telegram Account
        </h2>
        
        <div className="mb-6">
          <p className="text-gray-600 text-center mb-4">
            To continue using your account, you need to set up website login credentials.
          </p>
          <p className="text-sm text-gray-500 text-center">
            Click the button below to authenticate with your Telegram account, then create a password for website access.
          </p>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        <div id="telegram-login-container" className="flex justify-center mb-4">
          {isLoading && (
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-sm text-gray-500 mt-2">Authenticating...</p>
            </div>
          )}
        </div>

        <div className="text-center text-xs text-gray-500">
          <p>By continuing, you agree to migrate your Telegram account to website access.</p>
        </div>
      </div>
    );
  }

  if (step === 'password_setup') {
    return (
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold text-center mb-6 text-gray-800">
          Set Up Your Password
        </h2>
        
        <div className="mb-6">
          <p className="text-gray-600 text-center mb-2">
            Welcome, {existingTelegramUser?.first_name}!
          </p>
          <p className="text-sm text-gray-500 text-center">
            Username: <strong>{existingTelegramUser?.username}</strong>
          </p>
          <p className="text-sm text-gray-500 text-center mb-4">
            Email: <strong>{existingTelegramUser?.temp_email || 'Not set'}</strong>
          </p>
          <p className="text-sm text-gray-600 text-center">
            Create a password to access your account via the website.
          </p>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              New Password
            </label>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter your new password"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-2 text-gray-500 hover:text-gray-700"
              >
                {showPassword ? '👁️' : '👁️‍🗨️'}
              </button>
            </div>
            
            {password && (
              <div className="mt-2">
                <div className="text-xs space-y-1">
                  <div className={passwordValidation.requirements.minLength ? 'text-green-600' : 'text-red-600'}>
                    ✓ At least 8 characters
                  </div>
                  <div className={passwordValidation.requirements.hasUppercase ? 'text-green-600' : 'text-red-600'}>
                    ✓ One uppercase letter
                  </div>
                  <div className={passwordValidation.requirements.hasLowercase ? 'text-green-600' : 'text-red-600'}>
                    ✓ One lowercase letter
                  </div>
                  <div className={passwordValidation.requirements.hasNumber ? 'text-green-600' : 'text-red-600'}>
                    ✓ One number
                  </div>
                  <div className={passwordValidation.requirements.hasSpecialChar ? 'text-green-600' : 'text-red-600'}>
                    ✓ One special character
                  </div>
                </div>
                <div className="mt-1">
                  <div className="text-xs font-medium">
                    Status: <span className={passwordValidation.valid ? 'text-green-600' : 'text-red-600'}>
                      {passwordValidation.valid ? 'Valid' : 'Invalid'}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Confirm Password
            </label>
            <input
              type={showPassword ? 'text' : 'password'}
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Confirm your password"
            />
            {confirmPassword && !passwordsMatch && (
              <p className="text-red-600 text-xs mt-1">Passwords do not match</p>
            )}
          </div>

          <button
            onClick={handlePasswordSetup}
            disabled={!passwordValidation.valid || !passwordsMatch || isLoading}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Setting up...' : 'Complete Migration'}
          </button>
        </div>
      </div>
    );
  }

  if (step === 'migrating') {
    return (
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-bold text-gray-800 mb-2">Completing Migration...</h2>
          <p className="text-gray-600">Please wait while we set up your website access.</p>
        </div>
      </div>
    );
  }

  return null;
};
