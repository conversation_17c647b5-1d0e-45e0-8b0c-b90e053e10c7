import React, { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';
import { validateKYCForFinancialOperations, KYCValidationResult } from '../../lib/kycValidation';
import { affiliateTransactionService } from '../../lib/services/affiliateTransactionService';
import { affiliateNotificationService } from '../../lib/services/affiliateNotificationService';

interface ShareTransferSystemProps {
  userId: number;
  availableShares: number;
  onTransferComplete: () => void;
}

interface RecipientUser {
  id: number;
  username: string;
  full_name: string;
  email: string;
}

interface TransferPreview {
  shares: number;
  transferFee: number;
  netShares: number;
  recipientReceives: number;
}

export const ShareTransferSystem: React.FC<ShareTransferSystemProps> = ({
  userId,
  availableShares,
  onTransferComplete
}) => {
  const [kycValidation, setKycValidation] = useState<KYCValidationResult | null>(null);
  const [recipientKycValidation, setRecipientKycValidation] = useState<KYCValidationResult | null>(null);
  const [recipientSearch, setRecipientSearch] = useState('');
  const [selectedRecipient, setSelectedRecipient] = useState<RecipientUser | null>(null);
  const [searchResults, setSearchResults] = useState<RecipientUser[]>([]);
  const [shareAmount, setShareAmount] = useState('');
  const [preview, setPreview] = useState<TransferPreview | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [isTransferring, setIsTransferring] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [loading, setLoading] = useState(true);

  // Transfer fee configuration (2% of shares transferred)
  const TRANSFER_FEE_RATE = 0.02;

  // Load sender KYC status
  useEffect(() => {
    const loadSenderKYC = async () => {
      setLoading(true);
      try {
        const validation = await validateKYCForFinancialOperations(userId);
        setKycValidation(validation);
      } catch (error) {
        console.error('Error loading sender KYC:', error);
        setError('Failed to load KYC status. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    loadSenderKYC();
  }, [userId]);

  // Search for recipient users
  useEffect(() => {
    const searchUsers = async () => {
      if (recipientSearch.length < 2) {
        setSearchResults([]);
        return;
      }

      try {
        const { data: users, error } = await supabase
          .from('users')
          .select('id, username, full_name, email')
          .or(`username.ilike.%${recipientSearch}%,email.ilike.%${recipientSearch}%,full_name.ilike.%${recipientSearch}%`)
          .neq('id', userId) // Exclude sender
          .limit(5);

        if (error) {
          console.error('Error searching users:', error);
        } else {
          setSearchResults(users || []);
        }
      } catch (error) {
        console.error('Error in user search:', error);
      }
    };

    const debounceTimer = setTimeout(searchUsers, 300);
    return () => clearTimeout(debounceTimer);
  }, [recipientSearch, userId]);

  // Load recipient KYC when selected
  useEffect(() => {
    const loadRecipientKYC = async () => {
      if (!selectedRecipient) {
        setRecipientKycValidation(null);
        return;
      }

      try {
        const validation = await validateKYCForFinancialOperations(selectedRecipient.id);
        setRecipientKycValidation(validation);
      } catch (error) {
        console.error('Error loading recipient KYC:', error);
      }
    };

    loadRecipientKYC();
  }, [selectedRecipient]);

  // Update transfer preview
  useEffect(() => {
    if (shareAmount && selectedRecipient && kycValidation?.canWithdraw && recipientKycValidation?.canReceiveDividends) {
      const shares = parseInt(shareAmount);
      if (shares > 0 && shares <= availableShares) {
        const transferFee = Math.ceil(shares * TRANSFER_FEE_RATE);
        const netShares = shares + transferFee; // Total deducted from sender
        const recipientReceives = shares; // Recipient gets the requested amount

        setPreview({
          shares,
          transferFee,
          netShares,
          recipientReceives
        });
      } else {
        setPreview(null);
      }
    } else {
      setPreview(null);
    }
  }, [shareAmount, selectedRecipient, availableShares, kycValidation, recipientKycValidation]);

  const handleTransfer = async () => {
    if (!preview || !selectedRecipient || !kycValidation?.canWithdraw || !recipientKycValidation?.canReceiveDividends) {
      setError('Cannot process transfer. Please check all requirements.');
      return;
    }

    setIsTransferring(true);
    setError(null);

    try {
      // Use the new transaction service for atomic operations
      const result = await affiliateTransactionService.processShareTransfer({
        senderId: userId,
        recipientId: selectedRecipient.id,
        sharesTransferred: preview.shares,
        transferFee: preview.transferFee,
        totalDeducted: preview.netShares
      });

      if (!result.success) {
        throw new Error(result.error || 'Failed to process share transfer');
      }

      // Send comprehensive notifications (email + in-app)
      try {
        // Get user data for notifications
        const [senderData, recipientData] = await Promise.all([
          supabase.from('users').select('email, full_name, username').eq('id', userId).single(),
          supabase.from('users').select('email, full_name, username').eq('id', selectedRecipient.id).single()
        ]);

        if (senderData.data && recipientData.data) {
          await affiliateNotificationService.sendShareTransferNotifications({
            senderId: userId,
            senderEmail: senderData.data.email,
            senderName: senderData.data.full_name || senderData.data.username,
            recipientId: selectedRecipient.id,
            recipientEmail: recipientData.data.email,
            recipientName: recipientData.data.full_name || recipientData.data.username,
            sharesTransferred: preview.shares,
            transferFee: preview.transferFee,
            transactionId: result.transactionId || 'N/A',
            transferDate: new Date().toISOString()
          });
        }
      } catch (notificationError) {
        console.warn('Failed to send transfer notifications:', notificationError);
        // Don't fail the transfer if notifications fail
      }

      setSuccess(true);
      setShareAmount('');
      setSelectedRecipient(null);
      setRecipientSearch('');
      setPreview(null);
      setShowConfirmation(false);
      onTransferComplete();

      // Reset success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);

    } catch (error: any) {
      console.error('Transfer error:', error);
      setError(error.message || 'Failed to process transfer. Please try again.');
    } finally {
      setIsTransferring(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500"></div>
          <span className="ml-3 text-gray-300">Loading transfer system...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
      <h3 className="text-xl font-bold text-white mb-4">🔄 Transfer Shares</h3>

      {/* KYC Status */}
      <div className={`rounded-lg p-4 mb-6 ${
        kycValidation?.canWithdraw 
          ? 'bg-green-900/20 border border-green-500/30' 
          : 'bg-yellow-900/20 border border-yellow-500/30'
      }`}>
        <div className="flex items-start space-x-3">
          <div className="text-2xl">
            {kycValidation?.canWithdraw ? '✅' : '⚠️'}
          </div>
          <div>
            <h4 className="font-semibold text-white mb-1">
              {kycValidation?.canWithdraw ? 'KYC Verified' : 'KYC Required'}
            </h4>
            <p className={`text-sm ${
              kycValidation?.canWithdraw ? 'text-green-300' : 'text-yellow-300'
            }`}>
              {kycValidation?.message}
            </p>
          </div>
        </div>
      </div>

      {/* Available Shares */}
      <div className="bg-gray-700 rounded-lg p-4 mb-6">
        <h4 className="text-sm font-medium text-gray-300 mb-1">Available Shares</h4>
        <p className="text-2xl font-bold text-yellow-400">{availableShares}</p>
        <p className="text-sm text-gray-400">Shares available for transfer</p>
      </div>

      {/* Transfer Form */}
      {kycValidation?.canWithdraw ? (
        <div className="space-y-4">
          {/* Recipient Search */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Find Recipient (Username, Email, or Name)
            </label>
            <input
              type="text"
              value={recipientSearch}
              onChange={(e) => setRecipientSearch(e.target.value)}
              placeholder="Search for user..."
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-yellow-500"
            />
            
            {/* Search Results */}
            {searchResults.length > 0 && (
              <div className="mt-2 bg-gray-700 border border-gray-600 rounded-lg max-h-40 overflow-y-auto">
                {searchResults.map((user) => (
                  <button
                    key={user.id}
                    onClick={() => {
                      setSelectedRecipient(user);
                      setRecipientSearch('');
                      setSearchResults([]);
                    }}
                    className="w-full text-left px-3 py-2 hover:bg-gray-600 transition-colors"
                  >
                    <div className="text-white font-medium">{user.username}</div>
                    <div className="text-gray-400 text-sm">{user.full_name} • {user.email}</div>
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Selected Recipient */}
          {selectedRecipient && (
            <div className="bg-gray-700 rounded-lg p-4">
              <h4 className="font-semibold text-white mb-2">Selected Recipient</h4>
              <div className="text-sm">
                <p className="text-white"><strong>Username:</strong> {selectedRecipient.username}</p>
                <p className="text-gray-300"><strong>Name:</strong> {selectedRecipient.full_name}</p>
                <p className="text-gray-300"><strong>Email:</strong> {selectedRecipient.email}</p>
              </div>
              
              {/* Recipient KYC Status */}
              {recipientKycValidation && (
                <div className={`mt-3 p-2 rounded text-sm ${
                  recipientKycValidation.canReceiveDividends
                    ? 'bg-green-900/20 text-green-300'
                    : 'bg-red-900/20 text-red-300'
                }`}>
                  {recipientKycValidation.canReceiveDividends 
                    ? '✅ Recipient KYC verified - can receive shares'
                    : '❌ Recipient KYC not verified - cannot receive shares'
                  }
                </div>
              )}
            </div>
          )}

          {/* Share Amount */}
          {selectedRecipient && recipientKycValidation?.canReceiveDividends && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Shares to Transfer
              </label>
              <input
                type="number"
                value={shareAmount}
                onChange={(e) => setShareAmount(e.target.value)}
                placeholder={`Enter shares (max: ${availableShares - Math.ceil(availableShares * TRANSFER_FEE_RATE)})`}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-yellow-500"
                min="1"
                max={availableShares - Math.ceil(availableShares * TRANSFER_FEE_RATE)}
              />
            </div>
          )}

          {/* Transfer Preview */}
          {preview && (
            <div className="bg-gray-700 rounded-lg p-4">
              <h4 className="font-semibold text-white mb-3">Transfer Preview</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-300">Shares to Transfer:</span>
                  <span className="text-white">{preview.shares}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Transfer Fee (2%):</span>
                  <span className="text-yellow-400">{preview.transferFee}</span>
                </div>
                <div className="flex justify-between border-t border-gray-600 pt-2">
                  <span className="text-gray-300">Total Deducted:</span>
                  <span className="text-red-400 font-semibold">{preview.netShares}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Recipient Receives:</span>
                  <span className="text-green-400 font-semibold">{preview.recipientReceives}</span>
                </div>
              </div>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-3">
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          )}

          {/* Success Message */}
          {success && (
            <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-3">
              <p className="text-green-400 text-sm">
                ✅ Share transfer completed successfully! Both parties have been notified.
              </p>
            </div>
          )}

          {/* Transfer Button */}
          <button
            onClick={() => setShowConfirmation(true)}
            disabled={!preview || isTransferring}
            className="w-full bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-colors"
          >
            {isTransferring ? 'Processing Transfer...' : 'Transfer Shares'}
          </button>
        </div>
      ) : (
        <div className="text-center py-8">
          <p className="text-gray-400 mb-4">Complete KYC verification to transfer shares.</p>
          <button
            onClick={() => window.location.href = '#kyc'}
            className="bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-6 rounded-lg transition-colors"
          >
            Complete KYC
          </button>
        </div>
      )}

      {/* Confirmation Modal */}
      {showConfirmation && preview && selectedRecipient && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 w-full max-w-md mx-4">
            <h3 className="text-xl font-bold text-white mb-4">Confirm Share Transfer</h3>
            
            <div className="space-y-3 mb-6">
              <div className="flex justify-between">
                <span className="text-gray-300">Recipient:</span>
                <span className="text-white">{selectedRecipient.username}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-300">Shares Transferring:</span>
                <span className="text-white">{preview.shares}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-300">Transfer Fee:</span>
                <span className="text-yellow-400">{preview.transferFee}</span>
              </div>
              <div className="flex justify-between border-t border-gray-600 pt-2">
                <span className="text-gray-300">Total Deducted:</span>
                <span className="text-red-400 font-semibold">{preview.netShares}</span>
              </div>
            </div>

            <div className="flex gap-3">
              <button
                onClick={() => setShowConfirmation(false)}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                disabled={isTransferring}
              >
                Cancel
              </button>
              <button
                onClick={handleTransfer}
                className="flex-1 bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                disabled={isTransferring}
              >
                {isTransferring ? 'Processing...' : 'Confirm Transfer'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
