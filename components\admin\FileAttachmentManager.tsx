/**
 * FILE ATTACHMENT MANAGER
 * 
 * Comprehensive file attachment system for course resources:
 * - Multiple file type support (PDF, DOC, XLS, etc.)
 * - Drag & drop upload interface
 * - File organization and categorization
 * - Access control and download tracking
 * - File preview and metadata display
 * - Bulk upload and management
 */

import React, { useState, useRef, useCallback } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';

interface FileAttachmentManagerProps {
  courseId?: number;
  lessonId?: number;
  onFilesUploaded: (files: AttachedFile[]) => void;
  maxFileSize?: number; // in MB
  maxFiles?: number;
  className?: string;
}

interface AttachedFile {
  id?: number;
  fileName: string;
  fileUrl: string;
  fileType: string;
  fileSize: number;
  description?: string;
  isPublic: boolean;
  downloadCount: number;
  uploadedAt: string;
}

interface UploadProgress {
  fileName: string;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
  error?: string;
}

const SUPPORTED_TYPES = {
  'application/pdf': { icon: '📄', name: 'PDF Document', color: '#ef4444' },
  'application/msword': { icon: '📝', name: 'Word Document', color: '#2563eb' },
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': { icon: '📝', name: 'Word Document', color: '#2563eb' },
  'application/vnd.ms-excel': { icon: '📊', name: 'Excel Spreadsheet', color: '#059669' },
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': { icon: '📊', name: 'Excel Spreadsheet', color: '#059669' },
  'application/vnd.ms-powerpoint': { icon: '📈', name: 'PowerPoint Presentation', color: '#dc2626' },
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': { icon: '📈', name: 'PowerPoint Presentation', color: '#dc2626' },
  'text/plain': { icon: '📄', name: 'Text File', color: '#6b7280' },
  'application/zip': { icon: '🗜️', name: 'ZIP Archive', color: '#7c3aed' },
  'application/x-rar-compressed': { icon: '🗜️', name: 'RAR Archive', color: '#7c3aed' },
  'image/jpeg': { icon: '🖼️', name: 'JPEG Image', color: '#f59e0b' },
  'image/png': { icon: '🖼️', name: 'PNG Image', color: '#f59e0b' },
  'image/gif': { icon: '🖼️', name: 'GIF Image', color: '#f59e0b' },
  'video/mp4': { icon: '🎥', name: 'MP4 Video', color: '#8b5cf6' },
  'audio/mpeg': { icon: '🎵', name: 'MP3 Audio', color: '#06b6d4' },
  'audio/wav': { icon: '🎵', name: 'WAV Audio', color: '#06b6d4' }
};

export const FileAttachmentManager: React.FC<FileAttachmentManagerProps> = ({
  courseId,
  lessonId,
  onFilesUploaded,
  maxFileSize = 50, // 50MB default
  maxFiles = 10,
  className = ''
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [uploads, setUploads] = useState<UploadProgress[]>([]);
  const [attachedFiles, setAttachedFiles] = useState<AttachedFile[]>([]);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileTypeInfo = (mimeType: string) => {
    return SUPPORTED_TYPES[mimeType as keyof typeof SUPPORTED_TYPES] || {
      icon: '📎',
      name: 'Unknown File',
      color: '#6b7280'
    };
  };

  const validateFile = (file: File): string | null => {
    if (file.size > maxFileSize * 1024 * 1024) {
      return `File size exceeds ${maxFileSize}MB limit`;
    }

    if (attachedFiles.length >= maxFiles) {
      return `Maximum ${maxFiles} files allowed`;
    }

    return null;
  };

  const uploadFile = async (file: File, description: string = '', isPublic: boolean = false) => {
    // Add to uploads tracking
    setUploads(prev => [...prev, {
      fileName: file.name,
      progress: 0,
      status: 'uploading'
    }]);

    try {
      // Validate file
      const validationError = validateFile(file);
      if (validationError) {
        throw new Error(validationError);
      }

      // Generate unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `resource-${Date.now()}.${fileExt}`;

      // Upload file with progress tracking
      const { data, error } = await supabase.storage
        .from('training-assets')
        .upload(`course-files/${fileName}`, file, {
          onUploadProgress: (progress) => {
            const percentage = Math.round((progress.loaded / progress.total) * 100);
            setUploads(prev => prev.map(upload => 
              upload.fileName === file.name 
                ? { ...upload, progress: percentage }
                : upload
            ));
          }
        });

      if (error) throw error;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('training-assets')
        .getPublicUrl(`course-files/${fileName}`);

      // Save file metadata to database
      const serviceClient = getServiceRoleClient();
      const { data: fileRecord, error: dbError } = await serviceClient
        .from('training_course_files')
        .insert({
          course_id: courseId,
          lesson_id: lessonId,
          file_name: file.name,
          file_url: publicUrl,
          file_type: file.type,
          file_size: file.size,
          description: description || null,
          is_public: isPublic,
          download_count: 0
        })
        .select()
        .single();

      if (dbError) throw dbError;

      // Create attached file object
      const attachedFile: AttachedFile = {
        id: fileRecord.id,
        fileName: file.name,
        fileUrl: publicUrl,
        fileType: file.type,
        fileSize: file.size,
        description,
        isPublic,
        downloadCount: 0,
        uploadedAt: new Date().toISOString()
      };

      // Update uploads status
      setUploads(prev => prev.map(upload => 
        upload.fileName === file.name 
          ? { ...upload, progress: 100, status: 'completed' }
          : upload
      ));

      // Add to attached files
      setAttachedFiles(prev => [...prev, attachedFile]);
      
      // Notify parent component
      onFilesUploaded([attachedFile]);

      // Remove from uploads after delay
      setTimeout(() => {
        setUploads(prev => prev.filter(upload => upload.fileName !== file.name));
      }, 3000);

    } catch (error) {
      console.error('File upload error:', error);
      
      setUploads(prev => prev.map(upload => 
        upload.fileName === file.name 
          ? { 
              ...upload, 
              status: 'error', 
              error: error instanceof Error ? error.message : 'Upload failed'
            }
          : upload
      ));
    }
  };

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    files.forEach(file => uploadFile(file));
  }, []);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    files.forEach(file => uploadFile(file));
    
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removeFile = async (fileId: number, index: number) => {
    try {
      const serviceClient = getServiceRoleClient();
      const { error } = await serviceClient
        .from('training_course_files')
        .delete()
        .eq('id', fileId);

      if (error) throw error;

      setAttachedFiles(prev => prev.filter((_, i) => i !== index));
    } catch (error) {
      console.error('Error removing file:', error);
      alert('Failed to remove file. Please try again.');
    }
  };

  const downloadFile = async (file: AttachedFile) => {
    try {
      // Increment download count
      if (file.id) {
        const serviceClient = getServiceRoleClient();
        await serviceClient
          .from('training_course_files')
          .update({ download_count: file.downloadCount + 1 })
          .eq('id', file.id);
      }

      // Trigger download
      const link = document.createElement('a');
      link.href = file.fileUrl;
      link.download = file.fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Update local state
      setAttachedFiles(prev => prev.map(f => 
        f.id === file.id 
          ? { ...f, downloadCount: f.downloadCount + 1 }
          : f
      ));
    } catch (error) {
      console.error('Error downloading file:', error);
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <div
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          isDragging
            ? 'border-blue-500 bg-blue-500/10'
            : 'border-gray-600 hover:border-gray-500'
        }`}
      >
        <div className="space-y-3">
          <div className="text-3xl">📎</div>
          <div>
            <h3 className="text-lg font-semibold text-white mb-2">
              Upload Course Resources
            </h3>
            <p className="text-gray-400 mb-4">
              Drag and drop files here, or click to browse
            </p>
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors"
            >
              Choose Files
            </button>
          </div>
          <div className="text-sm text-gray-500">
            <p>Supported: PDF, DOC, XLS, PPT, TXT, ZIP, Images, Videos, Audio</p>
            <p>Maximum file size: {maxFileSize}MB • Maximum files: {maxFiles}</p>
          </div>
        </div>
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Upload Progress */}
      {uploads.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-white font-medium">Uploading Files</h4>
          {uploads.map((upload, index) => (
            <div key={index} className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-white text-sm font-medium truncate">
                  {upload.fileName}
                </span>
                <span className="text-gray-400 text-sm">
                  {upload.status === 'uploading' && `${upload.progress}%`}
                  {upload.status === 'completed' && '✅ Complete'}
                  {upload.status === 'error' && '❌ Error'}
                </span>
              </div>
              
              {upload.status === 'uploading' && (
                <div className="w-full bg-gray-600 rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${upload.progress}%` }}
                  />
                </div>
              )}
              
              {upload.status === 'error' && upload.error && (
                <p className="text-red-400 text-sm mt-2">{upload.error}</p>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Attached Files */}
      {attachedFiles.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-white font-medium">Course Resources ({attachedFiles.length})</h4>
          <div className="space-y-2">
            {attachedFiles.map((file, index) => {
              const typeInfo = getFileTypeInfo(file.fileType);
              return (
                <div key={index} className="bg-gray-700 rounded-lg p-4 flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div 
                      className="text-2xl p-2 rounded-lg"
                      style={{ backgroundColor: `${typeInfo.color}20` }}
                    >
                      {typeInfo.icon}
                    </div>
                    <div>
                      <h5 className="text-white font-medium text-sm">
                        {file.fileName}
                      </h5>
                      <div className="flex items-center space-x-3 text-xs text-gray-400">
                        <span>{typeInfo.name}</span>
                        <span>•</span>
                        <span>{formatFileSize(file.fileSize)}</span>
                        <span>•</span>
                        <span>{file.downloadCount} downloads</span>
                        {file.isPublic && (
                          <>
                            <span>•</span>
                            <span className="text-green-400">Public</span>
                          </>
                        )}
                      </div>
                      {file.description && (
                        <p className="text-gray-400 text-xs mt-1">{file.description}</p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => downloadFile(file)}
                      className="text-blue-400 hover:text-blue-300 text-sm px-2 py-1 rounded"
                      title="Download"
                    >
                      ⬇️
                    </button>
                    <button
                      onClick={() => file.id && removeFile(file.id, index)}
                      className="text-red-400 hover:text-red-300 text-sm px-2 py-1 rounded"
                      title="Remove"
                    >
                      🗑️
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};
