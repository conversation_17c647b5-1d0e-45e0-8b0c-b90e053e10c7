import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.VITE_SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);

async function testTemplateFiltering() {
  console.log('🧪 Testing Email Template Filtering...\n');

  try {
    // Test 1: Get ALL templates (including system ones)
    console.log('📋 Fetching ALL email templates...');
    const { data: allTemplates, error: allError } = await supabase
      .from('email_templates')
      .select('*')
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (allError) throw allError;

    console.log(`✅ Found ${allTemplates.length} total templates:`);
    allTemplates.forEach(template => {
      const isSystem = [
        'share_purchase_confirmation',
        'verification_email', 
        'conversion_confirmation',
        'withdrawal_confirmation',
        'welcome_email',
        'password_reset',
        'commission_notification'
      ].includes(template.template_name);
      
      console.log(`   ${isSystem ? '🔧' : '📧'} ${template.template_name} (${template.template_type})`);
    });

    // Test 2: Get ONLY marketing templates (filtered)
    console.log('\n📧 Fetching MARKETING templates only...');
    const systemTemplateNames = [
      'share_purchase_confirmation',
      'verification_email', 
      'conversion_confirmation',
      'withdrawal_confirmation',
      'welcome_email',
      'password_reset',
      'commission_notification'
    ];

    const { data: marketingTemplates, error: marketingError } = await supabase
      .from('email_templates')
      .select('*')
      .eq('is_active', true)
      .not('template_name', 'in', `(${systemTemplateNames.join(',')})`)
      .order('created_at', { ascending: false });

    if (marketingError) throw marketingError;

    console.log(`✅ Found ${marketingTemplates.length} marketing templates:`);
    marketingTemplates.forEach(template => {
      console.log(`   📧 ${template.template_name} (${template.template_type})`);
    });

    // Test 3: Verify no system templates in marketing results
    console.log('\n🔍 Verifying filter effectiveness...');
    const systemTemplatesInResults = marketingTemplates.filter(template => 
      systemTemplateNames.includes(template.template_name)
    );

    if (systemTemplatesInResults.length === 0) {
      console.log('✅ SUCCESS: No system templates found in marketing results!');
    } else {
      console.log('❌ FAILURE: System templates still present:');
      systemTemplatesInResults.forEach(template => {
        console.log(`   🔧 ${template.template_name}`);
      });
    }

    console.log('\n📊 SUMMARY:');
    console.log(`   Total templates: ${allTemplates.length}`);
    console.log(`   Marketing templates: ${marketingTemplates.length}`);
    console.log(`   System templates: ${allTemplates.length - marketingTemplates.length}`);
    console.log(`   Filter working: ${systemTemplatesInResults.length === 0 ? '✅ YES' : '❌ NO'}`);

  } catch (error) {
    console.error('❌ Error testing template filtering:', error.message);
  }
}

testTemplateFiltering();
