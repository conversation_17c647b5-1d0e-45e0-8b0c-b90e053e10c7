/**
 * EMAIL CLIENT
 * 
 * Core email client wrapper for Resend API with error handling,
 * retry logic, and rate limiting capabilities.
 */

import { Resend } from 'resend';
import { EmailDeliveryResult, EmailSendRequest, EmailServiceConfig } from './types/EmailTypes';

export class EmailClient {
  private resend: Resend | null = null;
  private config: EmailServiceConfig;
  private isConfigured: boolean = false;
  private rateLimitTracker: Map<string, number[]> = new Map();

  constructor(config: EmailServiceConfig) {
    this.config = config;
    this.initialize();
  }

  private initialize(): void {
    try {
      if (this.config.apiKey) {
        this.resend = new Resend(this.config.apiKey);
        this.isConfigured = true;
        console.log('✅ Email client initialized successfully');
      } else {
        console.warn('⚠️ Email API key not provided - email service disabled');
        this.isConfigured = false;
      }
    } catch (error) {
      console.error('❌ Failed to initialize email client:', error);
      this.isConfigured = false;
    }
  }

  /**
   * Check if the email client is properly configured
   */
  public getConfigurationStatus(): { isConfigured: boolean; error?: string } {
    return {
      isConfigured: this.isConfigured,
      error: this.isConfigured ? undefined : 'Email service not configured'
    };
  }

  /**
   * Send a single email with retry logic
   */
  public async sendEmail(request: EmailSendRequest): Promise<EmailDeliveryResult> {
    if (!this.isConfigured || !this.resend) {
      console.warn('⚠️ Email client not configured - email not sent');
      return {
        success: false,
        messageId: null,
        error: 'Email service not configured'
      };
    }

    // Check rate limiting
    if (!this.checkRateLimit(request.to)) {
      return {
        success: false,
        messageId: null,
        error: 'Rate limit exceeded for recipient'
      };
    }

    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        console.log(`📧 Sending ${request.emailType} email to ${request.to} (attempt ${attempt})`);

        const result = await this.resend.emails.send({
          from: `${this.config.fromName} <${this.config.fromEmail}>`,
          to: [request.to],
          subject: request.subject,
          html: request.htmlContent,
          text: request.textContent,
          attachments: request.attachments
        });

        if (result.error) {
          throw new Error(`Resend API error: ${result.error.message}`);
        }

        // Track successful send for rate limiting
        this.trackEmailSent(request.to);

        console.log(`✅ Email sent successfully: ${result.data?.id}`);
        return {
          success: true,
          messageId: result.data?.id || null
        };

      } catch (error: any) {
        lastError = error;
        console.error(`❌ Email send attempt ${attempt} failed:`, error.message);

        // Don't retry on certain errors
        if (this.isNonRetryableError(error)) {
          break;
        }

        // Wait before retrying (exponential backoff)
        if (attempt < this.config.maxRetries) {
          const delay = this.config.retryDelay * Math.pow(2, attempt - 1);
          console.log(`⏳ Waiting ${delay}ms before retry...`);
          await this.sleep(delay);
        }
      }
    }

    return {
      success: false,
      messageId: null,
      error: lastError?.message || 'Unknown error occurred'
    };
  }

  /**
   * Send multiple emails in batches
   */
  public async sendBulkEmails(requests: EmailSendRequest[]): Promise<EmailDeliveryResult[]> {
    const results: EmailDeliveryResult[] = [];
    
    // Process in batches to avoid overwhelming the API
    for (let i = 0; i < requests.length; i += this.config.batchSize) {
      const batch = requests.slice(i, i + this.config.batchSize);
      
      console.log(`📧 Processing email batch ${Math.floor(i / this.config.batchSize) + 1}/${Math.ceil(requests.length / this.config.batchSize)}`);
      
      // Send batch concurrently
      const batchPromises = batch.map(request => this.sendEmail(request));
      const batchResults = await Promise.all(batchPromises);
      
      results.push(...batchResults);
      
      // Add delay between batches to respect rate limits
      if (i + this.config.batchSize < requests.length) {
        await this.sleep(1000); // 1 second delay between batches
      }
    }

    return results;
  }

  /**
   * Validate email address format
   */
  public validateEmailAddress(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Check if we're within rate limits for a recipient
   */
  private checkRateLimit(email: string): boolean {
    const now = Date.now();
    const windowStart = now - (60 * 1000); // 1 minute window
    
    const emailHistory = this.rateLimitTracker.get(email) || [];
    const recentSends = emailHistory.filter(timestamp => timestamp > windowStart);
    
    return recentSends.length < this.config.rateLimitPerMinute;
  }

  /**
   * Track email send for rate limiting
   */
  private trackEmailSent(email: string): void {
    const now = Date.now();
    const emailHistory = this.rateLimitTracker.get(email) || [];
    
    emailHistory.push(now);
    
    // Keep only recent sends (last hour)
    const oneHourAgo = now - (60 * 60 * 1000);
    const recentSends = emailHistory.filter(timestamp => timestamp > oneHourAgo);
    
    this.rateLimitTracker.set(email, recentSends);
  }

  /**
   * Check if error should not be retried
   */
  private isNonRetryableError(error: any): boolean {
    const nonRetryableMessages = [
      'invalid email',
      'blocked recipient',
      'invalid api key',
      'insufficient credits',
      'domain not verified'
    ];

    const errorMessage = error.message?.toLowerCase() || '';
    return nonRetryableMessages.some(msg => errorMessage.includes(msg));
  }

  /**
   * Sleep utility for delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get rate limit status for debugging
   */
  public getRateLimitStatus(): Record<string, number> {
    const status: Record<string, number> = {};
    const now = Date.now();
    const windowStart = now - (60 * 1000);

    for (const [email, history] of this.rateLimitTracker.entries()) {
      const recentSends = history.filter(timestamp => timestamp > windowStart);
      status[email] = recentSends.length;
    }

    return status;
  }

  /**
   * Clear rate limit history (for testing)
   */
  public clearRateLimitHistory(): void {
    this.rateLimitTracker.clear();
  }
}
