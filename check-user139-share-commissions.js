import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkUser139ShareCommissions() {
  console.log('🔍 CHECKING USER 139 SHARE COMMISSION ANALYSIS\n');

  // Get user 139's commission transactions with detailed analysis
  const { data: commissions, error: commissionError } = await supabase
    .from('commission_transactions')
    .select(`
      *,
      referred_user:users!referred_id(id, username, full_name)
    `)
    .eq('referrer_id', 139)
    .order('created_at', { ascending: false });

  if (commissionError) {
    console.log('❌ Error getting commissions:', commissionError.message);
    return;
  }

  console.log(`💸 User 139's Commission Analysis:`);
  console.log(`Found ${commissions.length} commission transactions\n`);

  let totalExpectedUSDT = 0;
  let totalExpectedShares = 0;
  let totalReceivedUSDT = 0;
  let totalReceivedShares = 0;

  console.log('📊 DETAILED COMMISSION BREAKDOWN:');
  console.log('='.repeat(80));

  for (let i = 0; i < commissions.length; i++) {
    const commission = commissions[i];
    
    // Calculate what the share commission SHOULD be (15% of shares purchased)
    const purchaseAmount = commission.share_purchase_amount || 0;
    const expectedUSDTCommission = purchaseAmount * 0.15;
    
    // To calculate expected share commission, we need to know how many shares were purchased
    // Let's get the actual purchase details
    const { data: purchase, error: purchaseError } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .eq('user_id', commission.referred_id)
      .eq('total_amount', purchaseAmount)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    let expectedShareCommission = 0;
    let sharesPurchased = 0;
    
    if (!purchaseError && purchase) {
      sharesPurchased = purchase.shares_purchased || 0;
      expectedShareCommission = sharesPurchased * 0.15;
    }

    totalExpectedUSDT += expectedUSDTCommission;
    totalExpectedShares += expectedShareCommission;
    totalReceivedUSDT += commission.usdt_commission || 0;
    totalReceivedShares += commission.share_commission || 0;

    console.log(`${i + 1}. Transaction ID: ${commission.id}`);
    console.log(`   Referred User: ${commission.referred_user?.full_name || commission.referred_user?.username}`);
    console.log(`   Purchase Amount: $${purchaseAmount}`);
    console.log(`   Shares Purchased: ${sharesPurchased}`);
    console.log(`   `);
    console.log(`   EXPECTED COMMISSIONS (15% each):`);
    console.log(`   • USDT: $${expectedUSDTCommission.toFixed(2)}`);
    console.log(`   • Shares: ${expectedShareCommission.toFixed(2)}`);
    console.log(`   `);
    console.log(`   ACTUAL COMMISSIONS RECEIVED:`);
    console.log(`   • USDT: $${commission.usdt_commission || 0} ${(commission.usdt_commission || 0) === expectedUSDTCommission ? '✅' : '❌'}`);
    console.log(`   • Shares: ${commission.share_commission || 0} ${(commission.share_commission || 0) === expectedShareCommission ? '✅' : '❌'}`);
    console.log(`   `);
    console.log(`   Date: ${new Date(commission.created_at).toLocaleDateString()}`);
    console.log('-'.repeat(80));
  }

  console.log('\n📊 SUMMARY TOTALS:');
  console.log('='.repeat(50));
  console.log(`EXPECTED COMMISSIONS:`);
  console.log(`• USDT: $${totalExpectedUSDT.toFixed(2)}`);
  console.log(`• Shares: ${totalExpectedShares.toFixed(2)}`);
  console.log(``);
  console.log(`ACTUAL COMMISSIONS RECEIVED:`);
  console.log(`• USDT: $${totalReceivedUSDT.toFixed(2)} ${totalReceivedUSDT === totalExpectedUSDT ? '✅' : '❌'}`);
  console.log(`• Shares: ${totalReceivedShares.toFixed(2)} ${totalReceivedShares === totalExpectedShares ? '✅' : '❌'}`);
  console.log(``);
  console.log(`MISSING COMMISSIONS:`);
  console.log(`• USDT: $${(totalExpectedUSDT - totalReceivedUSDT).toFixed(2)}`);
  console.log(`• Shares: ${(totalExpectedShares - totalReceivedShares).toFixed(2)}`);

  // Check commission balance
  const { data: balance, error: balanceError } = await supabase
    .from('commission_balances')
    .select('*')
    .eq('user_id', 139)
    .single();

  if (!balanceError && balance) {
    console.log(`\n💰 COMMISSION BALANCE RECORD:`);
    console.log(`• USDT Balance: $${balance.usdt_balance || 0}`);
    console.log(`• Share Balance: ${balance.share_balance || 0}`);
    console.log(`• Total Earned USDT: $${balance.total_earned_usdt || 0}`);
    console.log(`• Total Earned Shares: ${balance.total_earned_shares || 0}`);
  }

  // Analysis and recommendations
  console.log(`\n🔍 ANALYSIS:`);
  if (totalReceivedShares === 0 && totalExpectedShares > 0) {
    console.log(`❌ ISSUE FOUND: User 139 is missing ALL share commissions!`);
    console.log(`   She should have received ${totalExpectedShares.toFixed(2)} shares in commission.`);
    console.log(`   This suggests the commission system is only paying USDT, not shares.`);
  } else if (totalReceivedShares < totalExpectedShares) {
    console.log(`⚠️ PARTIAL ISSUE: User 139 is missing some share commissions.`);
    console.log(`   Missing: ${(totalExpectedShares - totalReceivedShares).toFixed(2)} shares`);
  } else {
    console.log(`✅ Share commissions appear to be correct.`);
  }

  if (totalReceivedUSDT < totalExpectedUSDT) {
    console.log(`⚠️ USDT commissions are also short by $${(totalExpectedUSDT - totalReceivedUSDT).toFixed(2)}`);
  }

  console.log('\n✅ Analysis complete!');
}

// Run the analysis
checkUser139ShareCommissions().catch(console.error);
