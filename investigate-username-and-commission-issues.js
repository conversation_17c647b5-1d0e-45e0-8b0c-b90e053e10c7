import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

const supabase = createClient(supabaseUrl, supabaseKey);

async function investigateIssues() {
  console.log('🔍 INVESTIGATING USERNAME PROPAGATION AND COMMISSION ISSUES\n');

  // ===== PART 1: CHECK PROFILE IMAGE URL FIELD =====
  console.log('📋 PART 1: Checking profile_image_url field in users table');
  
  try {
    const { data: columns, error: columnError } = await supabase
      .rpc('get_table_columns', { table_name: 'users' });
    
    if (columnError) {
      console.log('⚠️ Could not check columns via RPC, trying direct query...');
      
      // Try direct query approach
      const { data: schemaData, error: schemaError } = await supabase
        .from('information_schema.columns')
        .select('column_name, data_type')
        .eq('table_schema', 'public')
        .eq('table_name', 'users')
        .eq('column_name', 'profile_image_url');
      
      if (schemaError) {
        console.log('❌ Could not check schema:', schemaError.message);
      } else {
        console.log('✅ Profile image URL field check:', schemaData.length > 0 ? 'EXISTS' : 'MISSING');
      }
    }
  } catch (error) {
    console.log('⚠️ Schema check failed, continuing with other checks...');
  }

  // ===== PART 2: USERNAME PROPAGATION CHECK =====
  console.log('\n📋 PART 2: Checking Username Propagation');
  
  // Check which tables reference usernames or user IDs
  const tablesToCheck = [
    'referrals',
    'commission_transactions', 
    'commission_balances',
    'aureus_share_purchases',
    'crypto_payment_transactions',
    'telegram_users'
  ];

  console.log('\n🔍 Tables that should be updated when username changes:');
  for (const table of tablesToCheck) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);
      
      if (!error && data && data.length > 0) {
        const columns = Object.keys(data[0]);
        const userRelatedColumns = columns.filter(col => 
          col.includes('user') || col.includes('referrer') || col.includes('referred')
        );
        console.log(`   • ${table}: ${userRelatedColumns.join(', ')}`);
      }
    } catch (err) {
      console.log(`   • ${table}: Could not check`);
    }
  }

  // ===== PART 3: USER 139 COMMISSION INVESTIGATION =====
  console.log('\n📋 PART 3: Investigating User 139 Commission Issues');
  
  // Get user 139 details
  const { data: user139, error: userError } = await supabase
    .from('users')
    .select('*')
    .eq('id', 139)
    .single();

  if (userError) {
    console.log('❌ Could not find user 139:', userError.message);
    return;
  }

  console.log(`\n👤 User 139 Details:`);
  console.log(`   ID: ${user139.id}`);
  console.log(`   Username: ${user139.username}`);
  console.log(`   Full Name: ${user139.full_name}`);
  console.log(`   Email: ${user139.email}`);

  // Check user 139's downline (people they referred)
  const { data: downline, error: downlineError } = await supabase
    .from('referrals')
    .select(`
      *,
      referred:users!referred_id(id, username, full_name, email)
    `)
    .eq('referrer_id', 139)
    .eq('status', 'active');

  if (downlineError) {
    console.log('❌ Error getting downline:', downlineError.message);
  } else {
    console.log(`\n🔗 User 139's Active Downline (${downline.length} people):`);
    downline.forEach((ref, index) => {
      console.log(`   ${index + 1}. ${ref.referred.full_name || ref.referred.username} (ID: ${ref.referred.id})`);
    });
  }

  // Check purchases made by downline members
  if (downline && downline.length > 0) {
    console.log(`\n💰 Checking purchases made by downline members:`);
    
    const downlineIds = downline.map(ref => ref.referred_id);
    
    const { data: purchases, error: purchaseError } = await supabase
      .from('aureus_share_purchases')
      .select(`
        *,
        users!user_id(id, username, full_name)
      `)
      .in('user_id', downlineIds)
      .eq('status', 'approved')
      .order('created_at', { ascending: false });

    if (purchaseError) {
      console.log('❌ Error getting purchases:', purchaseError.message);
    } else {
      console.log(`   Found ${purchases.length} approved purchases from downline members:`);
      
      let totalPurchaseAmount = 0;
      let totalExpectedUSDTCommission = 0;
      let totalExpectedShareCommission = 0;
      
      purchases.forEach((purchase, index) => {
        const usdtCommission = purchase.total_amount * 0.15;
        const shareCommission = purchase.shares_purchased * 0.15;
        
        totalPurchaseAmount += purchase.total_amount;
        totalExpectedUSDTCommission += usdtCommission;
        totalExpectedShareCommission += shareCommission;
        
        console.log(`   ${index + 1}. ${purchase.users.full_name || purchase.users.username}`);
        console.log(`      Purchase: $${purchase.total_amount} (${purchase.shares_purchased} shares)`);
        console.log(`      Expected Commission: $${usdtCommission.toFixed(2)} USDT + ${shareCommission.toFixed(2)} shares`);
        console.log(`      Date: ${new Date(purchase.created_at).toLocaleDateString()}`);
      });
      
      console.log(`\n📊 TOTALS:`);
      console.log(`   Total Purchase Amount: $${totalPurchaseAmount.toFixed(2)}`);
      console.log(`   Expected USDT Commission: $${totalExpectedUSDTCommission.toFixed(2)}`);
      console.log(`   Expected Share Commission: ${totalExpectedShareCommission.toFixed(2)} shares`);
    }
  }

  // Check actual commissions received by user 139
  const { data: commissions, error: commissionError } = await supabase
    .from('commission_transactions')
    .select('*')
    .eq('referrer_id', 139)
    .order('created_at', { ascending: false });

  if (commissionError) {
    console.log('❌ Error getting commissions:', commissionError.message);
  } else {
    console.log(`\n💸 User 139's Commission Transactions (${commissions.length} found):`);
    
    let totalReceivedUSDT = 0;
    let totalReceivedShares = 0;
    
    commissions.forEach((commission, index) => {
      totalReceivedUSDT += commission.usdt_commission || 0;
      totalReceivedShares += commission.share_commission || 0;
      
      console.log(`   ${index + 1}. Commission ID: ${commission.id}`);
      console.log(`      USDT: $${commission.usdt_commission || 0}`);
      console.log(`      Shares: ${commission.share_commission || 0}`);
      console.log(`      Status: ${commission.status}`);
      console.log(`      Date: ${new Date(commission.created_at).toLocaleDateString()}`);
    });
    
    console.log(`\n📊 COMMISSION TOTALS:`);
    console.log(`   Total Received USDT: $${totalReceivedUSDT.toFixed(2)}`);
    console.log(`   Total Received Shares: ${totalReceivedShares.toFixed(2)}`);
  }

  // Check commission balance
  const { data: balance, error: balanceError } = await supabase
    .from('commission_balances')
    .select('*')
    .eq('user_id', 139)
    .single();

  if (balanceError) {
    console.log('❌ Error getting commission balance:', balanceError.message);
  } else if (balance) {
    console.log(`\n💰 User 139's Commission Balance:`);
    console.log(`   USDT Balance: $${balance.usdt_balance || 0}`);
    console.log(`   Share Balance: ${balance.share_balance || 0}`);
    console.log(`   Total Earned USDT: $${balance.total_earned_usdt || 0}`);
    console.log(`   Total Earned Shares: ${balance.total_earned_shares || 0}`);
    console.log(`   Last Updated: ${balance.last_updated ? new Date(balance.last_updated).toLocaleDateString() : 'Never'}`);
  } else {
    console.log('⚠️ No commission balance record found for user 139');
  }

  console.log('\n✅ Investigation complete!');
}

// Run the investigation
investigateIssues().catch(console.error);
