#!/usr/bin/env node

/**
 * Generate Missing PWA Icons Script
 * 
 * This script creates placeholder icons for the PWA manifest to prevent 404 errors.
 * It generates simple SVG-based icons in the required sizes.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Icon sizes needed based on manifest.json
const iconSizes = [72, 96, 128, 144, 152, 192, 384, 512];
const shortcutSizes = [96]; // For shortcut icons

// Create icons directory
const iconsDir = path.join(__dirname, '..', 'public', 'icons');
const screenshotsDir = path.join(__dirname, '..', 'public', 'screenshots');

// Ensure directories exist
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
  console.log('✅ Created icons directory');
}

if (!fs.existsSync(screenshotsDir)) {
  fs.mkdirSync(screenshotsDir, { recursive: true });
  console.log('✅ Created screenshots directory');
}

/**
 * Generate SVG icon content
 */
function generateSVGIcon(size, type = 'main') {
  const colors = {
    main: { bg: '#111827', accent: '#f59e0b', text: '#ffffff' },
    buy: { bg: '#059669', accent: '#10b981', text: '#ffffff' },
    portfolio: { bg: '#7c3aed', accent: '#8b5cf6', text: '#ffffff' },
    referrals: { bg: '#dc2626', accent: '#ef4444', text: '#ffffff' }
  };
  
  const color = colors[type] || colors.main;
  const fontSize = Math.max(size * 0.15, 12);
  const iconSize = size * 0.4;
  
  let symbol = '';
  switch (type) {
    case 'buy':
      symbol = `<circle cx="${size/2}" cy="${size/2}" r="${iconSize/3}" fill="none" stroke="${color.accent}" stroke-width="3"/>
                <path d="M${size/2-iconSize/6},${size/2} L${size/2+iconSize/6},${size/2} M${size/2},${size/2-iconSize/6} L${size/2},${size/2+iconSize/6}" stroke="${color.accent}" stroke-width="3"/>`;
      break;
    case 'portfolio':
      symbol = `<rect x="${size/2-iconSize/3}" y="${size/2-iconSize/4}" width="${iconSize/1.5}" height="${iconSize/2}" fill="none" stroke="${color.accent}" stroke-width="2"/>
                <path d="M${size/2-iconSize/6},${size/2} L${size/2},${size/2-iconSize/8} L${size/2+iconSize/6},${size/2+iconSize/8}" stroke="${color.accent}" stroke-width="2" fill="none"/>`;
      break;
    case 'referrals':
      symbol = `<circle cx="${size/2-iconSize/4}" cy="${size/2-iconSize/6}" r="${iconSize/8}" fill="${color.accent}"/>
                <circle cx="${size/2+iconSize/4}" cy="${size/2-iconSize/6}" r="${iconSize/8}" fill="${color.accent}"/>
                <circle cx="${size/2}" cy="${size/2+iconSize/6}" r="${iconSize/8}" fill="${color.accent}"/>
                <path d="M${size/2-iconSize/4},${size/2-iconSize/12} L${size/2+iconSize/4},${size/2-iconSize/12}" stroke="${color.accent}" stroke-width="2"/>
                <path d="M${size/2-iconSize/8},${size/2+iconSize/12} L${size/2+iconSize/8},${size/2+iconSize/12}" stroke="${color.accent}" stroke-width="2"/>`;
      break;
    default:
      // Main icon - stylized "A" for Aureus
      symbol = `<path d="M${size/2-iconSize/3},${size/2+iconSize/3} L${size/2},${size/2-iconSize/3} L${size/2+iconSize/3},${size/2+iconSize/3}" 
                      stroke="${color.accent}" stroke-width="4" fill="none"/>
                <path d="M${size/2-iconSize/6},${size/2+iconSize/12} L${size/2+iconSize/6},${size/2+iconSize/12}" 
                      stroke="${color.accent}" stroke-width="3"/>`;
  }

  return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <rect width="${size}" height="${size}" fill="${color.bg}" rx="${size * 0.1}"/>
  ${symbol}
  <text x="${size/2}" y="${size - size * 0.1}" text-anchor="middle" fill="${color.text}" 
        font-family="Arial, sans-serif" font-size="${fontSize * 0.6}" font-weight="bold">
    ${type === 'main' ? 'AUREUS' : type.toUpperCase()}
  </text>
</svg>`;
}

/**
 * Convert SVG to PNG using a simple canvas approach (for Node.js)
 * Note: This creates SVG files that browsers will render as needed
 */
function createIconFile(size, type = 'main') {
  const svgContent = generateSVGIcon(size, type);
  const filename = type === 'main' ? `icon-${size}x${size}.png` : `shortcut-${type}.png`;
  const filepath = path.join(iconsDir, filename);
  
  // For now, save as SVG with PNG extension (browsers handle this well for PWA icons)
  fs.writeFileSync(filepath, svgContent);
  console.log(`✅ Created ${filename}`);
}

/**
 * Create placeholder screenshots
 */
function createScreenshots() {
  const screenshots = [
    { name: 'mobile-dashboard.png', width: 390, height: 844 },
    { name: 'mobile-purchase.png', width: 390, height: 844 },
    { name: 'desktop-dashboard.png', width: 1280, height: 720 }
  ];

  screenshots.forEach(({ name, width, height }) => {
    const svgContent = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
  <rect width="${width}" height="${height}" fill="#111827"/>
  <rect x="20" y="20" width="${width-40}" height="${height-40}" fill="#1f2937" rx="10"/>
  <text x="${width/2}" y="${height/2}" text-anchor="middle" fill="#f59e0b" 
        font-family="Arial, sans-serif" font-size="24" font-weight="bold">
    Aureus Africa Dashboard
  </text>
  <text x="${width/2}" y="${height/2 + 40}" text-anchor="middle" fill="#9ca3af" 
        font-family="Arial, sans-serif" font-size="16">
    ${name.replace('.png', '').replace('-', ' ').toUpperCase()}
  </text>
</svg>`;
    
    const filepath = path.join(screenshotsDir, name);
    fs.writeFileSync(filepath, svgContent);
    console.log(`✅ Created screenshot ${name}`);
  });
}

// Generate all required icons
console.log('🎨 Generating PWA icons...');

// Main app icons
iconSizes.forEach(size => createIconFile(size, 'main'));

// Shortcut icons
createIconFile(96, 'buy');
createIconFile(96, 'portfolio');
createIconFile(96, 'referrals');

// Screenshots
createScreenshots();

console.log('✅ All PWA assets generated successfully!');
console.log('📱 Your PWA manifest should now work without 404 errors.');
