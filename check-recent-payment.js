#!/usr/bin/env node

/**
 * Check Recent Payment Issue
 * 
 * This script checks the specific $5 payment that was approved
 * and why commission wasn't distributed.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ Missing SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkRecentPayment() {
  try {
    console.log('🔍 Checking Recent Payment Issue\n');
    
    // Find <PERSON>'s user ID
    const { data: jeanPierre, error: jpError } = await supabase
      .from('users')
      .select('*')
      .eq('username', 'TTTFOUNDER')
      .single();
    
    if (jpError || !jeanPierre) {
      console.error('❌ <PERSON> (TTTFOUNDER) not found:', jpError);
      return;
    }
    
    console.log(`👤 Jean Pierre ID: ${jeanPierre.id}`);
    
    // Find the most recent approved payment
    const { data: recentPayment, error: paymentError } = await supabase
      .from('crypto_payment_transactions')
      .select('*')
      .eq('user_id', jeanPierre.id)
      .eq('status', 'approved')
      .order('approved_at', { ascending: false })
      .limit(1)
      .single();
    
    if (paymentError) {
      console.error('❌ No recent approved payment found:', paymentError);
      return;
    }
    
    console.log('💳 Most Recent Approved Payment:');
    console.log(`   Payment ID: ${recentPayment.id}`);
    console.log(`   Amount: $${recentPayment.amount}`);
    console.log(`   Created: ${recentPayment.created_at}`);
    console.log(`   Approved: ${recentPayment.approved_at}`);
    console.log(`   Approved by Admin ID: ${recentPayment.approved_by_admin_id}`);
    
    // Check if there's a corresponding share purchase
    const { data: sharePurchase, error: shareError } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .eq('user_id', jeanPierre.id)
      .gte('created_at', recentPayment.approved_at)
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (shareError) {
      console.error('❌ Error checking share purchases:', shareError);
    } else {
      console.log(`\n📈 Share Purchases after payment approval: ${sharePurchase.length}`);
      if (sharePurchase.length > 0) {
        const purchase = sharePurchase[0];
        console.log(`   Purchase ID: ${purchase.id}`);
        console.log(`   Shares: ${purchase.shares_purchased}`);
        console.log(`   Amount: $${purchase.total_amount}`);
        console.log(`   Created: ${purchase.created_at}`);
        console.log(`   Status: ${purchase.status}`);
      }
    }
    
    // Check commission transactions for this timeframe
    const { data: commissions, error: commError } = await supabase
      .from('commission_transactions')
      .select(`
        *,
        referrer:users!referrer_id(id, username, full_name)
      `)
      .eq('referred_id', jeanPierre.id)
      .gte('payment_date', recentPayment.approved_at)
      .order('payment_date', { ascending: false });
    
    if (commError) {
      console.error('❌ Error checking commissions:', commError);
    } else {
      console.log(`\n💸 Commission Transactions after payment approval: ${commissions.length}`);
      commissions.forEach(comm => {
        console.log(`   Commission ID: ${comm.id}`);
        console.log(`   To: ${comm.referrer?.full_name || comm.referrer?.username} (ID: ${comm.referrer_id})`);
        console.log(`   USDT: $${comm.usdt_commission}, Shares: ${comm.share_commission}`);
        console.log(`   Purchase Amount: $${comm.share_purchase_amount}`);
        console.log(`   Status: ${comm.status}`);
        console.log(`   Date: ${comm.payment_date}`);
        console.log('');
      });
    }
    
    // Check Jean Pierre's referral relationships
    const { data: referrals, error: refError } = await supabase
      .from('referrals')
      .select(`
        *,
        referrer:users!referrer_id(id, username, full_name)
      `)
      .eq('referred_id', jeanPierre.id)
      .eq('status', 'active');
    
    if (refError) {
      console.error('❌ Error checking referrals:', refError);
    } else {
      console.log(`\n🔗 Active Referral Relationships: ${referrals.length}`);
      referrals.forEach(ref => {
        console.log(`   Referrer: ${ref.referrer?.full_name || ref.referrer?.username} (ID: ${ref.referrer_id})`);
        console.log(`   Commission Rate: ${ref.commission_rate}%`);
        console.log(`   Status: ${ref.status}`);
        console.log(`   Created: ${ref.created_at}`);
        console.log('');
      });
    }
    
    // Check if there are any incomplete payments that should have been approved
    const { data: incompletePay, error: incError } = await supabase
      .from('crypto_payment_transactions')
      .select('*')
      .eq('user_id', jeanPierre.id)
      .eq('status', 'incomplete')
      .eq('amount', 5)
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (incError) {
      console.error('❌ Error checking incomplete payments:', incError);
    } else if (incompletePay.length > 0) {
      console.log('\n⚠️  Found incomplete $5 payment:');
      const payment = incompletePay[0];
      console.log(`   Payment ID: ${payment.id}`);
      console.log(`   Amount: $${payment.amount}`);
      console.log(`   Created: ${payment.created_at}`);
      console.log(`   Status: ${payment.status}`);
      console.log(`   Network: ${payment.network}`);
      console.log(`   TX Hash: ${payment.transaction_hash || 'None'}`);
      
      console.log('\n🤔 This payment might need to be approved to trigger commission distribution.');
    }
    
  } catch (error) {
    console.error('❌ Check failed:', error);
  }
}

checkRecentPayment();
