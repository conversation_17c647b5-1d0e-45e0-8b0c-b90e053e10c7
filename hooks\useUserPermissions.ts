import { useState, useEffect } from 'react';
import { validateImpersonationSession, getCurrentImpersonationSession } from '../lib/adminImpersonation';
import { getUserType } from '../lib/supabase';

export interface UserPermissions {
  canPurchaseShares: boolean;
  canViewPortfolio: boolean;
  canManageReferrals: boolean;
  canWithdrawCommissions: boolean;
  canAccessSettings: boolean;
  canViewPayments: boolean;
  canAccessSupport: boolean;
  isImpersonated: boolean;
  userType: 'shareholder' | 'affiliate' | 'admin' | null;
}

export interface ImpersonationSession {
  isActive: boolean;
  adminUser?: any;
  targetUser?: any;
  sessionId?: string;
}

export interface UseUserPermissionsReturn {
  permissions: UserPermissions;
  impersonationSession: ImpersonationSession;
  loading: boolean;
  error: string | null;
  refreshPermissions: () => Promise<void>;
}

export const useUserPermissions = (user: any): UseUserPermissionsReturn => {
  const [permissions, setPermissions] = useState<UserPermissions>({
    canPurchaseShares: false,
    canViewPortfolio: false,
    canManageReferrals: false,
    canWithdrawCommissions: false,
    canAccessSettings: false,
    canViewPayments: false,
    canAccessSupport: false,
    isImpersonated: false,
    userType: null
  });

  const [impersonationSession, setImpersonationSession] = useState<ImpersonationSession>({
    isActive: false
  });

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadPermissions = async () => {
    if (!user) {
      setPermissions({
        canPurchaseShares: false,
        canViewPortfolio: false,
        canManageReferrals: false,
        canWithdrawCommissions: false,
        canAccessSettings: false,
        canViewPayments: false,
        canAccessSupport: false,
        isImpersonated: false,
        userType: null
      });
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Check impersonation status
      const impersonationValid = await validateImpersonationSession();
      const currentImpersonation = await getCurrentImpersonationSession();

      if (impersonationValid && currentImpersonation) {
        setImpersonationSession({
          isActive: true,
          adminUser: currentImpersonation.adminUser,
          targetUser: currentImpersonation.targetUser,
          sessionId: currentImpersonation.sessionId
        });
      } else {
        setImpersonationSession({ isActive: false });
      }

      // Determine user type
      const userType = await getUserType(user);

      // Set permissions based on user type and status
      const basePermissions = {
        canPurchaseShares: true, // All users can purchase shares
        canViewPortfolio: true,  // All users can view their portfolio
        canAccessSettings: true, // All users can access settings
        canAccessSupport: true,  // All users can access support
        canViewPayments: true,   // All users can view their payments
        isImpersonated: impersonationValid,
        userType: userType as 'shareholder' | 'affiliate' | 'admin'
      };

      // Additional permissions based on user type
      let additionalPermissions = {};

      switch (userType) {
        case 'admin':
          additionalPermissions = {
            canManageReferrals: true,
            canWithdrawCommissions: true,
            // Admins have all permissions
          };
          break;

        case 'affiliate':
          additionalPermissions = {
            canManageReferrals: true,
            canWithdrawCommissions: true, // Subject to KYC validation
          };
          break;

        case 'shareholder':
        default:
          additionalPermissions = {
            canManageReferrals: false,
            canWithdrawCommissions: false,
          };
          break;
      }

      setPermissions({
        ...basePermissions,
        ...additionalPermissions
      });

    } catch (err: any) {
      console.error('Error loading user permissions:', err);
      setError(err.message || 'Failed to load user permissions');
      
      // Set minimal permissions on error
      setPermissions({
        canPurchaseShares: true,
        canViewPortfolio: true,
        canManageReferrals: false,
        canWithdrawCommissions: false,
        canAccessSettings: true,
        canViewPayments: true,
        canAccessSupport: true,
        isImpersonated: false,
        userType: 'shareholder'
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPermissions();
  }, [user]);

  return {
    permissions,
    impersonationSession,
    loading,
    error,
    refreshPermissions: loadPermissions
  };
};
