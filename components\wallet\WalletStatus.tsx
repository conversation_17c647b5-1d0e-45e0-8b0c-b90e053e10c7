import React, { useState, useEffect } from 'react';
import { walletConnectionService } from '../../lib/services/walletConnectionService';

interface WalletStatusProps {
  onConnect: () => void;
  onDisconnect: () => void;
  className?: string;
}

export const WalletStatus: React.FC<WalletStatusProps> = ({
  onConnect,
  onDisconnect,
  className = ''
}) => {
  const [isConnected, setIsConnected] = useState(false);
  const [address, setAddress] = useState<string | null>(null);
  const [networkInfo, setNetworkInfo] = useState<{ chainId: string; networkName: string } | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    checkWalletStatus();
  }, []);

  const checkWalletStatus = async () => {
    const connected = walletConnectionService.isConnected();
    const walletAddress = walletConnectionService.getConnectedAddress();
    
    setIsConnected(connected);
    setAddress(walletAddress);

    if (connected) {
      try {
        const info = await walletConnectionService.getNetworkInfo();
        setNetworkInfo(info);
      } catch (error) {
        console.error('Failed to get network info:', error);
      }
    }
  };

  const handleConnect = () => {
    setIsLoading(true);
    onConnect();
    // Reset loading state after a delay to allow for connection process
    setTimeout(() => {
      setIsLoading(false);
      checkWalletStatus();
    }, 2000);
  };

  const handleDisconnect = () => {
    walletConnectionService.disconnect();
    setIsConnected(false);
    setAddress(null);
    setNetworkInfo(null);
    onDisconnect();
  };

  const formatAddress = (addr: string): string => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  const getNetworkColor = (chainId: string): string => {
    const colors: { [key: string]: string } = {
      '0x1': 'text-blue-400',
      '0x38': 'text-yellow-400',
      '0x89': 'text-purple-400',
      '0xa86a': 'text-red-400'
    };
    return colors[chainId] || 'text-gray-400';
  };

  const getNetworkIcon = (chainId: string): string => {
    const icons: { [key: string]: string } = {
      '0x1': '🔷',
      '0x38': '🟡',
      '0x89': '🟣',
      '0xa86a': '🔴'
    };
    return icons[chainId] || '🌐';
  };

  if (isConnected && address) {
    return (
      <div className={`bg-gray-800 rounded-lg border border-gray-700 p-4 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center">
              <span className="text-white text-sm">👛</span>
            </div>
            <div>
              <div className="flex items-center space-x-2">
                <span className="text-green-400 text-sm font-medium">Connected</span>
                <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
              </div>
              <p className="text-gray-300 text-sm font-mono">{formatAddress(address)}</p>
              {networkInfo && (
                <div className="flex items-center space-x-1 mt-1">
                  <span className="text-xs">{getNetworkIcon(networkInfo.chainId)}</span>
                  <span className={`text-xs ${getNetworkColor(networkInfo.chainId)}`}>
                    {networkInfo.networkName}
                  </span>
                </div>
              )}
            </div>
          </div>
          <button
            onClick={handleDisconnect}
            className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors"
          >
            Disconnect
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-gray-800 rounded-lg border border-gray-700 p-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center">
            <span className="text-gray-400 text-sm">👛</span>
          </div>
          <div>
            <span className="text-gray-400 text-sm font-medium">Wallet Not Connected</span>
            <p className="text-gray-500 text-xs">Connect your wallet for secure payments</p>
          </div>
        </div>
        <button
          onClick={handleConnect}
          disabled={isLoading}
          className="px-4 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
        >
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Connecting...</span>
            </>
          ) : (
            <span>Connect Wallet</span>
          )}
        </button>
      </div>
    </div>
  );
};

// Wallet Connection Button Component
interface WalletConnectionButtonProps {
  onConnect: () => void;
  isConnected: boolean;
  address?: string | null;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const WalletConnectionButton: React.FC<WalletConnectionButtonProps> = ({
  onConnect,
  isConnected,
  address,
  className = '',
  size = 'md'
}) => {
  const sizeClasses = {
    sm: 'px-3 py-1 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };

  const formatAddress = (addr: string): string => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  if (isConnected && address) {
    return (
      <div className={`flex items-center space-x-2 bg-green-900/30 border border-green-700 rounded-lg ${sizeClasses[size]} ${className}`}>
        <span className="w-2 h-2 bg-green-400 rounded-full"></span>
        <span className="text-green-400 font-mono">{formatAddress(address)}</span>
      </div>
    );
  }

  return (
    <button
      onClick={onConnect}
      className={`bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors ${sizeClasses[size]} ${className}`}
    >
      Connect Wallet
    </button>
  );
};

// Transaction Status Component
interface TransactionStatusProps {
  txHash?: string;
  status: 'pending' | 'confirmed' | 'failed';
  networkExplorerUrl?: string;
  className?: string;
}

export const TransactionStatus: React.FC<TransactionStatusProps> = ({
  txHash,
  status,
  networkExplorerUrl = 'https://bscscan.com',
  className = ''
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'pending':
        return {
          icon: '⏳',
          color: 'text-yellow-400',
          bgColor: 'bg-yellow-900/30',
          borderColor: 'border-yellow-700',
          message: 'Transaction Pending'
        };
      case 'confirmed':
        return {
          icon: '✅',
          color: 'text-green-400',
          bgColor: 'bg-green-900/30',
          borderColor: 'border-green-700',
          message: 'Transaction Confirmed'
        };
      case 'failed':
        return {
          icon: '❌',
          color: 'text-red-400',
          bgColor: 'bg-red-900/30',
          borderColor: 'border-red-700',
          message: 'Transaction Failed'
        };
    }
  };

  const config = getStatusConfig();

  return (
    <div className={`${config.bgColor} border ${config.borderColor} rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">{config.icon}</span>
          <div>
            <p className={`${config.color} font-medium`}>{config.message}</p>
            {txHash && (
              <p className="text-gray-400 text-sm font-mono">
                {txHash.slice(0, 10)}...{txHash.slice(-8)}
              </p>
            )}
          </div>
        </div>
        {txHash && networkExplorerUrl && (
          <a
            href={`${networkExplorerUrl}/tx/${txHash}`}
            target="_blank"
            rel="noopener noreferrer"
            className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
          >
            View on Explorer
          </a>
        )}
      </div>
    </div>
  );
};
