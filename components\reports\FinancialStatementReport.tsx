import React, { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';

interface Transaction {
  id: string;
  date: string;
  description: string;
  reference_id: string;
  debit: number;
  credit: number;
  balance_usdt: number;
  balance_shares: number;
  type: 'commission' | 'purchase' | 'withdrawal' | 'conversion' | 'transfer';
  currency: 'USDT' | 'Shares';
  raw_data?: any;
}

interface StatementSummary {
  opening_balance_usdt: number;
  opening_balance_shares: number;
  total_credits_usdt: number;
  total_credits_shares: number;
  total_debits_usdt: number;
  total_debits_shares: number;
  closing_balance_usdt: number;
  closing_balance_shares: number;
  transaction_count: number;
}

interface FinancialStatementProps {
  userId: number;
  userName?: string;
  isAdmin?: boolean;
  onClose?: () => void;
}

interface FilterState {
  dateFrom: string;
  dateTo: string;
  transactionTypes: string[];
  currency: 'All' | 'USDT' | 'Shares';
  searchTerm: string;
  itemsPerPage: number;
  currentPage: number;
}

const FinancialStatementReport: React.FC<FinancialStatementProps> = ({
  userId,
  userName,
  isAdmin = false,
  onClose
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [summary, setSummary] = useState<StatementSummary | null>(null);
  const [userInfo, setUserInfo] = useState<any>(null);
  const [currentPhase, setCurrentPhase] = useState<any>(null);
  const [exporting, setExporting] = useState(false);

  const [filters, setFilters] = useState<FilterState>({
    dateFrom: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    dateTo: new Date().toISOString().split('T')[0],
    transactionTypes: [],
    currency: 'All',
    searchTerm: '',
    itemsPerPage: 50,
    currentPage: 1
  });

  const transactionTypeOptions = [
    { value: 'commission', label: 'Commission Earnings', color: 'bg-green-500' },
    { value: 'purchase', label: 'Share Purchases', color: 'bg-blue-500' },
    { value: 'withdrawal', label: 'Withdrawals', color: 'bg-red-500' },
    { value: 'conversion', label: 'USDT Conversions', color: 'bg-purple-500' },
    { value: 'transfer', label: 'Share Transfers', color: 'bg-yellow-500' }
  ];

  useEffect(() => {
    loadFinancialData();
  }, [userId, filters.dateFrom, filters.dateTo]);

  const loadFinancialData = async () => {
    try {
      setLoading(true);
      setError(null);

      const serviceClient = getServiceRoleClient();

      // Load user information
      const { data: userData, error: userError } = await serviceClient
        .from('users')
        .select('id, username, first_name, last_name, full_name, email')
        .eq('id', userId)
        .single();

      if (userError) throw userError;
      setUserInfo(userData);

      // Load current phase for share price calculations
      const { data: phaseData, error: phaseError } = await serviceClient
        .from('investment_phases')
        .select('*')
        .eq('is_active', true)
        .single();

      if (phaseError) throw phaseError;
      setCurrentPhase(phaseData);

      // Load all transaction data
      const [
        cryptoPayments,
        commissionTxns,
        conversions,
        transfers,
        purchases,
        withdrawals
      ] = await Promise.all([
        loadCryptoPayments(serviceClient),
        loadCommissionTransactions(serviceClient),
        loadCommissionConversions(serviceClient),
        loadShareTransfers(serviceClient),
        loadSharePurchases(serviceClient),
        loadWithdrawalRequests(serviceClient)
      ]);

      // Process and combine all transactions
      const allTransactions = await processTransactions({
        cryptoPayments,
        commissionTxns,
        conversions,
        transfers,
        purchases,
        withdrawals
      });

      setTransactions(allTransactions);

      // Calculate summary
      const statementSummary = calculateSummary(allTransactions);
      setSummary(statementSummary);

    } catch (error) {
      console.error('Error loading financial data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load financial data');
    } finally {
      setLoading(false);
    }
  };

  const loadCryptoPayments = async (serviceClient: any) => {
    const { data, error } = await serviceClient
      .from('crypto_payment_transactions')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'approved')
      .gte('created_at', `${filters.dateFrom}T00:00:00.000Z`)
      .lte('created_at', `${filters.dateTo}T23:59:59.999Z`)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  };

  const loadCommissionTransactions = async (serviceClient: any) => {
    const { data, error } = await serviceClient
      .from('commission_transactions')
      .select(`
        *,
        referred_user:users!referred_id(username, full_name)
      `)
      .eq('referrer_id', userId)
      .gte('created_at', `${filters.dateFrom}T00:00:00.000Z`)
      .lte('created_at', `${filters.dateTo}T23:59:59.999Z`)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  };

  const loadCommissionConversions = async (serviceClient: any) => {
    const { data, error } = await serviceClient
      .from('commission_conversions')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'approved')
      .gte('created_at', `${filters.dateFrom}T00:00:00.000Z`)
      .lte('created_at', `${filters.dateTo}T23:59:59.999Z`)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  };

  const loadShareTransfers = async (serviceClient: any) => {
    const { data: sentTransfers, error: sentError } = await serviceClient
      .from('share_transfers')
      .select(`
        *,
        recipient:users!recipient_user_id(username, full_name)
      `)
      .eq('sender_user_id', userId)
      .gte('created_at', `${filters.dateFrom}T00:00:00.000Z`)
      .lte('created_at', `${filters.dateTo}T23:59:59.999Z`);

    const { data: receivedTransfers, error: receivedError } = await serviceClient
      .from('share_transfers')
      .select(`
        *,
        sender:users!sender_user_id(username, full_name)
      `)
      .eq('recipient_user_id', userId)
      .gte('created_at', `${filters.dateFrom}T00:00:00.000Z`)
      .lte('created_at', `${filters.dateTo}T23:59:59.999Z`);

    if (sentError) throw sentError;
    if (receivedError) throw receivedError;

    return [...(sentTransfers || []), ...(receivedTransfers || [])];
  };

  const loadSharePurchases = async (serviceClient: any) => {
    const { data, error } = await serviceClient
      .from('aureus_share_purchases')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'active')
      .gte('created_at', `${filters.dateFrom}T00:00:00.000Z`)
      .lte('created_at', `${filters.dateTo}T23:59:59.999Z`)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  };

  const loadWithdrawalRequests = async (serviceClient: any) => {
    const { data, error } = await serviceClient
      .from('commission_withdrawal_requests')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'completed')
      .gte('created_at', `${filters.dateFrom}T00:00:00.000Z`)
      .lte('created_at', `${filters.dateTo}T23:59:59.999Z`)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  };

  const processTransactions = async (data: any): Promise<Transaction[]> => {
    const processedTransactions: Transaction[] = [];
    let runningBalanceUSDT = 0;
    let runningBalanceShares = 0;

    // Process crypto payments (credits)
    data.cryptoPayments.forEach((payment: any) => {
      runningBalanceUSDT += parseFloat(payment.amount);
      processedTransactions.push({
        id: payment.id,
        date: payment.created_at,
        description: `USDT Payment - ${payment.currency} ${payment.network}`,
        reference_id: payment.id.substring(0, 8) + '...',
        debit: 0,
        credit: parseFloat(payment.amount),
        balance_usdt: runningBalanceUSDT,
        balance_shares: runningBalanceShares,
        type: 'purchase',
        currency: 'USDT',
        raw_data: payment
      });
    });

    // Process commission transactions (credits)
    data.commissionTxns.forEach((commission: any) => {
      if (commission.usdt_commission > 0) {
        runningBalanceUSDT += parseFloat(commission.usdt_commission);
        processedTransactions.push({
          id: commission.id,
          date: commission.created_at,
          description: `USDT Commission from ${commission.referred_user?.full_name || commission.referred_user?.username || 'referral'}`,
          reference_id: commission.id.substring(0, 8) + '...',
          debit: 0,
          credit: parseFloat(commission.usdt_commission),
          balance_usdt: runningBalanceUSDT,
          balance_shares: runningBalanceShares,
          type: 'commission',
          currency: 'USDT',
          raw_data: commission
        });
      }

      if (commission.share_commission > 0) {
        runningBalanceShares += parseFloat(commission.share_commission);
        processedTransactions.push({
          id: commission.id + '_shares',
          date: commission.created_at,
          description: `Share Commission from ${commission.referred_user?.full_name || commission.referred_user?.username || 'referral'}`,
          reference_id: commission.id.substring(0, 8) + '...',
          debit: 0,
          credit: parseFloat(commission.share_commission),
          balance_usdt: runningBalanceUSDT,
          balance_shares: runningBalanceShares,
          type: 'commission',
          currency: 'Shares',
          raw_data: commission
        });
      }
    });

    // Process conversions (debits USDT, credits shares)
    data.conversions.forEach((conversion: any) => {
      runningBalanceUSDT -= parseFloat(conversion.usdt_amount);
      runningBalanceShares += parseFloat(conversion.shares_requested);

      processedTransactions.push({
        id: conversion.id,
        date: conversion.created_at,
        description: `Converted $${conversion.usdt_amount} to ${conversion.shares_requested} shares`,
        reference_id: conversion.id.substring(0, 8) + '...',
        debit: parseFloat(conversion.usdt_amount),
        credit: parseFloat(conversion.shares_requested),
        balance_usdt: runningBalanceUSDT,
        balance_shares: runningBalanceShares,
        type: 'conversion',
        currency: 'USDT',
        raw_data: conversion
      });
    });

    // Process share transfers
    data.transfers.forEach((transfer: any) => {
      if (transfer.sender_user_id === userId) {
        // Outgoing transfer (debit)
        runningBalanceShares -= parseFloat(transfer.shares_transferred);
        processedTransactions.push({
          id: transfer.id,
          date: transfer.created_at,
          description: `Share transfer to ${transfer.recipient?.full_name || transfer.recipient?.username || 'user'}`,
          reference_id: transfer.id.substring(0, 8) + '...',
          debit: parseFloat(transfer.shares_transferred),
          credit: 0,
          balance_usdt: runningBalanceUSDT,
          balance_shares: runningBalanceShares,
          type: 'transfer',
          currency: 'Shares',
          raw_data: transfer
        });
      } else {
        // Incoming transfer (credit)
        runningBalanceShares += parseFloat(transfer.shares_transferred);
        processedTransactions.push({
          id: transfer.id,
          date: transfer.created_at,
          description: `Share transfer from ${transfer.sender?.full_name || transfer.sender?.username || 'user'}`,
          reference_id: transfer.id.substring(0, 8) + '...',
          debit: 0,
          credit: parseFloat(transfer.shares_transferred),
          balance_usdt: runningBalanceUSDT,
          balance_shares: runningBalanceShares,
          type: 'transfer',
          currency: 'Shares',
          raw_data: transfer
        });
      }
    });

    // Process withdrawals (debits)
    data.withdrawals.forEach((withdrawal: any) => {
      runningBalanceUSDT -= parseFloat(withdrawal.amount);
      processedTransactions.push({
        id: withdrawal.id,
        date: withdrawal.created_at,
        description: `USDT Withdrawal to ${withdrawal.wallet_address?.substring(0, 10)}...`,
        reference_id: withdrawal.id.substring(0, 8) + '...',
        debit: parseFloat(withdrawal.amount),
        credit: 0,
        balance_usdt: runningBalanceUSDT,
        balance_shares: runningBalanceShares,
        type: 'withdrawal',
        currency: 'USDT',
        raw_data: withdrawal
      });
    });

    // Sort by date (most recent first)
    return processedTransactions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  };

  const calculateSummary = (transactions: Transaction[]): StatementSummary => {
    const summary: StatementSummary = {
      opening_balance_usdt: 0,
      opening_balance_shares: 0,
      total_credits_usdt: 0,
      total_credits_shares: 0,
      total_debits_usdt: 0,
      total_debits_shares: 0,
      closing_balance_usdt: 0,
      closing_balance_shares: 0,
      transaction_count: transactions.length
    };

    transactions.forEach(txn => {
      if (txn.currency === 'USDT') {
        if (txn.credit > 0) summary.total_credits_usdt += txn.credit;
        if (txn.debit > 0) summary.total_debits_usdt += txn.debit;
      } else {
        if (txn.credit > 0) summary.total_credits_shares += txn.credit;
        if (txn.debit > 0) summary.total_debits_shares += txn.debit;
      }
    });

    // Get final balances from last transaction
    if (transactions.length > 0) {
      const lastTxn = transactions[0]; // Most recent
      summary.closing_balance_usdt = lastTxn.balance_usdt;
      summary.closing_balance_shares = lastTxn.balance_shares;
    }

    // Calculate opening balances
    summary.opening_balance_usdt = summary.closing_balance_usdt - summary.total_credits_usdt + summary.total_debits_usdt;
    summary.opening_balance_shares = summary.closing_balance_shares - summary.total_credits_shares + summary.total_debits_shares;

    return summary;
  };

  const formatCurrency = (amount: number, currency: 'USDT' | 'Shares' = 'USDT') => {
    if (currency === 'USDT') {
      return `$${amount.toFixed(2)}`;
    }
    return `${amount.toFixed(4)} shares`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const getTransactionTypeColor = (type: string) => {
    const typeConfig = transactionTypeOptions.find(t => t.value === type);
    return typeConfig?.color || 'bg-gray-500';
  };

  const resetFilters = () => {
    setFilters({
      dateFrom: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      dateTo: new Date().toISOString().split('T')[0],
      transactionTypes: [],
      currency: 'All',
      searchTerm: '',
      itemsPerPage: 50,
      currentPage: 1
    });
  };

  const exportToPDF = async () => {
    try {
      setExporting(true);

      // Create PDF content
      const pdfContent = generatePDFContent();

      // For now, we'll create a simple HTML version that can be printed
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(pdfContent);
        printWindow.document.close();
        printWindow.print();
      }
    } catch (error) {
      console.error('Error exporting PDF:', error);
      alert('Failed to export PDF. Please try again.');
    } finally {
      setExporting(false);
    }
  };

  const exportToCSV = () => {
    try {
      setExporting(true);

      const csvContent = generateCSVContent();
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');

      if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `financial_statement_${userInfo?.username}_${filters.dateFrom}_${filters.dateTo}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error('Error exporting CSV:', error);
      alert('Failed to export CSV. Please try again.');
    } finally {
      setExporting(false);
    }
  };

  const generatePDFContent = () => {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Financial Statement - ${userInfo?.full_name || userInfo?.username}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; color: #333; }
          .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #f4c430; padding-bottom: 20px; }
          .summary { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 30px; }
          .summary-item { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; }
          table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
          th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
          th { background-color: #f8f9fa; font-weight: bold; }
          .credit { color: #28a745; }
          .debit { color: #dc3545; }
          .footer { text-align: center; font-size: 12px; color: #666; margin-top: 30px; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Financial Statement</h1>
          <h2>${userInfo?.full_name || userInfo?.username}</h2>
          <p>Account ID: ${userId} | Period: ${new Date(filters.dateFrom).toLocaleDateString()} - ${new Date(filters.dateTo).toLocaleDateString()}</p>
          <p>Generated on: ${new Date().toLocaleString()}</p>
        </div>

        ${summary ? `
        <div class="summary">
          <div class="summary-item">
            <h3>Opening Balance</h3>
            <p>${formatCurrency(summary.opening_balance_usdt)}</p>
            <small>${formatCurrency(summary.opening_balance_shares, 'Shares')}</small>
          </div>
          <div class="summary-item">
            <h3>Total Credits</h3>
            <p class="credit">${formatCurrency(summary.total_credits_usdt)}</p>
            <small class="credit">${formatCurrency(summary.total_credits_shares, 'Shares')}</small>
          </div>
          <div class="summary-item">
            <h3>Total Debits</h3>
            <p class="debit">${formatCurrency(summary.total_debits_usdt)}</p>
            <small class="debit">${formatCurrency(summary.total_debits_shares, 'Shares')}</small>
          </div>
          <div class="summary-item">
            <h3>Closing Balance</h3>
            <p>${formatCurrency(summary.closing_balance_usdt)}</p>
            <small>${formatCurrency(summary.closing_balance_shares, 'Shares')}</small>
          </div>
        </div>
        ` : ''}

        <table>
          <thead>
            <tr>
              <th>Date/Time</th>
              <th>Description</th>
              <th>Reference</th>
              <th>Type</th>
              <th>Debit</th>
              <th>Credit</th>
              <th>Balance</th>
            </tr>
          </thead>
          <tbody>
            ${transactions.map(txn => `
              <tr>
                <td>${formatDate(txn.date)}</td>
                <td>${txn.description}</td>
                <td>${txn.reference_id}</td>
                <td>${transactionTypeOptions.find(t => t.value === txn.type)?.label || txn.type}</td>
                <td class="debit">${txn.debit > 0 ? formatCurrency(txn.debit, txn.currency) : '-'}</td>
                <td class="credit">${txn.credit > 0 ? formatCurrency(txn.credit, txn.currency) : '-'}</td>
                <td>${formatCurrency(txn.balance_usdt)} / ${formatCurrency(txn.balance_shares, 'Shares')}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <div class="footer">
          <p>This statement is generated automatically and reflects all transactions for the specified period.</p>
          <p>For questions or discrepancies, please contact <NAME_EMAIL></p>
          <p>Aureus Alliance Holdings (Pty) Ltd • Generated on ${new Date().toLocaleString()}</p>
        </div>
      </body>
      </html>
    `;
  };

  const generateCSVContent = () => {
    const headers = ['Date/Time', 'Description', 'Reference ID', 'Type', 'Debit', 'Credit', 'Balance USDT', 'Balance Shares', 'Currency'];

    const rows = transactions.map(txn => [
      formatDate(txn.date),
      `"${txn.description.replace(/"/g, '""')}"`,
      txn.reference_id,
      transactionTypeOptions.find(t => t.value === txn.type)?.label || txn.type,
      txn.debit > 0 ? txn.debit.toFixed(4) : '0',
      txn.credit > 0 ? txn.credit.toFixed(4) : '0',
      txn.balance_usdt.toFixed(2),
      txn.balance_shares.toFixed(4),
      txn.currency
    ]);

    // Add summary rows
    if (summary) {
      rows.unshift(['', '', '', '', '', '', '', '', '']); // Empty row
      rows.unshift(['SUMMARY', '', '', '', '', '', '', '', '']);
      rows.unshift(['Opening Balance USDT', '', '', '', '', '', summary.opening_balance_usdt.toFixed(2), '', 'USDT']);
      rows.unshift(['Opening Balance Shares', '', '', '', '', '', '', summary.opening_balance_shares.toFixed(4), 'Shares']);
      rows.unshift(['Total Credits USDT', '', '', '', '', summary.total_credits_usdt.toFixed(2), '', '', 'USDT']);
      rows.unshift(['Total Credits Shares', '', '', '', '', summary.total_credits_shares.toFixed(4), '', '', 'Shares']);
      rows.unshift(['Total Debits USDT', '', '', '', summary.total_debits_usdt.toFixed(2), '', '', '', 'USDT']);
      rows.unshift(['Total Debits Shares', '', '', '', summary.total_debits_shares.toFixed(4), '', '', '', 'Shares']);
      rows.unshift(['Closing Balance USDT', '', '', '', '', '', summary.closing_balance_usdt.toFixed(2), '', 'USDT']);
      rows.unshift(['Closing Balance Shares', '', '', '', '', '', '', summary.closing_balance_shares.toFixed(4), 'Shares']);
      rows.unshift(['', '', '', '', '', '', '', '', '']); // Empty row
    }

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-gray-800 rounded-lg p-8 max-w-md w-full mx-4">
          <div className="flex items-center justify-center mb-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400"></div>
          </div>
          <p className="text-white text-center">Generating Financial Statement...</p>
          <p className="text-gray-400 text-center text-sm mt-2">Please wait while we compile your transaction history</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-gray-800 rounded-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="text-red-400 text-4xl mb-4">⚠️</div>
            <h3 className="text-white text-lg font-semibold mb-2">Error Loading Statement</h3>
            <p className="text-gray-400 mb-4">{error}</p>
            <div className="flex gap-3 justify-center">
              <button
                onClick={loadFinancialData}
                className="bg-yellow-600 hover:bg-yellow-700 text-black px-4 py-2 rounded-lg font-medium"
              >
                Retry
              </button>
              {onClose && (
                <button
                  onClick={onClose}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium"
                >
                  Close
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-lg w-full max-w-7xl h-full max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div>
            <h2 className="text-2xl font-bold text-white">Financial Statement</h2>
            <p className="text-gray-400">
              {userInfo?.full_name || userInfo?.username} • Account ID: {userId}
            </p>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={exportToPDF}
              disabled={exporting}
              className="bg-red-600 hover:bg-red-700 disabled:opacity-50 text-white px-4 py-2 rounded-lg font-medium flex items-center gap-2"
            >
              📄 Export PDF
            </button>
            <button
              onClick={exportToCSV}
              disabled={exporting}
              className="bg-green-600 hover:bg-green-700 disabled:opacity-50 text-white px-4 py-2 rounded-lg font-medium flex items-center gap-2"
            >
              📊 Export CSV
            </button>
            {onClose && (
              <button
                onClick={onClose}
                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium"
              >
                ✕ Close
              </button>
            )}
          </div>
        </div>

        {/* Statement Content */}
        <div className="flex-1 overflow-hidden flex flex-col">
          {/* Statement Header */}
          <div className="bg-gray-800 p-6 border-b border-gray-700">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="text-center">
                <p className="text-gray-400 text-sm">Statement Period</p>
                <p className="text-white font-semibold">
                  {new Date(filters.dateFrom).toLocaleDateString()} - {new Date(filters.dateTo).toLocaleDateString()}
                </p>
              </div>
              <div className="text-center">
                <p className="text-gray-400 text-sm">Generated On</p>
                <p className="text-white font-semibold">{new Date().toLocaleDateString()}</p>
              </div>
              <div className="text-center">
                <p className="text-gray-400 text-sm">Total Transactions</p>
                <p className="text-white font-semibold">{summary?.transaction_count || 0}</p>
              </div>
              <div className="text-center">
                <p className="text-gray-400 text-sm">Current Share Price</p>
                <p className="text-white font-semibold">${currentPhase?.price_per_share || '5.00'}</p>
              </div>
            </div>
          </div>

          {/* Summary Section */}
          {summary && (
            <div className="bg-gray-800 p-6 border-b border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-4">Account Summary</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-gray-700 rounded-lg p-4">
                  <p className="text-gray-400 text-sm">Opening Balance</p>
                  <p className="text-white font-semibold">{formatCurrency(summary.opening_balance_usdt)}</p>
                  <p className="text-gray-300 text-xs">{formatCurrency(summary.opening_balance_shares, 'Shares')}</p>
                </div>
                <div className="bg-gray-700 rounded-lg p-4">
                  <p className="text-gray-400 text-sm">Total Credits</p>
                  <p className="text-green-400 font-semibold">{formatCurrency(summary.total_credits_usdt)}</p>
                  <p className="text-green-300 text-xs">{formatCurrency(summary.total_credits_shares, 'Shares')}</p>
                </div>
                <div className="bg-gray-700 rounded-lg p-4">
                  <p className="text-gray-400 text-sm">Total Debits</p>
                  <p className="text-red-400 font-semibold">{formatCurrency(summary.total_debits_usdt)}</p>
                  <p className="text-red-300 text-xs">{formatCurrency(summary.total_debits_shares, 'Shares')}</p>
                </div>
                <div className="bg-gray-700 rounded-lg p-4">
                  <p className="text-gray-400 text-sm">Closing Balance</p>
                  <p className="text-white font-semibold">{formatCurrency(summary.closing_balance_usdt)}</p>
                  <p className="text-gray-300 text-xs">{formatCurrency(summary.closing_balance_shares, 'Shares')}</p>
                </div>
              </div>
            </div>
          )}

          {/* Filters */}
          <div className="bg-gray-800 p-4 border-b border-gray-700">
            <div className="flex flex-wrap gap-4 items-center">
              <div className="flex items-center gap-2">
                <label className="text-gray-400 text-sm">From:</label>
                <input
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => setFilters(prev => ({ ...prev, dateFrom: e.target.value }))}
                  className="bg-gray-700 text-white px-3 py-1 rounded border border-gray-600 text-sm"
                />
              </div>
              <div className="flex items-center gap-2">
                <label className="text-gray-400 text-sm">To:</label>
                <input
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => setFilters(prev => ({ ...prev, dateTo: e.target.value }))}
                  className="bg-gray-700 text-white px-3 py-1 rounded border border-gray-600 text-sm"
                />
              </div>
              <div className="flex items-center gap-2">
                <label className="text-gray-400 text-sm">Currency:</label>
                <select
                  value={filters.currency}
                  onChange={(e) => setFilters(prev => ({ ...prev, currency: e.target.value as any }))}
                  className="bg-gray-700 text-white px-3 py-1 rounded border border-gray-600 text-sm"
                >
                  <option value="All">All</option>
                  <option value="USDT">USDT</option>
                  <option value="Shares">Shares</option>
                </select>
              </div>
              <div className="flex items-center gap-2">
                <input
                  type="text"
                  placeholder="Search transactions..."
                  value={filters.searchTerm}
                  onChange={(e) => setFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
                  className="bg-gray-700 text-white px-3 py-1 rounded border border-gray-600 text-sm w-48"
                />
              </div>
              <button
                onClick={resetFilters}
                className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm"
              >
                Reset
              </button>
            </div>
          </div>

          {/* Transaction Table */}
          <div className="flex-1 overflow-auto">
            {transactions.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-400 text-4xl mb-4">📄</div>
                <h3 className="text-white text-lg font-semibold mb-2">No Transactions Found</h3>
                <p className="text-gray-400">No transactions found for the selected period and filters.</p>
              </div>
            ) : (
              <table className="w-full">
                <thead className="bg-gray-800 sticky top-0">
                  <tr>
                    <th className="text-left text-gray-400 font-medium p-4 border-b border-gray-700">Date/Time</th>
                    <th className="text-left text-gray-400 font-medium p-4 border-b border-gray-700">Description</th>
                    <th className="text-left text-gray-400 font-medium p-4 border-b border-gray-700">Reference</th>
                    <th className="text-left text-gray-400 font-medium p-4 border-b border-gray-700">Type</th>
                    <th className="text-right text-gray-400 font-medium p-4 border-b border-gray-700">Debit</th>
                    <th className="text-right text-gray-400 font-medium p-4 border-b border-gray-700">Credit</th>
                    <th className="text-right text-gray-400 font-medium p-4 border-b border-gray-700">Balance</th>
                  </tr>
                </thead>
                <tbody>
                  {transactions.map((transaction, index) => (
                    <tr key={transaction.id} className={`${index % 2 === 0 ? 'bg-gray-900' : 'bg-gray-800'} hover:bg-gray-700`}>
                      <td className="p-4 text-white text-sm">{formatDate(transaction.date)}</td>
                      <td className="p-4 text-white text-sm">{transaction.description}</td>
                      <td className="p-4 text-gray-400 text-sm font-mono">{transaction.reference_id}</td>
                      <td className="p-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium text-white ${getTransactionTypeColor(transaction.type)}`}>
                          {transactionTypeOptions.find(t => t.value === transaction.type)?.label || transaction.type}
                        </span>
                      </td>
                      <td className="p-4 text-right text-red-400 text-sm">
                        {transaction.debit > 0 ? formatCurrency(transaction.debit, transaction.currency) : '-'}
                      </td>
                      <td className="p-4 text-right text-green-400 text-sm">
                        {transaction.credit > 0 ? formatCurrency(transaction.credit, transaction.currency) : '-'}
                      </td>
                      <td className="p-4 text-right text-white text-sm">
                        <div>{formatCurrency(transaction.balance_usdt)}</div>
                        <div className="text-xs text-gray-400">{formatCurrency(transaction.balance_shares, 'Shares')}</div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>

          {/* Footer */}
          <div className="bg-gray-800 p-4 border-t border-gray-700">
            <div className="text-center text-gray-400 text-xs">
              <p>This statement is generated automatically and reflects all transactions for the specified period.</p>
              <p className="mt-1">For questions or discrepancies, please contact <NAME_EMAIL></p>
              <p className="mt-2 text-gray-500">Aureus Alliance Holdings (Pty) Ltd • Generated on {new Date().toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FinancialStatementReport;
